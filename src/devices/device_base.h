/*
 * Copyright ©2015-2025 AUBO (Beijing) Robotics Technology Co., Ltd. All rights reserved.
 */

#ifndef AUBO_COFFEE_SERVICE_DEVICE_BASE_H
#define AUBO_COFFEE_SERVICE_DEVICE_BASE_H

#include <string>

namespace aubo {

/**
 * @class DeviceBase
 * @brief 设备基类
 * 
 * 所有咖啡制作系统中的设备都应该继承此基类
 */
class DeviceBase {
public:
    /**
     * @brief 构造函数
     * @param device_name 设备名称
     */
    explicit DeviceBase(const std::string& device_name);

    /**
     * @brief 虚析构函数
     */
    virtual ~DeviceBase() = default;

    /**
     * @brief 初始化设备
     * @return 初始化成功返回true
     */
    virtual bool init() = 0;

    /**
     * @brief 关闭设备
     * @return 关闭成功返回true
     */
    virtual bool shutdown() = 0;

    /**
     * @brief 紧急停止
     * @return 紧急停止成功返回true
     */
    virtual bool emergency_stop() = 0;

    /**
     * @brief 获取设备名称
     * @return 设备名称
     */
    const std::string& get_device_name() const;

    /**
     * @brief 获取设备状态
     * @return 设备是否已初始化
     */
    bool is_initialized() const;

    /**
     * @brief 获取设备连接状态
     * @return 设备是否已连接
     */
    bool is_connected() const;

protected:
    /**
     * @brief 设置初始化状态
     * @param initialized 初始化状态
     */
    void set_initialized(bool initialized);

    /**
     * @brief 设置连接状态
     * @param connected 连接状态
     */
    void set_connected(bool connected);

private:
    std::string device_name_;       ///< 设备名称
    bool initialized_;              ///< 初始化状态
    bool connected_;                ///< 连接状态
};

} // namespace aubo

#endif // AUBO_COFFEE_SERVICE_DEVICE_BASE_H
