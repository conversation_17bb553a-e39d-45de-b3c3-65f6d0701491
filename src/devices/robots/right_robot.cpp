/*
 * Copyright ©2015-2025 AUBO (Beijing) Robotics Technology Co., Ltd. All rights reserved.
 */

#include "right_robot.h"

#include <chrono>
#include <cmath>
#include <iostream>
#include <thread>

#include <aubo-base/log.h>
#include <aubo_sdk/AuboRobotMetaType.h>
#include <aubo_sdk/serviceinterface.h>

#include "../../types/latte_art_config.h"

namespace aubo {

// 配置常量
constexpr const char* RIGHT_ROBOT_HOST = "*************";
constexpr int RIGHT_ROBOT_PORT = 8899;

// 取牛奶序列常量定义
constexpr int MILK_SEQUENCE_LENGTH = 5;
constexpr double MILK_SEQUENCE_POSITIONS[MILK_SEQUENCE_LENGTH][aubo_robot_namespace::ARM_DOF] = {
    {-0.054005, 0.256593, 2.192446, -1.230923, -1.207646, 0.114618},    // 【取奶准备】
    {-0.223398, 0.022299, 1.981184, -1.205320, -0.358279, 0.114224},    // 【取奶下方】
    {-0.126652, -0.061707, 1.528101, -1.822993, 0.093292, -0.225399},   // 【倾斜取奶】
    {-0.223398, 0.022299, 1.981184, -1.205320, -0.358279, 0.114224},    // 【取完奶后回正】
    {-0.053949, 0.259882, 2.186474, -1.240176, -1.207596, 0.114617}     // 【取奶前方】
};

constexpr const char* MILK_SEQUENCE_NAMES[MILK_SEQUENCE_LENGTH] = {
    "右臂取奶准备",
    "右臂取奶下方",
    "右臂倾斜取奶",
    "右臂取完奶后回正",
    "右臂取奶前方"
};

// 摇奶序列常量定义
constexpr int SHAKE_MILK_SEQUENCE_LENGTH = 3;
constexpr double SHAKE_MILK_SEQUENCE_POSITIONS[SHAKE_MILK_SEQUENCE_LENGTH][aubo_robot_namespace::ARM_DOF] = {
    {-0.054118, 0.259726, 2.186295, -1.240109, -1.207265, 0.114604}, // 【摇奶位置1】
    {-0.151755, 0.267723, 2.191979, -1.241637, -1.304873, 0.112070}, // 【摇奶位置2】
    {-0.218171, 0.161130, 2.110561, -1.216082, -1.371269, 0.110424}  // 【摇奶位置3】
};

constexpr double SHAKE_MILK_SEQUENCE_POSITIONS2[SHAKE_MILK_SEQUENCE_LENGTH][7] = {
    {0.248760, -0.166112, 0.220066, 0.411338, -0.357746, 0.619466, -0.564872}, // 【摇奶位置1】
    {0.233760, -0.181112, 0.220066, 0.411338, -0.357746, 0.619466, -0.564872}, // 【摇奶位置2】
    {0.248760, -0.196112, 0.220066, 0.411338, -0.357746, 0.619466, -0.564872}  // 【摇奶位置3】
};

constexpr const char* SHAKE_MILK_SEQUENCE_NAMES[SHAKE_MILK_SEQUENCE_LENGTH] = {
    "摇奶位置1",
    "摇奶位置2",
    "摇奶位置3"
};

// 拉花序列常量定义
constexpr int LATTE_ART_SEQUENCE_LENGTH = 1;
constexpr double LATTE_ART_SEQUENCE_POSITIONS[LATTE_ART_SEQUENCE_LENGTH][aubo_robot_namespace::ARM_DOF] = {
    {-0.377120, 0.213000, 1.635890, -1.683930, -1.814830, -0.783520}, // 【拉花准备】
};

constexpr const char* LATTE_ART_SEQUENCE_NAMES[LATTE_ART_SEQUENCE_LENGTH] = {
    "右臂拉花准备"
};

// 倾倒牛奶序列常量定义
constexpr int POUR_MILK_SEQUENCE_LENGTH = 3;
constexpr double POUR_MILK_SEQUENCE_POSITIONS[POUR_MILK_SEQUENCE_LENGTH][aubo_robot_namespace::ARM_DOF] = {
    {-0.231389, 0.325317, 1.693122, -1.655029, -1.500510, -0.401216}, // 【倾倒位置1】
    {-0.173430, 0.147302, 2.101791, -1.235604, -1.260543, -0.424707}, // 【倾倒位置2】
    {-0.011403, 0.064681, 1.964967, -1.347639, -1.257074, -1.061824}  // 【倾倒位置3】
};

constexpr const char* POUR_MILK_SEQUENCE_NAMES[POUR_MILK_SEQUENCE_LENGTH] = {
    "倾倒位置1",
    "倾倒位置2",
    "倾倒位置3"
};

// 清洗序列常量定义
constexpr int CLEANING_SEQUENCE_LENGTH = 4;
constexpr double CLEANING_SEQUENCE_POSITIONS[CLEANING_SEQUENCE_LENGTH][aubo_robot_namespace::ARM_DOF] = {
    {0.053472, 0.029112, 1.868314, -1.348693, -1.339303, -1.786542}, // 【清洗位置1】
    {0.336054, -0.045866, 2.000638, -1.131380, -1.200204, -2.864809}, // 【清洗位置2】
    {0.284735, -0.381071, 2.162107, -0.644146, -1.228713, -2.868520}, // 【清洗位置3】
    {0.299558, -0.253792, 2.193381, -0.681089, -1.286692, -3.001649}  // 【清洗位置4】
};

constexpr const char* CLEANING_SEQUENCE_NAMES[CLEANING_SEQUENCE_LENGTH] = {
    "清洗位置1",
    "清洗位置2",
    "清洗位置3",
    "清洗位置4"
};

// Home位置常量定义
constexpr double HOME_POSITION[aubo_robot_namespace::ARM_DOF] = {
    0.299558, -0.253792, 2.193381, -0.681089, -1.286692, -3.001649  // 【Home位置】
};

// 离开Home前往工作准备位置序列常量定义
constexpr int LEAVE_HOME_SEQUENCE_LENGTH = 3;
constexpr double LEAVE_HOME_SEQUENCE_POSITIONS[LEAVE_HOME_SEQUENCE_LENGTH][aubo_robot_namespace::ARM_DOF] = {
    {0.299558, -0.253792, 2.193381, -0.681089, -1.286692, -3.001649}, // 【Home位置】
    {0.302517, 0.053719, 1.899584, -1.264403, -1.281932, -2.948872},  // 【过渡位置】
    {0.101288, 0.171540, 1.921859, -1.414690, -1.288168, -1.353138}   // 【工作准备位置】
};

constexpr const char* LEAVE_HOME_SEQUENCE_NAMES[LEAVE_HOME_SEQUENCE_LENGTH] = {
    "Home位置",
    "过渡位置",
    "工作准备位置"
};

// 位置比较阈值
constexpr double POSITION_TOLERANCE = 0.1;

// 计算两个关节位置之间的距离
static double calculate_joint_distance(const double* pos1, const double* pos2) {
    double sum = 0.0;
    for (int i = 0; i < aubo_robot_namespace::ARM_DOF; i++) {
        double diff = pos1[i] - pos2[i];
        sum += diff * diff;
    }
    return std::sqrt(sum);
}

// 检查两个位置是否接近
bool is_position_close(const double* pos1, const double* pos2, double tolerance = POSITION_TOLERANCE) {
    return calculate_joint_distance(pos1, pos2) < tolerance;
}

// 检查当前位置是否在取奶序列中
bool is_in_milk_sequence(const double* current_joint_pos) {
    for (int i = 0; i < MILK_SEQUENCE_LENGTH; i++) {
        if (is_position_close(current_joint_pos, MILK_SEQUENCE_POSITIONS[i])) {
            LOG_INFO("[RightRobot] 检测到机器人在取奶序列位置 {} 附近", i);
            return true;
        }
    }
    return false;
}

// 检查当前位置是否在拉花序列中
bool is_in_latte_art_sequence(const double* current_joint_pos) {
    for (int i = 0; i < LATTE_ART_SEQUENCE_LENGTH; i++) {
        if (is_position_close(current_joint_pos, LATTE_ART_SEQUENCE_POSITIONS[i])) {
            LOG_INFO("[RightRobot] 检测到机器人在拉花序列位置 {} 附近", i);
            return true;
        }
    }
    return false;
}

// 检查当前位置是否在摇奶序列中
bool is_in_shake_milk_sequence(const double* current_joint_pos) {
    for (int i = 0; i < SHAKE_MILK_SEQUENCE_LENGTH; i++) {
        if (is_position_close(current_joint_pos, SHAKE_MILK_SEQUENCE_POSITIONS[i])) {
            LOG_INFO("[RightRobot] 检测到机器人在摇奶序列位置 {} 附近", i);
            return true;
        }
    }
    return false;
}

// 检查当前位置是否在倾倒牛奶序列中
bool is_in_pour_milk_sequence(const double* current_joint_pos) {
    for (int i = 0; i < POUR_MILK_SEQUENCE_LENGTH; i++) {
        if (is_position_close(current_joint_pos, POUR_MILK_SEQUENCE_POSITIONS[i])) {
            LOG_INFO("[RightRobot] 检测到机器人在倾倒牛奶序列位置 {} 附近", i);
            return true;
        }
    }
    return false;
}

// 检查当前位置是否在清洗序列中
bool is_in_cleaning_sequence(const double* current_joint_pos) {
    for (int i = 0; i < CLEANING_SEQUENCE_LENGTH - 2; i++) {
        if (is_position_close(current_joint_pos, CLEANING_SEQUENCE_POSITIONS[i])) {
            LOG_INFO("[RightRobot] 检测到机器人在清洗序列位置 {} 附近", i);
            return true;
        }
    }
    return false;
}

// 检查当前位置是否在清洗序列的最后一个位置附近
bool is_at_cleaning_final_position(const double* current_joint_pos) {
    // 检查是否在清洗序列的最后一个位置（清洗位置4）附近
    int final_position_index = CLEANING_SEQUENCE_LENGTH - 1;
    if (is_position_close(current_joint_pos, CLEANING_SEQUENCE_POSITIONS[final_position_index])) {
        LOG_INFO("[RightRobot] 检测到机器人在清洗最后位置附近");
        return true;
    }
    return false;
}

// 检查当前位置是否在Home位置附近
bool is_at_home_position(const double* current_joint_pos) {
    if (is_position_close(current_joint_pos, HOME_POSITION)) {
        LOG_INFO("[RightRobot] 检测到机器人在Home位置附近");
        return true;
    }
    return false;
}

// 检查当前位置是否在离开Home序列中
bool is_in_leave_home_sequence(const double* current_joint_pos) {
    for (int i = 0; i < LEAVE_HOME_SEQUENCE_LENGTH; i++) {
        if (is_position_close(current_joint_pos, LEAVE_HOME_SEQUENCE_POSITIONS[i])) {
            LOG_INFO("[RightRobot] 检测到机器人在离开Home序列位置 {} 附近", i);
            return true;
        }
    }
    return false;
}

// 检查当前位置是否在工作准备位置附近
bool is_at_work_ready_position(const double* current_joint_pos) {
    // 工作准备位置是离开Home序列的最后一个位置
    int work_ready_index = LEAVE_HOME_SEQUENCE_LENGTH - 1;
    if (is_position_close(current_joint_pos, LEAVE_HOME_SEQUENCE_POSITIONS[work_ready_index])) {
        LOG_INFO("[RightRobot] 检测到机器人在工作准备位置附近");
        return true;
    }
    return false;
}

// 执行取奶安全离开序列
bool execute_milk_exit_sequence(const double* current_joint_pos, ServiceInterface& robot_service) {
    // 找到当前位置最接近序列中的哪个位置
    int closest_position = 0;
    double min_distance = std::numeric_limits<double>::max();

    for (int i = 0; i < MILK_SEQUENCE_LENGTH; i++) {
        double distance = calculate_joint_distance(current_joint_pos, MILK_SEQUENCE_POSITIONS[i]);
        if (distance < min_distance) {
            min_distance = distance;
            closest_position = i;
        }
    }

    LOG_INFO("[RightRobot] 当前位置最接近 {}", MILK_SEQUENCE_NAMES[closest_position]);

    // 根据当前位置决定安全离开路径
    int exit_path[3] = {0, 0, 0};
    int exit_path_length = 0;

    if (closest_position == 0 || closest_position == 4) {
        // 如果在【准备】或【再次准备】位置，已经是安全位置
        LOG_INFO("[RightRobot] 机器人已在奶源安全位置，无需执行离开动作");
        return true;
    } else if (closest_position == 1) {
        // 如果在【奶源下方】位置，直接回到【再次准备】位置
        exit_path[0] = 4; // 【再次准备】
        exit_path_length = 1;
    } else if (closest_position == 2 || closest_position == 3) {
        // 如果在【倾斜】或【平放】位置，需要依次回到：【平放】→【再次准备】
        if (closest_position == 2) {
            // 从倾斜位置需要先到平放位置
            exit_path[0] = 3; // 【平放】
            exit_path[1] = 4; // 【再次准备】
            exit_path_length = 2;
        } else {
            // 从平放位置直接到再次准备位置
            exit_path[0] = 4; // 【再次准备】
            exit_path_length = 1;
        }
    }

    // 执行安全离开路径
    for (int i = 0; i < exit_path_length; i++) {
        int pos_index = exit_path[i];
        LOG_INFO("[RightRobot] 安全离开: 移动到 {}", MILK_SEQUENCE_NAMES[pos_index]);

        // 创建非const副本以传递给API
        double joint_position[aubo_robot_namespace::ARM_DOF];
        for (int j = 0; j < aubo_robot_namespace::ARM_DOF; j++) {
            joint_position[j] = MILK_SEQUENCE_POSITIONS[pos_index][j];
        }

        int result = robot_service.robotServiceJointMove(joint_position, true);
        if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
            LOG_ERROR("[RightRobot] 移动到 {} 失败, 结果 = {}", MILK_SEQUENCE_NAMES[pos_index], result);
            return false;
        }

        LOG_INFO("[RightRobot] 成功移动到 {}", MILK_SEQUENCE_NAMES[pos_index]);
    }

    return true;
}

// 执行摇奶安全离开序列
bool execute_shake_milk_exit_sequence(const double* current_joint_pos, ServiceInterface& robot_service) {
    // 找到当前位置最接近序列中的哪个位置
    int closest_position = 0;
    double min_distance = std::numeric_limits<double>::max();

    for (int i = 0; i < SHAKE_MILK_SEQUENCE_LENGTH; i++) {
        double distance = calculate_joint_distance(current_joint_pos, SHAKE_MILK_SEQUENCE_POSITIONS[i]);
        if (distance < min_distance) {
            min_distance = distance;
            closest_position = i;
        }
    }

    LOG_INFO("[RightRobot] 当前位置最接近 {}", SHAKE_MILK_SEQUENCE_NAMES[closest_position]);

    // 摇奶序列的安全离开：移动到清洗序列的第一个位置
    LOG_INFO("[RightRobot] 摇奶安全离开: 移动到清洗位置1");

    double joint_position[aubo_robot_namespace::ARM_DOF];
    for (int j = 0; j < aubo_robot_namespace::ARM_DOF; j++) {
        joint_position[j] = CLEANING_SEQUENCE_POSITIONS[0][j];
    }

    int result = robot_service.robotServiceJointMove(joint_position, true);
    if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
        LOG_ERROR("[RightRobot] 移动到清洗位置1失败, 结果 = {}", result);
        return false;
    }

    LOG_INFO("[RightRobot] 成功移动到清洗位置1");
    return true;
}

// 执行拉花安全离开序列
bool execute_latte_art_exit_sequence(const double* /*current_joint_pos*/, ServiceInterface& robot_service) {
    LOG_INFO("[RightRobot] 拉花安全离开: 移动到倾倒位置1");

    double joint_position[aubo_robot_namespace::ARM_DOF];
    for (int j = 0; j < aubo_robot_namespace::ARM_DOF; j++) {
        joint_position[j] = POUR_MILK_SEQUENCE_POSITIONS[0][j];
    }

    int result = robot_service.robotServiceJointMove(joint_position, true);
    if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
        LOG_ERROR("[RightRobot] 移动到倾倒位置1失败, 结果 = {}", result);
        return false;
    }

    LOG_INFO("[RightRobot] 成功移动到倾倒位置1");
    return true;
}

// 执行倾倒牛奶安全离开序列
bool execute_pour_milk_exit_sequence(const double* current_joint_pos, ServiceInterface& robot_service) {
    // 找到当前位置最接近序列中的哪个位置
    int closest_position = 0;
    double min_distance = std::numeric_limits<double>::max();

    for (int i = 0; i < POUR_MILK_SEQUENCE_LENGTH; i++) {
        double distance = calculate_joint_distance(current_joint_pos, POUR_MILK_SEQUENCE_POSITIONS[i]);
        if (distance < min_distance) {
            min_distance = distance;
            closest_position = i;
        }
    }

    LOG_INFO("[RightRobot] 当前位置最接近 {}", POUR_MILK_SEQUENCE_NAMES[closest_position]);

    // 倾倒牛奶序列的安全离开：移动到清洗序列的第一个位置
    LOG_INFO("[RightRobot] 倾倒牛奶安全离开: 移动到清洗位置1");

    double joint_position[aubo_robot_namespace::ARM_DOF];
    for (int j = 0; j < aubo_robot_namespace::ARM_DOF; j++) {
        joint_position[j] = CLEANING_SEQUENCE_POSITIONS[0][j];
    }

    int result = robot_service.robotServiceJointMove(joint_position, true);
    if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
        LOG_ERROR("[RightRobot] 移动到清洗位置1失败, 结果 = {}", result);
        return false;
    }

    LOG_INFO("[RightRobot] 成功移动到清洗位置1");
    return true;
}

// 执行清洗安全离开序列
bool execute_cleaning_exit_sequence(const double* current_joint_pos, ServiceInterface& robot_service) {
    // 找到当前位置最接近序列中的哪个位置
    int closest_position = 0;
    double min_distance = std::numeric_limits<double>::max();

    for (int i = 0; i < CLEANING_SEQUENCE_LENGTH; i++) {
        double distance = calculate_joint_distance(current_joint_pos, CLEANING_SEQUENCE_POSITIONS[i]);
        if (distance < min_distance) {
            min_distance = distance;
            closest_position = i;
        }
    }

    LOG_INFO("[RightRobot] 当前位置最接近 {}", CLEANING_SEQUENCE_NAMES[closest_position]);

    // 根据当前位置决定安全离开路径
    int exit_path[4] = {0, 0, 0, 0};
    int exit_path_length = 0;

    if (closest_position == CLEANING_SEQUENCE_LENGTH - 1) {
        // 如果已经在最后一个清洗位置，无需额外动作
        LOG_INFO("[RightRobot] 机器人已在清洗最后位置，无需执行离开动作");
        return true;
    } else {
        // 移动到清洗序列的最后位置
        for (int i = closest_position + 1; i < CLEANING_SEQUENCE_LENGTH; i++) {
            exit_path[exit_path_length++] = i;
        }
    }

    // 执行安全离开路径
    for (int i = 0; i < exit_path_length; i++) {
        int pos_index = exit_path[i];
        LOG_INFO("[RightRobot] 清洗安全离开: 移动到 {}", CLEANING_SEQUENCE_NAMES[pos_index]);

        double joint_position[aubo_robot_namespace::ARM_DOF];
        for (int j = 0; j < aubo_robot_namespace::ARM_DOF; j++) {
            joint_position[j] = CLEANING_SEQUENCE_POSITIONS[pos_index][j];
        }

        int result = robot_service.robotServiceJointMove(joint_position, true);
        if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
            LOG_ERROR("[RightRobot] 移动到 {} 失败, 结果 = {}", CLEANING_SEQUENCE_NAMES[pos_index], result);
            return false;
        }

        LOG_INFO("[RightRobot] 成功移动到 {}", CLEANING_SEQUENCE_NAMES[pos_index]);
    }

    return true;
}

class RightRobot::Impl {
public:
    Impl() = default;

    ~Impl() {
        LOG_INFO("[RightRobot] ~Impl");
        // 析构时自动断开连接并关闭机器人
        if (initialized_) {
            shutdown();
        }
        if (connected_) {
            disconnect();
        }
    }

    static void robot_event_callback(const aubo_robot_namespace::RobotEventInfo *eventInfo, void */*arg*/) {
        LOG_INFO("[RightRobot] 机器人事件: type -> {}, code -> {}, content -> {}", static_cast<int>(eventInfo->eventType), eventInfo->eventCode, eventInfo->eventContent);
    }

    bool init() {
        LOG_INFO("[RightRobot] 初始化");

        // 首先连接到机器人
        if (!connect(RIGHT_ROBOT_HOST, RIGHT_ROBOT_PORT, "aubo", "123456")) {
            LOG_ERROR("[RightRobot] 连接失败，无法初始化");
            return false;
        }

        // 启动机器人
        aubo_robot_namespace::ROBOT_SERVICE_STATE state;
        aubo_robot_namespace::ToolDynamicsParam tool_dynamics_param{};

        int result = robot_service_.rootServiceRobotStartup(tool_dynamics_param,
                                                            13, true, true, 1000, state);
        if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
            LOG_ERROR("[RightRobot] 启动失败, 结果 = {}", result);
            disconnect(); // 启动失败时断开连接
            return false;
        }

        robot_service_.robotServiceRegisterRobotEventInfoCallback(robot_event_callback, nullptr);

        LOG_INFO("[RightRobot] 启动成功");

        // 初始化运动配置
        result = robot_service_.robotServiceInitGlobalMoveProfile();
        if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
            LOG_ERROR("[RightRobot] 初始化全局运动配置失败, 结果 = {}", result);
            return false;
        }

        // 设置默认运动参数
        configure_movement_parameters();

        // 加载拉花配置
        if (!latte_art_config_.load_from_directory("../share/config/latte_art")) {
            LOG_WARN("[RightRobot] 无法加载拉花配置目录，请检查配置文件");
        } else {
            LOG_INFO("[RightRobot] 拉花配置加载成功");
        }

        initialized_ = true;
        return true;
    }

    bool move_to_home() {
        LOG_INFO("[RightRobot] 移动到初始位置");

        if (!check_robot_state()) {
            return false;
        }

        // 初始化运动配置
        auto result = robot_service_.robotServiceInitGlobalMoveProfile();
        if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
            LOG_ERROR("[RightRobot] 初始化全局运动配置失败, 结果 = {}", result);
            return false;
        }

        // 设置默认运动参数
        configure_movement_parameters();

        // 获取当前关节角
        aubo_robot_namespace::JointParam joint_angle;
        result = robot_service_.robotServiceGetJointAngleInfo(joint_angle);
        if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
            LOG_ERROR("[RightRobot] 获取当前关节角失败, 结果 = {}", result);
            return false;
        }

        // 记录当前位置
        LOG_INFO("[RightRobot] 当前关节角:");
        for (int i = 0; i < aubo_robot_namespace::ARM_DOF; i++) {
            LOG_INFO("[RightRobot] 关节 {}: {}", i, joint_angle.jointPos[i]);
        }

        // 检查是否已经在Home位置
        bool at_home_position = is_at_home_position(joint_angle.jointPos);
        if (at_home_position) {
            LOG_INFO("[RightRobot] 机器人已在Home位置");
            return true;
        }

        // 检查是否在取奶过程中
        bool in_milk_sequence = is_in_milk_sequence(joint_angle.jointPos);
        if (in_milk_sequence) {
            LOG_INFO("[RightRobot] 检测到机器人在取奶过程中，执行安全离开动作");
            if (!execute_milk_exit_sequence(joint_angle.jointPos, robot_service_)) {
                LOG_ERROR("[RightRobot] 执行取奶安全离开动作失败");
                return false;
            }
        }

        // 检查是否在摇奶过程中
        bool in_shake_milk_sequence = is_in_shake_milk_sequence(joint_angle.jointPos);
        if (in_shake_milk_sequence) {
            LOG_INFO("[RightRobot] 检测到机器人在摇奶过程中，执行安全离开动作");
            if (!execute_shake_milk_exit_sequence(joint_angle.jointPos, robot_service_)) {
                LOG_ERROR("[RightRobot] 执行摇奶安全离开动作失败");
                return false;
            }
        }

        // 检查是否在拉花过程中
        bool in_latte_art_sequence = is_in_latte_art_sequence(joint_angle.jointPos);
        if (in_latte_art_sequence) {
            LOG_INFO("[RightRobot] 检测到机器人在拉花过程中，执行安全离开动作");
            if (!execute_latte_art_exit_sequence(joint_angle.jointPos, robot_service_)) {
                LOG_ERROR("[RightRobot] 执行拉花安全离开动作失败");
                return false;
            }
        }

        // 检查是否在倾倒牛奶过程中
        bool in_pour_milk_sequence = is_in_pour_milk_sequence(joint_angle.jointPos);
        if (in_pour_milk_sequence) {
            LOG_INFO("[RightRobot] 检测到机器人在倾倒牛奶过程中，执行安全离开动作");
            if (!execute_pour_milk_exit_sequence(joint_angle.jointPos, robot_service_)) {
                LOG_ERROR("[RightRobot] 执行倾倒牛奶安全离开动作失败");
                return false;
            }
        }

        // 检查是否在清洗过程中
        bool in_cleaning_sequence = is_in_cleaning_sequence(joint_angle.jointPos);
        if (in_cleaning_sequence) {
            LOG_INFO("[RightRobot] 检测到机器人在清洗过程中，执行安全离开动作");
            if (!execute_cleaning_exit_sequence(joint_angle.jointPos, robot_service_)) {
                LOG_ERROR("[RightRobot] 执行清洗安全离开动作失败");
                return false;
            }
        }

        // 检查是否在离开Home过程中
        bool in_leave_home_sequence = is_in_leave_home_sequence(joint_angle.jointPos);
        if (in_leave_home_sequence) {
            LOG_INFO("[RightRobot] 检测到机器人在离开Home过程中，直接移动到Home位置");
            // 离开Home序列位置相对安全，可以直接移动到Home
        }

        // 检查是否在清洗最后位置，如果不是则给出警告但仍允许移动
        bool at_cleaning_final_position = is_at_cleaning_final_position(joint_angle.jointPos);
        if (!at_cleaning_final_position && !in_leave_home_sequence && !at_home_position) {
            LOG_WARN("[RightRobot] 当前位置不在清洗最后位置附近，但仍允许移动到Home");
            // 不再强制返回false，允许移动到Home但给出警告
        }

        // 移动到Home位置
        LOG_INFO("[RightRobot] 移动到Home位置");
        double home_joint_angle[aubo_robot_namespace::ARM_DOF];
        for (int i = 0; i < aubo_robot_namespace::ARM_DOF; i++) {
            home_joint_angle[i] = HOME_POSITION[i];
        }
        result = robot_service_.robotServiceJointMove(home_joint_angle, true);
        if (result == aubo_robot_namespace::InterfaceCallSuccCode) {
            LOG_INFO("[RightRobot] 移动到初始位置成功");
            return true;
        } else {
            LOG_ERROR("[RightRobot] 移动到初始位置失败, 结果 = {}", result);
            return false;
        }
    }

    bool execute_leave_home_sequence() {
        LOG_INFO("[RightRobot] 执行离开Home前往工作准备位置序列");

        if (!check_robot_state()) {
            return false;
        }

        // 执行序列中的每个动作
        for (int i = 0; i < LEAVE_HOME_SEQUENCE_LENGTH; i++) {
            LOG_INFO("[RightRobot] 移动到 {}", LEAVE_HOME_SEQUENCE_NAMES[i]);

            // 创建非const副本以传递给API
            double joint_position[aubo_robot_namespace::ARM_DOF];
            for (int j = 0; j < aubo_robot_namespace::ARM_DOF; j++) {
                joint_position[j] = LEAVE_HOME_SEQUENCE_POSITIONS[i][j];
            }

            // 初始化运动配置
            auto result = robot_service_.robotServiceInitGlobalMoveProfile();
            if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
                LOG_ERROR("[RightRobot] 初始化全局运动配置失败, 结果 = {}", result);
                return false;
            }

            // 设置默认运动参数
            configure_movement_parameters();
            result = robot_service_.robotServiceJointMove(joint_position, true);
            if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
                LOG_ERROR("[RightRobot] 移动到 {} 失败, 结果 = {}", LEAVE_HOME_SEQUENCE_NAMES[i], result);
                return false;
            }

            LOG_INFO("[RightRobot] 成功移动到 {}", LEAVE_HOME_SEQUENCE_NAMES[i]);
        }

        LOG_INFO("[RightRobot] 离开Home前往工作准备位置序列执行完成");
        return true;
    }

    bool execute_get_milk_sequence() {
        LOG_INFO("[RightRobot] 执行取奶序列");

        if (!check_robot_state()) {
            return false;
        }

        // 获取当前关节角
        aubo_robot_namespace::JointParam joint_angle;
        int result = robot_service_.robotServiceGetJointAngleInfo(joint_angle);
        if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
            LOG_ERROR("[RightRobot] 获取当前关节角失败, 结果 = {}", result);
            return false;
        }

        // 检查是否在工作准备位置，如果不是则先执行离开Home序列
        bool at_work_ready_position = is_at_work_ready_position(joint_angle.jointPos);
        if (!at_work_ready_position) {
            LOG_INFO("[RightRobot] 不在工作准备位置，先执行离开Home序列");
            if (!execute_leave_home_sequence()) {
                LOG_ERROR("[RightRobot] 执行离开Home序列失败");
                return false;
            }
        } else {
            LOG_INFO("[RightRobot] 已在工作准备位置，直接开始取奶");
        }

        // 执行序列中的每个动作
        for (int i = 0; i < MILK_SEQUENCE_LENGTH; i++) {
            LOG_INFO("[RightRobot] 移动到 {}", MILK_SEQUENCE_NAMES[i]);

            // 创建非const副本以传递给API
            double joint_position[aubo_robot_namespace::ARM_DOF];
            for (int j = 0; j < aubo_robot_namespace::ARM_DOF; j++) {
                joint_position[j] = MILK_SEQUENCE_POSITIONS[i][j];
            }

            // 初始化运动配置
            auto result = robot_service_.robotServiceInitGlobalMoveProfile();
            if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
                LOG_ERROR("[RightRobot] 初始化全局运动配置失败, 结果 = {}", result);
                return false;
            }

            // 设置默认运动参数
            configure_movement_parameters();
            result = robot_service_.robotServiceJointMove(joint_position, true);
            if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
                LOG_ERROR("[RightRobot] 移动到 {} 失败, 结果 = {}", MILK_SEQUENCE_NAMES[i], result);
                return false;
            }

            LOG_INFO("[RightRobot] 成功移动到 {}", MILK_SEQUENCE_NAMES[i]);


            if (i == 2) {
                std::this_thread::sleep_for(std::chrono::seconds(30));
            }
        }

        return true;
    }

    bool execute_shake_milk_sequence() {
        LOG_INFO("[RightRobot] 执行摇奶序列");

        return true;

        if (!check_robot_state()) {
            return false;
        }

        // 移动到第一个点
        LOG_INFO("[RightRobot] 移动到第一个路点");
        double first_point[aubo_robot_namespace::ARM_DOF];
        for (int j = 0; j < aubo_robot_namespace::ARM_DOF; j++) {
            first_point[j] = SHAKE_MILK_SEQUENCE_POSITIONS[0][j];
        }
        int result = robot_service_.robotServiceJointMove(first_point, true);
        if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
            LOG_ERROR("[RightRobot] 移动到第一个路点失败, 结果 = {}", result);
            return false;
        }

        // 初始化全局运动配置
        result = robot_service_.robotServiceInitGlobalMoveProfile();
        if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
            LOG_ERROR("[RightRobot] 初始化全局运动配置失败, 结果 = {}", result);
            return false;
        }
        // 清空之前的路点
        robot_service_.robotServiceClearGlobalWayPointVector();

        // 添加所有摇奶路点
        for (size_t i = 0; i < SHAKE_MILK_SEQUENCE_LENGTH; i++) {
            const auto& waypoint = SHAKE_MILK_SEQUENCE_POSITIONS[i];
            aubo_robot_namespace::wayPoint_S way_point;
            for (int j = 0; j < aubo_robot_namespace::ARM_DOF; j++) {
                way_point.jointpos[j] = waypoint[j];
            }



            int result = robot_service_.robotServiceAddGlobalWayPoint(way_point);
            if (result == aubo_robot_namespace::InterfaceCallSuccCode) {
                // LOG_INFO("[LeftRobot] 添加摇奶路点成功: 路点{}", i+1);
            } else {
                LOG_ERROR("[RightRobot] 添加摇奶路点失败: 路点{}, 结果 = {}", i+1, result);
                return false;
            }
        }

        // 设置交融半径
        robot_service_.robotServiceSetGlobalBlendRadius(0.02);

        aubo_robot_namespace::JointVelcAccParam joint_max_acc;
        joint_max_acc.jointPara[0] = 1.0;
        joint_max_acc.jointPara[1] = 1.74533;
        joint_max_acc.jointPara[2] = 0.436332;
        joint_max_acc.jointPara[3] = 0.436332;
        joint_max_acc.jointPara[4] = 0.436332;
        joint_max_acc.jointPara[5] = 0.436332;
        robot_service_.robotServiceSetGlobalMoveJointMaxAcc(joint_max_acc);
        aubo_robot_namespace::JointVelcAccParam joint_max_velc;
        joint_max_velc.jointPara[0] = 0.19635;
        joint_max_velc.jointPara[1] = 1.74533;
        joint_max_velc.jointPara[2] = 0.436332;
        joint_max_velc.jointPara[3] = 0.436332;
        joint_max_velc.jointPara[4] = 0.436332;
        joint_max_velc.jointPara[5] = 0.436332;
        robot_service_.robotServiceSetGlobalMoveJointMaxVelc(joint_max_velc);

        robot_service_.robotServiceSetGlobalCircularLoopTimes(12);

        // 执行轨迹运动
        LOG_INFO("[RightRobot] 开始执行摇奶运动，共 {} 个路点", SHAKE_MILK_SEQUENCE_LENGTH);
        result = robot_service_.robotServiceTrackMove(aubo_robot_namespace::ARC_CIR, true);
        if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
            LOG_ERROR("[RightRobot] 摇奶运动失败, 结果 = {}", result);
            return false;
        }

        LOG_INFO("[RightRobot] 摇奶运动执行成功");
        return true;
    }

    bool prepare_for_latte_art() {
        LOG_INFO("[RightRobot] 准备拉花位置");

        if (!check_robot_state()) {
            return false;
        }

        // 执行序列中的每个动作
        for (int i = 0; i < LATTE_ART_SEQUENCE_LENGTH; i++) {
            LOG_INFO("[RightRobot] 移动到 {}", LATTE_ART_SEQUENCE_NAMES[i]);

            // 初始化运动配置
            auto result = robot_service_.robotServiceInitGlobalMoveProfile();
            if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
                LOG_ERROR("[RightRobot] 初始化全局运动配置失败, 结果 = {}", result);
                return false;
            }

            // 设置默认运动参数
            configure_movement_parameters();

            // 创建非const副本以传递给API
            double joint_position[aubo_robot_namespace::ARM_DOF];
            for (int j = 0; j < aubo_robot_namespace::ARM_DOF; j++) {
                joint_position[j] = LATTE_ART_SEQUENCE_POSITIONS[i][j];
            }
            result = robot_service_.robotServiceJointMove(joint_position, true);
            if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
                LOG_ERROR("[RightRobot] 移动到 {} 失败, 结果 = {}", LATTE_ART_SEQUENCE_NAMES[i], result);
                return false;
            }

            LOG_INFO("[RightRobot] 成功移动到 {}", LATTE_ART_SEQUENCE_NAMES[i]);
        }

        LOG_INFO("[RightRobot] 拉花准备位置完成");
        return true;
    }

    bool execute_latte_art_sequence(LatteArtType art_type) {
        LOG_INFO("[RightRobot] 执行拉花动作: {}", get_latte_art_name(art_type));

        if (!check_robot_state()) {
            return false;
        }

        // 如果不需要拉花，直接返回成功
        if (art_type == LatteArtType::NONE) {
            LOG_INFO("[RightRobot] 无需拉花");
            return true;
        }

        // 尝试从配置文件加载轨迹
        if (latte_art_config_.is_loaded()) {
            auto points = latte_art_config_.get_right_robot_waypoints(art_type);
            if (!points.empty()) {
                auto motion_params = latte_art_config_.get_motion_parameters(art_type);
                return execute_trajectory_from_config(points, motion_params);
            } else {
                LOG_WARN("[RightRobot] 配置中未找到 {} 轨迹", get_latte_art_name(art_type));
            }
        } else {
            LOG_WARN("[RightRobot] 拉花配置未加载成功");
        }
        return false;
    }

    // 从配置执行轨迹
    bool execute_trajectory_from_config(const std::vector<std::vector<double>>& points,
                                       const MotionParameters& motion_params) {
		LOG_INFO("[RightRobot] 从配置执行轨迹");

        if (points.empty()) {
            LOG_ERROR("[RightRobot] 轨迹为空");
            return false;
        }

        // 移动到第一个点
        LOG_INFO("[RightRobot] 移动到第一个路点");
        double first_point[aubo_robot_namespace::ARM_DOF];
        for (int j = 0; j < aubo_robot_namespace::ARM_DOF; j++) {
            first_point[j] = points[0][j];
        }
        int result = robot_service_.robotServiceJointMove(first_point, true);
        if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
            LOG_ERROR("[RightRobot] 移动到第一个路点失败, 结果 = {}", result);
            return false;
        }

        // 初始化全局运动配置
        result = robot_service_.robotServiceInitGlobalMoveProfile();
        if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
            LOG_ERROR("[RightRobot] 初始化全局运动配置失败, 结果 = {}", result);
            return false;
        }

        // 清空之前的路点
        robot_service_.robotServiceClearGlobalWayPointVector();

        // 添加所有拉花路点
        for (size_t i = 0; i < points.size(); i++) {
            const auto& waypoint = points[i];
            aubo_robot_namespace::wayPoint_S way_point;
            for (int j = 0; j < aubo_robot_namespace::ARM_DOF && j < (int)waypoint.size(); j++) {
                way_point.jointpos[j] = waypoint[j];
            }

            int result = robot_service_.robotServiceAddGlobalWayPoint(way_point);
            if (result == aubo_robot_namespace::InterfaceCallSuccCode) {
                // LOG_INFO("[LeftRobot] 添加拉花路点成功: 路点{}", i+1);
            } else {
                LOG_ERROR("[RightRobot] 添加拉花路点失败: 路点{}, 结果 = {}", i+1, result);
                return false;
            }
        }

        // 设置交融半径
        result = robot_service_.robotServiceSetGlobalBlendRadius(motion_params.blend_radius);
        if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
            LOG_ERROR("[RightRobot] 设置交融半径失败, 结果 = {}", result);
            return false;
        }

        // 设置从配置文件读取的运动参数
        result = robot_service_.robotServiceSetGlobalMoveEndMaxLineAcc(motion_params.line_acceleration);
        if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
            LOG_ERROR("[RightRobot] 设置末端执行器线性加速度失败, 结果 = {}", result);
            return false;
        }
        result = robot_service_.robotServiceSetGlobalMoveEndMaxAngleAcc(motion_params.angle_acceleration);
        if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
            LOG_ERROR("[RightRobot] 设置末端执行器角度加速度失败, 结果 = {}", result);
            return false;
        }
        result = robot_service_.robotServiceSetGlobalMoveEndMaxLineVelc(motion_params.line_velocity);
        if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
            LOG_ERROR("[RightRobot] 设置末端执行器线性速度失败, 结果 = {}", result);
            return false;
        }
        result = robot_service_.robotServiceSetGlobalMoveEndMaxAngleVelc(motion_params.angle_velocity);
        if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
            LOG_ERROR("[RightRobot] 设置末端执行器角度速度失败, 结果 = {}", result);
            return false;
        }

        aubo_robot_namespace::JointVelcAccParam joint_max_acc;
        for (int i = 0; i < aubo_robot_namespace::ARM_DOF; i++) {
            joint_max_acc.jointPara[i] = motion_params.angle_acceleration;
        }
        result = robot_service_.robotServiceSetGlobalMoveJointMaxAcc(joint_max_acc);
        if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
            LOG_ERROR("[RightRobot] 设置关节运动最大加速度失败, 结果 = {}", result);
            return false;
        }

        aubo_robot_namespace::JointVelcAccParam joint_max_velc;
        for (int i = 0; i < aubo_robot_namespace::ARM_DOF; i++) {
            joint_max_velc.jointPara[i] = motion_params.angle_velocity;
        }
        result = robot_service_.robotServiceSetGlobalMoveJointMaxVelc(joint_max_velc);
        if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
            LOG_ERROR("[RightRobot] 设置关节运动最大速度失败, 结果 = {}", result);
            return false;
        }

        // 执行轨迹运动
        LOG_INFO("[RightRobot] 开始执行轨迹运动，共 {} 个路点", points.size());
        result = robot_service_.robotServiceTrackMove(aubo_robot_namespace::JOINT_GNUBSPLINEINTP, true);
        if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
            LOG_ERROR("[RightRobot] 轨迹运动失败, 结果 = {}", result);
            return false;
        }

        LOG_INFO("[RightRobot] 轨迹运动执行成功");
        return true;
    }

    bool execute_pour_milk_sequence() {
        LOG_INFO("[RightRobot] 执行倾倒牛奶序列");

        if (!check_robot_state()) {
            return false;
        }

        // 执行序列中的每个动作
        for (int i = 0; i < POUR_MILK_SEQUENCE_LENGTH; i++) {
            LOG_INFO("[RightRobot] 移动到 {}", POUR_MILK_SEQUENCE_NAMES[i]);

            // 创建非const副本以传递给API
            double joint_position[aubo_robot_namespace::ARM_DOF];
            for (int j = 0; j < aubo_robot_namespace::ARM_DOF; j++) {
                joint_position[j] = POUR_MILK_SEQUENCE_POSITIONS[i][j];
            }

            // 初始化运动配置
            auto result = robot_service_.robotServiceInitGlobalMoveProfile();
            if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
                LOG_ERROR("[RightRobot] 初始化全局运动配置失败, 结果 = {}", result);
                return false;
            }

            // 设置默认运动参数
            configure_movement_parameters();
            result = robot_service_.robotServiceJointMove(joint_position, true);
            if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
                LOG_ERROR("[RightRobot] 移动到 {} 失败, 结果 = {}", POUR_MILK_SEQUENCE_NAMES[i], result);
                return false;
            }

            LOG_INFO("[RightRobot] 成功移动到 {}", POUR_MILK_SEQUENCE_NAMES[i]);
        }

        LOG_INFO("[RightRobot] 倾倒牛奶序列执行完成");
        return true;
    }

    bool execute_cleaning_sequence() {
        LOG_INFO("[RightRobot] 执行清洗序列");

        if (!check_robot_state()) {
            return false;
        }

        // 执行序列中的每个动作
        for (int i = 0; i < CLEANING_SEQUENCE_LENGTH; i++) {
            LOG_INFO("[RightRobot] 移动到 {}", CLEANING_SEQUENCE_NAMES[i]);

            // 创建非const副本以传递给API
            double joint_position[aubo_robot_namespace::ARM_DOF];
            for (int j = 0; j < aubo_robot_namespace::ARM_DOF; j++) {
                joint_position[j] = CLEANING_SEQUENCE_POSITIONS[i][j];
            }

            // 初始化运动配置
            auto result = robot_service_.robotServiceInitGlobalMoveProfile();
            if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
                LOG_ERROR("[RightRobot] 初始化全局运动配置失败, 结果 = {}", result);
                return false;
            }

            // 设置默认运动参数
            configure_movement_parameters();
            result = robot_service_.robotServiceJointMove(joint_position, true);
            if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
                LOG_ERROR("[RightRobot] 移动到 {} 失败, 结果 = {}", CLEANING_SEQUENCE_NAMES[i], result);
                return false;
            }

            LOG_INFO("[RightRobot] 成功移动到 {}", CLEANING_SEQUENCE_NAMES[i]);
        }

        LOG_INFO("[RightRobot] 清洗序列执行完成");
        return true;
    }

    // 关闭机器人
    bool shutdown() {
        if (connected_ && initialized_) {
            LOG_INFO("[RightRobot] 关闭机器人");
            // robot_service_.robotServiceRobotShutdown();
            initialized_ = false;
            return true;
        }
        return false;
    }

    // 紧急停止
    bool emergency_stop() {
        LOG_WARN("[RightRobot] 执行紧急停止");

        if (!connected_) {
            LOG_ERROR("[RightRobot] 紧急停止失败: 未连接");
            return false;
        }

        try {
            // 停止当前运动
            auto result = robot_service_.robotMoveStop();
            if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
                LOG_ERROR("[RightRobot] 紧急停止失败, 结果 = {}", result);
                return false;
            }

            LOG_INFO("[RightRobot] 紧急停止成功");
            return true;
        } catch (const std::exception& e) {
            LOG_ERROR("[RightRobot] 紧急停止异常: {}", e.what());
            return false;
        }
    }

private:
    // 连接到机器人
    bool connect(const std::string& host, int port, const std::string& username, const std::string& password) {
        LOG_INFO("[RightRobot] 连接到 {}:{}", host, port);

        try {
            int result = robot_service_.robotServiceLogin(host.c_str(), port, username.c_str(), password.c_str());
            if (result == aubo_robot_namespace::InterfaceCallSuccCode) {
                LOG_INFO("[RightRobot] 登录成功");
                connected_ = true;
            } else {
                LOG_ERROR("[RightRobot] 登录失败, 结果 = {}", result);
                connected_ = false;
            }
        } catch (std::exception & e) {
            LOG_ERROR("[RightRobot] 登录异常: {}", e.what());
            connected_ = false;
        }

        return connected_;
    }

    // 断开与机器人的连接
    bool disconnect() {
        if (connected_) {
            LOG_INFO("[RightRobot] 断开连接");
            robot_service_.robotServiceLogout();
            connected_ = false;
        }
        return true;
    }



    // 配置运动参数
    void configure_movement_parameters() {
        // 设置关节运动最大加速度
        aubo_robot_namespace::JointVelcAccParam joint_max_acc;
        for (int i = 0; i < aubo_robot_namespace::ARM_DOF; i++) {
            joint_max_acc.jointPara[i] = 2.0;
        }
        auto result = robot_service_.robotServiceSetGlobalMoveJointMaxAcc(joint_max_acc);
        if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
            LOG_ERROR("[RightRobot] 设置关节运动最大加速度失败, 结果 = {}", result);
        }

        // 设置关节运动最大速度
        aubo_robot_namespace::JointVelcAccParam joint_max_velc;
        for (int i = 0; i < aubo_robot_namespace::ARM_DOF; i++) {
            joint_max_velc.jointPara[i] = 2.0;
        }
        result = robot_service_.robotServiceSetGlobalMoveJointMaxVelc(joint_max_velc);
        if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
            LOG_ERROR("[RightRobot] 设置关节运动最大速度失败, 结果 = {}", result);
        }

        // 设置末端执行器运动参数
        result = robot_service_.robotServiceSetGlobalMoveEndMaxLineAcc(0.436332);
        if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
            LOG_ERROR("[RightRobot] 设置末端执行器线性加速度失败, 结果 = {}", result);
        }
        result = robot_service_.robotServiceSetGlobalMoveEndMaxAngleAcc(2.0);
        if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
            LOG_ERROR("[RightRobot] 设置末端执行器角度加速度失败, 结果 = {}", result);
        }
        result = robot_service_.robotServiceSetGlobalMoveEndMaxLineVelc(0.436332);
        if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
            LOG_ERROR("[RightRobot] 设置末端执行器线性速度失败, 结果 = {}", result);
        }
        result = robot_service_.robotServiceSetGlobalMoveEndMaxAngleVelc(2.0);
        if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
            LOG_ERROR("[RightRobot] 设置末端执行器角度速度失败, 结果 = {}", result);
        }
    }

    // 检查机器人状态
    bool check_robot_state() {
        if (!connected_) {
            LOG_ERROR("[RightRobot] 无法移动: 未连接");
            return false;
        }

        if (!initialized_) {
            LOG_ERROR("[RightRobot] 无法移动: 未初始化");
            return false;
        }

        return true;
    }

    ServiceInterface robot_service_;
    LatteArtConfig latte_art_config_;
    bool connected_ = false;
    bool initialized_ = false;
};

// RightRobot 公共接口实现
RightRobot::RightRobot() : DeviceBase("RightRobot") {
    impl_ = std::make_unique<Impl>();
}

RightRobot::~RightRobot() = default;

bool RightRobot::init() {
    bool success = impl_->init();
    set_initialized(success);
    set_connected(success);
    return success;
}

bool RightRobot::move_to_home() {
    return impl_->move_to_home();
}

bool RightRobot::move_to_ready() {
    // 移动到准备位置（通常是初始位置的一个变体）
    return impl_->move_to_home();
}

bool RightRobot::move_to_milk_outlet() {
    // 移动到牛奶出口位置（取奶序列的第一步）
    return impl_->execute_get_milk_sequence();
}

bool RightRobot::move_to_latte_art() {
    // 移动到拉花位置
    return impl_->prepare_for_latte_art();
}

bool RightRobot::move_to_pour_milk() {
    // 移动到倒奶位置
    return impl_->execute_pour_milk_sequence();
}

bool RightRobot::move_to_cleaning() {
    // 移动到清洁位置
    return impl_->execute_cleaning_sequence();
}

bool RightRobot::get_milk() {
    return impl_->execute_get_milk_sequence();
}

bool RightRobot::shake_milk(int shake_rounds) {
    // 这里可以根据shake_rounds参数调整摇奶的圈数
    // 目前使用默认实现
    return impl_->execute_shake_milk_sequence();
}

bool RightRobot::prepare_for_latte_art() {
    return impl_->prepare_for_latte_art();
}

bool RightRobot::do_latte_art(LatteArtType art_type) {
    return impl_->execute_latte_art_sequence(art_type);
}

bool RightRobot::pour_remaining_milk() {
    return impl_->execute_pour_milk_sequence();
}

bool RightRobot::start_cleaning() {
    LOG_INFO("[RightRobot] 开始清洁");
    // 这里可以实现开始清洁的具体逻辑
    return true;
}

bool RightRobot::stop_cleaning() {
    LOG_INFO("[RightRobot] 停止清洁");
    // 这里可以实现停止清洁的具体逻辑
    return true;
}

bool RightRobot::clean() {
    return impl_->execute_cleaning_sequence();
}

bool RightRobot::shutdown() {
    bool success = impl_->shutdown();
    if (success) {
        set_initialized(false);
        set_connected(false);
    }
    return success;
}

bool RightRobot::emergency_stop() {
    return impl_->emergency_stop();
}

} // namespace aubo
