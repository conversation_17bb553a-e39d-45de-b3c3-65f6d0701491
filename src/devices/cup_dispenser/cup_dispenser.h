/*
 * Copyright ©2015-2025 AUBO (Beijing) Robotics Technology Co., Ltd. All rights reserved.
 */

#ifndef AUBO_COFFEE_SERVICE_CUP_DISPENSER_H
#define AUBO_COFFEE_SERVICE_CUP_DISPENSER_H

#include "../device_base.h"
#include <memory>

namespace aubo {

/**
 * @enum CupType
 * @brief 杯子类型枚举
 */
enum class CupType {
    REGULAR,        ///< 普通杯
    LARGE,          ///< 大杯
    SMALL           ///< 小杯
};

/**
 * @class CupDispenser
 * @brief 杯子分配器类
 * 
 * 负责管理和分配不同类型的杯子
 */
class CupDispenser : public DeviceBase {
public:
    /**
     * @brief 构造函数
     */
    CupDispenser();

    /**
     * @brief 析构函数
     */
    ~CupDispenser();

    /**
     * @brief 初始化杯子分配器
     * @return 初始化成功返回true
     */
    bool init() override;

    /**
     * @brief 关闭杯子分配器
     * @return 关闭成功返回true
     */
    bool shutdown() override;

    /**
     * @brief 紧急停止
     * @return 紧急停止成功返回true
     */
    bool emergency_stop() override;

    /**
     * @brief 分配杯子
     * @param cup_type 杯子类型，默认为普通杯
     * @return 分配成功返回true
     */
    bool dispense_cup(CupType cup_type = CupType::REGULAR);

    /**
     * @brief 检查杯子库存
     * @param cup_type 杯子类型
     * @return 剩余杯子数量
     */
    int check_cup_inventory(CupType cup_type) const;

    /**
     * @brief 补充杯子
     * @param cup_type 杯子类型
     * @param count 补充数量
     * @return 补充成功返回true
     */
    bool refill_cups(CupType cup_type, int count);

    /**
     * @brief 获取杯子分配器状态
     * @return 状态信息字符串
     */
    std::string get_status() const;

private:
    class Impl;
    std::unique_ptr<Impl> impl_;
};

/**
 * @brief 杯子类型转字符串
 * @param cup_type 杯子类型
 * @return 字符串表示
 */
std::string cup_type_to_string(CupType cup_type);

/**
 * @brief 字符串转杯子类型
 * @param type_string 类型字符串
 * @return 杯子类型
 */
CupType string_to_cup_type(const std::string& type_string);

} // namespace aubo

#endif // AUBO_COFFEE_SERVICE_CUP_DISPENSER_H
