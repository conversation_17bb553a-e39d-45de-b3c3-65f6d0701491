/*
 * Copyright ©2015-2025 AUBO (Beijing) Robotics Technology Co., Ltd. All rights reserved.
 */

#include "device_manager.h"
#include <sstream>
#include <vector>

// 日志宏定义 - 简化版本，实际项目中应该使用统一的日志系统
#define LOG_INFO(msg, ...) printf("[DeviceManager] INFO: " msg "\n", ##__VA_ARGS__)
#define LOG_ERROR(msg, ...) printf("[DeviceManager] ERROR: " msg "\n", ##__VA_ARGS__)
#define LOG_WARN(msg, ...) printf("[DeviceManager] WARN: " msg "\n", ##__VA_ARGS__)

namespace aubo {

class DeviceManager::Impl {
public:
    Impl() {
        // 创建所有设备实例
        left_robot_ = std::make_shared<LeftRobot>();
        right_robot_ = std::make_shared<RightRobot>();
        cup_dispenser_ = std::make_shared<CupDispenser>();
        coffee_machine_ = std::make_shared<CoffeeMachine>();
        milk_container_cleaner_ = std::make_shared<MilkContainerCleaner>();
    }

    bool initialize_all_devices() {
        LOG_INFO("开始初始化所有设备");

        bool all_success = true;
        int success_count = 0;
        int total_count = 5;

        // 初始化左臂机器人
        if (left_robot_->init()) {
            LOG_INFO("左臂机器人初始化成功");
            success_count++;
        } else {
            LOG_ERROR("左臂机器人初始化失败");
            all_success = false;
        }

        // 初始化右臂机器人
        if (right_robot_->init()) {
            LOG_INFO("右臂机器人初始化成功");
            success_count++;
        } else {
            LOG_ERROR("右臂机器人初始化失败");
            all_success = false;
        }

        // 初始化杯子分配器
        if (cup_dispenser_->init()) {
            LOG_INFO("杯子分配器初始化成功");
            success_count++;
        } else {
            LOG_ERROR("杯子分配器初始化失败");
            all_success = false;
        }

        // 初始化咖啡机
        if (coffee_machine_->init()) {
            LOG_INFO("咖啡机初始化成功");
            success_count++;
        } else {
            LOG_ERROR("咖啡机初始化失败");
            all_success = false;
        }

        // 初始化牛奶容器清洁器
        if (milk_container_cleaner_->init()) {
            LOG_INFO("牛奶容器清洁器初始化成功");
            success_count++;
        } else {
            LOG_ERROR("牛奶容器清洁器初始化失败");
            all_success = false;
        }

        LOG_INFO("设备初始化完成: %d/%d 成功", success_count, total_count);
        return all_success;
    }

    bool shutdown_all_devices() {
        LOG_INFO("开始关闭所有设备");

        bool all_success = true;
        int success_count = 0;
        int total_count = 5;

        // 逆序关闭设备
        if (milk_container_cleaner_->shutdown()) {
            LOG_INFO("牛奶容器清洁器关闭成功");
            success_count++;
        } else {
            LOG_ERROR("牛奶容器清洁器关闭失败");
            all_success = false;
        }

        if (coffee_machine_->shutdown()) {
            LOG_INFO("咖啡机关闭成功");
            success_count++;
        } else {
            LOG_ERROR("咖啡机关闭失败");
            all_success = false;
        }

        if (cup_dispenser_->shutdown()) {
            LOG_INFO("杯子分配器关闭成功");
            success_count++;
        } else {
            LOG_ERROR("杯子分配器关闭失败");
            all_success = false;
        }

        if (right_robot_->shutdown()) {
            LOG_INFO("右臂机器人关闭成功");
            success_count++;
        } else {
            LOG_ERROR("右臂机器人关闭失败");
            all_success = false;
        }

        if (left_robot_->shutdown()) {
            LOG_INFO("左臂机器人关闭成功");
            success_count++;
        } else {
            LOG_ERROR("左臂机器人关闭失败");
            all_success = false;
        }

        LOG_INFO("设备关闭完成: %d/%d 成功", success_count, total_count);
        return all_success;
    }

    bool emergency_stop_all_devices() {
        LOG_WARN("紧急停止所有设备");

        bool all_success = true;
        int success_count = 0;
        int total_count = 5;

        // 紧急停止所有设备
        if (left_robot_->emergency_stop()) {
            success_count++;
        } else {
            LOG_ERROR("左臂机器人紧急停止失败");
            all_success = false;
        }

        if (right_robot_->emergency_stop()) {
            success_count++;
        } else {
            LOG_ERROR("右臂机器人紧急停止失败");
            all_success = false;
        }

        if (cup_dispenser_->emergency_stop()) {
            success_count++;
        } else {
            LOG_ERROR("杯子分配器紧急停止失败");
            all_success = false;
        }

        if (coffee_machine_->emergency_stop()) {
            success_count++;
        } else {
            LOG_ERROR("咖啡机紧急停止失败");
            all_success = false;
        }

        if (milk_container_cleaner_->emergency_stop()) {
            success_count++;
        } else {
            LOG_ERROR("牛奶容器清洁器紧急停止失败");
            all_success = false;
        }

        LOG_WARN("紧急停止完成: %d/%d 成功", success_count, total_count);
        return all_success;
    }

    std::shared_ptr<LeftRobot> get_left_robot() const {
        return left_robot_;
    }

    std::shared_ptr<RightRobot> get_right_robot() const {
        return right_robot_;
    }

    std::shared_ptr<CupDispenser> get_cup_dispenser() const {
        return cup_dispenser_;
    }

    std::shared_ptr<CoffeeMachine> get_coffee_machine() const {
        return coffee_machine_;
    }

    std::shared_ptr<MilkContainerCleaner> get_milk_container_cleaner() const {
        return milk_container_cleaner_;
    }



private:
    std::shared_ptr<LeftRobot> left_robot_;
    std::shared_ptr<RightRobot> right_robot_;
    std::shared_ptr<CupDispenser> cup_dispenser_;
    std::shared_ptr<CoffeeMachine> coffee_machine_;
    std::shared_ptr<MilkContainerCleaner> milk_container_cleaner_;
    

};

// DeviceManager 公共接口实现
DeviceManager::DeviceManager() {
    impl_ = std::make_unique<Impl>();
}

DeviceManager::~DeviceManager() = default;

bool DeviceManager::initialize_all_devices() {
    return impl_->initialize_all_devices();
}

bool DeviceManager::shutdown_all_devices() {
    return impl_->shutdown_all_devices();
}

bool DeviceManager::emergency_stop_all_devices() {
    return impl_->emergency_stop_all_devices();
}

std::shared_ptr<LeftRobot> DeviceManager::get_left_robot() const {
    return impl_->get_left_robot();
}

std::shared_ptr<RightRobot> DeviceManager::get_right_robot() const {
    return impl_->get_right_robot();
}

std::shared_ptr<CupDispenser> DeviceManager::get_cup_dispenser() const {
    return impl_->get_cup_dispenser();
}

std::shared_ptr<CoffeeMachine> DeviceManager::get_coffee_machine() const {
    return impl_->get_coffee_machine();
}

std::shared_ptr<MilkContainerCleaner> DeviceManager::get_milk_container_cleaner() const {
    return impl_->get_milk_container_cleaner();
}



} // namespace aubo
