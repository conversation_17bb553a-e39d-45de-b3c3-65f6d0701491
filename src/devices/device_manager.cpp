/*
 * Copyright ©2015-2025 AUBO (Beijing) Robotics Technology Co., Ltd. All rights reserved.
 */

#include "device_manager.h"
#include <sstream>
#include <vector>

// 日志宏定义 - 简化版本，实际项目中应该使用统一的日志系统
#define LOG_INFO(msg, ...) printf("[DeviceManager] INFO: " msg "\n", ##__VA_ARGS__)
#define LOG_ERROR(msg, ...) printf("[DeviceManager] ERROR: " msg "\n", ##__VA_ARGS__)
#define LOG_WARN(msg, ...) printf("[DeviceManager] WARN: " msg "\n", ##__VA_ARGS__)

namespace aubo {

class DeviceManager::Impl {
public:
    Impl() {
        // 创建所有设备实例
        left_robot_ = std::make_shared<LeftRobot>();
        right_robot_ = std::make_shared<RightRobot>();
        cup_dispenser_ = std::make_shared<CupDispenser>();
        coffee_machine_ = std::make_shared<CoffeeMachine>();
        milk_container_cleaner_ = std::make_shared<MilkContainerCleaner>();
        
        // 将设备添加到管理列表
        devices_.push_back(left_robot_);
        devices_.push_back(right_robot_);
        devices_.push_back(cup_dispenser_);
        devices_.push_back(coffee_machine_);
        devices_.push_back(milk_container_cleaner_);
    }

    bool initialize_all_devices() {
        LOG_INFO("开始初始化所有设备");
        
        bool all_success = true;
        int success_count = 0;
        
        for (auto& device : devices_) {
            LOG_INFO("初始化设备: %s", device->get_device_name().c_str());
            
            if (device->init()) {
                LOG_INFO("设备初始化成功: %s", device->get_device_name().c_str());
                success_count++;
            } else {
                LOG_ERROR("设备初始化失败: %s", device->get_device_name().c_str());
                all_success = false;
            }
        }
        
        LOG_INFO("设备初始化完成: %d/%d 成功", success_count, static_cast<int>(devices_.size()));
        return all_success;
    }

    bool shutdown_all_devices() {
        LOG_INFO("开始关闭所有设备");
        
        bool all_success = true;
        int success_count = 0;
        
        // 逆序关闭设备
        for (auto it = devices_.rbegin(); it != devices_.rend(); ++it) {
            auto& device = *it;
            LOG_INFO("关闭设备: %s", device->get_device_name().c_str());
            
            if (device->shutdown()) {
                LOG_INFO("设备关闭成功: %s", device->get_device_name().c_str());
                success_count++;
            } else {
                LOG_ERROR("设备关闭失败: %s", device->get_device_name().c_str());
                all_success = false;
            }
        }
        
        LOG_INFO("设备关闭完成: %d/%d 成功", success_count, static_cast<int>(devices_.size()));
        return all_success;
    }

    bool emergency_stop_all_devices() {
        LOG_WARN("紧急停止所有设备");
        
        bool all_success = true;
        int success_count = 0;
        
        for (auto& device : devices_) {
            if (device->emergency_stop()) {
                success_count++;
            } else {
                LOG_ERROR("设备紧急停止失败: %s", device->get_device_name().c_str());
                all_success = false;
            }
        }
        
        LOG_WARN("紧急停止完成: %d/%d 成功", success_count, static_cast<int>(devices_.size()));
        return all_success;
    }

    std::shared_ptr<LeftRobot> get_left_robot() const {
        return left_robot_;
    }

    std::shared_ptr<RightRobot> get_right_robot() const {
        return right_robot_;
    }

    std::shared_ptr<CupDispenser> get_cup_dispenser() const {
        return cup_dispenser_;
    }

    std::shared_ptr<CoffeeMachine> get_coffee_machine() const {
        return coffee_machine_;
    }

    std::shared_ptr<MilkContainerCleaner> get_milk_container_cleaner() const {
        return milk_container_cleaner_;
    }



private:
    std::shared_ptr<LeftRobot> left_robot_;
    std::shared_ptr<RightRobot> right_robot_;
    std::shared_ptr<CupDispenser> cup_dispenser_;
    std::shared_ptr<CoffeeMachine> coffee_machine_;
    std::shared_ptr<MilkContainerCleaner> milk_container_cleaner_;
    
    std::vector<std::shared_ptr<DeviceBase>> devices_;
};

// DeviceManager 公共接口实现
DeviceManager::DeviceManager() {
    impl_ = std::make_unique<Impl>();
}

DeviceManager::~DeviceManager() = default;

bool DeviceManager::initialize_all_devices() {
    return impl_->initialize_all_devices();
}

bool DeviceManager::shutdown_all_devices() {
    return impl_->shutdown_all_devices();
}

bool DeviceManager::emergency_stop_all_devices() {
    return impl_->emergency_stop_all_devices();
}

std::shared_ptr<LeftRobot> DeviceManager::get_left_robot() const {
    return impl_->get_left_robot();
}

std::shared_ptr<RightRobot> DeviceManager::get_right_robot() const {
    return impl_->get_right_robot();
}

std::shared_ptr<CupDispenser> DeviceManager::get_cup_dispenser() const {
    return impl_->get_cup_dispenser();
}

std::shared_ptr<CoffeeMachine> DeviceManager::get_coffee_machine() const {
    return impl_->get_coffee_machine();
}

std::shared_ptr<MilkContainerCleaner> DeviceManager::get_milk_container_cleaner() const {
    return impl_->get_milk_container_cleaner();
}



} // namespace aubo
