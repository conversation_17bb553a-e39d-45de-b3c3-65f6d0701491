/*
 * Copyright ©2015-2025 AUBO (Beijing) Robotics Technology Co., Ltd. All rights reserved.
 */

#ifndef AUBO_COFFEE_SERVICE_COFFEE_MACHINE_H
#define AUBO_COFFEE_SERVICE_COFFEE_MACHINE_H

#include "../device_base.h"
#include <memory>

namespace aubo {

/**
 * @enum CoffeeStrength
 * @brief 咖啡浓度枚举
 */
enum class CoffeeStrength {
    LIGHT,          ///< 淡
    MEDIUM,         ///< 中等
    STRONG          ///< 浓
};

/**
 * @enum CoffeeSize
 * @brief 咖啡大小枚举
 */
enum class CoffeeSize {
    SMALL,          ///< 小杯
    MEDIUM,         ///< 中杯
    LARGE           ///< 大杯
};

/**
 * @struct CoffeeParameters
 * @brief 咖啡制作参数
 */
struct CoffeeParameters {
    CoffeeStrength strength;    ///< 咖啡浓度
    CoffeeSize size;           ///< 咖啡大小
    int temperature;           ///< 温度(摄氏度)
    
    CoffeeParameters() 
        : strength(CoffeeStrength::MEDIUM), size(CoffeeSize::MEDIUM), temperature(85) {}
};

/**
 * @class CoffeeMachine
 * @brief 咖啡机类
 * 
 * 负责制作各种类型的咖啡
 */
class CoffeeMachine : public DeviceBase {
public:
    /**
     * @brief 构造函数
     */
    CoffeeMachine();

    /**
     * @brief 析构函数
     */
    ~CoffeeMachine();

    /**
     * @brief 初始化咖啡机
     * @return 初始化成功返回true
     */
    bool init() override;

    /**
     * @brief 关闭咖啡机
     * @return 关闭成功返回true
     */
    bool shutdown() override;

    /**
     * @brief 紧急停止
     * @return 紧急停止成功返回true
     */
    bool emergency_stop() override;

    /**
     * @brief 制作咖啡
     * @param params 咖啡制作参数
     * @return 制作成功返回true
     */
    bool brew_coffee(const CoffeeParameters& params = CoffeeParameters());

    /**
     * @brief 预热咖啡机
     * @return 预热成功返回true
     */
    bool preheat();

    /**
     * @brief 清洁咖啡机
     * @return 清洁成功返回true
     */
    bool clean();

    /**
     * @brief 检查水箱水位
     * @return 水位百分比 (0-100)
     */
    int check_water_level() const;

    /**
     * @brief 检查咖啡豆余量
     * @return 咖啡豆余量百分比 (0-100)
     */
    int check_bean_level() const;

    /**
     * @brief 获取咖啡机状态
     * @return 状态信息字符串
     */
    std::string get_status() const;

    /**
     * @brief 获取当前温度
     * @return 当前温度(摄氏度)
     */
    int get_current_temperature() const;

private:
    class Impl;
    std::unique_ptr<Impl> impl_;
};

/**
 * @brief 咖啡浓度转字符串
 * @param strength 咖啡浓度
 * @return 字符串表示
 */
std::string coffee_strength_to_string(CoffeeStrength strength);

/**
 * @brief 字符串转咖啡浓度
 * @param strength_string 浓度字符串
 * @return 咖啡浓度
 */
CoffeeStrength string_to_coffee_strength(const std::string& strength_string);

/**
 * @brief 咖啡大小转字符串
 * @param size 咖啡大小
 * @return 字符串表示
 */
std::string coffee_size_to_string(CoffeeSize size);

/**
 * @brief 字符串转咖啡大小
 * @param size_string 大小字符串
 * @return 咖啡大小
 */
CoffeeSize string_to_coffee_size(const std::string& size_string);

} // namespace aubo

#endif // AUBO_COFFEE_SERVICE_COFFEE_MACHINE_H
