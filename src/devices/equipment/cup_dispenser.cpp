/*
 * Copyright ©2015-2025 AUBO (Beijing) Robotics Technology Co., Ltd. All rights reserved.
 */

#include "cup_dispenser.h"
#include <thread>
#include <chrono>
#include <map>
#include <sstream>

// 日志宏定义 - 简化版本，实际项目中应该使用统一的日志系统
#define LOG_INFO(msg, ...) printf("[CupDispenser] INFO: " msg "\n", ##__VA_ARGS__)
#define LOG_ERROR(msg, ...) printf("[CupDispenser] ERROR: " msg "\n", ##__VA_ARGS__)
#define LOG_WARN(msg, ...) printf("[CupDispenser] WARN: " msg "\n", ##__VA_ARGS__)

namespace aubo {

class CupDispenser::Impl {
public:
    Impl() : cup_inventory_{{CupType::REGULAR, 100}, {CupType::LARGE, 50}, {CupType::SMALL, 75}} {
    }

    bool init() {
        LOG_INFO("初始化杯子分配器");
        
        // 模拟初始化过程
        std::this_thread::sleep_for(std::chrono::milliseconds(500));
        
        LOG_INFO("杯子分配器初始化完成");
        return true;
    }

    bool shutdown() {
        LOG_INFO("关闭杯子分配器");
        return true;
    }

    bool emergency_stop() {
        LOG_WARN("杯子分配器紧急停止");
        return true;
    }

    bool dispense_cup(CupType cup_type) {
        LOG_INFO("分配杯子: %s", cup_type_to_string(cup_type).c_str());
        
        // 检查库存
        if (cup_inventory_[cup_type] <= 0) {
            LOG_ERROR("杯子库存不足: %s", cup_type_to_string(cup_type).c_str());
            return false;
        }
        
        // 模拟分配过程
        std::this_thread::sleep_for(std::chrono::milliseconds(800));
        
        // 减少库存
        cup_inventory_[cup_type]--;
        
        LOG_INFO("杯子分配成功: %s，剩余库存: %d", 
                 cup_type_to_string(cup_type).c_str(), 
                 cup_inventory_[cup_type]);
        
        return true;
    }

    int check_cup_inventory(CupType cup_type) const {
        auto it = cup_inventory_.find(cup_type);
        return (it != cup_inventory_.end()) ? it->second : 0;
    }

    bool refill_cups(CupType cup_type, int count) {
        LOG_INFO("补充杯子: %s，数量: %d", cup_type_to_string(cup_type).c_str(), count);
        
        if (count <= 0) {
            LOG_ERROR("补充数量必须大于0");
            return false;
        }
        
        cup_inventory_[cup_type] += count;
        
        LOG_INFO("杯子补充完成: %s，当前库存: %d", 
                 cup_type_to_string(cup_type).c_str(), 
                 cup_inventory_[cup_type]);
        
        return true;
    }

    std::string get_status() const {
        std::ostringstream oss;
        oss << "杯子分配器状态:\n";
        for (const auto& [cup_type, count] : cup_inventory_) {
            oss << "  " << cup_type_to_string(cup_type) << ": " << count << "个\n";
        }
        return oss.str();
    }

private:
    std::map<CupType, int> cup_inventory_;  ///< 杯子库存
};

// CupDispenser 公共接口实现
CupDispenser::CupDispenser() : DeviceBase("CupDispenser") {
    impl_ = std::make_unique<Impl>();
}

CupDispenser::~CupDispenser() = default;

bool CupDispenser::init() {
    bool success = impl_->init();
    set_initialized(success);
    set_connected(success);
    return success;
}

bool CupDispenser::shutdown() {
    bool success = impl_->shutdown();
    if (success) {
        set_initialized(false);
        set_connected(false);
    }
    return success;
}

bool CupDispenser::emergency_stop() {
    return impl_->emergency_stop();
}

bool CupDispenser::dispense_cup(CupType cup_type) {
    return impl_->dispense_cup(cup_type);
}

int CupDispenser::check_cup_inventory(CupType cup_type) const {
    return impl_->check_cup_inventory(cup_type);
}

bool CupDispenser::refill_cups(CupType cup_type, int count) {
    return impl_->refill_cups(cup_type, count);
}

std::string CupDispenser::get_status() const {
    return impl_->get_status();
}

// 辅助函数实现
std::string cup_type_to_string(CupType cup_type) {
    switch (cup_type) {
        case CupType::REGULAR: return "普通杯";
        case CupType::LARGE: return "大杯";
        case CupType::SMALL: return "小杯";
        default: return "未知杯型";
    }
}

CupType string_to_cup_type(const std::string& type_string) {
    if (type_string == "large") return CupType::LARGE;
    if (type_string == "small") return CupType::SMALL;
    return CupType::REGULAR;
}

} // namespace aubo
