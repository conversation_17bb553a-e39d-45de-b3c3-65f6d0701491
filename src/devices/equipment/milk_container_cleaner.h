/*
 * Copyright ©2015-2025 AUBO (Beijing) Robotics Technology Co., Ltd. All rights reserved.
 */

#ifndef AUBO_COFFEE_SERVICE_MILK_CONTAINER_CLEANER_H
#define AUBO_COFFEE_SERVICE_MILK_CONTAINER_CLEANER_H

#include "../device_base.h"
#include <memory>

namespace aubo {

/**
 * @enum CleaningMode
 * @brief 清洁模式枚举
 */
enum class CleaningMode {
    QUICK,          ///< 快速清洁
    STANDARD,       ///< 标准清洁
    DEEP            ///< 深度清洁
};

/**
 * @enum CleaningStatus
 * @brief 清洁状态枚举
 */
enum class CleaningStatus {
    IDLE,           ///< 空闲
    CLEANING,       ///< 清洁中
    RINSING,        ///< 冲洗中
    DRYING,         ///< 烘干中
    COMPLETED,      ///< 完成
    ERROR           ///< 错误
};

/**
 * @struct CleaningParameters
 * @brief 清洁参数
 */
struct CleaningParameters {
    CleaningMode mode;          ///< 清洁模式
    int temperature;            ///< 清洁温度(摄氏度)
    int duration;               ///< 清洁时长(秒)
    bool use_detergent;         ///< 是否使用清洁剂
    
    CleaningParameters() 
        : mode(CleaningMode::STANDARD), temperature(60), duration(120), use_detergent(true) {}
};

/**
 * @class MilkContainerCleaner
 * @brief 牛奶容器清洁器类
 * 
 * 负责清洁牛奶容器和相关管路
 */
class MilkContainerCleaner : public DeviceBase {
public:
    /**
     * @brief 构造函数
     */
    MilkContainerCleaner();

    /**
     * @brief 析构函数
     */
    ~MilkContainerCleaner();

    /**
     * @brief 初始化清洁器
     * @return 初始化成功返回true
     */
    bool init() override;

    /**
     * @brief 关闭清洁器
     * @return 关闭成功返回true
     */
    bool shutdown() override;

    /**
     * @brief 紧急停止
     * @return 紧急停止成功返回true
     */
    bool emergency_stop() override;

    /**
     * @brief 开始清洁
     * @param params 清洁参数
     * @return 开始成功返回true
     */
    bool start_cleaning(const CleaningParameters& params = CleaningParameters());

    /**
     * @brief 停止清洁
     * @return 停止成功返回true
     */
    bool stop_cleaning();

    /**
     * @brief 暂停清洁
     * @return 暂停成功返回true
     */
    bool pause_cleaning();

    /**
     * @brief 恢复清洁
     * @return 恢复成功返回true
     */
    bool resume_cleaning();

    /**
     * @brief 获取清洁状态
     * @return 当前清洁状态
     */
    CleaningStatus get_cleaning_status() const;

    /**
     * @brief 获取清洁进度
     * @return 清洁进度百分比 (0-100)
     */
    int get_cleaning_progress() const;

    /**
     * @brief 检查清洁剂余量
     * @return 清洁剂余量百分比 (0-100)
     */
    int check_detergent_level() const;

    /**
     * @brief 补充清洁剂
     * @return 补充成功返回true
     */
    bool refill_detergent();

    /**
     * @brief 获取清洁器状态
     * @return 状态信息字符串
     */
    std::string get_status() const;

private:
    class Impl;
    std::unique_ptr<Impl> impl_;
};

/**
 * @brief 清洁模式转字符串
 * @param mode 清洁模式
 * @return 字符串表示
 */
std::string cleaning_mode_to_string(CleaningMode mode);

/**
 * @brief 字符串转清洁模式
 * @param mode_string 模式字符串
 * @return 清洁模式
 */
CleaningMode string_to_cleaning_mode(const std::string& mode_string);

/**
 * @brief 清洁状态转字符串
 * @param status 清洁状态
 * @return 字符串表示
 */
std::string cleaning_status_to_string(CleaningStatus status);

} // namespace aubo

#endif // AUBO_COFFEE_SERVICE_MILK_CONTAINER_CLEANER_H
