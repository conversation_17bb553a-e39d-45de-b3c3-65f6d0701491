/*
 * Copyright ©2015-2025 AUBO (Beijing) Robotics Technology Co., Ltd. All rights reserved.
 */

#include "device_base.h"

namespace aubo {

DeviceBase::DeviceBase(const std::string& device_name)
    : device_name_(device_name), initialized_(false), connected_(false) {
}

const std::string& DeviceBase::get_device_name() const {
    return device_name_;
}

bool DeviceBase::is_initialized() const {
    return initialized_;
}

bool DeviceBase::is_connected() const {
    return connected_;
}

void DeviceBase::set_initialized(bool initialized) {
    initialized_ = initialized;
}

void DeviceBase::set_connected(bool connected) {
    connected_ = connected;
}

} // namespace aubo
