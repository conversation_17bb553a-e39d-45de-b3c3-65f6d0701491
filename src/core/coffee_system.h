/*
 * Copyright ©2015-2025 AUBO (Beijing) Robotics Technology Co., Ltd. All rights reserved.
 */

#ifndef AUBO_COFFEE_SERVICE_COFFEE_SYSTEM_H
#define AUBO_COFFEE_SERVICE_COFFEE_SYSTEM_H

#include <memory>
#include <string>
#include <vector>

#include "../types/coffee_types.h"

namespace aubo {

/**
 * @class CoffeeSystem
 * @brief 咖啡制作系统
 *
 * 集成了机器人管理和工作流执行的完整咖啡制作系统。
 * 提供简洁的API用于咖啡制作的全流程管理。
 */
class CoffeeSystem {
public:
    /**
     * @brief 构造函数
     */
    CoffeeSystem();

    /**
     * @brief 析构函数
     */
    ~CoffeeSystem();

    /**
     * @brief 初始化咖啡制作系统
     *
     * 初始化机器人臂和工作流引擎
     *
     * @return 如果初始化成功则返回true
     */
    bool initialize();

    /**
     * @brief 关闭咖啡制作系统
     *
     * 安全关闭所有组件并清理资源
     *
     * @return 如果关闭成功则返回true
     */
    bool shutdown();

    /**
     * @brief 制作咖啡
     *
     * 根据订单信息自动选择合适的制作流程
     *
     * @param order 咖啡订单
     * @return 如果制作成功则返回true
     */
    bool make_coffee(const CoffeeOrder& order);

    /**
     * @brief 获取支持的咖啡类型列表
     *
     * @return 咖啡类型列表
     */
    std::vector<CoffeeType> get_supported_coffee_types() const;

    /**
     * @brief 停止当前制作过程
     *
     * @return 如果停止成功则返回true
     */
    bool stop_current_process();

    /**
     * @brief 检查是否正在制作咖啡
     *
     * @return 如果正在制作咖啡则返回true
     */
    bool is_making_coffee() const;

    /**
     * @brief 获取当前制作步骤
     *
     * @return 当前步骤描述，如果未制作返回空字符串
     */
    std::string get_current_step() const;

    /**
     * @brief 获取当前系统状态
     *
     * @return 当前系统状态
     */
    CoffeeStatus get_status() const;

    /**
     * @brief 紧急停止所有操作
     *
     * 立即停止所有操作并进入安全状态
     *
     * @return 如果紧急停止成功则返回true
     */
    bool emergency_stop();

private:
    class Impl;
    std::unique_ptr<Impl> impl_;
};

} // namespace aubo

#endif // AUBO_COFFEE_SERVICE_COFFEE_SYSTEM_H
