/*
 * Copyright ©2015-2025 AUBO (Beijing) Robotics Technology Co., Ltd. All rights reserved.
 */

#include "left_robot.h"

#include <chrono>
#include <cmath>
#include <iostream>
#include <thread>

#include <aubo-base/log.h>
#include <aubo_sdk/AuboRobotMetaType.h>
#include <aubo_sdk/serviceinterface.h>

#include "latte_art_config.h"

namespace aubo {

// 配置常量
constexpr const char* LEFT_ROBOT_HOST = "*************";
constexpr int LEFT_ROBOT_PORT = 8899;

// 取杯序列常量定义
constexpr int CUP_SEQUENCE_LENGTH = 6;
constexpr double CUP_SEQUENCE_POSITIONS[CUP_SEQUENCE_LENGTH][aubo_robot_namespace::ARM_DOF] = {
    {0.394969, -0.582201, -2.294774, 1.378310, 1.567179, -0.005875}, // 【准备】
    {1.748653, -0.582250, -2.294792, 1.377843, 1.567181, -0.005866}, // 【前方】
    {1.798266, 0.316536, -1.375015, 1.427817, 1.768882, 0.003928},   // 【中间】
    {1.789321, 0.360326, -1.165683, 1.593793, 1.754975, 0.004124},   // 【提起】
    {1.798266, 0.316536, -1.375015, 1.427817, 1.768882, 0.003928},   // 【再次中间】
    {1.748653, -0.582250, -2.294792, 1.377843, 1.567181, -0.005866}  // 【再次前方】
};

constexpr const char* CUP_SEQUENCE_NAMES[CUP_SEQUENCE_LENGTH] = {
    "准备取杯",
    "杯子分配器前方",
    "杯子分配器中间",
    "提起抓取杯子",
    "杯子分配器中间",
    "杯子分配器前方"
};

// 取咖啡序列常量定义
constexpr int COFFEE_SEQUENCE_LENGTH = 5;
constexpr double COFFEE_SEQUENCE_POSITIONS[COFFEE_SEQUENCE_LENGTH][aubo_robot_namespace::ARM_DOF] = {
    {0.394894, -0.585567, -2.276495, 1.399861, 1.567112, -0.005871}, // 【准备】
    {0.455068, 0.281083, -1.454357, 1.425689, 1.488030, -0.034480},  // 【出口下方】
    {0.515712, 0.266762, -1.354910, 1.491814, 1.656176, -0.577095},  // 【倾斜】
    {0.424998, 0.255695, -1.439219, 1.466285, 1.402348, -0.005819},  // 【平放】
    {0.394960, -0.588153, -2.257726, 1.421358, 1.567158, -0.005875}  // 【再次准备】
};

constexpr const char* COFFEE_SEQUENCE_NAMES[COFFEE_SEQUENCE_LENGTH] = {
    "咖啡机准备",
    "咖啡出口下方",
    "取咖啡倾斜位置",
    "取咖啡平放位置",
    "咖啡机退出"
};

// 拉花序列常量定义
constexpr int LATTE_ART_SEQUENCE_LENGTH = 3;
constexpr double LATTE_ART_SEQUENCE_POSITIONS[LATTE_ART_SEQUENCE_LENGTH][aubo_robot_namespace::ARM_DOF] = {
    {-0.057637, -0.517702, -2.347097, 1.260445, 2.236833, -0.005356}, // 【准备】
    {-0.348188, 0.061563, -1.862573, 1.193162, 1.617185, -0.005369},  // 【水平】
    {-0.166120, 0.272210, -1.263570, 1.839400, 1.338660, -0.728690}   // 【倾斜】
};

constexpr const char* LATTE_ART_SEQUENCE_NAMES[LATTE_ART_SEQUENCE_LENGTH] = {
    "拉花准备位置",
    "拉花水平位置",
    "拉花倾斜位置"
};

// 咖啡交付序列常量定义
constexpr int COFFEE_DELIVERY_SEQUENCE_LENGTH = 7;
constexpr double COFFEE_DELIVERY_SEQUENCE_POSITIONS[COFFEE_DELIVERY_SEQUENCE_LENGTH][aubo_robot_namespace::ARM_DOF] = {
    {-0.348188, 0.061563, -1.862573, 1.193162, 1.617185, -0.005369}, // 【交付准备位置】
    {-0.598221, 0.125717, -2.140542, 0.871622, 1.621427, 0.016601},  // 【交付路径1】
    {-0.598236, 0.214382, -2.183287, 0.740350, 1.621435, 0.016628},  // 【交付路径2】
    {-0.598151, 0.296397, -2.207923, 0.773433, 1.618430, 0.016553},  // 【交付路径3】
    {-0.603985, 0.081795, -2.607261, 0.437128, 1.591528, -0.004395}, // 【交付位置】
    {-0.603985, 0.081795, -2.607261, 0.437128, 1.591528, -0.004395}, // 【交付确认位置】
    {-0.323560, -0.416307, -2.326127, 1.229185, 2.080945, -0.005333} // 【交付完成位置】
};

constexpr const char* COFFEE_DELIVERY_SEQUENCE_NAMES[COFFEE_DELIVERY_SEQUENCE_LENGTH] = {
    "交付准备位置",
    "交付路径1",
    "交付路径2",
    "交付路径3",
    "交付位置",
    "交付确认位置",
    "交付完成位置"
};

// 计算两个关节位置之间的距离
static double calculate_joint_distance(const double* pos1, const double* pos2) {
    double sum_squared_diff = 0.0;

    for (int i = 0; i < aubo_robot_namespace::ARM_DOF; i++) {
        double diff = pos1[i] - pos2[i];
        sum_squared_diff += diff * diff;
    }

    return std::sqrt(sum_squared_diff);
}

// 检查两个位置是否接近
bool is_position_close(const double* pos1, const double* pos2) {
    const double threshold = 0.3; // 弧度，约17度

    for (int i = 0; i < aubo_robot_namespace::ARM_DOF; i++) {
        if (std::fabs(pos1[i] - pos2[i]) > threshold) {
            return false;
        }
    }

    return true;
}

// 检查当前位置是否在取杯序列中
bool is_in_cup_sequence(const double* current_joint_pos) {
    // 检查当前位置是否接近取杯序列中的任何位置
    for (int i = 0; i < CUP_SEQUENCE_LENGTH; i++) {
        if (is_position_close(current_joint_pos, CUP_SEQUENCE_POSITIONS[i])) {
            LOG_INFO("[LeftRobot] 检测到机器人在取杯序列位置 {} 附近", i);
            return true;
        }
    }

    return false;
}

// 检查当前位置是否在取咖啡序列中
bool is_in_coffee_sequence(const double* current_joint_pos) {
    // 检查当前位置是否接近取咖啡序列中的任何位置
    for (int i = 0; i < COFFEE_SEQUENCE_LENGTH; i++) {
        if (is_position_close(current_joint_pos, COFFEE_SEQUENCE_POSITIONS[i])) {
            LOG_INFO("[LeftRobot] 检测到机器人在取咖啡序列位置 {} 附近", i);
            return true;
        }
    }

    return false;
}

// 检查当前位置是否在咖啡交付序列中
bool is_in_coffee_delivery_sequence(const double* current_joint_pos) {
    // 检查当前位置是否接近咖啡交付序列中的任何位置
    for (int i = 0; i < COFFEE_DELIVERY_SEQUENCE_LENGTH; i++) {
        if (is_position_close(current_joint_pos, COFFEE_DELIVERY_SEQUENCE_POSITIONS[i])) {
            LOG_INFO("[LeftRobot] 检测到机器人在咖啡交付序列位置 {} 附近", i);
            return true;
        }
    }

    return false;
}

// 执行取杯安全离开序列
bool execute_cup_exit_sequence(const double* current_joint_pos, ServiceInterface& robot_service) {
    // 找到当前位置最接近序列中的哪个位置
    int closest_position = 0;
    double min_distance = std::numeric_limits<double>::max();

    for (int i = 0; i < CUP_SEQUENCE_LENGTH; i++) {
        double distance = calculate_joint_distance(current_joint_pos, CUP_SEQUENCE_POSITIONS[i]);
        if (distance < min_distance) {
            min_distance = distance;
            closest_position = i;
        }
    }

    LOG_INFO("[LeftRobot] 当前位置最接近 {}", CUP_SEQUENCE_NAMES[closest_position]);

    // 根据当前位置决定安全离开路径
    int exit_path[4] = {0, 0, 0, 0};
    int exit_path_length = 0;

    if (closest_position == 0) {
        // 如果已经在【准备位置】，不需要额外动作
        LOG_INFO("[LeftRobot] 机器人已在准备位置，无需执行离开动作");
        return true;
    } else if (closest_position == 1) {
        // 如果在【前方】位置，直接回到【准备位置】
        exit_path[0] = 0; // 【准备位置】
        exit_path_length = 1;
    } else if (closest_position == 2 || closest_position == 3 || closest_position == 4) {
        // 如果在【中间】、【提起】或【再次中间】位置，需要依次回到：【再次中间】→【再次前方】→【准备位置】
        exit_path[0] = 4; // 【再次中间】
        exit_path[1] = 5; // 【再次前方】
        exit_path[2] = 0; // 【准备位置】
        exit_path_length = 3;
    } else if (closest_position == 5) {
        // 如果在【再次前方】位置，回到【准备位置】
        exit_path[0] = 0; // 【准备位置】
        exit_path_length = 1;
    }

    // 执行安全离开路径
    for (int i = 0; i < exit_path_length; i++) {
        int pos_index = exit_path[i];
        LOG_INFO("[LeftRobot] 安全离开: 移动到 {}", CUP_SEQUENCE_NAMES[pos_index]);

        // 创建非const副本以传递给API
        double joint_position[aubo_robot_namespace::ARM_DOF];
        for (int j = 0; j < aubo_robot_namespace::ARM_DOF; j++) {
            joint_position[j] = CUP_SEQUENCE_POSITIONS[pos_index][j];
        }

        int result = robot_service.robotServiceJointMove(joint_position, true);
        if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
            LOG_ERROR("[LeftRobot] 移动到 {} 失败, 结果 = {}", CUP_SEQUENCE_NAMES[pos_index], result);
            return false;
        }

        LOG_INFO("[LeftRobot] 成功移动到 {}", CUP_SEQUENCE_NAMES[pos_index]);
    }

    return true;
}

// 执行取咖啡安全离开序列
bool execute_coffee_exit_sequence(const double* current_joint_pos, ServiceInterface& robot_service) {
    // 找到当前位置最接近序列中的哪个位置
    int closest_position = 0;
    double min_distance = std::numeric_limits<double>::max();

    for (int i = 0; i < COFFEE_SEQUENCE_LENGTH; i++) {
        double distance = calculate_joint_distance(current_joint_pos, COFFEE_SEQUENCE_POSITIONS[i]);
        if (distance < min_distance) {
            min_distance = distance;
            closest_position = i;
        }
    }

    LOG_INFO("[LeftRobot] 当前位置最接近 {}", COFFEE_SEQUENCE_NAMES[closest_position]);

    // 根据当前位置决定安全离开路径
    int exit_path[3] = {0, 0, 0};
    int exit_path_length = 0;

    if (closest_position == 0 || closest_position == 4) {
        // 如果在【准备】或【再次准备】位置，已经是安全位置
        LOG_INFO("[LeftRobot] 机器人已在咖啡机安全位置，无需执行离开动作");
        return true;
    } else if (closest_position == 1) {
        // 如果在【出口下方】位置，直接回到【再次准备】位置
        exit_path[0] = 4; // 【再次准备】
        exit_path_length = 1;
    } else if (closest_position == 2 || closest_position == 3) {
        // 如果在【倾斜】或【平放】位置，需要依次回到：【平放】→【再次准备】
        if (closest_position == 2) {
            // 从倾斜位置需要先到平放位置
            exit_path[0] = 3; // 【平放】
            exit_path[1] = 4; // 【再次准备】
            exit_path_length = 2;
        } else {
            // 从平放位置直接到再次准备位置
            exit_path[0] = 4; // 【再次准备】
            exit_path_length = 1;
        }
    }

    // 执行安全离开路径
    for (int i = 0; i < exit_path_length; i++) {
        int pos_index = exit_path[i];
        LOG_INFO("[LeftRobot] 安全离开: 移动到 {}", COFFEE_SEQUENCE_NAMES[pos_index]);

        // 创建非const副本以传递给API
        double joint_position[aubo_robot_namespace::ARM_DOF];
        for (int j = 0; j < aubo_robot_namespace::ARM_DOF; j++) {
            joint_position[j] = COFFEE_SEQUENCE_POSITIONS[pos_index][j];
        }

        int result = robot_service.robotServiceJointMove(joint_position, true);
        if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
            LOG_ERROR("[LeftRobot] 移动到 {} 失败, 结果 = {}", COFFEE_SEQUENCE_NAMES[pos_index], result);
            return false;
        }

        LOG_INFO("[LeftRobot] 成功移动到 {}", COFFEE_SEQUENCE_NAMES[pos_index]);
    }

    return true;
}

class LeftRobot::Impl {
public:
    Impl() = default;

    ~Impl() {
        LOG_INFO("[LeftRobot] ~Impl");
        // 析构时自动断开连接并关闭机器人
        if (initialized_) {
            shutdown();
        }
        if (connected_) {
            disconnect();
        }
    }

    static void robot_event_callback(const aubo_robot_namespace::RobotEventInfo *eventInfo, void */*arg*/) {
        LOG_INFO("[LeftRobot] 机器人事件: type -> {}, code -> {}, content -> {}", static_cast<int>(eventInfo->eventType), eventInfo->eventCode, eventInfo->eventContent);
    }

    bool init() {
        LOG_INFO("[LeftRobot] 初始化");

        // 首先连接到机器人
        if (!connect(LEFT_ROBOT_HOST, LEFT_ROBOT_PORT, "aubo", "123456")) {
            LOG_ERROR("[LeftRobot] 连接失败，无法初始化");
            return false;
        }

        // 启动机器人
        aubo_robot_namespace::ROBOT_SERVICE_STATE state;
        aubo_robot_namespace::ToolDynamicsParam tool_dynamics_param{};

        int result = robot_service_.rootServiceRobotStartup(tool_dynamics_param,
                                                            13, true, true, 1000, state);
        if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
            LOG_ERROR("[LeftRobot] 启动失败, 结果 = {}", result);
            disconnect(); // 启动失败时断开连接
            return false;
        }

        robot_service_.robotServiceRegisterRobotEventInfoCallback(robot_event_callback, nullptr);

        LOG_INFO("[LeftRobot] 启动成功");

        // 初始化运动配置
        result = robot_service_.robotServiceInitGlobalMoveProfile();
        if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
            LOG_ERROR("[LeftRobot] 初始化全局运动配置失败, 结果 = {}", result);
            return false;
        }

        // 设置默认运动参数
        configure_movement_parameters();

        // 加载拉花配置
        if (!latte_art_config_.load_from_directory("../share/config/latte_art")) {
            LOG_WARN("[LeftRobot] 无法加载拉花配置目录，请检查配置文件");
        } else {
            LOG_INFO("[LeftRobot] 拉花配置加载成功");
        }

        initialized_ = true;
        return true;
    }

    bool move_to_home() {
        LOG_INFO("[LeftRobot] 移动到初始位置");

        if (!check_robot_state()) {
            return false;
        }

        // 初始化运动配置
        auto result = robot_service_.robotServiceInitGlobalMoveProfile();
        if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
            LOG_ERROR("[LeftRobot] 初始化全局运动配置失败, 结果 = {}", result);
            return false;
        }

        // 设置默认运动参数
        configure_movement_parameters();

        // 获取当前关节角
        aubo_robot_namespace::JointParam joint_angle;
        result = robot_service_.robotServiceGetJointAngleInfo(joint_angle);
        if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
            LOG_ERROR("[LeftRobot] 获取当前关节角失败, 结果 = {}", result);
            return false;
        }

        // 记录当前位置
        LOG_INFO("[LeftRobot] 当前关节角:");
        for (int i = 0; i < aubo_robot_namespace::ARM_DOF; i++) {
            LOG_INFO("[LeftRobot] 关节 {}: {}", i, joint_angle.jointPos[i]);
        }

        // 检查是否已经在HOME位置
        double home_joint_angle[aubo_robot_namespace::ARM_DOF] =
            {0.106664, -0.083567, -1.929689, 1.285737, 1.715605, -0.006741};

        if (is_position_close(joint_angle.jointPos, home_joint_angle)) {
            LOG_INFO("[LeftRobot] 机器人已在HOME位置，无需移动");
            return true;
        }

        // 检查是否在取杯过程中
        bool in_cup_sequence = is_in_cup_sequence(joint_angle.jointPos);
        if (in_cup_sequence) {
            LOG_INFO("[LeftRobot] 检测到机器人在取杯过程中，执行安全离开动作");
            if (!execute_cup_exit_sequence(joint_angle.jointPos, robot_service_)) {
                LOG_ERROR("[LeftRobot] 执行取杯安全离开动作失败");
                return false;
            }
        }

        // 检查是否在取咖啡过程中
        bool in_coffee_sequence = is_in_coffee_sequence(joint_angle.jointPos);
        if (in_coffee_sequence) {
            LOG_INFO("[LeftRobot] 检测到机器人在取咖啡过程中，执行安全离开动作");
            if (!execute_coffee_exit_sequence(joint_angle.jointPos, robot_service_)) {
                LOG_ERROR("[LeftRobot] 执行取咖啡安全离开动作失败");
                return false;
            }
        }

        // 检查是否在咖啡交付过程中
        bool in_coffee_delivery_sequence = is_in_coffee_delivery_sequence(joint_angle.jointPos);
        if (in_coffee_delivery_sequence) {
            LOG_INFO("[LeftRobot] 检测到机器人在咖啡交付过程中，移动到安全位置");
            // 直接移动到初始位置即可，因为交付位置相对安全
        }

        // 移动到初始位置
        result = robot_service_.robotServiceJointMove(home_joint_angle, true);
        if (result == aubo_robot_namespace::InterfaceCallSuccCode) {
            LOG_INFO("[LeftRobot] 移动到初始位置成功");
            return true;
        } else {
            LOG_ERROR("[LeftRobot] 移动到初始位置失败, 结果 = {}", result);
            return false;
        }
    }

    bool execute_get_cup_sequence() {
        LOG_INFO("[LeftRobot] 执行取杯序列");

        if (!check_robot_state()) {
            return false;
        }

        // 执行序列中的每个动作
        for (int i = 0; i < CUP_SEQUENCE_LENGTH; i++) {
            LOG_INFO("[LeftRobot] 移动到 {}", CUP_SEQUENCE_NAMES[i]);

            // 创建非const副本以传递给API
            double joint_position[aubo_robot_namespace::ARM_DOF];
            for (int j = 0; j < aubo_robot_namespace::ARM_DOF; j++) {
                joint_position[j] = CUP_SEQUENCE_POSITIONS[i][j];
            }

            // 初始化运动配置
            auto result = robot_service_.robotServiceInitGlobalMoveProfile();
            if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
                LOG_ERROR("[LeftRobot] 初始化全局运动配置失败, 结果 = {}", result);
                return false;
            }

            // 设置默认运动参数
            configure_movement_parameters();
            result = robot_service_.robotServiceJointMove(joint_position, true);
            if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
                LOG_ERROR("[LeftRobot] 移动到 {} 失败, 结果 = {}", CUP_SEQUENCE_NAMES[i], result);
                return false;
            }

            LOG_INFO("[LeftRobot] 成功移动到 {}", CUP_SEQUENCE_NAMES[i]);
        }

        return true;
    }

    bool execute_get_coffee_sequence() {
        LOG_INFO("[LeftRobot] 执行取咖啡序列");

        if (!check_robot_state()) {
            return false;
        }

        // 执行序列中的每个动作
        for (int i = 0; i < COFFEE_SEQUENCE_LENGTH; i++) {
            LOG_INFO("[LeftRobot] 移动到 {}", COFFEE_SEQUENCE_NAMES[i]);

            // 创建非const副本以传递给API
            double joint_position[aubo_robot_namespace::ARM_DOF];
            for (int j = 0; j < aubo_robot_namespace::ARM_DOF; j++) {
                joint_position[j] = COFFEE_SEQUENCE_POSITIONS[i][j];
            }
            // 初始化运动配置
            auto result = robot_service_.robotServiceInitGlobalMoveProfile();
            if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
                LOG_ERROR("[LeftRobot] 初始化全局运动配置失败, 结果 = {}", result);
                return false;
            }

            // 设置默认运动参数
            configure_movement_parameters();
            result = robot_service_.robotServiceJointMove(joint_position, true);
            if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
                LOG_ERROR("[LeftRobot] 移动到 {} 失败, 结果 = {}", COFFEE_SEQUENCE_NAMES[i], result);
                return false;
            }

            LOG_INFO("[LeftRobot] 成功移动到 {}", COFFEE_SEQUENCE_NAMES[i]);

            if (i == 2) {
                std::this_thread::sleep_for(std::chrono::seconds(30));
            }
        }

        return true;
    }

    bool prepare_for_latte_art() {
        LOG_INFO("[LeftRobot] 准备拉花位置");

        if (!check_robot_state()) {
            return false;
        }

        // 执行序列中的每个动作
        for (int i = 0; i < LATTE_ART_SEQUENCE_LENGTH; i++) {
            LOG_INFO("[LeftRobot] 移动到 {}", LATTE_ART_SEQUENCE_NAMES[i]);

            // 初始化运动配置
            auto result = robot_service_.robotServiceInitGlobalMoveProfile();
            if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
                LOG_ERROR("[LeftRobot] 初始化全局运动配置失败, 结果 = {}", result);
                return false;
            }

            // 设置默认运动参数
            configure_movement_parameters();

            // 创建非const副本以传递给API
            double joint_position[aubo_robot_namespace::ARM_DOF];
            for (int j = 0; j < aubo_robot_namespace::ARM_DOF; j++) {
                joint_position[j] = LATTE_ART_SEQUENCE_POSITIONS[i][j];
            }
            result = robot_service_.robotServiceJointMove(joint_position, true);
            if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
                LOG_ERROR("[LeftRobot] 移动到 {} 失败, 结果 = {}", LATTE_ART_SEQUENCE_NAMES[i], result);
                return false;
            }

            LOG_INFO("[LeftRobot] 成功移动到 {}", LATTE_ART_SEQUENCE_NAMES[i]);
        }

        LOG_INFO("[LeftRobot] 拉花准备位置完成");
        return true;
    }

    bool execute_latte_art_sequence(LatteArtType art_type = LatteArtType::NONE) {
        LOG_INFO("[LeftRobot] 执行拉花动作: {}", get_latte_art_name(art_type));

        if (!check_robot_state()) {
            return false;
        }

        // 如果不需要拉花，直接返回成功
        if (art_type == LatteArtType::NONE) {
            LOG_INFO("[LeftRobot] 无需拉花");
            return true;
        }

        // 尝试从配置文件加载轨迹
        if (latte_art_config_.is_loaded()) {
            auto points = latte_art_config_.get_left_robot_waypoints(art_type);
            if (!points.empty()) {
                auto motion_params = latte_art_config_.get_motion_parameters(art_type);
                return execute_trajectory_from_config(points, motion_params);
            } else {
                LOG_WARN("[LeftRobot] 配置中未找到 {} 轨迹", get_latte_art_name(art_type));
            }
        } else {
            LOG_WARN("[LeftRobot] 拉花配置未加载成功");
        }
        return false;
    }

    bool execute_coffee_delivery_sequence() {
        LOG_INFO("[LeftRobot] 执行咖啡交付序列");

        if (!check_robot_state()) {
            return false;
        }

        // 执行序列中的每个动作
        for (int i = 0; i < COFFEE_DELIVERY_SEQUENCE_LENGTH; i++) {
            LOG_INFO("[LeftRobot] 移动到 {}", COFFEE_DELIVERY_SEQUENCE_NAMES[i]);

            // 创建非const副本以传递给API
            double joint_position[aubo_robot_namespace::ARM_DOF];
            for (int j = 0; j < aubo_robot_namespace::ARM_DOF; j++) {
                joint_position[j] = COFFEE_DELIVERY_SEQUENCE_POSITIONS[i][j];
            }

            // 初始化运动配置
            auto result = robot_service_.robotServiceInitGlobalMoveProfile();
            if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
                LOG_ERROR("[LeftRobot] 初始化全局运动配置失败, 结果 = {}", result);
                return false;
            }

            // 设置默认运动参数
            configure_movement_parameters();
            result = robot_service_.robotServiceJointMove(joint_position, true);
            if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
                LOG_ERROR("[LeftRobot] 移动到 {} 失败, 结果 = {}", COFFEE_DELIVERY_SEQUENCE_NAMES[i], result);
                return false;
            }

            LOG_INFO("[LeftRobot] 成功移动到 {}", COFFEE_DELIVERY_SEQUENCE_NAMES[i]);
        }

        LOG_INFO("[LeftRobot] 咖啡交付序列执行完成");
        return true;
    }

    // 从配置执行轨迹
    bool execute_trajectory_from_config(const std::vector<std::vector<double>>& points,
                                       const MotionParameters& motion_params) {
        LOG_INFO("[LeftRobot] 从配置执行轨迹");

        if (points.empty()) {
            LOG_ERROR("[LeftRobot] 轨迹为空");
            return false;
        }

        // 移动到第一个点
        LOG_INFO("[LeftRobot] 移动到第一个路点");
        double first_point[aubo_robot_namespace::ARM_DOF];
        for (int j = 0; j < aubo_robot_namespace::ARM_DOF; j++) {
            first_point[j] = points[0][j];
        }
        int result = robot_service_.robotServiceJointMove(first_point, true);
        if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
            LOG_ERROR("[LeftRobot] 移动到第一个路点失败, 结果 = {}", result);
            return false;
        }

        // 初始化全局运动配置
        result = robot_service_.robotServiceInitGlobalMoveProfile();
        if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
            LOG_ERROR("[LeftRobot] 初始化全局运动配置失败, 结果 = {}", result);
            return false;
        }

        // 清空之前的路点
        robot_service_.robotServiceClearGlobalWayPointVector();

        // 添加所有拉花路点
        for (size_t i = 0; i < points.size(); i++) {
            const auto& waypoint = points[i];
            aubo_robot_namespace::wayPoint_S way_point;
            for (int j = 0; j < aubo_robot_namespace::ARM_DOF && j < (int)waypoint.size(); j++) {
                way_point.jointpos[j] = waypoint[j];
            }

            int result = robot_service_.robotServiceAddGlobalWayPoint(way_point);
            if (result == aubo_robot_namespace::InterfaceCallSuccCode) {
                // LOG_INFO("[LeftRobot] 添加拉花路点成功: 路点{}", i+1);
            } else {
                LOG_ERROR("[LeftRobot] 添加拉花路点失败: 路点{}, 结果 = {}", i+1, result);
                return false;
            }
        }

        // 设置交融半径
        result = robot_service_.robotServiceSetGlobalBlendRadius(motion_params.blend_radius);
        if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
            LOG_ERROR("[LeftRobot] 设置交融半径失败, 结果 = {}", result);
            return false;
        }

        // 设置从配置文件读取的运动参数
        result = robot_service_.robotServiceSetGlobalMoveEndMaxLineAcc(motion_params.line_acceleration);
        if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
            LOG_ERROR("[LeftRobot] 设置末端执行器线性加速度失败, 结果 = {}", result);
            return false;
        }
        result = robot_service_.robotServiceSetGlobalMoveEndMaxAngleAcc(motion_params.angle_acceleration);
        if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
            LOG_ERROR("[LeftRobot] 设置末端执行器角度加速度失败, 结果 = {}", result);
            return false;
        }
        result = robot_service_.robotServiceSetGlobalMoveEndMaxLineVelc(motion_params.line_velocity);
        if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
            LOG_ERROR("[LeftRobot] 设置末端执行器线性速度失败, 结果 = {}", result);
            return false;
        }
        result = robot_service_.robotServiceSetGlobalMoveEndMaxAngleVelc(motion_params.angle_velocity);
        if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
            LOG_ERROR("[LeftRobot] 设置末端执行器角度速度失败, 结果 = {}", result);
            return false;
        }

        aubo_robot_namespace::JointVelcAccParam joint_max_acc;
        for (int i = 0; i < aubo_robot_namespace::ARM_DOF; i++) {
            joint_max_acc.jointPara[i] = motion_params.angle_acceleration;
        }
        result = robot_service_.robotServiceSetGlobalMoveJointMaxAcc(joint_max_acc);
        if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
            LOG_ERROR("[LeftRobot] 设置关节运动最大加速度失败, 结果 = {}", result);
            return false;
        }

        aubo_robot_namespace::JointVelcAccParam joint_max_velc;
        for (int i = 0; i < aubo_robot_namespace::ARM_DOF; i++) {
            joint_max_velc.jointPara[i] = motion_params.angle_velocity;
        }
        result = robot_service_.robotServiceSetGlobalMoveJointMaxVelc(joint_max_velc);
        if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
            LOG_ERROR("[LeftRobot] 设置关节运动最大速度失败, 结果 = {}", result);
            return false;
        }

        // 执行轨迹运动
        LOG_INFO("[LeftRobot] 开始执行轨迹运动，共 {} 个路点", points.size());
        result = robot_service_.robotServiceTrackMove(aubo_robot_namespace::JOINT_GNUBSPLINEINTP, true);
        if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
            LOG_ERROR("[LeftRobot] 轨迹运动失败, 结果 = {}", result);
            return false;
        }

        LOG_INFO("[LeftRobot] 轨迹运动执行成功");
        return true;
    }

    // 关闭机器人
    bool shutdown() {
        if (connected_ && initialized_) {
            LOG_INFO("[LeftRobot] 关闭机器人");
            // robot_service_.robotServiceRobotShutdown();
            initialized_ = false;
            return true;
        }
        return false;
    }

    // 紧急停止
    bool emergency_stop() {
        LOG_WARN("[LeftRobot] 执行紧急停止");

        if (!connected_) {
            LOG_ERROR("[LeftRobot] 紧急停止失败: 未连接");
            return false;
        }

        try {
            // 停止当前运动
            auto result = robot_service_.robotMoveStop();
            if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
                LOG_ERROR("[LeftRobot] 紧急停止失败, 结果 = {}", result);
                return false;
            }

            LOG_INFO("[LeftRobot] 紧急停止成功");
            return true;
        } catch (const std::exception& e) {
            LOG_ERROR("[LeftRobot] 紧急停止异常: {}", e.what());
            return false;
        }
    }

private:
    // 连接到机器人
    bool connect(const std::string& host, int port, const std::string& username, const std::string& password) {
        LOG_INFO("[LeftRobot] 连接到 {}:{}", host, port);

        try {
            int result = robot_service_.robotServiceLogin(host.c_str(), port, username.c_str(), password.c_str());
            if (result == aubo_robot_namespace::InterfaceCallSuccCode) {
                LOG_INFO("[LeftRobot] 登录成功");
                connected_ = true;
            } else {
                LOG_ERROR("[LeftRobot] 登录失败, 结果 = {}", result);
                connected_ = false;
            }
        } catch (std::exception & e) {
            LOG_ERROR("[LeftRobot] 登录异常: {}", e.what());
            connected_ = false;
        }

        return connected_;
    }

    // 断开与机器人的连接
    bool disconnect() {
        if (connected_) {
            LOG_INFO("[LeftRobot] 断开连接");
            robot_service_.robotServiceLogout();
            connected_ = false;
        }
        return true;
    }



    // 配置运动参数
    void configure_movement_parameters() {
        // 设置关节运动最大加速度
        aubo_robot_namespace::JointVelcAccParam joint_max_acc;
        for (int i = 0; i < aubo_robot_namespace::ARM_DOF; i++) {
            joint_max_acc.jointPara[i] = 2.0;
        }
        auto result = robot_service_.robotServiceSetGlobalMoveJointMaxAcc(joint_max_acc);
        if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
            LOG_ERROR("[LeftRobot] 设置关节运动最大加速度失败, 结果 = {}", result);
        }

        // 设置关节运动最大速度
        aubo_robot_namespace::JointVelcAccParam joint_max_velc;
        for (int i = 0; i < aubo_robot_namespace::ARM_DOF; i++) {
            joint_max_velc.jointPara[i] = 2.0;
        }
        result = robot_service_.robotServiceSetGlobalMoveJointMaxVelc(joint_max_velc);
        if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
            LOG_ERROR("[LeftRobot] 设置关节运动最大速度失败, 结果 = {}", result);
        }

        // 设置末端执行器运动参数
        result = robot_service_.robotServiceSetGlobalMoveEndMaxLineAcc(0.436332);
        if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
            LOG_ERROR("[LeftRobot] 设置末端执行器线性加速度失败, 结果 = {}", result);
        }
        result = robot_service_.robotServiceSetGlobalMoveEndMaxAngleAcc(2.0);
        if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
            LOG_ERROR("[LeftRobot] 设置末端执行器角度加速度失败, 结果 = {}", result);
        }
        result = robot_service_.robotServiceSetGlobalMoveEndMaxLineVelc(0.436332);
        if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
            LOG_ERROR("[LeftRobot] 设置末端执行器线性速度失败, 结果 = {}", result);
        }
        result = robot_service_.robotServiceSetGlobalMoveEndMaxAngleVelc(2.0);
        if (result != aubo_robot_namespace::InterfaceCallSuccCode) {
            LOG_ERROR("[LeftRobot] 设置末端执行器角度速度失败, 结果 = {}", result);
        }
    }

    // 检查机器人状态
    bool check_robot_state() {
        if (!connected_) {
            LOG_ERROR("[LeftRobot] 无法移动: 未连接");
            return false;
        }

        if (!initialized_) {
            LOG_ERROR("[LeftRobot] 无法移动: 未初始化");
            return false;
        }

        return true;
    }

    ServiceInterface robot_service_;
    LatteArtConfig latte_art_config_;
    bool connected_ = false;
    bool initialized_ = false;
};

// LeftRobot 公共接口实现
LeftRobot::LeftRobot() {
    impl_ = std::make_unique<Impl>();
}

LeftRobot::~LeftRobot() = default;

bool LeftRobot::init() {
    return impl_->init();
}

bool LeftRobot::move_to_home() {
    return impl_->move_to_home();
}

bool LeftRobot::move_to_ready() {
    // 移动到准备位置（通常是初始位置的一个变体）
    return impl_->move_to_home();
}

bool LeftRobot::move_to_cup_outlet() {
    // 移动到杯子出口位置（取杯序列的第一步）
    return impl_->execute_get_cup_sequence();
}

bool LeftRobot::move_to_coffee_outlet() {
    // 移动到咖啡出口位置（取咖啡序列的第一步）
    return impl_->execute_get_coffee_sequence();
}

bool LeftRobot::move_to_latte_art() {
    // 移动到拉花位置
    return impl_->prepare_for_latte_art();
}

bool LeftRobot::get_cup() {
    return impl_->execute_get_cup_sequence();
}

bool LeftRobot::get_coffee() {
    return impl_->execute_get_coffee_sequence();
}

bool LeftRobot::prepare_for_latte_art() {
    return impl_->prepare_for_latte_art();
}

bool LeftRobot::do_latte_art(LatteArtType art_type) {
    return impl_->execute_latte_art_sequence(art_type);
}

bool LeftRobot::deliver_coffee() {
    return impl_->execute_coffee_delivery_sequence();
}

bool LeftRobot::shutdown() {
    return impl_->shutdown();
}

bool LeftRobot::emergency_stop() {
    return impl_->emergency_stop();
}

} // namespace aubo
