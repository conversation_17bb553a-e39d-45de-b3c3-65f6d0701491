/*
 * Copyright ©2015-2025 AUBO (Beijing) Robotics Technology Co., Ltd. All rights reserved.
 */

#ifndef AUBO_COFFEE_SERVICE_COFFEE_WORKFLOW_H
#define AUBO_COFFEE_SERVICE_COFFEE_WORKFLOW_H

#include <any>
#include <chrono>
#include <functional>
#include <map>
#include <memory>
#include <string>
#include <vector>

#include "../types/coffee_types.h"

namespace aubo {

// 前向声明
class LeftRobot;
class RightRobot;
class CupDispenser;
class CoffeeMachine;
class MilkContainerCleaner;

/**
 * @enum ExecutionType
 * @brief 执行类型枚举
 */
enum class ExecutionType {
    SEQUENTIAL,     ///< 顺序执行
    PARALLEL        ///< 并行执行
};

/**
 * @enum ActionType
 * @brief 动作类型枚举
 */
enum class ActionType {
    ACTION,         ///< 基础动作
    WAIT,           ///< 等待动作
    GROUP           ///< 动作组
};

/**
 * @enum ExecutorType
 * @brief 执行器类型枚举
 */
enum class ExecutorType {
    LEFT_ARM,           ///< 左臂
    RIGHT_ARM,          ///< 右臂
    CUP_DISPENSER,      ///< 杯子分配器
    COFFEE_MACHINE,     ///< 咖啡机
    MILK_CONTAINER_CLEANER  ///< 牛奶容器清洁器
};

/**
 * @struct ActionParameters
 * @brief 动作参数结构
 */
struct ActionParameters {
    std::map<std::string, std::any> params;
    
    template<typename T>
    void set(const std::string& key, const T& value) {
        params[key] = value;
    }
    
    template<typename T>
    T get(const std::string& key, const T& default_value = T{}) const {
        auto it = params.find(key);
        if (it != params.end()) {
            try {
                return std::any_cast<T>(it->second);
            } catch (const std::bad_any_cast&) {
                return default_value;
            }
        }
        return default_value;
    }
    
    bool has(const std::string& key) const {
        return params.find(key) != params.end();
    }
};

/**
 * @struct WorkflowAction
 * @brief 工作流动作结构
 */
struct WorkflowAction {
    ActionType type;                    ///< 动作类型
    ExecutorType executor;              ///< 执行器类型
    std::string operation;              ///< 操作名称
    ActionParameters parameters;        ///< 动作参数
    int duration;                       ///< 等待时长(秒，仅用于WAIT类型)
    ExecutionType execution;            ///< 执行类型(仅用于GROUP类型)
    std::vector<WorkflowAction> actions; ///< 子动作列表(仅用于GROUP类型)

    WorkflowAction() : type(ActionType::ACTION), executor(ExecutorType::LEFT_ARM), duration(0), execution(ExecutionType::SEQUENTIAL) {}
};

/**
 * @struct WorkflowStep
 * @brief 工作流步骤结构
 */
struct WorkflowStep {
    std::string id;                     ///< 步骤ID
    std::string name;                   ///< 步骤名称
    ExecutionType execution;            ///< 执行类型
    std::vector<WorkflowAction> actions; ///< 动作列表

    WorkflowStep() : execution(ExecutionType::SEQUENTIAL) {}
};

/**
 * @struct CoffeeWorkflow
 * @brief 咖啡制作工作流结构
 */
struct CoffeeWorkflow {
    std::string name;                   ///< 工作流名称
    std::string description;            ///< 工作流描述
    std::vector<WorkflowStep> steps;    ///< 步骤列表
    
    CoffeeWorkflow() = default;
    CoffeeWorkflow(const std::string& workflow_name, const std::string& workflow_description)
        : name(workflow_name), description(workflow_description) {}
};

/**
 * @struct WorkflowSettings
 * @brief 工作流全局设置
 */
struct WorkflowSettings {
    int default_timeout;                ///< 默认超时时间
    int retry_attempts;                 ///< 重试次数
    bool emergency_stop_on_failure;     ///< 失败时紧急停止
    double parallel_execution_delay;    ///< 并行执行延迟
    
    WorkflowSettings() 
        : default_timeout(30), retry_attempts(2), 
          emergency_stop_on_failure(true), parallel_execution_delay(0.5) {}
};

/**
 * @class CoffeeWorkflowEngine
 * @brief 咖啡制作工作流执行引擎
 */
class CoffeeWorkflowEngine {
public:
    /**
     * @brief 构造函数
     * @param left_robot 左臂机器人实例
     * @param right_robot 右臂机器人实例
     * @param cup_dispenser 杯子分配器实例
     * @param coffee_machine 咖啡机实例
     * @param milk_container_cleaner 牛奶容器清洁器实例
     */
    CoffeeWorkflowEngine(std::shared_ptr<LeftRobot> left_robot,
                        std::shared_ptr<RightRobot> right_robot,
                        std::shared_ptr<CupDispenser> cup_dispenser = nullptr,
                        std::shared_ptr<CoffeeMachine> coffee_machine = nullptr,
                        std::shared_ptr<MilkContainerCleaner> milk_container_cleaner = nullptr);
    
    /**
     * @brief 析构函数
     */
    ~CoffeeWorkflowEngine();
    
    /**
     * @brief 从工作流目录加载所有工作流
     * @param workflow_directory 工作流目录路径
     * @return 加载成功返回true
     */
    bool load_workflows_from_directory(const std::string& workflow_directory);

    /**
     * @brief 加载单个工作流文件
     * @param workflow_file_path 工作流文件路径
     * @return 加载成功返回true
     */
    bool load_single_workflow(const std::string& workflow_file_path);
    
    /**
     * @brief 获取可用的工作流列表
     * @return 工作流名称列表
     */
    std::vector<std::string> get_available_workflows() const;
    
    /**
     * @brief 获取指定工作流
     * @param workflow_name 工作流名称
     * @return 工作流结构，如果不存在返回空
     */
    CoffeeWorkflow get_workflow(const std::string& workflow_name) const;
    
    /**
     * @brief 执行工作流
     * @param workflow_name 工作流名称
     * @param order 咖啡订单（用于参数传递）
     * @return 执行成功返回true
     */
    bool execute_workflow(const std::string& workflow_name, const CoffeeOrder& order);
    
    /**
     * @brief 执行自定义工作流
     * @param workflow 工作流结构
     * @param order 咖啡订单（用于参数传递）
     * @return 执行成功返回true
     */
    bool execute_workflow(const CoffeeWorkflow& workflow, const CoffeeOrder& order);
    
    /**
     * @brief 停止当前执行的工作流
     * @return 停止成功返回true
     */
    bool stop_current_workflow();
    
    /**
     * @brief 获取当前执行状态
     * @return 当前是否正在执行工作流
     */
    bool is_executing() const;
    
    /**
     * @brief 获取当前执行的步骤信息
     * @return 当前步骤名称，如果未执行返回空字符串
     */
    std::string get_current_step() const;
    
    /**
     * @brief 设置工作流全局设置
     * @param settings 全局设置
     */
    void set_global_settings(const WorkflowSettings& settings);
    
    /**
     * @brief 获取工作流全局设置
     * @return 全局设置
     */
    WorkflowSettings get_global_settings() const;



private:
    class Impl;
    std::unique_ptr<Impl> impl_;
};

/**
 * @brief 从字符串解析执行类型
 * @param type_string 类型字符串
 * @return 执行类型
 */
ExecutionType string_to_execution_type(const std::string& type_string);

/**
 * @brief 从字符串解析动作类型
 * @param type_string 类型字符串
 * @return 动作类型
 */
ActionType string_to_action_type(const std::string& type_string);

/**
 * @brief 从字符串解析执行器类型
 * @param executor_string 执行器字符串
 * @return 执行器类型
 */
ExecutorType string_to_executor_type(const std::string& executor_string);

/**
 * @brief 获取执行类型的字符串表示
 * @param type 执行类型
 * @return 字符串表示
 */
std::string execution_type_to_string(ExecutionType type);

/**
 * @brief 获取动作类型的字符串表示
 * @param type 动作类型
 * @return 字符串表示
 */
std::string action_type_to_string(ActionType type);

/**
 * @brief 获取执行器类型的字符串表示
 * @param executor 执行器类型
 * @return 字符串表示
 */
std::string executor_type_to_string(ExecutorType executor);

} // namespace aubo

#endif // AUBO_COFFEE_SERVICE_COFFEE_WORKFLOW_H
