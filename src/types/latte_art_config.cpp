/*
 * Copyright ©2015-2025 AUBO (Beijing) Robotics Technology Co., Ltd. All rights reserved.
 */

#include "latte_art_config.h"
// 注意：由于 fmt/compile.h 中的 FMT_ENABLE_IF 宏与编译环境不兼容，
// 暂时不包含 aubo-base/log.h，改用 std::cout
#include <fstream>
#include <sstream>
#include <map>
#include <iostream>
#include <stdexcept>
#include <memory>
#include <nlohmann/json.hpp>

using json = nlohmann::json;

namespace aubo {

// LatteArtTrajectory 实现
LatteArtTrajectory::LatteArtTrajectory(const std::string& trajectory_name,
                                       const std::string& trajectory_description)
    : name(trajectory_name), description(trajectory_description) {
}

// MotionParameters 实现
MotionParameters::MotionParameters()
    : line_acceleration(0.1)
    , angle_acceleration(0.436332)
    , line_velocity(0.1)
    , angle_velocity(0.436332)
    , blend_radius(0.01) {
}

// LatteArtConfig 实现类
class LatteArtConfig::Impl {
public:
    Impl() : loaded_(false) {}

    bool load_from_directory(const std::string& config_dir_path) {
        config_dir_path_ = config_dir_path;

        bool success = true;
        success &= load_latte_art_from_file(config_dir_path + "/heart.json", LatteArtType::HEART);
        success &= load_latte_art_from_file(config_dir_path + "/leaf.json", LatteArtType::LEAF);
        success &= load_latte_art_from_file(config_dir_path + "/tulip.json", LatteArtType::TULIP);
        success &= load_latte_art_from_file(config_dir_path + "/swan.json", LatteArtType::SWAN);

        if (success) {
            loaded_ = true;
            std::cout << "[LatteArtConfig] 所有配置加载成功" << std::endl;
        } else {
            std::cout << "[LatteArtConfig] 部分配置加载失败" << std::endl;
        }

        return success;
    }

    bool load_latte_art_from_file(const std::string& latte_art_file_path, LatteArtType art_type) {
        std::ifstream file(latte_art_file_path);
        if (!file.is_open()) {
            std::cout << "[LatteArtConfig] 无法打开拉花配置文件: " << latte_art_file_path << std::endl;
            return false;
        }

        try {
            json j;
            file >> j;
            file.close();

            return load_single_latte_art(j, art_type);
        } catch (const std::exception& e) {
            std::cerr << "[LatteArtConfig] 拉花配置解析失败: " << e.what() << std::endl;
            return false;
        }
    }

    bool load_single_latte_art(const json& j, LatteArtType art_type) {
        try {
            LatteArtTrajectory trajectory;
    
            // 加载基本信息
            if (j.contains("name")) {
                trajectory.name = j["name"];
            }
    
            if (j.contains("description")) {
                trajectory.description = j["description"];
            }
    
            // 加载运动参数
            if (j.contains("motion_parameters")) {
                load_motion_parameters(j["motion_parameters"], trajectory.motion_parameters);
            } else {
                trajectory.motion_parameters = MotionParameters();  // 使用默认参数
            }
    
            // 加载左臂轨迹
            if (j.contains("left_robot") && j["left_robot"].contains("waypoints")) {
                for (const auto& waypoint : j["left_robot"]["waypoints"]) {
                    if (waypoint.is_array() && waypoint.size() == 6) {
                        std::vector<double> joints = waypoint;
                        trajectory.left_waypoints.push_back(joints);
                    }
                }
            }
    
            // 加载右臂轨迹
            if (j.contains("right_robot") && j["right_robot"].contains("waypoints")) {
                for (const auto& waypoint : j["right_robot"]["waypoints"]) {
                    if (waypoint.is_array() && waypoint.size() == 6) {
                        std::vector<double> joints = waypoint;
                        trajectory.right_waypoints.push_back(joints);
                    }
                }
            }
    
            trajectories_[art_type] = trajectory;
            std::cout << "[LatteArtConfig] 加载轨迹: " << trajectory.name
                      << " (左臂:" << trajectory.left_waypoints.size() << "个路点, "
                      << "右臂:" << trajectory.right_waypoints.size() << "个路点)" << std::endl;
    
            return true;
        } catch (const std::exception& e) {
            std::cerr << "[LatteArtConfig] 轨迹数据解析失败: " << e.what() << std::endl;
            return false;
        }
    }

    LatteArtTrajectory get_latte_art_trajectory(LatteArtType art_type) const {
        auto it = trajectories_.find(art_type);
        if (it != trajectories_.end()) {
            return it->second;
        }
        std::cout << "[LatteArtConfig] 未找到拉花类型 " << latte_art_type_to_string(art_type) << " 的轨迹" << std::endl;
        return LatteArtTrajectory();
    }

    std::vector<std::vector<double>> get_left_robot_waypoints(LatteArtType art_type) const {
        auto it = trajectories_.find(art_type);
        if (it != trajectories_.end()) {
            return it->second.left_waypoints;
        }
        return std::vector<std::vector<double>>();
    }

    std::vector<std::vector<double>> get_right_robot_waypoints(LatteArtType art_type) const {
        auto it = trajectories_.find(art_type);
        if (it != trajectories_.end()) {
            return it->second.right_waypoints;
        }
        return std::vector<std::vector<double>>();
    }

    MotionParameters get_motion_parameters(LatteArtType art_type) const {
        auto it = trajectories_.find(art_type);
        if (it != trajectories_.end()) {
            return it->second.motion_parameters;
        }
        return MotionParameters();  // 返回默认参数
    }

    bool is_loaded() const {
        return loaded_;
    }

    bool reload() {
        if (config_dir_path_.empty()) {
            std::cerr << "[LatteArtConfig] 无法重新加载：未指定配置目录路径" << std::endl;
            return false;
        }
        return load_from_directory(config_dir_path_);
    }

    std::vector<LatteArtType> get_supported_latte_art_types() const {
        std::vector<LatteArtType> types;
        for (const auto& pair : trajectories_) {
            types.push_back(pair.first);
        }
        return types;
    }

private:
    void load_motion_parameters(const json& params_data, MotionParameters& motion_params) {
        if (params_data.contains("line_acceleration")) {
            motion_params.line_acceleration = params_data["line_acceleration"];
        }
    
        if (params_data.contains("angle_acceleration")) {
            motion_params.angle_acceleration = params_data["angle_acceleration"];
        }
    
        if (params_data.contains("line_velocity")) {
            motion_params.line_velocity = params_data["line_velocity"];
        }
    
        if (params_data.contains("angle_velocity")) {
            motion_params.angle_velocity = params_data["angle_velocity"];
        }
    
        if (params_data.contains("blend_radius")) {
            motion_params.blend_radius = params_data["blend_radius"];
        }
    }

public:
    static std::string latte_art_type_to_string(LatteArtType art_type) {
        switch (art_type) {
            case LatteArtType::NONE:     return "NONE";
            case LatteArtType::HEART:    return "HEART";
            case LatteArtType::LEAF:     return "LEAF";
            case LatteArtType::TULIP:    return "TULIP";
            case LatteArtType::SWAN:     return "SWAN";
            default:                     return "UNKNOWN";
        }
    }

    static LatteArtType string_to_latte_art_type(const std::string& type_string) {
        if (type_string == "NONE")     return LatteArtType::NONE;
        if (type_string == "HEART")    return LatteArtType::HEART;
        if (type_string == "LEAF")     return LatteArtType::LEAF;
        if (type_string == "TULIP")    return LatteArtType::TULIP;
        if (type_string == "SWAN")     return LatteArtType::SWAN;
        return LatteArtType::NONE;
    }

    bool loaded_;
    std::string config_dir_path_;
    std::map<LatteArtType, LatteArtTrajectory> trajectories_;
};

// LatteArtConfig 公共接口实现
LatteArtConfig::LatteArtConfig() {
    impl_ = std::make_unique<Impl>();
}

LatteArtConfig::~LatteArtConfig() = default;

bool LatteArtConfig::load_from_directory(const std::string& config_dir_path) {
    return impl_->load_from_directory(config_dir_path);
}

bool LatteArtConfig::load_latte_art_from_file(const std::string& latte_art_file_path, LatteArtType art_type) {
    return impl_->load_latte_art_from_file(latte_art_file_path, art_type);
}

LatteArtTrajectory LatteArtConfig::get_latte_art_trajectory(LatteArtType art_type) const {
    return impl_->get_latte_art_trajectory(art_type);
}

std::vector<std::vector<double>> LatteArtConfig::get_left_robot_waypoints(LatteArtType art_type) const {
    return impl_->get_left_robot_waypoints(art_type);
}

std::vector<std::vector<double>> LatteArtConfig::get_right_robot_waypoints(LatteArtType art_type) const {
    return impl_->get_right_robot_waypoints(art_type);
}

MotionParameters LatteArtConfig::get_motion_parameters(LatteArtType art_type) const {
    return impl_->get_motion_parameters(art_type);
}

bool LatteArtConfig::is_loaded() const {
    return impl_->is_loaded();
}

bool LatteArtConfig::reload() {
    return impl_->reload();
}

std::vector<LatteArtType> LatteArtConfig::get_supported_latte_art_types() const {
    return impl_->get_supported_latte_art_types();
}

bool LatteArtConfig::validate_trajectory(const LatteArtTrajectory& trajectory) {
    // 检查左臂轨迹
    if (!trajectory.left_waypoints.empty()) {
        for (const auto& waypoint : trajectory.left_waypoints) {
            if (waypoint.size() != 6) {  // 必须有6个关节角度
                return false;
            }

            for (size_t i = 0; i < waypoint.size(); i++) {
                // 检查关节角度是否在合理范围内
                if (waypoint[i] < -6.28318 || waypoint[i] > 6.28318) {
                    return false;
                }
            }
        }
    }

    // 检查右臂轨迹
    if (!trajectory.right_waypoints.empty()) {
        for (const auto& waypoint : trajectory.right_waypoints) {
            if (waypoint.size() != 6) {  // 必须有6个关节角度
                return false;
            }

            for (size_t i = 0; i < waypoint.size(); i++) {
                // 检查关节角度是否在合理范围内
                if (waypoint[i] < -6.28318 || waypoint[i] > 6.28318) {
                    return false;
                }
            }
        }
    }

    // 至少要有左臂或右臂轨迹之一
    return !trajectory.left_waypoints.empty() || !trajectory.right_waypoints.empty();
}

std::string LatteArtConfig::latte_art_type_to_string(LatteArtType art_type) {
    return Impl::latte_art_type_to_string(art_type);
}

LatteArtType LatteArtConfig::string_to_latte_art_type(const std::string& type_string) {
    return Impl::string_to_latte_art_type(type_string);
}

} // namespace aubo
