/*
 * Copyright ©2015-2025 AUBO (Beijing) Robotics Technology Co., Ltd. All rights reserved.
 */

#include "coffee_types.h"

namespace aubo {

std::string get_coffee_type_name(CoffeeType type) {
    switch (type) {
        case CoffeeType::AMERICANO:  return "美式咖啡";
        case CoffeeType::LATTE:      return "拿铁";
        case CoffeeType::CAPPUCCINO: return "卡布奇诺";
        default:                     return "未知咖啡";
    }
}

std::string get_latte_art_name(LatteArtType type) {
    switch (type) {
        case LatteArtType::NONE:     return "无拉花";
        case LatteArtType::HEART:    return "心形";
        case LatteArtType::LEAF:     return "叶子";
        case LatteArtType::TULIP:    return "郁金香";
        case LatteArtType::SWAN:     return "天鹅";
        default:                     return "未知拉花";
    }
}





} // namespace aubo
