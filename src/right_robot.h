/*
 * Copyright ©2015-2025 AUBO (Beijing) Robotics Technology Co., Ltd. All rights reserved.
 */

#ifndef AUBO_COFFEE_SERVICE_RIGHT_ROBOT_H
#define AUBO_COFFEE_SERVICE_RIGHT_ROBOT_H

#include <memory>
#include "coffee_types.h"

namespace aubo {

/**
 * @class RightRobot
 * @brief 控制咖啡服务的右侧机器人臂
 *
 * 该类用于控制咖啡服务中的右侧机器人臂。它主要负责接奶和协助拉花功能。
 * 右臂与左臂协调工作，在咖啡制作过程中提供必要的辅助操作。
 */
class RightRobot {
public:
    /**
     * @brief 构造函数
     */
    RightRobot();

    /**
     * @brief 析构函数
     */
    ~RightRobot();

    /**
     * @brief 初始化机器人
     *
     * 连接到机器人并初始化它以进行操作。
     *
     * @return 如果初始化成功则返回true
     */
    bool init();

    /**
     * @brief 将机器人移动到初始位置
     *
     * @return 如果移动成功则返回true
     */
    bool move_to_home();

    /**
     * @brief 执行接奶序列
     *
     * 从奶源获取牛奶，为制作拿铁、卡布奇诺等需要牛奶的咖啡做准备
     *
     * @return 如果序列执行成功则返回true
     */
    bool get_milk();

    /**
     * @brief 执行摇奶动作
     *
     * 在取完奶后执行转圈摇奶动作，使牛奶充分混合，为拉花做准备
     * 通常在 get_milk() 之后、prepare_for_latte_art() 之前调用
     *
     * @return 如果摇奶动作执行成功则返回true
     */
    bool shake_milk();

    /**
     * @brief 准备拉花位置
     *
     * 将右臂移动到拉花的准备位置，这是所有拉花类型的通用准备动作
     * 与左臂配合，共同完成拉花动作
     *
     * @return 如果移动成功则返回true
     */
    bool prepare_for_latte_art();

    /**
     * @brief 执行拉花动作
     *
     * 右臂执行拉花动作，与左臂配合完成完整的拉花过程
     * 需要在调用 prepare_for_latte_art() 之后调用
     *
     * @param art_type 拉花类型，默认为无拉花
     * @return 如果拉花动作执行成功则返回true
     */
    bool do_latte_art(LatteArtType art_type = LatteArtType::NONE);

    /**
     * @brief 倾倒剩余牛奶
     *
     * 拉花完成后倾倒剩余的牛奶，避免浪费
     * 通常在 do_latte_art() 之后调用
     *
     * @return 如果倾倒动作执行成功则返回true
     */
    bool pour_remaining_milk();

    /**
     * @brief 执行清洗过程
     *
     * 倾倒牛奶完成后进行清洗，保持设备卫生
     * 通常在 pour_remaining_milk() 之后调用
     *
     * @return 如果清洗过程执行成功则返回true
     */
    bool clean();

    /**
     * @brief 关闭机器人
     *
     * 安全关闭机器人连接和资源
     *
     * @return 如果关闭成功则返回true
     */
    bool shutdown();

    /**
     * @brief 紧急停止
     *
     * 立即停止机器人的所有动作
     *
     * @return 如果紧急停止成功则返回true
     */
    bool emergency_stop();

private:
    class Impl;
    std::unique_ptr<Impl> impl_;
};

} // namespace aubo

#endif // AUBO_COFFEE_SERVICE_RIGHT_ROBOT_H
