/*
 * Copyright ©2015-2025 AUBO (Beijing) Robotics Technology Co., Ltd. All rights reserved.
 */

#ifndef AUBO_COFFEE_SERVICE_LATTE_ART_CONFIG_H
#define AUBO_COFFEE_SERVICE_LATTE_ART_CONFIG_H

#include <string>
#include <vector>
#include <memory>
#include "coffee_types.h"
#include <aubo_sdk/AuboRobotMetaType.h>

namespace aubo {

/**
 * @struct MotionParameters
 * @brief 运动参数结构
 */
struct MotionParameters {
    double line_acceleration;      ///< 线性加速度 (m/s²)
    double angle_acceleration;     ///< 角度加速度 (rad/s²)
    double line_velocity;          ///< 线性速度 (m/s)
    double angle_velocity;         ///< 角度速度 (rad/s)
    double blend_radius;           ///< 混合半径 (m)

    MotionParameters();
};

/**
 * @struct LatteArtTrajectory
 * @brief 拉花轨迹结构
 */
struct LatteArtTrajectory {
    std::string name;                                           ///< 轨迹名称
    std::string description;                                    ///< 轨迹描述
    MotionParameters motion_parameters;                         ///< 运动参数
    std::vector<std::vector<double>> left_waypoints;           ///< 左臂路点列表
    std::vector<std::vector<double>> right_waypoints;          ///< 右臂路点列表

    LatteArtTrajectory() = default;
    LatteArtTrajectory(const std::string& trajectory_name,
                       const std::string& trajectory_description);
};

/**
 * @class LatteArtConfig
 * @brief 拉花配置管理类
 */
class LatteArtConfig {
public:
    /**
     * @brief 构造函数
     */
    LatteArtConfig();
    
    /**
     * @brief 析构函数
     */
    ~LatteArtConfig();
    
    /**
     * @brief 从配置目录加载所有拉花轨迹
     * @param config_dir_path 配置目录路径，默认为 "share/config"
     * @return 加载成功返回true
     */
    bool load_from_directory(const std::string& config_dir_path = "share/config");

    /**
     * @brief 从单个拉花配置文件加载轨迹
     * @param latte_art_file_path 拉花配置文件路径
     * @param art_type 拉花类型
     * @return 加载成功返回true
     */
    bool load_latte_art_from_file(const std::string& latte_art_file_path, LatteArtType art_type);
    
    /**
     * @brief 获取拉花轨迹（包含左右臂轨迹和运动参数）
     * @param art_type 拉花类型
     * @return 拉花轨迹，如果不存在返回空轨迹
     */
    LatteArtTrajectory get_latte_art_trajectory(LatteArtType art_type) const;

    /**
     * @brief 获取左臂拉花轨迹路点
     * @param art_type 拉花类型
     * @return 左臂路点列表
     */
    std::vector<std::vector<double>> get_left_robot_waypoints(LatteArtType art_type) const;

    /**
     * @brief 获取右臂拉花轨迹路点
     * @param art_type 拉花类型
     * @return 右臂路点列表
     */
    std::vector<std::vector<double>> get_right_robot_waypoints(LatteArtType art_type) const;

    /**
     * @brief 获取拉花类型的运动参数
     * @param art_type 拉花类型
     * @return 运动参数
     */
    MotionParameters get_motion_parameters(LatteArtType art_type) const;
    
    /**
     * @brief 检查配置是否已加载
     * @return 已加载返回true
     */
    bool is_loaded() const;
    
    /**
     * @brief 重新加载配置
     * @return 重新加载成功返回true
     */
    bool reload();
    
    /**
     * @brief 获取支持的拉花类型列表
     * @return 拉花类型列表
     */
    std::vector<LatteArtType> get_supported_latte_art_types() const;
    
    /**
     * @brief 验证轨迹数据的有效性
     * @param trajectory 要验证的轨迹
     * @return 有效返回true
     */
    static bool validate_trajectory(const LatteArtTrajectory& trajectory);
    
    /**
     * @brief 获取拉花类型的字符串表示
     * @param art_type 拉花类型
     * @return 字符串表示
     */
    static std::string latte_art_type_to_string(LatteArtType art_type);
    
    /**
     * @brief 从字符串解析拉花类型
     * @param type_string 类型字符串
     * @return 拉花类型
     */
    static LatteArtType string_to_latte_art_type(const std::string& type_string);

private:
    class Impl;
    std::unique_ptr<Impl> impl_;
};

} // namespace aubo

#endif // AUBO_COFFEE_SERVICE_LATTE_ART_CONFIG_H
