/*
 * Copyright ©2015-2025 AUBO (Beijing) Robotics Technology Co., Ltd. All rights reserved.
 */

#ifndef AUBO_COFFEE_SERVICE_COFFEE_TYPES_H
#define AUBO_COFFEE_SERVICE_COFFEE_TYPES_H

#include <string>

namespace aubo {

/**
 * @enum CoffeeType
 * @brief 咖啡类型枚举
 */
enum class CoffeeType {
    AMERICANO,      ///< 美式咖啡
    LATTE,          ///< 拿铁
    CAPPUCCINO      ///< 卡布奇诺
};

/**
 * @enum LatteArtType
 * @brief 拉花类型枚举
 */
enum class LatteArtType {
    NONE,           ///< 无拉花
    HEART,          ///< 心形
    LEAF,           ///< 叶子
    TULIP,          ///< 郁金香
    SWAN            ///< 天鹅
};

/**
 * @enum RobotArm
 * @brief 机器人臂枚举
 */
enum class RobotArm {
    LEFT,           ///< 左臂
    RIGHT,          ///< 右臂
    BOTH            ///< 双臂
};

/**
 * @enum CoffeeStatus
 * @brief 咖啡制作状态枚举
 */
enum class CoffeeStatus {
    IDLE,           ///< 空闲
    INITIALIZING,   ///< 初始化中
    PREPARING,      ///< 准备中
    MAKING,         ///< 制作中
    FINISHING,      ///< 完成中
    COMPLETED,      ///< 已完成
    ERROR           ///< 错误
};

/**
 * @struct CoffeeOrder
 * @brief 咖啡订单结构
 */
struct CoffeeOrder {
    std::string order_id;       ///< 订单ID
    CoffeeType type;            ///< 咖啡类型
    LatteArtType latte_art;     ///< 拉花类型
    int quantity;               ///< 数量

    /**
     * @brief 构造函数
     * @param order_id 订单ID
     * @param coffee_type 咖啡类型
     * @param latte_art_type 拉花类型
     * @param quantity 数量
     */
    CoffeeOrder(const std::string& order_id = "",
                CoffeeType coffee_type = CoffeeType::AMERICANO,
                LatteArtType latte_art_type = LatteArtType::NONE,
                int quantity = 1)
        : order_id(order_id), type(coffee_type), latte_art(latte_art_type), quantity(quantity) {}

    /**
     * @brief 检查是否需要牛奶
     * @return 如果需要牛奶则返回true
     */
    bool needs_milk() const {
        return type == CoffeeType::LATTE ||
               type == CoffeeType::CAPPUCCINO;
    }

    /**
     * @brief 检查是否需要拉花
     * @return 如果需要拉花则返回true
     */
    bool needs_latte_art() const {
        return needs_milk() && latte_art != LatteArtType::NONE;
    }

    /**
     * @brief 获取对应的工作流名称
     * @return 工作流名称
     */
    std::string get_workflow_name() const {
        switch (type) {
            case CoffeeType::AMERICANO:
                return "americano";
            case CoffeeType::LATTE:
                switch (latte_art) {
                    case LatteArtType::HEART: return "latte_heart";
                    case LatteArtType::LEAF: return "latte_leaf";
                    case LatteArtType::TULIP: return "latte_tulip";
                    case LatteArtType::SWAN: return "latte_swan";
                    default: return "latte_heart"; // 默认心形
                }
            case CoffeeType::CAPPUCCINO:
                return "cappuccino_heart"; // 卡布奇诺只支持心形
            default:
                return "americano";
        }
    }

    /**
     * @brief 验证咖啡和拉花组合是否有效
     * @return 如果组合有效则返回true
     */
    bool is_valid_combination() const {
        switch (type) {
            case CoffeeType::AMERICANO:
                return latte_art == LatteArtType::NONE;
            case CoffeeType::LATTE:
                return latte_art == LatteArtType::HEART ||
                       latte_art == LatteArtType::LEAF ||
                       latte_art == LatteArtType::TULIP ||
                       latte_art == LatteArtType::SWAN;
            case CoffeeType::CAPPUCCINO:
                return latte_art == LatteArtType::HEART;
            default:
                return false;
        }
    }
};

/**
 * @brief 获取咖啡类型的名称
 * @param type 咖啡类型
 * @return 咖啡类型的名称字符串
 */
std::string get_coffee_type_name(CoffeeType type);

/**
 * @brief 获取拉花类型的名称
 * @param type 拉花类型
 * @return 拉花类型的名称字符串
 */
std::string get_latte_art_name(LatteArtType type);

} // namespace aubo

#endif // AUBO_COFFEE_SERVICE_COFFEE_TYPES_H
