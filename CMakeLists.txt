# Copyright ©2015-2024 Aubo (Beijing) Robotics Technology Co., Ltd. All rights reserved.

cmake_minimum_required(VERSION 3.22.1)

# Use aubo-toolchain. (must be set before project)
set(CMAKE_TOOLCHAIN_FILE "${CMAKE_CURRENT_LIST_DIR}/cmake/modules/aubo-toolchain.cmake")

project(aubo-coffee-service
    VERSION 0.1.0
    DESCRIPTION "AUBO Coffee Service"
    HOMEPAGE_URL "https://www.aubo-robotics.cn"
    LANGUAGES CXX
)

# Compiler settings (must be set after project)
include(cmake/modules/compiler-options.cmake)

# basic config. (set cxx standard, etc.)
include(cmake/modules/base-config.cmake)

find_package(aubo-base CONFIG REQUIRED)
find_package(Threads REQUIRED)
find_package(eclipse-paho-mqtt-c CONFIG REQUIRED)
find_package(Taskflow CONFIG REQUIRED)
find_package(cpprestsdk CONFIG REQUIRED)

add_library(aubo_sdk::aubo_sdk SHARED IMPORTED)
set_target_properties(aubo_sdk::aubo_sdk PROPERTIES
    IMPORTED_LOCATION "${AUBO_THIRD_PARTY_DIR}/${AUBO_SYSTEM}/lib/libaubo_sdk.so"
    INTERFACE_INCLUDE_DIRECTORIES "${AUBO_THIRD_PARTY_DIR}/${AUBO_SYSTEM}/include"
)

set(SOURCES
    ${CMAKE_CURRENT_LIST_DIR}/src/coffee_system.cpp
    ${CMAKE_CURRENT_LIST_DIR}/src/coffee_types.cpp
    ${CMAKE_CURRENT_LIST_DIR}/src/coffee_workflow.cpp
    ${CMAKE_CURRENT_LIST_DIR}/src/latte_art_config.cpp
    ${CMAKE_CURRENT_LIST_DIR}/src/left_robot.cpp
    ${CMAKE_CURRENT_LIST_DIR}/src/right_robot.cpp
)

if(BUILD_EXECUTABLE)
    add_executable(${PROJECT_NAME}
        ${SOURCES}
        src/main.cpp
    )
else()
    if(BUILD_SHARED_LIBS)
        add_library(${PROJECT_NAME} SHARED ${SOURCES})
    else()
        add_library(${PROJECT_NAME} STATIC ${SOURCES})
    endif()
endif()

if(BUILD_TESTING)
    add_subdirectory(tests)
endif()

if(BUILD_EXAMPLES)
    add_subdirectory(examples)
endif()

# target common config. (set target properties, include directories, etc.)
include(cmake/modules/target-config.cmake)

target_link_libraries(${PROJECT_NAME}
    PUBLIC aubo_sdk::aubo_sdk Threads::Threads
    PUBLIC aubo::aubo-base
    PRIVATE cpprestsdk::cpprest cpprestsdk::cpprestsdk_zlib_internal cpprestsdk::cpprestsdk_boost_internal cpprestsdk::cpprestsdk_openssl_internal
    PRIVATE eclipse-paho-mqtt-c::paho-mqtt3cs-static eclipse-paho-mqtt-c::paho-mqtt3as-static
    PRIVATE Taskflow::Taskflow
)

# Configure common installation. (install `include` directories, etc.)
include(cmake/modules/install-config.cmake)

# Project specific installation

# Configure packaging. (use `cpack` to generate a package)
include(cmake/modules/packaging.cmake)

# Print build information
include(FeatureSummary)
feature_summary(WHAT ALL)

# Copy config data files to build directory
add_custom_command(TARGET ${PROJECT_NAME} PRE_BUILD
        COMMAND ${CMAKE_COMMAND} -E echo "Copying config files"
        COMMAND ${CMAKE_COMMAND} -E copy_directory
        ${CMAKE_CURRENT_LIST_DIR}/share/config $<TARGET_FILE_DIR:${PROJECT_NAME}>/../share/config/
)
