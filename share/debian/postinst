#!/bin/bash

# Enable strict error handling
set -e
set -o pipefail

echo "Starting installation of aubo-coffee-service service..."

mkdir -p /etc/systemd/system/

echo "Copying service file to /etc/systemd/system/"
if ! cp /opt/aubo-coffee-service/systemd/aubo-coffee-service.service /etc/systemd/system/; then
    echo "Error: Failed to copy service file" >&2
    exit 1
fi

echo "Reloading systemd configuration..."
if ! systemctl daemon-reload; then
    echo "Warning: systemctl daemon-reload command failed" >&2
fi

echo "Enabling aubo-coffee-service service..."
if ! systemctl enable aubo-coffee-service.service; then
    echo "Error: Failed to enable service" >&2
    exit 1
fi

echo "Starting aubo-coffee-service service..."
if ! systemctl start aubo-coffee-service.service; then
    echo "Error: Failed to start service" >&2
    exit 1
fi

if [ -d "/opt/aubo-coffee-service/systemd" ]; then
    echo "Cleaning up /opt/aubo-coffee-service/systemd"
    rm -rf /opt/aubo-coffee-service/systemd
fi

if [ -d "/opt/aubo-coffee-service/debian" ]; then
    echo "Cleaning up /opt/aubo-coffee-service/debian"
    rm -rf /opt/aubo-coffee-service/debian
fi

echo "Installation of aubo-coffee-service completed"
