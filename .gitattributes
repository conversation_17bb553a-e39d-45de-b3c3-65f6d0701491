# 动态库文件
*.dll filter=lfs diff=lfs merge=lfs -text
*.so* filter=lfs diff=lfs merge=lfs -text
*.dylib filter=lfs diff=lfs merge=lfs -text

# 静态库文件
*.lib filter=lfs diff=lfs merge=lfs -text
*.a filter=lfs diff=lfs merge=lfs -text

# 图片文件
*.png filter=lfs diff=lfs merge=lfs -text
*.jpg filter=lfs diff=lfs merge=lfs -text
*.jpeg filter=lfs diff=lfs merge=lfs -text
*.gif filter=lfs diff=lfs merge=lfs -text
*.bmp filter=lfs diff=lfs merge=lfs -text
*.tiff filter=lfs diff=lfs merge=lfs -text
*.svg filter=lfs diff=lfs merge=lfs -text

# 音乐文件
*.mp3 filter=lfs diff=lfs merge=lfs -text
*.wav filter=lfs diff=lfs merge=lfs -text
*.aac filter=lfs diff=lfs merge=lfs -text
*.flac filter=lfs diff=lfs merge=lfs -text
*.ogg filter=lfs diff=lfs merge=lfs -text
*.m4a filter=lfs diff=lfs merge=lfs -text

# 压缩包文件
*.zip filter=lfs diff=lfs merge=lfs -text
*.tar filter=lfs diff=lfs merge=lfs -text
*.tar.gz filter=lfs diff=lfs merge=lfs -text
*.rar filter=lfs diff=lfs merge=lfs -text
*.7z filter=lfs diff=lfs merge=lfs -text

# ONNX模型
*.onnx filter=lfs diff=lfs merge=lfs -text

# 字体
*.ttf filter=lfs diff=lfs merge=lfs -text
*.oft filter=lfs diff=lfs merge=lfs -text
*.woff filter=lfs diff=lfs merge=lfs -text
*.woff2 filter=lfs diff=lfs merge=lfs -text

# 追踪特定目录下的所有文件
bin/** filter=lfs diff=lfs merge=lfs -text

# 测试数据
tests/**/*.txt filter=lfs diff=lfs merge=lfs -text

# 点云数据
*.pcd filter=lfs diff=lfs merge=lfs -text

# 排除CMakeList.txt文件
CMakeLists.txt filter=- diff=- merge=- -text

# Thirdparty全部文件
third_party/** filter=lfs diff=lfs merge=lfs -text
