x64-linux/
x64-linux/include/
x64-linux/include/boost/
x64-linux/include/boost/scope/
x64-linux/include/boost/scope/defer.hpp
x64-linux/include/boost/scope/detail/
x64-linux/include/boost/scope/detail/compact_storage.hpp
x64-linux/include/boost/scope/detail/config.hpp
x64-linux/include/boost/scope/detail/footer.hpp
x64-linux/include/boost/scope/detail/header.hpp
x64-linux/include/boost/scope/detail/is_nonnull_default_constructible.hpp
x64-linux/include/boost/scope/detail/is_not_like.hpp
x64-linux/include/boost/scope/detail/move_or_copy_assign_ref.hpp
x64-linux/include/boost/scope/detail/move_or_copy_construct_ref.hpp
x64-linux/include/boost/scope/detail/type_traits/
x64-linux/include/boost/scope/detail/type_traits/conjunction.hpp
x64-linux/include/boost/scope/detail/type_traits/disjunction.hpp
x64-linux/include/boost/scope/detail/type_traits/is_final.hpp
x64-linux/include/boost/scope/detail/type_traits/is_invocable.hpp
x64-linux/include/boost/scope/detail/type_traits/is_nothrow_invocable.hpp
x64-linux/include/boost/scope/detail/type_traits/is_nothrow_swappable.hpp
x64-linux/include/boost/scope/detail/type_traits/is_swappable.hpp
x64-linux/include/boost/scope/detail/type_traits/negation.hpp
x64-linux/include/boost/scope/error_code_checker.hpp
x64-linux/include/boost/scope/exception_checker.hpp
x64-linux/include/boost/scope/fd_deleter.hpp
x64-linux/include/boost/scope/fd_resource_traits.hpp
x64-linux/include/boost/scope/scope_exit.hpp
x64-linux/include/boost/scope/scope_fail.hpp
x64-linux/include/boost/scope/scope_success.hpp
x64-linux/include/boost/scope/unique_fd.hpp
x64-linux/include/boost/scope/unique_resource.hpp
x64-linux/include/boost/scope/unique_resource_fwd.hpp
x64-linux/share/
x64-linux/share/boost-scope/
x64-linux/share/boost-scope/copyright
x64-linux/share/boost-scope/vcpkg.spdx.json
x64-linux/share/boost-scope/vcpkg_abi_info.txt
x64-linux/share/boost_scope/
x64-linux/share/boost_scope/boost_scope-config-version.cmake
x64-linux/share/boost_scope/boost_scope-config.cmake
x64-linux/share/boost_scope/boost_scope-targets.cmake
