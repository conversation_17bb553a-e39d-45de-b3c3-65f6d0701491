#ifdef _METHODS
DAT(GET,              _XPLATSTR("GET"))
DAT(POST,             _XPLATSTR("POST"))
DAT(PUT,              _XPLATSTR("PUT"))
DAT(DEL,              _XPLATSTR("DELETE"))
DAT(HEAD,             _XPLATSTR("HEAD"))
DAT(OPTIONS,          _XPLATSTR("OPTIONS"))
DAT(TRCE,             _XPLATSTR("TRACE"))
DAT(CONNECT,          _XPLATSTR("CONNECT"))
DAT(MERGE,            _XPLATSTR("MERGE"))
DAT(PATCH,            _XPLATSTR("PATCH"))
#endif

#ifdef _PHRASES
DAT(Continue,              100, _XPLATSTR("Continue"))
DAT(SwitchingProtocols,    101, _XPLATSTR("Switching Protocols"))
DAT(OK,                    200, _XPLATSTR("OK"))
DAT(Created,               201, _XPLATSTR("Created"))
DAT(Accepted,              202, _XPLATSTR("Accepted"))
DAT(NonAuthInfo,           203, _XPLATSTR("Non-Authoritative Information"))
DAT(NoContent,             204, _XPLATSTR("No Content"))
DAT(ResetContent,          205, _XPLATSTR("Reset Content"))
DAT(PartialContent,        206, _XPLATSTR("Partial Content"))
DAT(MultiStatus,           207, _XPLATSTR("Multi-Status"))
DAT(AlreadyReported,       208, _XPLATSTR("Already Reported"))
DAT(IMUsed,                226, _XPLATSTR("IM Used"))
DAT(MultipleChoices,       300, _XPLATSTR("Multiple Choices"))
DAT(MovedPermanently,      301, _XPLATSTR("Moved Permanently"))
DAT(Found,                 302, _XPLATSTR("Found"))
DAT(SeeOther,              303, _XPLATSTR("See Other"))
DAT(NotModified,           304, _XPLATSTR("Not Modified"))
DAT(UseProxy,              305, _XPLATSTR("Use Proxy"))
DAT(TemporaryRedirect,     307, _XPLATSTR("Temporary Redirect"))
DAT(PermanentRedirect,     308, _XPLATSTR("Permanent Redirect"))
DAT(BadRequest,            400, _XPLATSTR("Bad Request"))
DAT(Unauthorized,          401, _XPLATSTR("Unauthorized"))
DAT(PaymentRequired,       402, _XPLATSTR("Payment Required"))
DAT(Forbidden,             403, _XPLATSTR("Forbidden"))
DAT(NotFound,              404, _XPLATSTR("Not Found"))
DAT(MethodNotAllowed,      405, _XPLATSTR("Method Not Allowed"))
DAT(NotAcceptable,         406, _XPLATSTR("Not Acceptable"))
DAT(ProxyAuthRequired,     407, _XPLATSTR("Proxy Authentication Required"))
DAT(RequestTimeout,        408, _XPLATSTR("Request Time-out"))
DAT(Conflict,              409, _XPLATSTR("Conflict"))
DAT(Gone,                  410, _XPLATSTR("Gone"))
DAT(LengthRequired,        411, _XPLATSTR("Length Required"))
DAT(PreconditionFailed,    412, _XPLATSTR("Precondition Failed"))
DAT(RequestEntityTooLarge, 413, _XPLATSTR("Request Entity Too Large"))
DAT(RequestUriTooLarge,    414, _XPLATSTR("Request Uri Too Large"))
DAT(UnsupportedMediaType,  415, _XPLATSTR("Unsupported Media Type"))
DAT(RangeNotSatisfiable,   416, _XPLATSTR("Requested range not satisfiable"))
DAT(ExpectationFailed,     417, _XPLATSTR("Expectation Failed"))
DAT(MisdirectedRequest,    421, _XPLATSTR("Misdirected Request"))
DAT(UnprocessableEntity,   422, _XPLATSTR("Unprocessable Entity"))
DAT(Locked,                423, _XPLATSTR("Locked"))
DAT(FailedDependency,      424, _XPLATSTR("Failed Dependency"))
DAT(UpgradeRequired,       426, _XPLATSTR("Upgrade Required"))
DAT(PreconditionRequired,  428, _XPLATSTR("Precondition Required"))
DAT(TooManyRequests,       429, _XPLATSTR("Too Many Requests"))
DAT(RequestHeaderFieldsTooLarge,  431, _XPLATSTR("Request Header Fields Too Large"))
DAT(UnavailableForLegalReasons,   451, _XPLATSTR("Unavailable For Legal Reasons"))
DAT(InternalError,         500, _XPLATSTR("Internal Error"))
DAT(NotImplemented,        501, _XPLATSTR("Not Implemented"))
DAT(BadGateway,            502, _XPLATSTR("Bad Gateway"))
DAT(ServiceUnavailable,    503, _XPLATSTR("Service Unavailable"))
DAT(GatewayTimeout,        504, _XPLATSTR("Gateway Time-out"))
DAT(HttpVersionNotSupported, 505, _XPLATSTR("HTTP Version not supported"))
DAT(VariantAlsoNegotiates, 506, _XPLATSTR("Variant Also Negotiates"))
DAT(InsufficientStorage,   507, _XPLATSTR("Insufficient Storage"))
DAT(LoopDetected,          508, _XPLATSTR("Loop Detected"))
DAT(NotExtended,           510, _XPLATSTR("Not Extended"))
DAT(NetworkAuthenticationRequired, 511, _XPLATSTR("Network Authentication Required"))
#endif // _PHRASES

#ifdef _HEADER_NAMES
DAT(accept,                 "Accept")
DAT(accept_charset,         "Accept-Charset")
DAT(accept_encoding,        "Accept-Encoding")
DAT(accept_language,        "Accept-Language")
DAT(accept_ranges,          "Accept-Ranges")
DAT(access_control_allow_origin,  "Access-Control-Allow-Origin")
DAT(age,                    "Age")
DAT(allow,                  "Allow")
DAT(authorization,          "Authorization")
DAT(cache_control,          "Cache-Control")
DAT(connection,             "Connection")
DAT(content_encoding,       "Content-Encoding")
DAT(content_language,       "Content-Language")
DAT(content_length,         "Content-Length")
DAT(content_location,       "Content-Location")
DAT(content_md5,            "Content-MD5")
DAT(content_range,          "Content-Range")
DAT(content_type,           "Content-Type")
DAT(content_disposition,    "Content-Disposition")
DAT(date,                   "Date")
DAT(etag,                   "ETag")
DAT(expect,                 "Expect")
DAT(expires,                "Expires")
DAT(from,                   "From")
DAT(host,                   "Host")
DAT(if_match,               "If-Match")
DAT(if_modified_since,      "If-Modified-Since")
DAT(if_none_match,          "If-None-Match")
DAT(if_range,               "If-Range")
DAT(if_unmodified_since,    "If-Unmodified-Since")
DAT(last_modified,          "Last-Modified")
DAT(location,               "Location")
DAT(max_forwards,           "Max-Forwards")
DAT(pragma,                 "Pragma")
DAT(proxy_authenticate,     "Proxy-Authenticate")
DAT(proxy_authorization,    "Proxy-Authorization")
DAT(range,                  "Range")
DAT(referer,                "Referer")
DAT(retry_after,            "Retry-After")
DAT(server,                 "Server")
DAT(te,                     "TE")
DAT(trailer,                "Trailer")
DAT(transfer_encoding,      "Transfer-Encoding")
DAT(upgrade,                "Upgrade")
DAT(user_agent,             "User-Agent")
DAT(vary,                   "Vary")
DAT(via,                    "Via")
DAT(warning,                "Warning")
DAT(www_authenticate,       "WWW-Authenticate")
#endif // _HEADER_NAMES

#ifdef _MIME_TYPES
DAT(application_atom_xml,                  "application/atom+xml")
DAT(application_http,                      "application/http")
DAT(application_javascript,                "application/javascript")
DAT(application_json,                      "application/json")
DAT(application_xjson,                     "application/x-json")
DAT(application_octetstream,               "application/octet-stream")
DAT(application_x_www_form_urlencoded,     "application/x-www-form-urlencoded")
DAT(multipart_form_data,                   "multipart/form-data")
DAT(boundary,                              "boundary")
DAT(form_data,                             "form-data")
DAT(application_xjavascript,               "application/x-javascript")
DAT(application_xml,                       "application/xml")
DAT(message_http,                          "message/http")
DAT(text,                                  "text")
DAT(text_javascript,                       "text/javascript")
DAT(text_json,                             "text/json")
DAT(text_plain,                            "text/plain")
DAT(text_plain_utf16,                      "text/plain; charset=utf-16")
DAT(text_plain_utf16le,                    "text/plain; charset=utf-16le")
DAT(text_plain_utf8,                       "text/plain; charset=utf-8")
DAT(text_xjavascript,                      "text/x-javascript")
DAT(text_xjson,                            "text/x-json")
#endif // _MIME_TYPES

#ifdef _CHARSET_TYPES
DAT(ascii,                          "ascii")
DAT(usascii,                        "us-ascii")
DAT(latin1,                         "iso-8859-1")
DAT(utf8,                           "utf-8")
DAT(utf16,                          "utf-16")
DAT(utf16le,                        "utf-16le")
DAT(utf16be,                        "utf-16be")
#endif // _CHARSET_TYPES

#ifdef _OAUTH1_METHODS
DAT(hmac_sha1,                      _XPLATSTR("HMAC-SHA1"))
DAT(plaintext,                      _XPLATSTR("PLAINTEXT"))
#endif // _OAUTH1_METHODS

#ifdef _OAUTH1_STRINGS
DAT(callback,                       "oauth_callback")
DAT(callback_confirmed,             "oauth_callback_confirmed")
DAT(consumer_key,                   "oauth_consumer_key")
DAT(nonce,                          "oauth_nonce")
DAT(realm,                          "realm") // NOTE: No "oauth_" prefix.
DAT(signature,                      "oauth_signature")
DAT(signature_method,               "oauth_signature_method")
DAT(timestamp,                      "oauth_timestamp")
DAT(token,                          "oauth_token")
DAT(token_secret,                   "oauth_token_secret")
DAT(verifier,                       "oauth_verifier")
DAT(version,                        "oauth_version")
#endif // _OAUTH1_STRINGS

#ifdef _OAUTH2_STRINGS
DAT(access_token,                   "access_token")
DAT(authorization_code,             "authorization_code")
DAT(bearer,                         "bearer")
DAT(client_id,                      "client_id")
DAT(client_secret,                  "client_secret")
DAT(code,                           "code")
DAT(expires_in,                     "expires_in")
DAT(grant_type,                     "grant_type")
DAT(redirect_uri,                   "redirect_uri")
DAT(refresh_token,                  "refresh_token")
DAT(client_credentials,             "client_credentials")
DAT(response_type,                  "response_type")
DAT(scope,                          "scope")
DAT(state,                          "state")
DAT(token,                          "token")
DAT(token_type,                     "token_type")
#endif // _OAUTH2_STRINGS
