/*
Copyright 2014 <PERSON>
(<EMAIL>)

Distributed under the Boost Software License, Version 1.0.
(http://www.boost.org/LICENSE_1_0.txt)
*/
#ifndef BOOST_ALIGN_ALIGNED_ALLOCATOR_ADAPTOR_FORWARD_HPP
#define BOOST_ALIGN_ALIGNED_ALLOCATOR_ADAPTOR_FORWARD_HPP

#include <cstddef>

namespace boost {
namespace alignment {

template<class Allocator, std::size_t Alignment = 1>
class aligned_allocator_adaptor;

} /* alignment */
} /* boost */

#endif
