
// Copyright Al<PERSON>sey Gurtovoy 2000-2004
//
// Distributed under the Boost Software License, Version 1.0. 
// (See accompanying file LICENSE_1_0.txt or copy at 
// http://www.boost.org/LICENSE_1_0.txt)
//

// Preprocessed version of "boost/mpl/list/list50_c.hpp" header
// -- DO NOT modify by hand!

namespace boost { namespace mpl {

template<
      typename T
    , T C0, T C1, T C2, T C3, T C4, T C5, T C6, T C7, T C8, T C9, T C10
    , T C11, T C12, T C13, T C14, T C15, T C16, T C17, T C18, T C19, T C20
    , T C21, T C22, T C23, T C24, T C25, T C26, T C27, T C28, T C29, T C30
    , T C31, T C32, T C33, T C34, T C35, T C36, T C37, T C38, T C39, T C40
    >
struct list41_c
    : l_item<
          long_<41>
        , integral_c< T,C0 >
        , list40_c< T,C1,C2,C3,C4,C5,C6,C7,C8,C9,C10,C11,C12,C13,C14,<PERSON>15,C16,<PERSON>17,C18,C19,C20,C21,C22,C23,C24,C25,C26,C27,C28,C29,C30,C31,C32,C33,C34,C35,C36,C37,C38,C39,C40 >
        >
{
    typedef list41_c type;
    typedef T value_type;
};

template<
      typename T
    , T C0, T C1, T C2, T C3, T C4, T C5, T C6, T C7, T C8, T C9, T C10
    , T C11, T C12, T C13, T C14, T C15, T C16, T C17, T C18, T C19, T C20
    , T C21, T C22, T C23, T C24, T C25, T C26, T C27, T C28, T C29, T C30
    , T C31, T C32, T C33, T C34, T C35, T C36, T C37, T C38, T C39, T C40
    , T C41
    >
struct list42_c
    : l_item<
          long_<42>
        , integral_c< T,C0 >
        , list41_c< T,C1,C2,C3,C4,C5,C6,C7,C8,C9,C10,C11,C12,C13,C14,C15,C16,C17,C18,C19,C20,C21,C22,C23,C24,C25,C26,C27,C28,C29,C30,C31,C32,C33,C34,C35,C36,C37,C38,C39,C40,C41 >
        >
{
    typedef list42_c type;
    typedef T value_type;
};

template<
      typename T
    , T C0, T C1, T C2, T C3, T C4, T C5, T C6, T C7, T C8, T C9, T C10
    , T C11, T C12, T C13, T C14, T C15, T C16, T C17, T C18, T C19, T C20
    , T C21, T C22, T C23, T C24, T C25, T C26, T C27, T C28, T C29, T C30
    , T C31, T C32, T C33, T C34, T C35, T C36, T C37, T C38, T C39, T C40
    , T C41, T C42
    >
struct list43_c
    : l_item<
          long_<43>
        , integral_c< T,C0 >
        , list42_c< T,C1,C2,C3,C4,C5,C6,C7,C8,C9,C10,C11,C12,C13,C14,C15,C16,C17,C18,C19,C20,C21,C22,C23,C24,C25,C26,C27,C28,C29,C30,C31,C32,C33,C34,C35,C36,C37,C38,C39,C40,C41,C42 >
        >
{
    typedef list43_c type;
    typedef T value_type;
};

template<
      typename T
    , T C0, T C1, T C2, T C3, T C4, T C5, T C6, T C7, T C8, T C9, T C10
    , T C11, T C12, T C13, T C14, T C15, T C16, T C17, T C18, T C19, T C20
    , T C21, T C22, T C23, T C24, T C25, T C26, T C27, T C28, T C29, T C30
    , T C31, T C32, T C33, T C34, T C35, T C36, T C37, T C38, T C39, T C40
    , T C41, T C42, T C43
    >
struct list44_c
    : l_item<
          long_<44>
        , integral_c< T,C0 >
        , list43_c< T,C1,C2,C3,C4,C5,C6,C7,C8,C9,C10,C11,C12,C13,C14,C15,C16,C17,C18,C19,C20,C21,C22,C23,C24,C25,C26,C27,C28,C29,C30,C31,C32,C33,C34,C35,C36,C37,C38,C39,C40,C41,C42,C43 >
        >
{
    typedef list44_c type;
    typedef T value_type;
};

template<
      typename T
    , T C0, T C1, T C2, T C3, T C4, T C5, T C6, T C7, T C8, T C9, T C10
    , T C11, T C12, T C13, T C14, T C15, T C16, T C17, T C18, T C19, T C20
    , T C21, T C22, T C23, T C24, T C25, T C26, T C27, T C28, T C29, T C30
    , T C31, T C32, T C33, T C34, T C35, T C36, T C37, T C38, T C39, T C40
    , T C41, T C42, T C43, T C44
    >
struct list45_c
    : l_item<
          long_<45>
        , integral_c< T,C0 >
        , list44_c< T,C1,C2,C3,C4,C5,C6,C7,C8,C9,C10,C11,C12,C13,C14,C15,C16,C17,C18,C19,C20,C21,C22,C23,C24,C25,C26,C27,C28,C29,C30,C31,C32,C33,C34,C35,C36,C37,C38,C39,C40,C41,C42,C43,C44 >
        >
{
    typedef list45_c type;
    typedef T value_type;
};

template<
      typename T
    , T C0, T C1, T C2, T C3, T C4, T C5, T C6, T C7, T C8, T C9, T C10
    , T C11, T C12, T C13, T C14, T C15, T C16, T C17, T C18, T C19, T C20
    , T C21, T C22, T C23, T C24, T C25, T C26, T C27, T C28, T C29, T C30
    , T C31, T C32, T C33, T C34, T C35, T C36, T C37, T C38, T C39, T C40
    , T C41, T C42, T C43, T C44, T C45
    >
struct list46_c
    : l_item<
          long_<46>
        , integral_c< T,C0 >
        , list45_c< T,C1,C2,C3,C4,C5,C6,C7,C8,C9,C10,C11,C12,C13,C14,C15,C16,C17,C18,C19,C20,C21,C22,C23,C24,C25,C26,C27,C28,C29,C30,C31,C32,C33,C34,C35,C36,C37,C38,C39,C40,C41,C42,C43,C44,C45 >
        >
{
    typedef list46_c type;
    typedef T value_type;
};

template<
      typename T
    , T C0, T C1, T C2, T C3, T C4, T C5, T C6, T C7, T C8, T C9, T C10
    , T C11, T C12, T C13, T C14, T C15, T C16, T C17, T C18, T C19, T C20
    , T C21, T C22, T C23, T C24, T C25, T C26, T C27, T C28, T C29, T C30
    , T C31, T C32, T C33, T C34, T C35, T C36, T C37, T C38, T C39, T C40
    , T C41, T C42, T C43, T C44, T C45, T C46
    >
struct list47_c
    : l_item<
          long_<47>
        , integral_c< T,C0 >
        , list46_c< T,C1,C2,C3,C4,C5,C6,C7,C8,C9,C10,C11,C12,C13,C14,C15,C16,C17,C18,C19,C20,C21,C22,C23,C24,C25,C26,C27,C28,C29,C30,C31,C32,C33,C34,C35,C36,C37,C38,C39,C40,C41,C42,C43,C44,C45,C46 >
        >
{
    typedef list47_c type;
    typedef T value_type;
};

template<
      typename T
    , T C0, T C1, T C2, T C3, T C4, T C5, T C6, T C7, T C8, T C9, T C10
    , T C11, T C12, T C13, T C14, T C15, T C16, T C17, T C18, T C19, T C20
    , T C21, T C22, T C23, T C24, T C25, T C26, T C27, T C28, T C29, T C30
    , T C31, T C32, T C33, T C34, T C35, T C36, T C37, T C38, T C39, T C40
    , T C41, T C42, T C43, T C44, T C45, T C46, T C47
    >
struct list48_c
    : l_item<
          long_<48>
        , integral_c< T,C0 >
        , list47_c< T,C1,C2,C3,C4,C5,C6,C7,C8,C9,C10,C11,C12,C13,C14,C15,C16,C17,C18,C19,C20,C21,C22,C23,C24,C25,C26,C27,C28,C29,C30,C31,C32,C33,C34,C35,C36,C37,C38,C39,C40,C41,C42,C43,C44,C45,C46,C47 >
        >
{
    typedef list48_c type;
    typedef T value_type;
};

template<
      typename T
    , T C0, T C1, T C2, T C3, T C4, T C5, T C6, T C7, T C8, T C9, T C10
    , T C11, T C12, T C13, T C14, T C15, T C16, T C17, T C18, T C19, T C20
    , T C21, T C22, T C23, T C24, T C25, T C26, T C27, T C28, T C29, T C30
    , T C31, T C32, T C33, T C34, T C35, T C36, T C37, T C38, T C39, T C40
    , T C41, T C42, T C43, T C44, T C45, T C46, T C47, T C48
    >
struct list49_c
    : l_item<
          long_<49>
        , integral_c< T,C0 >
        , list48_c< T,C1,C2,C3,C4,C5,C6,C7,C8,C9,C10,C11,C12,C13,C14,C15,C16,C17,C18,C19,C20,C21,C22,C23,C24,C25,C26,C27,C28,C29,C30,C31,C32,C33,C34,C35,C36,C37,C38,C39,C40,C41,C42,C43,C44,C45,C46,C47,C48 >
        >
{
    typedef list49_c type;
    typedef T value_type;
};

template<
      typename T
    , T C0, T C1, T C2, T C3, T C4, T C5, T C6, T C7, T C8, T C9, T C10
    , T C11, T C12, T C13, T C14, T C15, T C16, T C17, T C18, T C19, T C20
    , T C21, T C22, T C23, T C24, T C25, T C26, T C27, T C28, T C29, T C30
    , T C31, T C32, T C33, T C34, T C35, T C36, T C37, T C38, T C39, T C40
    , T C41, T C42, T C43, T C44, T C45, T C46, T C47, T C48, T C49
    >
struct list50_c
    : l_item<
          long_<50>
        , integral_c< T,C0 >
        , list49_c< T,C1,C2,C3,C4,C5,C6,C7,C8,C9,C10,C11,C12,C13,C14,C15,C16,C17,C18,C19,C20,C21,C22,C23,C24,C25,C26,C27,C28,C29,C30,C31,C32,C33,C34,C35,C36,C37,C38,C39,C40,C41,C42,C43,C44,C45,C46,C47,C48,C49 >
        >
{
    typedef list50_c type;
    typedef T value_type;
};

}}
