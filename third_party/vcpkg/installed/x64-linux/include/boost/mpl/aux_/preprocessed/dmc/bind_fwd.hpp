
// Copyright Aleksey Gurtovoy 2000-2004
//
// Distributed under the Boost Software License, Version 1.0. 
// (See accompanying file LICENSE_1_0.txt or copy at 
// http://www.boost.org/LICENSE_1_0.txt)
//

// Preprocessed version of "boost/mpl/bind_fwd.hpp" header
// -- DO NOT modify by hand!

namespace boost { namespace mpl {

template<
      typename F, typename T1 = na, typename T2 = na, typename T3 = na
    , typename T4 = na, typename T5 = na, int dummy_ = 0
    >
struct bind;

template<
      typename F, int dummy_ = 0
    >
struct bind0;

template<
      typename F, typename T1, int dummy_ = 0
    >
struct bind1;

template<
      typename F, typename T1, typename T2, int dummy_ = 0
    >
struct bind2;

template<
      typename F, typename T1, typename T2, typename T3, int dummy_ = 0
    >
struct bind3;

template<
      typename F, typename T1, typename T2, typename T3, typename T4
    , int dummy_ = 0
    >
struct bind4;

template<
      typename F, typename T1, typename T2, typename T3, typename T4
    , typename T5, int dummy_ = 0
    >
struct bind5;

}}

