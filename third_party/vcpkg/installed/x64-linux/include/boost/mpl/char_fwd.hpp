
#ifndef BOOST_MPL_CHAR_FWD_HPP_INCLUDED
#define BOOST_MPL_CHAR_FWD_HPP_INCLUDED

// Copyright <PERSON> 2008
//
// Distributed under the Boost Software License, Version 1.0. 
// (See accompanying file LICENSE_1_0.txt or copy at 
// http://www.boost.org/LICENSE_1_0.txt)
//
// See http://www.boost.org/libs/mpl for documentation.

// $Source$
// $Date: 2008-06-14 08:41:37 -0700 (Sat, 16 Jun 2008) $
// $Revision: 24874 $

#include <boost/mpl/aux_/adl_barrier.hpp>
#include <boost/mpl/aux_/nttp_decl.hpp>

BOOST_MPL_AUX_ADL_BARRIER_NAMESPACE_OPEN

template< BOOST_MPL_AUX_NTTP_DECL(char, N) > struct char_;

BOOST_MPL_AUX_ADL_BARRIER_NAMESPACE_CLOSE
BOOST_MPL_AUX_ADL_BARRIER_DECL(char_)

#endif // BOOST_MPL_CHAR_FWD_HPP_INCLUDED
