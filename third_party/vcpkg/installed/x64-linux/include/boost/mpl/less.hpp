
#ifndef BOOST_MPL_LESS_HPP_INCLUDED
#define BOOST_MPL_LESS_HPP_INCLUDED

// Copyright Aleksey Gurtovoy 2000-2004
//
// Distributed under the Boost Software License, Version 1.0. 
// (See accompanying file LICENSE_1_0.txt or copy at 
// http://www.boost.org/LICENSE_1_0.txt)
//
// See http://www.boost.org/libs/mpl for documentation.

// $Id$
// $Date$
// $Revision$

#define AUX778076_OP_NAME less
#define AUX778076_OP_TOKEN <
#include <boost/mpl/aux_/comparison_op.hpp>

#endif // BOOST_MPL_LESS_HPP_INCLUDED
