
// Copyright Al<PERSON><PERSON> Gurtovoy 2000-2004
//
// Distributed under the Boost Software License, Version 1.0. 
// (See accompanying file LICENSE_1_0.txt or copy at 
// http://www.boost.org/LICENSE_1_0.txt)
//

// Preprocessed version of "boost/mpl/vector/vector50_c.hpp" header
// -- DO NOT modify by hand!

namespace boost { namespace mpl {

template<
      typename T
    , T C0, T C1, T C2, T C3, T C4, T C5, T C6, T C7, T C8, T C9, T C10
    , T C11, T C12, T C13, T C14, T C15, T C16, T C17, T C18, T C19, T C20
    , T C21, T C22, T C23, T C24, T C25, T C26, T C27, T C28, T C29, T C30
    , T C31, T C32, T C33, T C34, T C35, T C36, T C37, T C38, T C39, T C40
    >
struct vector41_c
    : vector41<
          integral_c< T,C0 >, integral_c< T,C1 >, integral_c< T,C2 >
        , integral_c< T,C3 >, integral_c< T,C4 >, integral_c< T,C5 >, integral_c< T,C6 >
        , integral_c< T,C7 >, integral_c< T,C8 >, integral_c< T,C9 >
        , integral_c< T,C10 >, integral_c< T,C11 >, integral_c< T,C12 >
        , integral_c< T,C13 >, integral_c< T,C14 >, integral_c< T,C15 >
        , integral_c< T,C16 >, integral_c< T,C17 >, integral_c< T,C18 >
        , integral_c< T,C19 >, integral_c< T,C20 >, integral_c< T,C21 >
        , integral_c< T,C22 >, integral_c< T,C23 >, integral_c< T,C24 >
        , integral_c< T,C25 >, integral_c< T,C26 >, integral_c< T,C27 >
        , integral_c< T,C28 >, integral_c< T,C29 >, integral_c< T,C30 >
        , integral_c< T,C31 >, integral_c< T,C32 >, integral_c< T,C33 >
        , integral_c< T,C34 >, integral_c< T,C35 >, integral_c< T,C36 >
        , integral_c< T,C37 >, integral_c< T,C38 >, integral_c< T,C39 >, integral_c<T
        , C40> 
 >
{
    typedef vector41_c type;
    typedef T value_type;
};

template<
      typename T
    , T C0, T C1, T C2, T C3, T C4, T C5, T C6, T C7, T C8, T C9, T C10
    , T C11, T C12, T C13, T C14, T C15, T C16, T C17, T C18, T C19, T C20
    , T C21, T C22, T C23, T C24, T C25, T C26, T C27, T C28, T C29, T C30
    , T C31, T C32, T C33, T C34, T C35, T C36, T C37, T C38, T C39, T C40
    , T C41
    >
struct vector42_c
    : vector42<
          integral_c< T,C0 >, integral_c< T,C1 >, integral_c< T,C2 >
        , integral_c< T,C3 >, integral_c< T,C4 >, integral_c< T,C5 >, integral_c< T,C6 >
        , integral_c< T,C7 >, integral_c< T,C8 >, integral_c< T,C9 >
        , integral_c< T,C10 >, integral_c< T,C11 >, integral_c< T,C12 >
        , integral_c< T,C13 >, integral_c< T,C14 >, integral_c< T,C15 >
        , integral_c< T,C16 >, integral_c< T,C17 >, integral_c< T,C18 >
        , integral_c< T,C19 >, integral_c< T,C20 >, integral_c< T,C21 >
        , integral_c< T,C22 >, integral_c< T,C23 >, integral_c< T,C24 >
        , integral_c< T,C25 >, integral_c< T,C26 >, integral_c< T,C27 >
        , integral_c< T,C28 >, integral_c< T,C29 >, integral_c< T,C30 >
        , integral_c< T,C31 >, integral_c< T,C32 >, integral_c< T,C33 >
        , integral_c< T,C34 >, integral_c< T,C35 >, integral_c< T,C36 >
        , integral_c< T,C37 >, integral_c< T,C38 >, integral_c< T,C39 >
        , integral_c< T,C40 >, integral_c< T,C41 > 
 >
{
    typedef vector42_c type;
    typedef T value_type;
};

template<
      typename T
    , T C0, T C1, T C2, T C3, T C4, T C5, T C6, T C7, T C8, T C9, T C10
    , T C11, T C12, T C13, T C14, T C15, T C16, T C17, T C18, T C19, T C20
    , T C21, T C22, T C23, T C24, T C25, T C26, T C27, T C28, T C29, T C30
    , T C31, T C32, T C33, T C34, T C35, T C36, T C37, T C38, T C39, T C40
    , T C41, T C42
    >
struct vector43_c
    : vector43<
          integral_c< T,C0 >, integral_c< T,C1 >, integral_c< T,C2 >
        , integral_c< T,C3 >, integral_c< T,C4 >, integral_c< T,C5 >, integral_c< T,C6 >
        , integral_c< T,C7 >, integral_c< T,C8 >, integral_c< T,C9 >
        , integral_c< T,C10 >, integral_c< T,C11 >, integral_c< T,C12 >
        , integral_c< T,C13 >, integral_c< T,C14 >, integral_c< T,C15 >
        , integral_c< T,C16 >, integral_c< T,C17 >, integral_c< T,C18 >
        , integral_c< T,C19 >, integral_c< T,C20 >, integral_c< T,C21 >
        , integral_c< T,C22 >, integral_c< T,C23 >, integral_c< T,C24 >
        , integral_c< T,C25 >, integral_c< T,C26 >, integral_c< T,C27 >
        , integral_c< T,C28 >, integral_c< T,C29 >, integral_c< T,C30 >
        , integral_c< T,C31 >, integral_c< T,C32 >, integral_c< T,C33 >
        , integral_c< T,C34 >, integral_c< T,C35 >, integral_c< T,C36 >
        , integral_c< T,C37 >, integral_c< T,C38 >, integral_c< T,C39 >
        , integral_c< T,C40 >, integral_c< T,C41 >, integral_c< T,C42 > 
 >
{
    typedef vector43_c type;
    typedef T value_type;
};

template<
      typename T
    , T C0, T C1, T C2, T C3, T C4, T C5, T C6, T C7, T C8, T C9, T C10
    , T C11, T C12, T C13, T C14, T C15, T C16, T C17, T C18, T C19, T C20
    , T C21, T C22, T C23, T C24, T C25, T C26, T C27, T C28, T C29, T C30
    , T C31, T C32, T C33, T C34, T C35, T C36, T C37, T C38, T C39, T C40
    , T C41, T C42, T C43
    >
struct vector44_c
    : vector44<
          integral_c< T,C0 >, integral_c< T,C1 >, integral_c< T,C2 >
        , integral_c< T,C3 >, integral_c< T,C4 >, integral_c< T,C5 >, integral_c< T,C6 >
        , integral_c< T,C7 >, integral_c< T,C8 >, integral_c< T,C9 >
        , integral_c< T,C10 >, integral_c< T,C11 >, integral_c< T,C12 >
        , integral_c< T,C13 >, integral_c< T,C14 >, integral_c< T,C15 >
        , integral_c< T,C16 >, integral_c< T,C17 >, integral_c< T,C18 >
        , integral_c< T,C19 >, integral_c< T,C20 >, integral_c< T,C21 >
        , integral_c< T,C22 >, integral_c< T,C23 >, integral_c< T,C24 >
        , integral_c< T,C25 >, integral_c< T,C26 >, integral_c< T,C27 >
        , integral_c< T,C28 >, integral_c< T,C29 >, integral_c< T,C30 >
        , integral_c< T,C31 >, integral_c< T,C32 >, integral_c< T,C33 >
        , integral_c< T,C34 >, integral_c< T,C35 >, integral_c< T,C36 >
        , integral_c< T,C37 >, integral_c< T,C38 >, integral_c< T,C39 >
        , integral_c< T,C40 >, integral_c< T,C41 >, integral_c< T,C42 >, integral_c<T
        , C43> 
 >
{
    typedef vector44_c type;
    typedef T value_type;
};

template<
      typename T
    , T C0, T C1, T C2, T C3, T C4, T C5, T C6, T C7, T C8, T C9, T C10
    , T C11, T C12, T C13, T C14, T C15, T C16, T C17, T C18, T C19, T C20
    , T C21, T C22, T C23, T C24, T C25, T C26, T C27, T C28, T C29, T C30
    , T C31, T C32, T C33, T C34, T C35, T C36, T C37, T C38, T C39, T C40
    , T C41, T C42, T C43, T C44
    >
struct vector45_c
    : vector45<
          integral_c< T,C0 >, integral_c< T,C1 >, integral_c< T,C2 >
        , integral_c< T,C3 >, integral_c< T,C4 >, integral_c< T,C5 >, integral_c< T,C6 >
        , integral_c< T,C7 >, integral_c< T,C8 >, integral_c< T,C9 >
        , integral_c< T,C10 >, integral_c< T,C11 >, integral_c< T,C12 >
        , integral_c< T,C13 >, integral_c< T,C14 >, integral_c< T,C15 >
        , integral_c< T,C16 >, integral_c< T,C17 >, integral_c< T,C18 >
        , integral_c< T,C19 >, integral_c< T,C20 >, integral_c< T,C21 >
        , integral_c< T,C22 >, integral_c< T,C23 >, integral_c< T,C24 >
        , integral_c< T,C25 >, integral_c< T,C26 >, integral_c< T,C27 >
        , integral_c< T,C28 >, integral_c< T,C29 >, integral_c< T,C30 >
        , integral_c< T,C31 >, integral_c< T,C32 >, integral_c< T,C33 >
        , integral_c< T,C34 >, integral_c< T,C35 >, integral_c< T,C36 >
        , integral_c< T,C37 >, integral_c< T,C38 >, integral_c< T,C39 >
        , integral_c< T,C40 >, integral_c< T,C41 >, integral_c< T,C42 >
        , integral_c< T,C43 >, integral_c< T,C44 > 
 >
{
    typedef vector45_c type;
    typedef T value_type;
};

template<
      typename T
    , T C0, T C1, T C2, T C3, T C4, T C5, T C6, T C7, T C8, T C9, T C10
    , T C11, T C12, T C13, T C14, T C15, T C16, T C17, T C18, T C19, T C20
    , T C21, T C22, T C23, T C24, T C25, T C26, T C27, T C28, T C29, T C30
    , T C31, T C32, T C33, T C34, T C35, T C36, T C37, T C38, T C39, T C40
    , T C41, T C42, T C43, T C44, T C45
    >
struct vector46_c
    : vector46<
          integral_c< T,C0 >, integral_c< T,C1 >, integral_c< T,C2 >
        , integral_c< T,C3 >, integral_c< T,C4 >, integral_c< T,C5 >, integral_c< T,C6 >
        , integral_c< T,C7 >, integral_c< T,C8 >, integral_c< T,C9 >
        , integral_c< T,C10 >, integral_c< T,C11 >, integral_c< T,C12 >
        , integral_c< T,C13 >, integral_c< T,C14 >, integral_c< T,C15 >
        , integral_c< T,C16 >, integral_c< T,C17 >, integral_c< T,C18 >
        , integral_c< T,C19 >, integral_c< T,C20 >, integral_c< T,C21 >
        , integral_c< T,C22 >, integral_c< T,C23 >, integral_c< T,C24 >
        , integral_c< T,C25 >, integral_c< T,C26 >, integral_c< T,C27 >
        , integral_c< T,C28 >, integral_c< T,C29 >, integral_c< T,C30 >
        , integral_c< T,C31 >, integral_c< T,C32 >, integral_c< T,C33 >
        , integral_c< T,C34 >, integral_c< T,C35 >, integral_c< T,C36 >
        , integral_c< T,C37 >, integral_c< T,C38 >, integral_c< T,C39 >
        , integral_c< T,C40 >, integral_c< T,C41 >, integral_c< T,C42 >
        , integral_c< T,C43 >, integral_c< T,C44 >, integral_c< T,C45 > 
 >
{
    typedef vector46_c type;
    typedef T value_type;
};

template<
      typename T
    , T C0, T C1, T C2, T C3, T C4, T C5, T C6, T C7, T C8, T C9, T C10
    , T C11, T C12, T C13, T C14, T C15, T C16, T C17, T C18, T C19, T C20
    , T C21, T C22, T C23, T C24, T C25, T C26, T C27, T C28, T C29, T C30
    , T C31, T C32, T C33, T C34, T C35, T C36, T C37, T C38, T C39, T C40
    , T C41, T C42, T C43, T C44, T C45, T C46
    >
struct vector47_c
    : vector47<
          integral_c< T,C0 >, integral_c< T,C1 >, integral_c< T,C2 >
        , integral_c< T,C3 >, integral_c< T,C4 >, integral_c< T,C5 >, integral_c< T,C6 >
        , integral_c< T,C7 >, integral_c< T,C8 >, integral_c< T,C9 >
        , integral_c< T,C10 >, integral_c< T,C11 >, integral_c< T,C12 >
        , integral_c< T,C13 >, integral_c< T,C14 >, integral_c< T,C15 >
        , integral_c< T,C16 >, integral_c< T,C17 >, integral_c< T,C18 >
        , integral_c< T,C19 >, integral_c< T,C20 >, integral_c< T,C21 >
        , integral_c< T,C22 >, integral_c< T,C23 >, integral_c< T,C24 >
        , integral_c< T,C25 >, integral_c< T,C26 >, integral_c< T,C27 >
        , integral_c< T,C28 >, integral_c< T,C29 >, integral_c< T,C30 >
        , integral_c< T,C31 >, integral_c< T,C32 >, integral_c< T,C33 >
        , integral_c< T,C34 >, integral_c< T,C35 >, integral_c< T,C36 >
        , integral_c< T,C37 >, integral_c< T,C38 >, integral_c< T,C39 >
        , integral_c< T,C40 >, integral_c< T,C41 >, integral_c< T,C42 >
        , integral_c< T,C43 >, integral_c< T,C44 >, integral_c< T,C45 >, integral_c<T
        , C46> 
 >
{
    typedef vector47_c type;
    typedef T value_type;
};

template<
      typename T
    , T C0, T C1, T C2, T C3, T C4, T C5, T C6, T C7, T C8, T C9, T C10
    , T C11, T C12, T C13, T C14, T C15, T C16, T C17, T C18, T C19, T C20
    , T C21, T C22, T C23, T C24, T C25, T C26, T C27, T C28, T C29, T C30
    , T C31, T C32, T C33, T C34, T C35, T C36, T C37, T C38, T C39, T C40
    , T C41, T C42, T C43, T C44, T C45, T C46, T C47
    >
struct vector48_c
    : vector48<
          integral_c< T,C0 >, integral_c< T,C1 >, integral_c< T,C2 >
        , integral_c< T,C3 >, integral_c< T,C4 >, integral_c< T,C5 >, integral_c< T,C6 >
        , integral_c< T,C7 >, integral_c< T,C8 >, integral_c< T,C9 >
        , integral_c< T,C10 >, integral_c< T,C11 >, integral_c< T,C12 >
        , integral_c< T,C13 >, integral_c< T,C14 >, integral_c< T,C15 >
        , integral_c< T,C16 >, integral_c< T,C17 >, integral_c< T,C18 >
        , integral_c< T,C19 >, integral_c< T,C20 >, integral_c< T,C21 >
        , integral_c< T,C22 >, integral_c< T,C23 >, integral_c< T,C24 >
        , integral_c< T,C25 >, integral_c< T,C26 >, integral_c< T,C27 >
        , integral_c< T,C28 >, integral_c< T,C29 >, integral_c< T,C30 >
        , integral_c< T,C31 >, integral_c< T,C32 >, integral_c< T,C33 >
        , integral_c< T,C34 >, integral_c< T,C35 >, integral_c< T,C36 >
        , integral_c< T,C37 >, integral_c< T,C38 >, integral_c< T,C39 >
        , integral_c< T,C40 >, integral_c< T,C41 >, integral_c< T,C42 >
        , integral_c< T,C43 >, integral_c< T,C44 >, integral_c< T,C45 >
        , integral_c< T,C46 >, integral_c< T,C47 > 
 >
{
    typedef vector48_c type;
    typedef T value_type;
};

template<
      typename T
    , T C0, T C1, T C2, T C3, T C4, T C5, T C6, T C7, T C8, T C9, T C10
    , T C11, T C12, T C13, T C14, T C15, T C16, T C17, T C18, T C19, T C20
    , T C21, T C22, T C23, T C24, T C25, T C26, T C27, T C28, T C29, T C30
    , T C31, T C32, T C33, T C34, T C35, T C36, T C37, T C38, T C39, T C40
    , T C41, T C42, T C43, T C44, T C45, T C46, T C47, T C48
    >
struct vector49_c
    : vector49<
          integral_c< T,C0 >, integral_c< T,C1 >, integral_c< T,C2 >
        , integral_c< T,C3 >, integral_c< T,C4 >, integral_c< T,C5 >, integral_c< T,C6 >
        , integral_c< T,C7 >, integral_c< T,C8 >, integral_c< T,C9 >
        , integral_c< T,C10 >, integral_c< T,C11 >, integral_c< T,C12 >
        , integral_c< T,C13 >, integral_c< T,C14 >, integral_c< T,C15 >
        , integral_c< T,C16 >, integral_c< T,C17 >, integral_c< T,C18 >
        , integral_c< T,C19 >, integral_c< T,C20 >, integral_c< T,C21 >
        , integral_c< T,C22 >, integral_c< T,C23 >, integral_c< T,C24 >
        , integral_c< T,C25 >, integral_c< T,C26 >, integral_c< T,C27 >
        , integral_c< T,C28 >, integral_c< T,C29 >, integral_c< T,C30 >
        , integral_c< T,C31 >, integral_c< T,C32 >, integral_c< T,C33 >
        , integral_c< T,C34 >, integral_c< T,C35 >, integral_c< T,C36 >
        , integral_c< T,C37 >, integral_c< T,C38 >, integral_c< T,C39 >
        , integral_c< T,C40 >, integral_c< T,C41 >, integral_c< T,C42 >
        , integral_c< T,C43 >, integral_c< T,C44 >, integral_c< T,C45 >
        , integral_c< T,C46 >, integral_c< T,C47 >, integral_c< T,C48 > 
 >
{
    typedef vector49_c type;
    typedef T value_type;
};

template<
      typename T
    , T C0, T C1, T C2, T C3, T C4, T C5, T C6, T C7, T C8, T C9, T C10
    , T C11, T C12, T C13, T C14, T C15, T C16, T C17, T C18, T C19, T C20
    , T C21, T C22, T C23, T C24, T C25, T C26, T C27, T C28, T C29, T C30
    , T C31, T C32, T C33, T C34, T C35, T C36, T C37, T C38, T C39, T C40
    , T C41, T C42, T C43, T C44, T C45, T C46, T C47, T C48, T C49
    >
struct vector50_c
    : vector50<
          integral_c< T,C0 >, integral_c< T,C1 >, integral_c< T,C2 >
        , integral_c< T,C3 >, integral_c< T,C4 >, integral_c< T,C5 >, integral_c< T,C6 >
        , integral_c< T,C7 >, integral_c< T,C8 >, integral_c< T,C9 >
        , integral_c< T,C10 >, integral_c< T,C11 >, integral_c< T,C12 >
        , integral_c< T,C13 >, integral_c< T,C14 >, integral_c< T,C15 >
        , integral_c< T,C16 >, integral_c< T,C17 >, integral_c< T,C18 >
        , integral_c< T,C19 >, integral_c< T,C20 >, integral_c< T,C21 >
        , integral_c< T,C22 >, integral_c< T,C23 >, integral_c< T,C24 >
        , integral_c< T,C25 >, integral_c< T,C26 >, integral_c< T,C27 >
        , integral_c< T,C28 >, integral_c< T,C29 >, integral_c< T,C30 >
        , integral_c< T,C31 >, integral_c< T,C32 >, integral_c< T,C33 >
        , integral_c< T,C34 >, integral_c< T,C35 >, integral_c< T,C36 >
        , integral_c< T,C37 >, integral_c< T,C38 >, integral_c< T,C39 >
        , integral_c< T,C40 >, integral_c< T,C41 >, integral_c< T,C42 >
        , integral_c< T,C43 >, integral_c< T,C44 >, integral_c< T,C45 >
        , integral_c< T,C46 >, integral_c< T,C47 >, integral_c< T,C48 >, integral_c<T
        , C49> 
 >
{
    typedef vector50_c type;
    typedef T value_type;
};

}}
