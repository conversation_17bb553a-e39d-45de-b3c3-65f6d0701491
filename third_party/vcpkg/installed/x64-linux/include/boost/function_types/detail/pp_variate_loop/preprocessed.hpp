
// (C) Copyright <PERSON>
//
// Use modification and distribution are subject to the boost Software License,
// Version 1.0. (See http://www.boost.org/LICENSE_1_0.txt).

//------------------------------------------------------------------------------

// this file has been generated from the master.hpp file in the same directory
# define BOOST_FT_mfp 0
# define BOOST_FT_syntax BOOST_FT_type_function
# if ! ! (4 & (BOOST_FT_variations))
# if (519 & (BOOST_FT_cond)) == (BOOST_FT_cond)
# define BOOST_FT_ell
# define BOOST_FT_nullary_param BOOST_FT_NULLARY_PARAM
# define BOOST_FT_cv 
# define BOOST_FT_flags 519
# include BOOST_FT_variate_file
# undef BOOST_FT_cv
# undef BOOST_FT_ell
# undef BOOST_FT_nullary_param
# undef BOOST_FT_flags
# endif
# endif
# if ! ! (4 & (BOOST_FT_variations))
# if (263 & (BOOST_FT_cond)) == (BOOST_FT_cond)
# define BOOST_FT_ell ...
# define BOOST_FT_nullary_param
# define BOOST_FT_cv 
# define BOOST_FT_flags 263
# include BOOST_FT_variate_file
# undef BOOST_FT_cv
# undef BOOST_FT_ell
# undef BOOST_FT_nullary_param
# undef BOOST_FT_flags
# endif
# endif
# if !BOOST_FT_NO_CV_FUNC_SUPPORT
# if ! ! (4 & (BOOST_FT_variations))
# if (1543 & (BOOST_FT_cond)) == (BOOST_FT_cond)
# define BOOST_FT_ell
# define BOOST_FT_nullary_param BOOST_FT_NULLARY_PARAM
# define BOOST_FT_cv const
# define BOOST_FT_flags 1543
# include BOOST_FT_variate_file
# undef BOOST_FT_cv
# undef BOOST_FT_ell
# undef BOOST_FT_nullary_param
# undef BOOST_FT_flags
# endif
# endif
# if ! ! (4 & (BOOST_FT_variations))
# if (1287 & (BOOST_FT_cond)) == (BOOST_FT_cond)
# define BOOST_FT_ell ...
# define BOOST_FT_nullary_param
# define BOOST_FT_cv const
# define BOOST_FT_flags 1287
# include BOOST_FT_variate_file
# undef BOOST_FT_cv
# undef BOOST_FT_ell
# undef BOOST_FT_nullary_param
# undef BOOST_FT_flags
# endif
# endif
# if ! ! (4 & (BOOST_FT_variations))
# if (2567 & (BOOST_FT_cond)) == (BOOST_FT_cond)
# define BOOST_FT_ell
# define BOOST_FT_nullary_param BOOST_FT_NULLARY_PARAM
# define BOOST_FT_cv volatile
# define BOOST_FT_flags 2567
# include BOOST_FT_variate_file
# undef BOOST_FT_cv
# undef BOOST_FT_ell
# undef BOOST_FT_nullary_param
# undef BOOST_FT_flags
# endif
# endif
# if ! ! (4 & (BOOST_FT_variations))
# if (2311 & (BOOST_FT_cond)) == (BOOST_FT_cond)
# define BOOST_FT_ell ...
# define BOOST_FT_nullary_param
# define BOOST_FT_cv volatile
# define BOOST_FT_flags 2311
# include BOOST_FT_variate_file
# undef BOOST_FT_cv
# undef BOOST_FT_ell
# undef BOOST_FT_nullary_param
# undef BOOST_FT_flags
# endif
# endif
# if ! ! (4 & (BOOST_FT_variations))
# if (3591 & (BOOST_FT_cond)) == (BOOST_FT_cond)
# define BOOST_FT_ell
# define BOOST_FT_nullary_param BOOST_FT_NULLARY_PARAM
# define BOOST_FT_cv const volatile
# define BOOST_FT_flags 3591
# include BOOST_FT_variate_file
# undef BOOST_FT_cv
# undef BOOST_FT_ell
# undef BOOST_FT_nullary_param
# undef BOOST_FT_flags
# endif
# endif
# if ! ! (4 & (BOOST_FT_variations))
# if (3335 & (BOOST_FT_cond)) == (BOOST_FT_cond)
# define BOOST_FT_ell ...
# define BOOST_FT_nullary_param
# define BOOST_FT_cv const volatile
# define BOOST_FT_flags 3335
# include BOOST_FT_variate_file
# undef BOOST_FT_cv
# undef BOOST_FT_ell
# undef BOOST_FT_nullary_param
# undef BOOST_FT_flags
# endif
# endif
# endif
# undef BOOST_FT_syntax
# define BOOST_FT_syntax BOOST_FT_type_function_pointer
# if ! ! (8 & (BOOST_FT_variations))
# if (523 & (BOOST_FT_cond)) == (BOOST_FT_cond)
# define BOOST_FT_ell
# define BOOST_FT_nullary_param BOOST_FT_NULLARY_PARAM
# define BOOST_FT_cv 
# define BOOST_FT_flags 523
# include BOOST_FT_variate_file
# undef BOOST_FT_cv
# undef BOOST_FT_ell
# undef BOOST_FT_nullary_param
# undef BOOST_FT_flags
# endif
# endif
# if ! ! (8 & (BOOST_FT_variations))
# if (267 & (BOOST_FT_cond)) == (BOOST_FT_cond)
# define BOOST_FT_ell ...
# define BOOST_FT_nullary_param
# define BOOST_FT_cv 
# define BOOST_FT_flags 267
# include BOOST_FT_variate_file
# undef BOOST_FT_cv
# undef BOOST_FT_ell
# undef BOOST_FT_nullary_param
# undef BOOST_FT_flags
# endif
# endif
# undef BOOST_FT_syntax
# define BOOST_FT_syntax BOOST_FT_type_function_reference
# if ! ! (16 & (BOOST_FT_variations))
# if (531 & (BOOST_FT_cond)) == (BOOST_FT_cond)
# define BOOST_FT_ell
# define BOOST_FT_nullary_param BOOST_FT_NULLARY_PARAM
# define BOOST_FT_cv 
# define BOOST_FT_flags 531
# include BOOST_FT_variate_file
# undef BOOST_FT_cv
# undef BOOST_FT_ell
# undef BOOST_FT_nullary_param
# undef BOOST_FT_flags
# endif
# endif
# if ! ! (16 & (BOOST_FT_variations))
# if (275 & (BOOST_FT_cond)) == (BOOST_FT_cond)
# define BOOST_FT_ell ...
# define BOOST_FT_nullary_param
# define BOOST_FT_cv 
# define BOOST_FT_flags 275
# include BOOST_FT_variate_file
# undef BOOST_FT_cv
# undef BOOST_FT_ell
# undef BOOST_FT_nullary_param
# undef BOOST_FT_flags
# endif
# endif
# undef BOOST_FT_syntax
# undef BOOST_FT_mfp
# define BOOST_FT_mfp 1
# define BOOST_FT_syntax BOOST_FT_type_member_function_pointer
# if ! ! (96 & (BOOST_FT_variations))
# if (609 & (BOOST_FT_cond)) == (BOOST_FT_cond)
# define BOOST_FT_ell
# define BOOST_FT_nullary_param BOOST_FT_NULLARY_PARAM
# define BOOST_FT_cv 
# define BOOST_FT_flags 609
# include BOOST_FT_variate_file
# undef BOOST_FT_cv
# undef BOOST_FT_ell
# undef BOOST_FT_nullary_param
# undef BOOST_FT_flags
# endif
# endif
# if ! ! (96 & (BOOST_FT_variations))
# if (353 & (BOOST_FT_cond)) == (BOOST_FT_cond)
# define BOOST_FT_ell ...
# define BOOST_FT_nullary_param
# define BOOST_FT_cv 
# define BOOST_FT_flags 353
# include BOOST_FT_variate_file
# undef BOOST_FT_cv
# undef BOOST_FT_ell
# undef BOOST_FT_nullary_param
# undef BOOST_FT_flags
# endif
# endif
# if ! ! (96 & (BOOST_FT_variations))
# if (1633 & (BOOST_FT_cond)) == (BOOST_FT_cond)
# define BOOST_FT_ell
# define BOOST_FT_nullary_param BOOST_FT_NULLARY_PARAM
# define BOOST_FT_cv const
# define BOOST_FT_flags 1633
# include BOOST_FT_variate_file
# undef BOOST_FT_cv
# undef BOOST_FT_ell
# undef BOOST_FT_nullary_param
# undef BOOST_FT_flags
# endif
# endif
# if ! ! (96 & (BOOST_FT_variations))
# if (1377 & (BOOST_FT_cond)) == (BOOST_FT_cond)
# define BOOST_FT_ell ...
# define BOOST_FT_nullary_param
# define BOOST_FT_cv const
# define BOOST_FT_flags 1377
# include BOOST_FT_variate_file
# undef BOOST_FT_cv
# undef BOOST_FT_ell
# undef BOOST_FT_nullary_param
# undef BOOST_FT_flags
# endif
# endif
# if ! ! (96 & (BOOST_FT_variations))
# if (2657 & (BOOST_FT_cond)) == (BOOST_FT_cond)
# define BOOST_FT_ell
# define BOOST_FT_nullary_param BOOST_FT_NULLARY_PARAM
# define BOOST_FT_cv volatile
# define BOOST_FT_flags 2657
# include BOOST_FT_variate_file
# undef BOOST_FT_cv
# undef BOOST_FT_ell
# undef BOOST_FT_nullary_param
# undef BOOST_FT_flags
# endif
# endif
# if ! ! (96 & (BOOST_FT_variations))
# if (2401 & (BOOST_FT_cond)) == (BOOST_FT_cond)
# define BOOST_FT_ell ...
# define BOOST_FT_nullary_param
# define BOOST_FT_cv volatile
# define BOOST_FT_flags 2401
# include BOOST_FT_variate_file
# undef BOOST_FT_cv
# undef BOOST_FT_ell
# undef BOOST_FT_nullary_param
# undef BOOST_FT_flags
# endif
# endif
# if ! ! (96 & (BOOST_FT_variations))
# if (3681 & (BOOST_FT_cond)) == (BOOST_FT_cond)
# define BOOST_FT_ell
# define BOOST_FT_nullary_param BOOST_FT_NULLARY_PARAM
# define BOOST_FT_cv const volatile
# define BOOST_FT_flags 3681
# include BOOST_FT_variate_file
# undef BOOST_FT_cv
# undef BOOST_FT_ell
# undef BOOST_FT_nullary_param
# undef BOOST_FT_flags
# endif
# endif
# if ! ! (96 & (BOOST_FT_variations))
# if (3425 & (BOOST_FT_cond)) == (BOOST_FT_cond)
# define BOOST_FT_ell ...
# define BOOST_FT_nullary_param
# define BOOST_FT_cv const volatile
# define BOOST_FT_flags 3425
# include BOOST_FT_variate_file
# undef BOOST_FT_cv
# undef BOOST_FT_ell
# undef BOOST_FT_nullary_param
# undef BOOST_FT_flags
# endif
# endif
# undef BOOST_FT_syntax
# undef BOOST_FT_mfp
