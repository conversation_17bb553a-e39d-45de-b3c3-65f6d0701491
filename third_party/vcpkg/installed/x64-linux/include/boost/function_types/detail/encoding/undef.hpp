
// (C) Copyright <PERSON>
//
// Use modification and distribution are subject to the boost Software License,
// Version 1.0. (See http://www.boost.org/LICENSE_1_0.txt).

//------------------------------------------------------------------------------

// no include guards, this file is intended for multiple inclusion

#undef BOOST_FT_type_mask
#undef BOOST_FT_kind_mask
#undef BOOST_FT_callable_builtin
#undef BOOST_FT_non_member
#undef BOOST_FT_function
#undef BOOST_FT_pointer
#undef BOOST_FT_reference
#undef BOOST_FT_non_member_callable_builtin
#undef BOOST_FT_member_pointer
#undef BOOST_FT_member_function_pointer
#undef BOOST_FT_member_object_pointer
#undef BOOST_FT_member_object_pointer_flags

#undef BOOST_FT_variadic
#undef BOOST_FT_non_variadic
#undef BOOST_FT_variadic_mask

#undef BOOST_FT_const
#undef BOOST_FT_volatile

#undef BOOST_FT_default_cc
#undef BOOST_FT_cc_mask

#undef BOOST_FT_flags_mask
#undef BOOST_FT_full_mask

#undef BOOST_FT_arity_mask

