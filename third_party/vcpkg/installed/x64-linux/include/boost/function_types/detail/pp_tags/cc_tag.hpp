
// (C) Copyright <PERSON>
//
// Use modification and distribution are subject to the boost Software License,
// Version 1.0. (See http://www.boost.org/LICENSE_1_0.txt).

//------------------------------------------------------------------------------

// no include guards, this file is intended for multiple inclusions

  struct BOOST_FT_cc_name
  {
    typedef detail::encode_bits<0,BOOST_FT_cc_id> bits;
    typedef detail::constant<BOOST_FT_cc_mask> mask;
  };


