# /* **************************************************************************
#  *                                                                          *
#  *     (C) Copyright <PERSON> 2002.
#  *     Distributed under the Boost Software License, Version 1.0. (See
#  *     accompanying file LICENSE_1_0.txt or copy at
#  *     http://www.boost.org/LICENSE_1_0.txt)
#  *                                                                          *
#  ************************************************************************** */
#
# /* Revised by <PERSON> (2020) */
#
# /* See http://www.boost.org for most recent version. */
#
# ifndef BOOST_PREPROCESSOR_SEQ_FOLD_LEFT_HPP
# define BOOST_PREPROCESSOR_SEQ_FOLD_LEFT_HPP
#
# include <boost/preprocessor/arithmetic/dec.hpp>
# include <boost/preprocessor/cat.hpp>
# include <boost/preprocessor/control/if.hpp>
# include <boost/preprocessor/debug/error.hpp>
# include <boost/preprocessor/detail/auto_rec.hpp>
# include <boost/preprocessor/seq/seq.hpp>
# include <boost/preprocessor/seq/size.hpp>
#
# /* BOOST_PP_SEQ_FOLD_LEFT */
#
# if 0
#    define BOOST_PP_SEQ_FOLD_LEFT(op, state, seq) ...
# endif
#
# include <boost/preprocessor/config/config.hpp>
#
# if ~BOOST_PP_CONFIG_FLAGS() & BOOST_PP_CONFIG_STRICT()
#
# define BOOST_PP_SEQ_FOLD_LEFT BOOST_PP_CAT(BOOST_PP_SEQ_FOLD_LEFT_, BOOST_PP_AUTO_REC(BOOST_PP_SEQ_FOLD_LEFT_P, 256))
# define BOOST_PP_SEQ_FOLD_LEFT_P(n) BOOST_PP_CAT(BOOST_PP_SEQ_FOLD_LEFT_CHECK_, BOOST_PP_SEQ_FOLD_LEFT_I_ ## n(BOOST_PP_SEQ_FOLD_LEFT_O, BOOST_PP_NIL, (nil), 1))
# define BOOST_PP_SEQ_FOLD_LEFT_O(s, st, _) st
#
# define BOOST_PP_SEQ_FOLD_LEFT_257(op, st, ss) BOOST_PP_ERROR(0x0005)
# define BOOST_PP_SEQ_FOLD_LEFT_I_257(op, st, ss, sz) BOOST_PP_ERROR(0x0005)
#
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_NIL 1
#
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_1(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_2(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_3(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_4(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_5(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_6(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_7(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_8(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_9(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_10(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_11(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_12(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_13(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_14(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_15(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_16(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_17(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_18(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_19(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_20(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_21(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_22(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_23(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_24(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_25(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_26(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_27(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_28(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_29(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_30(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_31(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_32(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_33(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_34(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_35(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_36(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_37(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_38(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_39(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_40(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_41(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_42(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_43(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_44(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_45(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_46(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_47(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_48(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_49(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_50(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_51(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_52(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_53(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_54(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_55(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_56(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_57(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_58(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_59(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_60(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_61(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_62(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_63(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_64(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_65(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_66(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_67(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_68(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_69(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_70(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_71(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_72(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_73(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_74(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_75(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_76(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_77(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_78(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_79(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_80(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_81(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_82(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_83(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_84(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_85(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_86(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_87(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_88(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_89(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_90(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_91(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_92(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_93(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_94(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_95(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_96(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_97(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_98(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_99(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_100(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_101(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_102(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_103(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_104(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_105(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_106(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_107(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_108(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_109(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_110(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_111(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_112(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_113(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_114(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_115(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_116(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_117(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_118(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_119(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_120(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_121(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_122(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_123(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_124(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_125(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_126(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_127(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_128(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_129(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_130(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_131(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_132(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_133(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_134(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_135(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_136(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_137(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_138(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_139(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_140(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_141(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_142(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_143(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_144(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_145(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_146(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_147(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_148(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_149(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_150(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_151(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_152(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_153(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_154(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_155(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_156(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_157(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_158(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_159(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_160(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_161(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_162(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_163(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_164(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_165(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_166(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_167(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_168(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_169(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_170(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_171(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_172(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_173(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_174(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_175(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_176(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_177(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_178(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_179(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_180(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_181(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_182(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_183(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_184(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_185(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_186(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_187(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_188(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_189(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_190(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_191(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_192(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_193(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_194(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_195(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_196(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_197(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_198(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_199(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_200(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_201(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_202(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_203(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_204(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_205(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_206(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_207(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_208(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_209(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_210(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_211(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_212(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_213(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_214(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_215(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_216(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_217(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_218(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_219(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_220(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_221(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_222(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_223(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_224(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_225(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_226(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_227(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_228(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_229(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_230(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_231(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_232(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_233(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_234(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_235(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_236(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_237(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_238(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_239(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_240(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_241(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_242(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_243(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_244(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_245(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_246(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_247(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_248(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_249(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_250(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_251(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_252(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_253(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_254(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_255(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_256(op, st, ss, sz) 0
#
# define BOOST_PP_SEQ_FOLD_LEFT_F(op, st, ss, sz) st
#
# define BOOST_PP_SEQ_FOLD_LEFT_1(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_1(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_2(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_2(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_3(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_3(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_4(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_4(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_5(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_5(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_6(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_6(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_7(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_7(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_8(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_8(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_9(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_9(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_10(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_10(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_11(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_11(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_12(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_12(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_13(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_13(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_14(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_14(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_15(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_15(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_16(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_16(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_17(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_17(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_18(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_18(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_19(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_19(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_20(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_20(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_21(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_21(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_22(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_22(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_23(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_23(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_24(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_24(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_25(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_25(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_26(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_26(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_27(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_27(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_28(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_28(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_29(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_29(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_30(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_30(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_31(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_31(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_32(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_32(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_33(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_33(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_34(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_34(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_35(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_35(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_36(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_36(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_37(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_37(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_38(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_38(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_39(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_39(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_40(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_40(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_41(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_41(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_42(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_42(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_43(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_43(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_44(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_44(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_45(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_45(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_46(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_46(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_47(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_47(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_48(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_48(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_49(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_49(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_50(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_50(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_51(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_51(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_52(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_52(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_53(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_53(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_54(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_54(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_55(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_55(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_56(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_56(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_57(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_57(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_58(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_58(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_59(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_59(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_60(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_60(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_61(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_61(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_62(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_62(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_63(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_63(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_64(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_64(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_65(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_65(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_66(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_66(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_67(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_67(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_68(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_68(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_69(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_69(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_70(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_70(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_71(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_71(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_72(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_72(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_73(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_73(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_74(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_74(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_75(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_75(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_76(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_76(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_77(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_77(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_78(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_78(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_79(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_79(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_80(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_80(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_81(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_81(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_82(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_82(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_83(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_83(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_84(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_84(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_85(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_85(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_86(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_86(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_87(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_87(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_88(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_88(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_89(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_89(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_90(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_90(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_91(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_91(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_92(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_92(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_93(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_93(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_94(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_94(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_95(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_95(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_96(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_96(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_97(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_97(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_98(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_98(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_99(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_99(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_100(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_100(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_101(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_101(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_102(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_102(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_103(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_103(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_104(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_104(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_105(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_105(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_106(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_106(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_107(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_107(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_108(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_108(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_109(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_109(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_110(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_110(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_111(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_111(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_112(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_112(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_113(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_113(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_114(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_114(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_115(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_115(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_116(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_116(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_117(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_117(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_118(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_118(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_119(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_119(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_120(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_120(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_121(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_121(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_122(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_122(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_123(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_123(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_124(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_124(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_125(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_125(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_126(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_126(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_127(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_127(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_128(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_128(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_129(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_129(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_130(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_130(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_131(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_131(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_132(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_132(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_133(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_133(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_134(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_134(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_135(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_135(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_136(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_136(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_137(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_137(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_138(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_138(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_139(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_139(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_140(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_140(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_141(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_141(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_142(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_142(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_143(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_143(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_144(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_144(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_145(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_145(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_146(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_146(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_147(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_147(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_148(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_148(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_149(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_149(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_150(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_150(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_151(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_151(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_152(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_152(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_153(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_153(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_154(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_154(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_155(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_155(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_156(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_156(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_157(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_157(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_158(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_158(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_159(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_159(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_160(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_160(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_161(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_161(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_162(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_162(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_163(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_163(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_164(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_164(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_165(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_165(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_166(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_166(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_167(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_167(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_168(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_168(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_169(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_169(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_170(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_170(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_171(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_171(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_172(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_172(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_173(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_173(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_174(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_174(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_175(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_175(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_176(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_176(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_177(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_177(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_178(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_178(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_179(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_179(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_180(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_180(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_181(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_181(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_182(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_182(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_183(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_183(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_184(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_184(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_185(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_185(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_186(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_186(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_187(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_187(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_188(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_188(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_189(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_189(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_190(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_190(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_191(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_191(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_192(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_192(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_193(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_193(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_194(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_194(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_195(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_195(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_196(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_196(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_197(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_197(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_198(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_198(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_199(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_199(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_200(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_200(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_201(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_201(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_202(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_202(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_203(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_203(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_204(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_204(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_205(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_205(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_206(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_206(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_207(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_207(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_208(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_208(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_209(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_209(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_210(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_210(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_211(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_211(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_212(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_212(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_213(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_213(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_214(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_214(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_215(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_215(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_216(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_216(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_217(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_217(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_218(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_218(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_219(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_219(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_220(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_220(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_221(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_221(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_222(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_222(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_223(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_223(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_224(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_224(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_225(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_225(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_226(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_226(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_227(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_227(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_228(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_228(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_229(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_229(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_230(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_230(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_231(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_231(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_232(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_232(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_233(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_233(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_234(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_234(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_235(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_235(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_236(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_236(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_237(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_237(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_238(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_238(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_239(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_239(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_240(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_240(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_241(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_241(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_242(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_242(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_243(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_243(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_244(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_244(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_245(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_245(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_246(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_246(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_247(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_247(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_248(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_248(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_249(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_249(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_250(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_250(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_251(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_251(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_252(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_252(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_253(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_253(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_254(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_254(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_255(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_255(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_256(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_256(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
#
# if ~BOOST_PP_CONFIG_FLAGS() & BOOST_PP_CONFIG_DMC()
#    define BOOST_PP_SEQ_FOLD_LEFT_I_1(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_2, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(2, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_2(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_3, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(3, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_3(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_4, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(4, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_4(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_5, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(5, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_5(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_6, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(6, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_6(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_7, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(7, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_7(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_8, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(8, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_8(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_9, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(9, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_9(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_10, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(10, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_10(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_11, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(11, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_11(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_12, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(12, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_12(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_13, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(13, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_13(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_14, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(14, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_14(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_15, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(15, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_15(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_16, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(16, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_16(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_17, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(17, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_17(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_18, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(18, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_18(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_19, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(19, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_19(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_20, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(20, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_20(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_21, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(21, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_21(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_22, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(22, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_22(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_23, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(23, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_23(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_24, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(24, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_24(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_25, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(25, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_25(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_26, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(26, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_26(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_27, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(27, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_27(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_28, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(28, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_28(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_29, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(29, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_29(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_30, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(30, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_30(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_31, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(31, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_31(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_32, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(32, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_32(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_33, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(33, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_33(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_34, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(34, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_34(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_35, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(35, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_35(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_36, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(36, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_36(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_37, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(37, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_37(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_38, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(38, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_38(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_39, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(39, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_39(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_40, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(40, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_40(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_41, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(41, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_41(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_42, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(42, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_42(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_43, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(43, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_43(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_44, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(44, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_44(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_45, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(45, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_45(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_46, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(46, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_46(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_47, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(47, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_47(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_48, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(48, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_48(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_49, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(49, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_49(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_50, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(50, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_50(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_51, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(51, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_51(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_52, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(52, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_52(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_53, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(53, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_53(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_54, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(54, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_54(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_55, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(55, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_55(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_56, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(56, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_56(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_57, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(57, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_57(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_58, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(58, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_58(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_59, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(59, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_59(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_60, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(60, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_60(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_61, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(61, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_61(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_62, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(62, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_62(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_63, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(63, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_63(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_64, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(64, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_64(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_65, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(65, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_65(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_66, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(66, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_66(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_67, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(67, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_67(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_68, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(68, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_68(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_69, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(69, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_69(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_70, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(70, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_70(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_71, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(71, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_71(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_72, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(72, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_72(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_73, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(73, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_73(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_74, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(74, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_74(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_75, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(75, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_75(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_76, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(76, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_76(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_77, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(77, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_77(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_78, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(78, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_78(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_79, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(79, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_79(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_80, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(80, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_80(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_81, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(81, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_81(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_82, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(82, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_82(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_83, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(83, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_83(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_84, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(84, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_84(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_85, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(85, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_85(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_86, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(86, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_86(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_87, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(87, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_87(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_88, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(88, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_88(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_89, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(89, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_89(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_90, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(90, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_90(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_91, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(91, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_91(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_92, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(92, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_92(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_93, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(93, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_93(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_94, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(94, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_94(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_95, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(95, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_95(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_96, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(96, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_96(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_97, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(97, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_97(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_98, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(98, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_98(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_99, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(99, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_99(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_100, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(100, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_100(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_101, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(101, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_101(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_102, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(102, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_102(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_103, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(103, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_103(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_104, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(104, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_104(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_105, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(105, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_105(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_106, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(106, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_106(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_107, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(107, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_107(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_108, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(108, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_108(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_109, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(109, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_109(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_110, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(110, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_110(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_111, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(111, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_111(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_112, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(112, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_112(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_113, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(113, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_113(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_114, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(114, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_114(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_115, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(115, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_115(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_116, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(116, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_116(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_117, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(117, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_117(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_118, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(118, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_118(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_119, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(119, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_119(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_120, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(120, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_120(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_121, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(121, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_121(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_122, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(122, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_122(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_123, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(123, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_123(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_124, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(124, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_124(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_125, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(125, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_125(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_126, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(126, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_126(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_127, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(127, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_127(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_128, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(128, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_128(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_129, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(129, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_129(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_130, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(130, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_130(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_131, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(131, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_131(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_132, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(132, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_132(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_133, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(133, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_133(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_134, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(134, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_134(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_135, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(135, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_135(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_136, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(136, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_136(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_137, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(137, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_137(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_138, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(138, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_138(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_139, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(139, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_139(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_140, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(140, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_140(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_141, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(141, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_141(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_142, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(142, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_142(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_143, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(143, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_143(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_144, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(144, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_144(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_145, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(145, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_145(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_146, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(146, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_146(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_147, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(147, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_147(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_148, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(148, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_148(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_149, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(149, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_149(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_150, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(150, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_150(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_151, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(151, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_151(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_152, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(152, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_152(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_153, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(153, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_153(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_154, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(154, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_154(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_155, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(155, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_155(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_156, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(156, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_156(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_157, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(157, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_157(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_158, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(158, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_158(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_159, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(159, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_159(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_160, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(160, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_160(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_161, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(161, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_161(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_162, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(162, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_162(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_163, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(163, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_163(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_164, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(164, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_164(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_165, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(165, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_165(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_166, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(166, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_166(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_167, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(167, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_167(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_168, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(168, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_168(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_169, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(169, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_169(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_170, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(170, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_170(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_171, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(171, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_171(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_172, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(172, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_172(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_173, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(173, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_173(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_174, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(174, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_174(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_175, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(175, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_175(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_176, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(176, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_176(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_177, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(177, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_177(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_178, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(178, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_178(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_179, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(179, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_179(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_180, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(180, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_180(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_181, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(181, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_181(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_182, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(182, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_182(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_183, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(183, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_183(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_184, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(184, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_184(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_185, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(185, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_185(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_186, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(186, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_186(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_187, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(187, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_187(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_188, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(188, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_188(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_189, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(189, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_189(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_190, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(190, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_190(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_191, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(191, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_191(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_192, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(192, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_192(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_193, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(193, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_193(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_194, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(194, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_194(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_195, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(195, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_195(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_196, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(196, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_196(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_197, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(197, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_197(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_198, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(198, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_198(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_199, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(199, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_199(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_200, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(200, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_200(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_201, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(201, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_201(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_202, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(202, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_202(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_203, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(203, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_203(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_204, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(204, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_204(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_205, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(205, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_205(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_206, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(206, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_206(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_207, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(207, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_207(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_208, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(208, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_208(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_209, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(209, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_209(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_210, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(210, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_210(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_211, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(211, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_211(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_212, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(212, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_212(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_213, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(213, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_213(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_214, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(214, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_214(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_215, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(215, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_215(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_216, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(216, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_216(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_217, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(217, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_217(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_218, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(218, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_218(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_219, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(219, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_219(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_220, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(220, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_220(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_221, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(221, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_221(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_222, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(222, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_222(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_223, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(223, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_223(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_224, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(224, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_224(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_225, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(225, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_225(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_226, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(226, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_226(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_227, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(227, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_227(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_228, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(228, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_228(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_229, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(229, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_229(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_230, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(230, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_230(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_231, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(231, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_231(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_232, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(232, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_232(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_233, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(233, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_233(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_234, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(234, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_234(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_235, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(235, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_235(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_236, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(236, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_236(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_237, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(237, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_237(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_238, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(238, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_238(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_239, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(239, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_239(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_240, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(240, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_240(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_241, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(241, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_241(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_242, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(242, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_242(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_243, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(243, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_243(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_244, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(244, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_244(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_245, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(245, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_245(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_246, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(246, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_246(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_247, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(247, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_247(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_248, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(248, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_248(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_249, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(249, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_249(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_250, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(250, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_250(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_251, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(251, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_251(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_252, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(252, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_252(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_253, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(253, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_253(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_254, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(254, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_254(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_255, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(255, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_255(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_256, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(256, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_256(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_257, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(257, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# else
#    define BOOST_PP_SEQ_FOLD_LEFT_I_1(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_2, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(2, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_2(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_3, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(3, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_3(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_4, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(4, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_4(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_5, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(5, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_5(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_6, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(6, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_6(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_7, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(7, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_7(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_8, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(8, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_8(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_9, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(9, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_9(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_10, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(10, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_10(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_11, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(11, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_11(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_12, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(12, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_12(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_13, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(13, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_13(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_14, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(14, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_14(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_15, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(15, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_15(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_16, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(16, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_16(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_17, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(17, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_17(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_18, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(18, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_18(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_19, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(19, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_19(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_20, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(20, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_20(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_21, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(21, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_21(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_22, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(22, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_22(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_23, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(23, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_23(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_24, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(24, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_24(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_25, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(25, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_25(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_26, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(26, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_26(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_27, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(27, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_27(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_28, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(28, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_28(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_29, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(29, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_29(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_30, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(30, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_30(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_31, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(31, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_31(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_32, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(32, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_32(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_33, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(33, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_33(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_34, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(34, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_34(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_35, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(35, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_35(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_36, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(36, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_36(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_37, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(37, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_37(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_38, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(38, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_38(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_39, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(39, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_39(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_40, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(40, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_40(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_41, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(41, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_41(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_42, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(42, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_42(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_43, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(43, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_43(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_44, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(44, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_44(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_45, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(45, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_45(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_46, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(46, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_46(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_47, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(47, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_47(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_48, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(48, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_48(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_49, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(49, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_49(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_50, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(50, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_50(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_51, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(51, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_51(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_52, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(52, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_52(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_53, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(53, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_53(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_54, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(54, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_54(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_55, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(55, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_55(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_56, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(56, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_56(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_57, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(57, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_57(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_58, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(58, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_58(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_59, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(59, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_59(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_60, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(60, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_60(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_61, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(61, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_61(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_62, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(62, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_62(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_63, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(63, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_63(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_64, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(64, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_64(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_65, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(65, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_65(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_66, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(66, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_66(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_67, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(67, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_67(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_68, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(68, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_68(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_69, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(69, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_69(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_70, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(70, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_70(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_71, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(71, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_71(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_72, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(72, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_72(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_73, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(73, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_73(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_74, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(74, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_74(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_75, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(75, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_75(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_76, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(76, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_76(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_77, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(77, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_77(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_78, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(78, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_78(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_79, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(79, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_79(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_80, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(80, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_80(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_81, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(81, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_81(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_82, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(82, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_82(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_83, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(83, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_83(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_84, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(84, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_84(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_85, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(85, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_85(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_86, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(86, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_86(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_87, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(87, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_87(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_88, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(88, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_88(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_89, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(89, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_89(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_90, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(90, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_90(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_91, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(91, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_91(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_92, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(92, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_92(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_93, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(93, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_93(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_94, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(94, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_94(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_95, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(95, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_95(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_96, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(96, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_96(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_97, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(97, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_97(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_98, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(98, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_98(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_99, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(99, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_99(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_100, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(100, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_100(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_101, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(101, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_101(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_102, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(102, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_102(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_103, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(103, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_103(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_104, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(104, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_104(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_105, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(105, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_105(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_106, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(106, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_106(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_107, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(107, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_107(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_108, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(108, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_108(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_109, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(109, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_109(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_110, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(110, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_110(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_111, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(111, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_111(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_112, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(112, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_112(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_113, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(113, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_113(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_114, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(114, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_114(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_115, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(115, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_115(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_116, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(116, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_116(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_117, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(117, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_117(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_118, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(118, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_118(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_119, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(119, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_119(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_120, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(120, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_120(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_121, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(121, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_121(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_122, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(122, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_122(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_123, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(123, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_123(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_124, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(124, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_124(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_125, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(125, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_125(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_126, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(126, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_126(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_127, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(127, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_127(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_128, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(128, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_128(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_129, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(129, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_129(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_130, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(130, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_130(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_131, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(131, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_131(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_132, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(132, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_132(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_133, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(133, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_133(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_134, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(134, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_134(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_135, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(135, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_135(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_136, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(136, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_136(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_137, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(137, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_137(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_138, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(138, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_138(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_139, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(139, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_139(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_140, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(140, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_140(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_141, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(141, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_141(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_142, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(142, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_142(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_143, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(143, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_143(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_144, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(144, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_144(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_145, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(145, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_145(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_146, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(146, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_146(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_147, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(147, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_147(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_148, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(148, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_148(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_149, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(149, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_149(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_150, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(150, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_150(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_151, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(151, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_151(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_152, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(152, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_152(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_153, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(153, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_153(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_154, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(154, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_154(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_155, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(155, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_155(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_156, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(156, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_156(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_157, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(157, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_157(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_158, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(158, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_158(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_159, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(159, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_159(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_160, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(160, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_160(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_161, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(161, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_161(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_162, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(162, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_162(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_163, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(163, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_163(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_164, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(164, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_164(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_165, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(165, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_165(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_166, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(166, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_166(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_167, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(167, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_167(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_168, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(168, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_168(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_169, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(169, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_169(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_170, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(170, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_170(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_171, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(171, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_171(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_172, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(172, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_172(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_173, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(173, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_173(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_174, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(174, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_174(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_175, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(175, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_175(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_176, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(176, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_176(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_177, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(177, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_177(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_178, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(178, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_178(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_179, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(179, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_179(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_180, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(180, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_180(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_181, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(181, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_181(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_182, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(182, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_182(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_183, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(183, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_183(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_184, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(184, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_184(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_185, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(185, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_185(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_186, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(186, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_186(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_187, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(187, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_187(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_188, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(188, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_188(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_189, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(189, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_189(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_190, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(190, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_190(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_191, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(191, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_191(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_192, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(192, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_192(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_193, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(193, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_193(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_194, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(194, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_194(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_195, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(195, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_195(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_196, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(196, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_196(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_197, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(197, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_197(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_198, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(198, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_198(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_199, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(199, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_199(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_200, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(200, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_200(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_201, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(201, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_201(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_202, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(202, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_202(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_203, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(203, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_203(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_204, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(204, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_204(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_205, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(205, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_205(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_206, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(206, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_206(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_207, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(207, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_207(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_208, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(208, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_208(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_209, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(209, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_209(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_210, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(210, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_210(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_211, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(211, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_211(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_212, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(212, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_212(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_213, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(213, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_213(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_214, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(214, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_214(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_215, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(215, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_215(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_216, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(216, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_216(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_217, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(217, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_217(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_218, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(218, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_218(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_219, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(219, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_219(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_220, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(220, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_220(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_221, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(221, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_221(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_222, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(222, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_222(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_223, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(223, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_223(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_224, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(224, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_224(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_225, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(225, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_225(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_226, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(226, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_226(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_227, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(227, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_227(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_228, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(228, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_228(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_229, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(229, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_229(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_230, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(230, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_230(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_231, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(231, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_231(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_232, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(232, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_232(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_233, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(233, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_233(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_234, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(234, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_234(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_235, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(235, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_235(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_236, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(236, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_236(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_237, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(237, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_237(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_238, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(238, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_238(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_239, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(239, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_239(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_240, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(240, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_240(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_241, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(241, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_241(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_242, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(242, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_242(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_243, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(243, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_243(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_244, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(244, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_244(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_245, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(245, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_245(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_246, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(246, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_246(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_247, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(247, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_247(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_248, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(248, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_248(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_249, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(249, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_249(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_250, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(250, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_250(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_251, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(251, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_251(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_252, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(252, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_252(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_253, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(253, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_253(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_254, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(254, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_254(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_255, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(255, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_255(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_256, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(256, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#    define BOOST_PP_SEQ_FOLD_LEFT_I_256(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_257, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op##(257, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# endif
#
# else
#
# include <boost/preprocessor/config/limits.hpp>
#
# if BOOST_PP_LIMIT_SEQ == 256
# define BOOST_PP_SEQ_FOLD_LEFT BOOST_PP_CAT(BOOST_PP_SEQ_FOLD_LEFT_, BOOST_PP_DEC(BOOST_PP_AUTO_REC(BOOST_PP_SEQ_FOLD_LEFT_P, 256)))
# elif BOOST_PP_LIMIT_SEQ == 512
# define BOOST_PP_SEQ_FOLD_LEFT BOOST_PP_CAT(BOOST_PP_SEQ_FOLD_LEFT_, BOOST_PP_DEC(BOOST_PP_AUTO_REC(BOOST_PP_SEQ_FOLD_LEFT_P, 512)))
# elif BOOST_PP_LIMIT_SEQ == 1024
# define BOOST_PP_SEQ_FOLD_LEFT BOOST_PP_CAT(BOOST_PP_SEQ_FOLD_LEFT_, BOOST_PP_DEC(BOOST_PP_AUTO_REC(BOOST_PP_SEQ_FOLD_LEFT_P, 1024)))
# else
# error Incorrect value for the BOOST_PP_LIMIT_SEQ limit
# endif
#
# define BOOST_PP_SEQ_FOLD_LEFT_P(n) BOOST_PP_SEQ_FOLD_LEFT_P_DEC(BOOST_PP_DEC(n))
# define BOOST_PP_SEQ_FOLD_LEFT_P_DEC(n) BOOST_PP_CAT(BOOST_PP_SEQ_FOLD_LEFT_CHECK_, BOOST_PP_CAT(BOOST_PP_SEQ_FOLD_LEFT_I_,n)(BOOST_PP_SEQ_FOLD_LEFT_O, BOOST_PP_NIL, (nil), 1))
# define BOOST_PP_SEQ_FOLD_LEFT_O(s, st, _) st
#
# if BOOST_PP_LIMIT_SEQ == 256
# define BOOST_PP_SEQ_FOLD_LEFT_257(op, st, ss) BOOST_PP_ERROR(0x0005)
# define BOOST_PP_SEQ_FOLD_LEFT_I_257(op, st, ss, sz) BOOST_PP_ERROR(0x0005)
# elif BOOST_PP_LIMIT_SEQ == 512
# define BOOST_PP_SEQ_FOLD_LEFT_513(op, st, ss) BOOST_PP_ERROR(0x0005)
# define BOOST_PP_SEQ_FOLD_LEFT_I_513(op, st, ss, sz) BOOST_PP_ERROR(0x0005)
# elif BOOST_PP_LIMIT_SEQ == 1024
# define BOOST_PP_SEQ_FOLD_LEFT_1025(op, st, ss) BOOST_PP_ERROR(0x0005)
# define BOOST_PP_SEQ_FOLD_LEFT_I_1025(op, st, ss, sz) BOOST_PP_ERROR(0x0005)
# endif
#
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_NIL 1
#
# define BOOST_PP_SEQ_FOLD_LEFT_F(op, st, ss, sz) st
#
# if BOOST_PP_LIMIT_SEQ == 256
# include <boost/preprocessor/seq/limits/fold_left_256.hpp>
# elif BOOST_PP_LIMIT_SEQ == 512
# include <boost/preprocessor/seq/limits/fold_left_256.hpp>
# include <boost/preprocessor/seq/limits/fold_left_512.hpp>
# elif BOOST_PP_LIMIT_SEQ == 1024
# include <boost/preprocessor/seq/limits/fold_left_256.hpp>
# include <boost/preprocessor/seq/limits/fold_left_512.hpp>
# include <boost/preprocessor/seq/limits/fold_left_1024.hpp>
# endif
#
# endif
#
# endif
