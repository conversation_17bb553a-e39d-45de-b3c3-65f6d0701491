# /* **************************************************************************
#  *                                                                          *
#  *     (C) Copyright <PERSON> 2005.                                  *
#  *     Distributed under the Boost Software License, Version 1.0. (See      *
#  *     accompanying file LICENSE_1_0.txt or copy at                         *
#  *     http://www.boost.org/LICENSE_1_0.txt)                                *
#  *                                                                          *
#  ************************************************************************** */
#
# /* See http://www.boost.org for most recent version. */
#
# ifndef BOOST_PREPROCESSOR_SLOT_COUNTER_HPP
# define BOOST_PREPROCESSOR_SLOT_COUNTER_HPP
#
# include <boost/preprocessor/slot/detail/def.hpp>
#
# /* BOOST_PP_COUNTER */
#
# define BOOST_PP_COUNTER 0
#
# /* BOOST_PP_UPDATE_COUNTER */
#
# define BOOST_PP_UPDATE_COUNTER() <boost/preprocessor/slot/detail/counter.hpp>
#
# endif
