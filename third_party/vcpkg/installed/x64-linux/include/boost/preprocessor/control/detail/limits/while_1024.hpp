# /* Copyright (C) 2001
#  * Housemarque Oy
#  * http://www.housemarque.com
#  *
#  * Distributed under the Boost Software License, Version 1.0. (See
#  * accompanying file LICENSE_1_0.txt or copy at
#  * http://www.boost.org/LICENSE_1_0.txt)
#  */
#
# /* Revised by <PERSON> (2002) */
# /* Revised by <PERSON> (2020) */
#
# /* See http://www.boost.org for most recent version. */
#
# ifndef BOOST_PREPROCESSOR_CONTROL_DETAIL_WHILE_1024_HPP
# define BOOST_PREPROCESSOR_CONTROL_DETAIL_WHILE_1024_HPP
#
# define BOOST_PP_WHILE_513(p, o, s) BOOST_PP_WHILE_513_C(BOOST_PP_BOOL(p(514, s)), p, o, s)
# define BOOST_PP_WHILE_514(p, o, s) BOOST_PP_WHILE_514_C(BOOST_PP_BOOL(p(515, s)), p, o, s)
# define BOOST_PP_WHILE_515(p, o, s) BOOST_PP_WHILE_515_C(BOOST_PP_BOOL(p(516, s)), p, o, s)
# define BOOST_PP_WHILE_516(p, o, s) BOOST_PP_WHILE_516_C(BOOST_PP_BOOL(p(517, s)), p, o, s)
# define BOOST_PP_WHILE_517(p, o, s) BOOST_PP_WHILE_517_C(BOOST_PP_BOOL(p(518, s)), p, o, s)
# define BOOST_PP_WHILE_518(p, o, s) BOOST_PP_WHILE_518_C(BOOST_PP_BOOL(p(519, s)), p, o, s)
# define BOOST_PP_WHILE_519(p, o, s) BOOST_PP_WHILE_519_C(BOOST_PP_BOOL(p(520, s)), p, o, s)
# define BOOST_PP_WHILE_520(p, o, s) BOOST_PP_WHILE_520_C(BOOST_PP_BOOL(p(521, s)), p, o, s)
# define BOOST_PP_WHILE_521(p, o, s) BOOST_PP_WHILE_521_C(BOOST_PP_BOOL(p(522, s)), p, o, s)
# define BOOST_PP_WHILE_522(p, o, s) BOOST_PP_WHILE_522_C(BOOST_PP_BOOL(p(523, s)), p, o, s)
# define BOOST_PP_WHILE_523(p, o, s) BOOST_PP_WHILE_523_C(BOOST_PP_BOOL(p(524, s)), p, o, s)
# define BOOST_PP_WHILE_524(p, o, s) BOOST_PP_WHILE_524_C(BOOST_PP_BOOL(p(525, s)), p, o, s)
# define BOOST_PP_WHILE_525(p, o, s) BOOST_PP_WHILE_525_C(BOOST_PP_BOOL(p(526, s)), p, o, s)
# define BOOST_PP_WHILE_526(p, o, s) BOOST_PP_WHILE_526_C(BOOST_PP_BOOL(p(527, s)), p, o, s)
# define BOOST_PP_WHILE_527(p, o, s) BOOST_PP_WHILE_527_C(BOOST_PP_BOOL(p(528, s)), p, o, s)
# define BOOST_PP_WHILE_528(p, o, s) BOOST_PP_WHILE_528_C(BOOST_PP_BOOL(p(529, s)), p, o, s)
# define BOOST_PP_WHILE_529(p, o, s) BOOST_PP_WHILE_529_C(BOOST_PP_BOOL(p(530, s)), p, o, s)
# define BOOST_PP_WHILE_530(p, o, s) BOOST_PP_WHILE_530_C(BOOST_PP_BOOL(p(531, s)), p, o, s)
# define BOOST_PP_WHILE_531(p, o, s) BOOST_PP_WHILE_531_C(BOOST_PP_BOOL(p(532, s)), p, o, s)
# define BOOST_PP_WHILE_532(p, o, s) BOOST_PP_WHILE_532_C(BOOST_PP_BOOL(p(533, s)), p, o, s)
# define BOOST_PP_WHILE_533(p, o, s) BOOST_PP_WHILE_533_C(BOOST_PP_BOOL(p(534, s)), p, o, s)
# define BOOST_PP_WHILE_534(p, o, s) BOOST_PP_WHILE_534_C(BOOST_PP_BOOL(p(535, s)), p, o, s)
# define BOOST_PP_WHILE_535(p, o, s) BOOST_PP_WHILE_535_C(BOOST_PP_BOOL(p(536, s)), p, o, s)
# define BOOST_PP_WHILE_536(p, o, s) BOOST_PP_WHILE_536_C(BOOST_PP_BOOL(p(537, s)), p, o, s)
# define BOOST_PP_WHILE_537(p, o, s) BOOST_PP_WHILE_537_C(BOOST_PP_BOOL(p(538, s)), p, o, s)
# define BOOST_PP_WHILE_538(p, o, s) BOOST_PP_WHILE_538_C(BOOST_PP_BOOL(p(539, s)), p, o, s)
# define BOOST_PP_WHILE_539(p, o, s) BOOST_PP_WHILE_539_C(BOOST_PP_BOOL(p(540, s)), p, o, s)
# define BOOST_PP_WHILE_540(p, o, s) BOOST_PP_WHILE_540_C(BOOST_PP_BOOL(p(541, s)), p, o, s)
# define BOOST_PP_WHILE_541(p, o, s) BOOST_PP_WHILE_541_C(BOOST_PP_BOOL(p(542, s)), p, o, s)
# define BOOST_PP_WHILE_542(p, o, s) BOOST_PP_WHILE_542_C(BOOST_PP_BOOL(p(543, s)), p, o, s)
# define BOOST_PP_WHILE_543(p, o, s) BOOST_PP_WHILE_543_C(BOOST_PP_BOOL(p(544, s)), p, o, s)
# define BOOST_PP_WHILE_544(p, o, s) BOOST_PP_WHILE_544_C(BOOST_PP_BOOL(p(545, s)), p, o, s)
# define BOOST_PP_WHILE_545(p, o, s) BOOST_PP_WHILE_545_C(BOOST_PP_BOOL(p(546, s)), p, o, s)
# define BOOST_PP_WHILE_546(p, o, s) BOOST_PP_WHILE_546_C(BOOST_PP_BOOL(p(547, s)), p, o, s)
# define BOOST_PP_WHILE_547(p, o, s) BOOST_PP_WHILE_547_C(BOOST_PP_BOOL(p(548, s)), p, o, s)
# define BOOST_PP_WHILE_548(p, o, s) BOOST_PP_WHILE_548_C(BOOST_PP_BOOL(p(549, s)), p, o, s)
# define BOOST_PP_WHILE_549(p, o, s) BOOST_PP_WHILE_549_C(BOOST_PP_BOOL(p(550, s)), p, o, s)
# define BOOST_PP_WHILE_550(p, o, s) BOOST_PP_WHILE_550_C(BOOST_PP_BOOL(p(551, s)), p, o, s)
# define BOOST_PP_WHILE_551(p, o, s) BOOST_PP_WHILE_551_C(BOOST_PP_BOOL(p(552, s)), p, o, s)
# define BOOST_PP_WHILE_552(p, o, s) BOOST_PP_WHILE_552_C(BOOST_PP_BOOL(p(553, s)), p, o, s)
# define BOOST_PP_WHILE_553(p, o, s) BOOST_PP_WHILE_553_C(BOOST_PP_BOOL(p(554, s)), p, o, s)
# define BOOST_PP_WHILE_554(p, o, s) BOOST_PP_WHILE_554_C(BOOST_PP_BOOL(p(555, s)), p, o, s)
# define BOOST_PP_WHILE_555(p, o, s) BOOST_PP_WHILE_555_C(BOOST_PP_BOOL(p(556, s)), p, o, s)
# define BOOST_PP_WHILE_556(p, o, s) BOOST_PP_WHILE_556_C(BOOST_PP_BOOL(p(557, s)), p, o, s)
# define BOOST_PP_WHILE_557(p, o, s) BOOST_PP_WHILE_557_C(BOOST_PP_BOOL(p(558, s)), p, o, s)
# define BOOST_PP_WHILE_558(p, o, s) BOOST_PP_WHILE_558_C(BOOST_PP_BOOL(p(559, s)), p, o, s)
# define BOOST_PP_WHILE_559(p, o, s) BOOST_PP_WHILE_559_C(BOOST_PP_BOOL(p(560, s)), p, o, s)
# define BOOST_PP_WHILE_560(p, o, s) BOOST_PP_WHILE_560_C(BOOST_PP_BOOL(p(561, s)), p, o, s)
# define BOOST_PP_WHILE_561(p, o, s) BOOST_PP_WHILE_561_C(BOOST_PP_BOOL(p(562, s)), p, o, s)
# define BOOST_PP_WHILE_562(p, o, s) BOOST_PP_WHILE_562_C(BOOST_PP_BOOL(p(563, s)), p, o, s)
# define BOOST_PP_WHILE_563(p, o, s) BOOST_PP_WHILE_563_C(BOOST_PP_BOOL(p(564, s)), p, o, s)
# define BOOST_PP_WHILE_564(p, o, s) BOOST_PP_WHILE_564_C(BOOST_PP_BOOL(p(565, s)), p, o, s)
# define BOOST_PP_WHILE_565(p, o, s) BOOST_PP_WHILE_565_C(BOOST_PP_BOOL(p(566, s)), p, o, s)
# define BOOST_PP_WHILE_566(p, o, s) BOOST_PP_WHILE_566_C(BOOST_PP_BOOL(p(567, s)), p, o, s)
# define BOOST_PP_WHILE_567(p, o, s) BOOST_PP_WHILE_567_C(BOOST_PP_BOOL(p(568, s)), p, o, s)
# define BOOST_PP_WHILE_568(p, o, s) BOOST_PP_WHILE_568_C(BOOST_PP_BOOL(p(569, s)), p, o, s)
# define BOOST_PP_WHILE_569(p, o, s) BOOST_PP_WHILE_569_C(BOOST_PP_BOOL(p(570, s)), p, o, s)
# define BOOST_PP_WHILE_570(p, o, s) BOOST_PP_WHILE_570_C(BOOST_PP_BOOL(p(571, s)), p, o, s)
# define BOOST_PP_WHILE_571(p, o, s) BOOST_PP_WHILE_571_C(BOOST_PP_BOOL(p(572, s)), p, o, s)
# define BOOST_PP_WHILE_572(p, o, s) BOOST_PP_WHILE_572_C(BOOST_PP_BOOL(p(573, s)), p, o, s)
# define BOOST_PP_WHILE_573(p, o, s) BOOST_PP_WHILE_573_C(BOOST_PP_BOOL(p(574, s)), p, o, s)
# define BOOST_PP_WHILE_574(p, o, s) BOOST_PP_WHILE_574_C(BOOST_PP_BOOL(p(575, s)), p, o, s)
# define BOOST_PP_WHILE_575(p, o, s) BOOST_PP_WHILE_575_C(BOOST_PP_BOOL(p(576, s)), p, o, s)
# define BOOST_PP_WHILE_576(p, o, s) BOOST_PP_WHILE_576_C(BOOST_PP_BOOL(p(577, s)), p, o, s)
# define BOOST_PP_WHILE_577(p, o, s) BOOST_PP_WHILE_577_C(BOOST_PP_BOOL(p(578, s)), p, o, s)
# define BOOST_PP_WHILE_578(p, o, s) BOOST_PP_WHILE_578_C(BOOST_PP_BOOL(p(579, s)), p, o, s)
# define BOOST_PP_WHILE_579(p, o, s) BOOST_PP_WHILE_579_C(BOOST_PP_BOOL(p(580, s)), p, o, s)
# define BOOST_PP_WHILE_580(p, o, s) BOOST_PP_WHILE_580_C(BOOST_PP_BOOL(p(581, s)), p, o, s)
# define BOOST_PP_WHILE_581(p, o, s) BOOST_PP_WHILE_581_C(BOOST_PP_BOOL(p(582, s)), p, o, s)
# define BOOST_PP_WHILE_582(p, o, s) BOOST_PP_WHILE_582_C(BOOST_PP_BOOL(p(583, s)), p, o, s)
# define BOOST_PP_WHILE_583(p, o, s) BOOST_PP_WHILE_583_C(BOOST_PP_BOOL(p(584, s)), p, o, s)
# define BOOST_PP_WHILE_584(p, o, s) BOOST_PP_WHILE_584_C(BOOST_PP_BOOL(p(585, s)), p, o, s)
# define BOOST_PP_WHILE_585(p, o, s) BOOST_PP_WHILE_585_C(BOOST_PP_BOOL(p(586, s)), p, o, s)
# define BOOST_PP_WHILE_586(p, o, s) BOOST_PP_WHILE_586_C(BOOST_PP_BOOL(p(587, s)), p, o, s)
# define BOOST_PP_WHILE_587(p, o, s) BOOST_PP_WHILE_587_C(BOOST_PP_BOOL(p(588, s)), p, o, s)
# define BOOST_PP_WHILE_588(p, o, s) BOOST_PP_WHILE_588_C(BOOST_PP_BOOL(p(589, s)), p, o, s)
# define BOOST_PP_WHILE_589(p, o, s) BOOST_PP_WHILE_589_C(BOOST_PP_BOOL(p(590, s)), p, o, s)
# define BOOST_PP_WHILE_590(p, o, s) BOOST_PP_WHILE_590_C(BOOST_PP_BOOL(p(591, s)), p, o, s)
# define BOOST_PP_WHILE_591(p, o, s) BOOST_PP_WHILE_591_C(BOOST_PP_BOOL(p(592, s)), p, o, s)
# define BOOST_PP_WHILE_592(p, o, s) BOOST_PP_WHILE_592_C(BOOST_PP_BOOL(p(593, s)), p, o, s)
# define BOOST_PP_WHILE_593(p, o, s) BOOST_PP_WHILE_593_C(BOOST_PP_BOOL(p(594, s)), p, o, s)
# define BOOST_PP_WHILE_594(p, o, s) BOOST_PP_WHILE_594_C(BOOST_PP_BOOL(p(595, s)), p, o, s)
# define BOOST_PP_WHILE_595(p, o, s) BOOST_PP_WHILE_595_C(BOOST_PP_BOOL(p(596, s)), p, o, s)
# define BOOST_PP_WHILE_596(p, o, s) BOOST_PP_WHILE_596_C(BOOST_PP_BOOL(p(597, s)), p, o, s)
# define BOOST_PP_WHILE_597(p, o, s) BOOST_PP_WHILE_597_C(BOOST_PP_BOOL(p(598, s)), p, o, s)
# define BOOST_PP_WHILE_598(p, o, s) BOOST_PP_WHILE_598_C(BOOST_PP_BOOL(p(599, s)), p, o, s)
# define BOOST_PP_WHILE_599(p, o, s) BOOST_PP_WHILE_599_C(BOOST_PP_BOOL(p(600, s)), p, o, s)
# define BOOST_PP_WHILE_600(p, o, s) BOOST_PP_WHILE_600_C(BOOST_PP_BOOL(p(601, s)), p, o, s)
# define BOOST_PP_WHILE_601(p, o, s) BOOST_PP_WHILE_601_C(BOOST_PP_BOOL(p(602, s)), p, o, s)
# define BOOST_PP_WHILE_602(p, o, s) BOOST_PP_WHILE_602_C(BOOST_PP_BOOL(p(603, s)), p, o, s)
# define BOOST_PP_WHILE_603(p, o, s) BOOST_PP_WHILE_603_C(BOOST_PP_BOOL(p(604, s)), p, o, s)
# define BOOST_PP_WHILE_604(p, o, s) BOOST_PP_WHILE_604_C(BOOST_PP_BOOL(p(605, s)), p, o, s)
# define BOOST_PP_WHILE_605(p, o, s) BOOST_PP_WHILE_605_C(BOOST_PP_BOOL(p(606, s)), p, o, s)
# define BOOST_PP_WHILE_606(p, o, s) BOOST_PP_WHILE_606_C(BOOST_PP_BOOL(p(607, s)), p, o, s)
# define BOOST_PP_WHILE_607(p, o, s) BOOST_PP_WHILE_607_C(BOOST_PP_BOOL(p(608, s)), p, o, s)
# define BOOST_PP_WHILE_608(p, o, s) BOOST_PP_WHILE_608_C(BOOST_PP_BOOL(p(609, s)), p, o, s)
# define BOOST_PP_WHILE_609(p, o, s) BOOST_PP_WHILE_609_C(BOOST_PP_BOOL(p(610, s)), p, o, s)
# define BOOST_PP_WHILE_610(p, o, s) BOOST_PP_WHILE_610_C(BOOST_PP_BOOL(p(611, s)), p, o, s)
# define BOOST_PP_WHILE_611(p, o, s) BOOST_PP_WHILE_611_C(BOOST_PP_BOOL(p(612, s)), p, o, s)
# define BOOST_PP_WHILE_612(p, o, s) BOOST_PP_WHILE_612_C(BOOST_PP_BOOL(p(613, s)), p, o, s)
# define BOOST_PP_WHILE_613(p, o, s) BOOST_PP_WHILE_613_C(BOOST_PP_BOOL(p(614, s)), p, o, s)
# define BOOST_PP_WHILE_614(p, o, s) BOOST_PP_WHILE_614_C(BOOST_PP_BOOL(p(615, s)), p, o, s)
# define BOOST_PP_WHILE_615(p, o, s) BOOST_PP_WHILE_615_C(BOOST_PP_BOOL(p(616, s)), p, o, s)
# define BOOST_PP_WHILE_616(p, o, s) BOOST_PP_WHILE_616_C(BOOST_PP_BOOL(p(617, s)), p, o, s)
# define BOOST_PP_WHILE_617(p, o, s) BOOST_PP_WHILE_617_C(BOOST_PP_BOOL(p(618, s)), p, o, s)
# define BOOST_PP_WHILE_618(p, o, s) BOOST_PP_WHILE_618_C(BOOST_PP_BOOL(p(619, s)), p, o, s)
# define BOOST_PP_WHILE_619(p, o, s) BOOST_PP_WHILE_619_C(BOOST_PP_BOOL(p(620, s)), p, o, s)
# define BOOST_PP_WHILE_620(p, o, s) BOOST_PP_WHILE_620_C(BOOST_PP_BOOL(p(621, s)), p, o, s)
# define BOOST_PP_WHILE_621(p, o, s) BOOST_PP_WHILE_621_C(BOOST_PP_BOOL(p(622, s)), p, o, s)
# define BOOST_PP_WHILE_622(p, o, s) BOOST_PP_WHILE_622_C(BOOST_PP_BOOL(p(623, s)), p, o, s)
# define BOOST_PP_WHILE_623(p, o, s) BOOST_PP_WHILE_623_C(BOOST_PP_BOOL(p(624, s)), p, o, s)
# define BOOST_PP_WHILE_624(p, o, s) BOOST_PP_WHILE_624_C(BOOST_PP_BOOL(p(625, s)), p, o, s)
# define BOOST_PP_WHILE_625(p, o, s) BOOST_PP_WHILE_625_C(BOOST_PP_BOOL(p(626, s)), p, o, s)
# define BOOST_PP_WHILE_626(p, o, s) BOOST_PP_WHILE_626_C(BOOST_PP_BOOL(p(627, s)), p, o, s)
# define BOOST_PP_WHILE_627(p, o, s) BOOST_PP_WHILE_627_C(BOOST_PP_BOOL(p(628, s)), p, o, s)
# define BOOST_PP_WHILE_628(p, o, s) BOOST_PP_WHILE_628_C(BOOST_PP_BOOL(p(629, s)), p, o, s)
# define BOOST_PP_WHILE_629(p, o, s) BOOST_PP_WHILE_629_C(BOOST_PP_BOOL(p(630, s)), p, o, s)
# define BOOST_PP_WHILE_630(p, o, s) BOOST_PP_WHILE_630_C(BOOST_PP_BOOL(p(631, s)), p, o, s)
# define BOOST_PP_WHILE_631(p, o, s) BOOST_PP_WHILE_631_C(BOOST_PP_BOOL(p(632, s)), p, o, s)
# define BOOST_PP_WHILE_632(p, o, s) BOOST_PP_WHILE_632_C(BOOST_PP_BOOL(p(633, s)), p, o, s)
# define BOOST_PP_WHILE_633(p, o, s) BOOST_PP_WHILE_633_C(BOOST_PP_BOOL(p(634, s)), p, o, s)
# define BOOST_PP_WHILE_634(p, o, s) BOOST_PP_WHILE_634_C(BOOST_PP_BOOL(p(635, s)), p, o, s)
# define BOOST_PP_WHILE_635(p, o, s) BOOST_PP_WHILE_635_C(BOOST_PP_BOOL(p(636, s)), p, o, s)
# define BOOST_PP_WHILE_636(p, o, s) BOOST_PP_WHILE_636_C(BOOST_PP_BOOL(p(637, s)), p, o, s)
# define BOOST_PP_WHILE_637(p, o, s) BOOST_PP_WHILE_637_C(BOOST_PP_BOOL(p(638, s)), p, o, s)
# define BOOST_PP_WHILE_638(p, o, s) BOOST_PP_WHILE_638_C(BOOST_PP_BOOL(p(639, s)), p, o, s)
# define BOOST_PP_WHILE_639(p, o, s) BOOST_PP_WHILE_639_C(BOOST_PP_BOOL(p(640, s)), p, o, s)
# define BOOST_PP_WHILE_640(p, o, s) BOOST_PP_WHILE_640_C(BOOST_PP_BOOL(p(641, s)), p, o, s)
# define BOOST_PP_WHILE_641(p, o, s) BOOST_PP_WHILE_641_C(BOOST_PP_BOOL(p(642, s)), p, o, s)
# define BOOST_PP_WHILE_642(p, o, s) BOOST_PP_WHILE_642_C(BOOST_PP_BOOL(p(643, s)), p, o, s)
# define BOOST_PP_WHILE_643(p, o, s) BOOST_PP_WHILE_643_C(BOOST_PP_BOOL(p(644, s)), p, o, s)
# define BOOST_PP_WHILE_644(p, o, s) BOOST_PP_WHILE_644_C(BOOST_PP_BOOL(p(645, s)), p, o, s)
# define BOOST_PP_WHILE_645(p, o, s) BOOST_PP_WHILE_645_C(BOOST_PP_BOOL(p(646, s)), p, o, s)
# define BOOST_PP_WHILE_646(p, o, s) BOOST_PP_WHILE_646_C(BOOST_PP_BOOL(p(647, s)), p, o, s)
# define BOOST_PP_WHILE_647(p, o, s) BOOST_PP_WHILE_647_C(BOOST_PP_BOOL(p(648, s)), p, o, s)
# define BOOST_PP_WHILE_648(p, o, s) BOOST_PP_WHILE_648_C(BOOST_PP_BOOL(p(649, s)), p, o, s)
# define BOOST_PP_WHILE_649(p, o, s) BOOST_PP_WHILE_649_C(BOOST_PP_BOOL(p(650, s)), p, o, s)
# define BOOST_PP_WHILE_650(p, o, s) BOOST_PP_WHILE_650_C(BOOST_PP_BOOL(p(651, s)), p, o, s)
# define BOOST_PP_WHILE_651(p, o, s) BOOST_PP_WHILE_651_C(BOOST_PP_BOOL(p(652, s)), p, o, s)
# define BOOST_PP_WHILE_652(p, o, s) BOOST_PP_WHILE_652_C(BOOST_PP_BOOL(p(653, s)), p, o, s)
# define BOOST_PP_WHILE_653(p, o, s) BOOST_PP_WHILE_653_C(BOOST_PP_BOOL(p(654, s)), p, o, s)
# define BOOST_PP_WHILE_654(p, o, s) BOOST_PP_WHILE_654_C(BOOST_PP_BOOL(p(655, s)), p, o, s)
# define BOOST_PP_WHILE_655(p, o, s) BOOST_PP_WHILE_655_C(BOOST_PP_BOOL(p(656, s)), p, o, s)
# define BOOST_PP_WHILE_656(p, o, s) BOOST_PP_WHILE_656_C(BOOST_PP_BOOL(p(657, s)), p, o, s)
# define BOOST_PP_WHILE_657(p, o, s) BOOST_PP_WHILE_657_C(BOOST_PP_BOOL(p(658, s)), p, o, s)
# define BOOST_PP_WHILE_658(p, o, s) BOOST_PP_WHILE_658_C(BOOST_PP_BOOL(p(659, s)), p, o, s)
# define BOOST_PP_WHILE_659(p, o, s) BOOST_PP_WHILE_659_C(BOOST_PP_BOOL(p(660, s)), p, o, s)
# define BOOST_PP_WHILE_660(p, o, s) BOOST_PP_WHILE_660_C(BOOST_PP_BOOL(p(661, s)), p, o, s)
# define BOOST_PP_WHILE_661(p, o, s) BOOST_PP_WHILE_661_C(BOOST_PP_BOOL(p(662, s)), p, o, s)
# define BOOST_PP_WHILE_662(p, o, s) BOOST_PP_WHILE_662_C(BOOST_PP_BOOL(p(663, s)), p, o, s)
# define BOOST_PP_WHILE_663(p, o, s) BOOST_PP_WHILE_663_C(BOOST_PP_BOOL(p(664, s)), p, o, s)
# define BOOST_PP_WHILE_664(p, o, s) BOOST_PP_WHILE_664_C(BOOST_PP_BOOL(p(665, s)), p, o, s)
# define BOOST_PP_WHILE_665(p, o, s) BOOST_PP_WHILE_665_C(BOOST_PP_BOOL(p(666, s)), p, o, s)
# define BOOST_PP_WHILE_666(p, o, s) BOOST_PP_WHILE_666_C(BOOST_PP_BOOL(p(667, s)), p, o, s)
# define BOOST_PP_WHILE_667(p, o, s) BOOST_PP_WHILE_667_C(BOOST_PP_BOOL(p(668, s)), p, o, s)
# define BOOST_PP_WHILE_668(p, o, s) BOOST_PP_WHILE_668_C(BOOST_PP_BOOL(p(669, s)), p, o, s)
# define BOOST_PP_WHILE_669(p, o, s) BOOST_PP_WHILE_669_C(BOOST_PP_BOOL(p(670, s)), p, o, s)
# define BOOST_PP_WHILE_670(p, o, s) BOOST_PP_WHILE_670_C(BOOST_PP_BOOL(p(671, s)), p, o, s)
# define BOOST_PP_WHILE_671(p, o, s) BOOST_PP_WHILE_671_C(BOOST_PP_BOOL(p(672, s)), p, o, s)
# define BOOST_PP_WHILE_672(p, o, s) BOOST_PP_WHILE_672_C(BOOST_PP_BOOL(p(673, s)), p, o, s)
# define BOOST_PP_WHILE_673(p, o, s) BOOST_PP_WHILE_673_C(BOOST_PP_BOOL(p(674, s)), p, o, s)
# define BOOST_PP_WHILE_674(p, o, s) BOOST_PP_WHILE_674_C(BOOST_PP_BOOL(p(675, s)), p, o, s)
# define BOOST_PP_WHILE_675(p, o, s) BOOST_PP_WHILE_675_C(BOOST_PP_BOOL(p(676, s)), p, o, s)
# define BOOST_PP_WHILE_676(p, o, s) BOOST_PP_WHILE_676_C(BOOST_PP_BOOL(p(677, s)), p, o, s)
# define BOOST_PP_WHILE_677(p, o, s) BOOST_PP_WHILE_677_C(BOOST_PP_BOOL(p(678, s)), p, o, s)
# define BOOST_PP_WHILE_678(p, o, s) BOOST_PP_WHILE_678_C(BOOST_PP_BOOL(p(679, s)), p, o, s)
# define BOOST_PP_WHILE_679(p, o, s) BOOST_PP_WHILE_679_C(BOOST_PP_BOOL(p(680, s)), p, o, s)
# define BOOST_PP_WHILE_680(p, o, s) BOOST_PP_WHILE_680_C(BOOST_PP_BOOL(p(681, s)), p, o, s)
# define BOOST_PP_WHILE_681(p, o, s) BOOST_PP_WHILE_681_C(BOOST_PP_BOOL(p(682, s)), p, o, s)
# define BOOST_PP_WHILE_682(p, o, s) BOOST_PP_WHILE_682_C(BOOST_PP_BOOL(p(683, s)), p, o, s)
# define BOOST_PP_WHILE_683(p, o, s) BOOST_PP_WHILE_683_C(BOOST_PP_BOOL(p(684, s)), p, o, s)
# define BOOST_PP_WHILE_684(p, o, s) BOOST_PP_WHILE_684_C(BOOST_PP_BOOL(p(685, s)), p, o, s)
# define BOOST_PP_WHILE_685(p, o, s) BOOST_PP_WHILE_685_C(BOOST_PP_BOOL(p(686, s)), p, o, s)
# define BOOST_PP_WHILE_686(p, o, s) BOOST_PP_WHILE_686_C(BOOST_PP_BOOL(p(687, s)), p, o, s)
# define BOOST_PP_WHILE_687(p, o, s) BOOST_PP_WHILE_687_C(BOOST_PP_BOOL(p(688, s)), p, o, s)
# define BOOST_PP_WHILE_688(p, o, s) BOOST_PP_WHILE_688_C(BOOST_PP_BOOL(p(689, s)), p, o, s)
# define BOOST_PP_WHILE_689(p, o, s) BOOST_PP_WHILE_689_C(BOOST_PP_BOOL(p(690, s)), p, o, s)
# define BOOST_PP_WHILE_690(p, o, s) BOOST_PP_WHILE_690_C(BOOST_PP_BOOL(p(691, s)), p, o, s)
# define BOOST_PP_WHILE_691(p, o, s) BOOST_PP_WHILE_691_C(BOOST_PP_BOOL(p(692, s)), p, o, s)
# define BOOST_PP_WHILE_692(p, o, s) BOOST_PP_WHILE_692_C(BOOST_PP_BOOL(p(693, s)), p, o, s)
# define BOOST_PP_WHILE_693(p, o, s) BOOST_PP_WHILE_693_C(BOOST_PP_BOOL(p(694, s)), p, o, s)
# define BOOST_PP_WHILE_694(p, o, s) BOOST_PP_WHILE_694_C(BOOST_PP_BOOL(p(695, s)), p, o, s)
# define BOOST_PP_WHILE_695(p, o, s) BOOST_PP_WHILE_695_C(BOOST_PP_BOOL(p(696, s)), p, o, s)
# define BOOST_PP_WHILE_696(p, o, s) BOOST_PP_WHILE_696_C(BOOST_PP_BOOL(p(697, s)), p, o, s)
# define BOOST_PP_WHILE_697(p, o, s) BOOST_PP_WHILE_697_C(BOOST_PP_BOOL(p(698, s)), p, o, s)
# define BOOST_PP_WHILE_698(p, o, s) BOOST_PP_WHILE_698_C(BOOST_PP_BOOL(p(699, s)), p, o, s)
# define BOOST_PP_WHILE_699(p, o, s) BOOST_PP_WHILE_699_C(BOOST_PP_BOOL(p(700, s)), p, o, s)
# define BOOST_PP_WHILE_700(p, o, s) BOOST_PP_WHILE_700_C(BOOST_PP_BOOL(p(701, s)), p, o, s)
# define BOOST_PP_WHILE_701(p, o, s) BOOST_PP_WHILE_701_C(BOOST_PP_BOOL(p(702, s)), p, o, s)
# define BOOST_PP_WHILE_702(p, o, s) BOOST_PP_WHILE_702_C(BOOST_PP_BOOL(p(703, s)), p, o, s)
# define BOOST_PP_WHILE_703(p, o, s) BOOST_PP_WHILE_703_C(BOOST_PP_BOOL(p(704, s)), p, o, s)
# define BOOST_PP_WHILE_704(p, o, s) BOOST_PP_WHILE_704_C(BOOST_PP_BOOL(p(705, s)), p, o, s)
# define BOOST_PP_WHILE_705(p, o, s) BOOST_PP_WHILE_705_C(BOOST_PP_BOOL(p(706, s)), p, o, s)
# define BOOST_PP_WHILE_706(p, o, s) BOOST_PP_WHILE_706_C(BOOST_PP_BOOL(p(707, s)), p, o, s)
# define BOOST_PP_WHILE_707(p, o, s) BOOST_PP_WHILE_707_C(BOOST_PP_BOOL(p(708, s)), p, o, s)
# define BOOST_PP_WHILE_708(p, o, s) BOOST_PP_WHILE_708_C(BOOST_PP_BOOL(p(709, s)), p, o, s)
# define BOOST_PP_WHILE_709(p, o, s) BOOST_PP_WHILE_709_C(BOOST_PP_BOOL(p(710, s)), p, o, s)
# define BOOST_PP_WHILE_710(p, o, s) BOOST_PP_WHILE_710_C(BOOST_PP_BOOL(p(711, s)), p, o, s)
# define BOOST_PP_WHILE_711(p, o, s) BOOST_PP_WHILE_711_C(BOOST_PP_BOOL(p(712, s)), p, o, s)
# define BOOST_PP_WHILE_712(p, o, s) BOOST_PP_WHILE_712_C(BOOST_PP_BOOL(p(713, s)), p, o, s)
# define BOOST_PP_WHILE_713(p, o, s) BOOST_PP_WHILE_713_C(BOOST_PP_BOOL(p(714, s)), p, o, s)
# define BOOST_PP_WHILE_714(p, o, s) BOOST_PP_WHILE_714_C(BOOST_PP_BOOL(p(715, s)), p, o, s)
# define BOOST_PP_WHILE_715(p, o, s) BOOST_PP_WHILE_715_C(BOOST_PP_BOOL(p(716, s)), p, o, s)
# define BOOST_PP_WHILE_716(p, o, s) BOOST_PP_WHILE_716_C(BOOST_PP_BOOL(p(717, s)), p, o, s)
# define BOOST_PP_WHILE_717(p, o, s) BOOST_PP_WHILE_717_C(BOOST_PP_BOOL(p(718, s)), p, o, s)
# define BOOST_PP_WHILE_718(p, o, s) BOOST_PP_WHILE_718_C(BOOST_PP_BOOL(p(719, s)), p, o, s)
# define BOOST_PP_WHILE_719(p, o, s) BOOST_PP_WHILE_719_C(BOOST_PP_BOOL(p(720, s)), p, o, s)
# define BOOST_PP_WHILE_720(p, o, s) BOOST_PP_WHILE_720_C(BOOST_PP_BOOL(p(721, s)), p, o, s)
# define BOOST_PP_WHILE_721(p, o, s) BOOST_PP_WHILE_721_C(BOOST_PP_BOOL(p(722, s)), p, o, s)
# define BOOST_PP_WHILE_722(p, o, s) BOOST_PP_WHILE_722_C(BOOST_PP_BOOL(p(723, s)), p, o, s)
# define BOOST_PP_WHILE_723(p, o, s) BOOST_PP_WHILE_723_C(BOOST_PP_BOOL(p(724, s)), p, o, s)
# define BOOST_PP_WHILE_724(p, o, s) BOOST_PP_WHILE_724_C(BOOST_PP_BOOL(p(725, s)), p, o, s)
# define BOOST_PP_WHILE_725(p, o, s) BOOST_PP_WHILE_725_C(BOOST_PP_BOOL(p(726, s)), p, o, s)
# define BOOST_PP_WHILE_726(p, o, s) BOOST_PP_WHILE_726_C(BOOST_PP_BOOL(p(727, s)), p, o, s)
# define BOOST_PP_WHILE_727(p, o, s) BOOST_PP_WHILE_727_C(BOOST_PP_BOOL(p(728, s)), p, o, s)
# define BOOST_PP_WHILE_728(p, o, s) BOOST_PP_WHILE_728_C(BOOST_PP_BOOL(p(729, s)), p, o, s)
# define BOOST_PP_WHILE_729(p, o, s) BOOST_PP_WHILE_729_C(BOOST_PP_BOOL(p(730, s)), p, o, s)
# define BOOST_PP_WHILE_730(p, o, s) BOOST_PP_WHILE_730_C(BOOST_PP_BOOL(p(731, s)), p, o, s)
# define BOOST_PP_WHILE_731(p, o, s) BOOST_PP_WHILE_731_C(BOOST_PP_BOOL(p(732, s)), p, o, s)
# define BOOST_PP_WHILE_732(p, o, s) BOOST_PP_WHILE_732_C(BOOST_PP_BOOL(p(733, s)), p, o, s)
# define BOOST_PP_WHILE_733(p, o, s) BOOST_PP_WHILE_733_C(BOOST_PP_BOOL(p(734, s)), p, o, s)
# define BOOST_PP_WHILE_734(p, o, s) BOOST_PP_WHILE_734_C(BOOST_PP_BOOL(p(735, s)), p, o, s)
# define BOOST_PP_WHILE_735(p, o, s) BOOST_PP_WHILE_735_C(BOOST_PP_BOOL(p(736, s)), p, o, s)
# define BOOST_PP_WHILE_736(p, o, s) BOOST_PP_WHILE_736_C(BOOST_PP_BOOL(p(737, s)), p, o, s)
# define BOOST_PP_WHILE_737(p, o, s) BOOST_PP_WHILE_737_C(BOOST_PP_BOOL(p(738, s)), p, o, s)
# define BOOST_PP_WHILE_738(p, o, s) BOOST_PP_WHILE_738_C(BOOST_PP_BOOL(p(739, s)), p, o, s)
# define BOOST_PP_WHILE_739(p, o, s) BOOST_PP_WHILE_739_C(BOOST_PP_BOOL(p(740, s)), p, o, s)
# define BOOST_PP_WHILE_740(p, o, s) BOOST_PP_WHILE_740_C(BOOST_PP_BOOL(p(741, s)), p, o, s)
# define BOOST_PP_WHILE_741(p, o, s) BOOST_PP_WHILE_741_C(BOOST_PP_BOOL(p(742, s)), p, o, s)
# define BOOST_PP_WHILE_742(p, o, s) BOOST_PP_WHILE_742_C(BOOST_PP_BOOL(p(743, s)), p, o, s)
# define BOOST_PP_WHILE_743(p, o, s) BOOST_PP_WHILE_743_C(BOOST_PP_BOOL(p(744, s)), p, o, s)
# define BOOST_PP_WHILE_744(p, o, s) BOOST_PP_WHILE_744_C(BOOST_PP_BOOL(p(745, s)), p, o, s)
# define BOOST_PP_WHILE_745(p, o, s) BOOST_PP_WHILE_745_C(BOOST_PP_BOOL(p(746, s)), p, o, s)
# define BOOST_PP_WHILE_746(p, o, s) BOOST_PP_WHILE_746_C(BOOST_PP_BOOL(p(747, s)), p, o, s)
# define BOOST_PP_WHILE_747(p, o, s) BOOST_PP_WHILE_747_C(BOOST_PP_BOOL(p(748, s)), p, o, s)
# define BOOST_PP_WHILE_748(p, o, s) BOOST_PP_WHILE_748_C(BOOST_PP_BOOL(p(749, s)), p, o, s)
# define BOOST_PP_WHILE_749(p, o, s) BOOST_PP_WHILE_749_C(BOOST_PP_BOOL(p(750, s)), p, o, s)
# define BOOST_PP_WHILE_750(p, o, s) BOOST_PP_WHILE_750_C(BOOST_PP_BOOL(p(751, s)), p, o, s)
# define BOOST_PP_WHILE_751(p, o, s) BOOST_PP_WHILE_751_C(BOOST_PP_BOOL(p(752, s)), p, o, s)
# define BOOST_PP_WHILE_752(p, o, s) BOOST_PP_WHILE_752_C(BOOST_PP_BOOL(p(753, s)), p, o, s)
# define BOOST_PP_WHILE_753(p, o, s) BOOST_PP_WHILE_753_C(BOOST_PP_BOOL(p(754, s)), p, o, s)
# define BOOST_PP_WHILE_754(p, o, s) BOOST_PP_WHILE_754_C(BOOST_PP_BOOL(p(755, s)), p, o, s)
# define BOOST_PP_WHILE_755(p, o, s) BOOST_PP_WHILE_755_C(BOOST_PP_BOOL(p(756, s)), p, o, s)
# define BOOST_PP_WHILE_756(p, o, s) BOOST_PP_WHILE_756_C(BOOST_PP_BOOL(p(757, s)), p, o, s)
# define BOOST_PP_WHILE_757(p, o, s) BOOST_PP_WHILE_757_C(BOOST_PP_BOOL(p(758, s)), p, o, s)
# define BOOST_PP_WHILE_758(p, o, s) BOOST_PP_WHILE_758_C(BOOST_PP_BOOL(p(759, s)), p, o, s)
# define BOOST_PP_WHILE_759(p, o, s) BOOST_PP_WHILE_759_C(BOOST_PP_BOOL(p(760, s)), p, o, s)
# define BOOST_PP_WHILE_760(p, o, s) BOOST_PP_WHILE_760_C(BOOST_PP_BOOL(p(761, s)), p, o, s)
# define BOOST_PP_WHILE_761(p, o, s) BOOST_PP_WHILE_761_C(BOOST_PP_BOOL(p(762, s)), p, o, s)
# define BOOST_PP_WHILE_762(p, o, s) BOOST_PP_WHILE_762_C(BOOST_PP_BOOL(p(763, s)), p, o, s)
# define BOOST_PP_WHILE_763(p, o, s) BOOST_PP_WHILE_763_C(BOOST_PP_BOOL(p(764, s)), p, o, s)
# define BOOST_PP_WHILE_764(p, o, s) BOOST_PP_WHILE_764_C(BOOST_PP_BOOL(p(765, s)), p, o, s)
# define BOOST_PP_WHILE_765(p, o, s) BOOST_PP_WHILE_765_C(BOOST_PP_BOOL(p(766, s)), p, o, s)
# define BOOST_PP_WHILE_766(p, o, s) BOOST_PP_WHILE_766_C(BOOST_PP_BOOL(p(767, s)), p, o, s)
# define BOOST_PP_WHILE_767(p, o, s) BOOST_PP_WHILE_767_C(BOOST_PP_BOOL(p(768, s)), p, o, s)
# define BOOST_PP_WHILE_768(p, o, s) BOOST_PP_WHILE_768_C(BOOST_PP_BOOL(p(769, s)), p, o, s)
# define BOOST_PP_WHILE_769(p, o, s) BOOST_PP_WHILE_769_C(BOOST_PP_BOOL(p(770, s)), p, o, s)
# define BOOST_PP_WHILE_770(p, o, s) BOOST_PP_WHILE_770_C(BOOST_PP_BOOL(p(771, s)), p, o, s)
# define BOOST_PP_WHILE_771(p, o, s) BOOST_PP_WHILE_771_C(BOOST_PP_BOOL(p(772, s)), p, o, s)
# define BOOST_PP_WHILE_772(p, o, s) BOOST_PP_WHILE_772_C(BOOST_PP_BOOL(p(773, s)), p, o, s)
# define BOOST_PP_WHILE_773(p, o, s) BOOST_PP_WHILE_773_C(BOOST_PP_BOOL(p(774, s)), p, o, s)
# define BOOST_PP_WHILE_774(p, o, s) BOOST_PP_WHILE_774_C(BOOST_PP_BOOL(p(775, s)), p, o, s)
# define BOOST_PP_WHILE_775(p, o, s) BOOST_PP_WHILE_775_C(BOOST_PP_BOOL(p(776, s)), p, o, s)
# define BOOST_PP_WHILE_776(p, o, s) BOOST_PP_WHILE_776_C(BOOST_PP_BOOL(p(777, s)), p, o, s)
# define BOOST_PP_WHILE_777(p, o, s) BOOST_PP_WHILE_777_C(BOOST_PP_BOOL(p(778, s)), p, o, s)
# define BOOST_PP_WHILE_778(p, o, s) BOOST_PP_WHILE_778_C(BOOST_PP_BOOL(p(779, s)), p, o, s)
# define BOOST_PP_WHILE_779(p, o, s) BOOST_PP_WHILE_779_C(BOOST_PP_BOOL(p(780, s)), p, o, s)
# define BOOST_PP_WHILE_780(p, o, s) BOOST_PP_WHILE_780_C(BOOST_PP_BOOL(p(781, s)), p, o, s)
# define BOOST_PP_WHILE_781(p, o, s) BOOST_PP_WHILE_781_C(BOOST_PP_BOOL(p(782, s)), p, o, s)
# define BOOST_PP_WHILE_782(p, o, s) BOOST_PP_WHILE_782_C(BOOST_PP_BOOL(p(783, s)), p, o, s)
# define BOOST_PP_WHILE_783(p, o, s) BOOST_PP_WHILE_783_C(BOOST_PP_BOOL(p(784, s)), p, o, s)
# define BOOST_PP_WHILE_784(p, o, s) BOOST_PP_WHILE_784_C(BOOST_PP_BOOL(p(785, s)), p, o, s)
# define BOOST_PP_WHILE_785(p, o, s) BOOST_PP_WHILE_785_C(BOOST_PP_BOOL(p(786, s)), p, o, s)
# define BOOST_PP_WHILE_786(p, o, s) BOOST_PP_WHILE_786_C(BOOST_PP_BOOL(p(787, s)), p, o, s)
# define BOOST_PP_WHILE_787(p, o, s) BOOST_PP_WHILE_787_C(BOOST_PP_BOOL(p(788, s)), p, o, s)
# define BOOST_PP_WHILE_788(p, o, s) BOOST_PP_WHILE_788_C(BOOST_PP_BOOL(p(789, s)), p, o, s)
# define BOOST_PP_WHILE_789(p, o, s) BOOST_PP_WHILE_789_C(BOOST_PP_BOOL(p(790, s)), p, o, s)
# define BOOST_PP_WHILE_790(p, o, s) BOOST_PP_WHILE_790_C(BOOST_PP_BOOL(p(791, s)), p, o, s)
# define BOOST_PP_WHILE_791(p, o, s) BOOST_PP_WHILE_791_C(BOOST_PP_BOOL(p(792, s)), p, o, s)
# define BOOST_PP_WHILE_792(p, o, s) BOOST_PP_WHILE_792_C(BOOST_PP_BOOL(p(793, s)), p, o, s)
# define BOOST_PP_WHILE_793(p, o, s) BOOST_PP_WHILE_793_C(BOOST_PP_BOOL(p(794, s)), p, o, s)
# define BOOST_PP_WHILE_794(p, o, s) BOOST_PP_WHILE_794_C(BOOST_PP_BOOL(p(795, s)), p, o, s)
# define BOOST_PP_WHILE_795(p, o, s) BOOST_PP_WHILE_795_C(BOOST_PP_BOOL(p(796, s)), p, o, s)
# define BOOST_PP_WHILE_796(p, o, s) BOOST_PP_WHILE_796_C(BOOST_PP_BOOL(p(797, s)), p, o, s)
# define BOOST_PP_WHILE_797(p, o, s) BOOST_PP_WHILE_797_C(BOOST_PP_BOOL(p(798, s)), p, o, s)
# define BOOST_PP_WHILE_798(p, o, s) BOOST_PP_WHILE_798_C(BOOST_PP_BOOL(p(799, s)), p, o, s)
# define BOOST_PP_WHILE_799(p, o, s) BOOST_PP_WHILE_799_C(BOOST_PP_BOOL(p(800, s)), p, o, s)
# define BOOST_PP_WHILE_800(p, o, s) BOOST_PP_WHILE_800_C(BOOST_PP_BOOL(p(801, s)), p, o, s)
# define BOOST_PP_WHILE_801(p, o, s) BOOST_PP_WHILE_801_C(BOOST_PP_BOOL(p(802, s)), p, o, s)
# define BOOST_PP_WHILE_802(p, o, s) BOOST_PP_WHILE_802_C(BOOST_PP_BOOL(p(803, s)), p, o, s)
# define BOOST_PP_WHILE_803(p, o, s) BOOST_PP_WHILE_803_C(BOOST_PP_BOOL(p(804, s)), p, o, s)
# define BOOST_PP_WHILE_804(p, o, s) BOOST_PP_WHILE_804_C(BOOST_PP_BOOL(p(805, s)), p, o, s)
# define BOOST_PP_WHILE_805(p, o, s) BOOST_PP_WHILE_805_C(BOOST_PP_BOOL(p(806, s)), p, o, s)
# define BOOST_PP_WHILE_806(p, o, s) BOOST_PP_WHILE_806_C(BOOST_PP_BOOL(p(807, s)), p, o, s)
# define BOOST_PP_WHILE_807(p, o, s) BOOST_PP_WHILE_807_C(BOOST_PP_BOOL(p(808, s)), p, o, s)
# define BOOST_PP_WHILE_808(p, o, s) BOOST_PP_WHILE_808_C(BOOST_PP_BOOL(p(809, s)), p, o, s)
# define BOOST_PP_WHILE_809(p, o, s) BOOST_PP_WHILE_809_C(BOOST_PP_BOOL(p(810, s)), p, o, s)
# define BOOST_PP_WHILE_810(p, o, s) BOOST_PP_WHILE_810_C(BOOST_PP_BOOL(p(811, s)), p, o, s)
# define BOOST_PP_WHILE_811(p, o, s) BOOST_PP_WHILE_811_C(BOOST_PP_BOOL(p(812, s)), p, o, s)
# define BOOST_PP_WHILE_812(p, o, s) BOOST_PP_WHILE_812_C(BOOST_PP_BOOL(p(813, s)), p, o, s)
# define BOOST_PP_WHILE_813(p, o, s) BOOST_PP_WHILE_813_C(BOOST_PP_BOOL(p(814, s)), p, o, s)
# define BOOST_PP_WHILE_814(p, o, s) BOOST_PP_WHILE_814_C(BOOST_PP_BOOL(p(815, s)), p, o, s)
# define BOOST_PP_WHILE_815(p, o, s) BOOST_PP_WHILE_815_C(BOOST_PP_BOOL(p(816, s)), p, o, s)
# define BOOST_PP_WHILE_816(p, o, s) BOOST_PP_WHILE_816_C(BOOST_PP_BOOL(p(817, s)), p, o, s)
# define BOOST_PP_WHILE_817(p, o, s) BOOST_PP_WHILE_817_C(BOOST_PP_BOOL(p(818, s)), p, o, s)
# define BOOST_PP_WHILE_818(p, o, s) BOOST_PP_WHILE_818_C(BOOST_PP_BOOL(p(819, s)), p, o, s)
# define BOOST_PP_WHILE_819(p, o, s) BOOST_PP_WHILE_819_C(BOOST_PP_BOOL(p(820, s)), p, o, s)
# define BOOST_PP_WHILE_820(p, o, s) BOOST_PP_WHILE_820_C(BOOST_PP_BOOL(p(821, s)), p, o, s)
# define BOOST_PP_WHILE_821(p, o, s) BOOST_PP_WHILE_821_C(BOOST_PP_BOOL(p(822, s)), p, o, s)
# define BOOST_PP_WHILE_822(p, o, s) BOOST_PP_WHILE_822_C(BOOST_PP_BOOL(p(823, s)), p, o, s)
# define BOOST_PP_WHILE_823(p, o, s) BOOST_PP_WHILE_823_C(BOOST_PP_BOOL(p(824, s)), p, o, s)
# define BOOST_PP_WHILE_824(p, o, s) BOOST_PP_WHILE_824_C(BOOST_PP_BOOL(p(825, s)), p, o, s)
# define BOOST_PP_WHILE_825(p, o, s) BOOST_PP_WHILE_825_C(BOOST_PP_BOOL(p(826, s)), p, o, s)
# define BOOST_PP_WHILE_826(p, o, s) BOOST_PP_WHILE_826_C(BOOST_PP_BOOL(p(827, s)), p, o, s)
# define BOOST_PP_WHILE_827(p, o, s) BOOST_PP_WHILE_827_C(BOOST_PP_BOOL(p(828, s)), p, o, s)
# define BOOST_PP_WHILE_828(p, o, s) BOOST_PP_WHILE_828_C(BOOST_PP_BOOL(p(829, s)), p, o, s)
# define BOOST_PP_WHILE_829(p, o, s) BOOST_PP_WHILE_829_C(BOOST_PP_BOOL(p(830, s)), p, o, s)
# define BOOST_PP_WHILE_830(p, o, s) BOOST_PP_WHILE_830_C(BOOST_PP_BOOL(p(831, s)), p, o, s)
# define BOOST_PP_WHILE_831(p, o, s) BOOST_PP_WHILE_831_C(BOOST_PP_BOOL(p(832, s)), p, o, s)
# define BOOST_PP_WHILE_832(p, o, s) BOOST_PP_WHILE_832_C(BOOST_PP_BOOL(p(833, s)), p, o, s)
# define BOOST_PP_WHILE_833(p, o, s) BOOST_PP_WHILE_833_C(BOOST_PP_BOOL(p(834, s)), p, o, s)
# define BOOST_PP_WHILE_834(p, o, s) BOOST_PP_WHILE_834_C(BOOST_PP_BOOL(p(835, s)), p, o, s)
# define BOOST_PP_WHILE_835(p, o, s) BOOST_PP_WHILE_835_C(BOOST_PP_BOOL(p(836, s)), p, o, s)
# define BOOST_PP_WHILE_836(p, o, s) BOOST_PP_WHILE_836_C(BOOST_PP_BOOL(p(837, s)), p, o, s)
# define BOOST_PP_WHILE_837(p, o, s) BOOST_PP_WHILE_837_C(BOOST_PP_BOOL(p(838, s)), p, o, s)
# define BOOST_PP_WHILE_838(p, o, s) BOOST_PP_WHILE_838_C(BOOST_PP_BOOL(p(839, s)), p, o, s)
# define BOOST_PP_WHILE_839(p, o, s) BOOST_PP_WHILE_839_C(BOOST_PP_BOOL(p(840, s)), p, o, s)
# define BOOST_PP_WHILE_840(p, o, s) BOOST_PP_WHILE_840_C(BOOST_PP_BOOL(p(841, s)), p, o, s)
# define BOOST_PP_WHILE_841(p, o, s) BOOST_PP_WHILE_841_C(BOOST_PP_BOOL(p(842, s)), p, o, s)
# define BOOST_PP_WHILE_842(p, o, s) BOOST_PP_WHILE_842_C(BOOST_PP_BOOL(p(843, s)), p, o, s)
# define BOOST_PP_WHILE_843(p, o, s) BOOST_PP_WHILE_843_C(BOOST_PP_BOOL(p(844, s)), p, o, s)
# define BOOST_PP_WHILE_844(p, o, s) BOOST_PP_WHILE_844_C(BOOST_PP_BOOL(p(845, s)), p, o, s)
# define BOOST_PP_WHILE_845(p, o, s) BOOST_PP_WHILE_845_C(BOOST_PP_BOOL(p(846, s)), p, o, s)
# define BOOST_PP_WHILE_846(p, o, s) BOOST_PP_WHILE_846_C(BOOST_PP_BOOL(p(847, s)), p, o, s)
# define BOOST_PP_WHILE_847(p, o, s) BOOST_PP_WHILE_847_C(BOOST_PP_BOOL(p(848, s)), p, o, s)
# define BOOST_PP_WHILE_848(p, o, s) BOOST_PP_WHILE_848_C(BOOST_PP_BOOL(p(849, s)), p, o, s)
# define BOOST_PP_WHILE_849(p, o, s) BOOST_PP_WHILE_849_C(BOOST_PP_BOOL(p(850, s)), p, o, s)
# define BOOST_PP_WHILE_850(p, o, s) BOOST_PP_WHILE_850_C(BOOST_PP_BOOL(p(851, s)), p, o, s)
# define BOOST_PP_WHILE_851(p, o, s) BOOST_PP_WHILE_851_C(BOOST_PP_BOOL(p(852, s)), p, o, s)
# define BOOST_PP_WHILE_852(p, o, s) BOOST_PP_WHILE_852_C(BOOST_PP_BOOL(p(853, s)), p, o, s)
# define BOOST_PP_WHILE_853(p, o, s) BOOST_PP_WHILE_853_C(BOOST_PP_BOOL(p(854, s)), p, o, s)
# define BOOST_PP_WHILE_854(p, o, s) BOOST_PP_WHILE_854_C(BOOST_PP_BOOL(p(855, s)), p, o, s)
# define BOOST_PP_WHILE_855(p, o, s) BOOST_PP_WHILE_855_C(BOOST_PP_BOOL(p(856, s)), p, o, s)
# define BOOST_PP_WHILE_856(p, o, s) BOOST_PP_WHILE_856_C(BOOST_PP_BOOL(p(857, s)), p, o, s)
# define BOOST_PP_WHILE_857(p, o, s) BOOST_PP_WHILE_857_C(BOOST_PP_BOOL(p(858, s)), p, o, s)
# define BOOST_PP_WHILE_858(p, o, s) BOOST_PP_WHILE_858_C(BOOST_PP_BOOL(p(859, s)), p, o, s)
# define BOOST_PP_WHILE_859(p, o, s) BOOST_PP_WHILE_859_C(BOOST_PP_BOOL(p(860, s)), p, o, s)
# define BOOST_PP_WHILE_860(p, o, s) BOOST_PP_WHILE_860_C(BOOST_PP_BOOL(p(861, s)), p, o, s)
# define BOOST_PP_WHILE_861(p, o, s) BOOST_PP_WHILE_861_C(BOOST_PP_BOOL(p(862, s)), p, o, s)
# define BOOST_PP_WHILE_862(p, o, s) BOOST_PP_WHILE_862_C(BOOST_PP_BOOL(p(863, s)), p, o, s)
# define BOOST_PP_WHILE_863(p, o, s) BOOST_PP_WHILE_863_C(BOOST_PP_BOOL(p(864, s)), p, o, s)
# define BOOST_PP_WHILE_864(p, o, s) BOOST_PP_WHILE_864_C(BOOST_PP_BOOL(p(865, s)), p, o, s)
# define BOOST_PP_WHILE_865(p, o, s) BOOST_PP_WHILE_865_C(BOOST_PP_BOOL(p(866, s)), p, o, s)
# define BOOST_PP_WHILE_866(p, o, s) BOOST_PP_WHILE_866_C(BOOST_PP_BOOL(p(867, s)), p, o, s)
# define BOOST_PP_WHILE_867(p, o, s) BOOST_PP_WHILE_867_C(BOOST_PP_BOOL(p(868, s)), p, o, s)
# define BOOST_PP_WHILE_868(p, o, s) BOOST_PP_WHILE_868_C(BOOST_PP_BOOL(p(869, s)), p, o, s)
# define BOOST_PP_WHILE_869(p, o, s) BOOST_PP_WHILE_869_C(BOOST_PP_BOOL(p(870, s)), p, o, s)
# define BOOST_PP_WHILE_870(p, o, s) BOOST_PP_WHILE_870_C(BOOST_PP_BOOL(p(871, s)), p, o, s)
# define BOOST_PP_WHILE_871(p, o, s) BOOST_PP_WHILE_871_C(BOOST_PP_BOOL(p(872, s)), p, o, s)
# define BOOST_PP_WHILE_872(p, o, s) BOOST_PP_WHILE_872_C(BOOST_PP_BOOL(p(873, s)), p, o, s)
# define BOOST_PP_WHILE_873(p, o, s) BOOST_PP_WHILE_873_C(BOOST_PP_BOOL(p(874, s)), p, o, s)
# define BOOST_PP_WHILE_874(p, o, s) BOOST_PP_WHILE_874_C(BOOST_PP_BOOL(p(875, s)), p, o, s)
# define BOOST_PP_WHILE_875(p, o, s) BOOST_PP_WHILE_875_C(BOOST_PP_BOOL(p(876, s)), p, o, s)
# define BOOST_PP_WHILE_876(p, o, s) BOOST_PP_WHILE_876_C(BOOST_PP_BOOL(p(877, s)), p, o, s)
# define BOOST_PP_WHILE_877(p, o, s) BOOST_PP_WHILE_877_C(BOOST_PP_BOOL(p(878, s)), p, o, s)
# define BOOST_PP_WHILE_878(p, o, s) BOOST_PP_WHILE_878_C(BOOST_PP_BOOL(p(879, s)), p, o, s)
# define BOOST_PP_WHILE_879(p, o, s) BOOST_PP_WHILE_879_C(BOOST_PP_BOOL(p(880, s)), p, o, s)
# define BOOST_PP_WHILE_880(p, o, s) BOOST_PP_WHILE_880_C(BOOST_PP_BOOL(p(881, s)), p, o, s)
# define BOOST_PP_WHILE_881(p, o, s) BOOST_PP_WHILE_881_C(BOOST_PP_BOOL(p(882, s)), p, o, s)
# define BOOST_PP_WHILE_882(p, o, s) BOOST_PP_WHILE_882_C(BOOST_PP_BOOL(p(883, s)), p, o, s)
# define BOOST_PP_WHILE_883(p, o, s) BOOST_PP_WHILE_883_C(BOOST_PP_BOOL(p(884, s)), p, o, s)
# define BOOST_PP_WHILE_884(p, o, s) BOOST_PP_WHILE_884_C(BOOST_PP_BOOL(p(885, s)), p, o, s)
# define BOOST_PP_WHILE_885(p, o, s) BOOST_PP_WHILE_885_C(BOOST_PP_BOOL(p(886, s)), p, o, s)
# define BOOST_PP_WHILE_886(p, o, s) BOOST_PP_WHILE_886_C(BOOST_PP_BOOL(p(887, s)), p, o, s)
# define BOOST_PP_WHILE_887(p, o, s) BOOST_PP_WHILE_887_C(BOOST_PP_BOOL(p(888, s)), p, o, s)
# define BOOST_PP_WHILE_888(p, o, s) BOOST_PP_WHILE_888_C(BOOST_PP_BOOL(p(889, s)), p, o, s)
# define BOOST_PP_WHILE_889(p, o, s) BOOST_PP_WHILE_889_C(BOOST_PP_BOOL(p(890, s)), p, o, s)
# define BOOST_PP_WHILE_890(p, o, s) BOOST_PP_WHILE_890_C(BOOST_PP_BOOL(p(891, s)), p, o, s)
# define BOOST_PP_WHILE_891(p, o, s) BOOST_PP_WHILE_891_C(BOOST_PP_BOOL(p(892, s)), p, o, s)
# define BOOST_PP_WHILE_892(p, o, s) BOOST_PP_WHILE_892_C(BOOST_PP_BOOL(p(893, s)), p, o, s)
# define BOOST_PP_WHILE_893(p, o, s) BOOST_PP_WHILE_893_C(BOOST_PP_BOOL(p(894, s)), p, o, s)
# define BOOST_PP_WHILE_894(p, o, s) BOOST_PP_WHILE_894_C(BOOST_PP_BOOL(p(895, s)), p, o, s)
# define BOOST_PP_WHILE_895(p, o, s) BOOST_PP_WHILE_895_C(BOOST_PP_BOOL(p(896, s)), p, o, s)
# define BOOST_PP_WHILE_896(p, o, s) BOOST_PP_WHILE_896_C(BOOST_PP_BOOL(p(897, s)), p, o, s)
# define BOOST_PP_WHILE_897(p, o, s) BOOST_PP_WHILE_897_C(BOOST_PP_BOOL(p(898, s)), p, o, s)
# define BOOST_PP_WHILE_898(p, o, s) BOOST_PP_WHILE_898_C(BOOST_PP_BOOL(p(899, s)), p, o, s)
# define BOOST_PP_WHILE_899(p, o, s) BOOST_PP_WHILE_899_C(BOOST_PP_BOOL(p(900, s)), p, o, s)
# define BOOST_PP_WHILE_900(p, o, s) BOOST_PP_WHILE_900_C(BOOST_PP_BOOL(p(901, s)), p, o, s)
# define BOOST_PP_WHILE_901(p, o, s) BOOST_PP_WHILE_901_C(BOOST_PP_BOOL(p(902, s)), p, o, s)
# define BOOST_PP_WHILE_902(p, o, s) BOOST_PP_WHILE_902_C(BOOST_PP_BOOL(p(903, s)), p, o, s)
# define BOOST_PP_WHILE_903(p, o, s) BOOST_PP_WHILE_903_C(BOOST_PP_BOOL(p(904, s)), p, o, s)
# define BOOST_PP_WHILE_904(p, o, s) BOOST_PP_WHILE_904_C(BOOST_PP_BOOL(p(905, s)), p, o, s)
# define BOOST_PP_WHILE_905(p, o, s) BOOST_PP_WHILE_905_C(BOOST_PP_BOOL(p(906, s)), p, o, s)
# define BOOST_PP_WHILE_906(p, o, s) BOOST_PP_WHILE_906_C(BOOST_PP_BOOL(p(907, s)), p, o, s)
# define BOOST_PP_WHILE_907(p, o, s) BOOST_PP_WHILE_907_C(BOOST_PP_BOOL(p(908, s)), p, o, s)
# define BOOST_PP_WHILE_908(p, o, s) BOOST_PP_WHILE_908_C(BOOST_PP_BOOL(p(909, s)), p, o, s)
# define BOOST_PP_WHILE_909(p, o, s) BOOST_PP_WHILE_909_C(BOOST_PP_BOOL(p(910, s)), p, o, s)
# define BOOST_PP_WHILE_910(p, o, s) BOOST_PP_WHILE_910_C(BOOST_PP_BOOL(p(911, s)), p, o, s)
# define BOOST_PP_WHILE_911(p, o, s) BOOST_PP_WHILE_911_C(BOOST_PP_BOOL(p(912, s)), p, o, s)
# define BOOST_PP_WHILE_912(p, o, s) BOOST_PP_WHILE_912_C(BOOST_PP_BOOL(p(913, s)), p, o, s)
# define BOOST_PP_WHILE_913(p, o, s) BOOST_PP_WHILE_913_C(BOOST_PP_BOOL(p(914, s)), p, o, s)
# define BOOST_PP_WHILE_914(p, o, s) BOOST_PP_WHILE_914_C(BOOST_PP_BOOL(p(915, s)), p, o, s)
# define BOOST_PP_WHILE_915(p, o, s) BOOST_PP_WHILE_915_C(BOOST_PP_BOOL(p(916, s)), p, o, s)
# define BOOST_PP_WHILE_916(p, o, s) BOOST_PP_WHILE_916_C(BOOST_PP_BOOL(p(917, s)), p, o, s)
# define BOOST_PP_WHILE_917(p, o, s) BOOST_PP_WHILE_917_C(BOOST_PP_BOOL(p(918, s)), p, o, s)
# define BOOST_PP_WHILE_918(p, o, s) BOOST_PP_WHILE_918_C(BOOST_PP_BOOL(p(919, s)), p, o, s)
# define BOOST_PP_WHILE_919(p, o, s) BOOST_PP_WHILE_919_C(BOOST_PP_BOOL(p(920, s)), p, o, s)
# define BOOST_PP_WHILE_920(p, o, s) BOOST_PP_WHILE_920_C(BOOST_PP_BOOL(p(921, s)), p, o, s)
# define BOOST_PP_WHILE_921(p, o, s) BOOST_PP_WHILE_921_C(BOOST_PP_BOOL(p(922, s)), p, o, s)
# define BOOST_PP_WHILE_922(p, o, s) BOOST_PP_WHILE_922_C(BOOST_PP_BOOL(p(923, s)), p, o, s)
# define BOOST_PP_WHILE_923(p, o, s) BOOST_PP_WHILE_923_C(BOOST_PP_BOOL(p(924, s)), p, o, s)
# define BOOST_PP_WHILE_924(p, o, s) BOOST_PP_WHILE_924_C(BOOST_PP_BOOL(p(925, s)), p, o, s)
# define BOOST_PP_WHILE_925(p, o, s) BOOST_PP_WHILE_925_C(BOOST_PP_BOOL(p(926, s)), p, o, s)
# define BOOST_PP_WHILE_926(p, o, s) BOOST_PP_WHILE_926_C(BOOST_PP_BOOL(p(927, s)), p, o, s)
# define BOOST_PP_WHILE_927(p, o, s) BOOST_PP_WHILE_927_C(BOOST_PP_BOOL(p(928, s)), p, o, s)
# define BOOST_PP_WHILE_928(p, o, s) BOOST_PP_WHILE_928_C(BOOST_PP_BOOL(p(929, s)), p, o, s)
# define BOOST_PP_WHILE_929(p, o, s) BOOST_PP_WHILE_929_C(BOOST_PP_BOOL(p(930, s)), p, o, s)
# define BOOST_PP_WHILE_930(p, o, s) BOOST_PP_WHILE_930_C(BOOST_PP_BOOL(p(931, s)), p, o, s)
# define BOOST_PP_WHILE_931(p, o, s) BOOST_PP_WHILE_931_C(BOOST_PP_BOOL(p(932, s)), p, o, s)
# define BOOST_PP_WHILE_932(p, o, s) BOOST_PP_WHILE_932_C(BOOST_PP_BOOL(p(933, s)), p, o, s)
# define BOOST_PP_WHILE_933(p, o, s) BOOST_PP_WHILE_933_C(BOOST_PP_BOOL(p(934, s)), p, o, s)
# define BOOST_PP_WHILE_934(p, o, s) BOOST_PP_WHILE_934_C(BOOST_PP_BOOL(p(935, s)), p, o, s)
# define BOOST_PP_WHILE_935(p, o, s) BOOST_PP_WHILE_935_C(BOOST_PP_BOOL(p(936, s)), p, o, s)
# define BOOST_PP_WHILE_936(p, o, s) BOOST_PP_WHILE_936_C(BOOST_PP_BOOL(p(937, s)), p, o, s)
# define BOOST_PP_WHILE_937(p, o, s) BOOST_PP_WHILE_937_C(BOOST_PP_BOOL(p(938, s)), p, o, s)
# define BOOST_PP_WHILE_938(p, o, s) BOOST_PP_WHILE_938_C(BOOST_PP_BOOL(p(939, s)), p, o, s)
# define BOOST_PP_WHILE_939(p, o, s) BOOST_PP_WHILE_939_C(BOOST_PP_BOOL(p(940, s)), p, o, s)
# define BOOST_PP_WHILE_940(p, o, s) BOOST_PP_WHILE_940_C(BOOST_PP_BOOL(p(941, s)), p, o, s)
# define BOOST_PP_WHILE_941(p, o, s) BOOST_PP_WHILE_941_C(BOOST_PP_BOOL(p(942, s)), p, o, s)
# define BOOST_PP_WHILE_942(p, o, s) BOOST_PP_WHILE_942_C(BOOST_PP_BOOL(p(943, s)), p, o, s)
# define BOOST_PP_WHILE_943(p, o, s) BOOST_PP_WHILE_943_C(BOOST_PP_BOOL(p(944, s)), p, o, s)
# define BOOST_PP_WHILE_944(p, o, s) BOOST_PP_WHILE_944_C(BOOST_PP_BOOL(p(945, s)), p, o, s)
# define BOOST_PP_WHILE_945(p, o, s) BOOST_PP_WHILE_945_C(BOOST_PP_BOOL(p(946, s)), p, o, s)
# define BOOST_PP_WHILE_946(p, o, s) BOOST_PP_WHILE_946_C(BOOST_PP_BOOL(p(947, s)), p, o, s)
# define BOOST_PP_WHILE_947(p, o, s) BOOST_PP_WHILE_947_C(BOOST_PP_BOOL(p(948, s)), p, o, s)
# define BOOST_PP_WHILE_948(p, o, s) BOOST_PP_WHILE_948_C(BOOST_PP_BOOL(p(949, s)), p, o, s)
# define BOOST_PP_WHILE_949(p, o, s) BOOST_PP_WHILE_949_C(BOOST_PP_BOOL(p(950, s)), p, o, s)
# define BOOST_PP_WHILE_950(p, o, s) BOOST_PP_WHILE_950_C(BOOST_PP_BOOL(p(951, s)), p, o, s)
# define BOOST_PP_WHILE_951(p, o, s) BOOST_PP_WHILE_951_C(BOOST_PP_BOOL(p(952, s)), p, o, s)
# define BOOST_PP_WHILE_952(p, o, s) BOOST_PP_WHILE_952_C(BOOST_PP_BOOL(p(953, s)), p, o, s)
# define BOOST_PP_WHILE_953(p, o, s) BOOST_PP_WHILE_953_C(BOOST_PP_BOOL(p(954, s)), p, o, s)
# define BOOST_PP_WHILE_954(p, o, s) BOOST_PP_WHILE_954_C(BOOST_PP_BOOL(p(955, s)), p, o, s)
# define BOOST_PP_WHILE_955(p, o, s) BOOST_PP_WHILE_955_C(BOOST_PP_BOOL(p(956, s)), p, o, s)
# define BOOST_PP_WHILE_956(p, o, s) BOOST_PP_WHILE_956_C(BOOST_PP_BOOL(p(957, s)), p, o, s)
# define BOOST_PP_WHILE_957(p, o, s) BOOST_PP_WHILE_957_C(BOOST_PP_BOOL(p(958, s)), p, o, s)
# define BOOST_PP_WHILE_958(p, o, s) BOOST_PP_WHILE_958_C(BOOST_PP_BOOL(p(959, s)), p, o, s)
# define BOOST_PP_WHILE_959(p, o, s) BOOST_PP_WHILE_959_C(BOOST_PP_BOOL(p(960, s)), p, o, s)
# define BOOST_PP_WHILE_960(p, o, s) BOOST_PP_WHILE_960_C(BOOST_PP_BOOL(p(961, s)), p, o, s)
# define BOOST_PP_WHILE_961(p, o, s) BOOST_PP_WHILE_961_C(BOOST_PP_BOOL(p(962, s)), p, o, s)
# define BOOST_PP_WHILE_962(p, o, s) BOOST_PP_WHILE_962_C(BOOST_PP_BOOL(p(963, s)), p, o, s)
# define BOOST_PP_WHILE_963(p, o, s) BOOST_PP_WHILE_963_C(BOOST_PP_BOOL(p(964, s)), p, o, s)
# define BOOST_PP_WHILE_964(p, o, s) BOOST_PP_WHILE_964_C(BOOST_PP_BOOL(p(965, s)), p, o, s)
# define BOOST_PP_WHILE_965(p, o, s) BOOST_PP_WHILE_965_C(BOOST_PP_BOOL(p(966, s)), p, o, s)
# define BOOST_PP_WHILE_966(p, o, s) BOOST_PP_WHILE_966_C(BOOST_PP_BOOL(p(967, s)), p, o, s)
# define BOOST_PP_WHILE_967(p, o, s) BOOST_PP_WHILE_967_C(BOOST_PP_BOOL(p(968, s)), p, o, s)
# define BOOST_PP_WHILE_968(p, o, s) BOOST_PP_WHILE_968_C(BOOST_PP_BOOL(p(969, s)), p, o, s)
# define BOOST_PP_WHILE_969(p, o, s) BOOST_PP_WHILE_969_C(BOOST_PP_BOOL(p(970, s)), p, o, s)
# define BOOST_PP_WHILE_970(p, o, s) BOOST_PP_WHILE_970_C(BOOST_PP_BOOL(p(971, s)), p, o, s)
# define BOOST_PP_WHILE_971(p, o, s) BOOST_PP_WHILE_971_C(BOOST_PP_BOOL(p(972, s)), p, o, s)
# define BOOST_PP_WHILE_972(p, o, s) BOOST_PP_WHILE_972_C(BOOST_PP_BOOL(p(973, s)), p, o, s)
# define BOOST_PP_WHILE_973(p, o, s) BOOST_PP_WHILE_973_C(BOOST_PP_BOOL(p(974, s)), p, o, s)
# define BOOST_PP_WHILE_974(p, o, s) BOOST_PP_WHILE_974_C(BOOST_PP_BOOL(p(975, s)), p, o, s)
# define BOOST_PP_WHILE_975(p, o, s) BOOST_PP_WHILE_975_C(BOOST_PP_BOOL(p(976, s)), p, o, s)
# define BOOST_PP_WHILE_976(p, o, s) BOOST_PP_WHILE_976_C(BOOST_PP_BOOL(p(977, s)), p, o, s)
# define BOOST_PP_WHILE_977(p, o, s) BOOST_PP_WHILE_977_C(BOOST_PP_BOOL(p(978, s)), p, o, s)
# define BOOST_PP_WHILE_978(p, o, s) BOOST_PP_WHILE_978_C(BOOST_PP_BOOL(p(979, s)), p, o, s)
# define BOOST_PP_WHILE_979(p, o, s) BOOST_PP_WHILE_979_C(BOOST_PP_BOOL(p(980, s)), p, o, s)
# define BOOST_PP_WHILE_980(p, o, s) BOOST_PP_WHILE_980_C(BOOST_PP_BOOL(p(981, s)), p, o, s)
# define BOOST_PP_WHILE_981(p, o, s) BOOST_PP_WHILE_981_C(BOOST_PP_BOOL(p(982, s)), p, o, s)
# define BOOST_PP_WHILE_982(p, o, s) BOOST_PP_WHILE_982_C(BOOST_PP_BOOL(p(983, s)), p, o, s)
# define BOOST_PP_WHILE_983(p, o, s) BOOST_PP_WHILE_983_C(BOOST_PP_BOOL(p(984, s)), p, o, s)
# define BOOST_PP_WHILE_984(p, o, s) BOOST_PP_WHILE_984_C(BOOST_PP_BOOL(p(985, s)), p, o, s)
# define BOOST_PP_WHILE_985(p, o, s) BOOST_PP_WHILE_985_C(BOOST_PP_BOOL(p(986, s)), p, o, s)
# define BOOST_PP_WHILE_986(p, o, s) BOOST_PP_WHILE_986_C(BOOST_PP_BOOL(p(987, s)), p, o, s)
# define BOOST_PP_WHILE_987(p, o, s) BOOST_PP_WHILE_987_C(BOOST_PP_BOOL(p(988, s)), p, o, s)
# define BOOST_PP_WHILE_988(p, o, s) BOOST_PP_WHILE_988_C(BOOST_PP_BOOL(p(989, s)), p, o, s)
# define BOOST_PP_WHILE_989(p, o, s) BOOST_PP_WHILE_989_C(BOOST_PP_BOOL(p(990, s)), p, o, s)
# define BOOST_PP_WHILE_990(p, o, s) BOOST_PP_WHILE_990_C(BOOST_PP_BOOL(p(991, s)), p, o, s)
# define BOOST_PP_WHILE_991(p, o, s) BOOST_PP_WHILE_991_C(BOOST_PP_BOOL(p(992, s)), p, o, s)
# define BOOST_PP_WHILE_992(p, o, s) BOOST_PP_WHILE_992_C(BOOST_PP_BOOL(p(993, s)), p, o, s)
# define BOOST_PP_WHILE_993(p, o, s) BOOST_PP_WHILE_993_C(BOOST_PP_BOOL(p(994, s)), p, o, s)
# define BOOST_PP_WHILE_994(p, o, s) BOOST_PP_WHILE_994_C(BOOST_PP_BOOL(p(995, s)), p, o, s)
# define BOOST_PP_WHILE_995(p, o, s) BOOST_PP_WHILE_995_C(BOOST_PP_BOOL(p(996, s)), p, o, s)
# define BOOST_PP_WHILE_996(p, o, s) BOOST_PP_WHILE_996_C(BOOST_PP_BOOL(p(997, s)), p, o, s)
# define BOOST_PP_WHILE_997(p, o, s) BOOST_PP_WHILE_997_C(BOOST_PP_BOOL(p(998, s)), p, o, s)
# define BOOST_PP_WHILE_998(p, o, s) BOOST_PP_WHILE_998_C(BOOST_PP_BOOL(p(999, s)), p, o, s)
# define BOOST_PP_WHILE_999(p, o, s) BOOST_PP_WHILE_999_C(BOOST_PP_BOOL(p(1000, s)), p, o, s)
# define BOOST_PP_WHILE_1000(p, o, s) BOOST_PP_WHILE_1000_C(BOOST_PP_BOOL(p(1001, s)), p, o, s)
# define BOOST_PP_WHILE_1001(p, o, s) BOOST_PP_WHILE_1001_C(BOOST_PP_BOOL(p(1002, s)), p, o, s)
# define BOOST_PP_WHILE_1002(p, o, s) BOOST_PP_WHILE_1002_C(BOOST_PP_BOOL(p(1003, s)), p, o, s)
# define BOOST_PP_WHILE_1003(p, o, s) BOOST_PP_WHILE_1003_C(BOOST_PP_BOOL(p(1004, s)), p, o, s)
# define BOOST_PP_WHILE_1004(p, o, s) BOOST_PP_WHILE_1004_C(BOOST_PP_BOOL(p(1005, s)), p, o, s)
# define BOOST_PP_WHILE_1005(p, o, s) BOOST_PP_WHILE_1005_C(BOOST_PP_BOOL(p(1006, s)), p, o, s)
# define BOOST_PP_WHILE_1006(p, o, s) BOOST_PP_WHILE_1006_C(BOOST_PP_BOOL(p(1007, s)), p, o, s)
# define BOOST_PP_WHILE_1007(p, o, s) BOOST_PP_WHILE_1007_C(BOOST_PP_BOOL(p(1008, s)), p, o, s)
# define BOOST_PP_WHILE_1008(p, o, s) BOOST_PP_WHILE_1008_C(BOOST_PP_BOOL(p(1009, s)), p, o, s)
# define BOOST_PP_WHILE_1009(p, o, s) BOOST_PP_WHILE_1009_C(BOOST_PP_BOOL(p(1010, s)), p, o, s)
# define BOOST_PP_WHILE_1010(p, o, s) BOOST_PP_WHILE_1010_C(BOOST_PP_BOOL(p(1011, s)), p, o, s)
# define BOOST_PP_WHILE_1011(p, o, s) BOOST_PP_WHILE_1011_C(BOOST_PP_BOOL(p(1012, s)), p, o, s)
# define BOOST_PP_WHILE_1012(p, o, s) BOOST_PP_WHILE_1012_C(BOOST_PP_BOOL(p(1013, s)), p, o, s)
# define BOOST_PP_WHILE_1013(p, o, s) BOOST_PP_WHILE_1013_C(BOOST_PP_BOOL(p(1014, s)), p, o, s)
# define BOOST_PP_WHILE_1014(p, o, s) BOOST_PP_WHILE_1014_C(BOOST_PP_BOOL(p(1015, s)), p, o, s)
# define BOOST_PP_WHILE_1015(p, o, s) BOOST_PP_WHILE_1015_C(BOOST_PP_BOOL(p(1016, s)), p, o, s)
# define BOOST_PP_WHILE_1016(p, o, s) BOOST_PP_WHILE_1016_C(BOOST_PP_BOOL(p(1017, s)), p, o, s)
# define BOOST_PP_WHILE_1017(p, o, s) BOOST_PP_WHILE_1017_C(BOOST_PP_BOOL(p(1018, s)), p, o, s)
# define BOOST_PP_WHILE_1018(p, o, s) BOOST_PP_WHILE_1018_C(BOOST_PP_BOOL(p(1019, s)), p, o, s)
# define BOOST_PP_WHILE_1019(p, o, s) BOOST_PP_WHILE_1019_C(BOOST_PP_BOOL(p(1020, s)), p, o, s)
# define BOOST_PP_WHILE_1020(p, o, s) BOOST_PP_WHILE_1020_C(BOOST_PP_BOOL(p(1021, s)), p, o, s)
# define BOOST_PP_WHILE_1021(p, o, s) BOOST_PP_WHILE_1021_C(BOOST_PP_BOOL(p(1022, s)), p, o, s)
# define BOOST_PP_WHILE_1022(p, o, s) BOOST_PP_WHILE_1022_C(BOOST_PP_BOOL(p(1023, s)), p, o, s)
# define BOOST_PP_WHILE_1023(p, o, s) BOOST_PP_WHILE_1023_C(BOOST_PP_BOOL(p(1024, s)), p, o, s)
# define BOOST_PP_WHILE_1024(p, o, s) BOOST_PP_WHILE_1024_C(BOOST_PP_BOOL(p(1025, s)), p, o, s)
#
# define BOOST_PP_WHILE_513_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_514, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(514, s))
# define BOOST_PP_WHILE_514_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_515, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(515, s))
# define BOOST_PP_WHILE_515_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_516, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(516, s))
# define BOOST_PP_WHILE_516_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_517, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(517, s))
# define BOOST_PP_WHILE_517_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_518, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(518, s))
# define BOOST_PP_WHILE_518_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_519, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(519, s))
# define BOOST_PP_WHILE_519_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_520, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(520, s))
# define BOOST_PP_WHILE_520_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_521, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(521, s))
# define BOOST_PP_WHILE_521_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_522, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(522, s))
# define BOOST_PP_WHILE_522_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_523, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(523, s))
# define BOOST_PP_WHILE_523_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_524, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(524, s))
# define BOOST_PP_WHILE_524_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_525, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(525, s))
# define BOOST_PP_WHILE_525_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_526, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(526, s))
# define BOOST_PP_WHILE_526_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_527, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(527, s))
# define BOOST_PP_WHILE_527_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_528, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(528, s))
# define BOOST_PP_WHILE_528_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_529, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(529, s))
# define BOOST_PP_WHILE_529_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_530, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(530, s))
# define BOOST_PP_WHILE_530_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_531, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(531, s))
# define BOOST_PP_WHILE_531_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_532, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(532, s))
# define BOOST_PP_WHILE_532_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_533, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(533, s))
# define BOOST_PP_WHILE_533_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_534, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(534, s))
# define BOOST_PP_WHILE_534_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_535, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(535, s))
# define BOOST_PP_WHILE_535_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_536, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(536, s))
# define BOOST_PP_WHILE_536_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_537, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(537, s))
# define BOOST_PP_WHILE_537_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_538, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(538, s))
# define BOOST_PP_WHILE_538_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_539, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(539, s))
# define BOOST_PP_WHILE_539_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_540, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(540, s))
# define BOOST_PP_WHILE_540_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_541, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(541, s))
# define BOOST_PP_WHILE_541_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_542, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(542, s))
# define BOOST_PP_WHILE_542_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_543, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(543, s))
# define BOOST_PP_WHILE_543_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_544, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(544, s))
# define BOOST_PP_WHILE_544_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_545, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(545, s))
# define BOOST_PP_WHILE_545_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_546, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(546, s))
# define BOOST_PP_WHILE_546_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_547, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(547, s))
# define BOOST_PP_WHILE_547_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_548, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(548, s))
# define BOOST_PP_WHILE_548_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_549, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(549, s))
# define BOOST_PP_WHILE_549_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_550, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(550, s))
# define BOOST_PP_WHILE_550_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_551, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(551, s))
# define BOOST_PP_WHILE_551_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_552, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(552, s))
# define BOOST_PP_WHILE_552_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_553, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(553, s))
# define BOOST_PP_WHILE_553_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_554, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(554, s))
# define BOOST_PP_WHILE_554_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_555, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(555, s))
# define BOOST_PP_WHILE_555_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_556, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(556, s))
# define BOOST_PP_WHILE_556_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_557, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(557, s))
# define BOOST_PP_WHILE_557_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_558, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(558, s))
# define BOOST_PP_WHILE_558_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_559, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(559, s))
# define BOOST_PP_WHILE_559_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_560, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(560, s))
# define BOOST_PP_WHILE_560_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_561, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(561, s))
# define BOOST_PP_WHILE_561_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_562, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(562, s))
# define BOOST_PP_WHILE_562_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_563, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(563, s))
# define BOOST_PP_WHILE_563_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_564, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(564, s))
# define BOOST_PP_WHILE_564_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_565, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(565, s))
# define BOOST_PP_WHILE_565_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_566, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(566, s))
# define BOOST_PP_WHILE_566_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_567, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(567, s))
# define BOOST_PP_WHILE_567_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_568, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(568, s))
# define BOOST_PP_WHILE_568_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_569, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(569, s))
# define BOOST_PP_WHILE_569_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_570, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(570, s))
# define BOOST_PP_WHILE_570_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_571, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(571, s))
# define BOOST_PP_WHILE_571_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_572, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(572, s))
# define BOOST_PP_WHILE_572_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_573, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(573, s))
# define BOOST_PP_WHILE_573_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_574, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(574, s))
# define BOOST_PP_WHILE_574_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_575, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(575, s))
# define BOOST_PP_WHILE_575_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_576, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(576, s))
# define BOOST_PP_WHILE_576_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_577, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(577, s))
# define BOOST_PP_WHILE_577_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_578, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(578, s))
# define BOOST_PP_WHILE_578_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_579, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(579, s))
# define BOOST_PP_WHILE_579_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_580, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(580, s))
# define BOOST_PP_WHILE_580_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_581, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(581, s))
# define BOOST_PP_WHILE_581_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_582, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(582, s))
# define BOOST_PP_WHILE_582_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_583, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(583, s))
# define BOOST_PP_WHILE_583_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_584, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(584, s))
# define BOOST_PP_WHILE_584_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_585, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(585, s))
# define BOOST_PP_WHILE_585_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_586, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(586, s))
# define BOOST_PP_WHILE_586_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_587, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(587, s))
# define BOOST_PP_WHILE_587_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_588, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(588, s))
# define BOOST_PP_WHILE_588_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_589, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(589, s))
# define BOOST_PP_WHILE_589_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_590, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(590, s))
# define BOOST_PP_WHILE_590_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_591, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(591, s))
# define BOOST_PP_WHILE_591_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_592, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(592, s))
# define BOOST_PP_WHILE_592_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_593, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(593, s))
# define BOOST_PP_WHILE_593_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_594, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(594, s))
# define BOOST_PP_WHILE_594_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_595, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(595, s))
# define BOOST_PP_WHILE_595_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_596, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(596, s))
# define BOOST_PP_WHILE_596_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_597, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(597, s))
# define BOOST_PP_WHILE_597_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_598, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(598, s))
# define BOOST_PP_WHILE_598_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_599, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(599, s))
# define BOOST_PP_WHILE_599_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_600, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(600, s))
# define BOOST_PP_WHILE_600_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_601, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(601, s))
# define BOOST_PP_WHILE_601_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_602, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(602, s))
# define BOOST_PP_WHILE_602_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_603, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(603, s))
# define BOOST_PP_WHILE_603_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_604, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(604, s))
# define BOOST_PP_WHILE_604_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_605, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(605, s))
# define BOOST_PP_WHILE_605_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_606, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(606, s))
# define BOOST_PP_WHILE_606_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_607, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(607, s))
# define BOOST_PP_WHILE_607_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_608, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(608, s))
# define BOOST_PP_WHILE_608_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_609, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(609, s))
# define BOOST_PP_WHILE_609_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_610, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(610, s))
# define BOOST_PP_WHILE_610_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_611, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(611, s))
# define BOOST_PP_WHILE_611_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_612, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(612, s))
# define BOOST_PP_WHILE_612_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_613, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(613, s))
# define BOOST_PP_WHILE_613_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_614, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(614, s))
# define BOOST_PP_WHILE_614_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_615, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(615, s))
# define BOOST_PP_WHILE_615_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_616, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(616, s))
# define BOOST_PP_WHILE_616_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_617, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(617, s))
# define BOOST_PP_WHILE_617_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_618, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(618, s))
# define BOOST_PP_WHILE_618_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_619, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(619, s))
# define BOOST_PP_WHILE_619_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_620, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(620, s))
# define BOOST_PP_WHILE_620_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_621, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(621, s))
# define BOOST_PP_WHILE_621_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_622, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(622, s))
# define BOOST_PP_WHILE_622_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_623, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(623, s))
# define BOOST_PP_WHILE_623_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_624, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(624, s))
# define BOOST_PP_WHILE_624_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_625, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(625, s))
# define BOOST_PP_WHILE_625_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_626, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(626, s))
# define BOOST_PP_WHILE_626_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_627, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(627, s))
# define BOOST_PP_WHILE_627_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_628, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(628, s))
# define BOOST_PP_WHILE_628_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_629, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(629, s))
# define BOOST_PP_WHILE_629_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_630, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(630, s))
# define BOOST_PP_WHILE_630_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_631, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(631, s))
# define BOOST_PP_WHILE_631_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_632, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(632, s))
# define BOOST_PP_WHILE_632_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_633, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(633, s))
# define BOOST_PP_WHILE_633_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_634, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(634, s))
# define BOOST_PP_WHILE_634_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_635, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(635, s))
# define BOOST_PP_WHILE_635_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_636, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(636, s))
# define BOOST_PP_WHILE_636_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_637, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(637, s))
# define BOOST_PP_WHILE_637_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_638, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(638, s))
# define BOOST_PP_WHILE_638_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_639, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(639, s))
# define BOOST_PP_WHILE_639_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_640, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(640, s))
# define BOOST_PP_WHILE_640_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_641, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(641, s))
# define BOOST_PP_WHILE_641_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_642, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(642, s))
# define BOOST_PP_WHILE_642_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_643, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(643, s))
# define BOOST_PP_WHILE_643_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_644, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(644, s))
# define BOOST_PP_WHILE_644_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_645, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(645, s))
# define BOOST_PP_WHILE_645_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_646, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(646, s))
# define BOOST_PP_WHILE_646_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_647, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(647, s))
# define BOOST_PP_WHILE_647_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_648, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(648, s))
# define BOOST_PP_WHILE_648_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_649, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(649, s))
# define BOOST_PP_WHILE_649_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_650, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(650, s))
# define BOOST_PP_WHILE_650_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_651, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(651, s))
# define BOOST_PP_WHILE_651_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_652, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(652, s))
# define BOOST_PP_WHILE_652_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_653, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(653, s))
# define BOOST_PP_WHILE_653_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_654, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(654, s))
# define BOOST_PP_WHILE_654_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_655, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(655, s))
# define BOOST_PP_WHILE_655_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_656, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(656, s))
# define BOOST_PP_WHILE_656_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_657, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(657, s))
# define BOOST_PP_WHILE_657_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_658, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(658, s))
# define BOOST_PP_WHILE_658_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_659, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(659, s))
# define BOOST_PP_WHILE_659_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_660, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(660, s))
# define BOOST_PP_WHILE_660_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_661, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(661, s))
# define BOOST_PP_WHILE_661_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_662, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(662, s))
# define BOOST_PP_WHILE_662_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_663, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(663, s))
# define BOOST_PP_WHILE_663_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_664, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(664, s))
# define BOOST_PP_WHILE_664_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_665, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(665, s))
# define BOOST_PP_WHILE_665_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_666, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(666, s))
# define BOOST_PP_WHILE_666_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_667, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(667, s))
# define BOOST_PP_WHILE_667_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_668, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(668, s))
# define BOOST_PP_WHILE_668_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_669, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(669, s))
# define BOOST_PP_WHILE_669_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_670, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(670, s))
# define BOOST_PP_WHILE_670_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_671, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(671, s))
# define BOOST_PP_WHILE_671_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_672, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(672, s))
# define BOOST_PP_WHILE_672_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_673, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(673, s))
# define BOOST_PP_WHILE_673_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_674, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(674, s))
# define BOOST_PP_WHILE_674_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_675, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(675, s))
# define BOOST_PP_WHILE_675_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_676, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(676, s))
# define BOOST_PP_WHILE_676_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_677, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(677, s))
# define BOOST_PP_WHILE_677_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_678, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(678, s))
# define BOOST_PP_WHILE_678_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_679, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(679, s))
# define BOOST_PP_WHILE_679_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_680, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(680, s))
# define BOOST_PP_WHILE_680_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_681, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(681, s))
# define BOOST_PP_WHILE_681_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_682, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(682, s))
# define BOOST_PP_WHILE_682_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_683, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(683, s))
# define BOOST_PP_WHILE_683_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_684, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(684, s))
# define BOOST_PP_WHILE_684_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_685, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(685, s))
# define BOOST_PP_WHILE_685_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_686, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(686, s))
# define BOOST_PP_WHILE_686_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_687, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(687, s))
# define BOOST_PP_WHILE_687_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_688, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(688, s))
# define BOOST_PP_WHILE_688_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_689, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(689, s))
# define BOOST_PP_WHILE_689_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_690, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(690, s))
# define BOOST_PP_WHILE_690_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_691, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(691, s))
# define BOOST_PP_WHILE_691_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_692, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(692, s))
# define BOOST_PP_WHILE_692_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_693, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(693, s))
# define BOOST_PP_WHILE_693_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_694, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(694, s))
# define BOOST_PP_WHILE_694_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_695, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(695, s))
# define BOOST_PP_WHILE_695_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_696, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(696, s))
# define BOOST_PP_WHILE_696_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_697, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(697, s))
# define BOOST_PP_WHILE_697_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_698, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(698, s))
# define BOOST_PP_WHILE_698_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_699, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(699, s))
# define BOOST_PP_WHILE_699_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_700, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(700, s))
# define BOOST_PP_WHILE_700_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_701, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(701, s))
# define BOOST_PP_WHILE_701_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_702, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(702, s))
# define BOOST_PP_WHILE_702_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_703, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(703, s))
# define BOOST_PP_WHILE_703_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_704, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(704, s))
# define BOOST_PP_WHILE_704_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_705, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(705, s))
# define BOOST_PP_WHILE_705_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_706, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(706, s))
# define BOOST_PP_WHILE_706_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_707, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(707, s))
# define BOOST_PP_WHILE_707_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_708, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(708, s))
# define BOOST_PP_WHILE_708_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_709, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(709, s))
# define BOOST_PP_WHILE_709_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_710, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(710, s))
# define BOOST_PP_WHILE_710_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_711, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(711, s))
# define BOOST_PP_WHILE_711_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_712, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(712, s))
# define BOOST_PP_WHILE_712_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_713, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(713, s))
# define BOOST_PP_WHILE_713_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_714, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(714, s))
# define BOOST_PP_WHILE_714_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_715, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(715, s))
# define BOOST_PP_WHILE_715_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_716, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(716, s))
# define BOOST_PP_WHILE_716_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_717, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(717, s))
# define BOOST_PP_WHILE_717_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_718, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(718, s))
# define BOOST_PP_WHILE_718_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_719, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(719, s))
# define BOOST_PP_WHILE_719_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_720, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(720, s))
# define BOOST_PP_WHILE_720_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_721, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(721, s))
# define BOOST_PP_WHILE_721_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_722, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(722, s))
# define BOOST_PP_WHILE_722_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_723, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(723, s))
# define BOOST_PP_WHILE_723_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_724, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(724, s))
# define BOOST_PP_WHILE_724_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_725, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(725, s))
# define BOOST_PP_WHILE_725_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_726, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(726, s))
# define BOOST_PP_WHILE_726_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_727, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(727, s))
# define BOOST_PP_WHILE_727_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_728, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(728, s))
# define BOOST_PP_WHILE_728_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_729, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(729, s))
# define BOOST_PP_WHILE_729_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_730, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(730, s))
# define BOOST_PP_WHILE_730_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_731, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(731, s))
# define BOOST_PP_WHILE_731_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_732, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(732, s))
# define BOOST_PP_WHILE_732_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_733, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(733, s))
# define BOOST_PP_WHILE_733_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_734, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(734, s))
# define BOOST_PP_WHILE_734_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_735, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(735, s))
# define BOOST_PP_WHILE_735_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_736, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(736, s))
# define BOOST_PP_WHILE_736_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_737, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(737, s))
# define BOOST_PP_WHILE_737_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_738, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(738, s))
# define BOOST_PP_WHILE_738_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_739, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(739, s))
# define BOOST_PP_WHILE_739_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_740, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(740, s))
# define BOOST_PP_WHILE_740_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_741, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(741, s))
# define BOOST_PP_WHILE_741_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_742, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(742, s))
# define BOOST_PP_WHILE_742_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_743, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(743, s))
# define BOOST_PP_WHILE_743_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_744, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(744, s))
# define BOOST_PP_WHILE_744_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_745, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(745, s))
# define BOOST_PP_WHILE_745_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_746, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(746, s))
# define BOOST_PP_WHILE_746_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_747, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(747, s))
# define BOOST_PP_WHILE_747_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_748, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(748, s))
# define BOOST_PP_WHILE_748_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_749, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(749, s))
# define BOOST_PP_WHILE_749_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_750, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(750, s))
# define BOOST_PP_WHILE_750_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_751, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(751, s))
# define BOOST_PP_WHILE_751_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_752, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(752, s))
# define BOOST_PP_WHILE_752_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_753, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(753, s))
# define BOOST_PP_WHILE_753_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_754, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(754, s))
# define BOOST_PP_WHILE_754_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_755, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(755, s))
# define BOOST_PP_WHILE_755_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_756, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(756, s))
# define BOOST_PP_WHILE_756_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_757, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(757, s))
# define BOOST_PP_WHILE_757_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_758, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(758, s))
# define BOOST_PP_WHILE_758_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_759, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(759, s))
# define BOOST_PP_WHILE_759_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_760, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(760, s))
# define BOOST_PP_WHILE_760_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_761, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(761, s))
# define BOOST_PP_WHILE_761_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_762, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(762, s))
# define BOOST_PP_WHILE_762_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_763, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(763, s))
# define BOOST_PP_WHILE_763_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_764, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(764, s))
# define BOOST_PP_WHILE_764_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_765, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(765, s))
# define BOOST_PP_WHILE_765_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_766, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(766, s))
# define BOOST_PP_WHILE_766_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_767, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(767, s))
# define BOOST_PP_WHILE_767_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_768, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(768, s))
# define BOOST_PP_WHILE_768_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_769, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(769, s))
# define BOOST_PP_WHILE_769_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_770, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(770, s))
# define BOOST_PP_WHILE_770_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_771, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(771, s))
# define BOOST_PP_WHILE_771_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_772, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(772, s))
# define BOOST_PP_WHILE_772_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_773, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(773, s))
# define BOOST_PP_WHILE_773_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_774, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(774, s))
# define BOOST_PP_WHILE_774_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_775, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(775, s))
# define BOOST_PP_WHILE_775_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_776, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(776, s))
# define BOOST_PP_WHILE_776_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_777, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(777, s))
# define BOOST_PP_WHILE_777_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_778, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(778, s))
# define BOOST_PP_WHILE_778_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_779, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(779, s))
# define BOOST_PP_WHILE_779_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_780, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(780, s))
# define BOOST_PP_WHILE_780_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_781, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(781, s))
# define BOOST_PP_WHILE_781_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_782, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(782, s))
# define BOOST_PP_WHILE_782_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_783, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(783, s))
# define BOOST_PP_WHILE_783_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_784, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(784, s))
# define BOOST_PP_WHILE_784_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_785, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(785, s))
# define BOOST_PP_WHILE_785_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_786, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(786, s))
# define BOOST_PP_WHILE_786_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_787, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(787, s))
# define BOOST_PP_WHILE_787_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_788, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(788, s))
# define BOOST_PP_WHILE_788_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_789, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(789, s))
# define BOOST_PP_WHILE_789_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_790, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(790, s))
# define BOOST_PP_WHILE_790_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_791, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(791, s))
# define BOOST_PP_WHILE_791_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_792, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(792, s))
# define BOOST_PP_WHILE_792_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_793, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(793, s))
# define BOOST_PP_WHILE_793_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_794, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(794, s))
# define BOOST_PP_WHILE_794_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_795, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(795, s))
# define BOOST_PP_WHILE_795_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_796, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(796, s))
# define BOOST_PP_WHILE_796_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_797, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(797, s))
# define BOOST_PP_WHILE_797_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_798, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(798, s))
# define BOOST_PP_WHILE_798_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_799, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(799, s))
# define BOOST_PP_WHILE_799_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_800, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(800, s))
# define BOOST_PP_WHILE_800_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_801, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(801, s))
# define BOOST_PP_WHILE_801_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_802, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(802, s))
# define BOOST_PP_WHILE_802_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_803, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(803, s))
# define BOOST_PP_WHILE_803_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_804, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(804, s))
# define BOOST_PP_WHILE_804_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_805, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(805, s))
# define BOOST_PP_WHILE_805_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_806, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(806, s))
# define BOOST_PP_WHILE_806_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_807, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(807, s))
# define BOOST_PP_WHILE_807_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_808, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(808, s))
# define BOOST_PP_WHILE_808_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_809, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(809, s))
# define BOOST_PP_WHILE_809_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_810, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(810, s))
# define BOOST_PP_WHILE_810_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_811, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(811, s))
# define BOOST_PP_WHILE_811_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_812, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(812, s))
# define BOOST_PP_WHILE_812_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_813, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(813, s))
# define BOOST_PP_WHILE_813_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_814, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(814, s))
# define BOOST_PP_WHILE_814_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_815, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(815, s))
# define BOOST_PP_WHILE_815_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_816, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(816, s))
# define BOOST_PP_WHILE_816_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_817, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(817, s))
# define BOOST_PP_WHILE_817_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_818, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(818, s))
# define BOOST_PP_WHILE_818_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_819, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(819, s))
# define BOOST_PP_WHILE_819_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_820, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(820, s))
# define BOOST_PP_WHILE_820_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_821, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(821, s))
# define BOOST_PP_WHILE_821_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_822, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(822, s))
# define BOOST_PP_WHILE_822_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_823, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(823, s))
# define BOOST_PP_WHILE_823_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_824, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(824, s))
# define BOOST_PP_WHILE_824_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_825, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(825, s))
# define BOOST_PP_WHILE_825_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_826, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(826, s))
# define BOOST_PP_WHILE_826_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_827, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(827, s))
# define BOOST_PP_WHILE_827_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_828, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(828, s))
# define BOOST_PP_WHILE_828_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_829, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(829, s))
# define BOOST_PP_WHILE_829_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_830, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(830, s))
# define BOOST_PP_WHILE_830_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_831, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(831, s))
# define BOOST_PP_WHILE_831_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_832, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(832, s))
# define BOOST_PP_WHILE_832_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_833, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(833, s))
# define BOOST_PP_WHILE_833_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_834, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(834, s))
# define BOOST_PP_WHILE_834_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_835, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(835, s))
# define BOOST_PP_WHILE_835_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_836, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(836, s))
# define BOOST_PP_WHILE_836_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_837, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(837, s))
# define BOOST_PP_WHILE_837_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_838, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(838, s))
# define BOOST_PP_WHILE_838_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_839, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(839, s))
# define BOOST_PP_WHILE_839_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_840, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(840, s))
# define BOOST_PP_WHILE_840_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_841, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(841, s))
# define BOOST_PP_WHILE_841_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_842, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(842, s))
# define BOOST_PP_WHILE_842_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_843, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(843, s))
# define BOOST_PP_WHILE_843_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_844, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(844, s))
# define BOOST_PP_WHILE_844_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_845, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(845, s))
# define BOOST_PP_WHILE_845_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_846, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(846, s))
# define BOOST_PP_WHILE_846_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_847, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(847, s))
# define BOOST_PP_WHILE_847_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_848, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(848, s))
# define BOOST_PP_WHILE_848_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_849, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(849, s))
# define BOOST_PP_WHILE_849_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_850, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(850, s))
# define BOOST_PP_WHILE_850_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_851, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(851, s))
# define BOOST_PP_WHILE_851_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_852, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(852, s))
# define BOOST_PP_WHILE_852_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_853, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(853, s))
# define BOOST_PP_WHILE_853_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_854, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(854, s))
# define BOOST_PP_WHILE_854_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_855, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(855, s))
# define BOOST_PP_WHILE_855_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_856, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(856, s))
# define BOOST_PP_WHILE_856_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_857, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(857, s))
# define BOOST_PP_WHILE_857_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_858, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(858, s))
# define BOOST_PP_WHILE_858_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_859, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(859, s))
# define BOOST_PP_WHILE_859_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_860, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(860, s))
# define BOOST_PP_WHILE_860_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_861, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(861, s))
# define BOOST_PP_WHILE_861_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_862, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(862, s))
# define BOOST_PP_WHILE_862_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_863, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(863, s))
# define BOOST_PP_WHILE_863_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_864, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(864, s))
# define BOOST_PP_WHILE_864_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_865, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(865, s))
# define BOOST_PP_WHILE_865_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_866, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(866, s))
# define BOOST_PP_WHILE_866_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_867, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(867, s))
# define BOOST_PP_WHILE_867_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_868, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(868, s))
# define BOOST_PP_WHILE_868_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_869, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(869, s))
# define BOOST_PP_WHILE_869_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_870, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(870, s))
# define BOOST_PP_WHILE_870_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_871, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(871, s))
# define BOOST_PP_WHILE_871_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_872, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(872, s))
# define BOOST_PP_WHILE_872_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_873, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(873, s))
# define BOOST_PP_WHILE_873_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_874, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(874, s))
# define BOOST_PP_WHILE_874_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_875, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(875, s))
# define BOOST_PP_WHILE_875_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_876, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(876, s))
# define BOOST_PP_WHILE_876_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_877, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(877, s))
# define BOOST_PP_WHILE_877_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_878, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(878, s))
# define BOOST_PP_WHILE_878_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_879, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(879, s))
# define BOOST_PP_WHILE_879_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_880, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(880, s))
# define BOOST_PP_WHILE_880_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_881, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(881, s))
# define BOOST_PP_WHILE_881_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_882, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(882, s))
# define BOOST_PP_WHILE_882_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_883, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(883, s))
# define BOOST_PP_WHILE_883_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_884, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(884, s))
# define BOOST_PP_WHILE_884_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_885, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(885, s))
# define BOOST_PP_WHILE_885_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_886, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(886, s))
# define BOOST_PP_WHILE_886_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_887, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(887, s))
# define BOOST_PP_WHILE_887_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_888, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(888, s))
# define BOOST_PP_WHILE_888_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_889, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(889, s))
# define BOOST_PP_WHILE_889_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_890, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(890, s))
# define BOOST_PP_WHILE_890_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_891, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(891, s))
# define BOOST_PP_WHILE_891_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_892, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(892, s))
# define BOOST_PP_WHILE_892_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_893, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(893, s))
# define BOOST_PP_WHILE_893_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_894, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(894, s))
# define BOOST_PP_WHILE_894_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_895, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(895, s))
# define BOOST_PP_WHILE_895_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_896, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(896, s))
# define BOOST_PP_WHILE_896_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_897, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(897, s))
# define BOOST_PP_WHILE_897_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_898, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(898, s))
# define BOOST_PP_WHILE_898_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_899, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(899, s))
# define BOOST_PP_WHILE_899_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_900, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(900, s))
# define BOOST_PP_WHILE_900_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_901, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(901, s))
# define BOOST_PP_WHILE_901_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_902, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(902, s))
# define BOOST_PP_WHILE_902_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_903, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(903, s))
# define BOOST_PP_WHILE_903_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_904, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(904, s))
# define BOOST_PP_WHILE_904_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_905, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(905, s))
# define BOOST_PP_WHILE_905_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_906, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(906, s))
# define BOOST_PP_WHILE_906_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_907, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(907, s))
# define BOOST_PP_WHILE_907_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_908, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(908, s))
# define BOOST_PP_WHILE_908_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_909, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(909, s))
# define BOOST_PP_WHILE_909_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_910, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(910, s))
# define BOOST_PP_WHILE_910_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_911, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(911, s))
# define BOOST_PP_WHILE_911_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_912, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(912, s))
# define BOOST_PP_WHILE_912_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_913, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(913, s))
# define BOOST_PP_WHILE_913_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_914, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(914, s))
# define BOOST_PP_WHILE_914_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_915, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(915, s))
# define BOOST_PP_WHILE_915_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_916, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(916, s))
# define BOOST_PP_WHILE_916_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_917, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(917, s))
# define BOOST_PP_WHILE_917_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_918, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(918, s))
# define BOOST_PP_WHILE_918_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_919, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(919, s))
# define BOOST_PP_WHILE_919_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_920, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(920, s))
# define BOOST_PP_WHILE_920_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_921, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(921, s))
# define BOOST_PP_WHILE_921_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_922, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(922, s))
# define BOOST_PP_WHILE_922_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_923, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(923, s))
# define BOOST_PP_WHILE_923_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_924, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(924, s))
# define BOOST_PP_WHILE_924_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_925, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(925, s))
# define BOOST_PP_WHILE_925_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_926, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(926, s))
# define BOOST_PP_WHILE_926_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_927, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(927, s))
# define BOOST_PP_WHILE_927_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_928, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(928, s))
# define BOOST_PP_WHILE_928_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_929, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(929, s))
# define BOOST_PP_WHILE_929_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_930, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(930, s))
# define BOOST_PP_WHILE_930_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_931, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(931, s))
# define BOOST_PP_WHILE_931_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_932, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(932, s))
# define BOOST_PP_WHILE_932_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_933, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(933, s))
# define BOOST_PP_WHILE_933_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_934, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(934, s))
# define BOOST_PP_WHILE_934_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_935, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(935, s))
# define BOOST_PP_WHILE_935_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_936, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(936, s))
# define BOOST_PP_WHILE_936_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_937, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(937, s))
# define BOOST_PP_WHILE_937_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_938, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(938, s))
# define BOOST_PP_WHILE_938_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_939, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(939, s))
# define BOOST_PP_WHILE_939_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_940, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(940, s))
# define BOOST_PP_WHILE_940_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_941, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(941, s))
# define BOOST_PP_WHILE_941_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_942, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(942, s))
# define BOOST_PP_WHILE_942_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_943, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(943, s))
# define BOOST_PP_WHILE_943_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_944, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(944, s))
# define BOOST_PP_WHILE_944_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_945, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(945, s))
# define BOOST_PP_WHILE_945_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_946, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(946, s))
# define BOOST_PP_WHILE_946_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_947, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(947, s))
# define BOOST_PP_WHILE_947_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_948, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(948, s))
# define BOOST_PP_WHILE_948_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_949, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(949, s))
# define BOOST_PP_WHILE_949_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_950, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(950, s))
# define BOOST_PP_WHILE_950_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_951, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(951, s))
# define BOOST_PP_WHILE_951_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_952, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(952, s))
# define BOOST_PP_WHILE_952_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_953, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(953, s))
# define BOOST_PP_WHILE_953_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_954, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(954, s))
# define BOOST_PP_WHILE_954_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_955, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(955, s))
# define BOOST_PP_WHILE_955_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_956, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(956, s))
# define BOOST_PP_WHILE_956_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_957, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(957, s))
# define BOOST_PP_WHILE_957_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_958, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(958, s))
# define BOOST_PP_WHILE_958_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_959, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(959, s))
# define BOOST_PP_WHILE_959_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_960, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(960, s))
# define BOOST_PP_WHILE_960_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_961, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(961, s))
# define BOOST_PP_WHILE_961_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_962, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(962, s))
# define BOOST_PP_WHILE_962_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_963, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(963, s))
# define BOOST_PP_WHILE_963_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_964, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(964, s))
# define BOOST_PP_WHILE_964_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_965, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(965, s))
# define BOOST_PP_WHILE_965_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_966, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(966, s))
# define BOOST_PP_WHILE_966_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_967, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(967, s))
# define BOOST_PP_WHILE_967_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_968, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(968, s))
# define BOOST_PP_WHILE_968_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_969, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(969, s))
# define BOOST_PP_WHILE_969_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_970, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(970, s))
# define BOOST_PP_WHILE_970_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_971, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(971, s))
# define BOOST_PP_WHILE_971_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_972, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(972, s))
# define BOOST_PP_WHILE_972_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_973, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(973, s))
# define BOOST_PP_WHILE_973_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_974, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(974, s))
# define BOOST_PP_WHILE_974_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_975, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(975, s))
# define BOOST_PP_WHILE_975_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_976, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(976, s))
# define BOOST_PP_WHILE_976_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_977, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(977, s))
# define BOOST_PP_WHILE_977_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_978, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(978, s))
# define BOOST_PP_WHILE_978_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_979, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(979, s))
# define BOOST_PP_WHILE_979_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_980, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(980, s))
# define BOOST_PP_WHILE_980_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_981, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(981, s))
# define BOOST_PP_WHILE_981_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_982, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(982, s))
# define BOOST_PP_WHILE_982_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_983, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(983, s))
# define BOOST_PP_WHILE_983_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_984, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(984, s))
# define BOOST_PP_WHILE_984_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_985, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(985, s))
# define BOOST_PP_WHILE_985_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_986, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(986, s))
# define BOOST_PP_WHILE_986_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_987, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(987, s))
# define BOOST_PP_WHILE_987_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_988, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(988, s))
# define BOOST_PP_WHILE_988_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_989, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(989, s))
# define BOOST_PP_WHILE_989_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_990, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(990, s))
# define BOOST_PP_WHILE_990_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_991, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(991, s))
# define BOOST_PP_WHILE_991_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_992, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(992, s))
# define BOOST_PP_WHILE_992_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_993, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(993, s))
# define BOOST_PP_WHILE_993_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_994, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(994, s))
# define BOOST_PP_WHILE_994_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_995, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(995, s))
# define BOOST_PP_WHILE_995_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_996, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(996, s))
# define BOOST_PP_WHILE_996_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_997, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(997, s))
# define BOOST_PP_WHILE_997_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_998, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(998, s))
# define BOOST_PP_WHILE_998_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_999, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(999, s))
# define BOOST_PP_WHILE_999_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_1000, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(1000, s))
# define BOOST_PP_WHILE_1000_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_1001, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(1001, s))
# define BOOST_PP_WHILE_1001_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_1002, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(1002, s))
# define BOOST_PP_WHILE_1002_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_1003, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(1003, s))
# define BOOST_PP_WHILE_1003_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_1004, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(1004, s))
# define BOOST_PP_WHILE_1004_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_1005, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(1005, s))
# define BOOST_PP_WHILE_1005_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_1006, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(1006, s))
# define BOOST_PP_WHILE_1006_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_1007, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(1007, s))
# define BOOST_PP_WHILE_1007_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_1008, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(1008, s))
# define BOOST_PP_WHILE_1008_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_1009, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(1009, s))
# define BOOST_PP_WHILE_1009_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_1010, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(1010, s))
# define BOOST_PP_WHILE_1010_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_1011, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(1011, s))
# define BOOST_PP_WHILE_1011_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_1012, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(1012, s))
# define BOOST_PP_WHILE_1012_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_1013, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(1013, s))
# define BOOST_PP_WHILE_1013_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_1014, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(1014, s))
# define BOOST_PP_WHILE_1014_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_1015, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(1015, s))
# define BOOST_PP_WHILE_1015_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_1016, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(1016, s))
# define BOOST_PP_WHILE_1016_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_1017, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(1017, s))
# define BOOST_PP_WHILE_1017_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_1018, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(1018, s))
# define BOOST_PP_WHILE_1018_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_1019, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(1019, s))
# define BOOST_PP_WHILE_1019_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_1020, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(1020, s))
# define BOOST_PP_WHILE_1020_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_1021, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(1021, s))
# define BOOST_PP_WHILE_1021_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_1022, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(1022, s))
# define BOOST_PP_WHILE_1022_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_1023, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(1023, s))
# define BOOST_PP_WHILE_1023_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_1024, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(1024, s))
# define BOOST_PP_WHILE_1024_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_1025, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(1025, s))
#
# endif
