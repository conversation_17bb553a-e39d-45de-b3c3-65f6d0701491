/*
Copyright Rene Rivera 2008-2015
Distributed under the Boost Software License, Version 1.0.
(See accompanying file LICENSE_1_0.txt or copy at
http://www.boost.org/LICENSE_1_0.txt)
*/

#if !defined(BOOST_PREDEF_COMPILER_H) || defined(BOOST_PREDEF_INTERNAL_GENERATE_TESTS)
#ifndef BOOST_PREDEF_COMPILER_H
#define BOOST_PREDEF_COMPILER_H
#endif

#include <boost/predef/compiler/borland.h>
#include <boost/predef/compiler/clang.h>
#include <boost/predef/compiler/comeau.h>
#include <boost/predef/compiler/compaq.h>
#include <boost/predef/compiler/diab.h>
#include <boost/predef/compiler/digitalmars.h>
#include <boost/predef/compiler/dignus.h>
#include <boost/predef/compiler/edg.h>
#include <boost/predef/compiler/ekopath.h>
#include <boost/predef/compiler/gcc_xml.h>
#include <boost/predef/compiler/gcc.h>
#include <boost/predef/compiler/greenhills.h>
#include <boost/predef/compiler/hp_acc.h>
#include <boost/predef/compiler/iar.h>
#include <boost/predef/compiler/ibm.h>
#include <boost/predef/compiler/intel.h>
#include <boost/predef/compiler/kai.h>
#include <boost/predef/compiler/llvm.h>
#include <boost/predef/compiler/metaware.h>
#include <boost/predef/compiler/metrowerks.h>
#include <boost/predef/compiler/microtec.h>
#include <boost/predef/compiler/mpw.h>
#include <boost/predef/compiler/nvcc.h>
#include <boost/predef/compiler/palm.h>
#include <boost/predef/compiler/pgi.h>
#include <boost/predef/compiler/sgi_mipspro.h>
#include <boost/predef/compiler/sunpro.h>
#include <boost/predef/compiler/tendra.h>
#include <boost/predef/compiler/visualc.h>
#include <boost/predef/compiler/watcom.h>

#endif
