//////////////////////////////////////////////////////////////////////////////
//
// (C) Copyright <PERSON>, <PERSON> 2009.
// (C) Copyright Ion <PERSON> 2009-2012.
// Distributed under the Boost Software License, Version 1.0.
// (See accompanying file LICENSE_1_0.txt or copy at
// http://www.boost.org/LICENSE_1_0.txt)
//
// See http://www.boost.org/libs/move for documentation.
//
//////////////////////////////////////////////////////////////////////////////

//! \file
//! A general library header that includes
//! the rest of top-level headers.

#ifndef BOOST_MOVE_MOVE_HPP
#define BOOST_MOVE_MOVE_HPP

#ifndef BOOST_CONFIG_HPP
#  include <boost/config.hpp>
#endif
#
#if defined(BOOST_HAS_PRAGMA_ONCE)
#  pragma once
#endif

#include <boost/move/detail/config_begin.hpp>
#include <boost/move/utility.hpp>
#include <boost/move/iterator.hpp>
#include <boost/move/traits.hpp>
#include <boost/move/algorithm.hpp>
#include <boost/move/detail/config_end.hpp>

#endif //#ifndef BOOST_MOVE_MOVE_HPP
