#ifndef BOOST_DESCRIBE_HPP_INCLUDED
#define BOOST_DESCRIBE_HPP_INCLUDED

// Copyright 2020 Peter Dimov
// Distributed under the Boost Software License, Version 1.0.
// https://www.boost.org/LICENSE_1_0.txt

#include <boost/describe/enumerators.hpp>
#include <boost/describe/bases.hpp>
#include <boost/describe/members.hpp>
#include <boost/describe/enum.hpp>
#include <boost/describe/class.hpp>
#include <boost/describe/modifiers.hpp>
#include <boost/describe/enum_to_string.hpp>
#include <boost/describe/enum_from_string.hpp>
#include <boost/describe/operators.hpp>
#include <boost/describe/descriptor_by_name.hpp>
#include <boost/describe/descriptor_by_pointer.hpp>
#include <boost/describe/modifier_description.hpp>

#endif // #ifndef BOOST_DESCRIBE_HPP_INCLUDED
