//
// ts/net.hpp
// ~~~~~~~~~~
//
// Copyright (c) 2003-2024 <PERSON> (chris at kohlhoff dot com)
//
// Distributed under the Boost Software License, Version 1.0. (See accompanying
// file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
//

#ifndef BOOST_ASIO_TS_NET_HPP
#define BOOST_ASIO_TS_NET_HPP

#if defined(_MSC_VER) && (_MSC_VER >= 1200)
# pragma once
#endif // defined(_MSC_VER) && (_MSC_VER >= 1200)

#include <boost/asio/ts/netfwd.hpp>
#include <boost/asio/ts/executor.hpp>
#include <boost/asio/ts/io_context.hpp>
#include <boost/asio/ts/timer.hpp>
#include <boost/asio/ts/buffer.hpp>
#include <boost/asio/ts/socket.hpp>
#include <boost/asio/ts/internet.hpp>

#endif // BOOST_ASIO_TS_NET_HPP
