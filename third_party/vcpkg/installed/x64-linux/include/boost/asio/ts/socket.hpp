//
// ts/socket.hpp
// ~~~~~~~~~~~~~
//
// Copyright (c) 2003-2024 <PERSON> (chris at kohlhoff dot com)
//
// Distributed under the Boost Software License, Version 1.0. (See accompanying
// file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
//

#ifndef BOOST_ASIO_TS_SOCKET_HPP
#define BOOST_ASIO_TS_SOCKET_HPP

#if defined(_MSC_VER) && (_MSC_VER >= 1200)
# pragma once
#endif // defined(_MSC_VER) && (_MSC_VER >= 1200)

#include <boost/asio/socket_base.hpp>
#include <boost/asio/basic_socket.hpp>
#include <boost/asio/basic_datagram_socket.hpp>
#include <boost/asio/basic_stream_socket.hpp>
#include <boost/asio/basic_socket_acceptor.hpp>
#include <boost/asio/basic_socket_streambuf.hpp>
#include <boost/asio/basic_socket_iostream.hpp>
#include <boost/asio/connect.hpp>

#endif // BOOST_ASIO_TS_SOCKET_HPP
