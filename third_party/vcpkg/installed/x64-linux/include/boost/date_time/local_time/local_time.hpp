#ifndef LOCAL_TIME_LOCAL_TIME_HPP__
#define LOCAL_TIME_LOCAL_TIME_HPP__

/* Copyright (c) 2003-2004 CrystalClear Software, Inc.
 * Subject to the Boost Software License, Version 1.0. 
 * (See accompanying file LICENSE_1_0.txt or http://www.boost.org/LICENSE_1_0.txt)
 * Author: <PERSON>, <PERSON>
 * $Date$
 */

#include "boost/date_time/posix_time/posix_time.hpp"
#include "boost/date_time/local_time/local_date_time.hpp"
#include "boost/date_time/local_time/local_time_types.hpp"
#if !defined(USE_DATE_TIME_PRE_1_33_FACET_IO)
#include "boost/date_time/local_time/local_time_io.hpp"
#endif // USE_DATE_TIME_PRE_1_33_FACET_IO
#include "boost/date_time/local_time/posix_time_zone.hpp"
#include "boost/date_time/local_time/custom_time_zone.hpp"
#include "boost/date_time/local_time/tz_database.hpp"
#include "boost/date_time/local_time/conversion.hpp"
#include "boost/date_time/time_zone_base.hpp"


#endif
