#ifndef BOOST_DATE_TIME_ALL_HPP___
#define BOOST_DATE_TIME_ALL_HPP___

/* Copyright (c) 2006 CrystalClear Software, Inc.
 * Use, modification and distribution is subject to the 
 * Boost Software License, Version 1.0. (See accompanying
 * file LICENSE_1_0.txt or http://www.boost.org/LICENSE_1_0.txt)
 * Author: <PERSON>
 * $Date$
 */
 
 //  See www.boost.org/libs/date_time for documentation.

//gregorian and posix time included by indirectly
#include "boost/date_time/local_time/local_time.hpp"

#endif // BOOST_DATE_TIME_ALL_HPP___
