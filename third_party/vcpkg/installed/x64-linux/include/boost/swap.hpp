/*
 * Copyright (c) 2014 <PERSON>
 *
 * Distributed under the Boost Software License, Version 1.0. (See
 * accompanying file LICENSE_1_0.txt or copy at
 * http://www.boost.org/LICENSE_1_0.txt)
 */

#ifndef BOOST_SWAP_HPP
#define BOOST_SWAP_HPP

// The header file at this path is deprecated;
// use boost/core/invoke_swap.hpp instead.

#include <boost/config/header_deprecated.hpp>
#include <boost/core/swap.hpp>

BOOST_HEADER_DEPRECATED("boost/core/invoke_swap.hpp")

#endif
