/*=============================================================================
    Copyright (c) 2006-2007 <PERSON>er
  
    Use modification and distribution are subject to the Boost Software 
    License, Version 1.0. (See accompanying file LICENSE_1_0.txt or copy at
    http://www.boost.org/LICENSE_1_0.txt).
==============================================================================*/

#if !defined(BOOST_FUSION_FUNCTIONAL_HPP_INCLUDED)
#define BOOST_FUSION_FUNCTIONAL_HPP_INCLUDED

#include <boost/fusion/support/config.hpp>
#include <boost/fusion/functional/invocation.hpp>
#include <boost/fusion/functional/adapter.hpp>
#include <boost/fusion/functional/generation.hpp>

#endif

