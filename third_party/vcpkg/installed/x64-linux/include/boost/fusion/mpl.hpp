/*=============================================================================
    Copyright (c) 2001-2011 <PERSON>

    Distributed under the Boost Software License, Version 1.0. (See accompanying
    file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
==============================================================================*/
#if !defined(FUSION_MPL_09172006_2049)
#define FUSION_MPL_09172006_2049

// The fusion <--> MPL link headers
#include <boost/fusion/iterator/mpl.hpp>
#include <boost/fusion/adapted/mpl.hpp>

#include <boost/fusion/mpl/at.hpp>
#include <boost/fusion/mpl/back.hpp>
#include <boost/fusion/mpl/begin.hpp>
#include <boost/fusion/mpl/clear.hpp>
#include <boost/fusion/mpl/empty.hpp>
#include <boost/fusion/mpl/end.hpp>
#include <boost/fusion/mpl/erase.hpp>
#include <boost/fusion/mpl/erase_key.hpp>
#include <boost/fusion/mpl/front.hpp>
#include <boost/fusion/mpl/has_key.hpp>
#include <boost/fusion/mpl/insert.hpp>
#include <boost/fusion/mpl/insert_range.hpp>
#include <boost/fusion/mpl/pop_back.hpp>
#include <boost/fusion/mpl/pop_front.hpp>
#include <boost/fusion/mpl/push_back.hpp>
#include <boost/fusion/mpl/push_front.hpp>
#include <boost/fusion/mpl/size.hpp>

#endif
