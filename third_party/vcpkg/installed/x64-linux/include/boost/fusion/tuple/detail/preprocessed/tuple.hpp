/*=============================================================================
    Copyright (c) 2001-2011 <PERSON>

    Distributed under the Boost Software License, Version 1.0. (See accompanying
    file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

    This is an auto-generated file. Do not edit!
==============================================================================*/
#if FUSION_MAX_VECTOR_SIZE <= 10
#include <boost/fusion/tuple/detail/preprocessed/tuple10.hpp>
#elif FUSION_MAX_VECTOR_SIZE <= 20
#include <boost/fusion/tuple/detail/preprocessed/tuple20.hpp>
#elif FUSION_MAX_VECTOR_SIZE <= 30
#include <boost/fusion/tuple/detail/preprocessed/tuple30.hpp>
#elif FUSION_MAX_VECTOR_SIZE <= 40
#include <boost/fusion/tuple/detail/preprocessed/tuple40.hpp>
#elif FUSION_MAX_VECTOR_SIZE <= 50
#include <boost/fusion/tuple/detail/preprocessed/tuple50.hpp>
#else
#error "FUSION_MAX_VECTOR_SIZE out of bounds for preprocessed headers"
#endif

