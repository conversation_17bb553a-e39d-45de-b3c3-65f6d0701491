// Copyright (C) 2003, <PERSON>.
// Copyright (C) 2007, <PERSON>.
//
// Use, modification, and distribution is subject to the Boost Software
// License, Version 1.0. (See accompanying file LICENSE_1_0.txt or copy at
// http://www.boost.org/LICENSE_1_0.txt)
//
// See http://www.boost.org/libs/optional for documentation.
//
// You are welcome to contact the author at:
//  <EMAIL>
//
#ifndef BOOST_UTILITY_DETAIL_INPLACE_FACTORY_SUFFIX_04APR2007_HPP
#define BOOST_UTILITY_DETAIL_INPLACE_FACTORY_SUFFIX_04APR2007_HPP

#undef BOOST_DEFINE_INPLACE_FACTORY_CLASS_MEMBER_INIT
#undef BOOST_DEFINE_INPLACE_FACTORY_CLASS_MEMBER_DECL
#undef BOOST_MAX_INPLACE_FACTORY_ARITY

#undef BOOST_UTILITY_DETAIL_INPLACE_FACTORY_PREFIX_04APR2007_HPP

#endif

