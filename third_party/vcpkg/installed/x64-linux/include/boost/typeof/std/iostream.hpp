// Copyright (C) 2005 Ark<PERSON><PERSON>, <PERSON><PERSON><PERSON>.
// Use, modification and distribution is subject to the Boost Software
// License, Version 1.0. (http://www.boost.org/LICENSE_1_0.txt)

#ifndef BOOST_TYPEOF_STD_iostream_hpp_INCLUDED
#define BOOST_TYPEOF_STD_iostream_hpp_INCLUDED

// This header is no longer useful and is only retained for compatibility

#include <iostream>
#include <boost/typeof/typeof.hpp>
#include <boost/typeof/std/string.hpp>

#endif//BOOST_TYPEOF_STD_iostream_hpp_INCLUDED
