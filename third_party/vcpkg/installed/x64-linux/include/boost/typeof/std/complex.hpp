// Copyright (C) 2005 <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>.
// Use, modification and distribution is subject to the Boost Software
// License, Version 1.0. (http://www.boost.org/LICENSE_1_0.txt)

#ifndef BOOST_TYPEOF_STD_complex_hpp_INCLUDED
#define BOOST_TYPEOF_STD_complex_hpp_INCLUDED

// This header is no longer useful and is only retained for compatibility

#include <complex>
#include <boost/typeof/typeof.hpp>

#endif//BOOST_TYPEOF_STD_complex_hpp_INCLUDED
