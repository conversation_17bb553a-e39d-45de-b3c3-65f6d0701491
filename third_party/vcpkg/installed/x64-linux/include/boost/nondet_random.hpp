/* boost nondet_random.hpp header file
 *
 * Copyright Jens Maurer 2000
 * Distributed under the Boost Software License, Version 1.0. (See
 * accompanying file LICENSE_1_0.txt or copy at
 * http://www.boost.org/LICENSE_1_0.txt)
 *
 * $Id$
 *
 * Revision history
 *  2000-02-18  Portability fixes (thanks to <PERSON><PERSON>)
 */

//  See http://www.boost.org/libs/random for documentation.


#ifndef BOOST_NONDET_RANDOM_HPP
#define BOOST_NONDET_RANDOM_HPP

#include <boost/random/random_device.hpp>

#endif /* BOOST_NONDET_RANDOM_HPP */
