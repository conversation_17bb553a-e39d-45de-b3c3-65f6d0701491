//  ratio.hpp  ---------------------------------------------------------------//

//  Copyright 2008 <PERSON>
//  Copyright 2008 <PERSON><PERSON>
//  Copyright 2009 Vicente <PERSON> Bo<PERSON>t Escriba

//  Distributed under the Boost Software License, Version 1.0.
//  See http://www.boost.org/LICENSE_1_0.txt

/*

This code was derived by <PERSON><PERSON> from <PERSON>'s time2_demo prototype.
Many thanks to <PERSON> for making his code available under the Boost license.
The original code was modified to conform to Boost conventions and to section
20.4 Compile-time rational arithmetic [ratio], of the C++ committee working
paper N2798.
See http://www.open-std.org/jtc1/sc22/wg21/docs/papers/2008/n2798.pdf.

time2_demo contained this comment:

    Much thanks to <PERSON>,
                   <PERSON>,
                   <PERSON>,
                   <PERSON>,
                   <PERSON>,
                   <PERSON>,
                   <PERSON>.
*/

// The way overflow is managed for ratio_less is taken from llvm/libcxx/include/ratio

#ifndef BOOST_RATIO_RATIO_HPP
#define BOOST_RATIO_RATIO_HPP

#include <boost/ratio/ratio_fwd.hpp>
#include <boost/ratio/detail/gcd_lcm.hpp>

namespace boost
{

// extension used by Chrono

template <class R1, class R2> using ratio_gcd = typename ratio<
    ratio_detail::gcd<R1::num, R2::num>::value,
    ratio_detail::lcm<R1::den, R2::den>::value>::type;

}  // namespace boost

#endif  // BOOST_RATIO_RATIO_HPP
