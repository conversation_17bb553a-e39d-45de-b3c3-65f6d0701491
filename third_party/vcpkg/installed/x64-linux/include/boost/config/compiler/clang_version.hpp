// Copyright 2021 Peter Dimov
// Distributed under the Boost Software License, Version 1.0.
// https://www.boost.org/LICENSE_1_0.txt)

#if !defined(__apple_build_version__)

# define BOOST_CLANG_VERSION (__clang_major__ * 10000 + __clang_minor__ * 100 + __clang_patchlevel__ % 100)

#else
# define BOOST_CLANG_REPORTED_VERSION (__clang_major__ * 10000 + __clang_minor__ * 100 + __clang_patchlevel__ % 100)

// https://en.wikipedia.org/wiki/Xcode#Toolchain_versions

# if BOOST_CLANG_REPORTED_VERSION >= 150000
#   define BOOST_CLANG_VERSION 160000

# elif BOOST_CLANG_REPORTED_VERSION >= 140003
#   define BOOST_CLANG_VERSION 150000

# elif BOOST_CLANG_REPORTED_VERSION >= 140000
#   define BOOST_CLANG_VERSION 140000

# elif BOOST_CLANG_REPORTED_VERSION >= 130100
#   define BOOST_CLANG_VERSION 130000

# elif BOOST_CLANG_REPORTED_VERSION >= 130000
#   define BOOST_CLANG_VERSION 120000

# elif BOOST_CLANG_REPORTED_VERSION >= 120005
#   define BOOST_CLANG_VERSION 110100

# elif BOOST_CLANG_REPORTED_VERSION >= 120000
#   define BOOST_CLANG_VERSION 100000

# elif BOOST_CLANG_REPORTED_VERSION >= 110003
#   define BOOST_CLANG_VERSION 90000

# elif BOOST_CLANG_REPORTED_VERSION >= 110000
#   define BOOST_CLANG_VERSION 80000

# elif BOOST_CLANG_REPORTED_VERSION >= 100001
#   define BOOST_CLANG_VERSION 70000

# elif BOOST_CLANG_REPORTED_VERSION >= 100000
#   define BOOST_CLANG_VERSION 60001

# elif BOOST_CLANG_REPORTED_VERSION >= 90100
#   define BOOST_CLANG_VERSION 50002

# elif BOOST_CLANG_REPORTED_VERSION >= 90000
#   define BOOST_CLANG_VERSION 40000

# elif BOOST_CLANG_REPORTED_VERSION >= 80000
#   define BOOST_CLANG_VERSION 30900

# elif BOOST_CLANG_REPORTED_VERSION >= 70300
#   define BOOST_CLANG_VERSION 30800

# elif BOOST_CLANG_REPORTED_VERSION >= 70000
#   define BOOST_CLANG_VERSION 30700

# elif BOOST_CLANG_REPORTED_VERSION >= 60100
#   define BOOST_CLANG_VERSION 30600

# elif BOOST_CLANG_REPORTED_VERSION >= 60000
#   define BOOST_CLANG_VERSION 30500

# elif BOOST_CLANG_REPORTED_VERSION >= 50100
#   define BOOST_CLANG_VERSION 30400

# elif BOOST_CLANG_REPORTED_VERSION >= 50000
#   define BOOST_CLANG_VERSION 30300

# elif BOOST_CLANG_REPORTED_VERSION >= 40200
#   define BOOST_CLANG_VERSION 30200

# elif BOOST_CLANG_REPORTED_VERSION >= 30100
#   define BOOST_CLANG_VERSION 30100

# elif BOOST_CLANG_REPORTED_VERSION >= 20100
#   define BOOST_CLANG_VERSION 30000

# else
#   define BOOST_CLANG_VERSION 20900

# endif

# undef BOOST_CLANG_REPORTED_VERSION
#endif
