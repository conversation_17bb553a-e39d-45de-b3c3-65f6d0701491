//  This file was automatically generated on Fri Oct 13 19:09:38 2023
//  by libs/config/tools/generate.cpp
//  Copyright <PERSON> 2002-21.
//  Use, modification and distribution are subject to the 
//  Boost Software License, Version 1.0. (See accompanying file 
//  LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

//  See http://www.boost.org/libs/config for the most recent version.//
//  Revision $Id$
//

#include <boost/config.hpp>

#ifdef BOOST_NO_ADL_BARRIER
#  error "Your compiler appears not to be fully C++03 compliant.  Detected via defect macro BOOST_NO_ADL_BARRIER."
#endif
#ifdef BOOST_NO_ARGUMENT_DEPENDENT_LOOKUP
#  error "Your compiler appears not to be fully C++03 compliant.  Detected via defect macro BOOST_NO_ARGUMENT_DEPENDENT_LOOKUP."
#endif
#ifdef BOOST_NO_ARRAY_TYPE_SPECIALIZATIONS
#  error "Your compiler appears not to be fully C++03 compliant.  Detected via defect macro BOOST_NO_ARRAY_TYPE_SPECIALIZATIONS."
#endif
#ifdef BOOST_NO_COMPLETE_VALUE_INITIALIZATION
#  error "Your compiler appears not to be fully C++03 compliant.  Detected via defect macro BOOST_NO_COMPLETE_VALUE_INITIALIZATION."
#endif
#ifdef BOOST_NO_CTYPE_FUNCTIONS
#  error "Your compiler appears not to be fully C++03 compliant.  Detected via defect macro BOOST_NO_CTYPE_FUNCTIONS."
#endif
#ifdef BOOST_NO_CV_SPECIALIZATIONS
#  error "Your compiler appears not to be fully C++03 compliant.  Detected via defect macro BOOST_NO_CV_SPECIALIZATIONS."
#endif
#ifdef BOOST_NO_CV_VOID_SPECIALIZATIONS
#  error "Your compiler appears not to be fully C++03 compliant.  Detected via defect macro BOOST_NO_CV_VOID_SPECIALIZATIONS."
#endif
#ifdef BOOST_NO_CWCHAR
#  error "Your compiler appears not to be fully C++03 compliant.  Detected via defect macro BOOST_NO_CWCHAR."
#endif
#ifdef BOOST_NO_CWCTYPE
#  error "Your compiler appears not to be fully C++03 compliant.  Detected via defect macro BOOST_NO_CWCTYPE."
#endif
#ifdef BOOST_NO_DEPENDENT_NESTED_DERIVATIONS
#  error "Your compiler appears not to be fully C++03 compliant.  Detected via defect macro BOOST_NO_DEPENDENT_NESTED_DERIVATIONS."
#endif
#ifdef BOOST_NO_DEPENDENT_TYPES_IN_TEMPLATE_VALUE_PARAMETERS
#  error "Your compiler appears not to be fully C++03 compliant.  Detected via defect macro BOOST_NO_DEPENDENT_TYPES_IN_TEMPLATE_VALUE_PARAMETERS."
#endif
#ifdef BOOST_NO_EXCEPTIONS
#  error "Your compiler appears not to be fully C++03 compliant.  Detected via defect macro BOOST_NO_EXCEPTIONS."
#endif
#ifdef BOOST_NO_EXCEPTION_STD_NAMESPACE
#  error "Your compiler appears not to be fully C++03 compliant.  Detected via defect macro BOOST_NO_EXCEPTION_STD_NAMESPACE."
#endif
#ifdef BOOST_NO_EXPLICIT_FUNCTION_TEMPLATE_ARGUMENTS
#  error "Your compiler appears not to be fully C++03 compliant.  Detected via defect macro BOOST_NO_EXPLICIT_FUNCTION_TEMPLATE_ARGUMENTS."
#endif
#ifdef BOOST_NO_FENV_H
#  error "Your compiler appears not to be fully C++03 compliant.  Detected via defect macro BOOST_NO_FENV_H."
#endif
#ifdef BOOST_NO_FUNCTION_TEMPLATE_ORDERING
#  error "Your compiler appears not to be fully C++03 compliant.  Detected via defect macro BOOST_NO_FUNCTION_TEMPLATE_ORDERING."
#endif
#ifdef BOOST_NO_FUNCTION_TYPE_SPECIALIZATIONS
#  error "Your compiler appears not to be fully C++03 compliant.  Detected via defect macro BOOST_NO_FUNCTION_TYPE_SPECIALIZATIONS."
#endif
#ifdef BOOST_NO_INCLASS_MEMBER_INITIALIZATION
#  error "Your compiler appears not to be fully C++03 compliant.  Detected via defect macro BOOST_NO_INCLASS_MEMBER_INITIALIZATION."
#endif
#ifdef BOOST_NO_INTEGRAL_INT64_T
#  error "Your compiler appears not to be fully C++03 compliant.  Detected via defect macro BOOST_NO_INTEGRAL_INT64_T."
#endif
#ifdef BOOST_NO_INTRINSIC_WCHAR_T
#  error "Your compiler appears not to be fully C++03 compliant.  Detected via defect macro BOOST_NO_INTRINSIC_WCHAR_T."
#endif
#ifdef BOOST_NO_IOSFWD
#  error "Your compiler appears not to be fully C++03 compliant.  Detected via defect macro BOOST_NO_IOSFWD."
#endif
#ifdef BOOST_NO_IOSTREAM
#  error "Your compiler appears not to be fully C++03 compliant.  Detected via defect macro BOOST_NO_IOSTREAM."
#endif
#ifdef BOOST_NO_IS_ABSTRACT
#  error "Your compiler appears not to be fully C++03 compliant.  Detected via defect macro BOOST_NO_IS_ABSTRACT."
#endif
#ifdef BOOST_NO_LIMITS
#  error "Your compiler appears not to be fully C++03 compliant.  Detected via defect macro BOOST_NO_LIMITS."
#endif
#ifdef BOOST_NO_LIMITS_COMPILE_TIME_CONSTANTS
#  error "Your compiler appears not to be fully C++03 compliant.  Detected via defect macro BOOST_NO_LIMITS_COMPILE_TIME_CONSTANTS."
#endif
#ifdef BOOST_NO_LONG_LONG
#  error "Your compiler appears not to be fully C++03 compliant.  Detected via defect macro BOOST_NO_LONG_LONG."
#endif
#ifdef BOOST_NO_LONG_LONG_NUMERIC_LIMITS
#  error "Your compiler appears not to be fully C++03 compliant.  Detected via defect macro BOOST_NO_LONG_LONG_NUMERIC_LIMITS."
#endif
#ifdef BOOST_NO_MEMBER_FUNCTION_SPECIALIZATIONS
#  error "Your compiler appears not to be fully C++03 compliant.  Detected via defect macro BOOST_NO_MEMBER_FUNCTION_SPECIALIZATIONS."
#endif
#ifdef BOOST_NO_MEMBER_TEMPLATES
#  error "Your compiler appears not to be fully C++03 compliant.  Detected via defect macro BOOST_NO_MEMBER_TEMPLATES."
#endif
#ifdef BOOST_NO_MEMBER_TEMPLATE_FRIENDS
#  error "Your compiler appears not to be fully C++03 compliant.  Detected via defect macro BOOST_NO_MEMBER_TEMPLATE_FRIENDS."
#endif
#ifdef BOOST_NO_MEMBER_TEMPLATE_KEYWORD
#  error "Your compiler appears not to be fully C++03 compliant.  Detected via defect macro BOOST_NO_MEMBER_TEMPLATE_KEYWORD."
#endif
#ifdef BOOST_NO_NESTED_FRIENDSHIP
#  error "Your compiler appears not to be fully C++03 compliant.  Detected via defect macro BOOST_NO_NESTED_FRIENDSHIP."
#endif
#ifdef BOOST_NO_OPERATORS_IN_NAMESPACE
#  error "Your compiler appears not to be fully C++03 compliant.  Detected via defect macro BOOST_NO_OPERATORS_IN_NAMESPACE."
#endif
#ifdef BOOST_NO_PARTIAL_SPECIALIZATION_IMPLICIT_DEFAULT_ARGS
#  error "Your compiler appears not to be fully C++03 compliant.  Detected via defect macro BOOST_NO_PARTIAL_SPECIALIZATION_IMPLICIT_DEFAULT_ARGS."
#endif
#ifdef BOOST_NO_POINTER_TO_MEMBER_CONST
#  error "Your compiler appears not to be fully C++03 compliant.  Detected via defect macro BOOST_NO_POINTER_TO_MEMBER_CONST."
#endif
#ifdef BOOST_NO_POINTER_TO_MEMBER_TEMPLATE_PARAMETERS
#  error "Your compiler appears not to be fully C++03 compliant.  Detected via defect macro BOOST_NO_POINTER_TO_MEMBER_TEMPLATE_PARAMETERS."
#endif
#ifdef BOOST_NO_PRIVATE_IN_AGGREGATE
#  error "Your compiler appears not to be fully C++03 compliant.  Detected via defect macro BOOST_NO_PRIVATE_IN_AGGREGATE."
#endif
#ifdef BOOST_NO_RESTRICT_REFERENCES
#  error "Your compiler appears not to be fully C++03 compliant.  Detected via defect macro BOOST_NO_RESTRICT_REFERENCES."
#endif
#ifdef BOOST_NO_RTTI
#  error "Your compiler appears not to be fully C++03 compliant.  Detected via defect macro BOOST_NO_RTTI."
#endif
#ifdef BOOST_NO_SFINAE
#  error "Your compiler appears not to be fully C++03 compliant.  Detected via defect macro BOOST_NO_SFINAE."
#endif
#ifdef BOOST_NO_SFINAE_EXPR
#  error "Your compiler appears not to be fully C++03 compliant.  Detected via defect macro BOOST_NO_SFINAE_EXPR."
#endif
#ifdef BOOST_NO_STDC_NAMESPACE
#  error "Your compiler appears not to be fully C++03 compliant.  Detected via defect macro BOOST_NO_STDC_NAMESPACE."
#endif
#ifdef BOOST_NO_STD_ALLOCATOR
#  error "Your compiler appears not to be fully C++03 compliant.  Detected via defect macro BOOST_NO_STD_ALLOCATOR."
#endif
#ifdef BOOST_NO_STD_DISTANCE
#  error "Your compiler appears not to be fully C++03 compliant.  Detected via defect macro BOOST_NO_STD_DISTANCE."
#endif
#ifdef BOOST_NO_STD_ITERATOR
#  error "Your compiler appears not to be fully C++03 compliant.  Detected via defect macro BOOST_NO_STD_ITERATOR."
#endif
#ifdef BOOST_NO_STD_ITERATOR_TRAITS
#  error "Your compiler appears not to be fully C++03 compliant.  Detected via defect macro BOOST_NO_STD_ITERATOR_TRAITS."
#endif
#ifdef BOOST_NO_STD_LOCALE
#  error "Your compiler appears not to be fully C++03 compliant.  Detected via defect macro BOOST_NO_STD_LOCALE."
#endif
#ifdef BOOST_NO_STD_MESSAGES
#  error "Your compiler appears not to be fully C++03 compliant.  Detected via defect macro BOOST_NO_STD_MESSAGES."
#endif
#ifdef BOOST_NO_STD_MIN_MAX
#  error "Your compiler appears not to be fully C++03 compliant.  Detected via defect macro BOOST_NO_STD_MIN_MAX."
#endif
#ifdef BOOST_NO_STD_OUTPUT_ITERATOR_ASSIGN
#  error "Your compiler appears not to be fully C++03 compliant.  Detected via defect macro BOOST_NO_STD_OUTPUT_ITERATOR_ASSIGN."
#endif
#ifdef BOOST_NO_STD_TYPEINFO
#  error "Your compiler appears not to be fully C++03 compliant.  Detected via defect macro BOOST_NO_STD_TYPEINFO."
#endif
#ifdef BOOST_NO_STD_USE_FACET
#  error "Your compiler appears not to be fully C++03 compliant.  Detected via defect macro BOOST_NO_STD_USE_FACET."
#endif
#ifdef BOOST_NO_STD_WSTREAMBUF
#  error "Your compiler appears not to be fully C++03 compliant.  Detected via defect macro BOOST_NO_STD_WSTREAMBUF."
#endif
#ifdef BOOST_NO_STD_WSTRING
#  error "Your compiler appears not to be fully C++03 compliant.  Detected via defect macro BOOST_NO_STD_WSTRING."
#endif
#ifdef BOOST_NO_STRINGSTREAM
#  error "Your compiler appears not to be fully C++03 compliant.  Detected via defect macro BOOST_NO_STRINGSTREAM."
#endif
#ifdef BOOST_NO_TEMPLATED_IOSTREAMS
#  error "Your compiler appears not to be fully C++03 compliant.  Detected via defect macro BOOST_NO_TEMPLATED_IOSTREAMS."
#endif
#ifdef BOOST_NO_TEMPLATED_ITERATOR_CONSTRUCTORS
#  error "Your compiler appears not to be fully C++03 compliant.  Detected via defect macro BOOST_NO_TEMPLATED_ITERATOR_CONSTRUCTORS."
#endif
#ifdef BOOST_NO_TEMPLATE_PARTIAL_SPECIALIZATION
#  error "Your compiler appears not to be fully C++03 compliant.  Detected via defect macro BOOST_NO_TEMPLATE_PARTIAL_SPECIALIZATION."
#endif
#ifdef BOOST_NO_TEMPLATE_TEMPLATES
#  error "Your compiler appears not to be fully C++03 compliant.  Detected via defect macro BOOST_NO_TEMPLATE_TEMPLATES."
#endif
#ifdef BOOST_NO_TWO_PHASE_NAME_LOOKUP
#  error "Your compiler appears not to be fully C++03 compliant.  Detected via defect macro BOOST_NO_TWO_PHASE_NAME_LOOKUP."
#endif
#ifdef BOOST_NO_TYPEID
#  error "Your compiler appears not to be fully C++03 compliant.  Detected via defect macro BOOST_NO_TYPEID."
#endif
#ifdef BOOST_NO_TYPENAME_WITH_CTOR
#  error "Your compiler appears not to be fully C++03 compliant.  Detected via defect macro BOOST_NO_TYPENAME_WITH_CTOR."
#endif
#ifdef BOOST_NO_UNREACHABLE_RETURN_DETECTION
#  error "Your compiler appears not to be fully C++03 compliant.  Detected via defect macro BOOST_NO_UNREACHABLE_RETURN_DETECTION."
#endif
#ifdef BOOST_NO_USING_DECLARATION_OVERLOADS_FROM_TYPENAME_BASE
#  error "Your compiler appears not to be fully C++03 compliant.  Detected via defect macro BOOST_NO_USING_DECLARATION_OVERLOADS_FROM_TYPENAME_BASE."
#endif
#ifdef BOOST_NO_USING_TEMPLATE
#  error "Your compiler appears not to be fully C++03 compliant.  Detected via defect macro BOOST_NO_USING_TEMPLATE."
#endif
#ifdef BOOST_NO_VOID_RETURNS
#  error "Your compiler appears not to be fully C++03 compliant.  Detected via defect macro BOOST_NO_VOID_RETURNS."
#endif
