/*
 * Copyright (c) 2014 <PERSON><PERSON>
 *
 * Distributed under the Boost Software License, Version 1.0. (See
 * accompanying file LICENSE_1_0.txt or copy at
 * http://www.boost.org/LICENSE_1_0.txt)
 */

#ifndef BOOST_DETAIL_SCOPED_ENUM_EMULATION_HPP
#define BOOST_DETAIL_SCOPED_ENUM_EMULATION_HPP

// The header file at this path is deprecated;
// use boost/core/scoped_enum.hpp instead.

#include <boost/config/header_deprecated.hpp>

BOOST_HEADER_DEPRECATED("<boost/core/scoped_enum.hpp>")

#include <boost/core/scoped_enum.hpp>

#endif
