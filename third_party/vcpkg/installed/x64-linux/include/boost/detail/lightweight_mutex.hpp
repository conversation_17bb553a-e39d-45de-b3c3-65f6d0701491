#ifndef BOOST_DETAIL_LIGHTWEIGHT_MUTEX_HPP_INCLUDED
#define BOOST_DETAIL_LIGHTWEIGHT_MUTEX_HPP_INCLUDED

// Copyright (c) 2002, 2003 Peter <PERSON> and Multi Media Ltd.
// Distributed under the Boost Software License, Version 1.0.
// https://www.boost.org/LICENSE_1_0.txt

#include <boost/config/header_deprecated.hpp>

BOOST_HEADER_DEPRECATED("<boost/smart_ptr/detail/lightweight_mutex.hpp>")

#include <boost/smart_ptr/detail/lightweight_mutex.hpp>

#endif // #ifndef BOOST_DETAIL_LIGHTWEIGHT_MUTEX_HPP_INCLUDED
