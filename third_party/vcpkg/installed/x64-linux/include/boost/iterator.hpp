//  (C) Copyright <PERSON><PERSON> 2000. Distributed under the Boost
//  Software License, Version 1.0. (See accompanying file
//  LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

#ifndef BOOST_ITERATOR_HPP
#define BOOST_ITERATOR_HPP

#include <boost/config/header_deprecated.hpp>

BOOST_HEADER_DEPRECATED("<iterator>")

#include <iterator>
#include <cstddef>           // std::ptrdiff_t

namespace boost
{

using std::iterator;

} // namespace boost

#endif // BOOST_ITERATOR_HPP
