{"$schema": "https://raw.githubusercontent.com/spdx/spdx-spec/v2.2.1/schemas/spdx-schema.json", "spdxVersion": "SPDX-2.2", "dataLicense": "CC0-1.0", "SPDXID": "SPDXRef-DOCUMENT", "documentNamespace": "https://spdx.org/spdxdocs/boost-cmake-x64-linux-1.87.0-b04b970e-235d-439d-a2fd-b91999bc972b", "name": "boost-cmake:x64-linux@1.87.0 cbe23a80263643a95f4366123c43fb9d07e4b7c0eee36f9c233ce7e6f77856b8", "creationInfo": {"creators": ["Tool: vcpkg-2025-04-07-b8b513ba8778c918cff49c3e837aae5999d5d2aa"], "created": "2025-06-10T09:04:15Z"}, "relationships": [{"spdxElementId": "SPDXRef-port", "relationshipType": "GENERATES", "relatedSpdxElement": "SPDXRef-binary"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-0"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-1"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-2"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-3"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-4"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-5"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-6"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-7"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-8"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-9"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-10"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-11"}, {"spdxElementId": "SPDXRef-binary", "relationshipType": "GENERATED_FROM", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-0", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-1", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-2", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-3", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-4", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-5", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-5", "relationshipType": "DEPENDENCY_MANIFEST_OF", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-6", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-7", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-8", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-9", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-10", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-11", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}], "packages": [{"name": "boost-cmake", "SPDXID": "SPDXRef-port", "versionInfo": "1.87.0", "downloadLocation": "git+https://github.com/Microsoft/vcpkg#ports/boost-cmake", "homepage": "https://github.com/boostorg/cmake", "licenseConcluded": "BSL-1.0", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "description": "Boost CMake support infrastructure", "comment": "This is the port (recipe) consumed by vcpkg."}, {"name": "boost-cmake:x64-linux", "SPDXID": "SPDXRef-binary", "versionInfo": "cbe23a80263643a95f4366123c43fb9d07e4b7c0eee36f9c233ce7e6f77856b8", "downloadLocation": "NONE", "licenseConcluded": "BSL-1.0", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "comment": "This is a binary package built by vcpkg."}, {"SPDXID": "SPDXRef-resource-0", "name": "boostorg/boost", "downloadLocation": "git+https://github.com/boostorg/boost@${boost_boost_ref}", "licenseConcluded": "NOASSERTION", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "checksums": [{"algorithm": "SHA512", "checksumValue": "${boost_boost_sha512}"}]}, {"SPDXID": "SPDXRef-resource-1", "name": "boostorg/cmake", "downloadLocation": "git+https://github.com/boostorg/cmake@${boost_cmake_ref}", "licenseConcluded": "NOASSERTION", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "checksums": [{"algorithm": "SHA512", "checksumValue": "${boost_cmake_sha512}"}]}], "files": [{"fileName": "./ref_sha.cmake", "SPDXID": "SPDXRef-file-0", "checksums": [{"algorithm": "SHA256", "checksumValue": "d97a5b86b95df1f8a8eae3091fcd765b264e69ff1d1990820aedbf041f305bf7"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./vcpkg-port-config.cmake", "SPDXID": "SPDXRef-file-1", "checksums": [{"algorithm": "SHA256", "checksumValue": "b78bffe3cced9519475c61e4b4da05976026ab7257bee7b13e2b0d11a1a40667"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./no-honor-static.diff", "SPDXID": "SPDXRef-file-2", "checksums": [{"algorithm": "SHA256", "checksumValue": "b5f34407c74c89819aee2b29e373f3a48cbfb81ed0e6e56b14a16d47378cfac3"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./usage", "SPDXID": "SPDXRef-file-3", "checksums": [{"algorithm": "SHA256", "checksumValue": "9e0ff3b7b8eaa953a1d04a866685e9c9c0dcfcc935f1478410f41ddf623f3a62"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./no-prefix.diff", "SPDXID": "SPDXRef-file-4", "checksums": [{"algorithm": "SHA256", "checksumValue": "645a68e1d215dcb651c7f0f5ccd348bcf797e25c36383119a8504da4cd9600da"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./vcpkg.json", "SPDXID": "SPDXRef-file-5", "checksums": [{"algorithm": "SHA256", "checksumValue": "4f5320bf4547aaf1b2ec3967af06a246159b8e6e1f2ba8e9d93c7aa4c4bf3f1a"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./portfile.cmake", "SPDXID": "SPDXRef-file-6", "checksums": [{"algorithm": "SHA256", "checksumValue": "e3d74ec0329ed49ee3a51de89ab5c0d617dc9e50ce6a01e01e7729e62023549e"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./no-config-suffix.diff", "SPDXID": "SPDXRef-file-7", "checksums": [{"algorithm": "SHA256", "checksumValue": "d5b75b03ac07c9f48ecfae6d3d08e74ed1983f794816467d75ce8e2255e85f78"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./add-optional-deps.diff", "SPDXID": "SPDXRef-file-8", "checksums": [{"algorithm": "SHA256", "checksumValue": "e7f5128a4fdd3d8195a4caf4aa2daec9223d14814eff2ed2cae2b094ebb6661f"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./fix-mpi.diff", "SPDXID": "SPDXRef-file-9", "checksums": [{"algorithm": "SHA256", "checksumValue": "20eaaf461900aafccc5f8278292a5394410856bb6dd075fdbb31dfc51b183043"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./CMakeLists.txt.in", "SPDXID": "SPDXRef-file-10", "checksums": [{"algorithm": "SHA256", "checksumValue": "ff0a1f8795d737e90a30acfe0062df8d5441a0f881694fcee258aed3a43a5423"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./vcpkg-build.diff", "SPDXID": "SPDXRef-file-11", "checksums": [{"algorithm": "SHA256", "checksumValue": "2d86ac26a6dde0375c719d9380db2c3ad2e5731fb71a4a0fdb3a0c61bf5d1a6b"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}]}