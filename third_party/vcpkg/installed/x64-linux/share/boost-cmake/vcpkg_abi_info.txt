CMakeLists.txt.in ff0a1f8795d737e90a30acfe0062df8d5441a0f881694fcee258aed3a43a5423
add-optional-deps.diff e7f5128a4fdd3d8195a4caf4aa2daec9223d14814eff2ed2cae2b094ebb6661f
boost-uninstall fa9fdbcd9adfd3b9abee0dd99d1f4537f4c38ed5dcae638f443e1ff54bc0f8b3
cmake 3.30.5
features core
fix-mpi.diff 20eaaf461900aafccc5f8278292a5394410856bb6dd075fdbb31dfc51b183043
no-config-suffix.diff d5b75b03ac07c9f48ecfae6d3d08e74ed1983f794816467d75ce8e2255e85f78
no-honor-static.diff b5f34407c74c89819aee2b29e373f3a48cbfb81ed0e6e56b14a16d47378cfac3
no-prefix.diff 645a68e1d215dcb651c7f0f5ccd348bcf797e25c36383119a8504da4cd9600da
portfile.cmake e3d74ec0329ed49ee3a51de89ab5c0d617dc9e50ce6a01e01e7729e62023549e
ports.cmake a9f8f745f9f4d225039f9810e79670f10f38c7455222865616ce430a06f75d1f
post_build_checks 2
ref_sha.cmake d97a5b86b95df1f8a8eae3091fcd765b264e69ff1d1990820aedbf041f305bf7
triplet x64-linux
triplet_abi 071689e8a9e42200a224208c0a52e5107f9f7e42dfb67eb092ae041012550f88-7300d6fc76582335b0d9fd6dc6a00f69d7ba728b9d6444ad2a9d42f054e1d991-a9cc48706cca742e5b6b955b1702a49b2a65371e
usage 9e0ff3b7b8eaa953a1d04a866685e9c9c0dcfcc935f1478410f41ddf623f3a62
vcpkg-boost 2ccf123eda0dd17bd7f4ddc9fa672cd728f43a7de301e2757339c370c7c8720e
vcpkg-build.diff 2d86ac26a6dde0375c719d9380db2c3ad2e5731fb71a4a0fdb3a0c61bf5d1a6b
vcpkg-cmake de2f19a93d26a154da64df86ba1d2823b5bbb0e3dda4e121cff4247a722ba6e3
vcpkg-cmake-config 68218f23fdf68b646a486fb74a5664cc5cc029fd557a1728d07ec73c1702bfd6
vcpkg-port-config.cmake b78bffe3cced9519475c61e4b4da05976026ab7257bee7b13e2b0d11a1a40667
vcpkg.json 4f5320bf4547aaf1b2ec3967af06a246159b8e6e1f2ba8e9d93c7aa4c4bf3f1a
vcpkg_from_git 96ed81968f76354c00096dd8cd4e63c6a235fa969334a11ab18d11c0c512ff58
vcpkg_from_github b743742296a114ea1b18ae99672e02f142c4eb2bef7f57d36c038bedbfb0502f
vcpkg_install_copyright ba6c169ab4e59fa05682e530cdeb883767de22c8391f023d4e6844a7ec5dd3d2
