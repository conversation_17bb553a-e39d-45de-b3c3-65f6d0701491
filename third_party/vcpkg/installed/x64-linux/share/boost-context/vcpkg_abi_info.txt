boost-assert 101a0a4072390892d7faa6ddd1ae78eda684a04c755dc2b61421263cd6b80039
boost-cmake cbe23a80263643a95f4366123c43fb9d07e4b7c0eee36f9c233ce7e6f77856b8
boost-config c3f5199231a5255ebb729e44013c850064331e0d3ffd3694d49c6052c596b162
boost-core 853b27c1ef82549ba1681bf403063685db49d68fe328c891548af179b939f16e
boost-headers fb9be230902d605763df2d977a58101c6edb81544f24a140dfef1a6b3d7e8f9e
boost-mp11 4ad7d3ce98bbfcc0a0b6842b5a06a95b3f40b25fd21b16af3da91f7f7f538773
boost-pool dc61a71413b78ea1fe7ac4b54a9676a04efef29341db739f2b8b7a222a7ef35b
boost-predef 2982d099a602f945477ba8f8c3a903a1e691fab493fc5d9eb04341257bf952ef
boost-smart-ptr 865110b792ce44a387fbfcbd5e7f02ade45fa64e19ab4ee2bea4ce311fe9b5df
cmake 3.30.5
features core
marmasm.patch fdc92ce93c003006c5c8d3defe627f0d389ccc90cbf0a0d398364c966dd1b050
portfile.cmake 07949372f9c9669b031eeed1fe8c3c7e7e88055563bdfb8f8ee8481d81447af7
ports.cmake a9f8f745f9f4d225039f9810e79670f10f38c7455222865616ce430a06f75d1f
post_build_checks 2
triplet x64-linux
triplet_abi 071689e8a9e42200a224208c0a52e5107f9f7e42dfb67eb092ae041012550f88-7300d6fc76582335b0d9fd6dc6a00f69d7ba728b9d6444ad2a9d42f054e1d991-a9cc48706cca742e5b6b955b1702a49b2a65371e
vcpkg.json 7968efe0e8944dad62d3b868c316e8f59feb32e6d004da09f6e709b4713919c3
vcpkg_from_git 96ed81968f76354c00096dd8cd4e63c6a235fa969334a11ab18d11c0c512ff58
vcpkg_from_github b743742296a114ea1b18ae99672e02f142c4eb2bef7f57d36c038bedbfb0502f
