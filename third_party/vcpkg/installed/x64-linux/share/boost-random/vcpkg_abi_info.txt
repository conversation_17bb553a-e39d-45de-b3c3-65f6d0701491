boost-array a5d9b1f1257c0fe8d9099bd1da5958243f93e77b11f5551e129ed7ac7b519450
boost-assert 101a0a4072390892d7faa6ddd1ae78eda684a04c755dc2b61421263cd6b80039
boost-cmake cbe23a80263643a95f4366123c43fb9d07e4b7c0eee36f9c233ce7e6f77856b8
boost-config c3f5199231a5255ebb729e44013c850064331e0d3ffd3694d49c6052c596b162
boost-core 853b27c1ef82549ba1681bf403063685db49d68fe328c891548af179b939f16e
boost-dynamic-bitset b0ff346883bcf0f69b1b4d7f1701919fe9bf4bfea33da838a9096f8642d248c3
boost-headers fb9be230902d605763df2d977a58101c6edb81544f24a140dfef1a6b3d7e8f9e
boost-integer bd64143037f9bfd9d7d1a3fb355345071d77c933445fffd78bbfb88b569a9ba5
boost-io f58b5e79d272b623417f3d4a2211795a2f00f576adea5f52fa5ad85a855baa80
boost-range 5afee78a79d73d045a5fd34f9e180237a38b558af58e4291bc41fdb2e6a0da8d
boost-static-assert 2121e5fa49fcaefd8a0caf8b99c6769fc1ffc90b8d89a3327b084adc113b996a
boost-system d3d87ab839b35d90cd0f768250fa6f39cae155118aff339a03c6ae2821867802
boost-throw-exception 2ba6715e3f4e8690930203481474cfe4967ef01b04fcb9abbf6ec3180b1ebff4
boost-type-traits 40e7e2cefeeb0012dd4a1d647dd3214ec0fb860d50a004a81f698cf0a1e7e505
boost-utility 073f4bafebf4427571f82cb6192b69a9a202873a68deed00a7347dd0a2eadb92
cmake 3.30.5
features core
portfile.cmake 6afba2915f26d07d999614559203e8558c428d92d27c343cfef4b3260b5aef11
ports.cmake a9f8f745f9f4d225039f9810e79670f10f38c7455222865616ce430a06f75d1f
post_build_checks 2
triplet x64-linux
triplet_abi 071689e8a9e42200a224208c0a52e5107f9f7e42dfb67eb092ae041012550f88-7300d6fc76582335b0d9fd6dc6a00f69d7ba728b9d6444ad2a9d42f054e1d991-a9cc48706cca742e5b6b955b1702a49b2a65371e
vcpkg.json f8bac2a7b934e2226de989e29b99099fa0e3aa9750e6a1f76c79a10bed37f2e5
vcpkg_from_git 96ed81968f76354c00096dd8cd4e63c6a235fa969334a11ab18d11c0c512ff58
vcpkg_from_github b743742296a114ea1b18ae99672e02f142c4eb2bef7f57d36c038bedbfb0502f
