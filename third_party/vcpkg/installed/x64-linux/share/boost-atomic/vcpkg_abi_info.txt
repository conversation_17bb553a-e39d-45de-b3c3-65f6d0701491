boost-align c5d809b532b1ff19fb36a411d45c85d5016d2139326373e6cf4f61ac81d1a9eb
boost-assert 101a0a4072390892d7faa6ddd1ae78eda684a04c755dc2b61421263cd6b80039
boost-cmake cbe23a80263643a95f4366123c43fb9d07e4b7c0eee36f9c233ce7e6f77856b8
boost-config c3f5199231a5255ebb729e44013c850064331e0d3ffd3694d49c6052c596b162
boost-headers fb9be230902d605763df2d977a58101c6edb81544f24a140dfef1a6b3d7e8f9e
boost-predef 2982d099a602f945477ba8f8c3a903a1e691fab493fc5d9eb04341257bf952ef
boost-preprocessor e85fb5253d74d0384c1f3d163cd4c0a57667237bbf8935423adae6b5b1bd0dfc
boost-type-traits 40e7e2cefeeb0012dd4a1d647dd3214ec0fb860d50a004a81f698cf0a1e7e505
boost-winapi 1d13e34101c5580b76b8a94398bd640186ffc4e98e1a3904c24c800d9461f9e9
cmake 3.30.5
features core
fix-include.patch 8349c71529f246ef9ba0aa31de7267006ec45a75e5afb91354a598830fd5f0c0
portfile.cmake 05dfa30973bd27ef45302615c51bd3fd82a16f6a4279d9193d82065d0cbe3705
ports.cmake a9f8f745f9f4d225039f9810e79670f10f38c7455222865616ce430a06f75d1f
post_build_checks 2
triplet x64-linux
triplet_abi 071689e8a9e42200a224208c0a52e5107f9f7e42dfb67eb092ae041012550f88-7300d6fc76582335b0d9fd6dc6a00f69d7ba728b9d6444ad2a9d42f054e1d991-a9cc48706cca742e5b6b955b1702a49b2a65371e
vcpkg.json 974c1600dd56fe86487e8a9c356fa9d9693e920a49c465c97648506af4c03515
vcpkg_from_git 96ed81968f76354c00096dd8cd4e63c6a235fa969334a11ab18d11c0c512ff58
vcpkg_from_github b743742296a114ea1b18ae99672e02f142c4eb2bef7f57d36c038bedbfb0502f
