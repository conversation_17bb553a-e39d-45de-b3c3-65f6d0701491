boost-assert 101a0a4072390892d7faa6ddd1ae78eda684a04c755dc2b61421263cd6b80039
boost-cmake cbe23a80263643a95f4366123c43fb9d07e4b7c0eee36f9c233ce7e6f77856b8
boost-config c3f5199231a5255ebb729e44013c850064331e0d3ffd3694d49c6052c596b162
boost-headers fb9be230902d605763df2d977a58101c6edb81544f24a140dfef1a6b3d7e8f9e
boost-intrusive ba10dd99ecd6c827a17ab17f105000666df5cc21f55bdee61f0f35ef2c585687
boost-move 5918c7c7dde783987266c8746a74fe58c10229d4d3a46c0c790fdd1fe7acb260
cmake 3.30.5
features core
portfile.cmake b190b69b5fe70a01f11ec25db395fa9d8891582efb751d28fcb6997f638b4191
ports.cmake a9f8f745f9f4d225039f9810e79670f10f38c7455222865616ce430a06f75d1f
posix-threads.diff 94aff70844c47aed2a5460b63d92527e6557780beea19037ee949f9a8d3a42a5
post_build_checks 2
triplet x64-linux
triplet_abi 071689e8a9e42200a224208c0a52e5107f9f7e42dfb67eb092ae041012550f88-7300d6fc76582335b0d9fd6dc6a00f69d7ba728b9d6444ad2a9d42f054e1d991-a9cc48706cca742e5b6b955b1702a49b2a65371e
vcpkg.json 5d283f46ee0665852921eef4fac0a4fb72ba8337ddfca1f74da4637ebe138fd1
vcpkg_from_git 96ed81968f76354c00096dd8cd4e63c6a235fa969334a11ab18d11c0c512ff58
vcpkg_from_github b743742296a114ea1b18ae99672e02f142c4eb2bef7f57d36c038bedbfb0502f
