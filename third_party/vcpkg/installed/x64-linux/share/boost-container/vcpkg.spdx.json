{"$schema": "https://raw.githubusercontent.com/spdx/spdx-spec/v2.2.1/schemas/spdx-schema.json", "spdxVersion": "SPDX-2.2", "dataLicense": "CC0-1.0", "SPDXID": "SPDXRef-DOCUMENT", "documentNamespace": "https://spdx.org/spdxdocs/boost-container-x64-linux-1.87.0-a56d2fde-8bf6-4a93-b6d1-a6ea47964677", "name": "boost-container:x64-linux@1.87.0 c5d7e7d9fd117d48dc6fddc073ee3ac56fa1220f0f9df415bd7e05e387c35bd4", "creationInfo": {"creators": ["Tool: vcpkg-2025-04-07-b8b513ba8778c918cff49c3e837aae5999d5d2aa"], "created": "2025-06-10T09:06:18Z"}, "relationships": [{"spdxElementId": "SPDXRef-port", "relationshipType": "GENERATES", "relatedSpdxElement": "SPDXRef-binary"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-0"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-1"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-2"}, {"spdxElementId": "SPDXRef-binary", "relationshipType": "GENERATED_FROM", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-0", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-0", "relationshipType": "DEPENDENCY_MANIFEST_OF", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-1", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-2", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}], "packages": [{"name": "boost-container", "SPDXID": "SPDXRef-port", "versionInfo": "1.87.0", "downloadLocation": "git+https://github.com/Microsoft/vcpkg#ports/boost-container", "homepage": "https://www.boost.org/libs/container", "licenseConcluded": "BSL-1.0", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "description": "Boost container module", "comment": "This is the port (recipe) consumed by vcpkg."}, {"name": "boost-container:x64-linux", "SPDXID": "SPDXRef-binary", "versionInfo": "c5d7e7d9fd117d48dc6fddc073ee3ac56fa1220f0f9df415bd7e05e387c35bd4", "downloadLocation": "NONE", "licenseConcluded": "BSL-1.0", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "comment": "This is a binary package built by vcpkg."}, {"SPDXID": "SPDXRef-resource-0", "name": "boostorg/container", "downloadLocation": "git+https://github.com/boostorg/container@boost-1.87.0", "licenseConcluded": "NOASSERTION", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "checksums": [{"algorithm": "SHA512", "checksumValue": "447df3942dbf9157c36aec3144f2c80f49a82e18ac4e8e479c85a0b0bcd84deb798c23cf6cab4324f785552af9a1246cab80a77ed7e96c53408bee5aa9c73eba"}]}], "files": [{"fileName": "./vcpkg.json", "SPDXID": "SPDXRef-file-0", "checksums": [{"algorithm": "SHA256", "checksumValue": "5d283f46ee0665852921eef4fac0a4fb72ba8337ddfca1f74da4637ebe138fd1"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./portfile.cmake", "SPDXID": "SPDXRef-file-1", "checksums": [{"algorithm": "SHA256", "checksumValue": "b190b69b5fe70a01f11ec25db395fa9d8891582efb751d28fcb6997f638b4191"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./posix-threads.diff", "SPDXID": "SPDXRef-file-2", "checksums": [{"algorithm": "SHA256", "checksumValue": "94aff70844c47aed2a5460b63d92527e6557780beea19037ee949f9a8d3a42a5"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}]}