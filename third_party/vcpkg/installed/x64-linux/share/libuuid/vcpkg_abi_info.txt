CMakeLists.txt e44e9a252df03125d721785cbe93241f6c0b1c7260fa8981f38c8587e7479fe7
cmake 3.30.5
config.linux.h 95b82bd74377635a7e67a95b1b96143b370443abc054fc42c728e5d5a4153297
features core
portfile.cmake b04be114cab9b5bcf98d29154c3a755c5398b0d2628f61ad78fe1c5fe9a8efec
ports.cmake a9f8f745f9f4d225039f9810e79670f10f38c7455222865616ce430a06f75d1f
post_build_checks 2
triplet x64-linux
triplet_abi 071689e8a9e42200a224208c0a52e5107f9f7e42dfb67eb092ae041012550f88-7300d6fc76582335b0d9fd6dc6a00f69d7ba728b9d6444ad2a9d42f054e1d991-a9cc48706cca742e5b6b955b1702a49b2a65371e
unofficial-libuuid-config.cmake.in d440b00400bed20ebc6c0e4ef9f646c558b8e752fa11f0a78ff0392a0653f584
vcpkg-cmake de2f19a93d26a154da64df86ba1d2823b5bbb0e3dda4e121cff4247a722ba6e3
vcpkg-cmake-config 68218f23fdf68b646a486fb74a5664cc5cc029fd557a1728d07ec73c1702bfd6
vcpkg.json eff230e1b020475dfaf67745b4116b470ec67d73e753d296e1f5f19bcd18ebe1
vcpkg_copy_pdbs d15c2f2822d93ecb9023af44803328e491c5374136e0813600289582a72d196d
vcpkg_fixup_pkgconfig 1a15f6c6d8e2b244d83a7514a0412d339127d2217d1df60ad1388b546c85f777
vcpkg_from_sourceforge 00cb7d5767d56fdd8a1715ebd3c159fccd44dc17653522e23d2e507bce44a4f8
vcpkg_install_copyright ba6c169ab4e59fa05682e530cdeb883767de22c8391f023d4e6844a7ec5dd3d2
