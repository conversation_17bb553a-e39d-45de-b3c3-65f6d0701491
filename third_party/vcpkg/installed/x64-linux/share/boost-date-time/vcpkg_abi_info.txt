boost-algorithm fff8c33cacff34c2af121c13d2d374b9fa3260860f41f5dc821576f5c31b6cde
boost-assert 101a0a4072390892d7faa6ddd1ae78eda684a04c755dc2b61421263cd6b80039
boost-cmake cbe23a80263643a95f4366123c43fb9d07e4b7c0eee36f9c233ce7e6f77856b8
boost-config c3f5199231a5255ebb729e44013c850064331e0d3ffd3694d49c6052c596b162
boost-core 853b27c1ef82549ba1681bf403063685db49d68fe328c891548af179b939f16e
boost-headers fb9be230902d605763df2d977a58101c6edb81544f24a140dfef1a6b3d7e8f9e
boost-io f58b5e79d272b623417f3d4a2211795a2f00f576adea5f52fa5ad85a855baa80
boost-lexical-cast 4eded4a6afcb16ae4b4af6d4800fdb2c743a696b484c90e2582664e381b286bb
boost-numeric-conversion 5aa09f7d77878f43807f760829b7eebcc9c95a858874fd67b516d81ffb1b2852
boost-range 5afee78a79d73d045a5fd34f9e180237a38b558af58e4291bc41fdb2e6a0da8d
boost-smart-ptr 865110b792ce44a387fbfcbd5e7f02ade45fa64e19ab4ee2bea4ce311fe9b5df
boost-static-assert 2121e5fa49fcaefd8a0caf8b99c6769fc1ffc90b8d89a3327b084adc113b996a
boost-throw-exception 2ba6715e3f4e8690930203481474cfe4967ef01b04fcb9abbf6ec3180b1ebff4
boost-tokenizer 7db94bce7b2d80e73b9589d7ccc5132d2e3ae0dff4b17bedb50e3f41053f87ff
boost-type-traits 40e7e2cefeeb0012dd4a1d647dd3214ec0fb860d50a004a81f698cf0a1e7e505
boost-utility 073f4bafebf4427571f82cb6192b69a9a202873a68deed00a7347dd0a2eadb92
boost-winapi 1d13e34101c5580b76b8a94398bd640186ffc4e98e1a3904c24c800d9461f9e9
cmake 3.30.5
features core
portfile.cmake 3edb599b1b0e0a19214fa1d48c340638448bcc22d11ba3a7ee1c18d6f3ddd546
ports.cmake a9f8f745f9f4d225039f9810e79670f10f38c7455222865616ce430a06f75d1f
post_build_checks 2
triplet x64-linux
triplet_abi 071689e8a9e42200a224208c0a52e5107f9f7e42dfb67eb092ae041012550f88-7300d6fc76582335b0d9fd6dc6a00f69d7ba728b9d6444ad2a9d42f054e1d991-a9cc48706cca742e5b6b955b1702a49b2a65371e
vcpkg.json 7729e383be35ccd3a9c7f03693c5bed4c587c092adcb14b915d09dd7806fee90
vcpkg_from_git 96ed81968f76354c00096dd8cd4e63c6a235fa969334a11ab18d11c0c512ff58
vcpkg_from_github b743742296a114ea1b18ae99672e02f142c4eb2bef7f57d36c038bedbfb0502f
