{"$schema": "https://raw.githubusercontent.com/spdx/spdx-spec/v2.2.1/schemas/spdx-schema.json", "spdxVersion": "SPDX-2.2", "dataLicense": "CC0-1.0", "SPDXID": "SPDXRef-DOCUMENT", "documentNamespace": "https://spdx.org/spdxdocs/boost-regex-x64-linux-1.87.0#1-236d9116-3b5c-4cd4-9c9c-1ffca578d0f3", "name": "boost-regex:x64-linux@1.87.0#1 ca1716846351f62334353ae47628e25f38c5d638cdafadc678f0251ec967207c", "creationInfo": {"creators": ["Tool: vcpkg-2025-04-07-b8b513ba8778c918cff49c3e837aae5999d5d2aa"], "created": "2025-06-10T09:05:43Z"}, "relationships": [{"spdxElementId": "SPDXRef-port", "relationshipType": "GENERATES", "relatedSpdxElement": "SPDXRef-binary"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-0"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-1"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-2"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-3"}, {"spdxElementId": "SPDXRef-binary", "relationshipType": "GENERATED_FROM", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-0", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-1", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-1", "relationshipType": "DEPENDENCY_MANIFEST_OF", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-2", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-3", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}], "packages": [{"name": "boost-regex", "SPDXID": "SPDXRef-port", "versionInfo": "1.87.0#1", "downloadLocation": "git+https://github.com/Microsoft/vcpkg#ports/boost-regex", "homepage": "https://www.boost.org/libs/regex", "licenseConcluded": "BSL-1.0", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "description": "Boost regex module", "comment": "This is the port (recipe) consumed by vcpkg."}, {"name": "boost-regex:x64-linux", "SPDXID": "SPDXRef-binary", "versionInfo": "ca1716846351f62334353ae47628e25f38c5d638cdafadc678f0251ec967207c", "downloadLocation": "NONE", "licenseConcluded": "BSL-1.0", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "comment": "This is a binary package built by vcpkg."}, {"SPDXID": "SPDXRef-resource-0", "name": "boostorg/regex", "downloadLocation": "git+https://github.com/boostorg/regex@boost-1.87.0", "licenseConcluded": "NOASSERTION", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "checksums": [{"algorithm": "SHA512", "checksumValue": "822deca098dbb21f1df0159a2ccee16b899558f32e4a5dfcb2c349af4fa5e57502c4eb959d989a26a930c877a649c7bae65a292fcac11b40eef4efc849ae8d19"}]}], "files": [{"fileName": "./compat.diff", "SPDXID": "SPDXRef-file-0", "checksums": [{"algorithm": "SHA256", "checksumValue": "9a356bbaa9c881453bf8949d5da5f22ce96e017421ba7bfe4268ba30c078aca9"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./vcpkg.json", "SPDXID": "SPDXRef-file-1", "checksums": [{"algorithm": "SHA256", "checksumValue": "28f80d2f258f1966d42818a45849203269aa32b2b6dc32efc132a1ad80312684"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./portfile.cmake", "SPDXID": "SPDXRef-file-2", "checksums": [{"algorithm": "SHA256", "checksumValue": "9ce7ea48f84cd408e72270c0f40235f5125c37e2b8b6b9e09cd6e622c51ec124"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./features.cmake", "SPDXID": "SPDXRef-file-3", "checksums": [{"algorithm": "SHA256", "checksumValue": "24a7353153ea5327340c43d4d812a8f67f42b6c07989cbdab45e1460f5d91b6e"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}]}