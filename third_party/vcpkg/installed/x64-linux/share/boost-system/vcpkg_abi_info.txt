boost-assert 101a0a4072390892d7faa6ddd1ae78eda684a04c755dc2b61421263cd6b80039
boost-cmake cbe23a80263643a95f4366123c43fb9d07e4b7c0eee36f9c233ce7e6f77856b8
boost-config c3f5199231a5255ebb729e44013c850064331e0d3ffd3694d49c6052c596b162
boost-headers fb9be230902d605763df2d977a58101c6edb81544f24a140dfef1a6b3d7e8f9e
boost-throw-exception 2ba6715e3f4e8690930203481474cfe4967ef01b04fcb9abbf6ec3180b1ebff4
boost-variant2 8c8ca099c02e94a0c0b357ad64ad75c0c9478a5e86ef5b129deb07e17a27f687
boost-winapi 1d13e34101c5580b76b8a94398bd640186ffc4e98e1a3904c24c800d9461f9e9
cmake 3.30.5
compat.diff 57257cafa3402afa053ef011ac89a88c6edde08a23a61c282ff373fde342e597
features core
portfile.cmake 40b4e42014f32af2470773b031a296ac0f2f2d5284735f66a1305f976274ce92
ports.cmake a9f8f745f9f4d225039f9810e79670f10f38c7455222865616ce430a06f75d1f
post_build_checks 2
triplet x64-linux
triplet_abi 071689e8a9e42200a224208c0a52e5107f9f7e42dfb67eb092ae041012550f88-7300d6fc76582335b0d9fd6dc6a00f69d7ba728b9d6444ad2a9d42f054e1d991-a9cc48706cca742e5b6b955b1702a49b2a65371e
vcpkg.json 46ab1efbb712294bd4a63c545228af79fae977db123c92e394b60122658f4ded
vcpkg_buildpath_length_warning fa440f3734e34b462ef96f3e89f0a9c5a11b609110a15a162d24da9218693fd1
vcpkg_from_git 96ed81968f76354c00096dd8cd4e63c6a235fa969334a11ab18d11c0c512ff58
vcpkg_from_github b743742296a114ea1b18ae99672e02f142c4eb2bef7f57d36c038bedbfb0502f
