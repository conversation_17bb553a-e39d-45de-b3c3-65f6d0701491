boost-assert 101a0a4072390892d7faa6ddd1ae78eda684a04c755dc2b61421263cd6b80039
boost-atomic bd2449aeb2aed7e8ed78f52644458ba79daa0eb3e29517da68fcb88ae4ba3222
boost-cmake cbe23a80263643a95f4366123c43fb9d07e4b7c0eee36f9c233ce7e6f77856b8
boost-config c3f5199231a5255ebb729e44013c850064331e0d3ffd3694d49c6052c596b162
boost-container-hash 6119a7803fbcb1a3b67d17ee5fa7c4ff9fabe02bc01bfef6ef6426bbae05a58f
boost-core 853b27c1ef82549ba1681bf403063685db49d68fe328c891548af179b939f16e
boost-detail 54143b147e5c0141aa1823846c755a28fc9fc2d0e542babbcd2898e1825d5746
boost-headers fb9be230902d605763df2d977a58101c6edb81544f24a140dfef1a6b3d7e8f9e
boost-io f58b5e79d272b623417f3d4a2211795a2f00f576adea5f52fa5ad85a855baa80
boost-iterator f03f6847fae064e02afe7f14a1e95ebec8011f420ee62287251324c16b4ee27c
boost-predef 2982d099a602f945477ba8f8c3a903a1e691fab493fc5d9eb04341257bf952ef
boost-scope cde02c4d9cdf274df6c671a185d48f46021afea1ac177411e12f00c4f5ee3282
boost-smart-ptr 865110b792ce44a387fbfcbd5e7f02ade45fa64e19ab4ee2bea4ce311fe9b5df
boost-system d3d87ab839b35d90cd0f768250fa6f39cae155118aff339a03c6ae2821867802
boost-type-traits 40e7e2cefeeb0012dd4a1d647dd3214ec0fb860d50a004a81f698cf0a1e7e505
boost-winapi 1d13e34101c5580b76b8a94398bd640186ffc4e98e1a3904c24c800d9461f9e9
cmake 3.30.5
features core
portfile.cmake 0c57dc99d7cf97c633e64faa3bcd10d40a194d70356a769cf223888087852fe5
ports.cmake a9f8f745f9f4d225039f9810e79670f10f38c7455222865616ce430a06f75d1f
post_build_checks 2
triplet x64-linux
triplet_abi 071689e8a9e42200a224208c0a52e5107f9f7e42dfb67eb092ae041012550f88-7300d6fc76582335b0d9fd6dc6a00f69d7ba728b9d6444ad2a9d42f054e1d991-a9cc48706cca742e5b6b955b1702a49b2a65371e
vcpkg.json b6c13cd136b8c63bbfe211f06e40fd5889ccc95f0d63d1ad9fb3e79bb23ac1ba
vcpkg_from_git 96ed81968f76354c00096dd8cd4e63c6a235fa969334a11ab18d11c0c512ff58
vcpkg_from_github b743742296a114ea1b18ae99672e02f142c4eb2bef7f57d36c038bedbfb0502f
