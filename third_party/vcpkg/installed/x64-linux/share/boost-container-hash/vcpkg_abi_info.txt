boost-cmake cbe23a80263643a95f4366123c43fb9d07e4b7c0eee36f9c233ce7e6f77856b8
boost-config c3f5199231a5255ebb729e44013c850064331e0d3ffd3694d49c6052c596b162
boost-describe f1d4746e4c1fc689cc1326aa7b77911f004f004b7ce1e43e8827366cea8353f6
boost-headers fb9be230902d605763df2d977a58101c6edb81544f24a140dfef1a6b3d7e8f9e
boost-mp11 4ad7d3ce98bbfcc0a0b6842b5a06a95b3f40b25fd21b16af3da91f7f7f538773
cmake 3.30.5
features core
portfile.cmake da6f82a55d505f60e40ddbeed377fcbe3be1ee4aa1210acfcac3483053eda2e5
ports.cmake a9f8f745f9f4d225039f9810e79670f10f38c7455222865616ce430a06f75d1f
post_build_checks 2
triplet x64-linux
triplet_abi 071689e8a9e42200a224208c0a52e5107f9f7e42dfb67eb092ae041012550f88-7300d6fc76582335b0d9fd6dc6a00f69d7ba728b9d6444ad2a9d42f054e1d991-a9cc48706cca742e5b6b955b1702a49b2a65371e
vcpkg.json b7937bd56f47b051a897da0b305b209fefca3641dd934def3073449af54855a5
vcpkg_from_git 96ed81968f76354c00096dd8cd4e63c6a235fa969334a11ab18d11c0c512ff58
vcpkg_from_github b743742296a114ea1b18ae99672e02f142c4eb2bef7f57d36c038bedbfb0502f
