# Copyright John <PERSON>.
# Use, modification and distribution are subject to the 
# Boost Software License, Version 1.0. (See accompanying file 
# LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

import modules ;

rule requires ( names + )
{
   local config-binding = [ modules.binding $(__name__) ] ;

   local result ;
   for name in $(names)
   {
      result += [ check-target-builds $(config-binding:D)//$(name) $(name) : : <build>no ] ;
   }
   return $(result) ;
}

