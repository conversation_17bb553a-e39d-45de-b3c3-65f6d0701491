cmake 3.30.5
features core
portfile.cmake cc841c300f71dd9e54b456bef19dbabbe3458c76dd512809fd997b831287eeb0
ports.cmake a9f8f745f9f4d225039f9810e79670f10f38c7455222865616ce430a06f75d1f
post_build_checks 2
triplet x64-linux
triplet_abi 071689e8a9e42200a224208c0a52e5107f9f7e42dfb67eb092ae041012550f88-7300d6fc76582335b0d9fd6dc6a00f69d7ba728b9d6444ad2a9d42f054e1d991-a9cc48706cca742e5b6b955b1702a49b2a65371e
vcpkg-cmake de2f19a93d26a154da64df86ba1d2823b5bbb0e3dda4e121cff4247a722ba6e3
vcpkg-cmake-config 68218f23fdf68b646a486fb74a5664cc5cc029fd557a1728d07ec73c1702bfd6
vcpkg.json 0c2dc861ed1f298762baad8e4dc7ce6da68da33e75436e967146b8c2eaadfd72
vcpkg_from_git 96ed81968f76354c00096dd8cd4e63c6a235fa969334a11ab18d11c0c512ff58
vcpkg_from_github b743742296a114ea1b18ae99672e02f142c4eb2bef7f57d36c038bedbfb0502f
vcpkg_install_copyright ba6c169ab4e59fa05682e530cdeb883767de22c8391f023d4e6844a7ec5dd3d2
