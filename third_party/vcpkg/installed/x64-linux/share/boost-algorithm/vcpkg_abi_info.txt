boost-array a5d9b1f1257c0fe8d9099bd1da5958243f93e77b11f5551e129ed7ac7b519450
boost-assert 101a0a4072390892d7faa6ddd1ae78eda684a04c755dc2b61421263cd6b80039
boost-bind 891cb28813980add6f5ade53ae8da21bddc5b28fe07fb9c9f6807477c5e2f3f0
boost-cmake cbe23a80263643a95f4366123c43fb9d07e4b7c0eee36f9c233ce7e6f77856b8
boost-concept-check a07bd0996355d119091b2080b573d54bf0d4acd3693113dbee8748bf3fb714e8
boost-config c3f5199231a5255ebb729e44013c850064331e0d3ffd3694d49c6052c596b162
boost-core 853b27c1ef82549ba1681bf403063685db49d68fe328c891548af179b939f16e
boost-exception a095c82a2819fdcb8ad6ad8aede331148edcced6f96a0ede724f73f6c3d0fbd7
boost-function 1386da11bd1baf45dbe87c948ba905e70fef9c4601fa47c0d8e7f97516f238d7
boost-headers fb9be230902d605763df2d977a58101c6edb81544f24a140dfef1a6b3d7e8f9e
boost-iterator f03f6847fae064e02afe7f14a1e95ebec8011f420ee62287251324c16b4ee27c
boost-mpl a96d23aa920592bca6d72724320f9d39282d2101a29d768283004b1e685c20d6
boost-range 5afee78a79d73d045a5fd34f9e180237a38b558af58e4291bc41fdb2e6a0da8d
boost-regex ca1716846351f62334353ae47628e25f38c5d638cdafadc678f0251ec967207c
boost-static-assert 2121e5fa49fcaefd8a0caf8b99c6769fc1ffc90b8d89a3327b084adc113b996a
boost-throw-exception 2ba6715e3f4e8690930203481474cfe4967ef01b04fcb9abbf6ec3180b1ebff4
boost-tuple bebb58d4b19538f33e4ac59ab99c0ecd7377a19da699a41cc408dd923132a453
boost-type-traits 40e7e2cefeeb0012dd4a1d647dd3214ec0fb860d50a004a81f698cf0a1e7e505
boost-unordered bcec401512c11505d1f703f24247331d7aecae27656f4ed9acccaa901b163c50
cmake 3.30.5
features core
portfile.cmake 866fd24eea53b1bf5ca26cd4769f524274d9d3db03a353e67ddf39d097cf1fd8
ports.cmake a9f8f745f9f4d225039f9810e79670f10f38c7455222865616ce430a06f75d1f
post_build_checks 2
triplet x64-linux
triplet_abi 071689e8a9e42200a224208c0a52e5107f9f7e42dfb67eb092ae041012550f88-7300d6fc76582335b0d9fd6dc6a00f69d7ba728b9d6444ad2a9d42f054e1d991-a9cc48706cca742e5b6b955b1702a49b2a65371e
vcpkg.json e53ec30d88d1b4498f947afcd8a14eceebcd4b5689325857e3e190a46af27ffd
vcpkg_from_git 96ed81968f76354c00096dd8cd4e63c6a235fa969334a11ab18d11c0c512ff58
vcpkg_from_github b743742296a114ea1b18ae99672e02f142c4eb2bef7f57d36c038bedbfb0502f
