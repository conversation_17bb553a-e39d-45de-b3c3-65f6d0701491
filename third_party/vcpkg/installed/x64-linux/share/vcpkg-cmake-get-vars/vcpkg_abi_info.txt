cmake 3.30.5
cmake_get_vars/CMakeLists.txt 3fda8c6435ffe8cb481ac9eda466cc29e3843d0a5e0b11c8158842ea25580225
features core
portfile.cmake bbcf9d6b88dd539617f4cfc1f76acbbe9a6e31fc373d79c1c626278768ea2e2a
ports.cmake a9f8f745f9f4d225039f9810e79670f10f38c7455222865616ce430a06f75d1f
post_build_checks 2
triplet x64-linux
triplet_abi 071689e8a9e42200a224208c0a52e5107f9f7e42dfb67eb092ae041012550f88-7300d6fc76582335b0d9fd6dc6a00f69d7ba728b9d6444ad2a9d42f054e1d991-a9cc48706cca742e5b6b955b1702a49b2a65371e
vcpkg-cmake de2f19a93d26a154da64df86ba1d2823b5bbb0e3dda4e121cff4247a722ba6e3
vcpkg-port-config.cmake 4fd4c2e909bbdf069eb3c59b4b847b0b386cdb41840714e12b34b7eff41f9e22
vcpkg.json f69b04b13a19ccd0124aebafe180915143d96b73df98163cb7bd2f9a800a03db
vcpkg_cmake_get_vars.cmake da894e0dafa6ef0acdc641f12b02633f273322d86a6d2e7065dc56134478cea3
