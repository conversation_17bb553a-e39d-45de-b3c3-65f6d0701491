cmake 3.30.5
features core
fix-ODR-libuuid-linux.patch ebaa47ddc8e5c0cd946b724dc55885756616c42e8ed5ed44c09a52c7611de646
fix-unresolvedsymbol-arm.patch d99ebb089a988de02dd62e5accd1a1e2047790083da62a32b2f6e2b73ef8c943
libuuid 392baa39d9d23cc297c5d553e673f640784966c894d06b058c3e6fef451998f3
openssl c705cf416c664dc76af9ab845970d243d71d6410e4b686931f9a9a9cbe20feae
portfile.cmake 023711d7b6ac4fe89374121f6521d49f842f9cec1a2f2bd2fa82b94f2f8b78f8
ports.cmake a9f8f745f9f4d225039f9810e79670f10f38c7455222865616ce430a06f75d1f
post_build_checks 2
triplet x64-linux
triplet_abi 071689e8a9e42200a224208c0a52e5107f9f7e42dfb67eb092ae041012550f88-7300d6fc76582335b0d9fd6dc6a00f69d7ba728b9d6444ad2a9d42f054e1d991-a9cc48706cca742e5b6b955b1702a49b2a65371e
vcpkg-cmake de2f19a93d26a154da64df86ba1d2823b5bbb0e3dda4e121cff4247a722ba6e3
vcpkg-cmake-config 68218f23fdf68b646a486fb74a5664cc5cc029fd557a1728d07ec73c1702bfd6
vcpkg.json c8a5f3df2f074ff946aebb7138397ee4d5531374c2a4b7023fc4104ff7992547
vcpkg_copy_pdbs d15c2f2822d93ecb9023af44803328e491c5374136e0813600289582a72d196d
vcpkg_copy_tools 3d45ff761bddbabe8923b52330168dc3abd295fa469d3f2e47cb14dce85332d5
vcpkg_from_git 96ed81968f76354c00096dd8cd4e63c6a235fa969334a11ab18d11c0c512ff58
vcpkg_from_github b743742296a114ea1b18ae99672e02f142c4eb2bef7f57d36c038bedbfb0502f
vcpkg_install_copyright ba6c169ab4e59fa05682e530cdeb883767de22c8391f023d4e6844a7ec5dd3d2
vcpkg_replace_string b450deb79207478b37119743e00808ebc42de0628e7b98c14ab24728bd5c78b8
