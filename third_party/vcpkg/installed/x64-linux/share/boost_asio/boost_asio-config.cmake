# Generated by BoostInstall.cmake for boost_asio-1.87.0

if(Boost_VERBOSE OR Boost_DEBUG)
  message(STATUS "Found boost_asio ${boost_asio_VERSION} at ${boost_asio_DIR}")
endif()

include(CMakeFindDependencyMacro)

if(NOT boost_align_FOUND)
  find_dependency(boost_align 1.87.0 EXACT HINTS "${CMAKE_CURRENT_LIST_DIR}/..")
endif()
if(NOT boost_assert_FOUND)
  find_dependency(boost_assert 1.87.0 EXACT HINTS "${CMAKE_CURRENT_LIST_DIR}/..")
endif()
if(NOT boost_config_FOUND)
  find_dependency(boost_config 1.87.0 EXACT HINTS "${CMAKE_CURRENT_LIST_DIR}/..")
endif()
if(NOT boost_context_FOUND)
  find_package(boost_context 1.87.0 EXACT QUIET HINTS "${CMAKE_CURRENT_LIST_DIR}/..")
endif()
if(NOT boost_date_time_FOUND)
  find_dependency(boost_date_time 1.87.0 EXACT HINTS "${CMAKE_CURRENT_LIST_DIR}/..")
endif()
if(NOT boost_system_FOUND)
  find_dependency(boost_system 1.87.0 EXACT HINTS "${CMAKE_CURRENT_LIST_DIR}/..")
endif()
if(NOT boost_throw_exception_FOUND)
  find_dependency(boost_throw_exception 1.87.0 EXACT HINTS "${CMAKE_CURRENT_LIST_DIR}/..")
endif()

include("${CMAKE_CURRENT_LIST_DIR}/boost_asio-targets.cmake")
