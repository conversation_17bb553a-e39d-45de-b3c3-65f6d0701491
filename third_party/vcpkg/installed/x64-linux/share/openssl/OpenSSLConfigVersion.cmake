# Generated by OpenSSL

set(PACKAGE_VERSION 3.5.0)

if(NOT PACKAGE_FIND_VERSION)
  # find_package() was called without any version information.  This is assumed to
  # mean that the caller accepts whatever they get.
  set(PACKAGE_VERSION_COMPATIBLE 1)
elseif(PACKAGE_FIND_VERSION_MAJOR LESS 3
   OR PACKAGE_FIND_VERSION VERSION_GREATER 3.5.0)
  set(PACKAGE_VERSION_UNSUITABLE 1)
else()
  set(PACKAGE_VERSION_COMPATIBLE 1)
  if(PACKAGE_FIND_VERSION VERSION_EQUAL 3.5.0)
    set(PACKAGE_VERSION_EXACT 1)
  endif()
endif()
