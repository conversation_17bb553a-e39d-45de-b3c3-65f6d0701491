cmake 3.30.5
cmake-config.patch 9b4cd414b2f7ea82e9bc46507312ad6046ba62403834869b1cc7badc13597a27
command-line-length.patch a1935f6eb38f49e424eaf5dd13b31f0ef46a30f531f9d7e149aac344051a1cbb
execute_process 66a937b9c074422643135c319d1abadaa45484a664f1b160d4c163efb444a446
features core
install-pc-files.cmake 001f4b55b3da7b4faa0910b906f9c587055abaa99edac9cfb3669a06b5387d44
openssl.pc.in add49223f08228831ced1bc35fc2c56a4ab0fe9741e1fb2bdc86c6d3da4b326a
portfile.cmake c5152ef5291ac1676f0b83de99574dce6445de5e0ea3b6960911fa0da8b63d4b
ports.cmake a9f8f745f9f4d225039f9810e79670f10f38c7455222865616ce430a06f75d1f
post_build_checks 2
script-prefix.patch a18b53acdea8180aa24c4abbe85c4269e4d092c6eba4f946d5feafa7ca2a163d
triplet x64-linux
triplet_abi 071689e8a9e42200a224208c0a52e5107f9f7e42dfb67eb092ae041012550f88-7300d6fc76582335b0d9fd6dc6a00f69d7ba728b9d6444ad2a9d42f054e1d991-a9cc48706cca742e5b6b955b1702a49b2a65371e
unix/android-cc.patch a3cde12ca3c7c7894e73d300e0ccf728de28b87afde13776e7d7267979c3ead2
unix/configure 96ef16490c8a7c240a0c96de7e641c93843d0b78400b4de7dc2c7d2e500f3480
unix/move-openssldir.patch 9e62ad861f8421e5b732bf488b7199131ac17a57f295f50c697d2591f5845e0e
unix/no-empty-dirs.patch a03d46642c903c9eb09184eabc553ce1467e000c28a9e7d7e92d995970004a62
unix/no-static-libs-for-shared.patch dc51d597ad7477fc56c2a394c84493c87a4de2b84898497dda2754dd0fd8cb53
unix/portfile.cmake 7b5bfbfb19d02de85f009a2ad132169624618ebe74dd3a208029eae09dc6551c
unix/remove-deps.cmake 6f9560155faa36f82e249d20b80d28edebd546c55db144b8dabbe0affea6ab14
usage 2f31595ba815f7c986ea9fed0f198f7a2691dc027f9e1a39dd3093753ed8bc79
vcpkg-cmake de2f19a93d26a154da64df86ba1d2823b5bbb0e3dda4e121cff4247a722ba6e3
vcpkg-cmake-config 68218f23fdf68b646a486fb74a5664cc5cc029fd557a1728d07ec73c1702bfd6
vcpkg-cmake-get-vars 846d6d7a1349a1ea0d1210cf8b3e143c4dc055654c709cdac724091ddd735538
vcpkg-cmake-wrapper.cmake.in b29a6ba88f6f26f03a70cf57c31beb6de58ba0306ce8f3028f52fef4b86aec13
vcpkg.json 996c881ee23b0e665078d936f3b4b8f7224cb192cdeefb1a3bcce11995de216c
vcpkg_acquire_msys 251a30badbb2082236811ba6975680d0d012a20dd55db7e39ccbc6cc49d67287
vcpkg_add_to_path 5f5ae75cf37b2a58d1a8561ca96496b64cd91ec9a0afab0b976c3e5d59030bfe
vcpkg_build_nmake 04f244a60749279ee930d3ad9d6f1a659ed280fbd958b3d84c73b10ade4e501c
vcpkg_check_linkage f8d5dc4ee94493155b76fb825a74e9c4f6037569b990c3f8c02074338223fbbd
vcpkg_configure_make 1e2d99e6035a77741312f71dc56f8b339f63b03fe5bea18971a052f624ef49a9
vcpkg_copy_pdbs d15c2f2822d93ecb9023af44803328e491c5374136e0813600289582a72d196d
vcpkg_copy_tools 3d45ff761bddbabe8923b52330168dc3abd295fa469d3f2e47cb14dce85332d5
vcpkg_find_acquire_program 03722d714d388b19731f5c0be35996c25a47d5b35dd6a09669ea1e599a56e005
vcpkg_find_acquire_program(CLANG) a7e999cf1fc40ca40cdb36a096d9ac97ebaa223d1eb9c3571974d8030bdf3ce0
vcpkg_find_acquire_program(NASM) ff801b330d90fb0a43f4588e55c37df774e2cf2304e784dc09e5388a9a17dfba
vcpkg_find_acquire_program(PERL) 187adedd1721a961e70595c183a93ab5ead976e340be77690c551f4c7f0111dd
vcpkg_fixup_pkgconfig 1a15f6c6d8e2b244d83a7514a0412d339127d2217d1df60ad1388b546c85f777
vcpkg_from_git 96ed81968f76354c00096dd8cd4e63c6a235fa969334a11ab18d11c0c512ff58
vcpkg_from_github b743742296a114ea1b18ae99672e02f142c4eb2bef7f57d36c038bedbfb0502f
vcpkg_host_path_list 1e8382b3a80fc7458293fce1659077dcacda9892eb63d911ae0330fdb1aedc2d
vcpkg_install_copyright ba6c169ab4e59fa05682e530cdeb883767de22c8391f023d4e6844a7ec5dd3d2
vcpkg_install_make ac7542b80f3a575fb396013378d8834eb2afa397fa7ab207969cacbca049b98f
vcpkg_list f5de3ebcbc40a4db90622ade9aca918e2cf404dc0d91342fcde457d730e6fa29
windows/install-layout.patch 06bf9d3f9d78a99d6472bd8a63442065a90cf413af5c4e2946348bece1ce50f2
windows/install-pdbs.patch e913fdc775b5e7be3eb71bfc223d51c3d8fd7e933410493efdbc53c5a1e1f3b7
windows/portfile.cmake 7f262ffb0219b3a0e2db7b172bec9ccad299898ac934f8de1e449fb749052582
