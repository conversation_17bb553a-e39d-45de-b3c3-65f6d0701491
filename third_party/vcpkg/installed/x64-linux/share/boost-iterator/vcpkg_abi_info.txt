boost-assert 101a0a4072390892d7faa6ddd1ae78eda684a04c755dc2b61421263cd6b80039
boost-cmake cbe23a80263643a95f4366123c43fb9d07e4b7c0eee36f9c233ce7e6f77856b8
boost-concept-check a07bd0996355d119091b2080b573d54bf0d4acd3693113dbee8748bf3fb714e8
boost-config c3f5199231a5255ebb729e44013c850064331e0d3ffd3694d49c6052c596b162
boost-core 853b27c1ef82549ba1681bf403063685db49d68fe328c891548af179b939f16e
boost-detail 54143b147e5c0141aa1823846c755a28fc9fc2d0e542babbcd2898e1825d5746
boost-function-types 1f387ea669b6f1ab72fbb9df56e7971321e241de6c01c954096ea9e3f367f6a9
boost-fusion 2bbd3b161b58181c111bbc922bfc12b0923bee074b5bdeb09489a2c7714c9e47
boost-headers fb9be230902d605763df2d977a58101c6edb81544f24a140dfef1a6b3d7e8f9e
boost-mpl a96d23aa920592bca6d72724320f9d39282d2101a29d768283004b1e685c20d6
boost-optional 84359084f71c96fdfd2c84cc008952445b32b2dbc1ebd9f7140efd5da5894a00
boost-smart-ptr 865110b792ce44a387fbfcbd5e7f02ade45fa64e19ab4ee2bea4ce311fe9b5df
boost-static-assert 2121e5fa49fcaefd8a0caf8b99c6769fc1ffc90b8d89a3327b084adc113b996a
boost-type-traits 40e7e2cefeeb0012dd4a1d647dd3214ec0fb860d50a004a81f698cf0a1e7e505
boost-utility 073f4bafebf4427571f82cb6192b69a9a202873a68deed00a7347dd0a2eadb92
cmake 3.30.5
features core
portfile.cmake 8835a9bbd923388e898e6f5a20460fdf144dbe96e177017354d4201a5d5cd442
ports.cmake a9f8f745f9f4d225039f9810e79670f10f38c7455222865616ce430a06f75d1f
post_build_checks 2
triplet x64-linux
triplet_abi 071689e8a9e42200a224208c0a52e5107f9f7e42dfb67eb092ae041012550f88-7300d6fc76582335b0d9fd6dc6a00f69d7ba728b9d6444ad2a9d42f054e1d991-a9cc48706cca742e5b6b955b1702a49b2a65371e
vcpkg.json 3d1a23730e726dfcf22d49854b1a81ba8594d1b81fb6bca69cff4167be216c68
vcpkg_from_git 96ed81968f76354c00096dd8cd4e63c6a235fa969334a11ab18d11c0c512ff58
vcpkg_from_github b743742296a114ea1b18ae99672e02f142c4eb2bef7f57d36c038bedbfb0502f
