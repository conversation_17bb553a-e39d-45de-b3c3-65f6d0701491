boost-align c5d809b532b1ff19fb36a411d45c85d5016d2139326373e6cf4f61ac81d1a9eb
boost-assert 101a0a4072390892d7faa6ddd1ae78eda684a04c755dc2b61421263cd6b80039
boost-cmake cbe23a80263643a95f4366123c43fb9d07e4b7c0eee36f9c233ce7e6f77856b8
boost-config c3f5199231a5255ebb729e44013c850064331e0d3ffd3694d49c6052c596b162
boost-context 0b49c2a6dca78de086f3ee7871b0fd6a415089264539e54ea0b9bdf85a04d616
boost-date-time 54c0c9a66086f20deb1a2bbfdd74945eb8c35385815209ee02c21e105935df41
boost-headers fb9be230902d605763df2d977a58101c6edb81544f24a140dfef1a6b3d7e8f9e
boost-system d3d87ab839b35d90cd0f768250fa6f39cae155118aff339a03c6ae2821867802
boost-throw-exception 2ba6715e3f4e8690930203481474cfe4967ef01b04fcb9abbf6ec3180b1ebff4
cmake 3.30.5
features core
opt-dep.diff 2a139ba11c2f5502cdbe1536da68068b99c21162ae95e850ac696f469d9572f6
portfile.cmake 2b32d1d229db61b2a4c1026ef40eb00c208eb2e42fe2660591c05b99be10a89e
ports.cmake a9f8f745f9f4d225039f9810e79670f10f38c7455222865616ce430a06f75d1f
post_build_checks 2
triplet x64-linux
triplet_abi 071689e8a9e42200a224208c0a52e5107f9f7e42dfb67eb092ae041012550f88-7300d6fc76582335b0d9fd6dc6a00f69d7ba728b9d6444ad2a9d42f054e1d991-a9cc48706cca742e5b6b955b1702a49b2a65371e
vcpkg.json e87241c3eadbda3cdf6496c022b5afcd199563c02e4cc939c1636d3447da68d0
vcpkg_from_git 96ed81968f76354c00096dd8cd4e63c6a235fa969334a11ab18d11c0c512ff58
vcpkg_from_github b743742296a114ea1b18ae99672e02f142c4eb2bef7f57d36c038bedbfb0502f
