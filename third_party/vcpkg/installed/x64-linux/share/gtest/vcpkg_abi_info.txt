001-fix-UWP-death-test.patch fca2d973dfbca64b3575d7453a189c3ea9db4e413838cd40343d39f64d813f3b
clang-tidy-no-lint.patch f276fb224af39a1e6ad9a88719bd49f2ccc88b7837763b2b4f3d74f107894957
cmake 3.30.5
features core
fix-main-lib-path.patch 40f5766826c684a063649f308531ba2efe829d540255f99db0be0aa43fb6a691
portfile.cmake 91e9824300769d9362f91dca62ef253f0d354769d19f6432d80f9321a8401788
ports.cmake a9f8f745f9f4d225039f9810e79670f10f38c7455222865616ce430a06f75d1f
post_build_checks 2
triplet x64-linux
triplet_abi 071689e8a9e42200a224208c0a52e5107f9f7e42dfb67eb092ae041012550f88-7300d6fc76582335b0d9fd6dc6a00f69d7ba728b9d6444ad2a9d42f054e1d991-a9cc48706cca742e5b6b955b1702a49b2a65371e
usage 48a60f73437a325fd92cebb25c77ce48108f20610836039420b7e2070c91132a
vcpkg-cmake de2f19a93d26a154da64df86ba1d2823b5bbb0e3dda4e121cff4247a722ba6e3
vcpkg-cmake-config 68218f23fdf68b646a486fb74a5664cc5cc029fd557a1728d07ec73c1702bfd6
vcpkg.json 91dcdf645f7523338fa7d74332ed5f140837ad5bc99d7952e3a55a817bab31c4
vcpkg_copy_pdbs d15c2f2822d93ecb9023af44803328e491c5374136e0813600289582a72d196d
vcpkg_fixup_pkgconfig 1a15f6c6d8e2b244d83a7514a0412d339127d2217d1df60ad1388b546c85f777
vcpkg_from_git 96ed81968f76354c00096dd8cd4e63c6a235fa969334a11ab18d11c0c512ff58
vcpkg_from_github b743742296a114ea1b18ae99672e02f142c4eb2bef7f57d36c038bedbfb0502f
vcpkg_replace_string b450deb79207478b37119743e00808ebc42de0628e7b98c14ab24728bd5c78b8
