{"$schema": "https://raw.githubusercontent.com/spdx/spdx-spec/v2.2.1/schemas/spdx-schema.json", "spdxVersion": "SPDX-2.2", "dataLicense": "CC0-1.0", "SPDXID": "SPDXRef-DOCUMENT", "documentNamespace": "https://spdx.org/spdxdocs/gtest-x64-linux-1.16.0#1-dcc985a0-bcbe-49cc-83fd-aae4ab270600", "name": "gtest:x64-linux@1.16.0#1 298020ff275ce6d1bb2139d000d270d91aac3e81682166ca180d9f4cdf4802ac", "creationInfo": {"creators": ["Tool: vcpkg-2025-04-07-b8b513ba8778c918cff49c3e837aae5999d5d2aa"], "created": "2025-06-10T09:08:11Z"}, "relationships": [{"spdxElementId": "SPDXRef-port", "relationshipType": "GENERATES", "relatedSpdxElement": "SPDXRef-binary"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-0"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-1"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-2"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-3"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-4"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-5"}, {"spdxElementId": "SPDXRef-binary", "relationshipType": "GENERATED_FROM", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-0", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-1", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-2", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-2", "relationshipType": "DEPENDENCY_MANIFEST_OF", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-3", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-4", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-5", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}], "packages": [{"name": "gtest", "SPDXID": "SPDXRef-port", "versionInfo": "1.16.0#1", "downloadLocation": "git+https://github.com/Microsoft/vcpkg#ports/gtest", "homepage": "https://github.com/google/googletest", "licenseConcluded": "BSD-3-<PERSON><PERSON>", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "description": "Google Testing and Mocking Framework", "comment": "This is the port (recipe) consumed by vcpkg."}, {"name": "gtest:x64-linux", "SPDXID": "SPDXRef-binary", "versionInfo": "298020ff275ce6d1bb2139d000d270d91aac3e81682166ca180d9f4cdf4802ac", "downloadLocation": "NONE", "licenseConcluded": "BSD-3-<PERSON><PERSON>", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "comment": "This is a binary package built by vcpkg."}, {"SPDXID": "SPDXRef-resource-0", "name": "google/googletest", "downloadLocation": "git+https://github.com/google/googletest@v1.16.0", "licenseConcluded": "NOASSERTION", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "checksums": [{"algorithm": "SHA512", "checksumValue": "bec8dad2a5abbea8e9e5f0ceedd8c9dbdb8939e9f74785476b0948f21f5db5901018157e78387e106c6717326558d6642fc0e39379c62af57bf1205a9df8a18b"}]}], "files": [{"fileName": "./clang-tidy-no-lint.patch", "SPDXID": "SPDXRef-file-0", "checksums": [{"algorithm": "SHA256", "checksumValue": "f276fb224af39a1e6ad9a88719bd49f2ccc88b7837763b2b4f3d74f107894957"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./usage", "SPDXID": "SPDXRef-file-1", "checksums": [{"algorithm": "SHA256", "checksumValue": "48a60f73437a325fd92cebb25c77ce48108f20610836039420b7e2070c91132a"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./vcpkg.json", "SPDXID": "SPDXRef-file-2", "checksums": [{"algorithm": "SHA256", "checksumValue": "91dcdf645f7523338fa7d74332ed5f140837ad5bc99d7952e3a55a817bab31c4"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./portfile.cmake", "SPDXID": "SPDXRef-file-3", "checksums": [{"algorithm": "SHA256", "checksumValue": "91e9824300769d9362f91dca62ef253f0d354769d19f6432d80f9321a8401788"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./001-fix-UWP-death-test.patch", "SPDXID": "SPDXRef-file-4", "checksums": [{"algorithm": "SHA256", "checksumValue": "fca2d973dfbca64b3575d7453a189c3ea9db4e413838cd40343d39f64d813f3b"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./fix-main-lib-path.patch", "SPDXID": "SPDXRef-file-5", "checksums": [{"algorithm": "SHA256", "checksumValue": "40f5766826c684a063649f308531ba2efe829d540255f99db0be0aa43fb6a691"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}]}