boost-assert 101a0a4072390892d7faa6ddd1ae78eda684a04c755dc2b61421263cd6b80039
boost-atomic bd2449aeb2aed7e8ed78f52644458ba79daa0eb3e29517da68fcb88ae4ba3222
boost-bind 891cb28813980add6f5ade53ae8da21bddc5b28fe07fb9c9f6807477c5e2f3f0
boost-chrono 20d7a1072b6b8875547555f8bee59e1e586bdb47e1ad0aafb66591ffcc00c85a
boost-cmake cbe23a80263643a95f4366123c43fb9d07e4b7c0eee36f9c233ce7e6f77856b8
boost-concept-check a07bd0996355d119091b2080b573d54bf0d4acd3693113dbee8748bf3fb714e8
boost-config c3f5199231a5255ebb729e44013c850064331e0d3ffd3694d49c6052c596b162
boost-container c5d7e7d9fd117d48dc6fddc073ee3ac56fa1220f0f9df415bd7e05e387c35bd4
boost-container-hash 6119a7803fbcb1a3b67d17ee5fa7c4ff9fabe02bc01bfef6ef6426bbae05a58f
boost-core 853b27c1ef82549ba1681bf403063685db49d68fe328c891548af179b939f16e
boost-date-time 54c0c9a66086f20deb1a2bbfdd74945eb8c35385815209ee02c21e105935df41
boost-exception a095c82a2819fdcb8ad6ad8aede331148edcced6f96a0ede724f73f6c3d0fbd7
boost-function 1386da11bd1baf45dbe87c948ba905e70fef9c4601fa47c0d8e7f97516f238d7
boost-headers fb9be230902d605763df2d977a58101c6edb81544f24a140dfef1a6b3d7e8f9e
boost-io f58b5e79d272b623417f3d4a2211795a2f00f576adea5f52fa5ad85a855baa80
boost-move 5918c7c7dde783987266c8746a74fe58c10229d4d3a46c0c790fdd1fe7acb260
boost-optional 84359084f71c96fdfd2c84cc008952445b32b2dbc1ebd9f7140efd5da5894a00
boost-predef 2982d099a602f945477ba8f8c3a903a1e691fab493fc5d9eb04341257bf952ef
boost-preprocessor e85fb5253d74d0384c1f3d163cd4c0a57667237bbf8935423adae6b5b1bd0dfc
boost-smart-ptr 865110b792ce44a387fbfcbd5e7f02ade45fa64e19ab4ee2bea4ce311fe9b5df
boost-static-assert 2121e5fa49fcaefd8a0caf8b99c6769fc1ffc90b8d89a3327b084adc113b996a
boost-system d3d87ab839b35d90cd0f768250fa6f39cae155118aff339a03c6ae2821867802
boost-throw-exception 2ba6715e3f4e8690930203481474cfe4967ef01b04fcb9abbf6ec3180b1ebff4
boost-tuple bebb58d4b19538f33e4ac59ab99c0ecd7377a19da699a41cc408dd923132a453
boost-type-traits 40e7e2cefeeb0012dd4a1d647dd3214ec0fb860d50a004a81f698cf0a1e7e505
boost-utility 073f4bafebf4427571f82cb6192b69a9a202873a68deed00a7347dd0a2eadb92
boost-winapi 1d13e34101c5580b76b8a94398bd640186ffc4e98e1a3904c24c800d9461f9e9
cmake 3.30.5
features core
portfile.cmake e15f58e9c744741427152872d36cbd683e69c93f85ac179238ba871cec311b60
ports.cmake a9f8f745f9f4d225039f9810e79670f10f38c7455222865616ce430a06f75d1f
post_build_checks 2
triplet x64-linux
triplet_abi 071689e8a9e42200a224208c0a52e5107f9f7e42dfb67eb092ae041012550f88-7300d6fc76582335b0d9fd6dc6a00f69d7ba728b9d6444ad2a9d42f054e1d991-a9cc48706cca742e5b6b955b1702a49b2a65371e
vcpkg.json 7d07a1e8e51aaab9823f5753a7d07e7c333161417634a73aca5558f337e46fa9
vcpkg_from_git 96ed81968f76354c00096dd8cd4e63c6a235fa969334a11ab18d11c0c512ff58
vcpkg_from_github b743742296a114ea1b18ae99672e02f142c4eb2bef7f57d36c038bedbfb0502f
