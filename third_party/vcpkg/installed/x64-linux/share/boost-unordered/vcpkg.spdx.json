{"$schema": "https://raw.githubusercontent.com/spdx/spdx-spec/v2.2.1/schemas/spdx-schema.json", "spdxVersion": "SPDX-2.2", "dataLicense": "CC0-1.0", "SPDXID": "SPDXRef-DOCUMENT", "documentNamespace": "https://spdx.org/spdxdocs/boost-unordered-x64-linux-1.87.0#1-d8d5df26-ac22-45aa-a734-6e7d752f164d", "name": "boost-unordered:x64-linux@1.87.0#1 bcec401512c11505d1f703f24247331d7aecae27656f4ed9acccaa901b163c50", "creationInfo": {"creators": ["Tool: vcpkg-2025-04-07-b8b513ba8778c918cff49c3e837aae5999d5d2aa"], "created": "2025-06-10T09:05:52Z"}, "relationships": [{"spdxElementId": "SPDXRef-port", "relationshipType": "GENERATES", "relatedSpdxElement": "SPDXRef-binary"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-0"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-1"}, {"spdxElementId": "SPDXRef-binary", "relationshipType": "GENERATED_FROM", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-0", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-0", "relationshipType": "DEPENDENCY_MANIFEST_OF", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-1", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}], "packages": [{"name": "boost-unordered", "SPDXID": "SPDXRef-port", "versionInfo": "1.87.0#1", "downloadLocation": "git+https://github.com/Microsoft/vcpkg#ports/boost-unordered", "homepage": "https://www.boost.org/libs/unordered", "licenseConcluded": "BSL-1.0", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "description": "Boost unordered module", "comment": "This is the port (recipe) consumed by vcpkg."}, {"name": "boost-unordered:x64-linux", "SPDXID": "SPDXRef-binary", "versionInfo": "bcec401512c11505d1f703f24247331d7aecae27656f4ed9acccaa901b163c50", "downloadLocation": "NONE", "licenseConcluded": "BSL-1.0", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "comment": "This is a binary package built by vcpkg."}, {"SPDXID": "SPDXRef-resource-0", "name": "boostorg/unordered", "downloadLocation": "git+https://github.com/boostorg/unordered@boost-1.87.0", "licenseConcluded": "NOASSERTION", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "checksums": [{"algorithm": "SHA512", "checksumValue": "c28cc7c7bbb9fa04634f9fa4d371e31df084a64177e9d6b0395e82084f513aac3ec01cdb3b0fa7019902c963dce6cfac65da5e66d444932c005b0430e69471cf"}]}, {"SPDXID": "SPDXRef-resource-1", "name": "boost-unordered-arm32-07f6463c1c302c5b1d28aa253e0b768e71c90235.patch", "packageFileName": "boost-unordered-arm32-07f6463c1c302c5b1d28aa253e0b768e71c90235.patch", "downloadLocation": "https://github.com/boostorg/unordered/commit/07f6463c1c302c5b1d28aa253e0b768e71c90235.patch?full_index=1", "licenseConcluded": "NOASSERTION", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "checksums": [{"algorithm": "SHA512", "checksumValue": "99f631e57b0c7d8d08f32d994c34dbca2588b409c10ad35500c36dc5374dba91888ee5ba3ca7ed24fa75539e9d44ffc123892d859a736f63ca0ae5fffee2c178"}]}], "files": [{"fileName": "./vcpkg.json", "SPDXID": "SPDXRef-file-0", "checksums": [{"algorithm": "SHA256", "checksumValue": "330d92342b5a51413fc1b2721223465e064b0ee4fa22c39b1ac6812663626720"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./portfile.cmake", "SPDXID": "SPDXRef-file-1", "checksums": [{"algorithm": "SHA256", "checksumValue": "fbd6dac025363039fbcf83eee39989e9cfed8429d9269c057dc2ff71e254d9bc"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}]}