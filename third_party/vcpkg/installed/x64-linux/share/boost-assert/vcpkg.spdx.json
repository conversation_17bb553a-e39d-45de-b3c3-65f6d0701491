{"$schema": "https://raw.githubusercontent.com/spdx/spdx-spec/v2.2.1/schemas/spdx-schema.json", "spdxVersion": "SPDX-2.2", "dataLicense": "CC0-1.0", "SPDXID": "SPDXRef-DOCUMENT", "documentNamespace": "https://spdx.org/spdxdocs/boost-assert-x64-linux-1.87.0-15f37fa5-f0ac-4100-b701-f7a8290fe5e8", "name": "boost-assert:x64-linux@1.87.0 101a0a4072390892d7faa6ddd1ae78eda684a04c755dc2b61421263cd6b80039", "creationInfo": {"creators": ["Tool: vcpkg-2025-04-07-b8b513ba8778c918cff49c3e837aae5999d5d2aa"], "created": "2025-06-10T09:04:25Z"}, "relationships": [{"spdxElementId": "SPDXRef-port", "relationshipType": "GENERATES", "relatedSpdxElement": "SPDXRef-binary"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-0"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-1"}, {"spdxElementId": "SPDXRef-binary", "relationshipType": "GENERATED_FROM", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-0", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-0", "relationshipType": "DEPENDENCY_MANIFEST_OF", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-1", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}], "packages": [{"name": "boost-assert", "SPDXID": "SPDXRef-port", "versionInfo": "1.87.0", "downloadLocation": "git+https://github.com/Microsoft/vcpkg#ports/boost-assert", "homepage": "https://www.boost.org/libs/assert", "licenseConcluded": "BSL-1.0", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "description": "Boost assert module", "comment": "This is the port (recipe) consumed by vcpkg."}, {"name": "boost-assert:x64-linux", "SPDXID": "SPDXRef-binary", "versionInfo": "101a0a4072390892d7faa6ddd1ae78eda684a04c755dc2b61421263cd6b80039", "downloadLocation": "NONE", "licenseConcluded": "BSL-1.0", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "comment": "This is a binary package built by vcpkg."}, {"SPDXID": "SPDXRef-resource-0", "name": "boostorg/assert", "downloadLocation": "git+https://github.com/boostorg/assert@boost-1.87.0", "licenseConcluded": "NOASSERTION", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "checksums": [{"algorithm": "SHA512", "checksumValue": "da141f67e0c8926e21d4fae6a1665f1706b300b1a85c1d870546eb74d7382369148846bb939d1d23b84fb0dfc542fdc1dafed613f34adacc5931df7655406276"}]}], "files": [{"fileName": "./vcpkg.json", "SPDXID": "SPDXRef-file-0", "checksums": [{"algorithm": "SHA256", "checksumValue": "f6cfc747e4fb327664e628c91655bc6a3328a4ae0f50b0cd62d0f61595ab0609"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./portfile.cmake", "SPDXID": "SPDXRef-file-1", "checksums": [{"algorithm": "SHA256", "checksumValue": "bf3aac9e9a7340f8d67548e6d2584a46cdca167d2f6848cfa4fa9a1d6a846a45"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}]}