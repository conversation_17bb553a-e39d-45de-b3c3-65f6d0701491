boost-cmake cbe23a80263643a95f4366123c43fb9d07e4b7c0eee36f9c233ce7e6f77856b8
boost-config c3f5199231a5255ebb729e44013c850064331e0d3ffd3694d49c6052c596b162
boost-container-hash 6119a7803fbcb1a3b67d17ee5fa7c4ff9fabe02bc01bfef6ef6426bbae05a58f
boost-core 853b27c1ef82549ba1681bf403063685db49d68fe328c891548af179b939f16e
boost-function-types 1f387ea669b6f1ab72fbb9df56e7971321e241de6c01c954096ea9e3f367f6a9
boost-functional bed8a3d10a72a60a76661719ea7493aa3cff866c9e680f0681da73a713dbf296
boost-headers fb9be230902d605763df2d977a58101c6edb81544f24a140dfef1a6b3d7e8f9e
boost-mpl a96d23aa920592bca6d72724320f9d39282d2101a29d768283004b1e685c20d6
boost-preprocessor e85fb5253d74d0384c1f3d163cd4c0a57667237bbf8935423adae6b5b1bd0dfc
boost-static-assert 2121e5fa49fcaefd8a0caf8b99c6769fc1ffc90b8d89a3327b084adc113b996a
boost-tuple bebb58d4b19538f33e4ac59ab99c0ecd7377a19da699a41cc408dd923132a453
boost-type-traits 40e7e2cefeeb0012dd4a1d647dd3214ec0fb860d50a004a81f698cf0a1e7e505
boost-typeof d4b357994159d294f9fdb113f9e2ddd024f394ab80296b8dc9e574cab2d2299b
boost-utility 073f4bafebf4427571f82cb6192b69a9a202873a68deed00a7347dd0a2eadb92
cmake 3.30.5
features core
portfile.cmake b27063725518f38912ac10430cc4e4b9203457ae21f7cef350d9effb3e513ded
ports.cmake a9f8f745f9f4d225039f9810e79670f10f38c7455222865616ce430a06f75d1f
post_build_checks 2
triplet x64-linux
triplet_abi 071689e8a9e42200a224208c0a52e5107f9f7e42dfb67eb092ae041012550f88-7300d6fc76582335b0d9fd6dc6a00f69d7ba728b9d6444ad2a9d42f054e1d991-a9cc48706cca742e5b6b955b1702a49b2a65371e
vcpkg.json 53d1d34f9a0e7de607f511f06baadc8211624e2d378bed461c275751ebbdecf1
vcpkg_from_git 96ed81968f76354c00096dd8cd4e63c6a235fa969334a11ab18d11c0c512ff58
vcpkg_from_github b743742296a114ea1b18ae99672e02f142c4eb2bef7f57d36c038bedbfb0502f
