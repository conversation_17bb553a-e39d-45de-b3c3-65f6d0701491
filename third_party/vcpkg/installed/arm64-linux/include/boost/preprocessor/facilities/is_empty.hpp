# /* **************************************************************************
#  *                                                                          *
#  *     (C) Copyright <PERSON> 2003.
#  *     (C) Copyright <PERSON> 2014.
#  *     Distributed under the Boost Software License, Version 1.0. (See
#  *     accompanying file LICENSE_1_0.txt or copy at
#  *     http://www.boost.org/LICENSE_1_0.txt)
#  *                                                                          *
#  ************************************************************************** */
#
# /* See http://www.boost.org for most recent version. */
#
# ifndef BOOST_PREPROCESSOR_FACILITIES_IS_EMPTY_HPP
# define BOOST_PREPROCESSOR_FACILITIES_IS_EMPTY_HPP
#
# include <boost/preprocessor/config/config.hpp>
# include <boost/preprocessor/facilities/is_empty_variadic.hpp>
#
# endif /* BOOST_PREPROCESSOR_FACILITIES_IS_EMPTY_HPP */
