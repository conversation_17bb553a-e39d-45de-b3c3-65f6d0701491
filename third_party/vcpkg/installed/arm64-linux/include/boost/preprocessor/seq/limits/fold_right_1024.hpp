# /* **************************************************************************
#  *                                                                          *
#  *     (C) Copyright <PERSON> 2002.
#  *     Distributed under the Boost Software License, Version 1.0. (See
#  *     accompanying file LICENSE_1_0.txt or copy at
#  *     http://www.boost.org/LICENSE_1_0.txt)
#  *                                                                          *
#  ************************************************************************** */
#
# /* Revised by <PERSON> (2020) */
#
# /* See http://www.boost.org for most recent version. */
#
# ifndef BOOST_PREPROCESSOR_SEQ_FOLD_RIGHT_1024_HPP
# define BOOST_PREPROCESSOR_SEQ_FOLD_RIGHT_1024_HPP
#
# define BOOST_PP_SEQ_FOLD_RIGHT_513(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_513(op, st, BOOST_PP_SEQ_REVERSE_S(514, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_514(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_514(op, st, BOOST_PP_SEQ_REVERSE_S(515, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_515(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_515(op, st, BOOST_PP_SEQ_REVERSE_S(516, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_516(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_516(op, st, BOOST_PP_SEQ_REVERSE_S(517, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_517(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_517(op, st, BOOST_PP_SEQ_REVERSE_S(518, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_518(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_518(op, st, BOOST_PP_SEQ_REVERSE_S(519, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_519(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_519(op, st, BOOST_PP_SEQ_REVERSE_S(520, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_520(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_520(op, st, BOOST_PP_SEQ_REVERSE_S(521, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_521(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_521(op, st, BOOST_PP_SEQ_REVERSE_S(522, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_522(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_522(op, st, BOOST_PP_SEQ_REVERSE_S(523, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_523(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_523(op, st, BOOST_PP_SEQ_REVERSE_S(524, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_524(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_524(op, st, BOOST_PP_SEQ_REVERSE_S(525, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_525(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_525(op, st, BOOST_PP_SEQ_REVERSE_S(526, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_526(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_526(op, st, BOOST_PP_SEQ_REVERSE_S(527, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_527(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_527(op, st, BOOST_PP_SEQ_REVERSE_S(528, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_528(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_528(op, st, BOOST_PP_SEQ_REVERSE_S(529, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_529(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_529(op, st, BOOST_PP_SEQ_REVERSE_S(530, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_530(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_530(op, st, BOOST_PP_SEQ_REVERSE_S(531, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_531(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_531(op, st, BOOST_PP_SEQ_REVERSE_S(532, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_532(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_532(op, st, BOOST_PP_SEQ_REVERSE_S(533, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_533(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_533(op, st, BOOST_PP_SEQ_REVERSE_S(534, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_534(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_534(op, st, BOOST_PP_SEQ_REVERSE_S(535, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_535(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_535(op, st, BOOST_PP_SEQ_REVERSE_S(536, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_536(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_536(op, st, BOOST_PP_SEQ_REVERSE_S(537, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_537(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_537(op, st, BOOST_PP_SEQ_REVERSE_S(538, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_538(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_538(op, st, BOOST_PP_SEQ_REVERSE_S(539, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_539(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_539(op, st, BOOST_PP_SEQ_REVERSE_S(540, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_540(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_540(op, st, BOOST_PP_SEQ_REVERSE_S(541, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_541(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_541(op, st, BOOST_PP_SEQ_REVERSE_S(542, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_542(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_542(op, st, BOOST_PP_SEQ_REVERSE_S(543, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_543(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_543(op, st, BOOST_PP_SEQ_REVERSE_S(544, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_544(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_544(op, st, BOOST_PP_SEQ_REVERSE_S(545, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_545(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_545(op, st, BOOST_PP_SEQ_REVERSE_S(546, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_546(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_546(op, st, BOOST_PP_SEQ_REVERSE_S(547, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_547(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_547(op, st, BOOST_PP_SEQ_REVERSE_S(548, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_548(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_548(op, st, BOOST_PP_SEQ_REVERSE_S(549, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_549(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_549(op, st, BOOST_PP_SEQ_REVERSE_S(550, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_550(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_550(op, st, BOOST_PP_SEQ_REVERSE_S(551, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_551(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_551(op, st, BOOST_PP_SEQ_REVERSE_S(552, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_552(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_552(op, st, BOOST_PP_SEQ_REVERSE_S(553, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_553(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_553(op, st, BOOST_PP_SEQ_REVERSE_S(554, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_554(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_554(op, st, BOOST_PP_SEQ_REVERSE_S(555, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_555(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_555(op, st, BOOST_PP_SEQ_REVERSE_S(556, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_556(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_556(op, st, BOOST_PP_SEQ_REVERSE_S(557, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_557(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_557(op, st, BOOST_PP_SEQ_REVERSE_S(558, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_558(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_558(op, st, BOOST_PP_SEQ_REVERSE_S(559, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_559(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_559(op, st, BOOST_PP_SEQ_REVERSE_S(560, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_560(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_560(op, st, BOOST_PP_SEQ_REVERSE_S(561, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_561(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_561(op, st, BOOST_PP_SEQ_REVERSE_S(562, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_562(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_562(op, st, BOOST_PP_SEQ_REVERSE_S(563, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_563(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_563(op, st, BOOST_PP_SEQ_REVERSE_S(564, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_564(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_564(op, st, BOOST_PP_SEQ_REVERSE_S(565, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_565(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_565(op, st, BOOST_PP_SEQ_REVERSE_S(566, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_566(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_566(op, st, BOOST_PP_SEQ_REVERSE_S(567, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_567(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_567(op, st, BOOST_PP_SEQ_REVERSE_S(568, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_568(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_568(op, st, BOOST_PP_SEQ_REVERSE_S(569, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_569(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_569(op, st, BOOST_PP_SEQ_REVERSE_S(570, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_570(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_570(op, st, BOOST_PP_SEQ_REVERSE_S(571, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_571(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_571(op, st, BOOST_PP_SEQ_REVERSE_S(572, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_572(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_572(op, st, BOOST_PP_SEQ_REVERSE_S(573, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_573(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_573(op, st, BOOST_PP_SEQ_REVERSE_S(574, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_574(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_574(op, st, BOOST_PP_SEQ_REVERSE_S(575, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_575(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_575(op, st, BOOST_PP_SEQ_REVERSE_S(576, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_576(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_576(op, st, BOOST_PP_SEQ_REVERSE_S(577, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_577(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_577(op, st, BOOST_PP_SEQ_REVERSE_S(578, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_578(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_578(op, st, BOOST_PP_SEQ_REVERSE_S(579, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_579(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_579(op, st, BOOST_PP_SEQ_REVERSE_S(580, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_580(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_580(op, st, BOOST_PP_SEQ_REVERSE_S(581, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_581(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_581(op, st, BOOST_PP_SEQ_REVERSE_S(582, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_582(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_582(op, st, BOOST_PP_SEQ_REVERSE_S(583, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_583(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_583(op, st, BOOST_PP_SEQ_REVERSE_S(584, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_584(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_584(op, st, BOOST_PP_SEQ_REVERSE_S(585, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_585(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_585(op, st, BOOST_PP_SEQ_REVERSE_S(586, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_586(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_586(op, st, BOOST_PP_SEQ_REVERSE_S(587, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_587(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_587(op, st, BOOST_PP_SEQ_REVERSE_S(588, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_588(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_588(op, st, BOOST_PP_SEQ_REVERSE_S(589, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_589(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_589(op, st, BOOST_PP_SEQ_REVERSE_S(590, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_590(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_590(op, st, BOOST_PP_SEQ_REVERSE_S(591, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_591(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_591(op, st, BOOST_PP_SEQ_REVERSE_S(592, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_592(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_592(op, st, BOOST_PP_SEQ_REVERSE_S(593, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_593(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_593(op, st, BOOST_PP_SEQ_REVERSE_S(594, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_594(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_594(op, st, BOOST_PP_SEQ_REVERSE_S(595, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_595(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_595(op, st, BOOST_PP_SEQ_REVERSE_S(596, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_596(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_596(op, st, BOOST_PP_SEQ_REVERSE_S(597, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_597(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_597(op, st, BOOST_PP_SEQ_REVERSE_S(598, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_598(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_598(op, st, BOOST_PP_SEQ_REVERSE_S(599, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_599(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_599(op, st, BOOST_PP_SEQ_REVERSE_S(600, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_600(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_600(op, st, BOOST_PP_SEQ_REVERSE_S(601, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_601(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_601(op, st, BOOST_PP_SEQ_REVERSE_S(602, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_602(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_602(op, st, BOOST_PP_SEQ_REVERSE_S(603, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_603(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_603(op, st, BOOST_PP_SEQ_REVERSE_S(604, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_604(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_604(op, st, BOOST_PP_SEQ_REVERSE_S(605, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_605(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_605(op, st, BOOST_PP_SEQ_REVERSE_S(606, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_606(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_606(op, st, BOOST_PP_SEQ_REVERSE_S(607, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_607(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_607(op, st, BOOST_PP_SEQ_REVERSE_S(608, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_608(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_608(op, st, BOOST_PP_SEQ_REVERSE_S(609, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_609(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_609(op, st, BOOST_PP_SEQ_REVERSE_S(610, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_610(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_610(op, st, BOOST_PP_SEQ_REVERSE_S(611, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_611(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_611(op, st, BOOST_PP_SEQ_REVERSE_S(612, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_612(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_612(op, st, BOOST_PP_SEQ_REVERSE_S(613, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_613(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_613(op, st, BOOST_PP_SEQ_REVERSE_S(614, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_614(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_614(op, st, BOOST_PP_SEQ_REVERSE_S(615, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_615(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_615(op, st, BOOST_PP_SEQ_REVERSE_S(616, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_616(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_616(op, st, BOOST_PP_SEQ_REVERSE_S(617, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_617(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_617(op, st, BOOST_PP_SEQ_REVERSE_S(618, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_618(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_618(op, st, BOOST_PP_SEQ_REVERSE_S(619, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_619(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_619(op, st, BOOST_PP_SEQ_REVERSE_S(620, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_620(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_620(op, st, BOOST_PP_SEQ_REVERSE_S(621, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_621(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_621(op, st, BOOST_PP_SEQ_REVERSE_S(622, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_622(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_622(op, st, BOOST_PP_SEQ_REVERSE_S(623, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_623(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_623(op, st, BOOST_PP_SEQ_REVERSE_S(624, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_624(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_624(op, st, BOOST_PP_SEQ_REVERSE_S(625, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_625(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_625(op, st, BOOST_PP_SEQ_REVERSE_S(626, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_626(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_626(op, st, BOOST_PP_SEQ_REVERSE_S(627, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_627(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_627(op, st, BOOST_PP_SEQ_REVERSE_S(628, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_628(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_628(op, st, BOOST_PP_SEQ_REVERSE_S(629, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_629(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_629(op, st, BOOST_PP_SEQ_REVERSE_S(630, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_630(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_630(op, st, BOOST_PP_SEQ_REVERSE_S(631, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_631(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_631(op, st, BOOST_PP_SEQ_REVERSE_S(632, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_632(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_632(op, st, BOOST_PP_SEQ_REVERSE_S(633, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_633(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_633(op, st, BOOST_PP_SEQ_REVERSE_S(634, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_634(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_634(op, st, BOOST_PP_SEQ_REVERSE_S(635, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_635(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_635(op, st, BOOST_PP_SEQ_REVERSE_S(636, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_636(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_636(op, st, BOOST_PP_SEQ_REVERSE_S(637, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_637(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_637(op, st, BOOST_PP_SEQ_REVERSE_S(638, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_638(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_638(op, st, BOOST_PP_SEQ_REVERSE_S(639, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_639(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_639(op, st, BOOST_PP_SEQ_REVERSE_S(640, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_640(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_640(op, st, BOOST_PP_SEQ_REVERSE_S(641, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_641(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_641(op, st, BOOST_PP_SEQ_REVERSE_S(642, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_642(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_642(op, st, BOOST_PP_SEQ_REVERSE_S(643, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_643(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_643(op, st, BOOST_PP_SEQ_REVERSE_S(644, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_644(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_644(op, st, BOOST_PP_SEQ_REVERSE_S(645, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_645(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_645(op, st, BOOST_PP_SEQ_REVERSE_S(646, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_646(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_646(op, st, BOOST_PP_SEQ_REVERSE_S(647, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_647(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_647(op, st, BOOST_PP_SEQ_REVERSE_S(648, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_648(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_648(op, st, BOOST_PP_SEQ_REVERSE_S(649, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_649(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_649(op, st, BOOST_PP_SEQ_REVERSE_S(650, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_650(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_650(op, st, BOOST_PP_SEQ_REVERSE_S(651, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_651(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_651(op, st, BOOST_PP_SEQ_REVERSE_S(652, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_652(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_652(op, st, BOOST_PP_SEQ_REVERSE_S(653, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_653(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_653(op, st, BOOST_PP_SEQ_REVERSE_S(654, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_654(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_654(op, st, BOOST_PP_SEQ_REVERSE_S(655, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_655(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_655(op, st, BOOST_PP_SEQ_REVERSE_S(656, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_656(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_656(op, st, BOOST_PP_SEQ_REVERSE_S(657, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_657(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_657(op, st, BOOST_PP_SEQ_REVERSE_S(658, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_658(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_658(op, st, BOOST_PP_SEQ_REVERSE_S(659, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_659(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_659(op, st, BOOST_PP_SEQ_REVERSE_S(660, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_660(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_660(op, st, BOOST_PP_SEQ_REVERSE_S(661, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_661(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_661(op, st, BOOST_PP_SEQ_REVERSE_S(662, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_662(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_662(op, st, BOOST_PP_SEQ_REVERSE_S(663, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_663(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_663(op, st, BOOST_PP_SEQ_REVERSE_S(664, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_664(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_664(op, st, BOOST_PP_SEQ_REVERSE_S(665, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_665(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_665(op, st, BOOST_PP_SEQ_REVERSE_S(666, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_666(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_666(op, st, BOOST_PP_SEQ_REVERSE_S(667, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_667(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_667(op, st, BOOST_PP_SEQ_REVERSE_S(668, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_668(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_668(op, st, BOOST_PP_SEQ_REVERSE_S(669, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_669(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_669(op, st, BOOST_PP_SEQ_REVERSE_S(670, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_670(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_670(op, st, BOOST_PP_SEQ_REVERSE_S(671, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_671(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_671(op, st, BOOST_PP_SEQ_REVERSE_S(672, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_672(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_672(op, st, BOOST_PP_SEQ_REVERSE_S(673, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_673(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_673(op, st, BOOST_PP_SEQ_REVERSE_S(674, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_674(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_674(op, st, BOOST_PP_SEQ_REVERSE_S(675, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_675(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_675(op, st, BOOST_PP_SEQ_REVERSE_S(676, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_676(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_676(op, st, BOOST_PP_SEQ_REVERSE_S(677, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_677(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_677(op, st, BOOST_PP_SEQ_REVERSE_S(678, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_678(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_678(op, st, BOOST_PP_SEQ_REVERSE_S(679, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_679(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_679(op, st, BOOST_PP_SEQ_REVERSE_S(680, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_680(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_680(op, st, BOOST_PP_SEQ_REVERSE_S(681, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_681(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_681(op, st, BOOST_PP_SEQ_REVERSE_S(682, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_682(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_682(op, st, BOOST_PP_SEQ_REVERSE_S(683, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_683(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_683(op, st, BOOST_PP_SEQ_REVERSE_S(684, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_684(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_684(op, st, BOOST_PP_SEQ_REVERSE_S(685, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_685(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_685(op, st, BOOST_PP_SEQ_REVERSE_S(686, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_686(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_686(op, st, BOOST_PP_SEQ_REVERSE_S(687, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_687(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_687(op, st, BOOST_PP_SEQ_REVERSE_S(688, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_688(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_688(op, st, BOOST_PP_SEQ_REVERSE_S(689, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_689(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_689(op, st, BOOST_PP_SEQ_REVERSE_S(690, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_690(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_690(op, st, BOOST_PP_SEQ_REVERSE_S(691, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_691(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_691(op, st, BOOST_PP_SEQ_REVERSE_S(692, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_692(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_692(op, st, BOOST_PP_SEQ_REVERSE_S(693, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_693(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_693(op, st, BOOST_PP_SEQ_REVERSE_S(694, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_694(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_694(op, st, BOOST_PP_SEQ_REVERSE_S(695, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_695(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_695(op, st, BOOST_PP_SEQ_REVERSE_S(696, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_696(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_696(op, st, BOOST_PP_SEQ_REVERSE_S(697, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_697(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_697(op, st, BOOST_PP_SEQ_REVERSE_S(698, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_698(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_698(op, st, BOOST_PP_SEQ_REVERSE_S(699, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_699(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_699(op, st, BOOST_PP_SEQ_REVERSE_S(700, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_700(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_700(op, st, BOOST_PP_SEQ_REVERSE_S(701, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_701(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_701(op, st, BOOST_PP_SEQ_REVERSE_S(702, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_702(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_702(op, st, BOOST_PP_SEQ_REVERSE_S(703, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_703(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_703(op, st, BOOST_PP_SEQ_REVERSE_S(704, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_704(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_704(op, st, BOOST_PP_SEQ_REVERSE_S(705, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_705(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_705(op, st, BOOST_PP_SEQ_REVERSE_S(706, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_706(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_706(op, st, BOOST_PP_SEQ_REVERSE_S(707, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_707(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_707(op, st, BOOST_PP_SEQ_REVERSE_S(708, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_708(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_708(op, st, BOOST_PP_SEQ_REVERSE_S(709, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_709(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_709(op, st, BOOST_PP_SEQ_REVERSE_S(710, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_710(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_710(op, st, BOOST_PP_SEQ_REVERSE_S(711, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_711(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_711(op, st, BOOST_PP_SEQ_REVERSE_S(712, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_712(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_712(op, st, BOOST_PP_SEQ_REVERSE_S(713, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_713(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_713(op, st, BOOST_PP_SEQ_REVERSE_S(714, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_714(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_714(op, st, BOOST_PP_SEQ_REVERSE_S(715, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_715(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_715(op, st, BOOST_PP_SEQ_REVERSE_S(716, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_716(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_716(op, st, BOOST_PP_SEQ_REVERSE_S(717, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_717(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_717(op, st, BOOST_PP_SEQ_REVERSE_S(718, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_718(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_718(op, st, BOOST_PP_SEQ_REVERSE_S(719, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_719(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_719(op, st, BOOST_PP_SEQ_REVERSE_S(720, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_720(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_720(op, st, BOOST_PP_SEQ_REVERSE_S(721, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_721(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_721(op, st, BOOST_PP_SEQ_REVERSE_S(722, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_722(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_722(op, st, BOOST_PP_SEQ_REVERSE_S(723, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_723(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_723(op, st, BOOST_PP_SEQ_REVERSE_S(724, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_724(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_724(op, st, BOOST_PP_SEQ_REVERSE_S(725, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_725(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_725(op, st, BOOST_PP_SEQ_REVERSE_S(726, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_726(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_726(op, st, BOOST_PP_SEQ_REVERSE_S(727, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_727(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_727(op, st, BOOST_PP_SEQ_REVERSE_S(728, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_728(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_728(op, st, BOOST_PP_SEQ_REVERSE_S(729, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_729(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_729(op, st, BOOST_PP_SEQ_REVERSE_S(730, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_730(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_730(op, st, BOOST_PP_SEQ_REVERSE_S(731, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_731(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_731(op, st, BOOST_PP_SEQ_REVERSE_S(732, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_732(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_732(op, st, BOOST_PP_SEQ_REVERSE_S(733, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_733(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_733(op, st, BOOST_PP_SEQ_REVERSE_S(734, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_734(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_734(op, st, BOOST_PP_SEQ_REVERSE_S(735, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_735(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_735(op, st, BOOST_PP_SEQ_REVERSE_S(736, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_736(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_736(op, st, BOOST_PP_SEQ_REVERSE_S(737, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_737(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_737(op, st, BOOST_PP_SEQ_REVERSE_S(738, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_738(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_738(op, st, BOOST_PP_SEQ_REVERSE_S(739, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_739(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_739(op, st, BOOST_PP_SEQ_REVERSE_S(740, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_740(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_740(op, st, BOOST_PP_SEQ_REVERSE_S(741, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_741(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_741(op, st, BOOST_PP_SEQ_REVERSE_S(742, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_742(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_742(op, st, BOOST_PP_SEQ_REVERSE_S(743, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_743(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_743(op, st, BOOST_PP_SEQ_REVERSE_S(744, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_744(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_744(op, st, BOOST_PP_SEQ_REVERSE_S(745, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_745(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_745(op, st, BOOST_PP_SEQ_REVERSE_S(746, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_746(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_746(op, st, BOOST_PP_SEQ_REVERSE_S(747, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_747(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_747(op, st, BOOST_PP_SEQ_REVERSE_S(748, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_748(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_748(op, st, BOOST_PP_SEQ_REVERSE_S(749, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_749(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_749(op, st, BOOST_PP_SEQ_REVERSE_S(750, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_750(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_750(op, st, BOOST_PP_SEQ_REVERSE_S(751, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_751(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_751(op, st, BOOST_PP_SEQ_REVERSE_S(752, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_752(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_752(op, st, BOOST_PP_SEQ_REVERSE_S(753, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_753(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_753(op, st, BOOST_PP_SEQ_REVERSE_S(754, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_754(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_754(op, st, BOOST_PP_SEQ_REVERSE_S(755, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_755(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_755(op, st, BOOST_PP_SEQ_REVERSE_S(756, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_756(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_756(op, st, BOOST_PP_SEQ_REVERSE_S(757, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_757(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_757(op, st, BOOST_PP_SEQ_REVERSE_S(758, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_758(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_758(op, st, BOOST_PP_SEQ_REVERSE_S(759, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_759(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_759(op, st, BOOST_PP_SEQ_REVERSE_S(760, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_760(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_760(op, st, BOOST_PP_SEQ_REVERSE_S(761, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_761(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_761(op, st, BOOST_PP_SEQ_REVERSE_S(762, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_762(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_762(op, st, BOOST_PP_SEQ_REVERSE_S(763, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_763(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_763(op, st, BOOST_PP_SEQ_REVERSE_S(764, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_764(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_764(op, st, BOOST_PP_SEQ_REVERSE_S(765, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_765(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_765(op, st, BOOST_PP_SEQ_REVERSE_S(766, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_766(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_766(op, st, BOOST_PP_SEQ_REVERSE_S(767, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_767(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_767(op, st, BOOST_PP_SEQ_REVERSE_S(768, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_768(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_768(op, st, BOOST_PP_SEQ_REVERSE_S(769, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_769(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_769(op, st, BOOST_PP_SEQ_REVERSE_S(770, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_770(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_770(op, st, BOOST_PP_SEQ_REVERSE_S(771, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_771(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_771(op, st, BOOST_PP_SEQ_REVERSE_S(772, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_772(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_772(op, st, BOOST_PP_SEQ_REVERSE_S(773, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_773(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_773(op, st, BOOST_PP_SEQ_REVERSE_S(774, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_774(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_774(op, st, BOOST_PP_SEQ_REVERSE_S(775, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_775(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_775(op, st, BOOST_PP_SEQ_REVERSE_S(776, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_776(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_776(op, st, BOOST_PP_SEQ_REVERSE_S(777, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_777(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_777(op, st, BOOST_PP_SEQ_REVERSE_S(778, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_778(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_778(op, st, BOOST_PP_SEQ_REVERSE_S(779, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_779(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_779(op, st, BOOST_PP_SEQ_REVERSE_S(780, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_780(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_780(op, st, BOOST_PP_SEQ_REVERSE_S(781, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_781(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_781(op, st, BOOST_PP_SEQ_REVERSE_S(782, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_782(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_782(op, st, BOOST_PP_SEQ_REVERSE_S(783, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_783(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_783(op, st, BOOST_PP_SEQ_REVERSE_S(784, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_784(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_784(op, st, BOOST_PP_SEQ_REVERSE_S(785, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_785(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_785(op, st, BOOST_PP_SEQ_REVERSE_S(786, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_786(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_786(op, st, BOOST_PP_SEQ_REVERSE_S(787, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_787(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_787(op, st, BOOST_PP_SEQ_REVERSE_S(788, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_788(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_788(op, st, BOOST_PP_SEQ_REVERSE_S(789, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_789(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_789(op, st, BOOST_PP_SEQ_REVERSE_S(790, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_790(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_790(op, st, BOOST_PP_SEQ_REVERSE_S(791, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_791(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_791(op, st, BOOST_PP_SEQ_REVERSE_S(792, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_792(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_792(op, st, BOOST_PP_SEQ_REVERSE_S(793, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_793(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_793(op, st, BOOST_PP_SEQ_REVERSE_S(794, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_794(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_794(op, st, BOOST_PP_SEQ_REVERSE_S(795, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_795(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_795(op, st, BOOST_PP_SEQ_REVERSE_S(796, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_796(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_796(op, st, BOOST_PP_SEQ_REVERSE_S(797, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_797(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_797(op, st, BOOST_PP_SEQ_REVERSE_S(798, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_798(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_798(op, st, BOOST_PP_SEQ_REVERSE_S(799, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_799(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_799(op, st, BOOST_PP_SEQ_REVERSE_S(800, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_800(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_800(op, st, BOOST_PP_SEQ_REVERSE_S(801, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_801(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_801(op, st, BOOST_PP_SEQ_REVERSE_S(802, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_802(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_802(op, st, BOOST_PP_SEQ_REVERSE_S(803, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_803(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_803(op, st, BOOST_PP_SEQ_REVERSE_S(804, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_804(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_804(op, st, BOOST_PP_SEQ_REVERSE_S(805, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_805(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_805(op, st, BOOST_PP_SEQ_REVERSE_S(806, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_806(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_806(op, st, BOOST_PP_SEQ_REVERSE_S(807, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_807(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_807(op, st, BOOST_PP_SEQ_REVERSE_S(808, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_808(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_808(op, st, BOOST_PP_SEQ_REVERSE_S(809, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_809(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_809(op, st, BOOST_PP_SEQ_REVERSE_S(810, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_810(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_810(op, st, BOOST_PP_SEQ_REVERSE_S(811, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_811(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_811(op, st, BOOST_PP_SEQ_REVERSE_S(812, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_812(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_812(op, st, BOOST_PP_SEQ_REVERSE_S(813, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_813(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_813(op, st, BOOST_PP_SEQ_REVERSE_S(814, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_814(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_814(op, st, BOOST_PP_SEQ_REVERSE_S(815, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_815(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_815(op, st, BOOST_PP_SEQ_REVERSE_S(816, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_816(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_816(op, st, BOOST_PP_SEQ_REVERSE_S(817, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_817(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_817(op, st, BOOST_PP_SEQ_REVERSE_S(818, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_818(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_818(op, st, BOOST_PP_SEQ_REVERSE_S(819, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_819(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_819(op, st, BOOST_PP_SEQ_REVERSE_S(820, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_820(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_820(op, st, BOOST_PP_SEQ_REVERSE_S(821, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_821(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_821(op, st, BOOST_PP_SEQ_REVERSE_S(822, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_822(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_822(op, st, BOOST_PP_SEQ_REVERSE_S(823, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_823(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_823(op, st, BOOST_PP_SEQ_REVERSE_S(824, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_824(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_824(op, st, BOOST_PP_SEQ_REVERSE_S(825, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_825(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_825(op, st, BOOST_PP_SEQ_REVERSE_S(826, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_826(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_826(op, st, BOOST_PP_SEQ_REVERSE_S(827, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_827(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_827(op, st, BOOST_PP_SEQ_REVERSE_S(828, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_828(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_828(op, st, BOOST_PP_SEQ_REVERSE_S(829, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_829(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_829(op, st, BOOST_PP_SEQ_REVERSE_S(830, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_830(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_830(op, st, BOOST_PP_SEQ_REVERSE_S(831, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_831(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_831(op, st, BOOST_PP_SEQ_REVERSE_S(832, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_832(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_832(op, st, BOOST_PP_SEQ_REVERSE_S(833, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_833(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_833(op, st, BOOST_PP_SEQ_REVERSE_S(834, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_834(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_834(op, st, BOOST_PP_SEQ_REVERSE_S(835, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_835(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_835(op, st, BOOST_PP_SEQ_REVERSE_S(836, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_836(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_836(op, st, BOOST_PP_SEQ_REVERSE_S(837, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_837(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_837(op, st, BOOST_PP_SEQ_REVERSE_S(838, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_838(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_838(op, st, BOOST_PP_SEQ_REVERSE_S(839, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_839(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_839(op, st, BOOST_PP_SEQ_REVERSE_S(840, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_840(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_840(op, st, BOOST_PP_SEQ_REVERSE_S(841, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_841(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_841(op, st, BOOST_PP_SEQ_REVERSE_S(842, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_842(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_842(op, st, BOOST_PP_SEQ_REVERSE_S(843, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_843(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_843(op, st, BOOST_PP_SEQ_REVERSE_S(844, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_844(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_844(op, st, BOOST_PP_SEQ_REVERSE_S(845, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_845(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_845(op, st, BOOST_PP_SEQ_REVERSE_S(846, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_846(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_846(op, st, BOOST_PP_SEQ_REVERSE_S(847, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_847(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_847(op, st, BOOST_PP_SEQ_REVERSE_S(848, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_848(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_848(op, st, BOOST_PP_SEQ_REVERSE_S(849, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_849(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_849(op, st, BOOST_PP_SEQ_REVERSE_S(850, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_850(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_850(op, st, BOOST_PP_SEQ_REVERSE_S(851, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_851(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_851(op, st, BOOST_PP_SEQ_REVERSE_S(852, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_852(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_852(op, st, BOOST_PP_SEQ_REVERSE_S(853, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_853(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_853(op, st, BOOST_PP_SEQ_REVERSE_S(854, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_854(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_854(op, st, BOOST_PP_SEQ_REVERSE_S(855, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_855(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_855(op, st, BOOST_PP_SEQ_REVERSE_S(856, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_856(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_856(op, st, BOOST_PP_SEQ_REVERSE_S(857, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_857(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_857(op, st, BOOST_PP_SEQ_REVERSE_S(858, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_858(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_858(op, st, BOOST_PP_SEQ_REVERSE_S(859, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_859(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_859(op, st, BOOST_PP_SEQ_REVERSE_S(860, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_860(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_860(op, st, BOOST_PP_SEQ_REVERSE_S(861, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_861(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_861(op, st, BOOST_PP_SEQ_REVERSE_S(862, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_862(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_862(op, st, BOOST_PP_SEQ_REVERSE_S(863, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_863(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_863(op, st, BOOST_PP_SEQ_REVERSE_S(864, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_864(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_864(op, st, BOOST_PP_SEQ_REVERSE_S(865, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_865(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_865(op, st, BOOST_PP_SEQ_REVERSE_S(866, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_866(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_866(op, st, BOOST_PP_SEQ_REVERSE_S(867, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_867(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_867(op, st, BOOST_PP_SEQ_REVERSE_S(868, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_868(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_868(op, st, BOOST_PP_SEQ_REVERSE_S(869, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_869(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_869(op, st, BOOST_PP_SEQ_REVERSE_S(870, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_870(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_870(op, st, BOOST_PP_SEQ_REVERSE_S(871, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_871(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_871(op, st, BOOST_PP_SEQ_REVERSE_S(872, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_872(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_872(op, st, BOOST_PP_SEQ_REVERSE_S(873, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_873(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_873(op, st, BOOST_PP_SEQ_REVERSE_S(874, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_874(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_874(op, st, BOOST_PP_SEQ_REVERSE_S(875, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_875(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_875(op, st, BOOST_PP_SEQ_REVERSE_S(876, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_876(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_876(op, st, BOOST_PP_SEQ_REVERSE_S(877, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_877(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_877(op, st, BOOST_PP_SEQ_REVERSE_S(878, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_878(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_878(op, st, BOOST_PP_SEQ_REVERSE_S(879, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_879(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_879(op, st, BOOST_PP_SEQ_REVERSE_S(880, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_880(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_880(op, st, BOOST_PP_SEQ_REVERSE_S(881, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_881(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_881(op, st, BOOST_PP_SEQ_REVERSE_S(882, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_882(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_882(op, st, BOOST_PP_SEQ_REVERSE_S(883, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_883(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_883(op, st, BOOST_PP_SEQ_REVERSE_S(884, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_884(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_884(op, st, BOOST_PP_SEQ_REVERSE_S(885, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_885(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_885(op, st, BOOST_PP_SEQ_REVERSE_S(886, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_886(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_886(op, st, BOOST_PP_SEQ_REVERSE_S(887, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_887(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_887(op, st, BOOST_PP_SEQ_REVERSE_S(888, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_888(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_888(op, st, BOOST_PP_SEQ_REVERSE_S(889, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_889(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_889(op, st, BOOST_PP_SEQ_REVERSE_S(890, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_890(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_890(op, st, BOOST_PP_SEQ_REVERSE_S(891, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_891(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_891(op, st, BOOST_PP_SEQ_REVERSE_S(892, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_892(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_892(op, st, BOOST_PP_SEQ_REVERSE_S(893, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_893(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_893(op, st, BOOST_PP_SEQ_REVERSE_S(894, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_894(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_894(op, st, BOOST_PP_SEQ_REVERSE_S(895, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_895(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_895(op, st, BOOST_PP_SEQ_REVERSE_S(896, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_896(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_896(op, st, BOOST_PP_SEQ_REVERSE_S(897, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_897(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_897(op, st, BOOST_PP_SEQ_REVERSE_S(898, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_898(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_898(op, st, BOOST_PP_SEQ_REVERSE_S(899, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_899(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_899(op, st, BOOST_PP_SEQ_REVERSE_S(900, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_900(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_900(op, st, BOOST_PP_SEQ_REVERSE_S(901, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_901(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_901(op, st, BOOST_PP_SEQ_REVERSE_S(902, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_902(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_902(op, st, BOOST_PP_SEQ_REVERSE_S(903, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_903(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_903(op, st, BOOST_PP_SEQ_REVERSE_S(904, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_904(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_904(op, st, BOOST_PP_SEQ_REVERSE_S(905, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_905(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_905(op, st, BOOST_PP_SEQ_REVERSE_S(906, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_906(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_906(op, st, BOOST_PP_SEQ_REVERSE_S(907, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_907(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_907(op, st, BOOST_PP_SEQ_REVERSE_S(908, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_908(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_908(op, st, BOOST_PP_SEQ_REVERSE_S(909, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_909(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_909(op, st, BOOST_PP_SEQ_REVERSE_S(910, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_910(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_910(op, st, BOOST_PP_SEQ_REVERSE_S(911, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_911(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_911(op, st, BOOST_PP_SEQ_REVERSE_S(912, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_912(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_912(op, st, BOOST_PP_SEQ_REVERSE_S(913, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_913(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_913(op, st, BOOST_PP_SEQ_REVERSE_S(914, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_914(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_914(op, st, BOOST_PP_SEQ_REVERSE_S(915, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_915(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_915(op, st, BOOST_PP_SEQ_REVERSE_S(916, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_916(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_916(op, st, BOOST_PP_SEQ_REVERSE_S(917, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_917(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_917(op, st, BOOST_PP_SEQ_REVERSE_S(918, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_918(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_918(op, st, BOOST_PP_SEQ_REVERSE_S(919, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_919(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_919(op, st, BOOST_PP_SEQ_REVERSE_S(920, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_920(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_920(op, st, BOOST_PP_SEQ_REVERSE_S(921, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_921(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_921(op, st, BOOST_PP_SEQ_REVERSE_S(922, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_922(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_922(op, st, BOOST_PP_SEQ_REVERSE_S(923, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_923(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_923(op, st, BOOST_PP_SEQ_REVERSE_S(924, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_924(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_924(op, st, BOOST_PP_SEQ_REVERSE_S(925, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_925(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_925(op, st, BOOST_PP_SEQ_REVERSE_S(926, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_926(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_926(op, st, BOOST_PP_SEQ_REVERSE_S(927, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_927(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_927(op, st, BOOST_PP_SEQ_REVERSE_S(928, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_928(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_928(op, st, BOOST_PP_SEQ_REVERSE_S(929, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_929(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_929(op, st, BOOST_PP_SEQ_REVERSE_S(930, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_930(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_930(op, st, BOOST_PP_SEQ_REVERSE_S(931, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_931(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_931(op, st, BOOST_PP_SEQ_REVERSE_S(932, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_932(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_932(op, st, BOOST_PP_SEQ_REVERSE_S(933, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_933(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_933(op, st, BOOST_PP_SEQ_REVERSE_S(934, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_934(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_934(op, st, BOOST_PP_SEQ_REVERSE_S(935, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_935(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_935(op, st, BOOST_PP_SEQ_REVERSE_S(936, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_936(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_936(op, st, BOOST_PP_SEQ_REVERSE_S(937, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_937(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_937(op, st, BOOST_PP_SEQ_REVERSE_S(938, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_938(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_938(op, st, BOOST_PP_SEQ_REVERSE_S(939, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_939(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_939(op, st, BOOST_PP_SEQ_REVERSE_S(940, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_940(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_940(op, st, BOOST_PP_SEQ_REVERSE_S(941, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_941(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_941(op, st, BOOST_PP_SEQ_REVERSE_S(942, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_942(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_942(op, st, BOOST_PP_SEQ_REVERSE_S(943, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_943(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_943(op, st, BOOST_PP_SEQ_REVERSE_S(944, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_944(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_944(op, st, BOOST_PP_SEQ_REVERSE_S(945, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_945(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_945(op, st, BOOST_PP_SEQ_REVERSE_S(946, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_946(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_946(op, st, BOOST_PP_SEQ_REVERSE_S(947, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_947(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_947(op, st, BOOST_PP_SEQ_REVERSE_S(948, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_948(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_948(op, st, BOOST_PP_SEQ_REVERSE_S(949, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_949(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_949(op, st, BOOST_PP_SEQ_REVERSE_S(950, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_950(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_950(op, st, BOOST_PP_SEQ_REVERSE_S(951, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_951(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_951(op, st, BOOST_PP_SEQ_REVERSE_S(952, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_952(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_952(op, st, BOOST_PP_SEQ_REVERSE_S(953, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_953(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_953(op, st, BOOST_PP_SEQ_REVERSE_S(954, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_954(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_954(op, st, BOOST_PP_SEQ_REVERSE_S(955, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_955(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_955(op, st, BOOST_PP_SEQ_REVERSE_S(956, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_956(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_956(op, st, BOOST_PP_SEQ_REVERSE_S(957, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_957(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_957(op, st, BOOST_PP_SEQ_REVERSE_S(958, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_958(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_958(op, st, BOOST_PP_SEQ_REVERSE_S(959, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_959(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_959(op, st, BOOST_PP_SEQ_REVERSE_S(960, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_960(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_960(op, st, BOOST_PP_SEQ_REVERSE_S(961, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_961(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_961(op, st, BOOST_PP_SEQ_REVERSE_S(962, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_962(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_962(op, st, BOOST_PP_SEQ_REVERSE_S(963, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_963(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_963(op, st, BOOST_PP_SEQ_REVERSE_S(964, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_964(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_964(op, st, BOOST_PP_SEQ_REVERSE_S(965, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_965(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_965(op, st, BOOST_PP_SEQ_REVERSE_S(966, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_966(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_966(op, st, BOOST_PP_SEQ_REVERSE_S(967, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_967(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_967(op, st, BOOST_PP_SEQ_REVERSE_S(968, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_968(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_968(op, st, BOOST_PP_SEQ_REVERSE_S(969, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_969(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_969(op, st, BOOST_PP_SEQ_REVERSE_S(970, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_970(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_970(op, st, BOOST_PP_SEQ_REVERSE_S(971, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_971(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_971(op, st, BOOST_PP_SEQ_REVERSE_S(972, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_972(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_972(op, st, BOOST_PP_SEQ_REVERSE_S(973, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_973(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_973(op, st, BOOST_PP_SEQ_REVERSE_S(974, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_974(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_974(op, st, BOOST_PP_SEQ_REVERSE_S(975, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_975(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_975(op, st, BOOST_PP_SEQ_REVERSE_S(976, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_976(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_976(op, st, BOOST_PP_SEQ_REVERSE_S(977, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_977(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_977(op, st, BOOST_PP_SEQ_REVERSE_S(978, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_978(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_978(op, st, BOOST_PP_SEQ_REVERSE_S(979, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_979(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_979(op, st, BOOST_PP_SEQ_REVERSE_S(980, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_980(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_980(op, st, BOOST_PP_SEQ_REVERSE_S(981, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_981(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_981(op, st, BOOST_PP_SEQ_REVERSE_S(982, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_982(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_982(op, st, BOOST_PP_SEQ_REVERSE_S(983, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_983(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_983(op, st, BOOST_PP_SEQ_REVERSE_S(984, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_984(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_984(op, st, BOOST_PP_SEQ_REVERSE_S(985, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_985(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_985(op, st, BOOST_PP_SEQ_REVERSE_S(986, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_986(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_986(op, st, BOOST_PP_SEQ_REVERSE_S(987, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_987(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_987(op, st, BOOST_PP_SEQ_REVERSE_S(988, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_988(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_988(op, st, BOOST_PP_SEQ_REVERSE_S(989, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_989(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_989(op, st, BOOST_PP_SEQ_REVERSE_S(990, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_990(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_990(op, st, BOOST_PP_SEQ_REVERSE_S(991, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_991(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_991(op, st, BOOST_PP_SEQ_REVERSE_S(992, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_992(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_992(op, st, BOOST_PP_SEQ_REVERSE_S(993, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_993(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_993(op, st, BOOST_PP_SEQ_REVERSE_S(994, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_994(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_994(op, st, BOOST_PP_SEQ_REVERSE_S(995, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_995(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_995(op, st, BOOST_PP_SEQ_REVERSE_S(996, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_996(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_996(op, st, BOOST_PP_SEQ_REVERSE_S(997, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_997(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_997(op, st, BOOST_PP_SEQ_REVERSE_S(998, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_998(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_998(op, st, BOOST_PP_SEQ_REVERSE_S(999, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_999(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_999(op, st, BOOST_PP_SEQ_REVERSE_S(1000, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_1000(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_1000(op, st, BOOST_PP_SEQ_REVERSE_S(1001, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_1001(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_1001(op, st, BOOST_PP_SEQ_REVERSE_S(1002, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_1002(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_1002(op, st, BOOST_PP_SEQ_REVERSE_S(1003, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_1003(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_1003(op, st, BOOST_PP_SEQ_REVERSE_S(1004, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_1004(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_1004(op, st, BOOST_PP_SEQ_REVERSE_S(1005, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_1005(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_1005(op, st, BOOST_PP_SEQ_REVERSE_S(1006, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_1006(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_1006(op, st, BOOST_PP_SEQ_REVERSE_S(1007, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_1007(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_1007(op, st, BOOST_PP_SEQ_REVERSE_S(1008, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_1008(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_1008(op, st, BOOST_PP_SEQ_REVERSE_S(1009, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_1009(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_1009(op, st, BOOST_PP_SEQ_REVERSE_S(1010, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_1010(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_1010(op, st, BOOST_PP_SEQ_REVERSE_S(1011, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_1011(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_1011(op, st, BOOST_PP_SEQ_REVERSE_S(1012, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_1012(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_1012(op, st, BOOST_PP_SEQ_REVERSE_S(1013, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_1013(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_1013(op, st, BOOST_PP_SEQ_REVERSE_S(1014, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_1014(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_1014(op, st, BOOST_PP_SEQ_REVERSE_S(1015, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_1015(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_1015(op, st, BOOST_PP_SEQ_REVERSE_S(1016, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_1016(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_1016(op, st, BOOST_PP_SEQ_REVERSE_S(1017, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_1017(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_1017(op, st, BOOST_PP_SEQ_REVERSE_S(1018, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_1018(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_1018(op, st, BOOST_PP_SEQ_REVERSE_S(1019, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_1019(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_1019(op, st, BOOST_PP_SEQ_REVERSE_S(1020, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_1020(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_1020(op, st, BOOST_PP_SEQ_REVERSE_S(1021, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_1021(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_1021(op, st, BOOST_PP_SEQ_REVERSE_S(1022, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_1022(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_1022(op, st, BOOST_PP_SEQ_REVERSE_S(1023, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_1023(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_1023(op, st, BOOST_PP_SEQ_REVERSE_S(1024, ss), BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_RIGHT_1024(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_1024(op, st, BOOST_PP_SEQ_REVERSE_S(1025, ss), BOOST_PP_SEQ_SIZE(ss))
#
# endif
