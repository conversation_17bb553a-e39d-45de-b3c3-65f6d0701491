# /* **************************************************************************
#  *                                                                          *
#  *     (C) Copyright <PERSON> 2002.
#  *     Distributed under the Boost Software License, Version 1.0. (See
#  *     accompanying file LICENSE_1_0.txt or copy at
#  *     http://www.boost.org/LICENSE_1_0.txt)
#  *                                                                          *
#  ************************************************************************** */
#
# /* See http://www.boost.org for most recent version. */
#
# include <boost/preprocessor/slot/detail/shared.hpp>
#
# undef BOOST_PP_SLOT_5
#
# undef BOOST_PP_SLOT_5_DIGIT_1
# undef BOOST_PP_SLOT_5_DIGIT_2
# undef BOOST_PP_SLOT_5_DIGIT_3
# undef BOOST_PP_SLOT_5_DIGIT_4
# undef BOOST_PP_SLOT_5_DIGIT_5
# undef BOOST_PP_SLOT_5_DIGIT_6
# undef BOOST_PP_SLOT_5_DIGIT_7
# undef BOOST_PP_SLOT_5_DIGIT_8
# undef BOOST_PP_SLOT_5_DIGIT_9
# undef BOOST_PP_SLOT_5_DIGIT_10
#
# if BOOST_PP_SLOT_TEMP_10 == 0
#    define BOOST_PP_SLOT_5_DIGIT_10 0
# elif BOOST_PP_SLOT_TEMP_10 == 1
#    define BOOST_PP_SLOT_5_DIGIT_10 1
# elif BOOST_PP_SLOT_TEMP_10 == 2
#    define BOOST_PP_SLOT_5_DIGIT_10 2
# elif BOOST_PP_SLOT_TEMP_10 == 3
#    define BOOST_PP_SLOT_5_DIGIT_10 3
# elif BOOST_PP_SLOT_TEMP_10 == 4
#    define BOOST_PP_SLOT_5_DIGIT_10 4
# elif BOOST_PP_SLOT_TEMP_10 == 5
#    define BOOST_PP_SLOT_5_DIGIT_10 5
# elif BOOST_PP_SLOT_TEMP_10 == 6
#    define BOOST_PP_SLOT_5_DIGIT_10 6
# elif BOOST_PP_SLOT_TEMP_10 == 7
#    define BOOST_PP_SLOT_5_DIGIT_10 7
# elif BOOST_PP_SLOT_TEMP_10 == 8
#    define BOOST_PP_SLOT_5_DIGIT_10 8
# elif BOOST_PP_SLOT_TEMP_10 == 9
#    define BOOST_PP_SLOT_5_DIGIT_10 9
# endif
#
# if BOOST_PP_SLOT_TEMP_9 == 0
#    define BOOST_PP_SLOT_5_DIGIT_9 0
# elif BOOST_PP_SLOT_TEMP_9 == 1
#    define BOOST_PP_SLOT_5_DIGIT_9 1
# elif BOOST_PP_SLOT_TEMP_9 == 2
#    define BOOST_PP_SLOT_5_DIGIT_9 2
# elif BOOST_PP_SLOT_TEMP_9 == 3
#    define BOOST_PP_SLOT_5_DIGIT_9 3
# elif BOOST_PP_SLOT_TEMP_9 == 4
#    define BOOST_PP_SLOT_5_DIGIT_9 4
# elif BOOST_PP_SLOT_TEMP_9 == 5
#    define BOOST_PP_SLOT_5_DIGIT_9 5
# elif BOOST_PP_SLOT_TEMP_9 == 6
#    define BOOST_PP_SLOT_5_DIGIT_9 6
# elif BOOST_PP_SLOT_TEMP_9 == 7
#    define BOOST_PP_SLOT_5_DIGIT_9 7
# elif BOOST_PP_SLOT_TEMP_9 == 8
#    define BOOST_PP_SLOT_5_DIGIT_9 8
# elif BOOST_PP_SLOT_TEMP_9 == 9
#    define BOOST_PP_SLOT_5_DIGIT_9 9
# endif
#
# if BOOST_PP_SLOT_TEMP_8 == 0
#    define BOOST_PP_SLOT_5_DIGIT_8 0
# elif BOOST_PP_SLOT_TEMP_8 == 1
#    define BOOST_PP_SLOT_5_DIGIT_8 1
# elif BOOST_PP_SLOT_TEMP_8 == 2
#    define BOOST_PP_SLOT_5_DIGIT_8 2
# elif BOOST_PP_SLOT_TEMP_8 == 3
#    define BOOST_PP_SLOT_5_DIGIT_8 3
# elif BOOST_PP_SLOT_TEMP_8 == 4
#    define BOOST_PP_SLOT_5_DIGIT_8 4
# elif BOOST_PP_SLOT_TEMP_8 == 5
#    define BOOST_PP_SLOT_5_DIGIT_8 5
# elif BOOST_PP_SLOT_TEMP_8 == 6
#    define BOOST_PP_SLOT_5_DIGIT_8 6
# elif BOOST_PP_SLOT_TEMP_8 == 7
#    define BOOST_PP_SLOT_5_DIGIT_8 7
# elif BOOST_PP_SLOT_TEMP_8 == 8
#    define BOOST_PP_SLOT_5_DIGIT_8 8
# elif BOOST_PP_SLOT_TEMP_8 == 9
#    define BOOST_PP_SLOT_5_DIGIT_8 9
# endif
#
# if BOOST_PP_SLOT_TEMP_7 == 0
#    define BOOST_PP_SLOT_5_DIGIT_7 0
# elif BOOST_PP_SLOT_TEMP_7 == 1
#    define BOOST_PP_SLOT_5_DIGIT_7 1
# elif BOOST_PP_SLOT_TEMP_7 == 2
#    define BOOST_PP_SLOT_5_DIGIT_7 2
# elif BOOST_PP_SLOT_TEMP_7 == 3
#    define BOOST_PP_SLOT_5_DIGIT_7 3
# elif BOOST_PP_SLOT_TEMP_7 == 4
#    define BOOST_PP_SLOT_5_DIGIT_7 4
# elif BOOST_PP_SLOT_TEMP_7 == 5
#    define BOOST_PP_SLOT_5_DIGIT_7 5
# elif BOOST_PP_SLOT_TEMP_7 == 6
#    define BOOST_PP_SLOT_5_DIGIT_7 6
# elif BOOST_PP_SLOT_TEMP_7 == 7
#    define BOOST_PP_SLOT_5_DIGIT_7 7
# elif BOOST_PP_SLOT_TEMP_7 == 8
#    define BOOST_PP_SLOT_5_DIGIT_7 8
# elif BOOST_PP_SLOT_TEMP_7 == 9
#    define BOOST_PP_SLOT_5_DIGIT_7 9
# endif
#
# if BOOST_PP_SLOT_TEMP_6 == 0
#    define BOOST_PP_SLOT_5_DIGIT_6 0
# elif BOOST_PP_SLOT_TEMP_6 == 1
#    define BOOST_PP_SLOT_5_DIGIT_6 1
# elif BOOST_PP_SLOT_TEMP_6 == 2
#    define BOOST_PP_SLOT_5_DIGIT_6 2
# elif BOOST_PP_SLOT_TEMP_6 == 3
#    define BOOST_PP_SLOT_5_DIGIT_6 3
# elif BOOST_PP_SLOT_TEMP_6 == 4
#    define BOOST_PP_SLOT_5_DIGIT_6 4
# elif BOOST_PP_SLOT_TEMP_6 == 5
#    define BOOST_PP_SLOT_5_DIGIT_6 5
# elif BOOST_PP_SLOT_TEMP_6 == 6
#    define BOOST_PP_SLOT_5_DIGIT_6 6
# elif BOOST_PP_SLOT_TEMP_6 == 7
#    define BOOST_PP_SLOT_5_DIGIT_6 7
# elif BOOST_PP_SLOT_TEMP_6 == 8
#    define BOOST_PP_SLOT_5_DIGIT_6 8
# elif BOOST_PP_SLOT_TEMP_6 == 9
#    define BOOST_PP_SLOT_5_DIGIT_6 9
# endif
#
# if BOOST_PP_SLOT_TEMP_5 == 0
#    define BOOST_PP_SLOT_5_DIGIT_5 0
# elif BOOST_PP_SLOT_TEMP_5 == 1
#    define BOOST_PP_SLOT_5_DIGIT_5 1
# elif BOOST_PP_SLOT_TEMP_5 == 2
#    define BOOST_PP_SLOT_5_DIGIT_5 2
# elif BOOST_PP_SLOT_TEMP_5 == 3
#    define BOOST_PP_SLOT_5_DIGIT_5 3
# elif BOOST_PP_SLOT_TEMP_5 == 4
#    define BOOST_PP_SLOT_5_DIGIT_5 4
# elif BOOST_PP_SLOT_TEMP_5 == 5
#    define BOOST_PP_SLOT_5_DIGIT_5 5
# elif BOOST_PP_SLOT_TEMP_5 == 6
#    define BOOST_PP_SLOT_5_DIGIT_5 6
# elif BOOST_PP_SLOT_TEMP_5 == 7
#    define BOOST_PP_SLOT_5_DIGIT_5 7
# elif BOOST_PP_SLOT_TEMP_5 == 8
#    define BOOST_PP_SLOT_5_DIGIT_5 8
# elif BOOST_PP_SLOT_TEMP_5 == 9
#    define BOOST_PP_SLOT_5_DIGIT_5 9
# endif
#
# if BOOST_PP_SLOT_TEMP_4 == 0
#    define BOOST_PP_SLOT_5_DIGIT_4 0
# elif BOOST_PP_SLOT_TEMP_4 == 1
#    define BOOST_PP_SLOT_5_DIGIT_4 1
# elif BOOST_PP_SLOT_TEMP_4 == 2
#    define BOOST_PP_SLOT_5_DIGIT_4 2
# elif BOOST_PP_SLOT_TEMP_4 == 3
#    define BOOST_PP_SLOT_5_DIGIT_4 3
# elif BOOST_PP_SLOT_TEMP_4 == 4
#    define BOOST_PP_SLOT_5_DIGIT_4 4
# elif BOOST_PP_SLOT_TEMP_4 == 5
#    define BOOST_PP_SLOT_5_DIGIT_4 5
# elif BOOST_PP_SLOT_TEMP_4 == 6
#    define BOOST_PP_SLOT_5_DIGIT_4 6
# elif BOOST_PP_SLOT_TEMP_4 == 7
#    define BOOST_PP_SLOT_5_DIGIT_4 7
# elif BOOST_PP_SLOT_TEMP_4 == 8
#    define BOOST_PP_SLOT_5_DIGIT_4 8
# elif BOOST_PP_SLOT_TEMP_4 == 9
#    define BOOST_PP_SLOT_5_DIGIT_4 9
# endif
#
# if BOOST_PP_SLOT_TEMP_3 == 0
#    define BOOST_PP_SLOT_5_DIGIT_3 0
# elif BOOST_PP_SLOT_TEMP_3 == 1
#    define BOOST_PP_SLOT_5_DIGIT_3 1
# elif BOOST_PP_SLOT_TEMP_3 == 2
#    define BOOST_PP_SLOT_5_DIGIT_3 2
# elif BOOST_PP_SLOT_TEMP_3 == 3
#    define BOOST_PP_SLOT_5_DIGIT_3 3
# elif BOOST_PP_SLOT_TEMP_3 == 4
#    define BOOST_PP_SLOT_5_DIGIT_3 4
# elif BOOST_PP_SLOT_TEMP_3 == 5
#    define BOOST_PP_SLOT_5_DIGIT_3 5
# elif BOOST_PP_SLOT_TEMP_3 == 6
#    define BOOST_PP_SLOT_5_DIGIT_3 6
# elif BOOST_PP_SLOT_TEMP_3 == 7
#    define BOOST_PP_SLOT_5_DIGIT_3 7
# elif BOOST_PP_SLOT_TEMP_3 == 8
#    define BOOST_PP_SLOT_5_DIGIT_3 8
# elif BOOST_PP_SLOT_TEMP_3 == 9
#    define BOOST_PP_SLOT_5_DIGIT_3 9
# endif
#
# if BOOST_PP_SLOT_TEMP_2 == 0
#    define BOOST_PP_SLOT_5_DIGIT_2 0
# elif BOOST_PP_SLOT_TEMP_2 == 1
#    define BOOST_PP_SLOT_5_DIGIT_2 1
# elif BOOST_PP_SLOT_TEMP_2 == 2
#    define BOOST_PP_SLOT_5_DIGIT_2 2
# elif BOOST_PP_SLOT_TEMP_2 == 3
#    define BOOST_PP_SLOT_5_DIGIT_2 3
# elif BOOST_PP_SLOT_TEMP_2 == 4
#    define BOOST_PP_SLOT_5_DIGIT_2 4
# elif BOOST_PP_SLOT_TEMP_2 == 5
#    define BOOST_PP_SLOT_5_DIGIT_2 5
# elif BOOST_PP_SLOT_TEMP_2 == 6
#    define BOOST_PP_SLOT_5_DIGIT_2 6
# elif BOOST_PP_SLOT_TEMP_2 == 7
#    define BOOST_PP_SLOT_5_DIGIT_2 7
# elif BOOST_PP_SLOT_TEMP_2 == 8
#    define BOOST_PP_SLOT_5_DIGIT_2 8
# elif BOOST_PP_SLOT_TEMP_2 == 9
#    define BOOST_PP_SLOT_5_DIGIT_2 9
# endif
#
# if BOOST_PP_SLOT_TEMP_1 == 0
#    define BOOST_PP_SLOT_5_DIGIT_1 0
# elif BOOST_PP_SLOT_TEMP_1 == 1
#    define BOOST_PP_SLOT_5_DIGIT_1 1
# elif BOOST_PP_SLOT_TEMP_1 == 2
#    define BOOST_PP_SLOT_5_DIGIT_1 2
# elif BOOST_PP_SLOT_TEMP_1 == 3
#    define BOOST_PP_SLOT_5_DIGIT_1 3
# elif BOOST_PP_SLOT_TEMP_1 == 4
#    define BOOST_PP_SLOT_5_DIGIT_1 4
# elif BOOST_PP_SLOT_TEMP_1 == 5
#    define BOOST_PP_SLOT_5_DIGIT_1 5
# elif BOOST_PP_SLOT_TEMP_1 == 6
#    define BOOST_PP_SLOT_5_DIGIT_1 6
# elif BOOST_PP_SLOT_TEMP_1 == 7
#    define BOOST_PP_SLOT_5_DIGIT_1 7
# elif BOOST_PP_SLOT_TEMP_1 == 8
#    define BOOST_PP_SLOT_5_DIGIT_1 8
# elif BOOST_PP_SLOT_TEMP_1 == 9
#    define BOOST_PP_SLOT_5_DIGIT_1 9
# endif
#
# if BOOST_PP_SLOT_5_DIGIT_10
#    define BOOST_PP_SLOT_5() BOOST_PP_SLOT_CC_10(BOOST_PP_SLOT_5_DIGIT_10, BOOST_PP_SLOT_5_DIGIT_9, BOOST_PP_SLOT_5_DIGIT_8, BOOST_PP_SLOT_5_DIGIT_7, BOOST_PP_SLOT_5_DIGIT_6, BOOST_PP_SLOT_5_DIGIT_5, BOOST_PP_SLOT_5_DIGIT_4, BOOST_PP_SLOT_5_DIGIT_3, BOOST_PP_SLOT_5_DIGIT_2, BOOST_PP_SLOT_5_DIGIT_1)
# elif BOOST_PP_SLOT_5_DIGIT_9
#    define BOOST_PP_SLOT_5() BOOST_PP_SLOT_CC_9(BOOST_PP_SLOT_5_DIGIT_9, BOOST_PP_SLOT_5_DIGIT_8, BOOST_PP_SLOT_5_DIGIT_7, BOOST_PP_SLOT_5_DIGIT_6, BOOST_PP_SLOT_5_DIGIT_5, BOOST_PP_SLOT_5_DIGIT_4, BOOST_PP_SLOT_5_DIGIT_3, BOOST_PP_SLOT_5_DIGIT_2, BOOST_PP_SLOT_5_DIGIT_1)
# elif BOOST_PP_SLOT_5_DIGIT_8
#    define BOOST_PP_SLOT_5() BOOST_PP_SLOT_CC_8(BOOST_PP_SLOT_5_DIGIT_8, BOOST_PP_SLOT_5_DIGIT_7, BOOST_PP_SLOT_5_DIGIT_6, BOOST_PP_SLOT_5_DIGIT_5, BOOST_PP_SLOT_5_DIGIT_4, BOOST_PP_SLOT_5_DIGIT_3, BOOST_PP_SLOT_5_DIGIT_2, BOOST_PP_SLOT_5_DIGIT_1)
# elif BOOST_PP_SLOT_5_DIGIT_7
#    define BOOST_PP_SLOT_5() BOOST_PP_SLOT_CC_7(BOOST_PP_SLOT_5_DIGIT_7, BOOST_PP_SLOT_5_DIGIT_6, BOOST_PP_SLOT_5_DIGIT_5, BOOST_PP_SLOT_5_DIGIT_4, BOOST_PP_SLOT_5_DIGIT_3, BOOST_PP_SLOT_5_DIGIT_2, BOOST_PP_SLOT_5_DIGIT_1)
# elif BOOST_PP_SLOT_5_DIGIT_6
#    define BOOST_PP_SLOT_5() BOOST_PP_SLOT_CC_6(BOOST_PP_SLOT_5_DIGIT_6, BOOST_PP_SLOT_5_DIGIT_5, BOOST_PP_SLOT_5_DIGIT_4, BOOST_PP_SLOT_5_DIGIT_3, BOOST_PP_SLOT_5_DIGIT_2, BOOST_PP_SLOT_5_DIGIT_1)
# elif BOOST_PP_SLOT_5_DIGIT_5
#    define BOOST_PP_SLOT_5() BOOST_PP_SLOT_CC_5(BOOST_PP_SLOT_5_DIGIT_5, BOOST_PP_SLOT_5_DIGIT_4, BOOST_PP_SLOT_5_DIGIT_3, BOOST_PP_SLOT_5_DIGIT_2, BOOST_PP_SLOT_5_DIGIT_1)
# elif BOOST_PP_SLOT_5_DIGIT_4
#    define BOOST_PP_SLOT_5() BOOST_PP_SLOT_CC_4(BOOST_PP_SLOT_5_DIGIT_4, BOOST_PP_SLOT_5_DIGIT_3, BOOST_PP_SLOT_5_DIGIT_2, BOOST_PP_SLOT_5_DIGIT_1)
# elif BOOST_PP_SLOT_5_DIGIT_3
#    define BOOST_PP_SLOT_5() BOOST_PP_SLOT_CC_3(BOOST_PP_SLOT_5_DIGIT_3, BOOST_PP_SLOT_5_DIGIT_2, BOOST_PP_SLOT_5_DIGIT_1)
# elif BOOST_PP_SLOT_5_DIGIT_2
#    define BOOST_PP_SLOT_5() BOOST_PP_SLOT_CC_2(BOOST_PP_SLOT_5_DIGIT_2, BOOST_PP_SLOT_5_DIGIT_1)
# else
#    define BOOST_PP_SLOT_5() BOOST_PP_SLOT_5_DIGIT_1
# endif
