#ifndef BOOST_SYSTEM_SYSTEM_CATEGORY_HPP_INCLUDED
#define BOOST_SYSTEM_SYSTEM_CATEGORY_HPP_INCLUDED

// Copyright 2020 Peter Dimov
// Distributed under the Boost Software License, Version 1.0
// http://www.boost.org/LICENSE_1_0.txt
//
// See library home page at http://www.boost.org/libs/system

#include <boost/system/detail/system_category.hpp>
#include <boost/system/detail/system_category_impl.hpp>
#include <boost/system/detail/error_category_impl.hpp>

#endif // #ifndef BOOST_SYSTEM_SYSTEM_CATEGORY_HPP_INCLUDED
