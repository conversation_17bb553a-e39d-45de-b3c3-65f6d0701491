#ifndef BOOST_SYSTEM_DETAIL_IS_SAME_HPP_INCLUDED
#define BOOST_SYSTEM_DETAIL_IS_SAME_HPP_INCLUDED

// Copyright 2021 Peter Dimov
// Distributed under the Boost Software License, Version 1.0
// http://www.boost.org/LICENSE_1_0.txt

#include <type_traits>

namespace boost
{
namespace system
{
namespace detail
{

using std::is_same;

} // namespace detail
} // namespace system
} // namespace boost

#endif // #ifndef BOOST_SYSTEM_DETAIL_IS_SAME_HPP_INCLUDED
