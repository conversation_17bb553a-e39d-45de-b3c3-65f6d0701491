/*
 *
 * Copyright (c) 2020
 * <PERSON>
 *
 * Use, modification and distribution are subject to the 
 * Boost Software License, Version 1.0. (See accompanying file 
 * LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
 *
 */
 
 /*
  *   LOCATION:    see http://www.boost.org for most recent version.
  *   FILE         unicode_iterator.hpp
  *   VERSION      see <boost/version.hpp>
  *   DESCRIPTION: Iterator adapters for converting between different Unicode encodings.
  */

#ifndef BOOST_REGEX_PENDING_UNICODE_ITERATOR_HPP
#define BOOST_REGEX_PENDING_UNICODE_ITERATOR_HPP

#include <boost/regex/config.hpp>

#if defined(BOOST_REGEX_CXX03)
#include <boost/regex/v4/unicode_iterator.hpp>
#else
#include <boost/regex/v5/unicode_iterator.hpp>
#endif


#endif // BOOST_REGEX_PENDING_UNICODE_ITERATOR_HPP

