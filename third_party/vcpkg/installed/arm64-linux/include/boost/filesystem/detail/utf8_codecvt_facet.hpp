// Copyright (c) 2001 <PERSON>, Indiana University (<EMAIL>)
// <PERSON>, Indiana University (<EMAIL>).

// Distributed under the Boost Software License, Version 1.0.
// (See http://www.boost.org/LICENSE_1_0.txt)

#ifndef BOOST_FILESYSTEM_UTF8_CODECVT_FACET_HPP
#define BOOST_FILESYSTEM_UTF8_CODECVT_FACET_HPP

#include <boost/filesystem/config.hpp>

#include <boost/filesystem/detail/header.hpp>

#define BOOST_UTF8_BEGIN_NAMESPACE \
    namespace boost { \
    namespace filesystem { \
    namespace detail {

#define BOOST_UTF8_END_NAMESPACE \
    } \
    } \
    }
#define BOOST_UTF8_DECL BOOST_FILESYSTEM_DECL

#include <boost/detail/utf8_codecvt_facet.hpp>

#undef BOOST_UTF8_BEGIN_NAMESPACE
#undef BOOST_UTF8_END_NAMESPACE
#undef BOOST_UTF8_DECL

#include <boost/filesystem/detail/footer.hpp>

#endif // BOOST_FILESYSTEM_UTF8_CODECVT_FACET_HPP
