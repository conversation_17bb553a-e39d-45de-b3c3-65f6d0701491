//  This file was automatically generated on Fri Oct 13 19:09:38 2023
//  by libs/config/tools/generate.cpp
//  Copyright <PERSON> 2002-21.
//  Use, modification and distribution are subject to the 
//  Boost Software License, Version 1.0. (See accompanying file 
//  LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

//  See http://www.boost.org/libs/config for the most recent version.//
//  Revision $Id$
//

#include <boost/config.hpp>
#include <boost/config/assert_cxx14.hpp>

#ifdef BOOST_NO_CXX17_AUTO_NONTYPE_TEMPLATE_PARAMS
#  error "Your compiler appears not to be fully C++17 compliant.  Detected via defect macro BOOST_NO_CXX17_AUTO_NONTYPE_TEMPLATE_PARAMS."
#endif
#ifdef BOOST_NO_CXX17_DEDUCTION_GUIDES
#  error "Your compiler appears not to be fully C++17 compliant.  Detected via defect macro BOOST_NO_CXX17_DEDUCTION_GUIDES."
#endif
#ifdef BOOST_NO_CXX17_FOLD_EXPRESSIONS
#  error "Your compiler appears not to be fully C++17 compliant.  Detected via defect macro BOOST_NO_CXX17_FOLD_EXPRESSIONS."
#endif
#ifdef BOOST_NO_CXX17_HDR_ANY
#  error "Your compiler appears not to be fully C++17 compliant.  Detected via defect macro BOOST_NO_CXX17_HDR_ANY."
#endif
#ifdef BOOST_NO_CXX17_HDR_CHARCONV
#  error "Your compiler appears not to be fully C++17 compliant.  Detected via defect macro BOOST_NO_CXX17_HDR_CHARCONV."
#endif
#ifdef BOOST_NO_CXX17_HDR_EXECUTION
#  error "Your compiler appears not to be fully C++17 compliant.  Detected via defect macro BOOST_NO_CXX17_HDR_EXECUTION."
#endif
#ifdef BOOST_NO_CXX17_HDR_FILESYSTEM
#  error "Your compiler appears not to be fully C++17 compliant.  Detected via defect macro BOOST_NO_CXX17_HDR_FILESYSTEM."
#endif
#ifdef BOOST_NO_CXX17_HDR_MEMORY_RESOURCE
#  error "Your compiler appears not to be fully C++17 compliant.  Detected via defect macro BOOST_NO_CXX17_HDR_MEMORY_RESOURCE."
#endif
#ifdef BOOST_NO_CXX17_HDR_OPTIONAL
#  error "Your compiler appears not to be fully C++17 compliant.  Detected via defect macro BOOST_NO_CXX17_HDR_OPTIONAL."
#endif
#ifdef BOOST_NO_CXX17_HDR_STRING_VIEW
#  error "Your compiler appears not to be fully C++17 compliant.  Detected via defect macro BOOST_NO_CXX17_HDR_STRING_VIEW."
#endif
#ifdef BOOST_NO_CXX17_HDR_VARIANT
#  error "Your compiler appears not to be fully C++17 compliant.  Detected via defect macro BOOST_NO_CXX17_HDR_VARIANT."
#endif
#ifdef BOOST_NO_CXX17_IF_CONSTEXPR
#  error "Your compiler appears not to be fully C++17 compliant.  Detected via defect macro BOOST_NO_CXX17_IF_CONSTEXPR."
#endif
#ifdef BOOST_NO_CXX17_INLINE_VARIABLES
#  error "Your compiler appears not to be fully C++17 compliant.  Detected via defect macro BOOST_NO_CXX17_INLINE_VARIABLES."
#endif
#ifdef BOOST_NO_CXX17_ITERATOR_TRAITS
#  error "Your compiler appears not to be fully C++17 compliant.  Detected via defect macro BOOST_NO_CXX17_ITERATOR_TRAITS."
#endif
#ifdef BOOST_NO_CXX17_STD_APPLY
#  error "Your compiler appears not to be fully C++17 compliant.  Detected via defect macro BOOST_NO_CXX17_STD_APPLY."
#endif
#ifdef BOOST_NO_CXX17_STD_INVOKE
#  error "Your compiler appears not to be fully C++17 compliant.  Detected via defect macro BOOST_NO_CXX17_STD_INVOKE."
#endif
#ifdef BOOST_NO_CXX17_STRUCTURED_BINDINGS
#  error "Your compiler appears not to be fully C++17 compliant.  Detected via defect macro BOOST_NO_CXX17_STRUCTURED_BINDINGS."
#endif
