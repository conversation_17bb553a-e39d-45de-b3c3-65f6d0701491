//  This file was automatically generated on Fri Oct 13 19:09:38 2023
//  by libs/config/tools/generate.cpp
//  Copyright <PERSON> 2002-21.
//  Use, modification and distribution are subject to the 
//  Boost Software License, Version 1.0. (See accompanying file 
//  LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

//  See http://www.boost.org/libs/config for the most recent version.//
//  Revision $Id$
//

#include <boost/config.hpp>
#include <boost/config/assert_cxx17.hpp>

#ifdef BOOST_NO_CXX20_HDR_BARRIER
#  error "Your compiler appears not to be fully C++20 compliant.  Detected via defect macro BOOST_NO_CXX20_HDR_BARRIER."
#endif
#ifdef BOOST_NO_CXX20_HDR_BIT
#  error "Your compiler appears not to be fully C++20 compliant.  Detected via defect macro BOOST_NO_CXX20_HDR_BIT."
#endif
#ifdef BOOST_NO_CXX20_HDR_COMPARE
#  error "Your compiler appears not to be fully C++20 compliant.  Detected via defect macro BOOST_NO_CXX20_HDR_COMPARE."
#endif
#ifdef BOOST_NO_CXX20_HDR_CONCEPTS
#  error "Your compiler appears not to be fully C++20 compliant.  Detected via defect macro BOOST_NO_CXX20_HDR_CONCEPTS."
#endif
#ifdef BOOST_NO_CXX20_HDR_COROUTINE
#  error "Your compiler appears not to be fully C++20 compliant.  Detected via defect macro BOOST_NO_CXX20_HDR_COROUTINE."
#endif
#ifdef BOOST_NO_CXX20_HDR_FORMAT
#  error "Your compiler appears not to be fully C++20 compliant.  Detected via defect macro BOOST_NO_CXX20_HDR_FORMAT."
#endif
#ifdef BOOST_NO_CXX20_HDR_LATCH
#  error "Your compiler appears not to be fully C++20 compliant.  Detected via defect macro BOOST_NO_CXX20_HDR_LATCH."
#endif
#ifdef BOOST_NO_CXX20_HDR_NUMBERS
#  error "Your compiler appears not to be fully C++20 compliant.  Detected via defect macro BOOST_NO_CXX20_HDR_NUMBERS."
#endif
#ifdef BOOST_NO_CXX20_HDR_RANGES
#  error "Your compiler appears not to be fully C++20 compliant.  Detected via defect macro BOOST_NO_CXX20_HDR_RANGES."
#endif
#ifdef BOOST_NO_CXX20_HDR_SEMAPHORE
#  error "Your compiler appears not to be fully C++20 compliant.  Detected via defect macro BOOST_NO_CXX20_HDR_SEMAPHORE."
#endif
#ifdef BOOST_NO_CXX20_HDR_SOURCE_LOCATION
#  error "Your compiler appears not to be fully C++20 compliant.  Detected via defect macro BOOST_NO_CXX20_HDR_SOURCE_LOCATION."
#endif
#ifdef BOOST_NO_CXX20_HDR_SPAN
#  error "Your compiler appears not to be fully C++20 compliant.  Detected via defect macro BOOST_NO_CXX20_HDR_SPAN."
#endif
#ifdef BOOST_NO_CXX20_HDR_STOP_TOKEN
#  error "Your compiler appears not to be fully C++20 compliant.  Detected via defect macro BOOST_NO_CXX20_HDR_STOP_TOKEN."
#endif
#ifdef BOOST_NO_CXX20_HDR_SYNCSTREAM
#  error "Your compiler appears not to be fully C++20 compliant.  Detected via defect macro BOOST_NO_CXX20_HDR_SYNCSTREAM."
#endif
#ifdef BOOST_NO_CXX20_HDR_VERSION
#  error "Your compiler appears not to be fully C++20 compliant.  Detected via defect macro BOOST_NO_CXX20_HDR_VERSION."
#endif
