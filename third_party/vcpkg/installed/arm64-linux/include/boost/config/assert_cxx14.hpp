//  This file was automatically generated on Fri Oct 13 19:09:38 2023
//  by libs/config/tools/generate.cpp
//  Copyright <PERSON> 2002-21.
//  Use, modification and distribution are subject to the 
//  Boost Software License, Version 1.0. (See accompanying file 
//  LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

//  See http://www.boost.org/libs/config for the most recent version.//
//  Revision $Id$
//

#include <boost/config.hpp>
#include <boost/config/assert_cxx11.hpp>

#ifdef BOOST_NO_CXX14_AGGREGATE_NSDMI
#  error "Your compiler appears not to be fully C++14 compliant.  Detected via defect macro BOOST_NO_CXX14_AGGREGATE_NSDMI."
#endif
#ifdef BOOST_NO_CXX14_BINARY_LITERALS
#  error "Your compiler appears not to be fully C++14 compliant.  Detected via defect macro BOOST_NO_CXX14_BINARY_LITERALS."
#endif
#ifdef BOOST_NO_CXX14_CONSTEXPR
#  error "Your compiler appears not to be fully C++14 compliant.  Detected via defect macro BOOST_NO_CXX14_CONSTEXPR."
#endif
#ifdef BOOST_NO_CXX14_DECLTYPE_AUTO
#  error "Your compiler appears not to be fully C++14 compliant.  Detected via defect macro BOOST_NO_CXX14_DECLTYPE_AUTO."
#endif
#ifdef BOOST_NO_CXX14_DIGIT_SEPARATORS
#  error "Your compiler appears not to be fully C++14 compliant.  Detected via defect macro BOOST_NO_CXX14_DIGIT_SEPARATORS."
#endif
#ifdef BOOST_NO_CXX14_GENERIC_LAMBDAS
#  error "Your compiler appears not to be fully C++14 compliant.  Detected via defect macro BOOST_NO_CXX14_GENERIC_LAMBDAS."
#endif
#ifdef BOOST_NO_CXX14_HDR_SHARED_MUTEX
#  error "Your compiler appears not to be fully C++14 compliant.  Detected via defect macro BOOST_NO_CXX14_HDR_SHARED_MUTEX."
#endif
#ifdef BOOST_NO_CXX14_INITIALIZED_LAMBDA_CAPTURES
#  error "Your compiler appears not to be fully C++14 compliant.  Detected via defect macro BOOST_NO_CXX14_INITIALIZED_LAMBDA_CAPTURES."
#endif
#ifdef BOOST_NO_CXX14_RETURN_TYPE_DEDUCTION
#  error "Your compiler appears not to be fully C++14 compliant.  Detected via defect macro BOOST_NO_CXX14_RETURN_TYPE_DEDUCTION."
#endif
#ifdef BOOST_NO_CXX14_STD_EXCHANGE
#  error "Your compiler appears not to be fully C++14 compliant.  Detected via defect macro BOOST_NO_CXX14_STD_EXCHANGE."
#endif
#ifdef BOOST_NO_CXX14_VARIABLE_TEMPLATES
#  error "Your compiler appears not to be fully C++14 compliant.  Detected via defect macro BOOST_NO_CXX14_VARIABLE_TEMPLATES."
#endif
