//  This file was automatically generated on Fri Oct 13 19:09:38 2023
//  by libs/config/tools/generate.cpp
//  Copyright <PERSON> 2002-21.
//  Use, modification and distribution are subject to the 
//  Boost Software License, Version 1.0. (See accompanying file 
//  LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

//  See http://www.boost.org/libs/config for the most recent version.//
//  Revision $Id$
//

#include <boost/config.hpp>
#include <boost/config/assert_cxx20.hpp>

#ifdef BOOST_NO_CXX23_HDR_EXPECTED
#  error "Your compiler appears not to be fully C++23 compliant.  Detected via defect macro BOOST_NO_CXX23_HDR_EXPECTED."
#endif
#ifdef BOOST_NO_CXX23_HDR_FLAT_MAP
#  error "Your compiler appears not to be fully C++23 compliant.  Detected via defect macro BOOST_NO_CXX23_HDR_FLAT_MAP."
#endif
#ifdef BOOST_NO_CXX23_HDR_FLAT_SET
#  error "Your compiler appears not to be fully C++23 compliant.  Detected via defect macro BOOST_NO_CXX23_HDR_FLAT_SET."
#endif
#ifdef BOOST_NO_CXX23_HDR_GENERATOR
#  error "Your compiler appears not to be fully C++23 compliant.  Detected via defect macro BOOST_NO_CXX23_HDR_GENERATOR."
#endif
#ifdef BOOST_NO_CXX23_HDR_MDSPAN
#  error "Your compiler appears not to be fully C++23 compliant.  Detected via defect macro BOOST_NO_CXX23_HDR_MDSPAN."
#endif
#ifdef BOOST_NO_CXX23_HDR_PRINT
#  error "Your compiler appears not to be fully C++23 compliant.  Detected via defect macro BOOST_NO_CXX23_HDR_PRINT."
#endif
#ifdef BOOST_NO_CXX23_HDR_SPANSTREAM
#  error "Your compiler appears not to be fully C++23 compliant.  Detected via defect macro BOOST_NO_CXX23_HDR_SPANSTREAM."
#endif
#ifdef BOOST_NO_CXX23_HDR_STACKTRACE
#  error "Your compiler appears not to be fully C++23 compliant.  Detected via defect macro BOOST_NO_CXX23_HDR_STACKTRACE."
#endif
#ifdef BOOST_NO_CXX23_HDR_STDFLOAT
#  error "Your compiler appears not to be fully C++23 compliant.  Detected via defect macro BOOST_NO_CXX23_HDR_STDFLOAT."
#endif
