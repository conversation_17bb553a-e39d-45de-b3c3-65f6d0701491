//  (C) Copyright <PERSON> 2020.
//  Use, modification and distribution are subject to the
//  Boost Software License, Version 1.0. (See accompanying file
//  LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

//  See http://www.boost.org for most recent version.

//  WASM specific config options:

#define BOOST_PLATFORM "Wasm"

#ifdef __has_include
#if __has_include(<unistd.h>)
#  define BOOST_HAS_UNISTD_H
#endif
#endif

// boilerplate code:
#include <boost/config/detail/posix_features.hpp>
//
// fenv lacks the C++11 macros:
//
#define BOOST_NO_FENV_H
