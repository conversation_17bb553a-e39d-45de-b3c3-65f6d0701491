/*
Copyright <PERSON> 2008-2015
Copyright Franz Detro 2014
Distributed under the Boost Software License, Version 1.0.
(See accompanying file LICENSE_1_0.txt or copy at
http://www.boost.org/LICENSE_1_0.txt)
*/

#if !defined(BOOST_PREDEF_OS_H) || defined(BOOST_PREDEF_INTERNAL_GENERATE_TESTS)
#ifndef BOOST_PREDEF_OS_H
#define BOOST_PREDEF_OS_H
#endif

#include <boost/predef/os/aix.h>
#include <boost/predef/os/amigaos.h>
#include <boost/predef/os/beos.h>
#include <boost/predef/os/bsd.h>
#include <boost/predef/os/cygwin.h>
#include <boost/predef/os/haiku.h>
#include <boost/predef/os/hpux.h>
#include <boost/predef/os/irix.h>
#include <boost/predef/os/ios.h>
#include <boost/predef/os/linux.h>
#include <boost/predef/os/macos.h>
#include <boost/predef/os/os400.h>
#include <boost/predef/os/qnxnto.h>
#include <boost/predef/os/solaris.h>
#include <boost/predef/os/unix.h>
#include <boost/predef/os/vms.h>
#include <boost/predef/os/windows.h>

#endif
