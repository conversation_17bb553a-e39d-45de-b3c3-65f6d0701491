
// Copyright <PERSON><PERSON><PERSON> G<PERSON>ovoy 2000-2004
// Copyright <PERSON> 2003-2004
//
// Distributed under the Boost Software License, Version 1.0. 
// (See accompanying file LICENSE_1_0.txt or copy at 
// http://www.boost.org/LICENSE_1_0.txt)
//

// Preprocessed version of "boost/mpl/set/set50.hpp" header
// -- DO NOT modify by hand!

namespace boost { namespace mpl {

template<
      typename T0, typename T1, typename T2, typename T3, typename T4
    , typename T5, typename T6, typename T7, typename T8, typename T9
    , typename T10, typename T11, typename T12, typename T13, typename T14
    , typename T15, typename T16, typename T17, typename T18, typename T19
    , typename T20, typename T21, typename T22, typename T23, typename T24
    , typename T25, typename T26, typename T27, typename T28, typename T29
    , typename T30, typename T31, typename T32, typename T33, typename T34
    , typename T35, typename T36, typename T37, typename T38, typename T39
    , typename T40
    >
struct set41
    : s_item<
          T40
        , typename set40< T0, T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11
        , T12, T13, T14, T15, T16, T17, T18, T19, T20, T21, T22, T23, T24, T25
        , T26, T27, T28, T29, T30, T31, T32, T33, T34, T35, T36, T37, T38
        , T39 >::item_
        >
{
    typedef set41 type;
};

template<
      typename T0, typename T1, typename T2, typename T3, typename T4
    , typename T5, typename T6, typename T7, typename T8, typename T9
    , typename T10, typename T11, typename T12, typename T13, typename T14
    , typename T15, typename T16, typename T17, typename T18, typename T19
    , typename T20, typename T21, typename T22, typename T23, typename T24
    , typename T25, typename T26, typename T27, typename T28, typename T29
    , typename T30, typename T31, typename T32, typename T33, typename T34
    , typename T35, typename T36, typename T37, typename T38, typename T39
    , typename T40, typename T41
    >
struct set42
    : s_item<
          T41
        , typename set41< T0, T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11
        , T12, T13, T14, T15, T16, T17, T18, T19, T20, T21, T22, T23, T24, T25
        , T26, T27, T28, T29, T30, T31, T32, T33, T34, T35, T36, T37, T38, T39
        , T40 >::item_
        >
{
    typedef set42 type;
};

template<
      typename T0, typename T1, typename T2, typename T3, typename T4
    , typename T5, typename T6, typename T7, typename T8, typename T9
    , typename T10, typename T11, typename T12, typename T13, typename T14
    , typename T15, typename T16, typename T17, typename T18, typename T19
    , typename T20, typename T21, typename T22, typename T23, typename T24
    , typename T25, typename T26, typename T27, typename T28, typename T29
    , typename T30, typename T31, typename T32, typename T33, typename T34
    , typename T35, typename T36, typename T37, typename T38, typename T39
    , typename T40, typename T41, typename T42
    >
struct set43
    : s_item<
          T42
        , typename set42< T0, T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11
        , T12, T13, T14, T15, T16, T17, T18, T19, T20, T21, T22, T23, T24, T25
        , T26, T27, T28, T29, T30, T31, T32, T33, T34, T35, T36, T37, T38, T39
        , T40, T41 >::item_
        >
{
    typedef set43 type;
};

template<
      typename T0, typename T1, typename T2, typename T3, typename T4
    , typename T5, typename T6, typename T7, typename T8, typename T9
    , typename T10, typename T11, typename T12, typename T13, typename T14
    , typename T15, typename T16, typename T17, typename T18, typename T19
    , typename T20, typename T21, typename T22, typename T23, typename T24
    , typename T25, typename T26, typename T27, typename T28, typename T29
    , typename T30, typename T31, typename T32, typename T33, typename T34
    , typename T35, typename T36, typename T37, typename T38, typename T39
    , typename T40, typename T41, typename T42, typename T43
    >
struct set44
    : s_item<
          T43
        , typename set43< T0, T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11
        , T12, T13, T14, T15, T16, T17, T18, T19, T20, T21, T22, T23, T24, T25
        , T26, T27, T28, T29, T30, T31, T32, T33, T34, T35, T36, T37, T38, T39
        , T40, T41, T42 >::item_
        >
{
    typedef set44 type;
};

template<
      typename T0, typename T1, typename T2, typename T3, typename T4
    , typename T5, typename T6, typename T7, typename T8, typename T9
    , typename T10, typename T11, typename T12, typename T13, typename T14
    , typename T15, typename T16, typename T17, typename T18, typename T19
    , typename T20, typename T21, typename T22, typename T23, typename T24
    , typename T25, typename T26, typename T27, typename T28, typename T29
    , typename T30, typename T31, typename T32, typename T33, typename T34
    , typename T35, typename T36, typename T37, typename T38, typename T39
    , typename T40, typename T41, typename T42, typename T43, typename T44
    >
struct set45
    : s_item<
          T44
        , typename set44< T0, T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11
        , T12, T13, T14, T15, T16, T17, T18, T19, T20, T21, T22, T23, T24, T25
        , T26, T27, T28, T29, T30, T31, T32, T33, T34, T35, T36, T37, T38, T39
        , T40, T41, T42, T43 >::item_
        >
{
    typedef set45 type;
};

template<
      typename T0, typename T1, typename T2, typename T3, typename T4
    , typename T5, typename T6, typename T7, typename T8, typename T9
    , typename T10, typename T11, typename T12, typename T13, typename T14
    , typename T15, typename T16, typename T17, typename T18, typename T19
    , typename T20, typename T21, typename T22, typename T23, typename T24
    , typename T25, typename T26, typename T27, typename T28, typename T29
    , typename T30, typename T31, typename T32, typename T33, typename T34
    , typename T35, typename T36, typename T37, typename T38, typename T39
    , typename T40, typename T41, typename T42, typename T43, typename T44
    , typename T45
    >
struct set46
    : s_item<
          T45
        , typename set45< T0, T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11
        , T12, T13, T14, T15, T16, T17, T18, T19, T20, T21, T22, T23, T24, T25
        , T26, T27, T28, T29, T30, T31, T32, T33, T34, T35, T36, T37, T38, T39
        , T40, T41, T42, T43, T44 >::item_
        >
{
    typedef set46 type;
};

template<
      typename T0, typename T1, typename T2, typename T3, typename T4
    , typename T5, typename T6, typename T7, typename T8, typename T9
    , typename T10, typename T11, typename T12, typename T13, typename T14
    , typename T15, typename T16, typename T17, typename T18, typename T19
    , typename T20, typename T21, typename T22, typename T23, typename T24
    , typename T25, typename T26, typename T27, typename T28, typename T29
    , typename T30, typename T31, typename T32, typename T33, typename T34
    , typename T35, typename T36, typename T37, typename T38, typename T39
    , typename T40, typename T41, typename T42, typename T43, typename T44
    , typename T45, typename T46
    >
struct set47
    : s_item<
          T46
        , typename set46< T0, T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11
        , T12, T13, T14, T15, T16, T17, T18, T19, T20, T21, T22, T23, T24, T25
        , T26, T27, T28, T29, T30, T31, T32, T33, T34, T35, T36, T37, T38, T39
        , T40, T41, T42, T43, T44, T45 >::item_
        >
{
    typedef set47 type;
};

template<
      typename T0, typename T1, typename T2, typename T3, typename T4
    , typename T5, typename T6, typename T7, typename T8, typename T9
    , typename T10, typename T11, typename T12, typename T13, typename T14
    , typename T15, typename T16, typename T17, typename T18, typename T19
    , typename T20, typename T21, typename T22, typename T23, typename T24
    , typename T25, typename T26, typename T27, typename T28, typename T29
    , typename T30, typename T31, typename T32, typename T33, typename T34
    , typename T35, typename T36, typename T37, typename T38, typename T39
    , typename T40, typename T41, typename T42, typename T43, typename T44
    , typename T45, typename T46, typename T47
    >
struct set48
    : s_item<
          T47
        , typename set47< T0, T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11
        , T12, T13, T14, T15, T16, T17, T18, T19, T20, T21, T22, T23, T24, T25
        , T26, T27, T28, T29, T30, T31, T32, T33, T34, T35, T36, T37, T38, T39
        , T40, T41, T42, T43, T44, T45, T46 >::item_
        >
{
    typedef set48 type;
};

template<
      typename T0, typename T1, typename T2, typename T3, typename T4
    , typename T5, typename T6, typename T7, typename T8, typename T9
    , typename T10, typename T11, typename T12, typename T13, typename T14
    , typename T15, typename T16, typename T17, typename T18, typename T19
    , typename T20, typename T21, typename T22, typename T23, typename T24
    , typename T25, typename T26, typename T27, typename T28, typename T29
    , typename T30, typename T31, typename T32, typename T33, typename T34
    , typename T35, typename T36, typename T37, typename T38, typename T39
    , typename T40, typename T41, typename T42, typename T43, typename T44
    , typename T45, typename T46, typename T47, typename T48
    >
struct set49
    : s_item<
          T48
        , typename set48< T0, T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11
        , T12, T13, T14, T15, T16, T17, T18, T19, T20, T21, T22, T23, T24, T25
        , T26, T27, T28, T29, T30, T31, T32, T33, T34, T35, T36, T37, T38, T39
        , T40, T41, T42, T43, T44, T45, T46, T47 >::item_
        >
{
    typedef set49 type;
};

template<
      typename T0, typename T1, typename T2, typename T3, typename T4
    , typename T5, typename T6, typename T7, typename T8, typename T9
    , typename T10, typename T11, typename T12, typename T13, typename T14
    , typename T15, typename T16, typename T17, typename T18, typename T19
    , typename T20, typename T21, typename T22, typename T23, typename T24
    , typename T25, typename T26, typename T27, typename T28, typename T29
    , typename T30, typename T31, typename T32, typename T33, typename T34
    , typename T35, typename T36, typename T37, typename T38, typename T39
    , typename T40, typename T41, typename T42, typename T43, typename T44
    , typename T45, typename T46, typename T47, typename T48, typename T49
    >
struct set50
    : s_item<
          T49
        , typename set49< T0, T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11
        , T12, T13, T14, T15, T16, T17, T18, T19, T20, T21, T22, T23, T24, T25
        , T26, T27, T28, T29, T30, T31, T32, T33, T34, T35, T36, T37, T38, T39
        , T40, T41, T42, T43, T44, T45, T46, T47, T48 >::item_
        >
{
    typedef set50 type;
};

}}
