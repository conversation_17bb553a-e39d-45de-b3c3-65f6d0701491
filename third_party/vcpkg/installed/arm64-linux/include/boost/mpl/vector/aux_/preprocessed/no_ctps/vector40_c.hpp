
// Copyright Al<PERSON><PERSON> Gurtovoy 2000-2004
//
// Distributed under the Boost Software License, Version 1.0. 
// (See accompanying file LICENSE_1_0.txt or copy at 
// http://www.boost.org/LICENSE_1_0.txt)
//

// Preprocessed version of "boost/mpl/vector/vector40_c.hpp" header
// -- DO NOT modify by hand!

namespace boost { namespace mpl {

template<
      typename T
    , T C0, T C1, T C2, T C3, T C4, T C5, T C6, T C7, T C8, T C9, T C10
    , T C11, T C12, T C13, T C14, T C15, T C16, T C17, T C18, T C19, T C20
    , T C21, T C22, T C23, T C24, T C25, T C26, T C27, T C28, T C29, T C30
    >
struct vector31_c
    : vector31<
          integral_c< T,C0 >, integral_c< T,C1 >, integral_c< T,C2 >
        , integral_c< T,C3 >, integral_c< T,C4 >, integral_c< T,C5 >, integral_c< T,C6 >
        , integral_c< T,C7 >, integral_c< T,C8 >, integral_c< T,C9 >
        , integral_c< T,C10 >, integral_c< T,C11 >, integral_c< T,C12 >
        , integral_c< T,C13 >, integral_c< T,C14 >, integral_c< T,C15 >
        , integral_c< T,C16 >, integral_c< T,C17 >, integral_c< T,C18 >
        , integral_c< T,C19 >, integral_c< T,C20 >, integral_c< T,C21 >
        , integral_c< T,C22 >, integral_c< T,C23 >, integral_c< T,C24 >
        , integral_c< T,C25 >, integral_c< T,C26 >, integral_c< T,C27 >
        , integral_c< T,C28 >, integral_c< T,C29 >, integral_c< T,C30 > 
 >
{
    typedef vector31_c type;
    typedef T value_type;
};

template<
      typename T
    , T C0, T C1, T C2, T C3, T C4, T C5, T C6, T C7, T C8, T C9, T C10
    , T C11, T C12, T C13, T C14, T C15, T C16, T C17, T C18, T C19, T C20
    , T C21, T C22, T C23, T C24, T C25, T C26, T C27, T C28, T C29, T C30
    , T C31
    >
struct vector32_c
    : vector32<
          integral_c< T,C0 >, integral_c< T,C1 >, integral_c< T,C2 >
        , integral_c< T,C3 >, integral_c< T,C4 >, integral_c< T,C5 >, integral_c< T,C6 >
        , integral_c< T,C7 >, integral_c< T,C8 >, integral_c< T,C9 >
        , integral_c< T,C10 >, integral_c< T,C11 >, integral_c< T,C12 >
        , integral_c< T,C13 >, integral_c< T,C14 >, integral_c< T,C15 >
        , integral_c< T,C16 >, integral_c< T,C17 >, integral_c< T,C18 >
        , integral_c< T,C19 >, integral_c< T,C20 >, integral_c< T,C21 >
        , integral_c< T,C22 >, integral_c< T,C23 >, integral_c< T,C24 >
        , integral_c< T,C25 >, integral_c< T,C26 >, integral_c< T,C27 >
        , integral_c< T,C28 >, integral_c< T,C29 >, integral_c< T,C30 >, integral_c<T
        , C31> 
 >
{
    typedef vector32_c type;
    typedef T value_type;
};

template<
      typename T
    , T C0, T C1, T C2, T C3, T C4, T C5, T C6, T C7, T C8, T C9, T C10
    , T C11, T C12, T C13, T C14, T C15, T C16, T C17, T C18, T C19, T C20
    , T C21, T C22, T C23, T C24, T C25, T C26, T C27, T C28, T C29, T C30
    , T C31, T C32
    >
struct vector33_c
    : vector33<
          integral_c< T,C0 >, integral_c< T,C1 >, integral_c< T,C2 >
        , integral_c< T,C3 >, integral_c< T,C4 >, integral_c< T,C5 >, integral_c< T,C6 >
        , integral_c< T,C7 >, integral_c< T,C8 >, integral_c< T,C9 >
        , integral_c< T,C10 >, integral_c< T,C11 >, integral_c< T,C12 >
        , integral_c< T,C13 >, integral_c< T,C14 >, integral_c< T,C15 >
        , integral_c< T,C16 >, integral_c< T,C17 >, integral_c< T,C18 >
        , integral_c< T,C19 >, integral_c< T,C20 >, integral_c< T,C21 >
        , integral_c< T,C22 >, integral_c< T,C23 >, integral_c< T,C24 >
        , integral_c< T,C25 >, integral_c< T,C26 >, integral_c< T,C27 >
        , integral_c< T,C28 >, integral_c< T,C29 >, integral_c< T,C30 >
        , integral_c< T,C31 >, integral_c< T,C32 > 
 >
{
    typedef vector33_c type;
    typedef T value_type;
};

template<
      typename T
    , T C0, T C1, T C2, T C3, T C4, T C5, T C6, T C7, T C8, T C9, T C10
    , T C11, T C12, T C13, T C14, T C15, T C16, T C17, T C18, T C19, T C20
    , T C21, T C22, T C23, T C24, T C25, T C26, T C27, T C28, T C29, T C30
    , T C31, T C32, T C33
    >
struct vector34_c
    : vector34<
          integral_c< T,C0 >, integral_c< T,C1 >, integral_c< T,C2 >
        , integral_c< T,C3 >, integral_c< T,C4 >, integral_c< T,C5 >, integral_c< T,C6 >
        , integral_c< T,C7 >, integral_c< T,C8 >, integral_c< T,C9 >
        , integral_c< T,C10 >, integral_c< T,C11 >, integral_c< T,C12 >
        , integral_c< T,C13 >, integral_c< T,C14 >, integral_c< T,C15 >
        , integral_c< T,C16 >, integral_c< T,C17 >, integral_c< T,C18 >
        , integral_c< T,C19 >, integral_c< T,C20 >, integral_c< T,C21 >
        , integral_c< T,C22 >, integral_c< T,C23 >, integral_c< T,C24 >
        , integral_c< T,C25 >, integral_c< T,C26 >, integral_c< T,C27 >
        , integral_c< T,C28 >, integral_c< T,C29 >, integral_c< T,C30 >
        , integral_c< T,C31 >, integral_c< T,C32 >, integral_c< T,C33 > 
 >
{
    typedef vector34_c type;
    typedef T value_type;
};

template<
      typename T
    , T C0, T C1, T C2, T C3, T C4, T C5, T C6, T C7, T C8, T C9, T C10
    , T C11, T C12, T C13, T C14, T C15, T C16, T C17, T C18, T C19, T C20
    , T C21, T C22, T C23, T C24, T C25, T C26, T C27, T C28, T C29, T C30
    , T C31, T C32, T C33, T C34
    >
struct vector35_c
    : vector35<
          integral_c< T,C0 >, integral_c< T,C1 >, integral_c< T,C2 >
        , integral_c< T,C3 >, integral_c< T,C4 >, integral_c< T,C5 >, integral_c< T,C6 >
        , integral_c< T,C7 >, integral_c< T,C8 >, integral_c< T,C9 >
        , integral_c< T,C10 >, integral_c< T,C11 >, integral_c< T,C12 >
        , integral_c< T,C13 >, integral_c< T,C14 >, integral_c< T,C15 >
        , integral_c< T,C16 >, integral_c< T,C17 >, integral_c< T,C18 >
        , integral_c< T,C19 >, integral_c< T,C20 >, integral_c< T,C21 >
        , integral_c< T,C22 >, integral_c< T,C23 >, integral_c< T,C24 >
        , integral_c< T,C25 >, integral_c< T,C26 >, integral_c< T,C27 >
        , integral_c< T,C28 >, integral_c< T,C29 >, integral_c< T,C30 >
        , integral_c< T,C31 >, integral_c< T,C32 >, integral_c< T,C33 >, integral_c<T
        , C34> 
 >
{
    typedef vector35_c type;
    typedef T value_type;
};

template<
      typename T
    , T C0, T C1, T C2, T C3, T C4, T C5, T C6, T C7, T C8, T C9, T C10
    , T C11, T C12, T C13, T C14, T C15, T C16, T C17, T C18, T C19, T C20
    , T C21, T C22, T C23, T C24, T C25, T C26, T C27, T C28, T C29, T C30
    , T C31, T C32, T C33, T C34, T C35
    >
struct vector36_c
    : vector36<
          integral_c< T,C0 >, integral_c< T,C1 >, integral_c< T,C2 >
        , integral_c< T,C3 >, integral_c< T,C4 >, integral_c< T,C5 >, integral_c< T,C6 >
        , integral_c< T,C7 >, integral_c< T,C8 >, integral_c< T,C9 >
        , integral_c< T,C10 >, integral_c< T,C11 >, integral_c< T,C12 >
        , integral_c< T,C13 >, integral_c< T,C14 >, integral_c< T,C15 >
        , integral_c< T,C16 >, integral_c< T,C17 >, integral_c< T,C18 >
        , integral_c< T,C19 >, integral_c< T,C20 >, integral_c< T,C21 >
        , integral_c< T,C22 >, integral_c< T,C23 >, integral_c< T,C24 >
        , integral_c< T,C25 >, integral_c< T,C26 >, integral_c< T,C27 >
        , integral_c< T,C28 >, integral_c< T,C29 >, integral_c< T,C30 >
        , integral_c< T,C31 >, integral_c< T,C32 >, integral_c< T,C33 >
        , integral_c< T,C34 >, integral_c< T,C35 > 
 >
{
    typedef vector36_c type;
    typedef T value_type;
};

template<
      typename T
    , T C0, T C1, T C2, T C3, T C4, T C5, T C6, T C7, T C8, T C9, T C10
    , T C11, T C12, T C13, T C14, T C15, T C16, T C17, T C18, T C19, T C20
    , T C21, T C22, T C23, T C24, T C25, T C26, T C27, T C28, T C29, T C30
    , T C31, T C32, T C33, T C34, T C35, T C36
    >
struct vector37_c
    : vector37<
          integral_c< T,C0 >, integral_c< T,C1 >, integral_c< T,C2 >
        , integral_c< T,C3 >, integral_c< T,C4 >, integral_c< T,C5 >, integral_c< T,C6 >
        , integral_c< T,C7 >, integral_c< T,C8 >, integral_c< T,C9 >
        , integral_c< T,C10 >, integral_c< T,C11 >, integral_c< T,C12 >
        , integral_c< T,C13 >, integral_c< T,C14 >, integral_c< T,C15 >
        , integral_c< T,C16 >, integral_c< T,C17 >, integral_c< T,C18 >
        , integral_c< T,C19 >, integral_c< T,C20 >, integral_c< T,C21 >
        , integral_c< T,C22 >, integral_c< T,C23 >, integral_c< T,C24 >
        , integral_c< T,C25 >, integral_c< T,C26 >, integral_c< T,C27 >
        , integral_c< T,C28 >, integral_c< T,C29 >, integral_c< T,C30 >
        , integral_c< T,C31 >, integral_c< T,C32 >, integral_c< T,C33 >
        , integral_c< T,C34 >, integral_c< T,C35 >, integral_c< T,C36 > 
 >
{
    typedef vector37_c type;
    typedef T value_type;
};

template<
      typename T
    , T C0, T C1, T C2, T C3, T C4, T C5, T C6, T C7, T C8, T C9, T C10
    , T C11, T C12, T C13, T C14, T C15, T C16, T C17, T C18, T C19, T C20
    , T C21, T C22, T C23, T C24, T C25, T C26, T C27, T C28, T C29, T C30
    , T C31, T C32, T C33, T C34, T C35, T C36, T C37
    >
struct vector38_c
    : vector38<
          integral_c< T,C0 >, integral_c< T,C1 >, integral_c< T,C2 >
        , integral_c< T,C3 >, integral_c< T,C4 >, integral_c< T,C5 >, integral_c< T,C6 >
        , integral_c< T,C7 >, integral_c< T,C8 >, integral_c< T,C9 >
        , integral_c< T,C10 >, integral_c< T,C11 >, integral_c< T,C12 >
        , integral_c< T,C13 >, integral_c< T,C14 >, integral_c< T,C15 >
        , integral_c< T,C16 >, integral_c< T,C17 >, integral_c< T,C18 >
        , integral_c< T,C19 >, integral_c< T,C20 >, integral_c< T,C21 >
        , integral_c< T,C22 >, integral_c< T,C23 >, integral_c< T,C24 >
        , integral_c< T,C25 >, integral_c< T,C26 >, integral_c< T,C27 >
        , integral_c< T,C28 >, integral_c< T,C29 >, integral_c< T,C30 >
        , integral_c< T,C31 >, integral_c< T,C32 >, integral_c< T,C33 >
        , integral_c< T,C34 >, integral_c< T,C35 >, integral_c< T,C36 >, integral_c<T
        , C37> 
 >
{
    typedef vector38_c type;
    typedef T value_type;
};

template<
      typename T
    , T C0, T C1, T C2, T C3, T C4, T C5, T C6, T C7, T C8, T C9, T C10
    , T C11, T C12, T C13, T C14, T C15, T C16, T C17, T C18, T C19, T C20
    , T C21, T C22, T C23, T C24, T C25, T C26, T C27, T C28, T C29, T C30
    , T C31, T C32, T C33, T C34, T C35, T C36, T C37, T C38
    >
struct vector39_c
    : vector39<
          integral_c< T,C0 >, integral_c< T,C1 >, integral_c< T,C2 >
        , integral_c< T,C3 >, integral_c< T,C4 >, integral_c< T,C5 >, integral_c< T,C6 >
        , integral_c< T,C7 >, integral_c< T,C8 >, integral_c< T,C9 >
        , integral_c< T,C10 >, integral_c< T,C11 >, integral_c< T,C12 >
        , integral_c< T,C13 >, integral_c< T,C14 >, integral_c< T,C15 >
        , integral_c< T,C16 >, integral_c< T,C17 >, integral_c< T,C18 >
        , integral_c< T,C19 >, integral_c< T,C20 >, integral_c< T,C21 >
        , integral_c< T,C22 >, integral_c< T,C23 >, integral_c< T,C24 >
        , integral_c< T,C25 >, integral_c< T,C26 >, integral_c< T,C27 >
        , integral_c< T,C28 >, integral_c< T,C29 >, integral_c< T,C30 >
        , integral_c< T,C31 >, integral_c< T,C32 >, integral_c< T,C33 >
        , integral_c< T,C34 >, integral_c< T,C35 >, integral_c< T,C36 >
        , integral_c< T,C37 >, integral_c< T,C38 > 
 >
{
    typedef vector39_c type;
    typedef T value_type;
};

template<
      typename T
    , T C0, T C1, T C2, T C3, T C4, T C5, T C6, T C7, T C8, T C9, T C10
    , T C11, T C12, T C13, T C14, T C15, T C16, T C17, T C18, T C19, T C20
    , T C21, T C22, T C23, T C24, T C25, T C26, T C27, T C28, T C29, T C30
    , T C31, T C32, T C33, T C34, T C35, T C36, T C37, T C38, T C39
    >
struct vector40_c
    : vector40<
          integral_c< T,C0 >, integral_c< T,C1 >, integral_c< T,C2 >
        , integral_c< T,C3 >, integral_c< T,C4 >, integral_c< T,C5 >, integral_c< T,C6 >
        , integral_c< T,C7 >, integral_c< T,C8 >, integral_c< T,C9 >
        , integral_c< T,C10 >, integral_c< T,C11 >, integral_c< T,C12 >
        , integral_c< T,C13 >, integral_c< T,C14 >, integral_c< T,C15 >
        , integral_c< T,C16 >, integral_c< T,C17 >, integral_c< T,C18 >
        , integral_c< T,C19 >, integral_c< T,C20 >, integral_c< T,C21 >
        , integral_c< T,C22 >, integral_c< T,C23 >, integral_c< T,C24 >
        , integral_c< T,C25 >, integral_c< T,C26 >, integral_c< T,C27 >
        , integral_c< T,C28 >, integral_c< T,C29 >, integral_c< T,C30 >
        , integral_c< T,C31 >, integral_c< T,C32 >, integral_c< T,C33 >
        , integral_c< T,C34 >, integral_c< T,C35 >, integral_c< T,C36 >
        , integral_c< T,C37 >, integral_c< T,C38 >, integral_c< T,C39 > 
 >
{
    typedef vector40_c type;
    typedef T value_type;
};

}}
