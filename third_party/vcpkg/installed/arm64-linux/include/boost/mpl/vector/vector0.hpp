
#ifndef BOOST_MPL_VECTOR_VECTOR0_HPP_INCLUDED
#define BOOST_MPL_VECTOR_VECTOR0_HPP_INCLUDED

// Copyright Aleksey Gurtovoy 2000-2004
//
// Distributed under the Boost Software License, Version 1.0. 
// (See accompanying file LICENSE_1_0.txt or copy at 
// http://www.boost.org/LICENSE_1_0.txt)
//
// See http://www.boost.org/libs/mpl for documentation.

// $Id$
// $Date$
// $Revision$

#include <boost/mpl/vector/aux_/at.hpp>
#include <boost/mpl/vector/aux_/front.hpp>
#include <boost/mpl/vector/aux_/push_front.hpp>
#include <boost/mpl/vector/aux_/pop_front.hpp>
#include <boost/mpl/vector/aux_/push_back.hpp>
#include <boost/mpl/vector/aux_/pop_back.hpp>
#include <boost/mpl/vector/aux_/back.hpp>
#include <boost/mpl/vector/aux_/clear.hpp>
#include <boost/mpl/vector/aux_/O1_size.hpp>
#include <boost/mpl/vector/aux_/size.hpp>
#include <boost/mpl/vector/aux_/empty.hpp>
#include <boost/mpl/vector/aux_/item.hpp>
#include <boost/mpl/vector/aux_/iterator.hpp>
#include <boost/mpl/vector/aux_/vector0.hpp>
#include <boost/mpl/vector/aux_/begin_end.hpp>
#include <boost/mpl/vector/aux_/tag.hpp>

#endif // BOOST_MPL_VECTOR_VECTOR0_HPP_INCLUDED
