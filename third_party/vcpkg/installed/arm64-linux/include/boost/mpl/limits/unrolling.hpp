
#ifndef BOOST_MPL_LIMITS_UNROLLING_HPP_INCLUDED
#define BOOST_MPL_LIMITS_UNROLLING_HPP_INCLUDED

// Copyright Aleksey Gurtovoy 2000-2004
//
// Distributed under the Boost Software License, Version 1.0. 
// (See accompanying file LICENSE_1_0.txt or copy at 
// http://www.boost.org/LICENSE_1_0.txt)
//
// See http://www.boost.org/libs/mpl for documentation.

// $Id$
// $Date$
// $Revision$

#if !defined(BOOST_MPL_LIMIT_UNROLLING)
#   define BOOST_MPL_LIMIT_UNROLLING 4
#endif

#endif // BOOST_MPL_LIMITS_UNROLLING_HPP_INCLUDED
