
#ifndef BOOST_MPL_SEQUENCE_TAG_FWD_HPP_INCLUDED
#define BOOST_MPL_SEQUENCE_TAG_FWD_HPP_INCLUDED

// Copyright Aleksey Gurtovoy 2000-2004
//
// Distributed under the Boost Software License, Version 1.0. 
// (See accompanying file LICENSE_1_0.txt or copy at 
// http://www.boost.org/LICENSE_1_0.txt)
//
// See http://www.boost.org/libs/mpl for documentation.

// $Id$
// $Date$
// $Revision$

namespace boost { namespace mpl {

struct nested_begin_end_tag;
struct non_sequence_tag;

template< typename Sequence > struct sequence_tag;

}}

#endif // BOOST_MPL_SEQUENCE_TAG_FWD_HPP_INCLUDED
