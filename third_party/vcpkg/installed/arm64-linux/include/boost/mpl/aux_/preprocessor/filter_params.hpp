
#ifndef BOOST_MPL_AUX_PREPROCESSOR_FILTER_PARAMS_HPP_INCLUDED
#define BOOST_MPL_AUX_PREPROCESSOR_FILTER_PARAMS_HPP_INCLUDED

// Copyright Aleksey Gurtovoy 2000-2004
//
// Distributed under the Boost Software License, Version 1.0. 
// (See accompanying file LICENSE_1_0.txt or copy at 
// http://www.boost.org/LICENSE_1_0.txt)
//
// See http://www.boost.org/libs/mpl for documentation.

// $Id$
// $Date$
// $Revision$

#define BOOST_MPL_PP_FILTER_PARAMS_0(p1,p2,p3,p4,p5,p6,p7,p8,p9) 
#define BOOST_MPL_PP_FILTER_PARAMS_1(p1,p2,p3,p4,p5,p6,p7,p8,p9) p1
#define BOOST_MPL_PP_FILTER_PARAMS_2(p1,p2,p3,p4,p5,p6,p7,p8,p9) p1,p2
#define BOOST_MPL_PP_FILTER_PARAMS_3(p1,p2,p3,p4,p5,p6,p7,p8,p9) p1,p2,p3
#define BOOST_MPL_PP_FILTER_PARAMS_4(p1,p2,p3,p4,p5,p6,p7,p8,p9) p1,p2,p3,p4
#define BOOST_MPL_PP_FILTER_PARAMS_5(p1,p2,p3,p4,p5,p6,p7,p8,p9) p1,p2,p3,p4,p5
#define BOOST_MPL_PP_FILTER_PARAMS_6(p1,p2,p3,p4,p5,p6,p7,p8,p9) p1,p2,p3,p4,p5,p6
#define BOOST_MPL_PP_FILTER_PARAMS_7(p1,p2,p3,p4,p5,p6,p7,p8,p9) p1,p2,p3,p4,p5,p6,p7
#define BOOST_MPL_PP_FILTER_PARAMS_8(p1,p2,p3,p4,p5,p6,p7,p8,p9) p1,p2,p3,p4,p5,p6,p7,p8
#define BOOST_MPL_PP_FILTER_PARAMS_9(p1,p2,p3,p4,p5,p6,p7,p8,p9) p1,p2,p3,p4,p5,p6,p7,p8,p9

#endif // BOOST_MPL_AUX_PREPROCESSOR_FILTER_PARAMS_HPP_INCLUDED
