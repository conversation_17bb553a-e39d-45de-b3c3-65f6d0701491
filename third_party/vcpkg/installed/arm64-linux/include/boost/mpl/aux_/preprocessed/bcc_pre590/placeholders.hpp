
// Copyright <PERSON><PERSON><PERSON> Gurtovoy 2001-2004
// Copyright <PERSON> 2001-2003
//
// Distributed under the Boost Software License, Version 1.0. 
// (See accompanying file LICENSE_1_0.txt or copy at 
// http://www.boost.org/LICENSE_1_0.txt)
//

// *Preprocessed* version of the main "placeholders.hpp" header
// -- DO NOT modify by hand!

BOOST_MPL_AUX_ADL_BARRIER_NAMESPACE_OPEN
typedef arg< -1 > _;
BOOST_MPL_AUX_ADL_BARRIER_NAMESPACE_CLOSE
namespace boost { namespace mpl {

BOOST_MPL_AUX_ARG_ADL_BARRIER_DECL(_)

namespace placeholders {
using BOOST_MPL_AUX_ADL_BARRIER_NAMESPACE::_;
}

}}

/// agurt, 17/mar/02: one more placeholder for the last 'apply#' 
/// specialization
BOOST_MPL_AUX_ADL_BARRIER_NAMESPACE_OPEN
typedef arg<1> _1;

BOOST_MPL_AUX_ADL_BARRIER_NAMESPACE_CLOSE
namespace boost { namespace mpl {

BOOST_MPL_AUX_ARG_ADL_BARRIER_DECL(_1)

namespace placeholders {
using BOOST_MPL_AUX_ADL_BARRIER_NAMESPACE::_1;
}

}}
BOOST_MPL_AUX_ADL_BARRIER_NAMESPACE_OPEN
typedef arg<2> _2;

BOOST_MPL_AUX_ADL_BARRIER_NAMESPACE_CLOSE
namespace boost { namespace mpl {

BOOST_MPL_AUX_ARG_ADL_BARRIER_DECL(_2)

namespace placeholders {
using BOOST_MPL_AUX_ADL_BARRIER_NAMESPACE::_2;
}

}}
BOOST_MPL_AUX_ADL_BARRIER_NAMESPACE_OPEN
typedef arg<3> _3;

BOOST_MPL_AUX_ADL_BARRIER_NAMESPACE_CLOSE
namespace boost { namespace mpl {

BOOST_MPL_AUX_ARG_ADL_BARRIER_DECL(_3)

namespace placeholders {
using BOOST_MPL_AUX_ADL_BARRIER_NAMESPACE::_3;
}

}}
BOOST_MPL_AUX_ADL_BARRIER_NAMESPACE_OPEN
typedef arg<4> _4;

BOOST_MPL_AUX_ADL_BARRIER_NAMESPACE_CLOSE
namespace boost { namespace mpl {

BOOST_MPL_AUX_ARG_ADL_BARRIER_DECL(_4)

namespace placeholders {
using BOOST_MPL_AUX_ADL_BARRIER_NAMESPACE::_4;
}

}}
BOOST_MPL_AUX_ADL_BARRIER_NAMESPACE_OPEN
typedef arg<5> _5;

BOOST_MPL_AUX_ADL_BARRIER_NAMESPACE_CLOSE
namespace boost { namespace mpl {

BOOST_MPL_AUX_ARG_ADL_BARRIER_DECL(_5)

namespace placeholders {
using BOOST_MPL_AUX_ADL_BARRIER_NAMESPACE::_5;
}

}}
BOOST_MPL_AUX_ADL_BARRIER_NAMESPACE_OPEN
typedef arg<6> _6;

BOOST_MPL_AUX_ADL_BARRIER_NAMESPACE_CLOSE
namespace boost { namespace mpl {

BOOST_MPL_AUX_ARG_ADL_BARRIER_DECL(_6)

namespace placeholders {
using BOOST_MPL_AUX_ADL_BARRIER_NAMESPACE::_6;
}

}}
