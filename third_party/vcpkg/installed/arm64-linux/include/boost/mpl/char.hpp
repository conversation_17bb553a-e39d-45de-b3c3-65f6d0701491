
#ifndef BOOST_MPL_CHAR_HPP_INCLUDED
#define BOOST_MPL_CHAR_HPP_INCLUDED

// Copyright <PERSON> 2008
//
// Distributed under the Boost Software License, Version 1.0. 
// (See accompanying file LICENSE_1_0.txt or copy at 
// http://www.boost.org/LICENSE_1_0.txt)
//
// See http://www.boost.org/libs/mpl for documentation.

// $Source$
// $Date: 2008-06-14 08:41:37 -0700 (Sat, 16 Jun 2008) $
// $Revision: 24874 $

#include <boost/mpl/char_fwd.hpp>

#define AUX_WRAPPER_VALUE_TYPE char
#include <boost/mpl/aux_/integral_wrapper.hpp>

#endif // BOOST_MPL_CHAR_HPP_INCLUDED
