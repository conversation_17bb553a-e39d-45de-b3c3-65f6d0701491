
// Copyright Aleksey Gurtovoy 2000-2004
//
// Distributed under the Boost Software License, Version 1.0. 
// (See accompanying file LICENSE_1_0.txt or copy at 
// http://www.boost.org/LICENSE_1_0.txt)
//

// Preprocessed version of "boost/mpl/list/list40_c.hpp" header
// -- DO NOT modify by hand!

namespace boost { namespace mpl {

template<
      typename T
    , T C0, T C1, T C2, T C3, T C4, T C5, T C6, T C7, T C8, T C9, T C10
    , T C11, T C12, T C13, T C14, T C15, T C16, T C17, T C18, T C19, T C20
    , T C21, T C22, T C23, T C24, T C25, T C26, T C27, T C28, T C29, T C30
    >
struct list31_c
    : l_item<
          long_<31>
        , integral_c< T,C0 >
        , list30_c< T,C1,C2,C3,C4,C5,C6,C7,C8,C9,C10,C11,C12,C13,C14,C15,C16,C17,C18,C19,C20,C21,C22,C23,C24,C25,C26,C27,C28,<PERSON>29,<PERSON>30 >
        >
{
    typedef list31_c type;
    typedef T value_type;
};

template<
      typename T
    , T C0, T C1, T C2, T C3, T C4, T C5, T C6, T C7, T C8, T C9, T C10
    , T C11, T C12, T C13, T C14, T C15, T C16, T C17, T <PERSON>18, T C19, T C20
    , T C21, T C22, T C23, T C24, T C25, T C26, T C27, T C28, T C29, T C30
    , T C31
    >
struct list32_c
    : l_item<
          long_<32>
        , integral_c< T,C0 >
        , list31_c< T,C1,C2,C3,C4,C5,C6,C7,C8,C9,C10,C11,C12,C13,C14,C15,C16,C17,C18,C19,C20,C21,C22,C23,C24,C25,C26,C27,C28,C29,C30,C31 >
        >
{
    typedef list32_c type;
    typedef T value_type;
};

template<
      typename T
    , T C0, T C1, T C2, T C3, T C4, T C5, T C6, T C7, T C8, T C9, T C10
    , T C11, T C12, T C13, T C14, T C15, T C16, T C17, T C18, T C19, T C20
    , T C21, T C22, T C23, T C24, T C25, T C26, T C27, T C28, T C29, T C30
    , T C31, T C32
    >
struct list33_c
    : l_item<
          long_<33>
        , integral_c< T,C0 >
        , list32_c< T,C1,C2,C3,C4,C5,C6,C7,C8,C9,C10,C11,C12,C13,C14,C15,C16,C17,C18,C19,C20,C21,C22,C23,C24,C25,C26,C27,C28,C29,C30,C31,C32 >
        >
{
    typedef list33_c type;
    typedef T value_type;
};

template<
      typename T
    , T C0, T C1, T C2, T C3, T C4, T C5, T C6, T C7, T C8, T C9, T C10
    , T C11, T C12, T C13, T C14, T C15, T C16, T C17, T C18, T C19, T C20
    , T C21, T C22, T C23, T C24, T C25, T C26, T C27, T C28, T C29, T C30
    , T C31, T C32, T C33
    >
struct list34_c
    : l_item<
          long_<34>
        , integral_c< T,C0 >
        , list33_c< T,C1,C2,C3,C4,C5,C6,C7,C8,C9,C10,C11,C12,C13,C14,C15,C16,C17,C18,C19,C20,C21,C22,C23,C24,C25,C26,C27,C28,C29,C30,C31,C32,C33 >
        >
{
    typedef list34_c type;
    typedef T value_type;
};

template<
      typename T
    , T C0, T C1, T C2, T C3, T C4, T C5, T C6, T C7, T C8, T C9, T C10
    , T C11, T C12, T C13, T C14, T C15, T C16, T C17, T C18, T C19, T C20
    , T C21, T C22, T C23, T C24, T C25, T C26, T C27, T C28, T C29, T C30
    , T C31, T C32, T C33, T C34
    >
struct list35_c
    : l_item<
          long_<35>
        , integral_c< T,C0 >
        , list34_c< T,C1,C2,C3,C4,C5,C6,C7,C8,C9,C10,C11,C12,C13,C14,C15,C16,C17,C18,C19,C20,C21,C22,C23,C24,C25,C26,C27,C28,C29,C30,C31,C32,C33,C34 >
        >
{
    typedef list35_c type;
    typedef T value_type;
};

template<
      typename T
    , T C0, T C1, T C2, T C3, T C4, T C5, T C6, T C7, T C8, T C9, T C10
    , T C11, T C12, T C13, T C14, T C15, T C16, T C17, T C18, T C19, T C20
    , T C21, T C22, T C23, T C24, T C25, T C26, T C27, T C28, T C29, T C30
    , T C31, T C32, T C33, T C34, T C35
    >
struct list36_c
    : l_item<
          long_<36>
        , integral_c< T,C0 >
        , list35_c< T,C1,C2,C3,C4,C5,C6,C7,C8,C9,C10,C11,C12,C13,C14,C15,C16,C17,C18,C19,C20,C21,C22,C23,C24,C25,C26,C27,C28,C29,C30,C31,C32,C33,C34,C35 >
        >
{
    typedef list36_c type;
    typedef T value_type;
};

template<
      typename T
    , T C0, T C1, T C2, T C3, T C4, T C5, T C6, T C7, T C8, T C9, T C10
    , T C11, T C12, T C13, T C14, T C15, T C16, T C17, T C18, T C19, T C20
    , T C21, T C22, T C23, T C24, T C25, T C26, T C27, T C28, T C29, T C30
    , T C31, T C32, T C33, T C34, T C35, T C36
    >
struct list37_c
    : l_item<
          long_<37>
        , integral_c< T,C0 >
        , list36_c< T,C1,C2,C3,C4,C5,C6,C7,C8,C9,C10,C11,C12,C13,C14,C15,C16,C17,C18,C19,C20,C21,C22,C23,C24,C25,C26,C27,C28,C29,C30,C31,C32,C33,C34,C35,C36 >
        >
{
    typedef list37_c type;
    typedef T value_type;
};

template<
      typename T
    , T C0, T C1, T C2, T C3, T C4, T C5, T C6, T C7, T C8, T C9, T C10
    , T C11, T C12, T C13, T C14, T C15, T C16, T C17, T C18, T C19, T C20
    , T C21, T C22, T C23, T C24, T C25, T C26, T C27, T C28, T C29, T C30
    , T C31, T C32, T C33, T C34, T C35, T C36, T C37
    >
struct list38_c
    : l_item<
          long_<38>
        , integral_c< T,C0 >
        , list37_c< T,C1,C2,C3,C4,C5,C6,C7,C8,C9,C10,C11,C12,C13,C14,C15,C16,C17,C18,C19,C20,C21,C22,C23,C24,C25,C26,C27,C28,C29,C30,C31,C32,C33,C34,C35,C36,C37 >
        >
{
    typedef list38_c type;
    typedef T value_type;
};

template<
      typename T
    , T C0, T C1, T C2, T C3, T C4, T C5, T C6, T C7, T C8, T C9, T C10
    , T C11, T C12, T C13, T C14, T C15, T C16, T C17, T C18, T C19, T C20
    , T C21, T C22, T C23, T C24, T C25, T C26, T C27, T C28, T C29, T C30
    , T C31, T C32, T C33, T C34, T C35, T C36, T C37, T C38
    >
struct list39_c
    : l_item<
          long_<39>
        , integral_c< T,C0 >
        , list38_c< T,C1,C2,C3,C4,C5,C6,C7,C8,C9,C10,C11,C12,C13,C14,C15,C16,C17,C18,C19,C20,C21,C22,C23,C24,C25,C26,C27,C28,C29,C30,C31,C32,C33,C34,C35,C36,C37,C38 >
        >
{
    typedef list39_c type;
    typedef T value_type;
};

template<
      typename T
    , T C0, T C1, T C2, T C3, T C4, T C5, T C6, T C7, T C8, T C9, T C10
    , T C11, T C12, T C13, T C14, T C15, T C16, T C17, T C18, T C19, T C20
    , T C21, T C22, T C23, T C24, T C25, T C26, T C27, T C28, T C29, T C30
    , T C31, T C32, T C33, T C34, T C35, T C36, T C37, T C38, T C39
    >
struct list40_c
    : l_item<
          long_<40>
        , integral_c< T,C0 >
        , list39_c< T,C1,C2,C3,C4,C5,C6,C7,C8,C9,C10,C11,C12,C13,C14,C15,C16,C17,C18,C19,C20,C21,C22,C23,C24,C25,C26,C27,C28,C29,C30,C31,C32,C33,C34,C35,C36,C37,C38,C39 >
        >
{
    typedef list40_c type;
    typedef T value_type;
};

}}
