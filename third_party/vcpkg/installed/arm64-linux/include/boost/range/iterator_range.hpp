// Boost.Range library
//
//  Copyright <PERSON> 2009.
//  Use, modification and distribution is subject to the Boost Software
//  License, Version 1.0. (See accompanying file LICENSE_1_0.txt or copy at
//  http://www.boost.org/LICENSE_1_0.txt)
//
// For more information, see http://www.boost.org/libs/range/
//
#ifndef BOOST_RANGE_ITERATOR_RANGE_HPP_INCLUDED
#define BOOST_RANGE_ITERATOR_RANGE_HPP_INCLUDED

#include "boost/range/iterator_range_core.hpp"
#include "boost/range/iterator_range_io.hpp"

#endif // include guard
