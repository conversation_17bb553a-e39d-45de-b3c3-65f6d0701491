// Boost.Range library
//
//  Copyright <PERSON><PERSON> 2003-2004. Use, modification and
//  distribution is subject to the Boost Software License, Version
//  1.0. (See accompanying file LICENSE_1_0.txt or copy at
//  http://www.boost.org/LICENSE_1_0.txt)
//
// For more information, see http://www.boost.org/libs/range/
//

#ifndef BOOST_RANGE_METAFUNCTIONS_HPP
#define BOOST_RANGE_METAFUNCTIONS_HPP

#if defined(_MSC_VER)
# pragma once
#endif

#include <boost/range/iterator.hpp>
#include <boost/range/has_range_iterator.hpp>
#include <boost/range/reverse_iterator.hpp>
#include <boost/range/value_type.hpp>
#include <boost/range/size_type.hpp>
#include <boost/range/difference_type.hpp>
#include <boost/range/category.hpp>
#include <boost/range/reference.hpp>
#include <boost/range/pointer.hpp>

#endif
