/////////////////////////////////////////////////////////////////////////////
//
// (C) Copyright Ion Gaztanaga  2014-2014
//
// Distributed under the Boost Software License, Version 1.0.
//    (See accompanying file LICENSE_1_0.txt or copy at
//          http://www.boost.org/LICENSE_1_0.txt)
//
// See http://www.boost.org/libs/intrusive for documentation.
//
/////////////////////////////////////////////////////////////////////////////

#ifndef BOOST_INTRUSIVE_DETAIL_REVERSE_ITERATOR_HPP
#define BOOST_INTRUSIVE_DETAIL_REVERSE_ITERATOR_HPP

#include <boost/move/detail/reverse_iterator.hpp>

namespace boost {
namespace intrusive {

using boost::movelib::reverse_iterator;
using boost::movelib::make_reverse_iterator;

} //namespace intrusive {
} //namespace boost {


#endif //BOOST_INTRUSIVE_DETAIL_REVERSE_ITERATOR_HPP
