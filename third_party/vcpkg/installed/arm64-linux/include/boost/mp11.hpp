#ifndef BOOST_MP11_HPP_INCLUDED
#define BOOST_MP11_HPP_INCLUDED

//  Copyright 2015-2024 Peter Dimov.
//
//  Distributed under the Boost Software License, Version 1.0.
//
//  See accompanying file LICENSE_1_0.txt or copy at
//  http://www.boost.org/LICENSE_1_0.txt

#include <boost/mp11/list.hpp>
#include <boost/mp11/algorithm.hpp>
#include <boost/mp11/integral.hpp>
#include <boost/mp11/utility.hpp>
#include <boost/mp11/function.hpp>
#include <boost/mp11/map.hpp>
#include <boost/mp11/set.hpp>
#include <boost/mp11/bind.hpp>
#include <boost/mp11/lambda.hpp>
#include <boost/mp11/integer_sequence.hpp>
#include <boost/mp11/tuple.hpp>

#endif // #ifndef BOOST_MP11_HPP_INCLUDED
