#ifndef BOOST_FUNCTION_HPP_INCLUDED
#define BOOST_FUNCTION_HPP_INCLUDED

// Boost.Function library

//  Copyright Douglas Gregor 2001-2003. Use, modification and
//  distribution is subject to the Boost Software License, Version
//  1.0. (See accompanying file LICENSE_1_0.txt or copy at
//  http://www.boost.org/LICENSE_1_0.txt)

// For more information, see http://www.boost.org/libs/function

// <PERSON>, <PERSON> and <PERSON> were all very helpful in the
// design of this library.

#include <boost/function/function_template.hpp>

#endif // #ifndef BOOST_FUNCTION_HPP_INCLUDED
