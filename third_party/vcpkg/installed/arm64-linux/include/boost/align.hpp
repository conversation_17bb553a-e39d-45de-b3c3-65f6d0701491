/*
Copyright 2014-2015 <PERSON>
(<EMAIL>)

Distributed under the Boost Software License, Version 1.0.
(http://www.boost.org/LICENSE_1_0.txt)
*/
#ifndef BOOST_ALIGN_HPP
#define BOOST_ALIGN_HPP

#include <boost/align/align.hpp>
#include <boost/align/align_down.hpp>
#include <boost/align/align_up.hpp>
#include <boost/align/aligned_alloc.hpp>
#include <boost/align/aligned_allocator.hpp>
#include <boost/align/aligned_allocator_adaptor.hpp>
#include <boost/align/aligned_delete.hpp>
#include <boost/align/alignment_of.hpp>
#include <boost/align/assume_aligned.hpp>
#include <boost/align/is_aligned.hpp>

#endif
