/*
 * Copyright 2017 <PERSON><PERSON>
 *
 * Distributed under the Boost Software License, Version 1.0.
 * See http://www.boost.org/LICENSE_1_0.txt
 *
 * This header is deprecated, use boost/winapi/error_handling.hpp instead.
 */

#ifndef BOOST_DETAIL_WINAPI_ERROR_HANDLING_HPP
#define BOOST_DETAIL_WINAPI_ERROR_HANDLING_HPP

#include <boost/config/header_deprecated.hpp>

BOOST_HEADER_DEPRECATED("<boost/winapi/error_handling.hpp>")

#include <boost/winapi/error_handling.hpp>
#include <boost/detail/winapi/detail/deprecated_namespace.hpp>

#ifdef BOOST_HAS_PRAGMA_ONCE
#pragma once
#endif

#endif // BOOST_DETAIL_WINAPI_ERROR_HANDLING_HPP
