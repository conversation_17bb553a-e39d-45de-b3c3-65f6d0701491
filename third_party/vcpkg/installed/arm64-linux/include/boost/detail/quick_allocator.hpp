#ifndef BOOST_DETAIL_QUICK_ALLOCATOR_HPP_INCLUDED
#define BOOST_DETAIL_QUICK_ALLOCATOR_HPP_INCLUDED

// Copyright (c) 2003 <PERSON>
// Copyright (c) 2003 Peter Dimov
// Distributed under the Boost Software License, Version 1.0.
// https://www.boost.org/LICENSE_1_0.txt

#include <boost/config/header_deprecated.hpp>

BOOST_HEADER_DEPRECATED("<boost/smart_ptr/detail/quick_allocator.hpp>")

#include <boost/smart_ptr/detail/quick_allocator.hpp>

#endif  // #ifndef BOOST_DETAIL_QUICK_ALLOCATOR_HPP_INCLUDED
