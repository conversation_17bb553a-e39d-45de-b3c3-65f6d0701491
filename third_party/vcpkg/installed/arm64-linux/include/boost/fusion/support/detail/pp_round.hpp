/*=============================================================================
    Copyright (c) 2011 <PERSON>

    Distributed under the Boost Software License, Version 1.0. (See accompanying
    file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
==============================================================================*/

#ifndef BOOST_BOOST_FUSION_SUPPORT_PP_ROUND_HPP
#define BOOST_BOOST_FUSION_SUPPORT_PP_ROUND_HPP

#include <boost/fusion/support/config.hpp>
#include <boost/preprocessor/cat.hpp>
#include <boost/preprocessor/comparison/less.hpp>
#include <boost/preprocessor/control/if.hpp>

#define BOOST_FUSION_PP_ROUND_UP(N)                                            \
      BOOST_PP_CAT(BOOST_FUSION_PP_DO_ROUND_UP_, N)()                          \
/**/

#define BOOST_FUSION_PP_DO_ROUND_UP_0()  10
#define BOOST_FUSION_PP_DO_ROUND_UP_1()  10
#define BOOST_FUSION_PP_DO_ROUND_UP_2()  10
#define BOOST_FUSION_PP_DO_ROUND_UP_3()  10
#define BOOST_FUSION_PP_DO_ROUND_UP_4()  10
#define BOOST_FUSION_PP_DO_ROUND_UP_5()  10
#define BOOST_FUSION_PP_DO_ROUND_UP_6()  10
#define BOOST_FUSION_PP_DO_ROUND_UP_7()  10
#define BOOST_FUSION_PP_DO_ROUND_UP_8()  10
#define BOOST_FUSION_PP_DO_ROUND_UP_9()  10
#define BOOST_FUSION_PP_DO_ROUND_UP_10() 10
#define BOOST_FUSION_PP_DO_ROUND_UP_11() 20
#define BOOST_FUSION_PP_DO_ROUND_UP_12() 20
#define BOOST_FUSION_PP_DO_ROUND_UP_13() 20
#define BOOST_FUSION_PP_DO_ROUND_UP_14() 20
#define BOOST_FUSION_PP_DO_ROUND_UP_15() 20
#define BOOST_FUSION_PP_DO_ROUND_UP_16() 20
#define BOOST_FUSION_PP_DO_ROUND_UP_17() 20
#define BOOST_FUSION_PP_DO_ROUND_UP_18() 20
#define BOOST_FUSION_PP_DO_ROUND_UP_19() 20
#define BOOST_FUSION_PP_DO_ROUND_UP_20() 20
#define BOOST_FUSION_PP_DO_ROUND_UP_21() 30
#define BOOST_FUSION_PP_DO_ROUND_UP_22() 30
#define BOOST_FUSION_PP_DO_ROUND_UP_23() 30
#define BOOST_FUSION_PP_DO_ROUND_UP_24() 30
#define BOOST_FUSION_PP_DO_ROUND_UP_25() 30
#define BOOST_FUSION_PP_DO_ROUND_UP_26() 30
#define BOOST_FUSION_PP_DO_ROUND_UP_27() 30
#define BOOST_FUSION_PP_DO_ROUND_UP_28() 30
#define BOOST_FUSION_PP_DO_ROUND_UP_29() 30
#define BOOST_FUSION_PP_DO_ROUND_UP_30() 30
#define BOOST_FUSION_PP_DO_ROUND_UP_31() 40
#define BOOST_FUSION_PP_DO_ROUND_UP_32() 40
#define BOOST_FUSION_PP_DO_ROUND_UP_33() 40
#define BOOST_FUSION_PP_DO_ROUND_UP_34() 40
#define BOOST_FUSION_PP_DO_ROUND_UP_35() 40
#define BOOST_FUSION_PP_DO_ROUND_UP_36() 40
#define BOOST_FUSION_PP_DO_ROUND_UP_37() 40
#define BOOST_FUSION_PP_DO_ROUND_UP_38() 40
#define BOOST_FUSION_PP_DO_ROUND_UP_39() 40
#define BOOST_FUSION_PP_DO_ROUND_UP_40() 40
#define BOOST_FUSION_PP_DO_ROUND_UP_41() 50
#define BOOST_FUSION_PP_DO_ROUND_UP_42() 50
#define BOOST_FUSION_PP_DO_ROUND_UP_43() 50
#define BOOST_FUSION_PP_DO_ROUND_UP_44() 50
#define BOOST_FUSION_PP_DO_ROUND_UP_45() 50
#define BOOST_FUSION_PP_DO_ROUND_UP_46() 50
#define BOOST_FUSION_PP_DO_ROUND_UP_47() 50
#define BOOST_FUSION_PP_DO_ROUND_UP_48() 50
#define BOOST_FUSION_PP_DO_ROUND_UP_49() 50
#define BOOST_FUSION_PP_DO_ROUND_UP_50() 50

#endif
