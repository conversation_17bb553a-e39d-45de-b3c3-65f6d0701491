/*=============================================================================
    Copyright (c) 2001-2011 <PERSON>

    Distributed under the Boost Software License, Version 1.0. (See accompanying
    file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
==============================================================================*/
#if !defined(FUSION_SEQUENCE_GENERATION_10022005_0615)
#define FUSION_SEQUENCE_GENERATION_10022005_0615

#include <boost/fusion/support/config.hpp>
#include <boost/fusion/container/generation/make_deque.hpp>
#include <boost/fusion/container/generation/deque_tie.hpp>
#include <boost/fusion/container/generation/cons_tie.hpp>
#include <boost/fusion/container/generation/ignore.hpp>
#include <boost/fusion/container/generation/list_tie.hpp>
#include <boost/fusion/container/generation/make_cons.hpp>
#include <boost/fusion/container/generation/make_list.hpp>
#include <boost/fusion/container/generation/make_map.hpp>
#include <boost/fusion/container/generation/map_tie.hpp>
#include <boost/fusion/container/generation/make_vector.hpp>
#include <boost/fusion/container/generation/vector_tie.hpp>
#include <boost/fusion/container/generation/make_set.hpp>

#endif
