/*=============================================================================
    Copyright (c) 2001-2011 <PERSON>

    Distributed under the Boost Software License, Version 1.0. (See accompanying
    file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

    This is an auto-generated file. Do not edit!
==============================================================================*/

#if FUSION_MAX_MAP_SIZE <= 10
#include <boost/fusion/container/generation/detail/preprocessed/make_map10.hpp>
#elif FUSION_MAX_MAP_SIZE <= 20
#include <boost/fusion/container/generation/detail/preprocessed/make_map20.hpp>
#elif FUSION_MAX_MAP_SIZE <= 30
#include <boost/fusion/container/generation/detail/preprocessed/make_map30.hpp>
#elif FUSION_MAX_MAP_SIZE <= 40
#include <boost/fusion/container/generation/detail/preprocessed/make_map40.hpp>
#elif FUSION_MAX_MAP_SIZE <= 50
#include <boost/fusion/container/generation/detail/preprocessed/make_map50.hpp>
#else
#error "FUSION_MAX_MAP_SIZE out of bounds for preprocessed headers"
#endif
