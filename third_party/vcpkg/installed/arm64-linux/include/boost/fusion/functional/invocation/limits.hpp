/*=============================================================================
    Copyright (c) 2006-2007 <PERSON>er
  
    Use modification and distribution are subject to the Boost Software 
    License, Version 1.0. (See accompanying file LICENSE_1_0.txt or copy at
    http://www.boost.org/LICENSE_1_0.txt).
==============================================================================*/

#if !defined(BOOST_FUSION_FUNCTIONAL_INVOCATION_LIMITS_HPP_INCLUDED)
#   define BOOST_FUSION_FUNCTIONAL_INVOCATION_LIMITS_HPP_INCLUDED

#   if !defined(BOOST_FUSION_INVOKE_MAX_ARITY)
#       define BOOST_FUSION_INVOKE_MAX_ARITY 6
#   endif
#   if !defined(BOOST_FUSION_INVOKE_PROCEDURE_MAX_ARITY)
#       define BOOST_FUSION_INVOKE_PROCEDURE_MAX_ARITY 6
#   endif
#   if !defined(BOOST_FUSION_INVOKE_FUNCTION_OBJECT_MAX_ARITY)
#       define BOOST_FUSION_INVOKE_FUNCTION_OBJECT_MAX_ARITY 6
#   endif

#endif

