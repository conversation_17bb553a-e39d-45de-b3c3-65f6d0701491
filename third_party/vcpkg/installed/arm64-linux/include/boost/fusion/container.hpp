/*=============================================================================
    Copyright (c) 2001-2011 <PERSON>

    Distributed under the Boost Software License, Version 1.0. (See accompanying
    file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
==============================================================================*/
#if !defined(FUSION_SEQUENCE_CLASS_10022005_0614)
#define FUSION_SEQUENCE_CLASS_10022005_0614

#include <boost/fusion/support/config.hpp>
#include <boost/fusion/container/vector.hpp>
#include <boost/fusion/container/list.hpp>
#include <boost/fusion/container/map.hpp>
#include <boost/fusion/container/set.hpp>
#include <boost/fusion/container/deque.hpp>
#include <boost/fusion/container/generation.hpp>

#endif
