/*=============================================================================
    Copyright (c) 2001-2011 <PERSON>
    Copyright (c) 2005-2006 Dan <PERSON>
    Copyright (c) 2010 <PERSON>

    Distributed under the Boost Software License, Version 1.0. (See accompanying 
    file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
==============================================================================*/
#if !defined(BOOST_FUSION_ARRAY_27122005_1035)
#define BOOST_FUSION_ARRAY_27122005_1035

//For backwards compatibility
#include <boost/fusion/adapted/boost_array.hpp>

#include <boost/fusion/adapted/array/tag_of.hpp>
#include <boost/fusion/adapted/array/is_view_impl.hpp>
#include <boost/fusion/adapted/array/is_sequence_impl.hpp>
#include <boost/fusion/adapted/array/category_of_impl.hpp>
#include <boost/fusion/adapted/array/begin_impl.hpp>
#include <boost/fusion/adapted/array/end_impl.hpp>
#include <boost/fusion/adapted/array/size_impl.hpp>
#include <boost/fusion/adapted/array/at_impl.hpp>
#include <boost/fusion/adapted/array/value_at_impl.hpp>
#include <boost/fusion/adapted/array/deref_impl.hpp>
#include <boost/fusion/adapted/array/value_of_impl.hpp>

#endif
