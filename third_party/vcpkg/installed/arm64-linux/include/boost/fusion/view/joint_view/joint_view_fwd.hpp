/*=============================================================================
    Copyright (c) 2011 <PERSON>

    Distributed under the Boost Software License, Version 1.0. (See accompanying
    file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
==============================================================================*/
#if !defined(BOOST_FUSION_JOINT_VIEW_FWD_HPP_INCLUDED)
#define BOOST_FUSION_JOINT_VIEW_FWD_HPP_INCLUDED

namespace boost { namespace fusion
{
    struct joint_view_tag;

    template <typename Sequence1, typename Sequence2>
    struct joint_view;
}}

#endif
