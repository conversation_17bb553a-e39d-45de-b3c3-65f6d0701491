/*=============================================================================
    Copyright (c) 2001-2011 <PERSON>

    Distributed under the Boost Software License, Version 1.0. (See accompanying 
    file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
==============================================================================*/
#if !defined(FUSION_TRANSFORM_VIEW_FORWARD_01052006_1839)
#define FUSION_TRANSFORM_VIEW_FORWARD_01052006_1839

namespace boost { namespace fusion
{
    struct void_;
    struct transform_view_tag;
    struct transform_view2_tag;

    template <typename A, typename B, typename C = void_>
    struct transform_view;
}}

#endif


