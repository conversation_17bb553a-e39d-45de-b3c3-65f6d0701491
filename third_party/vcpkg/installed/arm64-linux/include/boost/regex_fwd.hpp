/*
 *
 * Copyright (c) 1998-2002
 * <PERSON>
 *
 * Use, modification and distribution are subject to the 
 * Boost Software License, Version 1.0. (See accompanying file 
 * LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
 *
 */

 /*
  *   LOCATION:    see http://www.boost.org/libs/regex for documentation.
  *   FILE         regex_fwd.cpp
  *   VERSION      see <boost/version.hpp>
  *   DESCRIPTION: Forward declares boost::basic_regex<> and
  *                associated typedefs.
  */

#ifndef BOOST_REGEX_FWD_HPP
#define BOOST_REGEX_FWD_HPP

#ifndef BOOST_REGEX_CONFIG_HPP
#include <boost/regex/config.hpp>
#endif

#ifdef BOOST_REGEX_CXX03
#include <boost/regex/v4/regex_fwd.hpp>
#else
#include <boost/regex/v5/regex_fwd.hpp>
#endif

#endif




