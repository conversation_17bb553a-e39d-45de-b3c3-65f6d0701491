/*
 * Copyright 2016-2018 <PERSON><PERSON>
 *
 * Distributed under the Boost Software License, Version 1.0.
 * See http://www.boost.org/LICENSE_1_0.txt
 */

#ifndef BOOST_WINAPI_ERROR_CODES_HPP_INCLUDED_
#define BOOST_WINAPI_ERROR_CODES_HPP_INCLUDED_

#include <boost/winapi/basic_types.hpp>
#include <boost/winapi/detail/header.hpp>

#ifdef BOOST_HAS_PRAGMA_ONCE
#pragma once
#endif

namespace boost {
namespace winapi {

BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_NULL_ = 0;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_RPC_ = 1;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_DISPATCH_ = 2;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_STORAGE_ = 3;
BOOST_CONSTEX<PERSON>_OR_CONST DWORD_ FACILITY_ITF_ = 4;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_WIN32_ = 7;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_WINDOWS_ = 8;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_SSPI_ = 9;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_SECURITY_ = 9;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_CONTROL_ = 10;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_CERT_ = 11;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_INTERNET_ = 12;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_MEDIASERVER_ = 13;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_MSMQ_ = 14;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_SETUPAPI_ = 15;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_SCARD_ = 16;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_COMPLUS_ = 17;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_AAF_ = 18;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_URT_ = 19;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_ACS_ = 20;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_DPLAY_ = 21;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_UMI_ = 22;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_SXS_ = 23;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_WINDOWS_CE_ = 24;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_HTTP_ = 25;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_USERMODE_COMMONLOG_ = 26;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_WER_ = 27;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_USERMODE_FILTER_MANAGER_ = 31;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_BACKGROUNDCOPY_ = 32;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_CONFIGURATION_ = 33;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_WIA_ = 33;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_STATE_MANAGEMENT_ = 34;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_METADIRECTORY_ = 35;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_WINDOWSUPDATE_ = 36;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_DIRECTORYSERVICE_ = 37;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_GRAPHICS_ = 38;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_SHELL_ = 39;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_NAP_ = 39;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_TPM_SERVICES_ = 40;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_TPM_SOFTWARE_ = 41;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_UI_ = 42;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_XAML_ = 43;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_ACTION_QUEUE_ = 44;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_PLA_ = 48;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_WINDOWS_SETUP_ = 48;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_FVE_ = 49;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_FWP_ = 50;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_WINRM_ = 51;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_NDIS_ = 52;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_USERMODE_HYPERVISOR_ = 53;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_CMI_ = 54;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_USERMODE_VIRTUALIZATION_ = 55;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_USERMODE_VOLMGR_ = 56;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_BCD_ = 57;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_USERMODE_VHD_ = 58;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_USERMODE_HNS_ = 59;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_SDIAG_ = 60;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_WEBSERVICES_ = 61;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_WINPE_ = 61;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_WPN_ = 62;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_WINDOWS_STORE_ = 63;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_INPUT_ = 64;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_EAP_ = 66;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_WINDOWS_DEFENDER_ = 80;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_OPC_ = 81;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_XPS_ = 82;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_MBN_ = 84;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_POWERSHELL_ = 84;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_RAS_ = 83;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_P2P_INT_ = 98;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_P2P_ = 99;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_DAF_ = 100;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_BLUETOOTH_ATT_ = 101;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_AUDIO_ = 102;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_STATEREPOSITORY_ = 103;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_VISUALCPP_ = 109;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_SCRIPT_ = 112;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_PARSE_ = 113;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_BLB_ = 120;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_BLB_CLI_ = 121;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_WSBAPP_ = 122;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_BLBUI_ = 128;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_USN_ = 129;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_USERMODE_VOLSNAP_ = 130;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_TIERING_ = 131;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_WSB_ONLINE_ = 133;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_ONLINE_ID_ = 134;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_DEVICE_UPDATE_AGENT_ = 135;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_DLS_ = 153;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_DELIVERY_OPTIMIZATION_ = 208;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_USERMODE_SPACES_ = 231;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_USER_MODE_SECURITY_CORE_ = 232;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_USERMODE_LICENSING_ = 234;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_SOS_ = 160;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_DEBUGGERS_ = 176;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_SPP_ = 256;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_RESTORE_ = 256;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_DMSERVER_ = 256;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_DEPLOYMENT_SERVICES_SERVER_ = 257;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_DEPLOYMENT_SERVICES_IMAGING_ = 258;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_DEPLOYMENT_SERVICES_MANAGEMENT_ = 259;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_DEPLOYMENT_SERVICES_UTIL_ = 260;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_DEPLOYMENT_SERVICES_BINLSVC_ = 261;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_DEPLOYMENT_SERVICES_PXE_ = 263;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_DEPLOYMENT_SERVICES_TFTP_ = 264;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_DEPLOYMENT_SERVICES_TRANSPORT_MANAGEMENT_ = 272;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_DEPLOYMENT_SERVICES_DRIVER_PROVISIONING_ = 278;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_DEPLOYMENT_SERVICES_MULTICAST_SERVER_ = 289;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_DEPLOYMENT_SERVICES_MULTICAST_CLIENT_ = 290;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_DEPLOYMENT_SERVICES_CONTENT_PROVIDER_ = 293;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_LINGUISTIC_SERVICES_ = 305;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_AUDIOSTREAMING_ = 1094;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_ACCELERATOR_ = 1536;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_WMAAECMA_ = 1996;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_DIRECTMUSIC_ = 2168;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_DIRECT3D10_ = 2169;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_DXGI_ = 2170;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_DXGI_DDI_ = 2171;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_DIRECT3D11_ = 2172;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_DIRECT3D11_DEBUG_ = 2173;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_DIRECT3D12_ = 2174;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_DIRECT3D12_DEBUG_ = 2175;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_LEAP_ = 2184;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_AUDCLNT_ = 2185;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_WINCODEC_DWRITE_DWM_ = 2200;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_DIRECT2D_ = 2201;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_DEFRAG_ = 2304;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_USERMODE_SDBUS_ = 2305;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_JSCRIPT_ = 2306;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_PIDGENX_ = 2561;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_EAS_ = 85;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_WEB_ = 885;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_WEB_SOCKET_ = 886;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_MOBILE_ = 1793;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_SQLITE_ = 1967;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_UTC_ = 1989;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_WEP_ = 2049;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_SYNCENGINE_ = 2050;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_XBOX_ = 2339;
BOOST_CONSTEXPR_OR_CONST DWORD_ FACILITY_PIX_ = 2748;

BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SUCCESS_ = 0;
BOOST_CONSTEXPR_OR_CONST DWORD_ NO_ERROR_ = 0;

BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_FUNCTION_ = 1;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_FILE_NOT_FOUND_ = 2;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PATH_NOT_FOUND_ = 3;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_TOO_MANY_OPEN_FILES_ = 4;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_ACCESS_DENIED_ = 5;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_HANDLE_ = 6;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_ARENA_TRASHED_ = 7;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NOT_ENOUGH_MEMORY_ = 8;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_BLOCK_ = 9;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_BAD_ENVIRONMENT_ = 10;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_BAD_FORMAT_ = 11;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_ACCESS_ = 12;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_DATA_ = 13;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_OUTOFMEMORY_ = 14;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_DRIVE_ = 15;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CURRENT_DIRECTORY_ = 16;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NOT_SAME_DEVICE_ = 17;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NO_MORE_FILES_ = 18;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_WRITE_PROTECT_ = 19;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_BAD_UNIT_ = 20;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NOT_READY_ = 21;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_BAD_COMMAND_ = 22;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CRC_ = 23;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_BAD_LENGTH_ = 24;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SEEK_ = 25;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NOT_DOS_DISK_ = 26;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SECTOR_NOT_FOUND_ = 27;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_OUT_OF_PAPER_ = 28;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_WRITE_FAULT_ = 29;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_READ_FAULT_ = 30;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_GEN_FAILURE_ = 31;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SHARING_VIOLATION_ = 32;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LOCK_VIOLATION_ = 33;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_WRONG_DISK_ = 34;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SHARING_BUFFER_EXCEEDED_ = 36;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_HANDLE_EOF_ = 38;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_HANDLE_DISK_FULL_ = 39;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NOT_SUPPORTED_ = 50;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_REM_NOT_LIST_ = 51;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DUP_NAME_ = 52;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_BAD_NETPATH_ = 53;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NETWORK_BUSY_ = 54;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DEV_NOT_EXIST_ = 55;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_TOO_MANY_CMDS_ = 56;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_ADAP_HDW_ERR_ = 57;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_BAD_NET_RESP_ = 58;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_UNEXP_NET_ERR_ = 59;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_BAD_REM_ADAP_ = 60;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PRINTQ_FULL_ = 61;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NO_SPOOL_SPACE_ = 62;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PRINT_CANCELLED_ = 63;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NETNAME_DELETED_ = 64;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NETWORK_ACCESS_DENIED_ = 65;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_BAD_DEV_TYPE_ = 66;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_BAD_NET_NAME_ = 67;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_TOO_MANY_NAMES_ = 68;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_TOO_MANY_SESS_ = 69;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SHARING_PAUSED_ = 70;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_REQ_NOT_ACCEP_ = 71;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_REDIR_PAUSED_ = 72;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_FILE_EXISTS_ = 80;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CANNOT_MAKE_ = 82;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_FAIL_I24_ = 83;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_OUT_OF_STRUCTURES_ = 84;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_ALREADY_ASSIGNED_ = 85;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_PASSWORD_ = 86;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_PARAMETER_ = 87;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NET_WRITE_FAULT_ = 88;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NO_PROC_SLOTS_ = 89;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_TOO_MANY_SEMAPHORES_ = 100;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EXCL_SEM_ALREADY_OWNED_ = 101;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SEM_IS_SET_ = 102;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_TOO_MANY_SEM_REQUESTS_ = 103;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_AT_INTERRUPT_TIME_ = 104;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SEM_OWNER_DIED_ = 105;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SEM_USER_LIMIT_ = 106;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DISK_CHANGE_ = 107;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DRIVE_LOCKED_ = 108;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_BROKEN_PIPE_ = 109;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_OPEN_FAILED_ = 110;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_BUFFER_OVERFLOW_ = 111;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DISK_FULL_ = 112;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NO_MORE_SEARCH_HANDLES_ = 113;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_TARGET_HANDLE_ = 114;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_CATEGORY_ = 117;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_VERIFY_SWITCH_ = 118;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_BAD_DRIVER_LEVEL_ = 119;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CALL_NOT_IMPLEMENTED_ = 120;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SEM_TIMEOUT_ = 121;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INSUFFICIENT_BUFFER_ = 122;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_NAME_ = 123;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_LEVEL_ = 124;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NO_VOLUME_LABEL_ = 125;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MOD_NOT_FOUND_ = 126;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PROC_NOT_FOUND_ = 127;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_WAIT_NO_CHILDREN_ = 128;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CHILD_NOT_COMPLETE_ = 129;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DIRECT_ACCESS_HANDLE_ = 130;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NEGATIVE_SEEK_ = 131;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SEEK_ON_DEVICE_ = 132;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IS_JOIN_TARGET_ = 133;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IS_JOINED_ = 134;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IS_SUBSTED_ = 135;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NOT_JOINED_ = 136;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NOT_SUBSTED_ = 137;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_JOIN_TO_JOIN_ = 138;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SUBST_TO_SUBST_ = 139;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_JOIN_TO_SUBST_ = 140;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SUBST_TO_JOIN_ = 141;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_BUSY_DRIVE_ = 142;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SAME_DRIVE_ = 143;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DIR_NOT_ROOT_ = 144;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DIR_NOT_EMPTY_ = 145;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IS_SUBST_PATH_ = 146;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IS_JOIN_PATH_ = 147;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PATH_BUSY_ = 148;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IS_SUBST_TARGET_ = 149;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SYSTEM_TRACE_ = 150;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_EVENT_COUNT_ = 151;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_TOO_MANY_MUXWAITERS_ = 152;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_LIST_FORMAT_ = 153;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LABEL_TOO_LONG_ = 154;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_TOO_MANY_TCBS_ = 155;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SIGNAL_REFUSED_ = 156;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DISCARDED_ = 157;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NOT_LOCKED_ = 158;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_BAD_THREADID_ADDR_ = 159;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_BAD_ARGUMENTS_ = 160;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_BAD_PATHNAME_ = 161;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SIGNAL_PENDING_ = 162;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MAX_THRDS_REACHED_ = 164;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LOCK_FAILED_ = 167;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_BUSY_ = 170;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DEVICE_SUPPORT_IN_PROGRESS_ = 171;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CANCEL_VIOLATION_ = 173;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_ATOMIC_LOCKS_NOT_SUPPORTED_ = 174;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_SEGMENT_NUMBER_ = 180;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_ORDINAL_ = 182;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_ALREADY_EXISTS_ = 183;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_FLAG_NUMBER_ = 186;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SEM_NOT_FOUND_ = 187;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_STARTING_CODESEG_ = 188;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_STACKSEG_ = 189;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_MODULETYPE_ = 190;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_EXE_SIGNATURE_ = 191;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EXE_MARKED_INVALID_ = 192;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_BAD_EXE_FORMAT_ = 193;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_ITERATED_DATA_EXCEEDS_64k_ = 194;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_MINALLOCSIZE_ = 195;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DYNLINK_FROM_INVALID_RING_ = 196;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IOPL_NOT_ENABLED_ = 197;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_SEGDPL_ = 198;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_AUTODATASEG_EXCEEDS_64k_ = 199;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_RING2SEG_MUST_BE_MOVABLE_ = 200;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_RELOC_CHAIN_XEEDS_SEGLIM_ = 201;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INFLOOP_IN_RELOC_CHAIN_ = 202;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_ENVVAR_NOT_FOUND_ = 203;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NO_SIGNAL_SENT_ = 205;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_FILENAME_EXCED_RANGE_ = 206;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_RING2_STACK_IN_USE_ = 207;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_META_EXPANSION_TOO_LONG_ = 208;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_SIGNAL_NUMBER_ = 209;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_THREAD_1_INACTIVE_ = 210;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LOCKED_ = 212;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_TOO_MANY_MODULES_ = 214;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NESTING_NOT_ALLOWED_ = 215;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EXE_MACHINE_TYPE_MISMATCH_ = 216;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EXE_CANNOT_MODIFY_SIGNED_BINARY_ = 217;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EXE_CANNOT_MODIFY_STRONG_SIGNED_BINARY_ = 218;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_FILE_CHECKED_OUT_ = 220;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CHECKOUT_REQUIRED_ = 221;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_BAD_FILE_TYPE_ = 222;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_FILE_TOO_LARGE_ = 223;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_FORMS_AUTH_REQUIRED_ = 224;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_VIRUS_INFECTED_ = 225;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_VIRUS_DELETED_ = 226;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PIPE_LOCAL_ = 229;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_BAD_PIPE_ = 230;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PIPE_BUSY_ = 231;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NO_DATA_ = 232;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PIPE_NOT_CONNECTED_ = 233;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MORE_DATA_ = 234;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NO_WORK_DONE_ = 235;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_VC_DISCONNECTED_ = 240;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_EA_NAME_ = 254;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EA_LIST_INCONSISTENT_ = 255;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NO_MORE_ITEMS_ = 259;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CANNOT_COPY_ = 266;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DIRECTORY_ = 267;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EAS_DIDNT_FIT_ = 275;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EA_FILE_CORRUPT_ = 276;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EA_TABLE_FULL_ = 277;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_EA_HANDLE_ = 278;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EAS_NOT_SUPPORTED_ = 282;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NOT_OWNER_ = 288;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_TOO_MANY_POSTS_ = 298;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PARTIAL_COPY_ = 299;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_OPLOCK_NOT_GRANTED_ = 300;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_OPLOCK_PROTOCOL_ = 301;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DISK_TOO_FRAGMENTED_ = 302;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DELETE_PENDING_ = 303;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INCOMPATIBLE_WITH_GLOBAL_SHORT_NAME_REGISTRY_SETTING_ = 304;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SHORT_NAMES_NOT_ENABLED_ON_VOLUME_ = 305;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SECURITY_STREAM_IS_INCONSISTENT_ = 306;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_LOCK_RANGE_ = 307;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IMAGE_SUBSYSTEM_NOT_PRESENT_ = 308;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NOTIFICATION_GUID_ALREADY_DEFINED_ = 309;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_EXCEPTION_HANDLER_ = 310;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DUPLICATE_PRIVILEGES_ = 311;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NO_RANGES_PROCESSED_ = 312;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NOT_ALLOWED_ON_SYSTEM_FILE_ = 313;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DISK_RESOURCES_EXHAUSTED_ = 314;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_TOKEN_ = 315;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DEVICE_FEATURE_NOT_SUPPORTED_ = 316;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MR_MID_NOT_FOUND_ = 317;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SCOPE_NOT_FOUND_ = 318;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_UNDEFINED_SCOPE_ = 319;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_CAP_ = 320;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DEVICE_UNREACHABLE_ = 321;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DEVICE_NO_RESOURCES_ = 322;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DATA_CHECKSUM_ERROR_ = 323;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INTERMIXED_KERNEL_EA_OPERATION_ = 324;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_FILE_LEVEL_TRIM_NOT_SUPPORTED_ = 326;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_OFFSET_ALIGNMENT_VIOLATION_ = 327;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_FIELD_IN_PARAMETER_LIST_ = 328;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_OPERATION_IN_PROGRESS_ = 329;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_BAD_DEVICE_PATH_ = 330;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_TOO_MANY_DESCRIPTORS_ = 331;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SCRUB_DATA_DISABLED_ = 332;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NOT_REDUNDANT_STORAGE_ = 333;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_RESIDENT_FILE_NOT_SUPPORTED_ = 334;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_COMPRESSED_FILE_NOT_SUPPORTED_ = 335;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DIRECTORY_NOT_SUPPORTED_ = 336;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NOT_READ_FROM_COPY_ = 337;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_FT_WRITE_FAILURE_ = 338;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_FT_DI_SCAN_REQUIRED_ = 339;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_KERNEL_INFO_VERSION_ = 340;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_PEP_INFO_VERSION_ = 341;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_OBJECT_NOT_EXTERNALLY_BACKED_ = 342;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EXTERNAL_BACKING_PROVIDER_UNKNOWN_ = 343;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_COMPRESSION_NOT_BENEFICIAL_ = 344;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_STORAGE_TOPOLOGY_ID_MISMATCH_ = 345;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_BLOCKED_BY_PARENTAL_CONTROLS_ = 346;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_BLOCK_TOO_MANY_REFERENCES_ = 347;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MARKED_TO_DISALLOW_WRITES_ = 348;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_ENCLAVE_FAILURE_ = 349;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_FAIL_NOACTION_REBOOT_ = 350;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_FAIL_SHUTDOWN_ = 351;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_FAIL_RESTART_ = 352;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MAX_SESSIONS_REACHED_ = 353;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NETWORK_ACCESS_DENIED_EDP_ = 354;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DEVICE_HINT_NAME_BUFFER_TOO_SMALL_ = 355;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EDP_POLICY_DENIES_OPERATION_ = 356;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EDP_DPL_POLICY_CANT_BE_SATISFIED_ = 357;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLOUD_FILE_SYNC_ROOT_METADATA_CORRUPT_ = 358;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DEVICE_IN_MAINTENANCE_ = 359;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NOT_SUPPORTED_ON_DAX_ = 360;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DAX_MAPPING_EXISTS_ = 361;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLOUD_FILE_PROVIDER_NOT_RUNNING_ = 362;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLOUD_FILE_METADATA_CORRUPT_ = 363;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLOUD_FILE_METADATA_TOO_LARGE_ = 364;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLOUD_FILE_PROPERTY_BLOB_TOO_LARGE_ = 365;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLOUD_FILE_PROPERTY_BLOB_CHECKSUM_MISMATCH_ = 366;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CHILD_PROCESS_BLOCKED_ = 367;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_STORAGE_LOST_DATA_PERSISTENCE_ = 368;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_FILE_SYSTEM_VIRTUALIZATION_UNAVAILABLE_ = 369;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_FILE_SYSTEM_VIRTUALIZATION_METADATA_CORRUPT_ = 370;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_FILE_SYSTEM_VIRTUALIZATION_BUSY_ = 371;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_FILE_SYSTEM_VIRTUALIZATION_PROVIDER_UNKNOWN_ = 372;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_GDI_HANDLE_LEAK_ = 373;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLOUD_FILE_TOO_MANY_PROPERTY_BLOBS_ = 374;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLOUD_FILE_PROPERTY_VERSION_NOT_SUPPORTED_ = 375;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NOT_A_CLOUD_FILE_ = 376;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLOUD_FILE_NOT_IN_SYNC_ = 377;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLOUD_FILE_ALREADY_CONNECTED_ = 378;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLOUD_FILE_NOT_SUPPORTED_ = 379;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLOUD_FILE_INVALID_REQUEST_ = 380;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLOUD_FILE_READ_ONLY_VOLUME_ = 381;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLOUD_FILE_CONNECTED_PROVIDER_ONLY_ = 382;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLOUD_FILE_VALIDATION_FAILED_ = 383;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SMB1_NOT_AVAILABLE_ = 384;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_FILE_SYSTEM_VIRTUALIZATION_INVALID_OPERATION_ = 385;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLOUD_FILE_AUTHENTICATION_FAILED_ = 386;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLOUD_FILE_INSUFFICIENT_RESOURCES_ = 387;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLOUD_FILE_NETWORK_UNAVAILABLE_ = 388;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLOUD_FILE_UNSUCCESSFUL_ = 389;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLOUD_FILE_NOT_UNDER_SYNC_ROOT_ = 390;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLOUD_FILE_IN_USE_ = 391;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLOUD_FILE_PINNED_ = 392;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLOUD_FILE_REQUEST_ABORTED_ = 393;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLOUD_FILE_PROPERTY_CORRUPT_ = 394;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLOUD_FILE_ACCESS_DENIED_ = 395;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLOUD_FILE_INCOMPATIBLE_HARDLINKS_ = 396;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLOUD_FILE_PROPERTY_LOCK_CONFLICT_ = 397;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLOUD_FILE_REQUEST_CANCELED_ = 398;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EXTERNAL_SYSKEY_NOT_SUPPORTED_ = 399;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_THREAD_MODE_ALREADY_BACKGROUND_ = 400;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_THREAD_MODE_NOT_BACKGROUND_ = 401;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PROCESS_MODE_ALREADY_BACKGROUND_ = 402;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PROCESS_MODE_NOT_BACKGROUND_ = 403;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CAPAUTHZ_NOT_DEVUNLOCKED_ = 450;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CAPAUTHZ_CHANGE_TYPE_ = 451;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CAPAUTHZ_NOT_PROVISIONED_ = 452;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CAPAUTHZ_NOT_AUTHORIZED_ = 453;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CAPAUTHZ_NO_POLICY_ = 454;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CAPAUTHZ_DB_CORRUPTED_ = 455;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CAPAUTHZ_SCCD_INVALID_CATALOG_ = 456;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CAPAUTHZ_SCCD_NO_AUTH_ENTITY_ = 457;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CAPAUTHZ_SCCD_PARSE_ERROR_ = 458;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CAPAUTHZ_SCCD_DEV_MODE_REQUIRED_ = 459;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CAPAUTHZ_SCCD_NO_CAPABILITY_MATCH_ = 460;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PNP_QUERY_REMOVE_DEVICE_TIMEOUT_ = 480;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PNP_QUERY_REMOVE_RELATED_DEVICE_TIMEOUT_ = 481;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PNP_QUERY_REMOVE_UNRELATED_DEVICE_TIMEOUT_ = 482;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DEVICE_HARDWARE_ERROR_ = 483;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_ADDRESS_ = 487;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_VRF_CFG_ENABLED_ = 1183;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PARTITION_TERMINATING_ = 1184;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_USER_PROFILE_LOAD_ = 500;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_ARITHMETIC_OVERFLOW_ = 534;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PIPE_CONNECTED_ = 535;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PIPE_LISTENING_ = 536;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_VERIFIER_STOP_ = 537;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_ABIOS_ERROR_ = 538;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_WX86_WARNING_ = 539;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_WX86_ERROR_ = 540;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_TIMER_NOT_CANCELED_ = 541;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_UNWIND_ = 542;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_BAD_STACK_ = 543;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_UNWIND_TARGET_ = 544;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_PORT_ATTRIBUTES_ = 545;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PORT_MESSAGE_TOO_LONG_ = 546;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_QUOTA_LOWER_ = 547;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DEVICE_ALREADY_ATTACHED_ = 548;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INSTRUCTION_MISALIGNMENT_ = 549;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PROFILING_NOT_STARTED_ = 550;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PROFILING_NOT_STOPPED_ = 551;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_COULD_NOT_INTERPRET_ = 552;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PROFILING_AT_LIMIT_ = 553;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CANT_WAIT_ = 554;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CANT_TERMINATE_SELF_ = 555;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_UNEXPECTED_MM_CREATE_ERR_ = 556;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_UNEXPECTED_MM_MAP_ERROR_ = 557;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_UNEXPECTED_MM_EXTEND_ERR_ = 558;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_BAD_FUNCTION_TABLE_ = 559;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NO_GUID_TRANSLATION_ = 560;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_LDT_SIZE_ = 561;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_LDT_OFFSET_ = 563;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_LDT_DESCRIPTOR_ = 564;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_TOO_MANY_THREADS_ = 565;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_THREAD_NOT_IN_PROCESS_ = 566;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PAGEFILE_QUOTA_EXCEEDED_ = 567;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LOGON_SERVER_CONFLICT_ = 568;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SYNCHRONIZATION_REQUIRED_ = 569;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NET_OPEN_FAILED_ = 570;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IO_PRIVILEGE_FAILED_ = 571;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CONTROL_C_EXIT_ = 572;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MISSING_SYSTEMFILE_ = 573;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_UNHANDLED_EXCEPTION_ = 574;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_APP_INIT_FAILURE_ = 575;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PAGEFILE_CREATE_FAILED_ = 576;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_IMAGE_HASH_ = 577;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NO_PAGEFILE_ = 578;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_ILLEGAL_FLOAT_CONTEXT_ = 579;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NO_EVENT_PAIR_ = 580;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DOMAIN_CTRLR_CONFIG_ERROR_ = 581;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_ILLEGAL_CHARACTER_ = 582;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_UNDEFINED_CHARACTER_ = 583;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_FLOPPY_VOLUME_ = 584;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_BIOS_FAILED_TO_CONNECT_INTERRUPT_ = 585;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_BACKUP_CONTROLLER_ = 586;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MUTANT_LIMIT_EXCEEDED_ = 587;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_FS_DRIVER_REQUIRED_ = 588;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CANNOT_LOAD_REGISTRY_FILE_ = 589;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DEBUG_ATTACH_FAILED_ = 590;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SYSTEM_PROCESS_TERMINATED_ = 591;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DATA_NOT_ACCEPTED_ = 592;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_VDM_HARD_ERROR_ = 593;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DRIVER_CANCEL_TIMEOUT_ = 594;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_REPLY_MESSAGE_MISMATCH_ = 595;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LOST_WRITEBEHIND_DATA_ = 596;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLIENT_SERVER_PARAMETERS_INVALID_ = 597;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NOT_TINY_STREAM_ = 598;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_STACK_OVERFLOW_READ_ = 599;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CONVERT_TO_LARGE_ = 600;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_FOUND_OUT_OF_SCOPE_ = 601;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_ALLOCATE_BUCKET_ = 602;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MARSHALL_OVERFLOW_ = 603;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_VARIANT_ = 604;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_BAD_COMPRESSION_BUFFER_ = 605;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_AUDIT_FAILED_ = 606;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_TIMER_RESOLUTION_NOT_SET_ = 607;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INSUFFICIENT_LOGON_INFO_ = 608;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_BAD_DLL_ENTRYPOINT_ = 609;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_BAD_SERVICE_ENTRYPOINT_ = 610;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IP_ADDRESS_CONFLICT1_ = 611;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IP_ADDRESS_CONFLICT2_ = 612;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_REGISTRY_QUOTA_LIMIT_ = 613;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NO_CALLBACK_ACTIVE_ = 614;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PWD_TOO_SHORT_ = 615;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PWD_TOO_RECENT_ = 616;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PWD_HISTORY_CONFLICT_ = 617;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_UNSUPPORTED_COMPRESSION_ = 618;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_HW_PROFILE_ = 619;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_PLUGPLAY_DEVICE_PATH_ = 620;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_QUOTA_LIST_INCONSISTENT_ = 621;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EVALUATION_EXPIRATION_ = 622;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_ILLEGAL_DLL_RELOCATION_ = 623;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DLL_INIT_FAILED_LOGOFF_ = 624;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_VALIDATE_CONTINUE_ = 625;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NO_MORE_MATCHES_ = 626;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_RANGE_LIST_CONFLICT_ = 627;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SERVER_SID_MISMATCH_ = 628;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CANT_ENABLE_DENY_ONLY_ = 629;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_FLOAT_MULTIPLE_FAULTS_ = 630;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_FLOAT_MULTIPLE_TRAPS_ = 631;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NOINTERFACE_ = 632;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DRIVER_FAILED_SLEEP_ = 633;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CORRUPT_SYSTEM_FILE_ = 634;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_COMMITMENT_MINIMUM_ = 635;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PNP_RESTART_ENUMERATION_ = 636;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SYSTEM_IMAGE_BAD_SIGNATURE_ = 637;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PNP_REBOOT_REQUIRED_ = 638;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INSUFFICIENT_POWER_ = 639;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MULTIPLE_FAULT_VIOLATION_ = 640;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SYSTEM_SHUTDOWN_ = 641;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PORT_NOT_SET_ = 642;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_VERSION_CHECK_FAILURE_ = 643;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_RANGE_NOT_FOUND_ = 644;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NOT_SAFE_MODE_DRIVER_ = 646;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_FAILED_DRIVER_ENTRY_ = 647;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DEVICE_ENUMERATION_ERROR_ = 648;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MOUNT_POINT_NOT_RESOLVED_ = 649;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_DEVICE_OBJECT_PARAMETER_ = 650;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MCA_OCCURED_ = 651;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DRIVER_DATABASE_ERROR_ = 652;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SYSTEM_HIVE_TOO_LARGE_ = 653;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DRIVER_FAILED_PRIOR_UNLOAD_ = 654;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_VOLSNAP_PREPARE_HIBERNATE_ = 655;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_HIBERNATION_FAILURE_ = 656;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PWD_TOO_LONG_ = 657;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_FILE_SYSTEM_LIMITATION_ = 665;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_ASSERTION_FAILURE_ = 668;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_ACPI_ERROR_ = 669;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_WOW_ASSERTION_ = 670;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PNP_BAD_MPS_TABLE_ = 671;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PNP_TRANSLATION_FAILED_ = 672;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PNP_IRQ_TRANSLATION_FAILED_ = 673;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PNP_INVALID_ID_ = 674;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_WAKE_SYSTEM_DEBUGGER_ = 675;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_HANDLES_CLOSED_ = 676;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EXTRANEOUS_INFORMATION_ = 677;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_RXACT_COMMIT_NECESSARY_ = 678;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MEDIA_CHECK_ = 679;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_GUID_SUBSTITUTION_MADE_ = 680;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_STOPPED_ON_SYMLINK_ = 681;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LONGJUMP_ = 682;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PLUGPLAY_QUERY_VETOED_ = 683;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_UNWIND_CONSOLIDATE_ = 684;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_REGISTRY_HIVE_RECOVERED_ = 685;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DLL_MIGHT_BE_INSECURE_ = 686;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DLL_MIGHT_BE_INCOMPATIBLE_ = 687;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DBG_EXCEPTION_NOT_HANDLED_ = 688;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DBG_REPLY_LATER_ = 689;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DBG_UNABLE_TO_PROVIDE_HANDLE_ = 690;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DBG_TERMINATE_THREAD_ = 691;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DBG_TERMINATE_PROCESS_ = 692;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DBG_CONTROL_C_ = 693;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DBG_PRINTEXCEPTION_C_ = 694;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DBG_RIPEXCEPTION_ = 695;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DBG_CONTROL_BREAK_ = 696;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DBG_COMMAND_EXCEPTION_ = 697;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_OBJECT_NAME_EXISTS_ = 698;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_THREAD_WAS_SUSPENDED_ = 699;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IMAGE_NOT_AT_BASE_ = 700;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_RXACT_STATE_CREATED_ = 701;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SEGMENT_NOTIFICATION_ = 702;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_BAD_CURRENT_DIRECTORY_ = 703;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_FT_READ_RECOVERY_FROM_BACKUP_ = 704;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_FT_WRITE_RECOVERY_ = 705;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IMAGE_MACHINE_TYPE_MISMATCH_ = 706;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_RECEIVE_PARTIAL_ = 707;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_RECEIVE_EXPEDITED_ = 708;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_RECEIVE_PARTIAL_EXPEDITED_ = 709;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EVENT_DONE_ = 710;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EVENT_PENDING_ = 711;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CHECKING_FILE_SYSTEM_ = 712;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_FATAL_APP_EXIT_ = 713;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PREDEFINED_HANDLE_ = 714;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_WAS_UNLOCKED_ = 715;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SERVICE_NOTIFICATION_ = 716;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_WAS_LOCKED_ = 717;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LOG_HARD_ERROR_ = 718;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_ALREADY_WIN32_ = 719;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IMAGE_MACHINE_TYPE_MISMATCH_EXE_ = 720;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NO_YIELD_PERFORMED_ = 721;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_TIMER_RESUME_IGNORED_ = 722;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_ARBITRATION_UNHANDLED_ = 723;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CARDBUS_NOT_SUPPORTED_ = 724;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MP_PROCESSOR_MISMATCH_ = 725;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_HIBERNATED_ = 726;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_RESUME_HIBERNATION_ = 727;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_FIRMWARE_UPDATED_ = 728;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DRIVERS_LEAKING_LOCKED_PAGES_ = 729;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_WAKE_SYSTEM_ = 730;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_WAIT_1_ = 731;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_WAIT_2_ = 732;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_WAIT_3_ = 733;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_WAIT_63_ = 734;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_ABANDONED_WAIT_0_ = 735;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_ABANDONED_WAIT_63_ = 736;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_USER_APC_ = 737;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_KERNEL_APC_ = 738;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_ALERTED_ = 739;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_ELEVATION_REQUIRED_ = 740;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_REPARSE_ = 741;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_OPLOCK_BREAK_IN_PROGRESS_ = 742;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_VOLUME_MOUNTED_ = 743;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_RXACT_COMMITTED_ = 744;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NOTIFY_CLEANUP_ = 745;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PRIMARY_TRANSPORT_CONNECT_FAILED_ = 746;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PAGE_FAULT_TRANSITION_ = 747;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PAGE_FAULT_DEMAND_ZERO_ = 748;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PAGE_FAULT_COPY_ON_WRITE_ = 749;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PAGE_FAULT_GUARD_PAGE_ = 750;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PAGE_FAULT_PAGING_FILE_ = 751;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CACHE_PAGE_LOCKED_ = 752;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CRASH_DUMP_ = 753;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_BUFFER_ALL_ZEROS_ = 754;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_REPARSE_OBJECT_ = 755;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_RESOURCE_REQUIREMENTS_CHANGED_ = 756;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_TRANSLATION_COMPLETE_ = 757;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NOTHING_TO_TERMINATE_ = 758;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PROCESS_NOT_IN_JOB_ = 759;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PROCESS_IN_JOB_ = 760;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_VOLSNAP_HIBERNATE_READY_ = 761;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_FSFILTER_OP_COMPLETED_SUCCESSFULLY_ = 762;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INTERRUPT_VECTOR_ALREADY_CONNECTED_ = 763;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INTERRUPT_STILL_CONNECTED_ = 764;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_WAIT_FOR_OPLOCK_ = 765;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DBG_EXCEPTION_HANDLED_ = 766;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DBG_CONTINUE_ = 767;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CALLBACK_POP_STACK_ = 768;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_COMPRESSION_DISABLED_ = 769;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CANTFETCHBACKWARDS_ = 770;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CANTSCROLLBACKWARDS_ = 771;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_ROWSNOTRELEASED_ = 772;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_BAD_ACCESSOR_FLAGS_ = 773;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_ERRORS_ENCOUNTERED_ = 774;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NOT_CAPABLE_ = 775;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_REQUEST_OUT_OF_SEQUENCE_ = 776;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_VERSION_PARSE_ERROR_ = 777;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_BADSTARTPOSITION_ = 778;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MEMORY_HARDWARE_ = 779;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DISK_REPAIR_DISABLED_ = 780;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INSUFFICIENT_RESOURCE_FOR_SPECIFIED_SHARED_SECTION_SIZE_ = 781;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SYSTEM_POWERSTATE_TRANSITION_ = 782;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SYSTEM_POWERSTATE_COMPLEX_TRANSITION_ = 783;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MCA_EXCEPTION_ = 784;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_ACCESS_AUDIT_BY_POLICY_ = 785;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_ACCESS_DISABLED_NO_SAFER_UI_BY_POLICY_ = 786;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_ABANDON_HIBERFILE_ = 787;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LOST_WRITEBEHIND_DATA_NETWORK_DISCONNECTED_ = 788;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LOST_WRITEBEHIND_DATA_NETWORK_SERVER_ERROR_ = 789;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LOST_WRITEBEHIND_DATA_LOCAL_DISK_ERROR_ = 790;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_BAD_MCFG_TABLE_ = 791;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DISK_REPAIR_REDIRECTED_ = 792;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DISK_REPAIR_UNSUCCESSFUL_ = 793;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CORRUPT_LOG_OVERFULL_ = 794;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CORRUPT_LOG_CORRUPTED_ = 795;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CORRUPT_LOG_UNAVAILABLE_ = 796;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CORRUPT_LOG_DELETED_FULL_ = 797;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CORRUPT_LOG_CLEARED_ = 798;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_ORPHAN_NAME_EXHAUSTED_ = 799;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_OPLOCK_SWITCHED_TO_NEW_HANDLE_ = 800;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CANNOT_GRANT_REQUESTED_OPLOCK_ = 801;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CANNOT_BREAK_OPLOCK_ = 802;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_OPLOCK_HANDLE_CLOSED_ = 803;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NO_ACE_CONDITION_ = 804;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_ACE_CONDITION_ = 805;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_FILE_HANDLE_REVOKED_ = 806;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IMAGE_AT_DIFFERENT_BASE_ = 807;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_ENCRYPTED_IO_NOT_POSSIBLE_ = 808;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_FILE_METADATA_OPTIMIZATION_IN_PROGRESS_ = 809;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_QUOTA_ACTIVITY_ = 810;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_HANDLE_REVOKED_ = 811;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CALLBACK_INVOKE_INLINE_ = 812;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CPU_SET_INVALID_ = 813;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_ENCLAVE_NOT_TERMINATED_ = 814;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EA_ACCESS_DENIED_ = 994;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_OPERATION_ABORTED_ = 995;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IO_INCOMPLETE_ = 996;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IO_PENDING_ = 997;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NOACCESS_ = 998;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SWAPERROR_ = 999;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_STACK_OVERFLOW_ = 1001;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_MESSAGE_ = 1002;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CAN_NOT_COMPLETE_ = 1003;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_FLAGS_ = 1004;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_UNRECOGNIZED_VOLUME_ = 1005;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_FILE_INVALID_ = 1006;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_FULLSCREEN_MODE_ = 1007;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NO_TOKEN_ = 1008;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_BADDB_ = 1009;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_BADKEY_ = 1010;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CANTOPEN_ = 1011;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CANTREAD_ = 1012;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CANTWRITE_ = 1013;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_REGISTRY_RECOVERED_ = 1014;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_REGISTRY_CORRUPT_ = 1015;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_REGISTRY_IO_FAILED_ = 1016;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NOT_REGISTRY_FILE_ = 1017;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_KEY_DELETED_ = 1018;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NO_LOG_SPACE_ = 1019;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_KEY_HAS_CHILDREN_ = 1020;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CHILD_MUST_BE_VOLATILE_ = 1021;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NOTIFY_ENUM_DIR_ = 1022;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DEPENDENT_SERVICES_RUNNING_ = 1051;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_SERVICE_CONTROL_ = 1052;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SERVICE_REQUEST_TIMEOUT_ = 1053;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SERVICE_NO_THREAD_ = 1054;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SERVICE_DATABASE_LOCKED_ = 1055;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SERVICE_ALREADY_RUNNING_ = 1056;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_SERVICE_ACCOUNT_ = 1057;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SERVICE_DISABLED_ = 1058;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CIRCULAR_DEPENDENCY_ = 1059;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SERVICE_DOES_NOT_EXIST_ = 1060;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SERVICE_CANNOT_ACCEPT_CTRL_ = 1061;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SERVICE_NOT_ACTIVE_ = 1062;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_FAILED_SERVICE_CONTROLLER_CONNECT_ = 1063;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EXCEPTION_IN_SERVICE_ = 1064;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DATABASE_DOES_NOT_EXIST_ = 1065;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SERVICE_SPECIFIC_ERROR_ = 1066;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PROCESS_ABORTED_ = 1067;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SERVICE_DEPENDENCY_FAIL_ = 1068;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SERVICE_LOGON_FAILED_ = 1069;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SERVICE_START_HANG_ = 1070;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_SERVICE_LOCK_ = 1071;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SERVICE_MARKED_FOR_DELETE_ = 1072;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SERVICE_EXISTS_ = 1073;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_ALREADY_RUNNING_LKG_ = 1074;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SERVICE_DEPENDENCY_DELETED_ = 1075;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_BOOT_ALREADY_ACCEPTED_ = 1076;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SERVICE_NEVER_STARTED_ = 1077;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DUPLICATE_SERVICE_NAME_ = 1078;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DIFFERENT_SERVICE_ACCOUNT_ = 1079;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CANNOT_DETECT_DRIVER_FAILURE_ = 1080;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CANNOT_DETECT_PROCESS_ABORT_ = 1081;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NO_RECOVERY_PROGRAM_ = 1082;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SERVICE_NOT_IN_EXE_ = 1083;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NOT_SAFEBOOT_SERVICE_ = 1084;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_END_OF_MEDIA_ = 1100;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_FILEMARK_DETECTED_ = 1101;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_BEGINNING_OF_MEDIA_ = 1102;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SETMARK_DETECTED_ = 1103;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NO_DATA_DETECTED_ = 1104;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PARTITION_FAILURE_ = 1105;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_BLOCK_LENGTH_ = 1106;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DEVICE_NOT_PARTITIONED_ = 1107;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_UNABLE_TO_LOCK_MEDIA_ = 1108;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_UNABLE_TO_UNLOAD_MEDIA_ = 1109;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MEDIA_CHANGED_ = 1110;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_BUS_RESET_ = 1111;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NO_MEDIA_IN_DRIVE_ = 1112;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NO_UNICODE_TRANSLATION_ = 1113;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DLL_INIT_FAILED_ = 1114;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SHUTDOWN_IN_PROGRESS_ = 1115;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NO_SHUTDOWN_IN_PROGRESS_ = 1116;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IO_DEVICE_ = 1117;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SERIAL_NO_DEVICE_ = 1118;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IRQ_BUSY_ = 1119;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MORE_WRITES_ = 1120;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_COUNTER_TIMEOUT_ = 1121;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_FLOPPY_ID_MARK_NOT_FOUND_ = 1122;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_FLOPPY_WRONG_CYLINDER_ = 1123;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_FLOPPY_UNKNOWN_ERROR_ = 1124;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_FLOPPY_BAD_REGISTERS_ = 1125;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DISK_RECALIBRATE_FAILED_ = 1126;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DISK_OPERATION_FAILED_ = 1127;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DISK_RESET_FAILED_ = 1128;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EOM_OVERFLOW_ = 1129;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NOT_ENOUGH_SERVER_MEMORY_ = 1130;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_POSSIBLE_DEADLOCK_ = 1131;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MAPPED_ALIGNMENT_ = 1132;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SET_POWER_STATE_VETOED_ = 1140;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SET_POWER_STATE_FAILED_ = 1141;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_TOO_MANY_LINKS_ = 1142;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_OLD_WIN_VERSION_ = 1150;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_APP_WRONG_OS_ = 1151;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SINGLE_INSTANCE_APP_ = 1152;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_RMODE_APP_ = 1153;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_DLL_ = 1154;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NO_ASSOCIATION_ = 1155;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DDE_FAIL_ = 1156;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DLL_NOT_FOUND_ = 1157;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NO_MORE_USER_HANDLES_ = 1158;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MESSAGE_SYNC_ONLY_ = 1159;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SOURCE_ELEMENT_EMPTY_ = 1160;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DESTINATION_ELEMENT_FULL_ = 1161;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_ILLEGAL_ELEMENT_ADDRESS_ = 1162;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MAGAZINE_NOT_PRESENT_ = 1163;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DEVICE_REINITIALIZATION_NEEDED_ = 1164;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DEVICE_REQUIRES_CLEANING_ = 1165;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DEVICE_DOOR_OPEN_ = 1166;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DEVICE_NOT_CONNECTED_ = 1167;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NOT_FOUND_ = 1168;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NO_MATCH_ = 1169;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SET_NOT_FOUND_ = 1170;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_POINT_NOT_FOUND_ = 1171;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NO_TRACKING_SERVICE_ = 1172;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NO_VOLUME_ID_ = 1173;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_UNABLE_TO_REMOVE_REPLACED_ = 1175;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_UNABLE_TO_MOVE_REPLACEMENT_ = 1176;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_UNABLE_TO_MOVE_REPLACEMENT_2_ = 1177;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_JOURNAL_DELETE_IN_PROGRESS_ = 1178;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_JOURNAL_NOT_ACTIVE_ = 1179;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_POTENTIAL_FILE_FOUND_ = 1180;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_JOURNAL_ENTRY_DELETED_ = 1181;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SHUTDOWN_IS_SCHEDULED_ = 1190;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SHUTDOWN_USERS_LOGGED_ON_ = 1191;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_BAD_DEVICE_ = 1200;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CONNECTION_UNAVAIL_ = 1201;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DEVICE_ALREADY_REMEMBERED_ = 1202;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NO_NET_OR_BAD_PATH_ = 1203;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_BAD_PROVIDER_ = 1204;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CANNOT_OPEN_PROFILE_ = 1205;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_BAD_PROFILE_ = 1206;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NOT_CONTAINER_ = 1207;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EXTENDED_ERROR_ = 1208;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_GROUPNAME_ = 1209;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_COMPUTERNAME_ = 1210;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_EVENTNAME_ = 1211;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_DOMAINNAME_ = 1212;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_SERVICENAME_ = 1213;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_NETNAME_ = 1214;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_SHARENAME_ = 1215;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_PASSWORDNAME_ = 1216;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_MESSAGENAME_ = 1217;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_MESSAGEDEST_ = 1218;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SESSION_CREDENTIAL_CONFLICT_ = 1219;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_REMOTE_SESSION_LIMIT_EXCEEDED_ = 1220;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DUP_DOMAINNAME_ = 1221;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NO_NETWORK_ = 1222;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CANCELLED_ = 1223;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_USER_MAPPED_FILE_ = 1224;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CONNECTION_REFUSED_ = 1225;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_GRACEFUL_DISCONNECT_ = 1226;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_ADDRESS_ALREADY_ASSOCIATED_ = 1227;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_ADDRESS_NOT_ASSOCIATED_ = 1228;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CONNECTION_INVALID_ = 1229;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CONNECTION_ACTIVE_ = 1230;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NETWORK_UNREACHABLE_ = 1231;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_HOST_UNREACHABLE_ = 1232;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PROTOCOL_UNREACHABLE_ = 1233;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PORT_UNREACHABLE_ = 1234;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_REQUEST_ABORTED_ = 1235;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CONNECTION_ABORTED_ = 1236;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_RETRY_ = 1237;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CONNECTION_COUNT_LIMIT_ = 1238;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LOGIN_TIME_RESTRICTION_ = 1239;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LOGIN_WKSTA_RESTRICTION_ = 1240;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INCORRECT_ADDRESS_ = 1241;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_ALREADY_REGISTERED_ = 1242;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SERVICE_NOT_FOUND_ = 1243;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NOT_AUTHENTICATED_ = 1244;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NOT_LOGGED_ON_ = 1245;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CONTINUE_ = 1246;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_ALREADY_INITIALIZED_ = 1247;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NO_MORE_DEVICES_ = 1248;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NO_SUCH_SITE_ = 1249;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DOMAIN_CONTROLLER_EXISTS_ = 1250;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_ONLY_IF_CONNECTED_ = 1251;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_OVERRIDE_NOCHANGES_ = 1252;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_BAD_USER_PROFILE_ = 1253;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NOT_SUPPORTED_ON_SBS_ = 1254;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SERVER_SHUTDOWN_IN_PROGRESS_ = 1255;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_HOST_DOWN_ = 1256;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NON_ACCOUNT_SID_ = 1257;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NON_DOMAIN_SID_ = 1258;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_APPHELP_BLOCK_ = 1259;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_ACCESS_DISABLED_BY_POLICY_ = 1260;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_REG_NAT_CONSUMPTION_ = 1261;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CSCSHARE_OFFLINE_ = 1262;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PKINIT_FAILURE_ = 1263;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SMARTCARD_SUBSYSTEM_FAILURE_ = 1264;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DOWNGRADE_DETECTED_ = 1265;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MACHINE_LOCKED_ = 1271;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SMB_GUEST_LOGON_BLOCKED_ = 1272;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CALLBACK_SUPPLIED_INVALID_DATA_ = 1273;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SYNC_FOREGROUND_REFRESH_REQUIRED_ = 1274;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DRIVER_BLOCKED_ = 1275;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_IMPORT_OF_NON_DLL_ = 1276;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_ACCESS_DISABLED_WEBBLADE_ = 1277;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_ACCESS_DISABLED_WEBBLADE_TAMPER_ = 1278;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_RECOVERY_FAILURE_ = 1279;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_ALREADY_FIBER_ = 1280;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_ALREADY_THREAD_ = 1281;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_STACK_BUFFER_OVERRUN_ = 1282;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PARAMETER_QUOTA_EXCEEDED_ = 1283;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DEBUGGER_INACTIVE_ = 1284;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DELAY_LOAD_FAILED_ = 1285;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_VDM_DISALLOWED_ = 1286;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_UNIDENTIFIED_ERROR_ = 1287;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_CRUNTIME_PARAMETER_ = 1288;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_BEYOND_VDL_ = 1289;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INCOMPATIBLE_SERVICE_SID_TYPE_ = 1290;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DRIVER_PROCESS_TERMINATED_ = 1291;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IMPLEMENTATION_LIMIT_ = 1292;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PROCESS_IS_PROTECTED_ = 1293;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SERVICE_NOTIFY_CLIENT_LAGGING_ = 1294;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DISK_QUOTA_EXCEEDED_ = 1295;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CONTENT_BLOCKED_ = 1296;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INCOMPATIBLE_SERVICE_PRIVILEGE_ = 1297;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_APP_HANG_ = 1298;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_LABEL_ = 1299;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NOT_ALL_ASSIGNED_ = 1300;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SOME_NOT_MAPPED_ = 1301;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NO_QUOTAS_FOR_ACCOUNT_ = 1302;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LOCAL_USER_SESSION_KEY_ = 1303;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NULL_LM_PASSWORD_ = 1304;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_UNKNOWN_REVISION_ = 1305;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_REVISION_MISMATCH_ = 1306;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_OWNER_ = 1307;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_PRIMARY_GROUP_ = 1308;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NO_IMPERSONATION_TOKEN_ = 1309;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CANT_DISABLE_MANDATORY_ = 1310;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NO_LOGON_SERVERS_ = 1311;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NO_SUCH_LOGON_SESSION_ = 1312;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NO_SUCH_PRIVILEGE_ = 1313;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PRIVILEGE_NOT_HELD_ = 1314;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_ACCOUNT_NAME_ = 1315;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_USER_EXISTS_ = 1316;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NO_SUCH_USER_ = 1317;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_GROUP_EXISTS_ = 1318;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NO_SUCH_GROUP_ = 1319;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MEMBER_IN_GROUP_ = 1320;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MEMBER_NOT_IN_GROUP_ = 1321;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LAST_ADMIN_ = 1322;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_WRONG_PASSWORD_ = 1323;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_ILL_FORMED_PASSWORD_ = 1324;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PASSWORD_RESTRICTION_ = 1325;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LOGON_FAILURE_ = 1326;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_ACCOUNT_RESTRICTION_ = 1327;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_LOGON_HOURS_ = 1328;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_WORKSTATION_ = 1329;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PASSWORD_EXPIRED_ = 1330;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_ACCOUNT_DISABLED_ = 1331;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NONE_MAPPED_ = 1332;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_TOO_MANY_LUIDS_REQUESTED_ = 1333;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LUIDS_EXHAUSTED_ = 1334;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_SUB_AUTHORITY_ = 1335;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_ACL_ = 1336;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_SID_ = 1337;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_SECURITY_DESCR_ = 1338;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_BAD_INHERITANCE_ACL_ = 1340;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SERVER_DISABLED_ = 1341;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SERVER_NOT_DISABLED_ = 1342;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_ID_AUTHORITY_ = 1343;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_ALLOTTED_SPACE_EXCEEDED_ = 1344;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_GROUP_ATTRIBUTES_ = 1345;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_BAD_IMPERSONATION_LEVEL_ = 1346;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CANT_OPEN_ANONYMOUS_ = 1347;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_BAD_VALIDATION_CLASS_ = 1348;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_BAD_TOKEN_TYPE_ = 1349;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NO_SECURITY_ON_OBJECT_ = 1350;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CANT_ACCESS_DOMAIN_INFO_ = 1351;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_SERVER_STATE_ = 1352;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_DOMAIN_STATE_ = 1353;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_DOMAIN_ROLE_ = 1354;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NO_SUCH_DOMAIN_ = 1355;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DOMAIN_EXISTS_ = 1356;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DOMAIN_LIMIT_EXCEEDED_ = 1357;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INTERNAL_DB_CORRUPTION_ = 1358;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INTERNAL_ERROR_ = 1359;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_GENERIC_NOT_MAPPED_ = 1360;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_BAD_DESCRIPTOR_FORMAT_ = 1361;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NOT_LOGON_PROCESS_ = 1362;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LOGON_SESSION_EXISTS_ = 1363;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NO_SUCH_PACKAGE_ = 1364;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_BAD_LOGON_SESSION_STATE_ = 1365;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LOGON_SESSION_COLLISION_ = 1366;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_LOGON_TYPE_ = 1367;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CANNOT_IMPERSONATE_ = 1368;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_RXACT_INVALID_STATE_ = 1369;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_RXACT_COMMIT_FAILURE_ = 1370;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SPECIAL_ACCOUNT_ = 1371;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SPECIAL_GROUP_ = 1372;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SPECIAL_USER_ = 1373;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MEMBERS_PRIMARY_GROUP_ = 1374;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_TOKEN_ALREADY_IN_USE_ = 1375;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NO_SUCH_ALIAS_ = 1376;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MEMBER_NOT_IN_ALIAS_ = 1377;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MEMBER_IN_ALIAS_ = 1378;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_ALIAS_EXISTS_ = 1379;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LOGON_NOT_GRANTED_ = 1380;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_TOO_MANY_SECRETS_ = 1381;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SECRET_TOO_LONG_ = 1382;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INTERNAL_DB_ERROR_ = 1383;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_TOO_MANY_CONTEXT_IDS_ = 1384;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LOGON_TYPE_NOT_GRANTED_ = 1385;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NT_CROSS_ENCRYPTION_REQUIRED_ = 1386;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NO_SUCH_MEMBER_ = 1387;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_MEMBER_ = 1388;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_TOO_MANY_SIDS_ = 1389;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LM_CROSS_ENCRYPTION_REQUIRED_ = 1390;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NO_INHERITANCE_ = 1391;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_FILE_CORRUPT_ = 1392;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DISK_CORRUPT_ = 1393;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NO_USER_SESSION_KEY_ = 1394;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LICENSE_QUOTA_EXCEEDED_ = 1395;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_WRONG_TARGET_NAME_ = 1396;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MUTUAL_AUTH_FAILED_ = 1397;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_TIME_SKEW_ = 1398;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CURRENT_DOMAIN_NOT_ALLOWED_ = 1399;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_WINDOW_HANDLE_ = 1400;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_MENU_HANDLE_ = 1401;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_CURSOR_HANDLE_ = 1402;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_ACCEL_HANDLE_ = 1403;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_HOOK_HANDLE_ = 1404;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_DWP_HANDLE_ = 1405;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_TLW_WITH_WSCHILD_ = 1406;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CANNOT_FIND_WND_CLASS_ = 1407;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_WINDOW_OF_OTHER_THREAD_ = 1408;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_HOTKEY_ALREADY_REGISTERED_ = 1409;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLASS_ALREADY_EXISTS_ = 1410;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLASS_DOES_NOT_EXIST_ = 1411;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLASS_HAS_WINDOWS_ = 1412;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_INDEX_ = 1413;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_ICON_HANDLE_ = 1414;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PRIVATE_DIALOG_INDEX_ = 1415;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LISTBOX_ID_NOT_FOUND_ = 1416;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NO_WILDCARD_CHARACTERS_ = 1417;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLIPBOARD_NOT_OPEN_ = 1418;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_HOTKEY_NOT_REGISTERED_ = 1419;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_WINDOW_NOT_DIALOG_ = 1420;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CONTROL_ID_NOT_FOUND_ = 1421;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_COMBOBOX_MESSAGE_ = 1422;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_WINDOW_NOT_COMBOBOX_ = 1423;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_EDIT_HEIGHT_ = 1424;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DC_NOT_FOUND_ = 1425;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_HOOK_FILTER_ = 1426;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_FILTER_PROC_ = 1427;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_HOOK_NEEDS_HMOD_ = 1428;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_GLOBAL_ONLY_HOOK_ = 1429;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_JOURNAL_HOOK_SET_ = 1430;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_HOOK_NOT_INSTALLED_ = 1431;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_LB_MESSAGE_ = 1432;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SETCOUNT_ON_BAD_LB_ = 1433;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LB_WITHOUT_TABSTOPS_ = 1434;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DESTROY_OBJECT_OF_OTHER_THREAD_ = 1435;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CHILD_WINDOW_MENU_ = 1436;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NO_SYSTEM_MENU_ = 1437;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_MSGBOX_STYLE_ = 1438;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_SPI_VALUE_ = 1439;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SCREEN_ALREADY_LOCKED_ = 1440;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_HWNDS_HAVE_DIFF_PARENT_ = 1441;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NOT_CHILD_WINDOW_ = 1442;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_GW_COMMAND_ = 1443;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_THREAD_ID_ = 1444;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NON_MDICHILD_WINDOW_ = 1445;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_POPUP_ALREADY_ACTIVE_ = 1446;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NO_SCROLLBARS_ = 1447;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_SCROLLBAR_RANGE_ = 1448;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_SHOWWIN_COMMAND_ = 1449;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NO_SYSTEM_RESOURCES_ = 1450;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NONPAGED_SYSTEM_RESOURCES_ = 1451;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PAGED_SYSTEM_RESOURCES_ = 1452;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_WORKING_SET_QUOTA_ = 1453;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PAGEFILE_QUOTA_ = 1454;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_COMMITMENT_LIMIT_ = 1455;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MENU_ITEM_NOT_FOUND_ = 1456;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_KEYBOARD_HANDLE_ = 1457;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_HOOK_TYPE_NOT_ALLOWED_ = 1458;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_REQUIRES_INTERACTIVE_WINDOWSTATION_ = 1459;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_TIMEOUT_ = 1460;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_MONITOR_HANDLE_ = 1461;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INCORRECT_SIZE_ = 1462;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SYMLINK_CLASS_DISABLED_ = 1463;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SYMLINK_NOT_SUPPORTED_ = 1464;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_XML_PARSE_ERROR_ = 1465;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_XMLDSIG_ERROR_ = 1466;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_RESTART_APPLICATION_ = 1467;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_WRONG_COMPARTMENT_ = 1468;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_AUTHIP_FAILURE_ = 1469;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NO_NVRAM_RESOURCES_ = 1470;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NOT_GUI_PROCESS_ = 1471;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EVENTLOG_FILE_CORRUPT_ = 1500;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EVENTLOG_CANT_START_ = 1501;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LOG_FILE_FULL_ = 1502;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EVENTLOG_FILE_CHANGED_ = 1503;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CONTAINER_ASSIGNED_ = 1504;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_JOB_NO_CONTAINER_ = 1505;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_TASK_NAME_ = 1550;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_TASK_INDEX_ = 1551;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_THREAD_ALREADY_IN_TASK_ = 1552;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INSTALL_SERVICE_FAILURE_ = 1601;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INSTALL_USEREXIT_ = 1602;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INSTALL_FAILURE_ = 1603;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INSTALL_SUSPEND_ = 1604;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_UNKNOWN_PRODUCT_ = 1605;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_UNKNOWN_FEATURE_ = 1606;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_UNKNOWN_COMPONENT_ = 1607;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_UNKNOWN_PROPERTY_ = 1608;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_HANDLE_STATE_ = 1609;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_BAD_CONFIGURATION_ = 1610;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INDEX_ABSENT_ = 1611;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INSTALL_SOURCE_ABSENT_ = 1612;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INSTALL_PACKAGE_VERSION_ = 1613;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PRODUCT_UNINSTALLED_ = 1614;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_BAD_QUERY_SYNTAX_ = 1615;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_FIELD_ = 1616;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DEVICE_REMOVED_ = 1617;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INSTALL_ALREADY_RUNNING_ = 1618;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INSTALL_PACKAGE_OPEN_FAILED_ = 1619;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INSTALL_PACKAGE_INVALID_ = 1620;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INSTALL_UI_FAILURE_ = 1621;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INSTALL_LOG_FAILURE_ = 1622;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INSTALL_LANGUAGE_UNSUPPORTED_ = 1623;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INSTALL_TRANSFORM_FAILURE_ = 1624;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INSTALL_PACKAGE_REJECTED_ = 1625;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_FUNCTION_NOT_CALLED_ = 1626;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_FUNCTION_FAILED_ = 1627;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_TABLE_ = 1628;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DATATYPE_MISMATCH_ = 1629;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_UNSUPPORTED_TYPE_ = 1630;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CREATE_FAILED_ = 1631;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INSTALL_TEMP_UNWRITABLE_ = 1632;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INSTALL_PLATFORM_UNSUPPORTED_ = 1633;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INSTALL_NOTUSED_ = 1634;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PATCH_PACKAGE_OPEN_FAILED_ = 1635;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PATCH_PACKAGE_INVALID_ = 1636;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PATCH_PACKAGE_UNSUPPORTED_ = 1637;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PRODUCT_VERSION_ = 1638;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_COMMAND_LINE_ = 1639;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INSTALL_REMOTE_DISALLOWED_ = 1640;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SUCCESS_REBOOT_INITIATED_ = 1641;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PATCH_TARGET_NOT_FOUND_ = 1642;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PATCH_PACKAGE_REJECTED_ = 1643;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INSTALL_TRANSFORM_REJECTED_ = 1644;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INSTALL_REMOTE_PROHIBITED_ = 1645;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PATCH_REMOVAL_UNSUPPORTED_ = 1646;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_UNKNOWN_PATCH_ = 1647;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PATCH_NO_SEQUENCE_ = 1648;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PATCH_REMOVAL_DISALLOWED_ = 1649;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_PATCH_XML_ = 1650;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PATCH_MANAGED_ADVERTISED_PRODUCT_ = 1651;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INSTALL_SERVICE_SAFEBOOT_ = 1652;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_FAIL_FAST_EXCEPTION_ = 1653;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INSTALL_REJECTED_ = 1654;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DYNAMIC_CODE_BLOCKED_ = 1655;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NOT_SAME_OBJECT_ = 1656;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_STRICT_CFG_VIOLATION_ = 1657;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SET_CONTEXT_DENIED_ = 1660;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CROSS_PARTITION_VIOLATION_ = 1661;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_INVALID_STRING_BINDING_ = 1700;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_WRONG_KIND_OF_BINDING_ = 1701;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_INVALID_BINDING_ = 1702;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_PROTSEQ_NOT_SUPPORTED_ = 1703;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_INVALID_RPC_PROTSEQ_ = 1704;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_INVALID_STRING_UUID_ = 1705;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_INVALID_ENDPOINT_FORMAT_ = 1706;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_INVALID_NET_ADDR_ = 1707;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_NO_ENDPOINT_FOUND_ = 1708;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_INVALID_TIMEOUT_ = 1709;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_OBJECT_NOT_FOUND_ = 1710;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_ALREADY_REGISTERED_ = 1711;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_TYPE_ALREADY_REGISTERED_ = 1712;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_ALREADY_LISTENING_ = 1713;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_NO_PROTSEQS_REGISTERED_ = 1714;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_NOT_LISTENING_ = 1715;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_UNKNOWN_MGR_TYPE_ = 1716;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_UNKNOWN_IF_ = 1717;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_NO_BINDINGS_ = 1718;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_NO_PROTSEQS_ = 1719;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_CANT_CREATE_ENDPOINT_ = 1720;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_OUT_OF_RESOURCES_ = 1721;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_SERVER_UNAVAILABLE_ = 1722;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_SERVER_TOO_BUSY_ = 1723;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_INVALID_NETWORK_OPTIONS_ = 1724;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_NO_CALL_ACTIVE_ = 1725;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_CALL_FAILED_ = 1726;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_CALL_FAILED_DNE_ = 1727;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_PROTOCOL_ERROR_ = 1728;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_PROXY_ACCESS_DENIED_ = 1729;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_UNSUPPORTED_TRANS_SYN_ = 1730;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_UNSUPPORTED_TYPE_ = 1732;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_INVALID_TAG_ = 1733;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_INVALID_BOUND_ = 1734;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_NO_ENTRY_NAME_ = 1735;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_INVALID_NAME_SYNTAX_ = 1736;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_UNSUPPORTED_NAME_SYNTAX_ = 1737;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_UUID_NO_ADDRESS_ = 1739;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_DUPLICATE_ENDPOINT_ = 1740;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_UNKNOWN_AUTHN_TYPE_ = 1741;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_MAX_CALLS_TOO_SMALL_ = 1742;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_STRING_TOO_LONG_ = 1743;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_PROTSEQ_NOT_FOUND_ = 1744;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_PROCNUM_OUT_OF_RANGE_ = 1745;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_BINDING_HAS_NO_AUTH_ = 1746;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_UNKNOWN_AUTHN_SERVICE_ = 1747;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_UNKNOWN_AUTHN_LEVEL_ = 1748;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_INVALID_AUTH_IDENTITY_ = 1749;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_UNKNOWN_AUTHZ_SERVICE_ = 1750;
BOOST_CONSTEXPR_OR_CONST DWORD_ EPT_S_INVALID_ENTRY_ = 1751;
BOOST_CONSTEXPR_OR_CONST DWORD_ EPT_S_CANT_PERFORM_OP_ = 1752;
BOOST_CONSTEXPR_OR_CONST DWORD_ EPT_S_NOT_REGISTERED_ = 1753;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_NOTHING_TO_EXPORT_ = 1754;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_INCOMPLETE_NAME_ = 1755;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_INVALID_VERS_OPTION_ = 1756;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_NO_MORE_MEMBERS_ = 1757;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_NOT_ALL_OBJS_UNEXPORTED_ = 1758;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_INTERFACE_NOT_FOUND_ = 1759;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_ENTRY_ALREADY_EXISTS_ = 1760;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_ENTRY_NOT_FOUND_ = 1761;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_NAME_SERVICE_UNAVAILABLE_ = 1762;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_INVALID_NAF_ID_ = 1763;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_CANNOT_SUPPORT_ = 1764;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_NO_CONTEXT_AVAILABLE_ = 1765;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_INTERNAL_ERROR_ = 1766;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_ZERO_DIVIDE_ = 1767;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_ADDRESS_ERROR_ = 1768;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_FP_DIV_ZERO_ = 1769;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_FP_UNDERFLOW_ = 1770;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_FP_OVERFLOW_ = 1771;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_X_NO_MORE_ENTRIES_ = 1772;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_X_SS_CHAR_TRANS_OPEN_FAIL_ = 1773;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_X_SS_CHAR_TRANS_SHORT_FILE_ = 1774;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_X_SS_IN_NULL_CONTEXT_ = 1775;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_X_SS_CONTEXT_DAMAGED_ = 1777;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_X_SS_HANDLES_MISMATCH_ = 1778;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_X_SS_CANNOT_GET_CALL_HANDLE_ = 1779;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_X_NULL_REF_POINTER_ = 1780;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_X_ENUM_VALUE_OUT_OF_RANGE_ = 1781;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_X_BYTE_COUNT_TOO_SMALL_ = 1782;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_X_BAD_STUB_DATA_ = 1783;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_USER_BUFFER_ = 1784;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_UNRECOGNIZED_MEDIA_ = 1785;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NO_TRUST_LSA_SECRET_ = 1786;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NO_TRUST_SAM_ACCOUNT_ = 1787;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_TRUSTED_DOMAIN_FAILURE_ = 1788;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_TRUSTED_RELATIONSHIP_FAILURE_ = 1789;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_TRUST_FAILURE_ = 1790;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_CALL_IN_PROGRESS_ = 1791;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NETLOGON_NOT_STARTED_ = 1792;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_ACCOUNT_EXPIRED_ = 1793;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_REDIRECTOR_HAS_OPEN_HANDLES_ = 1794;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PRINTER_DRIVER_ALREADY_INSTALLED_ = 1795;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_UNKNOWN_PORT_ = 1796;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_UNKNOWN_PRINTER_DRIVER_ = 1797;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_UNKNOWN_PRINTPROCESSOR_ = 1798;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_SEPARATOR_FILE_ = 1799;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_PRIORITY_ = 1800;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_PRINTER_NAME_ = 1801;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PRINTER_ALREADY_EXISTS_ = 1802;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_PRINTER_COMMAND_ = 1803;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_DATATYPE_ = 1804;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_ENVIRONMENT_ = 1805;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_NO_MORE_BINDINGS_ = 1806;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NOLOGON_INTERDOMAIN_TRUST_ACCOUNT_ = 1807;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NOLOGON_WORKSTATION_TRUST_ACCOUNT_ = 1808;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NOLOGON_SERVER_TRUST_ACCOUNT_ = 1809;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DOMAIN_TRUST_INCONSISTENT_ = 1810;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SERVER_HAS_OPEN_HANDLES_ = 1811;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_RESOURCE_DATA_NOT_FOUND_ = 1812;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_RESOURCE_TYPE_NOT_FOUND_ = 1813;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_RESOURCE_NAME_NOT_FOUND_ = 1814;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_RESOURCE_LANG_NOT_FOUND_ = 1815;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NOT_ENOUGH_QUOTA_ = 1816;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_NO_INTERFACES_ = 1817;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_CALL_CANCELLED_ = 1818;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_BINDING_INCOMPLETE_ = 1819;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_COMM_FAILURE_ = 1820;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_UNSUPPORTED_AUTHN_LEVEL_ = 1821;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_NO_PRINC_NAME_ = 1822;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_NOT_RPC_ERROR_ = 1823;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_UUID_LOCAL_ONLY_ = 1824;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_SEC_PKG_ERROR_ = 1825;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_NOT_CANCELLED_ = 1826;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_X_INVALID_ES_ACTION_ = 1827;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_X_WRONG_ES_VERSION_ = 1828;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_X_WRONG_STUB_VERSION_ = 1829;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_X_INVALID_PIPE_OBJECT_ = 1830;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_X_WRONG_PIPE_ORDER_ = 1831;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_X_WRONG_PIPE_VERSION_ = 1832;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_COOKIE_AUTH_FAILED_ = 1833;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_DO_NOT_DISTURB_ = 1834;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_SYSTEM_HANDLE_COUNT_EXCEEDED_ = 1835;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_SYSTEM_HANDLE_TYPE_MISMATCH_ = 1836;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_GROUP_MEMBER_NOT_FOUND_ = 1898;
BOOST_CONSTEXPR_OR_CONST DWORD_ EPT_S_CANT_CREATE_ = 1899;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_INVALID_OBJECT_ = 1900;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_TIME_ = 1901;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_FORM_NAME_ = 1902;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_FORM_SIZE_ = 1903;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_ALREADY_WAITING_ = 1904;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PRINTER_DELETED_ = 1905;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_PRINTER_STATE_ = 1906;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PASSWORD_MUST_CHANGE_ = 1907;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DOMAIN_CONTROLLER_NOT_FOUND_ = 1908;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_ACCOUNT_LOCKED_OUT_ = 1909;
BOOST_CONSTEXPR_OR_CONST DWORD_ OR_INVALID_OXID_ = 1910;
BOOST_CONSTEXPR_OR_CONST DWORD_ OR_INVALID_OID_ = 1911;
BOOST_CONSTEXPR_OR_CONST DWORD_ OR_INVALID_SET_ = 1912;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_SEND_INCOMPLETE_ = 1913;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_INVALID_ASYNC_HANDLE_ = 1914;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_INVALID_ASYNC_CALL_ = 1915;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_X_PIPE_CLOSED_ = 1916;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_X_PIPE_DISCIPLINE_ERROR_ = 1917;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_X_PIPE_EMPTY_ = 1918;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NO_SITENAME_ = 1919;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CANT_ACCESS_FILE_ = 1920;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CANT_RESOLVE_FILENAME_ = 1921;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_ENTRY_TYPE_MISMATCH_ = 1922;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_NOT_ALL_OBJS_EXPORTED_ = 1923;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_INTERFACE_NOT_EXPORTED_ = 1924;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_PROFILE_NOT_ADDED_ = 1925;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_PRF_ELT_NOT_ADDED_ = 1926;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_PRF_ELT_NOT_REMOVED_ = 1927;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_GRP_ELT_NOT_ADDED_ = 1928;
BOOST_CONSTEXPR_OR_CONST DWORD_ RPC_S_GRP_ELT_NOT_REMOVED_ = 1929;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_KM_DRIVER_BLOCKED_ = 1930;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CONTEXT_EXPIRED_ = 1931;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PER_USER_TRUST_QUOTA_EXCEEDED_ = 1932;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_ALL_USER_TRUST_QUOTA_EXCEEDED_ = 1933;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_USER_DELETE_TRUST_QUOTA_EXCEEDED_ = 1934;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_AUTHENTICATION_FIREWALL_FAILED_ = 1935;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_REMOTE_PRINT_CONNECTIONS_BLOCKED_ = 1936;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NTLM_BLOCKED_ = 1937;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PASSWORD_CHANGE_REQUIRED_ = 1938;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LOST_MODE_LOGON_RESTRICTION_ = 1939;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_PIXEL_FORMAT_ = 2000;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_BAD_DRIVER_ = 2001;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_WINDOW_STYLE_ = 2002;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_METAFILE_NOT_SUPPORTED_ = 2003;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_TRANSFORM_NOT_SUPPORTED_ = 2004;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLIPPING_NOT_SUPPORTED_ = 2005;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_CMM_ = 2010;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_PROFILE_ = 2011;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_TAG_NOT_FOUND_ = 2012;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_TAG_NOT_PRESENT_ = 2013;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DUPLICATE_TAG_ = 2014;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PROFILE_NOT_ASSOCIATED_WITH_DEVICE_ = 2015;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PROFILE_NOT_FOUND_ = 2016;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_COLORSPACE_ = 2017;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_ICM_NOT_ENABLED_ = 2018;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DELETING_ICM_XFORM_ = 2019;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_TRANSFORM_ = 2020;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_COLORSPACE_MISMATCH_ = 2021;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_COLORINDEX_ = 2022;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PROFILE_DOES_NOT_MATCH_DEVICE_ = 2023;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CONNECTED_OTHER_PASSWORD_ = 2108;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CONNECTED_OTHER_PASSWORD_DEFAULT_ = 2109;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_BAD_USERNAME_ = 2202;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NOT_CONNECTED_ = 2250;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_OPEN_FILES_ = 2401;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_ACTIVE_CONNECTIONS_ = 2402;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DEVICE_IN_USE_ = 2404;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_UNKNOWN_PRINT_MONITOR_ = 3000;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PRINTER_DRIVER_IN_USE_ = 3001;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SPOOL_FILE_NOT_FOUND_ = 3002;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SPL_NO_STARTDOC_ = 3003;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SPL_NO_ADDJOB_ = 3004;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PRINT_PROCESSOR_ALREADY_INSTALLED_ = 3005;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PRINT_MONITOR_ALREADY_INSTALLED_ = 3006;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_PRINT_MONITOR_ = 3007;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PRINT_MONITOR_IN_USE_ = 3008;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PRINTER_HAS_JOBS_QUEUED_ = 3009;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SUCCESS_REBOOT_REQUIRED_ = 3010;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SUCCESS_RESTART_REQUIRED_ = 3011;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PRINTER_NOT_FOUND_ = 3012;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PRINTER_DRIVER_WARNED_ = 3013;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PRINTER_DRIVER_BLOCKED_ = 3014;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PRINTER_DRIVER_PACKAGE_IN_USE_ = 3015;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CORE_DRIVER_PACKAGE_NOT_FOUND_ = 3016;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_FAIL_REBOOT_REQUIRED_ = 3017;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_FAIL_REBOOT_INITIATED_ = 3018;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PRINTER_DRIVER_DOWNLOAD_NEEDED_ = 3019;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PRINT_JOB_RESTART_REQUIRED_ = 3020;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_PRINTER_DRIVER_MANIFEST_ = 3021;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PRINTER_NOT_SHAREABLE_ = 3022;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_REQUEST_PAUSED_ = 3050;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IO_REISSUE_AS_CACHED_ = 3950;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_WINS_INTERNAL_ = 4000;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CAN_NOT_DEL_LOCAL_WINS_ = 4001;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_STATIC_INIT_ = 4002;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INC_BACKUP_ = 4003;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_FULL_BACKUP_ = 4004;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_REC_NON_EXISTENT_ = 4005;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_RPL_NOT_ALLOWED_ = 4006;
BOOST_CONSTEXPR_OR_CONST DWORD_ PEERDIST_ERROR_CONTENTINFO_VERSION_UNSUPPORTED_ = 4050;
BOOST_CONSTEXPR_OR_CONST DWORD_ PEERDIST_ERROR_CANNOT_PARSE_CONTENTINFO_ = 4051;
BOOST_CONSTEXPR_OR_CONST DWORD_ PEERDIST_ERROR_MISSING_DATA_ = 4052;
BOOST_CONSTEXPR_OR_CONST DWORD_ PEERDIST_ERROR_NO_MORE_ = 4053;
BOOST_CONSTEXPR_OR_CONST DWORD_ PEERDIST_ERROR_NOT_INITIALIZED_ = 4054;
BOOST_CONSTEXPR_OR_CONST DWORD_ PEERDIST_ERROR_ALREADY_INITIALIZED_ = 4055;
BOOST_CONSTEXPR_OR_CONST DWORD_ PEERDIST_ERROR_SHUTDOWN_IN_PROGRESS_ = 4056;
BOOST_CONSTEXPR_OR_CONST DWORD_ PEERDIST_ERROR_INVALIDATED_ = 4057;
BOOST_CONSTEXPR_OR_CONST DWORD_ PEERDIST_ERROR_ALREADY_EXISTS_ = 4058;
BOOST_CONSTEXPR_OR_CONST DWORD_ PEERDIST_ERROR_OPERATION_NOTFOUND_ = 4059;
BOOST_CONSTEXPR_OR_CONST DWORD_ PEERDIST_ERROR_ALREADY_COMPLETED_ = 4060;
BOOST_CONSTEXPR_OR_CONST DWORD_ PEERDIST_ERROR_OUT_OF_BOUNDS_ = 4061;
BOOST_CONSTEXPR_OR_CONST DWORD_ PEERDIST_ERROR_VERSION_UNSUPPORTED_ = 4062;
BOOST_CONSTEXPR_OR_CONST DWORD_ PEERDIST_ERROR_INVALID_CONFIGURATION_ = 4063;
BOOST_CONSTEXPR_OR_CONST DWORD_ PEERDIST_ERROR_NOT_LICENSED_ = 4064;
BOOST_CONSTEXPR_OR_CONST DWORD_ PEERDIST_ERROR_SERVICE_UNAVAILABLE_ = 4065;
BOOST_CONSTEXPR_OR_CONST DWORD_ PEERDIST_ERROR_TRUST_FAILURE_ = 4066;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DHCP_ADDRESS_CONFLICT_ = 4100;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_WMI_GUID_NOT_FOUND_ = 4200;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_WMI_INSTANCE_NOT_FOUND_ = 4201;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_WMI_ITEMID_NOT_FOUND_ = 4202;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_WMI_TRY_AGAIN_ = 4203;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_WMI_DP_NOT_FOUND_ = 4204;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_WMI_UNRESOLVED_INSTANCE_REF_ = 4205;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_WMI_ALREADY_ENABLED_ = 4206;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_WMI_GUID_DISCONNECTED_ = 4207;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_WMI_SERVER_UNAVAILABLE_ = 4208;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_WMI_DP_FAILED_ = 4209;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_WMI_INVALID_MOF_ = 4210;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_WMI_INVALID_REGINFO_ = 4211;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_WMI_ALREADY_DISABLED_ = 4212;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_WMI_READ_ONLY_ = 4213;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_WMI_SET_FAILURE_ = 4214;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NOT_APPCONTAINER_ = 4250;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_APPCONTAINER_REQUIRED_ = 4251;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NOT_SUPPORTED_IN_APPCONTAINER_ = 4252;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_PACKAGE_SID_LENGTH_ = 4253;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_MEDIA_ = 4300;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_LIBRARY_ = 4301;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_MEDIA_POOL_ = 4302;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DRIVE_MEDIA_MISMATCH_ = 4303;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MEDIA_OFFLINE_ = 4304;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LIBRARY_OFFLINE_ = 4305;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EMPTY_ = 4306;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NOT_EMPTY_ = 4307;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MEDIA_UNAVAILABLE_ = 4308;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_RESOURCE_DISABLED_ = 4309;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_CLEANER_ = 4310;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_UNABLE_TO_CLEAN_ = 4311;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_OBJECT_NOT_FOUND_ = 4312;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DATABASE_FAILURE_ = 4313;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DATABASE_FULL_ = 4314;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MEDIA_INCOMPATIBLE_ = 4315;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_RESOURCE_NOT_PRESENT_ = 4316;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_OPERATION_ = 4317;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MEDIA_NOT_AVAILABLE_ = 4318;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DEVICE_NOT_AVAILABLE_ = 4319;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_REQUEST_REFUSED_ = 4320;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_DRIVE_OBJECT_ = 4321;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LIBRARY_FULL_ = 4322;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MEDIUM_NOT_ACCESSIBLE_ = 4323;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_UNABLE_TO_LOAD_MEDIUM_ = 4324;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_UNABLE_TO_INVENTORY_DRIVE_ = 4325;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_UNABLE_TO_INVENTORY_SLOT_ = 4326;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_UNABLE_TO_INVENTORY_TRANSPORT_ = 4327;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_TRANSPORT_FULL_ = 4328;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CONTROLLING_IEPORT_ = 4329;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_UNABLE_TO_EJECT_MOUNTED_MEDIA_ = 4330;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLEANER_SLOT_SET_ = 4331;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLEANER_SLOT_NOT_SET_ = 4332;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLEANER_CARTRIDGE_SPENT_ = 4333;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_UNEXPECTED_OMID_ = 4334;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CANT_DELETE_LAST_ITEM_ = 4335;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MESSAGE_EXCEEDS_MAX_SIZE_ = 4336;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_VOLUME_CONTAINS_SYS_FILES_ = 4337;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INDIGENOUS_TYPE_ = 4338;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NO_SUPPORTING_DRIVES_ = 4339;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLEANER_CARTRIDGE_INSTALLED_ = 4340;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IEPORT_FULL_ = 4341;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_FILE_OFFLINE_ = 4350;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_REMOTE_STORAGE_NOT_ACTIVE_ = 4351;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_REMOTE_STORAGE_MEDIA_ERROR_ = 4352;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NOT_A_REPARSE_POINT_ = 4390;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_REPARSE_ATTRIBUTE_CONFLICT_ = 4391;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_REPARSE_DATA_ = 4392;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_REPARSE_TAG_INVALID_ = 4393;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_REPARSE_TAG_MISMATCH_ = 4394;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_REPARSE_POINT_ENCOUNTERED_ = 4395;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_APP_DATA_NOT_FOUND_ = 4400;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_APP_DATA_EXPIRED_ = 4401;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_APP_DATA_CORRUPT_ = 4402;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_APP_DATA_LIMIT_EXCEEDED_ = 4403;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_APP_DATA_REBOOT_REQUIRED_ = 4404;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SECUREBOOT_ROLLBACK_DETECTED_ = 4420;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SECUREBOOT_POLICY_VIOLATION_ = 4421;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SECUREBOOT_INVALID_POLICY_ = 4422;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SECUREBOOT_POLICY_PUBLISHER_NOT_FOUND_ = 4423;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SECUREBOOT_POLICY_NOT_SIGNED_ = 4424;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SECUREBOOT_NOT_ENABLED_ = 4425;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SECUREBOOT_FILE_REPLACED_ = 4426;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SECUREBOOT_POLICY_NOT_AUTHORIZED_ = 4427;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SECUREBOOT_POLICY_UNKNOWN_ = 4428;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SECUREBOOT_POLICY_MISSING_ANTIROLLBACKVERSION_ = 4429;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SECUREBOOT_PLATFORM_ID_MISMATCH_ = 4430;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SECUREBOOT_POLICY_ROLLBACK_DETECTED_ = 4431;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SECUREBOOT_POLICY_UPGRADE_MISMATCH_ = 4432;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SECUREBOOT_REQUIRED_POLICY_FILE_MISSING_ = 4433;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SECUREBOOT_NOT_BASE_POLICY_ = 4434;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SECUREBOOT_NOT_SUPPLEMENTAL_POLICY_ = 4435;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_OFFLOAD_READ_FLT_NOT_SUPPORTED_ = 4440;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_OFFLOAD_WRITE_FLT_NOT_SUPPORTED_ = 4441;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_OFFLOAD_READ_FILE_NOT_SUPPORTED_ = 4442;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_OFFLOAD_WRITE_FILE_NOT_SUPPORTED_ = 4443;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_ALREADY_HAS_STREAM_ID_ = 4444;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SMR_GARBAGE_COLLECTION_REQUIRED_ = 4445;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_VOLUME_NOT_SIS_ENABLED_ = 4500;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SYSTEM_INTEGRITY_ROLLBACK_DETECTED_ = 4550;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SYSTEM_INTEGRITY_POLICY_VIOLATION_ = 4551;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SYSTEM_INTEGRITY_INVALID_POLICY_ = 4552;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SYSTEM_INTEGRITY_POLICY_NOT_SIGNED_ = 4553;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_VSM_NOT_INITIALIZED_ = 4560;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_VSM_DMA_PROTECTION_NOT_IN_USE_ = 4561;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PLATFORM_MANIFEST_NOT_AUTHORIZED_ = 4570;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PLATFORM_MANIFEST_INVALID_ = 4571;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PLATFORM_MANIFEST_FILE_NOT_AUTHORIZED_ = 4572;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PLATFORM_MANIFEST_CATALOG_NOT_AUTHORIZED_ = 4573;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PLATFORM_MANIFEST_BINARY_ID_NOT_FOUND_ = 4574;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PLATFORM_MANIFEST_NOT_ACTIVE_ = 4575;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PLATFORM_MANIFEST_NOT_SIGNED_ = 4576;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DEPENDENT_RESOURCE_EXISTS_ = 5001;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DEPENDENCY_NOT_FOUND_ = 5002;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DEPENDENCY_ALREADY_EXISTS_ = 5003;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_RESOURCE_NOT_ONLINE_ = 5004;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_HOST_NODE_NOT_AVAILABLE_ = 5005;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_RESOURCE_NOT_AVAILABLE_ = 5006;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_RESOURCE_NOT_FOUND_ = 5007;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SHUTDOWN_CLUSTER_ = 5008;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CANT_EVICT_ACTIVE_NODE_ = 5009;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_OBJECT_ALREADY_EXISTS_ = 5010;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_OBJECT_IN_LIST_ = 5011;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_GROUP_NOT_AVAILABLE_ = 5012;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_GROUP_NOT_FOUND_ = 5013;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_GROUP_NOT_ONLINE_ = 5014;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_HOST_NODE_NOT_RESOURCE_OWNER_ = 5015;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_HOST_NODE_NOT_GROUP_OWNER_ = 5016;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_RESMON_CREATE_FAILED_ = 5017;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_RESMON_ONLINE_FAILED_ = 5018;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_RESOURCE_ONLINE_ = 5019;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_QUORUM_RESOURCE_ = 5020;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NOT_QUORUM_CAPABLE_ = 5021;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_SHUTTING_DOWN_ = 5022;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_STATE_ = 5023;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_RESOURCE_PROPERTIES_STORED_ = 5024;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NOT_QUORUM_CLASS_ = 5025;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CORE_RESOURCE_ = 5026;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_QUORUM_RESOURCE_ONLINE_FAILED_ = 5027;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_QUORUMLOG_OPEN_FAILED_ = 5028;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTERLOG_CORRUPT_ = 5029;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTERLOG_RECORD_EXCEEDS_MAXSIZE_ = 5030;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTERLOG_EXCEEDS_MAXSIZE_ = 5031;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTERLOG_CHKPOINT_NOT_FOUND_ = 5032;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTERLOG_NOT_ENOUGH_SPACE_ = 5033;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_QUORUM_OWNER_ALIVE_ = 5034;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NETWORK_NOT_AVAILABLE_ = 5035;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NODE_NOT_AVAILABLE_ = 5036;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_ALL_NODES_NOT_AVAILABLE_ = 5037;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_RESOURCE_FAILED_ = 5038;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_INVALID_NODE_ = 5039;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_NODE_EXISTS_ = 5040;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_JOIN_IN_PROGRESS_ = 5041;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_NODE_NOT_FOUND_ = 5042;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_LOCAL_NODE_NOT_FOUND_ = 5043;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_NETWORK_EXISTS_ = 5044;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_NETWORK_NOT_FOUND_ = 5045;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_NETINTERFACE_EXISTS_ = 5046;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_NETINTERFACE_NOT_FOUND_ = 5047;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_INVALID_REQUEST_ = 5048;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_INVALID_NETWORK_PROVIDER_ = 5049;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_NODE_DOWN_ = 5050;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_NODE_UNREACHABLE_ = 5051;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_NODE_NOT_MEMBER_ = 5052;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_JOIN_NOT_IN_PROGRESS_ = 5053;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_INVALID_NETWORK_ = 5054;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_NODE_UP_ = 5056;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_IPADDR_IN_USE_ = 5057;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_NODE_NOT_PAUSED_ = 5058;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_NO_SECURITY_CONTEXT_ = 5059;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_NETWORK_NOT_INTERNAL_ = 5060;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_NODE_ALREADY_UP_ = 5061;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_NODE_ALREADY_DOWN_ = 5062;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_NETWORK_ALREADY_ONLINE_ = 5063;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_NETWORK_ALREADY_OFFLINE_ = 5064;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_NODE_ALREADY_MEMBER_ = 5065;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_LAST_INTERNAL_NETWORK_ = 5066;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_NETWORK_HAS_DEPENDENTS_ = 5067;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_OPERATION_ON_QUORUM_ = 5068;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DEPENDENCY_NOT_ALLOWED_ = 5069;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_NODE_PAUSED_ = 5070;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NODE_CANT_HOST_RESOURCE_ = 5071;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_NODE_NOT_READY_ = 5072;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_NODE_SHUTTING_DOWN_ = 5073;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_JOIN_ABORTED_ = 5074;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_INCOMPATIBLE_VERSIONS_ = 5075;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_MAXNUM_OF_RESOURCES_EXCEEDED_ = 5076;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_SYSTEM_CONFIG_CHANGED_ = 5077;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_RESOURCE_TYPE_NOT_FOUND_ = 5078;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_RESTYPE_NOT_SUPPORTED_ = 5079;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_RESNAME_NOT_FOUND_ = 5080;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_NO_RPC_PACKAGES_REGISTERED_ = 5081;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_OWNER_NOT_IN_PREFLIST_ = 5082;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_DATABASE_SEQMISMATCH_ = 5083;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_RESMON_INVALID_STATE_ = 5084;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_GUM_NOT_LOCKER_ = 5085;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_QUORUM_DISK_NOT_FOUND_ = 5086;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DATABASE_BACKUP_CORRUPT_ = 5087;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_NODE_ALREADY_HAS_DFS_ROOT_ = 5088;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_RESOURCE_PROPERTY_UNCHANGEABLE_ = 5089;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NO_ADMIN_ACCESS_POINT_ = 5090;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_MEMBERSHIP_INVALID_STATE_ = 5890;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_QUORUMLOG_NOT_FOUND_ = 5891;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_MEMBERSHIP_HALT_ = 5892;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_INSTANCE_ID_MISMATCH_ = 5893;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_NETWORK_NOT_FOUND_FOR_IP_ = 5894;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_PROPERTY_DATA_TYPE_MISMATCH_ = 5895;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_EVICT_WITHOUT_CLEANUP_ = 5896;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_PARAMETER_MISMATCH_ = 5897;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NODE_CANNOT_BE_CLUSTERED_ = 5898;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_WRONG_OS_VERSION_ = 5899;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_CANT_CREATE_DUP_CLUSTER_NAME_ = 5900;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSCFG_ALREADY_COMMITTED_ = 5901;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSCFG_ROLLBACK_FAILED_ = 5902;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSCFG_SYSTEM_DISK_DRIVE_LETTER_CONFLICT_ = 5903;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_OLD_VERSION_ = 5904;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_MISMATCHED_COMPUTER_ACCT_NAME_ = 5905;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_NO_NET_ADAPTERS_ = 5906;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_POISONED_ = 5907;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_GROUP_MOVING_ = 5908;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_RESOURCE_TYPE_BUSY_ = 5909;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_RESOURCE_CALL_TIMED_OUT_ = 5910;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_CLUSTER_IPV6_ADDRESS_ = 5911;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_INTERNAL_INVALID_FUNCTION_ = 5912;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_PARAMETER_OUT_OF_BOUNDS_ = 5913;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_PARTIAL_SEND_ = 5914;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_REGISTRY_INVALID_FUNCTION_ = 5915;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_INVALID_STRING_TERMINATION_ = 5916;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_INVALID_STRING_FORMAT_ = 5917;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_DATABASE_TRANSACTION_IN_PROGRESS_ = 5918;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_DATABASE_TRANSACTION_NOT_IN_PROGRESS_ = 5919;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_NULL_DATA_ = 5920;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_PARTIAL_READ_ = 5921;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_PARTIAL_WRITE_ = 5922;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_CANT_DESERIALIZE_DATA_ = 5923;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DEPENDENT_RESOURCE_PROPERTY_CONFLICT_ = 5924;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_NO_QUORUM_ = 5925;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_INVALID_IPV6_NETWORK_ = 5926;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_INVALID_IPV6_TUNNEL_NETWORK_ = 5927;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_QUORUM_NOT_ALLOWED_IN_THIS_GROUP_ = 5928;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DEPENDENCY_TREE_TOO_COMPLEX_ = 5929;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EXCEPTION_IN_RESOURCE_CALL_ = 5930;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_RHS_FAILED_INITIALIZATION_ = 5931;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_NOT_INSTALLED_ = 5932;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_RESOURCES_MUST_BE_ONLINE_ON_THE_SAME_NODE_ = 5933;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_MAX_NODES_IN_CLUSTER_ = 5934;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_TOO_MANY_NODES_ = 5935;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_OBJECT_ALREADY_USED_ = 5936;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NONCORE_GROUPS_FOUND_ = 5937;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_FILE_SHARE_RESOURCE_CONFLICT_ = 5938;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_EVICT_INVALID_REQUEST_ = 5939;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_SINGLETON_RESOURCE_ = 5940;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_GROUP_SINGLETON_RESOURCE_ = 5941;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_RESOURCE_PROVIDER_FAILED_ = 5942;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_RESOURCE_CONFIGURATION_ERROR_ = 5943;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_GROUP_BUSY_ = 5944;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_NOT_SHARED_VOLUME_ = 5945;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_INVALID_SECURITY_DESCRIPTOR_ = 5946;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_SHARED_VOLUMES_IN_USE_ = 5947;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_USE_SHARED_VOLUMES_API_ = 5948;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_BACKUP_IN_PROGRESS_ = 5949;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NON_CSV_PATH_ = 5950;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CSV_VOLUME_NOT_LOCAL_ = 5951;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_WATCHDOG_TERMINATING_ = 5952;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_RESOURCE_VETOED_MOVE_INCOMPATIBLE_NODES_ = 5953;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_INVALID_NODE_WEIGHT_ = 5954;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_RESOURCE_VETOED_CALL_ = 5955;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_RESMON_SYSTEM_RESOURCES_LACKING_ = 5956;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_RESOURCE_VETOED_MOVE_NOT_ENOUGH_RESOURCES_ON_DESTINATION_ = 5957;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_RESOURCE_VETOED_MOVE_NOT_ENOUGH_RESOURCES_ON_SOURCE_ = 5958;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_GROUP_QUEUED_ = 5959;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_RESOURCE_LOCKED_STATUS_ = 5960;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_SHARED_VOLUME_FAILOVER_NOT_ALLOWED_ = 5961;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_NODE_DRAIN_IN_PROGRESS_ = 5962;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_DISK_NOT_CONNECTED_ = 5963;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DISK_NOT_CSV_CAPABLE_ = 5964;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_RESOURCE_NOT_IN_AVAILABLE_STORAGE_ = 5965;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_SHARED_VOLUME_REDIRECTED_ = 5966;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_SHARED_VOLUME_NOT_REDIRECTED_ = 5967;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_CANNOT_RETURN_PROPERTIES_ = 5968;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_RESOURCE_CONTAINS_UNSUPPORTED_DIFF_AREA_FOR_SHARED_VOLUMES_ = 5969;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_RESOURCE_IS_IN_MAINTENANCE_MODE_ = 5970;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_AFFINITY_CONFLICT_ = 5971;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_RESOURCE_IS_REPLICA_VIRTUAL_MACHINE_ = 5972;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_UPGRADE_INCOMPATIBLE_VERSIONS_ = 5973;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_UPGRADE_FIX_QUORUM_NOT_SUPPORTED_ = 5974;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_UPGRADE_RESTART_REQUIRED_ = 5975;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_UPGRADE_IN_PROGRESS_ = 5976;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_UPGRADE_INCOMPLETE_ = 5977;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_NODE_IN_GRACE_PERIOD_ = 5978;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_CSV_IO_PAUSE_TIMEOUT_ = 5979;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NODE_NOT_ACTIVE_CLUSTER_MEMBER_ = 5980;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_RESOURCE_NOT_MONITORED_ = 5981;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_RESOURCE_DOES_NOT_SUPPORT_UNMONITORED_ = 5982;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_RESOURCE_IS_REPLICATED_ = 5983;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_NODE_ISOLATED_ = 5984;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_NODE_QUARANTINED_ = 5985;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_DATABASE_UPDATE_CONDITION_FAILED_ = 5986;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_SPACE_DEGRADED_ = 5987;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_TOKEN_DELEGATION_NOT_SUPPORTED_ = 5988;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_CSV_INVALID_HANDLE_ = 5989;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_CSV_SUPPORTED_ONLY_ON_COORDINATOR_ = 5990;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_GROUPSET_NOT_AVAILABLE_ = 5991;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_GROUPSET_NOT_FOUND_ = 5992;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_GROUPSET_CANT_PROVIDE_ = 5993;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_FAULT_DOMAIN_PARENT_NOT_FOUND_ = 5994;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_FAULT_DOMAIN_INVALID_HIERARCHY_ = 5995;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_FAULT_DOMAIN_FAILED_S2D_VALIDATION_ = 5996;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_FAULT_DOMAIN_S2D_CONNECTIVITY_LOSS_ = 5997;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTER_INVALID_INFRASTRUCTURE_FILESERVER_NAME_ = 5998;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CLUSTERSET_MANAGEMENT_CLUSTER_UNREACHABLE_ = 5999;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_ENCRYPTION_FAILED_ = 6000;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DECRYPTION_FAILED_ = 6001;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_FILE_ENCRYPTED_ = 6002;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NO_RECOVERY_POLICY_ = 6003;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NO_EFS_ = 6004;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_WRONG_EFS_ = 6005;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NO_USER_KEYS_ = 6006;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_FILE_NOT_ENCRYPTED_ = 6007;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NOT_EXPORT_FORMAT_ = 6008;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_FILE_READ_ONLY_ = 6009;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DIR_EFS_DISALLOWED_ = 6010;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EFS_SERVER_NOT_TRUSTED_ = 6011;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_BAD_RECOVERY_POLICY_ = 6012;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EFS_ALG_BLOB_TOO_BIG_ = 6013;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_VOLUME_NOT_SUPPORT_EFS_ = 6014;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EFS_DISABLED_ = 6015;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EFS_VERSION_NOT_SUPPORT_ = 6016;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CS_ENCRYPTION_INVALID_SERVER_RESPONSE_ = 6017;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CS_ENCRYPTION_UNSUPPORTED_SERVER_ = 6018;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CS_ENCRYPTION_EXISTING_ENCRYPTED_FILE_ = 6019;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CS_ENCRYPTION_NEW_ENCRYPTED_FILE_ = 6020;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CS_ENCRYPTION_FILE_NOT_CSE_ = 6021;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_ENCRYPTION_POLICY_DENIES_OPERATION_ = 6022;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NO_BROWSER_SERVERS_FOUND_ = 6118;
BOOST_CONSTEXPR_OR_CONST DWORD_ SCHED_E_SERVICE_NOT_LOCALSYSTEM_ = 6200;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LOG_SECTOR_INVALID_ = 6600;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LOG_SECTOR_PARITY_INVALID_ = 6601;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LOG_SECTOR_REMAPPED_ = 6602;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LOG_BLOCK_INCOMPLETE_ = 6603;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LOG_INVALID_RANGE_ = 6604;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LOG_BLOCKS_EXHAUSTED_ = 6605;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LOG_READ_CONTEXT_INVALID_ = 6606;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LOG_RESTART_INVALID_ = 6607;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LOG_BLOCK_VERSION_ = 6608;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LOG_BLOCK_INVALID_ = 6609;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LOG_READ_MODE_INVALID_ = 6610;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LOG_NO_RESTART_ = 6611;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LOG_METADATA_CORRUPT_ = 6612;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LOG_METADATA_INVALID_ = 6613;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LOG_METADATA_INCONSISTENT_ = 6614;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LOG_RESERVATION_INVALID_ = 6615;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LOG_CANT_DELETE_ = 6616;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LOG_CONTAINER_LIMIT_EXCEEDED_ = 6617;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LOG_START_OF_LOG_ = 6618;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LOG_POLICY_ALREADY_INSTALLED_ = 6619;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LOG_POLICY_NOT_INSTALLED_ = 6620;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LOG_POLICY_INVALID_ = 6621;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LOG_POLICY_CONFLICT_ = 6622;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LOG_PINNED_ARCHIVE_TAIL_ = 6623;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LOG_RECORD_NONEXISTENT_ = 6624;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LOG_RECORDS_RESERVED_INVALID_ = 6625;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LOG_SPACE_RESERVED_INVALID_ = 6626;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LOG_TAIL_INVALID_ = 6627;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LOG_FULL_ = 6628;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_COULD_NOT_RESIZE_LOG_ = 6629;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LOG_MULTIPLEXED_ = 6630;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LOG_DEDICATED_ = 6631;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LOG_ARCHIVE_NOT_IN_PROGRESS_ = 6632;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LOG_ARCHIVE_IN_PROGRESS_ = 6633;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LOG_EPHEMERAL_ = 6634;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LOG_NOT_ENOUGH_CONTAINERS_ = 6635;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LOG_CLIENT_ALREADY_REGISTERED_ = 6636;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LOG_CLIENT_NOT_REGISTERED_ = 6637;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LOG_FULL_HANDLER_IN_PROGRESS_ = 6638;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LOG_CONTAINER_READ_FAILED_ = 6639;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LOG_CONTAINER_WRITE_FAILED_ = 6640;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LOG_CONTAINER_OPEN_FAILED_ = 6641;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LOG_CONTAINER_STATE_INVALID_ = 6642;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LOG_STATE_INVALID_ = 6643;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LOG_PINNED_ = 6644;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LOG_METADATA_FLUSH_FAILED_ = 6645;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LOG_INCONSISTENT_SECURITY_ = 6646;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LOG_APPENDED_FLUSH_FAILED_ = 6647;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LOG_PINNED_RESERVATION_ = 6648;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_TRANSACTION_ = 6700;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_TRANSACTION_NOT_ACTIVE_ = 6701;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_TRANSACTION_REQUEST_NOT_VALID_ = 6702;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_TRANSACTION_NOT_REQUESTED_ = 6703;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_TRANSACTION_ALREADY_ABORTED_ = 6704;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_TRANSACTION_ALREADY_COMMITTED_ = 6705;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_TM_INITIALIZATION_FAILED_ = 6706;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_RESOURCEMANAGER_READ_ONLY_ = 6707;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_TRANSACTION_NOT_JOINED_ = 6708;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_TRANSACTION_SUPERIOR_EXISTS_ = 6709;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CRM_PROTOCOL_ALREADY_EXISTS_ = 6710;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_TRANSACTION_PROPAGATION_FAILED_ = 6711;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CRM_PROTOCOL_NOT_FOUND_ = 6712;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_TRANSACTION_INVALID_MARSHALL_BUFFER_ = 6713;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CURRENT_TRANSACTION_NOT_VALID_ = 6714;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_TRANSACTION_NOT_FOUND_ = 6715;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_RESOURCEMANAGER_NOT_FOUND_ = 6716;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_ENLISTMENT_NOT_FOUND_ = 6717;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_TRANSACTIONMANAGER_NOT_FOUND_ = 6718;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_TRANSACTIONMANAGER_NOT_ONLINE_ = 6719;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_TRANSACTIONMANAGER_RECOVERY_NAME_COLLISION_ = 6720;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_TRANSACTION_NOT_ROOT_ = 6721;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_TRANSACTION_OBJECT_EXPIRED_ = 6722;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_TRANSACTION_RESPONSE_NOT_ENLISTED_ = 6723;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_TRANSACTION_RECORD_TOO_LONG_ = 6724;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IMPLICIT_TRANSACTION_NOT_SUPPORTED_ = 6725;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_TRANSACTION_INTEGRITY_VIOLATED_ = 6726;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_TRANSACTIONMANAGER_IDENTITY_MISMATCH_ = 6727;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_RM_CANNOT_BE_FROZEN_FOR_SNAPSHOT_ = 6728;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_TRANSACTION_MUST_WRITETHROUGH_ = 6729;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_TRANSACTION_NO_SUPERIOR_ = 6730;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_HEURISTIC_DAMAGE_POSSIBLE_ = 6731;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_TRANSACTIONAL_CONFLICT_ = 6800;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_RM_NOT_ACTIVE_ = 6801;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_RM_METADATA_CORRUPT_ = 6802;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DIRECTORY_NOT_RM_ = 6803;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_TRANSACTIONS_UNSUPPORTED_REMOTE_ = 6805;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LOG_RESIZE_INVALID_SIZE_ = 6806;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_OBJECT_NO_LONGER_EXISTS_ = 6807;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_STREAM_MINIVERSION_NOT_FOUND_ = 6808;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_STREAM_MINIVERSION_NOT_VALID_ = 6809;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MINIVERSION_INACCESSIBLE_FROM_SPECIFIED_TRANSACTION_ = 6810;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CANT_OPEN_MINIVERSION_WITH_MODIFY_INTENT_ = 6811;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CANT_CREATE_MORE_STREAM_MINIVERSIONS_ = 6812;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_REMOTE_FILE_VERSION_MISMATCH_ = 6814;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_HANDLE_NO_LONGER_VALID_ = 6815;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NO_TXF_METADATA_ = 6816;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LOG_CORRUPTION_DETECTED_ = 6817;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CANT_RECOVER_WITH_HANDLE_OPEN_ = 6818;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_RM_DISCONNECTED_ = 6819;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_ENLISTMENT_NOT_SUPERIOR_ = 6820;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_RECOVERY_NOT_NEEDED_ = 6821;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_RM_ALREADY_STARTED_ = 6822;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_FILE_IDENTITY_NOT_PERSISTENT_ = 6823;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CANT_BREAK_TRANSACTIONAL_DEPENDENCY_ = 6824;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CANT_CROSS_RM_BOUNDARY_ = 6825;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_TXF_DIR_NOT_EMPTY_ = 6826;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INDOUBT_TRANSACTIONS_EXIST_ = 6827;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_TM_VOLATILE_ = 6828;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_ROLLBACK_TIMER_EXPIRED_ = 6829;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_TXF_ATTRIBUTE_CORRUPT_ = 6830;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EFS_NOT_ALLOWED_IN_TRANSACTION_ = 6831;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_TRANSACTIONAL_OPEN_NOT_ALLOWED_ = 6832;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_LOG_GROWTH_FAILED_ = 6833;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_TRANSACTED_MAPPING_UNSUPPORTED_REMOTE_ = 6834;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_TXF_METADATA_ALREADY_PRESENT_ = 6835;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_TRANSACTION_SCOPE_CALLBACKS_NOT_SET_ = 6836;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_TRANSACTION_REQUIRED_PROMOTION_ = 6837;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CANNOT_EXECUTE_FILE_IN_TRANSACTION_ = 6838;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_TRANSACTIONS_NOT_FROZEN_ = 6839;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_TRANSACTION_FREEZE_IN_PROGRESS_ = 6840;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NOT_SNAPSHOT_VOLUME_ = 6841;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NO_SAVEPOINT_WITH_OPEN_FILES_ = 6842;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DATA_LOST_REPAIR_ = 6843;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SPARSE_NOT_ALLOWED_IN_TRANSACTION_ = 6844;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_TM_IDENTITY_MISMATCH_ = 6845;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_FLOATED_SECTION_ = 6846;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CANNOT_ACCEPT_TRANSACTED_WORK_ = 6847;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CANNOT_ABORT_TRANSACTIONS_ = 6848;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_BAD_CLUSTERS_ = 6849;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_COMPRESSION_NOT_ALLOWED_IN_TRANSACTION_ = 6850;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_VOLUME_DIRTY_ = 6851;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NO_LINK_TRACKING_IN_TRANSACTION_ = 6852;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_OPERATION_NOT_SUPPORTED_IN_TRANSACTION_ = 6853;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EXPIRED_HANDLE_ = 6854;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_TRANSACTION_NOT_ENLISTED_ = 6855;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CTX_WINSTATION_NAME_INVALID_ = 7001;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CTX_INVALID_PD_ = 7002;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CTX_PD_NOT_FOUND_ = 7003;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CTX_WD_NOT_FOUND_ = 7004;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CTX_CANNOT_MAKE_EVENTLOG_ENTRY_ = 7005;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CTX_SERVICE_NAME_COLLISION_ = 7006;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CTX_CLOSE_PENDING_ = 7007;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CTX_NO_OUTBUF_ = 7008;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CTX_MODEM_INF_NOT_FOUND_ = 7009;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CTX_INVALID_MODEMNAME_ = 7010;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CTX_MODEM_RESPONSE_ERROR_ = 7011;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CTX_MODEM_RESPONSE_TIMEOUT_ = 7012;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CTX_MODEM_RESPONSE_NO_CARRIER_ = 7013;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CTX_MODEM_RESPONSE_NO_DIALTONE_ = 7014;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CTX_MODEM_RESPONSE_BUSY_ = 7015;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CTX_MODEM_RESPONSE_VOICE_ = 7016;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CTX_TD_ERROR_ = 7017;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CTX_WINSTATION_NOT_FOUND_ = 7022;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CTX_WINSTATION_ALREADY_EXISTS_ = 7023;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CTX_WINSTATION_BUSY_ = 7024;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CTX_BAD_VIDEO_MODE_ = 7025;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CTX_GRAPHICS_INVALID_ = 7035;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CTX_LOGON_DISABLED_ = 7037;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CTX_NOT_CONSOLE_ = 7038;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CTX_CLIENT_QUERY_TIMEOUT_ = 7040;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CTX_CONSOLE_DISCONNECT_ = 7041;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CTX_CONSOLE_CONNECT_ = 7042;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CTX_SHADOW_DENIED_ = 7044;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CTX_WINSTATION_ACCESS_DENIED_ = 7045;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CTX_INVALID_WD_ = 7049;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CTX_SHADOW_INVALID_ = 7050;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CTX_SHADOW_DISABLED_ = 7051;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CTX_CLIENT_LICENSE_IN_USE_ = 7052;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CTX_CLIENT_LICENSE_NOT_SET_ = 7053;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CTX_LICENSE_NOT_AVAILABLE_ = 7054;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CTX_LICENSE_CLIENT_INVALID_ = 7055;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CTX_LICENSE_EXPIRED_ = 7056;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CTX_SHADOW_NOT_RUNNING_ = 7057;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CTX_SHADOW_ENDED_BY_MODE_CHANGE_ = 7058;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_ACTIVATION_COUNT_EXCEEDED_ = 7059;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CTX_WINSTATIONS_DISABLED_ = 7060;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CTX_ENCRYPTION_LEVEL_REQUIRED_ = 7061;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CTX_SESSION_IN_USE_ = 7062;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CTX_NO_FORCE_LOGOFF_ = 7063;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CTX_ACCOUNT_RESTRICTION_ = 7064;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_RDP_PROTOCOL_ERROR_ = 7065;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CTX_CDM_CONNECT_ = 7066;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CTX_CDM_DISCONNECT_ = 7067;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CTX_SECURITY_LAYER_ERROR_ = 7068;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_TS_INCOMPATIBLE_SESSIONS_ = 7069;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_TS_VIDEO_SUBSYSTEM_ERROR_ = 7070;
BOOST_CONSTEXPR_OR_CONST DWORD_ FRS_ERR_INVALID_API_SEQUENCE_ = 8001;
BOOST_CONSTEXPR_OR_CONST DWORD_ FRS_ERR_STARTING_SERVICE_ = 8002;
BOOST_CONSTEXPR_OR_CONST DWORD_ FRS_ERR_STOPPING_SERVICE_ = 8003;
BOOST_CONSTEXPR_OR_CONST DWORD_ FRS_ERR_INTERNAL_API_ = 8004;
BOOST_CONSTEXPR_OR_CONST DWORD_ FRS_ERR_INTERNAL_ = 8005;
BOOST_CONSTEXPR_OR_CONST DWORD_ FRS_ERR_SERVICE_COMM_ = 8006;
BOOST_CONSTEXPR_OR_CONST DWORD_ FRS_ERR_INSUFFICIENT_PRIV_ = 8007;
BOOST_CONSTEXPR_OR_CONST DWORD_ FRS_ERR_AUTHENTICATION_ = 8008;
BOOST_CONSTEXPR_OR_CONST DWORD_ FRS_ERR_PARENT_INSUFFICIENT_PRIV_ = 8009;
BOOST_CONSTEXPR_OR_CONST DWORD_ FRS_ERR_PARENT_AUTHENTICATION_ = 8010;
BOOST_CONSTEXPR_OR_CONST DWORD_ FRS_ERR_CHILD_TO_PARENT_COMM_ = 8011;
BOOST_CONSTEXPR_OR_CONST DWORD_ FRS_ERR_PARENT_TO_CHILD_COMM_ = 8012;
BOOST_CONSTEXPR_OR_CONST DWORD_ FRS_ERR_SYSVOL_POPULATE_ = 8013;
BOOST_CONSTEXPR_OR_CONST DWORD_ FRS_ERR_SYSVOL_POPULATE_TIMEOUT_ = 8014;
BOOST_CONSTEXPR_OR_CONST DWORD_ FRS_ERR_SYSVOL_IS_BUSY_ = 8015;
BOOST_CONSTEXPR_OR_CONST DWORD_ FRS_ERR_SYSVOL_DEMOTE_ = 8016;
BOOST_CONSTEXPR_OR_CONST DWORD_ FRS_ERR_INVALID_SERVICE_PARAMETER_ = 8017;
BOOST_CONSTEXPR_OR_CONST DWORD_ DS_S_SUCCESS_ = NO_ERROR_;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_NOT_INSTALLED_ = 8200;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_MEMBERSHIP_EVALUATED_LOCALLY_ = 8201;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_NO_ATTRIBUTE_OR_VALUE_ = 8202;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_INVALID_ATTRIBUTE_SYNTAX_ = 8203;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_ATTRIBUTE_TYPE_UNDEFINED_ = 8204;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_ATTRIBUTE_OR_VALUE_EXISTS_ = 8205;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_BUSY_ = 8206;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_UNAVAILABLE_ = 8207;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_NO_RIDS_ALLOCATED_ = 8208;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_NO_MORE_RIDS_ = 8209;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_INCORRECT_ROLE_OWNER_ = 8210;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_RIDMGR_INIT_ERROR_ = 8211;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_OBJ_CLASS_VIOLATION_ = 8212;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_CANT_ON_NON_LEAF_ = 8213;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_CANT_ON_RDN_ = 8214;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_CANT_MOD_OBJ_CLASS_ = 8215;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_CROSS_DOM_MOVE_ERROR_ = 8216;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_GC_NOT_AVAILABLE_ = 8217;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SHARED_POLICY_ = 8218;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_POLICY_OBJECT_NOT_FOUND_ = 8219;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_POLICY_ONLY_IN_DS_ = 8220;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PROMOTION_ACTIVE_ = 8221;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NO_PROMOTION_ACTIVE_ = 8222;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_OPERATIONS_ERROR_ = 8224;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_PROTOCOL_ERROR_ = 8225;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_TIMELIMIT_EXCEEDED_ = 8226;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_SIZELIMIT_EXCEEDED_ = 8227;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_ADMIN_LIMIT_EXCEEDED_ = 8228;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_COMPARE_FALSE_ = 8229;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_COMPARE_TRUE_ = 8230;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_AUTH_METHOD_NOT_SUPPORTED_ = 8231;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_STRONG_AUTH_REQUIRED_ = 8232;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_INAPPROPRIATE_AUTH_ = 8233;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_AUTH_UNKNOWN_ = 8234;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_REFERRAL_ = 8235;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_UNAVAILABLE_CRIT_EXTENSION_ = 8236;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_CONFIDENTIALITY_REQUIRED_ = 8237;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_INAPPROPRIATE_MATCHING_ = 8238;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_CONSTRAINT_VIOLATION_ = 8239;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_NO_SUCH_OBJECT_ = 8240;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_ALIAS_PROBLEM_ = 8241;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_INVALID_DN_SYNTAX_ = 8242;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_IS_LEAF_ = 8243;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_ALIAS_DEREF_PROBLEM_ = 8244;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_UNWILLING_TO_PERFORM_ = 8245;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_LOOP_DETECT_ = 8246;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_NAMING_VIOLATION_ = 8247;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_OBJECT_RESULTS_TOO_LARGE_ = 8248;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_AFFECTS_MULTIPLE_DSAS_ = 8249;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_SERVER_DOWN_ = 8250;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_LOCAL_ERROR_ = 8251;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_ENCODING_ERROR_ = 8252;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_DECODING_ERROR_ = 8253;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_FILTER_UNKNOWN_ = 8254;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_PARAM_ERROR_ = 8255;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_NOT_SUPPORTED_ = 8256;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_NO_RESULTS_RETURNED_ = 8257;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_CONTROL_NOT_FOUND_ = 8258;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_CLIENT_LOOP_ = 8259;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_REFERRAL_LIMIT_EXCEEDED_ = 8260;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_SORT_CONTROL_MISSING_ = 8261;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_OFFSET_RANGE_ERROR_ = 8262;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_RIDMGR_DISABLED_ = 8263;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_ROOT_MUST_BE_NC_ = 8301;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_ADD_REPLICA_INHIBITED_ = 8302;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_ATT_NOT_DEF_IN_SCHEMA_ = 8303;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_MAX_OBJ_SIZE_EXCEEDED_ = 8304;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_OBJ_STRING_NAME_EXISTS_ = 8305;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_NO_RDN_DEFINED_IN_SCHEMA_ = 8306;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_RDN_DOESNT_MATCH_SCHEMA_ = 8307;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_NO_REQUESTED_ATTS_FOUND_ = 8308;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_USER_BUFFER_TO_SMALL_ = 8309;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_ATT_IS_NOT_ON_OBJ_ = 8310;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_ILLEGAL_MOD_OPERATION_ = 8311;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_OBJ_TOO_LARGE_ = 8312;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_BAD_INSTANCE_TYPE_ = 8313;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_MASTERDSA_REQUIRED_ = 8314;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_OBJECT_CLASS_REQUIRED_ = 8315;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_MISSING_REQUIRED_ATT_ = 8316;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_ATT_NOT_DEF_FOR_CLASS_ = 8317;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_ATT_ALREADY_EXISTS_ = 8318;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_CANT_ADD_ATT_VALUES_ = 8320;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_SINGLE_VALUE_CONSTRAINT_ = 8321;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_RANGE_CONSTRAINT_ = 8322;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_ATT_VAL_ALREADY_EXISTS_ = 8323;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_CANT_REM_MISSING_ATT_ = 8324;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_CANT_REM_MISSING_ATT_VAL_ = 8325;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_ROOT_CANT_BE_SUBREF_ = 8326;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_NO_CHAINING_ = 8327;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_NO_CHAINED_EVAL_ = 8328;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_NO_PARENT_OBJECT_ = 8329;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_PARENT_IS_AN_ALIAS_ = 8330;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_CANT_MIX_MASTER_AND_REPS_ = 8331;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_CHILDREN_EXIST_ = 8332;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_OBJ_NOT_FOUND_ = 8333;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_ALIASED_OBJ_MISSING_ = 8334;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_BAD_NAME_SYNTAX_ = 8335;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_ALIAS_POINTS_TO_ALIAS_ = 8336;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_CANT_DEREF_ALIAS_ = 8337;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_OUT_OF_SCOPE_ = 8338;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_OBJECT_BEING_REMOVED_ = 8339;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_CANT_DELETE_DSA_OBJ_ = 8340;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_GENERIC_ERROR_ = 8341;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_DSA_MUST_BE_INT_MASTER_ = 8342;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_CLASS_NOT_DSA_ = 8343;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_INSUFF_ACCESS_RIGHTS_ = 8344;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_ILLEGAL_SUPERIOR_ = 8345;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_ATTRIBUTE_OWNED_BY_SAM_ = 8346;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_NAME_TOO_MANY_PARTS_ = 8347;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_NAME_TOO_LONG_ = 8348;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_NAME_VALUE_TOO_LONG_ = 8349;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_NAME_UNPARSEABLE_ = 8350;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_NAME_TYPE_UNKNOWN_ = 8351;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_NOT_AN_OBJECT_ = 8352;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_SEC_DESC_TOO_SHORT_ = 8353;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_SEC_DESC_INVALID_ = 8354;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_NO_DELETED_NAME_ = 8355;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_SUBREF_MUST_HAVE_PARENT_ = 8356;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_NCNAME_MUST_BE_NC_ = 8357;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_CANT_ADD_SYSTEM_ONLY_ = 8358;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_CLASS_MUST_BE_CONCRETE_ = 8359;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_INVALID_DMD_ = 8360;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_OBJ_GUID_EXISTS_ = 8361;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_NOT_ON_BACKLINK_ = 8362;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_NO_CROSSREF_FOR_NC_ = 8363;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_SHUTTING_DOWN_ = 8364;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_UNKNOWN_OPERATION_ = 8365;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_INVALID_ROLE_OWNER_ = 8366;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_COULDNT_CONTACT_FSMO_ = 8367;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_CROSS_NC_DN_RENAME_ = 8368;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_CANT_MOD_SYSTEM_ONLY_ = 8369;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_REPLICATOR_ONLY_ = 8370;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_OBJ_CLASS_NOT_DEFINED_ = 8371;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_OBJ_CLASS_NOT_SUBCLASS_ = 8372;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_NAME_REFERENCE_INVALID_ = 8373;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_CROSS_REF_EXISTS_ = 8374;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_CANT_DEL_MASTER_CROSSREF_ = 8375;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_SUBTREE_NOTIFY_NOT_NC_HEAD_ = 8376;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_NOTIFY_FILTER_TOO_COMPLEX_ = 8377;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_DUP_RDN_ = 8378;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_DUP_OID_ = 8379;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_DUP_MAPI_ID_ = 8380;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_DUP_SCHEMA_ID_GUID_ = 8381;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_DUP_LDAP_DISPLAY_NAME_ = 8382;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_SEMANTIC_ATT_TEST_ = 8383;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_SYNTAX_MISMATCH_ = 8384;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_EXISTS_IN_MUST_HAVE_ = 8385;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_EXISTS_IN_MAY_HAVE_ = 8386;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_NONEXISTENT_MAY_HAVE_ = 8387;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_NONEXISTENT_MUST_HAVE_ = 8388;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_AUX_CLS_TEST_FAIL_ = 8389;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_NONEXISTENT_POSS_SUP_ = 8390;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_SUB_CLS_TEST_FAIL_ = 8391;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_BAD_RDN_ATT_ID_SYNTAX_ = 8392;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_EXISTS_IN_AUX_CLS_ = 8393;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_EXISTS_IN_SUB_CLS_ = 8394;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_EXISTS_IN_POSS_SUP_ = 8395;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_RECALCSCHEMA_FAILED_ = 8396;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_TREE_DELETE_NOT_FINISHED_ = 8397;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_CANT_DELETE_ = 8398;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_ATT_SCHEMA_REQ_ID_ = 8399;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_BAD_ATT_SCHEMA_SYNTAX_ = 8400;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_CANT_CACHE_ATT_ = 8401;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_CANT_CACHE_CLASS_ = 8402;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_CANT_REMOVE_ATT_CACHE_ = 8403;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_CANT_REMOVE_CLASS_CACHE_ = 8404;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_CANT_RETRIEVE_DN_ = 8405;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_MISSING_SUPREF_ = 8406;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_CANT_RETRIEVE_INSTANCE_ = 8407;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_CODE_INCONSISTENCY_ = 8408;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_DATABASE_ERROR_ = 8409;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_GOVERNSID_MISSING_ = 8410;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_MISSING_EXPECTED_ATT_ = 8411;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_NCNAME_MISSING_CR_REF_ = 8412;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_SECURITY_CHECKING_ERROR_ = 8413;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_SCHEMA_NOT_LOADED_ = 8414;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_SCHEMA_ALLOC_FAILED_ = 8415;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_ATT_SCHEMA_REQ_SYNTAX_ = 8416;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_GCVERIFY_ERROR_ = 8417;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_DRA_SCHEMA_MISMATCH_ = 8418;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_CANT_FIND_DSA_OBJ_ = 8419;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_CANT_FIND_EXPECTED_NC_ = 8420;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_CANT_FIND_NC_IN_CACHE_ = 8421;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_CANT_RETRIEVE_CHILD_ = 8422;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_SECURITY_ILLEGAL_MODIFY_ = 8423;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_CANT_REPLACE_HIDDEN_REC_ = 8424;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_BAD_HIERARCHY_FILE_ = 8425;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_BUILD_HIERARCHY_TABLE_FAILED_ = 8426;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_CONFIG_PARAM_MISSING_ = 8427;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_COUNTING_AB_INDICES_FAILED_ = 8428;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_HIERARCHY_TABLE_MALLOC_FAILED_ = 8429;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_INTERNAL_FAILURE_ = 8430;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_UNKNOWN_ERROR_ = 8431;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_ROOT_REQUIRES_CLASS_TOP_ = 8432;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_REFUSING_FSMO_ROLES_ = 8433;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_MISSING_FSMO_SETTINGS_ = 8434;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_UNABLE_TO_SURRENDER_ROLES_ = 8435;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_DRA_GENERIC_ = 8436;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_DRA_INVALID_PARAMETER_ = 8437;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_DRA_BUSY_ = 8438;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_DRA_BAD_DN_ = 8439;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_DRA_BAD_NC_ = 8440;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_DRA_DN_EXISTS_ = 8441;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_DRA_INTERNAL_ERROR_ = 8442;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_DRA_INCONSISTENT_DIT_ = 8443;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_DRA_CONNECTION_FAILED_ = 8444;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_DRA_BAD_INSTANCE_TYPE_ = 8445;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_DRA_OUT_OF_MEM_ = 8446;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_DRA_MAIL_PROBLEM_ = 8447;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_DRA_REF_ALREADY_EXISTS_ = 8448;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_DRA_REF_NOT_FOUND_ = 8449;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_DRA_OBJ_IS_REP_SOURCE_ = 8450;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_DRA_DB_ERROR_ = 8451;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_DRA_NO_REPLICA_ = 8452;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_DRA_ACCESS_DENIED_ = 8453;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_DRA_NOT_SUPPORTED_ = 8454;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_DRA_RPC_CANCELLED_ = 8455;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_DRA_SOURCE_DISABLED_ = 8456;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_DRA_SINK_DISABLED_ = 8457;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_DRA_NAME_COLLISION_ = 8458;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_DRA_SOURCE_REINSTALLED_ = 8459;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_DRA_MISSING_PARENT_ = 8460;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_DRA_PREEMPTED_ = 8461;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_DRA_ABANDON_SYNC_ = 8462;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_DRA_SHUTDOWN_ = 8463;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_DRA_INCOMPATIBLE_PARTIAL_SET_ = 8464;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_DRA_SOURCE_IS_PARTIAL_REPLICA_ = 8465;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_DRA_EXTN_CONNECTION_FAILED_ = 8466;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_INSTALL_SCHEMA_MISMATCH_ = 8467;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_DUP_LINK_ID_ = 8468;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_NAME_ERROR_RESOLVING_ = 8469;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_NAME_ERROR_NOT_FOUND_ = 8470;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_NAME_ERROR_NOT_UNIQUE_ = 8471;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_NAME_ERROR_NO_MAPPING_ = 8472;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_NAME_ERROR_DOMAIN_ONLY_ = 8473;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_NAME_ERROR_NO_SYNTACTICAL_MAPPING_ = 8474;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_CONSTRUCTED_ATT_MOD_ = 8475;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_WRONG_OM_OBJ_CLASS_ = 8476;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_DRA_REPL_PENDING_ = 8477;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_DS_REQUIRED_ = 8478;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_INVALID_LDAP_DISPLAY_NAME_ = 8479;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_NON_BASE_SEARCH_ = 8480;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_CANT_RETRIEVE_ATTS_ = 8481;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_BACKLINK_WITHOUT_LINK_ = 8482;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_EPOCH_MISMATCH_ = 8483;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_SRC_NAME_MISMATCH_ = 8484;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_SRC_AND_DST_NC_IDENTICAL_ = 8485;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_DST_NC_MISMATCH_ = 8486;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_NOT_AUTHORITIVE_FOR_DST_NC_ = 8487;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_SRC_GUID_MISMATCH_ = 8488;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_CANT_MOVE_DELETED_OBJECT_ = 8489;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_PDC_OPERATION_IN_PROGRESS_ = 8490;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_CROSS_DOMAIN_CLEANUP_REQD_ = 8491;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_ILLEGAL_XDOM_MOVE_OPERATION_ = 8492;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_CANT_WITH_ACCT_GROUP_MEMBERSHPS_ = 8493;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_NC_MUST_HAVE_NC_PARENT_ = 8494;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_CR_IMPOSSIBLE_TO_VALIDATE_ = 8495;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_DST_DOMAIN_NOT_NATIVE_ = 8496;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_MISSING_INFRASTRUCTURE_CONTAINER_ = 8497;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_CANT_MOVE_ACCOUNT_GROUP_ = 8498;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_CANT_MOVE_RESOURCE_GROUP_ = 8499;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_INVALID_SEARCH_FLAG_ = 8500;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_NO_TREE_DELETE_ABOVE_NC_ = 8501;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_COULDNT_LOCK_TREE_FOR_DELETE_ = 8502;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_COULDNT_IDENTIFY_OBJECTS_FOR_TREE_DELETE_ = 8503;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_SAM_INIT_FAILURE_ = 8504;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_SENSITIVE_GROUP_VIOLATION_ = 8505;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_CANT_MOD_PRIMARYGROUPID_ = 8506;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_ILLEGAL_BASE_SCHEMA_MOD_ = 8507;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_NONSAFE_SCHEMA_CHANGE_ = 8508;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_SCHEMA_UPDATE_DISALLOWED_ = 8509;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_CANT_CREATE_UNDER_SCHEMA_ = 8510;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_INSTALL_NO_SRC_SCH_VERSION_ = 8511;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_INSTALL_NO_SCH_VERSION_IN_INIFILE_ = 8512;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_INVALID_GROUP_TYPE_ = 8513;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_NO_NEST_GLOBALGROUP_IN_MIXEDDOMAIN_ = 8514;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_NO_NEST_LOCALGROUP_IN_MIXEDDOMAIN_ = 8515;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_GLOBAL_CANT_HAVE_LOCAL_MEMBER_ = 8516;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_GLOBAL_CANT_HAVE_UNIVERSAL_MEMBER_ = 8517;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_UNIVERSAL_CANT_HAVE_LOCAL_MEMBER_ = 8518;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_GLOBAL_CANT_HAVE_CROSSDOMAIN_MEMBER_ = 8519;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_LOCAL_CANT_HAVE_CROSSDOMAIN_LOCAL_MEMBER_ = 8520;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_HAVE_PRIMARY_MEMBERS_ = 8521;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_STRING_SD_CONVERSION_FAILED_ = 8522;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_NAMING_MASTER_GC_ = 8523;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_DNS_LOOKUP_FAILURE_ = 8524;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_COULDNT_UPDATE_SPNS_ = 8525;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_CANT_RETRIEVE_SD_ = 8526;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_KEY_NOT_UNIQUE_ = 8527;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_WRONG_LINKED_ATT_SYNTAX_ = 8528;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_SAM_NEED_BOOTKEY_PASSWORD_ = 8529;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_SAM_NEED_BOOTKEY_FLOPPY_ = 8530;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_CANT_START_ = 8531;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_INIT_FAILURE_ = 8532;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_NO_PKT_PRIVACY_ON_CONNECTION_ = 8533;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_SOURCE_DOMAIN_IN_FOREST_ = 8534;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_DESTINATION_DOMAIN_NOT_IN_FOREST_ = 8535;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_DESTINATION_AUDITING_NOT_ENABLED_ = 8536;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_CANT_FIND_DC_FOR_SRC_DOMAIN_ = 8537;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_SRC_OBJ_NOT_GROUP_OR_USER_ = 8538;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_SRC_SID_EXISTS_IN_FOREST_ = 8539;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_SRC_AND_DST_OBJECT_CLASS_MISMATCH_ = 8540;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SAM_INIT_FAILURE_ = 8541;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_DRA_SCHEMA_INFO_SHIP_ = 8542;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_DRA_SCHEMA_CONFLICT_ = 8543;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_DRA_EARLIER_SCHEMA_CONFLICT_ = 8544;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_DRA_OBJ_NC_MISMATCH_ = 8545;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_NC_STILL_HAS_DSAS_ = 8546;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_GC_REQUIRED_ = 8547;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_LOCAL_MEMBER_OF_LOCAL_ONLY_ = 8548;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_NO_FPO_IN_UNIVERSAL_GROUPS_ = 8549;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_CANT_ADD_TO_GC_ = 8550;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_NO_CHECKPOINT_WITH_PDC_ = 8551;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_SOURCE_AUDITING_NOT_ENABLED_ = 8552;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_CANT_CREATE_IN_NONDOMAIN_NC_ = 8553;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_INVALID_NAME_FOR_SPN_ = 8554;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_FILTER_USES_CONTRUCTED_ATTRS_ = 8555;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_UNICODEPWD_NOT_IN_QUOTES_ = 8556;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_MACHINE_ACCOUNT_QUOTA_EXCEEDED_ = 8557;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_MUST_BE_RUN_ON_DST_DC_ = 8558;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_SRC_DC_MUST_BE_SP4_OR_GREATER_ = 8559;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_CANT_TREE_DELETE_CRITICAL_OBJ_ = 8560;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_INIT_FAILURE_CONSOLE_ = 8561;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_SAM_INIT_FAILURE_CONSOLE_ = 8562;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_FOREST_VERSION_TOO_HIGH_ = 8563;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_DOMAIN_VERSION_TOO_HIGH_ = 8564;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_FOREST_VERSION_TOO_LOW_ = 8565;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_DOMAIN_VERSION_TOO_LOW_ = 8566;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_INCOMPATIBLE_VERSION_ = 8567;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_LOW_DSA_VERSION_ = 8568;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_NO_BEHAVIOR_VERSION_IN_MIXEDDOMAIN_ = 8569;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_NOT_SUPPORTED_SORT_ORDER_ = 8570;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_NAME_NOT_UNIQUE_ = 8571;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_MACHINE_ACCOUNT_CREATED_PRENT4_ = 8572;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_OUT_OF_VERSION_STORE_ = 8573;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_INCOMPATIBLE_CONTROLS_USED_ = 8574;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_NO_REF_DOMAIN_ = 8575;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_RESERVED_LINK_ID_ = 8576;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_LINK_ID_NOT_AVAILABLE_ = 8577;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_AG_CANT_HAVE_UNIVERSAL_MEMBER_ = 8578;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_MODIFYDN_DISALLOWED_BY_INSTANCE_TYPE_ = 8579;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_NO_OBJECT_MOVE_IN_SCHEMA_NC_ = 8580;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_MODIFYDN_DISALLOWED_BY_FLAG_ = 8581;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_MODIFYDN_WRONG_GRANDPARENT_ = 8582;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_NAME_ERROR_TRUST_REFERRAL_ = 8583;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NOT_SUPPORTED_ON_STANDARD_SERVER_ = 8584;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_CANT_ACCESS_REMOTE_PART_OF_AD_ = 8585;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_CR_IMPOSSIBLE_TO_VALIDATE_V2_ = 8586;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_THREAD_LIMIT_EXCEEDED_ = 8587;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_NOT_CLOSEST_ = 8588;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_CANT_DERIVE_SPN_WITHOUT_SERVER_REF_ = 8589;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_SINGLE_USER_MODE_FAILED_ = 8590;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_NTDSCRIPT_SYNTAX_ERROR_ = 8591;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_NTDSCRIPT_PROCESS_ERROR_ = 8592;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_DIFFERENT_REPL_EPOCHS_ = 8593;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_DRS_EXTENSIONS_CHANGED_ = 8594;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_REPLICA_SET_CHANGE_NOT_ALLOWED_ON_DISABLED_CR_ = 8595;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_NO_MSDS_INTID_ = 8596;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_DUP_MSDS_INTID_ = 8597;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_EXISTS_IN_RDNATTID_ = 8598;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_AUTHORIZATION_FAILED_ = 8599;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_INVALID_SCRIPT_ = 8600;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_REMOTE_CROSSREF_OP_FAILED_ = 8601;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_CROSS_REF_BUSY_ = 8602;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_CANT_DERIVE_SPN_FOR_DELETED_DOMAIN_ = 8603;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_CANT_DEMOTE_WITH_WRITEABLE_NC_ = 8604;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_DUPLICATE_ID_FOUND_ = 8605;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_INSUFFICIENT_ATTR_TO_CREATE_OBJECT_ = 8606;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_GROUP_CONVERSION_ERROR_ = 8607;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_CANT_MOVE_APP_BASIC_GROUP_ = 8608;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_CANT_MOVE_APP_QUERY_GROUP_ = 8609;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_ROLE_NOT_VERIFIED_ = 8610;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_WKO_CONTAINER_CANNOT_BE_SPECIAL_ = 8611;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_DOMAIN_RENAME_IN_PROGRESS_ = 8612;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_EXISTING_AD_CHILD_NC_ = 8613;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_REPL_LIFETIME_EXCEEDED_ = 8614;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_DISALLOWED_IN_SYSTEM_CONTAINER_ = 8615;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_LDAP_SEND_QUEUE_FULL_ = 8616;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_DRA_OUT_SCHEDULE_WINDOW_ = 8617;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_POLICY_NOT_KNOWN_ = 8618;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NO_SITE_SETTINGS_OBJECT_ = 8619;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NO_SECRETS_ = 8620;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NO_WRITABLE_DC_FOUND_ = 8621;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_NO_SERVER_OBJECT_ = 8622;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_NO_NTDSA_OBJECT_ = 8623;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_NON_ASQ_SEARCH_ = 8624;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_AUDIT_FAILURE_ = 8625;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_INVALID_SEARCH_FLAG_SUBTREE_ = 8626;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_INVALID_SEARCH_FLAG_TUPLE_ = 8627;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_HIERARCHY_TABLE_TOO_DEEP_ = 8628;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_DRA_CORRUPT_UTD_VECTOR_ = 8629;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_DRA_SECRETS_DENIED_ = 8630;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_RESERVED_MAPI_ID_ = 8631;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_MAPI_ID_NOT_AVAILABLE_ = 8632;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_DRA_MISSING_KRBTGT_SECRET_ = 8633;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_DOMAIN_NAME_EXISTS_IN_FOREST_ = 8634;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_FLAT_NAME_EXISTS_IN_FOREST_ = 8635;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_USER_PRINCIPAL_NAME_ = 8636;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_OID_MAPPED_GROUP_CANT_HAVE_MEMBERS_ = 8637;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_OID_NOT_FOUND_ = 8638;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_DRA_RECYCLED_TARGET_ = 8639;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_DISALLOWED_NC_REDIRECT_ = 8640;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_HIGH_ADLDS_FFL_ = 8641;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_HIGH_DSA_VERSION_ = 8642;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_LOW_ADLDS_FFL_ = 8643;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DOMAIN_SID_SAME_AS_LOCAL_WORKSTATION_ = 8644;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_UNDELETE_SAM_VALIDATION_FAILED_ = 8645;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INCORRECT_ACCOUNT_TYPE_ = 8646;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_SPN_VALUE_NOT_UNIQUE_IN_FOREST_ = 8647;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_UPN_VALUE_NOT_UNIQUE_IN_FOREST_ = 8648;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_MISSING_FOREST_TRUST_ = 8649;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DS_VALUE_KEY_NOT_UNIQUE_ = 8650;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_RESPONSE_CODES_BASE_ = 9000;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_RCODE_NO_ERROR_ = NO_ERROR_;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_MASK_ = 0x00002328;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_RCODE_FORMAT_ERROR_ = 9001;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_RCODE_SERVER_FAILURE_ = 9002;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_RCODE_NAME_ERROR_ = 9003;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_RCODE_NOT_IMPLEMENTED_ = 9004;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_RCODE_REFUSED_ = 9005;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_RCODE_YXDOMAIN_ = 9006;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_RCODE_YXRRSET_ = 9007;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_RCODE_NXRRSET_ = 9008;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_RCODE_NOTAUTH_ = 9009;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_RCODE_NOTZONE_ = 9010;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_RCODE_BADSIG_ = 9016;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_RCODE_BADKEY_ = 9017;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_RCODE_BADTIME_ = 9018;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_RCODE_LAST_ = DNS_ERROR_RCODE_BADTIME_;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_DNSSEC_BASE_ = 9100;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_KEYMASTER_REQUIRED_ = 9101;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_NOT_ALLOWED_ON_SIGNED_ZONE_ = 9102;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_NSEC3_INCOMPATIBLE_WITH_RSA_SHA1_ = 9103;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_NOT_ENOUGH_SIGNING_KEY_DESCRIPTORS_ = 9104;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_UNSUPPORTED_ALGORITHM_ = 9105;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_INVALID_KEY_SIZE_ = 9106;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_SIGNING_KEY_NOT_ACCESSIBLE_ = 9107;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_KSP_DOES_NOT_SUPPORT_PROTECTION_ = 9108;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_UNEXPECTED_DATA_PROTECTION_ERROR_ = 9109;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_UNEXPECTED_CNG_ERROR_ = 9110;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_UNKNOWN_SIGNING_PARAMETER_VERSION_ = 9111;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_KSP_NOT_ACCESSIBLE_ = 9112;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_TOO_MANY_SKDS_ = 9113;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_INVALID_ROLLOVER_PERIOD_ = 9114;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_INVALID_INITIAL_ROLLOVER_OFFSET_ = 9115;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_ROLLOVER_IN_PROGRESS_ = 9116;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_STANDBY_KEY_NOT_PRESENT_ = 9117;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_NOT_ALLOWED_ON_ZSK_ = 9118;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_NOT_ALLOWED_ON_ACTIVE_SKD_ = 9119;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_ROLLOVER_ALREADY_QUEUED_ = 9120;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_NOT_ALLOWED_ON_UNSIGNED_ZONE_ = 9121;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_BAD_KEYMASTER_ = 9122;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_INVALID_SIGNATURE_VALIDITY_PERIOD_ = 9123;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_INVALID_NSEC3_ITERATION_COUNT_ = 9124;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_DNSSEC_IS_DISABLED_ = 9125;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_INVALID_XML_ = 9126;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_NO_VALID_TRUST_ANCHORS_ = 9127;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_ROLLOVER_NOT_POKEABLE_ = 9128;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_NSEC3_NAME_COLLISION_ = 9129;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_NSEC_INCOMPATIBLE_WITH_NSEC3_RSA_SHA1_ = 9130;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_PACKET_FMT_BASE_ = 9500;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_INFO_NO_RECORDS_ = 9501;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_BAD_PACKET_ = 9502;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_NO_PACKET_ = 9503;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_RCODE_ = 9504;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_UNSECURE_PACKET_ = 9505;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_STATUS_PACKET_UNSECURE_ = DNS_ERROR_UNSECURE_PACKET_;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_REQUEST_PENDING_ = 9506;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_NO_MEMORY_ = ERROR_OUTOFMEMORY_;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_INVALID_NAME_ = ERROR_INVALID_NAME_;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_INVALID_DATA_ = ERROR_INVALID_DATA_;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_GENERAL_API_BASE_ = 9550;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_INVALID_TYPE_ = 9551;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_INVALID_IP_ADDRESS_ = 9552;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_INVALID_PROPERTY_ = 9553;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_TRY_AGAIN_LATER_ = 9554;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_NOT_UNIQUE_ = 9555;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_NON_RFC_NAME_ = 9556;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_STATUS_FQDN_ = 9557;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_STATUS_DOTTED_NAME_ = 9558;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_STATUS_SINGLE_PART_NAME_ = 9559;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_INVALID_NAME_CHAR_ = 9560;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_NUMERIC_NAME_ = 9561;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_NOT_ALLOWED_ON_ROOT_SERVER_ = 9562;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_NOT_ALLOWED_UNDER_DELEGATION_ = 9563;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_CANNOT_FIND_ROOT_HINTS_ = 9564;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_INCONSISTENT_ROOT_HINTS_ = 9565;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_DWORD_VALUE_TOO_SMALL_ = 9566;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_DWORD_VALUE_TOO_LARGE_ = 9567;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_BACKGROUND_LOADING_ = 9568;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_NOT_ALLOWED_ON_RODC_ = 9569;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_NOT_ALLOWED_UNDER_DNAME_ = 9570;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_DELEGATION_REQUIRED_ = 9571;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_INVALID_POLICY_TABLE_ = 9572;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_ADDRESS_REQUIRED_ = 9573;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_ZONE_BASE_ = 9600;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_ZONE_DOES_NOT_EXIST_ = 9601;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_NO_ZONE_INFO_ = 9602;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_INVALID_ZONE_OPERATION_ = 9603;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_ZONE_CONFIGURATION_ERROR_ = 9604;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_ZONE_HAS_NO_SOA_RECORD_ = 9605;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_ZONE_HAS_NO_NS_RECORDS_ = 9606;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_ZONE_LOCKED_ = 9607;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_ZONE_CREATION_FAILED_ = 9608;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_ZONE_ALREADY_EXISTS_ = 9609;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_AUTOZONE_ALREADY_EXISTS_ = 9610;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_INVALID_ZONE_TYPE_ = 9611;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_SECONDARY_REQUIRES_MASTER_IP_ = 9612;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_ZONE_NOT_SECONDARY_ = 9613;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_NEED_SECONDARY_ADDRESSES_ = 9614;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_WINS_INIT_FAILED_ = 9615;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_NEED_WINS_SERVERS_ = 9616;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_NBSTAT_INIT_FAILED_ = 9617;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_SOA_DELETE_INVALID_ = 9618;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_FORWARDER_ALREADY_EXISTS_ = 9619;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_ZONE_REQUIRES_MASTER_IP_ = 9620;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_ZONE_IS_SHUTDOWN_ = 9621;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_ZONE_LOCKED_FOR_SIGNING_ = 9622;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_DATAFILE_BASE_ = 9650;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_PRIMARY_REQUIRES_DATAFILE_ = 9651;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_INVALID_DATAFILE_NAME_ = 9652;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_DATAFILE_OPEN_FAILURE_ = 9653;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_FILE_WRITEBACK_FAILED_ = 9654;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_DATAFILE_PARSING_ = 9655;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_DATABASE_BASE_ = 9700;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_RECORD_DOES_NOT_EXIST_ = 9701;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_RECORD_FORMAT_ = 9702;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_NODE_CREATION_FAILED_ = 9703;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_UNKNOWN_RECORD_TYPE_ = 9704;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_RECORD_TIMED_OUT_ = 9705;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_NAME_NOT_IN_ZONE_ = 9706;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_CNAME_LOOP_ = 9707;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_NODE_IS_CNAME_ = 9708;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_CNAME_COLLISION_ = 9709;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_RECORD_ONLY_AT_ZONE_ROOT_ = 9710;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_RECORD_ALREADY_EXISTS_ = 9711;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_SECONDARY_DATA_ = 9712;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_NO_CREATE_CACHE_DATA_ = 9713;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_NAME_DOES_NOT_EXIST_ = 9714;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_WARNING_PTR_CREATE_FAILED_ = 9715;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_WARNING_DOMAIN_UNDELETED_ = 9716;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_DS_UNAVAILABLE_ = 9717;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_DS_ZONE_ALREADY_EXISTS_ = 9718;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_NO_BOOTFILE_IF_DS_ZONE_ = 9719;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_NODE_IS_DNAME_ = 9720;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_DNAME_COLLISION_ = 9721;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_ALIAS_LOOP_ = 9722;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_OPERATION_BASE_ = 9750;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_INFO_AXFR_COMPLETE_ = 9751;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_AXFR_ = 9752;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_INFO_ADDED_LOCAL_WINS_ = 9753;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_SECURE_BASE_ = 9800;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_STATUS_CONTINUE_NEEDED_ = 9801;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_SETUP_BASE_ = 9850;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_NO_TCPIP_ = 9851;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_NO_DNS_SERVERS_ = 9852;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_DP_BASE_ = 9900;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_DP_DOES_NOT_EXIST_ = 9901;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_DP_ALREADY_EXISTS_ = 9902;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_DP_NOT_ENLISTED_ = 9903;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_DP_ALREADY_ENLISTED_ = 9904;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_DP_NOT_AVAILABLE_ = 9905;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_DP_FSMO_ERROR_ = 9906;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_RRL_NOT_ENABLED_ = 9911;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_RRL_INVALID_WINDOW_SIZE_ = 9912;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_RRL_INVALID_IPV4_PREFIX_ = 9913;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_RRL_INVALID_IPV6_PREFIX_ = 9914;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_RRL_INVALID_TC_RATE_ = 9915;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_RRL_INVALID_LEAK_RATE_ = 9916;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_RRL_LEAK_RATE_LESSTHAN_TC_RATE_ = 9917;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_VIRTUALIZATION_INSTANCE_ALREADY_EXISTS_ = 9921;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_VIRTUALIZATION_INSTANCE_DOES_NOT_EXIST_ = 9922;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_VIRTUALIZATION_TREE_LOCKED_ = 9923;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_INVAILD_VIRTUALIZATION_INSTANCE_NAME_ = 9924;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_DEFAULT_VIRTUALIZATION_INSTANCE_ = 9925;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_ZONESCOPE_ALREADY_EXISTS_ = 9951;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_ZONESCOPE_DOES_NOT_EXIST_ = 9952;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_DEFAULT_ZONESCOPE_ = 9953;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_INVALID_ZONESCOPE_NAME_ = 9954;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_NOT_ALLOWED_WITH_ZONESCOPES_ = 9955;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_LOAD_ZONESCOPE_FAILED_ = 9956;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_ZONESCOPE_FILE_WRITEBACK_FAILED_ = 9957;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_INVALID_SCOPE_NAME_ = 9958;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_SCOPE_DOES_NOT_EXIST_ = 9959;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_DEFAULT_SCOPE_ = 9960;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_INVALID_SCOPE_OPERATION_ = 9961;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_SCOPE_LOCKED_ = 9962;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_SCOPE_ALREADY_EXISTS_ = 9963;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_POLICY_ALREADY_EXISTS_ = 9971;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_POLICY_DOES_NOT_EXIST_ = 9972;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_POLICY_INVALID_CRITERIA_ = 9973;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_POLICY_INVALID_SETTINGS_ = 9974;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_CLIENT_SUBNET_IS_ACCESSED_ = 9975;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_CLIENT_SUBNET_DOES_NOT_EXIST_ = 9976;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_CLIENT_SUBNET_ALREADY_EXISTS_ = 9977;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_SUBNET_DOES_NOT_EXIST_ = 9978;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_SUBNET_ALREADY_EXISTS_ = 9979;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_POLICY_LOCKED_ = 9980;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_POLICY_INVALID_WEIGHT_ = 9981;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_POLICY_INVALID_NAME_ = 9982;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_POLICY_MISSING_CRITERIA_ = 9983;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_INVALID_CLIENT_SUBNET_NAME_ = 9984;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_POLICY_PROCESSING_ORDER_INVALID_ = 9985;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_POLICY_SCOPE_MISSING_ = 9986;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_POLICY_SCOPE_NOT_ALLOWED_ = 9987;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_SERVERSCOPE_IS_REFERENCED_ = 9988;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_ZONESCOPE_IS_REFERENCED_ = 9989;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_POLICY_INVALID_CRITERIA_CLIENT_SUBNET_ = 9990;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_POLICY_INVALID_CRITERIA_TRANSPORT_PROTOCOL_ = 9991;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_POLICY_INVALID_CRITERIA_NETWORK_PROTOCOL_ = 9992;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_POLICY_INVALID_CRITERIA_INTERFACE_ = 9993;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_POLICY_INVALID_CRITERIA_FQDN_ = 9994;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_POLICY_INVALID_CRITERIA_QUERY_TYPE_ = 9995;
BOOST_CONSTEXPR_OR_CONST DWORD_ DNS_ERROR_POLICY_INVALID_CRITERIA_TIME_OF_DAY_ = 9996;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSABASEERR_ = 10000;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSAEINTR_ = 10004;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSAEBADF_ = 10009;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSAEACCES_ = 10013;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSAEFAULT_ = 10014;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSAEINVAL_ = 10022;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSAEMFILE_ = 10024;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSAEWOULDBLOCK_ = 10035;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSAEINPROGRESS_ = 10036;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSAEALREADY_ = 10037;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSAENOTSOCK_ = 10038;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSAEDESTADDRREQ_ = 10039;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSAEMSGSIZE_ = 10040;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSAEPROTOTYPE_ = 10041;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSAENOPROTOOPT_ = 10042;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSAEPROTONOSUPPORT_ = 10043;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSAESOCKTNOSUPPORT_ = 10044;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSAEOPNOTSUPP_ = 10045;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSAEPFNOSUPPORT_ = 10046;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSAEAFNOSUPPORT_ = 10047;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSAEADDRINUSE_ = 10048;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSAEADDRNOTAVAIL_ = 10049;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSAENETDOWN_ = 10050;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSAENETUNREACH_ = 10051;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSAENETRESET_ = 10052;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSAECONNABORTED_ = 10053;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSAECONNRESET_ = 10054;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSAENOBUFS_ = 10055;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSAEISCONN_ = 10056;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSAENOTCONN_ = 10057;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSAESHUTDOWN_ = 10058;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSAETOOMANYREFS_ = 10059;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSAETIMEDOUT_ = 10060;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSAECONNREFUSED_ = 10061;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSAELOOP_ = 10062;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSAENAMETOOLONG_ = 10063;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSAEHOSTDOWN_ = 10064;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSAEHOSTUNREACH_ = 10065;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSAENOTEMPTY_ = 10066;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSAEPROCLIM_ = 10067;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSAEUSERS_ = 10068;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSAEDQUOT_ = 10069;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSAESTALE_ = 10070;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSAEREMOTE_ = 10071;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSASYSNOTREADY_ = 10091;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSAVERNOTSUPPORTED_ = 10092;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSANOTINITIALISED_ = 10093;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSAEDISCON_ = 10101;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSAENOMORE_ = 10102;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSAECANCELLED_ = 10103;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSAEINVALIDPROCTABLE_ = 10104;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSAEINVALIDPROVIDER_ = 10105;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSAEPROVIDERFAILEDINIT_ = 10106;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSASYSCALLFAILURE_ = 10107;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSASERVICE_NOT_FOUND_ = 10108;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSATYPE_NOT_FOUND_ = 10109;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSA_E_NO_MORE_ = 10110;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSA_E_CANCELLED_ = 10111;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSAEREFUSED_ = 10112;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSAHOST_NOT_FOUND_ = 11001;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSATRY_AGAIN_ = 11002;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSANO_RECOVERY_ = 11003;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSANO_DATA_ = 11004;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSA_QOS_RECEIVERS_ = 11005;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSA_QOS_SENDERS_ = 11006;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSA_QOS_NO_SENDERS_ = 11007;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSA_QOS_NO_RECEIVERS_ = 11008;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSA_QOS_REQUEST_CONFIRMED_ = 11009;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSA_QOS_ADMISSION_FAILURE_ = 11010;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSA_QOS_POLICY_FAILURE_ = 11011;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSA_QOS_BAD_STYLE_ = 11012;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSA_QOS_BAD_OBJECT_ = 11013;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSA_QOS_TRAFFIC_CTRL_ERROR_ = 11014;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSA_QOS_GENERIC_ERROR_ = 11015;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSA_QOS_ESERVICETYPE_ = 11016;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSA_QOS_EFLOWSPEC_ = 11017;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSA_QOS_EPROVSPECBUF_ = 11018;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSA_QOS_EFILTERSTYLE_ = 11019;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSA_QOS_EFILTERTYPE_ = 11020;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSA_QOS_EFILTERCOUNT_ = 11021;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSA_QOS_EOBJLENGTH_ = 11022;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSA_QOS_EFLOWCOUNT_ = 11023;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSA_QOS_EUNKOWNPSOBJ_ = 11024;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSA_QOS_EPOLICYOBJ_ = 11025;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSA_QOS_EFLOWDESC_ = 11026;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSA_QOS_EPSFLOWSPEC_ = 11027;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSA_QOS_EPSFILTERSPEC_ = 11028;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSA_QOS_ESDMODEOBJ_ = 11029;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSA_QOS_ESHAPERATEOBJ_ = 11030;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSA_QOS_RESERVED_PETYPE_ = 11031;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSA_SECURE_HOST_NOT_FOUND_ = 11032;
BOOST_CONSTEXPR_OR_CONST DWORD_ WSA_IPSEC_NAME_POLICY_ERROR_ = 11033;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_QM_POLICY_EXISTS_ = 13000;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_QM_POLICY_NOT_FOUND_ = 13001;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_QM_POLICY_IN_USE_ = 13002;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_MM_POLICY_EXISTS_ = 13003;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_MM_POLICY_NOT_FOUND_ = 13004;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_MM_POLICY_IN_USE_ = 13005;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_MM_FILTER_EXISTS_ = 13006;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_MM_FILTER_NOT_FOUND_ = 13007;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_TRANSPORT_FILTER_EXISTS_ = 13008;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_TRANSPORT_FILTER_NOT_FOUND_ = 13009;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_MM_AUTH_EXISTS_ = 13010;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_MM_AUTH_NOT_FOUND_ = 13011;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_MM_AUTH_IN_USE_ = 13012;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_DEFAULT_MM_POLICY_NOT_FOUND_ = 13013;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_DEFAULT_MM_AUTH_NOT_FOUND_ = 13014;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_DEFAULT_QM_POLICY_NOT_FOUND_ = 13015;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_TUNNEL_FILTER_EXISTS_ = 13016;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_TUNNEL_FILTER_NOT_FOUND_ = 13017;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_MM_FILTER_PENDING_DELETION_ = 13018;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_TRANSPORT_FILTER_PENDING_DELETION_ = 13019;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_TUNNEL_FILTER_PENDING_DELETION_ = 13020;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_MM_POLICY_PENDING_DELETION_ = 13021;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_MM_AUTH_PENDING_DELETION_ = 13022;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_QM_POLICY_PENDING_DELETION_ = 13023;
BOOST_CONSTEXPR_OR_CONST DWORD_ WARNING_IPSEC_MM_POLICY_PRUNED_ = 13024;
BOOST_CONSTEXPR_OR_CONST DWORD_ WARNING_IPSEC_QM_POLICY_PRUNED_ = 13025;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_NEG_STATUS_BEGIN_ = 13800;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_AUTH_FAIL_ = 13801;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_ATTRIB_FAIL_ = 13802;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_NEGOTIATION_PENDING_ = 13803;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_GENERAL_PROCESSING_ERROR_ = 13804;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_TIMED_OUT_ = 13805;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_NO_CERT_ = 13806;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_SA_DELETED_ = 13807;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_SA_REAPED_ = 13808;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_MM_ACQUIRE_DROP_ = 13809;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_QM_ACQUIRE_DROP_ = 13810;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_QUEUE_DROP_MM_ = 13811;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_QUEUE_DROP_NO_MM_ = 13812;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_DROP_NO_RESPONSE_ = 13813;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_MM_DELAY_DROP_ = 13814;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_QM_DELAY_DROP_ = 13815;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_ERROR_ = 13816;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_CRL_FAILED_ = 13817;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_INVALID_KEY_USAGE_ = 13818;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_INVALID_CERT_TYPE_ = 13819;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_NO_PRIVATE_KEY_ = 13820;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_SIMULTANEOUS_REKEY_ = 13821;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_DH_FAIL_ = 13822;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_CRITICAL_PAYLOAD_NOT_RECOGNIZED_ = 13823;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_INVALID_HEADER_ = 13824;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_NO_POLICY_ = 13825;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_INVALID_SIGNATURE_ = 13826;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_KERBEROS_ERROR_ = 13827;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_NO_PUBLIC_KEY_ = 13828;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_PROCESS_ERR_ = 13829;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_PROCESS_ERR_SA_ = 13830;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_PROCESS_ERR_PROP_ = 13831;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_PROCESS_ERR_TRANS_ = 13832;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_PROCESS_ERR_KE_ = 13833;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_PROCESS_ERR_ID_ = 13834;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_PROCESS_ERR_CERT_ = 13835;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_PROCESS_ERR_CERT_REQ_ = 13836;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_PROCESS_ERR_HASH_ = 13837;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_PROCESS_ERR_SIG_ = 13838;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_PROCESS_ERR_NONCE_ = 13839;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_PROCESS_ERR_NOTIFY_ = 13840;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_PROCESS_ERR_DELETE_ = 13841;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_PROCESS_ERR_VENDOR_ = 13842;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_INVALID_PAYLOAD_ = 13843;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_LOAD_SOFT_SA_ = 13844;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_SOFT_SA_TORN_DOWN_ = 13845;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_INVALID_COOKIE_ = 13846;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_NO_PEER_CERT_ = 13847;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_PEER_CRL_FAILED_ = 13848;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_POLICY_CHANGE_ = 13849;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_NO_MM_POLICY_ = 13850;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_NOTCBPRIV_ = 13851;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_SECLOADFAIL_ = 13852;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_FAILSSPINIT_ = 13853;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_FAILQUERYSSP_ = 13854;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_SRVACQFAIL_ = 13855;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_SRVQUERYCRED_ = 13856;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_GETSPIFAIL_ = 13857;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_INVALID_FILTER_ = 13858;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_OUT_OF_MEMORY_ = 13859;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_ADD_UPDATE_KEY_FAILED_ = 13860;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_INVALID_POLICY_ = 13861;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_UNKNOWN_DOI_ = 13862;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_INVALID_SITUATION_ = 13863;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_DH_FAILURE_ = 13864;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_INVALID_GROUP_ = 13865;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_ENCRYPT_ = 13866;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_DECRYPT_ = 13867;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_POLICY_MATCH_ = 13868;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_UNSUPPORTED_ID_ = 13869;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_INVALID_HASH_ = 13870;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_INVALID_HASH_ALG_ = 13871;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_INVALID_HASH_SIZE_ = 13872;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_INVALID_ENCRYPT_ALG_ = 13873;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_INVALID_AUTH_ALG_ = 13874;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_INVALID_SIG_ = 13875;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_LOAD_FAILED_ = 13876;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_RPC_DELETE_ = 13877;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_BENIGN_REINIT_ = 13878;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_INVALID_RESPONDER_LIFETIME_NOTIFY_ = 13879;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_INVALID_MAJOR_VERSION_ = 13880;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_INVALID_CERT_KEYLEN_ = 13881;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_MM_LIMIT_ = 13882;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_NEGOTIATION_DISABLED_ = 13883;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_QM_LIMIT_ = 13884;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_MM_EXPIRED_ = 13885;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_PEER_MM_ASSUMED_INVALID_ = 13886;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_CERT_CHAIN_POLICY_MISMATCH_ = 13887;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_UNEXPECTED_MESSAGE_ID_ = 13888;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_INVALID_AUTH_PAYLOAD_ = 13889;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_DOS_COOKIE_SENT_ = 13890;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_SHUTTING_DOWN_ = 13891;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_CGA_AUTH_FAILED_ = 13892;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_PROCESS_ERR_NATOA_ = 13893;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_INVALID_MM_FOR_QM_ = 13894;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_QM_EXPIRED_ = 13895;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_TOO_MANY_FILTERS_ = 13896;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_NEG_STATUS_END_ = 13897;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_KILL_DUMMY_NAP_TUNNEL_ = 13898;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_INNER_IP_ASSIGNMENT_FAILURE_ = 13899;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_REQUIRE_CP_PAYLOAD_MISSING_ = 13900;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_KEY_MODULE_IMPERSONATION_NEGOTIATION_PENDING_ = 13901;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_COEXISTENCE_SUPPRESS_ = 13902;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_RATELIMIT_DROP_ = 13903;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_PEER_DOESNT_SUPPORT_MOBIKE_ = 13904;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_AUTHORIZATION_FAILURE_ = 13905;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_STRONG_CRED_AUTHORIZATION_FAILURE_ = 13906;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_AUTHORIZATION_FAILURE_WITH_OPTIONAL_RETRY_ = 13907;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_STRONG_CRED_AUTHORIZATION_AND_CERTMAP_FAILURE_ = 13908;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_IKE_NEG_STATUS_EXTENDED_END_ = 13909;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_BAD_SPI_ = 13910;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_SA_LIFETIME_EXPIRED_ = 13911;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_WRONG_SA_ = 13912;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_REPLAY_CHECK_FAILED_ = 13913;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_INVALID_PACKET_ = 13914;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_INTEGRITY_CHECK_FAILED_ = 13915;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_CLEAR_TEXT_DROP_ = 13916;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_AUTH_FIREWALL_DROP_ = 13917;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_THROTTLE_DROP_ = 13918;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_DOSP_BLOCK_ = 13925;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_DOSP_RECEIVED_MULTICAST_ = 13926;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_DOSP_INVALID_PACKET_ = 13927;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_DOSP_STATE_LOOKUP_FAILED_ = 13928;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_DOSP_MAX_ENTRIES_ = 13929;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_DOSP_KEYMOD_NOT_ALLOWED_ = 13930;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_DOSP_NOT_INSTALLED_ = 13931;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_IPSEC_DOSP_MAX_PER_IP_RATELIMIT_QUEUES_ = 13932;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_SECTION_NOT_FOUND_ = 14000;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_CANT_GEN_ACTCTX_ = 14001;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_INVALID_ACTCTXDATA_FORMAT_ = 14002;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_ASSEMBLY_NOT_FOUND_ = 14003;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_MANIFEST_FORMAT_ERROR_ = 14004;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_MANIFEST_PARSE_ERROR_ = 14005;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_ACTIVATION_CONTEXT_DISABLED_ = 14006;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_KEY_NOT_FOUND_ = 14007;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_VERSION_CONFLICT_ = 14008;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_WRONG_SECTION_TYPE_ = 14009;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_THREAD_QUERIES_DISABLED_ = 14010;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_PROCESS_DEFAULT_ALREADY_SET_ = 14011;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_UNKNOWN_ENCODING_GROUP_ = 14012;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_UNKNOWN_ENCODING_ = 14013;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_INVALID_XML_NAMESPACE_URI_ = 14014;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_ROOT_MANIFEST_DEPENDENCY_NOT_INSTALLED_ = 14015;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_LEAF_MANIFEST_DEPENDENCY_NOT_INSTALLED_ = 14016;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_INVALID_ASSEMBLY_IDENTITY_ATTRIBUTE_ = 14017;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_MANIFEST_MISSING_REQUIRED_DEFAULT_NAMESPACE_ = 14018;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_MANIFEST_INVALID_REQUIRED_DEFAULT_NAMESPACE_ = 14019;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_PRIVATE_MANIFEST_CROSS_PATH_WITH_REPARSE_POINT_ = 14020;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_DUPLICATE_DLL_NAME_ = 14021;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_DUPLICATE_WINDOWCLASS_NAME_ = 14022;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_DUPLICATE_CLSID_ = 14023;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_DUPLICATE_IID_ = 14024;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_DUPLICATE_TLBID_ = 14025;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_DUPLICATE_PROGID_ = 14026;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_DUPLICATE_ASSEMBLY_NAME_ = 14027;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_FILE_HASH_MISMATCH_ = 14028;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_POLICY_PARSE_ERROR_ = 14029;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_XML_E_MISSINGQUOTE_ = 14030;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_XML_E_COMMENTSYNTAX_ = 14031;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_XML_E_BADSTARTNAMECHAR_ = 14032;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_XML_E_BADNAMECHAR_ = 14033;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_XML_E_BADCHARINSTRING_ = 14034;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_XML_E_XMLDECLSYNTAX_ = 14035;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_XML_E_BADCHARDATA_ = 14036;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_XML_E_MISSINGWHITESPACE_ = 14037;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_XML_E_EXPECTINGTAGEND_ = 14038;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_XML_E_MISSINGSEMICOLON_ = 14039;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_XML_E_UNBALANCEDPAREN_ = 14040;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_XML_E_INTERNALERROR_ = 14041;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_XML_E_UNEXPECTED_WHITESPACE_ = 14042;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_XML_E_INCOMPLETE_ENCODING_ = 14043;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_XML_E_MISSING_PAREN_ = 14044;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_XML_E_EXPECTINGCLOSEQUOTE_ = 14045;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_XML_E_MULTIPLE_COLONS_ = 14046;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_XML_E_INVALID_DECIMAL_ = 14047;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_XML_E_INVALID_HEXIDECIMAL_ = 14048;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_XML_E_INVALID_UNICODE_ = 14049;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_XML_E_WHITESPACEORQUESTIONMARK_ = 14050;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_XML_E_UNEXPECTEDENDTAG_ = 14051;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_XML_E_UNCLOSEDTAG_ = 14052;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_XML_E_DUPLICATEATTRIBUTE_ = 14053;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_XML_E_MULTIPLEROOTS_ = 14054;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_XML_E_INVALIDATROOTLEVEL_ = 14055;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_XML_E_BADXMLDECL_ = 14056;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_XML_E_MISSINGROOT_ = 14057;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_XML_E_UNEXPECTEDEOF_ = 14058;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_XML_E_BADPEREFINSUBSET_ = 14059;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_XML_E_UNCLOSEDSTARTTAG_ = 14060;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_XML_E_UNCLOSEDENDTAG_ = 14061;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_XML_E_UNCLOSEDSTRING_ = 14062;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_XML_E_UNCLOSEDCOMMENT_ = 14063;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_XML_E_UNCLOSEDDECL_ = 14064;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_XML_E_UNCLOSEDCDATA_ = 14065;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_XML_E_RESERVEDNAMESPACE_ = 14066;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_XML_E_INVALIDENCODING_ = 14067;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_XML_E_INVALIDSWITCH_ = 14068;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_XML_E_BADXMLCASE_ = 14069;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_XML_E_INVALID_STANDALONE_ = 14070;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_XML_E_UNEXPECTED_STANDALONE_ = 14071;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_XML_E_INVALID_VERSION_ = 14072;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_XML_E_MISSINGEQUALS_ = 14073;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_PROTECTION_RECOVERY_FAILED_ = 14074;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_PROTECTION_PUBLIC_KEY_TOO_SHORT_ = 14075;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_PROTECTION_CATALOG_NOT_VALID_ = 14076;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_UNTRANSLATABLE_HRESULT_ = 14077;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_PROTECTION_CATALOG_FILE_MISSING_ = 14078;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_MISSING_ASSEMBLY_IDENTITY_ATTRIBUTE_ = 14079;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_INVALID_ASSEMBLY_IDENTITY_ATTRIBUTE_NAME_ = 14080;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_ASSEMBLY_MISSING_ = 14081;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_CORRUPT_ACTIVATION_STACK_ = 14082;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_CORRUPTION_ = 14083;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_EARLY_DEACTIVATION_ = 14084;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_INVALID_DEACTIVATION_ = 14085;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_MULTIPLE_DEACTIVATION_ = 14086;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_PROCESS_TERMINATION_REQUESTED_ = 14087;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_RELEASE_ACTIVATION_CONTEXT_ = 14088;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_SYSTEM_DEFAULT_ACTIVATION_CONTEXT_EMPTY_ = 14089;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_INVALID_IDENTITY_ATTRIBUTE_VALUE_ = 14090;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_INVALID_IDENTITY_ATTRIBUTE_NAME_ = 14091;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_IDENTITY_DUPLICATE_ATTRIBUTE_ = 14092;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_IDENTITY_PARSE_ERROR_ = 14093;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MALFORMED_SUBSTITUTION_STRING_ = 14094;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_INCORRECT_PUBLIC_KEY_TOKEN_ = 14095;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_UNMAPPED_SUBSTITUTION_STRING_ = 14096;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_ASSEMBLY_NOT_LOCKED_ = 14097;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_COMPONENT_STORE_CORRUPT_ = 14098;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_ADVANCED_INSTALLER_FAILED_ = 14099;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_XML_ENCODING_MISMATCH_ = 14100;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_MANIFEST_IDENTITY_SAME_BUT_CONTENTS_DIFFERENT_ = 14101;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_IDENTITIES_DIFFERENT_ = 14102;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_ASSEMBLY_IS_NOT_A_DEPLOYMENT_ = 14103;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_FILE_NOT_PART_OF_ASSEMBLY_ = 14104;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_MANIFEST_TOO_BIG_ = 14105;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_SETTING_NOT_REGISTERED_ = 14106;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_TRANSACTION_CLOSURE_INCOMPLETE_ = 14107;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SMI_PRIMITIVE_INSTALLER_FAILED_ = 14108;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_GENERIC_COMMAND_FAILED_ = 14109;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SXS_FILE_HASH_MISSING_ = 14110;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EVT_INVALID_CHANNEL_PATH_ = 15000;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EVT_INVALID_QUERY_ = 15001;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EVT_PUBLISHER_METADATA_NOT_FOUND_ = 15002;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EVT_EVENT_TEMPLATE_NOT_FOUND_ = 15003;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EVT_INVALID_PUBLISHER_NAME_ = 15004;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EVT_INVALID_EVENT_DATA_ = 15005;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EVT_CHANNEL_NOT_FOUND_ = 15007;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EVT_MALFORMED_XML_TEXT_ = 15008;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EVT_SUBSCRIPTION_TO_DIRECT_CHANNEL_ = 15009;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EVT_CONFIGURATION_ERROR_ = 15010;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EVT_QUERY_RESULT_STALE_ = 15011;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EVT_QUERY_RESULT_INVALID_POSITION_ = 15012;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EVT_NON_VALIDATING_MSXML_ = 15013;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EVT_FILTER_ALREADYSCOPED_ = 15014;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EVT_FILTER_NOTELTSET_ = 15015;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EVT_FILTER_INVARG_ = 15016;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EVT_FILTER_INVTEST_ = 15017;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EVT_FILTER_INVTYPE_ = 15018;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EVT_FILTER_PARSEERR_ = 15019;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EVT_FILTER_UNSUPPORTEDOP_ = 15020;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EVT_FILTER_UNEXPECTEDTOKEN_ = 15021;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EVT_INVALID_OPERATION_OVER_ENABLED_DIRECT_CHANNEL_ = 15022;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EVT_INVALID_CHANNEL_PROPERTY_VALUE_ = 15023;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EVT_INVALID_PUBLISHER_PROPERTY_VALUE_ = 15024;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EVT_CHANNEL_CANNOT_ACTIVATE_ = 15025;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EVT_FILTER_TOO_COMPLEX_ = 15026;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EVT_MESSAGE_NOT_FOUND_ = 15027;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EVT_MESSAGE_ID_NOT_FOUND_ = 15028;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EVT_UNRESOLVED_VALUE_INSERT_ = 15029;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EVT_UNRESOLVED_PARAMETER_INSERT_ = 15030;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EVT_MAX_INSERTS_REACHED_ = 15031;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EVT_EVENT_DEFINITION_NOT_FOUND_ = 15032;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EVT_MESSAGE_LOCALE_NOT_FOUND_ = 15033;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EVT_VERSION_TOO_OLD_ = 15034;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EVT_VERSION_TOO_NEW_ = 15035;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EVT_CANNOT_OPEN_CHANNEL_OF_QUERY_ = 15036;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EVT_PUBLISHER_DISABLED_ = 15037;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EVT_FILTER_OUT_OF_RANGE_ = 15038;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EC_SUBSCRIPTION_CANNOT_ACTIVATE_ = 15080;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EC_LOG_DISABLED_ = 15081;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EC_CIRCULAR_FORWARDING_ = 15082;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EC_CREDSTORE_FULL_ = 15083;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EC_CRED_NOT_FOUND_ = 15084;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_EC_NO_ACTIVE_CHANNEL_ = 15085;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MUI_FILE_NOT_FOUND_ = 15100;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MUI_INVALID_FILE_ = 15101;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MUI_INVALID_RC_CONFIG_ = 15102;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MUI_INVALID_LOCALE_NAME_ = 15103;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MUI_INVALID_ULTIMATEFALLBACK_NAME_ = 15104;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MUI_FILE_NOT_LOADED_ = 15105;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_RESOURCE_ENUM_USER_STOP_ = 15106;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MUI_INTLSETTINGS_UILANG_NOT_INSTALLED_ = 15107;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MUI_INTLSETTINGS_INVALID_LOCALE_NAME_ = 15108;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MRM_RUNTIME_NO_DEFAULT_OR_NEUTRAL_RESOURCE_ = 15110;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MRM_INVALID_PRICONFIG_ = 15111;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MRM_INVALID_FILE_TYPE_ = 15112;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MRM_UNKNOWN_QUALIFIER_ = 15113;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MRM_INVALID_QUALIFIER_VALUE_ = 15114;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MRM_NO_CANDIDATE_ = 15115;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MRM_NO_MATCH_OR_DEFAULT_CANDIDATE_ = 15116;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MRM_RESOURCE_TYPE_MISMATCH_ = 15117;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MRM_DUPLICATE_MAP_NAME_ = 15118;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MRM_DUPLICATE_ENTRY_ = 15119;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MRM_INVALID_RESOURCE_IDENTIFIER_ = 15120;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MRM_FILEPATH_TOO_LONG_ = 15121;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MRM_UNSUPPORTED_DIRECTORY_TYPE_ = 15122;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MRM_INVALID_PRI_FILE_ = 15126;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MRM_NAMED_RESOURCE_NOT_FOUND_ = 15127;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MRM_MAP_NOT_FOUND_ = 15135;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MRM_UNSUPPORTED_PROFILE_TYPE_ = 15136;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MRM_INVALID_QUALIFIER_OPERATOR_ = 15137;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MRM_INDETERMINATE_QUALIFIER_VALUE_ = 15138;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MRM_AUTOMERGE_ENABLED_ = 15139;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MRM_TOO_MANY_RESOURCES_ = 15140;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MRM_UNSUPPORTED_FILE_TYPE_FOR_MERGE_ = 15141;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MRM_UNSUPPORTED_FILE_TYPE_FOR_LOAD_UNLOAD_PRI_FILE_ = 15142;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MRM_NO_CURRENT_VIEW_ON_THREAD_ = 15143;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DIFFERENT_PROFILE_RESOURCE_MANAGER_EXIST_ = 15144;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_OPERATION_NOT_ALLOWED_FROM_SYSTEM_COMPONENT_ = 15145;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MRM_DIRECT_REF_TO_NON_DEFAULT_RESOURCE_ = 15146;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MRM_GENERATION_COUNT_MISMATCH_ = 15147;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PRI_MERGE_VERSION_MISMATCH_ = 15148;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PRI_MERGE_MISSING_SCHEMA_ = 15149;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PRI_MERGE_LOAD_FILE_FAILED_ = 15150;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PRI_MERGE_ADD_FILE_FAILED_ = 15151;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PRI_MERGE_WRITE_FILE_FAILED_ = 15152;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PRI_MERGE_MULTIPLE_PACKAGE_FAMILIES_NOT_ALLOWED_ = 15153;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PRI_MERGE_MULTIPLE_MAIN_PACKAGES_NOT_ALLOWED_ = 15154;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PRI_MERGE_BUNDLE_PACKAGES_NOT_ALLOWED_ = 15155;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PRI_MERGE_MAIN_PACKAGE_REQUIRED_ = 15156;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PRI_MERGE_RESOURCE_PACKAGE_REQUIRED_ = 15157;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PRI_MERGE_INVALID_FILE_NAME_ = 15158;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MCA_INVALID_CAPABILITIES_STRING_ = 15200;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MCA_INVALID_VCP_VERSION_ = 15201;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MCA_MONITOR_VIOLATES_MCCS_SPECIFICATION_ = 15202;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MCA_MCCS_VERSION_MISMATCH_ = 15203;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MCA_UNSUPPORTED_MCCS_VERSION_ = 15204;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MCA_INTERNAL_ERROR_ = 15205;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MCA_INVALID_TECHNOLOGY_TYPE_RETURNED_ = 15206;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_MCA_UNSUPPORTED_COLOR_TEMPERATURE_ = 15207;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_AMBIGUOUS_SYSTEM_DEVICE_ = 15250;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SYSTEM_DEVICE_NOT_FOUND_ = 15299;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_HASH_NOT_SUPPORTED_ = 15300;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_HASH_NOT_PRESENT_ = 15301;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SECONDARY_IC_PROVIDER_NOT_REGISTERED_ = 15321;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_GPIO_CLIENT_INFORMATION_INVALID_ = 15322;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_GPIO_VERSION_NOT_SUPPORTED_ = 15323;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_GPIO_INVALID_REGISTRATION_PACKET_ = 15324;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_GPIO_OPERATION_DENIED_ = 15325;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_GPIO_INCOMPATIBLE_CONNECT_MODE_ = 15326;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_GPIO_INTERRUPT_ALREADY_UNMASKED_ = 15327;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_CANNOT_SWITCH_RUNLEVEL_ = 15400;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_RUNLEVEL_SETTING_ = 15401;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_RUNLEVEL_SWITCH_TIMEOUT_ = 15402;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_RUNLEVEL_SWITCH_AGENT_TIMEOUT_ = 15403;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_RUNLEVEL_SWITCH_IN_PROGRESS_ = 15404;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SERVICES_FAILED_AUTOSTART_ = 15405;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_COM_TASK_STOP_PENDING_ = 15501;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INSTALL_OPEN_PACKAGE_FAILED_ = 15600;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INSTALL_PACKAGE_NOT_FOUND_ = 15601;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INSTALL_INVALID_PACKAGE_ = 15602;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INSTALL_RESOLVE_DEPENDENCY_FAILED_ = 15603;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INSTALL_OUT_OF_DISK_SPACE_ = 15604;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INSTALL_NETWORK_FAILURE_ = 15605;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INSTALL_REGISTRATION_FAILURE_ = 15606;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INSTALL_DEREGISTRATION_FAILURE_ = 15607;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INSTALL_CANCEL_ = 15608;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INSTALL_FAILED_ = 15609;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_REMOVE_FAILED_ = 15610;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PACKAGE_ALREADY_EXISTS_ = 15611;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NEEDS_REMEDIATION_ = 15612;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INSTALL_PREREQUISITE_FAILED_ = 15613;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PACKAGE_REPOSITORY_CORRUPTED_ = 15614;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INSTALL_POLICY_FAILURE_ = 15615;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PACKAGE_UPDATING_ = 15616;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DEPLOYMENT_BLOCKED_BY_POLICY_ = 15617;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PACKAGES_IN_USE_ = 15618;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_RECOVERY_FILE_CORRUPT_ = 15619;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INVALID_STAGED_SIGNATURE_ = 15620;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DELETING_EXISTING_APPLICATIONDATA_STORE_FAILED_ = 15621;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INSTALL_PACKAGE_DOWNGRADE_ = 15622;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_SYSTEM_NEEDS_REMEDIATION_ = 15623;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_APPX_INTEGRITY_FAILURE_CLR_NGEN_ = 15624;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_RESILIENCY_FILE_CORRUPT_ = 15625;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INSTALL_FIREWALL_SERVICE_NOT_RUNNING_ = 15626;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PACKAGE_MOVE_FAILED_ = 15627;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INSTALL_VOLUME_NOT_EMPTY_ = 15628;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INSTALL_VOLUME_OFFLINE_ = 15629;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INSTALL_VOLUME_CORRUPT_ = 15630;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_NEEDS_REGISTRATION_ = 15631;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INSTALL_WRONG_PROCESSOR_ARCHITECTURE_ = 15632;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DEV_SIDELOAD_LIMIT_EXCEEDED_ = 15633;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INSTALL_OPTIONAL_PACKAGE_REQUIRES_MAIN_PACKAGE_ = 15634;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PACKAGE_NOT_SUPPORTED_ON_FILESYSTEM_ = 15635;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PACKAGE_MOVE_BLOCKED_BY_STREAMING_ = 15636;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INSTALL_OPTIONAL_PACKAGE_APPLICATIONID_NOT_UNIQUE_ = 15637;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PACKAGE_STAGING_ONHOLD_ = 15638;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INSTALL_INVALID_RELATED_SET_UPDATE_ = 15639;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_INSTALL_OPTIONAL_PACKAGE_REQUIRES_MAIN_PACKAGE_FULLTRUST_CAPABILITY_ = 15640;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_DEPLOYMENT_BLOCKED_BY_USER_LOG_OFF_ = 15641;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PROVISION_OPTIONAL_PACKAGE_REQUIRES_MAIN_PACKAGE_PROVISIONED_ = 15642;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PACKAGES_REPUTATION_CHECK_FAILED_ = 15643;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_PACKAGES_REPUTATION_CHECK_TIMEDOUT_ = 15644;
BOOST_CONSTEXPR_OR_CONST DWORD_ APPMODEL_ERROR_NO_PACKAGE_ = 15700;
BOOST_CONSTEXPR_OR_CONST DWORD_ APPMODEL_ERROR_PACKAGE_RUNTIME_CORRUPT_ = 15701;
BOOST_CONSTEXPR_OR_CONST DWORD_ APPMODEL_ERROR_PACKAGE_IDENTITY_CORRUPT_ = 15702;
BOOST_CONSTEXPR_OR_CONST DWORD_ APPMODEL_ERROR_NO_APPLICATION_ = 15703;
BOOST_CONSTEXPR_OR_CONST DWORD_ APPMODEL_ERROR_DYNAMIC_PROPERTY_READ_FAILED_ = 15704;
BOOST_CONSTEXPR_OR_CONST DWORD_ APPMODEL_ERROR_DYNAMIC_PROPERTY_INVALID_ = 15705;
BOOST_CONSTEXPR_OR_CONST DWORD_ APPMODEL_ERROR_PACKAGE_NOT_AVAILABLE_ = 15706;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_STATE_LOAD_STORE_FAILED_ = 15800;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_STATE_GET_VERSION_FAILED_ = 15801;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_STATE_SET_VERSION_FAILED_ = 15802;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_STATE_STRUCTURED_RESET_FAILED_ = 15803;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_STATE_OPEN_CONTAINER_FAILED_ = 15804;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_STATE_CREATE_CONTAINER_FAILED_ = 15805;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_STATE_DELETE_CONTAINER_FAILED_ = 15806;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_STATE_READ_SETTING_FAILED_ = 15807;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_STATE_WRITE_SETTING_FAILED_ = 15808;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_STATE_DELETE_SETTING_FAILED_ = 15809;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_STATE_QUERY_SETTING_FAILED_ = 15810;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_STATE_READ_COMPOSITE_SETTING_FAILED_ = 15811;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_STATE_WRITE_COMPOSITE_SETTING_FAILED_ = 15812;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_STATE_ENUMERATE_CONTAINER_FAILED_ = 15813;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_STATE_ENUMERATE_SETTINGS_FAILED_ = 15814;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_STATE_COMPOSITE_SETTING_VALUE_SIZE_LIMIT_EXCEEDED_ = 15815;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_STATE_SETTING_VALUE_SIZE_LIMIT_EXCEEDED_ = 15816;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_STATE_SETTING_NAME_SIZE_LIMIT_EXCEEDED_ = 15817;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_STATE_CONTAINER_NAME_SIZE_LIMIT_EXCEEDED_ = 15818;
BOOST_CONSTEXPR_OR_CONST DWORD_ ERROR_API_UNAVAILABLE_ = 15841;
BOOST_CONSTEXPR_OR_CONST DWORD_ STORE_ERROR_UNLICENSED_ = 15861;
BOOST_CONSTEXPR_OR_CONST DWORD_ STORE_ERROR_UNLICENSED_USER_ = 15862;
BOOST_CONSTEXPR_OR_CONST DWORD_ STORE_ERROR_PENDING_COM_TRANSACTION_ = 15863;
BOOST_CONSTEXPR_OR_CONST DWORD_ STORE_ERROR_LICENSE_REVOKED_ = 15864;

} // namespace winapi
} // namespace boost

#include <boost/winapi/detail/footer.hpp>

#endif // BOOST_WINAPI_ERROR_CODES_HPP_INCLUDED_
