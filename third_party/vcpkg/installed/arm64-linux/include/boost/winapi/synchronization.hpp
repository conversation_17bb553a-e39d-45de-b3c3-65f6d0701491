/*
 * Copyright 2010 <PERSON> Escriba
 * Copyright 2015 <PERSON><PERSON>
 *
 * Distributed under the Boost Software License, Version 1.0.
 * See http://www.boost.org/LICENSE_1_0.txt
 */

#ifndef BOOST_WINAPI_SYNCHRONIZATION_HPP_INCLUDED_
#define BOOST_WINAPI_SYNCHRONIZATION_HPP_INCLUDED_

#include <boost/winapi/basic_types.hpp>
#include <boost/winapi/critical_section.hpp>
#include <boost/winapi/wait.hpp>
#include <boost/winapi/event.hpp>
#include <boost/winapi/mutex.hpp>
#include <boost/winapi/semaphore.hpp>
#include <boost/winapi/init_once.hpp>
#include <boost/winapi/srw_lock.hpp>
#include <boost/winapi/condition_variable.hpp>
#include <boost/winapi/wait_on_address.hpp>
#include <boost/winapi/apc.hpp>

#ifdef BOOST_HAS_PRAGMA_ONCE
#pragma once
#endif

#endif // BOOST_WINAPI_SYNCHRONIZATION_HPP_INCLUDED_
