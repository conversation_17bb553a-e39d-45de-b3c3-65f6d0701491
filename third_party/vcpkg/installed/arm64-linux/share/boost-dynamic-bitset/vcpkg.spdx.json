{"$schema": "https://raw.githubusercontent.com/spdx/spdx-spec/v2.2.1/schemas/spdx-schema.json", "spdxVersion": "SPDX-2.2", "dataLicense": "CC0-1.0", "SPDXID": "SPDXRef-DOCUMENT", "documentNamespace": "https://spdx.org/spdxdocs/boost-dynamic-bitset-arm64-linux-1.87.0-afc9a3f5-148a-4992-8d9e-c4625ad4afe1", "name": "boost-dynamic-bitset:arm64-linux@1.87.0 e0b5b2047f296db02250237731f66556d9735c98a2bda21d19944d9cf0b14f0e", "creationInfo": {"creators": ["Tool: vcpkg-2025-04-07-b8b513ba8778c918cff49c3e837aae5999d5d2aa"], "created": "2025-06-10T09:13:24Z"}, "relationships": [{"spdxElementId": "SPDXRef-port", "relationshipType": "GENERATES", "relatedSpdxElement": "SPDXRef-binary"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-0"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-1"}, {"spdxElementId": "SPDXRef-binary", "relationshipType": "GENERATED_FROM", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-0", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-0", "relationshipType": "DEPENDENCY_MANIFEST_OF", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-1", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}], "packages": [{"name": "boost-dynamic-bitset", "SPDXID": "SPDXRef-port", "versionInfo": "1.87.0", "downloadLocation": "git+https://github.com/Microsoft/vcpkg#ports/boost-dynamic-bitset", "homepage": "https://www.boost.org/libs/dynamic_bitset", "licenseConcluded": "BSL-1.0", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "description": "Boost dynamic_bitset module", "comment": "This is the port (recipe) consumed by vcpkg."}, {"name": "boost-dynamic-bitset:arm64-linux", "SPDXID": "SPDXRef-binary", "versionInfo": "e0b5b2047f296db02250237731f66556d9735c98a2bda21d19944d9cf0b14f0e", "downloadLocation": "NONE", "licenseConcluded": "BSL-1.0", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "comment": "This is a binary package built by vcpkg."}, {"SPDXID": "SPDXRef-resource-0", "name": "boostorg/dynamic_bitset", "downloadLocation": "git+https://github.com/boostorg/dynamic_bitset@boost-1.87.0", "licenseConcluded": "NOASSERTION", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "checksums": [{"algorithm": "SHA512", "checksumValue": "e01d1d36a585a6cb382ddbcbea6d4f94692018c2a6f9e548422565cd22bdf2fb1af36e0bcf5f7950cc7fda6068501fda308d646841186967b0995eb41755fd6f"}]}], "files": [{"fileName": "./vcpkg.json", "SPDXID": "SPDXRef-file-0", "checksums": [{"algorithm": "SHA256", "checksumValue": "12afbe8f24c10926e6833b4a4774430e0aaab73f95673cd1f372f2ae108d7fdb"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./portfile.cmake", "SPDXID": "SPDXRef-file-1", "checksums": [{"algorithm": "SHA256", "checksumValue": "aea3ca5721bf1b88d76d92dd2448bd4e9262b402e5f6485e83fb42025703d06e"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}]}