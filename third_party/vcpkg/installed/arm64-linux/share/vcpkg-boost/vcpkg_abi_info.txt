boost-install.cmake 19f0f619f254311a6b55bee022a584ae1ff040d3bdb0728e646833e62c9e6dc9
cmake 0
features core
portfile.cmake ce7596f5f687382765463a53fd02aae8a4aecc4ab1d009b8eade3f5bcfc4066b
ports.cmake a9f8f745f9f4d225039f9810e79670f10f38c7455222865616ce430a06f75d1f
post_build_checks 2
triplet arm64-linux
triplet_abi 971960c7eb0461eeba2679a4edd056aa99288fbd95bb9a4e2989e5f4a78158ab-7300d6fc76582335b0d9fd6dc6a00f69d7ba728b9d6444ad2a9d42f054e1d991-1d43c82f51082f8427993d1dac28c7cb4e3749af
usage.in 8af069bf0969049bf10b8df8fb5ba55184bf1b816fb61773022555ef35d038a4
vcpkg-port-config.cmake 265420304e6dc003f7771114d1ce18392b055b6b54e666edecf74a6ad07b5464
vcpkg.json 1a5c536c91efe25c64bf85acbb031ee37f79a0ab5ed3ff1dd64bdb317174b888
vcpkg_install_copyright ba6c169ab4e59fa05682e530cdeb883767de22c8391f023d4e6844a7ec5dd3d2
