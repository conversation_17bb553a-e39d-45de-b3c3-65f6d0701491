boost-assert 3245b8cef1975eb17584e363015c24184970f16eac38da3efcf0e50047d59de8
boost-cmake c2f9d310477a33734a24c277fd7062de3f66a52aa462954c392ea364152275d2
boost-config 94d5d122f9ebdf91e590c7784e001b446a6b97eef8209076f7d30e2c904830be
boost-core cd5e9603f3beaa44da04a286f84dc358866ff2f3cc265e53847d64d45c3a2a02
boost-headers 1fa483b45b4ae788b6b025984193f1cf54839c8c418e21c4dc59e75cb634145d
boost-mp11 cb910bfd052630d241acc3574231ab5ee4822db5c853fbe10bd33182b568efd5
boost-pool 7cbc65b78ad3e3254b3b885aefed1b9cf63f54e95cb3fd28b402ed82e029c2d9
boost-predef c33e3bcdb00644c3cdc338715c31093dcb08d874229c60ebd6d87550d2929bfa
boost-smart-ptr 239a9c88250ca040432a86d51397371ec9b3b2985d97fe4eb64e978516bf6bf7
cmake 0
features core
marmasm.patch fdc92ce93c003006c5c8d3defe627f0d389ccc90cbf0a0d398364c966dd1b050
portfile.cmake 07949372f9c9669b031eeed1fe8c3c7e7e88055563bdfb8f8ee8481d81447af7
ports.cmake a9f8f745f9f4d225039f9810e79670f10f38c7455222865616ce430a06f75d1f
post_build_checks 2
triplet arm64-linux
triplet_abi 971960c7eb0461eeba2679a4edd056aa99288fbd95bb9a4e2989e5f4a78158ab-7300d6fc76582335b0d9fd6dc6a00f69d7ba728b9d6444ad2a9d42f054e1d991-1d43c82f51082f8427993d1dac28c7cb4e3749af
vcpkg.json 7968efe0e8944dad62d3b868c316e8f59feb32e6d004da09f6e709b4713919c3
vcpkg_from_git 96ed81968f76354c00096dd8cd4e63c6a235fa969334a11ab18d11c0c512ff58
vcpkg_from_github b743742296a114ea1b18ae99672e02f142c4eb2bef7f57d36c038bedbfb0502f
