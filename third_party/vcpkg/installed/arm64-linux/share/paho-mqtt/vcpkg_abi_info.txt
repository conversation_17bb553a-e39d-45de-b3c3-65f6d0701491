cmake 0
features core
fix-ODR-libuuid-linux.patch ebaa47ddc8e5c0cd946b724dc55885756616c42e8ed5ed44c09a52c7611de646
fix-unresolvedsymbol-arm.patch d99ebb089a988de02dd62e5accd1a1e2047790083da62a32b2f6e2b73ef8c943
libuuid e3be2f1e1ae64ca91ac07a5ddd7e61d95ad202017298b594b5dea059de522b08
openssl 6023758f537eac05d201bb9ddaed4d9dbc019260340c53098cc07b0eb4dfc2a5
portfile.cmake 023711d7b6ac4fe89374121f6521d49f842f9cec1a2f2bd2fa82b94f2f8b78f8
ports.cmake a9f8f745f9f4d225039f9810e79670f10f38c7455222865616ce430a06f75d1f
post_build_checks 2
triplet arm64-linux
triplet_abi 971960c7eb0461eeba2679a4edd056aa99288fbd95bb9a4e2989e5f4a78158ab-7300d6fc76582335b0d9fd6dc6a00f69d7ba728b9d6444ad2a9d42f054e1d991-1d43c82f51082f8427993d1dac28c7cb4e3749af
vcpkg-cmake 54c8ef002d40e1119b296ccd854f951f842d35332e8f78ef641b5f85bd6e84a5
vcpkg-cmake-config 4d9dbdb43f5b22c6a049c80770a648167f13ce34eef888a375cb405c0eb6b7c7
vcpkg.json c8a5f3df2f074ff946aebb7138397ee4d5531374c2a4b7023fc4104ff7992547
vcpkg_copy_pdbs d15c2f2822d93ecb9023af44803328e491c5374136e0813600289582a72d196d
vcpkg_copy_tools 3d45ff761bddbabe8923b52330168dc3abd295fa469d3f2e47cb14dce85332d5
vcpkg_from_git 96ed81968f76354c00096dd8cd4e63c6a235fa969334a11ab18d11c0c512ff58
vcpkg_from_github b743742296a114ea1b18ae99672e02f142c4eb2bef7f57d36c038bedbfb0502f
vcpkg_install_copyright ba6c169ab4e59fa05682e530cdeb883767de22c8391f023d4e6844a7ec5dd3d2
vcpkg_replace_string b450deb79207478b37119743e00808ebc42de0628e7b98c14ab24728bd5c78b8
