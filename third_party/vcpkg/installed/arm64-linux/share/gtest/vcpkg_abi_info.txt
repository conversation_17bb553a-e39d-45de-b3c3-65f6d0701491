001-fix-UWP-death-test.patch fca2d973dfbca64b3575d7453a189c3ea9db4e413838cd40343d39f64d813f3b
clang-tidy-no-lint.patch f276fb224af39a1e6ad9a88719bd49f2ccc88b7837763b2b4f3d74f107894957
cmake 0
features core
fix-main-lib-path.patch 40f5766826c684a063649f308531ba2efe829d540255f99db0be0aa43fb6a691
portfile.cmake 91e9824300769d9362f91dca62ef253f0d354769d19f6432d80f9321a8401788
ports.cmake a9f8f745f9f4d225039f9810e79670f10f38c7455222865616ce430a06f75d1f
post_build_checks 2
triplet arm64-linux
triplet_abi 971960c7eb0461eeba2679a4edd056aa99288fbd95bb9a4e2989e5f4a78158ab-7300d6fc76582335b0d9fd6dc6a00f69d7ba728b9d6444ad2a9d42f054e1d991-1d43c82f51082f8427993d1dac28c7cb4e3749af
usage 48a60f73437a325fd92cebb25c77ce48108f20610836039420b7e2070c91132a
vcpkg-cmake 54c8ef002d40e1119b296ccd854f951f842d35332e8f78ef641b5f85bd6e84a5
vcpkg-cmake-config 4d9dbdb43f5b22c6a049c80770a648167f13ce34eef888a375cb405c0eb6b7c7
vcpkg.json 91dcdf645f7523338fa7d74332ed5f140837ad5bc99d7952e3a55a817bab31c4
vcpkg_copy_pdbs d15c2f2822d93ecb9023af44803328e491c5374136e0813600289582a72d196d
vcpkg_fixup_pkgconfig 1a15f6c6d8e2b244d83a7514a0412d339127d2217d1df60ad1388b546c85f777
vcpkg_from_git 96ed81968f76354c00096dd8cd4e63c6a235fa969334a11ab18d11c0c512ff58
vcpkg_from_github b743742296a114ea1b18ae99672e02f142c4eb2bef7f57d36c038bedbfb0502f
vcpkg_replace_string b450deb79207478b37119743e00808ebc42de0628e7b98c14ab24728bd5c78b8
