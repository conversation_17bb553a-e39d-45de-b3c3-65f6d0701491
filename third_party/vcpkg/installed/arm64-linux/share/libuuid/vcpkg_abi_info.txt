CMakeLists.txt e44e9a252df03125d721785cbe93241f6c0b1c7260fa8981f38c8587e7479fe7
cmake 0
config.linux.h 95b82bd74377635a7e67a95b1b96143b370443abc054fc42c728e5d5a4153297
features core
portfile.cmake b04be114cab9b5bcf98d29154c3a755c5398b0d2628f61ad78fe1c5fe9a8efec
ports.cmake a9f8f745f9f4d225039f9810e79670f10f38c7455222865616ce430a06f75d1f
post_build_checks 2
triplet arm64-linux
triplet_abi 971960c7eb0461eeba2679a4edd056aa99288fbd95bb9a4e2989e5f4a78158ab-7300d6fc76582335b0d9fd6dc6a00f69d7ba728b9d6444ad2a9d42f054e1d991-1d43c82f51082f8427993d1dac28c7cb4e3749af
unofficial-libuuid-config.cmake.in d440b00400bed20ebc6c0e4ef9f646c558b8e752fa11f0a78ff0392a0653f584
vcpkg-cmake 54c8ef002d40e1119b296ccd854f951f842d35332e8f78ef641b5f85bd6e84a5
vcpkg-cmake-config 4d9dbdb43f5b22c6a049c80770a648167f13ce34eef888a375cb405c0eb6b7c7
vcpkg.json eff230e1b020475dfaf67745b4116b470ec67d73e753d296e1f5f19bcd18ebe1
vcpkg_copy_pdbs d15c2f2822d93ecb9023af44803328e491c5374136e0813600289582a72d196d
vcpkg_fixup_pkgconfig 1a15f6c6d8e2b244d83a7514a0412d339127d2217d1df60ad1388b546c85f777
vcpkg_from_sourceforge 00cb7d5767d56fdd8a1715ebd3c159fccd44dc17653522e23d2e507bce44a4f8
vcpkg_install_copyright ba6c169ab4e59fa05682e530cdeb883767de22c8391f023d4e6844a7ec5dd3d2
