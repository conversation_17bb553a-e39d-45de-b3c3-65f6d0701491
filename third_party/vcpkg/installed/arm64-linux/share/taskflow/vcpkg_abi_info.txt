cmake 0
features core
portfile.cmake cc841c300f71dd9e54b456bef19dbabbe3458c76dd512809fd997b831287eeb0
ports.cmake a9f8f745f9f4d225039f9810e79670f10f38c7455222865616ce430a06f75d1f
post_build_checks 2
triplet arm64-linux
triplet_abi 971960c7eb0461eeba2679a4edd056aa99288fbd95bb9a4e2989e5f4a78158ab-7300d6fc76582335b0d9fd6dc6a00f69d7ba728b9d6444ad2a9d42f054e1d991-1d43c82f51082f8427993d1dac28c7cb4e3749af
vcpkg-cmake 54c8ef002d40e1119b296ccd854f951f842d35332e8f78ef641b5f85bd6e84a5
vcpkg-cmake-config 4d9dbdb43f5b22c6a049c80770a648167f13ce34eef888a375cb405c0eb6b7c7
vcpkg.json 0c2dc861ed1f298762baad8e4dc7ce6da68da33e75436e967146b8c2eaadfd72
vcpkg_from_git 96ed81968f76354c00096dd8cd4e63c6a235fa969334a11ab18d11c0c512ff58
vcpkg_from_github b743742296a114ea1b18ae99672e02f142c4eb2bef7f57d36c038bedbfb0502f
vcpkg_install_copyright ba6c169ab4e59fa05682e530cdeb883767de22c8391f023d4e6844a7ec5dd3d2
