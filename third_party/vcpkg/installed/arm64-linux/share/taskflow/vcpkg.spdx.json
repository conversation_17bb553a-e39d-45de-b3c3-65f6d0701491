{"$schema": "https://raw.githubusercontent.com/spdx/spdx-spec/v2.2.1/schemas/spdx-schema.json", "spdxVersion": "SPDX-2.2", "dataLicense": "CC0-1.0", "SPDXID": "SPDXRef-DOCUMENT", "documentNamespace": "https://spdx.org/spdxdocs/taskflow-arm64-linux-3.9.0-95eb4502-884b-42b9-ae85-29601c080d33", "name": "taskflow:arm64-linux@3.9.0 d02f94f75cff2de638b8fcf2e884efa6a1e7132495693bb0c20e3c7d91971267", "creationInfo": {"creators": ["Tool: vcpkg-2025-04-07-b8b513ba8778c918cff49c3e837aae5999d5d2aa"], "created": "2025-06-10T09:27:06Z"}, "relationships": [{"spdxElementId": "SPDXRef-port", "relationshipType": "GENERATES", "relatedSpdxElement": "SPDXRef-binary"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-0"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-1"}, {"spdxElementId": "SPDXRef-binary", "relationshipType": "GENERATED_FROM", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-0", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-0", "relationshipType": "DEPENDENCY_MANIFEST_OF", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-1", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}], "packages": [{"name": "taskflow", "SPDXID": "SPDXRef-port", "versionInfo": "3.9.0", "downloadLocation": "git+https://github.com/Microsoft/vcpkg#ports/taskflow", "homepage": "https://github.com/taskflow/taskflow", "licenseConcluded": "MIT", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "description": "Fast Parallel Tasking Programming Library using Modern C++", "comment": "This is the port (recipe) consumed by vcpkg."}, {"name": "taskflow:arm64-linux", "SPDXID": "SPDXRef-binary", "versionInfo": "d02f94f75cff2de638b8fcf2e884efa6a1e7132495693bb0c20e3c7d91971267", "downloadLocation": "NONE", "licenseConcluded": "MIT", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "comment": "This is a binary package built by vcpkg."}, {"SPDXID": "SPDXRef-resource-0", "name": "taskflow/taskflow", "downloadLocation": "git+https://github.com/taskflow/taskflow@v3.9.0", "licenseConcluded": "NOASSERTION", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "checksums": [{"algorithm": "SHA512", "checksumValue": "d31f5f07b644044460887923a1be8a2c655f8b3edfcfa811318333101dc84f11bd524a1d1b0edc89752bb401e9e4e33c569c054abbe8f77139fa108c0aebb1c4"}]}], "files": [{"fileName": "./vcpkg.json", "SPDXID": "SPDXRef-file-0", "checksums": [{"algorithm": "SHA256", "checksumValue": "0c2dc861ed1f298762baad8e4dc7ce6da68da33e75436e967146b8c2eaadfd72"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./portfile.cmake", "SPDXID": "SPDXRef-file-1", "checksums": [{"algorithm": "SHA256", "checksumValue": "cc841c300f71dd9e54b456bef19dbabbe3458c76dd512809fd997b831287eeb0"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}]}