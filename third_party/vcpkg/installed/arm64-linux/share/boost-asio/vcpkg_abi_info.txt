boost-align afde3c89584fcc80897969cf3b0c21ff9770695fb91919a81915c048bb7f237a
boost-assert 3245b8cef1975eb17584e363015c24184970f16eac38da3efcf0e50047d59de8
boost-cmake c2f9d310477a33734a24c277fd7062de3f66a52aa462954c392ea364152275d2
boost-config 94d5d122f9ebdf91e590c7784e001b446a6b97eef8209076f7d30e2c904830be
boost-context 61ea49fd06b89dff27c88f9bcfacbe6156ee5b04da68f64a1c773eacc60faa95
boost-date-time 70b5a88e20f486e3fd0dc1b9b54a400327403757bae1d4a14e4996952529c68f
boost-headers 1fa483b45b4ae788b6b025984193f1cf54839c8c418e21c4dc59e75cb634145d
boost-system 54b74ab3a91c3c11e32264b118f8024efdd3020713e5f522ea48501342e2884d
boost-throw-exception cb7cf3422cfdb27853202c34af872339231f93fe976dc1721ef700682b141208
cmake 0
features core
opt-dep.diff 2a139ba11c2f5502cdbe1536da68068b99c21162ae95e850ac696f469d9572f6
portfile.cmake 2b32d1d229db61b2a4c1026ef40eb00c208eb2e42fe2660591c05b99be10a89e
ports.cmake a9f8f745f9f4d225039f9810e79670f10f38c7455222865616ce430a06f75d1f
post_build_checks 2
triplet arm64-linux
triplet_abi 971960c7eb0461eeba2679a4edd056aa99288fbd95bb9a4e2989e5f4a78158ab-7300d6fc76582335b0d9fd6dc6a00f69d7ba728b9d6444ad2a9d42f054e1d991-1d43c82f51082f8427993d1dac28c7cb4e3749af
vcpkg.json e87241c3eadbda3cdf6496c022b5afcd199563c02e4cc939c1636d3447da68d0
vcpkg_from_git 96ed81968f76354c00096dd8cd4e63c6a235fa969334a11ab18d11c0c512ff58
vcpkg_from_github b743742296a114ea1b18ae99672e02f142c4eb2bef7f57d36c038bedbfb0502f
