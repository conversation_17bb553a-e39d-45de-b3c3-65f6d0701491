0001-Prevent-invalid-inclusions-when-HAVE_-is-set-to-0.patch 750b9542cb55e6328cca01d3ca997f1373b9530afa95e04213168676936e7bfa
0002-build-static-or-shared-not-both.patch dd4945e8af55d9feb65af706cb0254a6925dc4ac12789586c0d28acf323ad075
0003-android-and-mingw-fixes.patch 63e62c9bae3952b8cca832e01aa96ef3b2696b21976c5031d6d6d79937d8d54c
cmake 0
features core
portfile.cmake b14cee469c2da06e036e63293c0b6e6509924462e9b341b317986df46b281695
ports.cmake a9f8f745f9f4d225039f9810e79670f10f38c7455222865616ce430a06f75d1f
post_build_checks 2
triplet arm64-linux
triplet_abi 971960c7eb0461eeba2679a4edd056aa99288fbd95bb9a4e2989e5f4a78158ab-7300d6fc76582335b0d9fd6dc6a00f69d7ba728b9d6444ad2a9d42f054e1d991-1d43c82f51082f8427993d1dac28c7cb4e3749af
usage be22662327df993eebc437495add75acb365ab18d37c7e5de735d4ea4f5d3083
vcpkg-cmake 54c8ef002d40e1119b296ccd854f951f842d35332e8f78ef641b5f85bd6e84a5
vcpkg-cmake-wrapper.cmake 5d49ef2ee6448479c2aad0e5f732e2676eaba0411860f9bebabe6002d66f57d1
vcpkg.json 8ad7072167e1bf56d2ed531e43ecc95026d5b305ed0393140d5ec78f5c6b6c00
vcpkg_copy_pdbs d15c2f2822d93ecb9023af44803328e491c5374136e0813600289582a72d196d
vcpkg_fixup_pkgconfig 1a15f6c6d8e2b244d83a7514a0412d339127d2217d1df60ad1388b546c85f777
vcpkg_from_git 96ed81968f76354c00096dd8cd4e63c6a235fa969334a11ab18d11c0c512ff58
vcpkg_from_github b743742296a114ea1b18ae99672e02f142c4eb2bef7f57d36c038bedbfb0502f
vcpkg_replace_string b450deb79207478b37119743e00808ebc42de0628e7b98c14ab24728bd5c78b8
