boost-assert 3245b8cef1975eb17584e363015c24184970f16eac38da3efcf0e50047d59de8
boost-cmake c2f9d310477a33734a24c277fd7062de3f66a52aa462954c392ea364152275d2
boost-config 94d5d122f9ebdf91e590c7784e001b446a6b97eef8209076f7d30e2c904830be
boost-core cd5e9603f3beaa44da04a286f84dc358866ff2f3cc265e53847d64d45c3a2a02
boost-headers 1fa483b45b4ae788b6b025984193f1cf54839c8c418e21c4dc59e75cb634145d
boost-integer 635f28a2daf0fa58ab47c8c26d65e4e306d75202ee8a776382a0b7b6d09867d4
boost-move 8c1e6d515be042166f10a1535f02ea14ae8537bbda4a9fff5c5a1afc10bfaccb
boost-mpl c9666ab1cfff28308a577e0ac519fc28a9c3ea33dd6235ccb3714362e044abe2
boost-predef c33e3bcdb00644c3cdc338715c31093dcb08d874229c60ebd6d87550d2929bfa
boost-ratio 17c2e75cbeb9bc79c62578d16d451c323e43e96375313963d414726920307ee6
boost-static-assert 5bdacd62569fd4dc1abae15ad00008ce1ea70dd190892dc68903f99e97822243
boost-system 54b74ab3a91c3c11e32264b118f8024efdd3020713e5f522ea48501342e2884d
boost-throw-exception cb7cf3422cfdb27853202c34af872339231f93fe976dc1721ef700682b141208
boost-type-traits 386aa7b34165317f0982924bd0f0f324737e30bb1f68545f7554c85f3c1e7031
boost-typeof 57642a68cf18b472c1652016579ff9446cc79a62a0902606947ceb5f552be367
boost-utility b8e8716d390c71985d43b190bdfcdb3811c216df8a42f14170aec1557b78f6dc
boost-winapi 4c591a1c3f14929e8cfe1c7fcc4b8e9c9d0c07fb119faff1e0f848b40b0fd51c
cmake 0
features core
portfile.cmake 1c6a90ee913bbe8d265127481ef6a18d75bf2e4d0c77a91d2b917b59156b000a
ports.cmake a9f8f745f9f4d225039f9810e79670f10f38c7455222865616ce430a06f75d1f
post_build_checks 2
triplet arm64-linux
triplet_abi 971960c7eb0461eeba2679a4edd056aa99288fbd95bb9a4e2989e5f4a78158ab-7300d6fc76582335b0d9fd6dc6a00f69d7ba728b9d6444ad2a9d42f054e1d991-1d43c82f51082f8427993d1dac28c7cb4e3749af
vcpkg.json c9b6d713d7145d6b9873cb53b5ce0e728b38b04d157844ef36b3642ac5bf6830
vcpkg_from_git 96ed81968f76354c00096dd8cd4e63c6a235fa969334a11ab18d11c0c512ff58
vcpkg_from_github b743742296a114ea1b18ae99672e02f142c4eb2bef7f57d36c038bedbfb0502f
