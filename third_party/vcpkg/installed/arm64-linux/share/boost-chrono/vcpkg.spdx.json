{"$schema": "https://raw.githubusercontent.com/spdx/spdx-spec/v2.2.1/schemas/spdx-schema.json", "spdxVersion": "SPDX-2.2", "dataLicense": "CC0-1.0", "SPDXID": "SPDXRef-DOCUMENT", "documentNamespace": "https://spdx.org/spdxdocs/boost-chrono-arm64-linux-1.87.0-36fcfb60-0b7a-4668-890c-9912ac973a9e", "name": "boost-chrono:arm64-linux@1.87.0 ad173a3da775450e72c1943570e909e333a66ed99bfe45a0e6eaa5ff3930cedf", "creationInfo": {"creators": ["Tool: vcpkg-2025-04-07-b8b513ba8778c918cff49c3e837aae5999d5d2aa"], "created": "2025-06-10T09:13:15Z"}, "relationships": [{"spdxElementId": "SPDXRef-port", "relationshipType": "GENERATES", "relatedSpdxElement": "SPDXRef-binary"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-0"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-1"}, {"spdxElementId": "SPDXRef-binary", "relationshipType": "GENERATED_FROM", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-0", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-0", "relationshipType": "DEPENDENCY_MANIFEST_OF", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-1", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}], "packages": [{"name": "boost-chrono", "SPDXID": "SPDXRef-port", "versionInfo": "1.87.0", "downloadLocation": "git+https://github.com/Microsoft/vcpkg#ports/boost-chrono", "homepage": "https://www.boost.org/libs/chrono", "licenseConcluded": "BSL-1.0", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "description": "Boost chrono module", "comment": "This is the port (recipe) consumed by vcpkg."}, {"name": "boost-chrono:arm64-linux", "SPDXID": "SPDXRef-binary", "versionInfo": "ad173a3da775450e72c1943570e909e333a66ed99bfe45a0e6eaa5ff3930cedf", "downloadLocation": "NONE", "licenseConcluded": "BSL-1.0", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "comment": "This is a binary package built by vcpkg."}, {"SPDXID": "SPDXRef-resource-0", "name": "boostorg/chrono", "downloadLocation": "git+https://github.com/boostorg/chrono@boost-1.87.0", "licenseConcluded": "NOASSERTION", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "checksums": [{"algorithm": "SHA512", "checksumValue": "c339a9a5e15d16494db768cd827062bac2beef0fc98c2d9cb821f2984d547dc5c27b06a0774b0115c27201ddde3b83e38c482b98949b53d28588eb76611ecd18"}]}], "files": [{"fileName": "./vcpkg.json", "SPDXID": "SPDXRef-file-0", "checksums": [{"algorithm": "SHA256", "checksumValue": "c9b6d713d7145d6b9873cb53b5ce0e728b38b04d157844ef36b3642ac5bf6830"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./portfile.cmake", "SPDXID": "SPDXRef-file-1", "checksums": [{"algorithm": "SHA256", "checksumValue": "1c6a90ee913bbe8d265127481ef6a18d75bf2e4d0c77a91d2b917b59156b000a"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}]}