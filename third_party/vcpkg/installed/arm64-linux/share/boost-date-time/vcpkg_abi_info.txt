boost-algorithm 45a4708197893eedb763a11f32f9dd589a232ebd28c22536023f8d516e2ae076
boost-assert 3245b8cef1975eb17584e363015c24184970f16eac38da3efcf0e50047d59de8
boost-cmake c2f9d310477a33734a24c277fd7062de3f66a52aa462954c392ea364152275d2
boost-config 94d5d122f9ebdf91e590c7784e001b446a6b97eef8209076f7d30e2c904830be
boost-core cd5e9603f3beaa44da04a286f84dc358866ff2f3cc265e53847d64d45c3a2a02
boost-headers 1fa483b45b4ae788b6b025984193f1cf54839c8c418e21c4dc59e75cb634145d
boost-io bf0ba4dadf5887d63e172c2970c816c018dad41b70333583426c1a940b468635
boost-lexical-cast 2edb0693dcbb826f3b13a3635b110efbc8ed9023595fef279d22ed494e4cd843
boost-numeric-conversion 36c02242e820cd661de5f5f5a199e831820052a28db7467c4bc4f95dc8ee9b3f
boost-range 3a6d8b11702bfc5f6c7873fa39f81672cc8c394eae227e3a642173d2f14e26da
boost-smart-ptr 239a9c88250ca040432a86d51397371ec9b3b2985d97fe4eb64e978516bf6bf7
boost-static-assert 5bdacd62569fd4dc1abae15ad00008ce1ea70dd190892dc68903f99e97822243
boost-throw-exception cb7cf3422cfdb27853202c34af872339231f93fe976dc1721ef700682b141208
boost-tokenizer 27f275cef18ba2c09615251c3b922a6dac475b7907987bd5c5c98fc9f836153c
boost-type-traits 386aa7b34165317f0982924bd0f0f324737e30bb1f68545f7554c85f3c1e7031
boost-utility b8e8716d390c71985d43b190bdfcdb3811c216df8a42f14170aec1557b78f6dc
boost-winapi 4c591a1c3f14929e8cfe1c7fcc4b8e9c9d0c07fb119faff1e0f848b40b0fd51c
cmake 0
features core
portfile.cmake 3edb599b1b0e0a19214fa1d48c340638448bcc22d11ba3a7ee1c18d6f3ddd546
ports.cmake a9f8f745f9f4d225039f9810e79670f10f38c7455222865616ce430a06f75d1f
post_build_checks 2
triplet arm64-linux
triplet_abi 971960c7eb0461eeba2679a4edd056aa99288fbd95bb9a4e2989e5f4a78158ab-7300d6fc76582335b0d9fd6dc6a00f69d7ba728b9d6444ad2a9d42f054e1d991-1d43c82f51082f8427993d1dac28c7cb4e3749af
vcpkg.json 7729e383be35ccd3a9c7f03693c5bed4c587c092adcb14b915d09dd7806fee90
vcpkg_from_git 96ed81968f76354c00096dd8cd4e63c6a235fa969334a11ab18d11c0c512ff58
vcpkg_from_github b743742296a114ea1b18ae99672e02f142c4eb2bef7f57d36c038bedbfb0502f
