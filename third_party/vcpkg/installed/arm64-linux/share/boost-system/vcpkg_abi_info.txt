boost-assert 3245b8cef1975eb17584e363015c24184970f16eac38da3efcf0e50047d59de8
boost-cmake c2f9d310477a33734a24c277fd7062de3f66a52aa462954c392ea364152275d2
boost-config 94d5d122f9ebdf91e590c7784e001b446a6b97eef8209076f7d30e2c904830be
boost-headers 1fa483b45b4ae788b6b025984193f1cf54839c8c418e21c4dc59e75cb634145d
boost-throw-exception cb7cf3422cfdb27853202c34af872339231f93fe976dc1721ef700682b141208
boost-variant2 a287966245a55072b668fcc94b348e220abe54a000a61b80660b5bc6af2bac6a
boost-winapi 4c591a1c3f14929e8cfe1c7fcc4b8e9c9d0c07fb119faff1e0f848b40b0fd51c
cmake 0
compat.diff 57257cafa3402afa053ef011ac89a88c6edde08a23a61c282ff373fde342e597
features core
portfile.cmake 40b4e42014f32af2470773b031a296ac0f2f2d5284735f66a1305f976274ce92
ports.cmake a9f8f745f9f4d225039f9810e79670f10f38c7455222865616ce430a06f75d1f
post_build_checks 2
triplet arm64-linux
triplet_abi 971960c7eb0461eeba2679a4edd056aa99288fbd95bb9a4e2989e5f4a78158ab-7300d6fc76582335b0d9fd6dc6a00f69d7ba728b9d6444ad2a9d42f054e1d991-1d43c82f51082f8427993d1dac28c7cb4e3749af
vcpkg.json 46ab1efbb712294bd4a63c545228af79fae977db123c92e394b60122658f4ded
vcpkg_buildpath_length_warning fa440f3734e34b462ef96f3e89f0a9c5a11b609110a15a162d24da9218693fd1
vcpkg_from_git 96ed81968f76354c00096dd8cd4e63c6a235fa969334a11ab18d11c0c512ff58
vcpkg_from_github b743742296a114ea1b18ae99672e02f142c4eb2bef7f57d36c038bedbfb0502f
