boost-array 8d956c4f883b509cfd5a6c4df23196eddba52e0e489a05a4c6130e4e2ec84ba2
boost-assert 3245b8cef1975eb17584e363015c24184970f16eac38da3efcf0e50047d59de8
boost-cmake c2f9d310477a33734a24c277fd7062de3f66a52aa462954c392ea364152275d2
boost-config 94d5d122f9ebdf91e590c7784e001b446a6b97eef8209076f7d30e2c904830be
boost-core cd5e9603f3beaa44da04a286f84dc358866ff2f3cc265e53847d64d45c3a2a02
boost-dynamic-bitset e0b5b2047f296db02250237731f66556d9735c98a2bda21d19944d9cf0b14f0e
boost-headers 1fa483b45b4ae788b6b025984193f1cf54839c8c418e21c4dc59e75cb634145d
boost-integer 635f28a2daf0fa58ab47c8c26d65e4e306d75202ee8a776382a0b7b6d09867d4
boost-io bf0ba4dadf5887d63e172c2970c816c018dad41b70333583426c1a940b468635
boost-range 3a6d8b11702bfc5f6c7873fa39f81672cc8c394eae227e3a642173d2f14e26da
boost-static-assert 5bdacd62569fd4dc1abae15ad00008ce1ea70dd190892dc68903f99e97822243
boost-system 54b74ab3a91c3c11e32264b118f8024efdd3020713e5f522ea48501342e2884d
boost-throw-exception cb7cf3422cfdb27853202c34af872339231f93fe976dc1721ef700682b141208
boost-type-traits 386aa7b34165317f0982924bd0f0f324737e30bb1f68545f7554c85f3c1e7031
boost-utility b8e8716d390c71985d43b190bdfcdb3811c216df8a42f14170aec1557b78f6dc
cmake 0
features core
portfile.cmake 6afba2915f26d07d999614559203e8558c428d92d27c343cfef4b3260b5aef11
ports.cmake a9f8f745f9f4d225039f9810e79670f10f38c7455222865616ce430a06f75d1f
post_build_checks 2
triplet arm64-linux
triplet_abi 971960c7eb0461eeba2679a4edd056aa99288fbd95bb9a4e2989e5f4a78158ab-7300d6fc76582335b0d9fd6dc6a00f69d7ba728b9d6444ad2a9d42f054e1d991-1d43c82f51082f8427993d1dac28c7cb4e3749af
vcpkg.json f8bac2a7b934e2226de989e29b99099fa0e3aa9750e6a1f76c79a10bed37f2e5
vcpkg_from_git 96ed81968f76354c00096dd8cd4e63c6a235fa969334a11ab18d11c0c512ff58
vcpkg_from_github b743742296a114ea1b18ae99672e02f142c4eb2bef7f57d36c038bedbfb0502f
