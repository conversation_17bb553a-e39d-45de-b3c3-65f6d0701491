{"$schema": "https://raw.githubusercontent.com/spdx/spdx-spec/v2.2.1/schemas/spdx-schema.json", "spdxVersion": "SPDX-2.2", "dataLicense": "CC0-1.0", "SPDXID": "SPDXRef-DOCUMENT", "documentNamespace": "https://spdx.org/spdxdocs/boost-lexical-cast-arm64-linux-1.87.0#1-f0fb05f1-7279-435b-bd4f-6b230335153f", "name": "boost-lexical-cast:arm64-linux@1.87.0#1 2edb0693dcbb826f3b13a3635b110efbc8ed9023595fef279d22ed494e4cd843", "creationInfo": {"creators": ["Tool: vcpkg-2025-04-07-b8b513ba8778c918cff49c3e837aae5999d5d2aa"], "created": "2025-06-10T09:11:30Z"}, "relationships": [{"spdxElementId": "SPDXRef-port", "relationshipType": "GENERATES", "relatedSpdxElement": "SPDXRef-binary"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-0"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-1"}, {"spdxElementId": "SPDXRef-binary", "relationshipType": "GENERATED_FROM", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-0", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-0", "relationshipType": "DEPENDENCY_MANIFEST_OF", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-1", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}], "packages": [{"name": "boost-lexical-cast", "SPDXID": "SPDXRef-port", "versionInfo": "1.87.0#1", "downloadLocation": "git+https://github.com/Microsoft/vcpkg#ports/boost-lexical-cast", "homepage": "https://www.boost.org/libs/lexical_cast", "licenseConcluded": "BSL-1.0", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "description": "Boost lexical_cast module", "comment": "This is the port (recipe) consumed by vcpkg."}, {"name": "boost-lexical-cast:arm64-linux", "SPDXID": "SPDXRef-binary", "versionInfo": "2edb0693dcbb826f3b13a3635b110efbc8ed9023595fef279d22ed494e4cd843", "downloadLocation": "NONE", "licenseConcluded": "BSL-1.0", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "comment": "This is a binary package built by vcpkg."}, {"SPDXID": "SPDXRef-resource-0", "name": "boostorg/lexical_cast", "downloadLocation": "git+https://github.com/boostorg/lexical_cast@boost-1.87.0", "licenseConcluded": "NOASSERTION", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "checksums": [{"algorithm": "SHA512", "checksumValue": "ff5ca9f69a8fea9354cccc652baa59477542e94b9b02bb7f7b5e65ec67a9374a97c67d2a87e78e0603382be88079b4a4697c647f8d218d5697fccf87c1e0290c"}]}], "files": [{"fileName": "./vcpkg.json", "SPDXID": "SPDXRef-file-0", "checksums": [{"algorithm": "SHA256", "checksumValue": "e304a2c8290f6ab9fd8ecb17822ee2a79c8ffc208b5b7fe74a538c934e69267e"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./portfile.cmake", "SPDXID": "SPDXRef-file-1", "checksums": [{"algorithm": "SHA256", "checksumValue": "7a43211aced867b9a1bfcf3f78b2b3c3b8342a5fcde572b4b69a48aa15f7befd"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}]}