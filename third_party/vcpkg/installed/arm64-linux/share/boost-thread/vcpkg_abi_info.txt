boost-assert 3245b8cef1975eb17584e363015c24184970f16eac38da3efcf0e50047d59de8
boost-atomic e46215aaab5c568f0f55c8be4400aa6f99440736b44234fb6de94aa58463283e
boost-bind 0bf8b0b2c3c5009f16bbda39e79a28ad44b8d3296af08f25f0fce4fe0f37d1dd
boost-chrono ad173a3da775450e72c1943570e909e333a66ed99bfe45a0e6eaa5ff3930cedf
boost-cmake c2f9d310477a33734a24c277fd7062de3f66a52aa462954c392ea364152275d2
boost-concept-check c638ebb4b2b8845354840a54c5bf4d14a08ed590862ff49ba2a32a04de960be1
boost-config 94d5d122f9ebdf91e590c7784e001b446a6b97eef8209076f7d30e2c904830be
boost-container 60e02af10fa36b28dfe02a8ca80769126d125dc70302c3db3c8f3257efa0ab3e
boost-container-hash b49f85a045c962edb311991c82138b5adfaa0bb9fe4072357a4844a1b4f61ff4
boost-core cd5e9603f3beaa44da04a286f84dc358866ff2f3cc265e53847d64d45c3a2a02
boost-date-time 70b5a88e20f486e3fd0dc1b9b54a400327403757bae1d4a14e4996952529c68f
boost-exception 73c50785ef658ea8a7e9ba8a0e7884b0cb66bc70be880a835ec823307df20fa2
boost-function eef3a433a2205dcecb0d9058d8d849aa2bb1e1071710a19c7ddf1b047249ba93
boost-headers 1fa483b45b4ae788b6b025984193f1cf54839c8c418e21c4dc59e75cb634145d
boost-io bf0ba4dadf5887d63e172c2970c816c018dad41b70333583426c1a940b468635
boost-move 8c1e6d515be042166f10a1535f02ea14ae8537bbda4a9fff5c5a1afc10bfaccb
boost-optional 112feb128c38e6427114d329a91a0f70924af120e634c4726410cf75c24e14f1
boost-predef c33e3bcdb00644c3cdc338715c31093dcb08d874229c60ebd6d87550d2929bfa
boost-preprocessor b1c967b994e46e39b2e39304295567b86a25652e938cfdb3cfcf168983de2d51
boost-smart-ptr 239a9c88250ca040432a86d51397371ec9b3b2985d97fe4eb64e978516bf6bf7
boost-static-assert 5bdacd62569fd4dc1abae15ad00008ce1ea70dd190892dc68903f99e97822243
boost-system 54b74ab3a91c3c11e32264b118f8024efdd3020713e5f522ea48501342e2884d
boost-throw-exception cb7cf3422cfdb27853202c34af872339231f93fe976dc1721ef700682b141208
boost-tuple d07c146ccb6997627ad2022a6dd35c3ba36a356028d615d04b9fe312b105aa73
boost-type-traits 386aa7b34165317f0982924bd0f0f324737e30bb1f68545f7554c85f3c1e7031
boost-utility b8e8716d390c71985d43b190bdfcdb3811c216df8a42f14170aec1557b78f6dc
boost-winapi 4c591a1c3f14929e8cfe1c7fcc4b8e9c9d0c07fb119faff1e0f848b40b0fd51c
cmake 0
features core
portfile.cmake e15f58e9c744741427152872d36cbd683e69c93f85ac179238ba871cec311b60
ports.cmake a9f8f745f9f4d225039f9810e79670f10f38c7455222865616ce430a06f75d1f
post_build_checks 2
triplet arm64-linux
triplet_abi 971960c7eb0461eeba2679a4edd056aa99288fbd95bb9a4e2989e5f4a78158ab-7300d6fc76582335b0d9fd6dc6a00f69d7ba728b9d6444ad2a9d42f054e1d991-1d43c82f51082f8427993d1dac28c7cb4e3749af
vcpkg.json 7d07a1e8e51aaab9823f5753a7d07e7c333161417634a73aca5558f337e46fa9
vcpkg_from_git 96ed81968f76354c00096dd8cd4e63c6a235fa969334a11ab18d11c0c512ff58
vcpkg_from_github b743742296a114ea1b18ae99672e02f142c4eb2bef7f57d36c038bedbfb0502f
