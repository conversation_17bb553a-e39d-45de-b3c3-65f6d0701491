//  This file was automatically generated on Fri Oct 13 19:09:38 2023
//  by libs/config/tools/generate.cpp
//  Copyright <PERSON> 2002-21.
//  Use, modification and distribution are subject to the 
//  Boost Software License, Version 1.0. (See accompanying file 
//  LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

//  See http://www.boost.org/libs/config for the most recent version.//
//  Revision $Id$
//

#include <boost/config.hpp>

#ifdef TEST_BOOST_HAS_TWO_ARG_USE_FACET
#  ifndef BOOST_HAS_TWO_ARG_USE_FACET
#     error "Feature macro BOOST_HAS_TWO_ARG_USE_FACET is not defined."
#  endif
#endif
#ifdef TEST_BOOST_HAS_BETHREADS
#  ifndef BOOST_HAS_BETHREADS
#     error "Feature macro BOOST_HAS_BETHREADS is not defined."
#  endif
#endif
#ifdef TEST_BOOST_HAS_CLOCK_GETTIME
#  ifndef BOOST_HAS_CLOCK_GETTIME
#     error "Feature macro BOOST_HAS_CLOCK_GETTIME is not defined."
#  endif
#endif
#ifdef TEST_BOOST_HAS_PRAGMA_DETECT_MISMATCH
#  ifndef BOOST_HAS_PRAGMA_DETECT_MISMATCH
#     error "Feature macro BOOST_HAS_PRAGMA_DETECT_MISMATCH is not defined."
#  endif
#endif
#ifdef TEST_BOOST_HAS_DIRENT_H
#  ifndef BOOST_HAS_DIRENT_H
#     error "Feature macro BOOST_HAS_DIRENT_H is not defined."
#  endif
#endif
#ifdef TEST_BOOST_HAS_EXPM1
#  ifndef BOOST_HAS_EXPM1
#     error "Feature macro BOOST_HAS_EXPM1 is not defined."
#  endif
#endif
#ifdef TEST_BOOST_HAS_FLOAT128
#  ifndef BOOST_HAS_FLOAT128
#     error "Feature macro BOOST_HAS_FLOAT128 is not defined."
#  endif
#endif
#ifdef TEST_BOOST_HAS_FTIME
#  ifndef BOOST_HAS_FTIME
#     error "Feature macro BOOST_HAS_FTIME is not defined."
#  endif
#endif
#ifdef TEST_BOOST_HAS_GETSYSTEMTIMEASFILETIME
#  ifndef BOOST_HAS_GETSYSTEMTIMEASFILETIME
#     error "Feature macro BOOST_HAS_GETSYSTEMTIMEASFILETIME is not defined."
#  endif
#endif
#ifdef TEST_BOOST_HAS_GETTIMEOFDAY
#  ifndef BOOST_HAS_GETTIMEOFDAY
#     error "Feature macro BOOST_HAS_GETTIMEOFDAY is not defined."
#  endif
#endif
#ifdef TEST_BOOST_HAS_HASH
#  ifndef BOOST_HAS_HASH
#     error "Feature macro BOOST_HAS_HASH is not defined."
#  endif
#endif
#ifdef TEST_BOOST_HAS_INT128
#  ifndef BOOST_HAS_INT128
#     error "Feature macro BOOST_HAS_INT128 is not defined."
#  endif
#endif
#ifdef TEST_BOOST_HAS_LOG1P
#  ifndef BOOST_HAS_LOG1P
#     error "Feature macro BOOST_HAS_LOG1P is not defined."
#  endif
#endif
#ifdef TEST_BOOST_HAS_LONG_LONG
#  ifndef BOOST_HAS_LONG_LONG
#     error "Feature macro BOOST_HAS_LONG_LONG is not defined."
#  endif
#endif
#ifdef TEST_BOOST_HAS_MACRO_USE_FACET
#  ifndef BOOST_HAS_MACRO_USE_FACET
#     error "Feature macro BOOST_HAS_MACRO_USE_FACET is not defined."
#  endif
#endif
#ifdef TEST_BOOST_HAS_MS_INT64
#  ifndef BOOST_HAS_MS_INT64
#     error "Feature macro BOOST_HAS_MS_INT64 is not defined."
#  endif
#endif
#ifdef TEST_BOOST_HAS_NANOSLEEP
#  ifndef BOOST_HAS_NANOSLEEP
#     error "Feature macro BOOST_HAS_NANOSLEEP is not defined."
#  endif
#endif
#ifdef TEST_BOOST_HAS_NL_TYPES_H
#  ifndef BOOST_HAS_NL_TYPES_H
#     error "Feature macro BOOST_HAS_NL_TYPES_H is not defined."
#  endif
#endif
#ifdef TEST_BOOST_HAS_NRVO
#  ifndef BOOST_HAS_NRVO
#     error "Feature macro BOOST_HAS_NRVO is not defined."
#  endif
#endif
#ifdef TEST_BOOST_HAS_PARTIAL_STD_ALLOCATOR
#  ifndef BOOST_HAS_PARTIAL_STD_ALLOCATOR
#     error "Feature macro BOOST_HAS_PARTIAL_STD_ALLOCATOR is not defined."
#  endif
#endif
#ifdef TEST_BOOST_HAS_PTHREAD_DELAY_NP
#  ifndef BOOST_HAS_PTHREAD_DELAY_NP
#     error "Feature macro BOOST_HAS_PTHREAD_DELAY_NP is not defined."
#  endif
#endif
#ifdef TEST_BOOST_HAS_PTHREAD_MUTEXATTR_SETTYPE
#  ifndef BOOST_HAS_PTHREAD_MUTEXATTR_SETTYPE
#     error "Feature macro BOOST_HAS_PTHREAD_MUTEXATTR_SETTYPE is not defined."
#  endif
#endif
#ifdef TEST_BOOST_HAS_PTHREAD_YIELD
#  ifndef BOOST_HAS_PTHREAD_YIELD
#     error "Feature macro BOOST_HAS_PTHREAD_YIELD is not defined."
#  endif
#endif
#ifdef TEST_BOOST_HAS_PTHREADS
#  ifndef BOOST_HAS_PTHREADS
#     error "Feature macro BOOST_HAS_PTHREADS is not defined."
#  endif
#endif
#ifdef TEST_BOOST_HAS_RVALUE_REFS
#  ifndef BOOST_HAS_RVALUE_REFS
#     error "Feature macro BOOST_HAS_RVALUE_REFS is not defined."
#  endif
#endif
#ifdef TEST_BOOST_HAS_SCHED_YIELD
#  ifndef BOOST_HAS_SCHED_YIELD
#     error "Feature macro BOOST_HAS_SCHED_YIELD is not defined."
#  endif
#endif
#ifdef TEST_BOOST_HAS_SGI_TYPE_TRAITS
#  ifndef BOOST_HAS_SGI_TYPE_TRAITS
#     error "Feature macro BOOST_HAS_SGI_TYPE_TRAITS is not defined."
#  endif
#endif
#ifdef TEST_BOOST_HAS_SIGACTION
#  ifndef BOOST_HAS_SIGACTION
#     error "Feature macro BOOST_HAS_SIGACTION is not defined."
#  endif
#endif
#ifdef TEST_BOOST_HAS_SLIST
#  ifndef BOOST_HAS_SLIST
#     error "Feature macro BOOST_HAS_SLIST is not defined."
#  endif
#endif
#ifdef TEST_BOOST_HAS_STATIC_ASSERT
#  ifndef BOOST_HAS_STATIC_ASSERT
#     error "Feature macro BOOST_HAS_STATIC_ASSERT is not defined."
#  endif
#endif
#ifdef TEST_BOOST_HAS_STDINT_H
#  ifndef BOOST_HAS_STDINT_H
#     error "Feature macro BOOST_HAS_STDINT_H is not defined."
#  endif
#endif
#ifdef TEST_BOOST_HAS_STLP_USE_FACET
#  ifndef BOOST_HAS_STLP_USE_FACET
#     error "Feature macro BOOST_HAS_STLP_USE_FACET is not defined."
#  endif
#endif
#ifdef TEST_BOOST_HAS_UNISTD_H
#  ifndef BOOST_HAS_UNISTD_H
#     error "Feature macro BOOST_HAS_UNISTD_H is not defined."
#  endif
#endif
#ifdef TEST_BOOST_HAS_VARIADIC_TMPL
#  ifndef BOOST_HAS_VARIADIC_TMPL
#     error "Feature macro BOOST_HAS_VARIADIC_TMPL is not defined."
#  endif
#endif
#ifdef TEST_BOOST_MSVC6_MEMBER_TEMPLATES
#  ifndef BOOST_MSVC6_MEMBER_TEMPLATES
#     error "Feature macro BOOST_MSVC6_MEMBER_TEMPLATES is not defined."
#  endif
#endif
#ifdef TEST_BOOST_MSVC_STD_ITERATOR
#  ifndef BOOST_MSVC_STD_ITERATOR
#     error "Feature macro BOOST_MSVC_STD_ITERATOR is not defined."
#  endif
#endif
#ifdef TEST_BOOST_HAS_WINTHREADS
#  ifndef BOOST_HAS_WINTHREADS
#     error "Feature macro BOOST_HAS_WINTHREADS is not defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_ADL_BARRIER
#  ifdef BOOST_NO_ADL_BARRIER
#     error "Defect macro BOOST_NO_ADL_BARRIER is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_ARGUMENT_DEPENDENT_LOOKUP
#  ifdef BOOST_NO_ARGUMENT_DEPENDENT_LOOKUP
#     error "Defect macro BOOST_NO_ARGUMENT_DEPENDENT_LOOKUP is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_ARRAY_TYPE_SPECIALIZATIONS
#  ifdef BOOST_NO_ARRAY_TYPE_SPECIALIZATIONS
#     error "Defect macro BOOST_NO_ARRAY_TYPE_SPECIALIZATIONS is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX11_AUTO_DECLARATIONS
#  ifdef BOOST_NO_CXX11_AUTO_DECLARATIONS
#     error "Defect macro BOOST_NO_CXX11_AUTO_DECLARATIONS is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX11_AUTO_MULTIDECLARATIONS
#  ifdef BOOST_NO_CXX11_AUTO_MULTIDECLARATIONS
#     error "Defect macro BOOST_NO_CXX11_AUTO_MULTIDECLARATIONS is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_AUTO_PTR
#  ifdef BOOST_NO_AUTO_PTR
#     error "Defect macro BOOST_NO_AUTO_PTR is defined."
#  endif
#endif
#ifdef TEST_BOOST_BCB_PARTIAL_SPECIALIZATION_BUG
#  ifdef BOOST_BCB_PARTIAL_SPECIALIZATION_BUG
#     error "Defect macro BOOST_BCB_PARTIAL_SPECIALIZATION_BUG is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX11_CHAR16_T
#  ifdef BOOST_NO_CXX11_CHAR16_T
#     error "Defect macro BOOST_NO_CXX11_CHAR16_T is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX11_CHAR32_T
#  ifdef BOOST_NO_CXX11_CHAR32_T
#     error "Defect macro BOOST_NO_CXX11_CHAR32_T is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_COMPLETE_VALUE_INITIALIZATION
#  ifdef BOOST_NO_COMPLETE_VALUE_INITIALIZATION
#     error "Defect macro BOOST_NO_COMPLETE_VALUE_INITIALIZATION is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX11_CONSTEXPR
#  ifdef BOOST_NO_CXX11_CONSTEXPR
#     error "Defect macro BOOST_NO_CXX11_CONSTEXPR is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CTYPE_FUNCTIONS
#  ifdef BOOST_NO_CTYPE_FUNCTIONS
#     error "Defect macro BOOST_NO_CTYPE_FUNCTIONS is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CV_SPECIALIZATIONS
#  ifdef BOOST_NO_CV_SPECIALIZATIONS
#     error "Defect macro BOOST_NO_CV_SPECIALIZATIONS is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CV_VOID_SPECIALIZATIONS
#  ifdef BOOST_NO_CV_VOID_SPECIALIZATIONS
#     error "Defect macro BOOST_NO_CV_VOID_SPECIALIZATIONS is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CWCHAR
#  ifdef BOOST_NO_CWCHAR
#     error "Defect macro BOOST_NO_CWCHAR is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CWCTYPE
#  ifdef BOOST_NO_CWCTYPE
#     error "Defect macro BOOST_NO_CWCTYPE is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX03
#  ifdef BOOST_NO_CXX03
#     error "Defect macro BOOST_NO_CXX03 is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX11
#  ifdef BOOST_NO_CXX11
#     error "Defect macro BOOST_NO_CXX11 is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX11_ADDRESSOF
#  ifdef BOOST_NO_CXX11_ADDRESSOF
#     error "Defect macro BOOST_NO_CXX11_ADDRESSOF is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX11_ALIGNAS
#  ifdef BOOST_NO_CXX11_ALIGNAS
#     error "Defect macro BOOST_NO_CXX11_ALIGNAS is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX11_ALIGNOF
#  ifdef BOOST_NO_CXX11_ALIGNOF
#     error "Defect macro BOOST_NO_CXX11_ALIGNOF is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX11_ALLOCATOR
#  ifdef BOOST_NO_CXX11_ALLOCATOR
#     error "Defect macro BOOST_NO_CXX11_ALLOCATOR is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX11_ATOMIC_SMART_PTR
#  ifdef BOOST_NO_CXX11_ATOMIC_SMART_PTR
#     error "Defect macro BOOST_NO_CXX11_ATOMIC_SMART_PTR is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX11_DEFAULTED_MOVES
#  ifdef BOOST_NO_CXX11_DEFAULTED_MOVES
#     error "Defect macro BOOST_NO_CXX11_DEFAULTED_MOVES is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX11_HDR_EXCEPTION
#  ifdef BOOST_NO_CXX11_HDR_EXCEPTION
#     error "Defect macro BOOST_NO_CXX11_HDR_EXCEPTION is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX11_FINAL
#  ifdef BOOST_NO_CXX11_FINAL
#     error "Defect macro BOOST_NO_CXX11_FINAL is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX11_HDR_ARRAY
#  ifdef BOOST_NO_CXX11_HDR_ARRAY
#     error "Defect macro BOOST_NO_CXX11_HDR_ARRAY is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX11_HDR_ATOMIC
#  ifdef BOOST_NO_CXX11_HDR_ATOMIC
#     error "Defect macro BOOST_NO_CXX11_HDR_ATOMIC is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX11_HDR_CHRONO
#  ifdef BOOST_NO_CXX11_HDR_CHRONO
#     error "Defect macro BOOST_NO_CXX11_HDR_CHRONO is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX11_HDR_CODECVT
#  ifdef BOOST_NO_CXX11_HDR_CODECVT
#     error "Defect macro BOOST_NO_CXX11_HDR_CODECVT is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX11_HDR_CONDITION_VARIABLE
#  ifdef BOOST_NO_CXX11_HDR_CONDITION_VARIABLE
#     error "Defect macro BOOST_NO_CXX11_HDR_CONDITION_VARIABLE is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX11_HDR_FORWARD_LIST
#  ifdef BOOST_NO_CXX11_HDR_FORWARD_LIST
#     error "Defect macro BOOST_NO_CXX11_HDR_FORWARD_LIST is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX11_HDR_FUTURE
#  ifdef BOOST_NO_CXX11_HDR_FUTURE
#     error "Defect macro BOOST_NO_CXX11_HDR_FUTURE is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX11_HDR_INITIALIZER_LIST
#  ifdef BOOST_NO_CXX11_HDR_INITIALIZER_LIST
#     error "Defect macro BOOST_NO_CXX11_HDR_INITIALIZER_LIST is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX11_HDR_MUTEX
#  ifdef BOOST_NO_CXX11_HDR_MUTEX
#     error "Defect macro BOOST_NO_CXX11_HDR_MUTEX is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX11_HDR_RANDOM
#  ifdef BOOST_NO_CXX11_HDR_RANDOM
#     error "Defect macro BOOST_NO_CXX11_HDR_RANDOM is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX11_HDR_RATIO
#  ifdef BOOST_NO_CXX11_HDR_RATIO
#     error "Defect macro BOOST_NO_CXX11_HDR_RATIO is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX11_HDR_REGEX
#  ifdef BOOST_NO_CXX11_HDR_REGEX
#     error "Defect macro BOOST_NO_CXX11_HDR_REGEX is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX11_HDR_SYSTEM_ERROR
#  ifdef BOOST_NO_CXX11_HDR_SYSTEM_ERROR
#     error "Defect macro BOOST_NO_CXX11_HDR_SYSTEM_ERROR is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX11_HDR_THREAD
#  ifdef BOOST_NO_CXX11_HDR_THREAD
#     error "Defect macro BOOST_NO_CXX11_HDR_THREAD is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX11_HDR_TUPLE
#  ifdef BOOST_NO_CXX11_HDR_TUPLE
#     error "Defect macro BOOST_NO_CXX11_HDR_TUPLE is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX11_HDR_TYPE_TRAITS
#  ifdef BOOST_NO_CXX11_HDR_TYPE_TRAITS
#     error "Defect macro BOOST_NO_CXX11_HDR_TYPE_TRAITS is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX11_HDR_TYPEINDEX
#  ifdef BOOST_NO_CXX11_HDR_TYPEINDEX
#     error "Defect macro BOOST_NO_CXX11_HDR_TYPEINDEX is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX11_HDR_UNORDERED_MAP
#  ifdef BOOST_NO_CXX11_HDR_UNORDERED_MAP
#     error "Defect macro BOOST_NO_CXX11_HDR_UNORDERED_MAP is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX11_HDR_UNORDERED_SET
#  ifdef BOOST_NO_CXX11_HDR_UNORDERED_SET
#     error "Defect macro BOOST_NO_CXX11_HDR_UNORDERED_SET is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX11_INLINE_NAMESPACES
#  ifdef BOOST_NO_CXX11_INLINE_NAMESPACES
#     error "Defect macro BOOST_NO_CXX11_INLINE_NAMESPACES is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX11_NON_PUBLIC_DEFAULTED_FUNCTIONS
#  ifdef BOOST_NO_CXX11_NON_PUBLIC_DEFAULTED_FUNCTIONS
#     error "Defect macro BOOST_NO_CXX11_NON_PUBLIC_DEFAULTED_FUNCTIONS is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX11_NUMERIC_LIMITS
#  ifdef BOOST_NO_CXX11_NUMERIC_LIMITS
#     error "Defect macro BOOST_NO_CXX11_NUMERIC_LIMITS is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX11_OVERRIDE
#  ifdef BOOST_NO_CXX11_OVERRIDE
#     error "Defect macro BOOST_NO_CXX11_OVERRIDE is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX11_POINTER_TRAITS
#  ifdef BOOST_NO_CXX11_POINTER_TRAITS
#     error "Defect macro BOOST_NO_CXX11_POINTER_TRAITS is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX11_REF_QUALIFIERS
#  ifdef BOOST_NO_CXX11_REF_QUALIFIERS
#     error "Defect macro BOOST_NO_CXX11_REF_QUALIFIERS is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX11_SFINAE_EXPR
#  ifdef BOOST_NO_CXX11_SFINAE_EXPR
#     error "Defect macro BOOST_NO_CXX11_SFINAE_EXPR is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX11_SMART_PTR
#  ifdef BOOST_NO_CXX11_SMART_PTR
#     error "Defect macro BOOST_NO_CXX11_SMART_PTR is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX11_STD_ALIGN
#  ifdef BOOST_NO_CXX11_STD_ALIGN
#     error "Defect macro BOOST_NO_CXX11_STD_ALIGN is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX11_THREAD_LOCAL
#  ifdef BOOST_NO_CXX11_THREAD_LOCAL
#     error "Defect macro BOOST_NO_CXX11_THREAD_LOCAL is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX11_TRAILING_RESULT_TYPES
#  ifdef BOOST_NO_CXX11_TRAILING_RESULT_TYPES
#     error "Defect macro BOOST_NO_CXX11_TRAILING_RESULT_TYPES is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX11_UNRESTRICTED_UNION
#  ifdef BOOST_NO_CXX11_UNRESTRICTED_UNION
#     error "Defect macro BOOST_NO_CXX11_UNRESTRICTED_UNION is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX11_USER_DEFINED_LITERALS
#  ifdef BOOST_NO_CXX11_USER_DEFINED_LITERALS
#     error "Defect macro BOOST_NO_CXX11_USER_DEFINED_LITERALS is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX14
#  ifdef BOOST_NO_CXX14
#     error "Defect macro BOOST_NO_CXX14 is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX14_BINARY_LITERALS
#  ifdef BOOST_NO_CXX14_BINARY_LITERALS
#     error "Defect macro BOOST_NO_CXX14_BINARY_LITERALS is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX14_CONSTEXPR
#  ifdef BOOST_NO_CXX14_CONSTEXPR
#     error "Defect macro BOOST_NO_CXX14_CONSTEXPR is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX14_DECLTYPE_AUTO
#  ifdef BOOST_NO_CXX14_DECLTYPE_AUTO
#     error "Defect macro BOOST_NO_CXX14_DECLTYPE_AUTO is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX14_DIGIT_SEPARATORS
#  ifdef BOOST_NO_CXX14_DIGIT_SEPARATORS
#     error "Defect macro BOOST_NO_CXX14_DIGIT_SEPARATORS is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX14_GENERIC_LAMBDAS
#  ifdef BOOST_NO_CXX14_GENERIC_LAMBDAS
#     error "Defect macro BOOST_NO_CXX14_GENERIC_LAMBDAS is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX14_HDR_SHARED_MUTEX
#  ifdef BOOST_NO_CXX14_HDR_SHARED_MUTEX
#     error "Defect macro BOOST_NO_CXX14_HDR_SHARED_MUTEX is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX14_INITIALIZED_LAMBDA_CAPTURES
#  ifdef BOOST_NO_CXX14_INITIALIZED_LAMBDA_CAPTURES
#     error "Defect macro BOOST_NO_CXX14_INITIALIZED_LAMBDA_CAPTURES is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX14_AGGREGATE_NSDMI
#  ifdef BOOST_NO_CXX14_AGGREGATE_NSDMI
#     error "Defect macro BOOST_NO_CXX14_AGGREGATE_NSDMI is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX14_RETURN_TYPE_DEDUCTION
#  ifdef BOOST_NO_CXX14_RETURN_TYPE_DEDUCTION
#     error "Defect macro BOOST_NO_CXX14_RETURN_TYPE_DEDUCTION is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX14_STD_EXCHANGE
#  ifdef BOOST_NO_CXX14_STD_EXCHANGE
#     error "Defect macro BOOST_NO_CXX14_STD_EXCHANGE is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX14_VARIABLE_TEMPLATES
#  ifdef BOOST_NO_CXX14_VARIABLE_TEMPLATES
#     error "Defect macro BOOST_NO_CXX14_VARIABLE_TEMPLATES is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX17
#  ifdef BOOST_NO_CXX17
#     error "Defect macro BOOST_NO_CXX17 is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX17_AUTO_NONTYPE_TEMPLATE_PARAMS
#  ifdef BOOST_NO_CXX17_AUTO_NONTYPE_TEMPLATE_PARAMS
#     error "Defect macro BOOST_NO_CXX17_AUTO_NONTYPE_TEMPLATE_PARAMS is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX17_DEDUCTION_GUIDES
#  ifdef BOOST_NO_CXX17_DEDUCTION_GUIDES
#     error "Defect macro BOOST_NO_CXX17_DEDUCTION_GUIDES is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX17_FOLD_EXPRESSIONS
#  ifdef BOOST_NO_CXX17_FOLD_EXPRESSIONS
#     error "Defect macro BOOST_NO_CXX17_FOLD_EXPRESSIONS is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX17_HDR_ANY
#  ifdef BOOST_NO_CXX17_HDR_ANY
#     error "Defect macro BOOST_NO_CXX17_HDR_ANY is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX17_HDR_CHARCONV
#  ifdef BOOST_NO_CXX17_HDR_CHARCONV
#     error "Defect macro BOOST_NO_CXX17_HDR_CHARCONV is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX17_HDR_EXECUTION
#  ifdef BOOST_NO_CXX17_HDR_EXECUTION
#     error "Defect macro BOOST_NO_CXX17_HDR_EXECUTION is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX17_HDR_FILESYSTEM
#  ifdef BOOST_NO_CXX17_HDR_FILESYSTEM
#     error "Defect macro BOOST_NO_CXX17_HDR_FILESYSTEM is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX17_HDR_MEMORY_RESOURCE
#  ifdef BOOST_NO_CXX17_HDR_MEMORY_RESOURCE
#     error "Defect macro BOOST_NO_CXX17_HDR_MEMORY_RESOURCE is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX17_HDR_OPTIONAL
#  ifdef BOOST_NO_CXX17_HDR_OPTIONAL
#     error "Defect macro BOOST_NO_CXX17_HDR_OPTIONAL is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX17_HDR_STRING_VIEW
#  ifdef BOOST_NO_CXX17_HDR_STRING_VIEW
#     error "Defect macro BOOST_NO_CXX17_HDR_STRING_VIEW is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX17_HDR_VARIANT
#  ifdef BOOST_NO_CXX17_HDR_VARIANT
#     error "Defect macro BOOST_NO_CXX17_HDR_VARIANT is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX17_IF_CONSTEXPR
#  ifdef BOOST_NO_CXX17_IF_CONSTEXPR
#     error "Defect macro BOOST_NO_CXX17_IF_CONSTEXPR is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX17_INLINE_VARIABLES
#  ifdef BOOST_NO_CXX17_INLINE_VARIABLES
#     error "Defect macro BOOST_NO_CXX17_INLINE_VARIABLES is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX17_ITERATOR_TRAITS
#  ifdef BOOST_NO_CXX17_ITERATOR_TRAITS
#     error "Defect macro BOOST_NO_CXX17_ITERATOR_TRAITS is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX17_STD_APPLY
#  ifdef BOOST_NO_CXX17_STD_APPLY
#     error "Defect macro BOOST_NO_CXX17_STD_APPLY is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX17_STD_INVOKE
#  ifdef BOOST_NO_CXX17_STD_INVOKE
#     error "Defect macro BOOST_NO_CXX17_STD_INVOKE is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX17_STRUCTURED_BINDINGS
#  ifdef BOOST_NO_CXX17_STRUCTURED_BINDINGS
#     error "Defect macro BOOST_NO_CXX17_STRUCTURED_BINDINGS is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX20_HDR_BARRIER
#  ifdef BOOST_NO_CXX20_HDR_BARRIER
#     error "Defect macro BOOST_NO_CXX20_HDR_BARRIER is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX20_HDR_BIT
#  ifdef BOOST_NO_CXX20_HDR_BIT
#     error "Defect macro BOOST_NO_CXX20_HDR_BIT is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX20_HDR_COMPARE
#  ifdef BOOST_NO_CXX20_HDR_COMPARE
#     error "Defect macro BOOST_NO_CXX20_HDR_COMPARE is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX20_HDR_CONCEPTS
#  ifdef BOOST_NO_CXX20_HDR_CONCEPTS
#     error "Defect macro BOOST_NO_CXX20_HDR_CONCEPTS is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX20_HDR_COROUTINE
#  ifdef BOOST_NO_CXX20_HDR_COROUTINE
#     error "Defect macro BOOST_NO_CXX20_HDR_COROUTINE is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX20_HDR_FORMAT
#  ifdef BOOST_NO_CXX20_HDR_FORMAT
#     error "Defect macro BOOST_NO_CXX20_HDR_FORMAT is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX20_HDR_LATCH
#  ifdef BOOST_NO_CXX20_HDR_LATCH
#     error "Defect macro BOOST_NO_CXX20_HDR_LATCH is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX20_HDR_NUMBERS
#  ifdef BOOST_NO_CXX20_HDR_NUMBERS
#     error "Defect macro BOOST_NO_CXX20_HDR_NUMBERS is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX20_HDR_RANGES
#  ifdef BOOST_NO_CXX20_HDR_RANGES
#     error "Defect macro BOOST_NO_CXX20_HDR_RANGES is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX20_HDR_SEMAPHORE
#  ifdef BOOST_NO_CXX20_HDR_SEMAPHORE
#     error "Defect macro BOOST_NO_CXX20_HDR_SEMAPHORE is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX20_HDR_SOURCE_LOCATION
#  ifdef BOOST_NO_CXX20_HDR_SOURCE_LOCATION
#     error "Defect macro BOOST_NO_CXX20_HDR_SOURCE_LOCATION is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX20_HDR_SPAN
#  ifdef BOOST_NO_CXX20_HDR_SPAN
#     error "Defect macro BOOST_NO_CXX20_HDR_SPAN is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX20_HDR_STOP_TOKEN
#  ifdef BOOST_NO_CXX20_HDR_STOP_TOKEN
#     error "Defect macro BOOST_NO_CXX20_HDR_STOP_TOKEN is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX20_HDR_SYNCSTREAM
#  ifdef BOOST_NO_CXX20_HDR_SYNCSTREAM
#     error "Defect macro BOOST_NO_CXX20_HDR_SYNCSTREAM is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX20_HDR_VERSION
#  ifdef BOOST_NO_CXX20_HDR_VERSION
#     error "Defect macro BOOST_NO_CXX20_HDR_VERSION is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX23_HDR_EXPECTED
#  ifdef BOOST_NO_CXX23_HDR_EXPECTED
#     error "Defect macro BOOST_NO_CXX23_HDR_EXPECTED is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX23_HDR_FLAT_MAP
#  ifdef BOOST_NO_CXX23_HDR_FLAT_MAP
#     error "Defect macro BOOST_NO_CXX23_HDR_FLAT_MAP is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX23_HDR_FLAT_SET
#  ifdef BOOST_NO_CXX23_HDR_FLAT_SET
#     error "Defect macro BOOST_NO_CXX23_HDR_FLAT_SET is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX23_HDR_GENERATOR
#  ifdef BOOST_NO_CXX23_HDR_GENERATOR
#     error "Defect macro BOOST_NO_CXX23_HDR_GENERATOR is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX23_HDR_MDSPAN
#  ifdef BOOST_NO_CXX23_HDR_MDSPAN
#     error "Defect macro BOOST_NO_CXX23_HDR_MDSPAN is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX23_HDR_PRINT
#  ifdef BOOST_NO_CXX23_HDR_PRINT
#     error "Defect macro BOOST_NO_CXX23_HDR_PRINT is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX23_HDR_SPANSTREAM
#  ifdef BOOST_NO_CXX23_HDR_SPANSTREAM
#     error "Defect macro BOOST_NO_CXX23_HDR_SPANSTREAM is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX23_HDR_STACKTRACE
#  ifdef BOOST_NO_CXX23_HDR_STACKTRACE
#     error "Defect macro BOOST_NO_CXX23_HDR_STACKTRACE is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX23_HDR_STDFLOAT
#  ifdef BOOST_NO_CXX23_HDR_STDFLOAT
#     error "Defect macro BOOST_NO_CXX23_HDR_STDFLOAT is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX98_BINDERS
#  ifdef BOOST_NO_CXX98_BINDERS
#     error "Defect macro BOOST_NO_CXX98_BINDERS is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX98_FUNCTION_BASE
#  ifdef BOOST_NO_CXX98_FUNCTION_BASE
#     error "Defect macro BOOST_NO_CXX98_FUNCTION_BASE is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX98_RANDOM_SHUFFLE
#  ifdef BOOST_NO_CXX98_RANDOM_SHUFFLE
#     error "Defect macro BOOST_NO_CXX98_RANDOM_SHUFFLE is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX11_HDR_FUNCTIONAL
#  ifdef BOOST_NO_CXX11_HDR_FUNCTIONAL
#     error "Defect macro BOOST_NO_CXX11_HDR_FUNCTIONAL is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX11_DECLTYPE
#  ifdef BOOST_NO_CXX11_DECLTYPE
#     error "Defect macro BOOST_NO_CXX11_DECLTYPE is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX11_DECLTYPE_N3276
#  ifdef BOOST_NO_CXX11_DECLTYPE_N3276
#     error "Defect macro BOOST_NO_CXX11_DECLTYPE_N3276 is defined."
#  endif
#endif
#ifdef TEST_BOOST_DEDUCED_TYPENAME
#  ifdef BOOST_DEDUCED_TYPENAME
#     error "Defect macro BOOST_DEDUCED_TYPENAME is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX11_DEFAULTED_FUNCTIONS
#  ifdef BOOST_NO_CXX11_DEFAULTED_FUNCTIONS
#     error "Defect macro BOOST_NO_CXX11_DEFAULTED_FUNCTIONS is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX11_DELETED_FUNCTIONS
#  ifdef BOOST_NO_CXX11_DELETED_FUNCTIONS
#     error "Defect macro BOOST_NO_CXX11_DELETED_FUNCTIONS is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_DEPENDENT_NESTED_DERIVATIONS
#  ifdef BOOST_NO_DEPENDENT_NESTED_DERIVATIONS
#     error "Defect macro BOOST_NO_DEPENDENT_NESTED_DERIVATIONS is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_DEPENDENT_TYPES_IN_TEMPLATE_VALUE_PARAMETERS
#  ifdef BOOST_NO_DEPENDENT_TYPES_IN_TEMPLATE_VALUE_PARAMETERS
#     error "Defect macro BOOST_NO_DEPENDENT_TYPES_IN_TEMPLATE_VALUE_PARAMETERS is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_EXCEPTION_STD_NAMESPACE
#  ifdef BOOST_NO_EXCEPTION_STD_NAMESPACE
#     error "Defect macro BOOST_NO_EXCEPTION_STD_NAMESPACE is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_EXCEPTIONS
#  ifdef BOOST_NO_EXCEPTIONS
#     error "Defect macro BOOST_NO_EXCEPTIONS is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_EXPLICIT_FUNCTION_TEMPLATE_ARGUMENTS
#  ifdef BOOST_NO_EXPLICIT_FUNCTION_TEMPLATE_ARGUMENTS
#     error "Defect macro BOOST_NO_EXPLICIT_FUNCTION_TEMPLATE_ARGUMENTS is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX11_EXPLICIT_CONVERSION_OPERATORS
#  ifdef BOOST_NO_CXX11_EXPLICIT_CONVERSION_OPERATORS
#     error "Defect macro BOOST_NO_CXX11_EXPLICIT_CONVERSION_OPERATORS is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX11_EXTERN_TEMPLATE
#  ifdef BOOST_NO_CXX11_EXTERN_TEMPLATE
#     error "Defect macro BOOST_NO_CXX11_EXTERN_TEMPLATE is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_FENV_H
#  ifdef BOOST_NO_FENV_H
#     error "Defect macro BOOST_NO_FENV_H is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX11_FIXED_LENGTH_VARIADIC_TEMPLATE_EXPANSION_PACKS
#  ifdef BOOST_NO_CXX11_FIXED_LENGTH_VARIADIC_TEMPLATE_EXPANSION_PACKS
#     error "Defect macro BOOST_NO_CXX11_FIXED_LENGTH_VARIADIC_TEMPLATE_EXPANSION_PACKS is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_FUNCTION_TEMPLATE_ORDERING
#  ifdef BOOST_NO_FUNCTION_TEMPLATE_ORDERING
#     error "Defect macro BOOST_NO_FUNCTION_TEMPLATE_ORDERING is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX11_FUNCTION_TEMPLATE_DEFAULT_ARGS
#  ifdef BOOST_NO_CXX11_FUNCTION_TEMPLATE_DEFAULT_ARGS
#     error "Defect macro BOOST_NO_CXX11_FUNCTION_TEMPLATE_DEFAULT_ARGS is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_FUNCTION_TYPE_SPECIALIZATIONS
#  ifdef BOOST_NO_FUNCTION_TYPE_SPECIALIZATIONS
#     error "Defect macro BOOST_NO_FUNCTION_TYPE_SPECIALIZATIONS is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_MS_INT64_NUMERIC_LIMITS
#  ifdef BOOST_NO_MS_INT64_NUMERIC_LIMITS
#     error "Defect macro BOOST_NO_MS_INT64_NUMERIC_LIMITS is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_INCLASS_MEMBER_INITIALIZATION
#  ifdef BOOST_NO_INCLASS_MEMBER_INITIALIZATION
#     error "Defect macro BOOST_NO_INCLASS_MEMBER_INITIALIZATION is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_INTEGRAL_INT64_T
#  ifdef BOOST_NO_INTEGRAL_INT64_T
#     error "Defect macro BOOST_NO_INTEGRAL_INT64_T is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_IOSFWD
#  ifdef BOOST_NO_IOSFWD
#     error "Defect macro BOOST_NO_IOSFWD is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_IOSTREAM
#  ifdef BOOST_NO_IOSTREAM
#     error "Defect macro BOOST_NO_IOSTREAM is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_IS_ABSTRACT
#  ifdef BOOST_NO_IS_ABSTRACT
#     error "Defect macro BOOST_NO_IS_ABSTRACT is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_TEMPLATED_ITERATOR_CONSTRUCTORS
#  ifdef BOOST_NO_TEMPLATED_ITERATOR_CONSTRUCTORS
#     error "Defect macro BOOST_NO_TEMPLATED_ITERATOR_CONSTRUCTORS is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX11_LAMBDAS
#  ifdef BOOST_NO_CXX11_LAMBDAS
#     error "Defect macro BOOST_NO_CXX11_LAMBDAS is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_LIMITS
#  ifdef BOOST_NO_LIMITS
#     error "Defect macro BOOST_NO_LIMITS is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_LIMITS_COMPILE_TIME_CONSTANTS
#  ifdef BOOST_NO_LIMITS_COMPILE_TIME_CONSTANTS
#     error "Defect macro BOOST_NO_LIMITS_COMPILE_TIME_CONSTANTS is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_LONG_LONG_NUMERIC_LIMITS
#  ifdef BOOST_NO_LONG_LONG_NUMERIC_LIMITS
#     error "Defect macro BOOST_NO_LONG_LONG_NUMERIC_LIMITS is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_LONG_LONG
#  ifdef BOOST_NO_LONG_LONG
#     error "Defect macro BOOST_NO_LONG_LONG is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_MEMBER_FUNCTION_SPECIALIZATIONS
#  ifdef BOOST_NO_MEMBER_FUNCTION_SPECIALIZATIONS
#     error "Defect macro BOOST_NO_MEMBER_FUNCTION_SPECIALIZATIONS is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_MEMBER_TEMPLATE_KEYWORD
#  ifdef BOOST_NO_MEMBER_TEMPLATE_KEYWORD
#     error "Defect macro BOOST_NO_MEMBER_TEMPLATE_KEYWORD is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_POINTER_TO_MEMBER_TEMPLATE_PARAMETERS
#  ifdef BOOST_NO_POINTER_TO_MEMBER_TEMPLATE_PARAMETERS
#     error "Defect macro BOOST_NO_POINTER_TO_MEMBER_TEMPLATE_PARAMETERS is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_MEMBER_TEMPLATE_FRIENDS
#  ifdef BOOST_NO_MEMBER_TEMPLATE_FRIENDS
#     error "Defect macro BOOST_NO_MEMBER_TEMPLATE_FRIENDS is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_MEMBER_TEMPLATES
#  ifdef BOOST_NO_MEMBER_TEMPLATES
#     error "Defect macro BOOST_NO_MEMBER_TEMPLATES is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_NESTED_FRIENDSHIP
#  ifdef BOOST_NO_NESTED_FRIENDSHIP
#     error "Defect macro BOOST_NO_NESTED_FRIENDSHIP is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX11_NOEXCEPT
#  ifdef BOOST_NO_CXX11_NOEXCEPT
#     error "Defect macro BOOST_NO_CXX11_NOEXCEPT is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX11_NULLPTR
#  ifdef BOOST_NO_CXX11_NULLPTR
#     error "Defect macro BOOST_NO_CXX11_NULLPTR is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_OPERATORS_IN_NAMESPACE
#  ifdef BOOST_NO_OPERATORS_IN_NAMESPACE
#     error "Defect macro BOOST_NO_OPERATORS_IN_NAMESPACE is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_PARTIAL_SPECIALIZATION_IMPLICIT_DEFAULT_ARGS
#  ifdef BOOST_NO_PARTIAL_SPECIALIZATION_IMPLICIT_DEFAULT_ARGS
#     error "Defect macro BOOST_NO_PARTIAL_SPECIALIZATION_IMPLICIT_DEFAULT_ARGS is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_TEMPLATE_PARTIAL_SPECIALIZATION
#  ifdef BOOST_NO_TEMPLATE_PARTIAL_SPECIALIZATION
#     error "Defect macro BOOST_NO_TEMPLATE_PARTIAL_SPECIALIZATION is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_PRIVATE_IN_AGGREGATE
#  ifdef BOOST_NO_PRIVATE_IN_AGGREGATE
#     error "Defect macro BOOST_NO_PRIVATE_IN_AGGREGATE is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_POINTER_TO_MEMBER_CONST
#  ifdef BOOST_NO_POINTER_TO_MEMBER_CONST
#     error "Defect macro BOOST_NO_POINTER_TO_MEMBER_CONST is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX11_RANGE_BASED_FOR
#  ifdef BOOST_NO_CXX11_RANGE_BASED_FOR
#     error "Defect macro BOOST_NO_CXX11_RANGE_BASED_FOR is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX11_RAW_LITERALS
#  ifdef BOOST_NO_CXX11_RAW_LITERALS
#     error "Defect macro BOOST_NO_CXX11_RAW_LITERALS is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_RESTRICT_REFERENCES
#  ifdef BOOST_NO_RESTRICT_REFERENCES
#     error "Defect macro BOOST_NO_RESTRICT_REFERENCES is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_UNREACHABLE_RETURN_DETECTION
#  ifdef BOOST_NO_UNREACHABLE_RETURN_DETECTION
#     error "Defect macro BOOST_NO_UNREACHABLE_RETURN_DETECTION is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_RTTI
#  ifdef BOOST_NO_RTTI
#     error "Defect macro BOOST_NO_RTTI is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX11_RVALUE_REFERENCES
#  ifdef BOOST_NO_CXX11_RVALUE_REFERENCES
#     error "Defect macro BOOST_NO_CXX11_RVALUE_REFERENCES is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX11_SCOPED_ENUMS
#  ifdef BOOST_NO_CXX11_SCOPED_ENUMS
#     error "Defect macro BOOST_NO_CXX11_SCOPED_ENUMS is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_SFINAE
#  ifdef BOOST_NO_SFINAE
#     error "Defect macro BOOST_NO_SFINAE is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_SFINAE_EXPR
#  ifdef BOOST_NO_SFINAE_EXPR
#     error "Defect macro BOOST_NO_SFINAE_EXPR is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_STRINGSTREAM
#  ifdef BOOST_NO_STRINGSTREAM
#     error "Defect macro BOOST_NO_STRINGSTREAM is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX11_STATIC_ASSERT
#  ifdef BOOST_NO_CXX11_STATIC_ASSERT
#     error "Defect macro BOOST_NO_CXX11_STATIC_ASSERT is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_STD_ALLOCATOR
#  ifdef BOOST_NO_STD_ALLOCATOR
#     error "Defect macro BOOST_NO_STD_ALLOCATOR is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_STD_DISTANCE
#  ifdef BOOST_NO_STD_DISTANCE
#     error "Defect macro BOOST_NO_STD_DISTANCE is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_STD_ITERATOR_TRAITS
#  ifdef BOOST_NO_STD_ITERATOR_TRAITS
#     error "Defect macro BOOST_NO_STD_ITERATOR_TRAITS is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_STD_ITERATOR
#  ifdef BOOST_NO_STD_ITERATOR
#     error "Defect macro BOOST_NO_STD_ITERATOR is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_STD_LOCALE
#  ifdef BOOST_NO_STD_LOCALE
#     error "Defect macro BOOST_NO_STD_LOCALE is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_STD_MESSAGES
#  ifdef BOOST_NO_STD_MESSAGES
#     error "Defect macro BOOST_NO_STD_MESSAGES is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_STD_MIN_MAX
#  ifdef BOOST_NO_STD_MIN_MAX
#     error "Defect macro BOOST_NO_STD_MIN_MAX is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_STD_OUTPUT_ITERATOR_ASSIGN
#  ifdef BOOST_NO_STD_OUTPUT_ITERATOR_ASSIGN
#     error "Defect macro BOOST_NO_STD_OUTPUT_ITERATOR_ASSIGN is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_STD_TYPEINFO
#  ifdef BOOST_NO_STD_TYPEINFO
#     error "Defect macro BOOST_NO_STD_TYPEINFO is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_STD_USE_FACET
#  ifdef BOOST_NO_STD_USE_FACET
#     error "Defect macro BOOST_NO_STD_USE_FACET is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_STD_WSTREAMBUF
#  ifdef BOOST_NO_STD_WSTREAMBUF
#     error "Defect macro BOOST_NO_STD_WSTREAMBUF is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_STD_WSTRING
#  ifdef BOOST_NO_STD_WSTRING
#     error "Defect macro BOOST_NO_STD_WSTRING is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_STDC_NAMESPACE
#  ifdef BOOST_NO_STDC_NAMESPACE
#     error "Defect macro BOOST_NO_STDC_NAMESPACE is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_SWPRINTF
#  ifdef BOOST_NO_SWPRINTF
#     error "Defect macro BOOST_NO_SWPRINTF is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX11_LOCAL_CLASS_TEMPLATE_PARAMETERS
#  ifdef BOOST_NO_CXX11_LOCAL_CLASS_TEMPLATE_PARAMETERS
#     error "Defect macro BOOST_NO_CXX11_LOCAL_CLASS_TEMPLATE_PARAMETERS is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX11_TEMPLATE_ALIASES
#  ifdef BOOST_NO_CXX11_TEMPLATE_ALIASES
#     error "Defect macro BOOST_NO_CXX11_TEMPLATE_ALIASES is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_TEMPLATED_IOSTREAMS
#  ifdef BOOST_NO_TEMPLATED_IOSTREAMS
#     error "Defect macro BOOST_NO_TEMPLATED_IOSTREAMS is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_TEMPLATE_TEMPLATES
#  ifdef BOOST_NO_TEMPLATE_TEMPLATES
#     error "Defect macro BOOST_NO_TEMPLATE_TEMPLATES is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_TWO_PHASE_NAME_LOOKUP
#  ifdef BOOST_NO_TWO_PHASE_NAME_LOOKUP
#     error "Defect macro BOOST_NO_TWO_PHASE_NAME_LOOKUP is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_TYPEID
#  ifdef BOOST_NO_TYPEID
#     error "Defect macro BOOST_NO_TYPEID is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_TYPENAME_WITH_CTOR
#  ifdef BOOST_NO_TYPENAME_WITH_CTOR
#     error "Defect macro BOOST_NO_TYPENAME_WITH_CTOR is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX11_UNICODE_LITERALS
#  ifdef BOOST_NO_CXX11_UNICODE_LITERALS
#     error "Defect macro BOOST_NO_CXX11_UNICODE_LITERALS is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX11_UNIFIED_INITIALIZATION_SYNTAX
#  ifdef BOOST_NO_CXX11_UNIFIED_INITIALIZATION_SYNTAX
#     error "Defect macro BOOST_NO_CXX11_UNIFIED_INITIALIZATION_SYNTAX is defined."
#  endif
#endif
#ifdef TEST_BOOST_FUNCTION_SCOPE_USING_DECLARATION_BREAKS_ADL
#  ifdef BOOST_FUNCTION_SCOPE_USING_DECLARATION_BREAKS_ADL
#     error "Defect macro BOOST_FUNCTION_SCOPE_USING_DECLARATION_BREAKS_ADL is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_USING_DECLARATION_OVERLOADS_FROM_TYPENAME_BASE
#  ifdef BOOST_NO_USING_DECLARATION_OVERLOADS_FROM_TYPENAME_BASE
#     error "Defect macro BOOST_NO_USING_DECLARATION_OVERLOADS_FROM_TYPENAME_BASE is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_USING_TEMPLATE
#  ifdef BOOST_NO_USING_TEMPLATE
#     error "Defect macro BOOST_NO_USING_TEMPLATE is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX11_VARIADIC_MACROS
#  ifdef BOOST_NO_CXX11_VARIADIC_MACROS
#     error "Defect macro BOOST_NO_CXX11_VARIADIC_MACROS is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_CXX11_VARIADIC_TEMPLATES
#  ifdef BOOST_NO_CXX11_VARIADIC_TEMPLATES
#     error "Defect macro BOOST_NO_CXX11_VARIADIC_TEMPLATES is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_VOID_RETURNS
#  ifdef BOOST_NO_VOID_RETURNS
#     error "Defect macro BOOST_NO_VOID_RETURNS is defined."
#  endif
#endif
#ifdef TEST_BOOST_NO_INTRINSIC_WCHAR_T
#  ifdef BOOST_NO_INTRINSIC_WCHAR_T
#     error "Defect macro BOOST_NO_INTRINSIC_WCHAR_T is defined."
#  endif
#endif

int main( int, char *[] )
{
   return 0;
}

