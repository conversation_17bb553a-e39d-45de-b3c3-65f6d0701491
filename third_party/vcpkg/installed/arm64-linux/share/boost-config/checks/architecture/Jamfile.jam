# Jamfile.jam
#
# Copyright 2012 <PERSON>
#
# Distributed under the Boost Software License Version 1.0. (See
# accompanying file LICENSE_1_0.txt or copy at
# http://www.boost.org/LICENSE_1_0.txt)

project /boost/architecture
        : requirements
          -<conditional>@boostcpp.deduce-address-model
          -<conditional>@boostcpp.deduce-architecture
          -<implicit-dependency>/boost//headers
        ;

obj 32 : 32.cpp ;
obj 64 : 64.cpp ;

obj arm      : arm.cpp ;
obj combined : combined.cpp ;
obj loongarch : loongarch.cpp ;
obj mips     : mips.cpp ;
alias mips1  : mips ; # Backwards compatibility
obj power    : power.cpp ;
obj riscv    : riscv.cpp ;
obj sparc    : sparc.cpp ;
obj x86      : x86.cpp ;
obj s390x    : s390x.cpp ;
