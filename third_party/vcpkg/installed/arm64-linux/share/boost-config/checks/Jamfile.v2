#
# *** DO NOT EDIT THIS FILE BY HAND ***
# This file was automatically generated on Fri Oct 13 19:09:38 2023
#  by libs/config/tools/generate.cpp
# Copyright John Maddock.
# Use, modification and distribution are subject to the 
# Boost Software License, Version 1.0. (See accompanying file 
# LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

import modules ;
import path ; 


obj two_arg_use_facet : test_case.cpp : <define>TEST_BOOST_HAS_TWO_ARG_USE_FACET ;
obj bethreads : test_case.cpp : <define>TEST_BOOST_HAS_BETHREADS ;
obj clock_gettime : test_case.cpp : <define>TEST_BOOST_HAS_CLOCK_GETTIME ;
obj pragma_detect_mismatch : test_case.cpp : <define>TEST_BOOST_HAS_PRAGMA_DETECT_MISMATCH ;
obj dirent_h : test_case.cpp : <define>TEST_BOOST_HAS_DIRENT_H ;
obj expm1 : test_case.cpp : <define>TEST_BOOST_HAS_EXPM1 ;
obj float128 : test_case.cpp : <define>TEST_BOOST_HAS_FLOAT128 ;
obj ftime : test_case.cpp : <define>TEST_BOOST_HAS_FTIME ;
obj getsystemtimeasfiletime : test_case.cpp : <define>TEST_BOOST_HAS_GETSYSTEMTIMEASFILETIME ;
obj gettimeofday : test_case.cpp : <define>TEST_BOOST_HAS_GETTIMEOFDAY ;
obj hash : test_case.cpp : <define>TEST_BOOST_HAS_HASH ;
obj int128 : test_case.cpp : <define>TEST_BOOST_HAS_INT128 ;
obj log1p : test_case.cpp : <define>TEST_BOOST_HAS_LOG1P ;
obj long_long : test_case.cpp : <define>TEST_BOOST_HAS_LONG_LONG ;
obj macro_use_facet : test_case.cpp : <define>TEST_BOOST_HAS_MACRO_USE_FACET ;
obj ms_int64 : test_case.cpp : <define>TEST_BOOST_HAS_MS_INT64 ;
obj nanosleep : test_case.cpp : <define>TEST_BOOST_HAS_NANOSLEEP ;
obj nl_types_h : test_case.cpp : <define>TEST_BOOST_HAS_NL_TYPES_H ;
obj nrvo : test_case.cpp : <define>TEST_BOOST_HAS_NRVO ;
obj partial_std_allocator : test_case.cpp : <define>TEST_BOOST_HAS_PARTIAL_STD_ALLOCATOR ;
obj pthread_delay_np : test_case.cpp : <define>TEST_BOOST_HAS_PTHREAD_DELAY_NP ;
obj pthread_mutexattr_settype : test_case.cpp : <define>TEST_BOOST_HAS_PTHREAD_MUTEXATTR_SETTYPE ;
obj pthread_yield : test_case.cpp : <define>TEST_BOOST_HAS_PTHREAD_YIELD ;
obj pthreads : test_case.cpp : <define>TEST_BOOST_HAS_PTHREADS ;
obj rvalue_refs : test_case.cpp : <define>TEST_BOOST_HAS_RVALUE_REFS ;
obj sched_yield : test_case.cpp : <define>TEST_BOOST_HAS_SCHED_YIELD ;
obj sgi_type_traits : test_case.cpp : <define>TEST_BOOST_HAS_SGI_TYPE_TRAITS ;
obj sigaction : test_case.cpp : <define>TEST_BOOST_HAS_SIGACTION ;
obj slist : test_case.cpp : <define>TEST_BOOST_HAS_SLIST ;
obj static_assert : test_case.cpp : <define>TEST_BOOST_HAS_STATIC_ASSERT ;
obj stdint_h : test_case.cpp : <define>TEST_BOOST_HAS_STDINT_H ;
obj stlp_use_facet : test_case.cpp : <define>TEST_BOOST_HAS_STLP_USE_FACET ;
obj unistd_h : test_case.cpp : <define>TEST_BOOST_HAS_UNISTD_H ;
obj variadic_tmpl : test_case.cpp : <define>TEST_BOOST_HAS_VARIADIC_TMPL ;
obj boost_msvc6_member_templates : test_case.cpp : <define>TEST_BOOST_MSVC6_MEMBER_TEMPLATES ;
obj boost_msvc_std_iterator : test_case.cpp : <define>TEST_BOOST_MSVC_STD_ITERATOR ;
obj winthreads : test_case.cpp : <define>TEST_BOOST_HAS_WINTHREADS ;
obj adl_barrier : test_case.cpp : <define>TEST_BOOST_NO_ADL_BARRIER ;
obj argument_dependent_lookup : test_case.cpp : <define>TEST_BOOST_NO_ARGUMENT_DEPENDENT_LOOKUP ;
obj array_type_specializations : test_case.cpp : <define>TEST_BOOST_NO_ARRAY_TYPE_SPECIALIZATIONS ;
obj cxx11_auto_declarations : test_case.cpp : <define>TEST_BOOST_NO_CXX11_AUTO_DECLARATIONS ;
obj cxx11_auto_multideclarations : test_case.cpp : <define>TEST_BOOST_NO_CXX11_AUTO_MULTIDECLARATIONS ;
obj auto_ptr : test_case.cpp : <define>TEST_BOOST_NO_AUTO_PTR ;
obj boost_bcb_partial_specialization_bug : test_case.cpp : <define>TEST_BOOST_BCB_PARTIAL_SPECIALIZATION_BUG ;
obj cxx11_char16_t : test_case.cpp : <define>TEST_BOOST_NO_CXX11_CHAR16_T ;
obj cxx11_char32_t : test_case.cpp : <define>TEST_BOOST_NO_CXX11_CHAR32_T ;
obj complete_value_initialization : test_case.cpp : <define>TEST_BOOST_NO_COMPLETE_VALUE_INITIALIZATION ;
obj cxx11_constexpr : test_case.cpp : <define>TEST_BOOST_NO_CXX11_CONSTEXPR ;
obj ctype_functions : test_case.cpp : <define>TEST_BOOST_NO_CTYPE_FUNCTIONS ;
obj cv_specializations : test_case.cpp : <define>TEST_BOOST_NO_CV_SPECIALIZATIONS ;
obj cv_void_specializations : test_case.cpp : <define>TEST_BOOST_NO_CV_VOID_SPECIALIZATIONS ;
obj cwchar : test_case.cpp : <define>TEST_BOOST_NO_CWCHAR ;
obj cwctype : test_case.cpp : <define>TEST_BOOST_NO_CWCTYPE ;
obj cxx03 : test_case.cpp : <define>TEST_BOOST_NO_CXX03 ;
obj cxx11 : test_case.cpp : <define>TEST_BOOST_NO_CXX11 ;
obj cxx11_addressof : test_case.cpp : <define>TEST_BOOST_NO_CXX11_ADDRESSOF ;
obj cxx11_alignas : test_case.cpp : <define>TEST_BOOST_NO_CXX11_ALIGNAS ;
obj cxx11_alignof : test_case.cpp : <define>TEST_BOOST_NO_CXX11_ALIGNOF ;
obj cxx11_allocator : test_case.cpp : <define>TEST_BOOST_NO_CXX11_ALLOCATOR ;
obj cxx11_atomic_smart_ptr : test_case.cpp : <define>TEST_BOOST_NO_CXX11_ATOMIC_SMART_PTR ;
obj cxx11_defaulted_moves : test_case.cpp : <define>TEST_BOOST_NO_CXX11_DEFAULTED_MOVES ;
obj cxx11_hdr_exception : test_case.cpp : <define>TEST_BOOST_NO_CXX11_HDR_EXCEPTION ;
obj cxx11_final : test_case.cpp : <define>TEST_BOOST_NO_CXX11_FINAL ;
obj cxx11_hdr_array : test_case.cpp : <define>TEST_BOOST_NO_CXX11_HDR_ARRAY ;
obj cxx11_hdr_atomic : test_case.cpp : <define>TEST_BOOST_NO_CXX11_HDR_ATOMIC ;
obj cxx11_hdr_chrono : test_case.cpp : <define>TEST_BOOST_NO_CXX11_HDR_CHRONO ;
obj cxx11_hdr_codecvt : test_case.cpp : <define>TEST_BOOST_NO_CXX11_HDR_CODECVT ;
obj cxx11_hdr_condition_variable : test_case.cpp : <define>TEST_BOOST_NO_CXX11_HDR_CONDITION_VARIABLE ;
obj cxx11_hdr_forward_list : test_case.cpp : <define>TEST_BOOST_NO_CXX11_HDR_FORWARD_LIST ;
obj cxx11_hdr_future : test_case.cpp : <define>TEST_BOOST_NO_CXX11_HDR_FUTURE ;
obj cxx11_hdr_initializer_list : test_case.cpp : <define>TEST_BOOST_NO_CXX11_HDR_INITIALIZER_LIST ;
obj cxx11_hdr_mutex : test_case.cpp : <define>TEST_BOOST_NO_CXX11_HDR_MUTEX ;
obj cxx11_hdr_random : test_case.cpp : <define>TEST_BOOST_NO_CXX11_HDR_RANDOM ;
obj cxx11_hdr_ratio : test_case.cpp : <define>TEST_BOOST_NO_CXX11_HDR_RATIO ;
obj cxx11_hdr_regex : test_case.cpp : <define>TEST_BOOST_NO_CXX11_HDR_REGEX ;
obj cxx11_hdr_system_error : test_case.cpp : <define>TEST_BOOST_NO_CXX11_HDR_SYSTEM_ERROR ;
obj cxx11_hdr_thread : test_case.cpp : <define>TEST_BOOST_NO_CXX11_HDR_THREAD ;
obj cxx11_hdr_tuple : test_case.cpp : <define>TEST_BOOST_NO_CXX11_HDR_TUPLE ;
obj cxx11_hdr_type_traits : test_case.cpp : <define>TEST_BOOST_NO_CXX11_HDR_TYPE_TRAITS ;
obj cxx11_hdr_typeindex : test_case.cpp : <define>TEST_BOOST_NO_CXX11_HDR_TYPEINDEX ;
obj cxx11_hdr_unordered_map : test_case.cpp : <define>TEST_BOOST_NO_CXX11_HDR_UNORDERED_MAP ;
obj cxx11_hdr_unordered_set : test_case.cpp : <define>TEST_BOOST_NO_CXX11_HDR_UNORDERED_SET ;
obj cxx11_inline_namespaces : test_case.cpp : <define>TEST_BOOST_NO_CXX11_INLINE_NAMESPACES ;
obj cxx11_non_public_defaulted_functions : test_case.cpp : <define>TEST_BOOST_NO_CXX11_NON_PUBLIC_DEFAULTED_FUNCTIONS ;
obj cxx11_numeric_limits : test_case.cpp : <define>TEST_BOOST_NO_CXX11_NUMERIC_LIMITS ;
obj cxx11_override : test_case.cpp : <define>TEST_BOOST_NO_CXX11_OVERRIDE ;
obj cxx11_pointer_traits : test_case.cpp : <define>TEST_BOOST_NO_CXX11_POINTER_TRAITS ;
obj cxx11_ref_qualifiers : test_case.cpp : <define>TEST_BOOST_NO_CXX11_REF_QUALIFIERS ;
obj cxx11_sfinae_expr : test_case.cpp : <define>TEST_BOOST_NO_CXX11_SFINAE_EXPR ;
obj cxx11_smart_ptr : test_case.cpp : <define>TEST_BOOST_NO_CXX11_SMART_PTR ;
obj cxx11_std_align : test_case.cpp : <define>TEST_BOOST_NO_CXX11_STD_ALIGN ;
obj cxx11_thread_local : test_case.cpp : <define>TEST_BOOST_NO_CXX11_THREAD_LOCAL ;
obj cxx11_trailing_result_types : test_case.cpp : <define>TEST_BOOST_NO_CXX11_TRAILING_RESULT_TYPES ;
obj cxx11_unrestricted_union : test_case.cpp : <define>TEST_BOOST_NO_CXX11_UNRESTRICTED_UNION ;
obj cxx11_user_defined_literals : test_case.cpp : <define>TEST_BOOST_NO_CXX11_USER_DEFINED_LITERALS ;
obj cxx14 : test_case.cpp : <define>TEST_BOOST_NO_CXX14 ;
obj cxx14_binary_literals : test_case.cpp : <define>TEST_BOOST_NO_CXX14_BINARY_LITERALS ;
obj cxx14_constexpr : test_case.cpp : <define>TEST_BOOST_NO_CXX14_CONSTEXPR ;
obj cxx14_decltype_auto : test_case.cpp : <define>TEST_BOOST_NO_CXX14_DECLTYPE_AUTO ;
obj cxx14_digit_separators : test_case.cpp : <define>TEST_BOOST_NO_CXX14_DIGIT_SEPARATORS ;
obj cxx14_generic_lambdas : test_case.cpp : <define>TEST_BOOST_NO_CXX14_GENERIC_LAMBDAS ;
obj cxx14_hdr_shared_mutex : test_case.cpp : <define>TEST_BOOST_NO_CXX14_HDR_SHARED_MUTEX ;
obj cxx14_initialized_lambda_captures : test_case.cpp : <define>TEST_BOOST_NO_CXX14_INITIALIZED_LAMBDA_CAPTURES ;
obj cxx14_aggregate_nsdmi : test_case.cpp : <define>TEST_BOOST_NO_CXX14_AGGREGATE_NSDMI ;
obj cxx14_return_type_deduction : test_case.cpp : <define>TEST_BOOST_NO_CXX14_RETURN_TYPE_DEDUCTION ;
obj cxx14_std_exchange : test_case.cpp : <define>TEST_BOOST_NO_CXX14_STD_EXCHANGE ;
obj cxx14_variable_templates : test_case.cpp : <define>TEST_BOOST_NO_CXX14_VARIABLE_TEMPLATES ;
obj cxx17 : test_case.cpp : <define>TEST_BOOST_NO_CXX17 ;
obj cxx17_auto_nontype_template_params : test_case.cpp : <define>TEST_BOOST_NO_CXX17_AUTO_NONTYPE_TEMPLATE_PARAMS ;
obj cxx17_deduction_guides : test_case.cpp : <define>TEST_BOOST_NO_CXX17_DEDUCTION_GUIDES ;
obj cxx17_fold_expressions : test_case.cpp : <define>TEST_BOOST_NO_CXX17_FOLD_EXPRESSIONS ;
obj cxx17_hdr_any : test_case.cpp : <define>TEST_BOOST_NO_CXX17_HDR_ANY ;
obj cxx17_hdr_charconv : test_case.cpp : <define>TEST_BOOST_NO_CXX17_HDR_CHARCONV ;
obj cxx17_hdr_execution : test_case.cpp : <define>TEST_BOOST_NO_CXX17_HDR_EXECUTION ;
obj cxx17_hdr_filesystem : test_case.cpp : <define>TEST_BOOST_NO_CXX17_HDR_FILESYSTEM ;
obj cxx17_hdr_memory_resource : test_case.cpp : <define>TEST_BOOST_NO_CXX17_HDR_MEMORY_RESOURCE ;
obj cxx17_hdr_optional : test_case.cpp : <define>TEST_BOOST_NO_CXX17_HDR_OPTIONAL ;
obj cxx17_hdr_string_view : test_case.cpp : <define>TEST_BOOST_NO_CXX17_HDR_STRING_VIEW ;
obj cxx17_hdr_variant : test_case.cpp : <define>TEST_BOOST_NO_CXX17_HDR_VARIANT ;
obj cxx17_if_constexpr : test_case.cpp : <define>TEST_BOOST_NO_CXX17_IF_CONSTEXPR ;
obj cxx17_inline_variables : test_case.cpp : <define>TEST_BOOST_NO_CXX17_INLINE_VARIABLES ;
obj cxx17_iterator_traits : test_case.cpp : <define>TEST_BOOST_NO_CXX17_ITERATOR_TRAITS ;
obj cxx17_std_apply : test_case.cpp : <define>TEST_BOOST_NO_CXX17_STD_APPLY ;
obj cxx17_std_invoke : test_case.cpp : <define>TEST_BOOST_NO_CXX17_STD_INVOKE ;
obj cxx17_structured_bindings : test_case.cpp : <define>TEST_BOOST_NO_CXX17_STRUCTURED_BINDINGS ;
obj cxx20_hdr_barrier : test_case.cpp : <define>TEST_BOOST_NO_CXX20_HDR_BARRIER ;
obj cxx20_hdr_bit : test_case.cpp : <define>TEST_BOOST_NO_CXX20_HDR_BIT ;
obj cxx20_hdr_compare : test_case.cpp : <define>TEST_BOOST_NO_CXX20_HDR_COMPARE ;
obj cxx20_hdr_concepts : test_case.cpp : <define>TEST_BOOST_NO_CXX20_HDR_CONCEPTS ;
obj cxx20_hdr_coroutine : test_case.cpp : <define>TEST_BOOST_NO_CXX20_HDR_COROUTINE ;
obj cxx20_hdr_format : test_case.cpp : <define>TEST_BOOST_NO_CXX20_HDR_FORMAT ;
obj cxx20_hdr_latch : test_case.cpp : <define>TEST_BOOST_NO_CXX20_HDR_LATCH ;
obj cxx20_hdr_numbers : test_case.cpp : <define>TEST_BOOST_NO_CXX20_HDR_NUMBERS ;
obj cxx20_hdr_ranges : test_case.cpp : <define>TEST_BOOST_NO_CXX20_HDR_RANGES ;
obj cxx20_hdr_semaphore : test_case.cpp : <define>TEST_BOOST_NO_CXX20_HDR_SEMAPHORE ;
obj cxx20_hdr_source_location : test_case.cpp : <define>TEST_BOOST_NO_CXX20_HDR_SOURCE_LOCATION ;
obj cxx20_hdr_span : test_case.cpp : <define>TEST_BOOST_NO_CXX20_HDR_SPAN ;
obj cxx20_hdr_stop_token : test_case.cpp : <define>TEST_BOOST_NO_CXX20_HDR_STOP_TOKEN ;
obj cxx20_hdr_syncstream : test_case.cpp : <define>TEST_BOOST_NO_CXX20_HDR_SYNCSTREAM ;
obj cxx20_hdr_version : test_case.cpp : <define>TEST_BOOST_NO_CXX20_HDR_VERSION ;
obj cxx23_hdr_expected : test_case.cpp : <define>TEST_BOOST_NO_CXX23_HDR_EXPECTED ;
obj cxx23_hdr_flat_map : test_case.cpp : <define>TEST_BOOST_NO_CXX23_HDR_FLAT_MAP ;
obj cxx23_hdr_flat_set : test_case.cpp : <define>TEST_BOOST_NO_CXX23_HDR_FLAT_SET ;
obj cxx23_hdr_generator : test_case.cpp : <define>TEST_BOOST_NO_CXX23_HDR_GENERATOR ;
obj cxx23_hdr_mdspan : test_case.cpp : <define>TEST_BOOST_NO_CXX23_HDR_MDSPAN ;
obj cxx23_hdr_print : test_case.cpp : <define>TEST_BOOST_NO_CXX23_HDR_PRINT ;
obj cxx23_hdr_spanstream : test_case.cpp : <define>TEST_BOOST_NO_CXX23_HDR_SPANSTREAM ;
obj cxx23_hdr_stacktrace : test_case.cpp : <define>TEST_BOOST_NO_CXX23_HDR_STACKTRACE ;
obj cxx23_hdr_stdfloat : test_case.cpp : <define>TEST_BOOST_NO_CXX23_HDR_STDFLOAT ;
obj cxx98_binders : test_case.cpp : <define>TEST_BOOST_NO_CXX98_BINDERS ;
obj cxx98_function_base : test_case.cpp : <define>TEST_BOOST_NO_CXX98_FUNCTION_BASE ;
obj cxx98_random_shuffle : test_case.cpp : <define>TEST_BOOST_NO_CXX98_RANDOM_SHUFFLE ;
obj cxx11_hdr_functional : test_case.cpp : <define>TEST_BOOST_NO_CXX11_HDR_FUNCTIONAL ;
obj cxx11_decltype : test_case.cpp : <define>TEST_BOOST_NO_CXX11_DECLTYPE ;
obj cxx11_decltype_n3276 : test_case.cpp : <define>TEST_BOOST_NO_CXX11_DECLTYPE_N3276 ;
obj boost_deduced_typename : test_case.cpp : <define>TEST_BOOST_DEDUCED_TYPENAME ;
obj cxx11_defaulted_functions : test_case.cpp : <define>TEST_BOOST_NO_CXX11_DEFAULTED_FUNCTIONS ;
obj cxx11_deleted_functions : test_case.cpp : <define>TEST_BOOST_NO_CXX11_DELETED_FUNCTIONS ;
obj dependent_nested_derivations : test_case.cpp : <define>TEST_BOOST_NO_DEPENDENT_NESTED_DERIVATIONS ;
obj dependent_types_in_template_value_parameters : test_case.cpp : <define>TEST_BOOST_NO_DEPENDENT_TYPES_IN_TEMPLATE_VALUE_PARAMETERS ;
obj exception_std_namespace : test_case.cpp : <define>TEST_BOOST_NO_EXCEPTION_STD_NAMESPACE ;
obj exceptions : test_case.cpp : <define>TEST_BOOST_NO_EXCEPTIONS ;
obj explicit_function_template_arguments : test_case.cpp : <define>TEST_BOOST_NO_EXPLICIT_FUNCTION_TEMPLATE_ARGUMENTS ;
obj cxx11_explicit_conversion_operators : test_case.cpp : <define>TEST_BOOST_NO_CXX11_EXPLICIT_CONVERSION_OPERATORS ;
obj cxx11_extern_template : test_case.cpp : <define>TEST_BOOST_NO_CXX11_EXTERN_TEMPLATE ;
obj fenv_h : test_case.cpp : <define>TEST_BOOST_NO_FENV_H ;
obj cxx11_fixed_length_variadic_template_expansion_packs : test_case.cpp : <define>TEST_BOOST_NO_CXX11_FIXED_LENGTH_VARIADIC_TEMPLATE_EXPANSION_PACKS ;
obj function_template_ordering : test_case.cpp : <define>TEST_BOOST_NO_FUNCTION_TEMPLATE_ORDERING ;
obj cxx11_function_template_default_args : test_case.cpp : <define>TEST_BOOST_NO_CXX11_FUNCTION_TEMPLATE_DEFAULT_ARGS ;
obj function_type_specializations : test_case.cpp : <define>TEST_BOOST_NO_FUNCTION_TYPE_SPECIALIZATIONS ;
obj ms_int64_numeric_limits : test_case.cpp : <define>TEST_BOOST_NO_MS_INT64_NUMERIC_LIMITS ;
obj inclass_member_initialization : test_case.cpp : <define>TEST_BOOST_NO_INCLASS_MEMBER_INITIALIZATION ;
obj integral_int64_t : test_case.cpp : <define>TEST_BOOST_NO_INTEGRAL_INT64_T ;
obj iosfwd : test_case.cpp : <define>TEST_BOOST_NO_IOSFWD ;
obj iostream : test_case.cpp : <define>TEST_BOOST_NO_IOSTREAM ;
obj is_abstract : test_case.cpp : <define>TEST_BOOST_NO_IS_ABSTRACT ;
obj templated_iterator_constructors : test_case.cpp : <define>TEST_BOOST_NO_TEMPLATED_ITERATOR_CONSTRUCTORS ;
obj cxx11_lambdas : test_case.cpp : <define>TEST_BOOST_NO_CXX11_LAMBDAS ;
obj limits : test_case.cpp : <define>TEST_BOOST_NO_LIMITS ;
obj limits_compile_time_constants : test_case.cpp : <define>TEST_BOOST_NO_LIMITS_COMPILE_TIME_CONSTANTS ;
obj long_long_numeric_limits : test_case.cpp : <define>TEST_BOOST_NO_LONG_LONG_NUMERIC_LIMITS ;
obj member_function_specializations : test_case.cpp : <define>TEST_BOOST_NO_MEMBER_FUNCTION_SPECIALIZATIONS ;
obj member_template_keyword : test_case.cpp : <define>TEST_BOOST_NO_MEMBER_TEMPLATE_KEYWORD ;
obj pointer_to_member_template_parameters : test_case.cpp : <define>TEST_BOOST_NO_POINTER_TO_MEMBER_TEMPLATE_PARAMETERS ;
obj member_template_friends : test_case.cpp : <define>TEST_BOOST_NO_MEMBER_TEMPLATE_FRIENDS ;
obj member_templates : test_case.cpp : <define>TEST_BOOST_NO_MEMBER_TEMPLATES ;
obj nested_friendship : test_case.cpp : <define>TEST_BOOST_NO_NESTED_FRIENDSHIP ;
obj cxx11_noexcept : test_case.cpp : <define>TEST_BOOST_NO_CXX11_NOEXCEPT ;
obj cxx11_nullptr : test_case.cpp : <define>TEST_BOOST_NO_CXX11_NULLPTR ;
obj operators_in_namespace : test_case.cpp : <define>TEST_BOOST_NO_OPERATORS_IN_NAMESPACE ;
obj partial_specialization_implicit_default_args : test_case.cpp : <define>TEST_BOOST_NO_PARTIAL_SPECIALIZATION_IMPLICIT_DEFAULT_ARGS ;
obj template_partial_specialization : test_case.cpp : <define>TEST_BOOST_NO_TEMPLATE_PARTIAL_SPECIALIZATION ;
obj private_in_aggregate : test_case.cpp : <define>TEST_BOOST_NO_PRIVATE_IN_AGGREGATE ;
obj pointer_to_member_const : test_case.cpp : <define>TEST_BOOST_NO_POINTER_TO_MEMBER_CONST ;
obj cxx11_range_based_for : test_case.cpp : <define>TEST_BOOST_NO_CXX11_RANGE_BASED_FOR ;
obj cxx11_raw_literals : test_case.cpp : <define>TEST_BOOST_NO_CXX11_RAW_LITERALS ;
obj restrict_references : test_case.cpp : <define>TEST_BOOST_NO_RESTRICT_REFERENCES ;
obj unreachable_return_detection : test_case.cpp : <define>TEST_BOOST_NO_UNREACHABLE_RETURN_DETECTION ;
obj rtti : test_case.cpp : <define>TEST_BOOST_NO_RTTI ;
obj cxx11_rvalue_references : test_case.cpp : <define>TEST_BOOST_NO_CXX11_RVALUE_REFERENCES ;
obj cxx11_scoped_enums : test_case.cpp : <define>TEST_BOOST_NO_CXX11_SCOPED_ENUMS ;
obj sfinae : test_case.cpp : <define>TEST_BOOST_NO_SFINAE ;
obj sfinae_expr : test_case.cpp : <define>TEST_BOOST_NO_SFINAE_EXPR ;
obj stringstream : test_case.cpp : <define>TEST_BOOST_NO_STRINGSTREAM ;
obj cxx11_static_assert : test_case.cpp : <define>TEST_BOOST_NO_CXX11_STATIC_ASSERT ;
obj std_allocator : test_case.cpp : <define>TEST_BOOST_NO_STD_ALLOCATOR ;
obj std_distance : test_case.cpp : <define>TEST_BOOST_NO_STD_DISTANCE ;
obj std_iterator_traits : test_case.cpp : <define>TEST_BOOST_NO_STD_ITERATOR_TRAITS ;
obj std_iterator : test_case.cpp : <define>TEST_BOOST_NO_STD_ITERATOR ;
obj std_locale : test_case.cpp : <define>TEST_BOOST_NO_STD_LOCALE ;
obj std_messages : test_case.cpp : <define>TEST_BOOST_NO_STD_MESSAGES ;
obj std_min_max : test_case.cpp : <define>TEST_BOOST_NO_STD_MIN_MAX ;
obj std_output_iterator_assign : test_case.cpp : <define>TEST_BOOST_NO_STD_OUTPUT_ITERATOR_ASSIGN ;
obj std_typeinfo : test_case.cpp : <define>TEST_BOOST_NO_STD_TYPEINFO ;
obj std_use_facet : test_case.cpp : <define>TEST_BOOST_NO_STD_USE_FACET ;
obj std_wstreambuf : test_case.cpp : <define>TEST_BOOST_NO_STD_WSTREAMBUF ;
obj std_wstring : test_case.cpp : <define>TEST_BOOST_NO_STD_WSTRING ;
obj stdc_namespace : test_case.cpp : <define>TEST_BOOST_NO_STDC_NAMESPACE ;
obj swprintf : test_case.cpp : <define>TEST_BOOST_NO_SWPRINTF ;
obj cxx11_local_class_template_parameters : test_case.cpp : <define>TEST_BOOST_NO_CXX11_LOCAL_CLASS_TEMPLATE_PARAMETERS ;
obj cxx11_template_aliases : test_case.cpp : <define>TEST_BOOST_NO_CXX11_TEMPLATE_ALIASES ;
obj templated_iostreams : test_case.cpp : <define>TEST_BOOST_NO_TEMPLATED_IOSTREAMS ;
obj template_templates : test_case.cpp : <define>TEST_BOOST_NO_TEMPLATE_TEMPLATES ;
obj two_phase_name_lookup : test_case.cpp : <define>TEST_BOOST_NO_TWO_PHASE_NAME_LOOKUP ;
obj typeid : test_case.cpp : <define>TEST_BOOST_NO_TYPEID ;
obj typename_with_ctor : test_case.cpp : <define>TEST_BOOST_NO_TYPENAME_WITH_CTOR ;
obj cxx11_unicode_literals : test_case.cpp : <define>TEST_BOOST_NO_CXX11_UNICODE_LITERALS ;
obj cxx11_unified_initialization_syntax : test_case.cpp : <define>TEST_BOOST_NO_CXX11_UNIFIED_INITIALIZATION_SYNTAX ;
obj boost_function_scope_using_declaration_breaks_adl : test_case.cpp : <define>TEST_BOOST_FUNCTION_SCOPE_USING_DECLARATION_BREAKS_ADL ;
obj using_declaration_overloads_from_typename_base : test_case.cpp : <define>TEST_BOOST_NO_USING_DECLARATION_OVERLOADS_FROM_TYPENAME_BASE ;
obj using_template : test_case.cpp : <define>TEST_BOOST_NO_USING_TEMPLATE ;
obj cxx11_variadic_macros : test_case.cpp : <define>TEST_BOOST_NO_CXX11_VARIADIC_MACROS ;
obj cxx11_variadic_templates : test_case.cpp : <define>TEST_BOOST_NO_CXX11_VARIADIC_TEMPLATES ;
obj void_returns : test_case.cpp : <define>TEST_BOOST_NO_VOID_RETURNS ;
obj intrinsic_wchar_t : test_case.cpp : <define>TEST_BOOST_NO_INTRINSIC_WCHAR_T ;
obj cpp_consteval_23 : std/cpp_consteval_23.cpp ;
alias cpp_consteval : cpp_consteval_23 ;
obj cpp_explicit_this_parameter_23 : std/cpp_explicit_this_parameter_23.cpp ;
alias cpp_explicit_this_parameter : cpp_explicit_this_parameter_23 ;
obj cpp_if_consteval_23 : std/cpp_if_consteval_23.cpp ;
alias cpp_if_consteval : cpp_if_consteval_23 ;
obj cpp_implicit_move_23 : std/cpp_implicit_move_23.cpp ;
alias cpp_implicit_move : cpp_implicit_move_23 ;
obj cpp_multidimensional_subscript_23 : std/cpp_multidimensional_subscript_23.cpp ;
alias cpp_multidimensional_subscript : cpp_multidimensional_subscript_23 ;
obj cpp_named_character_escapes_23 : std/cpp_named_character_escapes_23.cpp ;
alias cpp_named_character_escapes : cpp_named_character_escapes_23 ;
obj cpp_range_based_for_23 : std/cpp_range_based_for_23.cpp ;
alias cpp_range_based_for : cpp_range_based_for_23 ;
obj cpp_size_t_suffix_23 : std/cpp_size_t_suffix_23.cpp ;
alias cpp_size_t_suffix : cpp_size_t_suffix_23 ;
obj cpp_static_call_operator_23 : std/cpp_static_call_operator_23.cpp ;
alias cpp_static_call_operator : cpp_static_call_operator_23 ;
obj cpp_impl_destroying_delete_20 : std/cpp_impl_destroying_delete_20.cpp ;
alias cpp_impl_destroying_delete : cpp_impl_destroying_delete_20 ;
obj cpp_lib_destroying_delete_20 : std/cpp_lib_destroying_delete_20.cpp ;
alias cpp_lib_destroying_delete : cpp_lib_destroying_delete_20 ;
obj cpp_char8_t_20 : std/cpp_char8_t_20.cpp ;
alias cpp_char8_t : cpp_char8_t_20 ;
obj cpp_impl_three_way_comparison_20 : std/cpp_impl_three_way_comparison_20.cpp ;
alias cpp_impl_three_way_comparison : cpp_impl_three_way_comparison_20 ;
obj cpp_lib_three_way_comparison_20 : std/cpp_lib_three_way_comparison_20.cpp ;
alias cpp_lib_three_way_comparison : cpp_lib_three_way_comparison_20 ;
obj cpp_conditional_explicit_20 : std/cpp_conditional_explicit_20.cpp ;
alias cpp_conditional_explicit : cpp_conditional_explicit_20 ;
obj cpp_nontype_template_parameter_class_20 : std/cpp_nontype_template_parameter_class_20.cpp ;
alias cpp_nontype_template_parameter_class : cpp_nontype_template_parameter_class_20 ;
obj cpp_lib_char8_t_20 : std/cpp_lib_char8_t_20.cpp ;
alias cpp_lib_char8_t : cpp_lib_char8_t_20 ;
obj cpp_lib_concepts_20 : std/cpp_lib_concepts_20.cpp ;
alias cpp_lib_concepts : cpp_lib_concepts_20 ;
obj cpp_lib_constexpr_swap_algorithms_20 : std/cpp_lib_constexpr_swap_algorithms_20.cpp ;
alias cpp_lib_constexpr_swap_algorithms : cpp_lib_constexpr_swap_algorithms_20 ;
obj cpp_lib_constexpr_misc_20 : std/cpp_lib_constexpr_misc_20.cpp ;
alias cpp_lib_constexpr_misc : cpp_lib_constexpr_misc_20 ;
obj cpp_lib_bind_front_20 : std/cpp_lib_bind_front_20.cpp ;
alias cpp_lib_bind_front : cpp_lib_bind_front_20 ;
obj cpp_lib_is_constant_evaluated_20 : std/cpp_lib_is_constant_evaluated_20.cpp ;
alias cpp_lib_is_constant_evaluated : cpp_lib_is_constant_evaluated_20 ;
obj cpp_lib_erase_if_20 : std/cpp_lib_erase_if_20.cpp ;
alias cpp_lib_erase_if : cpp_lib_erase_if_20 ;
obj cpp_lib_list_remove_return_type_20 : std/cpp_lib_list_remove_return_type_20.cpp ;
alias cpp_lib_list_remove_return_type : cpp_lib_list_remove_return_type_20 ;
obj cpp_lib_generic_unordered_lookup_20 : std/cpp_lib_generic_unordered_lookup_20.cpp ;
alias cpp_lib_generic_unordered_lookup : cpp_lib_generic_unordered_lookup_20 ;
obj cpp_lib_ranges_20 : std/cpp_lib_ranges_20.cpp ;
alias cpp_lib_ranges : cpp_lib_ranges_20 ;
obj cpp_lib_bit_cast_20 : std/cpp_lib_bit_cast_20.cpp ;
alias cpp_lib_bit_cast : cpp_lib_bit_cast_20 ;
obj cpp_lib_atomic_ref_20 : std/cpp_lib_atomic_ref_20.cpp ;
alias cpp_lib_atomic_ref : cpp_lib_atomic_ref_20 ;
obj cpp_hex_float_17 : std/cpp_hex_float_17.cpp ;
alias cpp_hex_float : cpp_hex_float_17 ;
obj cpp_inline_variables_17 : std/cpp_inline_variables_17.cpp ;
alias cpp_inline_variables : cpp_inline_variables_17 ;
obj cpp_aligned_new_17 : std/cpp_aligned_new_17.cpp ;
alias cpp_aligned_new : cpp_aligned_new_17 ;
obj cpp_guaranteed_copy_elision_17 : std/cpp_guaranteed_copy_elision_17.cpp ;
alias cpp_guaranteed_copy_elision : cpp_guaranteed_copy_elision_17 ;
obj cpp_noexcept_function_type_17 : std/cpp_noexcept_function_type_17.cpp ;
alias cpp_noexcept_function_type : cpp_noexcept_function_type_17 ;
obj cpp_fold_expressions_17 : std/cpp_fold_expressions_17.cpp ;
alias cpp_fold_expressions : cpp_fold_expressions_17 ;
obj cpp_capture_star_this_17 : std/cpp_capture_star_this_17.cpp ;
alias cpp_capture_star_this : cpp_capture_star_this_17 ;
obj cpp_constexpr_17 : std/cpp_constexpr_17.cpp ;
obj cpp_if_constexpr_17 : std/cpp_if_constexpr_17.cpp ;
alias cpp_if_constexpr : cpp_if_constexpr_17 ;
obj cpp_range_based_for_17 : std/cpp_range_based_for_17.cpp ;
obj cpp_static_assert_17 : std/cpp_static_assert_17.cpp ;
obj cpp_deduction_guides_17 : std/cpp_deduction_guides_17.cpp ;
alias cpp_deduction_guides : cpp_deduction_guides_17 ;
obj cpp_nontype_template_parameter_auto_17 : std/cpp_nontype_template_parameter_auto_17.cpp ;
alias cpp_nontype_template_parameter_auto : cpp_nontype_template_parameter_auto_17 ;
obj cpp_namespace_attributes_17 : std/cpp_namespace_attributes_17.cpp ;
alias cpp_namespace_attributes : cpp_namespace_attributes_17 ;
obj cpp_enumerator_attributes_17 : std/cpp_enumerator_attributes_17.cpp ;
alias cpp_enumerator_attributes : cpp_enumerator_attributes_17 ;
obj cpp_inheriting_constructors_17 : std/cpp_inheriting_constructors_17.cpp ;
obj cpp_variadic_using_17 : std/cpp_variadic_using_17.cpp ;
alias cpp_variadic_using : cpp_variadic_using_17 ;
obj cpp_structured_bindings_17 : std/cpp_structured_bindings_17.cpp ;
alias cpp_structured_bindings : cpp_structured_bindings_17 ;
obj cpp_aggregate_bases_17 : std/cpp_aggregate_bases_17.cpp ;
alias cpp_aggregate_bases : cpp_aggregate_bases_17 ;
obj cpp_nontype_template_args_17 : std/cpp_nontype_template_args_17.cpp ;
alias cpp_nontype_template_args : cpp_nontype_template_args_17 ;
obj cpp_template_template_args_17 : std/cpp_template_template_args_17.cpp ;
alias cpp_template_template_args : cpp_template_template_args_17 ;
obj cpp_lib_byte_17 : std/cpp_lib_byte_17.cpp ;
alias cpp_lib_byte : cpp_lib_byte_17 ;
obj cpp_lib_hardware_interference_size_17 : std/cpp_lib_hardware_interference_size_17.cpp ;
alias cpp_lib_hardware_interference_size : cpp_lib_hardware_interference_size_17 ;
obj cpp_lib_launder_17 : std/cpp_lib_launder_17.cpp ;
alias cpp_lib_launder : cpp_lib_launder_17 ;
obj cpp_lib_uncaught_exceptions_17 : std/cpp_lib_uncaught_exceptions_17.cpp ;
alias cpp_lib_uncaught_exceptions : cpp_lib_uncaught_exceptions_17 ;
obj cpp_lib_as_const_17 : std/cpp_lib_as_const_17.cpp ;
alias cpp_lib_as_const : cpp_lib_as_const_17 ;
obj cpp_lib_make_from_tuple_17 : std/cpp_lib_make_from_tuple_17.cpp ;
alias cpp_lib_make_from_tuple : cpp_lib_make_from_tuple_17 ;
obj cpp_lib_apply_17 : std/cpp_lib_apply_17.cpp ;
alias cpp_lib_apply : cpp_lib_apply_17 ;
obj cpp_lib_optional_17 : std/cpp_lib_optional_17.cpp ;
alias cpp_lib_optional : cpp_lib_optional_17 ;
obj cpp_lib_variant_17 : std/cpp_lib_variant_17.cpp ;
alias cpp_lib_variant : cpp_lib_variant_17 ;
obj cpp_lib_any_17 : std/cpp_lib_any_17.cpp ;
alias cpp_lib_any : cpp_lib_any_17 ;
obj cpp_lib_addressof_constexpr_17 : std/cpp_lib_addressof_constexpr_17.cpp ;
alias cpp_lib_addressof_constexpr : cpp_lib_addressof_constexpr_17 ;
obj cpp_lib_raw_memory_algorithms_17 : std/cpp_lib_raw_memory_algorithms_17.cpp ;
alias cpp_lib_raw_memory_algorithms : cpp_lib_raw_memory_algorithms_17 ;
obj cpp_lib_transparent_operators_17 : std/cpp_lib_transparent_operators_17.cpp ;
obj cpp_lib_enable_shared_from_this_17 : std/cpp_lib_enable_shared_from_this_17.cpp ;
alias cpp_lib_enable_shared_from_this : cpp_lib_enable_shared_from_this_17 ;
obj cpp_lib_shared_ptr_weak_type_17 : std/cpp_lib_shared_ptr_weak_type_17.cpp ;
alias cpp_lib_shared_ptr_weak_type : cpp_lib_shared_ptr_weak_type_17 ;
obj cpp_lib_shared_ptr_arrays_17 : std/cpp_lib_shared_ptr_arrays_17.cpp ;
alias cpp_lib_shared_ptr_arrays : cpp_lib_shared_ptr_arrays_17 ;
obj cpp_lib_memory_resource_17 : std/cpp_lib_memory_resource_17.cpp ;
alias cpp_lib_memory_resource : cpp_lib_memory_resource_17 ;
obj cpp_lib_boyer_moore_searcher_17 : std/cpp_lib_boyer_moore_searcher_17.cpp ;
alias cpp_lib_boyer_moore_searcher : cpp_lib_boyer_moore_searcher_17 ;
obj cpp_lib_invoke_17 : std/cpp_lib_invoke_17.cpp ;
alias cpp_lib_invoke : cpp_lib_invoke_17 ;
obj cpp_lib_not_fn_17 : std/cpp_lib_not_fn_17.cpp ;
alias cpp_lib_not_fn : cpp_lib_not_fn_17 ;
obj cpp_lib_void_t_17 : std/cpp_lib_void_t_17.cpp ;
alias cpp_lib_void_t : cpp_lib_void_t_17 ;
obj cpp_lib_bool_constant_17 : std/cpp_lib_bool_constant_17.cpp ;
alias cpp_lib_bool_constant : cpp_lib_bool_constant_17 ;
obj cpp_lib_type_trait_variable_templates_17 : std/cpp_lib_type_trait_variable_templates_17.cpp ;
alias cpp_lib_type_trait_variable_templates : cpp_lib_type_trait_variable_templates_17 ;
obj cpp_lib_logical_traits_17 : std/cpp_lib_logical_traits_17.cpp ;
alias cpp_lib_logical_traits : cpp_lib_logical_traits_17 ;
obj cpp_lib_is_swappable_17 : std/cpp_lib_is_swappable_17.cpp ;
alias cpp_lib_is_swappable : cpp_lib_is_swappable_17 ;
obj cpp_lib_is_invocable_17 : std/cpp_lib_is_invocable_17.cpp ;
alias cpp_lib_is_invocable : cpp_lib_is_invocable_17 ;
obj cpp_lib_has_unique_object_representations_17 : std/cpp_lib_has_unique_object_representations_17.cpp ;
alias cpp_lib_has_unique_object_representations : cpp_lib_has_unique_object_representations_17 ;
obj cpp_lib_is_aggregate_17 : std/cpp_lib_is_aggregate_17.cpp ;
alias cpp_lib_is_aggregate : cpp_lib_is_aggregate_17 ;
obj cpp_lib_chrono_17 : std/cpp_lib_chrono_17.cpp ;
alias cpp_lib_chrono : cpp_lib_chrono_17 ;
obj cpp_lib_execution_17 : std/cpp_lib_execution_17.cpp ;
alias cpp_lib_execution : cpp_lib_execution_17 ;
obj cpp_lib_parallel_algorithm_17 : std/cpp_lib_parallel_algorithm_17.cpp ;
alias cpp_lib_parallel_algorithm : cpp_lib_parallel_algorithm_17 ;
obj cpp_lib_to_chars_17 : std/cpp_lib_to_chars_17.cpp ;
alias cpp_lib_to_chars : cpp_lib_to_chars_17 ;
obj cpp_lib_string_view_17 : std/cpp_lib_string_view_17.cpp ;
alias cpp_lib_string_view : cpp_lib_string_view_17 ;
obj cpp_lib_allocator_traits_is_always_equal_17 : std/cpp_lib_allocator_traits_is_always_equal_17.cpp ;
alias cpp_lib_allocator_traits_is_always_equal : cpp_lib_allocator_traits_is_always_equal_17 ;
obj cpp_lib_incomplete_container_elements_17 : std/cpp_lib_incomplete_container_elements_17.cpp ;
alias cpp_lib_incomplete_container_elements : cpp_lib_incomplete_container_elements_17 ;
obj cpp_lib_map_try_emplace_17 : std/cpp_lib_map_try_emplace_17.cpp ;
alias cpp_lib_map_try_emplace : cpp_lib_map_try_emplace_17 ;
obj cpp_lib_unordered_map_try_emplace_17 : std/cpp_lib_unordered_map_try_emplace_17.cpp ;
alias cpp_lib_unordered_map_try_emplace : cpp_lib_unordered_map_try_emplace_17 ;
obj cpp_lib_node_extract_17 : std/cpp_lib_node_extract_17.cpp ;
alias cpp_lib_node_extract : cpp_lib_node_extract_17 ;
obj cpp_lib_array_constexpr_17 : std/cpp_lib_array_constexpr_17.cpp ;
alias cpp_lib_array_constexpr : cpp_lib_array_constexpr_17 ;
obj cpp_lib_nonmember_container_access_17 : std/cpp_lib_nonmember_container_access_17.cpp ;
alias cpp_lib_nonmember_container_access : cpp_lib_nonmember_container_access_17 ;
obj cpp_lib_sample_17 : std/cpp_lib_sample_17.cpp ;
alias cpp_lib_sample : cpp_lib_sample_17 ;
obj cpp_lib_clamp_17 : std/cpp_lib_clamp_17.cpp ;
alias cpp_lib_clamp : cpp_lib_clamp_17 ;
obj cpp_lib_gcd_lcm_17 : std/cpp_lib_gcd_lcm_17.cpp ;
alias cpp_lib_gcd_lcm : cpp_lib_gcd_lcm_17 ;
obj cpp_lib_hypot_17 : std/cpp_lib_hypot_17.cpp ;
alias cpp_lib_hypot : cpp_lib_hypot_17 ;
obj cpp_lib_math_special_functions_17 : std/cpp_lib_math_special_functions_17.cpp ;
alias cpp_lib_math_special_functions : cpp_lib_math_special_functions_17 ;
obj cpp_lib_filesystem_17 : std/cpp_lib_filesystem_17.cpp ;
alias cpp_lib_filesystem : cpp_lib_filesystem_17 ;
obj cpp_lib_atomic_is_always_lock_free_17 : std/cpp_lib_atomic_is_always_lock_free_17.cpp ;
alias cpp_lib_atomic_is_always_lock_free : cpp_lib_atomic_is_always_lock_free_17 ;
obj cpp_lib_shared_mutex_17 : std/cpp_lib_shared_mutex_17.cpp ;
alias cpp_lib_shared_mutex : cpp_lib_shared_mutex_17 ;
obj cpp_lib_scoped_lock_17 : std/cpp_lib_scoped_lock_17.cpp ;
alias cpp_lib_scoped_lock : cpp_lib_scoped_lock_17 ;
obj cpp_binary_literals_14 : std/cpp_binary_literals_14.cpp ;
alias cpp_binary_literals : cpp_binary_literals_14 ;
obj cpp_init_captures_14 : std/cpp_init_captures_14.cpp ;
alias cpp_init_captures : cpp_init_captures_14 ;
obj cpp_generic_lambdas_14 : std/cpp_generic_lambdas_14.cpp ;
alias cpp_generic_lambdas : cpp_generic_lambdas_14 ;
obj cpp_sized_deallocation_14 : std/cpp_sized_deallocation_14.cpp ;
alias cpp_sized_deallocation : cpp_sized_deallocation_14 ;
obj cpp_constexpr_14 : std/cpp_constexpr_14.cpp ;
obj cpp_decltype_auto_14 : std/cpp_decltype_auto_14.cpp ;
alias cpp_decltype_auto : cpp_decltype_auto_14 ;
obj cpp_return_type_deduction_14 : std/cpp_return_type_deduction_14.cpp ;
alias cpp_return_type_deduction : cpp_return_type_deduction_14 ;
obj cpp_aggregate_nsdmi_14 : std/cpp_aggregate_nsdmi_14.cpp ;
alias cpp_aggregate_nsdmi : cpp_aggregate_nsdmi_14 ;
obj cpp_variable_templates_14 : std/cpp_variable_templates_14.cpp ;
alias cpp_variable_templates : cpp_variable_templates_14 ;
obj cpp_lib_integer_sequence_14 : std/cpp_lib_integer_sequence_14.cpp ;
alias cpp_lib_integer_sequence : cpp_lib_integer_sequence_14 ;
obj cpp_lib_exchange_function_14 : std/cpp_lib_exchange_function_14.cpp ;
alias cpp_lib_exchange_function : cpp_lib_exchange_function_14 ;
obj cpp_lib_tuples_by_type_14 : std/cpp_lib_tuples_by_type_14.cpp ;
alias cpp_lib_tuples_by_type : cpp_lib_tuples_by_type_14 ;
obj cpp_lib_tuple_element_t_14 : std/cpp_lib_tuple_element_t_14.cpp ;
alias cpp_lib_tuple_element_t : cpp_lib_tuple_element_t_14 ;
obj cpp_lib_make_unique_14 : std/cpp_lib_make_unique_14.cpp ;
alias cpp_lib_make_unique : cpp_lib_make_unique_14 ;
obj cpp_lib_transparent_operators_14 : std/cpp_lib_transparent_operators_14.cpp ;
alias cpp_lib_transparent_operators : cpp_lib_transparent_operators_14 ;
obj cpp_lib_integral_constant_callable_14 : std/cpp_lib_integral_constant_callable_14.cpp ;
alias cpp_lib_integral_constant_callable : cpp_lib_integral_constant_callable_14 ;
obj cpp_lib_transformation_trait_aliases_14 : std/cpp_lib_transformation_trait_aliases_14.cpp ;
alias cpp_lib_transformation_trait_aliases : cpp_lib_transformation_trait_aliases_14 ;
obj cpp_lib_result_of_sfinae_14 : std/cpp_lib_result_of_sfinae_14.cpp ;
alias cpp_lib_result_of_sfinae : cpp_lib_result_of_sfinae_14 ;
obj cpp_lib_is_final_14 : std/cpp_lib_is_final_14.cpp ;
alias cpp_lib_is_final : cpp_lib_is_final_14 ;
obj cpp_lib_is_null_pointer_14 : std/cpp_lib_is_null_pointer_14.cpp ;
alias cpp_lib_is_null_pointer : cpp_lib_is_null_pointer_14 ;
obj cpp_lib_chrono_udls_14 : std/cpp_lib_chrono_udls_14.cpp ;
alias cpp_lib_chrono_udls : cpp_lib_chrono_udls_14 ;
obj cpp_lib_string_udls_14 : std/cpp_lib_string_udls_14.cpp ;
alias cpp_lib_string_udls : cpp_lib_string_udls_14 ;
obj cpp_lib_generic_associative_lookup_14 : std/cpp_lib_generic_associative_lookup_14.cpp ;
alias cpp_lib_generic_associative_lookup : cpp_lib_generic_associative_lookup_14 ;
obj cpp_lib_null_iterators_14 : std/cpp_lib_null_iterators_14.cpp ;
alias cpp_lib_null_iterators : cpp_lib_null_iterators_14 ;
obj cpp_lib_make_reverse_iterator_14 : std/cpp_lib_make_reverse_iterator_14.cpp ;
alias cpp_lib_make_reverse_iterator : cpp_lib_make_reverse_iterator_14 ;
obj cpp_lib_robust_nonmodifying_seq_ops_14 : std/cpp_lib_robust_nonmodifying_seq_ops_14.cpp ;
alias cpp_lib_robust_nonmodifying_seq_ops : cpp_lib_robust_nonmodifying_seq_ops_14 ;
obj cpp_lib_complex_udls_14 : std/cpp_lib_complex_udls_14.cpp ;
alias cpp_lib_complex_udls : cpp_lib_complex_udls_14 ;
obj cpp_lib_quoted_string_io_14 : std/cpp_lib_quoted_string_io_14.cpp ;
alias cpp_lib_quoted_string_io : cpp_lib_quoted_string_io_14 ;
obj cpp_lib_shared_timed_mutex_14 : std/cpp_lib_shared_timed_mutex_14.cpp ;
alias cpp_lib_shared_timed_mutex : cpp_lib_shared_timed_mutex_14 ;
obj cpp_unicode_characters_11 : std/cpp_unicode_characters_11.cpp ;
alias cpp_unicode_characters : cpp_unicode_characters_11 ;
obj cpp_raw_strings_11 : std/cpp_raw_strings_11.cpp ;
alias cpp_raw_strings : cpp_raw_strings_11 ;
obj cpp_unicode_literals_11 : std/cpp_unicode_literals_11.cpp ;
alias cpp_unicode_literals : cpp_unicode_literals_11 ;
obj cpp_user_defined_literals_11 : std/cpp_user_defined_literals_11.cpp ;
alias cpp_user_defined_literals : cpp_user_defined_literals_11 ;
obj cpp_threadsafe_static_init_11 : std/cpp_threadsafe_static_init_11.cpp ;
alias cpp_threadsafe_static_init : cpp_threadsafe_static_init_11 ;
obj cpp_lambdas_11 : std/cpp_lambdas_11.cpp ;
alias cpp_lambdas : cpp_lambdas_11 ;
obj cpp_constexpr_11 : std/cpp_constexpr_11.cpp ;
alias cpp_constexpr : cpp_constexpr_11 ;
obj cpp_range_based_for_11 : std/cpp_range_based_for_11.cpp ;
alias cpp_range_based_for : cpp_range_based_for_11 ;
obj cpp_static_assert_11 : std/cpp_static_assert_11.cpp ;
alias cpp_static_assert : cpp_static_assert_11 ;
obj cpp_decltype_11 : std/cpp_decltype_11.cpp ;
alias cpp_decltype : cpp_decltype_11 ;
obj cpp_attributes_11 : std/cpp_attributes_11.cpp ;
alias cpp_attributes : cpp_attributes_11 ;
obj cpp_rvalue_references_11 : std/cpp_rvalue_references_11.cpp ;
alias cpp_rvalue_references : cpp_rvalue_references_11 ;
obj cpp_variadic_templates_11 : std/cpp_variadic_templates_11.cpp ;
alias cpp_variadic_templates : cpp_variadic_templates_11 ;
obj cpp_initializer_lists_11 : std/cpp_initializer_lists_11.cpp ;
alias cpp_initializer_lists : cpp_initializer_lists_11 ;
obj cpp_explicit_conversion_11 : std/cpp_explicit_conversion_11.cpp ;
alias cpp_explicit_conversion : cpp_explicit_conversion_11 ;
obj cpp_delegating_constructors_11 : std/cpp_delegating_constructors_11.cpp ;
alias cpp_delegating_constructors : cpp_delegating_constructors_11 ;
obj cpp_nsdmi_11 : std/cpp_nsdmi_11.cpp ;
alias cpp_nsdmi : cpp_nsdmi_11 ;
obj cpp_inheriting_constructors_11 : std/cpp_inheriting_constructors_11.cpp ;
alias cpp_inheriting_constructors : cpp_inheriting_constructors_11 ;
obj cpp_ref_qualifiers_11 : std/cpp_ref_qualifiers_11.cpp ;
alias cpp_ref_qualifiers : cpp_ref_qualifiers_11 ;
obj cpp_alias_templates_11 : std/cpp_alias_templates_11.cpp ;
alias cpp_alias_templates : cpp_alias_templates_11 ;
obj cpp_rtti_03 : std/cpp_rtti_03.cpp ;
alias cpp_rtti : cpp_rtti_03 ;
obj cpp_exceptions_03 : std/cpp_exceptions_03.cpp ;
alias cpp_exceptions : cpp_exceptions_03 ;

