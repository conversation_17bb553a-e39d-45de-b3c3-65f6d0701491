boost-cmake c2f9d310477a33734a24c277fd7062de3f66a52aa462954c392ea364152275d2
boost-config 94d5d122f9ebdf91e590c7784e001b446a6b97eef8209076f7d30e2c904830be
boost-core cd5e9603f3beaa44da04a286f84dc358866ff2f3cc265e53847d64d45c3a2a02
boost-detail 6ca550d8a12d125bdb8aec0a73027b001c78fd1c919670a144e8d5f2000024e4
boost-headers 1fa483b45b4ae788b6b025984193f1cf54839c8c418e21c4dc59e75cb634145d
boost-mpl c9666ab1cfff28308a577e0ac519fc28a9c3ea33dd6235ccb3714362e044abe2
boost-preprocessor b1c967b994e46e39b2e39304295567b86a25652e938cfdb3cfcf168983de2d51
boost-type-traits 386aa7b34165317f0982924bd0f0f324737e30bb1f68545f7554c85f3c1e7031
cmake 0
features core
portfile.cmake abb4a50cb27d1b0baaff1bf4ad314962691bb76fbc80a1af65176ba54874e87b
ports.cmake a9f8f745f9f4d225039f9810e79670f10f38c7455222865616ce430a06f75d1f
post_build_checks 2
triplet arm64-linux
triplet_abi 971960c7eb0461eeba2679a4edd056aa99288fbd95bb9a4e2989e5f4a78158ab-7300d6fc76582335b0d9fd6dc6a00f69d7ba728b9d6444ad2a9d42f054e1d991-1d43c82f51082f8427993d1dac28c7cb4e3749af
vcpkg.json d155dcebf5360fb64ed97b584effd67726c3bb1ce14a355c79278ea39b5710c3
vcpkg_from_git 96ed81968f76354c00096dd8cd4e63c6a235fa969334a11ab18d11c0c512ff58
vcpkg_from_github b743742296a114ea1b18ae99672e02f142c4eb2bef7f57d36c038bedbfb0502f
