# Copyright Rene Rivera 2015-2023
# Distributed under the Boost Software License, Version 1.0.
# (See accompanying file LICENSE_1_0.txt or copy at
# http://www.boost.org/LICENSE_1_0.txt)

exe predef_check_as_c : predef_check_as_c.c ;
exe predef_check_as_cpp : predef_check_as_cpp.cpp ;
exe predef_check_as_objc : predef_check_as_objc.m : <conditional>@objc ;
exe predef_check_as_objcpp : predef_check_as_objcpp.mm : <conditional>@objc ;

rule objc ( props * )
{
    if ! ( <target-os>darwin in $(props) )
    {
        return <build>no ;
    }
}
