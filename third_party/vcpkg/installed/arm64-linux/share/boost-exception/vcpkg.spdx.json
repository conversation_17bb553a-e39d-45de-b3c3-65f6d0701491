{"$schema": "https://raw.githubusercontent.com/spdx/spdx-spec/v2.2.1/schemas/spdx-schema.json", "spdxVersion": "SPDX-2.2", "dataLicense": "CC0-1.0", "SPDXID": "SPDXRef-DOCUMENT", "documentNamespace": "https://spdx.org/spdxdocs/boost-exception-arm64-linux-1.87.0-92c75b4a-5644-4ca7-8ce4-14d623e69a9d", "name": "boost-exception:arm64-linux@1.87.0 73c50785ef658ea8a7e9ba8a0e7884b0cb66bc70be880a835ec823307df20fa2", "creationInfo": {"creators": ["Tool: vcpkg-2025-04-07-b8b513ba8778c918cff49c3e837aae5999d5d2aa"], "created": "2025-06-10T09:06:28Z"}, "relationships": [{"spdxElementId": "SPDXRef-port", "relationshipType": "GENERATES", "relatedSpdxElement": "SPDXRef-binary"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-0"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-1"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-2"}, {"spdxElementId": "SPDXRef-binary", "relationshipType": "GENERATED_FROM", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-0", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-0", "relationshipType": "DEPENDENCY_MANIFEST_OF", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-1", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-2", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}], "packages": [{"name": "boost-exception", "SPDXID": "SPDXRef-port", "versionInfo": "1.87.0", "downloadLocation": "git+https://github.com/Microsoft/vcpkg#ports/boost-exception", "homepage": "https://www.boost.org/libs/exception", "licenseConcluded": "BSL-1.0", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "description": "Boost exception module", "comment": "This is the port (recipe) consumed by vcpkg."}, {"name": "boost-exception:arm64-linux", "SPDXID": "SPDXRef-binary", "versionInfo": "73c50785ef658ea8a7e9ba8a0e7884b0cb66bc70be880a835ec823307df20fa2", "downloadLocation": "NONE", "licenseConcluded": "BSL-1.0", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "comment": "This is a binary package built by vcpkg."}, {"SPDXID": "SPDXRef-resource-0", "name": "boostorg/exception", "downloadLocation": "git+https://github.com/boostorg/exception@boost-1.87.0", "licenseConcluded": "NOASSERTION", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "checksums": [{"algorithm": "SHA512", "checksumValue": "ea1c971da699001b4946f1336edd18fe5225b1d6ac9db50a7640fb39ec31b7596159c1769aa36e949dc887ec228150487718cb1249895dac9503a30e8240f448"}]}], "files": [{"fileName": "./vcpkg.json", "SPDXID": "SPDXRef-file-0", "checksums": [{"algorithm": "SHA256", "checksumValue": "fd480f0f4399226e167b3f178222c696691f74cbb8d9cf0296fa1575b29a47e9"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./portfile.cmake", "SPDXID": "SPDXRef-file-1", "checksums": [{"algorithm": "SHA256", "checksumValue": "d44e1652a8d2310b8ac239ccc2d84c522910f00379d8493f73d709726700344f"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./features.cmake", "SPDXID": "SPDXRef-file-2", "checksums": [{"algorithm": "SHA256", "checksumValue": "2f1215b74121b1b8ed4b0ff081fc28e794471e51e21b75920e22a71bfd72a14d"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}]}