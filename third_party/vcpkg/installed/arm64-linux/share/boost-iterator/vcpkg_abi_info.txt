boost-assert 3245b8cef1975eb17584e363015c24184970f16eac38da3efcf0e50047d59de8
boost-cmake c2f9d310477a33734a24c277fd7062de3f66a52aa462954c392ea364152275d2
boost-concept-check c638ebb4b2b8845354840a54c5bf4d14a08ed590862ff49ba2a32a04de960be1
boost-config 94d5d122f9ebdf91e590c7784e001b446a6b97eef8209076f7d30e2c904830be
boost-core cd5e9603f3beaa44da04a286f84dc358866ff2f3cc265e53847d64d45c3a2a02
boost-detail 6ca550d8a12d125bdb8aec0a73027b001c78fd1c919670a144e8d5f2000024e4
boost-function-types e3048718f186bb57f9c4e68cf4f9a981b0b924900e2b8e03c081d2be67a2bec4
boost-fusion 0ef92caf700f2ce0db1c6e25654e0ede8cda2f586d3192522deaa07ec4dfdd32
boost-headers 1fa483b45b4ae788b6b025984193f1cf54839c8c418e21c4dc59e75cb634145d
boost-mpl c9666ab1cfff28308a577e0ac519fc28a9c3ea33dd6235ccb3714362e044abe2
boost-optional 112feb128c38e6427114d329a91a0f70924af120e634c4726410cf75c24e14f1
boost-smart-ptr 239a9c88250ca040432a86d51397371ec9b3b2985d97fe4eb64e978516bf6bf7
boost-static-assert 5bdacd62569fd4dc1abae15ad00008ce1ea70dd190892dc68903f99e97822243
boost-type-traits 386aa7b34165317f0982924bd0f0f324737e30bb1f68545f7554c85f3c1e7031
boost-utility b8e8716d390c71985d43b190bdfcdb3811c216df8a42f14170aec1557b78f6dc
cmake 0
features core
portfile.cmake 8835a9bbd923388e898e6f5a20460fdf144dbe96e177017354d4201a5d5cd442
ports.cmake a9f8f745f9f4d225039f9810e79670f10f38c7455222865616ce430a06f75d1f
post_build_checks 2
triplet arm64-linux
triplet_abi 971960c7eb0461eeba2679a4edd056aa99288fbd95bb9a4e2989e5f4a78158ab-7300d6fc76582335b0d9fd6dc6a00f69d7ba728b9d6444ad2a9d42f054e1d991-1d43c82f51082f8427993d1dac28c7cb4e3749af
vcpkg.json 3d1a23730e726dfcf22d49854b1a81ba8594d1b81fb6bca69cff4167be216c68
vcpkg_from_git 96ed81968f76354c00096dd8cd4e63c6a235fa969334a11ab18d11c0c512ff58
vcpkg_from_github b743742296a114ea1b18ae99672e02f142c4eb2bef7f57d36c038bedbfb0502f
