# Generated by BoostInstall.cmake for boost_describe-1.87.0

if(Boost_VERBOSE OR Boost_DEBUG)
  message(STATUS "Found boost_describe ${boost_describe_VERSION} at ${boost_describe_DIR}")
endif()

include(CMakeFindDependencyMacro)

if(NOT boost_mp11_FOUND)
  find_dependency(boost_mp11 1.87.0 EXACT HINTS "${CMAKE_CURRENT_LIST_DIR}/..")
endif()

include("${CMAKE_CURRENT_LIST_DIR}/boost_describe-targets.cmake")
