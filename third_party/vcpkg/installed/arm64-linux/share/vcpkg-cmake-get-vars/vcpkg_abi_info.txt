cmake 0
cmake_get_vars/CMakeLists.txt 3fda8c6435ffe8cb481ac9eda466cc29e3843d0a5e0b11c8158842ea25580225
features core
portfile.cmake bbcf9d6b88dd539617f4cfc1f76acbbe9a6e31fc373d79c1c626278768ea2e2a
ports.cmake a9f8f745f9f4d225039f9810e79670f10f38c7455222865616ce430a06f75d1f
post_build_checks 2
triplet arm64-linux
triplet_abi 971960c7eb0461eeba2679a4edd056aa99288fbd95bb9a4e2989e5f4a78158ab-7300d6fc76582335b0d9fd6dc6a00f69d7ba728b9d6444ad2a9d42f054e1d991-1d43c82f51082f8427993d1dac28c7cb4e3749af
vcpkg-cmake 54c8ef002d40e1119b296ccd854f951f842d35332e8f78ef641b5f85bd6e84a5
vcpkg-port-config.cmake 4fd4c2e909bbdf069eb3c59b4b847b0b386cdb41840714e12b34b7eff41f9e22
vcpkg.json f69b04b13a19ccd0124aebafe180915143d96b73df98163cb7bd2f9a800a03db
vcpkg_cmake_get_vars.cmake da894e0dafa6ef0acdc641f12b02633f273322d86a6d2e7065dc56134478cea3
