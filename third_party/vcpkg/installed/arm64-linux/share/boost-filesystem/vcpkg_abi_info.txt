boost-assert 3245b8cef1975eb17584e363015c24184970f16eac38da3efcf0e50047d59de8
boost-atomic e46215aaab5c568f0f55c8be4400aa6f99440736b44234fb6de94aa58463283e
boost-cmake c2f9d310477a33734a24c277fd7062de3f66a52aa462954c392ea364152275d2
boost-config 94d5d122f9ebdf91e590c7784e001b446a6b97eef8209076f7d30e2c904830be
boost-container-hash b49f85a045c962edb311991c82138b5adfaa0bb9fe4072357a4844a1b4f61ff4
boost-core cd5e9603f3beaa44da04a286f84dc358866ff2f3cc265e53847d64d45c3a2a02
boost-detail 6ca550d8a12d125bdb8aec0a73027b001c78fd1c919670a144e8d5f2000024e4
boost-headers 1fa483b45b4ae788b6b025984193f1cf54839c8c418e21c4dc59e75cb634145d
boost-io bf0ba4dadf5887d63e172c2970c816c018dad41b70333583426c1a940b468635
boost-iterator 43bd8040404df676ed3e9abff616ea579f2eb315a776b92b96da92bca0d7b167
boost-predef c33e3bcdb00644c3cdc338715c31093dcb08d874229c60ebd6d87550d2929bfa
boost-scope 08c7da97d383ecddd33a38105ada5f6ff5afb31f6352a7cb6572c0e53840930e
boost-smart-ptr 239a9c88250ca040432a86d51397371ec9b3b2985d97fe4eb64e978516bf6bf7
boost-system 54b74ab3a91c3c11e32264b118f8024efdd3020713e5f522ea48501342e2884d
boost-type-traits 386aa7b34165317f0982924bd0f0f324737e30bb1f68545f7554c85f3c1e7031
boost-winapi 4c591a1c3f14929e8cfe1c7fcc4b8e9c9d0c07fb119faff1e0f848b40b0fd51c
cmake 0
features core
portfile.cmake 0c57dc99d7cf97c633e64faa3bcd10d40a194d70356a769cf223888087852fe5
ports.cmake a9f8f745f9f4d225039f9810e79670f10f38c7455222865616ce430a06f75d1f
post_build_checks 2
triplet arm64-linux
triplet_abi 971960c7eb0461eeba2679a4edd056aa99288fbd95bb9a4e2989e5f4a78158ab-7300d6fc76582335b0d9fd6dc6a00f69d7ba728b9d6444ad2a9d42f054e1d991-1d43c82f51082f8427993d1dac28c7cb4e3749af
vcpkg.json b6c13cd136b8c63bbfe211f06e40fd5889ccc95f0d63d1ad9fb3e79bb23ac1ba
vcpkg_from_git 96ed81968f76354c00096dd8cd4e63c6a235fa969334a11ab18d11c0c512ff58
vcpkg_from_github b743742296a114ea1b18ae99672e02f142c4eb2bef7f57d36c038bedbfb0502f
