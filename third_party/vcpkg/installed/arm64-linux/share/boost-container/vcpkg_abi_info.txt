boost-assert 3245b8cef1975eb17584e363015c24184970f16eac38da3efcf0e50047d59de8
boost-cmake c2f9d310477a33734a24c277fd7062de3f66a52aa462954c392ea364152275d2
boost-config 94d5d122f9ebdf91e590c7784e001b446a6b97eef8209076f7d30e2c904830be
boost-headers 1fa483b45b4ae788b6b025984193f1cf54839c8c418e21c4dc59e75cb634145d
boost-intrusive 1d87a711ba37f673127a895d165a059ea24be27cd6e7f0dc23bc2de47c94a446
boost-move 8c1e6d515be042166f10a1535f02ea14ae8537bbda4a9fff5c5a1afc10bfaccb
cmake 0
features core
portfile.cmake b190b69b5fe70a01f11ec25db395fa9d8891582efb751d28fcb6997f638b4191
ports.cmake a9f8f745f9f4d225039f9810e79670f10f38c7455222865616ce430a06f75d1f
posix-threads.diff 94aff70844c47aed2a5460b63d92527e6557780beea19037ee949f9a8d3a42a5
post_build_checks 2
triplet arm64-linux
triplet_abi 971960c7eb0461eeba2679a4edd056aa99288fbd95bb9a4e2989e5f4a78158ab-7300d6fc76582335b0d9fd6dc6a00f69d7ba728b9d6444ad2a9d42f054e1d991-1d43c82f51082f8427993d1dac28c7cb4e3749af
vcpkg.json 5d283f46ee0665852921eef4fac0a4fb72ba8337ddfca1f74da4637ebe138fd1
vcpkg_from_git 96ed81968f76354c00096dd8cd4e63c6a235fa969334a11ab18d11c0c512ff58
vcpkg_from_github b743742296a114ea1b18ae99672e02f142c4eb2bef7f57d36c038bedbfb0502f
