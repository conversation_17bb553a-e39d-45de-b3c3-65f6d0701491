<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">

  <!-- Import default properties if not done yet. This does not overwrite any previously defined properties. -->
  <Import Condition="'$(VcpkgPropsImported)' != 'true'" Project="vcpkg.props" />

  <!-- VS2015's version of "vcpkg integrate install" imports both the props and targets together in the "props" area,
  meaning we have no opportunity to respond to user customizations in their project files. It also means that this
  .targets must defend against normal properties being unset. (For example, VcpkgPlatformTarget below.)

  Also, we copy all initial values to internal values to avoid properties being inconsistently evaluated in targets
  and dependent properties.
  -->

  <PropertyGroup>
    <_ZVcpkgRoot>$(VcpkgRoot)</_ZVcpkgRoot>
    <_ZVcpkgManifestRoot>$(VcpkgManifestRoot)</_ZVcpkgManifestRoot>
    <_ZVcpkgInstalledDir>$(VcpkgInstalledDir)</_ZVcpkgInstalledDir>
  </PropertyGroup>

  <!-- Add trailing slashes to inputs that must have them to conform with msbuild conventions. -->
  <PropertyGroup>
    <_ZVcpkgRoot Condition="!$(_ZVcpkgRoot.EndsWith('\'))">$(_ZVcpkgRoot)\</_ZVcpkgRoot>
    <_ZVcpkgManifestRoot Condition="'$(_ZVcpkgManifestRoot)' != '' and !$(_ZVcpkgManifestRoot.EndsWith('\'))">$(_ZVcpkgManifestRoot)\</_ZVcpkgManifestRoot>
    <_ZVcpkgInstalledDir Condition="'$(_ZVcpkgInstalledDir)' != '' and !$(_ZVcpkgInstalledDir.EndsWith('\'))">$(_ZVcpkgInstalledDir)\</_ZVcpkgInstalledDir>
  </PropertyGroup>

  <PropertyGroup>
    <_ZVcpkgClassicOrManifest Condition="'$(VcpkgEnabled)' == 'true' And ('$(VcpkgEnableClassic)' == 'true' Or '$(VcpkgEnableManifest)' == 'true')">true</_ZVcpkgClassicOrManifest>
    <_ZVcpkgClassicOrManifest Condition="'$(_ZVcpkgClassicOrManifest)' == ''">false</_ZVcpkgClassicOrManifest>
  </PropertyGroup>

  <!-- Special-case custom MSBuild platforms defined in the Microsoft GDK. See https://aka.ms/gdk and https://aka.ms/gdkx -->
  <PropertyGroup Condition="'$(VcpkgOSTarget)' == '' AND '$(VcpkgPlatformTarget)' == '' AND '$(Platform)'=='Gaming.Desktop.x64'">
    <VcpkgOSTarget>windows</VcpkgOSTarget>
    <VcpkgPlatformTarget>x64</VcpkgPlatformTarget>
    <VcpkgUseMD>true</VcpkgUseMD>
  </PropertyGroup>
  <PropertyGroup Condition="'$(VcpkgOSTarget)' == '' AND '$(VcpkgPlatformTarget)' == '' AND '$(Platform)'=='Gaming.Xbox.Scarlett.x64'">
    <VcpkgOSTarget>xbox-scarlett</VcpkgOSTarget>
    <VcpkgPlatformTarget>x64</VcpkgPlatformTarget>
    <VcpkgUseMD>false</VcpkgUseMD>
  </PropertyGroup>
  <PropertyGroup Condition="'$(VcpkgOSTarget)' == '' AND '$(VcpkgPlatformTarget)' == '' AND '$(Platform)'=='Gaming.Xbox.XboxOne.x64'">
    <VcpkgOSTarget>xbox-xboxone</VcpkgOSTarget>
    <VcpkgPlatformTarget>x64</VcpkgPlatformTarget>
    <VcpkgUseMD>false</VcpkgUseMD>
  </PropertyGroup>

  <!-- Determine the triplet to use. Note that $(PlatformTarget) is not available at the top of the .vcxproj file. -->
  <PropertyGroup Condition="'$(VcpkgOSTarget)' == ''">
    <VcpkgOSTarget>windows</VcpkgOSTarget>
    <VcpkgOSTarget Condition="'$(AppContainerApplication)' == 'true'">uwp</VcpkgOSTarget>
  </PropertyGroup>

  <PropertyGroup Condition="'$(VcpkgPlatformTarget)' == ''">
    <VcpkgPlatformTarget>$(Platform.ToLower())</VcpkgPlatformTarget>
    <VcpkgPlatformTarget Condition="'$(VcpkgPlatformTarget)' == 'win32'">x86</VcpkgPlatformTarget>
  </PropertyGroup>

  <PropertyGroup>
    <_ZVcpkgLinkage />
    <_ZVcpkgLinkage Condition="'$(VcpkgUseStatic)' == 'true'">-static</_ZVcpkgLinkage>
    <_ZVcpkgLinkageMD />
    <_ZVcpkgLinkageMD Condition="'$(VcpkgUseStatic)' == 'true' and '$(VcpkgUseMD)' == 'true'">-md</_ZVcpkgLinkageMD>
    <VcpkgTriplet Condition="'$(VcpkgTriplet)' == ''">$(VcpkgPlatformTarget)-$(VcpkgOSTarget)$(_ZVcpkgLinkage)$(_ZVcpkgLinkageMD)</VcpkgTriplet>
    <VcpkgTriplet Condition="!$(VcpkgTriplet.EndsWith($(_ZVcpkgLinkage)$(_ZVcpkgLinkageMD)))">$(VcpkgTriplet)$(_ZVcpkgLinkage)$(_ZVcpkgLinkageMD)</VcpkgTriplet>
  </PropertyGroup>

  <!-- Include the triplet in ProjectStateLine to force VS2017 and later to fully rebuild if the user changes it.  -->
  <PropertyGroup>
    <ProjectStateLine>VcpkgTriplet=$(VcpkgTriplet):$(ProjectStateLine)</ProjectStateLine>
  </PropertyGroup>

  <!-- Determine the locations trees we want to consume. _ZVcpkgInstalledDir is special in that it doesn't have a default
  value in the .props because we normally derive it, but users may override the value. -->
  <Choose>
    <When Condition="'$(VcpkgEnableManifest)' == 'true'">
      <PropertyGroup>
        <_ZVcpkgInstalledDir Condition="'$(_ZVcpkgInstalledDir)' == ''">$(_ZVcpkgManifestRoot)vcpkg_installed\$(VcpkgTriplet)\</_ZVcpkgInstalledDir>
      </PropertyGroup>
    </When>
    <Otherwise>
      <PropertyGroup>
        <_ZVcpkgInstalledDir Condition="'$(_ZVcpkgInstalledDir)' == ''">$(_ZVcpkgRoot)installed\</_ZVcpkgInstalledDir>
      </PropertyGroup>
    </Otherwise>
  </Choose>

  <PropertyGroup>
    <_ZVcpkgCurrentInstalledDir>$(_ZVcpkgInstalledDir)$(VcpkgTriplet)\</_ZVcpkgCurrentInstalledDir>
    <_ZVcpkgNormalizedConfiguration Condition="$(VcpkgConfiguration.StartsWith('Debug'))">Debug</_ZVcpkgNormalizedConfiguration>
    <_ZVcpkgNormalizedConfiguration Condition="$(VcpkgConfiguration.StartsWith('Release')) or '$(VcpkgConfiguration)' == 'RelWithDebInfo' or '$(VcpkgConfiguration)' == 'MinSizeRel'">Release</_ZVcpkgNormalizedConfiguration>

    <_ZVcpkgConfigSubdir Condition="'$(_ZVcpkgNormalizedConfiguration)' == 'Debug'">debug\</_ZVcpkgConfigSubdir>
    <_ZVcpkgExecutable>$(_ZVcpkgRoot)vcpkg.exe</_ZVcpkgExecutable>
    <ExternalIncludePath Condition="'$(_ZVcpkgClassicOrManifest)' == 'true'">$(ExternalIncludePath);$(_ZVcpkgCurrentInstalledDir)include</ExternalIncludePath>
  </PropertyGroup>

  <PropertyGroup>
    <!-- Note: Overwrite VcpkgPageSchema with a non-existing path to disable the VcPkg property sheet in your projects -->
    <VcpkgPageSchema Condition="'$(VcpkgPageSchema)' == ''">$(_ZVcpkgRoot)scripts\buildsystems\msbuild\vcpkg-general.xml</VcpkgPageSchema>
  </PropertyGroup>

  <ItemGroup Condition="'$(VcpkgPageSchema)' != '' and exists('$(VcpkgPageSchema)') and '$(MSBuildToolsVersion)' != '14.0'">
    <PropertyPageSchema Include="$(VcpkgPageSchema)">
      <Context>Project</Context>
    </PropertyPageSchema>
  </ItemGroup>

  <!-- Install settings to get headers and import libs for the currently selected _ZVcpkgCurrentInstalledDir -->
  <ItemDefinitionGroup Condition="'$(_ZVcpkgClassicOrManifest)' == 'true'">
    <Lib>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories);$(_ZVcpkgCurrentInstalledDir)$(_ZVcpkgConfigSubdir)lib;$(_ZVcpkgCurrentInstalledDir)$(_ZVcpkgConfigSubdir)lib\manual-link</AdditionalLibraryDirectories>
    </Lib>
    <Link>
      <AdditionalDependencies Condition="'$(VcpkgAutoLink)' != 'false'">%(AdditionalDependencies);$(_ZVcpkgCurrentInstalledDir)$(_ZVcpkgConfigSubdir)lib\*.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories);$(_ZVcpkgCurrentInstalledDir)$(_ZVcpkgConfigSubdir)lib;$(_ZVcpkgCurrentInstalledDir)$(_ZVcpkgConfigSubdir)lib\manual-link</AdditionalLibraryDirectories>
    </Link>
    <ClCompile>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories);$(_ZVcpkgCurrentInstalledDir)include</AdditionalIncludeDirectories>
    </ClCompile>
    <ResourceCompile>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories);$(_ZVcpkgCurrentInstalledDir)include</AdditionalIncludeDirectories>
    </ResourceCompile>
  </ItemDefinitionGroup>

  <Target Name="VcpkgCheckManifestRoot" BeforeTargets="VcpkgInstallManifestDependencies" Condition="'$(VcpkgEnabled)' == 'true'">
    <Error Text="The vcpkg manifest was enabled, but we couldn't find a manifest file (vcpkg.json) in any directories above $(MSBuildProjectDirectory). Please add a manifest, disable manifests in your properties page, or pass /p:VcpkgEnableManifest=false."
           Condition="'$(VcpkgEnableManifest)' == 'true' and '$(_ZVcpkgManifestRoot)' == ''" />
    <Message Text="The vcpkg manifest was disabled, but we found a manifest file in $(_ZVcpkgManifestRoot). You may want to enable vcpkg manifests in your properties page or pass /p:VcpkgEnableManifest=true to the msbuild invocation."
             Importance="High" Condition="'$(VcpkgEnableManifest)' != 'true' and '$(_ZVcpkgManifestRoot)' != ''" />
  </Target>

  <Target Name="VcpkgTripletSelection" BeforeTargets="ClCompile" Condition="'$(_ZVcpkgClassicOrManifest)' == 'true'">
    <Message Text="Using triplet &quot;$(VcpkgTriplet)&quot; from &quot;$(_ZVcpkgCurrentInstalledDir)&quot;"
             Importance="Normal" Condition="'$(VcpkgEnabled)' == 'true'"/>
    <Message Text="Using normalized configuration &quot;$(_ZVcpkgNormalizedConfiguration)&quot;"
             Importance="Normal" Condition="'$(VcpkgEnabled)' == 'true'"/>
    <Message Text="Not using Vcpkg because VcpkgEnabled is &quot;$(VcpkgEnabled)&quot;"
             Importance="Normal" Condition="'$(VcpkgEnabled)' != 'true'"/>
    <Message Text="Vcpkg is unable to link because we cannot decide between Release and Debug libraries. Please define the property VcpkgConfiguration to be 'Release' or 'Debug' (currently '$(VcpkgConfiguration)')."
             Importance="High" Condition="'$(VcpkgEnabled)' == 'true' and '$(_ZVcpkgNormalizedConfiguration)' == ''"/>
  </Target>

  <Choose>
    <When Condition="'$(VcpkgHostTriplet)' != ''">
      <PropertyGroup>
        <_ZVcpkgHostTripletParameter>"--host-triplet=$(VcpkgHostTriplet)"</_ZVcpkgHostTripletParameter>
        <_ZVcpkgHostTripletSuffix>$(VcpkgHostTriplet).</_ZVcpkgHostTripletSuffix>
      </PropertyGroup>
    </When>
    <Otherwise>
      <PropertyGroup>
        <_ZVcpkgHostTripletParameter />
        <_ZVcpkgHostTripletSuffix />
      </PropertyGroup>
    </Otherwise>
  </Choose>

  <PropertyGroup>
    <_ZVcpkgManifestFileLocation>$(_ZVcpkgManifestRoot)vcpkg.json</_ZVcpkgManifestFileLocation>
    <_ZVcpkgConfigurationFileLocation>$(_ZVcpkgManifestRoot)vcpkg-configuration.json</_ZVcpkgConfigurationFileLocation>
    <_ZVcpkgMSBuildStampFile>$(_ZVcpkgInstalledDir).msbuildstamp-$(VcpkgTriplet).$(_ZVcpkgHostTripletSuffix)stamp</_ZVcpkgMSBuildStampFile>
  </PropertyGroup>

  <ItemGroup Condition="'$(VcpkgEnabled)' == 'true' and '$(VcpkgEnableManifest)' == 'true' and '$(VcpkgManifestInstall)' == 'true'">
    <_ZVcpkgInstallManifestDependenciesInputs Include="$(_ZVcpkgManifestFileLocation)"/>
    <_ZVcpkgInstallManifestDependenciesInputs Include="$(_ZVcpkgConfigurationFileLocation)" Condition="Exists('$(_ZVcpkgConfigurationFileLocation)')"/>
  </ItemGroup>

  <Target Name="VcpkgInstallManifestDependencies" BeforeTargets="ClCompile"
          Condition="'$(VcpkgEnabled)' == 'true' and '$(VcpkgEnableManifest)' == 'true' and '$(VcpkgManifestInstall)' == 'true'"
          Inputs="@(_ZVcpkgInstallManifestDependenciesInputs)"
          Outputs="$(_ZVcpkgMSBuildStampFile)">
    <!-- This is set inside the target because $(TLogLocation) may not be set yet when parsing the .targets on VS2015 -->
    <PropertyGroup>
      <_ZVcpkgTLogFileLocation>$(TLogLocation)VcpkgInstallManifest$(VcpkgTriplet).$(_ZVcpkgHostTripletSuffix)read.1u.tlog</_ZVcpkgTLogFileLocation>
    </PropertyGroup>
    <Message Text="Installing vcpkg dependencies to $(_ZVcpkgInstalledDir)" Importance="High" />
    <MakeDir Directories="$(_ZVcpkgInstalledDir)" />
    <Message Text="%22$(_ZVcpkgExecutable)%22 install $(_ZVcpkgHostTripletParameter) --x-wait-for-lock --triplet %22$(VcpkgTriplet)%22 --vcpkg-root %22$(_ZVcpkgRoot)\%22 %22--x-manifest-root=$(_ZVcpkgManifestRoot)\%22 %22--x-install-root=$(_ZVcpkgInstalledDir)\%22 $(VcpkgAdditionalInstallOptions)"
          Importance="High" />
    <Exec Command="%22$(_ZVcpkgExecutable)%22 install $(_ZVcpkgHostTripletParameter) --x-wait-for-lock --triplet %22$(VcpkgTriplet)%22 --vcpkg-root %22$(_ZVcpkgRoot)\%22 %22--x-manifest-root=$(_ZVcpkgManifestRoot)\%22 %22--x-install-root=$(_ZVcpkgInstalledDir)\%22 $(VcpkgAdditionalInstallOptions)"
          StandardOutputImportance="High"
          StandardErrorImportance="High"
          UseUtf8Encoding="Always"
          StdOutEncoding="utf-8"
          StdErrEncoding="utf-8"
          UseCommandProcessor="false"
          />
    <WriteLinesToFile File="$(_ZVcpkgTLogFileLocation)"
                      Lines="@(_ZVcpkgInstallManifestDependenciesInputs -> '^%(Identity)')"
                      Encoding="Unicode"
                      Overwrite="true"/>
    <Touch Files="$(_ZVcpkgMSBuildStampFile)" AlwaysCreate="true" />

    <CreateProperty Value="false">
      <Output TaskParameter="ValueSetByTask" PropertyName="Link_MinimalRebuildFromTracking" />
    </CreateProperty>
  </Target>

  <Target Name="AppLocalFromInstalled" AfterTargets="CopyFilesToOutputDirectory" BeforeTargets="CopyLocalFilesOutputGroup;RegisterOutput"
          Condition="'$(_ZVcpkgClassicOrManifest)' == 'true' and '$(VcpkgApplocalDeps)' == 'true' and '$(LinkSkippedExecution)' != 'true' and '@(Link)' != ''">
    <Message Text="[vcpkg] Starting VcpkgApplocalDeps" Importance="low" />
    <PropertyGroup>
      <_ZVcpkgApplocalInstalledBinDir>$(_ZVcpkgCurrentInstalledDir)$(_ZVcpkgConfigSubdir)bin</_ZVcpkgApplocalInstalledBinDir>
      <_ZVcpkgApplocalTLogPath>$(TLogLocation)$(ProjectName).write.1u.tlog</_ZVcpkgApplocalTLogPath>
      <_ZVcpkgApplocalCopiedFilesLogPath>$(IntDir)vcpkg.applocal.log</_ZVcpkgApplocalCopiedFilesLogPath>
      <_ZVcpkgApplocalBuiltinArguments>--target-binary="$(TargetPath)" --installed-bin-dir="$(_ZVcpkgApplocalInstalledBinDir)" --tlog-file="$(_ZVcpkgApplocalTLogPath)" --copied-files-log="$(_ZVcpkgApplocalCopiedFilesLogPath)"</_ZVcpkgApplocalBuiltinArguments>
      <_ZVcpkgAppLocalPowerShellCommonArguments>-ExecutionPolicy Bypass -noprofile -File "$(MSBuildThisFileDirectory)applocal.ps1" "$(TargetPath)" "$(_ZVcpkgApplocalInstalledBinDir)" "$(_ZVcpkgApplocalTLogPath)" "$(_ZVcpkgApplocalCopiedFilesLogPath)"</_ZVcpkgAppLocalPowerShellCommonArguments>
    </PropertyGroup>
    <Exec
      Condition="'$(VcpkgXUseBuiltInApplocalDeps)' == 'true'"
      Command="%22$(_ZVcpkgExecutable)%22 z-applocal $(_ZVcpkgApplocalBuiltinArguments)"
      UseUtf8Encoding="Always"
      StdOutEncoding="utf-8"
      StdErrEncoding="utf-8"
      UseCommandProcessor="false"
      IgnoreExitCode="true">
      <Output TaskParameter="ExitCode"
              PropertyName="_ZVcpkgAppLocalExitCode" />
    </Exec>
    <Warning Text="[vcpkg] Failed to gather app local DLL dependencies, program may not run. Set VcpkgApplocalDeps to false in your project file to suppress this warning. Builtin arguments: $(_ZVcpkgApplocalBuiltinArguments)"
      Condition="$(_ZVcpkgAppLocalExitCode) != 0 and '$(VcpkgXUseBuiltInApplocalDeps)' == 'true'"/>

    <!-- Search %PATH% for pwsh.exe if it is available. -->
    <Exec
      Condition="'$(VcpkgXUseBuiltInApplocalDeps)' != 'true'"
      Command="pwsh.exe $(_ZVcpkgAppLocalPowerShellCommonArguments)"
      IgnoreExitCode="true"
      UseCommandProcessor="false">
      <Output TaskParameter="ExitCode"
              PropertyName="_ZVcpkgAppLocalExitCode" />
    </Exec>
    <!-- Fall back to well known system PowerShell location otherwise. -->
    <Message Text="[vcpkg] Failed to run applocal.ps1 using pwsh, falling back to system PowerShell." Importance="low"
             Condition="$(_ZVcpkgAppLocalExitCode) == 9009" />
    <Exec
      Command="%22$(SystemRoot)\System32\WindowsPowerShell\v1.0\powershell.exe%22 $(_ZVcpkgAppLocalPowerShellCommonArguments)"
      IgnoreExitCode="true"
      UseCommandProcessor="false"
      Condition="$(_ZVcpkgAppLocalExitCode) == 9009 and '$(VcpkgXUseBuiltInApplocalDeps)' != 'true'">
      <Output TaskParameter="ExitCode"
              PropertyName="_ZVcpkgAppLocalExitCode" />
    </Exec>
    <!-- We're ignoring the above exit codes, so translate into a warning if both failed. -->
    <Warning Text="[vcpkg] Failed to gather app local DLL dependencies, program may not run. Set VcpkgApplocalDeps to false in your project file to suppress this warning. PowerShell arguments: $(_ZVcpkgAppLocalPowerShellCommonArguments)"
      Condition="$(_ZVcpkgAppLocalExitCode) != 0 and '$(VcpkgXUseBuiltInApplocalDeps)' != 'true'"/>

    <ReadLinesFromFile File="$(IntDir)vcpkg.applocal.log"
      Condition="$(_ZVcpkgAppLocalExitCode) == 0">
      <Output TaskParameter="Lines" ItemName="VcpkgAppLocalDLLs" />
    </ReadLinesFromFile>
    <Message Text="@(VcpkgAppLocalDLLs,'%0A')" Importance="Normal" Condition="$(_ZVcpkgAppLocalExitCode) == 0" />
    <ItemGroup Condition="$(_ZVcpkgAppLocalExitCode) == 0">
      <ReferenceCopyLocalPaths Include="@(VcpkgAppLocalDLLs)" />
    </ItemGroup>
  </Target>
</Project>
