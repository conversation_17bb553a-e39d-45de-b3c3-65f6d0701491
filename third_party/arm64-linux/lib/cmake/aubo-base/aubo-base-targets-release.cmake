#----------------------------------------------------------------
# Generated CMake target import file for configuration "Release".
#----------------------------------------------------------------

# Commands may need to know the format version.
set(CMAKE_IMPORT_FILE_VERSION 1)

# Import target "aubo::aubo-base" for configuration "Release"
set_property(TARGET aubo::aubo-base APPEND PROPERTY IMPORTED_CONFIGURATIONS RELEASE)
set_target_properties(aubo::aubo-base PROPERTIES
  IMPORTED_LOCATION_RELEASE "${_IMPORT_PREFIX}/lib/libaubo-base.so.3.2.0"
  IMPORTED_SONAME_RELEASE "libaubo-base.so.3.2.0"
  )

list(APPEND _cmake_import_check_targets aubo::aubo-base )
list(APPEND _cmake_import_check_files_for_aubo::aubo-base "${_IMPORT_PREFIX}/lib/libaubo-base.so.3.2.0" )

# Commands beyond this point should not need to know the version.
set(CMAKE_IMPORT_FILE_VERSION)
