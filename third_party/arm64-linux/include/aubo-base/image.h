//
// Copyright ©2015-2024 Aubo (Beijing) Robotics Technology Co., Ltd. All rights reserved.
//

#ifndef AUBO_IMAGE_H
#define AUBO_IMAGE_H

#include <fstream>
#include <functional>
#include <iterator>
#include <memory>
#include <stdexcept>
#include <vector>

#include <aubo-base/api.h>
#include <nlohmann/json.hpp>

namespace aubo {

/**
 * @brief 图像信息
 */
class AUBO_API Image {
public:
    /**
     * @brief 默认构造函数
     */
    Image() = default;

    /**
     * @brief 构造函数
     *
     * @param width 图像宽度
     * @param height 图像高度
     * @param data 图像信息(例如：RGB数据)
     * @param channels 图像通道数
     */
    Image(int width, int height, const std::shared_ptr<std::vector<uint8_t>> &data, int channels = 3)
            : width_(width), height_(height), image_data_(data) {
        if (!data) {
            throw std::invalid_argument("Image data is null");
        }

        channels_ = channels;

        if (width_ * height_ * channels_ != image_data_->size()) {
            throw std::invalid_argument("Invalid image dimensions");
        }
    }

    /**
     * @brief 构造函数
     *
     * @param width 图像宽度
     * @param height 图像高度
     * @param data 图像信息(例如：RGB数据)
     * @param channels 图像通道数
     */
    Image(int width, int height, uint8_t *data, int channels = 3) {
        if (!data) {
            throw std::invalid_argument("Image data is null");
        }

        width_ = width;
        height_ = height;
        channels_ = channels;

        image_data_ = std::make_shared<std::vector<uint8_t>>(data, data + channels_ * width * height);
    }

    /**
     * @brief 构造函数
     *
     * @param path 图像文件的路径
     */
    explicit Image(const std::string &path) {
        if (!load_from_file(path)) {
            throw std::invalid_argument("Failed to load image from file");
        }
    }

    /**
     * @brief 获取图像宽度
     *
     * @return 图像宽度
     */
    [[nodiscard]]
    int width() const { return width_; }

    /**
     * @brief 获取图像高度
     *
     * @return 图像高度
     */
    [[nodiscard]]
    int height() const { return height_; }

    /**
     * @brief 获取图像通道数
     *
     * @return 图像通道数
     */
    [[nodiscard]]
    int channels() const { return channels_; }

    /**
     * @brief 获取图像数据（RGB）
     *
     * @return 图像数据的共享指针
     */
    [[nodiscard]]
    std::shared_ptr<std::vector<uint8_t>> data() const { return image_data_; }

    /**
     * @brief 获取图像数据
     *
     * @param row 行索引
     * @param col 列索引
     * @return 图像数据
     */
    [[nodiscard]]
    uint8_t at(int row, int col) const {
        if (!check_index(row, col)) {
            throw std::out_of_range("Invalid index");
        }
        return image_data_->at(row * width_ + col);
    }

    /**
     * @brief 保存图像到文件
     *
     * @param path 文件路径
     * @return 成功与否
     */
    [[nodiscard]]
    bool save_to_file(const std::string &path) const;

    /**
     * @brief 异步保存图像到文件
     *
     * @param path 文件路径
     * @param callback 保存完成后的回调函数，参数为保存是否成功
     */
    void async_save_to_file(const std::string &path, std::function<void(bool)> callback = nullptr) const;

    /**
     * @brief 从文件加载图像
     *
     * @param path 文件路径
     * @return 成功与否
     */
    [[nodiscard]]
    bool load_from_file(const std::string &path);

    /**
     * @brief 获取图像的base64编码
     *
     * @return base64编码
     */
    [[nodiscard]]
    std::string base64_data() const;

    /**
     * @brief 设置图像的base64编码
     *
     * @param base64_data base64编码
     * @return 成功与否
     */
    [[nodiscard]]
    bool set_base64_data(const std::string &base64_data);

    /**
     * @brief 线性调整图像大小
     *
     * @param width 图片宽度
     * @param height 图片高度
     * @return 调整后的图像
     */
    [[nodiscard]]
    std::shared_ptr<Image> resize(int width, int height) const;

    /**
     * @brief 序列化
     *
     * @return 序列化后的json
     */
    [[nodiscard]]
    nlohmann::json to_json() const;

    /**
     * @brief 序列化
     *
     * @param j 序列化后的json
     */
    void to_json(nlohmann::json &j) const;

    /**
     * @brief 反序列化
     *
     * @param json 序列化后的json
     */
    void from_json(const nlohmann::json &json);

private:
    /// 校验索引是否合法
    [[nodiscard]]
    bool check_index(int row, int col) const {
        return row < height_ && col < width_;
    }

    int width_{0};      ///< 图像宽度
    int height_{0};     ///< 图像高度
    int channels_{3};   ///< 图像通道数

    /// 图像数据
    std::shared_ptr<std::vector<uint8_t>> image_data_;
};

} // namespace aubo

namespace nlohmann {

inline void to_json(nlohmann::json &j, const aubo::Image &image) {
    j = {{"width", image.width()},
         {"height", image.height()},
         {"channels", image.channels()},
         {"data", image.base64_data()}};
}

inline void from_json(const nlohmann::json &j, aubo::Image &image) {
    auto width = j.contains("width") && j["width"].is_number()
                 ? j["width"].get<int>()
                 : 0;
    auto height = j.contains("height") && j["height"].is_number()
                 ? j["height"].get<int>()
                 : 0;
    auto channels = j.contains("channels") && j["channels"].is_number()
                 ? j["channels"].get<int>()
                 : 3;
    auto data = j.contains("data") && j["data"].is_array()
                 ? j["data"].get<std::vector<uint8_t>>()
                 : std::vector<uint8_t>();
    image = aubo::Image(width, height, std::make_shared<std::vector<uint8_t>>(data));
}

} // namespace nlohmann

#endif // AUBO_IMAGE_H