//
// Copyright ©2015-2024 Aubo (Beijing) Robotics Technology Co., Ltd. All rights reserved.
//

#ifndef AUBO_MATH_H
#define AUBO_MATH_H

#include <cmath>

#include <aubo-base/api.h>
#include <aubo-base/point.h>
#include <aubo-base/pose.h>
#include <aubo-base/quaternion.h>
#include <aubo-base/vec.h>

namespace aubo {

/**
 * @brief 根据给定的位姿将一个点从一个坐标系转换到另一个坐标系。
 *
 * 这个函数使用位姿（包含旋转和平移信息）来转换一个点的坐标。
 * 位姿定义了从原始坐标系到目标坐标系的转换。
 *
 * @param position 待转换的点，表示在原始坐标系中的位置。
 * @param pose 位姿，描述了从原始坐标系到目标坐标系的旋转和平移。
 * @return Vec3d 转换后的点，表示在目标坐标系中的位置。
 */
AUBO_API Position transform_position(const Position &position,
                                        const Pose &pose);

} // namespace aubo

#endif //AUBO_MATH_H
