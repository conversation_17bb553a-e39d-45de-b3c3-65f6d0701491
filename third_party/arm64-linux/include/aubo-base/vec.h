//
// Copyright ©2015-2024 Aubo (Beijing) Robotics Technology Co., Ltd. All rights reserved.
//

#ifndef AUBO_VEC_H
#define AUBO_VEC_H

#include <array>
#include <cmath>
#include <stdexcept>
#include <string>

#include <aubo-base/api.h>
#include <aubo-base/log.h>
#include <nlohmann/json.hpp>

namespace aubo {

/**
 * @brief 通用向量类
 *
 * @tparam T 向量元素的类型
 * @tparam N 向量的维度
 */
template<typename T, size_t N,
         typename = std::enable_if_t<std::is_same<T, int>::value ||
                                     std::is_same<T, float>::value ||
                                     std::is_same<T, double>::value>>
class AUBO_API Vec {
public:

    /// 默认构造函数
    Vec() = default;

    /// 从std::array构造
    explicit Vec(const std::array<T, N>& arr) {
        std::copy(arr.begin(), arr.end(), data_.begin());
    }

    /// 可变参数构造函数
    template<typename... Args, typename = std::enable_if_t<(std::is_same<T, Args>::value && ...)>>
    explicit Vec(Args... args) {
        if (sizeof...(args) != N) {
            throw std::invalid_argument("The number of arguments is not equal to N.");
        }
        data_ = {{args...}};
    }

    /// 拷贝构造函数
    Vec(const Vec<T, N>& other) {
        data_ = other.data_;
    }

    /// 移动构造函数
    Vec(Vec<T, N>&& other) noexcept {
        data_ = std::move(other.data_);
    }

    /// 析构函数
    virtual ~Vec() = default;

    /// 向量叉乘（仅适用于三维向量）
    Vec<T, N> cross(const Vec<T, N> &other) const {
        static_assert(N == 3, "Cross product is only defined for 3D vectors");
        Vec<T, N> result;
        result[0] = data_[1] * other.data_[2] - data_[2] * other.data_[1];
        result[1] = data_[2] * other.data_[0] - data_[0] * other.data_[2];
        result[2] = data_[0] * other.data_[1] - data_[1] * other.data_[0];
        return result;
    }

    /// 重载[]运算符，用于访问向量元素
    T &operator[](size_t index) {
        check_index(index);
        return data_[index];
    }

    /// 重载[]运算符，用于访问向量元素
    const T &operator[](size_t index) const {
        check_index(index);
        return data_[index];
    }

    /// 拷贝赋值运算符
    Vec<T, N> &operator=(const Vec<T, N> &other) {
        if (this != &other) {
            data_ = other.data_;
        }
        return *this;
    }

    /// 移动赋值运算符
    Vec<T, N> &operator=(Vec<T, N> &&other) noexcept {
        if (this != &other) {
            data_ = std::move(other.data_);
        }
        return *this;
    }

    /// 重载!=运算符，用于判断两个向量是否不相等
    bool operator!=(const Vec<T, N> &other) const {
        const T kEpsilon = 1e-4;  // 定义一个小的容差值
        for (size_t i = 0; i < N; ++i) {
            if (std::fabs(data_[i] - other.data_[i]) > kEpsilon) {
                return true;
            }
        }
        return false;
    }

    /// 重载==运算符，用于判断两个向量是否相等
    bool operator==(const Vec<T, N> &other) const {
        return !(*this != other);
    }

    /// 重载+运算符，用于向量加法
    Vec<T, N> operator+(const Vec<T, N> &other) const {
        Vec<T, N> result;
        for (size_t i = 0; i < N; ++i) {
            result[i] = data_[i] + other.data_[i];
        }
        return result;
    }

    /// 重载-运算符，用于向量减法
    Vec<T, N> operator-(const Vec<T, N> &other) const {
        Vec<T, N> result;
        for (size_t i = 0; i < N; ++i) {
            result[i] = data_[i] - other.data_[i];
        }
        return result;
    }

    /// 重载*运算符，用于向量数乘
    Vec<T, N> operator*(T scalar) const {
        Vec<T, N> result;
        for (size_t i = 0; i < N; ++i) {
            result[i] = data_[i] * scalar;
        }
        return result;
    }

    /// 标量左乘向量运算符（友元函数）
    template<typename U>
    friend Vec<T, N> operator*(U scalar, const Vec<T, N>& vec) {
        return vec * scalar;
    }

    /// 重载/运算符，用于向量数除
    Vec<T, N> operator/(T scalar) const {
        Vec<T, N> result;
        for (size_t i = 0; i < N; ++i) {
            result[i] = data_[i] / scalar;
        }
        return result;
    }

    /// 重载+=运算符，用于向量加法
    Vec<T, N> &operator+=(const Vec<T, N> &other) {
        for (size_t i = 0; i < N; ++i) {
            data_[i] += other.data_[i];
        }
        return *this;
    }

    /// 重载-=运算符，用于向量减法
    Vec<T, N> &operator-=(const Vec<T, N> &other) {
        for (size_t i = 0; i < N; ++i) {
            data_[i] -= other.data_[i];
        }
        return *this;
    }

    /// 重载*=运算符，用于向量数乘
    Vec<T, N> &operator*=(T scalar) {
        for (size_t i = 0; i < N; ++i) {
            data_[i] *= scalar;
        }
        return *this;
    }

    /// 重载/=运算符，用于向量数除
    Vec<T, N> &operator/=(T scalar) {
        for (size_t i = 0; i < N; ++i) {
            data_[i] /= scalar;
        }
        return *this;
    }

    /// 重载*运算符，用于向量点乘
    T operator*(const Vec<T, N> &other) const {
        T result = 0;
        for (size_t i = 0; i < N; ++i) {
            result += data_[i] * other.data_[i];
        }
        return result;
    }

    /// 计算向量的平方范数
    T norm_squared() const {
        T result = 0;
        for (size_t i = 0; i < N; ++i) {
            result += data_[i] * data_[i];
        }
        return result;
    }

    /// 计算向量的范数
    T norm() const {
        return std::sqrt(norm_squared());
    }

    /// 返回归一化的向量
    Vec<T, N> normalized() const {
        T n = norm();
        if (n < 1e-10) {
            return *this;  // 避免除以接近零的数
        }
        return *this / n;
    }

    /// 将当前向量归一化
    Vec<T, N>& normalize() {
        T n = norm();
        if (n < 1e-10) {
            return *this;  // 避免除以接近零的数
        }
        *this /= n;
        return *this;
    }

    /// 取反向量
    Vec<T, N> operator-() const {
        Vec<T, N> result;
        for (size_t i = 0; i < N; ++i) {
            result[i] = -data_[i];
        }
        return result;
    }

    /// 获取 x 分量（仅适用于 N >= 1）
    T x() const {
        static_assert(N >= 1, "Vector dimension must be at least 1");
        return data_[0];
    }

    /// 获取 y 分量（仅适用于 N >= 2）
    T y() const {
        static_assert(N >= 2, "Vector dimension must be at least 2");
        return data_[1];
    }

    /// 获取 z 分量（仅适用于 N >= 3）
    T z() const {
        static_assert(N >= 3, "Vector dimension must be at least 3");
        return data_[2];
    }

    /// 设置 x 分量（仅适用于 N >= 1）
    void set_x(T value) {
        static_assert(N >= 1, "Vector dimension must be at least 1");
        data_[0] = value;
    }

    /// 设置 y 分量（仅适用于 N >= 2）
    void set_y(T value) {
        static_assert(N >= 2, "Vector dimension must be at least 2");
        data_[1] = value;
    }

    /// 设置 z 分量（仅适用于 N >= 3）
    void set_z(T value) {
        static_assert(N >= 3, "Vector dimension must be at least 3");
        data_[2] = value;
    }

    /// 获取底层数据的指针
    const T* data() const {
        return data_.data();
    }

    /// 获取底层数据的指针
    T* data() {
        return data_.data();
    }

    /// 获取向量的维度
    constexpr size_t size() const {
        return N;
    }

    /// 创建全零向量
    static Vec<T, N> zero() {
        Vec<T, N> result;
        for (size_t i = 0; i < N; ++i) {
            result[i] = 0;
        }
        return result;
    }

    /// 创建全一向量
    static Vec<T, N> ones() {
        Vec<T, N> result;
        for (size_t i = 0; i < N; ++i) {
            result[i] = 1;
        }
        return result;
    }

    /// 创建单位向量（指定轴上的值为1，其余为0）
    static Vec<T, N> unit(size_t axis) {
        if (axis >= N) {
            throw std::out_of_range("Axis index out of range");
        }
        Vec<T, N> result;
        for (size_t i = 0; i < N; ++i) {
            result[i] = (i == axis) ? 1 : 0;
        }
        return result;
    }

    /// 转换为JSON
    nlohmann::json to_json() const {
        try {
            nlohmann::json j;
            to_json(j);
            return j;
        } catch (const std::exception& e) {
            LOG_ERROR("[Vec] 转换为JSON时发生错误: {}", e.what());
            return nlohmann::json();
        }
    }

    /// 转换为JSON
    void to_json(nlohmann::json& j) const {
        try {
            nlohmann::json array = nlohmann::json::array();
            for (const auto& value : data_) {
                array.push_back(value);
            }
            j = array;
        } catch (const std::exception& e) {
            LOG_ERROR("[Vec] 转换为JSON时发生错误: {}", e.what());
        }
    }

    /// 从JSON解析
    void from_json(const nlohmann::json& j) {
        try {
            if (j.is_array() && j.size() == N) {
                for (size_t i = 0; i < N; ++i) {
                    data_[i] = j[i].get<T>();
                }
            }
        } catch (const std::exception& e) {
            LOG_ERROR("[Vec] 从JSON解析时发生错误: {}", e.what());
        }
    }

private:
    /// 检查索引是否越界
    void check_index(size_t index) const {
        if (index >= N) {
            throw std::out_of_range("Index out of range.");
        }
    }

protected:
    std::array<T, N> data_; ///< 向量的数据
};

/// 使用double类型的三维向量
using Vec3d = Vec<double, 3>;

/// 使用float类型的三维向量
using Vec3f = Vec<float, 3>;

/// 使用double类型的二维向量
using Vec2d = Vec<double, 2>;

/// 使用float类型的二维向量
using Vec2f = Vec<float, 2>;

} // namespace aubo

namespace nlohmann {

inline void to_json(nlohmann::json& j, const aubo::Vec3d& v) {
    j = v.to_json();
}

inline void from_json(const nlohmann::json& j, aubo::Vec3d& v) {
    v.from_json(j);
}

inline void to_json(nlohmann::json& j, const aubo::Vec3f& v) {
    j = v.to_json();
}

inline void from_json(const nlohmann::json& j, aubo::Vec3f& v) {
    v.from_json(j);
}

inline void to_json(nlohmann::json& j, const aubo::Vec2d& v) {
    j = v.to_json();
}

inline void from_json(const nlohmann::json& j, aubo::Vec2d& v) {
    v.from_json(j);
}

inline void to_json(nlohmann::json& j, const aubo::Vec2f& v) {
    j = v.to_json();
}

inline void from_json(const nlohmann::json& j, aubo::Vec2f& v) {
    v.from_json(j);
}

} // namespace nlohmann

#endif //AUBO_VEC_H
