//
// Copyright ©2015-2024 Aubo (Beijing) Robotics Technology Co., Ltd. All rights reserved.
//

#ifndef AUBO_UTILS_H
#define AUBO_UTILS_H

#include <iostream>
#include <chrono>
#include <sstream>

#include <aubo-base/api.h>

namespace aubo {

/**
 * @brief 工具类
 */
class AUBO_API Utils {
public:
    /// 获取保存文件的路径
    static std::string data_path(const std::string &sub_dir);
};

} // namespace aubo

#endif // AUBO_UTILS_H
