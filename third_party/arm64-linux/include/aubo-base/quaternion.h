//
// Copyright ©2015-2024 Aubo (Beijing) Robotics Technology Co., Ltd. All rights reserved.
//

#ifndef AUBO_QUATERNION_H
#define AUBO_QUATERNION_H

#include <cmath>
#include <string>

#include <nlohmann/json.hpp>

#include <aubo-base/api.h>
#include <aubo-base/euler_angles.h>
#include <aubo-base/vec.h>

namespace aubo {

/**
 * @brief 四元数结构体。
 *
 * 四元数（Quaternion）是一种用于表示三维空间中的旋转的数学工具。
 * 它由四个分量组成：w、x、y 和 z，其中w代表旋转的标量分量，x、y、z分别代表旋转轴上的三个分量。
 * 四元数通常用于机器人学、计算机图形和物理引擎等领域，以更有效地表示和处理旋转。
 *
 * 四元数要表示三维旋转，其各分量需满足条件：w^2 + x^2 + y^2 + z^2 = 1，即：它是一个单位四元数。
 * 四元数 (w, x, y, z) 和 (-w, -x, -y, -z) 表示相同的旋转，方向相反。
 */
class AUBO_API Quaternion {
public:
    double w; ///< 旋转的标量分量。
    double x; ///< X轴上的旋转向量分量。
    double y; ///< Y轴上的旋转向量分量。
    double z; ///< Z轴上的旋转向量分量。

    /**
     * @brief 默认构造函数。
     *
     * 默认构造函数创建一个Quaternion结构体实例，并将四元数初始化为单位四元数。
     * 单位四元数表示无旋转状态，即绕任何轴的旋转角度为零。
     */
    Quaternion() : w(1.0), x(0.0), y(0.0), z(0.0) {}

    /**
     * @brief 参数化构造函数。
     *
     * 该构造函数创建一个Quaternion结构体实例，并根据提供的四元数分量值进行初始化。
     * @param qw 旋转的标量分量。
     * @param qx X轴上的旋转分量。
     * @param qy Y轴上的旋转分量。
     * @param qz Z轴上的旋转分量。
     */
    Quaternion(double qw, double qx, double qy, double qz)
            : w(qw), x(qx), y(qy), z(qz) {}

    /// 归一化四元数
    void normalize() {
        double norm = std::sqrt(w * w + x * x + y * y + z * z);
        w /= norm;
        x /= norm;
        y /= norm;
        z /= norm;
    }

    /// 计算两个四元数的点积
    double dot(const Quaternion &other) const;

    /**
     * @brief 将四元数转换为欧拉角表示的方法。
     *
     * @note 欧拉角使用roll-pitch-yaw顺序，即先绕X轴，再绕Y轴，最后绕Z轴。
     *
     * @return 表示旋转的欧拉角。
     */
    [[nodiscard]]
    EulerAngles to_euler_angles() const;

    /**
     * @brief 四元数相乘
     *
     * 四元数相乘的结果是一个新的四元数，表示两个四元数的旋转相乘的结果。
     *
     * @param right 右侧四元数
     * @return 相乘后的四元数
     */
    Quaternion operator*(const Quaternion &right) const;

    /**
     * @brief 四元数相乘
     *
     * 四元数相乘的结果是一个新的四元数，表示两个四元数的旋转相乘的结果。
     *
     * @param right 右侧四元数
     * @return 相乘后的四元数
     */
    Quaternion &operator*=(const Quaternion &right) {
        Quaternion q;
        q.w = w * right.w - x * right.x - y * right.y - z * right.z;
        q.x = w * right.x + x * right.w + y * right.z - z * right.y;
        q.y = w * right.y - x * right.z + y * right.w + z * right.x;
        q.z = w * right.z + x * right.y - y * right.x + z * right.w;
        *this = q;
        return *this;
    }

    /**
     * @brief 是否是相同的旋转
     *
     * 四元数 (w, x, y, z) 和 (-w, -x, -y, -z) 表示相同的旋转，方向相反。
     *
     * @param cmp 要比较的四元数
     * @return true 表示相同，false 表示不同
     */
    bool operator==(const Quaternion &cmp) const;

    /**
     * @brief 四元数的球形线性插值 (Spherical Linear Interpolation, SLERP)。
     *
     * @param target 目标四元数。
     * @param t 插值参数，范围在 [0, 1] 之间，0 表示当前四元数，1 表示目标四元数。
     * @return 返回插值后的四元数。
     */
    Quaternion slerp(const Quaternion& target, double t);

    /**
     * @brief 计算到指定四元数的角度。
     *
     * @param other 目标四元数。
     * @return 返回到目标四元数的角度。
     */
    double angle_to(const Quaternion& other) const;

    /**
     * @brief 将四元数转换为字符串表示的方法。
     *
     * @return 四元数的字符串表示。
     */
    [[nodiscard]]
    nlohmann::json to_json() const;

    /**
     * @brief 从JSON中解析四元数
     *
     * @param j JSON对象
     */
    void from_json(const nlohmann::json& j);
};

} // namespace aubo

namespace nlohmann {

inline void to_json(nlohmann::json& j, const aubo::Quaternion& q) {
    j = q.to_json();
}

inline void from_json(const nlohmann::json& j, aubo::Quaternion& q) {
    q.from_json(j);
}

} // namespace nlohmann

#endif //AUBO_QUATERNION_H