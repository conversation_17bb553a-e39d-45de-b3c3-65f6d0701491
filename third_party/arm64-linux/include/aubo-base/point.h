//
// Copyright ©2015-2024 Aubo (Beijing) Robotics Technology Co., Ltd. All rights reserved.
//

#ifndef AUBO_POINT_H
#define AUBO_POINT_H

#include <cmath>

#include <aubo-base/api.h>
#include <aubo-base/vec.h>
#include <nlohmann/json.hpp>

namespace aubo {

class Pose;

template<typename T,
         typename = std::enable_if_t<std::is_same<T, int>::value ||
                                     std::is_same<T, float>::value ||
                                     std::is_same<T, double>::value>>
class AUBO_API Point2 {
public:
    /// X轴上的坐标
    T x = 0;

    /// Y轴上的坐标
    T y = 0;

    /// 默认构造函数
    Point2() : x(0), y(0) {}

    /// 默认构造函数
    Point2(T x, T y) : x(x), y(y) {}

    /// 拷贝构造函数
    Point2(const Point2& p) : x(p.x), y(p.y) {}

    /// 析构函数
    virtual ~Point2() = default;

    /// 判断点是否在线段上
    bool is_on_line(const Point2<T>& P1, const Point2<T>& P2, double tolerance = 1e-4) const {
        double dx1 = x - P1.x;
        double dy1 = y - P1.y;

        double dx2 = P2.x - P1.x;
        double dy2 = P2.y - P1.y;

        // 检查是否除零
        if (std::fabs(dx2) < tolerance && std::fabs(dy2) < tolerance) {
            throw std::invalid_argument("P1 and P2 cannot be the same point");
        }

        // 检查比例是否一致（容差处理）
        return std::fabs(dx1 / dx2 - dy1 / dy2) < tolerance;
    }

    /// 计算点到点的距离
    T distance_to(const Point2<T>& other) const {
        return std::sqrt(std::pow(x - other.x, 2) +
                         std::pow(y - other.y, 2));
    }

    /// 重载 = 运算符
    Point2<T> &operator=(const Point2<T> &cpy) {
        if (&cpy != this) {
            x = cpy.x;
            y = cpy.y;
        }
        return *this;
    }

    /// 重载 = 运算符
    Point2<T> &operator=(const Vec<T, 2> &cpy) {
        x = cpy[0];
        y = cpy[1];
        return *this;
    }

    /// 重载 == 运算符
    bool operator==(const Point2<T> &right) const {
        return fabs(x - right.x) < 1e-4 && fabs(y - right.y) < 1e-4;
    }

    /// 重载 + 运算符
    Point2<T> operator+(const Point2<T> &right) const {
        return Point2<T>(x + right.x, y + right.y);
    }

    /// 重载 * 运算符
    Point2<T> operator*(T right) const {
        return Point2<T>(x * right, y * right);
    }

    /// 转换为JSON
    [[nodiscard]]
    nlohmann::json to_json() const {
        try {
            nlohmann::json j;
            to_json(j);
            return j;
        } catch (const std::exception& e) {
            // 无法直接使用LOG_ERROR，因为这是头文件
            return nlohmann::json();
        }
    }

    /// 转换为JSON
    void to_json(nlohmann::json& j) const {
        try {
            j["x"] = x;
            j["y"] = y;
        } catch (const std::exception& e) {
            // 无法直接使用LOG_ERROR，因为这是头文件
        }
    }

    /// 从JSON中解析点
    void from_json(const nlohmann::json& j) {
        try {
            x = (j.contains("x") && j["x"].is_number())
                     ? j["x"].get<T>()
                     : 0;
            y = (j.contains("y") && j["y"].is_number())
                     ? j["y"].get<T>()
                     : 0;
        } catch (const std::exception& e) {
            // 无法直接使用LOG_ERROR，因为这是头文件
        }
    }
};

template<typename T,
         typename = std::enable_if_t<std::is_same<T, int>::value ||
                                     std::is_same<T, float>::value ||
                                     std::is_same<T, double>::value>>
class AUBO_API Point3 {
public:
    /// X轴上的坐标
    T x = 0;

    /// Y轴上的坐标
    T y = 0;

    /// Z轴上的坐标
    T z = 0;

    /// 默认构造函数
    Point3() : x(0), y(0), z(0) {}

    /// 构造函数
    Point3(T x, T y, T z) : x(x), y(y), z(z) {}

    /// 拷贝构造函数
    Point3(const Point3& p) : x(p.x), y(p.y), z(p.z) {}

    /// 析构函数
    virtual ~Point3() = default;

    /// 判断点是否在线段上
    bool is_on_line(const Point3<T>& p1, const Point3<T>& p2, double tolerance = 1e-4) const {
        double dx1 = x - p1.x;
        double dy1 = y - p1.y;
        double dz1 = z - p1.z;

        double dx2 = p2.x - p1.x;
        double dy2 = p2.y - p1.y;
        double dz2 = p2.z - p1.z;

        // 检查是否除零
        if (std::fabs(dx2) < tolerance && std::fabs(dy2) < tolerance && std::fabs(dz2) < tolerance) {
            throw std::invalid_argument("P1 and P2 cannot be the same point");
        }

        // 检查比例是否一致（容差处理）
        bool check_x = (std::fabs(dx2) < tolerance) || (std::fabs(dx1 / dx2 - dy1 / dy2) < tolerance);
        bool check_y = (std::fabs(dy2) < tolerance) || (std::fabs(dx1 / dx2 - dz1 / dz2) < tolerance);
        bool check_z = (std::fabs(dz2) < tolerance) || (std::fabs(dy1 / dy2 - dz1 / dz2) < tolerance);

        return check_x && check_y && check_z;
    }

    /// 计算点到点的距离
    T distance_to(const Point3<T>& other) const {
        return std::sqrt(std::pow(x - other.x, 2) +
                         std::pow(y - other.y, 2) +
                         std::pow(z - other.z, 2));
    }

    /// 重载 = 运算符
    Point3<T> &operator=(const Point3<T> &cpy) {
        if (&cpy != this) {
            x = cpy.x;
            y = cpy.y;
            z = cpy.z;
        }
        return *this;
    }

    /// 重载 = 运算符
    Point3<T> &operator=(const Vec<T, 3> &cpy) {
        x = cpy[0];
        y = cpy[1];
        z = cpy[2];
        return *this;
    }

    /**
     * @brief 重载 * 运算符，用于位姿的乘法运算。
     *
     * 将一个点从一个坐标系转换到另一个坐标系。
     *
     * @param right 位姿。
     * @return 返回一个新的Point3<T>实例，表示转换后的点。
     */
    [[nodiscard]]
    Point3<T> &operator*(const Pose &right) const {
        return transform_position({x, y, z}, right);
    }

    /**
     * @brief 重载 == 运算符，用于判断两个点是否相等。
     *
     * @param right 右操作数
     * @return 如果两个点的坐标都相等，则返回true，否则返回false。
     */
    bool operator==(const Point3<T> &right) const {
        return fabs(x - right.x) < 1e-4 && fabs(y - right.y) < 1e-4 && fabs(z - right.z) < 1e-4;
    }

    /**
     * @brief 重载 + 运算符，用于两个点的加法运算。
     *
     * @param right 右操作数
     * @return 返回一个新的Point3<T>实例，表示两个点的和。
     */
    Point3<T> operator+(const Point3<T> &right) const {
        return Point3<T>(x + right.x, y + right.y, z + right.z);
    }

    /**
     * @brief 重载 * 运算符，用于点的数乘运算。
     *
     * @param right 右操作数
     * @return 返回一个新的Point3<T>实例，表示点的数乘结果。
     */
    Point3<T> operator*(T right) const {
        return Point3<T>(x * right, y * right, z * right);
    }

    /// 转换为JSON
    [[nodiscard]] nlohmann::json to_json() const {
        try {
            nlohmann::json j;
            to_json(j);
            return j;
        } catch (const std::exception& e) {
            // 无法直接使用LOG_ERROR，因为这是头文件
            return nlohmann::json();
        }
    }

    /// 转换为JSON
    void to_json(nlohmann::json& j) const {
        try {
            j["x"] = x;
            j["y"] = y;
            j["z"] = z;
        } catch (const std::exception& e) {
            // 无法直接使用LOG_ERROR，因为这是头文件
        }
    }

    /// 从JSON中解析点
    void from_json(const nlohmann::json& j) {
        try {
            x = (j.contains("x") && j["x"].is_number())
                     ? j["x"].get<T>()
                     : 0;
            y = (j.contains("y") && j["y"].is_number())
                     ? j["y"].get<T>()
                     : 0;
            z = (j.contains("z") && j["z"].is_number())
                     ? j["z"].get<T>()
                     : 0;
        } catch (const std::exception& e) {
            // 无法直接使用LOG_ERROR，因为这是头文件
        }
    }
};

/// 三维点结构体(double)
using Point3d = Point3<double>;

/// 三维点结构体(float)
using Point3f = Point3<float>;

/// 三维点结构体(int)
using Point3i = Point3<int>;

/// 位置结构体, 单位：米
using Position = Point3<double>;

/// 二维点结构体(double)
using Point2d = Point2<double>;

/// 二维点结构体(float)
using Point2f = Point2<float>;

/// 二维点结构体(int)
using Point2i = Point2<int>;

} // namespace aubo

namespace nlohmann {

inline void to_json(nlohmann::json& j, const aubo::Point3d& p) {
    j = p.to_json();
}

inline void from_json(const nlohmann::json& j, aubo::Point3d& p) {
    p.from_json(j);
}

inline void to_json(nlohmann::json& j, const aubo::Point3f& p) {
    j = p.to_json();
}

inline void from_json(const nlohmann::json& j, aubo::Point3f& p) {
    p.from_json(j);
}

inline void to_json(nlohmann::json& j, const aubo::Point3i& p) {
    j = p.to_json();
}

inline void from_json(const nlohmann::json& j, aubo::Point3i& p) {
    p.from_json(j);
}

inline void to_json(nlohmann::json& j, const aubo::Point2d& p) {
    j = p.to_json();
}

inline void from_json(const nlohmann::json& j, aubo::Point2d& p) {
    p.from_json(j);
}

inline void to_json(nlohmann::json& j, const aubo::Point2f& p) {
    j = p.to_json();
}

inline void from_json(const nlohmann::json& j, aubo::Point2f& p) {
    p.from_json(j);
}

inline void to_json(nlohmann::json& j, const aubo::Point2i& p) {
    j = p.to_json();
}

inline void from_json(const nlohmann::json& j, aubo::Point2i& p) {
    p.from_json(j);
}

} // namespace nlohmann

#endif // AUBO_POINT_H