// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: robotcommunication.proto

#ifndef PROTOBUF_robotcommunication_2eproto__INCLUDED
#define PROTOBUF_robotcommunication_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 2006000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 2006001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)

namespace aubo {
namespace robot {
namespace communication {

// Internal implementation detail -- do not call these.
void  protobuf_AddDesc_robotcommunication_2eproto();
void protobuf_AssignDesc_robotcommunication_2eproto();
void protobuf_ShutdownFile_robotcommunication_2eproto();

class JointStatus;
class RobotCollisionCurrent;
class TcpParam;
class RobotTcpParam;
class RobotGravityComponent;
class JointVersion;
class OurRobotDevInfo;
class RobotDiagnosis;
class RobotExtDiagnosis;
class RobotSafetyConfig;
class OrpeSafetyStatus;
class protoSafeCommunicationParam;
class OriginPose;
class RobotToolConfig;
class ProtoRobotCommonResponse;
class ProtoRequestLogin;
class ProtoRobotDiagnosisIODesc;
class ProtoRobotAnalogIODesc;
class ProtoCommunicationRobotDiagnosisIOData;
class ProtoCommunicationRobotAnalogIOData;
class ProtoCommunicationGeneralData;
class ProtoCommunicationRobotDiagnosisInfo;
class ProtoCommunicationRobotEvent;
class ProtoCommunicationRobotStartupProfile;
class ProtoCommunicationOfflineExcitTraj;
class ProtoCommunicationDynIdentifyResults;
class InterfaceBoardError;
class ModbusCfg;
class TagIoCfg;
class ToolDigitalStatus;
class RobotToolStatus;
class toolAllIOStatus;
class ProtoCommunicationToolAllIOStatusInfoResponse;
class ProtoCommunicationRobotBoardFirmware;
class ProtoCommunicationEthernetDeviceNameResponse;
class ProtoCommunicationDoubleVector;
class ProtoCommunicationIntVector;
class ProtoCommunicationDoubleVectorResponse;
class ProtoCommunicationIntVectorResponse;
class ProtoJointCommonData;
class ProtoJointCommonDataResponse;

enum ModbusMode {
  MODBUS_MODE_TCP = 0,
  MODBUS_MODE_RTU = 1
};
bool ModbusMode_IsValid(int value);
const ModbusMode ModbusMode_MIN = MODBUS_MODE_TCP;
const ModbusMode ModbusMode_MAX = MODBUS_MODE_RTU;
const int ModbusMode_ARRAYSIZE = ModbusMode_MAX + 1;

const ::google::protobuf::EnumDescriptor* ModbusMode_descriptor();
inline const ::std::string& ModbusMode_Name(ModbusMode value) {
  return ::google::protobuf::internal::NameOfEnum(
    ModbusMode_descriptor(), value);
}
inline bool ModbusMode_Parse(
    const ::std::string& name, ModbusMode* value) {
  return ::google::protobuf::internal::ParseNamedEnum<ModbusMode>(
    ModbusMode_descriptor(), name, value);
}
enum TagIoType {
  USER_MODBUS_DI = 0,
  USER_MODBUS_DO = 1,
  USER_MODBUS_AI = 2,
  USER_MODBUS_AO = 3,
  ROBOT_BOARD_SAFE_DI = 4,
  ROBOT_BOARD_SAFE_DO = 5,
  ROBOT_BOARD_INTERNAL_DI = 6,
  ROBOT_BOARD_INTERNAL_DO = 7,
  ROBOT_BOARD_LINKAGE_DI = 8,
  ROBOT_BOARD_LINKAGE_DO = 9,
  ROBOT_BOARD_DPROJECT_DI = 10,
  ROBOT_BOARD_USER_AI = 11,
  ROBOT_BOARD_USER_AO = 12,
  ROBOT_BOARD_USER_DI = 13,
  ROBOT_BOARD_USER_DO = 14,
  ROBOT_TOOL_DI = 15,
  ROBOT_TOOL_DO = 16,
  ROBOT_TOOL_AI = 17,
  ROBOT_TOOL_AO = 18
};
bool TagIoType_IsValid(int value);
const TagIoType TagIoType_MIN = USER_MODBUS_DI;
const TagIoType TagIoType_MAX = ROBOT_TOOL_AO;
const int TagIoType_ARRAYSIZE = TagIoType_MAX + 1;

const ::google::protobuf::EnumDescriptor* TagIoType_descriptor();
inline const ::std::string& TagIoType_Name(TagIoType value) {
  return ::google::protobuf::internal::NameOfEnum(
    TagIoType_descriptor(), value);
}
inline bool TagIoType_Parse(
    const ::std::string& name, TagIoType* value) {
  return ::google::protobuf::internal::ParseNamedEnum<TagIoType>(
    TagIoType_descriptor(), name, value);
}
// ===================================================================

class JointStatus : public ::google::protobuf::Message {
 public:
  JointStatus();
  virtual ~JointStatus();

  JointStatus(const JointStatus& from);

  inline JointStatus& operator=(const JointStatus& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const JointStatus& default_instance();

  void Swap(JointStatus* other);

  // implements Message ----------------------------------------------

  JointStatus* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const JointStatus& from);
  void MergeFrom(const JointStatus& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required int32 jointCurrentI = 1;
  inline bool has_jointcurrenti() const;
  inline void clear_jointcurrenti();
  static const int kJointCurrentIFieldNumber = 1;
  inline ::google::protobuf::int32 jointcurrenti() const;
  inline void set_jointcurrenti(::google::protobuf::int32 value);

  // required int32 jointSpeedMoto = 2;
  inline bool has_jointspeedmoto() const;
  inline void clear_jointspeedmoto();
  static const int kJointSpeedMotoFieldNumber = 2;
  inline ::google::protobuf::int32 jointspeedmoto() const;
  inline void set_jointspeedmoto(::google::protobuf::int32 value);

  // required float jointPosJ = 3;
  inline bool has_jointposj() const;
  inline void clear_jointposj();
  static const int kJointPosJFieldNumber = 3;
  inline float jointposj() const;
  inline void set_jointposj(float value);

  // required float jointCurVol = 4;
  inline bool has_jointcurvol() const;
  inline void clear_jointcurvol();
  static const int kJointCurVolFieldNumber = 4;
  inline float jointcurvol() const;
  inline void set_jointcurvol(float value);

  // required float jointCurTemp = 5;
  inline bool has_jointcurtemp() const;
  inline void clear_jointcurtemp();
  static const int kJointCurTempFieldNumber = 5;
  inline float jointcurtemp() const;
  inline void set_jointcurtemp(float value);

  // required int32 jointTagCurrentI = 6;
  inline bool has_jointtagcurrenti() const;
  inline void clear_jointtagcurrenti();
  static const int kJointTagCurrentIFieldNumber = 6;
  inline ::google::protobuf::int32 jointtagcurrenti() const;
  inline void set_jointtagcurrenti(::google::protobuf::int32 value);

  // required float jointTagSpeedMoto = 7;
  inline bool has_jointtagspeedmoto() const;
  inline void clear_jointtagspeedmoto();
  static const int kJointTagSpeedMotoFieldNumber = 7;
  inline float jointtagspeedmoto() const;
  inline void set_jointtagspeedmoto(float value);

  // required float jointTagPosJ = 8;
  inline bool has_jointtagposj() const;
  inline void clear_jointtagposj();
  static const int kJointTagPosJFieldNumber = 8;
  inline float jointtagposj() const;
  inline void set_jointtagposj(float value);

  // required uint32 jointErrorNum = 9;
  inline bool has_jointerrornum() const;
  inline void clear_jointerrornum();
  static const int kJointErrorNumFieldNumber = 9;
  inline ::google::protobuf::uint32 jointerrornum() const;
  inline void set_jointerrornum(::google::protobuf::uint32 value);

  // @@protoc_insertion_point(class_scope:aubo.robot.communication.JointStatus)
 private:
  inline void set_has_jointcurrenti();
  inline void clear_has_jointcurrenti();
  inline void set_has_jointspeedmoto();
  inline void clear_has_jointspeedmoto();
  inline void set_has_jointposj();
  inline void clear_has_jointposj();
  inline void set_has_jointcurvol();
  inline void clear_has_jointcurvol();
  inline void set_has_jointcurtemp();
  inline void clear_has_jointcurtemp();
  inline void set_has_jointtagcurrenti();
  inline void clear_has_jointtagcurrenti();
  inline void set_has_jointtagspeedmoto();
  inline void clear_has_jointtagspeedmoto();
  inline void set_has_jointtagposj();
  inline void clear_has_jointtagposj();
  inline void set_has_jointerrornum();
  inline void clear_has_jointerrornum();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::int32 jointcurrenti_;
  ::google::protobuf::int32 jointspeedmoto_;
  float jointposj_;
  float jointcurvol_;
  float jointcurtemp_;
  ::google::protobuf::int32 jointtagcurrenti_;
  float jointtagspeedmoto_;
  float jointtagposj_;
  ::google::protobuf::uint32 jointerrornum_;
  friend void  protobuf_AddDesc_robotcommunication_2eproto();
  friend void protobuf_AssignDesc_robotcommunication_2eproto();
  friend void protobuf_ShutdownFile_robotcommunication_2eproto();

  void InitAsDefaultInstance();
  static JointStatus* default_instance_;
};
// -------------------------------------------------------------------

class RobotCollisionCurrent : public ::google::protobuf::Message {
 public:
  RobotCollisionCurrent();
  virtual ~RobotCollisionCurrent();

  RobotCollisionCurrent(const RobotCollisionCurrent& from);

  inline RobotCollisionCurrent& operator=(const RobotCollisionCurrent& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const RobotCollisionCurrent& default_instance();

  void Swap(RobotCollisionCurrent* other);

  // implements Message ----------------------------------------------

  RobotCollisionCurrent* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const RobotCollisionCurrent& from);
  void MergeFrom(const RobotCollisionCurrent& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated uint32 jointCollisionCurrent = 1;
  inline int jointcollisioncurrent_size() const;
  inline void clear_jointcollisioncurrent();
  static const int kJointCollisionCurrentFieldNumber = 1;
  inline ::google::protobuf::uint32 jointcollisioncurrent(int index) const;
  inline void set_jointcollisioncurrent(int index, ::google::protobuf::uint32 value);
  inline void add_jointcollisioncurrent(::google::protobuf::uint32 value);
  inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
      jointcollisioncurrent() const;
  inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
      mutable_jointcollisioncurrent();

  // required uint32 collisionClass = 2;
  inline bool has_collisionclass() const;
  inline void clear_collisionclass();
  static const int kCollisionClassFieldNumber = 2;
  inline ::google::protobuf::uint32 collisionclass() const;
  inline void set_collisionclass(::google::protobuf::uint32 value);

  // @@protoc_insertion_point(class_scope:aubo.robot.communication.RobotCollisionCurrent)
 private:
  inline void set_has_collisionclass();
  inline void clear_has_collisionclass();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 > jointcollisioncurrent_;
  ::google::protobuf::uint32 collisionclass_;
  friend void  protobuf_AddDesc_robotcommunication_2eproto();
  friend void protobuf_AssignDesc_robotcommunication_2eproto();
  friend void protobuf_ShutdownFile_robotcommunication_2eproto();

  void InitAsDefaultInstance();
  static RobotCollisionCurrent* default_instance_;
};
// -------------------------------------------------------------------

class TcpParam : public ::google::protobuf::Message {
 public:
  TcpParam();
  virtual ~TcpParam();

  TcpParam(const TcpParam& from);

  inline TcpParam& operator=(const TcpParam& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const TcpParam& default_instance();

  void Swap(TcpParam* other);

  // implements Message ----------------------------------------------

  TcpParam* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const TcpParam& from);
  void MergeFrom(const TcpParam& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required float positionX = 1;
  inline bool has_positionx() const;
  inline void clear_positionx();
  static const int kPositionXFieldNumber = 1;
  inline float positionx() const;
  inline void set_positionx(float value);

  // required float positionY = 2;
  inline bool has_positiony() const;
  inline void clear_positiony();
  static const int kPositionYFieldNumber = 2;
  inline float positiony() const;
  inline void set_positiony(float value);

  // required float positionZ = 3;
  inline bool has_positionz() const;
  inline void clear_positionz();
  static const int kPositionZFieldNumber = 3;
  inline float positionz() const;
  inline void set_positionz(float value);

  // required float payload = 4;
  inline bool has_payload() const;
  inline void clear_payload();
  static const int kPayloadFieldNumber = 4;
  inline float payload() const;
  inline void set_payload(float value);

  // @@protoc_insertion_point(class_scope:aubo.robot.communication.TcpParam)
 private:
  inline void set_has_positionx();
  inline void clear_has_positionx();
  inline void set_has_positiony();
  inline void clear_has_positiony();
  inline void set_has_positionz();
  inline void clear_has_positionz();
  inline void set_has_payload();
  inline void clear_has_payload();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  float positionx_;
  float positiony_;
  float positionz_;
  float payload_;
  friend void  protobuf_AddDesc_robotcommunication_2eproto();
  friend void protobuf_AssignDesc_robotcommunication_2eproto();
  friend void protobuf_ShutdownFile_robotcommunication_2eproto();

  void InitAsDefaultInstance();
  static TcpParam* default_instance_;
};
// -------------------------------------------------------------------

class RobotTcpParam : public ::google::protobuf::Message {
 public:
  RobotTcpParam();
  virtual ~RobotTcpParam();

  RobotTcpParam(const RobotTcpParam& from);

  inline RobotTcpParam& operator=(const RobotTcpParam& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const RobotTcpParam& default_instance();

  void Swap(RobotTcpParam* other);

  // implements Message ----------------------------------------------

  RobotTcpParam* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const RobotTcpParam& from);
  void MergeFrom(const RobotTcpParam& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .aubo.robot.communication.TcpParam paramAutorun = 1;
  inline bool has_paramautorun() const;
  inline void clear_paramautorun();
  static const int kParamAutorunFieldNumber = 1;
  inline const ::aubo::robot::communication::TcpParam& paramautorun() const;
  inline ::aubo::robot::communication::TcpParam* mutable_paramautorun();
  inline ::aubo::robot::communication::TcpParam* release_paramautorun();
  inline void set_allocated_paramautorun(::aubo::robot::communication::TcpParam* paramautorun);

  // required .aubo.robot.communication.TcpParam paramManual = 2;
  inline bool has_parammanual() const;
  inline void clear_parammanual();
  static const int kParamManualFieldNumber = 2;
  inline const ::aubo::robot::communication::TcpParam& parammanual() const;
  inline ::aubo::robot::communication::TcpParam* mutable_parammanual();
  inline ::aubo::robot::communication::TcpParam* release_parammanual();
  inline void set_allocated_parammanual(::aubo::robot::communication::TcpParam* parammanual);

  // @@protoc_insertion_point(class_scope:aubo.robot.communication.RobotTcpParam)
 private:
  inline void set_has_paramautorun();
  inline void clear_has_paramautorun();
  inline void set_has_parammanual();
  inline void clear_has_parammanual();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::aubo::robot::communication::TcpParam* paramautorun_;
  ::aubo::robot::communication::TcpParam* parammanual_;
  friend void  protobuf_AddDesc_robotcommunication_2eproto();
  friend void protobuf_AssignDesc_robotcommunication_2eproto();
  friend void protobuf_ShutdownFile_robotcommunication_2eproto();

  void InitAsDefaultInstance();
  static RobotTcpParam* default_instance_;
};
// -------------------------------------------------------------------

class RobotGravityComponent : public ::google::protobuf::Message {
 public:
  RobotGravityComponent();
  virtual ~RobotGravityComponent();

  RobotGravityComponent(const RobotGravityComponent& from);

  inline RobotGravityComponent& operator=(const RobotGravityComponent& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const RobotGravityComponent& default_instance();

  void Swap(RobotGravityComponent* other);

  // implements Message ----------------------------------------------

  RobotGravityComponent* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const RobotGravityComponent& from);
  void MergeFrom(const RobotGravityComponent& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required float x = 1;
  inline bool has_x() const;
  inline void clear_x();
  static const int kXFieldNumber = 1;
  inline float x() const;
  inline void set_x(float value);

  // required float y = 2;
  inline bool has_y() const;
  inline void clear_y();
  static const int kYFieldNumber = 2;
  inline float y() const;
  inline void set_y(float value);

  // required float z = 3;
  inline bool has_z() const;
  inline void clear_z();
  static const int kZFieldNumber = 3;
  inline float z() const;
  inline void set_z(float value);

  // @@protoc_insertion_point(class_scope:aubo.robot.communication.RobotGravityComponent)
 private:
  inline void set_has_x();
  inline void clear_has_x();
  inline void set_has_y();
  inline void clear_has_y();
  inline void set_has_z();
  inline void clear_has_z();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  float x_;
  float y_;
  float z_;
  friend void  protobuf_AddDesc_robotcommunication_2eproto();
  friend void protobuf_AssignDesc_robotcommunication_2eproto();
  friend void protobuf_ShutdownFile_robotcommunication_2eproto();

  void InitAsDefaultInstance();
  static RobotGravityComponent* default_instance_;
};
// -------------------------------------------------------------------

class JointVersion : public ::google::protobuf::Message {
 public:
  JointVersion();
  virtual ~JointVersion();

  JointVersion(const JointVersion& from);

  inline JointVersion& operator=(const JointVersion& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const JointVersion& default_instance();

  void Swap(JointVersion* other);

  // implements Message ----------------------------------------------

  JointVersion* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const JointVersion& from);
  void MergeFrom(const JointVersion& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required string hwVersion = 1;
  inline bool has_hwversion() const;
  inline void clear_hwversion();
  static const int kHwVersionFieldNumber = 1;
  inline const ::std::string& hwversion() const;
  inline void set_hwversion(const ::std::string& value);
  inline void set_hwversion(const char* value);
  inline void set_hwversion(const char* value, size_t size);
  inline ::std::string* mutable_hwversion();
  inline ::std::string* release_hwversion();
  inline void set_allocated_hwversion(::std::string* hwversion);

  // required string swVersion = 2;
  inline bool has_swversion() const;
  inline void clear_swversion();
  static const int kSwVersionFieldNumber = 2;
  inline const ::std::string& swversion() const;
  inline void set_swversion(const ::std::string& value);
  inline void set_swversion(const char* value);
  inline void set_swversion(const char* value, size_t size);
  inline ::std::string* mutable_swversion();
  inline ::std::string* release_swversion();
  inline void set_allocated_swversion(::std::string* swversion);

  // @@protoc_insertion_point(class_scope:aubo.robot.communication.JointVersion)
 private:
  inline void set_has_hwversion();
  inline void clear_has_hwversion();
  inline void set_has_swversion();
  inline void clear_has_swversion();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::std::string* hwversion_;
  ::std::string* swversion_;
  friend void  protobuf_AddDesc_robotcommunication_2eproto();
  friend void protobuf_AssignDesc_robotcommunication_2eproto();
  friend void protobuf_ShutdownFile_robotcommunication_2eproto();

  void InitAsDefaultInstance();
  static JointVersion* default_instance_;
};
// -------------------------------------------------------------------

class OurRobotDevInfo : public ::google::protobuf::Message {
 public:
  OurRobotDevInfo();
  virtual ~OurRobotDevInfo();

  OurRobotDevInfo(const OurRobotDevInfo& from);

  inline OurRobotDevInfo& operator=(const OurRobotDevInfo& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const OurRobotDevInfo& default_instance();

  void Swap(OurRobotDevInfo* other);

  // implements Message ----------------------------------------------

  OurRobotDevInfo* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const OurRobotDevInfo& from);
  void MergeFrom(const OurRobotDevInfo& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required uint32 type = 1;
  inline bool has_type() const;
  inline void clear_type();
  static const int kTypeFieldNumber = 1;
  inline ::google::protobuf::uint32 type() const;
  inline void set_type(::google::protobuf::uint32 value);

  // required string revision = 2;
  inline bool has_revision() const;
  inline void clear_revision();
  static const int kRevisionFieldNumber = 2;
  inline const ::std::string& revision() const;
  inline void set_revision(const ::std::string& value);
  inline void set_revision(const char* value);
  inline void set_revision(const char* value, size_t size);
  inline ::std::string* mutable_revision();
  inline ::std::string* release_revision();
  inline void set_allocated_revision(::std::string* revision);

  // required string manuID = 3;
  inline bool has_manuid() const;
  inline void clear_manuid();
  static const int kManuIDFieldNumber = 3;
  inline const ::std::string& manuid() const;
  inline void set_manuid(const ::std::string& value);
  inline void set_manuid(const char* value);
  inline void set_manuid(const char* value, size_t size);
  inline ::std::string* mutable_manuid();
  inline ::std::string* release_manuid();
  inline void set_allocated_manuid(::std::string* manuid);

  // required string jointType = 4;
  inline bool has_jointtype() const;
  inline void clear_jointtype();
  static const int kJointTypeFieldNumber = 4;
  inline const ::std::string& jointtype() const;
  inline void set_jointtype(const ::std::string& value);
  inline void set_jointtype(const char* value);
  inline void set_jointtype(const char* value, size_t size);
  inline ::std::string* mutable_jointtype();
  inline ::std::string* release_jointtype();
  inline void set_allocated_jointtype(::std::string* jointtype);

  // repeated .aubo.robot.communication.JointVersion jointVer = 5;
  inline int jointver_size() const;
  inline void clear_jointver();
  static const int kJointVerFieldNumber = 5;
  inline const ::aubo::robot::communication::JointVersion& jointver(int index) const;
  inline ::aubo::robot::communication::JointVersion* mutable_jointver(int index);
  inline ::aubo::robot::communication::JointVersion* add_jointver();
  inline const ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::JointVersion >&
      jointver() const;
  inline ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::JointVersion >*
      mutable_jointver();

  // required string desc = 6;
  inline bool has_desc() const;
  inline void clear_desc();
  static const int kDescFieldNumber = 6;
  inline const ::std::string& desc() const;
  inline void set_desc(const ::std::string& value);
  inline void set_desc(const char* value);
  inline void set_desc(const char* value, size_t size);
  inline ::std::string* mutable_desc();
  inline ::std::string* release_desc();
  inline void set_allocated_desc(::std::string* desc);

  // repeated string jointProductID = 7;
  inline int jointproductid_size() const;
  inline void clear_jointproductid();
  static const int kJointProductIDFieldNumber = 7;
  inline const ::std::string& jointproductid(int index) const;
  inline ::std::string* mutable_jointproductid(int index);
  inline void set_jointproductid(int index, const ::std::string& value);
  inline void set_jointproductid(int index, const char* value);
  inline void set_jointproductid(int index, const char* value, size_t size);
  inline ::std::string* add_jointproductid();
  inline void add_jointproductid(const ::std::string& value);
  inline void add_jointproductid(const char* value);
  inline void add_jointproductid(const char* value, size_t size);
  inline const ::google::protobuf::RepeatedPtrField< ::std::string>& jointproductid() const;
  inline ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_jointproductid();

  // required string slaveDevVersion = 8;
  inline bool has_slavedevversion() const;
  inline void clear_slavedevversion();
  static const int kSlaveDevVersionFieldNumber = 8;
  inline const ::std::string& slavedevversion() const;
  inline void set_slavedevversion(const ::std::string& value);
  inline void set_slavedevversion(const char* value);
  inline void set_slavedevversion(const char* value, size_t size);
  inline ::std::string* mutable_slavedevversion();
  inline ::std::string* release_slavedevversion();
  inline void set_allocated_slavedevversion(::std::string* slavedevversion);

  // required string extendIoBoardVersion = 9;
  inline bool has_extendioboardversion() const;
  inline void clear_extendioboardversion();
  static const int kExtendIoBoardVersionFieldNumber = 9;
  inline const ::std::string& extendioboardversion() const;
  inline void set_extendioboardversion(const ::std::string& value);
  inline void set_extendioboardversion(const char* value);
  inline void set_extendioboardversion(const char* value, size_t size);
  inline ::std::string* mutable_extendioboardversion();
  inline ::std::string* release_extendioboardversion();
  inline void set_allocated_extendioboardversion(::std::string* extendioboardversion);

  // @@protoc_insertion_point(class_scope:aubo.robot.communication.OurRobotDevInfo)
 private:
  inline void set_has_type();
  inline void clear_has_type();
  inline void set_has_revision();
  inline void clear_has_revision();
  inline void set_has_manuid();
  inline void clear_has_manuid();
  inline void set_has_jointtype();
  inline void clear_has_jointtype();
  inline void set_has_desc();
  inline void clear_has_desc();
  inline void set_has_slavedevversion();
  inline void clear_has_slavedevversion();
  inline void set_has_extendioboardversion();
  inline void clear_has_extendioboardversion();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::std::string* revision_;
  ::std::string* manuid_;
  ::std::string* jointtype_;
  ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::JointVersion > jointver_;
  ::std::string* desc_;
  ::google::protobuf::RepeatedPtrField< ::std::string> jointproductid_;
  ::std::string* slavedevversion_;
  ::std::string* extendioboardversion_;
  ::google::protobuf::uint32 type_;
  friend void  protobuf_AddDesc_robotcommunication_2eproto();
  friend void protobuf_AssignDesc_robotcommunication_2eproto();
  friend void protobuf_ShutdownFile_robotcommunication_2eproto();

  void InitAsDefaultInstance();
  static OurRobotDevInfo* default_instance_;
};
// -------------------------------------------------------------------

class RobotDiagnosis : public ::google::protobuf::Message {
 public:
  RobotDiagnosis();
  virtual ~RobotDiagnosis();

  RobotDiagnosis(const RobotDiagnosis& from);

  inline RobotDiagnosis& operator=(const RobotDiagnosis& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const RobotDiagnosis& default_instance();

  void Swap(RobotDiagnosis* other);

  // implements Message ----------------------------------------------

  RobotDiagnosis* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const RobotDiagnosis& from);
  void MergeFrom(const RobotDiagnosis& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required uint32 armCanbusStatus = 1;
  inline bool has_armcanbusstatus() const;
  inline void clear_armcanbusstatus();
  static const int kArmCanbusStatusFieldNumber = 1;
  inline ::google::protobuf::uint32 armcanbusstatus() const;
  inline void set_armcanbusstatus(::google::protobuf::uint32 value);

  // required float armPowerCurrent = 2;
  inline bool has_armpowercurrent() const;
  inline void clear_armpowercurrent();
  static const int kArmPowerCurrentFieldNumber = 2;
  inline float armpowercurrent() const;
  inline void set_armpowercurrent(float value);

  // required float armPowerVoltage = 3;
  inline bool has_armpowervoltage() const;
  inline void clear_armpowervoltage();
  static const int kArmPowerVoltageFieldNumber = 3;
  inline float armpowervoltage() const;
  inline void set_armpowervoltage(float value);

  // required bool armPowerStatus = 4;
  inline bool has_armpowerstatus() const;
  inline void clear_armpowerstatus();
  static const int kArmPowerStatusFieldNumber = 4;
  inline bool armpowerstatus() const;
  inline void set_armpowerstatus(bool value);

  // required int32 contorllerTemp = 5;
  inline bool has_contorllertemp() const;
  inline void clear_contorllertemp();
  static const int kContorllerTempFieldNumber = 5;
  inline ::google::protobuf::int32 contorllertemp() const;
  inline void set_contorllertemp(::google::protobuf::int32 value);

  // required uint32 contorllerHumidity = 6;
  inline bool has_contorllerhumidity() const;
  inline void clear_contorllerhumidity();
  static const int kContorllerHumidityFieldNumber = 6;
  inline ::google::protobuf::uint32 contorllerhumidity() const;
  inline void set_contorllerhumidity(::google::protobuf::uint32 value);

  // required bool remoteHalt = 7;
  inline bool has_remotehalt() const;
  inline void clear_remotehalt();
  static const int kRemoteHaltFieldNumber = 7;
  inline bool remotehalt() const;
  inline void set_remotehalt(bool value);

  // required bool softEmergency = 8;
  inline bool has_softemergency() const;
  inline void clear_softemergency();
  static const int kSoftEmergencyFieldNumber = 8;
  inline bool softemergency() const;
  inline void set_softemergency(bool value);

  // required bool remoteEmergency = 9;
  inline bool has_remoteemergency() const;
  inline void clear_remoteemergency();
  static const int kRemoteEmergencyFieldNumber = 9;
  inline bool remoteemergency() const;
  inline void set_remoteemergency(bool value);

  // required bool robotCollision = 10;
  inline bool has_robotcollision() const;
  inline void clear_robotcollision();
  static const int kRobotCollisionFieldNumber = 10;
  inline bool robotcollision() const;
  inline void set_robotcollision(bool value);

  // required bool forceControlMode = 11;
  inline bool has_forcecontrolmode() const;
  inline void clear_forcecontrolmode();
  static const int kForceControlModeFieldNumber = 11;
  inline bool forcecontrolmode() const;
  inline void set_forcecontrolmode(bool value);

  // required bool brakeStuats = 12;
  inline bool has_brakestuats() const;
  inline void clear_brakestuats();
  static const int kBrakeStuatsFieldNumber = 12;
  inline bool brakestuats() const;
  inline void set_brakestuats(bool value);

  // required float robotEndSpeed = 13;
  inline bool has_robotendspeed() const;
  inline void clear_robotendspeed();
  static const int kRobotEndSpeedFieldNumber = 13;
  inline float robotendspeed() const;
  inline void set_robotendspeed(float value);

  // required int32 robotMaxAcc = 14;
  inline bool has_robotmaxacc() const;
  inline void clear_robotmaxacc();
  static const int kRobotMaxAccFieldNumber = 14;
  inline ::google::protobuf::int32 robotmaxacc() const;
  inline void set_robotmaxacc(::google::protobuf::int32 value);

  // required bool orpeStatus = 15;
  inline bool has_orpestatus() const;
  inline void clear_orpestatus();
  static const int kOrpeStatusFieldNumber = 15;
  inline bool orpestatus() const;
  inline void set_orpestatus(bool value);

  // required bool enableReadPose = 16;
  inline bool has_enablereadpose() const;
  inline void clear_enablereadpose();
  static const int kEnableReadPoseFieldNumber = 16;
  inline bool enablereadpose() const;
  inline void set_enablereadpose(bool value);

  // required bool robotMountingPoseChanged = 17;
  inline bool has_robotmountingposechanged() const;
  inline void clear_robotmountingposechanged();
  static const int kRobotMountingPoseChangedFieldNumber = 17;
  inline bool robotmountingposechanged() const;
  inline void set_robotmountingposechanged(bool value);

  // required bool encoderErrorStatus = 18;
  inline bool has_encodererrorstatus() const;
  inline void clear_encodererrorstatus();
  static const int kEncoderErrorStatusFieldNumber = 18;
  inline bool encodererrorstatus() const;
  inline void set_encodererrorstatus(bool value);

  // required bool staticCollisionDetect = 19;
  inline bool has_staticcollisiondetect() const;
  inline void clear_staticcollisiondetect();
  static const int kStaticCollisionDetectFieldNumber = 19;
  inline bool staticcollisiondetect() const;
  inline void set_staticcollisiondetect(bool value);

  // required uint32 jointCollisionDetect = 20;
  inline bool has_jointcollisiondetect() const;
  inline void clear_jointcollisiondetect();
  static const int kJointCollisionDetectFieldNumber = 20;
  inline ::google::protobuf::uint32 jointcollisiondetect() const;
  inline void set_jointcollisiondetect(::google::protobuf::uint32 value);

  // required bool encoderLinesError = 21;
  inline bool has_encoderlineserror() const;
  inline void clear_encoderlineserror();
  static const int kEncoderLinesErrorFieldNumber = 21;
  inline bool encoderlineserror() const;
  inline void set_encoderlineserror(bool value);

  // required bool jointErrorStatus = 22;
  inline bool has_jointerrorstatus() const;
  inline void clear_jointerrorstatus();
  static const int kJointErrorStatusFieldNumber = 22;
  inline bool jointerrorstatus() const;
  inline void set_jointerrorstatus(bool value);

  // required bool singularityOverSpeedAlarm = 23;
  inline bool has_singularityoverspeedalarm() const;
  inline void clear_singularityoverspeedalarm();
  static const int kSingularityOverSpeedAlarmFieldNumber = 23;
  inline bool singularityoverspeedalarm() const;
  inline void set_singularityoverspeedalarm(bool value);

  // required bool robotCurrentAlarm = 24;
  inline bool has_robotcurrentalarm() const;
  inline void clear_robotcurrentalarm();
  static const int kRobotCurrentAlarmFieldNumber = 24;
  inline bool robotcurrentalarm() const;
  inline void set_robotcurrentalarm(bool value);

  // required int32 toolIoError = 25;
  inline bool has_toolioerror() const;
  inline void clear_toolioerror();
  static const int kToolIoErrorFieldNumber = 25;
  inline ::google::protobuf::int32 toolioerror() const;
  inline void set_toolioerror(::google::protobuf::int32 value);

  // required bool robotMountingPoseWarning = 26;
  inline bool has_robotmountingposewarning() const;
  inline void clear_robotmountingposewarning();
  static const int kRobotMountingPoseWarningFieldNumber = 26;
  inline bool robotmountingposewarning() const;
  inline void set_robotmountingposewarning(bool value);

  // required uint32 macTargetPosBufferSize = 27;
  inline bool has_mactargetposbuffersize() const;
  inline void clear_mactargetposbuffersize();
  static const int kMacTargetPosBufferSizeFieldNumber = 27;
  inline ::google::protobuf::uint32 mactargetposbuffersize() const;
  inline void set_mactargetposbuffersize(::google::protobuf::uint32 value);

  // required uint32 macTargetPosDataSize = 28;
  inline bool has_mactargetposdatasize() const;
  inline void clear_mactargetposdatasize();
  static const int kMacTargetPosDataSizeFieldNumber = 28;
  inline ::google::protobuf::uint32 mactargetposdatasize() const;
  inline void set_mactargetposdatasize(::google::protobuf::uint32 value);

  // required uint32 macDataInterruptWarning = 29;
  inline bool has_macdatainterruptwarning() const;
  inline void clear_macdatainterruptwarning();
  static const int kMacDataInterruptWarningFieldNumber = 29;
  inline ::google::protobuf::uint32 macdatainterruptwarning() const;
  inline void set_macdatainterruptwarning(::google::protobuf::uint32 value);

  // optional uint32 controlBoardAbnormalStateFlag = 30;
  inline bool has_controlboardabnormalstateflag() const;
  inline void clear_controlboardabnormalstateflag();
  static const int kControlBoardAbnormalStateFlagFieldNumber = 30;
  inline ::google::protobuf::uint32 controlboardabnormalstateflag() const;
  inline void set_controlboardabnormalstateflag(::google::protobuf::uint32 value);

  // @@protoc_insertion_point(class_scope:aubo.robot.communication.RobotDiagnosis)
 private:
  inline void set_has_armcanbusstatus();
  inline void clear_has_armcanbusstatus();
  inline void set_has_armpowercurrent();
  inline void clear_has_armpowercurrent();
  inline void set_has_armpowervoltage();
  inline void clear_has_armpowervoltage();
  inline void set_has_armpowerstatus();
  inline void clear_has_armpowerstatus();
  inline void set_has_contorllertemp();
  inline void clear_has_contorllertemp();
  inline void set_has_contorllerhumidity();
  inline void clear_has_contorllerhumidity();
  inline void set_has_remotehalt();
  inline void clear_has_remotehalt();
  inline void set_has_softemergency();
  inline void clear_has_softemergency();
  inline void set_has_remoteemergency();
  inline void clear_has_remoteemergency();
  inline void set_has_robotcollision();
  inline void clear_has_robotcollision();
  inline void set_has_forcecontrolmode();
  inline void clear_has_forcecontrolmode();
  inline void set_has_brakestuats();
  inline void clear_has_brakestuats();
  inline void set_has_robotendspeed();
  inline void clear_has_robotendspeed();
  inline void set_has_robotmaxacc();
  inline void clear_has_robotmaxacc();
  inline void set_has_orpestatus();
  inline void clear_has_orpestatus();
  inline void set_has_enablereadpose();
  inline void clear_has_enablereadpose();
  inline void set_has_robotmountingposechanged();
  inline void clear_has_robotmountingposechanged();
  inline void set_has_encodererrorstatus();
  inline void clear_has_encodererrorstatus();
  inline void set_has_staticcollisiondetect();
  inline void clear_has_staticcollisiondetect();
  inline void set_has_jointcollisiondetect();
  inline void clear_has_jointcollisiondetect();
  inline void set_has_encoderlineserror();
  inline void clear_has_encoderlineserror();
  inline void set_has_jointerrorstatus();
  inline void clear_has_jointerrorstatus();
  inline void set_has_singularityoverspeedalarm();
  inline void clear_has_singularityoverspeedalarm();
  inline void set_has_robotcurrentalarm();
  inline void clear_has_robotcurrentalarm();
  inline void set_has_toolioerror();
  inline void clear_has_toolioerror();
  inline void set_has_robotmountingposewarning();
  inline void clear_has_robotmountingposewarning();
  inline void set_has_mactargetposbuffersize();
  inline void clear_has_mactargetposbuffersize();
  inline void set_has_mactargetposdatasize();
  inline void clear_has_mactargetposdatasize();
  inline void set_has_macdatainterruptwarning();
  inline void clear_has_macdatainterruptwarning();
  inline void set_has_controlboardabnormalstateflag();
  inline void clear_has_controlboardabnormalstateflag();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::uint32 armcanbusstatus_;
  float armpowercurrent_;
  float armpowervoltage_;
  ::google::protobuf::int32 contorllertemp_;
  ::google::protobuf::uint32 contorllerhumidity_;
  bool armpowerstatus_;
  bool remotehalt_;
  bool softemergency_;
  bool remoteemergency_;
  bool robotcollision_;
  bool forcecontrolmode_;
  bool brakestuats_;
  bool orpestatus_;
  float robotendspeed_;
  ::google::protobuf::int32 robotmaxacc_;
  bool enablereadpose_;
  bool robotmountingposechanged_;
  bool encodererrorstatus_;
  bool staticcollisiondetect_;
  ::google::protobuf::uint32 jointcollisiondetect_;
  bool encoderlineserror_;
  bool jointerrorstatus_;
  bool singularityoverspeedalarm_;
  bool robotcurrentalarm_;
  ::google::protobuf::int32 toolioerror_;
  bool robotmountingposewarning_;
  ::google::protobuf::uint32 mactargetposbuffersize_;
  ::google::protobuf::uint32 mactargetposdatasize_;
  ::google::protobuf::uint32 macdatainterruptwarning_;
  ::google::protobuf::uint32 controlboardabnormalstateflag_;
  friend void  protobuf_AddDesc_robotcommunication_2eproto();
  friend void protobuf_AssignDesc_robotcommunication_2eproto();
  friend void protobuf_ShutdownFile_robotcommunication_2eproto();

  void InitAsDefaultInstance();
  static RobotDiagnosis* default_instance_;
};
// -------------------------------------------------------------------

class RobotExtDiagnosis : public ::google::protobuf::Message {
 public:
  RobotExtDiagnosis();
  virtual ~RobotExtDiagnosis();

  RobotExtDiagnosis(const RobotExtDiagnosis& from);

  inline RobotExtDiagnosis& operator=(const RobotExtDiagnosis& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const RobotExtDiagnosis& default_instance();

  void Swap(RobotExtDiagnosis* other);

  // implements Message ----------------------------------------------

  RobotExtDiagnosis* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const RobotExtDiagnosis& from);
  void MergeFrom(const RobotExtDiagnosis& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required uint32 robotState = 1;
  inline bool has_robotstate() const;
  inline void clear_robotstate();
  static const int kRobotStateFieldNumber = 1;
  inline ::google::protobuf::uint32 robotstate() const;
  inline void set_robotstate(::google::protobuf::uint32 value);

  // required uint32 robotStatus = 2;
  inline bool has_robotstatus() const;
  inline void clear_robotstatus();
  static const int kRobotStatusFieldNumber = 2;
  inline ::google::protobuf::uint32 robotstatus() const;
  inline void set_robotstatus(::google::protobuf::uint32 value);

  // repeated float targetPos = 3;
  inline int targetpos_size() const;
  inline void clear_targetpos();
  static const int kTargetPosFieldNumber = 3;
  inline float targetpos(int index) const;
  inline void set_targetpos(int index, float value);
  inline void add_targetpos(float value);
  inline const ::google::protobuf::RepeatedField< float >&
      targetpos() const;
  inline ::google::protobuf::RepeatedField< float >*
      mutable_targetpos();

  // repeated float theoreticalSpeed = 4;
  inline int theoreticalspeed_size() const;
  inline void clear_theoreticalspeed();
  static const int kTheoreticalSpeedFieldNumber = 4;
  inline float theoreticalspeed(int index) const;
  inline void set_theoreticalspeed(int index, float value);
  inline void add_theoreticalspeed(float value);
  inline const ::google::protobuf::RepeatedField< float >&
      theoreticalspeed() const;
  inline ::google::protobuf::RepeatedField< float >*
      mutable_theoreticalspeed();

  // repeated float theoreticalAcc = 5;
  inline int theoreticalacc_size() const;
  inline void clear_theoreticalacc();
  static const int kTheoreticalAccFieldNumber = 5;
  inline float theoreticalacc(int index) const;
  inline void set_theoreticalacc(int index, float value);
  inline void add_theoreticalacc(float value);
  inline const ::google::protobuf::RepeatedField< float >&
      theoreticalacc() const;
  inline ::google::protobuf::RepeatedField< float >*
      mutable_theoreticalacc();

  // repeated uint32 theoreticalCurrent = 6;
  inline int theoreticalcurrent_size() const;
  inline void clear_theoreticalcurrent();
  static const int kTheoreticalCurrentFieldNumber = 6;
  inline ::google::protobuf::uint32 theoreticalcurrent(int index) const;
  inline void set_theoreticalcurrent(int index, ::google::protobuf::uint32 value);
  inline void add_theoreticalcurrent(::google::protobuf::uint32 value);
  inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
      theoreticalcurrent() const;
  inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
      mutable_theoreticalcurrent();

  // @@protoc_insertion_point(class_scope:aubo.robot.communication.RobotExtDiagnosis)
 private:
  inline void set_has_robotstate();
  inline void clear_has_robotstate();
  inline void set_has_robotstatus();
  inline void clear_has_robotstatus();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::uint32 robotstate_;
  ::google::protobuf::uint32 robotstatus_;
  ::google::protobuf::RepeatedField< float > targetpos_;
  ::google::protobuf::RepeatedField< float > theoreticalspeed_;
  ::google::protobuf::RepeatedField< float > theoreticalacc_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 > theoreticalcurrent_;
  friend void  protobuf_AddDesc_robotcommunication_2eproto();
  friend void protobuf_AssignDesc_robotcommunication_2eproto();
  friend void protobuf_ShutdownFile_robotcommunication_2eproto();

  void InitAsDefaultInstance();
  static RobotExtDiagnosis* default_instance_;
};
// -------------------------------------------------------------------

class RobotSafetyConfig : public ::google::protobuf::Message {
 public:
  RobotSafetyConfig();
  virtual ~RobotSafetyConfig();

  RobotSafetyConfig(const RobotSafetyConfig& from);

  inline RobotSafetyConfig& operator=(const RobotSafetyConfig& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const RobotSafetyConfig& default_instance();

  void Swap(RobotSafetyConfig* other);

  // implements Message ----------------------------------------------

  RobotSafetyConfig* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const RobotSafetyConfig& from);
  void MergeFrom(const RobotSafetyConfig& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated uint32 robotReducedConfigJointSpeed = 1;
  inline int robotreducedconfigjointspeed_size() const;
  inline void clear_robotreducedconfigjointspeed();
  static const int kRobotReducedConfigJointSpeedFieldNumber = 1;
  inline ::google::protobuf::uint32 robotreducedconfigjointspeed(int index) const;
  inline void set_robotreducedconfigjointspeed(int index, ::google::protobuf::uint32 value);
  inline void add_robotreducedconfigjointspeed(::google::protobuf::uint32 value);
  inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
      robotreducedconfigjointspeed() const;
  inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
      mutable_robotreducedconfigjointspeed();

  // required uint32 robotReducedConfigTcpSpeed = 2;
  inline bool has_robotreducedconfigtcpspeed() const;
  inline void clear_robotreducedconfigtcpspeed();
  static const int kRobotReducedConfigTcpSpeedFieldNumber = 2;
  inline ::google::protobuf::uint32 robotreducedconfigtcpspeed() const;
  inline void set_robotreducedconfigtcpspeed(::google::protobuf::uint32 value);

  // required uint32 robotReducedConfigTcpForce = 3;
  inline bool has_robotreducedconfigtcpforce() const;
  inline void clear_robotreducedconfigtcpforce();
  static const int kRobotReducedConfigTcpForceFieldNumber = 3;
  inline ::google::protobuf::uint32 robotreducedconfigtcpforce() const;
  inline void set_robotreducedconfigtcpforce(::google::protobuf::uint32 value);

  // required uint32 robotReducedConfigMomentum = 4;
  inline bool has_robotreducedconfigmomentum() const;
  inline void clear_robotreducedconfigmomentum();
  static const int kRobotReducedConfigMomentumFieldNumber = 4;
  inline ::google::protobuf::uint32 robotreducedconfigmomentum() const;
  inline void set_robotreducedconfigmomentum(::google::protobuf::uint32 value);

  // required uint32 robotReducedConfigPower = 5;
  inline bool has_robotreducedconfigpower() const;
  inline void clear_robotreducedconfigpower();
  static const int kRobotReducedConfigPowerFieldNumber = 5;
  inline ::google::protobuf::uint32 robotreducedconfigpower() const;
  inline void set_robotreducedconfigpower(::google::protobuf::uint32 value);

  // required uint32 robotSafeguradResetConfig = 6;
  inline bool has_robotsafeguradresetconfig() const;
  inline void clear_robotsafeguradresetconfig();
  static const int kRobotSafeguradResetConfigFieldNumber = 6;
  inline ::google::protobuf::uint32 robotsafeguradresetconfig() const;
  inline void set_robotsafeguradresetconfig(::google::protobuf::uint32 value);

  // required uint32 robotOperationalModeConfig = 7;
  inline bool has_robotoperationalmodeconfig() const;
  inline void clear_robotoperationalmodeconfig();
  static const int kRobotOperationalModeConfigFieldNumber = 7;
  inline ::google::protobuf::uint32 robotoperationalmodeconfig() const;
  inline void set_robotoperationalmodeconfig(::google::protobuf::uint32 value);

  // @@protoc_insertion_point(class_scope:aubo.robot.communication.RobotSafetyConfig)
 private:
  inline void set_has_robotreducedconfigtcpspeed();
  inline void clear_has_robotreducedconfigtcpspeed();
  inline void set_has_robotreducedconfigtcpforce();
  inline void clear_has_robotreducedconfigtcpforce();
  inline void set_has_robotreducedconfigmomentum();
  inline void clear_has_robotreducedconfigmomentum();
  inline void set_has_robotreducedconfigpower();
  inline void clear_has_robotreducedconfigpower();
  inline void set_has_robotsafeguradresetconfig();
  inline void clear_has_robotsafeguradresetconfig();
  inline void set_has_robotoperationalmodeconfig();
  inline void clear_has_robotoperationalmodeconfig();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 > robotreducedconfigjointspeed_;
  ::google::protobuf::uint32 robotreducedconfigtcpspeed_;
  ::google::protobuf::uint32 robotreducedconfigtcpforce_;
  ::google::protobuf::uint32 robotreducedconfigmomentum_;
  ::google::protobuf::uint32 robotreducedconfigpower_;
  ::google::protobuf::uint32 robotsafeguradresetconfig_;
  ::google::protobuf::uint32 robotoperationalmodeconfig_;
  friend void  protobuf_AddDesc_robotcommunication_2eproto();
  friend void protobuf_AssignDesc_robotcommunication_2eproto();
  friend void protobuf_ShutdownFile_robotcommunication_2eproto();

  void InitAsDefaultInstance();
  static RobotSafetyConfig* default_instance_;
};
// -------------------------------------------------------------------

class OrpeSafetyStatus : public ::google::protobuf::Message {
 public:
  OrpeSafetyStatus();
  virtual ~OrpeSafetyStatus();

  OrpeSafetyStatus(const OrpeSafetyStatus& from);

  inline OrpeSafetyStatus& operator=(const OrpeSafetyStatus& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const OrpeSafetyStatus& default_instance();

  void Swap(OrpeSafetyStatus* other);

  // implements Message ----------------------------------------------

  OrpeSafetyStatus* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const OrpeSafetyStatus& from);
  void MergeFrom(const OrpeSafetyStatus& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required uint32 orpePause = 1;
  inline bool has_orpepause() const;
  inline void clear_orpepause();
  static const int kOrpePauseFieldNumber = 1;
  inline ::google::protobuf::uint32 orpepause() const;
  inline void set_orpepause(::google::protobuf::uint32 value);

  // required uint32 orpeStop = 2;
  inline bool has_orpestop() const;
  inline void clear_orpestop();
  static const int kOrpeStopFieldNumber = 2;
  inline ::google::protobuf::uint32 orpestop() const;
  inline void set_orpestop(::google::protobuf::uint32 value);

  // repeated uint32 orpeError = 3;
  inline int orpeerror_size() const;
  inline void clear_orpeerror();
  static const int kOrpeErrorFieldNumber = 3;
  inline ::google::protobuf::uint32 orpeerror(int index) const;
  inline void set_orpeerror(int index, ::google::protobuf::uint32 value);
  inline void add_orpeerror(::google::protobuf::uint32 value);
  inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
      orpeerror() const;
  inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
      mutable_orpeerror();

  // required uint32 systemEmergencyStop = 4;
  inline bool has_systememergencystop() const;
  inline void clear_systememergencystop();
  static const int kSystemEmergencyStopFieldNumber = 4;
  inline ::google::protobuf::uint32 systememergencystop() const;
  inline void set_systememergencystop(::google::protobuf::uint32 value);

  // required uint32 reducedModeError = 5;
  inline bool has_reducedmodeerror() const;
  inline void clear_reducedmodeerror();
  static const int kReducedModeErrorFieldNumber = 5;
  inline ::google::protobuf::uint32 reducedmodeerror() const;
  inline void set_reducedmodeerror(::google::protobuf::uint32 value);

  // required uint32 safetyguardResetSucc = 6;
  inline bool has_safetyguardresetsucc() const;
  inline void clear_safetyguardresetsucc();
  static const int kSafetyguardResetSuccFieldNumber = 6;
  inline ::google::protobuf::uint32 safetyguardresetsucc() const;
  inline void set_safetyguardresetsucc(::google::protobuf::uint32 value);

  // @@protoc_insertion_point(class_scope:aubo.robot.communication.OrpeSafetyStatus)
 private:
  inline void set_has_orpepause();
  inline void clear_has_orpepause();
  inline void set_has_orpestop();
  inline void clear_has_orpestop();
  inline void set_has_systememergencystop();
  inline void clear_has_systememergencystop();
  inline void set_has_reducedmodeerror();
  inline void clear_has_reducedmodeerror();
  inline void set_has_safetyguardresetsucc();
  inline void clear_has_safetyguardresetsucc();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::uint32 orpepause_;
  ::google::protobuf::uint32 orpestop_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 > orpeerror_;
  ::google::protobuf::uint32 systememergencystop_;
  ::google::protobuf::uint32 reducedmodeerror_;
  ::google::protobuf::uint32 safetyguardresetsucc_;
  friend void  protobuf_AddDesc_robotcommunication_2eproto();
  friend void protobuf_AssignDesc_robotcommunication_2eproto();
  friend void protobuf_ShutdownFile_robotcommunication_2eproto();

  void InitAsDefaultInstance();
  static OrpeSafetyStatus* default_instance_;
};
// -------------------------------------------------------------------

class protoSafeCommunicationParam : public ::google::protobuf::Message {
 public:
  protoSafeCommunicationParam();
  virtual ~protoSafeCommunicationParam();

  protoSafeCommunicationParam(const protoSafeCommunicationParam& from);

  inline protoSafeCommunicationParam& operator=(const protoSafeCommunicationParam& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const protoSafeCommunicationParam& default_instance();

  void Swap(protoSafeCommunicationParam* other);

  // implements Message ----------------------------------------------

  protoSafeCommunicationParam* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const protoSafeCommunicationParam& from);
  void MergeFrom(const protoSafeCommunicationParam& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required uint32 paramReserved = 1;
  inline bool has_paramreserved() const;
  inline void clear_paramreserved();
  static const int kParamReservedFieldNumber = 1;
  inline ::google::protobuf::uint32 paramreserved() const;
  inline void set_paramreserved(::google::protobuf::uint32 value);

  // repeated uint32 param = 2;
  inline int param_size() const;
  inline void clear_param();
  static const int kParamFieldNumber = 2;
  inline ::google::protobuf::uint32 param(int index) const;
  inline void set_param(int index, ::google::protobuf::uint32 value);
  inline void add_param(::google::protobuf::uint32 value);
  inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
      param() const;
  inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
      mutable_param();

  // @@protoc_insertion_point(class_scope:aubo.robot.communication.protoSafeCommunicationParam)
 private:
  inline void set_has_paramreserved();
  inline void clear_has_paramreserved();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 > param_;
  ::google::protobuf::uint32 paramreserved_;
  friend void  protobuf_AddDesc_robotcommunication_2eproto();
  friend void protobuf_AssignDesc_robotcommunication_2eproto();
  friend void protobuf_ShutdownFile_robotcommunication_2eproto();

  void InitAsDefaultInstance();
  static protoSafeCommunicationParam* default_instance_;
};
// -------------------------------------------------------------------

class OriginPose : public ::google::protobuf::Message {
 public:
  OriginPose();
  virtual ~OriginPose();

  OriginPose(const OriginPose& from);

  inline OriginPose& operator=(const OriginPose& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const OriginPose& default_instance();

  void Swap(OriginPose* other);

  // implements Message ----------------------------------------------

  OriginPose* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const OriginPose& from);
  void MergeFrom(const OriginPose& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required uint32 OriginPoseState = 1;
  inline bool has_originposestate() const;
  inline void clear_originposestate();
  static const int kOriginPoseStateFieldNumber = 1;
  inline ::google::protobuf::uint32 originposestate() const;
  inline void set_originposestate(::google::protobuf::uint32 value);

  // repeated float OriginPose = 2;
  inline int originpose_size() const;
  inline void clear_originpose();
  static const int kOriginPoseFieldNumber = 2;
  inline float originpose(int index) const;
  inline void set_originpose(int index, float value);
  inline void add_originpose(float value);
  inline const ::google::protobuf::RepeatedField< float >&
      originpose() const;
  inline ::google::protobuf::RepeatedField< float >*
      mutable_originpose();

  // @@protoc_insertion_point(class_scope:aubo.robot.communication.OriginPose)
 private:
  inline void set_has_originposestate();
  inline void clear_has_originposestate();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::RepeatedField< float > originpose_;
  ::google::protobuf::uint32 originposestate_;
  friend void  protobuf_AddDesc_robotcommunication_2eproto();
  friend void protobuf_AssignDesc_robotcommunication_2eproto();
  friend void protobuf_ShutdownFile_robotcommunication_2eproto();

  void InitAsDefaultInstance();
  static OriginPose* default_instance_;
};
// -------------------------------------------------------------------

class RobotToolConfig : public ::google::protobuf::Message {
 public:
  RobotToolConfig();
  virtual ~RobotToolConfig();

  RobotToolConfig(const RobotToolConfig& from);

  inline RobotToolConfig& operator=(const RobotToolConfig& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const RobotToolConfig& default_instance();

  void Swap(RobotToolConfig* other);

  // implements Message ----------------------------------------------

  RobotToolConfig* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const RobotToolConfig& from);
  void MergeFrom(const RobotToolConfig& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required uint32 config = 1;
  inline bool has_config() const;
  inline void clear_config();
  static const int kConfigFieldNumber = 1;
  inline ::google::protobuf::uint32 config() const;
  inline void set_config(::google::protobuf::uint32 value);

  // @@protoc_insertion_point(class_scope:aubo.robot.communication.RobotToolConfig)
 private:
  inline void set_has_config();
  inline void clear_has_config();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::uint32 config_;
  friend void  protobuf_AddDesc_robotcommunication_2eproto();
  friend void protobuf_AssignDesc_robotcommunication_2eproto();
  friend void protobuf_ShutdownFile_robotcommunication_2eproto();

  void InitAsDefaultInstance();
  static RobotToolConfig* default_instance_;
};
// -------------------------------------------------------------------

class ProtoRobotCommonResponse : public ::google::protobuf::Message {
 public:
  ProtoRobotCommonResponse();
  virtual ~ProtoRobotCommonResponse();

  ProtoRobotCommonResponse(const ProtoRobotCommonResponse& from);

  inline ProtoRobotCommonResponse& operator=(const ProtoRobotCommonResponse& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ProtoRobotCommonResponse& default_instance();

  void Swap(ProtoRobotCommonResponse* other);

  // implements Message ----------------------------------------------

  ProtoRobotCommonResponse* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ProtoRobotCommonResponse& from);
  void MergeFrom(const ProtoRobotCommonResponse& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required int32 errorCode = 1;
  inline bool has_errorcode() const;
  inline void clear_errorcode();
  static const int kErrorCodeFieldNumber = 1;
  inline ::google::protobuf::int32 errorcode() const;
  inline void set_errorcode(::google::protobuf::int32 value);

  // required string errorMsg = 2;
  inline bool has_errormsg() const;
  inline void clear_errormsg();
  static const int kErrorMsgFieldNumber = 2;
  inline const ::std::string& errormsg() const;
  inline void set_errormsg(const ::std::string& value);
  inline void set_errormsg(const char* value);
  inline void set_errormsg(const char* value, size_t size);
  inline ::std::string* mutable_errormsg();
  inline ::std::string* release_errormsg();
  inline void set_allocated_errormsg(::std::string* errormsg);

  // @@protoc_insertion_point(class_scope:aubo.robot.communication.ProtoRobotCommonResponse)
 private:
  inline void set_has_errorcode();
  inline void clear_has_errorcode();
  inline void set_has_errormsg();
  inline void clear_has_errormsg();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::std::string* errormsg_;
  ::google::protobuf::int32 errorcode_;
  friend void  protobuf_AddDesc_robotcommunication_2eproto();
  friend void protobuf_AssignDesc_robotcommunication_2eproto();
  friend void protobuf_ShutdownFile_robotcommunication_2eproto();

  void InitAsDefaultInstance();
  static ProtoRobotCommonResponse* default_instance_;
};
// -------------------------------------------------------------------

class ProtoRequestLogin : public ::google::protobuf::Message {
 public:
  ProtoRequestLogin();
  virtual ~ProtoRequestLogin();

  ProtoRequestLogin(const ProtoRequestLogin& from);

  inline ProtoRequestLogin& operator=(const ProtoRequestLogin& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ProtoRequestLogin& default_instance();

  void Swap(ProtoRequestLogin* other);

  // implements Message ----------------------------------------------

  ProtoRequestLogin* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ProtoRequestLogin& from);
  void MergeFrom(const ProtoRequestLogin& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required string userName = 1;
  inline bool has_username() const;
  inline void clear_username();
  static const int kUserNameFieldNumber = 1;
  inline const ::std::string& username() const;
  inline void set_username(const ::std::string& value);
  inline void set_username(const char* value);
  inline void set_username(const char* value, size_t size);
  inline ::std::string* mutable_username();
  inline ::std::string* release_username();
  inline void set_allocated_username(::std::string* username);

  // required string passwd = 2;
  inline bool has_passwd() const;
  inline void clear_passwd();
  static const int kPasswdFieldNumber = 2;
  inline const ::std::string& passwd() const;
  inline void set_passwd(const ::std::string& value);
  inline void set_passwd(const char* value);
  inline void set_passwd(const char* value, size_t size);
  inline ::std::string* mutable_passwd();
  inline ::std::string* release_passwd();
  inline void set_allocated_passwd(::std::string* passwd);

  // @@protoc_insertion_point(class_scope:aubo.robot.communication.ProtoRequestLogin)
 private:
  inline void set_has_username();
  inline void clear_has_username();
  inline void set_has_passwd();
  inline void clear_has_passwd();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::std::string* username_;
  ::std::string* passwd_;
  friend void  protobuf_AddDesc_robotcommunication_2eproto();
  friend void protobuf_AssignDesc_robotcommunication_2eproto();
  friend void protobuf_ShutdownFile_robotcommunication_2eproto();

  void InitAsDefaultInstance();
  static ProtoRequestLogin* default_instance_;
};
// -------------------------------------------------------------------

class ProtoRobotDiagnosisIODesc : public ::google::protobuf::Message {
 public:
  ProtoRobotDiagnosisIODesc();
  virtual ~ProtoRobotDiagnosisIODesc();

  ProtoRobotDiagnosisIODesc(const ProtoRobotDiagnosisIODesc& from);

  inline ProtoRobotDiagnosisIODesc& operator=(const ProtoRobotDiagnosisIODesc& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ProtoRobotDiagnosisIODesc& default_instance();

  void Swap(ProtoRobotDiagnosisIODesc* other);

  // implements Message ----------------------------------------------

  ProtoRobotDiagnosisIODesc* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ProtoRobotDiagnosisIODesc& from);
  void MergeFrom(const ProtoRobotDiagnosisIODesc& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required uint32 addr = 1;
  inline bool has_addr() const;
  inline void clear_addr();
  static const int kAddrFieldNumber = 1;
  inline ::google::protobuf::uint32 addr() const;
  inline void set_addr(::google::protobuf::uint32 value);

  // required uint32 type = 2;
  inline bool has_type() const;
  inline void clear_type();
  static const int kTypeFieldNumber = 2;
  inline ::google::protobuf::uint32 type() const;
  inline void set_type(::google::protobuf::uint32 value);

  // required uint32 value = 3;
  inline bool has_value() const;
  inline void clear_value();
  static const int kValueFieldNumber = 3;
  inline ::google::protobuf::uint32 value() const;
  inline void set_value(::google::protobuf::uint32 value);

  // @@protoc_insertion_point(class_scope:aubo.robot.communication.ProtoRobotDiagnosisIODesc)
 private:
  inline void set_has_addr();
  inline void clear_has_addr();
  inline void set_has_type();
  inline void clear_has_type();
  inline void set_has_value();
  inline void clear_has_value();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::uint32 addr_;
  ::google::protobuf::uint32 type_;
  ::google::protobuf::uint32 value_;
  friend void  protobuf_AddDesc_robotcommunication_2eproto();
  friend void protobuf_AssignDesc_robotcommunication_2eproto();
  friend void protobuf_ShutdownFile_robotcommunication_2eproto();

  void InitAsDefaultInstance();
  static ProtoRobotDiagnosisIODesc* default_instance_;
};
// -------------------------------------------------------------------

class ProtoRobotAnalogIODesc : public ::google::protobuf::Message {
 public:
  ProtoRobotAnalogIODesc();
  virtual ~ProtoRobotAnalogIODesc();

  ProtoRobotAnalogIODesc(const ProtoRobotAnalogIODesc& from);

  inline ProtoRobotAnalogIODesc& operator=(const ProtoRobotAnalogIODesc& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ProtoRobotAnalogIODesc& default_instance();

  void Swap(ProtoRobotAnalogIODesc* other);

  // implements Message ----------------------------------------------

  ProtoRobotAnalogIODesc* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ProtoRobotAnalogIODesc& from);
  void MergeFrom(const ProtoRobotAnalogIODesc& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required uint32 addr = 1;
  inline bool has_addr() const;
  inline void clear_addr();
  static const int kAddrFieldNumber = 1;
  inline ::google::protobuf::uint32 addr() const;
  inline void set_addr(::google::protobuf::uint32 value);

  // required uint32 type = 2;
  inline bool has_type() const;
  inline void clear_type();
  static const int kTypeFieldNumber = 2;
  inline ::google::protobuf::uint32 type() const;
  inline void set_type(::google::protobuf::uint32 value);

  // required float value = 3;
  inline bool has_value() const;
  inline void clear_value();
  static const int kValueFieldNumber = 3;
  inline float value() const;
  inline void set_value(float value);

  // @@protoc_insertion_point(class_scope:aubo.robot.communication.ProtoRobotAnalogIODesc)
 private:
  inline void set_has_addr();
  inline void clear_has_addr();
  inline void set_has_type();
  inline void clear_has_type();
  inline void set_has_value();
  inline void clear_has_value();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::uint32 addr_;
  ::google::protobuf::uint32 type_;
  float value_;
  friend void  protobuf_AddDesc_robotcommunication_2eproto();
  friend void protobuf_AssignDesc_robotcommunication_2eproto();
  friend void protobuf_ShutdownFile_robotcommunication_2eproto();

  void InitAsDefaultInstance();
  static ProtoRobotAnalogIODesc* default_instance_;
};
// -------------------------------------------------------------------

class ProtoCommunicationRobotDiagnosisIOData : public ::google::protobuf::Message {
 public:
  ProtoCommunicationRobotDiagnosisIOData();
  virtual ~ProtoCommunicationRobotDiagnosisIOData();

  ProtoCommunicationRobotDiagnosisIOData(const ProtoCommunicationRobotDiagnosisIOData& from);

  inline ProtoCommunicationRobotDiagnosisIOData& operator=(const ProtoCommunicationRobotDiagnosisIOData& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ProtoCommunicationRobotDiagnosisIOData& default_instance();

  void Swap(ProtoCommunicationRobotDiagnosisIOData* other);

  // implements Message ----------------------------------------------

  ProtoCommunicationRobotDiagnosisIOData* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ProtoCommunicationRobotDiagnosisIOData& from);
  void MergeFrom(const ProtoCommunicationRobotDiagnosisIOData& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required int32 type = 1;
  inline bool has_type() const;
  inline void clear_type();
  static const int kTypeFieldNumber = 1;
  inline ::google::protobuf::int32 type() const;
  inline void set_type(::google::protobuf::int32 value);

  // repeated .aubo.robot.communication.ProtoRobotCommonResponse errorInfo = 2;
  inline int errorinfo_size() const;
  inline void clear_errorinfo();
  static const int kErrorInfoFieldNumber = 2;
  inline const ::aubo::robot::communication::ProtoRobotCommonResponse& errorinfo(int index) const;
  inline ::aubo::robot::communication::ProtoRobotCommonResponse* mutable_errorinfo(int index);
  inline ::aubo::robot::communication::ProtoRobotCommonResponse* add_errorinfo();
  inline const ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::ProtoRobotCommonResponse >&
      errorinfo() const;
  inline ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::ProtoRobotCommonResponse >*
      mutable_errorinfo();

  // repeated .aubo.robot.communication.ProtoRobotDiagnosisIODesc ioDesc = 3;
  inline int iodesc_size() const;
  inline void clear_iodesc();
  static const int kIoDescFieldNumber = 3;
  inline const ::aubo::robot::communication::ProtoRobotDiagnosisIODesc& iodesc(int index) const;
  inline ::aubo::robot::communication::ProtoRobotDiagnosisIODesc* mutable_iodesc(int index);
  inline ::aubo::robot::communication::ProtoRobotDiagnosisIODesc* add_iodesc();
  inline const ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::ProtoRobotDiagnosisIODesc >&
      iodesc() const;
  inline ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::ProtoRobotDiagnosisIODesc >*
      mutable_iodesc();

  // @@protoc_insertion_point(class_scope:aubo.robot.communication.ProtoCommunicationRobotDiagnosisIOData)
 private:
  inline void set_has_type();
  inline void clear_has_type();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::ProtoRobotCommonResponse > errorinfo_;
  ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::ProtoRobotDiagnosisIODesc > iodesc_;
  ::google::protobuf::int32 type_;
  friend void  protobuf_AddDesc_robotcommunication_2eproto();
  friend void protobuf_AssignDesc_robotcommunication_2eproto();
  friend void protobuf_ShutdownFile_robotcommunication_2eproto();

  void InitAsDefaultInstance();
  static ProtoCommunicationRobotDiagnosisIOData* default_instance_;
};
// -------------------------------------------------------------------

class ProtoCommunicationRobotAnalogIOData : public ::google::protobuf::Message {
 public:
  ProtoCommunicationRobotAnalogIOData();
  virtual ~ProtoCommunicationRobotAnalogIOData();

  ProtoCommunicationRobotAnalogIOData(const ProtoCommunicationRobotAnalogIOData& from);

  inline ProtoCommunicationRobotAnalogIOData& operator=(const ProtoCommunicationRobotAnalogIOData& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ProtoCommunicationRobotAnalogIOData& default_instance();

  void Swap(ProtoCommunicationRobotAnalogIOData* other);

  // implements Message ----------------------------------------------

  ProtoCommunicationRobotAnalogIOData* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ProtoCommunicationRobotAnalogIOData& from);
  void MergeFrom(const ProtoCommunicationRobotAnalogIOData& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required int32 type = 3;
  inline bool has_type() const;
  inline void clear_type();
  static const int kTypeFieldNumber = 3;
  inline ::google::protobuf::int32 type() const;
  inline void set_type(::google::protobuf::int32 value);

  // repeated .aubo.robot.communication.ProtoRobotCommonResponse errorInfo = 2;
  inline int errorinfo_size() const;
  inline void clear_errorinfo();
  static const int kErrorInfoFieldNumber = 2;
  inline const ::aubo::robot::communication::ProtoRobotCommonResponse& errorinfo(int index) const;
  inline ::aubo::robot::communication::ProtoRobotCommonResponse* mutable_errorinfo(int index);
  inline ::aubo::robot::communication::ProtoRobotCommonResponse* add_errorinfo();
  inline const ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::ProtoRobotCommonResponse >&
      errorinfo() const;
  inline ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::ProtoRobotCommonResponse >*
      mutable_errorinfo();

  // repeated .aubo.robot.communication.ProtoRobotAnalogIODesc ioDesc = 1;
  inline int iodesc_size() const;
  inline void clear_iodesc();
  static const int kIoDescFieldNumber = 1;
  inline const ::aubo::robot::communication::ProtoRobotAnalogIODesc& iodesc(int index) const;
  inline ::aubo::robot::communication::ProtoRobotAnalogIODesc* mutable_iodesc(int index);
  inline ::aubo::robot::communication::ProtoRobotAnalogIODesc* add_iodesc();
  inline const ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::ProtoRobotAnalogIODesc >&
      iodesc() const;
  inline ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::ProtoRobotAnalogIODesc >*
      mutable_iodesc();

  // @@protoc_insertion_point(class_scope:aubo.robot.communication.ProtoCommunicationRobotAnalogIOData)
 private:
  inline void set_has_type();
  inline void clear_has_type();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::ProtoRobotCommonResponse > errorinfo_;
  ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::ProtoRobotAnalogIODesc > iodesc_;
  ::google::protobuf::int32 type_;
  friend void  protobuf_AddDesc_robotcommunication_2eproto();
  friend void protobuf_AssignDesc_robotcommunication_2eproto();
  friend void protobuf_ShutdownFile_robotcommunication_2eproto();

  void InitAsDefaultInstance();
  static ProtoCommunicationRobotAnalogIOData* default_instance_;
};
// -------------------------------------------------------------------

class ProtoCommunicationGeneralData : public ::google::protobuf::Message {
 public:
  ProtoCommunicationGeneralData();
  virtual ~ProtoCommunicationGeneralData();

  ProtoCommunicationGeneralData(const ProtoCommunicationGeneralData& from);

  inline ProtoCommunicationGeneralData& operator=(const ProtoCommunicationGeneralData& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ProtoCommunicationGeneralData& default_instance();

  void Swap(ProtoCommunicationGeneralData* other);

  // implements Message ----------------------------------------------

  ProtoCommunicationGeneralData* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ProtoCommunicationGeneralData& from);
  void MergeFrom(const ProtoCommunicationGeneralData& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated int32 property1 = 1;
  inline int property1_size() const;
  inline void clear_property1();
  static const int kProperty1FieldNumber = 1;
  inline ::google::protobuf::int32 property1(int index) const;
  inline void set_property1(int index, ::google::protobuf::int32 value);
  inline void add_property1(::google::protobuf::int32 value);
  inline const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
      property1() const;
  inline ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
      mutable_property1();

  // repeated bool property2 = 2;
  inline int property2_size() const;
  inline void clear_property2();
  static const int kProperty2FieldNumber = 2;
  inline bool property2(int index) const;
  inline void set_property2(int index, bool value);
  inline void add_property2(bool value);
  inline const ::google::protobuf::RepeatedField< bool >&
      property2() const;
  inline ::google::protobuf::RepeatedField< bool >*
      mutable_property2();

  // repeated .aubo.robot.communication.ProtoRobotCommonResponse errorInfo = 3;
  inline int errorinfo_size() const;
  inline void clear_errorinfo();
  static const int kErrorInfoFieldNumber = 3;
  inline const ::aubo::robot::communication::ProtoRobotCommonResponse& errorinfo(int index) const;
  inline ::aubo::robot::communication::ProtoRobotCommonResponse* mutable_errorinfo(int index);
  inline ::aubo::robot::communication::ProtoRobotCommonResponse* add_errorinfo();
  inline const ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::ProtoRobotCommonResponse >&
      errorinfo() const;
  inline ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::ProtoRobotCommonResponse >*
      mutable_errorinfo();

  // required int32 type = 4;
  inline bool has_type() const;
  inline void clear_type();
  static const int kTypeFieldNumber = 4;
  inline ::google::protobuf::int32 type() const;
  inline void set_type(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:aubo.robot.communication.ProtoCommunicationGeneralData)
 private:
  inline void set_has_type();
  inline void clear_has_type();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 > property1_;
  ::google::protobuf::RepeatedField< bool > property2_;
  ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::ProtoRobotCommonResponse > errorinfo_;
  ::google::protobuf::int32 type_;
  friend void  protobuf_AddDesc_robotcommunication_2eproto();
  friend void protobuf_AssignDesc_robotcommunication_2eproto();
  friend void protobuf_ShutdownFile_robotcommunication_2eproto();

  void InitAsDefaultInstance();
  static ProtoCommunicationGeneralData* default_instance_;
};
// -------------------------------------------------------------------

class ProtoCommunicationRobotDiagnosisInfo : public ::google::protobuf::Message {
 public:
  ProtoCommunicationRobotDiagnosisInfo();
  virtual ~ProtoCommunicationRobotDiagnosisInfo();

  ProtoCommunicationRobotDiagnosisInfo(const ProtoCommunicationRobotDiagnosisInfo& from);

  inline ProtoCommunicationRobotDiagnosisInfo& operator=(const ProtoCommunicationRobotDiagnosisInfo& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ProtoCommunicationRobotDiagnosisInfo& default_instance();

  void Swap(ProtoCommunicationRobotDiagnosisInfo* other);

  // implements Message ----------------------------------------------

  ProtoCommunicationRobotDiagnosisInfo* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ProtoCommunicationRobotDiagnosisInfo& from);
  void MergeFrom(const ProtoCommunicationRobotDiagnosisInfo& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required int32 num = 1;
  inline bool has_num() const;
  inline void clear_num();
  static const int kNumFieldNumber = 1;
  inline ::google::protobuf::int32 num() const;
  inline void set_num(::google::protobuf::int32 value);

  // repeated .aubo.robot.communication.RobotDiagnosis robotDiagnosis = 2;
  inline int robotdiagnosis_size() const;
  inline void clear_robotdiagnosis();
  static const int kRobotDiagnosisFieldNumber = 2;
  inline const ::aubo::robot::communication::RobotDiagnosis& robotdiagnosis(int index) const;
  inline ::aubo::robot::communication::RobotDiagnosis* mutable_robotdiagnosis(int index);
  inline ::aubo::robot::communication::RobotDiagnosis* add_robotdiagnosis();
  inline const ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::RobotDiagnosis >&
      robotdiagnosis() const;
  inline ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::RobotDiagnosis >*
      mutable_robotdiagnosis();

  // repeated .aubo.robot.communication.ProtoRobotCommonResponse errorInfo = 3;
  inline int errorinfo_size() const;
  inline void clear_errorinfo();
  static const int kErrorInfoFieldNumber = 3;
  inline const ::aubo::robot::communication::ProtoRobotCommonResponse& errorinfo(int index) const;
  inline ::aubo::robot::communication::ProtoRobotCommonResponse* mutable_errorinfo(int index);
  inline ::aubo::robot::communication::ProtoRobotCommonResponse* add_errorinfo();
  inline const ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::ProtoRobotCommonResponse >&
      errorinfo() const;
  inline ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::ProtoRobotCommonResponse >*
      mutable_errorinfo();

  // @@protoc_insertion_point(class_scope:aubo.robot.communication.ProtoCommunicationRobotDiagnosisInfo)
 private:
  inline void set_has_num();
  inline void clear_has_num();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::RobotDiagnosis > robotdiagnosis_;
  ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::ProtoRobotCommonResponse > errorinfo_;
  ::google::protobuf::int32 num_;
  friend void  protobuf_AddDesc_robotcommunication_2eproto();
  friend void protobuf_AssignDesc_robotcommunication_2eproto();
  friend void protobuf_ShutdownFile_robotcommunication_2eproto();

  void InitAsDefaultInstance();
  static ProtoCommunicationRobotDiagnosisInfo* default_instance_;
};
// -------------------------------------------------------------------

class ProtoCommunicationRobotEvent : public ::google::protobuf::Message {
 public:
  ProtoCommunicationRobotEvent();
  virtual ~ProtoCommunicationRobotEvent();

  ProtoCommunicationRobotEvent(const ProtoCommunicationRobotEvent& from);

  inline ProtoCommunicationRobotEvent& operator=(const ProtoCommunicationRobotEvent& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ProtoCommunicationRobotEvent& default_instance();

  void Swap(ProtoCommunicationRobotEvent* other);

  // implements Message ----------------------------------------------

  ProtoCommunicationRobotEvent* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ProtoCommunicationRobotEvent& from);
  void MergeFrom(const ProtoCommunicationRobotEvent& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required int32 eventType = 1;
  inline bool has_eventtype() const;
  inline void clear_eventtype();
  static const int kEventTypeFieldNumber = 1;
  inline ::google::protobuf::int32 eventtype() const;
  inline void set_eventtype(::google::protobuf::int32 value);

  // required int32 eventCode = 2;
  inline bool has_eventcode() const;
  inline void clear_eventcode();
  static const int kEventCodeFieldNumber = 2;
  inline ::google::protobuf::int32 eventcode() const;
  inline void set_eventcode(::google::protobuf::int32 value);

  // required bytes eventContent = 3;
  inline bool has_eventcontent() const;
  inline void clear_eventcontent();
  static const int kEventContentFieldNumber = 3;
  inline const ::std::string& eventcontent() const;
  inline void set_eventcontent(const ::std::string& value);
  inline void set_eventcontent(const char* value);
  inline void set_eventcontent(const void* value, size_t size);
  inline ::std::string* mutable_eventcontent();
  inline ::std::string* release_eventcontent();
  inline void set_allocated_eventcontent(::std::string* eventcontent);

  // @@protoc_insertion_point(class_scope:aubo.robot.communication.ProtoCommunicationRobotEvent)
 private:
  inline void set_has_eventtype();
  inline void clear_has_eventtype();
  inline void set_has_eventcode();
  inline void clear_has_eventcode();
  inline void set_has_eventcontent();
  inline void clear_has_eventcontent();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::int32 eventtype_;
  ::google::protobuf::int32 eventcode_;
  ::std::string* eventcontent_;
  friend void  protobuf_AddDesc_robotcommunication_2eproto();
  friend void protobuf_AssignDesc_robotcommunication_2eproto();
  friend void protobuf_ShutdownFile_robotcommunication_2eproto();

  void InitAsDefaultInstance();
  static ProtoCommunicationRobotEvent* default_instance_;
};
// -------------------------------------------------------------------

class ProtoCommunicationRobotStartupProfile : public ::google::protobuf::Message {
 public:
  ProtoCommunicationRobotStartupProfile();
  virtual ~ProtoCommunicationRobotStartupProfile();

  ProtoCommunicationRobotStartupProfile(const ProtoCommunicationRobotStartupProfile& from);

  inline ProtoCommunicationRobotStartupProfile& operator=(const ProtoCommunicationRobotStartupProfile& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ProtoCommunicationRobotStartupProfile& default_instance();

  void Swap(ProtoCommunicationRobotStartupProfile* other);

  // implements Message ----------------------------------------------

  ProtoCommunicationRobotStartupProfile* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ProtoCommunicationRobotStartupProfile& from);
  void MergeFrom(const ProtoCommunicationRobotStartupProfile& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .aubo.robot.communication.RobotTcpParam tcpParam = 1;
  inline bool has_tcpparam() const;
  inline void clear_tcpparam();
  static const int kTcpParamFieldNumber = 1;
  inline const ::aubo::robot::communication::RobotTcpParam& tcpparam() const;
  inline ::aubo::robot::communication::RobotTcpParam* mutable_tcpparam();
  inline ::aubo::robot::communication::RobotTcpParam* release_tcpparam();
  inline void set_allocated_tcpparam(::aubo::robot::communication::RobotTcpParam* tcpparam);

  // required bool readPose = 2;
  inline bool has_readpose() const;
  inline void clear_readpose();
  static const int kReadPoseFieldNumber = 2;
  inline bool readpose() const;
  inline void set_readpose(bool value);

  // required bool staticCollisionDetect = 3;
  inline bool has_staticcollisiondetect() const;
  inline void clear_staticcollisiondetect();
  static const int kStaticCollisionDetectFieldNumber = 3;
  inline bool staticcollisiondetect() const;
  inline void set_staticcollisiondetect(bool value);

  // required int32 collisionClass = 4;
  inline bool has_collisionclass() const;
  inline void clear_collisionclass();
  static const int kCollisionClassFieldNumber = 4;
  inline ::google::protobuf::int32 collisionclass() const;
  inline void set_collisionclass(::google::protobuf::int32 value);

  // required int32 maxAcc = 5;
  inline bool has_maxacc() const;
  inline void clear_maxacc();
  static const int kMaxAccFieldNumber = 5;
  inline ::google::protobuf::int32 maxacc() const;
  inline void set_maxacc(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:aubo.robot.communication.ProtoCommunicationRobotStartupProfile)
 private:
  inline void set_has_tcpparam();
  inline void clear_has_tcpparam();
  inline void set_has_readpose();
  inline void clear_has_readpose();
  inline void set_has_staticcollisiondetect();
  inline void clear_has_staticcollisiondetect();
  inline void set_has_collisionclass();
  inline void clear_has_collisionclass();
  inline void set_has_maxacc();
  inline void clear_has_maxacc();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::aubo::robot::communication::RobotTcpParam* tcpparam_;
  bool readpose_;
  bool staticcollisiondetect_;
  ::google::protobuf::int32 collisionclass_;
  ::google::protobuf::int32 maxacc_;
  friend void  protobuf_AddDesc_robotcommunication_2eproto();
  friend void protobuf_AssignDesc_robotcommunication_2eproto();
  friend void protobuf_ShutdownFile_robotcommunication_2eproto();

  void InitAsDefaultInstance();
  static ProtoCommunicationRobotStartupProfile* default_instance_;
};
// -------------------------------------------------------------------

class ProtoCommunicationOfflineExcitTraj : public ::google::protobuf::Message {
 public:
  ProtoCommunicationOfflineExcitTraj();
  virtual ~ProtoCommunicationOfflineExcitTraj();

  ProtoCommunicationOfflineExcitTraj(const ProtoCommunicationOfflineExcitTraj& from);

  inline ProtoCommunicationOfflineExcitTraj& operator=(const ProtoCommunicationOfflineExcitTraj& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ProtoCommunicationOfflineExcitTraj& default_instance();

  void Swap(ProtoCommunicationOfflineExcitTraj* other);

  // implements Message ----------------------------------------------

  ProtoCommunicationOfflineExcitTraj* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ProtoCommunicationOfflineExcitTraj& from);
  void MergeFrom(const ProtoCommunicationOfflineExcitTraj& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required string trackFile = 1;
  inline bool has_trackfile() const;
  inline void clear_trackfile();
  static const int kTrackFileFieldNumber = 1;
  inline const ::std::string& trackfile() const;
  inline void set_trackfile(const ::std::string& value);
  inline void set_trackfile(const char* value);
  inline void set_trackfile(const char* value, size_t size);
  inline ::std::string* mutable_trackfile();
  inline ::std::string* release_trackfile();
  inline void set_allocated_trackfile(::std::string* trackfile);

  // required int32 type = 2;
  inline bool has_type() const;
  inline void clear_type();
  static const int kTypeFieldNumber = 2;
  inline ::google::protobuf::int32 type() const;
  inline void set_type(::google::protobuf::int32 value);

  // required int32 subtype = 3;
  inline bool has_subtype() const;
  inline void clear_subtype();
  static const int kSubtypeFieldNumber = 3;
  inline ::google::protobuf::int32 subtype() const;
  inline void set_subtype(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:aubo.robot.communication.ProtoCommunicationOfflineExcitTraj)
 private:
  inline void set_has_trackfile();
  inline void clear_has_trackfile();
  inline void set_has_type();
  inline void clear_has_type();
  inline void set_has_subtype();
  inline void clear_has_subtype();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::std::string* trackfile_;
  ::google::protobuf::int32 type_;
  ::google::protobuf::int32 subtype_;
  friend void  protobuf_AddDesc_robotcommunication_2eproto();
  friend void protobuf_AssignDesc_robotcommunication_2eproto();
  friend void protobuf_ShutdownFile_robotcommunication_2eproto();

  void InitAsDefaultInstance();
  static ProtoCommunicationOfflineExcitTraj* default_instance_;
};
// -------------------------------------------------------------------

class ProtoCommunicationDynIdentifyResults : public ::google::protobuf::Message {
 public:
  ProtoCommunicationDynIdentifyResults();
  virtual ~ProtoCommunicationDynIdentifyResults();

  ProtoCommunicationDynIdentifyResults(const ProtoCommunicationDynIdentifyResults& from);

  inline ProtoCommunicationDynIdentifyResults& operator=(const ProtoCommunicationDynIdentifyResults& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ProtoCommunicationDynIdentifyResults& default_instance();

  void Swap(ProtoCommunicationDynIdentifyResults* other);

  // implements Message ----------------------------------------------

  ProtoCommunicationDynIdentifyResults* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ProtoCommunicationDynIdentifyResults& from);
  void MergeFrom(const ProtoCommunicationDynIdentifyResults& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required int32 temp = 1;
  inline bool has_temp() const;
  inline void clear_temp();
  static const int kTempFieldNumber = 1;
  inline ::google::protobuf::int32 temp() const;
  inline void set_temp(::google::protobuf::int32 value);

  // repeated int32 param = 2;
  inline int param_size() const;
  inline void clear_param();
  static const int kParamFieldNumber = 2;
  inline ::google::protobuf::int32 param(int index) const;
  inline void set_param(int index, ::google::protobuf::int32 value);
  inline void add_param(::google::protobuf::int32 value);
  inline const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
      param() const;
  inline ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
      mutable_param();

  // repeated .aubo.robot.communication.ProtoRobotCommonResponse errorInfo = 3;
  inline int errorinfo_size() const;
  inline void clear_errorinfo();
  static const int kErrorInfoFieldNumber = 3;
  inline const ::aubo::robot::communication::ProtoRobotCommonResponse& errorinfo(int index) const;
  inline ::aubo::robot::communication::ProtoRobotCommonResponse* mutable_errorinfo(int index);
  inline ::aubo::robot::communication::ProtoRobotCommonResponse* add_errorinfo();
  inline const ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::ProtoRobotCommonResponse >&
      errorinfo() const;
  inline ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::ProtoRobotCommonResponse >*
      mutable_errorinfo();

  // @@protoc_insertion_point(class_scope:aubo.robot.communication.ProtoCommunicationDynIdentifyResults)
 private:
  inline void set_has_temp();
  inline void clear_has_temp();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 > param_;
  ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::ProtoRobotCommonResponse > errorinfo_;
  ::google::protobuf::int32 temp_;
  friend void  protobuf_AddDesc_robotcommunication_2eproto();
  friend void protobuf_AssignDesc_robotcommunication_2eproto();
  friend void protobuf_ShutdownFile_robotcommunication_2eproto();

  void InitAsDefaultInstance();
  static ProtoCommunicationDynIdentifyResults* default_instance_;
};
// -------------------------------------------------------------------

class InterfaceBoardError : public ::google::protobuf::Message {
 public:
  InterfaceBoardError();
  virtual ~InterfaceBoardError();

  InterfaceBoardError(const InterfaceBoardError& from);

  inline InterfaceBoardError& operator=(const InterfaceBoardError& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const InterfaceBoardError& default_instance();

  void Swap(InterfaceBoardError* other);

  // implements Message ----------------------------------------------

  InterfaceBoardError* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const InterfaceBoardError& from);
  void MergeFrom(const InterfaceBoardError& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required bytes boardError = 1;
  inline bool has_boarderror() const;
  inline void clear_boarderror();
  static const int kBoardErrorFieldNumber = 1;
  inline const ::std::string& boarderror() const;
  inline void set_boarderror(const ::std::string& value);
  inline void set_boarderror(const char* value);
  inline void set_boarderror(const void* value, size_t size);
  inline ::std::string* mutable_boarderror();
  inline ::std::string* release_boarderror();
  inline void set_allocated_boarderror(::std::string* boarderror);

  // @@protoc_insertion_point(class_scope:aubo.robot.communication.InterfaceBoardError)
 private:
  inline void set_has_boarderror();
  inline void clear_has_boarderror();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::std::string* boarderror_;
  friend void  protobuf_AddDesc_robotcommunication_2eproto();
  friend void protobuf_AssignDesc_robotcommunication_2eproto();
  friend void protobuf_ShutdownFile_robotcommunication_2eproto();

  void InitAsDefaultInstance();
  static InterfaceBoardError* default_instance_;
};
// -------------------------------------------------------------------

class ModbusCfg : public ::google::protobuf::Message {
 public:
  ModbusCfg();
  virtual ~ModbusCfg();

  ModbusCfg(const ModbusCfg& from);

  inline ModbusCfg& operator=(const ModbusCfg& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ModbusCfg& default_instance();

  void Swap(ModbusCfg* other);

  // implements Message ----------------------------------------------

  ModbusCfg* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ModbusCfg& from);
  void MergeFrom(const ModbusCfg& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required string uuid = 1;
  inline bool has_uuid() const;
  inline void clear_uuid();
  static const int kUuidFieldNumber = 1;
  inline const ::std::string& uuid() const;
  inline void set_uuid(const ::std::string& value);
  inline void set_uuid(const char* value);
  inline void set_uuid(const char* value, size_t size);
  inline ::std::string* mutable_uuid();
  inline ::std::string* release_uuid();
  inline void set_allocated_uuid(::std::string* uuid);

  // required string name = 2;
  inline bool has_name() const;
  inline void clear_name();
  static const int kNameFieldNumber = 2;
  inline const ::std::string& name() const;
  inline void set_name(const ::std::string& value);
  inline void set_name(const char* value);
  inline void set_name(const char* value, size_t size);
  inline ::std::string* mutable_name();
  inline ::std::string* release_name();
  inline void set_allocated_name(::std::string* name);

  // required .aubo.robot.communication.ModbusMode mode = 3;
  inline bool has_mode() const;
  inline void clear_mode();
  static const int kModeFieldNumber = 3;
  inline ::aubo::robot::communication::ModbusMode mode() const;
  inline void set_mode(::aubo::robot::communication::ModbusMode value);

  // required uint32 slave = 4;
  inline bool has_slave() const;
  inline void clear_slave();
  static const int kSlaveFieldNumber = 4;
  inline ::google::protobuf::uint32 slave() const;
  inline void set_slave(::google::protobuf::uint32 value);

  // required string ip = 5;
  inline bool has_ip() const;
  inline void clear_ip();
  static const int kIpFieldNumber = 5;
  inline const ::std::string& ip() const;
  inline void set_ip(const ::std::string& value);
  inline void set_ip(const char* value);
  inline void set_ip(const char* value, size_t size);
  inline ::std::string* mutable_ip();
  inline ::std::string* release_ip();
  inline void set_allocated_ip(::std::string* ip);

  // required uint32 port = 6;
  inline bool has_port() const;
  inline void clear_port();
  static const int kPortFieldNumber = 6;
  inline ::google::protobuf::uint32 port() const;
  inline void set_port(::google::protobuf::uint32 value);

  // required string device = 7;
  inline bool has_device() const;
  inline void clear_device();
  static const int kDeviceFieldNumber = 7;
  inline const ::std::string& device() const;
  inline void set_device(const ::std::string& value);
  inline void set_device(const char* value);
  inline void set_device(const char* value, size_t size);
  inline ::std::string* mutable_device();
  inline ::std::string* release_device();
  inline void set_allocated_device(::std::string* device);

  // required uint32 baud = 8;
  inline bool has_baud() const;
  inline void clear_baud();
  static const int kBaudFieldNumber = 8;
  inline ::google::protobuf::uint32 baud() const;
  inline void set_baud(::google::protobuf::uint32 value);

  // required string parity = 9;
  inline bool has_parity() const;
  inline void clear_parity();
  static const int kParityFieldNumber = 9;
  inline const ::std::string& parity() const;
  inline void set_parity(const ::std::string& value);
  inline void set_parity(const char* value);
  inline void set_parity(const char* value, size_t size);
  inline ::std::string* mutable_parity();
  inline ::std::string* release_parity();
  inline void set_allocated_parity(::std::string* parity);

  // required uint32 data_bits = 10;
  inline bool has_data_bits() const;
  inline void clear_data_bits();
  static const int kDataBitsFieldNumber = 10;
  inline ::google::protobuf::uint32 data_bits() const;
  inline void set_data_bits(::google::protobuf::uint32 value);

  // required uint32 stop_bits = 11;
  inline bool has_stop_bits() const;
  inline void clear_stop_bits();
  static const int kStopBitsFieldNumber = 11;
  inline ::google::protobuf::uint32 stop_bits() const;
  inline void set_stop_bits(::google::protobuf::uint32 value);

  // required uint32 response = 12;
  inline bool has_response() const;
  inline void clear_response();
  static const int kResponseFieldNumber = 12;
  inline ::google::protobuf::uint32 response() const;
  inline void set_response(::google::protobuf::uint32 value);

  // required uint32 frequency = 13;
  inline bool has_frequency() const;
  inline void clear_frequency();
  static const int kFrequencyFieldNumber = 13;
  inline ::google::protobuf::uint32 frequency() const;
  inline void set_frequency(::google::protobuf::uint32 value);

  // @@protoc_insertion_point(class_scope:aubo.robot.communication.ModbusCfg)
 private:
  inline void set_has_uuid();
  inline void clear_has_uuid();
  inline void set_has_name();
  inline void clear_has_name();
  inline void set_has_mode();
  inline void clear_has_mode();
  inline void set_has_slave();
  inline void clear_has_slave();
  inline void set_has_ip();
  inline void clear_has_ip();
  inline void set_has_port();
  inline void clear_has_port();
  inline void set_has_device();
  inline void clear_has_device();
  inline void set_has_baud();
  inline void clear_has_baud();
  inline void set_has_parity();
  inline void clear_has_parity();
  inline void set_has_data_bits();
  inline void clear_has_data_bits();
  inline void set_has_stop_bits();
  inline void clear_has_stop_bits();
  inline void set_has_response();
  inline void clear_has_response();
  inline void set_has_frequency();
  inline void clear_has_frequency();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::std::string* uuid_;
  ::std::string* name_;
  int mode_;
  ::google::protobuf::uint32 slave_;
  ::std::string* ip_;
  ::std::string* device_;
  ::google::protobuf::uint32 port_;
  ::google::protobuf::uint32 baud_;
  ::std::string* parity_;
  ::google::protobuf::uint32 data_bits_;
  ::google::protobuf::uint32 stop_bits_;
  ::google::protobuf::uint32 response_;
  ::google::protobuf::uint32 frequency_;
  friend void  protobuf_AddDesc_robotcommunication_2eproto();
  friend void protobuf_AssignDesc_robotcommunication_2eproto();
  friend void protobuf_ShutdownFile_robotcommunication_2eproto();

  void InitAsDefaultInstance();
  static ModbusCfg* default_instance_;
};
// -------------------------------------------------------------------

class TagIoCfg : public ::google::protobuf::Message {
 public:
  TagIoCfg();
  virtual ~TagIoCfg();

  TagIoCfg(const TagIoCfg& from);

  inline TagIoCfg& operator=(const TagIoCfg& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const TagIoCfg& default_instance();

  void Swap(TagIoCfg* other);

  // implements Message ----------------------------------------------

  TagIoCfg* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const TagIoCfg& from);
  void MergeFrom(const TagIoCfg& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required string io_name = 1;
  inline bool has_io_name() const;
  inline void clear_io_name();
  static const int kIoNameFieldNumber = 1;
  inline const ::std::string& io_name() const;
  inline void set_io_name(const ::std::string& value);
  inline void set_io_name(const char* value);
  inline void set_io_name(const char* value, size_t size);
  inline ::std::string* mutable_io_name();
  inline ::std::string* release_io_name();
  inline void set_allocated_io_name(::std::string* io_name);

  // required .aubo.robot.communication.TagIoType io_type = 2;
  inline bool has_io_type() const;
  inline void clear_io_type();
  static const int kIoTypeFieldNumber = 2;
  inline ::aubo::robot::communication::TagIoType io_type() const;
  inline void set_io_type(::aubo::robot::communication::TagIoType value);

  // required uint32 register_addr = 3;
  inline bool has_register_addr() const;
  inline void clear_register_addr();
  static const int kRegisterAddrFieldNumber = 3;
  inline ::google::protobuf::uint32 register_addr() const;
  inline void set_register_addr(::google::protobuf::uint32 value);

  // required string modbus_uuid = 4;
  inline bool has_modbus_uuid() const;
  inline void clear_modbus_uuid();
  static const int kModbusUuidFieldNumber = 4;
  inline const ::std::string& modbus_uuid() const;
  inline void set_modbus_uuid(const ::std::string& value);
  inline void set_modbus_uuid(const char* value);
  inline void set_modbus_uuid(const char* value, size_t size);
  inline ::std::string* mutable_modbus_uuid();
  inline ::std::string* release_modbus_uuid();
  inline void set_allocated_modbus_uuid(::std::string* modbus_uuid);

  // required string io_id = 5;
  inline bool has_io_id() const;
  inline void clear_io_id();
  static const int kIoIdFieldNumber = 5;
  inline const ::std::string& io_id() const;
  inline void set_io_id(const ::std::string& value);
  inline void set_io_id(const char* value);
  inline void set_io_id(const char* value, size_t size);
  inline ::std::string* mutable_io_id();
  inline ::std::string* release_io_id();
  inline void set_allocated_io_id(::std::string* io_id);

  // required double io_value = 6;
  inline bool has_io_value() const;
  inline void clear_io_value();
  static const int kIoValueFieldNumber = 6;
  inline double io_value() const;
  inline void set_io_value(double value);

  // @@protoc_insertion_point(class_scope:aubo.robot.communication.TagIoCfg)
 private:
  inline void set_has_io_name();
  inline void clear_has_io_name();
  inline void set_has_io_type();
  inline void clear_has_io_type();
  inline void set_has_register_addr();
  inline void clear_has_register_addr();
  inline void set_has_modbus_uuid();
  inline void clear_has_modbus_uuid();
  inline void set_has_io_id();
  inline void clear_has_io_id();
  inline void set_has_io_value();
  inline void clear_has_io_value();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::std::string* io_name_;
  int io_type_;
  ::google::protobuf::uint32 register_addr_;
  ::std::string* modbus_uuid_;
  ::std::string* io_id_;
  double io_value_;
  friend void  protobuf_AddDesc_robotcommunication_2eproto();
  friend void protobuf_AssignDesc_robotcommunication_2eproto();
  friend void protobuf_ShutdownFile_robotcommunication_2eproto();

  void InitAsDefaultInstance();
  static TagIoCfg* default_instance_;
};
// -------------------------------------------------------------------

class ToolDigitalStatus : public ::google::protobuf::Message {
 public:
  ToolDigitalStatus();
  virtual ~ToolDigitalStatus();

  ToolDigitalStatus(const ToolDigitalStatus& from);

  inline ToolDigitalStatus& operator=(const ToolDigitalStatus& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ToolDigitalStatus& default_instance();

  void Swap(ToolDigitalStatus* other);

  // implements Message ----------------------------------------------

  ToolDigitalStatus* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ToolDigitalStatus& from);
  void MergeFrom(const ToolDigitalStatus& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required uint32 ioType = 1;
  inline bool has_iotype() const;
  inline void clear_iotype();
  static const int kIoTypeFieldNumber = 1;
  inline ::google::protobuf::uint32 iotype() const;
  inline void set_iotype(::google::protobuf::uint32 value);

  // required uint32 ioData = 2;
  inline bool has_iodata() const;
  inline void clear_iodata();
  static const int kIoDataFieldNumber = 2;
  inline ::google::protobuf::uint32 iodata() const;
  inline void set_iodata(::google::protobuf::uint32 value);

  // @@protoc_insertion_point(class_scope:aubo.robot.communication.ToolDigitalStatus)
 private:
  inline void set_has_iotype();
  inline void clear_has_iotype();
  inline void set_has_iodata();
  inline void clear_has_iodata();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::uint32 iotype_;
  ::google::protobuf::uint32 iodata_;
  friend void  protobuf_AddDesc_robotcommunication_2eproto();
  friend void protobuf_AssignDesc_robotcommunication_2eproto();
  friend void protobuf_ShutdownFile_robotcommunication_2eproto();

  void InitAsDefaultInstance();
  static ToolDigitalStatus* default_instance_;
};
// -------------------------------------------------------------------

class RobotToolStatus : public ::google::protobuf::Message {
 public:
  RobotToolStatus();
  virtual ~RobotToolStatus();

  RobotToolStatus(const RobotToolStatus& from);

  inline RobotToolStatus& operator=(const RobotToolStatus& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const RobotToolStatus& default_instance();

  void Swap(RobotToolStatus* other);

  // implements Message ----------------------------------------------

  RobotToolStatus* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const RobotToolStatus& from);
  void MergeFrom(const RobotToolStatus& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required uint32 ioData = 1;
  inline bool has_iodata() const;
  inline void clear_iodata();
  static const int kIoDataFieldNumber = 1;
  inline ::google::protobuf::uint32 iodata() const;
  inline void set_iodata(::google::protobuf::uint32 value);

  // repeated float aiData = 2;
  inline int aidata_size() const;
  inline void clear_aidata();
  static const int kAiDataFieldNumber = 2;
  inline float aidata(int index) const;
  inline void set_aidata(int index, float value);
  inline void add_aidata(float value);
  inline const ::google::protobuf::RepeatedField< float >&
      aidata() const;
  inline ::google::protobuf::RepeatedField< float >*
      mutable_aidata();

  // required float systemVoltage = 3;
  inline bool has_systemvoltage() const;
  inline void clear_systemvoltage();
  static const int kSystemVoltageFieldNumber = 3;
  inline float systemvoltage() const;
  inline void set_systemvoltage(float value);

  // required float systemTemperature = 4;
  inline bool has_systemtemperature() const;
  inline void clear_systemtemperature();
  static const int kSystemTemperatureFieldNumber = 4;
  inline float systemtemperature() const;
  inline void set_systemtemperature(float value);

  // required uint32 errorStatus = 5;
  inline bool has_errorstatus() const;
  inline void clear_errorstatus();
  static const int kErrorStatusFieldNumber = 5;
  inline ::google::protobuf::uint32 errorstatus() const;
  inline void set_errorstatus(::google::protobuf::uint32 value);

  // @@protoc_insertion_point(class_scope:aubo.robot.communication.RobotToolStatus)
 private:
  inline void set_has_iodata();
  inline void clear_has_iodata();
  inline void set_has_systemvoltage();
  inline void clear_has_systemvoltage();
  inline void set_has_systemtemperature();
  inline void clear_has_systemtemperature();
  inline void set_has_errorstatus();
  inline void clear_has_errorstatus();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::RepeatedField< float > aidata_;
  ::google::protobuf::uint32 iodata_;
  float systemvoltage_;
  float systemtemperature_;
  ::google::protobuf::uint32 errorstatus_;
  friend void  protobuf_AddDesc_robotcommunication_2eproto();
  friend void protobuf_AssignDesc_robotcommunication_2eproto();
  friend void protobuf_ShutdownFile_robotcommunication_2eproto();

  void InitAsDefaultInstance();
  static RobotToolStatus* default_instance_;
};
// -------------------------------------------------------------------

class toolAllIOStatus : public ::google::protobuf::Message {
 public:
  toolAllIOStatus();
  virtual ~toolAllIOStatus();

  toolAllIOStatus(const toolAllIOStatus& from);

  inline toolAllIOStatus& operator=(const toolAllIOStatus& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const toolAllIOStatus& default_instance();

  void Swap(toolAllIOStatus* other);

  // implements Message ----------------------------------------------

  toolAllIOStatus* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const toolAllIOStatus& from);
  void MergeFrom(const toolAllIOStatus& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required uint32 powerType = 1;
  inline bool has_powertype() const;
  inline void clear_powertype();
  static const int kPowerTypeFieldNumber = 1;
  inline ::google::protobuf::uint32 powertype() const;
  inline void set_powertype(::google::protobuf::uint32 value);

  // required double systemVoltage = 2;
  inline bool has_systemvoltage() const;
  inline void clear_systemvoltage();
  static const int kSystemVoltageFieldNumber = 2;
  inline double systemvoltage() const;
  inline void set_systemvoltage(double value);

  // required double systemTemperature = 3;
  inline bool has_systemtemperature() const;
  inline void clear_systemtemperature();
  static const int kSystemTemperatureFieldNumber = 3;
  inline double systemtemperature() const;
  inline void set_systemtemperature(double value);

  // required uint32 errorStatus = 4;
  inline bool has_errorstatus() const;
  inline void clear_errorstatus();
  static const int kErrorStatusFieldNumber = 4;
  inline ::google::protobuf::uint32 errorstatus() const;
  inline void set_errorstatus(::google::protobuf::uint32 value);

  // repeated .aubo.robot.communication.ToolDigitalStatus digitalIoStatus = 5;
  inline int digitaliostatus_size() const;
  inline void clear_digitaliostatus();
  static const int kDigitalIoStatusFieldNumber = 5;
  inline const ::aubo::robot::communication::ToolDigitalStatus& digitaliostatus(int index) const;
  inline ::aubo::robot::communication::ToolDigitalStatus* mutable_digitaliostatus(int index);
  inline ::aubo::robot::communication::ToolDigitalStatus* add_digitaliostatus();
  inline const ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::ToolDigitalStatus >&
      digitaliostatus() const;
  inline ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::ToolDigitalStatus >*
      mutable_digitaliostatus();

  // repeated double aiData = 6;
  inline int aidata_size() const;
  inline void clear_aidata();
  static const int kAiDataFieldNumber = 6;
  inline double aidata(int index) const;
  inline void set_aidata(int index, double value);
  inline void add_aidata(double value);
  inline const ::google::protobuf::RepeatedField< double >&
      aidata() const;
  inline ::google::protobuf::RepeatedField< double >*
      mutable_aidata();

  // @@protoc_insertion_point(class_scope:aubo.robot.communication.toolAllIOStatus)
 private:
  inline void set_has_powertype();
  inline void clear_has_powertype();
  inline void set_has_systemvoltage();
  inline void clear_has_systemvoltage();
  inline void set_has_systemtemperature();
  inline void clear_has_systemtemperature();
  inline void set_has_errorstatus();
  inline void clear_has_errorstatus();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  double systemvoltage_;
  ::google::protobuf::uint32 powertype_;
  ::google::protobuf::uint32 errorstatus_;
  double systemtemperature_;
  ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::ToolDigitalStatus > digitaliostatus_;
  ::google::protobuf::RepeatedField< double > aidata_;
  friend void  protobuf_AddDesc_robotcommunication_2eproto();
  friend void protobuf_AssignDesc_robotcommunication_2eproto();
  friend void protobuf_ShutdownFile_robotcommunication_2eproto();

  void InitAsDefaultInstance();
  static toolAllIOStatus* default_instance_;
};
// -------------------------------------------------------------------

class ProtoCommunicationToolAllIOStatusInfoResponse : public ::google::protobuf::Message {
 public:
  ProtoCommunicationToolAllIOStatusInfoResponse();
  virtual ~ProtoCommunicationToolAllIOStatusInfoResponse();

  ProtoCommunicationToolAllIOStatusInfoResponse(const ProtoCommunicationToolAllIOStatusInfoResponse& from);

  inline ProtoCommunicationToolAllIOStatusInfoResponse& operator=(const ProtoCommunicationToolAllIOStatusInfoResponse& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ProtoCommunicationToolAllIOStatusInfoResponse& default_instance();

  void Swap(ProtoCommunicationToolAllIOStatusInfoResponse* other);

  // implements Message ----------------------------------------------

  ProtoCommunicationToolAllIOStatusInfoResponse* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ProtoCommunicationToolAllIOStatusInfoResponse& from);
  void MergeFrom(const ProtoCommunicationToolAllIOStatusInfoResponse& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .aubo.robot.communication.toolAllIOStatus toolIOStatus = 1;
  inline bool has_tooliostatus() const;
  inline void clear_tooliostatus();
  static const int kToolIOStatusFieldNumber = 1;
  inline const ::aubo::robot::communication::toolAllIOStatus& tooliostatus() const;
  inline ::aubo::robot::communication::toolAllIOStatus* mutable_tooliostatus();
  inline ::aubo::robot::communication::toolAllIOStatus* release_tooliostatus();
  inline void set_allocated_tooliostatus(::aubo::robot::communication::toolAllIOStatus* tooliostatus);

  // required .aubo.robot.communication.ProtoRobotCommonResponse errorInfo = 2;
  inline bool has_errorinfo() const;
  inline void clear_errorinfo();
  static const int kErrorInfoFieldNumber = 2;
  inline const ::aubo::robot::communication::ProtoRobotCommonResponse& errorinfo() const;
  inline ::aubo::robot::communication::ProtoRobotCommonResponse* mutable_errorinfo();
  inline ::aubo::robot::communication::ProtoRobotCommonResponse* release_errorinfo();
  inline void set_allocated_errorinfo(::aubo::robot::communication::ProtoRobotCommonResponse* errorinfo);

  // @@protoc_insertion_point(class_scope:aubo.robot.communication.ProtoCommunicationToolAllIOStatusInfoResponse)
 private:
  inline void set_has_tooliostatus();
  inline void clear_has_tooliostatus();
  inline void set_has_errorinfo();
  inline void clear_has_errorinfo();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::aubo::robot::communication::toolAllIOStatus* tooliostatus_;
  ::aubo::robot::communication::ProtoRobotCommonResponse* errorinfo_;
  friend void  protobuf_AddDesc_robotcommunication_2eproto();
  friend void protobuf_AssignDesc_robotcommunication_2eproto();
  friend void protobuf_ShutdownFile_robotcommunication_2eproto();

  void InitAsDefaultInstance();
  static ProtoCommunicationToolAllIOStatusInfoResponse* default_instance_;
};
// -------------------------------------------------------------------

class ProtoCommunicationRobotBoardFirmware : public ::google::protobuf::Message {
 public:
  ProtoCommunicationRobotBoardFirmware();
  virtual ~ProtoCommunicationRobotBoardFirmware();

  ProtoCommunicationRobotBoardFirmware(const ProtoCommunicationRobotBoardFirmware& from);

  inline ProtoCommunicationRobotBoardFirmware& operator=(const ProtoCommunicationRobotBoardFirmware& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ProtoCommunicationRobotBoardFirmware& default_instance();

  void Swap(ProtoCommunicationRobotBoardFirmware* other);

  // implements Message ----------------------------------------------

  ProtoCommunicationRobotBoardFirmware* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ProtoCommunicationRobotBoardFirmware& from);
  void MergeFrom(const ProtoCommunicationRobotBoardFirmware& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required int32 command = 1;
  inline bool has_command() const;
  inline void clear_command();
  static const int kCommandFieldNumber = 1;
  inline ::google::protobuf::int32 command() const;
  inline void set_command(::google::protobuf::int32 value);

  // required bytes firmwareContent = 2;
  inline bool has_firmwarecontent() const;
  inline void clear_firmwarecontent();
  static const int kFirmwareContentFieldNumber = 2;
  inline const ::std::string& firmwarecontent() const;
  inline void set_firmwarecontent(const ::std::string& value);
  inline void set_firmwarecontent(const char* value);
  inline void set_firmwarecontent(const void* value, size_t size);
  inline ::std::string* mutable_firmwarecontent();
  inline ::std::string* release_firmwarecontent();
  inline void set_allocated_firmwarecontent(::std::string* firmwarecontent);

  // required int32 firmwareContentSize = 3;
  inline bool has_firmwarecontentsize() const;
  inline void clear_firmwarecontentsize();
  static const int kFirmwareContentSizeFieldNumber = 3;
  inline ::google::protobuf::int32 firmwarecontentsize() const;
  inline void set_firmwarecontentsize(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:aubo.robot.communication.ProtoCommunicationRobotBoardFirmware)
 private:
  inline void set_has_command();
  inline void clear_has_command();
  inline void set_has_firmwarecontent();
  inline void clear_has_firmwarecontent();
  inline void set_has_firmwarecontentsize();
  inline void clear_has_firmwarecontentsize();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::std::string* firmwarecontent_;
  ::google::protobuf::int32 command_;
  ::google::protobuf::int32 firmwarecontentsize_;
  friend void  protobuf_AddDesc_robotcommunication_2eproto();
  friend void protobuf_AssignDesc_robotcommunication_2eproto();
  friend void protobuf_ShutdownFile_robotcommunication_2eproto();

  void InitAsDefaultInstance();
  static ProtoCommunicationRobotBoardFirmware* default_instance_;
};
// -------------------------------------------------------------------

class ProtoCommunicationEthernetDeviceNameResponse : public ::google::protobuf::Message {
 public:
  ProtoCommunicationEthernetDeviceNameResponse();
  virtual ~ProtoCommunicationEthernetDeviceNameResponse();

  ProtoCommunicationEthernetDeviceNameResponse(const ProtoCommunicationEthernetDeviceNameResponse& from);

  inline ProtoCommunicationEthernetDeviceNameResponse& operator=(const ProtoCommunicationEthernetDeviceNameResponse& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ProtoCommunicationEthernetDeviceNameResponse& default_instance();

  void Swap(ProtoCommunicationEthernetDeviceNameResponse* other);

  // implements Message ----------------------------------------------

  ProtoCommunicationEthernetDeviceNameResponse* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ProtoCommunicationEthernetDeviceNameResponse& from);
  void MergeFrom(const ProtoCommunicationEthernetDeviceNameResponse& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required string name = 1;
  inline bool has_name() const;
  inline void clear_name();
  static const int kNameFieldNumber = 1;
  inline const ::std::string& name() const;
  inline void set_name(const ::std::string& value);
  inline void set_name(const char* value);
  inline void set_name(const char* value, size_t size);
  inline ::std::string* mutable_name();
  inline ::std::string* release_name();
  inline void set_allocated_name(::std::string* name);

  // required .aubo.robot.communication.ProtoRobotCommonResponse errorInfo = 2;
  inline bool has_errorinfo() const;
  inline void clear_errorinfo();
  static const int kErrorInfoFieldNumber = 2;
  inline const ::aubo::robot::communication::ProtoRobotCommonResponse& errorinfo() const;
  inline ::aubo::robot::communication::ProtoRobotCommonResponse* mutable_errorinfo();
  inline ::aubo::robot::communication::ProtoRobotCommonResponse* release_errorinfo();
  inline void set_allocated_errorinfo(::aubo::robot::communication::ProtoRobotCommonResponse* errorinfo);

  // @@protoc_insertion_point(class_scope:aubo.robot.communication.ProtoCommunicationEthernetDeviceNameResponse)
 private:
  inline void set_has_name();
  inline void clear_has_name();
  inline void set_has_errorinfo();
  inline void clear_has_errorinfo();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::std::string* name_;
  ::aubo::robot::communication::ProtoRobotCommonResponse* errorinfo_;
  friend void  protobuf_AddDesc_robotcommunication_2eproto();
  friend void protobuf_AssignDesc_robotcommunication_2eproto();
  friend void protobuf_ShutdownFile_robotcommunication_2eproto();

  void InitAsDefaultInstance();
  static ProtoCommunicationEthernetDeviceNameResponse* default_instance_;
};
// -------------------------------------------------------------------

class ProtoCommunicationDoubleVector : public ::google::protobuf::Message {
 public:
  ProtoCommunicationDoubleVector();
  virtual ~ProtoCommunicationDoubleVector();

  ProtoCommunicationDoubleVector(const ProtoCommunicationDoubleVector& from);

  inline ProtoCommunicationDoubleVector& operator=(const ProtoCommunicationDoubleVector& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ProtoCommunicationDoubleVector& default_instance();

  void Swap(ProtoCommunicationDoubleVector* other);

  // implements Message ----------------------------------------------

  ProtoCommunicationDoubleVector* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ProtoCommunicationDoubleVector& from);
  void MergeFrom(const ProtoCommunicationDoubleVector& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required int32 num = 1;
  inline bool has_num() const;
  inline void clear_num();
  static const int kNumFieldNumber = 1;
  inline ::google::protobuf::int32 num() const;
  inline void set_num(::google::protobuf::int32 value);

  // repeated double value = 2;
  inline int value_size() const;
  inline void clear_value();
  static const int kValueFieldNumber = 2;
  inline double value(int index) const;
  inline void set_value(int index, double value);
  inline void add_value(double value);
  inline const ::google::protobuf::RepeatedField< double >&
      value() const;
  inline ::google::protobuf::RepeatedField< double >*
      mutable_value();

  // @@protoc_insertion_point(class_scope:aubo.robot.communication.ProtoCommunicationDoubleVector)
 private:
  inline void set_has_num();
  inline void clear_has_num();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::RepeatedField< double > value_;
  ::google::protobuf::int32 num_;
  friend void  protobuf_AddDesc_robotcommunication_2eproto();
  friend void protobuf_AssignDesc_robotcommunication_2eproto();
  friend void protobuf_ShutdownFile_robotcommunication_2eproto();

  void InitAsDefaultInstance();
  static ProtoCommunicationDoubleVector* default_instance_;
};
// -------------------------------------------------------------------

class ProtoCommunicationIntVector : public ::google::protobuf::Message {
 public:
  ProtoCommunicationIntVector();
  virtual ~ProtoCommunicationIntVector();

  ProtoCommunicationIntVector(const ProtoCommunicationIntVector& from);

  inline ProtoCommunicationIntVector& operator=(const ProtoCommunicationIntVector& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ProtoCommunicationIntVector& default_instance();

  void Swap(ProtoCommunicationIntVector* other);

  // implements Message ----------------------------------------------

  ProtoCommunicationIntVector* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ProtoCommunicationIntVector& from);
  void MergeFrom(const ProtoCommunicationIntVector& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required int32 num = 1;
  inline bool has_num() const;
  inline void clear_num();
  static const int kNumFieldNumber = 1;
  inline ::google::protobuf::int32 num() const;
  inline void set_num(::google::protobuf::int32 value);

  // repeated int32 value = 2;
  inline int value_size() const;
  inline void clear_value();
  static const int kValueFieldNumber = 2;
  inline ::google::protobuf::int32 value(int index) const;
  inline void set_value(int index, ::google::protobuf::int32 value);
  inline void add_value(::google::protobuf::int32 value);
  inline const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
      value() const;
  inline ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
      mutable_value();

  // @@protoc_insertion_point(class_scope:aubo.robot.communication.ProtoCommunicationIntVector)
 private:
  inline void set_has_num();
  inline void clear_has_num();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 > value_;
  ::google::protobuf::int32 num_;
  friend void  protobuf_AddDesc_robotcommunication_2eproto();
  friend void protobuf_AssignDesc_robotcommunication_2eproto();
  friend void protobuf_ShutdownFile_robotcommunication_2eproto();

  void InitAsDefaultInstance();
  static ProtoCommunicationIntVector* default_instance_;
};
// -------------------------------------------------------------------

class ProtoCommunicationDoubleVectorResponse : public ::google::protobuf::Message {
 public:
  ProtoCommunicationDoubleVectorResponse();
  virtual ~ProtoCommunicationDoubleVectorResponse();

  ProtoCommunicationDoubleVectorResponse(const ProtoCommunicationDoubleVectorResponse& from);

  inline ProtoCommunicationDoubleVectorResponse& operator=(const ProtoCommunicationDoubleVectorResponse& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ProtoCommunicationDoubleVectorResponse& default_instance();

  void Swap(ProtoCommunicationDoubleVectorResponse* other);

  // implements Message ----------------------------------------------

  ProtoCommunicationDoubleVectorResponse* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ProtoCommunicationDoubleVectorResponse& from);
  void MergeFrom(const ProtoCommunicationDoubleVectorResponse& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required int32 num = 1;
  inline bool has_num() const;
  inline void clear_num();
  static const int kNumFieldNumber = 1;
  inline ::google::protobuf::int32 num() const;
  inline void set_num(::google::protobuf::int32 value);

  // repeated double value = 2;
  inline int value_size() const;
  inline void clear_value();
  static const int kValueFieldNumber = 2;
  inline double value(int index) const;
  inline void set_value(int index, double value);
  inline void add_value(double value);
  inline const ::google::protobuf::RepeatedField< double >&
      value() const;
  inline ::google::protobuf::RepeatedField< double >*
      mutable_value();

  // required .aubo.robot.communication.ProtoRobotCommonResponse errorInfo = 3;
  inline bool has_errorinfo() const;
  inline void clear_errorinfo();
  static const int kErrorInfoFieldNumber = 3;
  inline const ::aubo::robot::communication::ProtoRobotCommonResponse& errorinfo() const;
  inline ::aubo::robot::communication::ProtoRobotCommonResponse* mutable_errorinfo();
  inline ::aubo::robot::communication::ProtoRobotCommonResponse* release_errorinfo();
  inline void set_allocated_errorinfo(::aubo::robot::communication::ProtoRobotCommonResponse* errorinfo);

  // @@protoc_insertion_point(class_scope:aubo.robot.communication.ProtoCommunicationDoubleVectorResponse)
 private:
  inline void set_has_num();
  inline void clear_has_num();
  inline void set_has_errorinfo();
  inline void clear_has_errorinfo();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::RepeatedField< double > value_;
  ::aubo::robot::communication::ProtoRobotCommonResponse* errorinfo_;
  ::google::protobuf::int32 num_;
  friend void  protobuf_AddDesc_robotcommunication_2eproto();
  friend void protobuf_AssignDesc_robotcommunication_2eproto();
  friend void protobuf_ShutdownFile_robotcommunication_2eproto();

  void InitAsDefaultInstance();
  static ProtoCommunicationDoubleVectorResponse* default_instance_;
};
// -------------------------------------------------------------------

class ProtoCommunicationIntVectorResponse : public ::google::protobuf::Message {
 public:
  ProtoCommunicationIntVectorResponse();
  virtual ~ProtoCommunicationIntVectorResponse();

  ProtoCommunicationIntVectorResponse(const ProtoCommunicationIntVectorResponse& from);

  inline ProtoCommunicationIntVectorResponse& operator=(const ProtoCommunicationIntVectorResponse& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ProtoCommunicationIntVectorResponse& default_instance();

  void Swap(ProtoCommunicationIntVectorResponse* other);

  // implements Message ----------------------------------------------

  ProtoCommunicationIntVectorResponse* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ProtoCommunicationIntVectorResponse& from);
  void MergeFrom(const ProtoCommunicationIntVectorResponse& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required int32 num = 1;
  inline bool has_num() const;
  inline void clear_num();
  static const int kNumFieldNumber = 1;
  inline ::google::protobuf::int32 num() const;
  inline void set_num(::google::protobuf::int32 value);

  // repeated int32 value = 2;
  inline int value_size() const;
  inline void clear_value();
  static const int kValueFieldNumber = 2;
  inline ::google::protobuf::int32 value(int index) const;
  inline void set_value(int index, ::google::protobuf::int32 value);
  inline void add_value(::google::protobuf::int32 value);
  inline const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
      value() const;
  inline ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
      mutable_value();

  // required .aubo.robot.communication.ProtoRobotCommonResponse errorInfo = 3;
  inline bool has_errorinfo() const;
  inline void clear_errorinfo();
  static const int kErrorInfoFieldNumber = 3;
  inline const ::aubo::robot::communication::ProtoRobotCommonResponse& errorinfo() const;
  inline ::aubo::robot::communication::ProtoRobotCommonResponse* mutable_errorinfo();
  inline ::aubo::robot::communication::ProtoRobotCommonResponse* release_errorinfo();
  inline void set_allocated_errorinfo(::aubo::robot::communication::ProtoRobotCommonResponse* errorinfo);

  // @@protoc_insertion_point(class_scope:aubo.robot.communication.ProtoCommunicationIntVectorResponse)
 private:
  inline void set_has_num();
  inline void clear_has_num();
  inline void set_has_errorinfo();
  inline void clear_has_errorinfo();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 > value_;
  ::aubo::robot::communication::ProtoRobotCommonResponse* errorinfo_;
  ::google::protobuf::int32 num_;
  friend void  protobuf_AddDesc_robotcommunication_2eproto();
  friend void protobuf_AssignDesc_robotcommunication_2eproto();
  friend void protobuf_ShutdownFile_robotcommunication_2eproto();

  void InitAsDefaultInstance();
  static ProtoCommunicationIntVectorResponse* default_instance_;
};
// -------------------------------------------------------------------

class ProtoJointCommonData : public ::google::protobuf::Message {
 public:
  ProtoJointCommonData();
  virtual ~ProtoJointCommonData();

  ProtoJointCommonData(const ProtoJointCommonData& from);

  inline ProtoJointCommonData& operator=(const ProtoJointCommonData& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ProtoJointCommonData& default_instance();

  void Swap(ProtoJointCommonData* other);

  // implements Message ----------------------------------------------

  ProtoJointCommonData* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ProtoJointCommonData& from);
  void MergeFrom(const ProtoJointCommonData& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required int32 JointCurVol = 1;
  inline bool has_jointcurvol() const;
  inline void clear_jointcurvol();
  static const int kJointCurVolFieldNumber = 1;
  inline ::google::protobuf::int32 jointcurvol() const;
  inline void set_jointcurvol(::google::protobuf::int32 value);

  // required int32 JointCurTemp = 2;
  inline bool has_jointcurtemp() const;
  inline void clear_jointcurtemp();
  static const int kJointCurTempFieldNumber = 2;
  inline ::google::protobuf::int32 jointcurtemp() const;
  inline void set_jointcurtemp(::google::protobuf::int32 value);

  // required int32 JointWorkMode = 3;
  inline bool has_jointworkmode() const;
  inline void clear_jointworkmode();
  static const int kJointWorkModeFieldNumber = 3;
  inline ::google::protobuf::int32 jointworkmode() const;
  inline void set_jointworkmode(::google::protobuf::int32 value);

  // required int32 JointDriEnable = 4;
  inline bool has_jointdrienable() const;
  inline void clear_jointdrienable();
  static const int kJointDriEnableFieldNumber = 4;
  inline ::google::protobuf::int32 jointdrienable() const;
  inline void set_jointdrienable(::google::protobuf::int32 value);

  // required int32 JointOpenPwm = 5;
  inline bool has_jointopenpwm() const;
  inline void clear_jointopenpwm();
  static const int kJointOpenPwmFieldNumber = 5;
  inline ::google::protobuf::int32 jointopenpwm() const;
  inline void set_jointopenpwm(::google::protobuf::int32 value);

  // required int32 JointTagCurrent = 6;
  inline bool has_jointtagcurrent() const;
  inline void clear_jointtagcurrent();
  static const int kJointTagCurrentFieldNumber = 6;
  inline ::google::protobuf::int32 jointtagcurrent() const;
  inline void set_jointtagcurrent(::google::protobuf::int32 value);

  // required int32 JointTagSpeed = 7;
  inline bool has_jointtagspeed() const;
  inline void clear_jointtagspeed();
  static const int kJointTagSpeedFieldNumber = 7;
  inline ::google::protobuf::int32 jointtagspeed() const;
  inline void set_jointtagspeed(::google::protobuf::int32 value);

  // required int32 JointTagPos = 8;
  inline bool has_jointtagpos() const;
  inline void clear_jointtagpos();
  static const int kJointTagPosFieldNumber = 8;
  inline ::google::protobuf::int32 jointtagpos() const;
  inline void set_jointtagpos(::google::protobuf::int32 value);

  // required int32 JointMaxCur = 9;
  inline bool has_jointmaxcur() const;
  inline void clear_jointmaxcur();
  static const int kJointMaxCurFieldNumber = 9;
  inline ::google::protobuf::int32 jointmaxcur() const;
  inline void set_jointmaxcur(::google::protobuf::int32 value);

  // required int32 JointMaxSpeed = 10;
  inline bool has_jointmaxspeed() const;
  inline void clear_jointmaxspeed();
  static const int kJointMaxSpeedFieldNumber = 10;
  inline ::google::protobuf::int32 jointmaxspeed() const;
  inline void set_jointmaxspeed(::google::protobuf::int32 value);

  // required int32 JointMaxAcc = 11;
  inline bool has_jointmaxacc() const;
  inline void clear_jointmaxacc();
  static const int kJointMaxAccFieldNumber = 11;
  inline ::google::protobuf::int32 jointmaxacc() const;
  inline void set_jointmaxacc(::google::protobuf::int32 value);

  // required int32 JointMINPos = 12;
  inline bool has_jointminpos() const;
  inline void clear_jointminpos();
  static const int kJointMINPosFieldNumber = 12;
  inline ::google::protobuf::int32 jointminpos() const;
  inline void set_jointminpos(::google::protobuf::int32 value);

  // required int32 JointMAXPos = 13;
  inline bool has_jointmaxpos() const;
  inline void clear_jointmaxpos();
  static const int kJointMAXPosFieldNumber = 13;
  inline ::google::protobuf::int32 jointmaxpos() const;
  inline void set_jointmaxpos(::google::protobuf::int32 value);

  // required int32 JointSEVLock = 14;
  inline bool has_jointsevlock() const;
  inline void clear_jointsevlock();
  static const int kJointSEVLockFieldNumber = 14;
  inline ::google::protobuf::int32 jointsevlock() const;
  inline void set_jointsevlock(::google::protobuf::int32 value);

  // required int32 JointCurP = 15;
  inline bool has_jointcurp() const;
  inline void clear_jointcurp();
  static const int kJointCurPFieldNumber = 15;
  inline ::google::protobuf::int32 jointcurp() const;
  inline void set_jointcurp(::google::protobuf::int32 value);

  // required int32 JointCurI = 16;
  inline bool has_jointcuri() const;
  inline void clear_jointcuri();
  static const int kJointCurIFieldNumber = 16;
  inline ::google::protobuf::int32 jointcuri() const;
  inline void set_jointcuri(::google::protobuf::int32 value);

  // required int32 JointCurD = 17;
  inline bool has_jointcurd() const;
  inline void clear_jointcurd();
  static const int kJointCurDFieldNumber = 17;
  inline ::google::protobuf::int32 jointcurd() const;
  inline void set_jointcurd(::google::protobuf::int32 value);

  // required int32 JointSpeedP = 18;
  inline bool has_jointspeedp() const;
  inline void clear_jointspeedp();
  static const int kJointSpeedPFieldNumber = 18;
  inline ::google::protobuf::int32 jointspeedp() const;
  inline void set_jointspeedp(::google::protobuf::int32 value);

  // required int32 JointSpeedI = 19;
  inline bool has_jointspeedi() const;
  inline void clear_jointspeedi();
  static const int kJointSpeedIFieldNumber = 19;
  inline ::google::protobuf::int32 jointspeedi() const;
  inline void set_jointspeedi(::google::protobuf::int32 value);

  // required int32 JointSpeedD = 20;
  inline bool has_jointspeedd() const;
  inline void clear_jointspeedd();
  static const int kJointSpeedDFieldNumber = 20;
  inline ::google::protobuf::int32 jointspeedd() const;
  inline void set_jointspeedd(::google::protobuf::int32 value);

  // required int32 JointSpeedDS = 21;
  inline bool has_jointspeedds() const;
  inline void clear_jointspeedds();
  static const int kJointSpeedDSFieldNumber = 21;
  inline ::google::protobuf::int32 jointspeedds() const;
  inline void set_jointspeedds(::google::protobuf::int32 value);

  // required int32 JointPosP = 22;
  inline bool has_jointposp() const;
  inline void clear_jointposp();
  static const int kJointPosPFieldNumber = 22;
  inline ::google::protobuf::int32 jointposp() const;
  inline void set_jointposp(::google::protobuf::int32 value);

  // required int32 JointPosI = 23;
  inline bool has_jointposi() const;
  inline void clear_jointposi();
  static const int kJointPosIFieldNumber = 23;
  inline ::google::protobuf::int32 jointposi() const;
  inline void set_jointposi(::google::protobuf::int32 value);

  // required int32 JointPosD = 24;
  inline bool has_jointposd() const;
  inline void clear_jointposd();
  static const int kJointPosDFieldNumber = 24;
  inline ::google::protobuf::int32 jointposd() const;
  inline void set_jointposd(::google::protobuf::int32 value);

  // required int32 JointPosDS = 25;
  inline bool has_jointposds() const;
  inline void clear_jointposds();
  static const int kJointPosDSFieldNumber = 25;
  inline ::google::protobuf::int32 jointposds() const;
  inline void set_jointposds(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:aubo.robot.communication.ProtoJointCommonData)
 private:
  inline void set_has_jointcurvol();
  inline void clear_has_jointcurvol();
  inline void set_has_jointcurtemp();
  inline void clear_has_jointcurtemp();
  inline void set_has_jointworkmode();
  inline void clear_has_jointworkmode();
  inline void set_has_jointdrienable();
  inline void clear_has_jointdrienable();
  inline void set_has_jointopenpwm();
  inline void clear_has_jointopenpwm();
  inline void set_has_jointtagcurrent();
  inline void clear_has_jointtagcurrent();
  inline void set_has_jointtagspeed();
  inline void clear_has_jointtagspeed();
  inline void set_has_jointtagpos();
  inline void clear_has_jointtagpos();
  inline void set_has_jointmaxcur();
  inline void clear_has_jointmaxcur();
  inline void set_has_jointmaxspeed();
  inline void clear_has_jointmaxspeed();
  inline void set_has_jointmaxacc();
  inline void clear_has_jointmaxacc();
  inline void set_has_jointminpos();
  inline void clear_has_jointminpos();
  inline void set_has_jointmaxpos();
  inline void clear_has_jointmaxpos();
  inline void set_has_jointsevlock();
  inline void clear_has_jointsevlock();
  inline void set_has_jointcurp();
  inline void clear_has_jointcurp();
  inline void set_has_jointcuri();
  inline void clear_has_jointcuri();
  inline void set_has_jointcurd();
  inline void clear_has_jointcurd();
  inline void set_has_jointspeedp();
  inline void clear_has_jointspeedp();
  inline void set_has_jointspeedi();
  inline void clear_has_jointspeedi();
  inline void set_has_jointspeedd();
  inline void clear_has_jointspeedd();
  inline void set_has_jointspeedds();
  inline void clear_has_jointspeedds();
  inline void set_has_jointposp();
  inline void clear_has_jointposp();
  inline void set_has_jointposi();
  inline void clear_has_jointposi();
  inline void set_has_jointposd();
  inline void clear_has_jointposd();
  inline void set_has_jointposds();
  inline void clear_has_jointposds();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::int32 jointcurvol_;
  ::google::protobuf::int32 jointcurtemp_;
  ::google::protobuf::int32 jointworkmode_;
  ::google::protobuf::int32 jointdrienable_;
  ::google::protobuf::int32 jointopenpwm_;
  ::google::protobuf::int32 jointtagcurrent_;
  ::google::protobuf::int32 jointtagspeed_;
  ::google::protobuf::int32 jointtagpos_;
  ::google::protobuf::int32 jointmaxcur_;
  ::google::protobuf::int32 jointmaxspeed_;
  ::google::protobuf::int32 jointmaxacc_;
  ::google::protobuf::int32 jointminpos_;
  ::google::protobuf::int32 jointmaxpos_;
  ::google::protobuf::int32 jointsevlock_;
  ::google::protobuf::int32 jointcurp_;
  ::google::protobuf::int32 jointcuri_;
  ::google::protobuf::int32 jointcurd_;
  ::google::protobuf::int32 jointspeedp_;
  ::google::protobuf::int32 jointspeedi_;
  ::google::protobuf::int32 jointspeedd_;
  ::google::protobuf::int32 jointspeedds_;
  ::google::protobuf::int32 jointposp_;
  ::google::protobuf::int32 jointposi_;
  ::google::protobuf::int32 jointposd_;
  ::google::protobuf::int32 jointposds_;
  friend void  protobuf_AddDesc_robotcommunication_2eproto();
  friend void protobuf_AssignDesc_robotcommunication_2eproto();
  friend void protobuf_ShutdownFile_robotcommunication_2eproto();

  void InitAsDefaultInstance();
  static ProtoJointCommonData* default_instance_;
};
// -------------------------------------------------------------------

class ProtoJointCommonDataResponse : public ::google::protobuf::Message {
 public:
  ProtoJointCommonDataResponse();
  virtual ~ProtoJointCommonDataResponse();

  ProtoJointCommonDataResponse(const ProtoJointCommonDataResponse& from);

  inline ProtoJointCommonDataResponse& operator=(const ProtoJointCommonDataResponse& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ProtoJointCommonDataResponse& default_instance();

  void Swap(ProtoJointCommonDataResponse* other);

  // implements Message ----------------------------------------------

  ProtoJointCommonDataResponse* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ProtoJointCommonDataResponse& from);
  void MergeFrom(const ProtoJointCommonDataResponse& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .aubo.robot.communication.ProtoJointCommonData JointCommonData = 1;
  inline int jointcommondata_size() const;
  inline void clear_jointcommondata();
  static const int kJointCommonDataFieldNumber = 1;
  inline const ::aubo::robot::communication::ProtoJointCommonData& jointcommondata(int index) const;
  inline ::aubo::robot::communication::ProtoJointCommonData* mutable_jointcommondata(int index);
  inline ::aubo::robot::communication::ProtoJointCommonData* add_jointcommondata();
  inline const ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::ProtoJointCommonData >&
      jointcommondata() const;
  inline ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::ProtoJointCommonData >*
      mutable_jointcommondata();

  // required .aubo.robot.communication.ProtoRobotCommonResponse errorInfo = 2;
  inline bool has_errorinfo() const;
  inline void clear_errorinfo();
  static const int kErrorInfoFieldNumber = 2;
  inline const ::aubo::robot::communication::ProtoRobotCommonResponse& errorinfo() const;
  inline ::aubo::robot::communication::ProtoRobotCommonResponse* mutable_errorinfo();
  inline ::aubo::robot::communication::ProtoRobotCommonResponse* release_errorinfo();
  inline void set_allocated_errorinfo(::aubo::robot::communication::ProtoRobotCommonResponse* errorinfo);

  // @@protoc_insertion_point(class_scope:aubo.robot.communication.ProtoJointCommonDataResponse)
 private:
  inline void set_has_errorinfo();
  inline void clear_has_errorinfo();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::ProtoJointCommonData > jointcommondata_;
  ::aubo::robot::communication::ProtoRobotCommonResponse* errorinfo_;
  friend void  protobuf_AddDesc_robotcommunication_2eproto();
  friend void protobuf_AssignDesc_robotcommunication_2eproto();
  friend void protobuf_ShutdownFile_robotcommunication_2eproto();

  void InitAsDefaultInstance();
  static ProtoJointCommonDataResponse* default_instance_;
};
// ===================================================================


// ===================================================================

// JointStatus

// required int32 jointCurrentI = 1;
inline bool JointStatus::has_jointcurrenti() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void JointStatus::set_has_jointcurrenti() {
  _has_bits_[0] |= 0x00000001u;
}
inline void JointStatus::clear_has_jointcurrenti() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void JointStatus::clear_jointcurrenti() {
  jointcurrenti_ = 0;
  clear_has_jointcurrenti();
}
inline ::google::protobuf::int32 JointStatus::jointcurrenti() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.JointStatus.jointCurrentI)
  return jointcurrenti_;
}
inline void JointStatus::set_jointcurrenti(::google::protobuf::int32 value) {
  set_has_jointcurrenti();
  jointcurrenti_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.JointStatus.jointCurrentI)
}

// required int32 jointSpeedMoto = 2;
inline bool JointStatus::has_jointspeedmoto() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void JointStatus::set_has_jointspeedmoto() {
  _has_bits_[0] |= 0x00000002u;
}
inline void JointStatus::clear_has_jointspeedmoto() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void JointStatus::clear_jointspeedmoto() {
  jointspeedmoto_ = 0;
  clear_has_jointspeedmoto();
}
inline ::google::protobuf::int32 JointStatus::jointspeedmoto() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.JointStatus.jointSpeedMoto)
  return jointspeedmoto_;
}
inline void JointStatus::set_jointspeedmoto(::google::protobuf::int32 value) {
  set_has_jointspeedmoto();
  jointspeedmoto_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.JointStatus.jointSpeedMoto)
}

// required float jointPosJ = 3;
inline bool JointStatus::has_jointposj() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void JointStatus::set_has_jointposj() {
  _has_bits_[0] |= 0x00000004u;
}
inline void JointStatus::clear_has_jointposj() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void JointStatus::clear_jointposj() {
  jointposj_ = 0;
  clear_has_jointposj();
}
inline float JointStatus::jointposj() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.JointStatus.jointPosJ)
  return jointposj_;
}
inline void JointStatus::set_jointposj(float value) {
  set_has_jointposj();
  jointposj_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.JointStatus.jointPosJ)
}

// required float jointCurVol = 4;
inline bool JointStatus::has_jointcurvol() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void JointStatus::set_has_jointcurvol() {
  _has_bits_[0] |= 0x00000008u;
}
inline void JointStatus::clear_has_jointcurvol() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void JointStatus::clear_jointcurvol() {
  jointcurvol_ = 0;
  clear_has_jointcurvol();
}
inline float JointStatus::jointcurvol() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.JointStatus.jointCurVol)
  return jointcurvol_;
}
inline void JointStatus::set_jointcurvol(float value) {
  set_has_jointcurvol();
  jointcurvol_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.JointStatus.jointCurVol)
}

// required float jointCurTemp = 5;
inline bool JointStatus::has_jointcurtemp() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void JointStatus::set_has_jointcurtemp() {
  _has_bits_[0] |= 0x00000010u;
}
inline void JointStatus::clear_has_jointcurtemp() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void JointStatus::clear_jointcurtemp() {
  jointcurtemp_ = 0;
  clear_has_jointcurtemp();
}
inline float JointStatus::jointcurtemp() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.JointStatus.jointCurTemp)
  return jointcurtemp_;
}
inline void JointStatus::set_jointcurtemp(float value) {
  set_has_jointcurtemp();
  jointcurtemp_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.JointStatus.jointCurTemp)
}

// required int32 jointTagCurrentI = 6;
inline bool JointStatus::has_jointtagcurrenti() const {
  return (_has_bits_[0] & 0x00000020u) != 0;
}
inline void JointStatus::set_has_jointtagcurrenti() {
  _has_bits_[0] |= 0x00000020u;
}
inline void JointStatus::clear_has_jointtagcurrenti() {
  _has_bits_[0] &= ~0x00000020u;
}
inline void JointStatus::clear_jointtagcurrenti() {
  jointtagcurrenti_ = 0;
  clear_has_jointtagcurrenti();
}
inline ::google::protobuf::int32 JointStatus::jointtagcurrenti() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.JointStatus.jointTagCurrentI)
  return jointtagcurrenti_;
}
inline void JointStatus::set_jointtagcurrenti(::google::protobuf::int32 value) {
  set_has_jointtagcurrenti();
  jointtagcurrenti_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.JointStatus.jointTagCurrentI)
}

// required float jointTagSpeedMoto = 7;
inline bool JointStatus::has_jointtagspeedmoto() const {
  return (_has_bits_[0] & 0x00000040u) != 0;
}
inline void JointStatus::set_has_jointtagspeedmoto() {
  _has_bits_[0] |= 0x00000040u;
}
inline void JointStatus::clear_has_jointtagspeedmoto() {
  _has_bits_[0] &= ~0x00000040u;
}
inline void JointStatus::clear_jointtagspeedmoto() {
  jointtagspeedmoto_ = 0;
  clear_has_jointtagspeedmoto();
}
inline float JointStatus::jointtagspeedmoto() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.JointStatus.jointTagSpeedMoto)
  return jointtagspeedmoto_;
}
inline void JointStatus::set_jointtagspeedmoto(float value) {
  set_has_jointtagspeedmoto();
  jointtagspeedmoto_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.JointStatus.jointTagSpeedMoto)
}

// required float jointTagPosJ = 8;
inline bool JointStatus::has_jointtagposj() const {
  return (_has_bits_[0] & 0x00000080u) != 0;
}
inline void JointStatus::set_has_jointtagposj() {
  _has_bits_[0] |= 0x00000080u;
}
inline void JointStatus::clear_has_jointtagposj() {
  _has_bits_[0] &= ~0x00000080u;
}
inline void JointStatus::clear_jointtagposj() {
  jointtagposj_ = 0;
  clear_has_jointtagposj();
}
inline float JointStatus::jointtagposj() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.JointStatus.jointTagPosJ)
  return jointtagposj_;
}
inline void JointStatus::set_jointtagposj(float value) {
  set_has_jointtagposj();
  jointtagposj_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.JointStatus.jointTagPosJ)
}

// required uint32 jointErrorNum = 9;
inline bool JointStatus::has_jointerrornum() const {
  return (_has_bits_[0] & 0x00000100u) != 0;
}
inline void JointStatus::set_has_jointerrornum() {
  _has_bits_[0] |= 0x00000100u;
}
inline void JointStatus::clear_has_jointerrornum() {
  _has_bits_[0] &= ~0x00000100u;
}
inline void JointStatus::clear_jointerrornum() {
  jointerrornum_ = 0u;
  clear_has_jointerrornum();
}
inline ::google::protobuf::uint32 JointStatus::jointerrornum() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.JointStatus.jointErrorNum)
  return jointerrornum_;
}
inline void JointStatus::set_jointerrornum(::google::protobuf::uint32 value) {
  set_has_jointerrornum();
  jointerrornum_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.JointStatus.jointErrorNum)
}

// -------------------------------------------------------------------

// RobotCollisionCurrent

// repeated uint32 jointCollisionCurrent = 1;
inline int RobotCollisionCurrent::jointcollisioncurrent_size() const {
  return jointcollisioncurrent_.size();
}
inline void RobotCollisionCurrent::clear_jointcollisioncurrent() {
  jointcollisioncurrent_.Clear();
}
inline ::google::protobuf::uint32 RobotCollisionCurrent::jointcollisioncurrent(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.RobotCollisionCurrent.jointCollisionCurrent)
  return jointcollisioncurrent_.Get(index);
}
inline void RobotCollisionCurrent::set_jointcollisioncurrent(int index, ::google::protobuf::uint32 value) {
  jointcollisioncurrent_.Set(index, value);
  // @@protoc_insertion_point(field_set:aubo.robot.communication.RobotCollisionCurrent.jointCollisionCurrent)
}
inline void RobotCollisionCurrent::add_jointcollisioncurrent(::google::protobuf::uint32 value) {
  jointcollisioncurrent_.Add(value);
  // @@protoc_insertion_point(field_add:aubo.robot.communication.RobotCollisionCurrent.jointCollisionCurrent)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
RobotCollisionCurrent::jointcollisioncurrent() const {
  // @@protoc_insertion_point(field_list:aubo.robot.communication.RobotCollisionCurrent.jointCollisionCurrent)
  return jointcollisioncurrent_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
RobotCollisionCurrent::mutable_jointcollisioncurrent() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.communication.RobotCollisionCurrent.jointCollisionCurrent)
  return &jointcollisioncurrent_;
}

// required uint32 collisionClass = 2;
inline bool RobotCollisionCurrent::has_collisionclass() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void RobotCollisionCurrent::set_has_collisionclass() {
  _has_bits_[0] |= 0x00000002u;
}
inline void RobotCollisionCurrent::clear_has_collisionclass() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void RobotCollisionCurrent::clear_collisionclass() {
  collisionclass_ = 0u;
  clear_has_collisionclass();
}
inline ::google::protobuf::uint32 RobotCollisionCurrent::collisionclass() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.RobotCollisionCurrent.collisionClass)
  return collisionclass_;
}
inline void RobotCollisionCurrent::set_collisionclass(::google::protobuf::uint32 value) {
  set_has_collisionclass();
  collisionclass_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.RobotCollisionCurrent.collisionClass)
}

// -------------------------------------------------------------------

// TcpParam

// required float positionX = 1;
inline bool TcpParam::has_positionx() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void TcpParam::set_has_positionx() {
  _has_bits_[0] |= 0x00000001u;
}
inline void TcpParam::clear_has_positionx() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void TcpParam::clear_positionx() {
  positionx_ = 0;
  clear_has_positionx();
}
inline float TcpParam::positionx() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.TcpParam.positionX)
  return positionx_;
}
inline void TcpParam::set_positionx(float value) {
  set_has_positionx();
  positionx_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.TcpParam.positionX)
}

// required float positionY = 2;
inline bool TcpParam::has_positiony() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void TcpParam::set_has_positiony() {
  _has_bits_[0] |= 0x00000002u;
}
inline void TcpParam::clear_has_positiony() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void TcpParam::clear_positiony() {
  positiony_ = 0;
  clear_has_positiony();
}
inline float TcpParam::positiony() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.TcpParam.positionY)
  return positiony_;
}
inline void TcpParam::set_positiony(float value) {
  set_has_positiony();
  positiony_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.TcpParam.positionY)
}

// required float positionZ = 3;
inline bool TcpParam::has_positionz() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void TcpParam::set_has_positionz() {
  _has_bits_[0] |= 0x00000004u;
}
inline void TcpParam::clear_has_positionz() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void TcpParam::clear_positionz() {
  positionz_ = 0;
  clear_has_positionz();
}
inline float TcpParam::positionz() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.TcpParam.positionZ)
  return positionz_;
}
inline void TcpParam::set_positionz(float value) {
  set_has_positionz();
  positionz_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.TcpParam.positionZ)
}

// required float payload = 4;
inline bool TcpParam::has_payload() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void TcpParam::set_has_payload() {
  _has_bits_[0] |= 0x00000008u;
}
inline void TcpParam::clear_has_payload() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void TcpParam::clear_payload() {
  payload_ = 0;
  clear_has_payload();
}
inline float TcpParam::payload() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.TcpParam.payload)
  return payload_;
}
inline void TcpParam::set_payload(float value) {
  set_has_payload();
  payload_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.TcpParam.payload)
}

// -------------------------------------------------------------------

// RobotTcpParam

// required .aubo.robot.communication.TcpParam paramAutorun = 1;
inline bool RobotTcpParam::has_paramautorun() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void RobotTcpParam::set_has_paramautorun() {
  _has_bits_[0] |= 0x00000001u;
}
inline void RobotTcpParam::clear_has_paramautorun() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void RobotTcpParam::clear_paramautorun() {
  if (paramautorun_ != NULL) paramautorun_->::aubo::robot::communication::TcpParam::Clear();
  clear_has_paramautorun();
}
inline const ::aubo::robot::communication::TcpParam& RobotTcpParam::paramautorun() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.RobotTcpParam.paramAutorun)
  return paramautorun_ != NULL ? *paramautorun_ : *default_instance_->paramautorun_;
}
inline ::aubo::robot::communication::TcpParam* RobotTcpParam::mutable_paramautorun() {
  set_has_paramautorun();
  if (paramautorun_ == NULL) paramautorun_ = new ::aubo::robot::communication::TcpParam;
  // @@protoc_insertion_point(field_mutable:aubo.robot.communication.RobotTcpParam.paramAutorun)
  return paramautorun_;
}
inline ::aubo::robot::communication::TcpParam* RobotTcpParam::release_paramautorun() {
  clear_has_paramautorun();
  ::aubo::robot::communication::TcpParam* temp = paramautorun_;
  paramautorun_ = NULL;
  return temp;
}
inline void RobotTcpParam::set_allocated_paramautorun(::aubo::robot::communication::TcpParam* paramautorun) {
  delete paramautorun_;
  paramautorun_ = paramautorun;
  if (paramautorun) {
    set_has_paramautorun();
  } else {
    clear_has_paramautorun();
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.communication.RobotTcpParam.paramAutorun)
}

// required .aubo.robot.communication.TcpParam paramManual = 2;
inline bool RobotTcpParam::has_parammanual() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void RobotTcpParam::set_has_parammanual() {
  _has_bits_[0] |= 0x00000002u;
}
inline void RobotTcpParam::clear_has_parammanual() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void RobotTcpParam::clear_parammanual() {
  if (parammanual_ != NULL) parammanual_->::aubo::robot::communication::TcpParam::Clear();
  clear_has_parammanual();
}
inline const ::aubo::robot::communication::TcpParam& RobotTcpParam::parammanual() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.RobotTcpParam.paramManual)
  return parammanual_ != NULL ? *parammanual_ : *default_instance_->parammanual_;
}
inline ::aubo::robot::communication::TcpParam* RobotTcpParam::mutable_parammanual() {
  set_has_parammanual();
  if (parammanual_ == NULL) parammanual_ = new ::aubo::robot::communication::TcpParam;
  // @@protoc_insertion_point(field_mutable:aubo.robot.communication.RobotTcpParam.paramManual)
  return parammanual_;
}
inline ::aubo::robot::communication::TcpParam* RobotTcpParam::release_parammanual() {
  clear_has_parammanual();
  ::aubo::robot::communication::TcpParam* temp = parammanual_;
  parammanual_ = NULL;
  return temp;
}
inline void RobotTcpParam::set_allocated_parammanual(::aubo::robot::communication::TcpParam* parammanual) {
  delete parammanual_;
  parammanual_ = parammanual;
  if (parammanual) {
    set_has_parammanual();
  } else {
    clear_has_parammanual();
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.communication.RobotTcpParam.paramManual)
}

// -------------------------------------------------------------------

// RobotGravityComponent

// required float x = 1;
inline bool RobotGravityComponent::has_x() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void RobotGravityComponent::set_has_x() {
  _has_bits_[0] |= 0x00000001u;
}
inline void RobotGravityComponent::clear_has_x() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void RobotGravityComponent::clear_x() {
  x_ = 0;
  clear_has_x();
}
inline float RobotGravityComponent::x() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.RobotGravityComponent.x)
  return x_;
}
inline void RobotGravityComponent::set_x(float value) {
  set_has_x();
  x_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.RobotGravityComponent.x)
}

// required float y = 2;
inline bool RobotGravityComponent::has_y() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void RobotGravityComponent::set_has_y() {
  _has_bits_[0] |= 0x00000002u;
}
inline void RobotGravityComponent::clear_has_y() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void RobotGravityComponent::clear_y() {
  y_ = 0;
  clear_has_y();
}
inline float RobotGravityComponent::y() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.RobotGravityComponent.y)
  return y_;
}
inline void RobotGravityComponent::set_y(float value) {
  set_has_y();
  y_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.RobotGravityComponent.y)
}

// required float z = 3;
inline bool RobotGravityComponent::has_z() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void RobotGravityComponent::set_has_z() {
  _has_bits_[0] |= 0x00000004u;
}
inline void RobotGravityComponent::clear_has_z() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void RobotGravityComponent::clear_z() {
  z_ = 0;
  clear_has_z();
}
inline float RobotGravityComponent::z() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.RobotGravityComponent.z)
  return z_;
}
inline void RobotGravityComponent::set_z(float value) {
  set_has_z();
  z_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.RobotGravityComponent.z)
}

// -------------------------------------------------------------------

// JointVersion

// required string hwVersion = 1;
inline bool JointVersion::has_hwversion() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void JointVersion::set_has_hwversion() {
  _has_bits_[0] |= 0x00000001u;
}
inline void JointVersion::clear_has_hwversion() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void JointVersion::clear_hwversion() {
  if (hwversion_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    hwversion_->clear();
  }
  clear_has_hwversion();
}
inline const ::std::string& JointVersion::hwversion() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.JointVersion.hwVersion)
  return *hwversion_;
}
inline void JointVersion::set_hwversion(const ::std::string& value) {
  set_has_hwversion();
  if (hwversion_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    hwversion_ = new ::std::string;
  }
  hwversion_->assign(value);
  // @@protoc_insertion_point(field_set:aubo.robot.communication.JointVersion.hwVersion)
}
inline void JointVersion::set_hwversion(const char* value) {
  set_has_hwversion();
  if (hwversion_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    hwversion_ = new ::std::string;
  }
  hwversion_->assign(value);
  // @@protoc_insertion_point(field_set_char:aubo.robot.communication.JointVersion.hwVersion)
}
inline void JointVersion::set_hwversion(const char* value, size_t size) {
  set_has_hwversion();
  if (hwversion_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    hwversion_ = new ::std::string;
  }
  hwversion_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:aubo.robot.communication.JointVersion.hwVersion)
}
inline ::std::string* JointVersion::mutable_hwversion() {
  set_has_hwversion();
  if (hwversion_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    hwversion_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:aubo.robot.communication.JointVersion.hwVersion)
  return hwversion_;
}
inline ::std::string* JointVersion::release_hwversion() {
  clear_has_hwversion();
  if (hwversion_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = hwversion_;
    hwversion_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void JointVersion::set_allocated_hwversion(::std::string* hwversion) {
  if (hwversion_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete hwversion_;
  }
  if (hwversion) {
    set_has_hwversion();
    hwversion_ = hwversion;
  } else {
    clear_has_hwversion();
    hwversion_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.communication.JointVersion.hwVersion)
}

// required string swVersion = 2;
inline bool JointVersion::has_swversion() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void JointVersion::set_has_swversion() {
  _has_bits_[0] |= 0x00000002u;
}
inline void JointVersion::clear_has_swversion() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void JointVersion::clear_swversion() {
  if (swversion_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    swversion_->clear();
  }
  clear_has_swversion();
}
inline const ::std::string& JointVersion::swversion() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.JointVersion.swVersion)
  return *swversion_;
}
inline void JointVersion::set_swversion(const ::std::string& value) {
  set_has_swversion();
  if (swversion_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    swversion_ = new ::std::string;
  }
  swversion_->assign(value);
  // @@protoc_insertion_point(field_set:aubo.robot.communication.JointVersion.swVersion)
}
inline void JointVersion::set_swversion(const char* value) {
  set_has_swversion();
  if (swversion_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    swversion_ = new ::std::string;
  }
  swversion_->assign(value);
  // @@protoc_insertion_point(field_set_char:aubo.robot.communication.JointVersion.swVersion)
}
inline void JointVersion::set_swversion(const char* value, size_t size) {
  set_has_swversion();
  if (swversion_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    swversion_ = new ::std::string;
  }
  swversion_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:aubo.robot.communication.JointVersion.swVersion)
}
inline ::std::string* JointVersion::mutable_swversion() {
  set_has_swversion();
  if (swversion_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    swversion_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:aubo.robot.communication.JointVersion.swVersion)
  return swversion_;
}
inline ::std::string* JointVersion::release_swversion() {
  clear_has_swversion();
  if (swversion_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = swversion_;
    swversion_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void JointVersion::set_allocated_swversion(::std::string* swversion) {
  if (swversion_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete swversion_;
  }
  if (swversion) {
    set_has_swversion();
    swversion_ = swversion;
  } else {
    clear_has_swversion();
    swversion_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.communication.JointVersion.swVersion)
}

// -------------------------------------------------------------------

// OurRobotDevInfo

// required uint32 type = 1;
inline bool OurRobotDevInfo::has_type() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void OurRobotDevInfo::set_has_type() {
  _has_bits_[0] |= 0x00000001u;
}
inline void OurRobotDevInfo::clear_has_type() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void OurRobotDevInfo::clear_type() {
  type_ = 0u;
  clear_has_type();
}
inline ::google::protobuf::uint32 OurRobotDevInfo::type() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.OurRobotDevInfo.type)
  return type_;
}
inline void OurRobotDevInfo::set_type(::google::protobuf::uint32 value) {
  set_has_type();
  type_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.OurRobotDevInfo.type)
}

// required string revision = 2;
inline bool OurRobotDevInfo::has_revision() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void OurRobotDevInfo::set_has_revision() {
  _has_bits_[0] |= 0x00000002u;
}
inline void OurRobotDevInfo::clear_has_revision() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void OurRobotDevInfo::clear_revision() {
  if (revision_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    revision_->clear();
  }
  clear_has_revision();
}
inline const ::std::string& OurRobotDevInfo::revision() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.OurRobotDevInfo.revision)
  return *revision_;
}
inline void OurRobotDevInfo::set_revision(const ::std::string& value) {
  set_has_revision();
  if (revision_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    revision_ = new ::std::string;
  }
  revision_->assign(value);
  // @@protoc_insertion_point(field_set:aubo.robot.communication.OurRobotDevInfo.revision)
}
inline void OurRobotDevInfo::set_revision(const char* value) {
  set_has_revision();
  if (revision_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    revision_ = new ::std::string;
  }
  revision_->assign(value);
  // @@protoc_insertion_point(field_set_char:aubo.robot.communication.OurRobotDevInfo.revision)
}
inline void OurRobotDevInfo::set_revision(const char* value, size_t size) {
  set_has_revision();
  if (revision_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    revision_ = new ::std::string;
  }
  revision_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:aubo.robot.communication.OurRobotDevInfo.revision)
}
inline ::std::string* OurRobotDevInfo::mutable_revision() {
  set_has_revision();
  if (revision_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    revision_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:aubo.robot.communication.OurRobotDevInfo.revision)
  return revision_;
}
inline ::std::string* OurRobotDevInfo::release_revision() {
  clear_has_revision();
  if (revision_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = revision_;
    revision_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void OurRobotDevInfo::set_allocated_revision(::std::string* revision) {
  if (revision_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete revision_;
  }
  if (revision) {
    set_has_revision();
    revision_ = revision;
  } else {
    clear_has_revision();
    revision_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.communication.OurRobotDevInfo.revision)
}

// required string manuID = 3;
inline bool OurRobotDevInfo::has_manuid() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void OurRobotDevInfo::set_has_manuid() {
  _has_bits_[0] |= 0x00000004u;
}
inline void OurRobotDevInfo::clear_has_manuid() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void OurRobotDevInfo::clear_manuid() {
  if (manuid_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    manuid_->clear();
  }
  clear_has_manuid();
}
inline const ::std::string& OurRobotDevInfo::manuid() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.OurRobotDevInfo.manuID)
  return *manuid_;
}
inline void OurRobotDevInfo::set_manuid(const ::std::string& value) {
  set_has_manuid();
  if (manuid_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    manuid_ = new ::std::string;
  }
  manuid_->assign(value);
  // @@protoc_insertion_point(field_set:aubo.robot.communication.OurRobotDevInfo.manuID)
}
inline void OurRobotDevInfo::set_manuid(const char* value) {
  set_has_manuid();
  if (manuid_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    manuid_ = new ::std::string;
  }
  manuid_->assign(value);
  // @@protoc_insertion_point(field_set_char:aubo.robot.communication.OurRobotDevInfo.manuID)
}
inline void OurRobotDevInfo::set_manuid(const char* value, size_t size) {
  set_has_manuid();
  if (manuid_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    manuid_ = new ::std::string;
  }
  manuid_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:aubo.robot.communication.OurRobotDevInfo.manuID)
}
inline ::std::string* OurRobotDevInfo::mutable_manuid() {
  set_has_manuid();
  if (manuid_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    manuid_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:aubo.robot.communication.OurRobotDevInfo.manuID)
  return manuid_;
}
inline ::std::string* OurRobotDevInfo::release_manuid() {
  clear_has_manuid();
  if (manuid_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = manuid_;
    manuid_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void OurRobotDevInfo::set_allocated_manuid(::std::string* manuid) {
  if (manuid_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete manuid_;
  }
  if (manuid) {
    set_has_manuid();
    manuid_ = manuid;
  } else {
    clear_has_manuid();
    manuid_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.communication.OurRobotDevInfo.manuID)
}

// required string jointType = 4;
inline bool OurRobotDevInfo::has_jointtype() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void OurRobotDevInfo::set_has_jointtype() {
  _has_bits_[0] |= 0x00000008u;
}
inline void OurRobotDevInfo::clear_has_jointtype() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void OurRobotDevInfo::clear_jointtype() {
  if (jointtype_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    jointtype_->clear();
  }
  clear_has_jointtype();
}
inline const ::std::string& OurRobotDevInfo::jointtype() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.OurRobotDevInfo.jointType)
  return *jointtype_;
}
inline void OurRobotDevInfo::set_jointtype(const ::std::string& value) {
  set_has_jointtype();
  if (jointtype_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    jointtype_ = new ::std::string;
  }
  jointtype_->assign(value);
  // @@protoc_insertion_point(field_set:aubo.robot.communication.OurRobotDevInfo.jointType)
}
inline void OurRobotDevInfo::set_jointtype(const char* value) {
  set_has_jointtype();
  if (jointtype_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    jointtype_ = new ::std::string;
  }
  jointtype_->assign(value);
  // @@protoc_insertion_point(field_set_char:aubo.robot.communication.OurRobotDevInfo.jointType)
}
inline void OurRobotDevInfo::set_jointtype(const char* value, size_t size) {
  set_has_jointtype();
  if (jointtype_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    jointtype_ = new ::std::string;
  }
  jointtype_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:aubo.robot.communication.OurRobotDevInfo.jointType)
}
inline ::std::string* OurRobotDevInfo::mutable_jointtype() {
  set_has_jointtype();
  if (jointtype_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    jointtype_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:aubo.robot.communication.OurRobotDevInfo.jointType)
  return jointtype_;
}
inline ::std::string* OurRobotDevInfo::release_jointtype() {
  clear_has_jointtype();
  if (jointtype_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = jointtype_;
    jointtype_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void OurRobotDevInfo::set_allocated_jointtype(::std::string* jointtype) {
  if (jointtype_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete jointtype_;
  }
  if (jointtype) {
    set_has_jointtype();
    jointtype_ = jointtype;
  } else {
    clear_has_jointtype();
    jointtype_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.communication.OurRobotDevInfo.jointType)
}

// repeated .aubo.robot.communication.JointVersion jointVer = 5;
inline int OurRobotDevInfo::jointver_size() const {
  return jointver_.size();
}
inline void OurRobotDevInfo::clear_jointver() {
  jointver_.Clear();
}
inline const ::aubo::robot::communication::JointVersion& OurRobotDevInfo::jointver(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.OurRobotDevInfo.jointVer)
  return jointver_.Get(index);
}
inline ::aubo::robot::communication::JointVersion* OurRobotDevInfo::mutable_jointver(int index) {
  // @@protoc_insertion_point(field_mutable:aubo.robot.communication.OurRobotDevInfo.jointVer)
  return jointver_.Mutable(index);
}
inline ::aubo::robot::communication::JointVersion* OurRobotDevInfo::add_jointver() {
  // @@protoc_insertion_point(field_add:aubo.robot.communication.OurRobotDevInfo.jointVer)
  return jointver_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::JointVersion >&
OurRobotDevInfo::jointver() const {
  // @@protoc_insertion_point(field_list:aubo.robot.communication.OurRobotDevInfo.jointVer)
  return jointver_;
}
inline ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::JointVersion >*
OurRobotDevInfo::mutable_jointver() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.communication.OurRobotDevInfo.jointVer)
  return &jointver_;
}

// required string desc = 6;
inline bool OurRobotDevInfo::has_desc() const {
  return (_has_bits_[0] & 0x00000020u) != 0;
}
inline void OurRobotDevInfo::set_has_desc() {
  _has_bits_[0] |= 0x00000020u;
}
inline void OurRobotDevInfo::clear_has_desc() {
  _has_bits_[0] &= ~0x00000020u;
}
inline void OurRobotDevInfo::clear_desc() {
  if (desc_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    desc_->clear();
  }
  clear_has_desc();
}
inline const ::std::string& OurRobotDevInfo::desc() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.OurRobotDevInfo.desc)
  return *desc_;
}
inline void OurRobotDevInfo::set_desc(const ::std::string& value) {
  set_has_desc();
  if (desc_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    desc_ = new ::std::string;
  }
  desc_->assign(value);
  // @@protoc_insertion_point(field_set:aubo.robot.communication.OurRobotDevInfo.desc)
}
inline void OurRobotDevInfo::set_desc(const char* value) {
  set_has_desc();
  if (desc_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    desc_ = new ::std::string;
  }
  desc_->assign(value);
  // @@protoc_insertion_point(field_set_char:aubo.robot.communication.OurRobotDevInfo.desc)
}
inline void OurRobotDevInfo::set_desc(const char* value, size_t size) {
  set_has_desc();
  if (desc_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    desc_ = new ::std::string;
  }
  desc_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:aubo.robot.communication.OurRobotDevInfo.desc)
}
inline ::std::string* OurRobotDevInfo::mutable_desc() {
  set_has_desc();
  if (desc_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    desc_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:aubo.robot.communication.OurRobotDevInfo.desc)
  return desc_;
}
inline ::std::string* OurRobotDevInfo::release_desc() {
  clear_has_desc();
  if (desc_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = desc_;
    desc_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void OurRobotDevInfo::set_allocated_desc(::std::string* desc) {
  if (desc_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete desc_;
  }
  if (desc) {
    set_has_desc();
    desc_ = desc;
  } else {
    clear_has_desc();
    desc_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.communication.OurRobotDevInfo.desc)
}

// repeated string jointProductID = 7;
inline int OurRobotDevInfo::jointproductid_size() const {
  return jointproductid_.size();
}
inline void OurRobotDevInfo::clear_jointproductid() {
  jointproductid_.Clear();
}
inline const ::std::string& OurRobotDevInfo::jointproductid(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.OurRobotDevInfo.jointProductID)
  return jointproductid_.Get(index);
}
inline ::std::string* OurRobotDevInfo::mutable_jointproductid(int index) {
  // @@protoc_insertion_point(field_mutable:aubo.robot.communication.OurRobotDevInfo.jointProductID)
  return jointproductid_.Mutable(index);
}
inline void OurRobotDevInfo::set_jointproductid(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:aubo.robot.communication.OurRobotDevInfo.jointProductID)
  jointproductid_.Mutable(index)->assign(value);
}
inline void OurRobotDevInfo::set_jointproductid(int index, const char* value) {
  jointproductid_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:aubo.robot.communication.OurRobotDevInfo.jointProductID)
}
inline void OurRobotDevInfo::set_jointproductid(int index, const char* value, size_t size) {
  jointproductid_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:aubo.robot.communication.OurRobotDevInfo.jointProductID)
}
inline ::std::string* OurRobotDevInfo::add_jointproductid() {
  return jointproductid_.Add();
}
inline void OurRobotDevInfo::add_jointproductid(const ::std::string& value) {
  jointproductid_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:aubo.robot.communication.OurRobotDevInfo.jointProductID)
}
inline void OurRobotDevInfo::add_jointproductid(const char* value) {
  jointproductid_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:aubo.robot.communication.OurRobotDevInfo.jointProductID)
}
inline void OurRobotDevInfo::add_jointproductid(const char* value, size_t size) {
  jointproductid_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:aubo.robot.communication.OurRobotDevInfo.jointProductID)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
OurRobotDevInfo::jointproductid() const {
  // @@protoc_insertion_point(field_list:aubo.robot.communication.OurRobotDevInfo.jointProductID)
  return jointproductid_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
OurRobotDevInfo::mutable_jointproductid() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.communication.OurRobotDevInfo.jointProductID)
  return &jointproductid_;
}

// required string slaveDevVersion = 8;
inline bool OurRobotDevInfo::has_slavedevversion() const {
  return (_has_bits_[0] & 0x00000080u) != 0;
}
inline void OurRobotDevInfo::set_has_slavedevversion() {
  _has_bits_[0] |= 0x00000080u;
}
inline void OurRobotDevInfo::clear_has_slavedevversion() {
  _has_bits_[0] &= ~0x00000080u;
}
inline void OurRobotDevInfo::clear_slavedevversion() {
  if (slavedevversion_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    slavedevversion_->clear();
  }
  clear_has_slavedevversion();
}
inline const ::std::string& OurRobotDevInfo::slavedevversion() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.OurRobotDevInfo.slaveDevVersion)
  return *slavedevversion_;
}
inline void OurRobotDevInfo::set_slavedevversion(const ::std::string& value) {
  set_has_slavedevversion();
  if (slavedevversion_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    slavedevversion_ = new ::std::string;
  }
  slavedevversion_->assign(value);
  // @@protoc_insertion_point(field_set:aubo.robot.communication.OurRobotDevInfo.slaveDevVersion)
}
inline void OurRobotDevInfo::set_slavedevversion(const char* value) {
  set_has_slavedevversion();
  if (slavedevversion_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    slavedevversion_ = new ::std::string;
  }
  slavedevversion_->assign(value);
  // @@protoc_insertion_point(field_set_char:aubo.robot.communication.OurRobotDevInfo.slaveDevVersion)
}
inline void OurRobotDevInfo::set_slavedevversion(const char* value, size_t size) {
  set_has_slavedevversion();
  if (slavedevversion_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    slavedevversion_ = new ::std::string;
  }
  slavedevversion_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:aubo.robot.communication.OurRobotDevInfo.slaveDevVersion)
}
inline ::std::string* OurRobotDevInfo::mutable_slavedevversion() {
  set_has_slavedevversion();
  if (slavedevversion_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    slavedevversion_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:aubo.robot.communication.OurRobotDevInfo.slaveDevVersion)
  return slavedevversion_;
}
inline ::std::string* OurRobotDevInfo::release_slavedevversion() {
  clear_has_slavedevversion();
  if (slavedevversion_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = slavedevversion_;
    slavedevversion_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void OurRobotDevInfo::set_allocated_slavedevversion(::std::string* slavedevversion) {
  if (slavedevversion_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete slavedevversion_;
  }
  if (slavedevversion) {
    set_has_slavedevversion();
    slavedevversion_ = slavedevversion;
  } else {
    clear_has_slavedevversion();
    slavedevversion_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.communication.OurRobotDevInfo.slaveDevVersion)
}

// required string extendIoBoardVersion = 9;
inline bool OurRobotDevInfo::has_extendioboardversion() const {
  return (_has_bits_[0] & 0x00000100u) != 0;
}
inline void OurRobotDevInfo::set_has_extendioboardversion() {
  _has_bits_[0] |= 0x00000100u;
}
inline void OurRobotDevInfo::clear_has_extendioboardversion() {
  _has_bits_[0] &= ~0x00000100u;
}
inline void OurRobotDevInfo::clear_extendioboardversion() {
  if (extendioboardversion_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    extendioboardversion_->clear();
  }
  clear_has_extendioboardversion();
}
inline const ::std::string& OurRobotDevInfo::extendioboardversion() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.OurRobotDevInfo.extendIoBoardVersion)
  return *extendioboardversion_;
}
inline void OurRobotDevInfo::set_extendioboardversion(const ::std::string& value) {
  set_has_extendioboardversion();
  if (extendioboardversion_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    extendioboardversion_ = new ::std::string;
  }
  extendioboardversion_->assign(value);
  // @@protoc_insertion_point(field_set:aubo.robot.communication.OurRobotDevInfo.extendIoBoardVersion)
}
inline void OurRobotDevInfo::set_extendioboardversion(const char* value) {
  set_has_extendioboardversion();
  if (extendioboardversion_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    extendioboardversion_ = new ::std::string;
  }
  extendioboardversion_->assign(value);
  // @@protoc_insertion_point(field_set_char:aubo.robot.communication.OurRobotDevInfo.extendIoBoardVersion)
}
inline void OurRobotDevInfo::set_extendioboardversion(const char* value, size_t size) {
  set_has_extendioboardversion();
  if (extendioboardversion_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    extendioboardversion_ = new ::std::string;
  }
  extendioboardversion_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:aubo.robot.communication.OurRobotDevInfo.extendIoBoardVersion)
}
inline ::std::string* OurRobotDevInfo::mutable_extendioboardversion() {
  set_has_extendioboardversion();
  if (extendioboardversion_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    extendioboardversion_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:aubo.robot.communication.OurRobotDevInfo.extendIoBoardVersion)
  return extendioboardversion_;
}
inline ::std::string* OurRobotDevInfo::release_extendioboardversion() {
  clear_has_extendioboardversion();
  if (extendioboardversion_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = extendioboardversion_;
    extendioboardversion_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void OurRobotDevInfo::set_allocated_extendioboardversion(::std::string* extendioboardversion) {
  if (extendioboardversion_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete extendioboardversion_;
  }
  if (extendioboardversion) {
    set_has_extendioboardversion();
    extendioboardversion_ = extendioboardversion;
  } else {
    clear_has_extendioboardversion();
    extendioboardversion_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.communication.OurRobotDevInfo.extendIoBoardVersion)
}

// -------------------------------------------------------------------

// RobotDiagnosis

// required uint32 armCanbusStatus = 1;
inline bool RobotDiagnosis::has_armcanbusstatus() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void RobotDiagnosis::set_has_armcanbusstatus() {
  _has_bits_[0] |= 0x00000001u;
}
inline void RobotDiagnosis::clear_has_armcanbusstatus() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void RobotDiagnosis::clear_armcanbusstatus() {
  armcanbusstatus_ = 0u;
  clear_has_armcanbusstatus();
}
inline ::google::protobuf::uint32 RobotDiagnosis::armcanbusstatus() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.RobotDiagnosis.armCanbusStatus)
  return armcanbusstatus_;
}
inline void RobotDiagnosis::set_armcanbusstatus(::google::protobuf::uint32 value) {
  set_has_armcanbusstatus();
  armcanbusstatus_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.RobotDiagnosis.armCanbusStatus)
}

// required float armPowerCurrent = 2;
inline bool RobotDiagnosis::has_armpowercurrent() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void RobotDiagnosis::set_has_armpowercurrent() {
  _has_bits_[0] |= 0x00000002u;
}
inline void RobotDiagnosis::clear_has_armpowercurrent() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void RobotDiagnosis::clear_armpowercurrent() {
  armpowercurrent_ = 0;
  clear_has_armpowercurrent();
}
inline float RobotDiagnosis::armpowercurrent() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.RobotDiagnosis.armPowerCurrent)
  return armpowercurrent_;
}
inline void RobotDiagnosis::set_armpowercurrent(float value) {
  set_has_armpowercurrent();
  armpowercurrent_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.RobotDiagnosis.armPowerCurrent)
}

// required float armPowerVoltage = 3;
inline bool RobotDiagnosis::has_armpowervoltage() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void RobotDiagnosis::set_has_armpowervoltage() {
  _has_bits_[0] |= 0x00000004u;
}
inline void RobotDiagnosis::clear_has_armpowervoltage() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void RobotDiagnosis::clear_armpowervoltage() {
  armpowervoltage_ = 0;
  clear_has_armpowervoltage();
}
inline float RobotDiagnosis::armpowervoltage() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.RobotDiagnosis.armPowerVoltage)
  return armpowervoltage_;
}
inline void RobotDiagnosis::set_armpowervoltage(float value) {
  set_has_armpowervoltage();
  armpowervoltage_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.RobotDiagnosis.armPowerVoltage)
}

// required bool armPowerStatus = 4;
inline bool RobotDiagnosis::has_armpowerstatus() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void RobotDiagnosis::set_has_armpowerstatus() {
  _has_bits_[0] |= 0x00000008u;
}
inline void RobotDiagnosis::clear_has_armpowerstatus() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void RobotDiagnosis::clear_armpowerstatus() {
  armpowerstatus_ = false;
  clear_has_armpowerstatus();
}
inline bool RobotDiagnosis::armpowerstatus() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.RobotDiagnosis.armPowerStatus)
  return armpowerstatus_;
}
inline void RobotDiagnosis::set_armpowerstatus(bool value) {
  set_has_armpowerstatus();
  armpowerstatus_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.RobotDiagnosis.armPowerStatus)
}

// required int32 contorllerTemp = 5;
inline bool RobotDiagnosis::has_contorllertemp() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void RobotDiagnosis::set_has_contorllertemp() {
  _has_bits_[0] |= 0x00000010u;
}
inline void RobotDiagnosis::clear_has_contorllertemp() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void RobotDiagnosis::clear_contorllertemp() {
  contorllertemp_ = 0;
  clear_has_contorllertemp();
}
inline ::google::protobuf::int32 RobotDiagnosis::contorllertemp() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.RobotDiagnosis.contorllerTemp)
  return contorllertemp_;
}
inline void RobotDiagnosis::set_contorllertemp(::google::protobuf::int32 value) {
  set_has_contorllertemp();
  contorllertemp_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.RobotDiagnosis.contorllerTemp)
}

// required uint32 contorllerHumidity = 6;
inline bool RobotDiagnosis::has_contorllerhumidity() const {
  return (_has_bits_[0] & 0x00000020u) != 0;
}
inline void RobotDiagnosis::set_has_contorllerhumidity() {
  _has_bits_[0] |= 0x00000020u;
}
inline void RobotDiagnosis::clear_has_contorllerhumidity() {
  _has_bits_[0] &= ~0x00000020u;
}
inline void RobotDiagnosis::clear_contorllerhumidity() {
  contorllerhumidity_ = 0u;
  clear_has_contorllerhumidity();
}
inline ::google::protobuf::uint32 RobotDiagnosis::contorllerhumidity() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.RobotDiagnosis.contorllerHumidity)
  return contorllerhumidity_;
}
inline void RobotDiagnosis::set_contorllerhumidity(::google::protobuf::uint32 value) {
  set_has_contorllerhumidity();
  contorllerhumidity_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.RobotDiagnosis.contorllerHumidity)
}

// required bool remoteHalt = 7;
inline bool RobotDiagnosis::has_remotehalt() const {
  return (_has_bits_[0] & 0x00000040u) != 0;
}
inline void RobotDiagnosis::set_has_remotehalt() {
  _has_bits_[0] |= 0x00000040u;
}
inline void RobotDiagnosis::clear_has_remotehalt() {
  _has_bits_[0] &= ~0x00000040u;
}
inline void RobotDiagnosis::clear_remotehalt() {
  remotehalt_ = false;
  clear_has_remotehalt();
}
inline bool RobotDiagnosis::remotehalt() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.RobotDiagnosis.remoteHalt)
  return remotehalt_;
}
inline void RobotDiagnosis::set_remotehalt(bool value) {
  set_has_remotehalt();
  remotehalt_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.RobotDiagnosis.remoteHalt)
}

// required bool softEmergency = 8;
inline bool RobotDiagnosis::has_softemergency() const {
  return (_has_bits_[0] & 0x00000080u) != 0;
}
inline void RobotDiagnosis::set_has_softemergency() {
  _has_bits_[0] |= 0x00000080u;
}
inline void RobotDiagnosis::clear_has_softemergency() {
  _has_bits_[0] &= ~0x00000080u;
}
inline void RobotDiagnosis::clear_softemergency() {
  softemergency_ = false;
  clear_has_softemergency();
}
inline bool RobotDiagnosis::softemergency() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.RobotDiagnosis.softEmergency)
  return softemergency_;
}
inline void RobotDiagnosis::set_softemergency(bool value) {
  set_has_softemergency();
  softemergency_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.RobotDiagnosis.softEmergency)
}

// required bool remoteEmergency = 9;
inline bool RobotDiagnosis::has_remoteemergency() const {
  return (_has_bits_[0] & 0x00000100u) != 0;
}
inline void RobotDiagnosis::set_has_remoteemergency() {
  _has_bits_[0] |= 0x00000100u;
}
inline void RobotDiagnosis::clear_has_remoteemergency() {
  _has_bits_[0] &= ~0x00000100u;
}
inline void RobotDiagnosis::clear_remoteemergency() {
  remoteemergency_ = false;
  clear_has_remoteemergency();
}
inline bool RobotDiagnosis::remoteemergency() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.RobotDiagnosis.remoteEmergency)
  return remoteemergency_;
}
inline void RobotDiagnosis::set_remoteemergency(bool value) {
  set_has_remoteemergency();
  remoteemergency_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.RobotDiagnosis.remoteEmergency)
}

// required bool robotCollision = 10;
inline bool RobotDiagnosis::has_robotcollision() const {
  return (_has_bits_[0] & 0x00000200u) != 0;
}
inline void RobotDiagnosis::set_has_robotcollision() {
  _has_bits_[0] |= 0x00000200u;
}
inline void RobotDiagnosis::clear_has_robotcollision() {
  _has_bits_[0] &= ~0x00000200u;
}
inline void RobotDiagnosis::clear_robotcollision() {
  robotcollision_ = false;
  clear_has_robotcollision();
}
inline bool RobotDiagnosis::robotcollision() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.RobotDiagnosis.robotCollision)
  return robotcollision_;
}
inline void RobotDiagnosis::set_robotcollision(bool value) {
  set_has_robotcollision();
  robotcollision_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.RobotDiagnosis.robotCollision)
}

// required bool forceControlMode = 11;
inline bool RobotDiagnosis::has_forcecontrolmode() const {
  return (_has_bits_[0] & 0x00000400u) != 0;
}
inline void RobotDiagnosis::set_has_forcecontrolmode() {
  _has_bits_[0] |= 0x00000400u;
}
inline void RobotDiagnosis::clear_has_forcecontrolmode() {
  _has_bits_[0] &= ~0x00000400u;
}
inline void RobotDiagnosis::clear_forcecontrolmode() {
  forcecontrolmode_ = false;
  clear_has_forcecontrolmode();
}
inline bool RobotDiagnosis::forcecontrolmode() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.RobotDiagnosis.forceControlMode)
  return forcecontrolmode_;
}
inline void RobotDiagnosis::set_forcecontrolmode(bool value) {
  set_has_forcecontrolmode();
  forcecontrolmode_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.RobotDiagnosis.forceControlMode)
}

// required bool brakeStuats = 12;
inline bool RobotDiagnosis::has_brakestuats() const {
  return (_has_bits_[0] & 0x00000800u) != 0;
}
inline void RobotDiagnosis::set_has_brakestuats() {
  _has_bits_[0] |= 0x00000800u;
}
inline void RobotDiagnosis::clear_has_brakestuats() {
  _has_bits_[0] &= ~0x00000800u;
}
inline void RobotDiagnosis::clear_brakestuats() {
  brakestuats_ = false;
  clear_has_brakestuats();
}
inline bool RobotDiagnosis::brakestuats() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.RobotDiagnosis.brakeStuats)
  return brakestuats_;
}
inline void RobotDiagnosis::set_brakestuats(bool value) {
  set_has_brakestuats();
  brakestuats_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.RobotDiagnosis.brakeStuats)
}

// required float robotEndSpeed = 13;
inline bool RobotDiagnosis::has_robotendspeed() const {
  return (_has_bits_[0] & 0x00001000u) != 0;
}
inline void RobotDiagnosis::set_has_robotendspeed() {
  _has_bits_[0] |= 0x00001000u;
}
inline void RobotDiagnosis::clear_has_robotendspeed() {
  _has_bits_[0] &= ~0x00001000u;
}
inline void RobotDiagnosis::clear_robotendspeed() {
  robotendspeed_ = 0;
  clear_has_robotendspeed();
}
inline float RobotDiagnosis::robotendspeed() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.RobotDiagnosis.robotEndSpeed)
  return robotendspeed_;
}
inline void RobotDiagnosis::set_robotendspeed(float value) {
  set_has_robotendspeed();
  robotendspeed_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.RobotDiagnosis.robotEndSpeed)
}

// required int32 robotMaxAcc = 14;
inline bool RobotDiagnosis::has_robotmaxacc() const {
  return (_has_bits_[0] & 0x00002000u) != 0;
}
inline void RobotDiagnosis::set_has_robotmaxacc() {
  _has_bits_[0] |= 0x00002000u;
}
inline void RobotDiagnosis::clear_has_robotmaxacc() {
  _has_bits_[0] &= ~0x00002000u;
}
inline void RobotDiagnosis::clear_robotmaxacc() {
  robotmaxacc_ = 0;
  clear_has_robotmaxacc();
}
inline ::google::protobuf::int32 RobotDiagnosis::robotmaxacc() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.RobotDiagnosis.robotMaxAcc)
  return robotmaxacc_;
}
inline void RobotDiagnosis::set_robotmaxacc(::google::protobuf::int32 value) {
  set_has_robotmaxacc();
  robotmaxacc_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.RobotDiagnosis.robotMaxAcc)
}

// required bool orpeStatus = 15;
inline bool RobotDiagnosis::has_orpestatus() const {
  return (_has_bits_[0] & 0x00004000u) != 0;
}
inline void RobotDiagnosis::set_has_orpestatus() {
  _has_bits_[0] |= 0x00004000u;
}
inline void RobotDiagnosis::clear_has_orpestatus() {
  _has_bits_[0] &= ~0x00004000u;
}
inline void RobotDiagnosis::clear_orpestatus() {
  orpestatus_ = false;
  clear_has_orpestatus();
}
inline bool RobotDiagnosis::orpestatus() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.RobotDiagnosis.orpeStatus)
  return orpestatus_;
}
inline void RobotDiagnosis::set_orpestatus(bool value) {
  set_has_orpestatus();
  orpestatus_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.RobotDiagnosis.orpeStatus)
}

// required bool enableReadPose = 16;
inline bool RobotDiagnosis::has_enablereadpose() const {
  return (_has_bits_[0] & 0x00008000u) != 0;
}
inline void RobotDiagnosis::set_has_enablereadpose() {
  _has_bits_[0] |= 0x00008000u;
}
inline void RobotDiagnosis::clear_has_enablereadpose() {
  _has_bits_[0] &= ~0x00008000u;
}
inline void RobotDiagnosis::clear_enablereadpose() {
  enablereadpose_ = false;
  clear_has_enablereadpose();
}
inline bool RobotDiagnosis::enablereadpose() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.RobotDiagnosis.enableReadPose)
  return enablereadpose_;
}
inline void RobotDiagnosis::set_enablereadpose(bool value) {
  set_has_enablereadpose();
  enablereadpose_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.RobotDiagnosis.enableReadPose)
}

// required bool robotMountingPoseChanged = 17;
inline bool RobotDiagnosis::has_robotmountingposechanged() const {
  return (_has_bits_[0] & 0x00010000u) != 0;
}
inline void RobotDiagnosis::set_has_robotmountingposechanged() {
  _has_bits_[0] |= 0x00010000u;
}
inline void RobotDiagnosis::clear_has_robotmountingposechanged() {
  _has_bits_[0] &= ~0x00010000u;
}
inline void RobotDiagnosis::clear_robotmountingposechanged() {
  robotmountingposechanged_ = false;
  clear_has_robotmountingposechanged();
}
inline bool RobotDiagnosis::robotmountingposechanged() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.RobotDiagnosis.robotMountingPoseChanged)
  return robotmountingposechanged_;
}
inline void RobotDiagnosis::set_robotmountingposechanged(bool value) {
  set_has_robotmountingposechanged();
  robotmountingposechanged_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.RobotDiagnosis.robotMountingPoseChanged)
}

// required bool encoderErrorStatus = 18;
inline bool RobotDiagnosis::has_encodererrorstatus() const {
  return (_has_bits_[0] & 0x00020000u) != 0;
}
inline void RobotDiagnosis::set_has_encodererrorstatus() {
  _has_bits_[0] |= 0x00020000u;
}
inline void RobotDiagnosis::clear_has_encodererrorstatus() {
  _has_bits_[0] &= ~0x00020000u;
}
inline void RobotDiagnosis::clear_encodererrorstatus() {
  encodererrorstatus_ = false;
  clear_has_encodererrorstatus();
}
inline bool RobotDiagnosis::encodererrorstatus() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.RobotDiagnosis.encoderErrorStatus)
  return encodererrorstatus_;
}
inline void RobotDiagnosis::set_encodererrorstatus(bool value) {
  set_has_encodererrorstatus();
  encodererrorstatus_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.RobotDiagnosis.encoderErrorStatus)
}

// required bool staticCollisionDetect = 19;
inline bool RobotDiagnosis::has_staticcollisiondetect() const {
  return (_has_bits_[0] & 0x00040000u) != 0;
}
inline void RobotDiagnosis::set_has_staticcollisiondetect() {
  _has_bits_[0] |= 0x00040000u;
}
inline void RobotDiagnosis::clear_has_staticcollisiondetect() {
  _has_bits_[0] &= ~0x00040000u;
}
inline void RobotDiagnosis::clear_staticcollisiondetect() {
  staticcollisiondetect_ = false;
  clear_has_staticcollisiondetect();
}
inline bool RobotDiagnosis::staticcollisiondetect() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.RobotDiagnosis.staticCollisionDetect)
  return staticcollisiondetect_;
}
inline void RobotDiagnosis::set_staticcollisiondetect(bool value) {
  set_has_staticcollisiondetect();
  staticcollisiondetect_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.RobotDiagnosis.staticCollisionDetect)
}

// required uint32 jointCollisionDetect = 20;
inline bool RobotDiagnosis::has_jointcollisiondetect() const {
  return (_has_bits_[0] & 0x00080000u) != 0;
}
inline void RobotDiagnosis::set_has_jointcollisiondetect() {
  _has_bits_[0] |= 0x00080000u;
}
inline void RobotDiagnosis::clear_has_jointcollisiondetect() {
  _has_bits_[0] &= ~0x00080000u;
}
inline void RobotDiagnosis::clear_jointcollisiondetect() {
  jointcollisiondetect_ = 0u;
  clear_has_jointcollisiondetect();
}
inline ::google::protobuf::uint32 RobotDiagnosis::jointcollisiondetect() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.RobotDiagnosis.jointCollisionDetect)
  return jointcollisiondetect_;
}
inline void RobotDiagnosis::set_jointcollisiondetect(::google::protobuf::uint32 value) {
  set_has_jointcollisiondetect();
  jointcollisiondetect_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.RobotDiagnosis.jointCollisionDetect)
}

// required bool encoderLinesError = 21;
inline bool RobotDiagnosis::has_encoderlineserror() const {
  return (_has_bits_[0] & 0x00100000u) != 0;
}
inline void RobotDiagnosis::set_has_encoderlineserror() {
  _has_bits_[0] |= 0x00100000u;
}
inline void RobotDiagnosis::clear_has_encoderlineserror() {
  _has_bits_[0] &= ~0x00100000u;
}
inline void RobotDiagnosis::clear_encoderlineserror() {
  encoderlineserror_ = false;
  clear_has_encoderlineserror();
}
inline bool RobotDiagnosis::encoderlineserror() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.RobotDiagnosis.encoderLinesError)
  return encoderlineserror_;
}
inline void RobotDiagnosis::set_encoderlineserror(bool value) {
  set_has_encoderlineserror();
  encoderlineserror_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.RobotDiagnosis.encoderLinesError)
}

// required bool jointErrorStatus = 22;
inline bool RobotDiagnosis::has_jointerrorstatus() const {
  return (_has_bits_[0] & 0x00200000u) != 0;
}
inline void RobotDiagnosis::set_has_jointerrorstatus() {
  _has_bits_[0] |= 0x00200000u;
}
inline void RobotDiagnosis::clear_has_jointerrorstatus() {
  _has_bits_[0] &= ~0x00200000u;
}
inline void RobotDiagnosis::clear_jointerrorstatus() {
  jointerrorstatus_ = false;
  clear_has_jointerrorstatus();
}
inline bool RobotDiagnosis::jointerrorstatus() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.RobotDiagnosis.jointErrorStatus)
  return jointerrorstatus_;
}
inline void RobotDiagnosis::set_jointerrorstatus(bool value) {
  set_has_jointerrorstatus();
  jointerrorstatus_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.RobotDiagnosis.jointErrorStatus)
}

// required bool singularityOverSpeedAlarm = 23;
inline bool RobotDiagnosis::has_singularityoverspeedalarm() const {
  return (_has_bits_[0] & 0x00400000u) != 0;
}
inline void RobotDiagnosis::set_has_singularityoverspeedalarm() {
  _has_bits_[0] |= 0x00400000u;
}
inline void RobotDiagnosis::clear_has_singularityoverspeedalarm() {
  _has_bits_[0] &= ~0x00400000u;
}
inline void RobotDiagnosis::clear_singularityoverspeedalarm() {
  singularityoverspeedalarm_ = false;
  clear_has_singularityoverspeedalarm();
}
inline bool RobotDiagnosis::singularityoverspeedalarm() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.RobotDiagnosis.singularityOverSpeedAlarm)
  return singularityoverspeedalarm_;
}
inline void RobotDiagnosis::set_singularityoverspeedalarm(bool value) {
  set_has_singularityoverspeedalarm();
  singularityoverspeedalarm_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.RobotDiagnosis.singularityOverSpeedAlarm)
}

// required bool robotCurrentAlarm = 24;
inline bool RobotDiagnosis::has_robotcurrentalarm() const {
  return (_has_bits_[0] & 0x00800000u) != 0;
}
inline void RobotDiagnosis::set_has_robotcurrentalarm() {
  _has_bits_[0] |= 0x00800000u;
}
inline void RobotDiagnosis::clear_has_robotcurrentalarm() {
  _has_bits_[0] &= ~0x00800000u;
}
inline void RobotDiagnosis::clear_robotcurrentalarm() {
  robotcurrentalarm_ = false;
  clear_has_robotcurrentalarm();
}
inline bool RobotDiagnosis::robotcurrentalarm() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.RobotDiagnosis.robotCurrentAlarm)
  return robotcurrentalarm_;
}
inline void RobotDiagnosis::set_robotcurrentalarm(bool value) {
  set_has_robotcurrentalarm();
  robotcurrentalarm_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.RobotDiagnosis.robotCurrentAlarm)
}

// required int32 toolIoError = 25;
inline bool RobotDiagnosis::has_toolioerror() const {
  return (_has_bits_[0] & 0x01000000u) != 0;
}
inline void RobotDiagnosis::set_has_toolioerror() {
  _has_bits_[0] |= 0x01000000u;
}
inline void RobotDiagnosis::clear_has_toolioerror() {
  _has_bits_[0] &= ~0x01000000u;
}
inline void RobotDiagnosis::clear_toolioerror() {
  toolioerror_ = 0;
  clear_has_toolioerror();
}
inline ::google::protobuf::int32 RobotDiagnosis::toolioerror() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.RobotDiagnosis.toolIoError)
  return toolioerror_;
}
inline void RobotDiagnosis::set_toolioerror(::google::protobuf::int32 value) {
  set_has_toolioerror();
  toolioerror_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.RobotDiagnosis.toolIoError)
}

// required bool robotMountingPoseWarning = 26;
inline bool RobotDiagnosis::has_robotmountingposewarning() const {
  return (_has_bits_[0] & 0x02000000u) != 0;
}
inline void RobotDiagnosis::set_has_robotmountingposewarning() {
  _has_bits_[0] |= 0x02000000u;
}
inline void RobotDiagnosis::clear_has_robotmountingposewarning() {
  _has_bits_[0] &= ~0x02000000u;
}
inline void RobotDiagnosis::clear_robotmountingposewarning() {
  robotmountingposewarning_ = false;
  clear_has_robotmountingposewarning();
}
inline bool RobotDiagnosis::robotmountingposewarning() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.RobotDiagnosis.robotMountingPoseWarning)
  return robotmountingposewarning_;
}
inline void RobotDiagnosis::set_robotmountingposewarning(bool value) {
  set_has_robotmountingposewarning();
  robotmountingposewarning_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.RobotDiagnosis.robotMountingPoseWarning)
}

// required uint32 macTargetPosBufferSize = 27;
inline bool RobotDiagnosis::has_mactargetposbuffersize() const {
  return (_has_bits_[0] & 0x04000000u) != 0;
}
inline void RobotDiagnosis::set_has_mactargetposbuffersize() {
  _has_bits_[0] |= 0x04000000u;
}
inline void RobotDiagnosis::clear_has_mactargetposbuffersize() {
  _has_bits_[0] &= ~0x04000000u;
}
inline void RobotDiagnosis::clear_mactargetposbuffersize() {
  mactargetposbuffersize_ = 0u;
  clear_has_mactargetposbuffersize();
}
inline ::google::protobuf::uint32 RobotDiagnosis::mactargetposbuffersize() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.RobotDiagnosis.macTargetPosBufferSize)
  return mactargetposbuffersize_;
}
inline void RobotDiagnosis::set_mactargetposbuffersize(::google::protobuf::uint32 value) {
  set_has_mactargetposbuffersize();
  mactargetposbuffersize_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.RobotDiagnosis.macTargetPosBufferSize)
}

// required uint32 macTargetPosDataSize = 28;
inline bool RobotDiagnosis::has_mactargetposdatasize() const {
  return (_has_bits_[0] & 0x08000000u) != 0;
}
inline void RobotDiagnosis::set_has_mactargetposdatasize() {
  _has_bits_[0] |= 0x08000000u;
}
inline void RobotDiagnosis::clear_has_mactargetposdatasize() {
  _has_bits_[0] &= ~0x08000000u;
}
inline void RobotDiagnosis::clear_mactargetposdatasize() {
  mactargetposdatasize_ = 0u;
  clear_has_mactargetposdatasize();
}
inline ::google::protobuf::uint32 RobotDiagnosis::mactargetposdatasize() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.RobotDiagnosis.macTargetPosDataSize)
  return mactargetposdatasize_;
}
inline void RobotDiagnosis::set_mactargetposdatasize(::google::protobuf::uint32 value) {
  set_has_mactargetposdatasize();
  mactargetposdatasize_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.RobotDiagnosis.macTargetPosDataSize)
}

// required uint32 macDataInterruptWarning = 29;
inline bool RobotDiagnosis::has_macdatainterruptwarning() const {
  return (_has_bits_[0] & 0x10000000u) != 0;
}
inline void RobotDiagnosis::set_has_macdatainterruptwarning() {
  _has_bits_[0] |= 0x10000000u;
}
inline void RobotDiagnosis::clear_has_macdatainterruptwarning() {
  _has_bits_[0] &= ~0x10000000u;
}
inline void RobotDiagnosis::clear_macdatainterruptwarning() {
  macdatainterruptwarning_ = 0u;
  clear_has_macdatainterruptwarning();
}
inline ::google::protobuf::uint32 RobotDiagnosis::macdatainterruptwarning() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.RobotDiagnosis.macDataInterruptWarning)
  return macdatainterruptwarning_;
}
inline void RobotDiagnosis::set_macdatainterruptwarning(::google::protobuf::uint32 value) {
  set_has_macdatainterruptwarning();
  macdatainterruptwarning_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.RobotDiagnosis.macDataInterruptWarning)
}

// optional uint32 controlBoardAbnormalStateFlag = 30;
inline bool RobotDiagnosis::has_controlboardabnormalstateflag() const {
  return (_has_bits_[0] & 0x20000000u) != 0;
}
inline void RobotDiagnosis::set_has_controlboardabnormalstateflag() {
  _has_bits_[0] |= 0x20000000u;
}
inline void RobotDiagnosis::clear_has_controlboardabnormalstateflag() {
  _has_bits_[0] &= ~0x20000000u;
}
inline void RobotDiagnosis::clear_controlboardabnormalstateflag() {
  controlboardabnormalstateflag_ = 0u;
  clear_has_controlboardabnormalstateflag();
}
inline ::google::protobuf::uint32 RobotDiagnosis::controlboardabnormalstateflag() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.RobotDiagnosis.controlBoardAbnormalStateFlag)
  return controlboardabnormalstateflag_;
}
inline void RobotDiagnosis::set_controlboardabnormalstateflag(::google::protobuf::uint32 value) {
  set_has_controlboardabnormalstateflag();
  controlboardabnormalstateflag_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.RobotDiagnosis.controlBoardAbnormalStateFlag)
}

// -------------------------------------------------------------------

// RobotExtDiagnosis

// required uint32 robotState = 1;
inline bool RobotExtDiagnosis::has_robotstate() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void RobotExtDiagnosis::set_has_robotstate() {
  _has_bits_[0] |= 0x00000001u;
}
inline void RobotExtDiagnosis::clear_has_robotstate() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void RobotExtDiagnosis::clear_robotstate() {
  robotstate_ = 0u;
  clear_has_robotstate();
}
inline ::google::protobuf::uint32 RobotExtDiagnosis::robotstate() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.RobotExtDiagnosis.robotState)
  return robotstate_;
}
inline void RobotExtDiagnosis::set_robotstate(::google::protobuf::uint32 value) {
  set_has_robotstate();
  robotstate_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.RobotExtDiagnosis.robotState)
}

// required uint32 robotStatus = 2;
inline bool RobotExtDiagnosis::has_robotstatus() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void RobotExtDiagnosis::set_has_robotstatus() {
  _has_bits_[0] |= 0x00000002u;
}
inline void RobotExtDiagnosis::clear_has_robotstatus() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void RobotExtDiagnosis::clear_robotstatus() {
  robotstatus_ = 0u;
  clear_has_robotstatus();
}
inline ::google::protobuf::uint32 RobotExtDiagnosis::robotstatus() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.RobotExtDiagnosis.robotStatus)
  return robotstatus_;
}
inline void RobotExtDiagnosis::set_robotstatus(::google::protobuf::uint32 value) {
  set_has_robotstatus();
  robotstatus_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.RobotExtDiagnosis.robotStatus)
}

// repeated float targetPos = 3;
inline int RobotExtDiagnosis::targetpos_size() const {
  return targetpos_.size();
}
inline void RobotExtDiagnosis::clear_targetpos() {
  targetpos_.Clear();
}
inline float RobotExtDiagnosis::targetpos(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.RobotExtDiagnosis.targetPos)
  return targetpos_.Get(index);
}
inline void RobotExtDiagnosis::set_targetpos(int index, float value) {
  targetpos_.Set(index, value);
  // @@protoc_insertion_point(field_set:aubo.robot.communication.RobotExtDiagnosis.targetPos)
}
inline void RobotExtDiagnosis::add_targetpos(float value) {
  targetpos_.Add(value);
  // @@protoc_insertion_point(field_add:aubo.robot.communication.RobotExtDiagnosis.targetPos)
}
inline const ::google::protobuf::RepeatedField< float >&
RobotExtDiagnosis::targetpos() const {
  // @@protoc_insertion_point(field_list:aubo.robot.communication.RobotExtDiagnosis.targetPos)
  return targetpos_;
}
inline ::google::protobuf::RepeatedField< float >*
RobotExtDiagnosis::mutable_targetpos() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.communication.RobotExtDiagnosis.targetPos)
  return &targetpos_;
}

// repeated float theoreticalSpeed = 4;
inline int RobotExtDiagnosis::theoreticalspeed_size() const {
  return theoreticalspeed_.size();
}
inline void RobotExtDiagnosis::clear_theoreticalspeed() {
  theoreticalspeed_.Clear();
}
inline float RobotExtDiagnosis::theoreticalspeed(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.RobotExtDiagnosis.theoreticalSpeed)
  return theoreticalspeed_.Get(index);
}
inline void RobotExtDiagnosis::set_theoreticalspeed(int index, float value) {
  theoreticalspeed_.Set(index, value);
  // @@protoc_insertion_point(field_set:aubo.robot.communication.RobotExtDiagnosis.theoreticalSpeed)
}
inline void RobotExtDiagnosis::add_theoreticalspeed(float value) {
  theoreticalspeed_.Add(value);
  // @@protoc_insertion_point(field_add:aubo.robot.communication.RobotExtDiagnosis.theoreticalSpeed)
}
inline const ::google::protobuf::RepeatedField< float >&
RobotExtDiagnosis::theoreticalspeed() const {
  // @@protoc_insertion_point(field_list:aubo.robot.communication.RobotExtDiagnosis.theoreticalSpeed)
  return theoreticalspeed_;
}
inline ::google::protobuf::RepeatedField< float >*
RobotExtDiagnosis::mutable_theoreticalspeed() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.communication.RobotExtDiagnosis.theoreticalSpeed)
  return &theoreticalspeed_;
}

// repeated float theoreticalAcc = 5;
inline int RobotExtDiagnosis::theoreticalacc_size() const {
  return theoreticalacc_.size();
}
inline void RobotExtDiagnosis::clear_theoreticalacc() {
  theoreticalacc_.Clear();
}
inline float RobotExtDiagnosis::theoreticalacc(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.RobotExtDiagnosis.theoreticalAcc)
  return theoreticalacc_.Get(index);
}
inline void RobotExtDiagnosis::set_theoreticalacc(int index, float value) {
  theoreticalacc_.Set(index, value);
  // @@protoc_insertion_point(field_set:aubo.robot.communication.RobotExtDiagnosis.theoreticalAcc)
}
inline void RobotExtDiagnosis::add_theoreticalacc(float value) {
  theoreticalacc_.Add(value);
  // @@protoc_insertion_point(field_add:aubo.robot.communication.RobotExtDiagnosis.theoreticalAcc)
}
inline const ::google::protobuf::RepeatedField< float >&
RobotExtDiagnosis::theoreticalacc() const {
  // @@protoc_insertion_point(field_list:aubo.robot.communication.RobotExtDiagnosis.theoreticalAcc)
  return theoreticalacc_;
}
inline ::google::protobuf::RepeatedField< float >*
RobotExtDiagnosis::mutable_theoreticalacc() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.communication.RobotExtDiagnosis.theoreticalAcc)
  return &theoreticalacc_;
}

// repeated uint32 theoreticalCurrent = 6;
inline int RobotExtDiagnosis::theoreticalcurrent_size() const {
  return theoreticalcurrent_.size();
}
inline void RobotExtDiagnosis::clear_theoreticalcurrent() {
  theoreticalcurrent_.Clear();
}
inline ::google::protobuf::uint32 RobotExtDiagnosis::theoreticalcurrent(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.RobotExtDiagnosis.theoreticalCurrent)
  return theoreticalcurrent_.Get(index);
}
inline void RobotExtDiagnosis::set_theoreticalcurrent(int index, ::google::protobuf::uint32 value) {
  theoreticalcurrent_.Set(index, value);
  // @@protoc_insertion_point(field_set:aubo.robot.communication.RobotExtDiagnosis.theoreticalCurrent)
}
inline void RobotExtDiagnosis::add_theoreticalcurrent(::google::protobuf::uint32 value) {
  theoreticalcurrent_.Add(value);
  // @@protoc_insertion_point(field_add:aubo.robot.communication.RobotExtDiagnosis.theoreticalCurrent)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
RobotExtDiagnosis::theoreticalcurrent() const {
  // @@protoc_insertion_point(field_list:aubo.robot.communication.RobotExtDiagnosis.theoreticalCurrent)
  return theoreticalcurrent_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
RobotExtDiagnosis::mutable_theoreticalcurrent() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.communication.RobotExtDiagnosis.theoreticalCurrent)
  return &theoreticalcurrent_;
}

// -------------------------------------------------------------------

// RobotSafetyConfig

// repeated uint32 robotReducedConfigJointSpeed = 1;
inline int RobotSafetyConfig::robotreducedconfigjointspeed_size() const {
  return robotreducedconfigjointspeed_.size();
}
inline void RobotSafetyConfig::clear_robotreducedconfigjointspeed() {
  robotreducedconfigjointspeed_.Clear();
}
inline ::google::protobuf::uint32 RobotSafetyConfig::robotreducedconfigjointspeed(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.RobotSafetyConfig.robotReducedConfigJointSpeed)
  return robotreducedconfigjointspeed_.Get(index);
}
inline void RobotSafetyConfig::set_robotreducedconfigjointspeed(int index, ::google::protobuf::uint32 value) {
  robotreducedconfigjointspeed_.Set(index, value);
  // @@protoc_insertion_point(field_set:aubo.robot.communication.RobotSafetyConfig.robotReducedConfigJointSpeed)
}
inline void RobotSafetyConfig::add_robotreducedconfigjointspeed(::google::protobuf::uint32 value) {
  robotreducedconfigjointspeed_.Add(value);
  // @@protoc_insertion_point(field_add:aubo.robot.communication.RobotSafetyConfig.robotReducedConfigJointSpeed)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
RobotSafetyConfig::robotreducedconfigjointspeed() const {
  // @@protoc_insertion_point(field_list:aubo.robot.communication.RobotSafetyConfig.robotReducedConfigJointSpeed)
  return robotreducedconfigjointspeed_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
RobotSafetyConfig::mutable_robotreducedconfigjointspeed() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.communication.RobotSafetyConfig.robotReducedConfigJointSpeed)
  return &robotreducedconfigjointspeed_;
}

// required uint32 robotReducedConfigTcpSpeed = 2;
inline bool RobotSafetyConfig::has_robotreducedconfigtcpspeed() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void RobotSafetyConfig::set_has_robotreducedconfigtcpspeed() {
  _has_bits_[0] |= 0x00000002u;
}
inline void RobotSafetyConfig::clear_has_robotreducedconfigtcpspeed() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void RobotSafetyConfig::clear_robotreducedconfigtcpspeed() {
  robotreducedconfigtcpspeed_ = 0u;
  clear_has_robotreducedconfigtcpspeed();
}
inline ::google::protobuf::uint32 RobotSafetyConfig::robotreducedconfigtcpspeed() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.RobotSafetyConfig.robotReducedConfigTcpSpeed)
  return robotreducedconfigtcpspeed_;
}
inline void RobotSafetyConfig::set_robotreducedconfigtcpspeed(::google::protobuf::uint32 value) {
  set_has_robotreducedconfigtcpspeed();
  robotreducedconfigtcpspeed_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.RobotSafetyConfig.robotReducedConfigTcpSpeed)
}

// required uint32 robotReducedConfigTcpForce = 3;
inline bool RobotSafetyConfig::has_robotreducedconfigtcpforce() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void RobotSafetyConfig::set_has_robotreducedconfigtcpforce() {
  _has_bits_[0] |= 0x00000004u;
}
inline void RobotSafetyConfig::clear_has_robotreducedconfigtcpforce() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void RobotSafetyConfig::clear_robotreducedconfigtcpforce() {
  robotreducedconfigtcpforce_ = 0u;
  clear_has_robotreducedconfigtcpforce();
}
inline ::google::protobuf::uint32 RobotSafetyConfig::robotreducedconfigtcpforce() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.RobotSafetyConfig.robotReducedConfigTcpForce)
  return robotreducedconfigtcpforce_;
}
inline void RobotSafetyConfig::set_robotreducedconfigtcpforce(::google::protobuf::uint32 value) {
  set_has_robotreducedconfigtcpforce();
  robotreducedconfigtcpforce_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.RobotSafetyConfig.robotReducedConfigTcpForce)
}

// required uint32 robotReducedConfigMomentum = 4;
inline bool RobotSafetyConfig::has_robotreducedconfigmomentum() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void RobotSafetyConfig::set_has_robotreducedconfigmomentum() {
  _has_bits_[0] |= 0x00000008u;
}
inline void RobotSafetyConfig::clear_has_robotreducedconfigmomentum() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void RobotSafetyConfig::clear_robotreducedconfigmomentum() {
  robotreducedconfigmomentum_ = 0u;
  clear_has_robotreducedconfigmomentum();
}
inline ::google::protobuf::uint32 RobotSafetyConfig::robotreducedconfigmomentum() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.RobotSafetyConfig.robotReducedConfigMomentum)
  return robotreducedconfigmomentum_;
}
inline void RobotSafetyConfig::set_robotreducedconfigmomentum(::google::protobuf::uint32 value) {
  set_has_robotreducedconfigmomentum();
  robotreducedconfigmomentum_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.RobotSafetyConfig.robotReducedConfigMomentum)
}

// required uint32 robotReducedConfigPower = 5;
inline bool RobotSafetyConfig::has_robotreducedconfigpower() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void RobotSafetyConfig::set_has_robotreducedconfigpower() {
  _has_bits_[0] |= 0x00000010u;
}
inline void RobotSafetyConfig::clear_has_robotreducedconfigpower() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void RobotSafetyConfig::clear_robotreducedconfigpower() {
  robotreducedconfigpower_ = 0u;
  clear_has_robotreducedconfigpower();
}
inline ::google::protobuf::uint32 RobotSafetyConfig::robotreducedconfigpower() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.RobotSafetyConfig.robotReducedConfigPower)
  return robotreducedconfigpower_;
}
inline void RobotSafetyConfig::set_robotreducedconfigpower(::google::protobuf::uint32 value) {
  set_has_robotreducedconfigpower();
  robotreducedconfigpower_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.RobotSafetyConfig.robotReducedConfigPower)
}

// required uint32 robotSafeguradResetConfig = 6;
inline bool RobotSafetyConfig::has_robotsafeguradresetconfig() const {
  return (_has_bits_[0] & 0x00000020u) != 0;
}
inline void RobotSafetyConfig::set_has_robotsafeguradresetconfig() {
  _has_bits_[0] |= 0x00000020u;
}
inline void RobotSafetyConfig::clear_has_robotsafeguradresetconfig() {
  _has_bits_[0] &= ~0x00000020u;
}
inline void RobotSafetyConfig::clear_robotsafeguradresetconfig() {
  robotsafeguradresetconfig_ = 0u;
  clear_has_robotsafeguradresetconfig();
}
inline ::google::protobuf::uint32 RobotSafetyConfig::robotsafeguradresetconfig() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.RobotSafetyConfig.robotSafeguradResetConfig)
  return robotsafeguradresetconfig_;
}
inline void RobotSafetyConfig::set_robotsafeguradresetconfig(::google::protobuf::uint32 value) {
  set_has_robotsafeguradresetconfig();
  robotsafeguradresetconfig_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.RobotSafetyConfig.robotSafeguradResetConfig)
}

// required uint32 robotOperationalModeConfig = 7;
inline bool RobotSafetyConfig::has_robotoperationalmodeconfig() const {
  return (_has_bits_[0] & 0x00000040u) != 0;
}
inline void RobotSafetyConfig::set_has_robotoperationalmodeconfig() {
  _has_bits_[0] |= 0x00000040u;
}
inline void RobotSafetyConfig::clear_has_robotoperationalmodeconfig() {
  _has_bits_[0] &= ~0x00000040u;
}
inline void RobotSafetyConfig::clear_robotoperationalmodeconfig() {
  robotoperationalmodeconfig_ = 0u;
  clear_has_robotoperationalmodeconfig();
}
inline ::google::protobuf::uint32 RobotSafetyConfig::robotoperationalmodeconfig() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.RobotSafetyConfig.robotOperationalModeConfig)
  return robotoperationalmodeconfig_;
}
inline void RobotSafetyConfig::set_robotoperationalmodeconfig(::google::protobuf::uint32 value) {
  set_has_robotoperationalmodeconfig();
  robotoperationalmodeconfig_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.RobotSafetyConfig.robotOperationalModeConfig)
}

// -------------------------------------------------------------------

// OrpeSafetyStatus

// required uint32 orpePause = 1;
inline bool OrpeSafetyStatus::has_orpepause() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void OrpeSafetyStatus::set_has_orpepause() {
  _has_bits_[0] |= 0x00000001u;
}
inline void OrpeSafetyStatus::clear_has_orpepause() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void OrpeSafetyStatus::clear_orpepause() {
  orpepause_ = 0u;
  clear_has_orpepause();
}
inline ::google::protobuf::uint32 OrpeSafetyStatus::orpepause() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.OrpeSafetyStatus.orpePause)
  return orpepause_;
}
inline void OrpeSafetyStatus::set_orpepause(::google::protobuf::uint32 value) {
  set_has_orpepause();
  orpepause_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.OrpeSafetyStatus.orpePause)
}

// required uint32 orpeStop = 2;
inline bool OrpeSafetyStatus::has_orpestop() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void OrpeSafetyStatus::set_has_orpestop() {
  _has_bits_[0] |= 0x00000002u;
}
inline void OrpeSafetyStatus::clear_has_orpestop() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void OrpeSafetyStatus::clear_orpestop() {
  orpestop_ = 0u;
  clear_has_orpestop();
}
inline ::google::protobuf::uint32 OrpeSafetyStatus::orpestop() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.OrpeSafetyStatus.orpeStop)
  return orpestop_;
}
inline void OrpeSafetyStatus::set_orpestop(::google::protobuf::uint32 value) {
  set_has_orpestop();
  orpestop_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.OrpeSafetyStatus.orpeStop)
}

// repeated uint32 orpeError = 3;
inline int OrpeSafetyStatus::orpeerror_size() const {
  return orpeerror_.size();
}
inline void OrpeSafetyStatus::clear_orpeerror() {
  orpeerror_.Clear();
}
inline ::google::protobuf::uint32 OrpeSafetyStatus::orpeerror(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.OrpeSafetyStatus.orpeError)
  return orpeerror_.Get(index);
}
inline void OrpeSafetyStatus::set_orpeerror(int index, ::google::protobuf::uint32 value) {
  orpeerror_.Set(index, value);
  // @@protoc_insertion_point(field_set:aubo.robot.communication.OrpeSafetyStatus.orpeError)
}
inline void OrpeSafetyStatus::add_orpeerror(::google::protobuf::uint32 value) {
  orpeerror_.Add(value);
  // @@protoc_insertion_point(field_add:aubo.robot.communication.OrpeSafetyStatus.orpeError)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
OrpeSafetyStatus::orpeerror() const {
  // @@protoc_insertion_point(field_list:aubo.robot.communication.OrpeSafetyStatus.orpeError)
  return orpeerror_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
OrpeSafetyStatus::mutable_orpeerror() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.communication.OrpeSafetyStatus.orpeError)
  return &orpeerror_;
}

// required uint32 systemEmergencyStop = 4;
inline bool OrpeSafetyStatus::has_systememergencystop() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void OrpeSafetyStatus::set_has_systememergencystop() {
  _has_bits_[0] |= 0x00000008u;
}
inline void OrpeSafetyStatus::clear_has_systememergencystop() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void OrpeSafetyStatus::clear_systememergencystop() {
  systememergencystop_ = 0u;
  clear_has_systememergencystop();
}
inline ::google::protobuf::uint32 OrpeSafetyStatus::systememergencystop() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.OrpeSafetyStatus.systemEmergencyStop)
  return systememergencystop_;
}
inline void OrpeSafetyStatus::set_systememergencystop(::google::protobuf::uint32 value) {
  set_has_systememergencystop();
  systememergencystop_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.OrpeSafetyStatus.systemEmergencyStop)
}

// required uint32 reducedModeError = 5;
inline bool OrpeSafetyStatus::has_reducedmodeerror() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void OrpeSafetyStatus::set_has_reducedmodeerror() {
  _has_bits_[0] |= 0x00000010u;
}
inline void OrpeSafetyStatus::clear_has_reducedmodeerror() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void OrpeSafetyStatus::clear_reducedmodeerror() {
  reducedmodeerror_ = 0u;
  clear_has_reducedmodeerror();
}
inline ::google::protobuf::uint32 OrpeSafetyStatus::reducedmodeerror() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.OrpeSafetyStatus.reducedModeError)
  return reducedmodeerror_;
}
inline void OrpeSafetyStatus::set_reducedmodeerror(::google::protobuf::uint32 value) {
  set_has_reducedmodeerror();
  reducedmodeerror_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.OrpeSafetyStatus.reducedModeError)
}

// required uint32 safetyguardResetSucc = 6;
inline bool OrpeSafetyStatus::has_safetyguardresetsucc() const {
  return (_has_bits_[0] & 0x00000020u) != 0;
}
inline void OrpeSafetyStatus::set_has_safetyguardresetsucc() {
  _has_bits_[0] |= 0x00000020u;
}
inline void OrpeSafetyStatus::clear_has_safetyguardresetsucc() {
  _has_bits_[0] &= ~0x00000020u;
}
inline void OrpeSafetyStatus::clear_safetyguardresetsucc() {
  safetyguardresetsucc_ = 0u;
  clear_has_safetyguardresetsucc();
}
inline ::google::protobuf::uint32 OrpeSafetyStatus::safetyguardresetsucc() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.OrpeSafetyStatus.safetyguardResetSucc)
  return safetyguardresetsucc_;
}
inline void OrpeSafetyStatus::set_safetyguardresetsucc(::google::protobuf::uint32 value) {
  set_has_safetyguardresetsucc();
  safetyguardresetsucc_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.OrpeSafetyStatus.safetyguardResetSucc)
}

// -------------------------------------------------------------------

// protoSafeCommunicationParam

// required uint32 paramReserved = 1;
inline bool protoSafeCommunicationParam::has_paramreserved() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void protoSafeCommunicationParam::set_has_paramreserved() {
  _has_bits_[0] |= 0x00000001u;
}
inline void protoSafeCommunicationParam::clear_has_paramreserved() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void protoSafeCommunicationParam::clear_paramreserved() {
  paramreserved_ = 0u;
  clear_has_paramreserved();
}
inline ::google::protobuf::uint32 protoSafeCommunicationParam::paramreserved() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.protoSafeCommunicationParam.paramReserved)
  return paramreserved_;
}
inline void protoSafeCommunicationParam::set_paramreserved(::google::protobuf::uint32 value) {
  set_has_paramreserved();
  paramreserved_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.protoSafeCommunicationParam.paramReserved)
}

// repeated uint32 param = 2;
inline int protoSafeCommunicationParam::param_size() const {
  return param_.size();
}
inline void protoSafeCommunicationParam::clear_param() {
  param_.Clear();
}
inline ::google::protobuf::uint32 protoSafeCommunicationParam::param(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.protoSafeCommunicationParam.param)
  return param_.Get(index);
}
inline void protoSafeCommunicationParam::set_param(int index, ::google::protobuf::uint32 value) {
  param_.Set(index, value);
  // @@protoc_insertion_point(field_set:aubo.robot.communication.protoSafeCommunicationParam.param)
}
inline void protoSafeCommunicationParam::add_param(::google::protobuf::uint32 value) {
  param_.Add(value);
  // @@protoc_insertion_point(field_add:aubo.robot.communication.protoSafeCommunicationParam.param)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
protoSafeCommunicationParam::param() const {
  // @@protoc_insertion_point(field_list:aubo.robot.communication.protoSafeCommunicationParam.param)
  return param_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
protoSafeCommunicationParam::mutable_param() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.communication.protoSafeCommunicationParam.param)
  return &param_;
}

// -------------------------------------------------------------------

// OriginPose

// required uint32 OriginPoseState = 1;
inline bool OriginPose::has_originposestate() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void OriginPose::set_has_originposestate() {
  _has_bits_[0] |= 0x00000001u;
}
inline void OriginPose::clear_has_originposestate() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void OriginPose::clear_originposestate() {
  originposestate_ = 0u;
  clear_has_originposestate();
}
inline ::google::protobuf::uint32 OriginPose::originposestate() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.OriginPose.OriginPoseState)
  return originposestate_;
}
inline void OriginPose::set_originposestate(::google::protobuf::uint32 value) {
  set_has_originposestate();
  originposestate_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.OriginPose.OriginPoseState)
}

// repeated float OriginPose = 2;
inline int OriginPose::originpose_size() const {
  return originpose_.size();
}
inline void OriginPose::clear_originpose() {
  originpose_.Clear();
}
inline float OriginPose::originpose(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.OriginPose.OriginPose)
  return originpose_.Get(index);
}
inline void OriginPose::set_originpose(int index, float value) {
  originpose_.Set(index, value);
  // @@protoc_insertion_point(field_set:aubo.robot.communication.OriginPose.OriginPose)
}
inline void OriginPose::add_originpose(float value) {
  originpose_.Add(value);
  // @@protoc_insertion_point(field_add:aubo.robot.communication.OriginPose.OriginPose)
}
inline const ::google::protobuf::RepeatedField< float >&
OriginPose::originpose() const {
  // @@protoc_insertion_point(field_list:aubo.robot.communication.OriginPose.OriginPose)
  return originpose_;
}
inline ::google::protobuf::RepeatedField< float >*
OriginPose::mutable_originpose() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.communication.OriginPose.OriginPose)
  return &originpose_;
}

// -------------------------------------------------------------------

// RobotToolConfig

// required uint32 config = 1;
inline bool RobotToolConfig::has_config() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void RobotToolConfig::set_has_config() {
  _has_bits_[0] |= 0x00000001u;
}
inline void RobotToolConfig::clear_has_config() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void RobotToolConfig::clear_config() {
  config_ = 0u;
  clear_has_config();
}
inline ::google::protobuf::uint32 RobotToolConfig::config() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.RobotToolConfig.config)
  return config_;
}
inline void RobotToolConfig::set_config(::google::protobuf::uint32 value) {
  set_has_config();
  config_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.RobotToolConfig.config)
}

// -------------------------------------------------------------------

// ProtoRobotCommonResponse

// required int32 errorCode = 1;
inline bool ProtoRobotCommonResponse::has_errorcode() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ProtoRobotCommonResponse::set_has_errorcode() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ProtoRobotCommonResponse::clear_has_errorcode() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ProtoRobotCommonResponse::clear_errorcode() {
  errorcode_ = 0;
  clear_has_errorcode();
}
inline ::google::protobuf::int32 ProtoRobotCommonResponse::errorcode() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoRobotCommonResponse.errorCode)
  return errorcode_;
}
inline void ProtoRobotCommonResponse::set_errorcode(::google::protobuf::int32 value) {
  set_has_errorcode();
  errorcode_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ProtoRobotCommonResponse.errorCode)
}

// required string errorMsg = 2;
inline bool ProtoRobotCommonResponse::has_errormsg() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void ProtoRobotCommonResponse::set_has_errormsg() {
  _has_bits_[0] |= 0x00000002u;
}
inline void ProtoRobotCommonResponse::clear_has_errormsg() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void ProtoRobotCommonResponse::clear_errormsg() {
  if (errormsg_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    errormsg_->clear();
  }
  clear_has_errormsg();
}
inline const ::std::string& ProtoRobotCommonResponse::errormsg() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoRobotCommonResponse.errorMsg)
  return *errormsg_;
}
inline void ProtoRobotCommonResponse::set_errormsg(const ::std::string& value) {
  set_has_errormsg();
  if (errormsg_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    errormsg_ = new ::std::string;
  }
  errormsg_->assign(value);
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ProtoRobotCommonResponse.errorMsg)
}
inline void ProtoRobotCommonResponse::set_errormsg(const char* value) {
  set_has_errormsg();
  if (errormsg_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    errormsg_ = new ::std::string;
  }
  errormsg_->assign(value);
  // @@protoc_insertion_point(field_set_char:aubo.robot.communication.ProtoRobotCommonResponse.errorMsg)
}
inline void ProtoRobotCommonResponse::set_errormsg(const char* value, size_t size) {
  set_has_errormsg();
  if (errormsg_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    errormsg_ = new ::std::string;
  }
  errormsg_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:aubo.robot.communication.ProtoRobotCommonResponse.errorMsg)
}
inline ::std::string* ProtoRobotCommonResponse::mutable_errormsg() {
  set_has_errormsg();
  if (errormsg_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    errormsg_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:aubo.robot.communication.ProtoRobotCommonResponse.errorMsg)
  return errormsg_;
}
inline ::std::string* ProtoRobotCommonResponse::release_errormsg() {
  clear_has_errormsg();
  if (errormsg_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = errormsg_;
    errormsg_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void ProtoRobotCommonResponse::set_allocated_errormsg(::std::string* errormsg) {
  if (errormsg_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete errormsg_;
  }
  if (errormsg) {
    set_has_errormsg();
    errormsg_ = errormsg;
  } else {
    clear_has_errormsg();
    errormsg_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.communication.ProtoRobotCommonResponse.errorMsg)
}

// -------------------------------------------------------------------

// ProtoRequestLogin

// required string userName = 1;
inline bool ProtoRequestLogin::has_username() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ProtoRequestLogin::set_has_username() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ProtoRequestLogin::clear_has_username() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ProtoRequestLogin::clear_username() {
  if (username_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    username_->clear();
  }
  clear_has_username();
}
inline const ::std::string& ProtoRequestLogin::username() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoRequestLogin.userName)
  return *username_;
}
inline void ProtoRequestLogin::set_username(const ::std::string& value) {
  set_has_username();
  if (username_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    username_ = new ::std::string;
  }
  username_->assign(value);
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ProtoRequestLogin.userName)
}
inline void ProtoRequestLogin::set_username(const char* value) {
  set_has_username();
  if (username_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    username_ = new ::std::string;
  }
  username_->assign(value);
  // @@protoc_insertion_point(field_set_char:aubo.robot.communication.ProtoRequestLogin.userName)
}
inline void ProtoRequestLogin::set_username(const char* value, size_t size) {
  set_has_username();
  if (username_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    username_ = new ::std::string;
  }
  username_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:aubo.robot.communication.ProtoRequestLogin.userName)
}
inline ::std::string* ProtoRequestLogin::mutable_username() {
  set_has_username();
  if (username_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    username_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:aubo.robot.communication.ProtoRequestLogin.userName)
  return username_;
}
inline ::std::string* ProtoRequestLogin::release_username() {
  clear_has_username();
  if (username_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = username_;
    username_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void ProtoRequestLogin::set_allocated_username(::std::string* username) {
  if (username_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete username_;
  }
  if (username) {
    set_has_username();
    username_ = username;
  } else {
    clear_has_username();
    username_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.communication.ProtoRequestLogin.userName)
}

// required string passwd = 2;
inline bool ProtoRequestLogin::has_passwd() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void ProtoRequestLogin::set_has_passwd() {
  _has_bits_[0] |= 0x00000002u;
}
inline void ProtoRequestLogin::clear_has_passwd() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void ProtoRequestLogin::clear_passwd() {
  if (passwd_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    passwd_->clear();
  }
  clear_has_passwd();
}
inline const ::std::string& ProtoRequestLogin::passwd() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoRequestLogin.passwd)
  return *passwd_;
}
inline void ProtoRequestLogin::set_passwd(const ::std::string& value) {
  set_has_passwd();
  if (passwd_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    passwd_ = new ::std::string;
  }
  passwd_->assign(value);
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ProtoRequestLogin.passwd)
}
inline void ProtoRequestLogin::set_passwd(const char* value) {
  set_has_passwd();
  if (passwd_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    passwd_ = new ::std::string;
  }
  passwd_->assign(value);
  // @@protoc_insertion_point(field_set_char:aubo.robot.communication.ProtoRequestLogin.passwd)
}
inline void ProtoRequestLogin::set_passwd(const char* value, size_t size) {
  set_has_passwd();
  if (passwd_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    passwd_ = new ::std::string;
  }
  passwd_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:aubo.robot.communication.ProtoRequestLogin.passwd)
}
inline ::std::string* ProtoRequestLogin::mutable_passwd() {
  set_has_passwd();
  if (passwd_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    passwd_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:aubo.robot.communication.ProtoRequestLogin.passwd)
  return passwd_;
}
inline ::std::string* ProtoRequestLogin::release_passwd() {
  clear_has_passwd();
  if (passwd_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = passwd_;
    passwd_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void ProtoRequestLogin::set_allocated_passwd(::std::string* passwd) {
  if (passwd_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete passwd_;
  }
  if (passwd) {
    set_has_passwd();
    passwd_ = passwd;
  } else {
    clear_has_passwd();
    passwd_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.communication.ProtoRequestLogin.passwd)
}

// -------------------------------------------------------------------

// ProtoRobotDiagnosisIODesc

// required uint32 addr = 1;
inline bool ProtoRobotDiagnosisIODesc::has_addr() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ProtoRobotDiagnosisIODesc::set_has_addr() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ProtoRobotDiagnosisIODesc::clear_has_addr() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ProtoRobotDiagnosisIODesc::clear_addr() {
  addr_ = 0u;
  clear_has_addr();
}
inline ::google::protobuf::uint32 ProtoRobotDiagnosisIODesc::addr() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoRobotDiagnosisIODesc.addr)
  return addr_;
}
inline void ProtoRobotDiagnosisIODesc::set_addr(::google::protobuf::uint32 value) {
  set_has_addr();
  addr_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ProtoRobotDiagnosisIODesc.addr)
}

// required uint32 type = 2;
inline bool ProtoRobotDiagnosisIODesc::has_type() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void ProtoRobotDiagnosisIODesc::set_has_type() {
  _has_bits_[0] |= 0x00000002u;
}
inline void ProtoRobotDiagnosisIODesc::clear_has_type() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void ProtoRobotDiagnosisIODesc::clear_type() {
  type_ = 0u;
  clear_has_type();
}
inline ::google::protobuf::uint32 ProtoRobotDiagnosisIODesc::type() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoRobotDiagnosisIODesc.type)
  return type_;
}
inline void ProtoRobotDiagnosisIODesc::set_type(::google::protobuf::uint32 value) {
  set_has_type();
  type_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ProtoRobotDiagnosisIODesc.type)
}

// required uint32 value = 3;
inline bool ProtoRobotDiagnosisIODesc::has_value() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void ProtoRobotDiagnosisIODesc::set_has_value() {
  _has_bits_[0] |= 0x00000004u;
}
inline void ProtoRobotDiagnosisIODesc::clear_has_value() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void ProtoRobotDiagnosisIODesc::clear_value() {
  value_ = 0u;
  clear_has_value();
}
inline ::google::protobuf::uint32 ProtoRobotDiagnosisIODesc::value() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoRobotDiagnosisIODesc.value)
  return value_;
}
inline void ProtoRobotDiagnosisIODesc::set_value(::google::protobuf::uint32 value) {
  set_has_value();
  value_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ProtoRobotDiagnosisIODesc.value)
}

// -------------------------------------------------------------------

// ProtoRobotAnalogIODesc

// required uint32 addr = 1;
inline bool ProtoRobotAnalogIODesc::has_addr() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ProtoRobotAnalogIODesc::set_has_addr() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ProtoRobotAnalogIODesc::clear_has_addr() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ProtoRobotAnalogIODesc::clear_addr() {
  addr_ = 0u;
  clear_has_addr();
}
inline ::google::protobuf::uint32 ProtoRobotAnalogIODesc::addr() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoRobotAnalogIODesc.addr)
  return addr_;
}
inline void ProtoRobotAnalogIODesc::set_addr(::google::protobuf::uint32 value) {
  set_has_addr();
  addr_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ProtoRobotAnalogIODesc.addr)
}

// required uint32 type = 2;
inline bool ProtoRobotAnalogIODesc::has_type() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void ProtoRobotAnalogIODesc::set_has_type() {
  _has_bits_[0] |= 0x00000002u;
}
inline void ProtoRobotAnalogIODesc::clear_has_type() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void ProtoRobotAnalogIODesc::clear_type() {
  type_ = 0u;
  clear_has_type();
}
inline ::google::protobuf::uint32 ProtoRobotAnalogIODesc::type() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoRobotAnalogIODesc.type)
  return type_;
}
inline void ProtoRobotAnalogIODesc::set_type(::google::protobuf::uint32 value) {
  set_has_type();
  type_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ProtoRobotAnalogIODesc.type)
}

// required float value = 3;
inline bool ProtoRobotAnalogIODesc::has_value() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void ProtoRobotAnalogIODesc::set_has_value() {
  _has_bits_[0] |= 0x00000004u;
}
inline void ProtoRobotAnalogIODesc::clear_has_value() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void ProtoRobotAnalogIODesc::clear_value() {
  value_ = 0;
  clear_has_value();
}
inline float ProtoRobotAnalogIODesc::value() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoRobotAnalogIODesc.value)
  return value_;
}
inline void ProtoRobotAnalogIODesc::set_value(float value) {
  set_has_value();
  value_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ProtoRobotAnalogIODesc.value)
}

// -------------------------------------------------------------------

// ProtoCommunicationRobotDiagnosisIOData

// required int32 type = 1;
inline bool ProtoCommunicationRobotDiagnosisIOData::has_type() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ProtoCommunicationRobotDiagnosisIOData::set_has_type() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ProtoCommunicationRobotDiagnosisIOData::clear_has_type() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ProtoCommunicationRobotDiagnosisIOData::clear_type() {
  type_ = 0;
  clear_has_type();
}
inline ::google::protobuf::int32 ProtoCommunicationRobotDiagnosisIOData::type() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoCommunicationRobotDiagnosisIOData.type)
  return type_;
}
inline void ProtoCommunicationRobotDiagnosisIOData::set_type(::google::protobuf::int32 value) {
  set_has_type();
  type_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ProtoCommunicationRobotDiagnosisIOData.type)
}

// repeated .aubo.robot.communication.ProtoRobotCommonResponse errorInfo = 2;
inline int ProtoCommunicationRobotDiagnosisIOData::errorinfo_size() const {
  return errorinfo_.size();
}
inline void ProtoCommunicationRobotDiagnosisIOData::clear_errorinfo() {
  errorinfo_.Clear();
}
inline const ::aubo::robot::communication::ProtoRobotCommonResponse& ProtoCommunicationRobotDiagnosisIOData::errorinfo(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoCommunicationRobotDiagnosisIOData.errorInfo)
  return errorinfo_.Get(index);
}
inline ::aubo::robot::communication::ProtoRobotCommonResponse* ProtoCommunicationRobotDiagnosisIOData::mutable_errorinfo(int index) {
  // @@protoc_insertion_point(field_mutable:aubo.robot.communication.ProtoCommunicationRobotDiagnosisIOData.errorInfo)
  return errorinfo_.Mutable(index);
}
inline ::aubo::robot::communication::ProtoRobotCommonResponse* ProtoCommunicationRobotDiagnosisIOData::add_errorinfo() {
  // @@protoc_insertion_point(field_add:aubo.robot.communication.ProtoCommunicationRobotDiagnosisIOData.errorInfo)
  return errorinfo_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::ProtoRobotCommonResponse >&
ProtoCommunicationRobotDiagnosisIOData::errorinfo() const {
  // @@protoc_insertion_point(field_list:aubo.robot.communication.ProtoCommunicationRobotDiagnosisIOData.errorInfo)
  return errorinfo_;
}
inline ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::ProtoRobotCommonResponse >*
ProtoCommunicationRobotDiagnosisIOData::mutable_errorinfo() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.communication.ProtoCommunicationRobotDiagnosisIOData.errorInfo)
  return &errorinfo_;
}

// repeated .aubo.robot.communication.ProtoRobotDiagnosisIODesc ioDesc = 3;
inline int ProtoCommunicationRobotDiagnosisIOData::iodesc_size() const {
  return iodesc_.size();
}
inline void ProtoCommunicationRobotDiagnosisIOData::clear_iodesc() {
  iodesc_.Clear();
}
inline const ::aubo::robot::communication::ProtoRobotDiagnosisIODesc& ProtoCommunicationRobotDiagnosisIOData::iodesc(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoCommunicationRobotDiagnosisIOData.ioDesc)
  return iodesc_.Get(index);
}
inline ::aubo::robot::communication::ProtoRobotDiagnosisIODesc* ProtoCommunicationRobotDiagnosisIOData::mutable_iodesc(int index) {
  // @@protoc_insertion_point(field_mutable:aubo.robot.communication.ProtoCommunicationRobotDiagnosisIOData.ioDesc)
  return iodesc_.Mutable(index);
}
inline ::aubo::robot::communication::ProtoRobotDiagnosisIODesc* ProtoCommunicationRobotDiagnosisIOData::add_iodesc() {
  // @@protoc_insertion_point(field_add:aubo.robot.communication.ProtoCommunicationRobotDiagnosisIOData.ioDesc)
  return iodesc_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::ProtoRobotDiagnosisIODesc >&
ProtoCommunicationRobotDiagnosisIOData::iodesc() const {
  // @@protoc_insertion_point(field_list:aubo.robot.communication.ProtoCommunicationRobotDiagnosisIOData.ioDesc)
  return iodesc_;
}
inline ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::ProtoRobotDiagnosisIODesc >*
ProtoCommunicationRobotDiagnosisIOData::mutable_iodesc() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.communication.ProtoCommunicationRobotDiagnosisIOData.ioDesc)
  return &iodesc_;
}

// -------------------------------------------------------------------

// ProtoCommunicationRobotAnalogIOData

// required int32 type = 3;
inline bool ProtoCommunicationRobotAnalogIOData::has_type() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ProtoCommunicationRobotAnalogIOData::set_has_type() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ProtoCommunicationRobotAnalogIOData::clear_has_type() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ProtoCommunicationRobotAnalogIOData::clear_type() {
  type_ = 0;
  clear_has_type();
}
inline ::google::protobuf::int32 ProtoCommunicationRobotAnalogIOData::type() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoCommunicationRobotAnalogIOData.type)
  return type_;
}
inline void ProtoCommunicationRobotAnalogIOData::set_type(::google::protobuf::int32 value) {
  set_has_type();
  type_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ProtoCommunicationRobotAnalogIOData.type)
}

// repeated .aubo.robot.communication.ProtoRobotCommonResponse errorInfo = 2;
inline int ProtoCommunicationRobotAnalogIOData::errorinfo_size() const {
  return errorinfo_.size();
}
inline void ProtoCommunicationRobotAnalogIOData::clear_errorinfo() {
  errorinfo_.Clear();
}
inline const ::aubo::robot::communication::ProtoRobotCommonResponse& ProtoCommunicationRobotAnalogIOData::errorinfo(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoCommunicationRobotAnalogIOData.errorInfo)
  return errorinfo_.Get(index);
}
inline ::aubo::robot::communication::ProtoRobotCommonResponse* ProtoCommunicationRobotAnalogIOData::mutable_errorinfo(int index) {
  // @@protoc_insertion_point(field_mutable:aubo.robot.communication.ProtoCommunicationRobotAnalogIOData.errorInfo)
  return errorinfo_.Mutable(index);
}
inline ::aubo::robot::communication::ProtoRobotCommonResponse* ProtoCommunicationRobotAnalogIOData::add_errorinfo() {
  // @@protoc_insertion_point(field_add:aubo.robot.communication.ProtoCommunicationRobotAnalogIOData.errorInfo)
  return errorinfo_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::ProtoRobotCommonResponse >&
ProtoCommunicationRobotAnalogIOData::errorinfo() const {
  // @@protoc_insertion_point(field_list:aubo.robot.communication.ProtoCommunicationRobotAnalogIOData.errorInfo)
  return errorinfo_;
}
inline ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::ProtoRobotCommonResponse >*
ProtoCommunicationRobotAnalogIOData::mutable_errorinfo() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.communication.ProtoCommunicationRobotAnalogIOData.errorInfo)
  return &errorinfo_;
}

// repeated .aubo.robot.communication.ProtoRobotAnalogIODesc ioDesc = 1;
inline int ProtoCommunicationRobotAnalogIOData::iodesc_size() const {
  return iodesc_.size();
}
inline void ProtoCommunicationRobotAnalogIOData::clear_iodesc() {
  iodesc_.Clear();
}
inline const ::aubo::robot::communication::ProtoRobotAnalogIODesc& ProtoCommunicationRobotAnalogIOData::iodesc(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoCommunicationRobotAnalogIOData.ioDesc)
  return iodesc_.Get(index);
}
inline ::aubo::robot::communication::ProtoRobotAnalogIODesc* ProtoCommunicationRobotAnalogIOData::mutable_iodesc(int index) {
  // @@protoc_insertion_point(field_mutable:aubo.robot.communication.ProtoCommunicationRobotAnalogIOData.ioDesc)
  return iodesc_.Mutable(index);
}
inline ::aubo::robot::communication::ProtoRobotAnalogIODesc* ProtoCommunicationRobotAnalogIOData::add_iodesc() {
  // @@protoc_insertion_point(field_add:aubo.robot.communication.ProtoCommunicationRobotAnalogIOData.ioDesc)
  return iodesc_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::ProtoRobotAnalogIODesc >&
ProtoCommunicationRobotAnalogIOData::iodesc() const {
  // @@protoc_insertion_point(field_list:aubo.robot.communication.ProtoCommunicationRobotAnalogIOData.ioDesc)
  return iodesc_;
}
inline ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::ProtoRobotAnalogIODesc >*
ProtoCommunicationRobotAnalogIOData::mutable_iodesc() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.communication.ProtoCommunicationRobotAnalogIOData.ioDesc)
  return &iodesc_;
}

// -------------------------------------------------------------------

// ProtoCommunicationGeneralData

// repeated int32 property1 = 1;
inline int ProtoCommunicationGeneralData::property1_size() const {
  return property1_.size();
}
inline void ProtoCommunicationGeneralData::clear_property1() {
  property1_.Clear();
}
inline ::google::protobuf::int32 ProtoCommunicationGeneralData::property1(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoCommunicationGeneralData.property1)
  return property1_.Get(index);
}
inline void ProtoCommunicationGeneralData::set_property1(int index, ::google::protobuf::int32 value) {
  property1_.Set(index, value);
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ProtoCommunicationGeneralData.property1)
}
inline void ProtoCommunicationGeneralData::add_property1(::google::protobuf::int32 value) {
  property1_.Add(value);
  // @@protoc_insertion_point(field_add:aubo.robot.communication.ProtoCommunicationGeneralData.property1)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
ProtoCommunicationGeneralData::property1() const {
  // @@protoc_insertion_point(field_list:aubo.robot.communication.ProtoCommunicationGeneralData.property1)
  return property1_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
ProtoCommunicationGeneralData::mutable_property1() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.communication.ProtoCommunicationGeneralData.property1)
  return &property1_;
}

// repeated bool property2 = 2;
inline int ProtoCommunicationGeneralData::property2_size() const {
  return property2_.size();
}
inline void ProtoCommunicationGeneralData::clear_property2() {
  property2_.Clear();
}
inline bool ProtoCommunicationGeneralData::property2(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoCommunicationGeneralData.property2)
  return property2_.Get(index);
}
inline void ProtoCommunicationGeneralData::set_property2(int index, bool value) {
  property2_.Set(index, value);
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ProtoCommunicationGeneralData.property2)
}
inline void ProtoCommunicationGeneralData::add_property2(bool value) {
  property2_.Add(value);
  // @@protoc_insertion_point(field_add:aubo.robot.communication.ProtoCommunicationGeneralData.property2)
}
inline const ::google::protobuf::RepeatedField< bool >&
ProtoCommunicationGeneralData::property2() const {
  // @@protoc_insertion_point(field_list:aubo.robot.communication.ProtoCommunicationGeneralData.property2)
  return property2_;
}
inline ::google::protobuf::RepeatedField< bool >*
ProtoCommunicationGeneralData::mutable_property2() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.communication.ProtoCommunicationGeneralData.property2)
  return &property2_;
}

// repeated .aubo.robot.communication.ProtoRobotCommonResponse errorInfo = 3;
inline int ProtoCommunicationGeneralData::errorinfo_size() const {
  return errorinfo_.size();
}
inline void ProtoCommunicationGeneralData::clear_errorinfo() {
  errorinfo_.Clear();
}
inline const ::aubo::robot::communication::ProtoRobotCommonResponse& ProtoCommunicationGeneralData::errorinfo(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoCommunicationGeneralData.errorInfo)
  return errorinfo_.Get(index);
}
inline ::aubo::robot::communication::ProtoRobotCommonResponse* ProtoCommunicationGeneralData::mutable_errorinfo(int index) {
  // @@protoc_insertion_point(field_mutable:aubo.robot.communication.ProtoCommunicationGeneralData.errorInfo)
  return errorinfo_.Mutable(index);
}
inline ::aubo::robot::communication::ProtoRobotCommonResponse* ProtoCommunicationGeneralData::add_errorinfo() {
  // @@protoc_insertion_point(field_add:aubo.robot.communication.ProtoCommunicationGeneralData.errorInfo)
  return errorinfo_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::ProtoRobotCommonResponse >&
ProtoCommunicationGeneralData::errorinfo() const {
  // @@protoc_insertion_point(field_list:aubo.robot.communication.ProtoCommunicationGeneralData.errorInfo)
  return errorinfo_;
}
inline ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::ProtoRobotCommonResponse >*
ProtoCommunicationGeneralData::mutable_errorinfo() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.communication.ProtoCommunicationGeneralData.errorInfo)
  return &errorinfo_;
}

// required int32 type = 4;
inline bool ProtoCommunicationGeneralData::has_type() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void ProtoCommunicationGeneralData::set_has_type() {
  _has_bits_[0] |= 0x00000008u;
}
inline void ProtoCommunicationGeneralData::clear_has_type() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void ProtoCommunicationGeneralData::clear_type() {
  type_ = 0;
  clear_has_type();
}
inline ::google::protobuf::int32 ProtoCommunicationGeneralData::type() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoCommunicationGeneralData.type)
  return type_;
}
inline void ProtoCommunicationGeneralData::set_type(::google::protobuf::int32 value) {
  set_has_type();
  type_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ProtoCommunicationGeneralData.type)
}

// -------------------------------------------------------------------

// ProtoCommunicationRobotDiagnosisInfo

// required int32 num = 1;
inline bool ProtoCommunicationRobotDiagnosisInfo::has_num() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ProtoCommunicationRobotDiagnosisInfo::set_has_num() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ProtoCommunicationRobotDiagnosisInfo::clear_has_num() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ProtoCommunicationRobotDiagnosisInfo::clear_num() {
  num_ = 0;
  clear_has_num();
}
inline ::google::protobuf::int32 ProtoCommunicationRobotDiagnosisInfo::num() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoCommunicationRobotDiagnosisInfo.num)
  return num_;
}
inline void ProtoCommunicationRobotDiagnosisInfo::set_num(::google::protobuf::int32 value) {
  set_has_num();
  num_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ProtoCommunicationRobotDiagnosisInfo.num)
}

// repeated .aubo.robot.communication.RobotDiagnosis robotDiagnosis = 2;
inline int ProtoCommunicationRobotDiagnosisInfo::robotdiagnosis_size() const {
  return robotdiagnosis_.size();
}
inline void ProtoCommunicationRobotDiagnosisInfo::clear_robotdiagnosis() {
  robotdiagnosis_.Clear();
}
inline const ::aubo::robot::communication::RobotDiagnosis& ProtoCommunicationRobotDiagnosisInfo::robotdiagnosis(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoCommunicationRobotDiagnosisInfo.robotDiagnosis)
  return robotdiagnosis_.Get(index);
}
inline ::aubo::robot::communication::RobotDiagnosis* ProtoCommunicationRobotDiagnosisInfo::mutable_robotdiagnosis(int index) {
  // @@protoc_insertion_point(field_mutable:aubo.robot.communication.ProtoCommunicationRobotDiagnosisInfo.robotDiagnosis)
  return robotdiagnosis_.Mutable(index);
}
inline ::aubo::robot::communication::RobotDiagnosis* ProtoCommunicationRobotDiagnosisInfo::add_robotdiagnosis() {
  // @@protoc_insertion_point(field_add:aubo.robot.communication.ProtoCommunicationRobotDiagnosisInfo.robotDiagnosis)
  return robotdiagnosis_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::RobotDiagnosis >&
ProtoCommunicationRobotDiagnosisInfo::robotdiagnosis() const {
  // @@protoc_insertion_point(field_list:aubo.robot.communication.ProtoCommunicationRobotDiagnosisInfo.robotDiagnosis)
  return robotdiagnosis_;
}
inline ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::RobotDiagnosis >*
ProtoCommunicationRobotDiagnosisInfo::mutable_robotdiagnosis() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.communication.ProtoCommunicationRobotDiagnosisInfo.robotDiagnosis)
  return &robotdiagnosis_;
}

// repeated .aubo.robot.communication.ProtoRobotCommonResponse errorInfo = 3;
inline int ProtoCommunicationRobotDiagnosisInfo::errorinfo_size() const {
  return errorinfo_.size();
}
inline void ProtoCommunicationRobotDiagnosisInfo::clear_errorinfo() {
  errorinfo_.Clear();
}
inline const ::aubo::robot::communication::ProtoRobotCommonResponse& ProtoCommunicationRobotDiagnosisInfo::errorinfo(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoCommunicationRobotDiagnosisInfo.errorInfo)
  return errorinfo_.Get(index);
}
inline ::aubo::robot::communication::ProtoRobotCommonResponse* ProtoCommunicationRobotDiagnosisInfo::mutable_errorinfo(int index) {
  // @@protoc_insertion_point(field_mutable:aubo.robot.communication.ProtoCommunicationRobotDiagnosisInfo.errorInfo)
  return errorinfo_.Mutable(index);
}
inline ::aubo::robot::communication::ProtoRobotCommonResponse* ProtoCommunicationRobotDiagnosisInfo::add_errorinfo() {
  // @@protoc_insertion_point(field_add:aubo.robot.communication.ProtoCommunicationRobotDiagnosisInfo.errorInfo)
  return errorinfo_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::ProtoRobotCommonResponse >&
ProtoCommunicationRobotDiagnosisInfo::errorinfo() const {
  // @@protoc_insertion_point(field_list:aubo.robot.communication.ProtoCommunicationRobotDiagnosisInfo.errorInfo)
  return errorinfo_;
}
inline ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::ProtoRobotCommonResponse >*
ProtoCommunicationRobotDiagnosisInfo::mutable_errorinfo() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.communication.ProtoCommunicationRobotDiagnosisInfo.errorInfo)
  return &errorinfo_;
}

// -------------------------------------------------------------------

// ProtoCommunicationRobotEvent

// required int32 eventType = 1;
inline bool ProtoCommunicationRobotEvent::has_eventtype() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ProtoCommunicationRobotEvent::set_has_eventtype() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ProtoCommunicationRobotEvent::clear_has_eventtype() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ProtoCommunicationRobotEvent::clear_eventtype() {
  eventtype_ = 0;
  clear_has_eventtype();
}
inline ::google::protobuf::int32 ProtoCommunicationRobotEvent::eventtype() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoCommunicationRobotEvent.eventType)
  return eventtype_;
}
inline void ProtoCommunicationRobotEvent::set_eventtype(::google::protobuf::int32 value) {
  set_has_eventtype();
  eventtype_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ProtoCommunicationRobotEvent.eventType)
}

// required int32 eventCode = 2;
inline bool ProtoCommunicationRobotEvent::has_eventcode() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void ProtoCommunicationRobotEvent::set_has_eventcode() {
  _has_bits_[0] |= 0x00000002u;
}
inline void ProtoCommunicationRobotEvent::clear_has_eventcode() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void ProtoCommunicationRobotEvent::clear_eventcode() {
  eventcode_ = 0;
  clear_has_eventcode();
}
inline ::google::protobuf::int32 ProtoCommunicationRobotEvent::eventcode() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoCommunicationRobotEvent.eventCode)
  return eventcode_;
}
inline void ProtoCommunicationRobotEvent::set_eventcode(::google::protobuf::int32 value) {
  set_has_eventcode();
  eventcode_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ProtoCommunicationRobotEvent.eventCode)
}

// required bytes eventContent = 3;
inline bool ProtoCommunicationRobotEvent::has_eventcontent() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void ProtoCommunicationRobotEvent::set_has_eventcontent() {
  _has_bits_[0] |= 0x00000004u;
}
inline void ProtoCommunicationRobotEvent::clear_has_eventcontent() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void ProtoCommunicationRobotEvent::clear_eventcontent() {
  if (eventcontent_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    eventcontent_->clear();
  }
  clear_has_eventcontent();
}
inline const ::std::string& ProtoCommunicationRobotEvent::eventcontent() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoCommunicationRobotEvent.eventContent)
  return *eventcontent_;
}
inline void ProtoCommunicationRobotEvent::set_eventcontent(const ::std::string& value) {
  set_has_eventcontent();
  if (eventcontent_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    eventcontent_ = new ::std::string;
  }
  eventcontent_->assign(value);
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ProtoCommunicationRobotEvent.eventContent)
}
inline void ProtoCommunicationRobotEvent::set_eventcontent(const char* value) {
  set_has_eventcontent();
  if (eventcontent_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    eventcontent_ = new ::std::string;
  }
  eventcontent_->assign(value);
  // @@protoc_insertion_point(field_set_char:aubo.robot.communication.ProtoCommunicationRobotEvent.eventContent)
}
inline void ProtoCommunicationRobotEvent::set_eventcontent(const void* value, size_t size) {
  set_has_eventcontent();
  if (eventcontent_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    eventcontent_ = new ::std::string;
  }
  eventcontent_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:aubo.robot.communication.ProtoCommunicationRobotEvent.eventContent)
}
inline ::std::string* ProtoCommunicationRobotEvent::mutable_eventcontent() {
  set_has_eventcontent();
  if (eventcontent_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    eventcontent_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:aubo.robot.communication.ProtoCommunicationRobotEvent.eventContent)
  return eventcontent_;
}
inline ::std::string* ProtoCommunicationRobotEvent::release_eventcontent() {
  clear_has_eventcontent();
  if (eventcontent_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = eventcontent_;
    eventcontent_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void ProtoCommunicationRobotEvent::set_allocated_eventcontent(::std::string* eventcontent) {
  if (eventcontent_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete eventcontent_;
  }
  if (eventcontent) {
    set_has_eventcontent();
    eventcontent_ = eventcontent;
  } else {
    clear_has_eventcontent();
    eventcontent_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.communication.ProtoCommunicationRobotEvent.eventContent)
}

// -------------------------------------------------------------------

// ProtoCommunicationRobotStartupProfile

// required .aubo.robot.communication.RobotTcpParam tcpParam = 1;
inline bool ProtoCommunicationRobotStartupProfile::has_tcpparam() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ProtoCommunicationRobotStartupProfile::set_has_tcpparam() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ProtoCommunicationRobotStartupProfile::clear_has_tcpparam() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ProtoCommunicationRobotStartupProfile::clear_tcpparam() {
  if (tcpparam_ != NULL) tcpparam_->::aubo::robot::communication::RobotTcpParam::Clear();
  clear_has_tcpparam();
}
inline const ::aubo::robot::communication::RobotTcpParam& ProtoCommunicationRobotStartupProfile::tcpparam() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoCommunicationRobotStartupProfile.tcpParam)
  return tcpparam_ != NULL ? *tcpparam_ : *default_instance_->tcpparam_;
}
inline ::aubo::robot::communication::RobotTcpParam* ProtoCommunicationRobotStartupProfile::mutable_tcpparam() {
  set_has_tcpparam();
  if (tcpparam_ == NULL) tcpparam_ = new ::aubo::robot::communication::RobotTcpParam;
  // @@protoc_insertion_point(field_mutable:aubo.robot.communication.ProtoCommunicationRobotStartupProfile.tcpParam)
  return tcpparam_;
}
inline ::aubo::robot::communication::RobotTcpParam* ProtoCommunicationRobotStartupProfile::release_tcpparam() {
  clear_has_tcpparam();
  ::aubo::robot::communication::RobotTcpParam* temp = tcpparam_;
  tcpparam_ = NULL;
  return temp;
}
inline void ProtoCommunicationRobotStartupProfile::set_allocated_tcpparam(::aubo::robot::communication::RobotTcpParam* tcpparam) {
  delete tcpparam_;
  tcpparam_ = tcpparam;
  if (tcpparam) {
    set_has_tcpparam();
  } else {
    clear_has_tcpparam();
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.communication.ProtoCommunicationRobotStartupProfile.tcpParam)
}

// required bool readPose = 2;
inline bool ProtoCommunicationRobotStartupProfile::has_readpose() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void ProtoCommunicationRobotStartupProfile::set_has_readpose() {
  _has_bits_[0] |= 0x00000002u;
}
inline void ProtoCommunicationRobotStartupProfile::clear_has_readpose() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void ProtoCommunicationRobotStartupProfile::clear_readpose() {
  readpose_ = false;
  clear_has_readpose();
}
inline bool ProtoCommunicationRobotStartupProfile::readpose() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoCommunicationRobotStartupProfile.readPose)
  return readpose_;
}
inline void ProtoCommunicationRobotStartupProfile::set_readpose(bool value) {
  set_has_readpose();
  readpose_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ProtoCommunicationRobotStartupProfile.readPose)
}

// required bool staticCollisionDetect = 3;
inline bool ProtoCommunicationRobotStartupProfile::has_staticcollisiondetect() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void ProtoCommunicationRobotStartupProfile::set_has_staticcollisiondetect() {
  _has_bits_[0] |= 0x00000004u;
}
inline void ProtoCommunicationRobotStartupProfile::clear_has_staticcollisiondetect() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void ProtoCommunicationRobotStartupProfile::clear_staticcollisiondetect() {
  staticcollisiondetect_ = false;
  clear_has_staticcollisiondetect();
}
inline bool ProtoCommunicationRobotStartupProfile::staticcollisiondetect() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoCommunicationRobotStartupProfile.staticCollisionDetect)
  return staticcollisiondetect_;
}
inline void ProtoCommunicationRobotStartupProfile::set_staticcollisiondetect(bool value) {
  set_has_staticcollisiondetect();
  staticcollisiondetect_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ProtoCommunicationRobotStartupProfile.staticCollisionDetect)
}

// required int32 collisionClass = 4;
inline bool ProtoCommunicationRobotStartupProfile::has_collisionclass() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void ProtoCommunicationRobotStartupProfile::set_has_collisionclass() {
  _has_bits_[0] |= 0x00000008u;
}
inline void ProtoCommunicationRobotStartupProfile::clear_has_collisionclass() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void ProtoCommunicationRobotStartupProfile::clear_collisionclass() {
  collisionclass_ = 0;
  clear_has_collisionclass();
}
inline ::google::protobuf::int32 ProtoCommunicationRobotStartupProfile::collisionclass() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoCommunicationRobotStartupProfile.collisionClass)
  return collisionclass_;
}
inline void ProtoCommunicationRobotStartupProfile::set_collisionclass(::google::protobuf::int32 value) {
  set_has_collisionclass();
  collisionclass_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ProtoCommunicationRobotStartupProfile.collisionClass)
}

// required int32 maxAcc = 5;
inline bool ProtoCommunicationRobotStartupProfile::has_maxacc() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void ProtoCommunicationRobotStartupProfile::set_has_maxacc() {
  _has_bits_[0] |= 0x00000010u;
}
inline void ProtoCommunicationRobotStartupProfile::clear_has_maxacc() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void ProtoCommunicationRobotStartupProfile::clear_maxacc() {
  maxacc_ = 0;
  clear_has_maxacc();
}
inline ::google::protobuf::int32 ProtoCommunicationRobotStartupProfile::maxacc() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoCommunicationRobotStartupProfile.maxAcc)
  return maxacc_;
}
inline void ProtoCommunicationRobotStartupProfile::set_maxacc(::google::protobuf::int32 value) {
  set_has_maxacc();
  maxacc_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ProtoCommunicationRobotStartupProfile.maxAcc)
}

// -------------------------------------------------------------------

// ProtoCommunicationOfflineExcitTraj

// required string trackFile = 1;
inline bool ProtoCommunicationOfflineExcitTraj::has_trackfile() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ProtoCommunicationOfflineExcitTraj::set_has_trackfile() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ProtoCommunicationOfflineExcitTraj::clear_has_trackfile() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ProtoCommunicationOfflineExcitTraj::clear_trackfile() {
  if (trackfile_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    trackfile_->clear();
  }
  clear_has_trackfile();
}
inline const ::std::string& ProtoCommunicationOfflineExcitTraj::trackfile() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoCommunicationOfflineExcitTraj.trackFile)
  return *trackfile_;
}
inline void ProtoCommunicationOfflineExcitTraj::set_trackfile(const ::std::string& value) {
  set_has_trackfile();
  if (trackfile_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    trackfile_ = new ::std::string;
  }
  trackfile_->assign(value);
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ProtoCommunicationOfflineExcitTraj.trackFile)
}
inline void ProtoCommunicationOfflineExcitTraj::set_trackfile(const char* value) {
  set_has_trackfile();
  if (trackfile_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    trackfile_ = new ::std::string;
  }
  trackfile_->assign(value);
  // @@protoc_insertion_point(field_set_char:aubo.robot.communication.ProtoCommunicationOfflineExcitTraj.trackFile)
}
inline void ProtoCommunicationOfflineExcitTraj::set_trackfile(const char* value, size_t size) {
  set_has_trackfile();
  if (trackfile_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    trackfile_ = new ::std::string;
  }
  trackfile_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:aubo.robot.communication.ProtoCommunicationOfflineExcitTraj.trackFile)
}
inline ::std::string* ProtoCommunicationOfflineExcitTraj::mutable_trackfile() {
  set_has_trackfile();
  if (trackfile_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    trackfile_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:aubo.robot.communication.ProtoCommunicationOfflineExcitTraj.trackFile)
  return trackfile_;
}
inline ::std::string* ProtoCommunicationOfflineExcitTraj::release_trackfile() {
  clear_has_trackfile();
  if (trackfile_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = trackfile_;
    trackfile_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void ProtoCommunicationOfflineExcitTraj::set_allocated_trackfile(::std::string* trackfile) {
  if (trackfile_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete trackfile_;
  }
  if (trackfile) {
    set_has_trackfile();
    trackfile_ = trackfile;
  } else {
    clear_has_trackfile();
    trackfile_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.communication.ProtoCommunicationOfflineExcitTraj.trackFile)
}

// required int32 type = 2;
inline bool ProtoCommunicationOfflineExcitTraj::has_type() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void ProtoCommunicationOfflineExcitTraj::set_has_type() {
  _has_bits_[0] |= 0x00000002u;
}
inline void ProtoCommunicationOfflineExcitTraj::clear_has_type() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void ProtoCommunicationOfflineExcitTraj::clear_type() {
  type_ = 0;
  clear_has_type();
}
inline ::google::protobuf::int32 ProtoCommunicationOfflineExcitTraj::type() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoCommunicationOfflineExcitTraj.type)
  return type_;
}
inline void ProtoCommunicationOfflineExcitTraj::set_type(::google::protobuf::int32 value) {
  set_has_type();
  type_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ProtoCommunicationOfflineExcitTraj.type)
}

// required int32 subtype = 3;
inline bool ProtoCommunicationOfflineExcitTraj::has_subtype() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void ProtoCommunicationOfflineExcitTraj::set_has_subtype() {
  _has_bits_[0] |= 0x00000004u;
}
inline void ProtoCommunicationOfflineExcitTraj::clear_has_subtype() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void ProtoCommunicationOfflineExcitTraj::clear_subtype() {
  subtype_ = 0;
  clear_has_subtype();
}
inline ::google::protobuf::int32 ProtoCommunicationOfflineExcitTraj::subtype() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoCommunicationOfflineExcitTraj.subtype)
  return subtype_;
}
inline void ProtoCommunicationOfflineExcitTraj::set_subtype(::google::protobuf::int32 value) {
  set_has_subtype();
  subtype_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ProtoCommunicationOfflineExcitTraj.subtype)
}

// -------------------------------------------------------------------

// ProtoCommunicationDynIdentifyResults

// required int32 temp = 1;
inline bool ProtoCommunicationDynIdentifyResults::has_temp() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ProtoCommunicationDynIdentifyResults::set_has_temp() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ProtoCommunicationDynIdentifyResults::clear_has_temp() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ProtoCommunicationDynIdentifyResults::clear_temp() {
  temp_ = 0;
  clear_has_temp();
}
inline ::google::protobuf::int32 ProtoCommunicationDynIdentifyResults::temp() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoCommunicationDynIdentifyResults.temp)
  return temp_;
}
inline void ProtoCommunicationDynIdentifyResults::set_temp(::google::protobuf::int32 value) {
  set_has_temp();
  temp_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ProtoCommunicationDynIdentifyResults.temp)
}

// repeated int32 param = 2;
inline int ProtoCommunicationDynIdentifyResults::param_size() const {
  return param_.size();
}
inline void ProtoCommunicationDynIdentifyResults::clear_param() {
  param_.Clear();
}
inline ::google::protobuf::int32 ProtoCommunicationDynIdentifyResults::param(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoCommunicationDynIdentifyResults.param)
  return param_.Get(index);
}
inline void ProtoCommunicationDynIdentifyResults::set_param(int index, ::google::protobuf::int32 value) {
  param_.Set(index, value);
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ProtoCommunicationDynIdentifyResults.param)
}
inline void ProtoCommunicationDynIdentifyResults::add_param(::google::protobuf::int32 value) {
  param_.Add(value);
  // @@protoc_insertion_point(field_add:aubo.robot.communication.ProtoCommunicationDynIdentifyResults.param)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
ProtoCommunicationDynIdentifyResults::param() const {
  // @@protoc_insertion_point(field_list:aubo.robot.communication.ProtoCommunicationDynIdentifyResults.param)
  return param_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
ProtoCommunicationDynIdentifyResults::mutable_param() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.communication.ProtoCommunicationDynIdentifyResults.param)
  return &param_;
}

// repeated .aubo.robot.communication.ProtoRobotCommonResponse errorInfo = 3;
inline int ProtoCommunicationDynIdentifyResults::errorinfo_size() const {
  return errorinfo_.size();
}
inline void ProtoCommunicationDynIdentifyResults::clear_errorinfo() {
  errorinfo_.Clear();
}
inline const ::aubo::robot::communication::ProtoRobotCommonResponse& ProtoCommunicationDynIdentifyResults::errorinfo(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoCommunicationDynIdentifyResults.errorInfo)
  return errorinfo_.Get(index);
}
inline ::aubo::robot::communication::ProtoRobotCommonResponse* ProtoCommunicationDynIdentifyResults::mutable_errorinfo(int index) {
  // @@protoc_insertion_point(field_mutable:aubo.robot.communication.ProtoCommunicationDynIdentifyResults.errorInfo)
  return errorinfo_.Mutable(index);
}
inline ::aubo::robot::communication::ProtoRobotCommonResponse* ProtoCommunicationDynIdentifyResults::add_errorinfo() {
  // @@protoc_insertion_point(field_add:aubo.robot.communication.ProtoCommunicationDynIdentifyResults.errorInfo)
  return errorinfo_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::ProtoRobotCommonResponse >&
ProtoCommunicationDynIdentifyResults::errorinfo() const {
  // @@protoc_insertion_point(field_list:aubo.robot.communication.ProtoCommunicationDynIdentifyResults.errorInfo)
  return errorinfo_;
}
inline ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::ProtoRobotCommonResponse >*
ProtoCommunicationDynIdentifyResults::mutable_errorinfo() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.communication.ProtoCommunicationDynIdentifyResults.errorInfo)
  return &errorinfo_;
}

// -------------------------------------------------------------------

// InterfaceBoardError

// required bytes boardError = 1;
inline bool InterfaceBoardError::has_boarderror() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void InterfaceBoardError::set_has_boarderror() {
  _has_bits_[0] |= 0x00000001u;
}
inline void InterfaceBoardError::clear_has_boarderror() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void InterfaceBoardError::clear_boarderror() {
  if (boarderror_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    boarderror_->clear();
  }
  clear_has_boarderror();
}
inline const ::std::string& InterfaceBoardError::boarderror() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.InterfaceBoardError.boardError)
  return *boarderror_;
}
inline void InterfaceBoardError::set_boarderror(const ::std::string& value) {
  set_has_boarderror();
  if (boarderror_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    boarderror_ = new ::std::string;
  }
  boarderror_->assign(value);
  // @@protoc_insertion_point(field_set:aubo.robot.communication.InterfaceBoardError.boardError)
}
inline void InterfaceBoardError::set_boarderror(const char* value) {
  set_has_boarderror();
  if (boarderror_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    boarderror_ = new ::std::string;
  }
  boarderror_->assign(value);
  // @@protoc_insertion_point(field_set_char:aubo.robot.communication.InterfaceBoardError.boardError)
}
inline void InterfaceBoardError::set_boarderror(const void* value, size_t size) {
  set_has_boarderror();
  if (boarderror_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    boarderror_ = new ::std::string;
  }
  boarderror_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:aubo.robot.communication.InterfaceBoardError.boardError)
}
inline ::std::string* InterfaceBoardError::mutable_boarderror() {
  set_has_boarderror();
  if (boarderror_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    boarderror_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:aubo.robot.communication.InterfaceBoardError.boardError)
  return boarderror_;
}
inline ::std::string* InterfaceBoardError::release_boarderror() {
  clear_has_boarderror();
  if (boarderror_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = boarderror_;
    boarderror_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void InterfaceBoardError::set_allocated_boarderror(::std::string* boarderror) {
  if (boarderror_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete boarderror_;
  }
  if (boarderror) {
    set_has_boarderror();
    boarderror_ = boarderror;
  } else {
    clear_has_boarderror();
    boarderror_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.communication.InterfaceBoardError.boardError)
}

// -------------------------------------------------------------------

// ModbusCfg

// required string uuid = 1;
inline bool ModbusCfg::has_uuid() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ModbusCfg::set_has_uuid() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ModbusCfg::clear_has_uuid() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ModbusCfg::clear_uuid() {
  if (uuid_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    uuid_->clear();
  }
  clear_has_uuid();
}
inline const ::std::string& ModbusCfg::uuid() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ModbusCfg.uuid)
  return *uuid_;
}
inline void ModbusCfg::set_uuid(const ::std::string& value) {
  set_has_uuid();
  if (uuid_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    uuid_ = new ::std::string;
  }
  uuid_->assign(value);
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ModbusCfg.uuid)
}
inline void ModbusCfg::set_uuid(const char* value) {
  set_has_uuid();
  if (uuid_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    uuid_ = new ::std::string;
  }
  uuid_->assign(value);
  // @@protoc_insertion_point(field_set_char:aubo.robot.communication.ModbusCfg.uuid)
}
inline void ModbusCfg::set_uuid(const char* value, size_t size) {
  set_has_uuid();
  if (uuid_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    uuid_ = new ::std::string;
  }
  uuid_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:aubo.robot.communication.ModbusCfg.uuid)
}
inline ::std::string* ModbusCfg::mutable_uuid() {
  set_has_uuid();
  if (uuid_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    uuid_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:aubo.robot.communication.ModbusCfg.uuid)
  return uuid_;
}
inline ::std::string* ModbusCfg::release_uuid() {
  clear_has_uuid();
  if (uuid_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = uuid_;
    uuid_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void ModbusCfg::set_allocated_uuid(::std::string* uuid) {
  if (uuid_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete uuid_;
  }
  if (uuid) {
    set_has_uuid();
    uuid_ = uuid;
  } else {
    clear_has_uuid();
    uuid_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.communication.ModbusCfg.uuid)
}

// required string name = 2;
inline bool ModbusCfg::has_name() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void ModbusCfg::set_has_name() {
  _has_bits_[0] |= 0x00000002u;
}
inline void ModbusCfg::clear_has_name() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void ModbusCfg::clear_name() {
  if (name_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    name_->clear();
  }
  clear_has_name();
}
inline const ::std::string& ModbusCfg::name() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ModbusCfg.name)
  return *name_;
}
inline void ModbusCfg::set_name(const ::std::string& value) {
  set_has_name();
  if (name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    name_ = new ::std::string;
  }
  name_->assign(value);
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ModbusCfg.name)
}
inline void ModbusCfg::set_name(const char* value) {
  set_has_name();
  if (name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    name_ = new ::std::string;
  }
  name_->assign(value);
  // @@protoc_insertion_point(field_set_char:aubo.robot.communication.ModbusCfg.name)
}
inline void ModbusCfg::set_name(const char* value, size_t size) {
  set_has_name();
  if (name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    name_ = new ::std::string;
  }
  name_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:aubo.robot.communication.ModbusCfg.name)
}
inline ::std::string* ModbusCfg::mutable_name() {
  set_has_name();
  if (name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    name_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:aubo.robot.communication.ModbusCfg.name)
  return name_;
}
inline ::std::string* ModbusCfg::release_name() {
  clear_has_name();
  if (name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = name_;
    name_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void ModbusCfg::set_allocated_name(::std::string* name) {
  if (name_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete name_;
  }
  if (name) {
    set_has_name();
    name_ = name;
  } else {
    clear_has_name();
    name_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.communication.ModbusCfg.name)
}

// required .aubo.robot.communication.ModbusMode mode = 3;
inline bool ModbusCfg::has_mode() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void ModbusCfg::set_has_mode() {
  _has_bits_[0] |= 0x00000004u;
}
inline void ModbusCfg::clear_has_mode() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void ModbusCfg::clear_mode() {
  mode_ = 0;
  clear_has_mode();
}
inline ::aubo::robot::communication::ModbusMode ModbusCfg::mode() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ModbusCfg.mode)
  return static_cast< ::aubo::robot::communication::ModbusMode >(mode_);
}
inline void ModbusCfg::set_mode(::aubo::robot::communication::ModbusMode value) {
  assert(::aubo::robot::communication::ModbusMode_IsValid(value));
  set_has_mode();
  mode_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ModbusCfg.mode)
}

// required uint32 slave = 4;
inline bool ModbusCfg::has_slave() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void ModbusCfg::set_has_slave() {
  _has_bits_[0] |= 0x00000008u;
}
inline void ModbusCfg::clear_has_slave() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void ModbusCfg::clear_slave() {
  slave_ = 0u;
  clear_has_slave();
}
inline ::google::protobuf::uint32 ModbusCfg::slave() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ModbusCfg.slave)
  return slave_;
}
inline void ModbusCfg::set_slave(::google::protobuf::uint32 value) {
  set_has_slave();
  slave_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ModbusCfg.slave)
}

// required string ip = 5;
inline bool ModbusCfg::has_ip() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void ModbusCfg::set_has_ip() {
  _has_bits_[0] |= 0x00000010u;
}
inline void ModbusCfg::clear_has_ip() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void ModbusCfg::clear_ip() {
  if (ip_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    ip_->clear();
  }
  clear_has_ip();
}
inline const ::std::string& ModbusCfg::ip() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ModbusCfg.ip)
  return *ip_;
}
inline void ModbusCfg::set_ip(const ::std::string& value) {
  set_has_ip();
  if (ip_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    ip_ = new ::std::string;
  }
  ip_->assign(value);
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ModbusCfg.ip)
}
inline void ModbusCfg::set_ip(const char* value) {
  set_has_ip();
  if (ip_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    ip_ = new ::std::string;
  }
  ip_->assign(value);
  // @@protoc_insertion_point(field_set_char:aubo.robot.communication.ModbusCfg.ip)
}
inline void ModbusCfg::set_ip(const char* value, size_t size) {
  set_has_ip();
  if (ip_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    ip_ = new ::std::string;
  }
  ip_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:aubo.robot.communication.ModbusCfg.ip)
}
inline ::std::string* ModbusCfg::mutable_ip() {
  set_has_ip();
  if (ip_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    ip_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:aubo.robot.communication.ModbusCfg.ip)
  return ip_;
}
inline ::std::string* ModbusCfg::release_ip() {
  clear_has_ip();
  if (ip_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = ip_;
    ip_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void ModbusCfg::set_allocated_ip(::std::string* ip) {
  if (ip_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete ip_;
  }
  if (ip) {
    set_has_ip();
    ip_ = ip;
  } else {
    clear_has_ip();
    ip_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.communication.ModbusCfg.ip)
}

// required uint32 port = 6;
inline bool ModbusCfg::has_port() const {
  return (_has_bits_[0] & 0x00000020u) != 0;
}
inline void ModbusCfg::set_has_port() {
  _has_bits_[0] |= 0x00000020u;
}
inline void ModbusCfg::clear_has_port() {
  _has_bits_[0] &= ~0x00000020u;
}
inline void ModbusCfg::clear_port() {
  port_ = 0u;
  clear_has_port();
}
inline ::google::protobuf::uint32 ModbusCfg::port() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ModbusCfg.port)
  return port_;
}
inline void ModbusCfg::set_port(::google::protobuf::uint32 value) {
  set_has_port();
  port_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ModbusCfg.port)
}

// required string device = 7;
inline bool ModbusCfg::has_device() const {
  return (_has_bits_[0] & 0x00000040u) != 0;
}
inline void ModbusCfg::set_has_device() {
  _has_bits_[0] |= 0x00000040u;
}
inline void ModbusCfg::clear_has_device() {
  _has_bits_[0] &= ~0x00000040u;
}
inline void ModbusCfg::clear_device() {
  if (device_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    device_->clear();
  }
  clear_has_device();
}
inline const ::std::string& ModbusCfg::device() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ModbusCfg.device)
  return *device_;
}
inline void ModbusCfg::set_device(const ::std::string& value) {
  set_has_device();
  if (device_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    device_ = new ::std::string;
  }
  device_->assign(value);
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ModbusCfg.device)
}
inline void ModbusCfg::set_device(const char* value) {
  set_has_device();
  if (device_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    device_ = new ::std::string;
  }
  device_->assign(value);
  // @@protoc_insertion_point(field_set_char:aubo.robot.communication.ModbusCfg.device)
}
inline void ModbusCfg::set_device(const char* value, size_t size) {
  set_has_device();
  if (device_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    device_ = new ::std::string;
  }
  device_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:aubo.robot.communication.ModbusCfg.device)
}
inline ::std::string* ModbusCfg::mutable_device() {
  set_has_device();
  if (device_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    device_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:aubo.robot.communication.ModbusCfg.device)
  return device_;
}
inline ::std::string* ModbusCfg::release_device() {
  clear_has_device();
  if (device_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = device_;
    device_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void ModbusCfg::set_allocated_device(::std::string* device) {
  if (device_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete device_;
  }
  if (device) {
    set_has_device();
    device_ = device;
  } else {
    clear_has_device();
    device_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.communication.ModbusCfg.device)
}

// required uint32 baud = 8;
inline bool ModbusCfg::has_baud() const {
  return (_has_bits_[0] & 0x00000080u) != 0;
}
inline void ModbusCfg::set_has_baud() {
  _has_bits_[0] |= 0x00000080u;
}
inline void ModbusCfg::clear_has_baud() {
  _has_bits_[0] &= ~0x00000080u;
}
inline void ModbusCfg::clear_baud() {
  baud_ = 0u;
  clear_has_baud();
}
inline ::google::protobuf::uint32 ModbusCfg::baud() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ModbusCfg.baud)
  return baud_;
}
inline void ModbusCfg::set_baud(::google::protobuf::uint32 value) {
  set_has_baud();
  baud_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ModbusCfg.baud)
}

// required string parity = 9;
inline bool ModbusCfg::has_parity() const {
  return (_has_bits_[0] & 0x00000100u) != 0;
}
inline void ModbusCfg::set_has_parity() {
  _has_bits_[0] |= 0x00000100u;
}
inline void ModbusCfg::clear_has_parity() {
  _has_bits_[0] &= ~0x00000100u;
}
inline void ModbusCfg::clear_parity() {
  if (parity_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    parity_->clear();
  }
  clear_has_parity();
}
inline const ::std::string& ModbusCfg::parity() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ModbusCfg.parity)
  return *parity_;
}
inline void ModbusCfg::set_parity(const ::std::string& value) {
  set_has_parity();
  if (parity_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    parity_ = new ::std::string;
  }
  parity_->assign(value);
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ModbusCfg.parity)
}
inline void ModbusCfg::set_parity(const char* value) {
  set_has_parity();
  if (parity_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    parity_ = new ::std::string;
  }
  parity_->assign(value);
  // @@protoc_insertion_point(field_set_char:aubo.robot.communication.ModbusCfg.parity)
}
inline void ModbusCfg::set_parity(const char* value, size_t size) {
  set_has_parity();
  if (parity_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    parity_ = new ::std::string;
  }
  parity_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:aubo.robot.communication.ModbusCfg.parity)
}
inline ::std::string* ModbusCfg::mutable_parity() {
  set_has_parity();
  if (parity_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    parity_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:aubo.robot.communication.ModbusCfg.parity)
  return parity_;
}
inline ::std::string* ModbusCfg::release_parity() {
  clear_has_parity();
  if (parity_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = parity_;
    parity_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void ModbusCfg::set_allocated_parity(::std::string* parity) {
  if (parity_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete parity_;
  }
  if (parity) {
    set_has_parity();
    parity_ = parity;
  } else {
    clear_has_parity();
    parity_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.communication.ModbusCfg.parity)
}

// required uint32 data_bits = 10;
inline bool ModbusCfg::has_data_bits() const {
  return (_has_bits_[0] & 0x00000200u) != 0;
}
inline void ModbusCfg::set_has_data_bits() {
  _has_bits_[0] |= 0x00000200u;
}
inline void ModbusCfg::clear_has_data_bits() {
  _has_bits_[0] &= ~0x00000200u;
}
inline void ModbusCfg::clear_data_bits() {
  data_bits_ = 0u;
  clear_has_data_bits();
}
inline ::google::protobuf::uint32 ModbusCfg::data_bits() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ModbusCfg.data_bits)
  return data_bits_;
}
inline void ModbusCfg::set_data_bits(::google::protobuf::uint32 value) {
  set_has_data_bits();
  data_bits_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ModbusCfg.data_bits)
}

// required uint32 stop_bits = 11;
inline bool ModbusCfg::has_stop_bits() const {
  return (_has_bits_[0] & 0x00000400u) != 0;
}
inline void ModbusCfg::set_has_stop_bits() {
  _has_bits_[0] |= 0x00000400u;
}
inline void ModbusCfg::clear_has_stop_bits() {
  _has_bits_[0] &= ~0x00000400u;
}
inline void ModbusCfg::clear_stop_bits() {
  stop_bits_ = 0u;
  clear_has_stop_bits();
}
inline ::google::protobuf::uint32 ModbusCfg::stop_bits() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ModbusCfg.stop_bits)
  return stop_bits_;
}
inline void ModbusCfg::set_stop_bits(::google::protobuf::uint32 value) {
  set_has_stop_bits();
  stop_bits_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ModbusCfg.stop_bits)
}

// required uint32 response = 12;
inline bool ModbusCfg::has_response() const {
  return (_has_bits_[0] & 0x00000800u) != 0;
}
inline void ModbusCfg::set_has_response() {
  _has_bits_[0] |= 0x00000800u;
}
inline void ModbusCfg::clear_has_response() {
  _has_bits_[0] &= ~0x00000800u;
}
inline void ModbusCfg::clear_response() {
  response_ = 0u;
  clear_has_response();
}
inline ::google::protobuf::uint32 ModbusCfg::response() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ModbusCfg.response)
  return response_;
}
inline void ModbusCfg::set_response(::google::protobuf::uint32 value) {
  set_has_response();
  response_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ModbusCfg.response)
}

// required uint32 frequency = 13;
inline bool ModbusCfg::has_frequency() const {
  return (_has_bits_[0] & 0x00001000u) != 0;
}
inline void ModbusCfg::set_has_frequency() {
  _has_bits_[0] |= 0x00001000u;
}
inline void ModbusCfg::clear_has_frequency() {
  _has_bits_[0] &= ~0x00001000u;
}
inline void ModbusCfg::clear_frequency() {
  frequency_ = 0u;
  clear_has_frequency();
}
inline ::google::protobuf::uint32 ModbusCfg::frequency() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ModbusCfg.frequency)
  return frequency_;
}
inline void ModbusCfg::set_frequency(::google::protobuf::uint32 value) {
  set_has_frequency();
  frequency_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ModbusCfg.frequency)
}

// -------------------------------------------------------------------

// TagIoCfg

// required string io_name = 1;
inline bool TagIoCfg::has_io_name() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void TagIoCfg::set_has_io_name() {
  _has_bits_[0] |= 0x00000001u;
}
inline void TagIoCfg::clear_has_io_name() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void TagIoCfg::clear_io_name() {
  if (io_name_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    io_name_->clear();
  }
  clear_has_io_name();
}
inline const ::std::string& TagIoCfg::io_name() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.TagIoCfg.io_name)
  return *io_name_;
}
inline void TagIoCfg::set_io_name(const ::std::string& value) {
  set_has_io_name();
  if (io_name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    io_name_ = new ::std::string;
  }
  io_name_->assign(value);
  // @@protoc_insertion_point(field_set:aubo.robot.communication.TagIoCfg.io_name)
}
inline void TagIoCfg::set_io_name(const char* value) {
  set_has_io_name();
  if (io_name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    io_name_ = new ::std::string;
  }
  io_name_->assign(value);
  // @@protoc_insertion_point(field_set_char:aubo.robot.communication.TagIoCfg.io_name)
}
inline void TagIoCfg::set_io_name(const char* value, size_t size) {
  set_has_io_name();
  if (io_name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    io_name_ = new ::std::string;
  }
  io_name_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:aubo.robot.communication.TagIoCfg.io_name)
}
inline ::std::string* TagIoCfg::mutable_io_name() {
  set_has_io_name();
  if (io_name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    io_name_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:aubo.robot.communication.TagIoCfg.io_name)
  return io_name_;
}
inline ::std::string* TagIoCfg::release_io_name() {
  clear_has_io_name();
  if (io_name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = io_name_;
    io_name_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void TagIoCfg::set_allocated_io_name(::std::string* io_name) {
  if (io_name_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete io_name_;
  }
  if (io_name) {
    set_has_io_name();
    io_name_ = io_name;
  } else {
    clear_has_io_name();
    io_name_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.communication.TagIoCfg.io_name)
}

// required .aubo.robot.communication.TagIoType io_type = 2;
inline bool TagIoCfg::has_io_type() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void TagIoCfg::set_has_io_type() {
  _has_bits_[0] |= 0x00000002u;
}
inline void TagIoCfg::clear_has_io_type() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void TagIoCfg::clear_io_type() {
  io_type_ = 0;
  clear_has_io_type();
}
inline ::aubo::robot::communication::TagIoType TagIoCfg::io_type() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.TagIoCfg.io_type)
  return static_cast< ::aubo::robot::communication::TagIoType >(io_type_);
}
inline void TagIoCfg::set_io_type(::aubo::robot::communication::TagIoType value) {
  assert(::aubo::robot::communication::TagIoType_IsValid(value));
  set_has_io_type();
  io_type_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.TagIoCfg.io_type)
}

// required uint32 register_addr = 3;
inline bool TagIoCfg::has_register_addr() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void TagIoCfg::set_has_register_addr() {
  _has_bits_[0] |= 0x00000004u;
}
inline void TagIoCfg::clear_has_register_addr() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void TagIoCfg::clear_register_addr() {
  register_addr_ = 0u;
  clear_has_register_addr();
}
inline ::google::protobuf::uint32 TagIoCfg::register_addr() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.TagIoCfg.register_addr)
  return register_addr_;
}
inline void TagIoCfg::set_register_addr(::google::protobuf::uint32 value) {
  set_has_register_addr();
  register_addr_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.TagIoCfg.register_addr)
}

// required string modbus_uuid = 4;
inline bool TagIoCfg::has_modbus_uuid() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void TagIoCfg::set_has_modbus_uuid() {
  _has_bits_[0] |= 0x00000008u;
}
inline void TagIoCfg::clear_has_modbus_uuid() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void TagIoCfg::clear_modbus_uuid() {
  if (modbus_uuid_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    modbus_uuid_->clear();
  }
  clear_has_modbus_uuid();
}
inline const ::std::string& TagIoCfg::modbus_uuid() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.TagIoCfg.modbus_uuid)
  return *modbus_uuid_;
}
inline void TagIoCfg::set_modbus_uuid(const ::std::string& value) {
  set_has_modbus_uuid();
  if (modbus_uuid_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    modbus_uuid_ = new ::std::string;
  }
  modbus_uuid_->assign(value);
  // @@protoc_insertion_point(field_set:aubo.robot.communication.TagIoCfg.modbus_uuid)
}
inline void TagIoCfg::set_modbus_uuid(const char* value) {
  set_has_modbus_uuid();
  if (modbus_uuid_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    modbus_uuid_ = new ::std::string;
  }
  modbus_uuid_->assign(value);
  // @@protoc_insertion_point(field_set_char:aubo.robot.communication.TagIoCfg.modbus_uuid)
}
inline void TagIoCfg::set_modbus_uuid(const char* value, size_t size) {
  set_has_modbus_uuid();
  if (modbus_uuid_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    modbus_uuid_ = new ::std::string;
  }
  modbus_uuid_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:aubo.robot.communication.TagIoCfg.modbus_uuid)
}
inline ::std::string* TagIoCfg::mutable_modbus_uuid() {
  set_has_modbus_uuid();
  if (modbus_uuid_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    modbus_uuid_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:aubo.robot.communication.TagIoCfg.modbus_uuid)
  return modbus_uuid_;
}
inline ::std::string* TagIoCfg::release_modbus_uuid() {
  clear_has_modbus_uuid();
  if (modbus_uuid_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = modbus_uuid_;
    modbus_uuid_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void TagIoCfg::set_allocated_modbus_uuid(::std::string* modbus_uuid) {
  if (modbus_uuid_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete modbus_uuid_;
  }
  if (modbus_uuid) {
    set_has_modbus_uuid();
    modbus_uuid_ = modbus_uuid;
  } else {
    clear_has_modbus_uuid();
    modbus_uuid_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.communication.TagIoCfg.modbus_uuid)
}

// required string io_id = 5;
inline bool TagIoCfg::has_io_id() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void TagIoCfg::set_has_io_id() {
  _has_bits_[0] |= 0x00000010u;
}
inline void TagIoCfg::clear_has_io_id() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void TagIoCfg::clear_io_id() {
  if (io_id_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    io_id_->clear();
  }
  clear_has_io_id();
}
inline const ::std::string& TagIoCfg::io_id() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.TagIoCfg.io_id)
  return *io_id_;
}
inline void TagIoCfg::set_io_id(const ::std::string& value) {
  set_has_io_id();
  if (io_id_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    io_id_ = new ::std::string;
  }
  io_id_->assign(value);
  // @@protoc_insertion_point(field_set:aubo.robot.communication.TagIoCfg.io_id)
}
inline void TagIoCfg::set_io_id(const char* value) {
  set_has_io_id();
  if (io_id_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    io_id_ = new ::std::string;
  }
  io_id_->assign(value);
  // @@protoc_insertion_point(field_set_char:aubo.robot.communication.TagIoCfg.io_id)
}
inline void TagIoCfg::set_io_id(const char* value, size_t size) {
  set_has_io_id();
  if (io_id_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    io_id_ = new ::std::string;
  }
  io_id_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:aubo.robot.communication.TagIoCfg.io_id)
}
inline ::std::string* TagIoCfg::mutable_io_id() {
  set_has_io_id();
  if (io_id_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    io_id_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:aubo.robot.communication.TagIoCfg.io_id)
  return io_id_;
}
inline ::std::string* TagIoCfg::release_io_id() {
  clear_has_io_id();
  if (io_id_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = io_id_;
    io_id_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void TagIoCfg::set_allocated_io_id(::std::string* io_id) {
  if (io_id_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete io_id_;
  }
  if (io_id) {
    set_has_io_id();
    io_id_ = io_id;
  } else {
    clear_has_io_id();
    io_id_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.communication.TagIoCfg.io_id)
}

// required double io_value = 6;
inline bool TagIoCfg::has_io_value() const {
  return (_has_bits_[0] & 0x00000020u) != 0;
}
inline void TagIoCfg::set_has_io_value() {
  _has_bits_[0] |= 0x00000020u;
}
inline void TagIoCfg::clear_has_io_value() {
  _has_bits_[0] &= ~0x00000020u;
}
inline void TagIoCfg::clear_io_value() {
  io_value_ = 0;
  clear_has_io_value();
}
inline double TagIoCfg::io_value() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.TagIoCfg.io_value)
  return io_value_;
}
inline void TagIoCfg::set_io_value(double value) {
  set_has_io_value();
  io_value_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.TagIoCfg.io_value)
}

// -------------------------------------------------------------------

// ToolDigitalStatus

// required uint32 ioType = 1;
inline bool ToolDigitalStatus::has_iotype() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ToolDigitalStatus::set_has_iotype() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ToolDigitalStatus::clear_has_iotype() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ToolDigitalStatus::clear_iotype() {
  iotype_ = 0u;
  clear_has_iotype();
}
inline ::google::protobuf::uint32 ToolDigitalStatus::iotype() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ToolDigitalStatus.ioType)
  return iotype_;
}
inline void ToolDigitalStatus::set_iotype(::google::protobuf::uint32 value) {
  set_has_iotype();
  iotype_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ToolDigitalStatus.ioType)
}

// required uint32 ioData = 2;
inline bool ToolDigitalStatus::has_iodata() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void ToolDigitalStatus::set_has_iodata() {
  _has_bits_[0] |= 0x00000002u;
}
inline void ToolDigitalStatus::clear_has_iodata() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void ToolDigitalStatus::clear_iodata() {
  iodata_ = 0u;
  clear_has_iodata();
}
inline ::google::protobuf::uint32 ToolDigitalStatus::iodata() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ToolDigitalStatus.ioData)
  return iodata_;
}
inline void ToolDigitalStatus::set_iodata(::google::protobuf::uint32 value) {
  set_has_iodata();
  iodata_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ToolDigitalStatus.ioData)
}

// -------------------------------------------------------------------

// RobotToolStatus

// required uint32 ioData = 1;
inline bool RobotToolStatus::has_iodata() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void RobotToolStatus::set_has_iodata() {
  _has_bits_[0] |= 0x00000001u;
}
inline void RobotToolStatus::clear_has_iodata() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void RobotToolStatus::clear_iodata() {
  iodata_ = 0u;
  clear_has_iodata();
}
inline ::google::protobuf::uint32 RobotToolStatus::iodata() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.RobotToolStatus.ioData)
  return iodata_;
}
inline void RobotToolStatus::set_iodata(::google::protobuf::uint32 value) {
  set_has_iodata();
  iodata_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.RobotToolStatus.ioData)
}

// repeated float aiData = 2;
inline int RobotToolStatus::aidata_size() const {
  return aidata_.size();
}
inline void RobotToolStatus::clear_aidata() {
  aidata_.Clear();
}
inline float RobotToolStatus::aidata(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.RobotToolStatus.aiData)
  return aidata_.Get(index);
}
inline void RobotToolStatus::set_aidata(int index, float value) {
  aidata_.Set(index, value);
  // @@protoc_insertion_point(field_set:aubo.robot.communication.RobotToolStatus.aiData)
}
inline void RobotToolStatus::add_aidata(float value) {
  aidata_.Add(value);
  // @@protoc_insertion_point(field_add:aubo.robot.communication.RobotToolStatus.aiData)
}
inline const ::google::protobuf::RepeatedField< float >&
RobotToolStatus::aidata() const {
  // @@protoc_insertion_point(field_list:aubo.robot.communication.RobotToolStatus.aiData)
  return aidata_;
}
inline ::google::protobuf::RepeatedField< float >*
RobotToolStatus::mutable_aidata() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.communication.RobotToolStatus.aiData)
  return &aidata_;
}

// required float systemVoltage = 3;
inline bool RobotToolStatus::has_systemvoltage() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void RobotToolStatus::set_has_systemvoltage() {
  _has_bits_[0] |= 0x00000004u;
}
inline void RobotToolStatus::clear_has_systemvoltage() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void RobotToolStatus::clear_systemvoltage() {
  systemvoltage_ = 0;
  clear_has_systemvoltage();
}
inline float RobotToolStatus::systemvoltage() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.RobotToolStatus.systemVoltage)
  return systemvoltage_;
}
inline void RobotToolStatus::set_systemvoltage(float value) {
  set_has_systemvoltage();
  systemvoltage_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.RobotToolStatus.systemVoltage)
}

// required float systemTemperature = 4;
inline bool RobotToolStatus::has_systemtemperature() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void RobotToolStatus::set_has_systemtemperature() {
  _has_bits_[0] |= 0x00000008u;
}
inline void RobotToolStatus::clear_has_systemtemperature() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void RobotToolStatus::clear_systemtemperature() {
  systemtemperature_ = 0;
  clear_has_systemtemperature();
}
inline float RobotToolStatus::systemtemperature() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.RobotToolStatus.systemTemperature)
  return systemtemperature_;
}
inline void RobotToolStatus::set_systemtemperature(float value) {
  set_has_systemtemperature();
  systemtemperature_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.RobotToolStatus.systemTemperature)
}

// required uint32 errorStatus = 5;
inline bool RobotToolStatus::has_errorstatus() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void RobotToolStatus::set_has_errorstatus() {
  _has_bits_[0] |= 0x00000010u;
}
inline void RobotToolStatus::clear_has_errorstatus() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void RobotToolStatus::clear_errorstatus() {
  errorstatus_ = 0u;
  clear_has_errorstatus();
}
inline ::google::protobuf::uint32 RobotToolStatus::errorstatus() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.RobotToolStatus.errorStatus)
  return errorstatus_;
}
inline void RobotToolStatus::set_errorstatus(::google::protobuf::uint32 value) {
  set_has_errorstatus();
  errorstatus_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.RobotToolStatus.errorStatus)
}

// -------------------------------------------------------------------

// toolAllIOStatus

// required uint32 powerType = 1;
inline bool toolAllIOStatus::has_powertype() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void toolAllIOStatus::set_has_powertype() {
  _has_bits_[0] |= 0x00000001u;
}
inline void toolAllIOStatus::clear_has_powertype() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void toolAllIOStatus::clear_powertype() {
  powertype_ = 0u;
  clear_has_powertype();
}
inline ::google::protobuf::uint32 toolAllIOStatus::powertype() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.toolAllIOStatus.powerType)
  return powertype_;
}
inline void toolAllIOStatus::set_powertype(::google::protobuf::uint32 value) {
  set_has_powertype();
  powertype_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.toolAllIOStatus.powerType)
}

// required double systemVoltage = 2;
inline bool toolAllIOStatus::has_systemvoltage() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void toolAllIOStatus::set_has_systemvoltage() {
  _has_bits_[0] |= 0x00000002u;
}
inline void toolAllIOStatus::clear_has_systemvoltage() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void toolAllIOStatus::clear_systemvoltage() {
  systemvoltage_ = 0;
  clear_has_systemvoltage();
}
inline double toolAllIOStatus::systemvoltage() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.toolAllIOStatus.systemVoltage)
  return systemvoltage_;
}
inline void toolAllIOStatus::set_systemvoltage(double value) {
  set_has_systemvoltage();
  systemvoltage_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.toolAllIOStatus.systemVoltage)
}

// required double systemTemperature = 3;
inline bool toolAllIOStatus::has_systemtemperature() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void toolAllIOStatus::set_has_systemtemperature() {
  _has_bits_[0] |= 0x00000004u;
}
inline void toolAllIOStatus::clear_has_systemtemperature() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void toolAllIOStatus::clear_systemtemperature() {
  systemtemperature_ = 0;
  clear_has_systemtemperature();
}
inline double toolAllIOStatus::systemtemperature() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.toolAllIOStatus.systemTemperature)
  return systemtemperature_;
}
inline void toolAllIOStatus::set_systemtemperature(double value) {
  set_has_systemtemperature();
  systemtemperature_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.toolAllIOStatus.systemTemperature)
}

// required uint32 errorStatus = 4;
inline bool toolAllIOStatus::has_errorstatus() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void toolAllIOStatus::set_has_errorstatus() {
  _has_bits_[0] |= 0x00000008u;
}
inline void toolAllIOStatus::clear_has_errorstatus() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void toolAllIOStatus::clear_errorstatus() {
  errorstatus_ = 0u;
  clear_has_errorstatus();
}
inline ::google::protobuf::uint32 toolAllIOStatus::errorstatus() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.toolAllIOStatus.errorStatus)
  return errorstatus_;
}
inline void toolAllIOStatus::set_errorstatus(::google::protobuf::uint32 value) {
  set_has_errorstatus();
  errorstatus_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.toolAllIOStatus.errorStatus)
}

// repeated .aubo.robot.communication.ToolDigitalStatus digitalIoStatus = 5;
inline int toolAllIOStatus::digitaliostatus_size() const {
  return digitaliostatus_.size();
}
inline void toolAllIOStatus::clear_digitaliostatus() {
  digitaliostatus_.Clear();
}
inline const ::aubo::robot::communication::ToolDigitalStatus& toolAllIOStatus::digitaliostatus(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.toolAllIOStatus.digitalIoStatus)
  return digitaliostatus_.Get(index);
}
inline ::aubo::robot::communication::ToolDigitalStatus* toolAllIOStatus::mutable_digitaliostatus(int index) {
  // @@protoc_insertion_point(field_mutable:aubo.robot.communication.toolAllIOStatus.digitalIoStatus)
  return digitaliostatus_.Mutable(index);
}
inline ::aubo::robot::communication::ToolDigitalStatus* toolAllIOStatus::add_digitaliostatus() {
  // @@protoc_insertion_point(field_add:aubo.robot.communication.toolAllIOStatus.digitalIoStatus)
  return digitaliostatus_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::ToolDigitalStatus >&
toolAllIOStatus::digitaliostatus() const {
  // @@protoc_insertion_point(field_list:aubo.robot.communication.toolAllIOStatus.digitalIoStatus)
  return digitaliostatus_;
}
inline ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::ToolDigitalStatus >*
toolAllIOStatus::mutable_digitaliostatus() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.communication.toolAllIOStatus.digitalIoStatus)
  return &digitaliostatus_;
}

// repeated double aiData = 6;
inline int toolAllIOStatus::aidata_size() const {
  return aidata_.size();
}
inline void toolAllIOStatus::clear_aidata() {
  aidata_.Clear();
}
inline double toolAllIOStatus::aidata(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.toolAllIOStatus.aiData)
  return aidata_.Get(index);
}
inline void toolAllIOStatus::set_aidata(int index, double value) {
  aidata_.Set(index, value);
  // @@protoc_insertion_point(field_set:aubo.robot.communication.toolAllIOStatus.aiData)
}
inline void toolAllIOStatus::add_aidata(double value) {
  aidata_.Add(value);
  // @@protoc_insertion_point(field_add:aubo.robot.communication.toolAllIOStatus.aiData)
}
inline const ::google::protobuf::RepeatedField< double >&
toolAllIOStatus::aidata() const {
  // @@protoc_insertion_point(field_list:aubo.robot.communication.toolAllIOStatus.aiData)
  return aidata_;
}
inline ::google::protobuf::RepeatedField< double >*
toolAllIOStatus::mutable_aidata() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.communication.toolAllIOStatus.aiData)
  return &aidata_;
}

// -------------------------------------------------------------------

// ProtoCommunicationToolAllIOStatusInfoResponse

// required .aubo.robot.communication.toolAllIOStatus toolIOStatus = 1;
inline bool ProtoCommunicationToolAllIOStatusInfoResponse::has_tooliostatus() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ProtoCommunicationToolAllIOStatusInfoResponse::set_has_tooliostatus() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ProtoCommunicationToolAllIOStatusInfoResponse::clear_has_tooliostatus() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ProtoCommunicationToolAllIOStatusInfoResponse::clear_tooliostatus() {
  if (tooliostatus_ != NULL) tooliostatus_->::aubo::robot::communication::toolAllIOStatus::Clear();
  clear_has_tooliostatus();
}
inline const ::aubo::robot::communication::toolAllIOStatus& ProtoCommunicationToolAllIOStatusInfoResponse::tooliostatus() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoCommunicationToolAllIOStatusInfoResponse.toolIOStatus)
  return tooliostatus_ != NULL ? *tooliostatus_ : *default_instance_->tooliostatus_;
}
inline ::aubo::robot::communication::toolAllIOStatus* ProtoCommunicationToolAllIOStatusInfoResponse::mutable_tooliostatus() {
  set_has_tooliostatus();
  if (tooliostatus_ == NULL) tooliostatus_ = new ::aubo::robot::communication::toolAllIOStatus;
  // @@protoc_insertion_point(field_mutable:aubo.robot.communication.ProtoCommunicationToolAllIOStatusInfoResponse.toolIOStatus)
  return tooliostatus_;
}
inline ::aubo::robot::communication::toolAllIOStatus* ProtoCommunicationToolAllIOStatusInfoResponse::release_tooliostatus() {
  clear_has_tooliostatus();
  ::aubo::robot::communication::toolAllIOStatus* temp = tooliostatus_;
  tooliostatus_ = NULL;
  return temp;
}
inline void ProtoCommunicationToolAllIOStatusInfoResponse::set_allocated_tooliostatus(::aubo::robot::communication::toolAllIOStatus* tooliostatus) {
  delete tooliostatus_;
  tooliostatus_ = tooliostatus;
  if (tooliostatus) {
    set_has_tooliostatus();
  } else {
    clear_has_tooliostatus();
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.communication.ProtoCommunicationToolAllIOStatusInfoResponse.toolIOStatus)
}

// required .aubo.robot.communication.ProtoRobotCommonResponse errorInfo = 2;
inline bool ProtoCommunicationToolAllIOStatusInfoResponse::has_errorinfo() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void ProtoCommunicationToolAllIOStatusInfoResponse::set_has_errorinfo() {
  _has_bits_[0] |= 0x00000002u;
}
inline void ProtoCommunicationToolAllIOStatusInfoResponse::clear_has_errorinfo() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void ProtoCommunicationToolAllIOStatusInfoResponse::clear_errorinfo() {
  if (errorinfo_ != NULL) errorinfo_->::aubo::robot::communication::ProtoRobotCommonResponse::Clear();
  clear_has_errorinfo();
}
inline const ::aubo::robot::communication::ProtoRobotCommonResponse& ProtoCommunicationToolAllIOStatusInfoResponse::errorinfo() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoCommunicationToolAllIOStatusInfoResponse.errorInfo)
  return errorinfo_ != NULL ? *errorinfo_ : *default_instance_->errorinfo_;
}
inline ::aubo::robot::communication::ProtoRobotCommonResponse* ProtoCommunicationToolAllIOStatusInfoResponse::mutable_errorinfo() {
  set_has_errorinfo();
  if (errorinfo_ == NULL) errorinfo_ = new ::aubo::robot::communication::ProtoRobotCommonResponse;
  // @@protoc_insertion_point(field_mutable:aubo.robot.communication.ProtoCommunicationToolAllIOStatusInfoResponse.errorInfo)
  return errorinfo_;
}
inline ::aubo::robot::communication::ProtoRobotCommonResponse* ProtoCommunicationToolAllIOStatusInfoResponse::release_errorinfo() {
  clear_has_errorinfo();
  ::aubo::robot::communication::ProtoRobotCommonResponse* temp = errorinfo_;
  errorinfo_ = NULL;
  return temp;
}
inline void ProtoCommunicationToolAllIOStatusInfoResponse::set_allocated_errorinfo(::aubo::robot::communication::ProtoRobotCommonResponse* errorinfo) {
  delete errorinfo_;
  errorinfo_ = errorinfo;
  if (errorinfo) {
    set_has_errorinfo();
  } else {
    clear_has_errorinfo();
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.communication.ProtoCommunicationToolAllIOStatusInfoResponse.errorInfo)
}

// -------------------------------------------------------------------

// ProtoCommunicationRobotBoardFirmware

// required int32 command = 1;
inline bool ProtoCommunicationRobotBoardFirmware::has_command() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ProtoCommunicationRobotBoardFirmware::set_has_command() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ProtoCommunicationRobotBoardFirmware::clear_has_command() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ProtoCommunicationRobotBoardFirmware::clear_command() {
  command_ = 0;
  clear_has_command();
}
inline ::google::protobuf::int32 ProtoCommunicationRobotBoardFirmware::command() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoCommunicationRobotBoardFirmware.command)
  return command_;
}
inline void ProtoCommunicationRobotBoardFirmware::set_command(::google::protobuf::int32 value) {
  set_has_command();
  command_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ProtoCommunicationRobotBoardFirmware.command)
}

// required bytes firmwareContent = 2;
inline bool ProtoCommunicationRobotBoardFirmware::has_firmwarecontent() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void ProtoCommunicationRobotBoardFirmware::set_has_firmwarecontent() {
  _has_bits_[0] |= 0x00000002u;
}
inline void ProtoCommunicationRobotBoardFirmware::clear_has_firmwarecontent() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void ProtoCommunicationRobotBoardFirmware::clear_firmwarecontent() {
  if (firmwarecontent_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    firmwarecontent_->clear();
  }
  clear_has_firmwarecontent();
}
inline const ::std::string& ProtoCommunicationRobotBoardFirmware::firmwarecontent() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoCommunicationRobotBoardFirmware.firmwareContent)
  return *firmwarecontent_;
}
inline void ProtoCommunicationRobotBoardFirmware::set_firmwarecontent(const ::std::string& value) {
  set_has_firmwarecontent();
  if (firmwarecontent_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    firmwarecontent_ = new ::std::string;
  }
  firmwarecontent_->assign(value);
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ProtoCommunicationRobotBoardFirmware.firmwareContent)
}
inline void ProtoCommunicationRobotBoardFirmware::set_firmwarecontent(const char* value) {
  set_has_firmwarecontent();
  if (firmwarecontent_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    firmwarecontent_ = new ::std::string;
  }
  firmwarecontent_->assign(value);
  // @@protoc_insertion_point(field_set_char:aubo.robot.communication.ProtoCommunicationRobotBoardFirmware.firmwareContent)
}
inline void ProtoCommunicationRobotBoardFirmware::set_firmwarecontent(const void* value, size_t size) {
  set_has_firmwarecontent();
  if (firmwarecontent_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    firmwarecontent_ = new ::std::string;
  }
  firmwarecontent_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:aubo.robot.communication.ProtoCommunicationRobotBoardFirmware.firmwareContent)
}
inline ::std::string* ProtoCommunicationRobotBoardFirmware::mutable_firmwarecontent() {
  set_has_firmwarecontent();
  if (firmwarecontent_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    firmwarecontent_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:aubo.robot.communication.ProtoCommunicationRobotBoardFirmware.firmwareContent)
  return firmwarecontent_;
}
inline ::std::string* ProtoCommunicationRobotBoardFirmware::release_firmwarecontent() {
  clear_has_firmwarecontent();
  if (firmwarecontent_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = firmwarecontent_;
    firmwarecontent_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void ProtoCommunicationRobotBoardFirmware::set_allocated_firmwarecontent(::std::string* firmwarecontent) {
  if (firmwarecontent_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete firmwarecontent_;
  }
  if (firmwarecontent) {
    set_has_firmwarecontent();
    firmwarecontent_ = firmwarecontent;
  } else {
    clear_has_firmwarecontent();
    firmwarecontent_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.communication.ProtoCommunicationRobotBoardFirmware.firmwareContent)
}

// required int32 firmwareContentSize = 3;
inline bool ProtoCommunicationRobotBoardFirmware::has_firmwarecontentsize() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void ProtoCommunicationRobotBoardFirmware::set_has_firmwarecontentsize() {
  _has_bits_[0] |= 0x00000004u;
}
inline void ProtoCommunicationRobotBoardFirmware::clear_has_firmwarecontentsize() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void ProtoCommunicationRobotBoardFirmware::clear_firmwarecontentsize() {
  firmwarecontentsize_ = 0;
  clear_has_firmwarecontentsize();
}
inline ::google::protobuf::int32 ProtoCommunicationRobotBoardFirmware::firmwarecontentsize() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoCommunicationRobotBoardFirmware.firmwareContentSize)
  return firmwarecontentsize_;
}
inline void ProtoCommunicationRobotBoardFirmware::set_firmwarecontentsize(::google::protobuf::int32 value) {
  set_has_firmwarecontentsize();
  firmwarecontentsize_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ProtoCommunicationRobotBoardFirmware.firmwareContentSize)
}

// -------------------------------------------------------------------

// ProtoCommunicationEthernetDeviceNameResponse

// required string name = 1;
inline bool ProtoCommunicationEthernetDeviceNameResponse::has_name() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ProtoCommunicationEthernetDeviceNameResponse::set_has_name() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ProtoCommunicationEthernetDeviceNameResponse::clear_has_name() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ProtoCommunicationEthernetDeviceNameResponse::clear_name() {
  if (name_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    name_->clear();
  }
  clear_has_name();
}
inline const ::std::string& ProtoCommunicationEthernetDeviceNameResponse::name() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoCommunicationEthernetDeviceNameResponse.name)
  return *name_;
}
inline void ProtoCommunicationEthernetDeviceNameResponse::set_name(const ::std::string& value) {
  set_has_name();
  if (name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    name_ = new ::std::string;
  }
  name_->assign(value);
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ProtoCommunicationEthernetDeviceNameResponse.name)
}
inline void ProtoCommunicationEthernetDeviceNameResponse::set_name(const char* value) {
  set_has_name();
  if (name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    name_ = new ::std::string;
  }
  name_->assign(value);
  // @@protoc_insertion_point(field_set_char:aubo.robot.communication.ProtoCommunicationEthernetDeviceNameResponse.name)
}
inline void ProtoCommunicationEthernetDeviceNameResponse::set_name(const char* value, size_t size) {
  set_has_name();
  if (name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    name_ = new ::std::string;
  }
  name_->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:aubo.robot.communication.ProtoCommunicationEthernetDeviceNameResponse.name)
}
inline ::std::string* ProtoCommunicationEthernetDeviceNameResponse::mutable_name() {
  set_has_name();
  if (name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    name_ = new ::std::string;
  }
  // @@protoc_insertion_point(field_mutable:aubo.robot.communication.ProtoCommunicationEthernetDeviceNameResponse.name)
  return name_;
}
inline ::std::string* ProtoCommunicationEthernetDeviceNameResponse::release_name() {
  clear_has_name();
  if (name_ == &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    return NULL;
  } else {
    ::std::string* temp = name_;
    name_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    return temp;
  }
}
inline void ProtoCommunicationEthernetDeviceNameResponse::set_allocated_name(::std::string* name) {
  if (name_ != &::google::protobuf::internal::GetEmptyStringAlreadyInited()) {
    delete name_;
  }
  if (name) {
    set_has_name();
    name_ = name;
  } else {
    clear_has_name();
    name_ = const_cast< ::std::string*>(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.communication.ProtoCommunicationEthernetDeviceNameResponse.name)
}

// required .aubo.robot.communication.ProtoRobotCommonResponse errorInfo = 2;
inline bool ProtoCommunicationEthernetDeviceNameResponse::has_errorinfo() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void ProtoCommunicationEthernetDeviceNameResponse::set_has_errorinfo() {
  _has_bits_[0] |= 0x00000002u;
}
inline void ProtoCommunicationEthernetDeviceNameResponse::clear_has_errorinfo() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void ProtoCommunicationEthernetDeviceNameResponse::clear_errorinfo() {
  if (errorinfo_ != NULL) errorinfo_->::aubo::robot::communication::ProtoRobotCommonResponse::Clear();
  clear_has_errorinfo();
}
inline const ::aubo::robot::communication::ProtoRobotCommonResponse& ProtoCommunicationEthernetDeviceNameResponse::errorinfo() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoCommunicationEthernetDeviceNameResponse.errorInfo)
  return errorinfo_ != NULL ? *errorinfo_ : *default_instance_->errorinfo_;
}
inline ::aubo::robot::communication::ProtoRobotCommonResponse* ProtoCommunicationEthernetDeviceNameResponse::mutable_errorinfo() {
  set_has_errorinfo();
  if (errorinfo_ == NULL) errorinfo_ = new ::aubo::robot::communication::ProtoRobotCommonResponse;
  // @@protoc_insertion_point(field_mutable:aubo.robot.communication.ProtoCommunicationEthernetDeviceNameResponse.errorInfo)
  return errorinfo_;
}
inline ::aubo::robot::communication::ProtoRobotCommonResponse* ProtoCommunicationEthernetDeviceNameResponse::release_errorinfo() {
  clear_has_errorinfo();
  ::aubo::robot::communication::ProtoRobotCommonResponse* temp = errorinfo_;
  errorinfo_ = NULL;
  return temp;
}
inline void ProtoCommunicationEthernetDeviceNameResponse::set_allocated_errorinfo(::aubo::robot::communication::ProtoRobotCommonResponse* errorinfo) {
  delete errorinfo_;
  errorinfo_ = errorinfo;
  if (errorinfo) {
    set_has_errorinfo();
  } else {
    clear_has_errorinfo();
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.communication.ProtoCommunicationEthernetDeviceNameResponse.errorInfo)
}

// -------------------------------------------------------------------

// ProtoCommunicationDoubleVector

// required int32 num = 1;
inline bool ProtoCommunicationDoubleVector::has_num() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ProtoCommunicationDoubleVector::set_has_num() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ProtoCommunicationDoubleVector::clear_has_num() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ProtoCommunicationDoubleVector::clear_num() {
  num_ = 0;
  clear_has_num();
}
inline ::google::protobuf::int32 ProtoCommunicationDoubleVector::num() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoCommunicationDoubleVector.num)
  return num_;
}
inline void ProtoCommunicationDoubleVector::set_num(::google::protobuf::int32 value) {
  set_has_num();
  num_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ProtoCommunicationDoubleVector.num)
}

// repeated double value = 2;
inline int ProtoCommunicationDoubleVector::value_size() const {
  return value_.size();
}
inline void ProtoCommunicationDoubleVector::clear_value() {
  value_.Clear();
}
inline double ProtoCommunicationDoubleVector::value(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoCommunicationDoubleVector.value)
  return value_.Get(index);
}
inline void ProtoCommunicationDoubleVector::set_value(int index, double value) {
  value_.Set(index, value);
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ProtoCommunicationDoubleVector.value)
}
inline void ProtoCommunicationDoubleVector::add_value(double value) {
  value_.Add(value);
  // @@protoc_insertion_point(field_add:aubo.robot.communication.ProtoCommunicationDoubleVector.value)
}
inline const ::google::protobuf::RepeatedField< double >&
ProtoCommunicationDoubleVector::value() const {
  // @@protoc_insertion_point(field_list:aubo.robot.communication.ProtoCommunicationDoubleVector.value)
  return value_;
}
inline ::google::protobuf::RepeatedField< double >*
ProtoCommunicationDoubleVector::mutable_value() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.communication.ProtoCommunicationDoubleVector.value)
  return &value_;
}

// -------------------------------------------------------------------

// ProtoCommunicationIntVector

// required int32 num = 1;
inline bool ProtoCommunicationIntVector::has_num() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ProtoCommunicationIntVector::set_has_num() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ProtoCommunicationIntVector::clear_has_num() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ProtoCommunicationIntVector::clear_num() {
  num_ = 0;
  clear_has_num();
}
inline ::google::protobuf::int32 ProtoCommunicationIntVector::num() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoCommunicationIntVector.num)
  return num_;
}
inline void ProtoCommunicationIntVector::set_num(::google::protobuf::int32 value) {
  set_has_num();
  num_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ProtoCommunicationIntVector.num)
}

// repeated int32 value = 2;
inline int ProtoCommunicationIntVector::value_size() const {
  return value_.size();
}
inline void ProtoCommunicationIntVector::clear_value() {
  value_.Clear();
}
inline ::google::protobuf::int32 ProtoCommunicationIntVector::value(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoCommunicationIntVector.value)
  return value_.Get(index);
}
inline void ProtoCommunicationIntVector::set_value(int index, ::google::protobuf::int32 value) {
  value_.Set(index, value);
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ProtoCommunicationIntVector.value)
}
inline void ProtoCommunicationIntVector::add_value(::google::protobuf::int32 value) {
  value_.Add(value);
  // @@protoc_insertion_point(field_add:aubo.robot.communication.ProtoCommunicationIntVector.value)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
ProtoCommunicationIntVector::value() const {
  // @@protoc_insertion_point(field_list:aubo.robot.communication.ProtoCommunicationIntVector.value)
  return value_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
ProtoCommunicationIntVector::mutable_value() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.communication.ProtoCommunicationIntVector.value)
  return &value_;
}

// -------------------------------------------------------------------

// ProtoCommunicationDoubleVectorResponse

// required int32 num = 1;
inline bool ProtoCommunicationDoubleVectorResponse::has_num() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ProtoCommunicationDoubleVectorResponse::set_has_num() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ProtoCommunicationDoubleVectorResponse::clear_has_num() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ProtoCommunicationDoubleVectorResponse::clear_num() {
  num_ = 0;
  clear_has_num();
}
inline ::google::protobuf::int32 ProtoCommunicationDoubleVectorResponse::num() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoCommunicationDoubleVectorResponse.num)
  return num_;
}
inline void ProtoCommunicationDoubleVectorResponse::set_num(::google::protobuf::int32 value) {
  set_has_num();
  num_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ProtoCommunicationDoubleVectorResponse.num)
}

// repeated double value = 2;
inline int ProtoCommunicationDoubleVectorResponse::value_size() const {
  return value_.size();
}
inline void ProtoCommunicationDoubleVectorResponse::clear_value() {
  value_.Clear();
}
inline double ProtoCommunicationDoubleVectorResponse::value(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoCommunicationDoubleVectorResponse.value)
  return value_.Get(index);
}
inline void ProtoCommunicationDoubleVectorResponse::set_value(int index, double value) {
  value_.Set(index, value);
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ProtoCommunicationDoubleVectorResponse.value)
}
inline void ProtoCommunicationDoubleVectorResponse::add_value(double value) {
  value_.Add(value);
  // @@protoc_insertion_point(field_add:aubo.robot.communication.ProtoCommunicationDoubleVectorResponse.value)
}
inline const ::google::protobuf::RepeatedField< double >&
ProtoCommunicationDoubleVectorResponse::value() const {
  // @@protoc_insertion_point(field_list:aubo.robot.communication.ProtoCommunicationDoubleVectorResponse.value)
  return value_;
}
inline ::google::protobuf::RepeatedField< double >*
ProtoCommunicationDoubleVectorResponse::mutable_value() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.communication.ProtoCommunicationDoubleVectorResponse.value)
  return &value_;
}

// required .aubo.robot.communication.ProtoRobotCommonResponse errorInfo = 3;
inline bool ProtoCommunicationDoubleVectorResponse::has_errorinfo() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void ProtoCommunicationDoubleVectorResponse::set_has_errorinfo() {
  _has_bits_[0] |= 0x00000004u;
}
inline void ProtoCommunicationDoubleVectorResponse::clear_has_errorinfo() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void ProtoCommunicationDoubleVectorResponse::clear_errorinfo() {
  if (errorinfo_ != NULL) errorinfo_->::aubo::robot::communication::ProtoRobotCommonResponse::Clear();
  clear_has_errorinfo();
}
inline const ::aubo::robot::communication::ProtoRobotCommonResponse& ProtoCommunicationDoubleVectorResponse::errorinfo() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoCommunicationDoubleVectorResponse.errorInfo)
  return errorinfo_ != NULL ? *errorinfo_ : *default_instance_->errorinfo_;
}
inline ::aubo::robot::communication::ProtoRobotCommonResponse* ProtoCommunicationDoubleVectorResponse::mutable_errorinfo() {
  set_has_errorinfo();
  if (errorinfo_ == NULL) errorinfo_ = new ::aubo::robot::communication::ProtoRobotCommonResponse;
  // @@protoc_insertion_point(field_mutable:aubo.robot.communication.ProtoCommunicationDoubleVectorResponse.errorInfo)
  return errorinfo_;
}
inline ::aubo::robot::communication::ProtoRobotCommonResponse* ProtoCommunicationDoubleVectorResponse::release_errorinfo() {
  clear_has_errorinfo();
  ::aubo::robot::communication::ProtoRobotCommonResponse* temp = errorinfo_;
  errorinfo_ = NULL;
  return temp;
}
inline void ProtoCommunicationDoubleVectorResponse::set_allocated_errorinfo(::aubo::robot::communication::ProtoRobotCommonResponse* errorinfo) {
  delete errorinfo_;
  errorinfo_ = errorinfo;
  if (errorinfo) {
    set_has_errorinfo();
  } else {
    clear_has_errorinfo();
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.communication.ProtoCommunicationDoubleVectorResponse.errorInfo)
}

// -------------------------------------------------------------------

// ProtoCommunicationIntVectorResponse

// required int32 num = 1;
inline bool ProtoCommunicationIntVectorResponse::has_num() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ProtoCommunicationIntVectorResponse::set_has_num() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ProtoCommunicationIntVectorResponse::clear_has_num() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ProtoCommunicationIntVectorResponse::clear_num() {
  num_ = 0;
  clear_has_num();
}
inline ::google::protobuf::int32 ProtoCommunicationIntVectorResponse::num() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoCommunicationIntVectorResponse.num)
  return num_;
}
inline void ProtoCommunicationIntVectorResponse::set_num(::google::protobuf::int32 value) {
  set_has_num();
  num_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ProtoCommunicationIntVectorResponse.num)
}

// repeated int32 value = 2;
inline int ProtoCommunicationIntVectorResponse::value_size() const {
  return value_.size();
}
inline void ProtoCommunicationIntVectorResponse::clear_value() {
  value_.Clear();
}
inline ::google::protobuf::int32 ProtoCommunicationIntVectorResponse::value(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoCommunicationIntVectorResponse.value)
  return value_.Get(index);
}
inline void ProtoCommunicationIntVectorResponse::set_value(int index, ::google::protobuf::int32 value) {
  value_.Set(index, value);
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ProtoCommunicationIntVectorResponse.value)
}
inline void ProtoCommunicationIntVectorResponse::add_value(::google::protobuf::int32 value) {
  value_.Add(value);
  // @@protoc_insertion_point(field_add:aubo.robot.communication.ProtoCommunicationIntVectorResponse.value)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
ProtoCommunicationIntVectorResponse::value() const {
  // @@protoc_insertion_point(field_list:aubo.robot.communication.ProtoCommunicationIntVectorResponse.value)
  return value_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
ProtoCommunicationIntVectorResponse::mutable_value() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.communication.ProtoCommunicationIntVectorResponse.value)
  return &value_;
}

// required .aubo.robot.communication.ProtoRobotCommonResponse errorInfo = 3;
inline bool ProtoCommunicationIntVectorResponse::has_errorinfo() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void ProtoCommunicationIntVectorResponse::set_has_errorinfo() {
  _has_bits_[0] |= 0x00000004u;
}
inline void ProtoCommunicationIntVectorResponse::clear_has_errorinfo() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void ProtoCommunicationIntVectorResponse::clear_errorinfo() {
  if (errorinfo_ != NULL) errorinfo_->::aubo::robot::communication::ProtoRobotCommonResponse::Clear();
  clear_has_errorinfo();
}
inline const ::aubo::robot::communication::ProtoRobotCommonResponse& ProtoCommunicationIntVectorResponse::errorinfo() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoCommunicationIntVectorResponse.errorInfo)
  return errorinfo_ != NULL ? *errorinfo_ : *default_instance_->errorinfo_;
}
inline ::aubo::robot::communication::ProtoRobotCommonResponse* ProtoCommunicationIntVectorResponse::mutable_errorinfo() {
  set_has_errorinfo();
  if (errorinfo_ == NULL) errorinfo_ = new ::aubo::robot::communication::ProtoRobotCommonResponse;
  // @@protoc_insertion_point(field_mutable:aubo.robot.communication.ProtoCommunicationIntVectorResponse.errorInfo)
  return errorinfo_;
}
inline ::aubo::robot::communication::ProtoRobotCommonResponse* ProtoCommunicationIntVectorResponse::release_errorinfo() {
  clear_has_errorinfo();
  ::aubo::robot::communication::ProtoRobotCommonResponse* temp = errorinfo_;
  errorinfo_ = NULL;
  return temp;
}
inline void ProtoCommunicationIntVectorResponse::set_allocated_errorinfo(::aubo::robot::communication::ProtoRobotCommonResponse* errorinfo) {
  delete errorinfo_;
  errorinfo_ = errorinfo;
  if (errorinfo) {
    set_has_errorinfo();
  } else {
    clear_has_errorinfo();
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.communication.ProtoCommunicationIntVectorResponse.errorInfo)
}

// -------------------------------------------------------------------

// ProtoJointCommonData

// required int32 JointCurVol = 1;
inline bool ProtoJointCommonData::has_jointcurvol() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ProtoJointCommonData::set_has_jointcurvol() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ProtoJointCommonData::clear_has_jointcurvol() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ProtoJointCommonData::clear_jointcurvol() {
  jointcurvol_ = 0;
  clear_has_jointcurvol();
}
inline ::google::protobuf::int32 ProtoJointCommonData::jointcurvol() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoJointCommonData.JointCurVol)
  return jointcurvol_;
}
inline void ProtoJointCommonData::set_jointcurvol(::google::protobuf::int32 value) {
  set_has_jointcurvol();
  jointcurvol_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ProtoJointCommonData.JointCurVol)
}

// required int32 JointCurTemp = 2;
inline bool ProtoJointCommonData::has_jointcurtemp() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void ProtoJointCommonData::set_has_jointcurtemp() {
  _has_bits_[0] |= 0x00000002u;
}
inline void ProtoJointCommonData::clear_has_jointcurtemp() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void ProtoJointCommonData::clear_jointcurtemp() {
  jointcurtemp_ = 0;
  clear_has_jointcurtemp();
}
inline ::google::protobuf::int32 ProtoJointCommonData::jointcurtemp() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoJointCommonData.JointCurTemp)
  return jointcurtemp_;
}
inline void ProtoJointCommonData::set_jointcurtemp(::google::protobuf::int32 value) {
  set_has_jointcurtemp();
  jointcurtemp_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ProtoJointCommonData.JointCurTemp)
}

// required int32 JointWorkMode = 3;
inline bool ProtoJointCommonData::has_jointworkmode() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void ProtoJointCommonData::set_has_jointworkmode() {
  _has_bits_[0] |= 0x00000004u;
}
inline void ProtoJointCommonData::clear_has_jointworkmode() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void ProtoJointCommonData::clear_jointworkmode() {
  jointworkmode_ = 0;
  clear_has_jointworkmode();
}
inline ::google::protobuf::int32 ProtoJointCommonData::jointworkmode() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoJointCommonData.JointWorkMode)
  return jointworkmode_;
}
inline void ProtoJointCommonData::set_jointworkmode(::google::protobuf::int32 value) {
  set_has_jointworkmode();
  jointworkmode_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ProtoJointCommonData.JointWorkMode)
}

// required int32 JointDriEnable = 4;
inline bool ProtoJointCommonData::has_jointdrienable() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void ProtoJointCommonData::set_has_jointdrienable() {
  _has_bits_[0] |= 0x00000008u;
}
inline void ProtoJointCommonData::clear_has_jointdrienable() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void ProtoJointCommonData::clear_jointdrienable() {
  jointdrienable_ = 0;
  clear_has_jointdrienable();
}
inline ::google::protobuf::int32 ProtoJointCommonData::jointdrienable() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoJointCommonData.JointDriEnable)
  return jointdrienable_;
}
inline void ProtoJointCommonData::set_jointdrienable(::google::protobuf::int32 value) {
  set_has_jointdrienable();
  jointdrienable_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ProtoJointCommonData.JointDriEnable)
}

// required int32 JointOpenPwm = 5;
inline bool ProtoJointCommonData::has_jointopenpwm() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void ProtoJointCommonData::set_has_jointopenpwm() {
  _has_bits_[0] |= 0x00000010u;
}
inline void ProtoJointCommonData::clear_has_jointopenpwm() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void ProtoJointCommonData::clear_jointopenpwm() {
  jointopenpwm_ = 0;
  clear_has_jointopenpwm();
}
inline ::google::protobuf::int32 ProtoJointCommonData::jointopenpwm() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoJointCommonData.JointOpenPwm)
  return jointopenpwm_;
}
inline void ProtoJointCommonData::set_jointopenpwm(::google::protobuf::int32 value) {
  set_has_jointopenpwm();
  jointopenpwm_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ProtoJointCommonData.JointOpenPwm)
}

// required int32 JointTagCurrent = 6;
inline bool ProtoJointCommonData::has_jointtagcurrent() const {
  return (_has_bits_[0] & 0x00000020u) != 0;
}
inline void ProtoJointCommonData::set_has_jointtagcurrent() {
  _has_bits_[0] |= 0x00000020u;
}
inline void ProtoJointCommonData::clear_has_jointtagcurrent() {
  _has_bits_[0] &= ~0x00000020u;
}
inline void ProtoJointCommonData::clear_jointtagcurrent() {
  jointtagcurrent_ = 0;
  clear_has_jointtagcurrent();
}
inline ::google::protobuf::int32 ProtoJointCommonData::jointtagcurrent() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoJointCommonData.JointTagCurrent)
  return jointtagcurrent_;
}
inline void ProtoJointCommonData::set_jointtagcurrent(::google::protobuf::int32 value) {
  set_has_jointtagcurrent();
  jointtagcurrent_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ProtoJointCommonData.JointTagCurrent)
}

// required int32 JointTagSpeed = 7;
inline bool ProtoJointCommonData::has_jointtagspeed() const {
  return (_has_bits_[0] & 0x00000040u) != 0;
}
inline void ProtoJointCommonData::set_has_jointtagspeed() {
  _has_bits_[0] |= 0x00000040u;
}
inline void ProtoJointCommonData::clear_has_jointtagspeed() {
  _has_bits_[0] &= ~0x00000040u;
}
inline void ProtoJointCommonData::clear_jointtagspeed() {
  jointtagspeed_ = 0;
  clear_has_jointtagspeed();
}
inline ::google::protobuf::int32 ProtoJointCommonData::jointtagspeed() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoJointCommonData.JointTagSpeed)
  return jointtagspeed_;
}
inline void ProtoJointCommonData::set_jointtagspeed(::google::protobuf::int32 value) {
  set_has_jointtagspeed();
  jointtagspeed_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ProtoJointCommonData.JointTagSpeed)
}

// required int32 JointTagPos = 8;
inline bool ProtoJointCommonData::has_jointtagpos() const {
  return (_has_bits_[0] & 0x00000080u) != 0;
}
inline void ProtoJointCommonData::set_has_jointtagpos() {
  _has_bits_[0] |= 0x00000080u;
}
inline void ProtoJointCommonData::clear_has_jointtagpos() {
  _has_bits_[0] &= ~0x00000080u;
}
inline void ProtoJointCommonData::clear_jointtagpos() {
  jointtagpos_ = 0;
  clear_has_jointtagpos();
}
inline ::google::protobuf::int32 ProtoJointCommonData::jointtagpos() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoJointCommonData.JointTagPos)
  return jointtagpos_;
}
inline void ProtoJointCommonData::set_jointtagpos(::google::protobuf::int32 value) {
  set_has_jointtagpos();
  jointtagpos_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ProtoJointCommonData.JointTagPos)
}

// required int32 JointMaxCur = 9;
inline bool ProtoJointCommonData::has_jointmaxcur() const {
  return (_has_bits_[0] & 0x00000100u) != 0;
}
inline void ProtoJointCommonData::set_has_jointmaxcur() {
  _has_bits_[0] |= 0x00000100u;
}
inline void ProtoJointCommonData::clear_has_jointmaxcur() {
  _has_bits_[0] &= ~0x00000100u;
}
inline void ProtoJointCommonData::clear_jointmaxcur() {
  jointmaxcur_ = 0;
  clear_has_jointmaxcur();
}
inline ::google::protobuf::int32 ProtoJointCommonData::jointmaxcur() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoJointCommonData.JointMaxCur)
  return jointmaxcur_;
}
inline void ProtoJointCommonData::set_jointmaxcur(::google::protobuf::int32 value) {
  set_has_jointmaxcur();
  jointmaxcur_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ProtoJointCommonData.JointMaxCur)
}

// required int32 JointMaxSpeed = 10;
inline bool ProtoJointCommonData::has_jointmaxspeed() const {
  return (_has_bits_[0] & 0x00000200u) != 0;
}
inline void ProtoJointCommonData::set_has_jointmaxspeed() {
  _has_bits_[0] |= 0x00000200u;
}
inline void ProtoJointCommonData::clear_has_jointmaxspeed() {
  _has_bits_[0] &= ~0x00000200u;
}
inline void ProtoJointCommonData::clear_jointmaxspeed() {
  jointmaxspeed_ = 0;
  clear_has_jointmaxspeed();
}
inline ::google::protobuf::int32 ProtoJointCommonData::jointmaxspeed() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoJointCommonData.JointMaxSpeed)
  return jointmaxspeed_;
}
inline void ProtoJointCommonData::set_jointmaxspeed(::google::protobuf::int32 value) {
  set_has_jointmaxspeed();
  jointmaxspeed_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ProtoJointCommonData.JointMaxSpeed)
}

// required int32 JointMaxAcc = 11;
inline bool ProtoJointCommonData::has_jointmaxacc() const {
  return (_has_bits_[0] & 0x00000400u) != 0;
}
inline void ProtoJointCommonData::set_has_jointmaxacc() {
  _has_bits_[0] |= 0x00000400u;
}
inline void ProtoJointCommonData::clear_has_jointmaxacc() {
  _has_bits_[0] &= ~0x00000400u;
}
inline void ProtoJointCommonData::clear_jointmaxacc() {
  jointmaxacc_ = 0;
  clear_has_jointmaxacc();
}
inline ::google::protobuf::int32 ProtoJointCommonData::jointmaxacc() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoJointCommonData.JointMaxAcc)
  return jointmaxacc_;
}
inline void ProtoJointCommonData::set_jointmaxacc(::google::protobuf::int32 value) {
  set_has_jointmaxacc();
  jointmaxacc_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ProtoJointCommonData.JointMaxAcc)
}

// required int32 JointMINPos = 12;
inline bool ProtoJointCommonData::has_jointminpos() const {
  return (_has_bits_[0] & 0x00000800u) != 0;
}
inline void ProtoJointCommonData::set_has_jointminpos() {
  _has_bits_[0] |= 0x00000800u;
}
inline void ProtoJointCommonData::clear_has_jointminpos() {
  _has_bits_[0] &= ~0x00000800u;
}
inline void ProtoJointCommonData::clear_jointminpos() {
  jointminpos_ = 0;
  clear_has_jointminpos();
}
inline ::google::protobuf::int32 ProtoJointCommonData::jointminpos() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoJointCommonData.JointMINPos)
  return jointminpos_;
}
inline void ProtoJointCommonData::set_jointminpos(::google::protobuf::int32 value) {
  set_has_jointminpos();
  jointminpos_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ProtoJointCommonData.JointMINPos)
}

// required int32 JointMAXPos = 13;
inline bool ProtoJointCommonData::has_jointmaxpos() const {
  return (_has_bits_[0] & 0x00001000u) != 0;
}
inline void ProtoJointCommonData::set_has_jointmaxpos() {
  _has_bits_[0] |= 0x00001000u;
}
inline void ProtoJointCommonData::clear_has_jointmaxpos() {
  _has_bits_[0] &= ~0x00001000u;
}
inline void ProtoJointCommonData::clear_jointmaxpos() {
  jointmaxpos_ = 0;
  clear_has_jointmaxpos();
}
inline ::google::protobuf::int32 ProtoJointCommonData::jointmaxpos() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoJointCommonData.JointMAXPos)
  return jointmaxpos_;
}
inline void ProtoJointCommonData::set_jointmaxpos(::google::protobuf::int32 value) {
  set_has_jointmaxpos();
  jointmaxpos_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ProtoJointCommonData.JointMAXPos)
}

// required int32 JointSEVLock = 14;
inline bool ProtoJointCommonData::has_jointsevlock() const {
  return (_has_bits_[0] & 0x00002000u) != 0;
}
inline void ProtoJointCommonData::set_has_jointsevlock() {
  _has_bits_[0] |= 0x00002000u;
}
inline void ProtoJointCommonData::clear_has_jointsevlock() {
  _has_bits_[0] &= ~0x00002000u;
}
inline void ProtoJointCommonData::clear_jointsevlock() {
  jointsevlock_ = 0;
  clear_has_jointsevlock();
}
inline ::google::protobuf::int32 ProtoJointCommonData::jointsevlock() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoJointCommonData.JointSEVLock)
  return jointsevlock_;
}
inline void ProtoJointCommonData::set_jointsevlock(::google::protobuf::int32 value) {
  set_has_jointsevlock();
  jointsevlock_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ProtoJointCommonData.JointSEVLock)
}

// required int32 JointCurP = 15;
inline bool ProtoJointCommonData::has_jointcurp() const {
  return (_has_bits_[0] & 0x00004000u) != 0;
}
inline void ProtoJointCommonData::set_has_jointcurp() {
  _has_bits_[0] |= 0x00004000u;
}
inline void ProtoJointCommonData::clear_has_jointcurp() {
  _has_bits_[0] &= ~0x00004000u;
}
inline void ProtoJointCommonData::clear_jointcurp() {
  jointcurp_ = 0;
  clear_has_jointcurp();
}
inline ::google::protobuf::int32 ProtoJointCommonData::jointcurp() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoJointCommonData.JointCurP)
  return jointcurp_;
}
inline void ProtoJointCommonData::set_jointcurp(::google::protobuf::int32 value) {
  set_has_jointcurp();
  jointcurp_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ProtoJointCommonData.JointCurP)
}

// required int32 JointCurI = 16;
inline bool ProtoJointCommonData::has_jointcuri() const {
  return (_has_bits_[0] & 0x00008000u) != 0;
}
inline void ProtoJointCommonData::set_has_jointcuri() {
  _has_bits_[0] |= 0x00008000u;
}
inline void ProtoJointCommonData::clear_has_jointcuri() {
  _has_bits_[0] &= ~0x00008000u;
}
inline void ProtoJointCommonData::clear_jointcuri() {
  jointcuri_ = 0;
  clear_has_jointcuri();
}
inline ::google::protobuf::int32 ProtoJointCommonData::jointcuri() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoJointCommonData.JointCurI)
  return jointcuri_;
}
inline void ProtoJointCommonData::set_jointcuri(::google::protobuf::int32 value) {
  set_has_jointcuri();
  jointcuri_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ProtoJointCommonData.JointCurI)
}

// required int32 JointCurD = 17;
inline bool ProtoJointCommonData::has_jointcurd() const {
  return (_has_bits_[0] & 0x00010000u) != 0;
}
inline void ProtoJointCommonData::set_has_jointcurd() {
  _has_bits_[0] |= 0x00010000u;
}
inline void ProtoJointCommonData::clear_has_jointcurd() {
  _has_bits_[0] &= ~0x00010000u;
}
inline void ProtoJointCommonData::clear_jointcurd() {
  jointcurd_ = 0;
  clear_has_jointcurd();
}
inline ::google::protobuf::int32 ProtoJointCommonData::jointcurd() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoJointCommonData.JointCurD)
  return jointcurd_;
}
inline void ProtoJointCommonData::set_jointcurd(::google::protobuf::int32 value) {
  set_has_jointcurd();
  jointcurd_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ProtoJointCommonData.JointCurD)
}

// required int32 JointSpeedP = 18;
inline bool ProtoJointCommonData::has_jointspeedp() const {
  return (_has_bits_[0] & 0x00020000u) != 0;
}
inline void ProtoJointCommonData::set_has_jointspeedp() {
  _has_bits_[0] |= 0x00020000u;
}
inline void ProtoJointCommonData::clear_has_jointspeedp() {
  _has_bits_[0] &= ~0x00020000u;
}
inline void ProtoJointCommonData::clear_jointspeedp() {
  jointspeedp_ = 0;
  clear_has_jointspeedp();
}
inline ::google::protobuf::int32 ProtoJointCommonData::jointspeedp() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoJointCommonData.JointSpeedP)
  return jointspeedp_;
}
inline void ProtoJointCommonData::set_jointspeedp(::google::protobuf::int32 value) {
  set_has_jointspeedp();
  jointspeedp_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ProtoJointCommonData.JointSpeedP)
}

// required int32 JointSpeedI = 19;
inline bool ProtoJointCommonData::has_jointspeedi() const {
  return (_has_bits_[0] & 0x00040000u) != 0;
}
inline void ProtoJointCommonData::set_has_jointspeedi() {
  _has_bits_[0] |= 0x00040000u;
}
inline void ProtoJointCommonData::clear_has_jointspeedi() {
  _has_bits_[0] &= ~0x00040000u;
}
inline void ProtoJointCommonData::clear_jointspeedi() {
  jointspeedi_ = 0;
  clear_has_jointspeedi();
}
inline ::google::protobuf::int32 ProtoJointCommonData::jointspeedi() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoJointCommonData.JointSpeedI)
  return jointspeedi_;
}
inline void ProtoJointCommonData::set_jointspeedi(::google::protobuf::int32 value) {
  set_has_jointspeedi();
  jointspeedi_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ProtoJointCommonData.JointSpeedI)
}

// required int32 JointSpeedD = 20;
inline bool ProtoJointCommonData::has_jointspeedd() const {
  return (_has_bits_[0] & 0x00080000u) != 0;
}
inline void ProtoJointCommonData::set_has_jointspeedd() {
  _has_bits_[0] |= 0x00080000u;
}
inline void ProtoJointCommonData::clear_has_jointspeedd() {
  _has_bits_[0] &= ~0x00080000u;
}
inline void ProtoJointCommonData::clear_jointspeedd() {
  jointspeedd_ = 0;
  clear_has_jointspeedd();
}
inline ::google::protobuf::int32 ProtoJointCommonData::jointspeedd() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoJointCommonData.JointSpeedD)
  return jointspeedd_;
}
inline void ProtoJointCommonData::set_jointspeedd(::google::protobuf::int32 value) {
  set_has_jointspeedd();
  jointspeedd_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ProtoJointCommonData.JointSpeedD)
}

// required int32 JointSpeedDS = 21;
inline bool ProtoJointCommonData::has_jointspeedds() const {
  return (_has_bits_[0] & 0x00100000u) != 0;
}
inline void ProtoJointCommonData::set_has_jointspeedds() {
  _has_bits_[0] |= 0x00100000u;
}
inline void ProtoJointCommonData::clear_has_jointspeedds() {
  _has_bits_[0] &= ~0x00100000u;
}
inline void ProtoJointCommonData::clear_jointspeedds() {
  jointspeedds_ = 0;
  clear_has_jointspeedds();
}
inline ::google::protobuf::int32 ProtoJointCommonData::jointspeedds() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoJointCommonData.JointSpeedDS)
  return jointspeedds_;
}
inline void ProtoJointCommonData::set_jointspeedds(::google::protobuf::int32 value) {
  set_has_jointspeedds();
  jointspeedds_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ProtoJointCommonData.JointSpeedDS)
}

// required int32 JointPosP = 22;
inline bool ProtoJointCommonData::has_jointposp() const {
  return (_has_bits_[0] & 0x00200000u) != 0;
}
inline void ProtoJointCommonData::set_has_jointposp() {
  _has_bits_[0] |= 0x00200000u;
}
inline void ProtoJointCommonData::clear_has_jointposp() {
  _has_bits_[0] &= ~0x00200000u;
}
inline void ProtoJointCommonData::clear_jointposp() {
  jointposp_ = 0;
  clear_has_jointposp();
}
inline ::google::protobuf::int32 ProtoJointCommonData::jointposp() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoJointCommonData.JointPosP)
  return jointposp_;
}
inline void ProtoJointCommonData::set_jointposp(::google::protobuf::int32 value) {
  set_has_jointposp();
  jointposp_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ProtoJointCommonData.JointPosP)
}

// required int32 JointPosI = 23;
inline bool ProtoJointCommonData::has_jointposi() const {
  return (_has_bits_[0] & 0x00400000u) != 0;
}
inline void ProtoJointCommonData::set_has_jointposi() {
  _has_bits_[0] |= 0x00400000u;
}
inline void ProtoJointCommonData::clear_has_jointposi() {
  _has_bits_[0] &= ~0x00400000u;
}
inline void ProtoJointCommonData::clear_jointposi() {
  jointposi_ = 0;
  clear_has_jointposi();
}
inline ::google::protobuf::int32 ProtoJointCommonData::jointposi() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoJointCommonData.JointPosI)
  return jointposi_;
}
inline void ProtoJointCommonData::set_jointposi(::google::protobuf::int32 value) {
  set_has_jointposi();
  jointposi_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ProtoJointCommonData.JointPosI)
}

// required int32 JointPosD = 24;
inline bool ProtoJointCommonData::has_jointposd() const {
  return (_has_bits_[0] & 0x00800000u) != 0;
}
inline void ProtoJointCommonData::set_has_jointposd() {
  _has_bits_[0] |= 0x00800000u;
}
inline void ProtoJointCommonData::clear_has_jointposd() {
  _has_bits_[0] &= ~0x00800000u;
}
inline void ProtoJointCommonData::clear_jointposd() {
  jointposd_ = 0;
  clear_has_jointposd();
}
inline ::google::protobuf::int32 ProtoJointCommonData::jointposd() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoJointCommonData.JointPosD)
  return jointposd_;
}
inline void ProtoJointCommonData::set_jointposd(::google::protobuf::int32 value) {
  set_has_jointposd();
  jointposd_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ProtoJointCommonData.JointPosD)
}

// required int32 JointPosDS = 25;
inline bool ProtoJointCommonData::has_jointposds() const {
  return (_has_bits_[0] & 0x01000000u) != 0;
}
inline void ProtoJointCommonData::set_has_jointposds() {
  _has_bits_[0] |= 0x01000000u;
}
inline void ProtoJointCommonData::clear_has_jointposds() {
  _has_bits_[0] &= ~0x01000000u;
}
inline void ProtoJointCommonData::clear_jointposds() {
  jointposds_ = 0;
  clear_has_jointposds();
}
inline ::google::protobuf::int32 ProtoJointCommonData::jointposds() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoJointCommonData.JointPosDS)
  return jointposds_;
}
inline void ProtoJointCommonData::set_jointposds(::google::protobuf::int32 value) {
  set_has_jointposds();
  jointposds_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.communication.ProtoJointCommonData.JointPosDS)
}

// -------------------------------------------------------------------

// ProtoJointCommonDataResponse

// repeated .aubo.robot.communication.ProtoJointCommonData JointCommonData = 1;
inline int ProtoJointCommonDataResponse::jointcommondata_size() const {
  return jointcommondata_.size();
}
inline void ProtoJointCommonDataResponse::clear_jointcommondata() {
  jointcommondata_.Clear();
}
inline const ::aubo::robot::communication::ProtoJointCommonData& ProtoJointCommonDataResponse::jointcommondata(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoJointCommonDataResponse.JointCommonData)
  return jointcommondata_.Get(index);
}
inline ::aubo::robot::communication::ProtoJointCommonData* ProtoJointCommonDataResponse::mutable_jointcommondata(int index) {
  // @@protoc_insertion_point(field_mutable:aubo.robot.communication.ProtoJointCommonDataResponse.JointCommonData)
  return jointcommondata_.Mutable(index);
}
inline ::aubo::robot::communication::ProtoJointCommonData* ProtoJointCommonDataResponse::add_jointcommondata() {
  // @@protoc_insertion_point(field_add:aubo.robot.communication.ProtoJointCommonDataResponse.JointCommonData)
  return jointcommondata_.Add();
}
inline const ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::ProtoJointCommonData >&
ProtoJointCommonDataResponse::jointcommondata() const {
  // @@protoc_insertion_point(field_list:aubo.robot.communication.ProtoJointCommonDataResponse.JointCommonData)
  return jointcommondata_;
}
inline ::google::protobuf::RepeatedPtrField< ::aubo::robot::communication::ProtoJointCommonData >*
ProtoJointCommonDataResponse::mutable_jointcommondata() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.communication.ProtoJointCommonDataResponse.JointCommonData)
  return &jointcommondata_;
}

// required .aubo.robot.communication.ProtoRobotCommonResponse errorInfo = 2;
inline bool ProtoJointCommonDataResponse::has_errorinfo() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void ProtoJointCommonDataResponse::set_has_errorinfo() {
  _has_bits_[0] |= 0x00000002u;
}
inline void ProtoJointCommonDataResponse::clear_has_errorinfo() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void ProtoJointCommonDataResponse::clear_errorinfo() {
  if (errorinfo_ != NULL) errorinfo_->::aubo::robot::communication::ProtoRobotCommonResponse::Clear();
  clear_has_errorinfo();
}
inline const ::aubo::robot::communication::ProtoRobotCommonResponse& ProtoJointCommonDataResponse::errorinfo() const {
  // @@protoc_insertion_point(field_get:aubo.robot.communication.ProtoJointCommonDataResponse.errorInfo)
  return errorinfo_ != NULL ? *errorinfo_ : *default_instance_->errorinfo_;
}
inline ::aubo::robot::communication::ProtoRobotCommonResponse* ProtoJointCommonDataResponse::mutable_errorinfo() {
  set_has_errorinfo();
  if (errorinfo_ == NULL) errorinfo_ = new ::aubo::robot::communication::ProtoRobotCommonResponse;
  // @@protoc_insertion_point(field_mutable:aubo.robot.communication.ProtoJointCommonDataResponse.errorInfo)
  return errorinfo_;
}
inline ::aubo::robot::communication::ProtoRobotCommonResponse* ProtoJointCommonDataResponse::release_errorinfo() {
  clear_has_errorinfo();
  ::aubo::robot::communication::ProtoRobotCommonResponse* temp = errorinfo_;
  errorinfo_ = NULL;
  return temp;
}
inline void ProtoJointCommonDataResponse::set_allocated_errorinfo(::aubo::robot::communication::ProtoRobotCommonResponse* errorinfo) {
  delete errorinfo_;
  errorinfo_ = errorinfo;
  if (errorinfo) {
    set_has_errorinfo();
  } else {
    clear_has_errorinfo();
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.communication.ProtoJointCommonDataResponse.errorInfo)
}


// @@protoc_insertion_point(namespace_scope)

}  // namespace communication
}  // namespace robot
}  // namespace aubo

#ifndef SWIG
namespace google {
namespace protobuf {

template <> struct is_proto_enum< ::aubo::robot::communication::ModbusMode> : ::google::protobuf::internal::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::aubo::robot::communication::ModbusMode>() {
  return ::aubo::robot::communication::ModbusMode_descriptor();
}
template <> struct is_proto_enum< ::aubo::robot::communication::TagIoType> : ::google::protobuf::internal::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::aubo::robot::communication::TagIoType>() {
  return ::aubo::robot::communication::TagIoType_descriptor();
}

}  // namespace google
}  // namespace protobuf
#endif  // SWIG

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_robotcommunication_2eproto__INCLUDED
