// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: robotParameters.proto

#ifndef PROTOBUF_robotParameters_2eproto__INCLUDED
#define PROTOBUF_robotParameters_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 2006000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 2006001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
#include "robotcommunication.pb.h"
// @@protoc_insertion_point(includes)

namespace aubo {
namespace robot {
namespace paramerter {

// Internal implementation detail -- do not call these.
void  protobuf_AddDesc_robotParameters_2eproto();
void protobuf_AssignDesc_robotParameters_2eproto();
void protobuf_ShutdownFile_robotParameters_2eproto();

class ProtoRobotInfo;
class ProtoRobotDynamicsParameter;
class ProtoRobotHandguidingParameter;
class ProtoRobotKinematicsParameter;
class ProtoRobotKinematicsParameter_legacy;
class ProtoRobotFrictionParameter;
class ProtoRobotBaseParameter;
class ProtoRobotBaseParameter_legacy;
class ProtoRobotBaseParameterResponse;
class ProtoRobotBaseParameterResponse_legacy;
class ProtoRobotJointsParameter;
class ProtoRobotJointsParameterResponse;

// ===================================================================

class ProtoRobotInfo : public ::google::protobuf::Message {
 public:
  ProtoRobotInfo();
  virtual ~ProtoRobotInfo();

  ProtoRobotInfo(const ProtoRobotInfo& from);

  inline ProtoRobotInfo& operator=(const ProtoRobotInfo& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ProtoRobotInfo& default_instance();

  void Swap(ProtoRobotInfo* other);

  // implements Message ----------------------------------------------

  ProtoRobotInfo* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ProtoRobotInfo& from);
  void MergeFrom(const ProtoRobotInfo& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required uint32 robot_type = 1;
  inline bool has_robot_type() const;
  inline void clear_robot_type();
  static const int kRobotTypeFieldNumber = 1;
  inline ::google::protobuf::uint32 robot_type() const;
  inline void set_robot_type(::google::protobuf::uint32 value);

  // required uint32 auth_type = 2;
  inline bool has_auth_type() const;
  inline void clear_auth_type();
  static const int kAuthTypeFieldNumber = 2;
  inline ::google::protobuf::uint32 auth_type() const;
  inline void set_auth_type(::google::protobuf::uint32 value);

  // required uint32 robot_expire = 3;
  inline bool has_robot_expire() const;
  inline void clear_robot_expire();
  static const int kRobotExpireFieldNumber = 3;
  inline ::google::protobuf::uint32 robot_expire() const;
  inline void set_robot_expire(::google::protobuf::uint32 value);

  // repeated uint32 reserve = 4;
  inline int reserve_size() const;
  inline void clear_reserve();
  static const int kReserveFieldNumber = 4;
  inline ::google::protobuf::uint32 reserve(int index) const;
  inline void set_reserve(int index, ::google::protobuf::uint32 value);
  inline void add_reserve(::google::protobuf::uint32 value);
  inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
      reserve() const;
  inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
      mutable_reserve();

  // required uint32 robot_duration = 5;
  inline bool has_robot_duration() const;
  inline void clear_robot_duration();
  static const int kRobotDurationFieldNumber = 5;
  inline ::google::protobuf::uint32 robot_duration() const;
  inline void set_robot_duration(::google::protobuf::uint32 value);

  // repeated uint32 joint_duration = 6;
  inline int joint_duration_size() const;
  inline void clear_joint_duration();
  static const int kJointDurationFieldNumber = 6;
  inline ::google::protobuf::uint32 joint_duration(int index) const;
  inline void set_joint_duration(int index, ::google::protobuf::uint32 value);
  inline void add_joint_duration(::google::protobuf::uint32 value);
  inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
      joint_duration() const;
  inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
      mutable_joint_duration();

  // @@protoc_insertion_point(class_scope:aubo.robot.paramerter.ProtoRobotInfo)
 private:
  inline void set_has_robot_type();
  inline void clear_has_robot_type();
  inline void set_has_auth_type();
  inline void clear_has_auth_type();
  inline void set_has_robot_expire();
  inline void clear_has_robot_expire();
  inline void set_has_robot_duration();
  inline void clear_has_robot_duration();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::uint32 robot_type_;
  ::google::protobuf::uint32 auth_type_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 > reserve_;
  ::google::protobuf::uint32 robot_expire_;
  ::google::protobuf::uint32 robot_duration_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 > joint_duration_;
  friend void  protobuf_AddDesc_robotParameters_2eproto();
  friend void protobuf_AssignDesc_robotParameters_2eproto();
  friend void protobuf_ShutdownFile_robotParameters_2eproto();

  void InitAsDefaultInstance();
  static ProtoRobotInfo* default_instance_;
};
// -------------------------------------------------------------------

class ProtoRobotDynamicsParameter : public ::google::protobuf::Message {
 public:
  ProtoRobotDynamicsParameter();
  virtual ~ProtoRobotDynamicsParameter();

  ProtoRobotDynamicsParameter(const ProtoRobotDynamicsParameter& from);

  inline ProtoRobotDynamicsParameter& operator=(const ProtoRobotDynamicsParameter& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ProtoRobotDynamicsParameter& default_instance();

  void Swap(ProtoRobotDynamicsParameter* other);

  // implements Message ----------------------------------------------

  ProtoRobotDynamicsParameter* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ProtoRobotDynamicsParameter& from);
  void MergeFrom(const ProtoRobotDynamicsParameter& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated double K = 1;
  inline int k_size() const;
  inline void clear_k();
  static const int kKFieldNumber = 1;
  inline double k(int index) const;
  inline void set_k(int index, double value);
  inline void add_k(double value);
  inline const ::google::protobuf::RepeatedField< double >&
      k() const;
  inline ::google::protobuf::RepeatedField< double >*
      mutable_k();

  // repeated double IA = 2;
  inline int ia_size() const;
  inline void clear_ia();
  static const int kIAFieldNumber = 2;
  inline double ia(int index) const;
  inline void set_ia(int index, double value);
  inline void add_ia(double value);
  inline const ::google::protobuf::RepeatedField< double >&
      ia() const;
  inline ::google::protobuf::RepeatedField< double >*
      mutable_ia();

  // repeated double M = 3;
  inline int m_size() const;
  inline void clear_m();
  static const int kMFieldNumber = 3;
  inline double m(int index) const;
  inline void set_m(int index, double value);
  inline void add_m(double value);
  inline const ::google::protobuf::RepeatedField< double >&
      m() const;
  inline ::google::protobuf::RepeatedField< double >*
      mutable_m();

  // repeated double MXYZ = 4;
  inline int mxyz_size() const;
  inline void clear_mxyz();
  static const int kMXYZFieldNumber = 4;
  inline double mxyz(int index) const;
  inline void set_mxyz(int index, double value);
  inline void add_mxyz(double value);
  inline const ::google::protobuf::RepeatedField< double >&
      mxyz() const;
  inline ::google::protobuf::RepeatedField< double >*
      mutable_mxyz();

  // repeated double IXYZ = 5;
  inline int ixyz_size() const;
  inline void clear_ixyz();
  static const int kIXYZFieldNumber = 5;
  inline double ixyz(int index) const;
  inline void set_ixyz(int index, double value);
  inline void add_ixyz(double value);
  inline const ::google::protobuf::RepeatedField< double >&
      ixyz() const;
  inline ::google::protobuf::RepeatedField< double >*
      mutable_ixyz();

  // repeated double CB = 6;
  inline int cb_size() const;
  inline void clear_cb();
  static const int kCBFieldNumber = 6;
  inline double cb(int index) const;
  inline void set_cb(int index, double value);
  inline void add_cb(double value);
  inline const ::google::protobuf::RepeatedField< double >&
      cb() const;
  inline ::google::protobuf::RepeatedField< double >*
      mutable_cb();

  // @@protoc_insertion_point(class_scope:aubo.robot.paramerter.ProtoRobotDynamicsParameter)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::RepeatedField< double > k_;
  ::google::protobuf::RepeatedField< double > ia_;
  ::google::protobuf::RepeatedField< double > m_;
  ::google::protobuf::RepeatedField< double > mxyz_;
  ::google::protobuf::RepeatedField< double > ixyz_;
  ::google::protobuf::RepeatedField< double > cb_;
  friend void  protobuf_AddDesc_robotParameters_2eproto();
  friend void protobuf_AssignDesc_robotParameters_2eproto();
  friend void protobuf_ShutdownFile_robotParameters_2eproto();

  void InitAsDefaultInstance();
  static ProtoRobotDynamicsParameter* default_instance_;
};
// -------------------------------------------------------------------

class ProtoRobotHandguidingParameter : public ::google::protobuf::Message {
 public:
  ProtoRobotHandguidingParameter();
  virtual ~ProtoRobotHandguidingParameter();

  ProtoRobotHandguidingParameter(const ProtoRobotHandguidingParameter& from);

  inline ProtoRobotHandguidingParameter& operator=(const ProtoRobotHandguidingParameter& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ProtoRobotHandguidingParameter& default_instance();

  void Swap(ProtoRobotHandguidingParameter* other);

  // implements Message ----------------------------------------------

  ProtoRobotHandguidingParameter* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ProtoRobotHandguidingParameter& from);
  void MergeFrom(const ProtoRobotHandguidingParameter& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated double FP = 1;
  inline int fp_size() const;
  inline void clear_fp();
  static const int kFPFieldNumber = 1;
  inline double fp(int index) const;
  inline void set_fp(int index, double value);
  inline void add_fp(double value);
  inline const ::google::protobuf::RepeatedField< double >&
      fp() const;
  inline ::google::protobuf::RepeatedField< double >*
      mutable_fp();

  // repeated double FD = 2;
  inline int fd_size() const;
  inline void clear_fd();
  static const int kFDFieldNumber = 2;
  inline double fd(int index) const;
  inline void set_fd(int index, double value);
  inline void add_fd(double value);
  inline const ::google::protobuf::RepeatedField< double >&
      fd() const;
  inline ::google::protobuf::RepeatedField< double >*
      mutable_fd();

  // repeated double FK = 3;
  inline int fk_size() const;
  inline void clear_fk();
  static const int kFKFieldNumber = 3;
  inline double fk(int index) const;
  inline void set_fk(int index, double value);
  inline void add_fk(double value);
  inline const ::google::protobuf::RepeatedField< double >&
      fk() const;
  inline ::google::protobuf::RepeatedField< double >*
      mutable_fk();

  // repeated double FM = 4;
  inline int fm_size() const;
  inline void clear_fm();
  static const int kFMFieldNumber = 4;
  inline double fm(int index) const;
  inline void set_fm(int index, double value);
  inline void add_fm(double value);
  inline const ::google::protobuf::RepeatedField< double >&
      fm() const;
  inline ::google::protobuf::RepeatedField< double >*
      mutable_fm();

  // repeated double pos_limit = 5;
  inline int pos_limit_size() const;
  inline void clear_pos_limit();
  static const int kPosLimitFieldNumber = 5;
  inline double pos_limit(int index) const;
  inline void set_pos_limit(int index, double value);
  inline void add_pos_limit(double value);
  inline const ::google::protobuf::RepeatedField< double >&
      pos_limit() const;
  inline ::google::protobuf::RepeatedField< double >*
      mutable_pos_limit();

  // repeated double velocity_limit = 6;
  inline int velocity_limit_size() const;
  inline void clear_velocity_limit();
  static const int kVelocityLimitFieldNumber = 6;
  inline double velocity_limit(int index) const;
  inline void set_velocity_limit(int index, double value);
  inline void add_velocity_limit(double value);
  inline const ::google::protobuf::RepeatedField< double >&
      velocity_limit() const;
  inline ::google::protobuf::RepeatedField< double >*
      mutable_velocity_limit();

  // repeated double acceleration_limit = 7;
  inline int acceleration_limit_size() const;
  inline void clear_acceleration_limit();
  static const int kAccelerationLimitFieldNumber = 7;
  inline double acceleration_limit(int index) const;
  inline void set_acceleration_limit(int index, double value);
  inline void add_acceleration_limit(double value);
  inline const ::google::protobuf::RepeatedField< double >&
      acceleration_limit() const;
  inline ::google::protobuf::RepeatedField< double >*
      mutable_acceleration_limit();

  // @@protoc_insertion_point(class_scope:aubo.robot.paramerter.ProtoRobotHandguidingParameter)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::RepeatedField< double > fp_;
  ::google::protobuf::RepeatedField< double > fd_;
  ::google::protobuf::RepeatedField< double > fk_;
  ::google::protobuf::RepeatedField< double > fm_;
  ::google::protobuf::RepeatedField< double > pos_limit_;
  ::google::protobuf::RepeatedField< double > velocity_limit_;
  ::google::protobuf::RepeatedField< double > acceleration_limit_;
  friend void  protobuf_AddDesc_robotParameters_2eproto();
  friend void protobuf_AssignDesc_robotParameters_2eproto();
  friend void protobuf_ShutdownFile_robotParameters_2eproto();

  void InitAsDefaultInstance();
  static ProtoRobotHandguidingParameter* default_instance_;
};
// -------------------------------------------------------------------

class ProtoRobotKinematicsParameter : public ::google::protobuf::Message {
 public:
  ProtoRobotKinematicsParameter();
  virtual ~ProtoRobotKinematicsParameter();

  ProtoRobotKinematicsParameter(const ProtoRobotKinematicsParameter& from);

  inline ProtoRobotKinematicsParameter& operator=(const ProtoRobotKinematicsParameter& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ProtoRobotKinematicsParameter& default_instance();

  void Swap(ProtoRobotKinematicsParameter* other);

  // implements Message ----------------------------------------------

  ProtoRobotKinematicsParameter* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ProtoRobotKinematicsParameter& from);
  void MergeFrom(const ProtoRobotKinematicsParameter& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated double da = 1;
  inline int da_size() const;
  inline void clear_da();
  static const int kDaFieldNumber = 1;
  inline double da(int index) const;
  inline void set_da(int index, double value);
  inline void add_da(double value);
  inline const ::google::protobuf::RepeatedField< double >&
      da() const;
  inline ::google::protobuf::RepeatedField< double >*
      mutable_da();

  // repeated double dd = 2;
  inline int dd_size() const;
  inline void clear_dd();
  static const int kDdFieldNumber = 2;
  inline double dd(int index) const;
  inline void set_dd(int index, double value);
  inline void add_dd(double value);
  inline const ::google::protobuf::RepeatedField< double >&
      dd() const;
  inline ::google::protobuf::RepeatedField< double >*
      mutable_dd();

  // repeated double dalpha = 3;
  inline int dalpha_size() const;
  inline void clear_dalpha();
  static const int kDalphaFieldNumber = 3;
  inline double dalpha(int index) const;
  inline void set_dalpha(int index, double value);
  inline void add_dalpha(double value);
  inline const ::google::protobuf::RepeatedField< double >&
      dalpha() const;
  inline ::google::protobuf::RepeatedField< double >*
      mutable_dalpha();

  // repeated double dbeta = 4;
  inline int dbeta_size() const;
  inline void clear_dbeta();
  static const int kDbetaFieldNumber = 4;
  inline double dbeta(int index) const;
  inline void set_dbeta(int index, double value);
  inline void add_dbeta(double value);
  inline const ::google::protobuf::RepeatedField< double >&
      dbeta() const;
  inline ::google::protobuf::RepeatedField< double >*
      mutable_dbeta();

  // repeated double dratio = 5;
  inline int dratio_size() const;
  inline void clear_dratio();
  static const int kDratioFieldNumber = 5;
  inline double dratio(int index) const;
  inline void set_dratio(int index, double value);
  inline void add_dratio(double value);
  inline const ::google::protobuf::RepeatedField< double >&
      dratio() const;
  inline ::google::protobuf::RepeatedField< double >*
      mutable_dratio();

  // repeated double dtheta = 6;
  inline int dtheta_size() const;
  inline void clear_dtheta();
  static const int kDthetaFieldNumber = 6;
  inline double dtheta(int index) const;
  inline void set_dtheta(int index, double value);
  inline void add_dtheta(double value);
  inline const ::google::protobuf::RepeatedField< double >&
      dtheta() const;
  inline ::google::protobuf::RepeatedField< double >*
      mutable_dtheta();

  // @@protoc_insertion_point(class_scope:aubo.robot.paramerter.ProtoRobotKinematicsParameter)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::RepeatedField< double > da_;
  ::google::protobuf::RepeatedField< double > dd_;
  ::google::protobuf::RepeatedField< double > dalpha_;
  ::google::protobuf::RepeatedField< double > dbeta_;
  ::google::protobuf::RepeatedField< double > dratio_;
  ::google::protobuf::RepeatedField< double > dtheta_;
  friend void  protobuf_AddDesc_robotParameters_2eproto();
  friend void protobuf_AssignDesc_robotParameters_2eproto();
  friend void protobuf_ShutdownFile_robotParameters_2eproto();

  void InitAsDefaultInstance();
  static ProtoRobotKinematicsParameter* default_instance_;
};
// -------------------------------------------------------------------

class ProtoRobotKinematicsParameter_legacy : public ::google::protobuf::Message {
 public:
  ProtoRobotKinematicsParameter_legacy();
  virtual ~ProtoRobotKinematicsParameter_legacy();

  ProtoRobotKinematicsParameter_legacy(const ProtoRobotKinematicsParameter_legacy& from);

  inline ProtoRobotKinematicsParameter_legacy& operator=(const ProtoRobotKinematicsParameter_legacy& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ProtoRobotKinematicsParameter_legacy& default_instance();

  void Swap(ProtoRobotKinematicsParameter_legacy* other);

  // implements Message ----------------------------------------------

  ProtoRobotKinematicsParameter_legacy* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ProtoRobotKinematicsParameter_legacy& from);
  void MergeFrom(const ProtoRobotKinematicsParameter_legacy& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated uint32 da = 1;
  inline int da_size() const;
  inline void clear_da();
  static const int kDaFieldNumber = 1;
  inline ::google::protobuf::uint32 da(int index) const;
  inline void set_da(int index, ::google::protobuf::uint32 value);
  inline void add_da(::google::protobuf::uint32 value);
  inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
      da() const;
  inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
      mutable_da();

  // repeated uint32 dd = 2;
  inline int dd_size() const;
  inline void clear_dd();
  static const int kDdFieldNumber = 2;
  inline ::google::protobuf::uint32 dd(int index) const;
  inline void set_dd(int index, ::google::protobuf::uint32 value);
  inline void add_dd(::google::protobuf::uint32 value);
  inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
      dd() const;
  inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
      mutable_dd();

  // repeated uint32 dalpha = 3;
  inline int dalpha_size() const;
  inline void clear_dalpha();
  static const int kDalphaFieldNumber = 3;
  inline ::google::protobuf::uint32 dalpha(int index) const;
  inline void set_dalpha(int index, ::google::protobuf::uint32 value);
  inline void add_dalpha(::google::protobuf::uint32 value);
  inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
      dalpha() const;
  inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
      mutable_dalpha();

  // repeated uint32 dbeta = 4;
  inline int dbeta_size() const;
  inline void clear_dbeta();
  static const int kDbetaFieldNumber = 4;
  inline ::google::protobuf::uint32 dbeta(int index) const;
  inline void set_dbeta(int index, ::google::protobuf::uint32 value);
  inline void add_dbeta(::google::protobuf::uint32 value);
  inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
      dbeta() const;
  inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
      mutable_dbeta();

  // repeated uint32 dratio = 5;
  inline int dratio_size() const;
  inline void clear_dratio();
  static const int kDratioFieldNumber = 5;
  inline ::google::protobuf::uint32 dratio(int index) const;
  inline void set_dratio(int index, ::google::protobuf::uint32 value);
  inline void add_dratio(::google::protobuf::uint32 value);
  inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
      dratio() const;
  inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
      mutable_dratio();

  // repeated uint32 dtheta = 6;
  inline int dtheta_size() const;
  inline void clear_dtheta();
  static const int kDthetaFieldNumber = 6;
  inline ::google::protobuf::uint32 dtheta(int index) const;
  inline void set_dtheta(int index, ::google::protobuf::uint32 value);
  inline void add_dtheta(::google::protobuf::uint32 value);
  inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
      dtheta() const;
  inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
      mutable_dtheta();

  // @@protoc_insertion_point(class_scope:aubo.robot.paramerter.ProtoRobotKinematicsParameter_legacy)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 > da_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 > dd_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 > dalpha_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 > dbeta_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 > dratio_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 > dtheta_;
  friend void  protobuf_AddDesc_robotParameters_2eproto();
  friend void protobuf_AssignDesc_robotParameters_2eproto();
  friend void protobuf_ShutdownFile_robotParameters_2eproto();

  void InitAsDefaultInstance();
  static ProtoRobotKinematicsParameter_legacy* default_instance_;
};
// -------------------------------------------------------------------

class ProtoRobotFrictionParameter : public ::google::protobuf::Message {
 public:
  ProtoRobotFrictionParameter();
  virtual ~ProtoRobotFrictionParameter();

  ProtoRobotFrictionParameter(const ProtoRobotFrictionParameter& from);

  inline ProtoRobotFrictionParameter& operator=(const ProtoRobotFrictionParameter& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ProtoRobotFrictionParameter& default_instance();

  void Swap(ProtoRobotFrictionParameter* other);

  // implements Message ----------------------------------------------

  ProtoRobotFrictionParameter* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ProtoRobotFrictionParameter& from);
  void MergeFrom(const ProtoRobotFrictionParameter& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated uint32 FL = 1;
  inline int fl_size() const;
  inline void clear_fl();
  static const int kFLFieldNumber = 1;
  inline ::google::protobuf::uint32 fl(int index) const;
  inline void set_fl(int index, ::google::protobuf::uint32 value);
  inline void add_fl(::google::protobuf::uint32 value);
  inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
      fl() const;
  inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
      mutable_fl();

  // repeated uint32 FR = 2;
  inline int fr_size() const;
  inline void clear_fr();
  static const int kFRFieldNumber = 2;
  inline ::google::protobuf::uint32 fr(int index) const;
  inline void set_fr(int index, ::google::protobuf::uint32 value);
  inline void add_fr(::google::protobuf::uint32 value);
  inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
      fr() const;
  inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
      mutable_fr();

  // repeated uint32 tmp_a = 3;
  inline int tmp_a_size() const;
  inline void clear_tmp_a();
  static const int kTmpAFieldNumber = 3;
  inline ::google::protobuf::uint32 tmp_a(int index) const;
  inline void set_tmp_a(int index, ::google::protobuf::uint32 value);
  inline void add_tmp_a(::google::protobuf::uint32 value);
  inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
      tmp_a() const;
  inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
      mutable_tmp_a();

  // repeated uint32 tmp_b = 4;
  inline int tmp_b_size() const;
  inline void clear_tmp_b();
  static const int kTmpBFieldNumber = 4;
  inline ::google::protobuf::uint32 tmp_b(int index) const;
  inline void set_tmp_b(int index, ::google::protobuf::uint32 value);
  inline void add_tmp_b(::google::protobuf::uint32 value);
  inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
      tmp_b() const;
  inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
      mutable_tmp_b();

  // repeated uint32 posvel_a1 = 5;
  inline int posvel_a1_size() const;
  inline void clear_posvel_a1();
  static const int kPosvelA1FieldNumber = 5;
  inline ::google::protobuf::uint32 posvel_a1(int index) const;
  inline void set_posvel_a1(int index, ::google::protobuf::uint32 value);
  inline void add_posvel_a1(::google::protobuf::uint32 value);
  inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
      posvel_a1() const;
  inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
      mutable_posvel_a1();

  // repeated uint32 posvel_b1 = 6;
  inline int posvel_b1_size() const;
  inline void clear_posvel_b1();
  static const int kPosvelB1FieldNumber = 6;
  inline ::google::protobuf::uint32 posvel_b1(int index) const;
  inline void set_posvel_b1(int index, ::google::protobuf::uint32 value);
  inline void add_posvel_b1(::google::protobuf::uint32 value);
  inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
      posvel_b1() const;
  inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
      mutable_posvel_b1();

  // repeated uint32 posvel_a2 = 7;
  inline int posvel_a2_size() const;
  inline void clear_posvel_a2();
  static const int kPosvelA2FieldNumber = 7;
  inline ::google::protobuf::uint32 posvel_a2(int index) const;
  inline void set_posvel_a2(int index, ::google::protobuf::uint32 value);
  inline void add_posvel_a2(::google::protobuf::uint32 value);
  inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
      posvel_a2() const;
  inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
      mutable_posvel_a2();

  // repeated uint32 posvel_b2 = 8;
  inline int posvel_b2_size() const;
  inline void clear_posvel_b2();
  static const int kPosvelB2FieldNumber = 8;
  inline ::google::protobuf::uint32 posvel_b2(int index) const;
  inline void set_posvel_b2(int index, ::google::protobuf::uint32 value);
  inline void add_posvel_b2(::google::protobuf::uint32 value);
  inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
      posvel_b2() const;
  inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
      mutable_posvel_b2();

  // repeated uint32 posvel_c2 = 9;
  inline int posvel_c2_size() const;
  inline void clear_posvel_c2();
  static const int kPosvelC2FieldNumber = 9;
  inline ::google::protobuf::uint32 posvel_c2(int index) const;
  inline void set_posvel_c2(int index, ::google::protobuf::uint32 value);
  inline void add_posvel_c2(::google::protobuf::uint32 value);
  inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
      posvel_c2() const;
  inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
      mutable_posvel_c2();

  // repeated uint32 negvel_a1 = 10;
  inline int negvel_a1_size() const;
  inline void clear_negvel_a1();
  static const int kNegvelA1FieldNumber = 10;
  inline ::google::protobuf::uint32 negvel_a1(int index) const;
  inline void set_negvel_a1(int index, ::google::protobuf::uint32 value);
  inline void add_negvel_a1(::google::protobuf::uint32 value);
  inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
      negvel_a1() const;
  inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
      mutable_negvel_a1();

  // repeated uint32 negvel_b1 = 11;
  inline int negvel_b1_size() const;
  inline void clear_negvel_b1();
  static const int kNegvelB1FieldNumber = 11;
  inline ::google::protobuf::uint32 negvel_b1(int index) const;
  inline void set_negvel_b1(int index, ::google::protobuf::uint32 value);
  inline void add_negvel_b1(::google::protobuf::uint32 value);
  inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
      negvel_b1() const;
  inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
      mutable_negvel_b1();

  // repeated uint32 negvel_a2 = 12;
  inline int negvel_a2_size() const;
  inline void clear_negvel_a2();
  static const int kNegvelA2FieldNumber = 12;
  inline ::google::protobuf::uint32 negvel_a2(int index) const;
  inline void set_negvel_a2(int index, ::google::protobuf::uint32 value);
  inline void add_negvel_a2(::google::protobuf::uint32 value);
  inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
      negvel_a2() const;
  inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
      mutable_negvel_a2();

  // repeated uint32 negvel_b2 = 13;
  inline int negvel_b2_size() const;
  inline void clear_negvel_b2();
  static const int kNegvelB2FieldNumber = 13;
  inline ::google::protobuf::uint32 negvel_b2(int index) const;
  inline void set_negvel_b2(int index, ::google::protobuf::uint32 value);
  inline void add_negvel_b2(::google::protobuf::uint32 value);
  inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
      negvel_b2() const;
  inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
      mutable_negvel_b2();

  // repeated uint32 negvel_c2 = 14;
  inline int negvel_c2_size() const;
  inline void clear_negvel_c2();
  static const int kNegvelC2FieldNumber = 14;
  inline ::google::protobuf::uint32 negvel_c2(int index) const;
  inline void set_negvel_c2(int index, ::google::protobuf::uint32 value);
  inline void add_negvel_c2(::google::protobuf::uint32 value);
  inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
      negvel_c2() const;
  inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
      mutable_negvel_c2();

  // @@protoc_insertion_point(class_scope:aubo.robot.paramerter.ProtoRobotFrictionParameter)
 private:

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 > fl_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 > fr_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 > tmp_a_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 > tmp_b_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 > posvel_a1_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 > posvel_b1_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 > posvel_a2_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 > posvel_b2_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 > posvel_c2_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 > negvel_a1_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 > negvel_b1_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 > negvel_a2_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 > negvel_b2_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 > negvel_c2_;
  friend void  protobuf_AddDesc_robotParameters_2eproto();
  friend void protobuf_AssignDesc_robotParameters_2eproto();
  friend void protobuf_ShutdownFile_robotParameters_2eproto();

  void InitAsDefaultInstance();
  static ProtoRobotFrictionParameter* default_instance_;
};
// -------------------------------------------------------------------

class ProtoRobotBaseParameter : public ::google::protobuf::Message {
 public:
  ProtoRobotBaseParameter();
  virtual ~ProtoRobotBaseParameter();

  ProtoRobotBaseParameter(const ProtoRobotBaseParameter& from);

  inline ProtoRobotBaseParameter& operator=(const ProtoRobotBaseParameter& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ProtoRobotBaseParameter& default_instance();

  void Swap(ProtoRobotBaseParameter* other);

  // implements Message ----------------------------------------------

  ProtoRobotBaseParameter* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ProtoRobotBaseParameter& from);
  void MergeFrom(const ProtoRobotBaseParameter& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .aubo.robot.paramerter.ProtoRobotInfo robotInfo = 1;
  inline bool has_robotinfo() const;
  inline void clear_robotinfo();
  static const int kRobotInfoFieldNumber = 1;
  inline const ::aubo::robot::paramerter::ProtoRobotInfo& robotinfo() const;
  inline ::aubo::robot::paramerter::ProtoRobotInfo* mutable_robotinfo();
  inline ::aubo::robot::paramerter::ProtoRobotInfo* release_robotinfo();
  inline void set_allocated_robotinfo(::aubo::robot::paramerter::ProtoRobotInfo* robotinfo);

  // required .aubo.robot.paramerter.ProtoRobotDynamicsParameter dynamicsParameter = 2;
  inline bool has_dynamicsparameter() const;
  inline void clear_dynamicsparameter();
  static const int kDynamicsParameterFieldNumber = 2;
  inline const ::aubo::robot::paramerter::ProtoRobotDynamicsParameter& dynamicsparameter() const;
  inline ::aubo::robot::paramerter::ProtoRobotDynamicsParameter* mutable_dynamicsparameter();
  inline ::aubo::robot::paramerter::ProtoRobotDynamicsParameter* release_dynamicsparameter();
  inline void set_allocated_dynamicsparameter(::aubo::robot::paramerter::ProtoRobotDynamicsParameter* dynamicsparameter);

  // required .aubo.robot.paramerter.ProtoRobotHandguidingParameter handguidingParameter = 3;
  inline bool has_handguidingparameter() const;
  inline void clear_handguidingparameter();
  static const int kHandguidingParameterFieldNumber = 3;
  inline const ::aubo::robot::paramerter::ProtoRobotHandguidingParameter& handguidingparameter() const;
  inline ::aubo::robot::paramerter::ProtoRobotHandguidingParameter* mutable_handguidingparameter();
  inline ::aubo::robot::paramerter::ProtoRobotHandguidingParameter* release_handguidingparameter();
  inline void set_allocated_handguidingparameter(::aubo::robot::paramerter::ProtoRobotHandguidingParameter* handguidingparameter);

  // required .aubo.robot.paramerter.ProtoRobotKinematicsParameter KinematicsParameter = 4;
  inline bool has_kinematicsparameter() const;
  inline void clear_kinematicsparameter();
  static const int kKinematicsParameterFieldNumber = 4;
  inline const ::aubo::robot::paramerter::ProtoRobotKinematicsParameter& kinematicsparameter() const;
  inline ::aubo::robot::paramerter::ProtoRobotKinematicsParameter* mutable_kinematicsparameter();
  inline ::aubo::robot::paramerter::ProtoRobotKinematicsParameter* release_kinematicsparameter();
  inline void set_allocated_kinematicsparameter(::aubo::robot::paramerter::ProtoRobotKinematicsParameter* kinematicsparameter);

  // @@protoc_insertion_point(class_scope:aubo.robot.paramerter.ProtoRobotBaseParameter)
 private:
  inline void set_has_robotinfo();
  inline void clear_has_robotinfo();
  inline void set_has_dynamicsparameter();
  inline void clear_has_dynamicsparameter();
  inline void set_has_handguidingparameter();
  inline void clear_has_handguidingparameter();
  inline void set_has_kinematicsparameter();
  inline void clear_has_kinematicsparameter();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::aubo::robot::paramerter::ProtoRobotInfo* robotinfo_;
  ::aubo::robot::paramerter::ProtoRobotDynamicsParameter* dynamicsparameter_;
  ::aubo::robot::paramerter::ProtoRobotHandguidingParameter* handguidingparameter_;
  ::aubo::robot::paramerter::ProtoRobotKinematicsParameter* kinematicsparameter_;
  friend void  protobuf_AddDesc_robotParameters_2eproto();
  friend void protobuf_AssignDesc_robotParameters_2eproto();
  friend void protobuf_ShutdownFile_robotParameters_2eproto();

  void InitAsDefaultInstance();
  static ProtoRobotBaseParameter* default_instance_;
};
// -------------------------------------------------------------------

class ProtoRobotBaseParameter_legacy : public ::google::protobuf::Message {
 public:
  ProtoRobotBaseParameter_legacy();
  virtual ~ProtoRobotBaseParameter_legacy();

  ProtoRobotBaseParameter_legacy(const ProtoRobotBaseParameter_legacy& from);

  inline ProtoRobotBaseParameter_legacy& operator=(const ProtoRobotBaseParameter_legacy& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ProtoRobotBaseParameter_legacy& default_instance();

  void Swap(ProtoRobotBaseParameter_legacy* other);

  // implements Message ----------------------------------------------

  ProtoRobotBaseParameter_legacy* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ProtoRobotBaseParameter_legacy& from);
  void MergeFrom(const ProtoRobotBaseParameter_legacy& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .aubo.robot.paramerter.ProtoRobotInfo robotInfo = 1;
  inline bool has_robotinfo() const;
  inline void clear_robotinfo();
  static const int kRobotInfoFieldNumber = 1;
  inline const ::aubo::robot::paramerter::ProtoRobotInfo& robotinfo() const;
  inline ::aubo::robot::paramerter::ProtoRobotInfo* mutable_robotinfo();
  inline ::aubo::robot::paramerter::ProtoRobotInfo* release_robotinfo();
  inline void set_allocated_robotinfo(::aubo::robot::paramerter::ProtoRobotInfo* robotinfo);

  // required .aubo.robot.paramerter.ProtoRobotDynamicsParameter dynamicsParameter = 2;
  inline bool has_dynamicsparameter() const;
  inline void clear_dynamicsparameter();
  static const int kDynamicsParameterFieldNumber = 2;
  inline const ::aubo::robot::paramerter::ProtoRobotDynamicsParameter& dynamicsparameter() const;
  inline ::aubo::robot::paramerter::ProtoRobotDynamicsParameter* mutable_dynamicsparameter();
  inline ::aubo::robot::paramerter::ProtoRobotDynamicsParameter* release_dynamicsparameter();
  inline void set_allocated_dynamicsparameter(::aubo::robot::paramerter::ProtoRobotDynamicsParameter* dynamicsparameter);

  // required .aubo.robot.paramerter.ProtoRobotHandguidingParameter handguidingParameter = 3;
  inline bool has_handguidingparameter() const;
  inline void clear_handguidingparameter();
  static const int kHandguidingParameterFieldNumber = 3;
  inline const ::aubo::robot::paramerter::ProtoRobotHandguidingParameter& handguidingparameter() const;
  inline ::aubo::robot::paramerter::ProtoRobotHandguidingParameter* mutable_handguidingparameter();
  inline ::aubo::robot::paramerter::ProtoRobotHandguidingParameter* release_handguidingparameter();
  inline void set_allocated_handguidingparameter(::aubo::robot::paramerter::ProtoRobotHandguidingParameter* handguidingparameter);

  // required .aubo.robot.paramerter.ProtoRobotKinematicsParameter_legacy KinematicsParameter = 4;
  inline bool has_kinematicsparameter() const;
  inline void clear_kinematicsparameter();
  static const int kKinematicsParameterFieldNumber = 4;
  inline const ::aubo::robot::paramerter::ProtoRobotKinematicsParameter_legacy& kinematicsparameter() const;
  inline ::aubo::robot::paramerter::ProtoRobotKinematicsParameter_legacy* mutable_kinematicsparameter();
  inline ::aubo::robot::paramerter::ProtoRobotKinematicsParameter_legacy* release_kinematicsparameter();
  inline void set_allocated_kinematicsparameter(::aubo::robot::paramerter::ProtoRobotKinematicsParameter_legacy* kinematicsparameter);

  // @@protoc_insertion_point(class_scope:aubo.robot.paramerter.ProtoRobotBaseParameter_legacy)
 private:
  inline void set_has_robotinfo();
  inline void clear_has_robotinfo();
  inline void set_has_dynamicsparameter();
  inline void clear_has_dynamicsparameter();
  inline void set_has_handguidingparameter();
  inline void clear_has_handguidingparameter();
  inline void set_has_kinematicsparameter();
  inline void clear_has_kinematicsparameter();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::aubo::robot::paramerter::ProtoRobotInfo* robotinfo_;
  ::aubo::robot::paramerter::ProtoRobotDynamicsParameter* dynamicsparameter_;
  ::aubo::robot::paramerter::ProtoRobotHandguidingParameter* handguidingparameter_;
  ::aubo::robot::paramerter::ProtoRobotKinematicsParameter_legacy* kinematicsparameter_;
  friend void  protobuf_AddDesc_robotParameters_2eproto();
  friend void protobuf_AssignDesc_robotParameters_2eproto();
  friend void protobuf_ShutdownFile_robotParameters_2eproto();

  void InitAsDefaultInstance();
  static ProtoRobotBaseParameter_legacy* default_instance_;
};
// -------------------------------------------------------------------

class ProtoRobotBaseParameterResponse : public ::google::protobuf::Message {
 public:
  ProtoRobotBaseParameterResponse();
  virtual ~ProtoRobotBaseParameterResponse();

  ProtoRobotBaseParameterResponse(const ProtoRobotBaseParameterResponse& from);

  inline ProtoRobotBaseParameterResponse& operator=(const ProtoRobotBaseParameterResponse& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ProtoRobotBaseParameterResponse& default_instance();

  void Swap(ProtoRobotBaseParameterResponse* other);

  // implements Message ----------------------------------------------

  ProtoRobotBaseParameterResponse* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ProtoRobotBaseParameterResponse& from);
  void MergeFrom(const ProtoRobotBaseParameterResponse& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .aubo.robot.paramerter.ProtoRobotBaseParameter baseParameter = 1;
  inline bool has_baseparameter() const;
  inline void clear_baseparameter();
  static const int kBaseParameterFieldNumber = 1;
  inline const ::aubo::robot::paramerter::ProtoRobotBaseParameter& baseparameter() const;
  inline ::aubo::robot::paramerter::ProtoRobotBaseParameter* mutable_baseparameter();
  inline ::aubo::robot::paramerter::ProtoRobotBaseParameter* release_baseparameter();
  inline void set_allocated_baseparameter(::aubo::robot::paramerter::ProtoRobotBaseParameter* baseparameter);

  // required .aubo.robot.communication.ProtoRobotCommonResponse errorInfo = 2;
  inline bool has_errorinfo() const;
  inline void clear_errorinfo();
  static const int kErrorInfoFieldNumber = 2;
  inline const ::aubo::robot::communication::ProtoRobotCommonResponse& errorinfo() const;
  inline ::aubo::robot::communication::ProtoRobotCommonResponse* mutable_errorinfo();
  inline ::aubo::robot::communication::ProtoRobotCommonResponse* release_errorinfo();
  inline void set_allocated_errorinfo(::aubo::robot::communication::ProtoRobotCommonResponse* errorinfo);

  // @@protoc_insertion_point(class_scope:aubo.robot.paramerter.ProtoRobotBaseParameterResponse)
 private:
  inline void set_has_baseparameter();
  inline void clear_has_baseparameter();
  inline void set_has_errorinfo();
  inline void clear_has_errorinfo();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::aubo::robot::paramerter::ProtoRobotBaseParameter* baseparameter_;
  ::aubo::robot::communication::ProtoRobotCommonResponse* errorinfo_;
  friend void  protobuf_AddDesc_robotParameters_2eproto();
  friend void protobuf_AssignDesc_robotParameters_2eproto();
  friend void protobuf_ShutdownFile_robotParameters_2eproto();

  void InitAsDefaultInstance();
  static ProtoRobotBaseParameterResponse* default_instance_;
};
// -------------------------------------------------------------------

class ProtoRobotBaseParameterResponse_legacy : public ::google::protobuf::Message {
 public:
  ProtoRobotBaseParameterResponse_legacy();
  virtual ~ProtoRobotBaseParameterResponse_legacy();

  ProtoRobotBaseParameterResponse_legacy(const ProtoRobotBaseParameterResponse_legacy& from);

  inline ProtoRobotBaseParameterResponse_legacy& operator=(const ProtoRobotBaseParameterResponse_legacy& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ProtoRobotBaseParameterResponse_legacy& default_instance();

  void Swap(ProtoRobotBaseParameterResponse_legacy* other);

  // implements Message ----------------------------------------------

  ProtoRobotBaseParameterResponse_legacy* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ProtoRobotBaseParameterResponse_legacy& from);
  void MergeFrom(const ProtoRobotBaseParameterResponse_legacy& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .aubo.robot.paramerter.ProtoRobotBaseParameter_legacy baseParameter = 1;
  inline bool has_baseparameter() const;
  inline void clear_baseparameter();
  static const int kBaseParameterFieldNumber = 1;
  inline const ::aubo::robot::paramerter::ProtoRobotBaseParameter_legacy& baseparameter() const;
  inline ::aubo::robot::paramerter::ProtoRobotBaseParameter_legacy* mutable_baseparameter();
  inline ::aubo::robot::paramerter::ProtoRobotBaseParameter_legacy* release_baseparameter();
  inline void set_allocated_baseparameter(::aubo::robot::paramerter::ProtoRobotBaseParameter_legacy* baseparameter);

  // required .aubo.robot.communication.ProtoRobotCommonResponse errorInfo = 2;
  inline bool has_errorinfo() const;
  inline void clear_errorinfo();
  static const int kErrorInfoFieldNumber = 2;
  inline const ::aubo::robot::communication::ProtoRobotCommonResponse& errorinfo() const;
  inline ::aubo::robot::communication::ProtoRobotCommonResponse* mutable_errorinfo();
  inline ::aubo::robot::communication::ProtoRobotCommonResponse* release_errorinfo();
  inline void set_allocated_errorinfo(::aubo::robot::communication::ProtoRobotCommonResponse* errorinfo);

  // @@protoc_insertion_point(class_scope:aubo.robot.paramerter.ProtoRobotBaseParameterResponse_legacy)
 private:
  inline void set_has_baseparameter();
  inline void clear_has_baseparameter();
  inline void set_has_errorinfo();
  inline void clear_has_errorinfo();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::aubo::robot::paramerter::ProtoRobotBaseParameter_legacy* baseparameter_;
  ::aubo::robot::communication::ProtoRobotCommonResponse* errorinfo_;
  friend void  protobuf_AddDesc_robotParameters_2eproto();
  friend void protobuf_AssignDesc_robotParameters_2eproto();
  friend void protobuf_ShutdownFile_robotParameters_2eproto();

  void InitAsDefaultInstance();
  static ProtoRobotBaseParameterResponse_legacy* default_instance_;
};
// -------------------------------------------------------------------

class ProtoRobotJointsParameter : public ::google::protobuf::Message {
 public:
  ProtoRobotJointsParameter();
  virtual ~ProtoRobotJointsParameter();

  ProtoRobotJointsParameter(const ProtoRobotJointsParameter& from);

  inline ProtoRobotJointsParameter& operator=(const ProtoRobotJointsParameter& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ProtoRobotJointsParameter& default_instance();

  void Swap(ProtoRobotJointsParameter* other);

  // implements Message ----------------------------------------------

  ProtoRobotJointsParameter* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ProtoRobotJointsParameter& from);
  void MergeFrom(const ProtoRobotJointsParameter& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .aubo.robot.paramerter.ProtoRobotFrictionParameter frictionParameter = 1;
  inline bool has_frictionparameter() const;
  inline void clear_frictionparameter();
  static const int kFrictionParameterFieldNumber = 1;
  inline const ::aubo::robot::paramerter::ProtoRobotFrictionParameter& frictionparameter() const;
  inline ::aubo::robot::paramerter::ProtoRobotFrictionParameter* mutable_frictionparameter();
  inline ::aubo::robot::paramerter::ProtoRobotFrictionParameter* release_frictionparameter();
  inline void set_allocated_frictionparameter(::aubo::robot::paramerter::ProtoRobotFrictionParameter* frictionparameter);

  // @@protoc_insertion_point(class_scope:aubo.robot.paramerter.ProtoRobotJointsParameter)
 private:
  inline void set_has_frictionparameter();
  inline void clear_has_frictionparameter();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::aubo::robot::paramerter::ProtoRobotFrictionParameter* frictionparameter_;
  friend void  protobuf_AddDesc_robotParameters_2eproto();
  friend void protobuf_AssignDesc_robotParameters_2eproto();
  friend void protobuf_ShutdownFile_robotParameters_2eproto();

  void InitAsDefaultInstance();
  static ProtoRobotJointsParameter* default_instance_;
};
// -------------------------------------------------------------------

class ProtoRobotJointsParameterResponse : public ::google::protobuf::Message {
 public:
  ProtoRobotJointsParameterResponse();
  virtual ~ProtoRobotJointsParameterResponse();

  ProtoRobotJointsParameterResponse(const ProtoRobotJointsParameterResponse& from);

  inline ProtoRobotJointsParameterResponse& operator=(const ProtoRobotJointsParameterResponse& from) {
    CopyFrom(from);
    return *this;
  }

  inline const ::google::protobuf::UnknownFieldSet& unknown_fields() const {
    return _unknown_fields_;
  }

  inline ::google::protobuf::UnknownFieldSet* mutable_unknown_fields() {
    return &_unknown_fields_;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ProtoRobotJointsParameterResponse& default_instance();

  void Swap(ProtoRobotJointsParameterResponse* other);

  // implements Message ----------------------------------------------

  ProtoRobotJointsParameterResponse* New() const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ProtoRobotJointsParameterResponse& from);
  void MergeFrom(const ProtoRobotJointsParameterResponse& from);
  void Clear();
  bool IsInitialized() const;

  int ByteSize() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const;
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  public:
  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // required .aubo.robot.paramerter.ProtoRobotJointsParameter jointsParameter = 1;
  inline bool has_jointsparameter() const;
  inline void clear_jointsparameter();
  static const int kJointsParameterFieldNumber = 1;
  inline const ::aubo::robot::paramerter::ProtoRobotJointsParameter& jointsparameter() const;
  inline ::aubo::robot::paramerter::ProtoRobotJointsParameter* mutable_jointsparameter();
  inline ::aubo::robot::paramerter::ProtoRobotJointsParameter* release_jointsparameter();
  inline void set_allocated_jointsparameter(::aubo::robot::paramerter::ProtoRobotJointsParameter* jointsparameter);

  // required .aubo.robot.communication.ProtoRobotCommonResponse errorInfo = 2;
  inline bool has_errorinfo() const;
  inline void clear_errorinfo();
  static const int kErrorInfoFieldNumber = 2;
  inline const ::aubo::robot::communication::ProtoRobotCommonResponse& errorinfo() const;
  inline ::aubo::robot::communication::ProtoRobotCommonResponse* mutable_errorinfo();
  inline ::aubo::robot::communication::ProtoRobotCommonResponse* release_errorinfo();
  inline void set_allocated_errorinfo(::aubo::robot::communication::ProtoRobotCommonResponse* errorinfo);

  // @@protoc_insertion_point(class_scope:aubo.robot.paramerter.ProtoRobotJointsParameterResponse)
 private:
  inline void set_has_jointsparameter();
  inline void clear_has_jointsparameter();
  inline void set_has_errorinfo();
  inline void clear_has_errorinfo();

  ::google::protobuf::UnknownFieldSet _unknown_fields_;

  ::google::protobuf::uint32 _has_bits_[1];
  mutable int _cached_size_;
  ::aubo::robot::paramerter::ProtoRobotJointsParameter* jointsparameter_;
  ::aubo::robot::communication::ProtoRobotCommonResponse* errorinfo_;
  friend void  protobuf_AddDesc_robotParameters_2eproto();
  friend void protobuf_AssignDesc_robotParameters_2eproto();
  friend void protobuf_ShutdownFile_robotParameters_2eproto();

  void InitAsDefaultInstance();
  static ProtoRobotJointsParameterResponse* default_instance_;
};
// ===================================================================


// ===================================================================

// ProtoRobotInfo

// required uint32 robot_type = 1;
inline bool ProtoRobotInfo::has_robot_type() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ProtoRobotInfo::set_has_robot_type() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ProtoRobotInfo::clear_has_robot_type() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ProtoRobotInfo::clear_robot_type() {
  robot_type_ = 0u;
  clear_has_robot_type();
}
inline ::google::protobuf::uint32 ProtoRobotInfo::robot_type() const {
  // @@protoc_insertion_point(field_get:aubo.robot.paramerter.ProtoRobotInfo.robot_type)
  return robot_type_;
}
inline void ProtoRobotInfo::set_robot_type(::google::protobuf::uint32 value) {
  set_has_robot_type();
  robot_type_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.paramerter.ProtoRobotInfo.robot_type)
}

// required uint32 auth_type = 2;
inline bool ProtoRobotInfo::has_auth_type() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void ProtoRobotInfo::set_has_auth_type() {
  _has_bits_[0] |= 0x00000002u;
}
inline void ProtoRobotInfo::clear_has_auth_type() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void ProtoRobotInfo::clear_auth_type() {
  auth_type_ = 0u;
  clear_has_auth_type();
}
inline ::google::protobuf::uint32 ProtoRobotInfo::auth_type() const {
  // @@protoc_insertion_point(field_get:aubo.robot.paramerter.ProtoRobotInfo.auth_type)
  return auth_type_;
}
inline void ProtoRobotInfo::set_auth_type(::google::protobuf::uint32 value) {
  set_has_auth_type();
  auth_type_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.paramerter.ProtoRobotInfo.auth_type)
}

// required uint32 robot_expire = 3;
inline bool ProtoRobotInfo::has_robot_expire() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void ProtoRobotInfo::set_has_robot_expire() {
  _has_bits_[0] |= 0x00000004u;
}
inline void ProtoRobotInfo::clear_has_robot_expire() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void ProtoRobotInfo::clear_robot_expire() {
  robot_expire_ = 0u;
  clear_has_robot_expire();
}
inline ::google::protobuf::uint32 ProtoRobotInfo::robot_expire() const {
  // @@protoc_insertion_point(field_get:aubo.robot.paramerter.ProtoRobotInfo.robot_expire)
  return robot_expire_;
}
inline void ProtoRobotInfo::set_robot_expire(::google::protobuf::uint32 value) {
  set_has_robot_expire();
  robot_expire_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.paramerter.ProtoRobotInfo.robot_expire)
}

// repeated uint32 reserve = 4;
inline int ProtoRobotInfo::reserve_size() const {
  return reserve_.size();
}
inline void ProtoRobotInfo::clear_reserve() {
  reserve_.Clear();
}
inline ::google::protobuf::uint32 ProtoRobotInfo::reserve(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.paramerter.ProtoRobotInfo.reserve)
  return reserve_.Get(index);
}
inline void ProtoRobotInfo::set_reserve(int index, ::google::protobuf::uint32 value) {
  reserve_.Set(index, value);
  // @@protoc_insertion_point(field_set:aubo.robot.paramerter.ProtoRobotInfo.reserve)
}
inline void ProtoRobotInfo::add_reserve(::google::protobuf::uint32 value) {
  reserve_.Add(value);
  // @@protoc_insertion_point(field_add:aubo.robot.paramerter.ProtoRobotInfo.reserve)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
ProtoRobotInfo::reserve() const {
  // @@protoc_insertion_point(field_list:aubo.robot.paramerter.ProtoRobotInfo.reserve)
  return reserve_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
ProtoRobotInfo::mutable_reserve() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.paramerter.ProtoRobotInfo.reserve)
  return &reserve_;
}

// required uint32 robot_duration = 5;
inline bool ProtoRobotInfo::has_robot_duration() const {
  return (_has_bits_[0] & 0x00000010u) != 0;
}
inline void ProtoRobotInfo::set_has_robot_duration() {
  _has_bits_[0] |= 0x00000010u;
}
inline void ProtoRobotInfo::clear_has_robot_duration() {
  _has_bits_[0] &= ~0x00000010u;
}
inline void ProtoRobotInfo::clear_robot_duration() {
  robot_duration_ = 0u;
  clear_has_robot_duration();
}
inline ::google::protobuf::uint32 ProtoRobotInfo::robot_duration() const {
  // @@protoc_insertion_point(field_get:aubo.robot.paramerter.ProtoRobotInfo.robot_duration)
  return robot_duration_;
}
inline void ProtoRobotInfo::set_robot_duration(::google::protobuf::uint32 value) {
  set_has_robot_duration();
  robot_duration_ = value;
  // @@protoc_insertion_point(field_set:aubo.robot.paramerter.ProtoRobotInfo.robot_duration)
}

// repeated uint32 joint_duration = 6;
inline int ProtoRobotInfo::joint_duration_size() const {
  return joint_duration_.size();
}
inline void ProtoRobotInfo::clear_joint_duration() {
  joint_duration_.Clear();
}
inline ::google::protobuf::uint32 ProtoRobotInfo::joint_duration(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.paramerter.ProtoRobotInfo.joint_duration)
  return joint_duration_.Get(index);
}
inline void ProtoRobotInfo::set_joint_duration(int index, ::google::protobuf::uint32 value) {
  joint_duration_.Set(index, value);
  // @@protoc_insertion_point(field_set:aubo.robot.paramerter.ProtoRobotInfo.joint_duration)
}
inline void ProtoRobotInfo::add_joint_duration(::google::protobuf::uint32 value) {
  joint_duration_.Add(value);
  // @@protoc_insertion_point(field_add:aubo.robot.paramerter.ProtoRobotInfo.joint_duration)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
ProtoRobotInfo::joint_duration() const {
  // @@protoc_insertion_point(field_list:aubo.robot.paramerter.ProtoRobotInfo.joint_duration)
  return joint_duration_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
ProtoRobotInfo::mutable_joint_duration() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.paramerter.ProtoRobotInfo.joint_duration)
  return &joint_duration_;
}

// -------------------------------------------------------------------

// ProtoRobotDynamicsParameter

// repeated double K = 1;
inline int ProtoRobotDynamicsParameter::k_size() const {
  return k_.size();
}
inline void ProtoRobotDynamicsParameter::clear_k() {
  k_.Clear();
}
inline double ProtoRobotDynamicsParameter::k(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.paramerter.ProtoRobotDynamicsParameter.K)
  return k_.Get(index);
}
inline void ProtoRobotDynamicsParameter::set_k(int index, double value) {
  k_.Set(index, value);
  // @@protoc_insertion_point(field_set:aubo.robot.paramerter.ProtoRobotDynamicsParameter.K)
}
inline void ProtoRobotDynamicsParameter::add_k(double value) {
  k_.Add(value);
  // @@protoc_insertion_point(field_add:aubo.robot.paramerter.ProtoRobotDynamicsParameter.K)
}
inline const ::google::protobuf::RepeatedField< double >&
ProtoRobotDynamicsParameter::k() const {
  // @@protoc_insertion_point(field_list:aubo.robot.paramerter.ProtoRobotDynamicsParameter.K)
  return k_;
}
inline ::google::protobuf::RepeatedField< double >*
ProtoRobotDynamicsParameter::mutable_k() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.paramerter.ProtoRobotDynamicsParameter.K)
  return &k_;
}

// repeated double IA = 2;
inline int ProtoRobotDynamicsParameter::ia_size() const {
  return ia_.size();
}
inline void ProtoRobotDynamicsParameter::clear_ia() {
  ia_.Clear();
}
inline double ProtoRobotDynamicsParameter::ia(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.paramerter.ProtoRobotDynamicsParameter.IA)
  return ia_.Get(index);
}
inline void ProtoRobotDynamicsParameter::set_ia(int index, double value) {
  ia_.Set(index, value);
  // @@protoc_insertion_point(field_set:aubo.robot.paramerter.ProtoRobotDynamicsParameter.IA)
}
inline void ProtoRobotDynamicsParameter::add_ia(double value) {
  ia_.Add(value);
  // @@protoc_insertion_point(field_add:aubo.robot.paramerter.ProtoRobotDynamicsParameter.IA)
}
inline const ::google::protobuf::RepeatedField< double >&
ProtoRobotDynamicsParameter::ia() const {
  // @@protoc_insertion_point(field_list:aubo.robot.paramerter.ProtoRobotDynamicsParameter.IA)
  return ia_;
}
inline ::google::protobuf::RepeatedField< double >*
ProtoRobotDynamicsParameter::mutable_ia() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.paramerter.ProtoRobotDynamicsParameter.IA)
  return &ia_;
}

// repeated double M = 3;
inline int ProtoRobotDynamicsParameter::m_size() const {
  return m_.size();
}
inline void ProtoRobotDynamicsParameter::clear_m() {
  m_.Clear();
}
inline double ProtoRobotDynamicsParameter::m(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.paramerter.ProtoRobotDynamicsParameter.M)
  return m_.Get(index);
}
inline void ProtoRobotDynamicsParameter::set_m(int index, double value) {
  m_.Set(index, value);
  // @@protoc_insertion_point(field_set:aubo.robot.paramerter.ProtoRobotDynamicsParameter.M)
}
inline void ProtoRobotDynamicsParameter::add_m(double value) {
  m_.Add(value);
  // @@protoc_insertion_point(field_add:aubo.robot.paramerter.ProtoRobotDynamicsParameter.M)
}
inline const ::google::protobuf::RepeatedField< double >&
ProtoRobotDynamicsParameter::m() const {
  // @@protoc_insertion_point(field_list:aubo.robot.paramerter.ProtoRobotDynamicsParameter.M)
  return m_;
}
inline ::google::protobuf::RepeatedField< double >*
ProtoRobotDynamicsParameter::mutable_m() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.paramerter.ProtoRobotDynamicsParameter.M)
  return &m_;
}

// repeated double MXYZ = 4;
inline int ProtoRobotDynamicsParameter::mxyz_size() const {
  return mxyz_.size();
}
inline void ProtoRobotDynamicsParameter::clear_mxyz() {
  mxyz_.Clear();
}
inline double ProtoRobotDynamicsParameter::mxyz(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.paramerter.ProtoRobotDynamicsParameter.MXYZ)
  return mxyz_.Get(index);
}
inline void ProtoRobotDynamicsParameter::set_mxyz(int index, double value) {
  mxyz_.Set(index, value);
  // @@protoc_insertion_point(field_set:aubo.robot.paramerter.ProtoRobotDynamicsParameter.MXYZ)
}
inline void ProtoRobotDynamicsParameter::add_mxyz(double value) {
  mxyz_.Add(value);
  // @@protoc_insertion_point(field_add:aubo.robot.paramerter.ProtoRobotDynamicsParameter.MXYZ)
}
inline const ::google::protobuf::RepeatedField< double >&
ProtoRobotDynamicsParameter::mxyz() const {
  // @@protoc_insertion_point(field_list:aubo.robot.paramerter.ProtoRobotDynamicsParameter.MXYZ)
  return mxyz_;
}
inline ::google::protobuf::RepeatedField< double >*
ProtoRobotDynamicsParameter::mutable_mxyz() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.paramerter.ProtoRobotDynamicsParameter.MXYZ)
  return &mxyz_;
}

// repeated double IXYZ = 5;
inline int ProtoRobotDynamicsParameter::ixyz_size() const {
  return ixyz_.size();
}
inline void ProtoRobotDynamicsParameter::clear_ixyz() {
  ixyz_.Clear();
}
inline double ProtoRobotDynamicsParameter::ixyz(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.paramerter.ProtoRobotDynamicsParameter.IXYZ)
  return ixyz_.Get(index);
}
inline void ProtoRobotDynamicsParameter::set_ixyz(int index, double value) {
  ixyz_.Set(index, value);
  // @@protoc_insertion_point(field_set:aubo.robot.paramerter.ProtoRobotDynamicsParameter.IXYZ)
}
inline void ProtoRobotDynamicsParameter::add_ixyz(double value) {
  ixyz_.Add(value);
  // @@protoc_insertion_point(field_add:aubo.robot.paramerter.ProtoRobotDynamicsParameter.IXYZ)
}
inline const ::google::protobuf::RepeatedField< double >&
ProtoRobotDynamicsParameter::ixyz() const {
  // @@protoc_insertion_point(field_list:aubo.robot.paramerter.ProtoRobotDynamicsParameter.IXYZ)
  return ixyz_;
}
inline ::google::protobuf::RepeatedField< double >*
ProtoRobotDynamicsParameter::mutable_ixyz() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.paramerter.ProtoRobotDynamicsParameter.IXYZ)
  return &ixyz_;
}

// repeated double CB = 6;
inline int ProtoRobotDynamicsParameter::cb_size() const {
  return cb_.size();
}
inline void ProtoRobotDynamicsParameter::clear_cb() {
  cb_.Clear();
}
inline double ProtoRobotDynamicsParameter::cb(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.paramerter.ProtoRobotDynamicsParameter.CB)
  return cb_.Get(index);
}
inline void ProtoRobotDynamicsParameter::set_cb(int index, double value) {
  cb_.Set(index, value);
  // @@protoc_insertion_point(field_set:aubo.robot.paramerter.ProtoRobotDynamicsParameter.CB)
}
inline void ProtoRobotDynamicsParameter::add_cb(double value) {
  cb_.Add(value);
  // @@protoc_insertion_point(field_add:aubo.robot.paramerter.ProtoRobotDynamicsParameter.CB)
}
inline const ::google::protobuf::RepeatedField< double >&
ProtoRobotDynamicsParameter::cb() const {
  // @@protoc_insertion_point(field_list:aubo.robot.paramerter.ProtoRobotDynamicsParameter.CB)
  return cb_;
}
inline ::google::protobuf::RepeatedField< double >*
ProtoRobotDynamicsParameter::mutable_cb() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.paramerter.ProtoRobotDynamicsParameter.CB)
  return &cb_;
}

// -------------------------------------------------------------------

// ProtoRobotHandguidingParameter

// repeated double FP = 1;
inline int ProtoRobotHandguidingParameter::fp_size() const {
  return fp_.size();
}
inline void ProtoRobotHandguidingParameter::clear_fp() {
  fp_.Clear();
}
inline double ProtoRobotHandguidingParameter::fp(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.paramerter.ProtoRobotHandguidingParameter.FP)
  return fp_.Get(index);
}
inline void ProtoRobotHandguidingParameter::set_fp(int index, double value) {
  fp_.Set(index, value);
  // @@protoc_insertion_point(field_set:aubo.robot.paramerter.ProtoRobotHandguidingParameter.FP)
}
inline void ProtoRobotHandguidingParameter::add_fp(double value) {
  fp_.Add(value);
  // @@protoc_insertion_point(field_add:aubo.robot.paramerter.ProtoRobotHandguidingParameter.FP)
}
inline const ::google::protobuf::RepeatedField< double >&
ProtoRobotHandguidingParameter::fp() const {
  // @@protoc_insertion_point(field_list:aubo.robot.paramerter.ProtoRobotHandguidingParameter.FP)
  return fp_;
}
inline ::google::protobuf::RepeatedField< double >*
ProtoRobotHandguidingParameter::mutable_fp() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.paramerter.ProtoRobotHandguidingParameter.FP)
  return &fp_;
}

// repeated double FD = 2;
inline int ProtoRobotHandguidingParameter::fd_size() const {
  return fd_.size();
}
inline void ProtoRobotHandguidingParameter::clear_fd() {
  fd_.Clear();
}
inline double ProtoRobotHandguidingParameter::fd(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.paramerter.ProtoRobotHandguidingParameter.FD)
  return fd_.Get(index);
}
inline void ProtoRobotHandguidingParameter::set_fd(int index, double value) {
  fd_.Set(index, value);
  // @@protoc_insertion_point(field_set:aubo.robot.paramerter.ProtoRobotHandguidingParameter.FD)
}
inline void ProtoRobotHandguidingParameter::add_fd(double value) {
  fd_.Add(value);
  // @@protoc_insertion_point(field_add:aubo.robot.paramerter.ProtoRobotHandguidingParameter.FD)
}
inline const ::google::protobuf::RepeatedField< double >&
ProtoRobotHandguidingParameter::fd() const {
  // @@protoc_insertion_point(field_list:aubo.robot.paramerter.ProtoRobotHandguidingParameter.FD)
  return fd_;
}
inline ::google::protobuf::RepeatedField< double >*
ProtoRobotHandguidingParameter::mutable_fd() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.paramerter.ProtoRobotHandguidingParameter.FD)
  return &fd_;
}

// repeated double FK = 3;
inline int ProtoRobotHandguidingParameter::fk_size() const {
  return fk_.size();
}
inline void ProtoRobotHandguidingParameter::clear_fk() {
  fk_.Clear();
}
inline double ProtoRobotHandguidingParameter::fk(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.paramerter.ProtoRobotHandguidingParameter.FK)
  return fk_.Get(index);
}
inline void ProtoRobotHandguidingParameter::set_fk(int index, double value) {
  fk_.Set(index, value);
  // @@protoc_insertion_point(field_set:aubo.robot.paramerter.ProtoRobotHandguidingParameter.FK)
}
inline void ProtoRobotHandguidingParameter::add_fk(double value) {
  fk_.Add(value);
  // @@protoc_insertion_point(field_add:aubo.robot.paramerter.ProtoRobotHandguidingParameter.FK)
}
inline const ::google::protobuf::RepeatedField< double >&
ProtoRobotHandguidingParameter::fk() const {
  // @@protoc_insertion_point(field_list:aubo.robot.paramerter.ProtoRobotHandguidingParameter.FK)
  return fk_;
}
inline ::google::protobuf::RepeatedField< double >*
ProtoRobotHandguidingParameter::mutable_fk() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.paramerter.ProtoRobotHandguidingParameter.FK)
  return &fk_;
}

// repeated double FM = 4;
inline int ProtoRobotHandguidingParameter::fm_size() const {
  return fm_.size();
}
inline void ProtoRobotHandguidingParameter::clear_fm() {
  fm_.Clear();
}
inline double ProtoRobotHandguidingParameter::fm(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.paramerter.ProtoRobotHandguidingParameter.FM)
  return fm_.Get(index);
}
inline void ProtoRobotHandguidingParameter::set_fm(int index, double value) {
  fm_.Set(index, value);
  // @@protoc_insertion_point(field_set:aubo.robot.paramerter.ProtoRobotHandguidingParameter.FM)
}
inline void ProtoRobotHandguidingParameter::add_fm(double value) {
  fm_.Add(value);
  // @@protoc_insertion_point(field_add:aubo.robot.paramerter.ProtoRobotHandguidingParameter.FM)
}
inline const ::google::protobuf::RepeatedField< double >&
ProtoRobotHandguidingParameter::fm() const {
  // @@protoc_insertion_point(field_list:aubo.robot.paramerter.ProtoRobotHandguidingParameter.FM)
  return fm_;
}
inline ::google::protobuf::RepeatedField< double >*
ProtoRobotHandguidingParameter::mutable_fm() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.paramerter.ProtoRobotHandguidingParameter.FM)
  return &fm_;
}

// repeated double pos_limit = 5;
inline int ProtoRobotHandguidingParameter::pos_limit_size() const {
  return pos_limit_.size();
}
inline void ProtoRobotHandguidingParameter::clear_pos_limit() {
  pos_limit_.Clear();
}
inline double ProtoRobotHandguidingParameter::pos_limit(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.paramerter.ProtoRobotHandguidingParameter.pos_limit)
  return pos_limit_.Get(index);
}
inline void ProtoRobotHandguidingParameter::set_pos_limit(int index, double value) {
  pos_limit_.Set(index, value);
  // @@protoc_insertion_point(field_set:aubo.robot.paramerter.ProtoRobotHandguidingParameter.pos_limit)
}
inline void ProtoRobotHandguidingParameter::add_pos_limit(double value) {
  pos_limit_.Add(value);
  // @@protoc_insertion_point(field_add:aubo.robot.paramerter.ProtoRobotHandguidingParameter.pos_limit)
}
inline const ::google::protobuf::RepeatedField< double >&
ProtoRobotHandguidingParameter::pos_limit() const {
  // @@protoc_insertion_point(field_list:aubo.robot.paramerter.ProtoRobotHandguidingParameter.pos_limit)
  return pos_limit_;
}
inline ::google::protobuf::RepeatedField< double >*
ProtoRobotHandguidingParameter::mutable_pos_limit() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.paramerter.ProtoRobotHandguidingParameter.pos_limit)
  return &pos_limit_;
}

// repeated double velocity_limit = 6;
inline int ProtoRobotHandguidingParameter::velocity_limit_size() const {
  return velocity_limit_.size();
}
inline void ProtoRobotHandguidingParameter::clear_velocity_limit() {
  velocity_limit_.Clear();
}
inline double ProtoRobotHandguidingParameter::velocity_limit(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.paramerter.ProtoRobotHandguidingParameter.velocity_limit)
  return velocity_limit_.Get(index);
}
inline void ProtoRobotHandguidingParameter::set_velocity_limit(int index, double value) {
  velocity_limit_.Set(index, value);
  // @@protoc_insertion_point(field_set:aubo.robot.paramerter.ProtoRobotHandguidingParameter.velocity_limit)
}
inline void ProtoRobotHandguidingParameter::add_velocity_limit(double value) {
  velocity_limit_.Add(value);
  // @@protoc_insertion_point(field_add:aubo.robot.paramerter.ProtoRobotHandguidingParameter.velocity_limit)
}
inline const ::google::protobuf::RepeatedField< double >&
ProtoRobotHandguidingParameter::velocity_limit() const {
  // @@protoc_insertion_point(field_list:aubo.robot.paramerter.ProtoRobotHandguidingParameter.velocity_limit)
  return velocity_limit_;
}
inline ::google::protobuf::RepeatedField< double >*
ProtoRobotHandguidingParameter::mutable_velocity_limit() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.paramerter.ProtoRobotHandguidingParameter.velocity_limit)
  return &velocity_limit_;
}

// repeated double acceleration_limit = 7;
inline int ProtoRobotHandguidingParameter::acceleration_limit_size() const {
  return acceleration_limit_.size();
}
inline void ProtoRobotHandguidingParameter::clear_acceleration_limit() {
  acceleration_limit_.Clear();
}
inline double ProtoRobotHandguidingParameter::acceleration_limit(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.paramerter.ProtoRobotHandguidingParameter.acceleration_limit)
  return acceleration_limit_.Get(index);
}
inline void ProtoRobotHandguidingParameter::set_acceleration_limit(int index, double value) {
  acceleration_limit_.Set(index, value);
  // @@protoc_insertion_point(field_set:aubo.robot.paramerter.ProtoRobotHandguidingParameter.acceleration_limit)
}
inline void ProtoRobotHandguidingParameter::add_acceleration_limit(double value) {
  acceleration_limit_.Add(value);
  // @@protoc_insertion_point(field_add:aubo.robot.paramerter.ProtoRobotHandguidingParameter.acceleration_limit)
}
inline const ::google::protobuf::RepeatedField< double >&
ProtoRobotHandguidingParameter::acceleration_limit() const {
  // @@protoc_insertion_point(field_list:aubo.robot.paramerter.ProtoRobotHandguidingParameter.acceleration_limit)
  return acceleration_limit_;
}
inline ::google::protobuf::RepeatedField< double >*
ProtoRobotHandguidingParameter::mutable_acceleration_limit() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.paramerter.ProtoRobotHandguidingParameter.acceleration_limit)
  return &acceleration_limit_;
}

// -------------------------------------------------------------------

// ProtoRobotKinematicsParameter

// repeated double da = 1;
inline int ProtoRobotKinematicsParameter::da_size() const {
  return da_.size();
}
inline void ProtoRobotKinematicsParameter::clear_da() {
  da_.Clear();
}
inline double ProtoRobotKinematicsParameter::da(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.paramerter.ProtoRobotKinematicsParameter.da)
  return da_.Get(index);
}
inline void ProtoRobotKinematicsParameter::set_da(int index, double value) {
  da_.Set(index, value);
  // @@protoc_insertion_point(field_set:aubo.robot.paramerter.ProtoRobotKinematicsParameter.da)
}
inline void ProtoRobotKinematicsParameter::add_da(double value) {
  da_.Add(value);
  // @@protoc_insertion_point(field_add:aubo.robot.paramerter.ProtoRobotKinematicsParameter.da)
}
inline const ::google::protobuf::RepeatedField< double >&
ProtoRobotKinematicsParameter::da() const {
  // @@protoc_insertion_point(field_list:aubo.robot.paramerter.ProtoRobotKinematicsParameter.da)
  return da_;
}
inline ::google::protobuf::RepeatedField< double >*
ProtoRobotKinematicsParameter::mutable_da() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.paramerter.ProtoRobotKinematicsParameter.da)
  return &da_;
}

// repeated double dd = 2;
inline int ProtoRobotKinematicsParameter::dd_size() const {
  return dd_.size();
}
inline void ProtoRobotKinematicsParameter::clear_dd() {
  dd_.Clear();
}
inline double ProtoRobotKinematicsParameter::dd(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.paramerter.ProtoRobotKinematicsParameter.dd)
  return dd_.Get(index);
}
inline void ProtoRobotKinematicsParameter::set_dd(int index, double value) {
  dd_.Set(index, value);
  // @@protoc_insertion_point(field_set:aubo.robot.paramerter.ProtoRobotKinematicsParameter.dd)
}
inline void ProtoRobotKinematicsParameter::add_dd(double value) {
  dd_.Add(value);
  // @@protoc_insertion_point(field_add:aubo.robot.paramerter.ProtoRobotKinematicsParameter.dd)
}
inline const ::google::protobuf::RepeatedField< double >&
ProtoRobotKinematicsParameter::dd() const {
  // @@protoc_insertion_point(field_list:aubo.robot.paramerter.ProtoRobotKinematicsParameter.dd)
  return dd_;
}
inline ::google::protobuf::RepeatedField< double >*
ProtoRobotKinematicsParameter::mutable_dd() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.paramerter.ProtoRobotKinematicsParameter.dd)
  return &dd_;
}

// repeated double dalpha = 3;
inline int ProtoRobotKinematicsParameter::dalpha_size() const {
  return dalpha_.size();
}
inline void ProtoRobotKinematicsParameter::clear_dalpha() {
  dalpha_.Clear();
}
inline double ProtoRobotKinematicsParameter::dalpha(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.paramerter.ProtoRobotKinematicsParameter.dalpha)
  return dalpha_.Get(index);
}
inline void ProtoRobotKinematicsParameter::set_dalpha(int index, double value) {
  dalpha_.Set(index, value);
  // @@protoc_insertion_point(field_set:aubo.robot.paramerter.ProtoRobotKinematicsParameter.dalpha)
}
inline void ProtoRobotKinematicsParameter::add_dalpha(double value) {
  dalpha_.Add(value);
  // @@protoc_insertion_point(field_add:aubo.robot.paramerter.ProtoRobotKinematicsParameter.dalpha)
}
inline const ::google::protobuf::RepeatedField< double >&
ProtoRobotKinematicsParameter::dalpha() const {
  // @@protoc_insertion_point(field_list:aubo.robot.paramerter.ProtoRobotKinematicsParameter.dalpha)
  return dalpha_;
}
inline ::google::protobuf::RepeatedField< double >*
ProtoRobotKinematicsParameter::mutable_dalpha() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.paramerter.ProtoRobotKinematicsParameter.dalpha)
  return &dalpha_;
}

// repeated double dbeta = 4;
inline int ProtoRobotKinematicsParameter::dbeta_size() const {
  return dbeta_.size();
}
inline void ProtoRobotKinematicsParameter::clear_dbeta() {
  dbeta_.Clear();
}
inline double ProtoRobotKinematicsParameter::dbeta(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.paramerter.ProtoRobotKinematicsParameter.dbeta)
  return dbeta_.Get(index);
}
inline void ProtoRobotKinematicsParameter::set_dbeta(int index, double value) {
  dbeta_.Set(index, value);
  // @@protoc_insertion_point(field_set:aubo.robot.paramerter.ProtoRobotKinematicsParameter.dbeta)
}
inline void ProtoRobotKinematicsParameter::add_dbeta(double value) {
  dbeta_.Add(value);
  // @@protoc_insertion_point(field_add:aubo.robot.paramerter.ProtoRobotKinematicsParameter.dbeta)
}
inline const ::google::protobuf::RepeatedField< double >&
ProtoRobotKinematicsParameter::dbeta() const {
  // @@protoc_insertion_point(field_list:aubo.robot.paramerter.ProtoRobotKinematicsParameter.dbeta)
  return dbeta_;
}
inline ::google::protobuf::RepeatedField< double >*
ProtoRobotKinematicsParameter::mutable_dbeta() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.paramerter.ProtoRobotKinematicsParameter.dbeta)
  return &dbeta_;
}

// repeated double dratio = 5;
inline int ProtoRobotKinematicsParameter::dratio_size() const {
  return dratio_.size();
}
inline void ProtoRobotKinematicsParameter::clear_dratio() {
  dratio_.Clear();
}
inline double ProtoRobotKinematicsParameter::dratio(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.paramerter.ProtoRobotKinematicsParameter.dratio)
  return dratio_.Get(index);
}
inline void ProtoRobotKinematicsParameter::set_dratio(int index, double value) {
  dratio_.Set(index, value);
  // @@protoc_insertion_point(field_set:aubo.robot.paramerter.ProtoRobotKinematicsParameter.dratio)
}
inline void ProtoRobotKinematicsParameter::add_dratio(double value) {
  dratio_.Add(value);
  // @@protoc_insertion_point(field_add:aubo.robot.paramerter.ProtoRobotKinematicsParameter.dratio)
}
inline const ::google::protobuf::RepeatedField< double >&
ProtoRobotKinematicsParameter::dratio() const {
  // @@protoc_insertion_point(field_list:aubo.robot.paramerter.ProtoRobotKinematicsParameter.dratio)
  return dratio_;
}
inline ::google::protobuf::RepeatedField< double >*
ProtoRobotKinematicsParameter::mutable_dratio() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.paramerter.ProtoRobotKinematicsParameter.dratio)
  return &dratio_;
}

// repeated double dtheta = 6;
inline int ProtoRobotKinematicsParameter::dtheta_size() const {
  return dtheta_.size();
}
inline void ProtoRobotKinematicsParameter::clear_dtheta() {
  dtheta_.Clear();
}
inline double ProtoRobotKinematicsParameter::dtheta(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.paramerter.ProtoRobotKinematicsParameter.dtheta)
  return dtheta_.Get(index);
}
inline void ProtoRobotKinematicsParameter::set_dtheta(int index, double value) {
  dtheta_.Set(index, value);
  // @@protoc_insertion_point(field_set:aubo.robot.paramerter.ProtoRobotKinematicsParameter.dtheta)
}
inline void ProtoRobotKinematicsParameter::add_dtheta(double value) {
  dtheta_.Add(value);
  // @@protoc_insertion_point(field_add:aubo.robot.paramerter.ProtoRobotKinematicsParameter.dtheta)
}
inline const ::google::protobuf::RepeatedField< double >&
ProtoRobotKinematicsParameter::dtheta() const {
  // @@protoc_insertion_point(field_list:aubo.robot.paramerter.ProtoRobotKinematicsParameter.dtheta)
  return dtheta_;
}
inline ::google::protobuf::RepeatedField< double >*
ProtoRobotKinematicsParameter::mutable_dtheta() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.paramerter.ProtoRobotKinematicsParameter.dtheta)
  return &dtheta_;
}

// -------------------------------------------------------------------

// ProtoRobotKinematicsParameter_legacy

// repeated uint32 da = 1;
inline int ProtoRobotKinematicsParameter_legacy::da_size() const {
  return da_.size();
}
inline void ProtoRobotKinematicsParameter_legacy::clear_da() {
  da_.Clear();
}
inline ::google::protobuf::uint32 ProtoRobotKinematicsParameter_legacy::da(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.paramerter.ProtoRobotKinematicsParameter_legacy.da)
  return da_.Get(index);
}
inline void ProtoRobotKinematicsParameter_legacy::set_da(int index, ::google::protobuf::uint32 value) {
  da_.Set(index, value);
  // @@protoc_insertion_point(field_set:aubo.robot.paramerter.ProtoRobotKinematicsParameter_legacy.da)
}
inline void ProtoRobotKinematicsParameter_legacy::add_da(::google::protobuf::uint32 value) {
  da_.Add(value);
  // @@protoc_insertion_point(field_add:aubo.robot.paramerter.ProtoRobotKinematicsParameter_legacy.da)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
ProtoRobotKinematicsParameter_legacy::da() const {
  // @@protoc_insertion_point(field_list:aubo.robot.paramerter.ProtoRobotKinematicsParameter_legacy.da)
  return da_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
ProtoRobotKinematicsParameter_legacy::mutable_da() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.paramerter.ProtoRobotKinematicsParameter_legacy.da)
  return &da_;
}

// repeated uint32 dd = 2;
inline int ProtoRobotKinematicsParameter_legacy::dd_size() const {
  return dd_.size();
}
inline void ProtoRobotKinematicsParameter_legacy::clear_dd() {
  dd_.Clear();
}
inline ::google::protobuf::uint32 ProtoRobotKinematicsParameter_legacy::dd(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.paramerter.ProtoRobotKinematicsParameter_legacy.dd)
  return dd_.Get(index);
}
inline void ProtoRobotKinematicsParameter_legacy::set_dd(int index, ::google::protobuf::uint32 value) {
  dd_.Set(index, value);
  // @@protoc_insertion_point(field_set:aubo.robot.paramerter.ProtoRobotKinematicsParameter_legacy.dd)
}
inline void ProtoRobotKinematicsParameter_legacy::add_dd(::google::protobuf::uint32 value) {
  dd_.Add(value);
  // @@protoc_insertion_point(field_add:aubo.robot.paramerter.ProtoRobotKinematicsParameter_legacy.dd)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
ProtoRobotKinematicsParameter_legacy::dd() const {
  // @@protoc_insertion_point(field_list:aubo.robot.paramerter.ProtoRobotKinematicsParameter_legacy.dd)
  return dd_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
ProtoRobotKinematicsParameter_legacy::mutable_dd() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.paramerter.ProtoRobotKinematicsParameter_legacy.dd)
  return &dd_;
}

// repeated uint32 dalpha = 3;
inline int ProtoRobotKinematicsParameter_legacy::dalpha_size() const {
  return dalpha_.size();
}
inline void ProtoRobotKinematicsParameter_legacy::clear_dalpha() {
  dalpha_.Clear();
}
inline ::google::protobuf::uint32 ProtoRobotKinematicsParameter_legacy::dalpha(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.paramerter.ProtoRobotKinematicsParameter_legacy.dalpha)
  return dalpha_.Get(index);
}
inline void ProtoRobotKinematicsParameter_legacy::set_dalpha(int index, ::google::protobuf::uint32 value) {
  dalpha_.Set(index, value);
  // @@protoc_insertion_point(field_set:aubo.robot.paramerter.ProtoRobotKinematicsParameter_legacy.dalpha)
}
inline void ProtoRobotKinematicsParameter_legacy::add_dalpha(::google::protobuf::uint32 value) {
  dalpha_.Add(value);
  // @@protoc_insertion_point(field_add:aubo.robot.paramerter.ProtoRobotKinematicsParameter_legacy.dalpha)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
ProtoRobotKinematicsParameter_legacy::dalpha() const {
  // @@protoc_insertion_point(field_list:aubo.robot.paramerter.ProtoRobotKinematicsParameter_legacy.dalpha)
  return dalpha_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
ProtoRobotKinematicsParameter_legacy::mutable_dalpha() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.paramerter.ProtoRobotKinematicsParameter_legacy.dalpha)
  return &dalpha_;
}

// repeated uint32 dbeta = 4;
inline int ProtoRobotKinematicsParameter_legacy::dbeta_size() const {
  return dbeta_.size();
}
inline void ProtoRobotKinematicsParameter_legacy::clear_dbeta() {
  dbeta_.Clear();
}
inline ::google::protobuf::uint32 ProtoRobotKinematicsParameter_legacy::dbeta(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.paramerter.ProtoRobotKinematicsParameter_legacy.dbeta)
  return dbeta_.Get(index);
}
inline void ProtoRobotKinematicsParameter_legacy::set_dbeta(int index, ::google::protobuf::uint32 value) {
  dbeta_.Set(index, value);
  // @@protoc_insertion_point(field_set:aubo.robot.paramerter.ProtoRobotKinematicsParameter_legacy.dbeta)
}
inline void ProtoRobotKinematicsParameter_legacy::add_dbeta(::google::protobuf::uint32 value) {
  dbeta_.Add(value);
  // @@protoc_insertion_point(field_add:aubo.robot.paramerter.ProtoRobotKinematicsParameter_legacy.dbeta)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
ProtoRobotKinematicsParameter_legacy::dbeta() const {
  // @@protoc_insertion_point(field_list:aubo.robot.paramerter.ProtoRobotKinematicsParameter_legacy.dbeta)
  return dbeta_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
ProtoRobotKinematicsParameter_legacy::mutable_dbeta() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.paramerter.ProtoRobotKinematicsParameter_legacy.dbeta)
  return &dbeta_;
}

// repeated uint32 dratio = 5;
inline int ProtoRobotKinematicsParameter_legacy::dratio_size() const {
  return dratio_.size();
}
inline void ProtoRobotKinematicsParameter_legacy::clear_dratio() {
  dratio_.Clear();
}
inline ::google::protobuf::uint32 ProtoRobotKinematicsParameter_legacy::dratio(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.paramerter.ProtoRobotKinematicsParameter_legacy.dratio)
  return dratio_.Get(index);
}
inline void ProtoRobotKinematicsParameter_legacy::set_dratio(int index, ::google::protobuf::uint32 value) {
  dratio_.Set(index, value);
  // @@protoc_insertion_point(field_set:aubo.robot.paramerter.ProtoRobotKinematicsParameter_legacy.dratio)
}
inline void ProtoRobotKinematicsParameter_legacy::add_dratio(::google::protobuf::uint32 value) {
  dratio_.Add(value);
  // @@protoc_insertion_point(field_add:aubo.robot.paramerter.ProtoRobotKinematicsParameter_legacy.dratio)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
ProtoRobotKinematicsParameter_legacy::dratio() const {
  // @@protoc_insertion_point(field_list:aubo.robot.paramerter.ProtoRobotKinematicsParameter_legacy.dratio)
  return dratio_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
ProtoRobotKinematicsParameter_legacy::mutable_dratio() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.paramerter.ProtoRobotKinematicsParameter_legacy.dratio)
  return &dratio_;
}

// repeated uint32 dtheta = 6;
inline int ProtoRobotKinematicsParameter_legacy::dtheta_size() const {
  return dtheta_.size();
}
inline void ProtoRobotKinematicsParameter_legacy::clear_dtheta() {
  dtheta_.Clear();
}
inline ::google::protobuf::uint32 ProtoRobotKinematicsParameter_legacy::dtheta(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.paramerter.ProtoRobotKinematicsParameter_legacy.dtheta)
  return dtheta_.Get(index);
}
inline void ProtoRobotKinematicsParameter_legacy::set_dtheta(int index, ::google::protobuf::uint32 value) {
  dtheta_.Set(index, value);
  // @@protoc_insertion_point(field_set:aubo.robot.paramerter.ProtoRobotKinematicsParameter_legacy.dtheta)
}
inline void ProtoRobotKinematicsParameter_legacy::add_dtheta(::google::protobuf::uint32 value) {
  dtheta_.Add(value);
  // @@protoc_insertion_point(field_add:aubo.robot.paramerter.ProtoRobotKinematicsParameter_legacy.dtheta)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
ProtoRobotKinematicsParameter_legacy::dtheta() const {
  // @@protoc_insertion_point(field_list:aubo.robot.paramerter.ProtoRobotKinematicsParameter_legacy.dtheta)
  return dtheta_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
ProtoRobotKinematicsParameter_legacy::mutable_dtheta() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.paramerter.ProtoRobotKinematicsParameter_legacy.dtheta)
  return &dtheta_;
}

// -------------------------------------------------------------------

// ProtoRobotFrictionParameter

// repeated uint32 FL = 1;
inline int ProtoRobotFrictionParameter::fl_size() const {
  return fl_.size();
}
inline void ProtoRobotFrictionParameter::clear_fl() {
  fl_.Clear();
}
inline ::google::protobuf::uint32 ProtoRobotFrictionParameter::fl(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.paramerter.ProtoRobotFrictionParameter.FL)
  return fl_.Get(index);
}
inline void ProtoRobotFrictionParameter::set_fl(int index, ::google::protobuf::uint32 value) {
  fl_.Set(index, value);
  // @@protoc_insertion_point(field_set:aubo.robot.paramerter.ProtoRobotFrictionParameter.FL)
}
inline void ProtoRobotFrictionParameter::add_fl(::google::protobuf::uint32 value) {
  fl_.Add(value);
  // @@protoc_insertion_point(field_add:aubo.robot.paramerter.ProtoRobotFrictionParameter.FL)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
ProtoRobotFrictionParameter::fl() const {
  // @@protoc_insertion_point(field_list:aubo.robot.paramerter.ProtoRobotFrictionParameter.FL)
  return fl_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
ProtoRobotFrictionParameter::mutable_fl() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.paramerter.ProtoRobotFrictionParameter.FL)
  return &fl_;
}

// repeated uint32 FR = 2;
inline int ProtoRobotFrictionParameter::fr_size() const {
  return fr_.size();
}
inline void ProtoRobotFrictionParameter::clear_fr() {
  fr_.Clear();
}
inline ::google::protobuf::uint32 ProtoRobotFrictionParameter::fr(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.paramerter.ProtoRobotFrictionParameter.FR)
  return fr_.Get(index);
}
inline void ProtoRobotFrictionParameter::set_fr(int index, ::google::protobuf::uint32 value) {
  fr_.Set(index, value);
  // @@protoc_insertion_point(field_set:aubo.robot.paramerter.ProtoRobotFrictionParameter.FR)
}
inline void ProtoRobotFrictionParameter::add_fr(::google::protobuf::uint32 value) {
  fr_.Add(value);
  // @@protoc_insertion_point(field_add:aubo.robot.paramerter.ProtoRobotFrictionParameter.FR)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
ProtoRobotFrictionParameter::fr() const {
  // @@protoc_insertion_point(field_list:aubo.robot.paramerter.ProtoRobotFrictionParameter.FR)
  return fr_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
ProtoRobotFrictionParameter::mutable_fr() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.paramerter.ProtoRobotFrictionParameter.FR)
  return &fr_;
}

// repeated uint32 tmp_a = 3;
inline int ProtoRobotFrictionParameter::tmp_a_size() const {
  return tmp_a_.size();
}
inline void ProtoRobotFrictionParameter::clear_tmp_a() {
  tmp_a_.Clear();
}
inline ::google::protobuf::uint32 ProtoRobotFrictionParameter::tmp_a(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.paramerter.ProtoRobotFrictionParameter.tmp_a)
  return tmp_a_.Get(index);
}
inline void ProtoRobotFrictionParameter::set_tmp_a(int index, ::google::protobuf::uint32 value) {
  tmp_a_.Set(index, value);
  // @@protoc_insertion_point(field_set:aubo.robot.paramerter.ProtoRobotFrictionParameter.tmp_a)
}
inline void ProtoRobotFrictionParameter::add_tmp_a(::google::protobuf::uint32 value) {
  tmp_a_.Add(value);
  // @@protoc_insertion_point(field_add:aubo.robot.paramerter.ProtoRobotFrictionParameter.tmp_a)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
ProtoRobotFrictionParameter::tmp_a() const {
  // @@protoc_insertion_point(field_list:aubo.robot.paramerter.ProtoRobotFrictionParameter.tmp_a)
  return tmp_a_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
ProtoRobotFrictionParameter::mutable_tmp_a() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.paramerter.ProtoRobotFrictionParameter.tmp_a)
  return &tmp_a_;
}

// repeated uint32 tmp_b = 4;
inline int ProtoRobotFrictionParameter::tmp_b_size() const {
  return tmp_b_.size();
}
inline void ProtoRobotFrictionParameter::clear_tmp_b() {
  tmp_b_.Clear();
}
inline ::google::protobuf::uint32 ProtoRobotFrictionParameter::tmp_b(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.paramerter.ProtoRobotFrictionParameter.tmp_b)
  return tmp_b_.Get(index);
}
inline void ProtoRobotFrictionParameter::set_tmp_b(int index, ::google::protobuf::uint32 value) {
  tmp_b_.Set(index, value);
  // @@protoc_insertion_point(field_set:aubo.robot.paramerter.ProtoRobotFrictionParameter.tmp_b)
}
inline void ProtoRobotFrictionParameter::add_tmp_b(::google::protobuf::uint32 value) {
  tmp_b_.Add(value);
  // @@protoc_insertion_point(field_add:aubo.robot.paramerter.ProtoRobotFrictionParameter.tmp_b)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
ProtoRobotFrictionParameter::tmp_b() const {
  // @@protoc_insertion_point(field_list:aubo.robot.paramerter.ProtoRobotFrictionParameter.tmp_b)
  return tmp_b_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
ProtoRobotFrictionParameter::mutable_tmp_b() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.paramerter.ProtoRobotFrictionParameter.tmp_b)
  return &tmp_b_;
}

// repeated uint32 posvel_a1 = 5;
inline int ProtoRobotFrictionParameter::posvel_a1_size() const {
  return posvel_a1_.size();
}
inline void ProtoRobotFrictionParameter::clear_posvel_a1() {
  posvel_a1_.Clear();
}
inline ::google::protobuf::uint32 ProtoRobotFrictionParameter::posvel_a1(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.paramerter.ProtoRobotFrictionParameter.posvel_a1)
  return posvel_a1_.Get(index);
}
inline void ProtoRobotFrictionParameter::set_posvel_a1(int index, ::google::protobuf::uint32 value) {
  posvel_a1_.Set(index, value);
  // @@protoc_insertion_point(field_set:aubo.robot.paramerter.ProtoRobotFrictionParameter.posvel_a1)
}
inline void ProtoRobotFrictionParameter::add_posvel_a1(::google::protobuf::uint32 value) {
  posvel_a1_.Add(value);
  // @@protoc_insertion_point(field_add:aubo.robot.paramerter.ProtoRobotFrictionParameter.posvel_a1)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
ProtoRobotFrictionParameter::posvel_a1() const {
  // @@protoc_insertion_point(field_list:aubo.robot.paramerter.ProtoRobotFrictionParameter.posvel_a1)
  return posvel_a1_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
ProtoRobotFrictionParameter::mutable_posvel_a1() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.paramerter.ProtoRobotFrictionParameter.posvel_a1)
  return &posvel_a1_;
}

// repeated uint32 posvel_b1 = 6;
inline int ProtoRobotFrictionParameter::posvel_b1_size() const {
  return posvel_b1_.size();
}
inline void ProtoRobotFrictionParameter::clear_posvel_b1() {
  posvel_b1_.Clear();
}
inline ::google::protobuf::uint32 ProtoRobotFrictionParameter::posvel_b1(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.paramerter.ProtoRobotFrictionParameter.posvel_b1)
  return posvel_b1_.Get(index);
}
inline void ProtoRobotFrictionParameter::set_posvel_b1(int index, ::google::protobuf::uint32 value) {
  posvel_b1_.Set(index, value);
  // @@protoc_insertion_point(field_set:aubo.robot.paramerter.ProtoRobotFrictionParameter.posvel_b1)
}
inline void ProtoRobotFrictionParameter::add_posvel_b1(::google::protobuf::uint32 value) {
  posvel_b1_.Add(value);
  // @@protoc_insertion_point(field_add:aubo.robot.paramerter.ProtoRobotFrictionParameter.posvel_b1)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
ProtoRobotFrictionParameter::posvel_b1() const {
  // @@protoc_insertion_point(field_list:aubo.robot.paramerter.ProtoRobotFrictionParameter.posvel_b1)
  return posvel_b1_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
ProtoRobotFrictionParameter::mutable_posvel_b1() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.paramerter.ProtoRobotFrictionParameter.posvel_b1)
  return &posvel_b1_;
}

// repeated uint32 posvel_a2 = 7;
inline int ProtoRobotFrictionParameter::posvel_a2_size() const {
  return posvel_a2_.size();
}
inline void ProtoRobotFrictionParameter::clear_posvel_a2() {
  posvel_a2_.Clear();
}
inline ::google::protobuf::uint32 ProtoRobotFrictionParameter::posvel_a2(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.paramerter.ProtoRobotFrictionParameter.posvel_a2)
  return posvel_a2_.Get(index);
}
inline void ProtoRobotFrictionParameter::set_posvel_a2(int index, ::google::protobuf::uint32 value) {
  posvel_a2_.Set(index, value);
  // @@protoc_insertion_point(field_set:aubo.robot.paramerter.ProtoRobotFrictionParameter.posvel_a2)
}
inline void ProtoRobotFrictionParameter::add_posvel_a2(::google::protobuf::uint32 value) {
  posvel_a2_.Add(value);
  // @@protoc_insertion_point(field_add:aubo.robot.paramerter.ProtoRobotFrictionParameter.posvel_a2)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
ProtoRobotFrictionParameter::posvel_a2() const {
  // @@protoc_insertion_point(field_list:aubo.robot.paramerter.ProtoRobotFrictionParameter.posvel_a2)
  return posvel_a2_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
ProtoRobotFrictionParameter::mutable_posvel_a2() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.paramerter.ProtoRobotFrictionParameter.posvel_a2)
  return &posvel_a2_;
}

// repeated uint32 posvel_b2 = 8;
inline int ProtoRobotFrictionParameter::posvel_b2_size() const {
  return posvel_b2_.size();
}
inline void ProtoRobotFrictionParameter::clear_posvel_b2() {
  posvel_b2_.Clear();
}
inline ::google::protobuf::uint32 ProtoRobotFrictionParameter::posvel_b2(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.paramerter.ProtoRobotFrictionParameter.posvel_b2)
  return posvel_b2_.Get(index);
}
inline void ProtoRobotFrictionParameter::set_posvel_b2(int index, ::google::protobuf::uint32 value) {
  posvel_b2_.Set(index, value);
  // @@protoc_insertion_point(field_set:aubo.robot.paramerter.ProtoRobotFrictionParameter.posvel_b2)
}
inline void ProtoRobotFrictionParameter::add_posvel_b2(::google::protobuf::uint32 value) {
  posvel_b2_.Add(value);
  // @@protoc_insertion_point(field_add:aubo.robot.paramerter.ProtoRobotFrictionParameter.posvel_b2)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
ProtoRobotFrictionParameter::posvel_b2() const {
  // @@protoc_insertion_point(field_list:aubo.robot.paramerter.ProtoRobotFrictionParameter.posvel_b2)
  return posvel_b2_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
ProtoRobotFrictionParameter::mutable_posvel_b2() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.paramerter.ProtoRobotFrictionParameter.posvel_b2)
  return &posvel_b2_;
}

// repeated uint32 posvel_c2 = 9;
inline int ProtoRobotFrictionParameter::posvel_c2_size() const {
  return posvel_c2_.size();
}
inline void ProtoRobotFrictionParameter::clear_posvel_c2() {
  posvel_c2_.Clear();
}
inline ::google::protobuf::uint32 ProtoRobotFrictionParameter::posvel_c2(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.paramerter.ProtoRobotFrictionParameter.posvel_c2)
  return posvel_c2_.Get(index);
}
inline void ProtoRobotFrictionParameter::set_posvel_c2(int index, ::google::protobuf::uint32 value) {
  posvel_c2_.Set(index, value);
  // @@protoc_insertion_point(field_set:aubo.robot.paramerter.ProtoRobotFrictionParameter.posvel_c2)
}
inline void ProtoRobotFrictionParameter::add_posvel_c2(::google::protobuf::uint32 value) {
  posvel_c2_.Add(value);
  // @@protoc_insertion_point(field_add:aubo.robot.paramerter.ProtoRobotFrictionParameter.posvel_c2)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
ProtoRobotFrictionParameter::posvel_c2() const {
  // @@protoc_insertion_point(field_list:aubo.robot.paramerter.ProtoRobotFrictionParameter.posvel_c2)
  return posvel_c2_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
ProtoRobotFrictionParameter::mutable_posvel_c2() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.paramerter.ProtoRobotFrictionParameter.posvel_c2)
  return &posvel_c2_;
}

// repeated uint32 negvel_a1 = 10;
inline int ProtoRobotFrictionParameter::negvel_a1_size() const {
  return negvel_a1_.size();
}
inline void ProtoRobotFrictionParameter::clear_negvel_a1() {
  negvel_a1_.Clear();
}
inline ::google::protobuf::uint32 ProtoRobotFrictionParameter::negvel_a1(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.paramerter.ProtoRobotFrictionParameter.negvel_a1)
  return negvel_a1_.Get(index);
}
inline void ProtoRobotFrictionParameter::set_negvel_a1(int index, ::google::protobuf::uint32 value) {
  negvel_a1_.Set(index, value);
  // @@protoc_insertion_point(field_set:aubo.robot.paramerter.ProtoRobotFrictionParameter.negvel_a1)
}
inline void ProtoRobotFrictionParameter::add_negvel_a1(::google::protobuf::uint32 value) {
  negvel_a1_.Add(value);
  // @@protoc_insertion_point(field_add:aubo.robot.paramerter.ProtoRobotFrictionParameter.negvel_a1)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
ProtoRobotFrictionParameter::negvel_a1() const {
  // @@protoc_insertion_point(field_list:aubo.robot.paramerter.ProtoRobotFrictionParameter.negvel_a1)
  return negvel_a1_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
ProtoRobotFrictionParameter::mutable_negvel_a1() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.paramerter.ProtoRobotFrictionParameter.negvel_a1)
  return &negvel_a1_;
}

// repeated uint32 negvel_b1 = 11;
inline int ProtoRobotFrictionParameter::negvel_b1_size() const {
  return negvel_b1_.size();
}
inline void ProtoRobotFrictionParameter::clear_negvel_b1() {
  negvel_b1_.Clear();
}
inline ::google::protobuf::uint32 ProtoRobotFrictionParameter::negvel_b1(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.paramerter.ProtoRobotFrictionParameter.negvel_b1)
  return negvel_b1_.Get(index);
}
inline void ProtoRobotFrictionParameter::set_negvel_b1(int index, ::google::protobuf::uint32 value) {
  negvel_b1_.Set(index, value);
  // @@protoc_insertion_point(field_set:aubo.robot.paramerter.ProtoRobotFrictionParameter.negvel_b1)
}
inline void ProtoRobotFrictionParameter::add_negvel_b1(::google::protobuf::uint32 value) {
  negvel_b1_.Add(value);
  // @@protoc_insertion_point(field_add:aubo.robot.paramerter.ProtoRobotFrictionParameter.negvel_b1)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
ProtoRobotFrictionParameter::negvel_b1() const {
  // @@protoc_insertion_point(field_list:aubo.robot.paramerter.ProtoRobotFrictionParameter.negvel_b1)
  return negvel_b1_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
ProtoRobotFrictionParameter::mutable_negvel_b1() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.paramerter.ProtoRobotFrictionParameter.negvel_b1)
  return &negvel_b1_;
}

// repeated uint32 negvel_a2 = 12;
inline int ProtoRobotFrictionParameter::negvel_a2_size() const {
  return negvel_a2_.size();
}
inline void ProtoRobotFrictionParameter::clear_negvel_a2() {
  negvel_a2_.Clear();
}
inline ::google::protobuf::uint32 ProtoRobotFrictionParameter::negvel_a2(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.paramerter.ProtoRobotFrictionParameter.negvel_a2)
  return negvel_a2_.Get(index);
}
inline void ProtoRobotFrictionParameter::set_negvel_a2(int index, ::google::protobuf::uint32 value) {
  negvel_a2_.Set(index, value);
  // @@protoc_insertion_point(field_set:aubo.robot.paramerter.ProtoRobotFrictionParameter.negvel_a2)
}
inline void ProtoRobotFrictionParameter::add_negvel_a2(::google::protobuf::uint32 value) {
  negvel_a2_.Add(value);
  // @@protoc_insertion_point(field_add:aubo.robot.paramerter.ProtoRobotFrictionParameter.negvel_a2)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
ProtoRobotFrictionParameter::negvel_a2() const {
  // @@protoc_insertion_point(field_list:aubo.robot.paramerter.ProtoRobotFrictionParameter.negvel_a2)
  return negvel_a2_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
ProtoRobotFrictionParameter::mutable_negvel_a2() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.paramerter.ProtoRobotFrictionParameter.negvel_a2)
  return &negvel_a2_;
}

// repeated uint32 negvel_b2 = 13;
inline int ProtoRobotFrictionParameter::negvel_b2_size() const {
  return negvel_b2_.size();
}
inline void ProtoRobotFrictionParameter::clear_negvel_b2() {
  negvel_b2_.Clear();
}
inline ::google::protobuf::uint32 ProtoRobotFrictionParameter::negvel_b2(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.paramerter.ProtoRobotFrictionParameter.negvel_b2)
  return negvel_b2_.Get(index);
}
inline void ProtoRobotFrictionParameter::set_negvel_b2(int index, ::google::protobuf::uint32 value) {
  negvel_b2_.Set(index, value);
  // @@protoc_insertion_point(field_set:aubo.robot.paramerter.ProtoRobotFrictionParameter.negvel_b2)
}
inline void ProtoRobotFrictionParameter::add_negvel_b2(::google::protobuf::uint32 value) {
  negvel_b2_.Add(value);
  // @@protoc_insertion_point(field_add:aubo.robot.paramerter.ProtoRobotFrictionParameter.negvel_b2)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
ProtoRobotFrictionParameter::negvel_b2() const {
  // @@protoc_insertion_point(field_list:aubo.robot.paramerter.ProtoRobotFrictionParameter.negvel_b2)
  return negvel_b2_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
ProtoRobotFrictionParameter::mutable_negvel_b2() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.paramerter.ProtoRobotFrictionParameter.negvel_b2)
  return &negvel_b2_;
}

// repeated uint32 negvel_c2 = 14;
inline int ProtoRobotFrictionParameter::negvel_c2_size() const {
  return negvel_c2_.size();
}
inline void ProtoRobotFrictionParameter::clear_negvel_c2() {
  negvel_c2_.Clear();
}
inline ::google::protobuf::uint32 ProtoRobotFrictionParameter::negvel_c2(int index) const {
  // @@protoc_insertion_point(field_get:aubo.robot.paramerter.ProtoRobotFrictionParameter.negvel_c2)
  return negvel_c2_.Get(index);
}
inline void ProtoRobotFrictionParameter::set_negvel_c2(int index, ::google::protobuf::uint32 value) {
  negvel_c2_.Set(index, value);
  // @@protoc_insertion_point(field_set:aubo.robot.paramerter.ProtoRobotFrictionParameter.negvel_c2)
}
inline void ProtoRobotFrictionParameter::add_negvel_c2(::google::protobuf::uint32 value) {
  negvel_c2_.Add(value);
  // @@protoc_insertion_point(field_add:aubo.robot.paramerter.ProtoRobotFrictionParameter.negvel_c2)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
ProtoRobotFrictionParameter::negvel_c2() const {
  // @@protoc_insertion_point(field_list:aubo.robot.paramerter.ProtoRobotFrictionParameter.negvel_c2)
  return negvel_c2_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
ProtoRobotFrictionParameter::mutable_negvel_c2() {
  // @@protoc_insertion_point(field_mutable_list:aubo.robot.paramerter.ProtoRobotFrictionParameter.negvel_c2)
  return &negvel_c2_;
}

// -------------------------------------------------------------------

// ProtoRobotBaseParameter

// required .aubo.robot.paramerter.ProtoRobotInfo robotInfo = 1;
inline bool ProtoRobotBaseParameter::has_robotinfo() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ProtoRobotBaseParameter::set_has_robotinfo() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ProtoRobotBaseParameter::clear_has_robotinfo() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ProtoRobotBaseParameter::clear_robotinfo() {
  if (robotinfo_ != NULL) robotinfo_->::aubo::robot::paramerter::ProtoRobotInfo::Clear();
  clear_has_robotinfo();
}
inline const ::aubo::robot::paramerter::ProtoRobotInfo& ProtoRobotBaseParameter::robotinfo() const {
  // @@protoc_insertion_point(field_get:aubo.robot.paramerter.ProtoRobotBaseParameter.robotInfo)
  return robotinfo_ != NULL ? *robotinfo_ : *default_instance_->robotinfo_;
}
inline ::aubo::robot::paramerter::ProtoRobotInfo* ProtoRobotBaseParameter::mutable_robotinfo() {
  set_has_robotinfo();
  if (robotinfo_ == NULL) robotinfo_ = new ::aubo::robot::paramerter::ProtoRobotInfo;
  // @@protoc_insertion_point(field_mutable:aubo.robot.paramerter.ProtoRobotBaseParameter.robotInfo)
  return robotinfo_;
}
inline ::aubo::robot::paramerter::ProtoRobotInfo* ProtoRobotBaseParameter::release_robotinfo() {
  clear_has_robotinfo();
  ::aubo::robot::paramerter::ProtoRobotInfo* temp = robotinfo_;
  robotinfo_ = NULL;
  return temp;
}
inline void ProtoRobotBaseParameter::set_allocated_robotinfo(::aubo::robot::paramerter::ProtoRobotInfo* robotinfo) {
  delete robotinfo_;
  robotinfo_ = robotinfo;
  if (robotinfo) {
    set_has_robotinfo();
  } else {
    clear_has_robotinfo();
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.paramerter.ProtoRobotBaseParameter.robotInfo)
}

// required .aubo.robot.paramerter.ProtoRobotDynamicsParameter dynamicsParameter = 2;
inline bool ProtoRobotBaseParameter::has_dynamicsparameter() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void ProtoRobotBaseParameter::set_has_dynamicsparameter() {
  _has_bits_[0] |= 0x00000002u;
}
inline void ProtoRobotBaseParameter::clear_has_dynamicsparameter() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void ProtoRobotBaseParameter::clear_dynamicsparameter() {
  if (dynamicsparameter_ != NULL) dynamicsparameter_->::aubo::robot::paramerter::ProtoRobotDynamicsParameter::Clear();
  clear_has_dynamicsparameter();
}
inline const ::aubo::robot::paramerter::ProtoRobotDynamicsParameter& ProtoRobotBaseParameter::dynamicsparameter() const {
  // @@protoc_insertion_point(field_get:aubo.robot.paramerter.ProtoRobotBaseParameter.dynamicsParameter)
  return dynamicsparameter_ != NULL ? *dynamicsparameter_ : *default_instance_->dynamicsparameter_;
}
inline ::aubo::robot::paramerter::ProtoRobotDynamicsParameter* ProtoRobotBaseParameter::mutable_dynamicsparameter() {
  set_has_dynamicsparameter();
  if (dynamicsparameter_ == NULL) dynamicsparameter_ = new ::aubo::robot::paramerter::ProtoRobotDynamicsParameter;
  // @@protoc_insertion_point(field_mutable:aubo.robot.paramerter.ProtoRobotBaseParameter.dynamicsParameter)
  return dynamicsparameter_;
}
inline ::aubo::robot::paramerter::ProtoRobotDynamicsParameter* ProtoRobotBaseParameter::release_dynamicsparameter() {
  clear_has_dynamicsparameter();
  ::aubo::robot::paramerter::ProtoRobotDynamicsParameter* temp = dynamicsparameter_;
  dynamicsparameter_ = NULL;
  return temp;
}
inline void ProtoRobotBaseParameter::set_allocated_dynamicsparameter(::aubo::robot::paramerter::ProtoRobotDynamicsParameter* dynamicsparameter) {
  delete dynamicsparameter_;
  dynamicsparameter_ = dynamicsparameter;
  if (dynamicsparameter) {
    set_has_dynamicsparameter();
  } else {
    clear_has_dynamicsparameter();
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.paramerter.ProtoRobotBaseParameter.dynamicsParameter)
}

// required .aubo.robot.paramerter.ProtoRobotHandguidingParameter handguidingParameter = 3;
inline bool ProtoRobotBaseParameter::has_handguidingparameter() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void ProtoRobotBaseParameter::set_has_handguidingparameter() {
  _has_bits_[0] |= 0x00000004u;
}
inline void ProtoRobotBaseParameter::clear_has_handguidingparameter() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void ProtoRobotBaseParameter::clear_handguidingparameter() {
  if (handguidingparameter_ != NULL) handguidingparameter_->::aubo::robot::paramerter::ProtoRobotHandguidingParameter::Clear();
  clear_has_handguidingparameter();
}
inline const ::aubo::robot::paramerter::ProtoRobotHandguidingParameter& ProtoRobotBaseParameter::handguidingparameter() const {
  // @@protoc_insertion_point(field_get:aubo.robot.paramerter.ProtoRobotBaseParameter.handguidingParameter)
  return handguidingparameter_ != NULL ? *handguidingparameter_ : *default_instance_->handguidingparameter_;
}
inline ::aubo::robot::paramerter::ProtoRobotHandguidingParameter* ProtoRobotBaseParameter::mutable_handguidingparameter() {
  set_has_handguidingparameter();
  if (handguidingparameter_ == NULL) handguidingparameter_ = new ::aubo::robot::paramerter::ProtoRobotHandguidingParameter;
  // @@protoc_insertion_point(field_mutable:aubo.robot.paramerter.ProtoRobotBaseParameter.handguidingParameter)
  return handguidingparameter_;
}
inline ::aubo::robot::paramerter::ProtoRobotHandguidingParameter* ProtoRobotBaseParameter::release_handguidingparameter() {
  clear_has_handguidingparameter();
  ::aubo::robot::paramerter::ProtoRobotHandguidingParameter* temp = handguidingparameter_;
  handguidingparameter_ = NULL;
  return temp;
}
inline void ProtoRobotBaseParameter::set_allocated_handguidingparameter(::aubo::robot::paramerter::ProtoRobotHandguidingParameter* handguidingparameter) {
  delete handguidingparameter_;
  handguidingparameter_ = handguidingparameter;
  if (handguidingparameter) {
    set_has_handguidingparameter();
  } else {
    clear_has_handguidingparameter();
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.paramerter.ProtoRobotBaseParameter.handguidingParameter)
}

// required .aubo.robot.paramerter.ProtoRobotKinematicsParameter KinematicsParameter = 4;
inline bool ProtoRobotBaseParameter::has_kinematicsparameter() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void ProtoRobotBaseParameter::set_has_kinematicsparameter() {
  _has_bits_[0] |= 0x00000008u;
}
inline void ProtoRobotBaseParameter::clear_has_kinematicsparameter() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void ProtoRobotBaseParameter::clear_kinematicsparameter() {
  if (kinematicsparameter_ != NULL) kinematicsparameter_->::aubo::robot::paramerter::ProtoRobotKinematicsParameter::Clear();
  clear_has_kinematicsparameter();
}
inline const ::aubo::robot::paramerter::ProtoRobotKinematicsParameter& ProtoRobotBaseParameter::kinematicsparameter() const {
  // @@protoc_insertion_point(field_get:aubo.robot.paramerter.ProtoRobotBaseParameter.KinematicsParameter)
  return kinematicsparameter_ != NULL ? *kinematicsparameter_ : *default_instance_->kinematicsparameter_;
}
inline ::aubo::robot::paramerter::ProtoRobotKinematicsParameter* ProtoRobotBaseParameter::mutable_kinematicsparameter() {
  set_has_kinematicsparameter();
  if (kinematicsparameter_ == NULL) kinematicsparameter_ = new ::aubo::robot::paramerter::ProtoRobotKinematicsParameter;
  // @@protoc_insertion_point(field_mutable:aubo.robot.paramerter.ProtoRobotBaseParameter.KinematicsParameter)
  return kinematicsparameter_;
}
inline ::aubo::robot::paramerter::ProtoRobotKinematicsParameter* ProtoRobotBaseParameter::release_kinematicsparameter() {
  clear_has_kinematicsparameter();
  ::aubo::robot::paramerter::ProtoRobotKinematicsParameter* temp = kinematicsparameter_;
  kinematicsparameter_ = NULL;
  return temp;
}
inline void ProtoRobotBaseParameter::set_allocated_kinematicsparameter(::aubo::robot::paramerter::ProtoRobotKinematicsParameter* kinematicsparameter) {
  delete kinematicsparameter_;
  kinematicsparameter_ = kinematicsparameter;
  if (kinematicsparameter) {
    set_has_kinematicsparameter();
  } else {
    clear_has_kinematicsparameter();
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.paramerter.ProtoRobotBaseParameter.KinematicsParameter)
}

// -------------------------------------------------------------------

// ProtoRobotBaseParameter_legacy

// required .aubo.robot.paramerter.ProtoRobotInfo robotInfo = 1;
inline bool ProtoRobotBaseParameter_legacy::has_robotinfo() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ProtoRobotBaseParameter_legacy::set_has_robotinfo() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ProtoRobotBaseParameter_legacy::clear_has_robotinfo() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ProtoRobotBaseParameter_legacy::clear_robotinfo() {
  if (robotinfo_ != NULL) robotinfo_->::aubo::robot::paramerter::ProtoRobotInfo::Clear();
  clear_has_robotinfo();
}
inline const ::aubo::robot::paramerter::ProtoRobotInfo& ProtoRobotBaseParameter_legacy::robotinfo() const {
  // @@protoc_insertion_point(field_get:aubo.robot.paramerter.ProtoRobotBaseParameter_legacy.robotInfo)
  return robotinfo_ != NULL ? *robotinfo_ : *default_instance_->robotinfo_;
}
inline ::aubo::robot::paramerter::ProtoRobotInfo* ProtoRobotBaseParameter_legacy::mutable_robotinfo() {
  set_has_robotinfo();
  if (robotinfo_ == NULL) robotinfo_ = new ::aubo::robot::paramerter::ProtoRobotInfo;
  // @@protoc_insertion_point(field_mutable:aubo.robot.paramerter.ProtoRobotBaseParameter_legacy.robotInfo)
  return robotinfo_;
}
inline ::aubo::robot::paramerter::ProtoRobotInfo* ProtoRobotBaseParameter_legacy::release_robotinfo() {
  clear_has_robotinfo();
  ::aubo::robot::paramerter::ProtoRobotInfo* temp = robotinfo_;
  robotinfo_ = NULL;
  return temp;
}
inline void ProtoRobotBaseParameter_legacy::set_allocated_robotinfo(::aubo::robot::paramerter::ProtoRobotInfo* robotinfo) {
  delete robotinfo_;
  robotinfo_ = robotinfo;
  if (robotinfo) {
    set_has_robotinfo();
  } else {
    clear_has_robotinfo();
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.paramerter.ProtoRobotBaseParameter_legacy.robotInfo)
}

// required .aubo.robot.paramerter.ProtoRobotDynamicsParameter dynamicsParameter = 2;
inline bool ProtoRobotBaseParameter_legacy::has_dynamicsparameter() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void ProtoRobotBaseParameter_legacy::set_has_dynamicsparameter() {
  _has_bits_[0] |= 0x00000002u;
}
inline void ProtoRobotBaseParameter_legacy::clear_has_dynamicsparameter() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void ProtoRobotBaseParameter_legacy::clear_dynamicsparameter() {
  if (dynamicsparameter_ != NULL) dynamicsparameter_->::aubo::robot::paramerter::ProtoRobotDynamicsParameter::Clear();
  clear_has_dynamicsparameter();
}
inline const ::aubo::robot::paramerter::ProtoRobotDynamicsParameter& ProtoRobotBaseParameter_legacy::dynamicsparameter() const {
  // @@protoc_insertion_point(field_get:aubo.robot.paramerter.ProtoRobotBaseParameter_legacy.dynamicsParameter)
  return dynamicsparameter_ != NULL ? *dynamicsparameter_ : *default_instance_->dynamicsparameter_;
}
inline ::aubo::robot::paramerter::ProtoRobotDynamicsParameter* ProtoRobotBaseParameter_legacy::mutable_dynamicsparameter() {
  set_has_dynamicsparameter();
  if (dynamicsparameter_ == NULL) dynamicsparameter_ = new ::aubo::robot::paramerter::ProtoRobotDynamicsParameter;
  // @@protoc_insertion_point(field_mutable:aubo.robot.paramerter.ProtoRobotBaseParameter_legacy.dynamicsParameter)
  return dynamicsparameter_;
}
inline ::aubo::robot::paramerter::ProtoRobotDynamicsParameter* ProtoRobotBaseParameter_legacy::release_dynamicsparameter() {
  clear_has_dynamicsparameter();
  ::aubo::robot::paramerter::ProtoRobotDynamicsParameter* temp = dynamicsparameter_;
  dynamicsparameter_ = NULL;
  return temp;
}
inline void ProtoRobotBaseParameter_legacy::set_allocated_dynamicsparameter(::aubo::robot::paramerter::ProtoRobotDynamicsParameter* dynamicsparameter) {
  delete dynamicsparameter_;
  dynamicsparameter_ = dynamicsparameter;
  if (dynamicsparameter) {
    set_has_dynamicsparameter();
  } else {
    clear_has_dynamicsparameter();
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.paramerter.ProtoRobotBaseParameter_legacy.dynamicsParameter)
}

// required .aubo.robot.paramerter.ProtoRobotHandguidingParameter handguidingParameter = 3;
inline bool ProtoRobotBaseParameter_legacy::has_handguidingparameter() const {
  return (_has_bits_[0] & 0x00000004u) != 0;
}
inline void ProtoRobotBaseParameter_legacy::set_has_handguidingparameter() {
  _has_bits_[0] |= 0x00000004u;
}
inline void ProtoRobotBaseParameter_legacy::clear_has_handguidingparameter() {
  _has_bits_[0] &= ~0x00000004u;
}
inline void ProtoRobotBaseParameter_legacy::clear_handguidingparameter() {
  if (handguidingparameter_ != NULL) handguidingparameter_->::aubo::robot::paramerter::ProtoRobotHandguidingParameter::Clear();
  clear_has_handguidingparameter();
}
inline const ::aubo::robot::paramerter::ProtoRobotHandguidingParameter& ProtoRobotBaseParameter_legacy::handguidingparameter() const {
  // @@protoc_insertion_point(field_get:aubo.robot.paramerter.ProtoRobotBaseParameter_legacy.handguidingParameter)
  return handguidingparameter_ != NULL ? *handguidingparameter_ : *default_instance_->handguidingparameter_;
}
inline ::aubo::robot::paramerter::ProtoRobotHandguidingParameter* ProtoRobotBaseParameter_legacy::mutable_handguidingparameter() {
  set_has_handguidingparameter();
  if (handguidingparameter_ == NULL) handguidingparameter_ = new ::aubo::robot::paramerter::ProtoRobotHandguidingParameter;
  // @@protoc_insertion_point(field_mutable:aubo.robot.paramerter.ProtoRobotBaseParameter_legacy.handguidingParameter)
  return handguidingparameter_;
}
inline ::aubo::robot::paramerter::ProtoRobotHandguidingParameter* ProtoRobotBaseParameter_legacy::release_handguidingparameter() {
  clear_has_handguidingparameter();
  ::aubo::robot::paramerter::ProtoRobotHandguidingParameter* temp = handguidingparameter_;
  handguidingparameter_ = NULL;
  return temp;
}
inline void ProtoRobotBaseParameter_legacy::set_allocated_handguidingparameter(::aubo::robot::paramerter::ProtoRobotHandguidingParameter* handguidingparameter) {
  delete handguidingparameter_;
  handguidingparameter_ = handguidingparameter;
  if (handguidingparameter) {
    set_has_handguidingparameter();
  } else {
    clear_has_handguidingparameter();
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.paramerter.ProtoRobotBaseParameter_legacy.handguidingParameter)
}

// required .aubo.robot.paramerter.ProtoRobotKinematicsParameter_legacy KinematicsParameter = 4;
inline bool ProtoRobotBaseParameter_legacy::has_kinematicsparameter() const {
  return (_has_bits_[0] & 0x00000008u) != 0;
}
inline void ProtoRobotBaseParameter_legacy::set_has_kinematicsparameter() {
  _has_bits_[0] |= 0x00000008u;
}
inline void ProtoRobotBaseParameter_legacy::clear_has_kinematicsparameter() {
  _has_bits_[0] &= ~0x00000008u;
}
inline void ProtoRobotBaseParameter_legacy::clear_kinematicsparameter() {
  if (kinematicsparameter_ != NULL) kinematicsparameter_->::aubo::robot::paramerter::ProtoRobotKinematicsParameter_legacy::Clear();
  clear_has_kinematicsparameter();
}
inline const ::aubo::robot::paramerter::ProtoRobotKinematicsParameter_legacy& ProtoRobotBaseParameter_legacy::kinematicsparameter() const {
  // @@protoc_insertion_point(field_get:aubo.robot.paramerter.ProtoRobotBaseParameter_legacy.KinematicsParameter)
  return kinematicsparameter_ != NULL ? *kinematicsparameter_ : *default_instance_->kinematicsparameter_;
}
inline ::aubo::robot::paramerter::ProtoRobotKinematicsParameter_legacy* ProtoRobotBaseParameter_legacy::mutable_kinematicsparameter() {
  set_has_kinematicsparameter();
  if (kinematicsparameter_ == NULL) kinematicsparameter_ = new ::aubo::robot::paramerter::ProtoRobotKinematicsParameter_legacy;
  // @@protoc_insertion_point(field_mutable:aubo.robot.paramerter.ProtoRobotBaseParameter_legacy.KinematicsParameter)
  return kinematicsparameter_;
}
inline ::aubo::robot::paramerter::ProtoRobotKinematicsParameter_legacy* ProtoRobotBaseParameter_legacy::release_kinematicsparameter() {
  clear_has_kinematicsparameter();
  ::aubo::robot::paramerter::ProtoRobotKinematicsParameter_legacy* temp = kinematicsparameter_;
  kinematicsparameter_ = NULL;
  return temp;
}
inline void ProtoRobotBaseParameter_legacy::set_allocated_kinematicsparameter(::aubo::robot::paramerter::ProtoRobotKinematicsParameter_legacy* kinematicsparameter) {
  delete kinematicsparameter_;
  kinematicsparameter_ = kinematicsparameter;
  if (kinematicsparameter) {
    set_has_kinematicsparameter();
  } else {
    clear_has_kinematicsparameter();
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.paramerter.ProtoRobotBaseParameter_legacy.KinematicsParameter)
}

// -------------------------------------------------------------------

// ProtoRobotBaseParameterResponse

// required .aubo.robot.paramerter.ProtoRobotBaseParameter baseParameter = 1;
inline bool ProtoRobotBaseParameterResponse::has_baseparameter() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ProtoRobotBaseParameterResponse::set_has_baseparameter() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ProtoRobotBaseParameterResponse::clear_has_baseparameter() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ProtoRobotBaseParameterResponse::clear_baseparameter() {
  if (baseparameter_ != NULL) baseparameter_->::aubo::robot::paramerter::ProtoRobotBaseParameter::Clear();
  clear_has_baseparameter();
}
inline const ::aubo::robot::paramerter::ProtoRobotBaseParameter& ProtoRobotBaseParameterResponse::baseparameter() const {
  // @@protoc_insertion_point(field_get:aubo.robot.paramerter.ProtoRobotBaseParameterResponse.baseParameter)
  return baseparameter_ != NULL ? *baseparameter_ : *default_instance_->baseparameter_;
}
inline ::aubo::robot::paramerter::ProtoRobotBaseParameter* ProtoRobotBaseParameterResponse::mutable_baseparameter() {
  set_has_baseparameter();
  if (baseparameter_ == NULL) baseparameter_ = new ::aubo::robot::paramerter::ProtoRobotBaseParameter;
  // @@protoc_insertion_point(field_mutable:aubo.robot.paramerter.ProtoRobotBaseParameterResponse.baseParameter)
  return baseparameter_;
}
inline ::aubo::robot::paramerter::ProtoRobotBaseParameter* ProtoRobotBaseParameterResponse::release_baseparameter() {
  clear_has_baseparameter();
  ::aubo::robot::paramerter::ProtoRobotBaseParameter* temp = baseparameter_;
  baseparameter_ = NULL;
  return temp;
}
inline void ProtoRobotBaseParameterResponse::set_allocated_baseparameter(::aubo::robot::paramerter::ProtoRobotBaseParameter* baseparameter) {
  delete baseparameter_;
  baseparameter_ = baseparameter;
  if (baseparameter) {
    set_has_baseparameter();
  } else {
    clear_has_baseparameter();
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.paramerter.ProtoRobotBaseParameterResponse.baseParameter)
}

// required .aubo.robot.communication.ProtoRobotCommonResponse errorInfo = 2;
inline bool ProtoRobotBaseParameterResponse::has_errorinfo() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void ProtoRobotBaseParameterResponse::set_has_errorinfo() {
  _has_bits_[0] |= 0x00000002u;
}
inline void ProtoRobotBaseParameterResponse::clear_has_errorinfo() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void ProtoRobotBaseParameterResponse::clear_errorinfo() {
  if (errorinfo_ != NULL) errorinfo_->::aubo::robot::communication::ProtoRobotCommonResponse::Clear();
  clear_has_errorinfo();
}
inline const ::aubo::robot::communication::ProtoRobotCommonResponse& ProtoRobotBaseParameterResponse::errorinfo() const {
  // @@protoc_insertion_point(field_get:aubo.robot.paramerter.ProtoRobotBaseParameterResponse.errorInfo)
  return errorinfo_ != NULL ? *errorinfo_ : *default_instance_->errorinfo_;
}
inline ::aubo::robot::communication::ProtoRobotCommonResponse* ProtoRobotBaseParameterResponse::mutable_errorinfo() {
  set_has_errorinfo();
  if (errorinfo_ == NULL) errorinfo_ = new ::aubo::robot::communication::ProtoRobotCommonResponse;
  // @@protoc_insertion_point(field_mutable:aubo.robot.paramerter.ProtoRobotBaseParameterResponse.errorInfo)
  return errorinfo_;
}
inline ::aubo::robot::communication::ProtoRobotCommonResponse* ProtoRobotBaseParameterResponse::release_errorinfo() {
  clear_has_errorinfo();
  ::aubo::robot::communication::ProtoRobotCommonResponse* temp = errorinfo_;
  errorinfo_ = NULL;
  return temp;
}
inline void ProtoRobotBaseParameterResponse::set_allocated_errorinfo(::aubo::robot::communication::ProtoRobotCommonResponse* errorinfo) {
  delete errorinfo_;
  errorinfo_ = errorinfo;
  if (errorinfo) {
    set_has_errorinfo();
  } else {
    clear_has_errorinfo();
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.paramerter.ProtoRobotBaseParameterResponse.errorInfo)
}

// -------------------------------------------------------------------

// ProtoRobotBaseParameterResponse_legacy

// required .aubo.robot.paramerter.ProtoRobotBaseParameter_legacy baseParameter = 1;
inline bool ProtoRobotBaseParameterResponse_legacy::has_baseparameter() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ProtoRobotBaseParameterResponse_legacy::set_has_baseparameter() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ProtoRobotBaseParameterResponse_legacy::clear_has_baseparameter() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ProtoRobotBaseParameterResponse_legacy::clear_baseparameter() {
  if (baseparameter_ != NULL) baseparameter_->::aubo::robot::paramerter::ProtoRobotBaseParameter_legacy::Clear();
  clear_has_baseparameter();
}
inline const ::aubo::robot::paramerter::ProtoRobotBaseParameter_legacy& ProtoRobotBaseParameterResponse_legacy::baseparameter() const {
  // @@protoc_insertion_point(field_get:aubo.robot.paramerter.ProtoRobotBaseParameterResponse_legacy.baseParameter)
  return baseparameter_ != NULL ? *baseparameter_ : *default_instance_->baseparameter_;
}
inline ::aubo::robot::paramerter::ProtoRobotBaseParameter_legacy* ProtoRobotBaseParameterResponse_legacy::mutable_baseparameter() {
  set_has_baseparameter();
  if (baseparameter_ == NULL) baseparameter_ = new ::aubo::robot::paramerter::ProtoRobotBaseParameter_legacy;
  // @@protoc_insertion_point(field_mutable:aubo.robot.paramerter.ProtoRobotBaseParameterResponse_legacy.baseParameter)
  return baseparameter_;
}
inline ::aubo::robot::paramerter::ProtoRobotBaseParameter_legacy* ProtoRobotBaseParameterResponse_legacy::release_baseparameter() {
  clear_has_baseparameter();
  ::aubo::robot::paramerter::ProtoRobotBaseParameter_legacy* temp = baseparameter_;
  baseparameter_ = NULL;
  return temp;
}
inline void ProtoRobotBaseParameterResponse_legacy::set_allocated_baseparameter(::aubo::robot::paramerter::ProtoRobotBaseParameter_legacy* baseparameter) {
  delete baseparameter_;
  baseparameter_ = baseparameter;
  if (baseparameter) {
    set_has_baseparameter();
  } else {
    clear_has_baseparameter();
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.paramerter.ProtoRobotBaseParameterResponse_legacy.baseParameter)
}

// required .aubo.robot.communication.ProtoRobotCommonResponse errorInfo = 2;
inline bool ProtoRobotBaseParameterResponse_legacy::has_errorinfo() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void ProtoRobotBaseParameterResponse_legacy::set_has_errorinfo() {
  _has_bits_[0] |= 0x00000002u;
}
inline void ProtoRobotBaseParameterResponse_legacy::clear_has_errorinfo() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void ProtoRobotBaseParameterResponse_legacy::clear_errorinfo() {
  if (errorinfo_ != NULL) errorinfo_->::aubo::robot::communication::ProtoRobotCommonResponse::Clear();
  clear_has_errorinfo();
}
inline const ::aubo::robot::communication::ProtoRobotCommonResponse& ProtoRobotBaseParameterResponse_legacy::errorinfo() const {
  // @@protoc_insertion_point(field_get:aubo.robot.paramerter.ProtoRobotBaseParameterResponse_legacy.errorInfo)
  return errorinfo_ != NULL ? *errorinfo_ : *default_instance_->errorinfo_;
}
inline ::aubo::robot::communication::ProtoRobotCommonResponse* ProtoRobotBaseParameterResponse_legacy::mutable_errorinfo() {
  set_has_errorinfo();
  if (errorinfo_ == NULL) errorinfo_ = new ::aubo::robot::communication::ProtoRobotCommonResponse;
  // @@protoc_insertion_point(field_mutable:aubo.robot.paramerter.ProtoRobotBaseParameterResponse_legacy.errorInfo)
  return errorinfo_;
}
inline ::aubo::robot::communication::ProtoRobotCommonResponse* ProtoRobotBaseParameterResponse_legacy::release_errorinfo() {
  clear_has_errorinfo();
  ::aubo::robot::communication::ProtoRobotCommonResponse* temp = errorinfo_;
  errorinfo_ = NULL;
  return temp;
}
inline void ProtoRobotBaseParameterResponse_legacy::set_allocated_errorinfo(::aubo::robot::communication::ProtoRobotCommonResponse* errorinfo) {
  delete errorinfo_;
  errorinfo_ = errorinfo;
  if (errorinfo) {
    set_has_errorinfo();
  } else {
    clear_has_errorinfo();
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.paramerter.ProtoRobotBaseParameterResponse_legacy.errorInfo)
}

// -------------------------------------------------------------------

// ProtoRobotJointsParameter

// required .aubo.robot.paramerter.ProtoRobotFrictionParameter frictionParameter = 1;
inline bool ProtoRobotJointsParameter::has_frictionparameter() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ProtoRobotJointsParameter::set_has_frictionparameter() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ProtoRobotJointsParameter::clear_has_frictionparameter() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ProtoRobotJointsParameter::clear_frictionparameter() {
  if (frictionparameter_ != NULL) frictionparameter_->::aubo::robot::paramerter::ProtoRobotFrictionParameter::Clear();
  clear_has_frictionparameter();
}
inline const ::aubo::robot::paramerter::ProtoRobotFrictionParameter& ProtoRobotJointsParameter::frictionparameter() const {
  // @@protoc_insertion_point(field_get:aubo.robot.paramerter.ProtoRobotJointsParameter.frictionParameter)
  return frictionparameter_ != NULL ? *frictionparameter_ : *default_instance_->frictionparameter_;
}
inline ::aubo::robot::paramerter::ProtoRobotFrictionParameter* ProtoRobotJointsParameter::mutable_frictionparameter() {
  set_has_frictionparameter();
  if (frictionparameter_ == NULL) frictionparameter_ = new ::aubo::robot::paramerter::ProtoRobotFrictionParameter;
  // @@protoc_insertion_point(field_mutable:aubo.robot.paramerter.ProtoRobotJointsParameter.frictionParameter)
  return frictionparameter_;
}
inline ::aubo::robot::paramerter::ProtoRobotFrictionParameter* ProtoRobotJointsParameter::release_frictionparameter() {
  clear_has_frictionparameter();
  ::aubo::robot::paramerter::ProtoRobotFrictionParameter* temp = frictionparameter_;
  frictionparameter_ = NULL;
  return temp;
}
inline void ProtoRobotJointsParameter::set_allocated_frictionparameter(::aubo::robot::paramerter::ProtoRobotFrictionParameter* frictionparameter) {
  delete frictionparameter_;
  frictionparameter_ = frictionparameter;
  if (frictionparameter) {
    set_has_frictionparameter();
  } else {
    clear_has_frictionparameter();
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.paramerter.ProtoRobotJointsParameter.frictionParameter)
}

// -------------------------------------------------------------------

// ProtoRobotJointsParameterResponse

// required .aubo.robot.paramerter.ProtoRobotJointsParameter jointsParameter = 1;
inline bool ProtoRobotJointsParameterResponse::has_jointsparameter() const {
  return (_has_bits_[0] & 0x00000001u) != 0;
}
inline void ProtoRobotJointsParameterResponse::set_has_jointsparameter() {
  _has_bits_[0] |= 0x00000001u;
}
inline void ProtoRobotJointsParameterResponse::clear_has_jointsparameter() {
  _has_bits_[0] &= ~0x00000001u;
}
inline void ProtoRobotJointsParameterResponse::clear_jointsparameter() {
  if (jointsparameter_ != NULL) jointsparameter_->::aubo::robot::paramerter::ProtoRobotJointsParameter::Clear();
  clear_has_jointsparameter();
}
inline const ::aubo::robot::paramerter::ProtoRobotJointsParameter& ProtoRobotJointsParameterResponse::jointsparameter() const {
  // @@protoc_insertion_point(field_get:aubo.robot.paramerter.ProtoRobotJointsParameterResponse.jointsParameter)
  return jointsparameter_ != NULL ? *jointsparameter_ : *default_instance_->jointsparameter_;
}
inline ::aubo::robot::paramerter::ProtoRobotJointsParameter* ProtoRobotJointsParameterResponse::mutable_jointsparameter() {
  set_has_jointsparameter();
  if (jointsparameter_ == NULL) jointsparameter_ = new ::aubo::robot::paramerter::ProtoRobotJointsParameter;
  // @@protoc_insertion_point(field_mutable:aubo.robot.paramerter.ProtoRobotJointsParameterResponse.jointsParameter)
  return jointsparameter_;
}
inline ::aubo::robot::paramerter::ProtoRobotJointsParameter* ProtoRobotJointsParameterResponse::release_jointsparameter() {
  clear_has_jointsparameter();
  ::aubo::robot::paramerter::ProtoRobotJointsParameter* temp = jointsparameter_;
  jointsparameter_ = NULL;
  return temp;
}
inline void ProtoRobotJointsParameterResponse::set_allocated_jointsparameter(::aubo::robot::paramerter::ProtoRobotJointsParameter* jointsparameter) {
  delete jointsparameter_;
  jointsparameter_ = jointsparameter;
  if (jointsparameter) {
    set_has_jointsparameter();
  } else {
    clear_has_jointsparameter();
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.paramerter.ProtoRobotJointsParameterResponse.jointsParameter)
}

// required .aubo.robot.communication.ProtoRobotCommonResponse errorInfo = 2;
inline bool ProtoRobotJointsParameterResponse::has_errorinfo() const {
  return (_has_bits_[0] & 0x00000002u) != 0;
}
inline void ProtoRobotJointsParameterResponse::set_has_errorinfo() {
  _has_bits_[0] |= 0x00000002u;
}
inline void ProtoRobotJointsParameterResponse::clear_has_errorinfo() {
  _has_bits_[0] &= ~0x00000002u;
}
inline void ProtoRobotJointsParameterResponse::clear_errorinfo() {
  if (errorinfo_ != NULL) errorinfo_->::aubo::robot::communication::ProtoRobotCommonResponse::Clear();
  clear_has_errorinfo();
}
inline const ::aubo::robot::communication::ProtoRobotCommonResponse& ProtoRobotJointsParameterResponse::errorinfo() const {
  // @@protoc_insertion_point(field_get:aubo.robot.paramerter.ProtoRobotJointsParameterResponse.errorInfo)
  return errorinfo_ != NULL ? *errorinfo_ : *default_instance_->errorinfo_;
}
inline ::aubo::robot::communication::ProtoRobotCommonResponse* ProtoRobotJointsParameterResponse::mutable_errorinfo() {
  set_has_errorinfo();
  if (errorinfo_ == NULL) errorinfo_ = new ::aubo::robot::communication::ProtoRobotCommonResponse;
  // @@protoc_insertion_point(field_mutable:aubo.robot.paramerter.ProtoRobotJointsParameterResponse.errorInfo)
  return errorinfo_;
}
inline ::aubo::robot::communication::ProtoRobotCommonResponse* ProtoRobotJointsParameterResponse::release_errorinfo() {
  clear_has_errorinfo();
  ::aubo::robot::communication::ProtoRobotCommonResponse* temp = errorinfo_;
  errorinfo_ = NULL;
  return temp;
}
inline void ProtoRobotJointsParameterResponse::set_allocated_errorinfo(::aubo::robot::communication::ProtoRobotCommonResponse* errorinfo) {
  delete errorinfo_;
  errorinfo_ = errorinfo;
  if (errorinfo) {
    set_has_errorinfo();
  } else {
    clear_has_errorinfo();
  }
  // @@protoc_insertion_point(field_set_allocated:aubo.robot.paramerter.ProtoRobotJointsParameterResponse.errorInfo)
}


// @@protoc_insertion_point(namespace_scope)

}  // namespace paramerter
}  // namespace robot
}  // namespace aubo

#ifndef SWIG
namespace google {
namespace protobuf {


}  // namespace google
}  // namespace protobuf
#endif  // SWIG

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_robotParameters_2eproto__INCLUDED
