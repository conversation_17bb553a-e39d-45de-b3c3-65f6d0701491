// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: robotmovecondition.proto

#ifndef PROTOBUF_robotmovecondition_2eproto__INCLUDED
#define PROTOBUF_robotmovecondition_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 2006000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 2006001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/generated_enum_reflection.h>
// @@protoc_insertion_point(includes)

namespace aubo {
namespace robot {
namespace movecondition {

// Internal implementation detail -- do not call these.
void  protobuf_AddDesc_robotmovecondition_2eproto();
void protobuf_AssignDesc_robotmovecondition_2eproto();
void protobuf_ShutdownFile_robotmovecondition_2eproto();


enum robot_state {
  RobotStopped = 0,
  RobotRunning = 1,
  RobotPaused = 2,
  RobotResumed = 3
};
bool robot_state_IsValid(int value);
const robot_state robot_state_MIN = RobotStopped;
const robot_state robot_state_MAX = RobotResumed;
const int robot_state_ARRAYSIZE = robot_state_MAX + 1;

const ::google::protobuf::EnumDescriptor* robot_state_descriptor();
inline const ::std::string& robot_state_Name(robot_state value) {
  return ::google::protobuf::internal::NameOfEnum(
    robot_state_descriptor(), value);
}
inline bool robot_state_Parse(
    const ::std::string& name, robot_state* value) {
  return ::google::protobuf::internal::ParseNamedEnum<robot_state>(
    robot_state_descriptor(), name, value);
}
enum move_mode {
  NO_MOVEMODE = 0,
  MODEJ = 1,
  MODEL = 2,
  MODEP = 3
};
bool move_mode_IsValid(int value);
const move_mode move_mode_MIN = NO_MOVEMODE;
const move_mode move_mode_MAX = MODEP;
const int move_mode_ARRAYSIZE = move_mode_MAX + 1;

const ::google::protobuf::EnumDescriptor* move_mode_descriptor();
inline const ::std::string& move_mode_Name(move_mode value) {
  return ::google::protobuf::internal::NameOfEnum(
    move_mode_descriptor(), value);
}
inline bool move_mode_Parse(
    const ::std::string& name, move_mode* value) {
  return ::google::protobuf::internal::ParseNamedEnum<move_mode>(
    move_mode_descriptor(), name, value);
}
enum move_track {
  NO_TRACK = 0,
  TRACKING = 1,
  ARC_CIR = 2,
  CARTESIAN_MOVEP = 3,
  CARTESIAN_CUBICSPLINE = 4,
  CARTESIAN_UBSPLINEINTP = 5,
  CARTESIAN_GNUBSPLINEINTP = 6,
  CARTESIAN_LOOKAHEAD = 7,
  JIONT_CUBICSPLINE = 8,
  JOINT_UBSPLINEINTP = 9,
  JOINT_GNUBSPLINEINTP = 10
};
bool move_track_IsValid(int value);
const move_track move_track_MIN = NO_TRACK;
const move_track move_track_MAX = JOINT_GNUBSPLINEINTP;
const int move_track_ARRAYSIZE = move_track_MAX + 1;

const ::google::protobuf::EnumDescriptor* move_track_descriptor();
inline const ::std::string& move_track_Name(move_track value) {
  return ::google::protobuf::internal::NameOfEnum(
    move_track_descriptor(), value);
}
inline bool move_track_Parse(
    const ::std::string& name, move_track* value) {
  return ::google::protobuf::internal::ParseNamedEnum<move_track>(
    move_track_descriptor(), name, value);
}
enum coordinate_refer {
  BaseCoordinate = 0,
  EndCoordinate = 1,
  WorldCoordinate = 2
};
bool coordinate_refer_IsValid(int value);
const coordinate_refer coordinate_refer_MIN = BaseCoordinate;
const coordinate_refer coordinate_refer_MAX = WorldCoordinate;
const int coordinate_refer_ARRAYSIZE = coordinate_refer_MAX + 1;

const ::google::protobuf::EnumDescriptor* coordinate_refer_descriptor();
inline const ::std::string& coordinate_refer_Name(coordinate_refer value) {
  return ::google::protobuf::internal::NameOfEnum(
    coordinate_refer_descriptor(), value);
}
inline bool coordinate_refer_Parse(
    const ::std::string& name, coordinate_refer* value) {
  return ::google::protobuf::internal::ParseNamedEnum<coordinate_refer>(
    coordinate_refer_descriptor(), name, value);
}
enum teach_mode {
  NO_TEACH = 0,
  JOINT1 = 1,
  JOINT2 = 2,
  JOINT3 = 3,
  JOINT4 = 4,
  JOINT5 = 5,
  JOINT6 = 6,
  MOV_X = 7,
  MOV_Y = 8,
  MOV_Z = 9,
  ROT_X = 10,
  ROT_Y = 11,
  ROT_Z = 12
};
bool teach_mode_IsValid(int value);
const teach_mode teach_mode_MIN = NO_TEACH;
const teach_mode teach_mode_MAX = ROT_Z;
const int teach_mode_ARRAYSIZE = teach_mode_MAX + 1;

const ::google::protobuf::EnumDescriptor* teach_mode_descriptor();
inline const ::std::string& teach_mode_Name(teach_mode value) {
  return ::google::protobuf::internal::NameOfEnum(
    teach_mode_descriptor(), value);
}
inline bool teach_mode_Parse(
    const ::std::string& name, teach_mode* value) {
  return ::google::protobuf::internal::ParseNamedEnum<teach_mode>(
    teach_mode_descriptor(), name, value);
}
// ===================================================================


// ===================================================================


// ===================================================================


// @@protoc_insertion_point(namespace_scope)

}  // namespace movecondition
}  // namespace robot
}  // namespace aubo

#ifndef SWIG
namespace google {
namespace protobuf {

template <> struct is_proto_enum< ::aubo::robot::movecondition::robot_state> : ::google::protobuf::internal::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::aubo::robot::movecondition::robot_state>() {
  return ::aubo::robot::movecondition::robot_state_descriptor();
}
template <> struct is_proto_enum< ::aubo::robot::movecondition::move_mode> : ::google::protobuf::internal::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::aubo::robot::movecondition::move_mode>() {
  return ::aubo::robot::movecondition::move_mode_descriptor();
}
template <> struct is_proto_enum< ::aubo::robot::movecondition::move_track> : ::google::protobuf::internal::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::aubo::robot::movecondition::move_track>() {
  return ::aubo::robot::movecondition::move_track_descriptor();
}
template <> struct is_proto_enum< ::aubo::robot::movecondition::coordinate_refer> : ::google::protobuf::internal::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::aubo::robot::movecondition::coordinate_refer>() {
  return ::aubo::robot::movecondition::coordinate_refer_descriptor();
}
template <> struct is_proto_enum< ::aubo::robot::movecondition::teach_mode> : ::google::protobuf::internal::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::aubo::robot::movecondition::teach_mode>() {
  return ::aubo::robot::movecondition::teach_mode_descriptor();
}

}  // namespace google
}  // namespace protobuf
#endif  // SWIG

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_robotmovecondition_2eproto__INCLUDED
