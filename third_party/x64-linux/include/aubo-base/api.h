//
// Copyright ©2015-2024 Aubo (Beijing) Robotics Technology Co., Ltd. All rights reserved.
//

#ifndef AUBO_API_H
#define AUBO_API_H

#if defined(AUBO_STATIC)

#define AUBO_API
#define AUBO_API_HIDDEN

#elif defined(_WIN32) || defined(__CYGWIN__)

  #if defined(AUBO_BUILD_DLL)
    #ifdef __GNUC__
      #define AUBO_API __attribute__((dllexport))
    #else
      #define AUBO_API __declspec(dllexport)
    #endif
  #else
    #ifdef __GNUC__
      #define AUBO_API __attribute__((dllimport))
    #else
      #define AUBO_API __declspec(dllimport)
    #endif
  #endif

  #define AUBO_API_HIDDEN

#else

  #if __GNUC__ >= 4
    #define AUBO_API    __attribute__((visibility("default")))
    #define AUBO_API_HIDDEN __attribute__((visibility("hidden")))
  #else
    #define AUBO_API
    #define AUBO_API_HIDDEN
  #endif

#endif

#endif // AUBO_API_H
