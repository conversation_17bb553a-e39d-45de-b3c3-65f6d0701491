//
// Copyright ©2015-2024 Aubo (Beijing) Robotics Technology Co., Ltd. All rights reserved.
//

#ifndef AUBO_EULER_ANGLES_H
#define AUBO_EULER_ANGLES_H

#include <string>

#include <aubo-base/api.h>
#include <aubo-base/constants.h>
#include <nlohmann/json.hpp>

namespace aubo {

// 前置声明
class Quaternion;

/**
 * @struct EulerAngles
 * @brief 表示空间中的欧拉角（Euler angles），旋转顺序是roll-pitch-yaw, 即先绕X轴，再绕Y轴，最后绕Z轴。
 *
 * 欧拉角用三个角度描述一个物体在三维空间中的方向。这些角度以弧度为单位。
 * 它们描述了物体绕其自身的主轴的旋转。常见的旋转顺序是X-Y-Z，即先绕X轴，再绕Y轴，最后绕Z轴。
 * 为了避免"万向节锁"（gimbal lock）的问题，有时会使用四元数代替欧拉角。
 */
class AUBO_API EulerAngles {

public:
    double roll;    ///< 绕X轴的旋转角度，称为横滚角 (Roll)。单位：弧度，取值范围为[-π, π]。
    double pitch;   ///< 绕Y轴的旋转角度，称为俯仰角 (Pitch)。单位：弧度，取值范围为[-π/2, π/2]。
    double yaw;     ///< 绕Z轴的旋转角度，称为偏航角 (Yaw)。单位：弧度，取值范围为[-π, π]。

    /**
     * @brief 默认构造函数，初始化所有角度为0。
     */
    EulerAngles() : roll(0.0), pitch(0.0), yaw(0.0) {}

    /**
     * @brief 参数化构造函数，用于设置roll, pitch, yaw的初值（单位：弧度）。
     *
     * @param r 绕X轴的横滚角（单位：弧度，取值范围为[-π, π]）。
     * @param p 绕Y轴的俯仰角（单位：弧度，取值范围为[-π/2, π/2]）。
     * @param y 绕Z轴的偏航角（单位：弧度，取值范围为[-π, π]）。
     */
    EulerAngles(double r, double p, double y) : roll(r), pitch(p), yaw(y) {}

    /**
     * @brief 静态方法，用于使用度数单位设置roll, pitch, yaw的初值，并转换为弧度。
     *
     * @param r_deg 绕X轴的横滚角（单位：度，取值范围为[-180, 180]）。
     * @param p_deg 绕Y轴的俯仰角（单位：度，取值范围为[-90, 90]）。
     * @param y_deg 绕Z轴的偏航角（单位：度，取值范围为[-180, 180]）。
     * @return 一个新的EulerAngle对象，其角度值已转换为弧度。
     */
    static EulerAngles from_degrees(double r_deg, double p_deg, double y_deg) {
        return {r_deg * DEG_TO_RAD, p_deg * DEG_TO_RAD, y_deg * DEG_TO_RAD};
    }

    /**
     * @brief 将欧拉角转换为四元数表示的方法。
     *
     * @note 该方法使用roll-pitch-yaw顺序，即先绕X轴，再绕Y轴，最后绕Z轴。
     *
     * @return 四元素
     */
    [[nodiscard]] Quaternion to_quaternion() const;

    /**
     * @brief 将欧拉角转换为JSON表示的方法。
     *
     * @return JSON对象
     */
    [[nodiscard]] nlohmann::json to_json() const;

    /**
     * @brief 将欧拉角转换为JSON表示的方法。
     *
     * @param j JSON对象
     */
    void to_json(nlohmann::json& j) const;

    /**
     * @brief 从JSON对象中解析欧拉角
     *
     * @param j JSON对象
     */
    void from_json(const nlohmann::json& j);
};

} // namespace aubo

namespace nlohmann {

inline void to_json(nlohmann::json& j, const aubo::EulerAngles& e) {
    j = e.to_json();
}

inline void from_json(const nlohmann::json& j, aubo::EulerAngles& e) {
    e.from_json(j);
}

} // namespace nlohmann

#endif //AUBO_EULER_ANGLES_H