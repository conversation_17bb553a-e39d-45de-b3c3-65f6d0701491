//
// Copyright ©2015-2024 Aubo (Beijing) Robotics Technology Co., Ltd. All rights reserved.
//

#ifndef AUBO_LOG_H
#define AUBO_LOG_H

#include <iostream>
#include <memory>
#include <string>
#include <string_view>
#include <mutex>
#include <type_traits>
#include <fmt/format.h>
#include <fmt/compile.h>
#include <aubo-base/api.h>

// 检查 fmt 版本，提供兼容性支持
#if FMT_VERSION >= 90000  // fmt 9.0.0+
    #define AUBO_FMT_STRING(s) FMT_STRING(s)
    #define AUBO_FMT_RUNTIME(s) fmt::runtime(s)
#else
    #define AUBO_FMT_STRING(s) s
    #define AUBO_FMT_RUNTIME(s) s
#endif

// 安全的日志宏，支持编译时格式字符串检查
#define AUBO_LOG(level, format_str, ...)                                    \
    do {                                                                    \
        try {                                                               \
            aubo::Log::instance()->log(level, format_str, ##__VA_ARGS__);   \
        } catch (const std::exception& e) {                                 \
            std::cerr << "[LOG] Format error: " << e.what() << std::endl;   \
        } catch (...) {                                                     \
            std::cerr << "[LOG] Unknown error in logging" << std::endl;     \
        }                                                                   \
    } while (0)

#define LOG_DEBUG(format_str, ...) AUBO_LOG(aubo::Log::Level::Debug, format_str, ##__VA_ARGS__)
#define LOG_INFO(format_str, ...)  AUBO_LOG(aubo::Log::Level::Info,  format_str, ##__VA_ARGS__)
#define LOG_WARN(format_str, ...)  AUBO_LOG(aubo::Log::Level::Warn,  format_str, ##__VA_ARGS__)
#define LOG_ERROR(format_str, ...) AUBO_LOG(aubo::Log::Level::Error, format_str, ##__VA_ARGS__)
#define LOG_FATAL(format_str, ...) AUBO_LOG(aubo::Log::Level::Fatal, format_str, ##__VA_ARGS__)

namespace aubo {

/**
 * @brief 封装日志系统，提供分级日志记录功能，支持使用 fmt 库进行字符串格式化。
 */
class AUBO_API Log {
public:
    /**
     * @brief 日志级别枚举，定义了不同的日志消息严重性级别。
     */
    enum class Level {
        Debug,   ///< 用于详细的调试信息。
        Info,    ///< 记录常规信息。
        Warn,    ///< 记录潜在的错误信息。
        Error,   ///< 记录明确的错误信息。
        Fatal    ///< 记录致命错误信息。
    };

    /**
     * @brief 获取全局唯一的日志实例（单例）。
     * @return 日志实例（单例）。
     */
    static std::shared_ptr<Log> instance();

    /**
     * @brief 销毁日志实例。
     */
    static void destroy();

    /**
     * @brief 构造函数。
     */
    Log();

    /**
     * @brief 析构函数。
     */
    virtual ~Log();

    /**
     * @brief 初始化日志系统。
     * @param log_dir 日志文件目录。
     * @param log_name 日志文件名称。
     * @param level 日志级别。
     * @param output_console 是否输出到控制台。
     *
     * 若同样的 log_dir 和 log_name 已经配置过，则默认不会重复配置。
     */
    void init(const std::string& log_dir = "logs",
              const std::string& log_name = "aubo.log",
#ifndef NDEBUG
              Level level = Level::Debug,
              bool output_console = true
#else
              Level level = Level::Info,
              bool output_console = false
#endif
    );

    /**
     * @brief 设置日志的优先级级别。
     * @param priority 日志级别（枚举）。
     */
    void set_priority(Level priority);

    /**
     * @brief 设置日志的优先级级别（字符串形式）。
     * @param priority 日志级别支持："debug", "info", "warn", "error", "fatal"（大小写不敏感）。
     */
    void set_priority(const std::string& priority);

    /**
     * @brief 记录调试级别的格式化信息。
     * @param fmt_str 格式化字符串。
     * @param args 格式化参数。
     */
    template<typename FormatString, typename... Args>
    void debug(FormatString&& fmt_str, Args&&... args) {
        log(Level::Debug, std::forward<FormatString>(fmt_str), std::forward<Args>(args)...);
    }

    /**
     * @brief 记录信息级别的格式化信息。
     */
    template<typename FormatString, typename... Args>
    void info(FormatString&& fmt_str, Args&&... args) {
        log(Level::Info, std::forward<FormatString>(fmt_str), std::forward<Args>(args)...);
    }

    /**
     * @brief 记录警告级别的格式化信息。
     */
    template<typename FormatString, typename... Args>
    void warn(FormatString&& fmt_str, Args&&... args) {
        log(Level::Warn, std::forward<FormatString>(fmt_str), std::forward<Args>(args)...);
    }

    /**
     * @brief 记录错误级别的格式化信息。
     */
    template<typename FormatString, typename... Args>
    void error(FormatString&& fmt_str, Args&&... args) {
        log(Level::Error, std::forward<FormatString>(fmt_str), std::forward<Args>(args)...);
    }

    /**
     * @brief 记录致命级别的格式化信息。
     */
    template<typename FormatString, typename... Args>
    void fatal(FormatString&& fmt_str, Args&&... args) {
        log(Level::Fatal, std::forward<FormatString>(fmt_str), std::forward<Args>(args)...);
    }

    /**
     * @brief 记录指定级别的格式化信息（兼容 fmt 10+）
     * @param level 日志级别。
     * @param fmt_str 格式化字符串。
     * @param args 格式化参数。
     */
    template<typename FormatString, typename... Args>
    void log(Level level, FormatString&& fmt_str, Args&&... args) {
        try {
            if constexpr (sizeof...(args) == 0) {
                // 无参数的情况，直接传递字符串到非模板版本
                if constexpr (std::is_convertible_v<FormatString, std::string>) {
                    log_string(level, std::string(fmt_str));
                } else {
                    log_string(level, "Invalid format string type");
                }
            } else {
                // 有参数的情况，使用 fmt::format
                auto formatted = fmt::format(std::forward<FormatString>(fmt_str), std::forward<Args>(args)...);
                log_string(level, formatted);
            }
        } catch (const fmt::format_error& e) {
            std::cerr << "[LOG] Format error: " << e.what() << std::endl;
        } catch (const std::exception& e) {
            std::cerr << "[LOG] Logging error: " << e.what() << std::endl;
        }
    }

private:
    /**
     * @brief 内部字符串日志实现，避免模板递归
     * @param level 日志级别
     * @param message 日志消息
     */
    void log_string(Level level, const std::string& message);

    Log(const Log&) = delete;
    Log& operator=(const Log&) = delete;

    static std::shared_ptr<Log> instance_;

    // 内部实现类
    class LogImpl;
    std::unique_ptr<LogImpl> impl_;
};

} // namespace aubo

#endif // AUBO_LOG_H