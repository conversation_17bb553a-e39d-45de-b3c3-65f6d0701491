//
// Copyright ©2015-2024 Aubo (Beijing) Robotics Technology Co., Ltd. All rights reserved.
//

#ifndef AUBO_ACUPOINT_H
#define AUBO_ACUPOINT_H

#include <iostream>

#include <aubo-base/api.h>
#include <aubo-base/point.h>
#include <nlohmann/json.hpp>

namespace aubo {

/**
 * @brief 穴位信息模板类
 */
template<typename PointT>
class AUBO_API Acupoint {
public:
    /**
     * @brief 默认构造函数
     */
    Acupoint() : name_(""), point_() {}

    /**
     * @brief 构造函数
     * @param point 穴位位置
     */
    Acupoint(const PointT &point) : name_(""), point_(point) {}

    /**
     * @brief 构造函数
     * @param name 穴位名称
     * @param point 穴位位置
     */
    Acupoint(const std::string &name, const PointT &point)
            : name_(name), point_(point) {}

    /**
     * @brief 获取穴位名称
     *
     * @return 穴位名称
     */
    [[nodiscard]]
    std::string name() const {
        return name_;
    }

    /**
     * @brief 设置穴位名称
     *
     * @param name 穴位名称
     */
    void set_name(const std::string &name) {
        name_ = name;
    }

    /**
     * @brief 获取穴位位置
     *
     * @return 穴位位置
     */
    [[nodiscard]]
    PointT point() const {
        return point_;
    }

    /**
     * @brief 设置穴位位置
     *
     * @param point 穴位位置
     */
    void set_point(const PointT &point) {
        point_ = point;
    }

    /**
     * @brief 数据是否有效
     *
     * @return 是否有效
     */
    [[nodiscard]]
    bool is_valid() const {
        if (point_ == PointT()) {
            // 无法直接使用LOG_WARN，因为这是头文件
            return false;
        }
        return true;
    }

    /**
     * @brief 将穴位信息转换为 JSON 对象
     *
     * @return JSON 对象
     */
    [[nodiscard]]
    nlohmann::json to_json() const {
        try {
            nlohmann::json j;
            to_json(j);
            return j;
        } catch (const std::exception& e) {
            // 无法直接使用LOG_ERROR，因为这是头文件
            return nlohmann::json();
        }
    }

    /**
     * @brief 将穴位信息转换为 JSON 对象
     *
     * @param j JSON 对象
     */
    void to_json(nlohmann::json& j) const {
        try {
            j["name"] = name_;
            j["point"] = point_;
        } catch (const std::exception& e) {
            // 无法直接使用LOG_ERROR，因为这是头文件
        }
    }

    /**
     * @brief 从 JSON 对象解析穴位信息
     *
     * @param j JSON 对象
     */
    void from_json(const nlohmann::json& j) {
        try {
            name_ = j.contains("name") && j["name"].is_string()
                        ? j["name"].get<std::string>()
                        : "";
            if (j.contains("point") && j["point"].is_object()) {
                point_.from_json(j["point"]);
            } else {
                point_ = PointT{};
            }
        } catch (const std::exception& e) {
            // 无法直接使用LOG_ERROR，因为这是头文件
        }
    }

private:
    std::string name_;  ///< 穴位名称
    PointT point_;      ///< 穴位位置
};

// 常用类型别名
using Acupoint2d = Acupoint<Point2d>;
using Acupoint3d = Acupoint<Point3d>;
using Acupoint2i = Acupoint<Point2i>;

} // namespace aubo

namespace nlohmann {

template<typename PointT>
inline void to_json(nlohmann::json &j, const aubo::Acupoint<PointT> &acupoint) {
    j = acupoint.to_json();
}

template<typename PointT>
inline void from_json(const nlohmann::json &j, aubo::Acupoint<PointT> &acupoint) {
    acupoint.from_json(j);
}

} // namespace nlohmann

#endif //AUBO_ACUPOINT_H