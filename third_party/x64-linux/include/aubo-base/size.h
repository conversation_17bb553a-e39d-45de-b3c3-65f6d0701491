//
// Copyright ©2015-2024 Aubo (Beijing) Robotics Technology Co., Ltd. All rights reserved.
//

#ifndef AUBO_SIZE_H
#define AUBO_SIZE_H

#include <cassert>
#include <limits>
#include <string>

#include <nlohmann/json.hpp>

#include <aubo-base/api.h>
#include <aubo-base/log.h>

namespace aubo {

/// 定义一个模板类Size，用于表示尺寸，具有宽度和高度属性
template<typename T,
         typename = std::enable_if_t<std::is_same<T, int>::value ||
                                     std::is_same<T, float>::value ||
                                     std::is_same<T, double>::value>>
class AUBO_API Size {
public:
    typedef T value_type; ///< 定义一个类型别名value_type，表示尺寸的数据类型

    T width;    ///< 宽度
    T height;   ///< 高度

    /// 默认构造函数，初始化宽度和高度为0
    Size() : width(0), height(0) {}

    /// 参数化构造函数，通过给定的宽度和高度初始化Size对象
    Size(T _width, T _height) : width(_width), height(_height) {}

    /// 拷贝构造函数，默认实现
    Size(const Size &sz) = default;

    /// 析构函数，默认实现
    virtual ~Size() = default;

    /// 计算并返回尺寸的面积
    T area() const {
        const T result = width * height;
        // 断言检查，确保结果在数值范围内，特别是对于整数类型
        assert(!std::numeric_limits<T>::is_integer || width == 0 || result / width == height);
        return result;
    }

    /// 计算并返回宽高比
    [[nodiscard]]
    double aspect_ratio() const {
        return width / static_cast<double>(height);
    }

    /// 检查尺寸是否为空（宽度或高度<=0）
    [[nodiscard]]
    bool empty() const {
        return width <= 0 || height <= 0;
    }

    /// 重载 + 运算符
    Size<T> operator+(const Size<T> &right) const {
        return {width + right.width, height + right.height};
    }

    /// 重载 + 运算符
    Size<T> operator+(T right) const {
        return {width + right, height + right};
    }

    /// 重载 - 运算符
    Size<T> operator-(const Size<T> &right) const {
        return {width - right.width, height - right.height};
    }

    /// 重载 - 运算符
    Size<T> operator-(T right) const {
        return {width - right, height - right};
    }

    /// 重载 * 运算符
    Size<T> operator*(const Size<T> &right) const {
        return {width * right.width, height * right.height};
    }

    /// 重载 * 运算符
    Size<T> operator*(T right) const {
        return {width * right, height * right};
    }

    /// 重载 / 运算符
    Size<T> operator/(const Size<T> &right) const {
        return {width / right.width, height / right.height};
    }

    /// 重载 / 运算符
    Size<T> operator/(T right) const {
        return {width / right, height / right};
    }

    /// 重载 == 运算符
    bool operator==(const Size<T> &right) const {
        return width == right.width && height == right.height;
    }

    /// 重载 != 运算符
    bool operator!=(const Size<T> &right) const {
        return width != right.width || height != right.height;
    }

    /// 重载 = 运算符，进行拷贝赋值
    Size<T>& operator=(const Size<T> &cpy) {
        if (&cpy != this) {
            width = cpy.width;
            height = cpy.height;
        }
        return *this; // 应返回当前对象的引用
    }

    /// 转换为JSON
    [[nodiscard]]
    nlohmann::json to_json() const {
        try {
            nlohmann::json j;
            to_json(j);
            return j;
        } catch (const std::exception& e) {
            LOG_ERROR("[Size] 转换为JSON时发生错误: {}", e.what());
            return nlohmann::json();
        }
    }

    /// 转换为JSON
    void to_json(nlohmann::json& j) const {
        try {
            j["width"] = width;
            j["height"] = height;
        } catch (const std::exception& e) {
            LOG_ERROR("[Size] 转换为JSON时发生错误: {}", e.what());
        }
    }

    /// 从JSON中解析尺寸
    void from_json(const nlohmann::json& j) {
        try {
            width = (j.contains("width") && j["width"].is_number())
                        ? j["width"].get<T>()
                        : 0;
            height = (j.contains("height") && j["height"].is_number())
                        ? j["height"].get<T>()
                        : 0;
        } catch (const std::exception& e) {
            LOG_ERROR("[Size] 从JSON解析时发生错误: {}", e.what());
        }
    }
};

using Size2i = Size<int>;    ///< 用整数表示的二维尺寸
using Size2f = Size<float>;  ///< 用浮点数表示的二维尺寸
using Size2d = Size<double>; ///< 用双精度浮点数表示的二维尺寸

} // namespace aubo

namespace nlohmann {

inline void to_json(nlohmann::json& j, const aubo::Size2i& s) {
    j = s.to_json();
}

inline void from_json(const nlohmann::json& j, aubo::Size2i& s) {
    s.from_json(j);
}

inline void to_json(nlohmann::json& j, const aubo::Size2f& s) {
    j = s.to_json();
}

inline void from_json(const nlohmann::json& j, aubo::Size2f& s) {
    s.from_json(j);
}

inline void to_json(nlohmann::json& j, const aubo::Size2d& s) {
    j = s.to_json();
}

inline void from_json(const nlohmann::json& j, aubo::Size2d& s) {
    s.from_json(j);
}

} // namespace nlohmann

#endif //AUBO_SIZE_H
