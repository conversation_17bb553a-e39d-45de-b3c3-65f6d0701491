//
// Copyright ©2015-2024 Aubo (Beijing) Robotics Technology Co., Ltd. All rights reserved.
//

#ifndef AUBO_POSE_H
#define AUBO_POSE_H

#include <string>
#include <utility>

#include <nlohmann/json.hpp>

#include <aubo-base/constants.h>
#include <aubo-base/api.h>
#include <aubo-base/point.h>
#include <aubo-base/quaternion.h>

namespace aubo {

/**
 * @brief 位姿结构体，包括位置和四元数
 */
class AUBO_API Pose {
public:
    Position position;        ///< 位置信息
    Quaternion quaternion;    ///< 四元数表示的旋转信息

    /**
     * @brief 默认构造函数
     *
     * 默认构造函数创建一个Pose结构体实例，并将位置和旋转初始化为零。
     */
    Pose() = default;

    /**
     * @brief 构造函数
     *
     * 构造函数创建一个Pose结构体实例，并根据提供的位置和旋转值进行初始化。
     * @param position 位置信息
     * @param quaternion 四元数表示的旋转信息
     */
    Pose(const Position &position, const Quaternion &quaternion)
            : position(position), quaternion(quaternion) {}

    /**
     * @brief 构造函数
     *
     * 构造函数创建一个Pose结构体实例，并根据提供的位置和欧拉角进行初始化。
     * @param position 位置信息
     * @param euler_angles 欧拉角表示的旋转信息
     */
    Pose(const Position &position, const EulerAngles &euler_angles)
            : position(position),
              quaternion(euler_angles.to_quaternion()) {}

    /**
     * @brief 重载*运算符，用于位姿的乘法运算。
     *
     * 将一个位姿转换为另一个坐标系中的位姿可以通过将两个位姿相乘来实现。
     *
     * @param right 右操作数
     * @return 位姿的乘积
     */
    Pose operator*(const Pose &right) const;

    /**
     * @brief 重载 = 运算符，用于位姿的赋值运算。
     *
     * @param right 右操作数
     * @return 位姿的赋值
     */
    Pose &operator=(const Pose &right) {
        if (&right != this) {
            position = right.position;
            quaternion = right.quaternion;
        }
        return *this;
    }

    /**
     * @brief 计算位姿的逆
     *
     * @return 位姿的逆
     */
    Pose inverse() const;

    /**
     * @brief 转换为JSON
     *
     * @return JSON对象
     */
    [[nodiscard]]
    nlohmann::json to_json() const;

    /**
     * @brief 从JSON中解析位姿
     *
     * @param j JSON对象
     */
    void from_json(const nlohmann::json& j);
};

} // namespace aubo

namespace nlohmann {

inline void to_json(nlohmann::json& j, const aubo::Pose& pose) {
    j = pose.to_json();
}

inline void from_json(const nlohmann::json& j, aubo::Pose& pose) {
    pose.from_json(j);
}

} // namespace nlohmann

#endif //AUBO_POSE_H