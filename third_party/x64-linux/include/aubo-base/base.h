//
// Copyright ©2015-2024 <PERSON>bo (Beijing) Robotics Technology Co., Ltd. All rights reserved.
//

#ifndef AUBO_BASE_H
#define AUBO_BASE_H

#include <aubo-base/acupoint.h>
#include <aubo-base/api.h>
#include <aubo-base/base64.h>
#include <aubo-base/constants.h>
#include <aubo-base/euler_angles.h>
#include <aubo-base/image.h>
#include <aubo-base/image_depth_info.h>
#include <aubo-base/log.h>
#include <aubo-base/math.h>
#include <aubo-base/point.h>
#include <aubo-base/pose.h>
#include <aubo-base/quaternion.h>
#include <aubo-base/size.h>
#include <aubo-base/unique_id.h>
#include <aubo-base/vec.h>
#include <aubo-base/version.h>

#endif //AUBO_BASE_H