//
// Copyright ©2015-2024 Aubo (Beijing) Robotics Technology Co., Ltd. All rights reserved.
//

#ifndef AUBO_UNIQUE_ID_H
#define AUBO_UNIQUE_ID_H

#include <string>

#include <aubo-base/api.h>


namespace aubo {

/**
 * @brief 唯一ID生成器
 */
class AUBO_API UniqueID {
public:
    /**
     * @brief 生成唯一ID
     * @return 唯一ID
     */
    static std::string generate();
};

} // namespace aubo

#endif //AUBO_UNIQUE_ID_H
