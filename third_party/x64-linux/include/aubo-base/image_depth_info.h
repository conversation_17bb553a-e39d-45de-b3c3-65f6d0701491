//
// Copyright ©2015-2024 Aubo (Beijing) Robotics Technology Co., Ltd. All rights reserved.
//

#ifndef AUBO_IMAGE_DEPTH_INFO_H
#define AUBO_IMAGE_DEPTH_INFO_H

#include <functional>
#include <memory>
#include <stdexcept>
#include <vector>

#include <aubo-base/api.h>
#include <aubo-base/point.h>
#include <nlohmann/json.hpp>

namespace aubo {

/**
 * @brief 图像深度信息
 */
class AUBO_API ImageDepthInfo {
public:
    /**
     * @brief 构造函数
     */
    ImageDepthInfo() = default;

    /**
     * @brief 构造函数
     *
     * @param width 深度信息的宽度
     * @param height 深度信息的高度
     * @param depth_info 深度信息。每个像素点对应一个空间点坐标，单位：米。注意：排列顺序是从左到右，从上到下。
     */
    ImageDepthInfo(int width, int height, const std::shared_ptr<std::vector<Point3d>> &depth_info)
            : width_(width), height_(height), image_depth_info_(depth_info) {

        if (depth_info == nullptr) {
            throw std::invalid_argument("depth info is null");
        }

        if (width_ * height_ != static_cast<int>(image_depth_info_->size())) {
            throw std::invalid_argument("invalid depth info");
        }
    }

    /**
     * @brief 构造函数
     *
     * @param width 深度信息的宽度
     * @param height 深度信息的高度
     * @param path 深度信息文件的路径
     * @return 如果成功加载文件，则返回true；如果打开文件失败或读取数据失败，则返回false。
     *
     * @note 此函数打开一个指定路径的文件，并从中读取图像的深度信息。
     *       每行代表一个点的深度数据，格式为 "x,y,z"。
     */
    ImageDepthInfo(int width, int height, const std::string &path)
            : width_(width), height_(height) {
        load_from_file(width, height, path);
    }

    /**
     * @brief 构造函数
     *
     * @param pcd_file 深度信息文件的路径
     * @return 如果成功加载文件，则返回true；如果打开文件失败或读取数据失败，则返回false。
     */
    explicit ImageDepthInfo(const std::string &pcd_file) {
        load_from_pcd(pcd_file);
    }

    /**
     * @brief 获取深度信息的宽度
     *
     * @return 深度信息的宽度（列数）
     */
    [[nodiscard]]
    int width() const { return width_; }

    /**
     * @brief 获取深度信息的高度
     *
     * @return 深度图像的高度（行数）
     */
    [[nodiscard]]
    int height() const { return height_; }

    /**
     * @brief 获取图像的深度信息
     *
     * @return 每个像素点的空间点坐标，单位：米。注意：排列顺序是从左到右，从上到下。
     */
    [[nodiscard]]
    std::shared_ptr<std::vector<Point3d>> depth_info() const { return image_depth_info_; }

    /**
     * @brief 获取图像的深度信息
     *
     * @param row 行索引
     * @param col 列索引
     * @return 空间点坐标，单位：米
     */
    Point3d &operator()(int row, int col) const {
        if (!check_index(row, col)) {
            throw std::out_of_range(
                    "index out of range. row = " + std::to_string(row) + ", col = " + std::to_string(col) + ", width = " + std::to_string(width_) + ", height = " + std::to_string(height_));
        }
        return image_depth_info_->at(row * width_ + col);
    }

    /**
     * @brief 获取图像的深度信息
     *
     * @param row 行索引
     * @param col 列索引
     * @return 空间点坐标，单位：米
     */
    const Point3d &operator()(int row, int col) {
        if (!check_index(row, col)) {
            throw std::out_of_range(
                    "index out of range. row = " + std::to_string(row) + ", col = " + std::to_string(col) + ", width = " + std::to_string(width_) + ", height = " + std::to_string(height_));
        }
        return image_depth_info_->at(row * width_ + col);
    }

    /**
     * @brief 获取图像的深度信息
     *
     * @param row 行索引
     * @param col 列索引
     * @return 空间点坐标，单位：米
     */
    [[nodiscard]]
    Point3d &at(int row, int col) const {
        return operator()(row, col);
    }

    /**
     * @brief 获取图像的深度信息
     *
     * @param row 行索引
     * @param col 列索引
     * @return 空间点坐标，单位：米
     */
    const Point3d &at(int row, int col) {
        return operator()(row, col);
    }

    /**
     * @brief 保存深度信息到文件
     *
     * @param path 要保存的文件的路径
     * @return 如果成功保存文件，则返回true；如果打开文件失败或写入数据失败，则返回false。
     *
     * @note 此函数将图像的深度信息保存到指定路径的文件中，格式为PCD
     *
     * @note PCD文件格式：
     *      # .PCD v0.7 - Point Cloud Data file format
     *      VERSION 0.7
     *      FIELDS x y z
     *      SIZE 4 4 4
     *      TYPE F F F
     *      COUNT 1 1 1
     *      WIDTH 640
     *      HEIGHT 480
     *      VIEWPOINT 0 0 0 1 0 0 0
     *      POINTS 307200
     *      DATA ascii
     *      0.0 0.0 0.0
     *      0.0 0.0 0.0
     *      ...
     */
    [[nodiscard]]
    bool save_to_file(const std::string &path) const;

    /**
     * @brief 从PCD文件加载图像深度信息
     *
     * @param path 要加载的文件的路径
     */
    void load_from_pcd(const std::string &path);

    /**
     * @brief 异步保存深度信息到文件
     *
     * @param path 要保存的文件的路径
     * @param callback 保存完成后的回调函数，参数为保存是否成功
     */
    void async_save_to_file(const std::string &path, std::function<void(bool)> callback = nullptr) const;

    /**
     * @brief 序列化
     *
     * @return 序列化后的json
     */
    [[nodiscard]]
    nlohmann::json to_json() const;

    /**
     * @brief 序列化
     *
     * @param j 序列化后的json
     */
    void to_json(nlohmann::json &j) const;

    /**
     * @brief 反序列化
     *
     * @param json 序列化后的json
     */
    void from_json(const nlohmann::json &json);

private:
    /// 校验索引是否合法
    [[nodiscard]]
    bool check_index(int row, int col) const {
        return row < height_ && col < width_;
    }

    /**
     * @brief 从文件中加载图像深度信息。
     *
     * 此函数打开一个指定路径的文件，并从中读取图像的深度信息。
     * 每行代表一个点的深度数据，格式为 "x,y,z"。
     *
     * @param width 图像的宽度。
     * @param height 图像的高度。
     * @param path 要加载的文件的路径。
     * @return 如果成功加载文件，则返回true；如果打开文件失败或读取数据失败，则返回false。
     */
    bool load_from_file(int width, int height, const std::string &path);

private:
    /// 深度图像的宽度
    int width_{0};

    /// 深度图像的高度
    int height_{0};

    /// 图像深度的数据, 每个像素点的深度信息。即空间点坐标，单位：米
    std::shared_ptr<std::vector<Point3d>> image_depth_info_;
};

} // namespace aubo

namespace nlohmann {

inline void to_json(nlohmann::json &j, const aubo::ImageDepthInfo &depth_info) {
    j = depth_info.to_json();
}

inline void from_json(const nlohmann::json &j, aubo::ImageDepthInfo &depth_info) {
    depth_info.from_json(j);
}

} // namespace nlohmann

#endif //AUBO_IMAGE_DEPTH_INFO_H