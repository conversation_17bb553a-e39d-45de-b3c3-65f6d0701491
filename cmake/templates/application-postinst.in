#!/bin/bash

# Enable strict error handling
set -e
set -o pipefail

# Ensure necessary directories exist
mkdir -p ~/Desktop
mkdir -p /usr/share/applications

# Copy desktop entry to the system applications menu
echo "Copying desktop entry to /usr/share/applications/"

if desktop-file-install --dir=/usr/share/applications /opt/@PROJECT_NAME@/share/applications/@PROJECT_NAME@.desktop; then
    echo "Installed desktop entry: /usr/share/applications/@PROJECT_NAME@.desktop"
else
    echo "Error: Failed to install desktop entry" >&2
    exit 1
fi

# Copy desktop entry to the user's desktop
install -m 644 /opt/@PROJECT_NAME@/share/applications/@PROJECT_NAME@.desktop ~/Desktop/@PROJECT_NAME@.desktop

# Grant execute permission only in GNOME environment
if [[ $XDG_CURRENT_DESKTOP == "GNOME" ]]; then
    chmod +x ~/Desktop/@PROJECT_NAME@.desktop
fi
echo "Copied to Desktop: ~/Desktop/@PROJECT_NAME@.desktop"

# Update desktop database if the command exists
if command -v update-desktop-database &> /dev/null; then
    update-desktop-database /usr/share/applications
else
    echo "Warning: update-desktop-database not found, skipping update" >&2
fi

# Remove the specific .desktop file but do not delete the entire applications directory immediately
if [ -f "/opt/@PROJECT_NAME@/share/applications/@PROJECT_NAME@.desktop" ]; then
    rm -f /opt/@PROJECT_NAME@/share/applications/@PROJECT_NAME@.desktop
    echo "Removed: /opt/@PROJECT_NAME@/share/applications/@PROJECT_NAME@.desktop"
fi

# If the applications directory is empty, remove it
if [ -d "/opt/@PROJECT_NAME@/share/applications" ] && [ -z "$(ls -A /opt/@PROJECT_NAME@/share/applications)" ]; then
    rmdir /opt/@PROJECT_NAME@/share/applications
    echo "Removed empty directory: /opt/@PROJECT_NAME@/share/applications"
fi

echo "Installation of @PROJECT_NAME@ completed"
