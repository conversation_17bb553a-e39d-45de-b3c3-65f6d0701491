#!/bin/bash

# Enable strict error handling
set -e
set -o pipefail

echo "Starting installation of @PROJECT_NAME@ service..."

mkdir -p /etc/systemd/system/

echo "Copying service file to /etc/systemd/system/"
if ! cp /opt/@PROJECT_NAME@/systemd/@PROJECT_NAME@.service /etc/systemd/system/; then
    echo "Error: Failed to copy service file" >&2
    exit 1
fi

echo "Reloading systemd configuration..."
if ! systemctl daemon-reload; then
    echo "Warning: systemctl daemon-reload command failed" >&2
fi

echo "Enabling @PROJECT_NAME@ service..."
if ! systemctl enable @PROJECT_NAME@.service; then
    echo "Error: Failed to enable service" >&2
    exit 1
fi

echo "Starting @PROJECT_NAME@ service..."
if ! systemctl start @PROJECT_NAME@.service; then
    echo "Error: Failed to start service" >&2
    exit 1
fi

if [ -d "/opt/@PROJECT_NAME@/systemd" ]; then
    echo "Cleaning up temporary files..."
    rm -rf /opt/@PROJECT_NAME@/systemd
fi

echo "Installation of @PROJECT_NAME@ completed"
