# Copyright ©2015-2024 Aubo (Beijing) Robotics Technology Co., Ltd. All rights reserved.

message(STATUS "Configuring CPack for packaging...")

# Set basic package information
set(CPACK_PACKAGE_NAME "${PROJECT_NAME}")
set(CPACK_PACKAGE_VERSION ${PROJECT_VERSION})
set(CPACK_PACKAGE_DESCRIPTION ${PROJECT_DESCRIPTION})
set(CPACK_PACKAGE_VENDOR "AUBO")

if(BUILD_EXECUTABLE)
    message(STATUS "Project is an executable")

    set(CPACK_GENERATOR "DEB")
    set(CPACK_PACKAGE_ARCHITECTURE ${AUBO_SYSTEM_ARCH_DEBIAN})
    set(CPACK_PACKAGING_INSTALL_PREFIX ${CMAKE_INSTALL_PREFIX})

    set(CPACK_DEBIAN_PACKAGE_NAME ${PROJECT_NAME})
    set(CPACK_DEBIAN_PACKAGE_VERSION ${PROJECT_VERSION})
    set(CPACK_DE<PERSON>AN_PACKAGE_ARCHITECTURE ${CP<PERSON>K_PACKAGE_ARCHITECTURE})
    set(CPAC<PERSON>_DEBIAN_PACKAGE_MAINTAINER "AUBO")
    set(CPACK_DEBIAN_PACKAGE_SECTION "utils")
    set(CPACK_DEBIAN_PACKAGE_DESCRIPTION "AUBO Moxibustion Example")
    set(CPACK_DEBIAN_PACKAGE_CONTROL_EXTRA "share/debian/postinst")

    set(CPACK_PACKAGE_FILE_NAME "${PROJECT_NAME}_${PROJECT_VERSION}_${AUBO_SYSTEM_ARCH_DEBIAN}")

else()
    message(STATUS "Project is a library")

    set(CPACK_GENERATOR "TGZ")
    set(CPACK_PACKAGE_ARCHITECTURE ${AUBO_SYSTEM_ARCH})

    set(CPACK_PACKAGE_FILE_NAME "${PROJECT_NAME}_${PROJECT_VERSION}_${AUBO_SYSTEM}")
endif()

# Include CPack
include(CPack)