# Copyright ©2015-2024 Aubo (Beijing) Robotics Technology Co., Ltd. All rights reserved.

# Function to install a dependency library and its configurations
function(install_dependency_library TARGET_NAME)
    # Extract pure name without namespace (e.g., "fmt" from "fmt::fmt")
    if(TARGET_NAME MATCHES "::")
        string(REGEX REPLACE ".*::" "" _lib_name "${TARGET_NAME}")
    else()
        set(_lib_name "${TARGET_NAME}")
    endif()

    message(STATUS "Install dependency library: ${_lib_name}")

    # Install library file
    install(
        FILES $<TARGET_FILE:${TARGET_NAME}>
        DESTINATION lib
    )

    # Get include directories
    get_target_property(_include_dirs ${TARGET_NAME} INTERFACE_INCLUDE_DIRECTORIES)
    set(_found_include FALSE)

    foreach(_dir ${_include_dirs})
        message(STATUS "Checking include directory: ${_dir}")
        if(EXISTS "${_dir}/${_lib_name}")
            set(_include_dir "${_dir}")
            set(_found_include TRUE)
            break()
        endif()
    endforeach()

    if(NOT _found_include)
        message(FATAL_ERROR "Cannot find ${_lib_name} headers in any of the include directories. The ${_lib_name} package may not be installed correctly.")
    endif()

    message(STATUS "Using include directory: ${_include_dir}/${_lib_name}")

    # Install headers
    install(
        DIRECTORY "${_include_dir}/${_lib_name}"
        DESTINATION include
        FILES_MATCHING
            PATTERN "*.h"
            PATTERN "*.hpp"
            PATTERN "*.hxx"
            PATTERN "*.inl"
    )

    # Check if CMake configs exist
    if(NOT DEFINED ${_lib_name}_DIR)
        message(FATAL_ERROR "Cannot find ${_lib_name}_DIR. The ${_lib_name} package may not be installed correctly.")
    endif()

    # Get installation path from config directory
    string(FIND "${${_lib_name}_DIR}" "/share/cmake/" _share_cmake_pos)
    string(FIND "${${_lib_name}_DIR}" "/share/" _share_pos)
    string(FIND "${${_lib_name}_DIR}" "/lib/cmake/" _lib_cmake_pos)

    if(_share_cmake_pos GREATER -1)
        math(EXPR _path_start "${_share_cmake_pos} + 1")
        string(SUBSTRING "${${_lib_name}_DIR}" ${_path_start} -1 _install_path)
    elseif(_share_pos GREATER -1)
        math(EXPR _path_start "${_share_pos} + 1")
        string(SUBSTRING "${${_lib_name}_DIR}" ${_path_start} -1 _install_path)
    elseif(_lib_cmake_pos GREATER -1)
        math(EXPR _path_start "${_lib_cmake_pos} + 1")
        string(SUBSTRING "${${_lib_name}_DIR}" ${_path_start} -1 _install_path)
    else()
        message(FATAL_ERROR "Cannot determine installation path for ${_lib_name}. Config dir: ${${_lib_name}_DIR}")
    endif()

    # Install CMake configs
    install(
        DIRECTORY "${${_lib_name}_DIR}/"
        DESTINATION "${_install_path}"
        FILES_MATCHING
            PATTERN "${_lib_name}-*.cmake"
    )

    # Clean up temporary variables
    unset(_lib_name)
    unset(_include_dirs)
    unset(_found_include)
    unset(_include_dir)
    unset(_share_cmake_pos)
    unset(_share_pos)
    unset(_lib_cmake_pos)
    unset(_path_start)
    unset(_install_path)
endfunction()

if(BUILD_EXECUTABLE)
    # Install prefix
    set(CMAKE_INSTALL_PREFIX /opt/${PROJECT_NAME})
endif()

# Install targets
if(NOT BUILD_EXECUTABLE)
    install(TARGETS ${PROJECT_NAME}
        EXPORT ${PROJECT_NAME}-targets
        LIBRARY DESTINATION lib
        ARCHIVE DESTINATION lib
        RUNTIME DESTINATION bin
        INCLUDES DESTINATION include
    )
else()
    install(TARGETS ${PROJECT_NAME}
        LIBRARY DESTINATION lib
        ARCHIVE DESTINATION lib
        RUNTIME DESTINATION bin
    )
endif()


# Install third_party
set(_thirdparty_dir ${CMAKE_CURRENT_LIST_DIR}/../../third_party/${AUBO_SYSTEM})
if(NOT BUILD_EXECUTABLE)
    if(EXISTS ${_thirdparty_dir})
        install(DIRECTORY
            ${_thirdparty_dir}/
            DESTINATION .
        )
    endif()
else()
    # Install lib
    if(EXISTS ${_thirdparty_dir}/lib)
        install(DIRECTORY
            ${_thirdparty_dir}/lib/
            DESTINATION lib
            PATTERN "cmake" EXCLUDE
        )
    endif()

    # Install config
    if(EXISTS ${_thirdparty_dir}/share/config)
        install(DIRECTORY
            ${_thirdparty_dir}/share/config/
            DESTINATION share/config
        )
    endif()

    # Install models
    if(EXISTS ${_thirdparty_dir}/share/models)
        install(DIRECTORY
            ${_thirdparty_dir}/share/models/
            DESTINATION share/models
        )
    endif()

    # Install sounds
    if(EXISTS ${_thirdparty_dir}/share/sounds)
        install(DIRECTORY
            ${_thirdparty_dir}/share/sounds/
            DESTINATION share/sounds
        )
    endif()
endif()
unset(_thirdparty_dir)

if(NOT BUILD_EXECUTABLE)

    # Install headers
    install(DIRECTORY include/ DESTINATION include)

    # Install CMake configuration
    install(FILES
        "${CMAKE_CURRENT_BINARY_DIR}/${PROJECT_NAME}-config.cmake"
        "${CMAKE_CURRENT_BINARY_DIR}/${PROJECT_NAME}-config-version.cmake"
        DESTINATION lib/cmake/${PROJECT_NAME}
    )

    # Install CMake targets
    install(EXPORT ${PROJECT_NAME}-targets
        FILE ${PROJECT_NAME}-targets.cmake
        NAMESPACE aubo::
        DESTINATION lib/cmake/${PROJECT_NAME}
    )
endif()

# Handle libstdc++.so.6 from current system
execute_process(
    COMMAND "${CMAKE_CXX_COMPILER}" -print-file-name=libstdc++.so.6
    OUTPUT_VARIABLE STDCPP_SO6_PATH
    OUTPUT_STRIP_TRAILING_WHITESPACE
)

if(STDCPP_SO6_PATH AND EXISTS "${STDCPP_SO6_PATH}")
    execute_process(
        COMMAND readlink -f "${STDCPP_SO6_PATH}"
        OUTPUT_VARIABLE STDCPP_REAL
        OUTPUT_STRIP_TRAILING_WHITESPACE
    )

    if(STDCPP_REAL AND EXISTS "${STDCPP_REAL}")
        message(STATUS "Found system libstdc++: ${STDCPP_REAL}")

        # Clean old versions and install system version during installation
        install(CODE "
            # Get the version of the system libstdc++
            string(REGEX MATCH \"libstdc\\\\+\\\\+\\\\.so\\\\.6\\\\.0\\\\.[0-9]+\" STDCPP_REAL_NAME \"${STDCPP_REAL}\")

            # Check existing versions and only remove if different
            file(GLOB _old_files \"\${CMAKE_INSTALL_PREFIX}/lib/libstdc++.so.6.0.*\")
            foreach(_file \${_old_files})
                get_filename_component(_old_name \"\${_file}\" NAME)
                if(NOT \"\${_old_name}\" STREQUAL \"\${STDCPP_REAL_NAME}\")
                    file(REMOVE \"\${_file}\")
                    message(STATUS \"Removed different version of libstdc++: \${_file}\")
                endif()
            endforeach()

            # Install system version - use cp -P to preserve symlinks
            execute_process(
                COMMAND cp -P \"${STDCPP_SO6_PATH}\" \"\${CMAKE_INSTALL_PREFIX}/lib/\"
                COMMAND cp -P \"${STDCPP_REAL}\" \"\${CMAKE_INSTALL_PREFIX}/lib/\"
            )
            message(STATUS \"Installed system libstdc++ with preserved symlinks: ${STDCPP_SO6_PATH}\")
        ")

        # Clean existing versions in build directory
        file(GLOB EXISTING_STDCPP_FILES "${CMAKE_CURRENT_BINARY_DIR}/lib/libstdc++.so.6.0.*")
        foreach(OLD_FILE ${EXISTING_STDCPP_FILES})
            get_filename_component(_old_name "${OLD_FILE}" NAME)
            string(REGEX MATCH "libstdc\\+\\+\\.so\\.6\\.0\\.[0-9]+" STDCPP_REAL_NAME "${STDCPP_REAL}")
            if(NOT "${_old_name}" STREQUAL "${STDCPP_REAL_NAME}")
                file(REMOVE "${OLD_FILE}")
                message(STATUS "Removed different version of libstdc++ from build dir: ${OLD_FILE}")
            endif()
        endforeach()

        # Copy to build directory - use cp -P to preserve symlinks
        add_custom_command(TARGET ${PROJECT_NAME} POST_BUILD
            COMMAND ${CMAKE_COMMAND} -E make_directory ${CMAKE_CURRENT_BINARY_DIR}/lib
            COMMAND cp -P ${STDCPP_SO6_PATH} ${CMAKE_CURRENT_BINARY_DIR}/lib/
            COMMAND cp -P ${STDCPP_REAL} ${CMAKE_CURRENT_BINARY_DIR}/lib/
        )
        message(STATUS "STDCPP_SO6_PATH: ${STDCPP_SO6_PATH}")
        message(STATUS "STDCPP_REAL: ${STDCPP_REAL}")
        message(STATUS "Will copy system libstdc++ to build dir: ${STDCPP_REAL}")
    else()
        message(WARNING "Failed to resolve the system libstdc++. Skipping installation.")
    endif()
else()
    message(WARNING "System libstdc++.so.6 was not found. Skipping installation.")
endif()

# Install debian files
if(BUILD_EXECUTABLE)
    if(BUILD_EXECUTABLE_IS_SERVICE)
        if(NOT EXISTS "${CMAKE_CURRENT_LIST_DIR}/../../share/debian/postinst")
            message(WARNING "postinst file not found in share/debian, creating a default one")
            configure_file(
                    ${CMAKE_CURRENT_LIST_DIR}/../templates/service-postinst.in
                    ${CMAKE_CURRENT_LIST_DIR}/../../share/debian/postinst
                )
        endif()

        if (NOT EXISTS "${CMAKE_CURRENT_LIST_DIR}/../../share/systemd/${PROJECT_NAME}.service")
            message(WARNING "service file not found in share/systemd, creating a default one")
            configure_file(
                ${CMAKE_CURRENT_LIST_DIR}/../templates/application.service.in
                ${CMAKE_CURRENT_LIST_DIR}/../../share/systemd/${PROJECT_NAME}.service
            )
        endif()

        install(DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/share/systemd/ DESTINATION share/systemd)
    else()
        if(NOT EXISTS "${CMAKE_CURRENT_LIST_DIR}/../../share/debian/postinst")
            message(WARNING "postinst file not found in share/debian, creating a default one")
            configure_file(
                ${CMAKE_CURRENT_LIST_DIR}/../templates/application-postinst.in
                ${CMAKE_CURRENT_LIST_DIR}/../../share/debian/postinst
            )
        endif()

        if (NOT EXISTS "${CMAKE_CURRENT_LIST_DIR}/../../share/applications/${PROJECT_NAME}.desktop")
            message(WARNING "desktop file not found in share/applications, creating a default one")
            configure_file(
                ${CMAKE_CURRENT_LIST_DIR}/../templates/application.desktop.in
                ${CMAKE_CURRENT_LIST_DIR}/../../share/applications/${PROJECT_NAME}.desktop
            )
        endif()

        if (NOT EXISTS "${CMAKE_CURRENT_LIST_DIR}/../../share/icons/${PROJECT_NAME}.png")
            message(WARNING "icon file not found in share/icons, creating a default one")
            configure_file(
                ${CMAKE_CURRENT_LIST_DIR}/../templates/icon.png.in
                ${CMAKE_CURRENT_LIST_DIR}/../../share/icons/${PROJECT_NAME}.png
            )
        endif()

        install(DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/share/applications/ DESTINATION share/applications)
        install(DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/share/icons/ DESTINATION share/icons)
        install(DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/share/config/ DESTINATION config/default)
    endif()

    install(DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/share/debian/ DESTINATION share/debian)
endif()


if(BUILD_TESTING)
    # install(TARGETS test_${PROJECT_NAME} RUNTIME DESTINATION bin)
endif()
