# Copyright ©2015-2024 Aubo (Beijing) Robotics Technology Co., Ltd. All rights reserved.

# Add modules path
list(APPEND CMAKE_MODULE_PATH "${CMAKE_CURRENT_LIST_DIR}")
list(APPEND CMAKE_MODULE_PATH "${CMAKE_CURRENT_LIST_DIR}/..")
message(STATUS "CMAKE_MODULE_PATH: ${CMAKE_MODULE_PATH}")

# Include other configuration modules
include(system-info)       # System detection
include(build-options)     # Build options and features

# Basic compiler settings
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Default build type
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE "Release" CACHE STRING "Choose the type of build" FORCE)
endif()
set_property(CACHE CMAKE_BUILD_TYPE PROPERTY STRINGS "Debug" "Release" "RelWithDebInfo" "MinSizeRel")

# Add third_party path
list(PREPEND CMAKE_PREFIX_PATH
    "${CMAKE_CURRENT_SOURCE_DIR}/third_party/${AUBO_SYSTEM}/lib/cmake"
    "${CMAKE_CURRENT_SOURCE_DIR}/third_party/${AUBO_SYSTEM}/share"
)
message(STATUS "CMAKE_PREFIX_PATH: ${CMAKE_PREFIX_PATH}")

# Output directories
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)

# Generate compile_commands.json
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

# Position independent code
set(CMAKE_POSITION_INDEPENDENT_CODE ON)

# Symbol visibility
set(CMAKE_CXX_VISIBILITY_PRESET hidden)
set(CMAKE_VISIBILITY_INLINES_HIDDEN ON)

# Install paths
include(GNUInstallDirs)

# Convert project name to uppercase and replace special characters with underscore
function(convert_project_name_to_macro_prefix INPUT_NAME OUTPUT_VAR)
    # Convert to uppercase
    string(TOUPPER "${INPUT_NAME}" TEMP_NAME)
    # Replace special characters with underscore
    string(REGEX REPLACE "[^A-Z0-9]" "_" TEMP_NAME "${TEMP_NAME}")
    # Remove consecutive underscores
    string(REGEX REPLACE "_+" "_" TEMP_NAME "${TEMP_NAME}")
    # Remove leading/trailing underscores
    string(REGEX REPLACE "^_+|_+$" "" TEMP_NAME "${TEMP_NAME}")
    # Set the output variable in parent scope
    set(${OUTPUT_VAR} ${TEMP_NAME} PARENT_SCOPE)
endfunction()

# Configure version header with major, minor, and patch numbers
function(configure_version_header VERSION_MAJOR VERSION_MINOR VERSION_PATCH)
    # Validate required version parameters
    if(NOT DEFINED VERSION_MAJOR OR NOT DEFINED VERSION_MINOR OR NOT DEFINED VERSION_PATCH)
        message(FATAL_ERROR "Version numbers must be provided: VERSION_MAJOR, VERSION_MINOR, VERSION_PATCH")
    endif()

    # Convert project name to macro prefix
    convert_project_name_to_macro_prefix(${PROJECT_NAME} MACRO_PREFIX)

    # Validate version number format
    foreach(version ${VERSION_MAJOR} ${VERSION_MINOR} ${VERSION_PATCH})
        if(NOT version MATCHES "^[0-9]+$")
            message(FATAL_ERROR "Invalid version number format: ${version}")
        endif()
    endforeach()

    # Get local build datetime
    string(TIMESTAMP AUBO_BUILD_DATETIME "%Y-%m-%d %H:%M:%S")
    message(DEBUG "Build datetime set to: ${AUBO_BUILD_DATETIME}")

    # Set project specific variables for template
    set(PROJECT_MACRO_PREFIX ${MACRO_PREFIX})

    # Verify template existence
    set(TEMPLATE_PATH "${CMAKE_CURRENT_LIST_DIR}/../templates/version.h.in")
    if(NOT EXISTS ${TEMPLATE_PATH})
        message(FATAL_ERROR "Version template file not found: ${TEMPLATE_PATH}")
    endif()

    set(OUTPUT_PATH "${CMAKE_CURRENT_SOURCE_DIR}/include/${PROJECT_NAME}/version.h")
    get_filename_component(OUTPUT_DIR ${OUTPUT_PATH} DIRECTORY)

    # Create output directory if needed
    if(NOT EXISTS ${OUTPUT_DIR})
        file(MAKE_DIRECTORY ${OUTPUT_DIR})
    endif()

    configure_file(
        ${TEMPLATE_PATH}
        ${OUTPUT_PATH}
        @ONLY
    )

    message(STATUS "Generated version header: ${OUTPUT_PATH}")
endfunction()

# Generate package configuration
function(generate_package_config)
    cmake_parse_arguments(PARSE_ARGV 0 ARG
        ""
        "COMPATIBILITY"
        ""
    )

    # Set default compatibility level
    if(NOT ARG_COMPATIBILITY)
        set(ARG_COMPATIBILITY "AnyNewerVersion")
    endif()

    # Validate project configuration
    if(NOT DEFINED PROJECT_NAME)
        message(FATAL_ERROR "PROJECT_NAME is not defined")
    endif()

    if(NOT DEFINED PROJECT_VERSION)
        message(FATAL_ERROR "PROJECT_VERSION is not defined")
    endif()

    include(CMakePackageConfigHelpers)

    set(CONFIG_DIR "${CMAKE_CURRENT_BINARY_DIR}")

    # Generate version file
    write_basic_package_version_file(
        "${CONFIG_DIR}/${PROJECT_NAME}-config-version.cmake"
        VERSION ${PROJECT_VERSION}
        COMPATIBILITY ${ARG_COMPATIBILITY}
    )

    # Verify config template
    set(CONFIG_TEMPLATE "${CMAKE_CURRENT_LIST_DIR}/../templates/project-config.cmake.in")
    if(NOT EXISTS ${CONFIG_TEMPLATE})
        message(FATAL_ERROR "Package config template not found: ${CONFIG_TEMPLATE}")
    endif()

    configure_file(
        ${CONFIG_TEMPLATE}
        "${CONFIG_DIR}/${PROJECT_NAME}-config.cmake"
        @ONLY
    )

    message(STATUS "Generated package configuration files in: ${CONFIG_DIR}")
endfunction()

if(DEFINED PROJECT_VERSION)
    if(NOT BUILD_EXECUTABLE)
        message(STATUS "Building ${PROJECT_NAME} library")

        # Configure version header
        configure_version_header(
            ${PROJECT_VERSION_MAJOR}
            ${PROJECT_VERSION_MINOR}
            ${PROJECT_VERSION_PATCH}
        )

        # Generate package configuration
        generate_package_config()
    endif()
endif()

if(BUILD_TESTING)
    find_package(GTest CONFIG REQUIRED)
    enable_testing()
endif()

link_directories(${CMAKE_CURRENT_LIST_DIR}/../../third_party/${AUBO_SYSTEM}/lib)

# Print project information
message(STATUS "")
message(STATUS "Project: ${PROJECT_NAME}")
message(STATUS "Version: ${PROJECT_VERSION}")
message(STATUS "Description: ${PROJECT_DESCRIPTION}")
message(STATUS "Homepage: ${PROJECT_HOMEPAGE_URL}")
message(STATUS "")
message(STATUS "System name: ${CMAKE_SYSTEM_NAME}")
message(STATUS "System version: ${CMAKE_SYSTEM_VERSION}")
message(STATUS "System processor: ${CMAKE_SYSTEM_PROCESSOR}")
message(STATUS "")
message(STATUS "Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "Install prefix: ${CMAKE_INSTALL_PREFIX}")
message(STATUS "C++ standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "")
message(STATUS "Binary dir: ${CMAKE_BINARY_DIR}")
message(STATUS "Source dir: ${CMAKE_SOURCE_DIR}")
message(STATUS "")