# Copyright ©2015-2024 Aubo (Beijing) Robotics Technology Co., Ltd. All rights reserved.

#[[
    DetectSystem.cmake
    =================
    Detects system architecture and OS, setting AUBO_SYSTEM (e.g., arm64-linux).

    Variables set:
    - AUBO_SYSTEM_ARCH: arm64, x64
    - AUBO_SYSTEM_OS: linux, android
    - AUBO_SYSTEM: <arch>-<os>
    - AUBO_SYSTEM_ARCH_DEBIAN: arm64, amd64
]]

# Detect architecture (arm64, x64)
if(CMAKE_HOST_SYSTEM_PROCESSOR MATCHES "^(aarch64|arm64|ARM64)$" OR CMAKE_HOST_SYSTEM_NAME STREQUAL "Android")
    set(AUBO_SYSTEM_ARCH "arm64" CACHE STRING "System architecture")
    set(AUBO_SYSTEM_ARCH_DEBIAN "arm64" CACHE STRING "System architecture for Debian")
elseif(CMAKE_HOST_SYSTEM_PROCESSOR MATCHES "^(x86_64|AMD64|x64)$")
    set(AUBO_SYSTEM_ARCH "x64" CACHE STRING "System architecture")
    set(AUBO_SYSTEM_ARCH_DEBIAN "amd64" CACHE STRING "System architecture for Debian")
else()
    message(FATAL_ERROR "Unknown architecture '${CMAKE_HOST_SYSTEM_PROCESSOR}', please set AUBO_SYSTEM_ARCH manually")
endif()

mark_as_advanced(AUBO_SYSTEM_ARCH)

# Detect OS (linux, android)
if(CMAKE_HOST_SYSTEM_NAME STREQUAL "Linux")
    set(AUBO_SYSTEM_OS "linux" CACHE STRING "System operating system")
elseif(CMAKE_HOST_SYSTEM_NAME STREQUAL "Android")
    set(AUBO_SYSTEM_OS "android" CACHE STRING "System operating system")
else()
    message(FATAL_ERROR "Unknown OS '${CMAKE_HOST_SYSTEM_NAME}', please set AUBO_SYSTEM_OS manually")
endif()

mark_as_advanced(AUBO_SYSTEM_OS)

# Set system identifier
set(AUBO_SYSTEM "${AUBO_SYSTEM_ARCH}-${AUBO_SYSTEM_OS}" CACHE STRING "System identifier (<arch>-<os>)")
mark_as_advanced(AUBO_SYSTEM)

# Output detection result
message(STATUS "System detection results:")
message(STATUS "  Architecture: ${AUBO_SYSTEM_ARCH}")
message(STATUS "  OS: ${AUBO_SYSTEM_OS}")
message(STATUS "  System: ${AUBO_SYSTEM}")
message(STATUS "  Debian Architecture: ${AUBO_SYSTEM_ARCH_DEBIAN}")