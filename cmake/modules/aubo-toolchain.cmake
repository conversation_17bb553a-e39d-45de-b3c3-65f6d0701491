# Copyright ©2015-2024 Aubo (Beijing) Robotics Technology Co., Ltd. All rights reserved.

# Third party directories
set(AUBO_THIRD_PARTY_DIR "${CMAKE_CURRENT_LIST_DIR}/../../third_party")

# Include vcpkg toolchain
set(AUBO_VCPKG_TOOLCHAIN "${AUBO_THIRD_PARTY_DIR}/vcpkg/scripts/buildsystems/vcpkg.cmake")
if(NOT EXISTS "${AUBO_VCPKG_TOOLCHAIN}")
    message(FATAL_ERROR "vcpkg toolchain not found: ${AUBO_VCPKG_TOOLCHAIN}")
endif()
include("${AUBO_VCPKG_TOOLCHAIN}")

# Set vcpkg directories
set(AUBO_VCPKG_INSTALLED_DIR "${VCPKG_INSTALLED_DIR}/${VCPKG_TARGET_TRIPLET}")
set(AUBO_VCPKG_INCLUDE_DIR "${AUBO_VCPKG_INSTALLED_DIR}/include")
set(AUBO_VCPKG_LIB_DIR "${AUBO_VCPKG_INSTALLED_DIR}/lib")
set(AUBO_VCPKG_BIN_DIR "${AUBO_VCPKG_INSTALLED_DIR}/bin")
set(AUBO_VCPKG_SHARE_DIR "${AUBO_VCPKG_INSTALLED_DIR}/share")
set(AUBO_VCPKG_PKG_CONFIG_DIR "${AUBO_VCPKG_INSTALLED_DIR}/lib/pkgconfig")
set(AUBO_VCPKG_TOOLS_DIR "${AUBO_VCPKG_INSTALLED_DIR}/tools")
set(AUBO_VCPKG_PKG_ETC_DIR "${AUBO_VCPKG_INSTALLED_DIR}/etc")

# Print toolchain
message(STATUS "  Toolchain: ${CMAKE_TOOLCHAIN_FILE}")
