# Copyright ©2015-2024 Aubo (Beijing) Robotics Technology Co., Ltd. All rights reserved.

if(UNIX AND NOT APPLE)
    set_target_properties(${PROJECT_NAME} PROPERTIES
        BUILD_RPATH "$ORIGIN:$ORIGIN/../lib"
        INSTALL_RPATH "$ORIGIN:$ORIGIN/../lib"
    )
endif()

if(NOT BUILD_EXECUTABLE)
    set_target_properties(${PROJECT_NAME} PROPERTIES
        VERSION ${PROJECT_VERSION}
        CXX_VISIBILITY_PRESET hidden
        VISIBILITY_INLINES_HIDDEN ON
        POSITION_INDEPENDENT_CODE ON
    )
else()
    set_target_properties(${PROJECT_NAME} PROPERTIES
        CXX_VISIBILITY_PRESET hidden
        VISIBILITY_INLINES_HIDDEN ON
        POSITION_INDEPENDENT_CODE ON
    )
endif()

target_include_directories(${PROJECT_NAME}
    PUBLIC
        $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
        $<INSTALL_INTERFACE:include>
    PRIVATE
        src
)


if(BUILD_TESTING)

    if(UNIX AND NOT APPLE)
        set_target_properties(test_${PROJECT_NAME} PROPERTIES
            BUILD_RPATH "$ORIGIN:$ORIGIN/../lib"
            INSTALL_RPATH "$ORIGIN:$ORIGIN/../lib"
        )
    endif()

    target_include_directories(test_${PROJECT_NAME}
        PUBLIC
            $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
            $<INSTALL_INTERFACE:include>
        PRIVATE
            src
    )

    target_link_libraries(test_${PROJECT_NAME}
        PRIVATE GTest::gtest_main GTest::gmock_main
        PRIVATE ${PROJECT_NAME}
    )
endif()
