{"name": "vcpkg-ci-hello-imgui", "version-string": "ci", "description": "Port to force features of hello-imgui within CI", "homepage": "https://github.com/microsoft/vcpkg", "license": "MIT", "dependencies": [{"name": "hello-imgui", "default-features": false}, {"name": "vcpkg-cmake", "host": true}], "default-features": ["all"], "features": {"all": {"description": "Test all features", "dependencies": [{"name": "hello-imgui", "features": ["glfw-binding", "opengl3-binding", "test-engine"], "platform": "linux"}, {"$comment": "No platform backend available since removal of imgui[sdl2-binding]", "name": "hello-imgui", "features": ["opengl3-binding"], "platform": "android"}, {"name": "hello-imgui", "features": ["glfw-binding", "metal-binding"], "platform": "ios | osx"}, {"name": "hello-imgui", "features": ["glfw-binding", "opengl3-binding"], "platform": "x64 & windows"}, {"name": "hello-imgui", "features": ["experimental-dx11-binding", "glfw-binding"], "platform": "x86 & windows"}, {"name": "hello-imgui", "features": ["experimental-vulkan-binding", "glfw-binding", "test-engine"], "platform": "arm64 & windows"}]}}}