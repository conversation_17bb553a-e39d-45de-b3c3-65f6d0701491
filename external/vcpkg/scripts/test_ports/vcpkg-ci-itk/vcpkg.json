{"name": "vcpkg-ci-itk", "version-string": "ci", "description": "Validates itk; intentional permutation of features", "homepage": "https://github.com/microsoft/vcpkg", "license": "MIT", "dependencies": [{"name": "itk", "features": ["opencv"], "platform": "arm64"}, {"name": "itk", "features": ["rtk", "tools"], "platform": "arm64 | x64"}, {"name": "itk", "features": ["fftw", "opencl"], "platform": "android | osx"}, {"name": "itk", "features": ["vtk"], "platform": "osx | windows"}, {"name": "itk", "features": ["cuda"], "platform": "x64 & windows & !staticcrt"}, {"name": "itk", "features": ["cufftw", "opencl"], "platform": "x64 & (linux | windows) & static"}, {"name": "vcpkg-cmake", "host": true}]}