{"name": "vcpkg-ci-opencv", "version-string": "ci", "description": "Port to force features of certain ports within CI", "homepage": "https://github.com/microsoft/vcpkg", "dependencies": [{"name": "opencv", "default-features": false, "features": ["ade", "calib3d", "contrib", "eigen", "ffmpeg", "fs", "<PERSON><PERSON><PERSON>", "intrinsics", "jpeg", "nonfree", "png", "quirc", "thread", "tiff"]}, {"name": "opencv", "default-features": false, "features": ["openmp"], "platform": "!osx"}, {"name": "opencv", "default-features": false, "features": ["jpegxl", "openexr"], "platform": "!uwp"}, {"name": "opencv", "default-features": false, "features": ["opencl"], "platform": "!uwp & !osx"}, {"name": "opencv", "default-features": false, "features": ["dnn", "webp"], "platform": "!android"}, {"name": "opencv", "default-features": false, "features": ["directml", "dshow", "msmf", "win32ui"], "platform": "windows & !uwp"}, {"name": "opencv", "default-features": false, "features": ["tbb"], "platform": "!uwp & !static"}, {"name": "opencv", "default-features": false, "features": ["<PERSON>i", "gstreamer"], "platform": "!uwp & !(windows & static) & !android"}, {"name": "opencv", "default-features": false, "features": ["python"], "platform": "!uwp & !(windows & static) & !android & !linux"}, {"name": "opencv", "default-features": false, "features": ["ovis"], "platform": "!uwp & !android & !(windows & static)"}, {"name": "opencv", "default-features": false, "features": ["cuda", "cudnn", "dnn-cuda"], "platform": "(windows & x64 & !uwp) | (linux & x64) | (linux & arm64)"}, {"name": "opencv", "default-features": false, "features": ["freetype", "gdcm", "ipp", "openjpeg", "qt", "sfm", "vtk", "vulkan"], "platform": "!uwp & !android & !(windows & (arm | arm64)) & !(osx & arm64)"}, {"name": "opencv", "default-features": false, "features": ["aravis"], "platform": "!android & !uwp"}, {"name": "opencv2", "default-features": false, "features": ["eigen", "jpeg", "png", "tiff"], "platform": "!uwp & !(arm & windows) & !android"}, {"name": "opencv2", "default-features": false, "features": ["jasper", "opengl", "qt"], "platform": "!uwp & !android & !(windows & (arm | arm64))"}, {"name": "opencv3", "default-features": false, "features": ["eigen", "jpeg", "nonfree", "png", "quirc", "tiff"]}, {"name": "opencv3", "default-features": false, "features": ["openmp"], "platform": "!osx"}, {"name": "opencv3", "default-features": false, "features": ["dnn", "webp"], "platform": "!android"}, {"name": "opencv3", "default-features": false, "features": ["msmf", "ovis", "tbb"], "platform": "windows & x64 & !static & !uwp"}, {"name": "opencv3", "default-features": false, "features": ["gstreamer"], "platform": "!uwp & !android & !(windows & static)"}, {"name": "opencv3", "default-features": false, "features": ["contrib", "freetype", "gdcm", "ipp", "jasper", "opengl", "qt", "sfm", "vtk"], "platform": "!uwp & !android & !(windows & (arm | arm64)) & !(osx & arm64)"}]}