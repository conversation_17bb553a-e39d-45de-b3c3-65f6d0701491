diff --git a/examples/streamer/CMakeLists.txt b/examples/streamer/CMakeLists.txt
index 1bdeb7c..9cf65dd 100644
--- a/examples/streamer/CMakeLists.txt
+++ b/examples/streamer/CMakeLists.txt
@@ -2,6 +2,15 @@ cmake_minimum_required(VERSION 3.7)
 if(POLICY CMP0079)
 	cmake_policy(SET CMP0079 NEW)
 endif()
+project(vcpkg-ci)
+add_definitions(-DWIN32_LEAN_AND_MEAN)
+set(THREADS_PREFER_PTHREAD_FLAG ON)
+find_package(Threads REQUIRED)
+find_package(nlohmann_json CONFIG REQUIRED)
+find_package(LibDataChannel CONFIG REQUIRED)
+if(NOT TARGET LibDataChannel::LibDataChannel)
+	add_library(LibDataChannel::LibDataChannel ALIAS LibDataChannel::LibDataChannelStatic)
+endif()
 
 set(STREAMER_SOURCES
 	main.cpp
@@ -47,7 +56,7 @@ set_target_properties(streamer PROPERTIES
 find_package(Threads REQUIRED)
 target_link_libraries(streamer LibDataChannel::LibDataChannel Threads::Threads nlohmann_json::nlohmann_json)
 
-if(MSVC)
+if(0)
 	add_custom_command(TARGET streamer POST_BUILD
 		COMMAND ${CMAKE_COMMAND} -E copy_if_different
 		"$<TARGET_FILE_DIR:datachannel>/datachannel.dll"
