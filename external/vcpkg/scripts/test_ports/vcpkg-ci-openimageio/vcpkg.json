{"name": "vcpkg-ci-openimageio", "version-string": "ci", "port-version": 1, "description": "OpenImageIO testing within CI.", "license": "MIT", "dependencies": [{"name": "openimageio", "default-features": false}, {"name": "vcpkg-cmake", "host": true}], "default-features": ["openimageio-features"], "features": {"openimageio-features": {"description": "Enable OpenImageIO features", "dependencies": [{"name": "openimageio", "default-features": false, "features": ["ffmpeg", "freetype", "gif", "libraw", "opencolorio", "opencv", "openjpeg", {"name": "pybind11", "platform": "!(windows & static) & !uwp & !mingw"}, "tools", "webp"]}]}}}