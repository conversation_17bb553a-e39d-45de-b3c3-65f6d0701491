{"name": "vcpkg-ci-curl", "version-string": "ci", "description": "Port to force features of certain ports within CI", "homepage": "https://github.com/microsoft/vcpkg", "license": "MIT", "dependencies": [{"name": "curl", "default-features": false}, {"name": "vcpkg-cmake", "host": true}], "default-features": ["compression", "misc", "security"], "features": {"compression": {"description": "Compression features", "dependencies": [{"name": "curl", "default-features": false, "features": ["zstd"]}, {"$comment": "Known to break aws-sdk-cpp.", "name": "curl", "default-features": false, "features": ["brotli"], "platform": "!osx"}]}, "misc": {"description": "Misc features", "dependencies": [{"name": "curl", "default-features": false, "features": ["c-ares", "http2", "httpsrr", "idn", "rtmp", "ssh", "ssls-export"]}, {"name": "curl", "default-features": false, "features": ["psl"], "platform": "!uwp"}, {"name": "curl", "default-features": false, "features": ["ldap", "tool"], "platform": "!android & !uwp"}]}, "security": {"description": "Security features", "dependencies": [{"name": "curl", "default-features": false, "features": ["gsasl", "mbedtls", "openssl", "ssl", "wolfssl"], "platform": "!uwp"}, {"name": "curl", "default-features": false, "features": ["gssapi"], "platform": "linux | osx"}, {"name": "curl", "default-features": false, "features": ["sspi"], "platform": "windows & !uwp"}, {"$comment": "On arm, gnutls crypto symbols clash with openssl.", "name": "curl", "default-features": false, "features": ["gnutls"], "platform": "!android & !uwp & !xbox & !arm"}]}}}