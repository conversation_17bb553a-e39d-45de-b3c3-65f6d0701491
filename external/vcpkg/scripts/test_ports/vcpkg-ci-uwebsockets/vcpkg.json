{"name": "vcpkg-ci-uwebsockets", "version-string": "ci", "description": "Port to test features of uwebsockets within CI", "homepage": "https://github.com/microsoft/vcpkg", "license": "MIT", "dependencies": [{"name": "uwebsockets", "default-features": false}, {"name": "vcpkg-cmake", "host": true}], "default-features": ["all"], "features": {"all": {"description": "Test all features", "dependencies": [{"name": "uwebsockets", "features": ["ssl", "zlib"]}]}}}