{"name": "vcpkg-ci-boost", "version": "1", "description": "Boost features testing within CI.", "dependencies": ["boost", {"name": "boost", "default-features": false, "features": ["mpi"], "platform": "!staticcrt & !android & !(arm & windows) & !uwp"}, {"name": "boost-asio", "default-features": false, "features": ["ssl"]}, {"name": "boost-fiber", "default-features": false, "features": ["numa"], "platform": "!uwp & !arm & !emscripten"}, {"name": "boost-locale", "default-features": false, "features": ["icu"], "platform": "!uwp"}, {"name": "boost-mpi", "default-features": false, "features": ["python3"], "platform": "!staticcrt & !static & !android & !(arm & windows) & !uwp"}, {"name": "boost-odeint", "default-features": false, "features": ["mpi"], "platform": "!staticcrt & !android & !(windows & arm) & !uwp"}, {"name": "boost-python", "default-features": false, "platform": "!emscripten & !ios & !android & !uwp"}, {"name": "boost-regex", "default-features": false, "features": ["icu"]}]}