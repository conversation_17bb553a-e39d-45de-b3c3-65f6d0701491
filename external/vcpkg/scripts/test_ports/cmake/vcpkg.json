{"name": "cmake", "version": "4.0.0", "description": "CMake is an open-source, cross-platform family of tools designed to build, test and package software.", "homepage": "https://cmake.org/", "license": "BSD-3-<PERSON><PERSON>", "dependencies": ["cppdap", {"name": "curl", "default-features": false}, "expat", "jsoncpp", {"name": "libarchive", "default-features": false, "features": ["bzip2", "lzma", "zstd"]}, "libuv", {"name": "ncurses", "platform": "!windows"}, "nghttp2", {"name": "qtbase", "default-features": false, "features": ["widgets"]}, "rhash", {"name": "vcpkg-cmake", "host": true}, "zlib"]}