diff --git a/Source/Modules/CMakeBuildUtilities.cmake b/Source/Modules/CMakeBuildUtilities.cmake
index dba9d506..225779cd 100644
--- a/Source/Modules/CMakeBuildUtilities.cmake
+++ b/Source/Modules/CMakeBuildUtilities.cmake
@@ -328,6 +328,13 @@ endif()
 #---------------------------------------------------------------------
 # Build libuv library.
 if(CMAKE_USE_SYSTEM_LIBUV)
+  find_package(libuv CONFIG REQUIRED)
+  if (TARGET libuv::uv)
+    add_library(LibUV::LibUV ALIAS libuv::uv)
+  else()
+    add_library(LibUV::LibUV ALIAS libuv::uv_a)
+  endif()
+elseif(0)
   if(WIN32)
     find_package(LibUV 1.38.0)
   else()
