{"name": "sqlpp11-connector-mysql", "version-string": "ci", "description": "Overlay for an obsolete empty port in order to unblock sqlpp11 testing: platform-specific choice of testable implementation", "homepage": "https://github.com/microsoft/vcpkg", "license": "MIT", "supports": "!uwp", "dependencies": [{"name": "sqlpp11", "default-features": false, "features": ["ma<PERSON>b"], "platform": "x86 & windows"}, {"name": "sqlpp11", "default-features": false, "features": ["mysql"], "platform": "!(x86 & windows)"}]}