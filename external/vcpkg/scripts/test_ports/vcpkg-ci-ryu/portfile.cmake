set(VCPKG_POLICY_SKIP_ALL_POST_BUILD_CHECKS enabled)
vcpkg_cmake_configure(SOURCE_PATH "${CURRENT_PORT_DIR}")
vcpkg_cmake_install()
if(NOT VCPKG_CROSSCOMPILING)
    vcpkg_execute_required_process(COMMAND "${CURRENT_PACKAGES_DIR}/bin/vcpkg-ci-ryu/test" WORKING_DIRECTORY "." LOGNAME release-test)
    if(NOT VCPKG_BUILD_TYPE)
        vcpkg_execute_required_process(COMMAND "${CURRENT_PACKAGES_DIR}/debug/bin/vcpkg-ci-ryu/test" WORKING_DIRECTORY "." LOGNAME debug-test)
    endif()
endif()
