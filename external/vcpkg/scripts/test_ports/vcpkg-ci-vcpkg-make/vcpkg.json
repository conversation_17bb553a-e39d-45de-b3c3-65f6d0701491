{"name": "vcpkg-ci-vcpkg-make", "version-string": "ci", "description": "Ensures that the vcpkg-make port functions are unit tested.", "license": "MIT", "dependencies": [{"name": "unit-test-cmake", "host": true}, {"name": "vcpkg-make", "host": true}], "default-features": ["z-vcpkg-make-determine-arch", "z-vcpkg-make-determine-host-arch", "z-vcpkg-make-determine-target-arch", "z-vcpkg-make-get-configure-triplets", "z-vcpkg-make-prepare-compile-flags", "z-vcpkg-make-z-adapt-lib-link-names"], "features": {"z-vcpkg-make-determine-arch": {"description": "Test the z_vcpkg_make_determine_arch function"}, "z-vcpkg-make-determine-host-arch": {"description": "Test the z_vcpkg_make_determine_host_arch function"}, "z-vcpkg-make-determine-target-arch": {"description": "Test the z_vcpkg_make_determine_target_arch function"}, "z-vcpkg-make-get-configure-triplets": {"description": "Test the z_vcpkg_make_get_configure_triplets function"}, "z-vcpkg-make-prepare-compile-flags": {"description": "Test the z_vcpkg_make_prepare_compile_flags function"}, "z-vcpkg-make-z-adapt-lib-link-names": {"description": "Test the z_vcpkg_make_z_adapt_lib_link_names function"}}}