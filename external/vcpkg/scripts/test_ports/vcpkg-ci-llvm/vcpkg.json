{"name": "vcpkg-ci-llvm", "version-string": "0", "description": "LLVM features testing within CI.", "license": null, "supports": "!uwp & !(arm & windows)", "dependencies": [{"name": "llvm", "default-features": false, "features": ["clang", "compiler-rt", "default-targets", "enable-abi-breaking-checks", "enable-bindings", "enable-eh", "enable-rtti", "enable-terminfo", "enable-zlib", "lld", "lldb", "tools"]}, {"$comment": "Platform restriction due to CI artifact upload quirks; libc", "name": "llvm", "default-features": false, "features": ["libc"], "platform": "linux"}, {"$comment": "Platform restriction due to CI artifact upload quirks; features which need utils", "name": "llvm", "default-features": false, "features": ["bolt", "openmp", "polly", "utils"], "platform": "!static & !x86"}]}