{"name": "cmake-user", "version-string": "ci", "description": "Test port to verify the vcpkg toolchain in cmake user projects", "license": "MIT", "default-features": [{"name": "cmake-3-16", "platform": "x64 & (windows | linux | osx) & !uwp"}, "cmake-current", "find-package", "pkg-check-modules"], "features": {"cmake-3-16": {"description": "Run the tests with CMake 3.16"}, "cmake-current": {"description": "Run the tests with vcpkg's current version of CMake"}, "find-package": {"description": "Ports to be tested via find_package($package)", "dependencies": [{"$package": "ALSA", "name": "alsa", "platform": "linux"}, {"$package": "Boost", "name": "boost", "platform": "!uwp"}, {"$package": "BZip2", "name": "bzip2"}, {"$package": "CURL", "name": "curl", "default-features": false}, {"$package": "EXPAT", "name": "expat"}, {"$package": "Fontconfig", "name": "fontconfig", "platform": "!mingw & !uwp"}, {"$package": "GLUT", "name": "freeglut", "platform": "!uwp & !osx"}, {"$package": "Freetype", "name": "freetype", "default-features": false}, {"$package": "GDAL", "name": "gdal", "default-features": false, "platform": "!uwp"}, {"$package": "Intl", "name": "gettext-libintl"}, {"$package": "GIF", "name": "gif<PERSON>b"}, {"$package": "ICU", "name": "icu", "platform": "!uwp"}, {"$package": "LAPACK", "name": "lapack", "platform": "!(uwp & arm)"}, {"$package": "GnuTLS", "name": "libgnutls", "platform": "!android & (!windows | mingw)"}, {"$package": "Iconv", "name": "libiconv"}, {"$package": "JPEG", "name": "libjpeg-turbo"}, {"$package": "LibLZMA", "name": "liblzma"}, {"$package": "PNG", "name": "libpng"}, {"$package": "PostgreSQL", "name": "libpq", "default-features": false, "platform": "!uwp & !mingw"}, {"$package": "LibXml2", "name": "libxml2", "default-features": false}, {"$package": "LibXslt", "name": "libxslt", "default-features": false, "platform": "!uwp & !mingw"}, {"$package": "Curses", "name": "ncurses", "platform": "!windows | mingw"}, {"$package": "PhysFS", "name": "physfs"}, {"$package": "GnuTLS", "name": "shiftmedia-libgnutls", "platform": "windows & !arm & !mingw & !xbox"}, {"$package": "SQLite3", "name": "sqlite3", "default-features": false}, {"$package": "TIFF", "name": "tiff", "default-features": false, "features": ["lerc", "libdeflate", "zstd"]}, {"$package": "wxWidgets", "name": "wxwidgets", "default-features": false, "platform": "!uwp"}, {"$package": "ZLIB", "name": "zlib"}]}, "pkg-check-modules": {"description": "Test `find_package(PkgConfig)` and pkg_check_modules(...)", "dependencies": [{"name": "pkgconf", "host": true}, "zlib"]}}}