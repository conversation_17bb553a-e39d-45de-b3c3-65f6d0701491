<?xml version="1.0" encoding="utf-8"?>
<Rule Name="VcpkgOptions" DisplayName="vcpkg" PageTemplate="generic" Description="Vcpkg"
      xmlns="http://schemas.microsoft.com/build/2009/properties">

  <Rule.Categories>
    <Category Name="General" DisplayName="General" Description="General Vcpkg Configuration" />
    <Category Name="Conditional" DisplayName="Target and Configuration Specific" Description="Conditional Vcpkg Configuration" />
  </Rule.Categories>

  <Rule.DataSource>
    <!-- Note: HasConfigurationCondition must be either "true" or ommitted. Otherwise, the vcpkg property sheet will not be displayed. -->
    <!-- Note: Remove all instances of 'Label="Vcpkg"' from this file if the vcpkg property sheet does not display any values.  -->
    <DataSource Persistence="ProjectFile" Label="Vcpkg" HasConfigurationCondition="true" />
  </Rule.DataSource>

  <BoolProperty Name="VcpkgEnabled" DisplayName="Use Vcpkg" Category="General" Default="true"
                Description="Use Vcpkg for includes and libraries.">
    <BoolProperty.DataSource>
      <DataSource Persistence="ProjectFile" Label="Vcpkg" HasConfigurationCondition="false" />
    </BoolProperty.DataSource>
  </BoolProperty>

  <BoolProperty Name="VcpkgEnableManifest" DisplayName="Use Vcpkg Manifest" Category="General" Default="false"
                Description="Use the vcpkg manifest file to define your dependencies.">
    <BoolProperty.DataSource>
      <DataSource Persistence="ProjectFile" Label="Vcpkg" HasConfigurationCondition="false" />
    </BoolProperty.DataSource>
  </BoolProperty>

  <BoolProperty Name="VcpkgManifestInstall" DisplayName="Install Vcpkg Dependencies" Category="General" Default="true"
                Description="Install dependencies from the vcpkg manifest.">
    <BoolProperty.DataSource>
      <DataSource Persistence="ProjectFile" Label="Vcpkg" HasConfigurationCondition="false" />
    </BoolProperty.DataSource>
  </BoolProperty>

  <BoolProperty Name="VcpkgAutoLink" DisplayName="Use AutoLink" Category="General" Default="true"
                Description="Enables automatic linking with libraries build using Vcpkg. Does not work with lld-link.exe.">
    <BoolProperty.DataSource>
      <DataSource Persistence="ProjectFile" Label="Vcpkg" HasConfigurationCondition="false" />
    </BoolProperty.DataSource>
  </BoolProperty>

  <BoolProperty Name="VcpkgApplocalDeps" DisplayName="App-locally deploy DLLs" Category="General" Default="true"
                Description="Copies DLL dependencies from the vcpkg installed tree to the project output directory.">
    <BoolProperty.DataSource>
      <DataSource Persistence="ProjectFile" Label="Vcpkg" HasConfigurationCondition="false" />
    </BoolProperty.DataSource>
  </BoolProperty>

  <BoolProperty Name="VcpkgXUseBuiltInApplocalDeps" DisplayName="Use built-in app-local deployment" Category="General" Default="false"
                Description="(experimental) When copying DLL dependencies, use built-in-to-vcpkg implementation rather than PowerShell script.">
    <BoolProperty.DataSource>
      <DataSource Persistence="ProjectFile" Label="Vcpkg" HasConfigurationCondition="false" />
    </BoolProperty.DataSource>
  </BoolProperty>

  <StringProperty Name="VcpkgRoot" DisplayName="Vcpkg Root" Category="General" Subtype="folder" Visible="false"
                  Description="Root path where Vcpkg is located. Be careful with changing this one. It is, for example, unable to update this property page from the new location without restarting visual studio.">
    <StringProperty.DataSource>
      <DataSource Persistence="ProjectFile" Label="Vcpkg" HasConfigurationCondition="false" />
    </StringProperty.DataSource>
  </StringProperty>

  <StringProperty Name="VcpkgManifestRoot" DisplayName="Vcpkg Manifest Root" Category="General" Subtype="folder" Visible="false"
                  Description="The path to the directory which contains the manifest file, and the vcpkg_installed directory.">
    <StringProperty.DataSource>
      <DataSource Persistence="ProjectFile" Label="Vcpkg" HasConfigurationCondition="false" />
    </StringProperty.DataSource>
  </StringProperty>

  <StringProperty Name="VcpkgInstalledDir" DisplayName="Installed Directory" Category="General" Subtype="folder" Visible="true"
                  Description="The location where headers and binaries will be consumed from. In manifest mode, this directory will be created and populated based on vcpkg.json.">
  </StringProperty>

  <BoolProperty Name="VcpkgUseStatic" DisplayName="Use Static Libraries" Category="Conditional" Default="false"
                Description="Vcpkg can build static libraries (e.g. x64-windows-static). This options changes the default triplet to use these static libraries by appending -static to $(VcpkgTriplet). This will not be shown in the evaluation of the Triplet within the UI." />

  <BoolProperty Name="VcpkgUseMD" DisplayName="Use Dynamic CRT" Category="Conditional" Default="false"
                Description="Only effective if 'Use Static Libraries' is selected. Vcpkg can build static libraries compiled against MSVC runtime (e.g. x64-windows-static-md). This options changes the default triplet to use these static libraries by appending -md to $(VcpkgTriplet). This will not be shown in the evaluation of the Triplet within the UI." />

  <StringProperty Name="VcpkgTriplet" DisplayName="Triplet" Category="Conditional" Subtype="Text"
                  Description="Specifies the triplet used by Vcpkg. Does not include the '-static' or '-static-md' suffixes that may be added by the 'Use static libraries' and 'Use Dynamic CRT' flags." />

  <StringProperty Name="VcpkgHostTriplet" DisplayName="Host Triplet" Category="Conditional" Subtype="Text"
                  Description="Specifies the host triplet used by Vcpkg. If empty, this will be automatically determined." />

  <StringProperty Name="VcpkgAdditionalInstallOptions" DisplayName="Additional Options" Category="General" Subtype="Text"
                  Description="Additional command line options to be passed to the underlying vcpkg tool when installing in manifest mode." />

  <EnumProperty Name="VcpkgConfiguration" DisplayName="Vcpkg Configuration" Category="Conditional"
                Description="Specifies if release or debug libraries build with vcpkg should be used.">
    <EnumValue Name="Release" Description="Uses release libraries" />
    <EnumValue Name="Debug" Description="Uses debug libraries" />
  </EnumProperty>

</Rule>
