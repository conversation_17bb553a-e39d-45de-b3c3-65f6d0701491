set(program_name bazel)
set(program_version 4.2.2)
set(rename_binary_to "bazel")
if(CMAKE_HOST_SYSTEM_NAME STREQUAL "Linux")
    set(tool_subdirectory "${program_version}-linux")
    set(download_urls "https://github.com/bazelbuild/bazel/releases/download/${program_version}/bazel-${tool_subdirectory}-x86_64")
    set(download_filename "bazel-${tool_subdirectory}-x86_64")
    set(raw_executable ON)
    set(download_sha512 f38619e054df78cab38278a5901b2798f2e25b5cec53358d98278002e713d225fd3df96a209b7f22a2357835a279cee8ef1768e10561b3e9fe6361f324563bb9)
elseif(CMAKE_HOST_SYSTEM_NAME STREQUAL "Darwin")
    set(tool_subdirectory "${program_version}-darwin")
    set(download_urls "https://github.com/bazelbuild/bazel/releases/download/${program_version}/bazel-${tool_subdirectory}-x86_64")
    set(download_filename "bazel-${tool_subdirectory}-x86_64")
    set(raw_executable ON)
    set(download_sha512 a3fd8f9d71b0669d742439200f27ee0a3891c1f248df62c841ebb2b416a47534562f429f8a08793b074e9b74f2ede3d97a7e13ac9921c7ee2dc6a2dca8b7f275)
else()
    set(tool_subdirectory "${program_version}-windows")
    set(download_urls "https://github.com/bazelbuild/bazel/releases/download/${program_version}/bazel-${tool_subdirectory}-x86_64.zip")
    set(download_filename "bazel-${tool_subdirectory}-x86_64.zip")
    set(download_sha512 8a8196e242964114316232818cb81bfa19ebfd3a029ebf550a241e33b22a6e9ed636dade06411a8706c05c4e73def0bc8d7f45ff0ec5478bcc5de21b5638204d)
endif()
