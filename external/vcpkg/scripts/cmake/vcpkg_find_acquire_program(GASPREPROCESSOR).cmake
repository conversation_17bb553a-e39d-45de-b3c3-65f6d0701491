set(raw_executable true)
set(program_name gas-preprocessor)
set(interpreter PERL)
set(search_names "gas-preprocessor.pl")
set(paths_to_search "${DOWNLOADS}/tools/gas-preprocessor/${tool_subdirectory}")
set(rename_binary_to "gas-preprocessor.pl")
set(commit_id 9309c67acb535ca6248f092e96131d8eb07eefc1)
set(download_urls "https://raw.githubusercontent.com/FFmpeg/gas-preprocessor/${commit_id}/gas-preprocessor.pl")
string(SUBSTRING ${commit_id} 0 8 tool_subdirectory)
set(download_filename "gas-preprocessor-${tool_subdirectory}.pl")
set(download_sha512 b4749cf8aa758e3f28d4b21803422a5c2588f5fc48cfd317564606b374f8d739c636067cf7a4956d7365d63b055bc6e7626c304857e6c9013d6b4a0db9d8ad4f)
