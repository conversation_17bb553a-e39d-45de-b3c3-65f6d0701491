set(program_name meson)
set(search_names meson meson.py)
set(interpreter PYTHON3)
set(apt_package_name "meson")
set(brew_package_name "meson")
set(version_command --version)
set(extra_search_args EXACT_VERSION_MATCH)
if(EXISTS "${CURRENT_HOST_INSTALLED_DIR}/share/meson/version.txt")
    file(READ "${CURRENT_HOST_INSTALLED_DIR}/share/meson/version.txt" program_version)
    set(paths_to_search "${DOWNLOADS}/tools/meson-${program_version};${CURRENT_HOST_INSTALLED_DIR}/tools/meson")
else() # Old behavior
    set(program_version 0.58.1)
    set(ref aeda7f249c4a5dbbecc52e44f382246a2377b5b0)
    set(paths_to_search "${DOWNLOADS}/tools/meson/meson-${ref}")
    set(download_urls "https://github.com/mesonbuild/meson/archive/${ref}.tar.gz")
    set(download_filename "meson-${ref}.tar.gz")
    set(download_sha512 18a012a45274dbb4582e99fd69d920f38831e788d9860f9553c64847bedb1c2010ae0b5c0ef4a4350c03f5e0f95aaa0395378e1208109b59640c1a70b1e202d2)
endif()
