set(program_name ninja)
set(program_version 1.12.1)
set(version_command --version)
if(CMAKE_HOST_WIN32)
    if(DEFINED ENV{PROCESSOR_ARCHITEW6432})
        set(build_arch $ENV{PROCESSOR_ARCHITEW6432})
    else()
        set(build_arch $ENV{PROCESSOR_ARCHITECTURE})
    endif()
    if(build_arch MATCHES "^(ARM|arm)64$")
        set(download_filename "ninja-winarm64-${program_version}.zip")
        set(tool_subdirectory "${program_version}-windows-arm64")
        set(download_urls "https://github.com/ninja-build/ninja/releases/download/v${program_version}/ninja-winarm64.zip")
        set(download_sha512 b1826c422a677f47f9f7e001672ce831791b092e4f1cd84ddf2ea067781c31aa8246f26e91dd66300c23ffa77a8ea29910c48ccf7e4235ff20bccc2d2b6e247b)
    else()
        set(download_filename "ninja-win-${program_version}.zip")
        set(tool_subdirectory "${program_version}-windows")
        set(download_urls "https://github.com/ninja-build/ninja/releases/download/v${program_version}/ninja-win.zip")
        set(download_sha512 d6715c6458d798bcb809f410c0364dabd937b5b7a3ddb4cd5aba42f9fca45139b2a8a3e7fd9fbd88fd75d298ed99123220b33c7bdc8966a9d5f2a1c9c230955f)
    endif()
elseif(CMAKE_HOST_SYSTEM_NAME STREQUAL "Darwin")
    set(download_filename "ninja-mac-${program_version}.zip")
    set(download_urls "https://github.com/ninja-build/ninja/releases/download/v${program_version}/ninja-mac.zip")
    set(tool_subdirectory "${program_version}-osx")
    set(paths_to_search "${DOWNLOADS}/tools/ninja-${program_version}-osx")
    set(download_sha512 4c11f477359c9d1dcda72529a503a59948ec20b368992132e545d6d4f6e3aabfd1d6b1d0f32cf932626037959b24a7bb375ef901e2d002eabadc83a265cbc351)
elseif(CMAKE_HOST_SYSTEM_NAME STREQUAL "FreeBSD")
    set(paths_to_search "${DOWNLOADS}/tools/${tool_subdirectory}-freebsd")
else()
    vcpkg_execute_in_download_mode(COMMAND "uname" "-m" OUTPUT_VARIABLE HOST_ARCH OUTPUT_STRIP_TRAILING_WHITESPACE)
    if(HOST_ARCH MATCHES "x86_64|amd64|AMD64")
        set(download_filename "ninja-linux-${program_version}.zip")
        set(download_urls "https://github.com/ninja-build/ninja/releases/download/v${program_version}/ninja-linux.zip")
        set(tool_subdirectory "${program_version}-linux")
        set(paths_to_search "${DOWNLOADS}/tools/ninja-${program_version}-linux")
        set(download_sha512 9c2ad534e7e72e67c608de7784cfbae601095bfca96713731a3f1eca268d66a6302f40c138a4ad97f7e8c902cd3fb05994a175e46fe922295dcc2d1334bf9014)
    elseif(HOST_ARCH MATCHES "arm64|aarch64|ARM64|AARCH64")
        set(download_filename "ninja-linux-aarch64-${program_version}.zip")
        set(download_urls "https://github.com/ninja-build/ninja/releases/download/v${program_version}/ninja-linux-aarch64.zip")
        set(tool_subdirectory "${program_version}-linux-aarch64")
        set(paths_to_search "${DOWNLOADS}/tools/ninja-${program_version}-linux-aarch64")
        set(download_sha512 22c46abb7e6d916e11713705f78d093e9b30029cb49cadc65755908ad9f44b3f2548105174cc615a5ef86c4672b366173f18bd04c2d71710a303d952c06db334)
    else()
        set(version_command "") # somewhat hacky way to skip version check and use system binary
    endif()
endif()
