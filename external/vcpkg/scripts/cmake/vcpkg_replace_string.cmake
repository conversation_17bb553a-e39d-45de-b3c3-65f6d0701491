function(vcpkg_replace_string filename match replace)
    cmake_parse_arguments(PARSE_ARGV 3 "arg" "REGEX;IGNORE_UNCHANGED" "" "")
    if(arg_REGEX)
        set(arg_REGEX "REGEX")
    else()
        if("${match}" STREQUAL "${replace}")
            return() # Avoid reading the file or triggering warnings
        endif()

        set(arg_REGEX "")
    endif ()
    file(READ "${filename}" contents)
    string(SHA512 before_hash "${contents}")
    string(${arg_REGEX} REPLACE "${match}" "${replace}" contents "${contents}")
    string(SHA512 after_hash "${contents}")
    if(NOT arg_IGNORE_UNCHANGED AND "${before_hash}" STREQUAL "${after_hash}")
        message("${Z_VCPKG_BACKCOMPAT_MESSAGE_LEVEL}" "vcpkg_replace_string made no changes.")
    endif()
    file(WRITE "${filename}" "${contents}")
endfunction()
