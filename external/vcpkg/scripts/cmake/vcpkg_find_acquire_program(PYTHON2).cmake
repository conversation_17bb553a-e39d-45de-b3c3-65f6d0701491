if(CMAKE_HOST_WIN32)
    set(program_name python)
    set(program_version 2.7.18)
    if(EXISTS "${CURRENT_HOST_INSTALLED_DIR}/share/vcpkg-tool-python2/details.cmake")
        include("${CURRENT_HOST_INSTALLED_DIR}/share/vcpkg-tool-python2/details.cmake")
    else() # Old behavior
        if (VCPKG_TARGET_ARCHITECTURE STREQUAL x86)
            set(tool_subdirectory "python-${program_version}-x86")
            set(download_urls "https://www.python.org/ftp/python/${program_version}/python-${program_version}.msi")
            set(download_filename "python-${program_version}.msi")
            set(download_sha512 2c112733c777ddbf189b0a54047a9d5851ebce0564cc38b9687d79ce6c7a09006109dbad8627fb1a60c3ad55e261db850d9dfa454af0533b460b2afc316fe115)
        else()
            set(tool_subdirectory "python-${program_version}-x64")
            set(download_urls "https://www.python.org/ftp/python/${program_version}/python-${program_version}.amd64.msi")
            set(download_filename "python-${program_version}.amd64.msi")
            set(download_sha512 6a81a413b80fd39893e7444fd47efa455d240cbb77a456c9d12f7cf64962b38c08cfa244cd9c50a65947c40f936c6c8c5782f7236d7b92445ab3dd01e82af23e)
        endif()
        set(paths_to_search "${DOWNLOADS}/tools/python/${tool_subdirectory}")
    endif()
elseif(CMAKE_HOST_SYSTEM_NAME STREQUAL "Darwin")
    # macOS includes Python 2.7 built-in as `python`
    set(program_name python)
    set(brew_package_name "python2")
else()
    set(program_name python2)
    set(apt_package_name "python")
endif()
