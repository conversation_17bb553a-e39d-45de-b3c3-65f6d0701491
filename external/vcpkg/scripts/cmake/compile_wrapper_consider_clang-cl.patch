diff --git a/usr/share/automake-1.16/compile b/usr/share/automake-1.16/compile
index 2078fc833..dfc946593 100755
--- a/usr/share/automake-1.16/compile
+++ b/usr/share/automake-1.16/compile
@@ -256,6 +256,7 @@ EOF
     exit $?
     ;;
   cl | *[/\\]cl | cl.exe | *[/\\]cl.exe | \
+  clang-cl | *[/\\]clang-cl | clang-cl.exe | *[/\\]clang-cl.exe | \
   icl | *[/\\]icl | icl.exe | *[/\\]icl.exe )
     func_cl_wrapper "$@"      # Doesn't return...
     ;;
