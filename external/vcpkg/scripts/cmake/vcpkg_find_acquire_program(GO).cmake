set(program_name go)
set(brew_package_name "go")
set(apt_package_name "golang-go")
if(CMAKE_HOST_WIN32)
    set(tool_subdirectory 1.21.1.windows-386)
    set(paths_to_search "${DOWNLOADS}/tools/go/${tool_subdirectory}/go/bin")
    set(download_urls "https://dl.google.com/go/go${tool_subdirectory}.zip")
    set(download_filename "go${tool_subdirectory}.zip")
    set(download_sha512 417a4bd95a10f21c2166badd2303e1956d91d0e783e334c99ea0176a323e815729c8c3af3f7ec68f057b757d06bcc75be82584031c4069c89a2db62bbfa902e8)
endif()
