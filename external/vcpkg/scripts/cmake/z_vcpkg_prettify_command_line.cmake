function(z_vcpkg_prettify_command_line out_var)
    set(output_list "")
    z_vcpkg_function_arguments(args 1)
    foreach(v IN LISTS args)
        string(REPLACE [[\]] [[\\]] v "${v}")
        if(v MATCHES "( )")
            string(REPLACE [["]] [[\"]] v "${v}")
            list(APPEND output_list "\"${v}\"")
        else()
            list(APPEND output_list "${v}")
        endif()
    endforeach()
    list(JOIN output_list " " output)
    set("${out_var}" "${output}" PARENT_SCOPE)
endfunction()
