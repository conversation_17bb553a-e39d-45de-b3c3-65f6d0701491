set(program_name nuget)
set(brew_package_name "nuget")
if(CMAKE_HOST_WIN32)
    set(download_urls "https://dist.nuget.org/win-x86-commandline/v6.11.1/nuget.exe")
    set(download_filename "nuget.6.11.1.exe")
    set(download_sha512 8E139D1C4A97F35625E261DF07AC5B3ECB6B931907D303E3B0FCCA26EC537FF667FF49CA15CB57909B30A262EA39EF678C4CBF33C99658162E58A6648D336D52)
    set(tool_subdirectory "6.11.1")
    set(paths_to_search "${DOWNLOADS}/tools/nuget-${tool_subdirectory}-windows")
    set(raw_executable ON)
    set(rename_binary_to "nuget.exe")
endif()
