# Copyright (c) Microsoft Corporation.
# SPDX-License-Identifier: MIT
#

parameters:
  - name: vcpkgToolSha
    displayName: 'Custom SHA of vcpkg-tool to use rather than bootstrap'
    type: string
    default: 'use default'
  - name: jobName
    type: string
  - name: tripletPattern
    displayName: 'Enable the triplets which contain this substring'
    type: string
    default: ''

jobs:
- job: ${{ parameters.jobName }}
  condition: and(succeeded(), contains('^${{ replace(parameters.jobName, '_', '-') }}$', '${{ parameters.tripletPattern }}'))
  pool:
    name: PrAnd-WUS
  timeoutInMinutes: 1440 # 1 day
  variables:
  - name: WORKING_ROOT
    value: /mnt/vcpkg-ci
  - name: VCPKG_DOWNLOADS
    value: /mnt/vcpkg-ci/downloads
  - name: ANDROID_NDK_HOME
    value: /android-ndk-r27c
  - name: ANDROID_DOCKER_IMAGE
    value: 'vcpkgandroidwus.azurecr.io/vcpkg-android:2025-03-12'
  - name: LINUX_DOCKER_IMAGE
    value: 'vcpkgandroidwus.azurecr.io/vcpkg-linux:2025-03-12'
  steps:
    # Note: /mnt is the Azure machines' temporary disk.
  - bash: |
      sudo mkdir /home/<USER>
      sudo chown `id -u` /home/<USER>
      sudo mkdir ${{ variables.WORKING_ROOT }} -m=777
      sudo rm -rf ${{ variables.WORKING_ROOT }}/failure-logs
      sudo mkdir ${{ variables.WORKING_ROOT }}/failure-logs -m=777
      sudo mkdir ${{ variables.VCPKG_DOWNLOADS }} -m=777
      # Move the docker layers to the temp disk.
      sudo mkdir -p /etc/docker
      echo '{"data-root": "/mnt/docker"}' | sudo tee /etc/docker/daemon.json
      sudo systemctl restart docker
      exit 0
    displayName: 'Create working directories'
  - bash: ./bootstrap-vcpkg.sh -skipDependencyChecks
    displayName: 'Bootstrap vcpkg'
    condition: eq('use default', '${{ parameters.vcpkgToolSha }}')
  - task: AzureCLI@2
    displayName: 'Build vcpkg with CMake'
    condition: ne('use default', '${{ parameters.vcpkgToolSha }}')
    inputs:
      azureSubscription: 'VcpkgPrFleet'
      scriptType: bash
      scriptLocation: 'inlineScript'
      inlineScript: |
        # This is a second pull but the vcpkgToolSha setting is used rarely.
        USER=$(id --user)
        az acr login --name vcpkgandroidwus
        docker pull ${{ variables.LINUX_DOCKER_IMAGE }}
        docker run --init -i --rm \
        -a stderr \
        -a stdout \
        --user $USER \
        --mount type=bind,source=$(Build.Repository.LocalPath),target=/vcpkg \
        --workdir /vcpkg \
        ${{ variables.LINUX_DOCKER_IMAGE }} \
        /vcpkg/scripts/azure-pipelines/bootstrap-from-source.sh ${{ parameters.vcpkgToolSha }}
  - task: AzureCLI@2
    displayName: '*** Test Modified Ports'
    inputs:
      azureSubscription: 'VcpkgPrFleet'
      scriptType: bash
      scriptLocation: 'inlineScript'  # Be very very careful that the exit code from the last pwsh is reported correctly
      inlineScript: |
        end=`date -u -d "2 days" '+%Y-%m-%dT%H:%MZ'`
        assetSas=`az storage container generate-sas --name cache --account-name vcpkgassetcachewus --as-user --auth-mode login --https-only --permissions rcl --expiry $end -o tsv`
        binarySas=`az storage container generate-sas --name cache --account-name vcpkgbinarycachewus --as-user --auth-mode login --https-only --permissions rclw --expiry $end -o tsv`
        echo Minting SAS tokens valid through $end
        USER=$(id --user)
        az acr login --name vcpkgandroidwus
        docker pull ${{ variables.ANDROID_DOCKER_IMAGE }}
        docker run --init -i --rm \
        -a stderr \
        -a stdout \
        --user $USER \
        --mount type=bind,source=$(Build.Repository.LocalPath),target=/vcpkg \
        --mount type=bind,source=$(WORKING_ROOT)/failure-logs,target=/vcpkg/failure-logs \
        --mount type=bind,source=/mnt/vcpkg-ci,target=/mnt/vcpkg-ci \
        --env X_VCPKG_ASSET_SOURCES="x-azurl,https://vcpkgassetcachewus.blob.core.windows.net/cache,$assetSas,readwrite" \
        --env ANDROID_NDK_HOME="${{ variables.ANDROID_NDK_HOME }}" \
        --workdir /vcpkg \
        ${{ variables.ANDROID_DOCKER_IMAGE }} \
        pwsh \
         -File scripts/azure-pipelines/test-modified-ports.ps1 \
         -Triplet ${{ replace(parameters.jobName, '_', '-') }} \
         -BuildReason $(Build.Reason) \
         -BinarySourceStub "x-azblob,https://vcpkgbinarycachewus.blob.core.windows.net/cache,$binarySas" \
         -WorkingRoot ${{ variables.WORKING_ROOT }}
  - task: PublishPipelineArtifact@1
    displayName: "Publish Artifact: failure logs for ${{ replace(parameters.jobName, '_', '-') }}"
    inputs:
      targetPath: '$(WORKING_ROOT)/failure-logs'
      artifact: "failure logs for ${{ replace(parameters.jobName, '_', '-') }}"
    condition: ne(variables['FAILURE_LOGS_EMPTY'], 'True')
  - bash: |
      python3 scripts/file_script.py /mnt/vcpkg-ci/installed/vcpkg/info/
    displayName: 'Build a file list for all packages'
    condition: always()
  - task: PublishPipelineArtifact@1
    displayName: "Publish Artifact: file lists for ${{ replace(parameters.jobName, '_', '-') }}"
    condition: always()
    inputs:
      targetPath: scripts/list_files
      artifact: "file lists for ${{ replace(parameters.jobName, '_', '-') }}"
  - task: PublishTestResults@2
    displayName: 'Publish Test Results'
    condition: ne(variables['XML_RESULTS_FILE'], '')
    inputs:
      testRunTitle: ${{ replace(parameters.jobName, '_', '-') }}
      testResultsFormat: xUnit
      testResultsFiles: $(XML_RESULTS_FILE)
      platform: ${{ replace(parameters.jobName, '_', '-') }}
