# Copyright (c) Microsoft Corporation.
# SPDX-License-Identifier: MIT
#

parameters:
  - name: vcpkgToolSha
    displayName: 'Custom SHA of vcpkg-tool to use rather than bootstrap'
    type: string
    default: 'use default'
  - name: jobName
    type: string
    default: 'x64_osx'
  - name: poolName
    type: string
  - name: tripletPattern
    displayName: 'Enable the triplets which contain this substring'
    type: string
    default: ''

jobs:
- job: ${{ parameters.jobName }}
  condition: and(succeeded(), contains('^${{ replace(parameters.jobName, '_', '-') }}$', '${{ parameters.tripletPattern }}'))
  pool:
    name: ${{ parameters.poolName }}
  workspace:
    clean: resources
  timeoutInMinutes: 2880 # 2 days
  variables:
  - name: WORKING_ROOT
    value: /Users/<USER>/Data
  - name: VCPKG_DOWNLOADS
    value: /Users/<USER>/Data/downloads
  steps:
  - bash: |
      sudo mkdir ${{ variables.VCPKG_DOWNLOADS }} || 0
      sudo chmod 777 ${{ variables.VCPKG_DOWNLOADS }} || 0
      exit 0
    displayName: 'Create ${{ variables.VCPKG_DOWNLOADS }}'
  - bash: ./bootstrap-vcpkg.sh
    displayName: 'Bootstrap vcpkg'
    condition: eq('use default', '${{ parameters.vcpkgToolSha }}')
  - bash: ./scripts/azure-pipelines/bootstrap-from-source.sh ${{ parameters.vcpkgToolSha }}
    displayName: "Build vcpkg with CMake"
    condition: ne('use default', '${{ parameters.vcpkgToolSha }}')
  - task: AzureCLI@2
    displayName: '*** Test Modified Ports'
    inputs:
      azureSubscription: 'VcpkgPrFleet'
      scriptType: 'pscore'
      scriptLocation: 'inlineScript'
      inlineScript: |
          $current = Get-Date -AsUtc
          $endDate = $current.AddDays(2)
          $end = Get-Date -Date $endDate -UFormat '+%Y-%m-%dT%H:%MZ'
          $assetSas = az storage container generate-sas --name cache --account-name vcpkgassetcachewus --as-user --auth-mode login --https-only --permissions rcl --expiry $end -o tsv | Out-String
          $assetSas = $assetSas.Trim()
          $binarySas = az storage container generate-sas --name cache --account-name vcpkgbinarycachewus --as-user --auth-mode login --https-only --permissions rclw --expiry $end -o tsv | Out-String
          $binarySas = $binarySas.Trim()
          $env:X_VCPKG_ASSET_SOURCES = "x-azurl,https://vcpkgassetcachewus.blob.core.windows.net/cache,$assetSas,readwrite"
          & scripts/azure-pipelines/test-modified-ports.ps1 -Triplet ${{ replace(parameters.jobName, '_', '-') }} -BuildReason $(Build.Reason) -BinarySourceStub "x-azblob,https://vcpkgbinarycachewus.blob.core.windows.net/cache,$binarySas" -WorkingRoot $env:WORKING_ROOT -ArtifactStagingDirectory $(Build.ArtifactStagingDirectory)
  - task: PublishPipelineArtifact@1
    displayName: 'Publish Artifact: failure logs for x64-osx'
    inputs:
      targetPath: '$(Build.ArtifactStagingDirectory)/failure-logs'
      artifact: "failure logs for ${{ replace(parameters.jobName, '_', '-') }}"
    condition: ne(variables['FAILURE_LOGS_EMPTY'], 'True')
  - bash: python3 scripts/file_script.py /Users/<USER>/Data/installed/vcpkg/info/
    displayName: 'Build a file list for all packages'
    condition: always()
  - task: PublishPipelineArtifact@1
    displayName: "Publish Artifact: file lists for ${{ replace(parameters.jobName, '_', '-') }}"
    condition: always()
    inputs:
      targetPath: scripts/list_files
      artifact: "file lists for ${{ replace(parameters.jobName, '_', '-') }}"
  - task: PublishTestResults@2
    displayName: 'Publish Test Results'
    condition: ne(variables['XML_RESULTS_FILE'], '')
    inputs:
      testRunTitle: ${{ replace(parameters.jobName, '_', '-') }}
      testResultsFormat: xUnit
      testResultsFiles: $(XML_RESULTS_FILE)
      platform: ${{ replace(parameters.jobName, '_', '-') }}
      configuration: static
