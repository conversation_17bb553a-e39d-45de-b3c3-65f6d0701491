# Copyright (c) Microsoft Corporation.
# SPDX-License-Identifier: MIT
#
parameters:
  - name: vcpkgToolSha
    displayName: 'Custom SHA of vcpkg-tool to use rather than bootstrap'
    type: string
    default:  'use default'
  - name: tripletPattern
    displayName: 'Enable triplets which contain this substring'
    type: string
    default: '-'

jobs:
- template: windows/azure-pipelines.yml
  parameters:
    jobName: x86_windows
    vcpkgToolSha: ${{ parameters.vcpkgToolSha }}
    tripletPattern: ${{ parameters.tripletPattern }}

- template: windows/azure-pipelines.yml
  parameters:
    jobName: x64_windows
    vcpkgToolSha: ${{ parameters.vcpkgToolSha }}
    tripletPattern: ${{ parameters.tripletPattern }}

- template: windows/azure-pipelines.yml
  parameters:
    jobName: x64_windows_release
    vcpkgToolSha: ${{ parameters.vcpkgToolSha }}
    tripletPattern: ${{ parameters.tripletPattern }}

- template: windows/azure-pipelines.yml
  parameters:
    jobName: x64_windows_static
    vcpkgToolSha: ${{ parameters.vcpkgToolSha }}
    tripletPattern: ${{ parameters.tripletPattern }}

- template: windows/azure-pipelines.yml
  parameters:
    jobName: x64_windows_static_md
    vcpkgToolSha: ${{ parameters.vcpkgToolSha }}
    tripletPattern: ${{ parameters.tripletPattern }}

- template: windows/azure-pipelines.yml
  parameters:
    jobName: x64_uwp
    vcpkgToolSha: ${{ parameters.vcpkgToolSha }}
    tripletPattern: ${{ parameters.tripletPattern }}

- template: windows/azure-pipelines.yml
  parameters:
    jobName: arm64_windows
    vcpkgToolSha: ${{ parameters.vcpkgToolSha }}
    tripletPattern: ${{ parameters.tripletPattern }}

- template: windows/azure-pipelines.yml
  parameters:
    jobName: arm64_windows_static_md
    vcpkgToolSha: ${{ parameters.vcpkgToolSha }}
    tripletPattern: ${{ parameters.tripletPattern }}

- template: windows/azure-pipelines.yml
  parameters:
    jobName: arm64_uwp
    vcpkgToolSha: ${{ parameters.vcpkgToolSha }}
    tripletPattern: ${{ parameters.tripletPattern }}

- template: osx/azure-pipelines.yml
  parameters:
    jobName: x64_osx
    poolName: 'PrOsx-2025-01-24'
    vcpkgToolSha: ${{ parameters.vcpkgToolSha }}
    tripletPattern: ${{ parameters.tripletPattern }}

- template: osx/azure-pipelines.yml
  parameters:
    jobName: arm64_osx
    poolName: 'PrOsx-2025-01-24-arm64'
    vcpkgToolSha: ${{ parameters.vcpkgToolSha }}
    tripletPattern: ${{ parameters.tripletPattern }}

- template: linux/azure-pipelines.yml
  parameters:
    jobName: x64_linux
    vcpkgToolSha: ${{ parameters.vcpkgToolSha }}
    tripletPattern: ${{ parameters.tripletPattern }}

- template: android/azure-pipelines.yml
  parameters:
    jobName: arm_neon_android
    vcpkgToolSha: ${{ parameters.vcpkgToolSha }}
    tripletPattern: ${{ parameters.tripletPattern }}

- template: android/azure-pipelines.yml
  parameters:
    jobName: x64_android
    vcpkgToolSha: ${{ parameters.vcpkgToolSha }}
    tripletPattern: ${{ parameters.tripletPattern }}

- template: android/azure-pipelines.yml
  parameters:
    jobName: arm64_android
    vcpkgToolSha: ${{ parameters.vcpkgToolSha }}
    tripletPattern: ${{ parameters.tripletPattern }}
