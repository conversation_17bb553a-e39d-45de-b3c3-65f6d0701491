# Copyright (c) Microsoft Corporation.
# SPDX-License-Identifier: MIT

$ErrorActionPreference = 'Stop'
$ProgressPreference = 'SilentlyContinue'

Write-Host 'Disabling pagefile...'
wmic computersystem set AutomaticManagedPagefile=False
wmic pagefileset delete

$av = Get-Command Add-MPPreference -ErrorAction SilentlyContinue
if ($null -eq $av) {
  Write-Host 'AntiVirus not installed, skipping exclusions.'
} else {
  Write-Host 'Configuring AntiVirus exclusions...'
  Add-MpPreference -ExclusionPath C:\agent
  Add-MPPreference -ExclusionPath D:\
  Add-MPPreference -ExclusionPath E:\
  Add-MPPreference -ExclusionProcess ninja.exe
  Add-MPPreference -ExclusionProcess clang-cl.exe
  Add-MPPreference -ExclusionProcess cl.exe
  Add-MPPreference -ExclusionProcess link.exe
  Add-MPPreference -ExclusionProcess python.exe
}
