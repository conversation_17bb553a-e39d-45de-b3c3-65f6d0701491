# Copyright (c) Microsoft Corporation.
# SPDX-License-Identifier: MIT
#

parameters:
  - name: vcpkgToolSha
    displayName: 'Custom SHA of vcpkg-tool to use rather than bootstrap'
    type: string
    default: 'use default'
  - name: jobName
    type: string
    default: 'x86_windows'
  - name: tripletPattern
    displayName: 'Enable the triplets which contain this substring'
    type: string
    default: ''

jobs:
- job: ${{ parameters.jobName }}
  condition: and(succeeded(), contains('^${{ replace(parameters.jobName, '_', '-') }}$', '${{ parameters.tripletPattern }}'))
  pool:
    name: PrWin-WUS
    demands: ImageVersionOverride -equals 2025.03.12
  timeoutInMinutes: 2880 # 2 days
  variables:
  - name: WORKING_ROOT
    value: D:\
  - name: VCPKG_DOWNLOADS
    value: D:\downloads
  - name: DiffFile
    value: $(Build.ArtifactStagingDirectory)\format.diff
  - name: ExtraChecksTriplet
    value: x86-windows
  steps:
  - script: .\bootstrap-vcpkg.bat
    displayName: 'Bootstrap vcpkg'
    condition: eq('use default', '${{ parameters.vcpkgToolSha }}')
  - script: .\scripts\azure-pipelines\windows\bootstrap-from-source.cmd ${{ parameters.vcpkgToolSha }}
    displayName: "Build vcpkg with CMake"
    condition: ne('use default', '${{ parameters.vcpkgToolSha }}')
  - script: '.\vcpkg.exe format-manifest --all'
    displayName: 'Format Manifests'
    condition: eq('${{ replace(parameters.jobName, '_', '-') }}', '${{ variables.ExtraChecksTriplet }}')
  - task: PowerShell@2
    displayName: 'Create Diff'
    condition: eq('${{ replace(parameters.jobName, '_', '-') }}', '${{ variables.ExtraChecksTriplet }}')
    inputs:
      filePath: scripts/azure-pipelines/Create-PRDiff.ps1
      arguments: "-DiffFile '$(DiffFile)'"
      pwsh: true
  - task: PublishPipelineArtifact@1
    displayName: 'Publish Format and Documentation Diff'
    condition: and(eq('${{ replace(parameters.jobName, '_', '-') }}', '${{ variables.ExtraChecksTriplet }}'), failed())
    inputs:
      targetPath: '$(DiffFile)'
      artifact: 'format.diff'
  - task: AzureCLI@2
    displayName: '*** Test Modified Ports'
    inputs:
      azureSubscription: 'VcpkgPrFleet'
      scriptType: 'pscore'
      scriptLocation: 'inlineScript'
      inlineScript: |
          $current = Get-Date -AsUtc
          $endDate = $current.AddDays(2)
          $end = Get-Date -Date $endDate -UFormat '+%Y-%m-%dT%H:%MZ'
          $assetSas = az storage container generate-sas --name cache --account-name vcpkgassetcachewus --as-user --auth-mode login --https-only --permissions rcl --expiry $end -o tsv | Out-String
          $assetSas = $assetSas.Trim()
          $binarySas = az storage container generate-sas --name cache --account-name vcpkgbinarycachewus --as-user --auth-mode login --https-only --permissions rclw --expiry $end -o tsv | Out-String
          $binarySas = $binarySas.Trim()
          $env:X_VCPKG_ASSET_SOURCES = "x-azurl,https://vcpkgassetcachewus.blob.core.windows.net/cache,$assetSas,readwrite"
          ./vcpkg.exe fetch python3
          & scripts/azure-pipelines/test-modified-ports.ps1 -Triplet ${{ replace(parameters.jobName, '_', '-') }} -BuildReason $(Build.Reason) -BinarySourceStub "x-azblob,https://vcpkgbinarycachewus.blob.core.windows.net/cache,$binarySas" -WorkingRoot $env:WORKING_ROOT -ArtifactStagingDirectory $(Build.ArtifactStagingDirectory)
  - task: PowerShell@2
    displayName: 'Validate version files'
    condition: eq('${{ replace(parameters.jobName, '_', '-') }}', '${{ variables.ExtraChecksTriplet }}')
    inputs:
      filePath: 'scripts/azure-pipelines/windows/validate-version-files.ps1'
      pwsh: true
  - task: PublishPipelineArtifact@1
    displayName: "Publish Artifact: failure logs for ${{ replace(parameters.jobName, '_', '-') }}"
    inputs:
      targetPath: '$(Build.ArtifactStagingDirectory)\failure-logs'
      artifact: "failure logs for ${{ replace(parameters.jobName, '_', '-') }}"
    condition: ne(variables['FAILURE_LOGS_EMPTY'], 'True')
  - task: PowerShell@2
    displayName: 'Build a file list for all packages'
    condition: always()
    inputs:
      targetType: inline
      script: |
        & $(.\vcpkg fetch python3) .\scripts\file_script.py D:\installed\vcpkg\info\
      pwsh: true
  - task: PublishPipelineArtifact@1
    displayName: "Publish Artifact: file lists for ${{ replace(parameters.jobName, '_', '-') }}"
    condition: always()
    inputs:
      targetPath: scripts/list_files
      artifact: "file lists for ${{ replace(parameters.jobName, '_', '-') }}"
  - task: PublishTestResults@2
    displayName: 'Publish Test Results'
    condition: ne(variables['XML_RESULTS_FILE'], '')
    inputs:
      testRunTitle: ${{ replace(parameters.jobName, '_', '-') }}
      testResultsFormat: xUnit
      testResultsFiles: $(XML_RESULTS_FILE)
      platform: ${{ replace(parameters.jobName, '_', '-') }}
