###########################################################################
## This file defines the current expected build state of ports in CI.
##
## States
##   <unspecified> (default) -
##          If a port is missing from this file then it is assumed
##          to build successfully if not blocked by a dependency.
##   pass - The port must build successfully in the CI system. It is a hard
##          error if the port build is blocked by a failed dependency.
##   fail - The port build is expected to fail in the CI system.
##          This is not necessarily the same as if a port is expected to build
##          on a developers machine because it may fail due to the machine
##          configuration. When set to fail the CI system will silently skip
##          the port for pull request CI runs. But the CI system will still
##          attempt to build the port in scheduled runs, reporting unexpected
##          build success as a CI failure.
##   skip - Do not build this port in the CI system.
##          This is added to ports that may be flaky or conflict with other
##          ports.  Please comment for why a port is skipped so it can be
##          removed when the issue is resolved.
##
##
## CI tested triplets:
##    arm-neon-android
##    arm64-android
##    arm64-osx
##    arm64-uwp
##    arm64-windows
##    arm64-windows-static-md
##    x64-android
##    x64-linux
##    x64-osx
##    x64-uwp
##    x64-windows
##    x64-windows-release
##    x64-windows-static
##    x64-windows-static-md
##    x86-windows
##

# Add new items alphabetically

ace:arm-neon-android=fail
ace:arm64-android=fail
ace:x64-android=fail
# ada-url requires gcc12 and clang15 or later. linux-x64 always fails because it uses gcc11.
ada-url:x64-linux=fail
angelscript:x64-osx=fail
apr:arm-neon-android=fail
apr:arm64-android=fail
apr:x64-android=fail
apsi:arm-neon-android=fail
apsi:x64-android=fail
# Broken with CUDA 12; needs update to 3.8.3 and https://github.com/arrayfire/arrayfire/issues/3349 fixed
arrayfire:x64-linux=fail
avro-c:arm-neon-android=fail
avro-c:arm64-android=fail
avro-c:x64-android=fail
# C++20 conformant compiler required
atliac-minitest:arm64-uwp=fail
atliac-minitest:x64-linux=fail
atliac-minitest:x64-uwp=fail
backward-cpp:arm-neon-android=fail
backward-cpp:arm64-android=fail
backward-cpp:x64-android=fail
# conflict https://github.com/microsoft/vcpkg/pull/32645
bde:arm64-osx=skip
bde:x64-linux=skip
bde:x64-osx=skip
bde:x64-windows-release=skip
bde:x64-windows-static-md=skip
bde:x64-windows-static=skip
bde:x64-windows=skip
bde:x86-windows=skip
berkeleydb:arm-neon-android=fail
berkeleydb:arm64-android=fail
berkeleydb:x64-android=fail
binlog:arm-neon-android=fail
################################################################
# <BLAS+LAPACK>
# These skips need to match the decision tree for which backends we use; see ports/blas/portfile.cmake
lapack-test:arm64-osx=pass # accelerate framework
lapack-test:arm64-uwp=pass # clapack
lapack-test:arm64-windows-static-md=pass # clapack
lapack-test:arm64-windows=pass # clapack
lapack-test:x64-linux=pass # lapack-reference[noblas]
lapack-test:x64-osx=pass # accelerate framework
lapack-test:x64-uwp=pass # clapack
lapack-test:x64-windows-release=pass # lapack-reference[noblas]
lapack-test:x64-windows-static-md=pass # lapack-reference[blas]
lapack-test:x64-windows-static=pass # lapack-reference[blas]
lapack-test:x64-windows=pass # lapack-reference[noblas]
lapack-test:x86-windows=pass # lapack-reference[noblas]
vcpkg-ci-blas:arm64-android=pass # openblas
vcpkg-ci-blas:arm64-osx=pass # accelerate framework
vcpkg-ci-blas:arm64-uwp=pass # openblas
vcpkg-ci-blas:arm64-windows-static-md=pass # openblas
vcpkg-ci-blas:arm64-windows=pass # openblas
vcpkg-ci-blas:x64-linux=pass # openblas
vcpkg-ci-blas:x64-osx=pass # accelerate framework
vcpkg-ci-blas:x64-uwp=pass # openblas
vcpkg-ci-blas:x64-windows-release=pass # openblas
vcpkg-ci-blas:x64-windows-static-md=pass # lapack-reference[blas]
vcpkg-ci-blas:x64-windows-static=pass # lapack-reference[blas]
vcpkg-ci-blas:x64-windows=pass # openblas
vcpkg-ci-blas:x86-windows=pass # openblas

clapack:arm-neon-android=fail
clapack:arm64-android=fail
clapack:arm64-osx=skip
clapack:x64-android=fail
clapack:x64-linux=skip
clapack:x64-osx=skip
clapack:x64-windows-release=skip
clapack:x64-windows-static-md=skip
clapack:x64-windows-static=skip
clapack:x64-windows=skip
clapack:x86-windows=skip
lapack-reference:arm-neon-android=fail
lapack-reference:arm64-android=fail
lapack-reference:arm64-osx=skip
lapack-reference:arm64-uwp=skip
lapack-reference:arm64-windows-static-md=skip
lapack-reference:arm64-windows=skip
lapack-reference:x64-android=fail
lapack-reference:x64-osx=skip
lapack-reference:x64-uwp=skip
#openblas:arm64-osx=skip
#openblas:x64-osx=skip
#openblas:x64-windows-static-md=skip
#openblas:x64-windows-static=skip
# </BLAS+LAPACK>
################################################################
# Bug in VS2022 17.13 where <wchar.h> breaks with -Zc:arm64-aliased-neon-types-
blend2d:arm64-uwp=fail
blend2d:arm64-windows-static-md=fail
blend2d:arm64-windows=fail
blitz:x64-android=fail
boinc:arm-neon-android=fail
boinc:arm64-android=fail
boinc:x64-android=fail
bond:arm-neon-android=fail
bond:arm64-android=fail
bond:arm64-osx=fail
bond:x64-android=fail
bond:x64-osx=fail
# Conflicts with openssl
boringssl:arm-neon-android=skip
boringssl:arm64-android=skip
boringssl:arm64-osx=skip
boringssl:arm64-uwp=skip
boringssl:arm64-windows-static-md=skip
boringssl:arm64-windows=skip
boringssl:x64-android=skip
boringssl:x64-linux=skip
boringssl:x64-osx=skip
boringssl:x64-uwp=skip
boringssl:x64-windows-release=skip
boringssl:x64-windows-static-md=skip
boringssl:x64-windows-static=skip
boringssl:x64-windows=skip
boringssl:x86-windows=skip
brpc:x64-android=fail
buck-yeh-bux:x64-uwp=skip
buck-yeh-bux:x86-windows=skip
caf:arm-neon-android=fail
caf:arm64-android=fail
caf:arm64-uwp=fail
caf:x64-android=fail
caf:x64-uwp=fail
# file conflict with dbg-macro
c-dbg-macro:arm64-osx=skip
c-dbg-macro:arm64-uwp=skip
c-dbg-macro:arm64-windows-static-md=skip
c-dbg-macro:arm64-windows=skip
c-dbg-macro:x64-linux=skip
c-dbg-macro:x64-osx=skip
c-dbg-macro:x64-uwp=skip
c-dbg-macro:x64-windows-release=skip
c-dbg-macro:x64-windows-static-md=skip
c-dbg-macro:x64-windows-static=skip
c-dbg-macro:x64-windows=skip
c-dbg-macro:x86-windows=skip
casclib:arm64-uwp=fail
casclib:x64-uwp=fail
catch-classic:arm-neon-android=skip
catch-classic:arm64-android=skip
catch-classic:arm64-osx=skip
catch-classic:arm64-uwp=skip
catch-classic:arm64-windows-static-md=skip
catch-classic:arm64-windows=skip
catch-classic:x64-android=skip
catch-classic:x64-linux=skip
catch-classic:x64-osx=skip
catch-classic:x64-uwp=skip
catch-classic:x64-windows-release=skip
catch-classic:x64-windows-static-md=skip
catch-classic:x64-windows-static=skip
catch-classic:x64-windows=skip
catch-classic:x86-windows=skip
cctag:x64-windows-static-md=fail
cello:arm64-uwp=fail
cello:x64-uwp=fail
chakracore:x64-linux=skip
# chromium-base has several problems and is upgraded to "skip" because it hits a lot of servers that can slow CI
#  broken on Windows because it does not yet support VS2022
chromium-base:x64-windows-release=skip
chromium-base:x64-windows-static-md=skip
chromium-base:x64-windows-static=skip
chromium-base:x64-windows=skip
#  broken on Linux because
chromium-base:x64-linux=skip
#  broken on OSX for unknown reasons; it appears to be messing with some form of system-wide cache
#  because the first attempt to build it fails, but subsequent attempts succeed
chromium-base:x64-osx=skip
clamav:arm64-windows=fail
clblas:arm-neon-android=fail
clblas:arm64-android=fail
clblas:x64-android=fail
clockutils:arm-neon-android=fail
clockutils:arm64-android=fail
clockutils:x64-android=fail
clrng:arm-neon-android=fail
coin-or-ipopt:x64-linux=fail
coroutine:arm-neon-android=fail
coroutine:arm64-android=fail
coroutine:arm64-osx=fail
coroutine:x64-android=fail
coroutine:x64-osx=fail
cpp-ipc:arm-neon-android=fail
cpp-ipc:arm64-android=fail
cpp-ipc:x64-android=fail
cppmicroservices:arm-neon-android=fail
cppmicroservices:arm64-android=fail
cppmicroservices:arm64-uwp=fail
cppmicroservices:arm64-windows-static-md=fail
cppmicroservices:arm64-windows=fail
cppmicroservices:x64-android=fail
cppmicroservices:x64-uwp=fail
# Requires full C++20 support, currently absent from CI for these triplets.
cppcoro:arm-neon-android=fail
cppcoro:arm64-android=fail
cppcoro:arm64-osx=fail
cppcoro:x64-android=fail
cppcoro:x64-linux=fail
cppcoro:x64-osx=fail
cppslippi:x64-linux=fail
crashpad:x64-linux=fail #Compilation failed due to the lack of Clang++ compiler.
cserialport:arm-neon-android=fail
cserialport:arm64-android=fail
cserialport:x64-android=fail
ctbench:arm64-osx=fail
ctbench:x64-osx=fail
ctp:x64-android=fail
czmq:arm-neon-android=fail
czmq:arm64-android=fail
czmq:x64-android=fail
darknet:arm-neon-android=fail
darknet:arm64-android=fail
darknet:x64-android=fail
dbg-macro:arm-neon-android=skip
dbg-macro:arm64-android=skip
dbg-macro:x64-android=skip
# Since pipeline cannot automatically install dbghelp dependency, skip this detection
dbghelp:arm64-uwp=skip
dbghelp:arm64-windows-static-md=skip
dbghelp:arm64-windows=skip
dbghelp:x64-uwp=skip
dbghelp:x64-windows-release=skip
dbghelp:x64-windows-static-md=skip
dbghelp:x64-windows-static=skip
dbghelp:x64-windows=skip
dbghelp:x86-windows=skip
# Cross builds depend on try_run results
dcmtk:arm-neon-android=fail
dcmtk:arm64-android=fail
dcmtk:arm64-windows-static-md=fail
dcmtk:arm64-windows=fail
dcmtk:x64-android=fail
# legacy directxsdk which conflicts with dxsdk-d3dx
directxsdk:x64-windows-release=skip
directxsdk:x64-windows-static-md=skip
directxsdk:x64-windows-static=skip
directxsdk:x64-windows=skip
directxsdk:x86-windows=skip
discord-rpc:arm64-uwp=fail
discord-rpc:x64-uwp=fail
# Needs newer GCC than we have
discordcoreapi:x64-linux=fail
# Needs newer standard library than we have
discordcoreapi:x64-osx=fail
discount:x64-android=fail
eathread:x64-android=fail
ecal:arm-neon-android=fail
ecal:arm64-android=fail
ecal:x64-android=fail
elfio:arm-neon-android=fail
elfio:arm64-android=fail
elfio:x64-android=fail
# Checks for gnu extension so only works with gcc.
elfutils:arm-neon-android=fail
elfutils:arm64-android=fail
elfutils:arm64-osx=fail
elfutils:x64-android=fail
elfutils:x64-osx=fail
fastdds:arm-neon-android=fail
# clang rejects variable length arrays
fbgemm:x64-android=fail
# variable length arrays in C++ are a Clang extension
fbbgemmerror:arm-neon-android=fail
fbbgemmerror:arm64-android=fail
fbbgemmerror:x64-android=fail
flashlight-cpu:x64-linux=fail
flashlight-cuda:x64-linux=fail
flint:arm-neon-android=fail
flint:arm64-android=fail
flint:x64-android=fail
fltk:arm-neon-android=fail
fltk:arm64-android=fail
fltk:x64-android=fail
# fluidlite conflicts with fluidsynth; we test fluidsynth rather than fluidlite because
# fluidlite has no dependencies and thus is less likely to be broken by another package.
fluidlite:arm64-osx=skip
fluidlite:arm64-uwp=skip
fluidlite:arm64-windows-windows-static-md=skip
fluidlite:arm64-windows=skip
fluidlite:x64-linux=skip
fluidlite:x64-osx=skip
fluidlite:x64-uwp=skip
fluidlite:x64-windows-release=skip
fluidlite:x64-windows-static-md=skip
fluidlite:x64-windows-static=skip
fluidlite:x64-windows=skip
fluidlite:x86-windows=skip
fmi4cpp:arm64-uwp=fail
fmi4cpp:x64-uwp=fail
folly:arm64-android=fail
folly:x64-android=fail
# unsupported option '-mfloat-abi='
# ALooper_pollAll no longer available
freeglut:arm-neon-android=fail
freeglut:arm64-android=fail
freeglut:x64-android=fail
# Needs XQuartz
freeglut:arm64-osx=fail
freeglut:x64-osx=fail
freerdp:arm64-osx=fail
freerdp:x64-osx=fail
fruit:arm-neon-android=fail
fruit:arm64-android=fail
fruit:x64-android=fail
ftgl:arm-neon-android=fail
ftgl:arm64-android=fail
ftgl:x64-android=fail
fuzzylite:arm-neon-android=fail
fuzzylite:arm64-android=fail
fuzzylite:x64-android=fail
g2o:arm64-uwp=fail
g3log:arm-neon-android=fail
g3log:arm64-android=fail
g3log:x64-android=fail
gainput:arm-neon-android=fail
gainput:arm64-android=fail
gainput:x64-android=fail
gamenetworkingsockets:arm-neon-android=fail
gamenetworkingsockets:arm64-android=fail
gamenetworkingsockets:x64-android=fail
gdk-pixbuf:arm-neon-android=fail
gdk-pixbuf:arm64-android=fail
gdk-pixbuf:x64-android=fail
gegl:arm64-osx=fail
gegl:x64-osx=fail
gemmlowp:arm-neon-android=fail
gemmlowp:arm64-android=fail
gemmlowp:x64-android=fail
gherkin-c:arm-neon-android=fail
gherkin-c:arm64-android=fail
gherkin-c:x64-android=fail
# Conflicts with libevent
gherkin-c:arm64-uwp=skip
gherkin-c:arm64-windows-static-md=skip
gherkin-c:arm64-windows=skip
gherkin-c:x64-linux=skip
gherkin-c:x64-osx=skip
gherkin-c:x64-uwp=skip
gherkin-c:x64-windows-release=skip
gherkin-c:x64-windows-static-md=skip
gherkin-c:x64-windows-static=skip
gherkin-c:x64-windows=skip
gherkin-c:x86-windows=skip
gl3w:arm-neon-android=fail
gl3w:arm64-android=fail
gl3w:x64-android=fail
glfw3:arm-neon-android=fail
glfw3:arm64-android=fail
glfw3:arm64-uwp=fail
glfw3:x64-android=fail
glfw3:x64-uwp=fail
glibmm:arm64-windows-static-md=fail
glibmm:x64-windows-static-md=fail
glibmm:x64-windows-static=fail
graphicsmagick:arm64-uwp=fail
graphicsmagick:x64-uwp=fail
# gsoap does not offer stable public source downloads
gsoap:x64-android=fail
gsoap:x64-windows-release=skip
gsoap:x64-windows-static-md=skip
gsoap:x64-windows-static=skip
gsoap:x64-windows=skip
gsoap:x86-windows=skip
gstreamer:arm-neon-android=fail
gstreamer:arm64-android=fail
gstreamer:x64-android=fail
# upstream issue https://gitlab.freedesktop.org/gstreamer/gstreamer/-/issues/1629.
gstreamer:x64-windows-static-md=skip
gstreamer:x64-windows-static=skip
gul14:arm-neon-android=fail
gul14:arm64-android=fail
gul14:x64-android=fail
gz-tools:arm-neon-android=fail
gz-tools:arm64-android=fail
gz-tools:x64-android=fail
halide:x64-windows-static=fail
hello-imgui:arm-neon-android=fail
hello-imgui:arm64-android=fail
hello-imgui:x64-android=fail
hexl:x64-android=fail
hpx:x64-windows-static=fail
hwloc:arm-neon-android=fail
hwloc:arm64-android=fail
hwloc:x64-android=fail
hyperscan:x64-android=fail
iceoryx:arm-neon-android=fail
iceoryx:arm64-android=fail
iceoryx:x64-android=fail
ijg-libjpeg:arm-neon-android=skip
ijg-libjpeg:arm64-android=skip
ijg-libjpeg:arm64-osx=skip
ijg-libjpeg:arm64-uwp=skip
ijg-libjpeg:arm64-windows-static-md=skip
ijg-libjpeg:arm64-windows=skip
ijg-libjpeg:x64-android=skip
ijg-libjpeg:x64-linux=skip
ijg-libjpeg:x64-osx=skip
ijg-libjpeg:x64-uwp=skip
ijg-libjpeg:x64-windows-release=skip
ijg-libjpeg:x64-windows-static-md=fail
ijg-libjpeg:x64-windows-static=skip
ijg-libjpeg:x64-windows=skip
ijg-libjpeg:x86-windows=skip
intelrdfpmathlib:arm-neon-android=fail
intelrdfpmathlib:arm64-android=fail
intelrdfpmathlib:x64-android=fail
io2d:arm-neon-android=fail
io2d:arm64-android=fail
io2d:x64-android=fail
irrlicht:x64-android=fail
isal:x64-android=fail
# Failing on try_run() in cross builds
itk:arm-neon-android=fail
itk:arm64-android=fail
itk:arm64-windows-static-md=fail
itk:arm64-windows=fail
itk:x64-android=fail
jaeger-client-cpp:arm-neon-android=fail
jaeger-client-cpp:x64-android=fail
jemalloc:arm-neon-android=fail
jemalloc:arm64-android=fail
jemalloc:arm64-uwp=fail
jemalloc:arm64-windows-static-md=fail
jemalloc:arm64-windows=fail
jemalloc:x64-android=fail
jemalloc:x64-uwp=fail
jinja2cpplight:arm-neon-android=fail
jinja2cpplight:arm64-android=fail
jinja2cpplight:x64-android=fail
# needs android-29
juce:arm-neon-android=fail
juce:arm64-android=fail
juce:x64-android=fail
lcm:arm-neon-android=fail
lcm:arm64-android=fail
lcm:arm64-windows-static-md=fail
lcm:x64-android=fail
lcm:x64-windows-static-md=fail
lcm:x64-windows-static=fail
leptonica:arm64-uwp=fail
leptonica:x64-uwp=fail
libaiff:x64-linux=fail
libcanberra:arm-neon-android=fail
libcanberra:arm64-android=fail
libcanberra:x64-android=fail
libcpplocate:arm-neon-android=fail
libcpplocate:arm64-android=fail
libcpplocate:x64-android=fail
libdc1394:arm-neon-android=fail
libdc1394:arm64-android=fail
libdc1394:x64-android=fail
libfreenect2:arm64-windows-static-md=fail
libfreenect2:arm64-windows=fail
# error: call to undeclared function 'mktime_z'
libgnutls:arm-neon-android=fail
libgnutls:arm64-android=fail
libgnutls:x64-android=fail
# Fails to build due to incompatible delcaration of select in macOS 14.2
libgo:x64-android=fail
libgo:x64-osx=fail
libgxps:x64-windows-static=fail
libhdfs3:arm-neon-android=fail
libhdfs3:arm64-android=fail
libhdfs3:arm64-osx=fail
libhdfs3:x64-android=fail
libhdfs3:x64-linux=fail
libhdfs3:x64-osx=fail
# 120 min build time for libjxl arm64-uwp-rel, reason unknown
libjxl:arm64-uwp=skip
liblo:arm64-uwp=fail
liblo:x64-uwp=fail
# libmariadb conflicts with libmysql
libmariadb:arm64-osx=skip
libmariadb:arm64-windows-static-md=skip
libmariadb:arm64-windows=skip
libmariadb:x64-linux=skip
libmariadb:x64-osx=skip
libmariadb:x64-windows-release=skip
libmariadb:x64-windows-static-md=skip
libmariadb:x64-windows-static=skip
libmariadb:x64-windows=skip
libmaxminddb:arm-neon-android=fail
libmaxminddb:arm64-android=fail
libmaxminddb:x64-android=fail
# libmesh installs tons of problematic files that conflict with other ports (boost, eigen, etc)
libmesh:x64-linux=skip
libmikmod:arm-neon-android=fail
libmikmod:arm64-android=fail
libmikmod:x64-android=fail
libmodman:x64-windows-static=fail
libmpeg2:arm-neon-android=fail
libmpeg2:arm64-android=fail
libmpeg2:x64-android=fail
libmysql:x86-windows=skip
libopensp:arm-neon-android=fail
libopensp:arm64-android=fail
libopensp:x64-android=fail
libp7client:x64-android=fail
libpng-apng:arm64-uwp=skip
libpng-apng:arm64-windows-static-md=skip
libpng-apng:arm64-windows=skip
libpng-apng:x64-linux=skip
libpng-apng:x64-osx=skip
libpng-apng:x64-uwp=skip
libpng-apng:x64-windows-release=skip
libpng-apng:x64-windows-static-md=skip
libpng-apng:x64-windows-static=skip
libpng-apng:x64-windows=skip
libpng-apng:x86-windows=skip
# The developer of libqcow does not offer stable release archives
libqcow:arm-neon-android=skip
libqcow:arm64-android=skip
libqcow:arm64-uwp=skip
libqcow:arm64-windows-static-md=skip
libqcow:arm64-windows=skip
libqcow:x64-android=skip
libqcow:x64-linux=skip
libqcow:x64-osx=skip
libqcow:x64-uwp=skip
libqcow:x64-windows-release=skip
libqcow:x64-windows-static-md=skip
libqcow:x64-windows-static=skip
libqcow:x64-windows=skip
libqcow:x86-windows=skip
# Conflicts with openssl
libressl:arm-neon-android=skip
libressl:arm64-android=skip
libressl:arm64-osx=skip
libressl:arm64-uwp=skip
libressl:arm64-windows-static-md=skip
libressl:arm64-windows=skip
libressl:x64-android=skip
libressl:x64-linux=skip
libressl:x64-osx=skip
libressl:x64-uwp=skip
libressl:x64-windows-release=skip
libressl:x64-windows-static-md=skip
libressl:x64-windows-static=skip
libressl:x64-windows=skip
libressl:x86-windows=skip
libsoundio:arm64-windows-static-md=fail
libsoundio:arm64-windows=fail
libtar:arm-neon-android=fail
libtar:arm64-android=fail
libtcod:arm-neon-android=fail
libtcod:arm64-android=fail
libtcod:x64-android=fail
libtomcrypt:arm64-uwp=fail
libtomcrypt:arm64-windows-static-md=fail
libtomcrypt:arm64-windows=fail
libusb-win32:arm64-uwp=fail
libusb-win32:x64-uwp=fail
# upstream issue https://github.com/microsoft/vcpkg/pull/13765#issuecomment-699710253
libvmdk:arm-neon-android=skip
libvmdk:arm64-android=skip
libvmdk:arm64-osx=skip
libvmdk:arm64-windows-static-md=skip
libvmdk:arm64-windows=skip
libvmdk:x64-android=skip
libvmdk:x64-linux=skip
libvmdk:x64-osx=skip
libvmdk:x64-windows-release=skip
libvmdk:x64-windows-static-md=skip
libvmdk:x64-windows-static=skip
libvmdk:x64-windows=skip
libvmdk:x86-windows=skip
libwandio:arm-neon-android=fail
libwandio:arm64-android=fail
libwandio:x64-android=fail
libxaw:x64-windows-static=skip
# Clang 19 thinks inline asm in this port is malformed:
libxt:x64-windows-release=fail
libxt:x64-windows=fail
# Inline asm used doesn't appear to support arm64-windows:
libxt:arm64-windows-static-md=fail
libxt:arm64-windows=fail
linenoise-ng:arm64-uwp=fail
linenoise-ng:x64-uwp=fail
linenoise-ng:x64-windows-static-md=fail
live555:arm64-uwp=fail
live555:x64-uwp=fail
llfio:arm-neon-android=fail
llfio:arm64-android=fail
llfio:x64-android=fail
llgl:arm-neon-android=fail
llgl:arm64-android=fail
llgl:x64-android=fail
llvm:arm-neon-android=fail
llvm:arm64-android=fail
llvm:x64-android=fail
log4cplus:arm64-uwp=fail
log4cplus:x64-uwp=fail
log4cpp:x64-linux=fail # dynamic exception specifications
magma:x64-linux=fail
mchehab-zbar:arm-neon-android=fail
mchehab-zbar:arm64-android=fail
mchehab-zbar:x64-android=fail
mecab:arm64-uwp=skip
mecab:arm64-windows-static-md=skip
mecab:arm64-windows=skip
mecab:x64-linux=skip
mecab:x64-uwp=skip
mecab:x64-windows-release=skip
mecab:x64-windows-static-md=skip
mecab:x64-windows-static=skip
mecab:x64-windows=skip
mecab:x86-windows=skip
memorymodule:arm-neon-android=fail
memorymodule:arm64-android=fail
memorymodule:x64-android=fail
# Missing dependent libraries.
mesa:arm-neon-android=fail
mesa:arm64-android=fail
mesa:arm64-osx=fail
mesa:x64-android=fail
mesa:x64-linux=fail
mesa:x64-osx=fail
minifb:arm-neon-android=fail
minifb:arm64-android=fail
minifb:x64-android=fail
miniply:arm-neon-android=fail
monkeys-audio:arm-neon-android=fail
monkeys-audio:arm64-android=fail
monkeys-audio:x64-android=fail
moos-core:arm-neon-android=fail
moos-core:arm64-android=fail
moos-core:x64-android=fail
mpir:x64-android=fail
monkeys-audio:arm64-windows-static-md=fail
monkeys-audio:arm64-windows=fail
monkeys-audio:x64-windows-static=fail
moos-core:x64-windows-static=fail
moos-essential:arm64-windows-static-md=fail
moos-essential:arm64-windows=fail
moos-essential:x64-windows-release=fail
moos-essential:x64-windows-static-md=fail
moos-essential:x64-windows=fail
moos-essential:x86-windows=fail
# ms-gdkx require the Microsoft GDK with Xbox Extensions which is not installed on the CI pipeline machines
ms-gdkx:x64-windows-release=fail
ms-gdkx:x64-windows-static-md=fail
ms-gdkx:x64-windows-static=fail
ms-gdkx:x64-windows=fail
# Conflicts with libjpeg-turbo
mozjpeg:arm-neon-android=fail
mozjpeg:arm64-android=fail
mozjpeg:arm64-osx=skip
mozjpeg:arm64-uwp=skip
mozjpeg:arm64-windows-static-md=skip
mozjpeg:arm64-windows=skip
mozjpeg:x64-android=fail
mozjpeg:x64-linux=skip
mozjpeg:x64-osx=skip
mozjpeg:x64-uwp=skip
mozjpeg:x64-windows-release=skip
mozjpeg:x64-windows-static-md=skip
mozjpeg:x64-windows-static=skip
mozjpeg:x64-windows=skip
mozjpeg:x86-windows=skip
# mpir conflicts with gmp
# see https://github.com/microsoft/vcpkg/issues/11756
mpir:arm64-windows-static-md=skip
mpir:arm64-windows=skip
mpir:x64-linux=skip
mpir:x64-osx=skip
mpir:x64-windows-release=skip
mpir:x64-windows-static-md=skip
mpir:x64-windows-static=skip
mpir:x64-windows=skip
mpir:x86-windows=skip
msmpi:arm64-windows-static-md=fail
msmpi:arm64-windows=fail
msquic:arm-neon-android=fail
msquic:arm64-android=fail
msquic:x64-android=fail
nana:arm-neon-android=fail
nana:arm64-android=fail
nana:arm64-osx=fail
nana:x64-android=fail
nana:x64-linux=fail
nana:x64-osx=fail
nanodbc:x64-linux=skip
netcdf-c:arm-neon-android=fail
netcdf-c:arm64-android=fail
netcdf-c:x64-android=fail
netcdf-cxx4:arm64-windows-static-md=fail
netcdf-cxx4:x64-windows-static-md=fail
ngspice:x64-android=fail
ngspice:x64-windows-static=fail
nrf-ble-driver:arm-neon-android=fail
nrf-ble-driver:arm64-android=fail
nrf-ble-driver:x64-android=fail
nvtt:x64-android=fail
oatpp-libressl:x64-android=fail
offscale-libetcd-cpp:arm64-uwp=fail
offscale-libetcd-cpp:x64-uwp=fail
ogdf:arm64-android=fail
ogre-next:arm-neon-android=fail
ois:x64-android=fail
# opencc/deps/rapidjson-1.1.0/rapidjson.h: Unknown machine endianess detected
# opencc/deps/marisa-0.2.5/lib/marisa/grimoire/io/mapper.cc currently doesn't support UWP.
opencc:x64-android=fail
openimageio:arm-neon-android=fail
openimageio:arm64-android=fail
openimageio:arm64-windows-static-md=fail
openimageio:x64-android=fail
openldap:arm-neon-android=fail
openldap:arm64-android=fail
openldap:x64-android=fail
openmama:arm64-windows-static-md=fail
openmama:x64-windows-static-md=fail
openmesh:arm64-uwp=fail
openmesh:arm64-windows-static-md=fail
openmesh:arm64-windows=fail
openmesh:x64-uwp=fail
openmpi:arm-neon-android=fail
openmpi:arm64-android=fail
openmpi:x64-android=fail
openmvg:arm64-windows-static-md=fail
openmvg:x64-linux=fail
openmvs:arm64-windows-static-md=fail
openscap:arm-neon-android=fail
openscap:arm64-android=fail
openscap:arm64-osx=fail
openscap:x64-android=fail
openscap:x64-osx=fail
openscap:x64-windows-static=fail
opensubdiv:x64-android=fail
openturns:arm64-windows-static-md=fail
openturns:arm64-windows=fail
# Incorrect use of arm64 intrinsics in <wchar.h> in VS 2022 17.13 broke these with -Zc:arm64-aliased-neon-types-
openvino:arm64-windows-static-md=fail
openvino:arm64-windows=fail
openvr:x64-windows-static=fail
# Conflicts with optional-lite, by the same author
optional-bare:arm-neon-android=skip
optional-bare:arm64-android=skip
optional-bare:arm64-osx=skip
optional-bare:arm64-uwp=skip
optional-bare:arm64-windows-static-md=skip
optional-bare:arm64-windows=skip
optional-bare:x64-android=skip
optional-bare:x64-linux=skip
optional-bare:x64-osx=skip
optional-bare:x64-uwp=skip
optional-bare:x64-windows-release=skip
optional-bare:x64-windows-static-md=skip
optional-bare:x64-windows-static=skip
optional-bare:x64-windows=skip
optional-bare:x86-windows=skip
orc:arm-neon-android=fail
orc:arm64-android=fail
orc:x64-android=fail
outcome:arm-neon-android=fail
outcome:arm64-android=fail
outcome:x64-android=fail
paho-mqtt:arm64-uwp=fail
paho-mqtt:x64-uwp=fail
pcl:x64-android=fail
pdal:arm-neon-android=fail
pdal:arm64-android=fail
pdal:x64-android=fail
pdal:x64-windows-static=fail  # ONLY_DYNAMIC_LIBRARY
platform-folders:arm64-uwp=fail
platform-folders:x64-uwp=fail
plib:arm-neon-android=fail
plib:arm64-android=fail
plib:x64-android=fail
pmdk:x64-android=fail
pmdk:x64-osx=fail
pmdk:x64-windows-static=fail
# popsift is broken with CUDA 12.6 and later, and we test with 12.8, see https://github.com/alicevision/popsift/issues/161
popsift:x64-windows-release=fail
popsift:x64-windows-static-md=fail
popsift:x64-windows-static=fail
popsift:x64-windows=fail
python2:arm-neon-android=fail
python2:arm64-android=fail
python2:x64-android=fail
python3:arm-neon-android=fail
python3:arm64-android=fail
python3:x64-android=fail
qpid-proton:arm64-uwp=fail
qpid-proton:x64-uwp=fail
qpid-proton:x64-windows-static=fail
qt5-base:arm-neon-android=fail
qt5-base:arm64-android=fail
qt5-base:arm64-windows-static-md=fail
qt5-base:arm64-windows=fail
qt5-base:x64-android=fail
# Skip deprecated Qt module
# (remove after 1 year or longer due to vcpkg upgrade not handling removed ports correctly)
qt5-canvas3d:arm64-osx=skip
qt5-canvas3d:x64-linux=skip
qt5-canvas3d:x64-osx=skip
qt5-canvas3d:x64-windows-release=skip
qt5-canvas3d:x64-windows-static-md=skip
qt5-canvas3d:x64-windows-static=skip
qt5-canvas3d:x64-windows=skip
qt5-canvas3d:x86-windows=skip
# Skipped to avoid exceeding the 48 hour time limit in CI
# May also need an older ninja version
qt5-webengine:x64-windows-release=skip
qt5-webengine:x64-windows=skip
qt5-webengine:x86-windows=skip
# Missing system libraries
qt5-x11extras:arm64-osx=skip
qt5-x11extras:x64-osx=skip
# Missing system libraries
qtwayland:arm64-osx=skip
qtwayland:x64-osx=skip
quickfix:arm-neon-android=fail
quickfix:arm64-android=fail
qwt-qt6:x64-osx=fail
range-v3-vs2015:arm-neon-android=skip
range-v3-vs2015:arm64-android=skip
range-v3-vs2015:arm64-osx=skip
range-v3-vs2015:arm64-uwp=skip
range-v3-vs2015:arm64-windows-static-md=skip
range-v3-vs2015:arm64-windows=skip
range-v3-vs2015:x64-android=skip
range-v3-vs2015:x64-linux=skip
range-v3-vs2015:x64-osx=skip
range-v3-vs2015:x64-uwp=skip
range-v3-vs2015:x64-windows-release=skip
range-v3-vs2015:x64-windows-static-md=skip
range-v3-vs2015:x64-windows-static=skip
range-v3-vs2015:x64-windows=skip
range-v3-vs2015:x86-windows=skip
rapidstring:arm64-uwp=fail
rapidstring:arm64-windows-static-md=fail
rapidstring:arm64-windows=fail
rapidstring:x64-linux=fail
rapidstring:x64-uwp=fail
rapidstring:x64-windows-release=fail
rapidstring:x64-windows-static-md=fail
rapidstring:x64-windows-static=fail
rapidstring:x64-windows=fail
rapidstring:x86-windows=fail
# file conflicts with rbdl
rbdl:arm-neon-android=fail
rbdl:arm64-android=fail
rbdl:x64-android=fail
rbdl-orb:arm64-uwp=skip
rbdl-orb:arm64-windows-static-md=skip
rbdl-orb:arm64-windows=skip
rbdl-orb:x64-linux=skip
rbdl-orb:x64-osx=skip
rbdl-orb:x64-uwp=skip
rbdl-orb:x64-windows-release=skip
rbdl-orb:x64-windows-static-md=skip
rbdl-orb:x64-windows-static=skip
rbdl-orb:x64-windows=skip
rbdl-orb:x86-windows=skip
restbed:arm-neon-android=fail
restbed:arm64-android=fail
restbed:arm64-uwp=fail
restbed:x64-uwp=fail
rpclib:arm64-uwp=fail
rpclib:arm64-windows-static-md=fail
rpclib:arm64-windows=fail
rpclib:x64-uwp=fail
rtmidi:arm-neon-android=fail
rtmidi:arm64-android=fail
rtmidi:x64-android=fail
safetyhook:x64-linux=fail
salome-medcoupling:x64-linux=fail
scintilla:arm-neon-android=fail
scintilla:arm64-android=fail
scintilla:x64-android=fail
sciter:arm-neon-android=fail
sciter:arm64-android=fail
sciter:arm64-osx=skip
sciter:arm64-uwp=skip
sciter:arm64-windows-static-md=skip
sciter:arm64-windows=skip
sciter:x64-android=fail
sciter:x64-linux=skip
sciter:x64-osx=skip
sciter:x64-uwp=skip
sciter:x64-windows-release=skip
sciter:x64-windows-static-md=skip
sciter:x64-windows-static=skip
sciter:x64-windows=skip
sciter:x86-windows=skip
sdl1:arm-neon-android=fail
sdl1:arm64-android=fail
sdl1:x64-android=fail
septag-sx:x64-android=fail
sfml:arm-neon-android=fail
sfml:arm64-android=fail
sfml:x64-android=fail
shader-slang:x64-windows-static=fail
shogun:arm64-android=skip
shogun:arm64-osx=skip
shogun:arm64-uwp=skip
shogun:arm64-windows-static-md=skip
shogun:arm64-windows=skip
shogun:x64-osx=skip
shogun:x64-uwp=skip
shogun:x64-windows-release=skip
shogun:x64-windows-static-md=skip
shogun:x64-windows-static=skip
shogun:x64-windows=skip
shogun:x86-windows=skip
# "Obsolete" and conflicts with microsoft-signalr
signalrclient:arm-neon-android=skip
signalrclient:arm64-android=skip
signalrclient:arm64-osx=skip
signalrclient:arm64-uwp=skip
signalrclient:arm64-windows-static-md=skip
signalrclient:arm64-windows=skip
signalrclient:x64-android=skip
signalrclient:x64-linux=skip
signalrclient:x64-osx=skip
signalrclient:x64-uwp=skip
signalrclient:x64-windows-release=skip
signalrclient:x64-windows-static-md=skip
signalrclient:x64-windows-static=skip
signalrclient:x64-windows=skip
signalrclient:x86-windows=skip
simbody:arm64-windows-static-md=fail
simbody:arm64-windows=fail
simd:arm-neon-android=fail
simd:arm64-android=fail
simd:x64-android=fail
sjpeg:arm-neon-android=fail
sjpeg:arm64-android=fail
sjpeg:x64-android=fail
sleef:arm-neon-android=fail
sleef:arm64-android=fail
sleef:x86-windows=fail
slikenet:arm-neon-android=fail
slikenet:arm64-android=fail
slikenet:x64-android=fail
soem:arm-neon-android=fail
soem:arm64-android=fail
soem:x64-android=fail
soil:arm-neon-android=fail
soil:arm64-android=fail
soil:x64-android=fail
solid3:arm64-android=fail
spaceland:arm64-android=fail
spaceland:arm64-uwp=fail
spaceland:arm64-windows-static-md=fail
spaceland:arm64-windows=fail
spaceland:x64-android=fail
spaceland:x64-uwp=fail
spatialite-tools:arm-neon-android=fail
spatialite-tools:arm64-android=fail
spatialite-tools:x64-android=fail
spdk:x64-linux=fail
spdk-isal:arm-neon-android=fail
spdk-isal:arm64-android=fail
spdk-isal:x64-android=fail
# Conflict with isal, and "internal" dep of spdk:x64-linux=fail
spdk-isal:x64-linux=skip
spscqueue:arm-neon-android=fail
spscqueue:arm64-android=fail
spscqueue:x64-android=fail
# sqlpp11-connector-mysql: CI issues resolved by overlay port
stormlib:arm64-uwp=fail
stormlib:x64-uwp=fail
# _Interlocked* intrinsic functions are not available on x86
suitesparse-graphblas:x86-windows=fail
# "ninja: error: failed recompaction: Permission denied"
# See https://gist.github.com/valgur/a27f15e16efe93b1ed589703c3cb22cd for the build logs
suitesparse-graphblas:arm64-windows-static-md=fail
suitesparse-graphblas:arm64-windows=fail
# uwp: "unresolved external symbol __imp_RoInitialize referenced in function __scrt_initialize_winrt"
suitesparse-graphblas:arm64-uwp=fail
suitesparse-graphblas:x64-uwp=fail
systemc:arm64-uwp=fail
systemc:arm64-windows-static-md=fail
systemc:arm64-windows=fail
systemc:x64-uwp=fail
teemo:x64-android=fail

# tensorflow does not support VS2022
tensorflow:x64-android=skip
tensorflow:x64-windows-release=skip
tensorflow:x64-windows-static-md=skip
tensorflow:x64-windows-static=skip
tensorflow:x64-windows=skip
tensorflow-cc:x64-android=skip
tensorflow-cc:x64-windows-release=skip
tensorflow-cc:x64-windows-static-md=skip
tensorflow-cc:x64-windows-static=skip
tensorflow-cc:x64-windows=skip
# tensorflow is broken with system libraries on macOS 13.5
# Also skipping because our macOS machines are relatively underpowered and this saves 8 hours of CI
# time for a relatively unpopular library / system combo.
tensorflow:arm64-osx=skip
tensorflow:x64-osx=skip
tensorflow-cc:arm64-osx=skip
tensorflow-cc:x64-osx=skip
# Building tensorflow inside docker fails with
# FATAL: $USER is not set, and unable to look up name of current user: (error: 0): Success
tensorflow:x64-linux=skip
tensorflow-cc:x64-linux=skip

tinycthread:arm-neon-android=fail
tinycthread:arm64-android=fail
tinycthread:x64-android=fail
torch-th:arm-neon-android=fail
torch-th:arm64-android=fail
torch-th:arm64-uwp=fail
torch-th:arm64-windows-static-md=fail
torch-th:arm64-windows=fail
torch-th:x64-android=fail
torch-th:x64-uwp=fail
torch-th:x64-windows-static=fail
tvision:arm-neon-android=fail
tvision:arm64-android=fail
tvision:x64-android=fail
urho3d:x64-osx=fail
# Proper support for a true static usd build is left as a future port improvement. It probably require fiddling with its monolithic mode.
usd:x64-windows-static=skip
# the version of v8 we have in the repo doesn't support VS2022
v8:x64-android=fail
v8:x64-windows-release=fail
v8:x64-windows-static-md=fail
v8:x64-windows-static=fail
v8:x64-windows=fail
v8:x86-windows=fail
vowpal-wabbit:arm-neon-android=fail
vowpal-wabbit:arm64-android=fail
vowpal-wabbit:x64-android=fail
# vst3sdk CMake files work only with the GUI version of Xcode
vst3sdk:arm64-osx=fail
vst3sdk:x64-osx=fail
vulkan-extensionlayer:x64-windows-static=fail
wasmedge:arm-neon-android=fail
wavpack:arm-neon-android=fail
wavpack:x64-android=fail
# Collides with libpcap -> similar headers
winpcap:x64-windows-release=skip
winpcap:x64-windows=skip
winpcap:x86-windows=skip
wordnet:arm-neon-android=fail
wordnet:arm64-android=fail
wordnet:x64-android=fail
workflow:arm-neon-android=fail
workflow:arm64-android=fail
workflow:x64-android=fail
wpilib:arm-neon-android=fail # requires full c++20 support
wpilib:arm64-android=fail # requires full c++20 support
wpilib:x64-android=fail # requires full c++20 support
# No xorg-macros available on osx
xbitmaps:arm-neon-android=fail
xbitmaps:arm64-android=fail
xbitmaps:arm64-osx=skip
xbitmaps:x64-android=fail
xbitmaps:x64-osx=skip
yajl:arm-neon-android=fail
yajl:arm64-android=fail
yajl:x64-android=fail
zeroc-ice:arm-neon-android=fail
zeroc-ice:arm64-android=fail
zeroc-ice:x64-android=fail
zyre:arm64-windows-static-md=fail
zyre:x64-windows-static-md=fail

# Ports which needs to pass in CI
cmake:arm64-osx=pass
cmake:arm64-windows-static-md=pass
cmake:arm64-windows=pass
cmake:x64-linux=pass
cmake:x64-osx=pass
cmake:x64-windows-release=pass
cmake:x64-windows-static-md=pass
cmake:x64-windows-static=pass
cmake:x64-windows=pass
cmake-user:arm64-uwp=pass
cmake-user:arm64-windows-static-md=pass
cmake-user:arm64-windows=pass
cmake-user:x64-linux=pass
cmake-user:x64-osx=pass
cmake-user:x64-windows-release=pass
cmake-user:x64-windows-static-md=pass
cmake-user:x64-windows-static=pass
cmake-user:x64-windows=pass
cmake-user:x86-windows=pass
gtk:arm64-osx=pass
gtk:x64-linux=pass
gtk:x64-osx=pass
gtk:x64-windows-release=pass
gtk:x64-windows-static-md=pass
gtk:x64-windows=pass
gtk:x86-windows=pass
qt:arm64-osx=pass
qt:arm64-windows-static-md=pass
qt:arm64-windows=pass
qt:x64-linux=pass
qt:x64-osx=pass
qt:x64-windows-release=pass
qt:x64-windows-static-md=pass
qt:x64-windows-static=pass
qt:x64-windows=pass
vcpkg-ci-arrow:x64-linux=pass
vcpkg-ci-arrow:x64-osx=pass
vcpkg-ci-arrow:x64-windows-release=pass
vcpkg-ci-arrow:x64-windows-static-md=pass
vcpkg-ci-arrow:x64-windows-static=pass
vcpkg-ci-arrow:x64-windows=pass
vcpkg-ci-aurora-au:x64-linux=pass
vcpkg-ci-boost:arm-neon-android=pass
vcpkg-ci-boost:arm64-android=pass
vcpkg-ci-boost:arm64-uwp=pass
vcpkg-ci-boost:arm64-windows-static-md=pass
vcpkg-ci-boost:arm64-windows=pass
vcpkg-ci-boost:x64-android=pass
vcpkg-ci-boost:x64-linux=pass
vcpkg-ci-boost:x64-osx=pass
vcpkg-ci-boost:x64-uwp=pass
vcpkg-ci-boost:x64-windows-release=pass
vcpkg-ci-boost:x64-windows-static-md=pass
vcpkg-ci-boost:x64-windows-static=pass
vcpkg-ci-boost:x64-windows=pass
vcpkg-ci-boost:x86-windows=pass
vcpkg-ci-curl:arm-neon-android=pass
vcpkg-ci-curl:arm64-android=pass
vcpkg-ci-curl:arm64-osx=pass
vcpkg-ci-curl:arm64-uwp=pass
vcpkg-ci-curl:arm64-windows-static-md=pass
vcpkg-ci-curl:arm64-windows=pass
vcpkg-ci-curl:x64-android=pass
vcpkg-ci-curl:x64-linux=pass
vcpkg-ci-curl:x64-osx=pass
vcpkg-ci-curl:x64-uwp=pass
vcpkg-ci-curl:x64-windows-release=pass
vcpkg-ci-curl:x64-windows-static-md=pass
vcpkg-ci-curl:x64-windows-static=pass
vcpkg-ci-curl:x64-windows=pass
vcpkg-ci-curl:x86-windows=pass
vcpkg-ci-ffmpeg:arm-neon-android=pass
vcpkg-ci-ffmpeg:arm64-android=pass
vcpkg-ci-ffmpeg:arm64-osx=pass
vcpkg-ci-ffmpeg:arm64-uwp=pass
vcpkg-ci-ffmpeg:arm64-windows-static-md=pass
vcpkg-ci-ffmpeg:arm64-windows=pass
vcpkg-ci-ffmpeg:x64-android=pass
vcpkg-ci-ffmpeg:x64-linux=pass
vcpkg-ci-ffmpeg:x64-osx=pass
vcpkg-ci-ffmpeg:x64-uwp=pass
vcpkg-ci-ffmpeg:x64-windows-release=pass
vcpkg-ci-ffmpeg:x64-windows-static-md=pass
vcpkg-ci-ffmpeg:x64-windows-static=pass
vcpkg-ci-ffmpeg:x64-windows=pass
vcpkg-ci-ffmpeg:x86-windows=pass
vcpkg-ci-freerdp:arm-neon-android=pass
vcpkg-ci-freerdp:arm64-android=pass
vcpkg-ci-freerdp:x64-android=pass
vcpkg-ci-freerdp:x64-linux=pass
vcpkg-ci-freerdp:x64-windows-release=pass
vcpkg-ci-freerdp:x64-windows-static-md=pass
vcpkg-ci-freerdp:x64-windows-static=pass
vcpkg-ci-freerdp:x64-windows=pass
vcpkg-ci-freerdp:x86-windows=pass
vcpkg-ci-gdal:arm-neon-android=pass
vcpkg-ci-gdal:arm64-android=pass
vcpkg-ci-gdal:x64-android=pass
vcpkg-ci-gdal:x64-linux=pass
vcpkg-ci-gdal:x64-osx=pass
vcpkg-ci-gdal:x64-windows-release=pass
vcpkg-ci-gdal:x64-windows-static-md=pass
vcpkg-ci-gdal:x64-windows-static=pass
vcpkg-ci-gdal:x64-windows=pass
vcpkg-ci-gdal:x86-windows=pass
vcpkg-ci-itk:arm64-osx=pass
vcpkg-ci-itk:x64-linux=pass
vcpkg-ci-itk:x64-osx=pass
vcpkg-ci-itk:x64-windows-release=pass
vcpkg-ci-itk:x64-windows-static-md=pass
vcpkg-ci-itk:x64-windows-static=pass
vcpkg-ci-itk:x64-windows=pass
vcpkg-ci-itk:x86-windows=pass
vcpkg-ci-llvm:x64-linux=pass
vcpkg-ci-llvm:x64-osx=pass
vcpkg-ci-llvm:x64-windows-release=pass
vcpkg-ci-llvm:x64-windows-static-md=pass
vcpkg-ci-llvm:x64-windows-static=pass
vcpkg-ci-llvm:x64-windows=pass
vcpkg-ci-llvm:x86-windows=pass
vcpkg-ci-mathgl:x64-linux=pass
vcpkg-ci-mathgl:x64-osx=pass
vcpkg-ci-mathgl:x64-windows-release=pass
vcpkg-ci-mathgl:x64-windows-static-md=pass
vcpkg-ci-mathgl:x64-windows-static=pass
vcpkg-ci-mathgl:x64-windows=pass
vcpkg-ci-mathgl:x86-windows=pass
vcpkg-ci-opencv:arm-neon-android=pass
vcpkg-ci-opencv:arm64-android=pass
vcpkg-ci-opencv:arm64-osx=pass
# Incorrect use of arm64 intrinsics in <wchar.h> in VS 2022 17.13 broke these with -Zc:arm64-aliased-neon-types-
#vcpkg-ci-opencv:arm64-uwp=pass
opencv4:arm64-uwp=fail
#vcpkg-ci-opencv:arm64-windows-static-md=pass
opencv4:arm64-windows-static-md=fail
#vcpkg-ci-opencv:arm64-windows=pass
opencv4:arm64-windows=fail
vcpkg-ci-opencv:x64-android=pass
vcpkg-ci-opencv:x64-linux=pass
vcpkg-ci-opencv:x64-osx=pass
vcpkg-ci-opencv:x64-uwp=pass
vcpkg-ci-opencv:x64-windows-release=pass
vcpkg-ci-opencv:x64-windows-static-md=pass
vcpkg-ci-opencv:x64-windows-static=pass
vcpkg-ci-opencv:x64-windows=pass
vcpkg-ci-opencv:x86-windows=pass
vcpkg-ci-openimageio:x64-windows-release=pass
vcpkg-ci-openimageio:x64-windows-static-md=pass
vcpkg-ci-openimageio:x64-windows-static=pass
vcpkg-ci-openimageio:x64-windows=pass
vcpkg-ci-openimageio:x86-windows=pass
vcpkg-ci-paraview:arm64-osx=pass
vcpkg-ci-paraview:x64-linux=pass
vcpkg-ci-paraview:x64-osx=pass
vcpkg-ci-paraview:x64-windows-release=pass
vcpkg-ci-paraview:x64-windows-static-md=pass
vcpkg-ci-paraview:x64-windows-static=pass
vcpkg-ci-paraview:x64-windows=pass
vcpkg-ci-paraview:x86-windows=pass
vcpkg-ci-skia:arm-neon-android=pass
vcpkg-ci-skia:arm64-android=pass
vcpkg-ci-skia:arm64-uwp=pass
vcpkg-ci-skia:arm64-windows-static-md=pass
vcpkg-ci-skia:arm64-windows=pass
vcpkg-ci-skia:x64-android=pass
vcpkg-ci-skia:x64-linux=pass
vcpkg-ci-skia:x64-osx=pass
vcpkg-ci-skia:x64-uwp=pass
vcpkg-ci-skia:x64-windows-release=pass
vcpkg-ci-skia:x64-windows-static-md=pass
vcpkg-ci-skia:x64-windows-static=pass
vcpkg-ci-skia:x64-windows=pass
vcpkg-ci-skia:x86-windows=pass
vcpkg-ci-soci:x64-linux=pass
vcpkg-ci-soci:x64-osx=pass
vcpkg-ci-soci:x64-windows-release=pass
vcpkg-ci-soci:x64-windows-static-md=pass
vcpkg-ci-soci:x64-windows-static=pass
vcpkg-ci-soci:x64-windows=pass
vcpkg-ci-sqlpp11:arm-neon-android=pass
vcpkg-ci-sqlpp11:arm64-android=pass
vcpkg-ci-sqlpp11:arm64-osx=pass
vcpkg-ci-sqlpp11:arm64-uwp=pass
vcpkg-ci-sqlpp11:arm64-windows-static-md=pass
vcpkg-ci-sqlpp11:arm64-windows=pass
vcpkg-ci-sqlpp11:x64-android=pass
vcpkg-ci-sqlpp11:x64-linux=pass
vcpkg-ci-sqlpp11:x64-osx=pass
vcpkg-ci-sqlpp11:x64-uwp=pass
vcpkg-ci-sqlpp11:x64-windows-release=pass
vcpkg-ci-sqlpp11:x64-windows-static-md=pass
vcpkg-ci-sqlpp11:x64-windows-static=pass
vcpkg-ci-sqlpp11:x64-windows=pass
vcpkg-ci-sqlpp11:x86-windows=pass
vcpkg-ci-vxl:arm-neon-android=pass
vcpkg-ci-vxl:arm64-android=pass
vcpkg-ci-vxl:arm64-osx=pass
vcpkg-ci-vxl:arm64-windows-static-md=pass
vcpkg-ci-vxl:arm64-windows=pass
vcpkg-ci-vxl:x64-android=pass
vcpkg-ci-vxl:x64-linux=pass
vcpkg-ci-vxl:x64-osx=pass
vcpkg-ci-vxl:x64-windows-release=pass
vcpkg-ci-vxl:x64-windows-static-md=pass
vcpkg-ci-vxl:x64-windows-static=pass
vcpkg-ci-vxl:x64-windows=pass
vcpkg-ci-vxl:x86-windows=pass
vcpkg-ci-wxwidgets:arm64-windows-static-md=pass
vcpkg-ci-wxwidgets:arm64-windows=pass
vcpkg-ci-wxwidgets:x64-linux=pass
vcpkg-ci-wxwidgets:x64-osx=pass
vcpkg-ci-wxwidgets:x64-windows-release=pass
vcpkg-ci-wxwidgets:x64-windows-static-md=pass
vcpkg-ci-wxwidgets:x64-windows-static=pass
vcpkg-ci-wxwidgets:x64-windows=pass
vcpkg-ci-wxwidgets:x86-windows=pass
