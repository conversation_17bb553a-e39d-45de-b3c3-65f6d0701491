{"default": {"3fd": {"baseline": "2.6.3", "port-version": 4}, "7zip": {"baseline": "24.09", "port-version": 1}, "ableton": {"baseline": "3.0.6", "port-version": 2}, "ableton-link": {"baseline": "3.1.2", "port-version": 0}, "abseil": {"baseline": "20250127.0", "port-version": 0}, "absent": {"baseline": "0.3.1", "port-version": 3}, "ace": {"baseline": "8.0.2", "port-version": 0}, "acl": {"baseline": "2.3.2", "port-version": 0}, "activemq-cpp": {"baseline": "3.9.5", "port-version": 17}, "ada-idna": {"baseline": "0.3.2", "port-version": 0}, "ada-url": {"baseline": "3.2.2", "port-version": 0}, "ade": {"baseline": "0.1.2e", "port-version": 0}, "adios2": {"baseline": "2.9.2", "port-version": 0}, "advobfuscator": {"baseline": "2020-06-26", "port-version": 0}, "air-ctl": {"baseline": "1.1.2", "port-version": 3}, "aixlog": {"baseline": "1.5.0", "port-version": 1}, "aklomp-base64": {"baseline": "0.5.2", "port-version": 0}, "alac": {"baseline": "2017-11-03-c38887c5", "port-version": 4}, "alac-decoder": {"baseline": "0.2", "port-version": 7}, "alembic": {"baseline": "1.8.8", "port-version": 0}, "aliyun-oss-c-sdk": {"baseline": "3.11.2", "port-version": 0}, "aliyun-oss-cpp-sdk": {"baseline": "1.10.0", "port-version": 4}, "allegro5": {"baseline": "5.2.10.0", "port-version": 1}, "alpaca": {"baseline": "0.2.1", "port-version": 0}, "alpaka": {"baseline": "1.2.0", "port-version": 0}, "alsa": {"baseline": "1.2.13", "port-version": 1}, "amd-adl-sdk": {"baseline": "17.1", "port-version": 0}, "amd-amf": {"baseline": "1.4.36", "port-version": 0}, "ampl-asl": {"baseline": "2024-02-01", "port-version": 0}, "ampl-mp": {"baseline": "2020-11-11", "port-version": 5}, "amqpcpp": {"baseline": "4.3.27", "port-version": 0}, "anari": {"baseline": "0.13.1", "port-version": 0}, "anax": {"baseline": "2.1.0", "port-version": 8}, "angelscript": {"baseline": "2.37.0", "port-version": 0}, "angle": {"baseline": "chromium_5414", "port-version": 10}, "ankurvdev-embedresource": {"baseline": "0.0.12", "port-version": 0}, "annoy": {"baseline": "1.17.3", "port-version": 0}, "antlr4": {"baseline": "4.13.2", "port-version": 1}, "any-lite": {"baseline": "0.4.0", "port-version": 0}, "anyrpc": {"baseline": "2021-08-24", "port-version": 2}, "aom": {"baseline": "3.11.0", "port-version": 0}, "apache-datasketches": {"baseline": "5.1.0", "port-version": 0}, "approval-tests-cpp": {"baseline": "10.13.0", "port-version": 0}, "apr": {"baseline": "1.7.5", "port-version": 2}, "apr-util": {"baseline": "1.6.3", "port-version": 0}, "apriltag": {"baseline": "3.4.3", "port-version": 0}, "apsi": {"baseline": "0.11.0", "port-version": 0}, "aravis": {"baseline": "0.8.34", "port-version": 0}, "arb": {"baseline": "2.21.1", "port-version": 2}, "arcticdb-sparrow": {"baseline": "0.6.0", "port-version": 0}, "arcus": {"baseline": "4.10.0", "port-version": 4}, "arg-router": {"baseline": "1.4.0", "port-version": 1}, "argagg": {"baseline": "0.4.7", "port-version": 0}, "argh": {"baseline": "1.3.2", "port-version": 1}, "argon2": {"baseline": "20190702", "port-version": 1}, "argparse": {"baseline": "3.2", "port-version": 0}, "args": {"baseline": "6.4.7", "port-version": 0}, "argtable2": {"baseline": "2.13", "port-version": 10}, "argtable3": {"baseline": "3.2.2.f25c624", "port-version": 0}, "argumentum": {"baseline": "0.3.2", "port-version": 0}, "aricpp": {"baseline": "1.2.1", "port-version": 0}, "armadillo": {"baseline": "14.2.2", "port-version": 0}, "arpack-ng": {"baseline": "3.9.1", "port-version": 1}, "arrayfire": {"baseline": "3.8.0", "port-version": 8}, "arrow": {"baseline": "19.0.1", "port-version": 0}, "arrow-adbc": {"baseline": "16", "port-version": 0}, "arsenalgear": {"baseline": "2.1.1", "port-version": 0}, "ashes": {"baseline": "2023-03-12", "port-version": 0}, "asio": {"baseline": "1.32.0", "port-version": 0}, "asio-grpc": {"baseline": "3.4.3", "port-version": 0}, "asiochan": {"baseline": "2022-11-25", "port-version": 1}, "asiosdk": {"baseline": "2.3.3", "port-version": 7}, "asmjit": {"baseline": "2025-01-22", "port-version": 0}, "asmtk": {"baseline": "2022-11-06", "port-version": 1}, "asock": {"baseline": "1.0.7", "port-version": 0}, "assimp": {"baseline": "5.4.3", "port-version": 0}, "astr": {"baseline": "0.2.1", "port-version": 0}, "async-mqtt": {"baseline": "10.1.0", "port-version": 0}, "async-simple": {"baseline": "1.3", "port-version": 1}, "asynch": {"baseline": "2019-09-21", "port-version": 3}, "asyncplusplus": {"baseline": "1.2", "port-version": 0}, "at-spi2-atk": {"baseline": "2.38.0", "port-version": 1}, "at-spi2-core": {"baseline": "2.44.1", "port-version": 3}, "atk": {"baseline": "2.38.0", "port-version": 9}, "atkmm": {"baseline": "2.36.3", "port-version": 0}, "atl": {"baseline": "0", "port-version": 0}, "atliac-minitest": {"baseline": "1.0.0", "port-version": 0}, "atlmfc": {"baseline": "0", "port-version": 3}, "atomic-queue": {"baseline": "1.6.5", "port-version": 0}, "attr": {"baseline": "2.5.2", "port-version": 0}, "aubio": {"baseline": "2024-01-03", "port-version": 0}, "audioengine": {"baseline": "1.2", "port-version": 0}, "audiofile": {"baseline": "1.1.2", "port-version": 0}, "audit": {"baseline": "4.0.2", "port-version": 0}, "aurora": {"baseline": "2017-06-21-c75699d2a8caa726260c29b6d7a0fd35f8f28933", "port-version": 2}, "aurora-au": {"baseline": "0.4.1", "port-version": 0}, "autobahn": {"baseline": "20.8.1", "port-version": 2}, "autodock-vina": {"baseline": "1.2.6", "port-version": 1}, "avcpp": {"baseline": "2.6.0", "port-version": 0}, "avisynthplus": {"baseline": "3.7.3", "port-version": 1}, "avro-c": {"baseline": "1.12.0", "port-version": 0}, "avro-cpp": {"baseline": "1.12.0", "port-version": 2}, "awlib": {"baseline": "2024-04-06", "port-version": 1}, "aws-c-auth": {"baseline": "0.9.0", "port-version": 0}, "aws-c-cal": {"baseline": "0.8.8", "port-version": 0}, "aws-c-common": {"baseline": "0.12.2", "port-version": 0}, "aws-c-compression": {"baseline": "0.3.1", "port-version": 0}, "aws-c-event-stream": {"baseline": "0.5.4", "port-version": 0}, "aws-c-http": {"baseline": "0.9.5", "port-version": 0}, "aws-c-io": {"baseline": "0.17.0", "port-version": 0}, "aws-c-mqtt": {"baseline": "0.12.2", "port-version": 0}, "aws-c-s3": {"baseline": "0.7.13", "port-version": 0}, "aws-c-sdkutils": {"baseline": "0.2.3", "port-version": 0}, "aws-checksums": {"baseline": "0.2.4", "port-version": 0}, "aws-crt-cpp": {"baseline": "0.31.1", "port-version": 0}, "aws-lambda-cpp": {"baseline": "0.2.10", "port-version": 0}, "aws-sdk-cpp": {"baseline": "1.11.534", "port-version": 0}, "azmq": {"baseline": "2023-03-23", "port-version": 0}, "azure-c-shared-utility": {"baseline": "2025-03-31", "port-version": 0}, "azure-core-amqp-cpp": {"baseline": "1.0.0-beta.11", "port-version": 1}, "azure-core-cpp": {"baseline": "1.15.0", "port-version": 0}, "azure-core-tracing-opentelemetry-cpp": {"baseline": "1.0.0-beta.4", "port-version": 5}, "azure-data-tables-cpp": {"baseline": "1.0.0-beta.6", "port-version": 0}, "azure-identity-cpp": {"baseline": "1.11.0", "port-version": 0}, "azure-iot-sdk-c": {"baseline": "2025-03-31", "port-version": 0}, "azure-kinect-sensor-sdk": {"baseline": "1.4.1", "port-version": 8}, "azure-macro-utils-c": {"baseline": "2022-01-21", "port-version": 1}, "azure-messaging-eventhubs-checkpointstore-blob-cpp": {"baseline": "1.0.0-beta.1", "port-version": 4}, "azure-messaging-eventhubs-cpp": {"baseline": "1.0.0-beta.10", "port-version": 0}, "azure-security-attestation-cpp": {"baseline": "1.1.0", "port-version": 6}, "azure-security-keyvault-administration-cpp": {"baseline": "4.0.0-beta.5", "port-version": 1}, "azure-security-keyvault-certificates-cpp": {"baseline": "4.2.1", "port-version": 3}, "azure-security-keyvault-keys-cpp": {"baseline": "4.4.1", "port-version": 3}, "azure-security-keyvault-secrets-cpp": {"baseline": "4.2.1", "port-version": 3}, "azure-storage-blobs-cpp": {"baseline": "12.13.0", "port-version": 1}, "azure-storage-common-cpp": {"baseline": "12.10.0", "port-version": 0}, "azure-storage-cpp": {"baseline": "7.5.0", "port-version": 7}, "azure-storage-files-datalake-cpp": {"baseline": "12.12.0", "port-version": 1}, "azure-storage-files-shares-cpp": {"baseline": "12.13.0", "port-version": 0}, "azure-storage-queues-cpp": {"baseline": "12.4.0", "port-version": 1}, "azure-uamqp-c": {"baseline": "2025-03-31", "port-version": 0}, "azure-uhttp-c": {"baseline": "2025-03-31", "port-version": 0}, "azure-umqtt-c": {"baseline": "2025-03-31", "port-version": 0}, "b64": {"baseline": "*******", "port-version": 2}, "babl": {"baseline": "0.1.110", "port-version": 2}, "backward-cpp": {"baseline": "2023-11-24", "port-version": 1}, "baresip-libre": {"baseline": "3.21.0", "port-version": 0}, "bark": {"baseline": "0.3.0", "port-version": 0}, "basisu": {"baseline": "1.50.0", "port-version": 0}, "bcg729": {"baseline": "1.1.1", "port-version": 4}, "bddisasm": {"baseline": "2.2.0", "port-version": 0}, "bde": {"baseline": "********", "port-version": 0}, "bdwgc": {"baseline": "8.2.8", "port-version": 0}, "beast": {"baseline": "0", "port-version": 2}, "behaviortree-cpp": {"baseline": "4.6.2", "port-version": 0}, "benchmark": {"baseline": "1.9.2", "port-version": 0}, "bento4": {"baseline": "1.6.0-641", "port-version": 0}, "berkeleydb": {"baseline": "4.8.30", "port-version": 9}, "better-enums": {"baseline": "0.11.3", "port-version": 0}, "bext-di": {"baseline": "1.3.2", "port-version": 0}, "bext-mp": {"baseline": "2023-03-02", "port-version": 0}, "bext-sml": {"baseline": "1.1.11", "port-version": 0}, "bext-sml2": {"baseline": "2.0.0", "port-version": 0}, "bext-text": {"baseline": "2024-01-19", "port-version": 0}, "bext-ut": {"baseline": "2.0.1", "port-version": 0}, "bext-wintls": {"baseline": "0.9.8", "port-version": 0}, "bfgroup-lyra": {"baseline": "1.6.1", "port-version": 0}, "bgfx": {"baseline": "1.129.8866-491", "port-version": 0}, "bigint": {"baseline": "2010.04.30", "port-version": 8}, "binlog": {"baseline": "2021-04-16", "port-version": 1}, "binn": {"baseline": "3.0", "port-version": 3}, "bit7z": {"baseline": "4.0.9", "port-version": 1}, "bitmagic": {"baseline": "8.0.1", "port-version": 0}, "bitserializer": {"baseline": "0.75", "port-version": 0}, "bitsery": {"baseline": "5.2.4", "port-version": 0}, "blake3": {"baseline": "1.8.1", "port-version": 0}, "blas": {"baseline": "2023-04-14", "port-version": 3}, "blaze": {"baseline": "3.8.2", "port-version": 1}, "blend2d": {"baseline": "2025-03-08", "port-version": 0}, "blingfire": {"baseline": "0.1.8.1", "port-version": 1}, "blitz": {"baseline": "2020-03-25", "port-version": 7}, "bloomberg-quantum": {"baseline": "2023-02-03", "port-version": 0}, "blosc": {"baseline": "1.21.6", "port-version": 0}, "blpapi": {"baseline": "3.25.1", "port-version": 0}, "bluescarni-tanuki": {"baseline": "2024-08-17", "port-version": 0}, "boinc": {"baseline": "8.0.4", "port-version": 0}, "bond": {"baseline": "13.0.1", "port-version": 0}, "boolinq": {"baseline": "3.0.4", "port-version": 0}, "boost": {"baseline": "1.87.0", "port-version": 1}, "boost-accumulators": {"baseline": "1.87.0", "port-version": 0}, "boost-algorithm": {"baseline": "1.87.0", "port-version": 0}, "boost-align": {"baseline": "1.87.0", "port-version": 0}, "boost-any": {"baseline": "1.87.0", "port-version": 0}, "boost-array": {"baseline": "1.87.0", "port-version": 0}, "boost-asio": {"baseline": "1.87.0", "port-version": 1}, "boost-assert": {"baseline": "1.87.0", "port-version": 0}, "boost-assign": {"baseline": "1.87.0", "port-version": 0}, "boost-atomic": {"baseline": "1.87.0", "port-version": 1}, "boost-beast": {"baseline": "1.87.0", "port-version": 0}, "boost-bimap": {"baseline": "1.87.0", "port-version": 0}, "boost-bind": {"baseline": "1.87.0", "port-version": 0}, "boost-build": {"baseline": "1.87.0", "port-version": 0}, "boost-callable-traits": {"baseline": "1.87.0", "port-version": 0}, "boost-charconv": {"baseline": "1.87.0", "port-version": 0}, "boost-chrono": {"baseline": "1.87.0", "port-version": 0}, "boost-circular-buffer": {"baseline": "1.87.0", "port-version": 0}, "boost-cmake": {"baseline": "1.87.0", "port-version": 0}, "boost-cobalt": {"baseline": "1.87.0", "port-version": 1}, "boost-compat": {"baseline": "1.87.0", "port-version": 0}, "boost-compatibility": {"baseline": "1.86.0", "port-version": 0}, "boost-compute": {"baseline": "1.87.0", "port-version": 1}, "boost-concept-check": {"baseline": "1.87.0", "port-version": 0}, "boost-config": {"baseline": "1.87.0", "port-version": 0}, "boost-container": {"baseline": "1.87.0", "port-version": 0}, "boost-container-hash": {"baseline": "1.87.0", "port-version": 0}, "boost-context": {"baseline": "1.87.0", "port-version": 1}, "boost-contract": {"baseline": "1.87.0", "port-version": 0}, "boost-conversion": {"baseline": "1.87.0", "port-version": 0}, "boost-convert": {"baseline": "1.87.0", "port-version": 0}, "boost-core": {"baseline": "1.87.0", "port-version": 0}, "boost-coroutine": {"baseline": "1.87.0", "port-version": 0}, "boost-coroutine2": {"baseline": "1.87.0", "port-version": 0}, "boost-crc": {"baseline": "1.87.0", "port-version": 0}, "boost-date-time": {"baseline": "1.87.0", "port-version": 0}, "boost-describe": {"baseline": "1.87.0", "port-version": 0}, "boost-detail": {"baseline": "1.87.0", "port-version": 0}, "boost-dll": {"baseline": "1.87.0", "port-version": 0}, "boost-dynamic-bitset": {"baseline": "1.87.0", "port-version": 0}, "boost-endian": {"baseline": "1.87.0", "port-version": 0}, "boost-exception": {"baseline": "1.87.0", "port-version": 0}, "boost-fiber": {"baseline": "1.87.0", "port-version": 0}, "boost-filesystem": {"baseline": "1.87.0", "port-version": 0}, "boost-flyweight": {"baseline": "1.87.0", "port-version": 1}, "boost-foreach": {"baseline": "1.87.0", "port-version": 0}, "boost-format": {"baseline": "1.87.0", "port-version": 0}, "boost-function": {"baseline": "1.87.0", "port-version": 0}, "boost-function-types": {"baseline": "1.87.0", "port-version": 0}, "boost-functional": {"baseline": "1.87.0", "port-version": 0}, "boost-fusion": {"baseline": "1.87.0", "port-version": 0}, "boost-geometry": {"baseline": "1.87.0", "port-version": 0}, "boost-gil": {"baseline": "1.87.0", "port-version": 0}, "boost-graph": {"baseline": "1.87.0", "port-version": 0}, "boost-graph-parallel": {"baseline": "1.87.0", "port-version": 0}, "boost-hana": {"baseline": "1.87.0", "port-version": 0}, "boost-headers": {"baseline": "1.87.0", "port-version": 0}, "boost-heap": {"baseline": "1.87.0", "port-version": 0}, "boost-histogram": {"baseline": "1.87.0", "port-version": 0}, "boost-hof": {"baseline": "1.87.0", "port-version": 0}, "boost-icl": {"baseline": "1.87.0", "port-version": 0}, "boost-integer": {"baseline": "1.87.0", "port-version": 0}, "boost-interprocess": {"baseline": "1.87.0", "port-version": 1}, "boost-interval": {"baseline": "1.87.0", "port-version": 0}, "boost-intrusive": {"baseline": "1.87.0", "port-version": 0}, "boost-io": {"baseline": "1.87.0", "port-version": 0}, "boost-iostreams": {"baseline": "1.87.0", "port-version": 0}, "boost-iterator": {"baseline": "1.87.0", "port-version": 0}, "boost-json": {"baseline": "1.87.0", "port-version": 1}, "boost-lambda": {"baseline": "1.87.0", "port-version": 0}, "boost-lambda2": {"baseline": "1.87.0", "port-version": 0}, "boost-leaf": {"baseline": "1.87.0", "port-version": 0}, "boost-lexical-cast": {"baseline": "1.87.0", "port-version": 1}, "boost-local-function": {"baseline": "1.87.0", "port-version": 0}, "boost-locale": {"baseline": "1.87.0", "port-version": 0}, "boost-lockfree": {"baseline": "1.87.0", "port-version": 1}, "boost-log": {"baseline": "1.87.0", "port-version": 0}, "boost-logic": {"baseline": "1.87.0", "port-version": 0}, "boost-math": {"baseline": "1.87.0", "port-version": 0}, "boost-metaparse": {"baseline": "1.87.0", "port-version": 0}, "boost-move": {"baseline": "1.87.0", "port-version": 0}, "boost-mp11": {"baseline": "1.87.0", "port-version": 0}, "boost-mpi": {"baseline": "1.87.0", "port-version": 0}, "boost-mpl": {"baseline": "1.87.0", "port-version": 0}, "boost-msm": {"baseline": "1.87.0", "port-version": 0}, "boost-multi-array": {"baseline": "1.87.0", "port-version": 0}, "boost-multi-index": {"baseline": "1.87.0", "port-version": 0}, "boost-multiprecision": {"baseline": "1.87.0", "port-version": 0}, "boost-mysql": {"baseline": "1.87.0", "port-version": 1}, "boost-nowide": {"baseline": "1.87.0", "port-version": 0}, "boost-numeric-conversion": {"baseline": "1.87.0", "port-version": 0}, "boost-odeint": {"baseline": "1.87.0", "port-version": 0}, "boost-optional": {"baseline": "1.87.0", "port-version": 1}, "boost-outcome": {"baseline": "1.87.0", "port-version": 0}, "boost-parameter": {"baseline": "1.87.0", "port-version": 0}, "boost-parameter-python": {"baseline": "1.87.0", "port-version": 0}, "boost-parser": {"baseline": "1.87.0", "port-version": 1}, "boost-pfr": {"baseline": "1.87.0", "port-version": 0}, "boost-phoenix": {"baseline": "1.87.0", "port-version": 0}, "boost-poly-collection": {"baseline": "1.87.0", "port-version": 0}, "boost-polygon": {"baseline": "1.87.0", "port-version": 0}, "boost-pool": {"baseline": "1.87.0", "port-version": 0}, "boost-predef": {"baseline": "1.87.0", "port-version": 0}, "boost-preprocessor": {"baseline": "1.87.0", "port-version": 0}, "boost-process": {"baseline": "1.87.0", "port-version": 1}, "boost-program-options": {"baseline": "1.87.0", "port-version": 0}, "boost-property-map": {"baseline": "1.87.0", "port-version": 0}, "boost-property-map-parallel": {"baseline": "1.87.0", "port-version": 0}, "boost-property-tree": {"baseline": "1.87.0", "port-version": 0}, "boost-proto": {"baseline": "1.87.0", "port-version": 0}, "boost-ptr-container": {"baseline": "1.87.0", "port-version": 0}, "boost-python": {"baseline": "1.87.0", "port-version": 0}, "boost-qvm": {"baseline": "1.87.0", "port-version": 0}, "boost-random": {"baseline": "1.87.0", "port-version": 0}, "boost-range": {"baseline": "1.87.0", "port-version": 0}, "boost-ratio": {"baseline": "1.87.0", "port-version": 0}, "boost-rational": {"baseline": "1.87.0", "port-version": 0}, "boost-redis": {"baseline": "1.87.0", "port-version": 0}, "boost-regex": {"baseline": "1.87.0", "port-version": 1}, "boost-safe-numerics": {"baseline": "1.87.0", "port-version": 0}, "boost-scope": {"baseline": "1.87.0", "port-version": 0}, "boost-scope-exit": {"baseline": "1.87.0", "port-version": 0}, "boost-serialization": {"baseline": "1.87.0", "port-version": 0}, "boost-signals2": {"baseline": "1.87.0", "port-version": 0}, "boost-smart-ptr": {"baseline": "1.87.0", "port-version": 0}, "boost-sort": {"baseline": "1.87.0", "port-version": 0}, "boost-spirit": {"baseline": "1.87.0", "port-version": 0}, "boost-stacktrace": {"baseline": "1.87.0", "port-version": 0}, "boost-statechart": {"baseline": "1.87.0", "port-version": 0}, "boost-static-assert": {"baseline": "1.87.0", "port-version": 0}, "boost-static-string": {"baseline": "1.87.0", "port-version": 0}, "boost-stl-interfaces": {"baseline": "1.87.0", "port-version": 0}, "boost-system": {"baseline": "1.87.0", "port-version": 0}, "boost-test": {"baseline": "1.87.0", "port-version": 0}, "boost-thread": {"baseline": "1.87.0", "port-version": 0}, "boost-throw-exception": {"baseline": "1.87.0", "port-version": 0}, "boost-timer": {"baseline": "1.87.0", "port-version": 0}, "boost-tokenizer": {"baseline": "1.87.0", "port-version": 0}, "boost-tti": {"baseline": "1.87.0", "port-version": 0}, "boost-tuple": {"baseline": "1.87.0", "port-version": 0}, "boost-type-erasure": {"baseline": "1.87.0", "port-version": 0}, "boost-type-index": {"baseline": "1.87.0", "port-version": 0}, "boost-type-traits": {"baseline": "1.87.0", "port-version": 0}, "boost-typeof": {"baseline": "1.87.0", "port-version": 0}, "boost-ublas": {"baseline": "1.87.0", "port-version": 0}, "boost-uninstall": {"baseline": "1.87.0", "port-version": 0}, "boost-units": {"baseline": "1.87.0", "port-version": 0}, "boost-unordered": {"baseline": "1.87.0", "port-version": 1}, "boost-url": {"baseline": "1.87.0", "port-version": 0}, "boost-utility": {"baseline": "1.87.0", "port-version": 0}, "boost-uuid": {"baseline": "1.87.0", "port-version": 0}, "boost-variant": {"baseline": "1.87.0", "port-version": 0}, "boost-variant2": {"baseline": "1.87.0", "port-version": 0}, "boost-vcpkg-helpers": {"baseline": "1.84.0", "port-version": 0}, "boost-vmd": {"baseline": "1.87.0", "port-version": 0}, "boost-wave": {"baseline": "1.87.0", "port-version": 0}, "boost-winapi": {"baseline": "1.87.0", "port-version": 0}, "boost-xpressive": {"baseline": "1.87.0", "port-version": 0}, "boost-yap": {"baseline": "1.87.0", "port-version": 0}, "boringssl": {"baseline": "2024-09-13", "port-version": 1}, "botan": {"baseline": "3.7.1", "port-version": 0}, "box2d": {"baseline": "2.4.1", "port-version": 3}, "braft": {"baseline": "2021-26-04", "port-version": 5}, "breakpad": {"baseline": "2024-02-16", "port-version": 0}, "brigand": {"baseline": "1.3.0", "port-version": 3}, "brotli": {"baseline": "1.1.0", "port-version": 1}, "brpc": {"baseline": "1.11.0", "port-version": 1}, "brunocodutra-metal": {"baseline": "2.1.4", "port-version": 0}, "brynet": {"baseline": "1.12.2", "port-version": 0}, "bshoshany-thread-pool": {"baseline": "5.0.0", "port-version": 0}, "bsio": {"baseline": "1.0.0", "port-version": 0}, "buck-yeh-bux": {"baseline": "1.12.3", "port-version": 0}, "buck-yeh-bux-sqlite": {"baseline": "1.0.5", "port-version": 0}, "bullet3": {"baseline": "3.25", "port-version": 3}, "bustache": {"baseline": "1.1.0", "port-version": 2}, "butteraugli": {"baseline": "2019-05-08", "port-version": 4}, "bw-tempdir": {"baseline": "1.0.1", "port-version": 0}, "bxzstr": {"baseline": "1.2.2", "port-version": 0}, "byte-lite": {"baseline": "0.3.0", "port-version": 0}, "bzip2": {"baseline": "1.0.8", "port-version": 6}, "c-ares": {"baseline": "1.34.5", "port-version": 0}, "c-dbg-macro": {"baseline": "2020-02-29", "port-version": 0}, "c4core": {"baseline": "0.2.5", "port-version": 0}, "c89stringutils": {"baseline": "0.0.2", "port-version": 0}, "c9y": {"baseline": "0.8.0", "port-version": 0}, "cachelib": {"baseline": "2025.03.31.00", "port-version": 1}, "caf": {"baseline": "1.0.2", "port-version": 0}, "cairo": {"baseline": "1.18.2", "port-version": 1}, "cairomm": {"baseline": "1.18.0", "port-version": 0}, "calceph": {"baseline": "4.0.4", "port-version": 1}, "camport3": {"baseline": "1.6.2", "port-version": 0}, "canvas-ity": {"baseline": "1.0", "port-version": 0}, "capnproto": {"baseline": "1.1.0", "port-version": 0}, "capstone": {"baseline": "5.0.3", "port-version": 0}, "cargs": {"baseline": "1.2.0", "port-version": 0}, "casadi": {"baseline": "3.6.7", "port-version": 1}, "casclib": {"baseline": "2024-06-05", "port-version": 0}, "catch": {"baseline": "alias", "port-version": 1}, "catch-classic": {"baseline": "1.12.2", "port-version": 2}, "catch2": {"baseline": "3.8.0", "port-version": 0}, "cblas": {"baseline": "2024-03-19", "port-version": 0}, "cccapstone": {"baseline": "9b4128ee1153e78288a1b5433e2c06a0d47a4c4e", "port-version": 2}, "ccd": {"baseline": "2.1", "port-version": 4}, "ccfits": {"baseline": "2.5", "port-version": 13}, "cctag": {"baseline": "1.0.4", "port-version": 0}, "cctz": {"baseline": "2.4", "port-version": 0}, "cddlib": {"baseline": "0.94m", "port-version": 0}, "cdt": {"baseline": "1.4.1", "port-version": 0}, "celero": {"baseline": "2.9.0", "port-version": 0}, "cello": {"baseline": "2019-07-23", "port-version": 4}, "cereal": {"baseline": "1.3.2", "port-version": 1}, "ceres": {"baseline": "2.2.0", "port-version": 1}, "cfitsio": {"baseline": "3.49", "port-version": 6}, "cgal": {"baseline": "6.0.1", "port-version": 0}, "cgicc": {"baseline": "3.2.20", "port-version": 1}, "cglm": {"baseline": "0.9.4", "port-version": 0}, "cgltf": {"baseline": "1.14", "port-version": 0}, "cgns": {"baseline": "4.5.0", "port-version": 0}, "chaiscript": {"baseline": "6.1.0", "port-version": 3}, "chakracore": {"baseline": "2022-11-09", "port-version": 7}, "charls": {"baseline": "2.4.2", "port-version": 0}, "chartdir": {"baseline": "7.0.0", "port-version": 8}, "check": {"baseline": "0.15.2", "port-version": 5}, "chipmunk": {"baseline": "7.0.3", "port-version": 7}, "chmlib": {"baseline": "0.40", "port-version": 8}, "chromaprint": {"baseline": "1.5.1", "port-version": 0}, "chromium-base": {"baseline": "86.0.4199.1", "port-version": 6}, "chronoengine": {"baseline": "8.0.0", "port-version": 0}, "cialloo-rcon": {"baseline": "1.0.0", "port-version": 0}, "cimg": {"baseline": "3.5.2", "port-version": 0}, "cista": {"baseline": "0.15", "port-version": 0}, "cityhash": {"baseline": "2013-01-08", "port-version": 3}, "civetweb": {"baseline": "1.16", "port-version": 2}, "cjson": {"baseline": "1.7.18", "port-version": 0}, "clamav": {"baseline": "0.103.11", "port-version": 0}, "clap-cleveraudio": {"baseline": "1.2.3", "port-version": 0}, "clapack": {"baseline": "3.2.1", "port-version": 23}, "clara": {"baseline": "1.1.5", "port-version": 2}, "clblas": {"baseline": "2.12", "port-version": 7}, "clblast": {"baseline": "1.6.3", "port-version": 0}, "cld3": {"baseline": "3.0.14", "port-version": 2}, "clfft": {"baseline": "2.12.2", "port-version": 7}, "cli": {"baseline": "2.2.0", "port-version": 0}, "cli11": {"baseline": "2.5.0", "port-version": 0}, "clickhouse-cpp": {"baseline": "2.5.1", "port-version": 0}, "clipboardxx": {"baseline": "2022-02-04", "port-version": 0}, "clipp": {"baseline": "2019-04-30", "port-version": 2}, "clipper2": {"baseline": "1.5.2", "port-version": 0}, "clockutils": {"baseline": "1.1.1", "port-version": 2}, "clrng": {"baseline": "2020-12-01", "port-version": 3}, "clue": {"baseline": "1.0.0", "port-version": 0}, "cmakerc": {"baseline": "2023-07-24", "port-version": 0}, "cmark": {"baseline": "0.31.1", "port-version": 0}, "cminpack": {"baseline": "1.3.8", "port-version": 4}, "cmocka": {"baseline": "2020-08-01", "port-version": 3}, "cnats": {"baseline": "3.10.1", "port-version": 0}, "cnl": {"baseline": "1.1.7", "port-version": 3}, "co": {"baseline": "2.0.3", "port-version": 1}, "cocoyaxi": {"baseline": "2024-09-04", "port-version": 0}, "coin": {"baseline": "4.0.3", "port-version": 0}, "coin-or-buildtools": {"baseline": "2023-02-02", "port-version": 0}, "coin-or-cbc": {"baseline": "2024-06-04", "port-version": 1}, "coin-or-cgl": {"baseline": "2023-02-01", "port-version": 0}, "coin-or-clp": {"baseline": "2023-02-01", "port-version": 0}, "coin-or-ipopt": {"baseline": "2023-02-01", "port-version": 0}, "coin-or-osi": {"baseline": "2024-04-16", "port-version": 0}, "coinutils": {"baseline": "2024-04-08", "port-version": 0}, "collada-dom": {"baseline": "2.5.0", "port-version": 11}, "colmap": {"baseline": "3.11.1", "port-version": 3}, "color-console": {"baseline": "2022-03-20", "port-version": 0}, "commata": {"baseline": "1.1.0", "port-version": 0}, "comms": {"baseline": "5.2.7", "port-version": 0}, "comms-ublox": {"baseline": "1.0.0", "port-version": 0}, "commsdsl": {"baseline": "6.3.4", "port-version": 0}, "compoundfilereader": {"baseline": "0.1.0", "port-version": 0}, "concurrencpp": {"baseline": "0.1.7", "port-version": 2}, "concurrentqueue": {"baseline": "1.0.4", "port-version": 0}, "configcat": {"baseline": "4.0.4", "port-version": 0}, "conjure-enum": {"baseline": "1.1.0", "port-version": 1}, "console-bridge": {"baseline": "1.0.2", "port-version": 0}, "constexpr": {"baseline": "1.0", "port-version": 3}, "constexpr-contracts": {"baseline": "2020-08-09", "port-version": 3}, "continuable": {"baseline": "4.2.2", "port-version": 0}, "convectionkernels": {"baseline": "2022-06-08", "port-version": 0}, "coolprop": {"baseline": "6.4.3", "port-version": 3}, "copypp": {"baseline": "0.3.0", "port-version": 0}, "coroutine": {"baseline": "1.5.0", "port-version": 5}, "corrade": {"baseline": "2020.06", "port-version": 8}, "correlation-vector-cpp": {"baseline": "1.0", "port-version": 0}, "cpp-async": {"baseline": "1.1.0", "port-version": 0}, "cpp-base64": {"baseline": "V2.rc.08", "port-version": 0}, "cpp-exiftool": {"baseline": "1.8.0", "port-version": 0}, "cpp-httplib": {"baseline": "0.20.0", "port-version": 1}, "cpp-ipc": {"baseline": "1.3.0", "port-version": 0}, "cpp-jwt": {"baseline": "2022-08-27", "port-version": 1}, "cpp-kana": {"baseline": "1.0.0", "port-version": 0}, "cpp-netlib": {"baseline": "0.13.0", "port-version": 10}, "cpp-peglib": {"baseline": "1.9.1", "port-version": 0}, "cpp-pinyin": {"baseline": "1.0.2", "port-version": 0}, "cpp-redis": {"baseline": "4.3.1", "port-version": 5}, "cpp-sort": {"baseline": "1.16.0", "port-version": 0}, "cpp-taskflow": {"baseline": "2.6.0", "port-version": 2}, "cpp-timsort": {"baseline": "3.0.1", "port-version": 0}, "cppad": {"baseline": "20240000.7", "port-version": 0}, "cppcms": {"baseline": "1.2.1", "port-version": 7}, "cppcodec": {"baseline": "0.2", "port-version": 4}, "cppcoro": {"baseline": "2022-10-25", "port-version": 0}, "cppdap": {"baseline": "1.58.0-a", "port-version": 0}, "cppfs": {"baseline": "1.3.0", "port-version": 4}, "cppgraphqlgen": {"baseline": "4.5.7", "port-version": 2}, "cppitertools": {"baseline": "2.2", "port-version": 0}, "cppkafka": {"baseline": "0.4.0", "port-version": 0}, "cppmicroservices": {"baseline": "3.8.6", "port-version": 0}, "cppp-reiconv": {"baseline": "2.1.0", "port-version": 0}, "cpprealm": {"baseline": "2.2.0", "port-version": 0}, "cpprestsdk": {"baseline": "2.10.19", "port-version": 3}, "cppslippi": {"baseline": "********", "port-version": 0}, "cpptoml": {"baseline": "0.1.1", "port-version": 4}, "cpptrace": {"baseline": "0.8.3", "port-version": 0}, "cppunit": {"baseline": "1.15.1", "port-version": 4}, "cpputest": {"baseline": "4.0", "port-version": 0}, "cppwinrt": {"baseline": "2.0.240405.15", "port-version": 0}, "cppxaml": {"baseline": "0.0.16", "port-version": 1}, "cppzmq": {"baseline": "4.10.0", "port-version": 0}, "cpr": {"baseline": "1.11.2", "port-version": 0}, "cpu-features": {"baseline": "0.9.0", "port-version": 0}, "cpuid": {"baseline": "0.7.0", "port-version": 1}, "cpuinfo": {"baseline": "2022-07-19", "port-version": 3}, "cr": {"baseline": "2020-04-26", "port-version": 2}, "crashpad": {"baseline": "2024-04-11", "port-version": 7}, "crashrpt": {"baseline": "1.4.3", "port-version": 3}, "crc32c": {"baseline": "1.1.2", "port-version": 2}, "crfsuite": {"baseline": "2020-08-27", "port-version": 1}, "croncpp": {"baseline": "2023-03-30", "port-version": 0}, "crossguid": {"baseline": "2021-10-22", "port-version": 3}, "crow": {"baseline": "1.2.1.2", "port-version": 0}, "cryptopp": {"baseline": "8.9.0", "port-version": 1}, "cserialport": {"baseline": "4.3.1", "port-version": 0}, "cspice": {"baseline": "67", "port-version": 3}, "ctbench": {"baseline": "1.3.4", "port-version": 1}, "ctbignum": {"baseline": "2019-08-02", "port-version": 5}, "ctemplate": {"baseline": "2020-09-14", "port-version": 5}, "cthash": {"baseline": "2024-11-16", "port-version": 0}, "ctp": {"baseline": "6.6.1_P1_20210406_se", "port-version": 4}, "ctpg": {"baseline": "1.3.7", "port-version": 2}, "ctre": {"baseline": "3.9.0", "port-version": 0}, "ctstraffic": {"baseline": "2.0.3.2", "port-version": 0}, "cubeb": {"baseline": "2023-09-26", "port-version": 1}, "cuda": {"baseline": "10.1", "port-version": 13}, "cuda-api-wrappers": {"baseline": "0.8.0", "port-version": 0}, "cudnn": {"baseline": "7.6.5", "port-version": 15}, "cunit": {"baseline": "2.1.3", "port-version": 8}, "curl": {"baseline": "8.13.0", "port-version": 1}, "curlcpp": {"baseline": "3.1", "port-version": 1}, "curlpp": {"baseline": "2018-06-15", "port-version": 11}, "cute-headers": {"baseline": "2019-09-20", "port-version": 2}, "cutelyst2": {"baseline": "2.12.0", "port-version": 2}, "cwalk": {"baseline": "1.2.9", "port-version": 0}, "cwapi3d": {"baseline": "30.475.1", "port-version": 0}, "cxxgraph": {"baseline": "4.1.0", "port-version": 0}, "cxxopts": {"baseline": "3.2.1", "port-version": 0}, "cyclonedds": {"baseline": "0.10.5", "port-version": 0}, "cyclonedds-cxx": {"baseline": "0.10.5", "port-version": 0}, "cyrus-sasl": {"baseline": "2.1.28", "port-version": 2}, "czmq": {"baseline": "4.2.1", "port-version": 4}, "d3d12-memory-allocator": {"baseline": "2.0.1", "port-version": 0}, "d3dx12": {"baseline": "may2021", "port-version": 1}, "darknet": {"baseline": "2024-10-10", "port-version": 0}, "darts-clone": {"baseline": "1767ab87cffe", "port-version": 3}, "dartsim": {"baseline": "6.15.0", "port-version": 4}, "dataframe": {"baseline": "3.5.0", "port-version": 0}, "date": {"baseline": "3.0.3", "port-version": 0}, "datraw": {"baseline": "1.0.9", "port-version": 0}, "dav1d": {"baseline": "1.5.0", "port-version": 0}, "daw-header-libraries": {"baseline": "2.123.2", "port-version": 0}, "daw-json-link": {"baseline": "3.30.2", "port-version": 0}, "daw-utf-range": {"baseline": "2.2.5", "port-version": 1}, "daxa": {"baseline": "3.0.3", "port-version": 0}, "dbg-macro": {"baseline": "0.5.1", "port-version": 1}, "dbghelp": {"baseline": "0", "port-version": 2}, "dbow2": {"baseline": "2019-08-05", "port-version": 3}, "dbow3": {"baseline": "1.0.0", "port-version": 3}, "dbus": {"baseline": "1.16.2", "port-version": 1}, "dcmtk": {"baseline": "3.6.9", "port-version": 1}, "debug-assert": {"baseline": "1.3.4", "port-version": 0}, "decimal-for-cpp": {"baseline": "1.18", "port-version": 1}, "delaunator-cpp": {"baseline": "1.0.0", "port-version": 0}, "deniskovalchuk-libftp": {"baseline": "1.4.1", "port-version": 0}, "detours": {"baseline": "4.0.1", "port-version": 8}, "devicenameresolver": {"baseline": "2016-06-26", "port-version": 4}, "devil": {"baseline": "1.8.0", "port-version": 13}, "dimcli": {"baseline": "7.3.0", "port-version": 0}, "dingo": {"baseline": "0.1.0", "port-version": 0}, "directx-dxc": {"baseline": "2025-02-20", "port-version": 1}, "directx-headers": {"baseline": "1.615.0", "port-version": 0}, "directx12-agility": {"baseline": "1.615.0", "port-version": 0}, "directxmath": {"baseline": "2025-04-03", "port-version": 0}, "directxmesh": {"baseline": "2025-03-24", "port-version": 0}, "directxsdk": {"baseline": "jun10", "port-version": 8}, "directxtex": {"baseline": "2025-03-24", "port-version": 0}, "directxtk": {"baseline": "2025-03-20", "port-version": 0}, "directxtk12": {"baseline": "2025-03-20", "port-version": 0}, "dirent": {"baseline": "1.24", "port-version": 0}, "discord-game-sdk": {"baseline": "3.2.1", "port-version": 0}, "discord-rpc": {"baseline": "3.4.0", "port-version": 4}, "discordcoreapi": {"baseline": "2.0.8", "port-version": 0}, "discount": {"baseline": "3.0.0d", "port-version": 0}, "discreture": {"baseline": "2020-01-29", "port-version": 3}, "distorm": {"baseline": "3.5.2b", "port-version": 0}, "dlfcn-win32": {"baseline": "1.4.1", "port-version": 0}, "dlib": {"baseline": "19.24.6", "port-version": 0}, "dlpack": {"baseline": "1.1", "port-version": 0}, "dmlc": {"baseline": "2022-06-22", "port-version": 0}, "docopt": {"baseline": "2022-03-15", "port-version": 1}, "doctest": {"baseline": "2.4.11", "port-version": 0}, "double-conversion": {"baseline": "3.3.0", "port-version": 1}, "dp-thread-pool": {"baseline": "0.7.0", "port-version": 0}, "dpdk": {"baseline": "24.07", "port-version": 0}, "dpp": {"baseline": "10.1.2", "port-version": 0}, "draco": {"baseline": "1.5.7", "port-version": 0}, "drekar-launch-process-cpp": {"baseline": "0.1.0", "port-version": 0}, "drlibs": {"baseline": "2023-08-16", "port-version": 0}, "drogon": {"baseline": "1.9.10", "port-version": 0}, "dstorage": {"baseline": "1.2.3", "port-version": 1}, "dtl": {"baseline": "1.21", "port-version": 0}, "duckdb": {"baseline": "1.2.1", "port-version": 2}, "duckx": {"baseline": "1.2.2", "port-version": 1}, "duilib": {"baseline": "2019-04-28", "port-version": 6}, "dukglue": {"baseline": "2022-11-08", "port-version": 0}, "duktape": {"baseline": "2.7.0", "port-version": 2}, "dumb": {"baseline": "2.0.3", "port-version": 0}, "dv-processing": {"baseline": "1.7.9", "port-version": 3}, "dx": {"baseline": "1.0.1", "port-version": 3}, "dxcam-cpp": {"baseline": "0.2.1", "port-version": 0}, "dxsdk-d3dx": {"baseline": "9.29.952.8", "port-version": 7}, "dxut": {"baseline": "11.32", "port-version": 0}, "dylib": {"baseline": "2.2.1", "port-version": 0}, "dyno": {"baseline": "2019-11-13", "port-version": 0}, "eabase": {"baseline": "2024-08-18", "port-version": 0}, "earcut-hpp": {"baseline": "2.2.4", "port-version": 0}, "eastl": {"baseline": "3.21.23", "port-version": 0}, "easycl": {"baseline": "0.3", "port-version": 2}, "easyexif": {"baseline": "2022-10-07", "port-version": 0}, "easyhook": {"baseline": "2.7.7097.0", "port-version": 9}, "easyloggingpp": {"baseline": "9.97.1", "port-version": 1}, "eathread": {"baseline": "1.32.09", "port-version": 5}, "ebml": {"baseline": "1.4.5", "port-version": 0}, "ecal": {"baseline": "5.13.3", "port-version": 0}, "ecm": {"baseline": "6.7.0", "port-version": 0}, "ecos": {"baseline": "2.0.10", "port-version": 0}, "ed25519": {"baseline": "2017-02-10", "port-version": 1}, "edflib": {"baseline": "1.27", "port-version": 0}, "edlib": {"baseline": "1.2.7", "port-version": 1}, "effects11": {"baseline": "11.29", "port-version": 3}, "effolkronium-random": {"baseline": "1.5.0", "port-version": 0}, "efsw": {"baseline": "1.4.1", "port-version": 0}, "egl": {"baseline": "2022-12-04", "port-version": 0}, "egl-registry": {"baseline": "2024-01-25", "port-version": 0}, "eigen3": {"baseline": "3.4.0", "port-version": 5}, "eipscanner": {"baseline": "1.3.0", "port-version": 0}, "elements": {"baseline": "2024-09-12", "port-version": 0}, "elfio": {"baseline": "3.12", "port-version": 0}, "elfutils": {"baseline": "0.192", "port-version": 0}, "embree3": {"baseline": "3.13.5", "port-version": 4}, "enet": {"baseline": "1.3.18", "port-version": 0}, "enkits": {"baseline": "1.11", "port-version": 3}, "ensmallen": {"baseline": "2.21.1", "port-version": 0}, "entityx": {"baseline": "1.3.0", "port-version": 6}, "entt": {"baseline": "3.15.0", "port-version": 0}, "ereignis": {"baseline": "2.3", "port-version": 0}, "esaxx": {"baseline": "ca7cb332011ec37", "port-version": 1}, "etcd-cpp-apiv3": {"baseline": "0.15.4", "port-version": 3}, "etl": {"baseline": "20.40.0", "port-version": 0}, "eve": {"baseline": "2023.2.15", "port-version": 0}, "eventpp": {"baseline": "0.1.3", "port-version": 1}, "evpp": {"baseline": "0.7.0", "port-version": 8}, "exiv2": {"baseline": "0.28.5", "port-version": 0}, "expat": {"baseline": "2.7.1", "port-version": 0}, "expected-lite": {"baseline": "0.8.0", "port-version": 0}, "exprtk": {"baseline": "0.0.3", "port-version": 0}, "ezc3d": {"baseline": "1.5.11", "port-version": 0}, "ezfoundation": {"baseline": "21.10", "port-version": 0}, "faad2": {"baseline": "2.11.1", "port-version": 0}, "fadbad": {"baseline": "2.1.0", "port-version": 2}, "faiss": {"baseline": "1.8.0", "port-version": 0}, "fakeit": {"baseline": "2.4.1", "port-version": 0}, "fameta-counter": {"baseline": "2021-02-13", "port-version": 0}, "fann": {"baseline": "2023-01-26", "port-version": 0}, "farmhash": {"baseline": "1.1", "port-version": 6}, "fast-cpp-csv-parser": {"baseline": "2021-01-03", "port-version": 2}, "fast-double-parser": {"baseline": "0.8.0", "port-version": 0}, "fast-float": {"baseline": "8.0.2", "port-version": 0}, "fastcdr": {"baseline": "2.2.6", "port-version": 0}, "fastcgi": {"baseline": "2020-09-11", "port-version": 5}, "fastdds": {"baseline": "3.1.2", "port-version": 1}, "fastfeat": {"baseline": "391d5e9", "port-version": 4}, "fastgltf": {"baseline": "0.8.0", "port-version": 0}, "fastio": {"baseline": "2024-12-05", "port-version": 0}, "fastlz": {"baseline": "2024-08-02", "port-version": 1}, "fastor": {"baseline": "0.6.4", "port-version": 0}, "faudio": {"baseline": "24.09", "port-version": 0}, "fawdlstty-libfv": {"baseline": "0.0.8", "port-version": 0}, "fbgemm": {"baseline": "0.4.1", "port-version": 1}, "fbthrift": {"baseline": "2025.03.31.00", "port-version": 1}, "fcl": {"baseline": "0.7.0", "port-version": 4}, "fdk-aac": {"baseline": "2.0.2", "port-version": 4}, "fdlibm": {"baseline": "5.3", "port-version": 7}, "fenster": {"baseline": "2024-08-19", "port-version": 0}, "ffmpeg": {"baseline": "7.1.1", "port-version": 1}, "ffnvcodec": {"baseline": "12.2.72.0", "port-version": 2}, "fftw3": {"baseline": "3.3.10", "port-version": 9}, "fftwpp": {"baseline": "2019-12-19", "port-version": 2}, "fineftp": {"baseline": "1.3.4", "port-version": 0}, "fins": {"baseline": "2023-07-31", "port-version": 0}, "fixed-containers": {"baseline": "2024-09-19", "port-version": 0}, "fixed-string": {"baseline": "0.1.1", "port-version": 0}, "fizz": {"baseline": "2025.03.31.00", "port-version": 0}, "flagpp": {"baseline": "2.1", "port-version": 0}, "flann": {"baseline": "2022-10-28", "port-version": 0}, "flash-runtime-extensions": {"baseline": "2.4", "port-version": 1}, "flashlight-cpu": {"baseline": "0.3", "port-version": 5}, "flashlight-cuda": {"baseline": "0.3", "port-version": 7}, "flashlight-sequence": {"baseline": "0.0.1", "port-version": 0}, "flashlight-text": {"baseline": "0.0.4", "port-version": 0}, "flat": {"baseline": "2022-08-30", "port-version": 0}, "flatbuffers": {"baseline": "25.2.10", "port-version": 0}, "flatbush": {"baseline": "1.2.1", "port-version": 0}, "flatcc": {"baseline": "0.6.1", "port-version": 0}, "flecs": {"baseline": "4.0.5", "port-version": 0}, "flint": {"baseline": "2.9.0", "port-version": 1}, "fltk": {"baseline": "1.3.11", "port-version": 0}, "fluidlite": {"baseline": "2023-04-18", "port-version": 0}, "fluidsynth": {"baseline": "2.4.4", "port-version": 1}, "flux": {"baseline": "0.4.0", "port-version": 0}, "fmem": {"baseline": "c-libs-2ccee3d2fb", "port-version": 3}, "fmi4cpp": {"baseline": "0.8.0", "port-version": 0}, "fmilib": {"baseline": "2.4.1", "port-version": 2}, "fmt": {"baseline": "11.0.2", "port-version": 1}, "folly": {"baseline": "2025.03.31.00", "port-version": 1}, "font-chef": {"baseline": "1.1.0", "port-version": 0}, "font-util": {"baseline": "1.4.1", "port-version": 0}, "fontconfig": {"baseline": "2.15.0", "port-version": 2}, "foonathan-lexy": {"baseline": "2022.12.1", "port-version": 0}, "foonathan-memory": {"baseline": "0.7.3", "port-version": 2}, "forge": {"baseline": "1.0.8", "port-version": 3}, "foxi": {"baseline": "2021-12-01", "port-version": 0}, "fp16": {"baseline": "2021-02-21", "port-version": 2}, "freealut": {"baseline": "1.1.0", "port-version": 4}, "freeglut": {"baseline": "3.6.0", "port-version": 1}, "freeimage": {"baseline": "3.18.0", "port-version": 27}, "freeopcua": {"baseline": "20190125", "port-version": 9}, "freerdp": {"baseline": "3.8.0", "port-version": 0}, "freetds": {"baseline": "1.3.10", "port-version": 2}, "freetype": {"baseline": "2.13.3", "port-version": 0}, "freetype-gl": {"baseline": "1.0", "port-version": 0}, "freexl": {"baseline": "2.0.0", "port-version": 1}, "fribidi": {"baseline": "1.0.16", "port-version": 0}, "frozen": {"baseline": "1.2.0", "port-version": 0}, "frugally-deep": {"baseline": "0.18.0", "port-version": 0}, "fruit": {"baseline": "3.7.1", "port-version": 0}, "ftgl": {"baseline": "2.4.0", "port-version": 6}, "ftxui": {"baseline": "6.0.2", "port-version": 0}, "function2": {"baseline": "4.2.4", "port-version": 0}, "functionalplus": {"baseline": "0.2.25", "port-version": 0}, "functions-framework-cpp": {"baseline": "1.2.0", "port-version": 1}, "future-config": {"baseline": "0.1.0", "port-version": 0}, "fuzzylite": {"baseline": "6.0", "port-version": 6}, "fxaudio": {"baseline": "1.0.0", "port-version": 0}, "fxdiv": {"baseline": "2021-02-21", "port-version": 2}, "g2o": {"baseline": "2024-12-14", "port-version": 3}, "g3log": {"baseline": "2.4", "port-version": 0}, "gainput": {"baseline": "1.0.0", "port-version": 6}, "gamedev-framework": {"baseline": "1.2.0", "port-version": 0}, "gameinput": {"baseline": "1.0.26100.3987", "port-version": 0}, "gamenetworkingsockets": {"baseline": "1.4.1", "port-version": 1}, "games101-cgl": {"baseline": "0.1.0", "port-version": 0}, "gamma": {"baseline": "gamma-2018-01-27", "port-version": 6}, "gapp": {"baseline": "0.2.0", "port-version": 0}, "gasol": {"baseline": "2018-01-04", "port-version": 4}, "gaussianlib": {"baseline": "2024-11-03", "port-version": 0}, "gazebo": {"baseline": "11.15.1", "port-version": 0}, "gcem": {"baseline": "1.18.0", "port-version": 0}, "gdal": {"baseline": "3.10.3", "port-version": 0}, "gdbm": {"baseline": "1.24", "port-version": 0}, "gdcm": {"baseline": "3.0.24", "port-version": 0}, "gdk-pixbuf": {"baseline": "2.42.12", "port-version": 2}, "gegl": {"baseline": "0.4.54", "port-version": 0}, "gemmlowp": {"baseline": "2021-09-28", "port-version": 0}, "genann": {"baseline": "2019-07-10", "port-version": 3}, "geogram": {"baseline": "1.9.3", "port-version": 0}, "geographiclib": {"baseline": "2.5", "port-version": 0}, "geos": {"baseline": "3.13.0", "port-version": 1}, "geotrans": {"baseline": "3.9", "port-version": 1}, "getdns": {"baseline": "1.7.3", "port-version": 0}, "getopt": {"baseline": "0", "port-version": 3}, "getopt-win32": {"baseline": "1.1.0.20220925", "port-version": 0}, "gettext": {"baseline": "0.22.5", "port-version": 2}, "gettext-libintl": {"baseline": "0.22.5", "port-version": 3}, "gettimeofday": {"baseline": "2017-10-14", "port-version": 6}, "gexiv2": {"baseline": "0.14.3", "port-version": 1}, "gflags": {"baseline": "2.2.2", "port-version": 9}, "ggml": {"baseline": "2025-02-12", "port-version": 0}, "ghc-filesystem": {"baseline": "1.5.14", "port-version": 0}, "gherkin-c": {"baseline": "2019-10-07", "port-version": 3}, "giflib": {"baseline": "5.2.2", "port-version": 1}, "ginkgo": {"baseline": "1.9.0", "port-version": 0}, "gklib": {"baseline": "2023-03-27", "port-version": 0}, "gl2ps": {"baseline": "1.4.2", "port-version": 4}, "gl3w": {"baseline": "2018-05-31", "port-version": 5}, "glad": {"baseline": "0.1.36", "port-version": 0}, "glaze": {"baseline": "5.0.2", "port-version": 0}, "glbinding": {"baseline": "3.1.0", "port-version": 4}, "glew": {"baseline": "2.2.0", "port-version": 6}, "glfw3": {"baseline": "3.4", "port-version": 1}, "gli": {"baseline": "2021-07-06", "port-version": 2}, "glib": {"baseline": "2.83.4", "port-version": 0}, "glib-networking": {"baseline": "2.78.0", "port-version": 0}, "glibmm": {"baseline": "2.80.1", "port-version": 0}, "glm": {"baseline": "1.0.1", "port-version": 3}, "globjects": {"baseline": "1.1.0", "port-version": 6}, "glog": {"baseline": "0.7.1", "port-version": 0}, "gloo": {"baseline": "20240626", "port-version": 0}, "glpk": {"baseline": "5.0", "port-version": 3}, "glslang": {"baseline": "15.1.0", "port-version": 0}, "glui": {"baseline": "2019-11-30", "port-version": 4}, "gmime": {"baseline": "3.2.15", "port-version": 1}, "gmmlib": {"baseline": "22.5.2", "port-version": 0}, "gmp": {"baseline": "6.3.0", "port-version": 2}, "gmsh": {"baseline": "4.13.1", "port-version": 0}, "gobject-introspection": {"baseline": "1.82.0", "port-version": 0}, "godot-cpp": {"baseline": "4.4", "port-version": 0}, "google-cloud-cpp": {"baseline": "2.36.0", "port-version": 0}, "google-cloud-cpp-common": {"baseline": "alias", "port-version": 1}, "google-cloud-cpp-spanner": {"baseline": "alias", "port-version": 1}, "googleapis": {"baseline": "alias", "port-version": 2}, "gperf": {"baseline": "3.1", "port-version": 7}, "gperftools": {"baseline": "2.16", "port-version": 0}, "gpgme": {"baseline": "1.23.2", "port-version": 0}, "gpgmm": {"baseline": "0.1.2", "port-version": 1}, "gppanel": {"baseline": "2020-05-20", "port-version": 4}, "graaf": {"baseline": "1.1.1", "port-version": 0}, "grantlee": {"baseline": "5.3.1", "port-version": 2}, "graphene": {"baseline": "1.10.8", "port-version": 4}, "graphicsmagick": {"baseline": "1.3.41", "port-version": 1}, "graphite2": {"baseline": "1.3.14", "port-version": 4}, "graphviz": {"baseline": "10.0.1", "port-version": 3}, "greatest": {"baseline": "1.5.0", "port-version": 0}, "grpc": {"baseline": "1.71.0", "port-version": 0}, "grppi": {"baseline": "0.4.0", "port-version": 2}, "gsasl": {"baseline": "2.2.2", "port-version": 0}, "gsl": {"baseline": "2.8", "port-version": 0}, "gsl-lite": {"baseline": "0.42.0", "port-version": 0}, "gsoap": {"baseline": "2.8.112", "port-version": 2}, "gst-rtsp-server": {"baseline": "1.20.5", "port-version": 2}, "gstreamer": {"baseline": "1.24.12", "port-version": 0}, "gtest": {"baseline": "1.16.0", "port-version": 1}, "gtk": {"baseline": "4.16.3", "port-version": 2}, "gtk3": {"baseline": "3.24.43", "port-version": 0}, "gtkmm": {"baseline": "4.14.0", "port-version": 0}, "gtl": {"baseline": "1.2.0", "port-version": 0}, "gts": {"baseline": "0.7.6", "port-version": 9}, "gtsam": {"baseline": "4.2.0", "port-version": 1}, "guetzli": {"baseline": "2020-09-14", "port-version": 2}, "guile": {"baseline": "3.0.10", "port-version": 0}, "guilite": {"baseline": "2022-05-05", "port-version": 0}, "gul14": {"baseline": "2.11.2", "port-version": 0}, "gumbo": {"baseline": "0.12.3", "port-version": 0}, "gz-cmake": {"baseline": "4.1.1", "port-version": 1}, "gz-cmake3": {"baseline": "3.4.1", "port-version": 6}, "gz-common": {"baseline": "6.0.2", "port-version": 1}, "gz-common5": {"baseline": "5.4.1", "port-version": 4}, "gz-fuel-tools": {"baseline": "10.0.0", "port-version": 0}, "gz-fuel-tools8": {"baseline": "8.1.0", "port-version": 1}, "gz-gui": {"baseline": "9.0.0", "port-version": 1}, "gz-gui7": {"baseline": "7.2.1", "port-version": 1}, "gz-math": {"baseline": "8.1.0", "port-version": 0}, "gz-math7": {"baseline": "7.3.0", "port-version": 1}, "gz-msgs": {"baseline": "11.0.2", "port-version": 1}, "gz-msgs9": {"baseline": "9.5.0", "port-version": 1}, "gz-physics": {"baseline": "8.0.0", "port-version": 2}, "gz-physics6": {"baseline": "6.5.1", "port-version": 3}, "gz-plugin": {"baseline": "3.0.0", "port-version": 0}, "gz-plugin2": {"baseline": "2.0.1", "port-version": 1}, "gz-rendering": {"baseline": "9.0.0", "port-version": 0}, "gz-rendering7": {"baseline": "7.4.1", "port-version": 2}, "gz-sensors": {"baseline": "9.0.0", "port-version": 0}, "gz-sensors7": {"baseline": "7.3.0", "port-version": 1}, "gz-sim": {"baseline": "9.0.0", "port-version": 0}, "gz-tools": {"baseline": "2.0.1", "port-version": 0}, "gz-tools2": {"baseline": "2.0.0", "port-version": 2}, "gz-transport": {"baseline": "14.0.0", "port-version": 0}, "gz-transport12": {"baseline": "12.2.1", "port-version": 1}, "gz-utils": {"baseline": "3.1.0", "port-version": 0}, "gz-utils2": {"baseline": "2.0.0", "port-version": 1}, "gzip-hpp": {"baseline": "0.1.0", "port-version": 2}, "h3": {"baseline": "4.1.0", "port-version": 0}, "h5py-lzf": {"baseline": "3.12.1", "port-version": 0}, "half": {"baseline": "2.2.0", "port-version": 0}, "halide": {"baseline": "18.0.0", "port-version": 1}, "happly": {"baseline": "2021-03-19", "port-version": 0}, "hareflow": {"baseline": "0.1.1", "port-version": 1}, "harfbuzz": {"baseline": "10.2.0", "port-version": 3}, "hash-library": {"baseline": "8", "port-version": 3}, "hashids": {"baseline": "1.2.2", "port-version": 0}, "hayai": {"baseline": "2019-08-10", "port-version": 4}, "hazelcast-cpp-client": {"baseline": "5.3.0", "port-version": 2}, "hdf5": {"baseline": "********", "port-version": 4}, "hdr-histogram": {"baseline": "0.11.8", "port-version": 0}, "healpix": {"baseline": "1.12.10", "port-version": 9}, "hedley": {"baseline": "15", "port-version": 0}, "hello-imgui": {"baseline": "1.6.0", "port-version": 3}, "hexi": {"baseline": "1.3.4", "port-version": 0}, "hexl": {"baseline": "1.2.5", "port-version": 0}, "hffix": {"baseline": "1.4.1", "port-version": 0}, "hfsm2": {"baseline": "2.6.1", "port-version": 0}, "hidapi": {"baseline": "0.14.0", "port-version": 1}, "highfive": {"baseline": "2.10.1", "port-version": 1}, "highs": {"baseline": "1.10.0", "port-version": 0}, "highway": {"baseline": "1.2.0", "port-version": 0}, "hikogui": {"baseline": "0.8.1", "port-version": 2}, "hiredis": {"baseline": "1.2.0", "port-version": 0}, "hjson-cpp": {"baseline": "2.4.1", "port-version": 0}, "hlslpp": {"baseline": "3.6", "port-version": 0}, "hnswlib": {"baseline": "0.8.0", "port-version": 0}, "hps": {"baseline": "2022-01-18", "port-version": 0}, "hpx": {"baseline": "1.10.0", "port-version": 1}, "htscodecs": {"baseline": "1.6.1", "port-version": 0}, "htslib": {"baseline": "1.21", "port-version": 0}, "http-parser": {"baseline": "2.9.4", "port-version": 3}, "hungarian": {"baseline": "0.1.3", "port-version": 3}, "hunspell": {"baseline": "1.7.2", "port-version": 1}, "hwloc": {"baseline": "2.11.2", "port-version": 0}, "hyperscan": {"baseline": "5.4.2", "port-version": 0}, "hypodermic": {"baseline": "2023-03-03", "port-version": 0}, "hypre": {"baseline": "2.32.0", "port-version": 0}, "icecream-cpp": {"baseline": "1.0.0", "port-version": 0}, "iceoryx": {"baseline": "2.0.6", "port-version": 1}, "icu": {"baseline": "74.2", "port-version": 5}, "ideviceinstaller": {"baseline": "2023-07-21", "port-version": 0}, "idevicerestore": {"baseline": "2023-05-23", "port-version": 0}, "idyntree": {"baseline": "12.4.0", "port-version": 0}, "if97": {"baseline": "2.1.3", "port-version": 1}, "igloo": {"baseline": "1.1.1", "port-version": 2}, "ignition-modularscripts": {"baseline": "2025-02-27", "port-version": 0}, "igraph": {"baseline": "0.10.15", "port-version": 1}, "iir1": {"baseline": "1.9.5", "port-version": 1}, "ijg-libjpeg": {"baseline": "9e", "port-version": 2}, "im3d": {"baseline": "2022-10-11", "port-version": 0}, "imageinfo": {"baseline": "2024-12-02", "port-version": 0}, "imath": {"baseline": "3.1.12", "port-version": 0}, "imcce-openfa": {"baseline": "20231011.0.3", "port-version": 0}, "imgui": {"baseline": "1.91.9", "port-version": 0}, "imgui-node-editor": {"baseline": "0.9.3", "port-version": 2}, "imgui-sfml": {"baseline": "3.0", "port-version": 1}, "imguizmo": {"baseline": "2024-05-29", "port-version": 1}, "immer": {"baseline": "0.8.1", "port-version": 0}, "implot": {"baseline": "0.16", "port-version": 0}, "implot3d": {"baseline": "0.2", "port-version": 0}, "indicators": {"baseline": "2.3", "port-version": 0}, "indirect-value": {"baseline": "2023-06-01", "port-version": 0}, "influxdb-cxx": {"baseline": "0.7.4", "port-version": 0}, "infoware": {"baseline": "2023-04-12", "port-version": 0}, "inih": {"baseline": "59", "port-version": 0}, "iniparser": {"baseline": "2020-04-06", "port-version": 5}, "inipp": {"baseline": "1.0.12", "port-version": 0}, "inja": {"baseline": "3.4.0", "port-version": 0}, "intel-ipsec": {"baseline": "1.1", "port-version": 0}, "intel-mkl": {"baseline": "2023.0.0", "port-version": 5}, "intelrdfpmathlib": {"baseline": "20U2", "port-version": 6}, "intrusive-shared-ptr": {"baseline": "1.6", "port-version": 0}, "io2d": {"baseline": "2020-09-14", "port-version": 5}, "iowa-hills-dsp": {"baseline": "0.1.0", "port-version": 0}, "irrlicht": {"baseline": "1.8.5", "port-version": 0}, "irrxml": {"baseline": "0", "port-version": 1}, "irsdkcpp": {"baseline": "1.0.9", "port-version": 0}, "isal": {"baseline": "2.30.0", "port-version": 0}, "ismrmrd": {"baseline": "1.14.1", "port-version": 1}, "itay-grudev-singleapplication": {"baseline": "3.5.2", "port-version": 0}, "itk": {"baseline": "5.4.0", "port-version": 2}, "itlib": {"baseline": "1.11.7", "port-version": 0}, "itpp": {"baseline": "4.3.1", "port-version": 12}, "itsy-bitsy": {"baseline": "2022-08-02", "port-version": 0}, "ixwebsocket": {"baseline": "11.4.5", "port-version": 0}, "jack2": {"baseline": "1.9.22", "port-version": 0}, "jaeger-client-cpp": {"baseline": "0.7.0", "port-version": 2}, "jansson": {"baseline": "2.14", "port-version": 1}, "jasper": {"baseline": "4.2.4", "port-version": 2}, "jbig2dec": {"baseline": "0.20", "port-version": 0}, "jbigkit": {"baseline": "2.1", "port-version": 7}, "jemalloc": {"baseline": "5.3.0", "port-version": 2}, "jhasse-poly2tri": {"baseline": "2023-12-27", "port-version": 2}, "jigson": {"baseline": "0.1.3", "port-version": 0}, "jinja2cpplight": {"baseline": "2018-05-08", "port-version": 3}, "jkqtplotter": {"baseline": "2023-10-24", "port-version": 1}, "joltphysics": {"baseline": "5.3.0", "port-version": 0}, "josuttis-jthread": {"baseline": "2020-07-21", "port-version": 3}, "jsmn": {"baseline": "2019-04-27", "port-version": 2}, "json-c": {"baseline": "0.18-20240915", "port-version": 0}, "json-dto": {"baseline": "0.3.4", "port-version": 0}, "json-glib": {"baseline": "1.10.6", "port-version": 0}, "json-rpc-cxx": {"baseline": "0.3.2", "port-version": 0}, "json-schema-validator": {"baseline": "2.3.0", "port-version": 2}, "json-spirit": {"baseline": "4.1.0", "port-version": 5}, "json11": {"baseline": "2017-06-20", "port-version": 6}, "json5-parser": {"baseline": "1.0.0", "port-version": 7}, "jsoncons": {"baseline": "1.3.0", "port-version": 0}, "jsoncpp": {"baseline": "1.9.6", "port-version": 0}, "jsonifier": {"baseline": "0.9.98", "port-version": 0}, "jsonnet": {"baseline": "0.20.0", "port-version": 1}, "juce": {"baseline": "8.0.4", "port-version": 2}, "jwt-cpp": {"baseline": "0.7.0", "port-version": 1}, "jxrlib": {"baseline": "2019.10.9", "port-version": 7}, "kaitai-struct-cpp-stl-runtime": {"baseline": "0.10.1", "port-version": 1}, "kangaru": {"baseline": "4.3.2", "port-version": 0}, "kcp": {"baseline": "1.7", "port-version": 0}, "kdalgorithms": {"baseline": "1.4", "port-version": 0}, "kdbindings": {"baseline": "1.1.0", "port-version": 0}, "kddockwidgets": {"baseline": "2.1.0", "port-version": 1}, "kdreports": {"baseline": "2.3.0", "port-version": 0}, "kdsingleapplication": {"baseline": "1.1.0", "port-version": 1}, "kdsoap": {"baseline": "2.2.0", "port-version": 1}, "kdstatemachineeditor": {"baseline": "2.0.0", "port-version": 0}, "kealib": {"baseline": "1.6.1", "port-version": 0}, "keccak-tiny": {"baseline": "2014-09-08", "port-version": 2}, "kenlm": {"baseline": "20230531", "port-version": 1}, "kerbal": {"baseline": "2024.11.1", "port-version": 0}, "keystone": {"baseline": "0.9.2", "port-version": 3}, "kf5archive": {"baseline": "5.98.0", "port-version": 1}, "kf5attica": {"baseline": "5.98.0", "port-version": 0}, "kf5auth": {"baseline": "5.98.0", "port-version": 0}, "kf5bookmarks": {"baseline": "5.98.0", "port-version": 0}, "kf5codecs": {"baseline": "5.98.0", "port-version": 0}, "kf5completion": {"baseline": "5.98.0", "port-version": 0}, "kf5config": {"baseline": "5.98.0", "port-version": 0}, "kf5configwidgets": {"baseline": "5.98.0", "port-version": 0}, "kf5coreaddons": {"baseline": "5.98.0", "port-version": 0}, "kf5crash": {"baseline": "5.98.0", "port-version": 0}, "kf5dbusaddons": {"baseline": "5.98.0", "port-version": 0}, "kf5declarative": {"baseline": "5.98.0", "port-version": 0}, "kf5diagram": {"baseline": "2.8.0", "port-version": 1}, "kf5globalaccel": {"baseline": "5.98.0", "port-version": 0}, "kf5guiaddons": {"baseline": "5.98.0", "port-version": 0}, "kf5holidays": {"baseline": "5.98.0", "port-version": 0}, "kf5i18n": {"baseline": "5.98.0", "port-version": 1}, "kf5iconthemes": {"baseline": "5.98.0", "port-version": 0}, "kf5itemmodels": {"baseline": "5.98.0", "port-version": 0}, "kf5itemviews": {"baseline": "5.98.0", "port-version": 0}, "kf5jobwidgets": {"baseline": "5.98.0", "port-version": 0}, "kf5kcmutils": {"baseline": "5.98.0", "port-version": 0}, "kf5kio": {"baseline": "5.98.0", "port-version": 1}, "kf5newstuff": {"baseline": "5.98.0", "port-version": 0}, "kf5notifications": {"baseline": "5.98.0", "port-version": 0}, "kf5package": {"baseline": "5.98.0", "port-version": 0}, "kf5parts": {"baseline": "5.98.0", "port-version": 1}, "kf5plotting": {"baseline": "5.98.0", "port-version": 1}, "kf5service": {"baseline": "5.98.0", "port-version": 1}, "kf5solid": {"baseline": "5.98.0", "port-version": 3}, "kf5sonnet": {"baseline": "5.98.0", "port-version": 0}, "kf5syntaxhighlighting": {"baseline": "5.98.0", "port-version": 0}, "kf5texteditor": {"baseline": "5.98.0", "port-version": 2}, "kf5textwidgets": {"baseline": "5.98.0", "port-version": 0}, "kf5wallet": {"baseline": "5.98.0", "port-version": 0}, "kf5widgetsaddons": {"baseline": "5.98.0", "port-version": 0}, "kf5windowsystem": {"baseline": "5.98.0", "port-version": 0}, "kf5xmlgui": {"baseline": "5.98.0", "port-version": 0}, "kfr": {"baseline": "6.0.3", "port-version": 0}, "kinectsdk1": {"baseline": "1.8", "port-version": 8}, "kinectsdk2": {"baseline": "2.0", "port-version": 7}, "kissfft": {"baseline": "131.1.0", "port-version": 0}, "kissnet": {"baseline": "2024-01-20", "port-version": 0}, "klein": {"baseline": "2021-05-09", "port-version": 0}, "knet": {"baseline": "1.1.0", "port-version": 1}, "krabsetw": {"baseline": "4.3.2", "port-version": 0}, "krb5": {"baseline": "1.21.3", "port-version": 2}, "ktx": {"baseline": "4.3.2", "port-version": 1}, "kubazip": {"baseline": "0.3.3", "port-version": 0}, "kubernetes": {"baseline": "0.6.0", "port-version": 1}, "kuku": {"baseline": "2.1.0", "port-version": 0}, "kvasir-mpl": {"baseline": "2019-08-06", "port-version": 3}, "kwsys": {"baseline": "2021-08-06", "port-version": 1}, "lager": {"baseline": "2023-03-19", "port-version": 0}, "lapack": {"baseline": "2023-06-10", "port-version": 2}, "lapack-reference": {"baseline": "3.12.1", "port-version": 0}, "lastools": {"baseline": "2.0.3", "port-version": 0}, "laszip": {"baseline": "3.4.4", "port-version": 0}, "launch-darkly-server": {"baseline": "2.9.3", "port-version": 0}, "lazy-importer": {"baseline": "2023-08-03", "port-version": 0}, "lcm": {"baseline": "1.4.0", "port-version": 7}, "lcms": {"baseline": "2.16", "port-version": 0}, "leaf": {"baseline": "0.2.2", "port-version": 2}, "lely-core": {"baseline": "2.3.5", "port-version": 0}, "lemon": {"baseline": "0", "port-version": 2}, "lemon-parser-generator": {"baseline": "3.39.3", "port-version": 0}, "lensfun": {"baseline": "0.3.4", "port-version": 2}, "leptonica": {"baseline": "1.85.0", "port-version": 0}, "lerc": {"baseline": "4.0.4", "port-version": 0}, "lest": {"baseline": "1.35.2", "port-version": 0}, "level-zero": {"baseline": "1.20.5", "port-version": 0}, "leveldb": {"baseline": "1.23", "port-version": 0}, "levmar": {"baseline": "2.6", "port-version": 3}, "lexbor": {"baseline": "2.4.0", "port-version": 0}, "lfreist-hwinfo": {"baseline": "2024-09-01", "port-version": 0}, "lib3mf": {"baseline": "2.4.1", "port-version": 0}, "libaaplus": {"baseline": "2.36", "port-version": 1}, "libadlmidi": {"baseline": "1.5.1", "port-version": 1}, "libadwaita": {"baseline": "1.3.2", "port-version": 2}, "libaec": {"baseline": "1.1.3", "port-version": 0}, "libaes-siv": {"baseline": "2020-10-15", "port-version": 0}, "libaiff": {"baseline": "5.0", "port-version": 9}, "libaio": {"baseline": "0.3.113", "port-version": 0}, "libalkimia": {"baseline": "8.1.72", "port-version": 0}, "libao": {"baseline": "1.2.2", "port-version": 5}, "libarchive": {"baseline": "3.7.8", "port-version": 1}, "libaribcaption": {"baseline": "1.1.1", "port-version": 0}, "libass": {"baseline": "0.17.3", "port-version": 0}, "libassert": {"baseline": "2.1.5", "port-version": 0}, "libassuan": {"baseline": "3.0.1", "port-version": 0}, "libatomic-ops": {"baseline": "7.8.2", "port-version": 0}, "libavif": {"baseline": "1.1.1", "port-version": 1}, "libb2": {"baseline": "0.98.1", "port-version": 7}, "libbacktrace": {"baseline": "2024-11-30", "port-version": 0}, "libbf": {"baseline": "1.0.0", "port-version": 4}, "libbson": {"baseline": "1.30.2", "port-version": 0}, "libcaer": {"baseline": "3.3.15", "port-version": 2}, "libcanberra": {"baseline": "0.30", "port-version": 4}, "libcap": {"baseline": "2.73", "port-version": 0}, "libcbor": {"baseline": "0.12.0", "port-version": 0}, "libcds": {"baseline": "2.3.3", "port-version": 4}, "libcerf": {"baseline": "2.4", "port-version": 0}, "libcgroup": {"baseline": "3.1.0", "port-version": 0}, "libconfig": {"baseline": "1.7.3", "port-version": 5}, "libconfuse": {"baseline": "3.3", "port-version": 0}, "libcopp": {"baseline": "2.3.1", "port-version": 0}, "libcoro": {"baseline": "0.14.1", "port-version": 0}, "libcorrect": {"baseline": "2018-10-11", "port-version": 0}, "libcpplocate": {"baseline": "2.3.0", "port-version": 0}, "libcrafter": {"baseline": "1.0", "port-version": 2}, "libcred": {"baseline": "1.0.0", "port-version": 0}, "libcroco": {"baseline": "0.6.13", "port-version": 7}, "libcsv": {"baseline": "3.0.3", "port-version": 1}, "libcuckoo": {"baseline": "0.3.1", "port-version": 0}, "libcurl-simple-https": {"baseline": "2022-02-14", "port-version": 0}, "libdatachannel": {"baseline": "0.22.6", "port-version": 0}, "libdatrie": {"baseline": "0.2.13", "port-version": 1}, "libdc1394": {"baseline": "2.2.7", "port-version": 0}, "libde265": {"baseline": "1.0.15", "port-version": 0}, "libdeflate": {"baseline": "1.23", "port-version": 0}, "libdisasm": {"baseline": "0.23", "port-version": 11}, "libdivide": {"baseline": "5.2.0", "port-version": 0}, "libdjinterop": {"baseline": "0.24.3", "port-version": 0}, "libdmtx": {"baseline": "0.7.7", "port-version": 1}, "libdmx": {"baseline": "1.1.5", "port-version": 0}, "libdshowcapture": {"baseline": "0.6.0", "port-version": 4}, "libdvdcss": {"baseline": "1.4.3", "port-version": 0}, "libdvdnav": {"baseline": "6.1.1", "port-version": 0}, "libdvdread": {"baseline": "6.1.3", "port-version": 0}, "libdwarf": {"baseline": "0.12.0", "port-version": 0}, "libe57": {"baseline": "1.1.332", "port-version": 5}, "libe57format": {"baseline": "3.2.0", "port-version": 0}, "libebur128": {"baseline": "1.2.6", "port-version": 3}, "libedit": {"baseline": "2024-08-08", "port-version": 0}, "libenvpp": {"baseline": "1.5.1", "port-version": 0}, "libepoxy": {"baseline": "1.5.10", "port-version": 2}, "liberasurecode": {"baseline": "1.6.4", "port-version": 0}, "libev": {"baseline": "4.33", "port-version": 4}, "libevdev": {"baseline": "1.13.4", "port-version": 0}, "libevent": {"baseline": "2.1.12+20230128", "port-version": 1}, "libeventheader-decode": {"baseline": "1.4.0", "port-version": 0}, "libeventheader-tracepoint": {"baseline": "1.4.0", "port-version": 0}, "libevhtp": {"baseline": "1.2.18", "port-version": 5}, "libexif": {"baseline": "0.6.25", "port-version": 0}, "libfabric": {"baseline": "1.22.0", "port-version": 0}, "libffi": {"baseline": "3.4.7", "port-version": 1}, "libfido2": {"baseline": "1.15.0", "port-version": 1}, "libflac": {"baseline": "1.4.3", "port-version": 2}, "libfontenc": {"baseline": "1.1.4", "port-version": 0}, "libfork": {"baseline": "3.8.0", "port-version": 0}, "libfort": {"baseline": "0.4.2", "port-version": 1}, "libfreenect2": {"baseline": "0.2.1", "port-version": 2}, "libfs": {"baseline": "1.0.9", "port-version": 0}, "libftdi": {"baseline": "0.20", "port-version": 5}, "libftdi1": {"baseline": "1.5", "port-version": 5}, "libfuse": {"baseline": "3.16.2", "port-version": 0}, "libgcrypt": {"baseline": "1.11.0", "port-version": 0}, "libgd": {"baseline": "2.3.3", "port-version": 3}, "libgeotiff": {"baseline": "1.7.4", "port-version": 0}, "libgig": {"baseline": "4.4.1", "port-version": 0}, "libgit2": {"baseline": "1.9.0", "port-version": 1}, "libgme": {"baseline": "0.6.3", "port-version": 0}, "libgnutls": {"baseline": "3.8.7.1", "port-version": 0}, "libgo": {"baseline": "3.1", "port-version": 5}, "libgpg-error": {"baseline": "1.51", "port-version": 0}, "libgpiod": {"baseline": "2.1.3", "port-version": 0}, "libgta": {"baseline": "1.0.8", "port-version": 5}, "libguarded": {"baseline": "1.4.1", "port-version": 0}, "libgwenhywfar": {"baseline": "5.12.0", "port-version": 0}, "libgxps": {"baseline": "0.3.2", "port-version": 4}, "libharu": {"baseline": "2.4.4", "port-version": 1}, "libhdfs3": {"baseline": "2019-11-05", "port-version": 6}, "libheif": {"baseline": "1.19.5", "port-version": 3}, "libhsplasma": {"baseline": "2024-03-07", "port-version": 0}, "libhv": {"baseline": "1.3.3", "port-version": 0}, "libhydrogen": {"baseline": "2022-06-21", "port-version": 0}, "libical": {"baseline": "3.0.19", "port-version": 0}, "libice": {"baseline": "1.1.1", "port-version": 0}, "libiconv": {"baseline": "1.18", "port-version": 1}, "libics": {"baseline": "1.6.8", "port-version": 0}, "libid3tag": {"baseline": "0.16.3", "port-version": 0}, "libideviceactivation": {"baseline": "2023-05-01", "port-version": 1}, "libidn2": {"baseline": "2.3.7", "port-version": 2}, "libigl": {"baseline": "2.5.0", "port-version": 2}, "libilbc": {"baseline": "3.0.4", "port-version": 0}, "libimobiledevice": {"baseline": "2023-07-05", "port-version": 1}, "libimobiledevice-glue": {"baseline": "2023-05-13", "port-version": 0}, "libinterpolate": {"baseline": "2.7.1", "port-version": 0}, "libirecovery": {"baseline": "2023-05-13", "port-version": 2}, "libjpeg-turbo": {"baseline": "3.1.0", "port-version": 1}, "libjuice": {"baseline": "1.5.8", "port-version": 0}, "libjxl": {"baseline": "0.11.1", "port-version": 0}, "libkeyfinder": {"baseline": "2.2.8", "port-version": 0}, "libkml": {"baseline": "1.3.0", "port-version": 13}, "liblas": {"baseline": "1.8.1", "port-version": 15}, "liblbfgs": {"baseline": "1.10", "port-version": 2}, "libleidenalg": {"baseline": "0.11.1", "port-version": 0}, "liblemon": {"baseline": "2019-06-13", "port-version": 9}, "liblinear": {"baseline": "243", "port-version": 1}, "liblo": {"baseline": "0.32", "port-version": 0}, "liblrc": {"baseline": "1.0.0", "port-version": 0}, "liblsl": {"baseline": "1.16.2", "port-version": 0}, "liblsquic": {"baseline": "3.3.2", "port-version": 1}, "liblzf": {"baseline": "3.6", "port-version": 1}, "liblzma": {"baseline": "5.8.1", "port-version": 0}, "libmad": {"baseline": "0.16.4", "port-version": 3}, "libmagic": {"baseline": "5.46", "port-version": 2}, "libmariadb": {"baseline": "3.4.1", "port-version": 0}, "libmatio-cpp": {"baseline": "0.3.0", "port-version": 0}, "libmaxminddb": {"baseline": "1.12.2", "port-version": 0}, "libmediainfo": {"baseline": "25.3", "port-version": 0}, "libmem": {"baseline": "5.0.4", "port-version": 1}, "libmesh": {"baseline": "1.5.0", "port-version": 6}, "libmicrodns": {"baseline": "0.2.0", "port-version": 2}, "libmicrohttpd": {"baseline": "1.0.1", "port-version": 1}, "libmidi2": {"baseline": "0.15", "port-version": 0}, "libmikmod": {"baseline": "3.3.11.1", "port-version": 13}, "libmodbus": {"baseline": "3.1.10", "port-version": 0}, "libmodman": {"baseline": "2.0.1", "port-version": 5}, "libmodplug": {"baseline": "0.8.9.0", "port-version": 14}, "libmorton": {"baseline": "0.2.12", "port-version": 0}, "libmount": {"baseline": "2.40", "port-version": 0}, "libmpeg2": {"baseline": "0.5.1", "port-version": 3}, "libmspack": {"baseline": "0.11", "port-version": 0}, "libmt32emu": {"baseline": "2.7.1", "port-version": 0}, "libmultisense": {"baseline": "6.1.0", "port-version": 0}, "libmupdf": {"baseline": "1.25.4", "port-version": 0}, "libmypaint": {"baseline": "1.6.1", "port-version": 0}, "libmysofa": {"baseline": "1.3.2", "port-version": 0}, "libmysql": {"baseline": "8.0.40", "port-version": 0}, "libnice": {"baseline": "0.1.22", "port-version": 0}, "libnice-gst": {"baseline": "0.1.22", "port-version": 0}, "libnick": {"baseline": "2025.3.6", "port-version": 0}, "libnoise": {"baseline": "1.0.0", "port-version": 3}, "libnop": {"baseline": "2021-11-03", "port-version": 0}, "libobfuscate": {"baseline": "2024-07-10", "port-version": 0}, "libodb": {"baseline": "2.4.0", "port-version": 12}, "libodb-boost": {"baseline": "2.4.0", "port-version": 7}, "libodb-mysql": {"baseline": "2.4.0", "port-version": 11}, "libodb-pgsql": {"baseline": "2.4.0", "port-version": 8}, "libodb-sqlite": {"baseline": "2.4.0", "port-version": 13}, "libofx": {"baseline": "0.10.9", "port-version": 1}, "libogg": {"baseline": "1.3.5", "port-version": 2}, "libopenmpt": {"baseline": "0.7.13", "port-version": 0}, "libopensp": {"baseline": "1.5.2", "port-version": 3}, "libopnmidi": {"baseline": "1.5.1", "port-version": 2}, "libopusenc": {"baseline": "0.2.1", "port-version": 3}, "liboqs": {"baseline": "0.12.0", "port-version": 0}, "liborigin": {"baseline": "3.0.3", "port-version": 0}, "libosdp": {"baseline": "3.0.5", "port-version": 0}, "libosip2": {"baseline": "5.3.1", "port-version": 1}, "libosmium": {"baseline": "2.21.0", "port-version": 0}, "libosmscout": {"baseline": "1.1.1", "port-version": 5}, "libp7-baical": {"baseline": "replaced", "port-version": 1}, "libp7client": {"baseline": "5.6", "port-version": 5}, "libpcap": {"baseline": "1.10.5", "port-version": 0}, "libpff": {"baseline": "2021-11-14", "port-version": 2}, "libphonenumber": {"baseline": "9.0.2", "port-version": 0}, "libplist": {"baseline": "2023-06-15", "port-version": 1}, "libpmemobj-cpp": {"baseline": "1.13.0", "port-version": 1}, "libpng": {"baseline": "1.6.46", "port-version": 0}, "libpopt": {"baseline": "1.16", "port-version": 18}, "libpq": {"baseline": "16.4", "port-version": 0}, "libpqxx": {"baseline": "7.10.0", "port-version": 0}, "libprotobuf-mutator": {"baseline": "1.3", "port-version": 1}, "libproxy": {"baseline": "0.4.18", "port-version": 3}, "libpsl": {"baseline": "0.21.5", "port-version": 1}, "libqcow": {"baseline": "20221124", "port-version": 1}, "libqglviewer": {"baseline": "2.9.1", "port-version": 3}, "libqrencode": {"baseline": "4.1.1", "port-version": 2}, "libqtrest": {"baseline": "0.4.0", "port-version": 0}, "librabbitmq": {"baseline": "0.15.0", "port-version": 0}, "libraqm": {"baseline": "0.10.2", "port-version": 0}, "libraw": {"baseline": "0.21.3", "port-version": 0}, "librdkafka": {"baseline": "2.8.0", "port-version": 0}, "libredwg": {"baseline": "0.13.3", "port-version": 1}, "libremidi": {"baseline": "4.5.0", "port-version": 1}, "libressl": {"baseline": "4.0.0", "port-version": 1}, "librsvg": {"baseline": "2.40.21", "port-version": 0}, "librsync": {"baseline": "2.3.4", "port-version": 0}, "librtmp": {"baseline": "2024-03-01", "port-version": 1}, "librtpi": {"baseline": "1.0.1", "port-version": 0}, "librttopo": {"baseline": "1.1.0", "port-version": 9}, "libsamplerate": {"baseline": "0.2.2", "port-version": 1}, "libsass": {"baseline": "3.6.6", "port-version": 0}, "libsbml": {"baseline": "5.20.4", "port-version": 0}, "libsbsms": {"baseline": "2.3.0", "port-version": 0}, "libsecret": {"baseline": "0.21.4", "port-version": 1}, "libsercomm": {"baseline": "1.3.2", "port-version": 1}, "libsersi": {"baseline": "0.1.0", "port-version": 0}, "libshout": {"baseline": "2.4.6", "port-version": 0}, "libsigcpp": {"baseline": "3.6.0", "port-version": 1}, "libsigcpp-3": {"baseline": "3.0.3", "port-version": 1}, "libslirp": {"baseline": "4.9.0", "port-version": 0}, "libsm": {"baseline": "1.2.3", "port-version": 1}, "libsmacker": {"baseline": "1.2.0", "port-version": 0}, "libsmb2": {"baseline": "6.2", "port-version": 0}, "libsndfile": {"baseline": "1.2.2", "port-version": 0}, "libsnoretoast": {"baseline": "0.8.0", "port-version": 2}, "libsodium": {"baseline": "1.0.20", "port-version": 3}, "libsonic": {"baseline": "0.2.0", "port-version": 0}, "libsoundio": {"baseline": "2.0.0", "port-version": 7}, "libsoup": {"baseline": "3.6.0", "port-version": 0}, "libspatialindex": {"baseline": "2.0.0", "port-version": 0}, "libspatialite": {"baseline": "5.1.0", "port-version": 4}, "libspnav": {"baseline": "0.2.3", "port-version": 2}, "libspng": {"baseline": "0.7.4", "port-version": 0}, "libsquish": {"baseline": "1.15", "port-version": 14}, "libsrt": {"baseline": "1.5.4", "port-version": 0}, "libsrtp": {"baseline": "2.7.0", "port-version": 0}, "libssh": {"baseline": "0.10.6", "port-version": 1}, "libssh2": {"baseline": "1.11.1", "port-version": 1}, "libstemmer": {"baseline": "2021.2.2.0", "port-version": 0}, "libstk": {"baseline": "4.6.1", "port-version": 3}, "libsvm": {"baseline": "3.35", "port-version": 0}, "libsystemd": {"baseline": "256.4", "port-version": 0}, "libtar": {"baseline": "1.2.20", "port-version": 1}, "libtasn1": {"baseline": "4.19.0", "port-version": 1}, "libtcod": {"baseline": "2.1.1", "port-version": 0}, "libtess2": {"baseline": "2021-12-27", "port-version": 0}, "libtheora": {"baseline": "1.2.0alpha1-20170719", "port-version": 8}, "libtins": {"baseline": "4.5", "port-version": 0}, "libtomcrypt": {"baseline": "1.18.2", "port-version": 3}, "libtommath": {"baseline": "1.3.0", "port-version": 2}, "libtorch": {"baseline": "2.1.2", "port-version": 11}, "libtorrent": {"baseline": "2.0.11", "port-version": 0}, "libtracepoint": {"baseline": "1.4.0", "port-version": 0}, "libtracepoint-control": {"baseline": "1.4.0", "port-version": 0}, "libtracepoint-decode": {"baseline": "1.4.0", "port-version": 0}, "libu2f-server": {"baseline": "1.1.0", "port-version": 5}, "libudis86": {"baseline": "2018-01-28", "port-version": 4}, "libudns": {"baseline": "0.4", "port-version": 6}, "libui": {"baseline": "2018-11-03", "port-version": 3}, "libunibreak": {"baseline": "6.1", "port-version": 1}, "libunifex": {"baseline": "0.4.0", "port-version": 0}, "libunistring": {"baseline": "1.2", "port-version": 0}, "libunwind": {"baseline": "1.8.1", "port-version": 3}, "liburing": {"baseline": "2.9", "port-version": 0}, "libusb": {"baseline": "1.0.27", "port-version": 2}, "libusb-win32": {"baseline": "1.2.6.0", "port-version": 10}, "libusbmuxd": {"baseline": "2023-06-21", "port-version": 1}, "libusbp": {"baseline": "1.3.1", "port-version": 0}, "libuuid": {"baseline": "1.0.3", "port-version": 15}, "libuv": {"baseline": "1.50.0", "port-version": 0}, "libuvc": {"baseline": "0.0.7", "port-version": 1}, "libvault": {"baseline": "0.61.0", "port-version": 0}, "libvhdi": {"baseline": "20231127", "port-version": 0}, "libvmaf": {"baseline": "3.0.0", "port-version": 0}, "libvmdk": {"baseline": "20221124", "port-version": 1}, "libvorbis": {"baseline": "1.3.7", "port-version": 4}, "libvpx": {"baseline": "1.13.1", "port-version": 4}, "libwandio": {"baseline": "4.2.1", "port-version": 6}, "libwebm": {"baseline": "1.0.0.31", "port-version": 1}, "libwebp": {"baseline": "1.5.0", "port-version": 0}, "libwebsockets": {"baseline": "4.3.3", "port-version": 1}, "libx11": {"baseline": "1.8.1", "port-version": 2}, "libxau": {"baseline": "1.0.9", "port-version": 0}, "libxaw": {"baseline": "1.0.13", "port-version": 0}, "libxcomposite": {"baseline": "0.4.5", "port-version": 0}, "libxcrypt": {"baseline": "4.4.38", "port-version": 0}, "libxcvt": {"baseline": "0.1.2", "port-version": 1}, "libxdamage": {"baseline": "1.1.5", "port-version": 0}, "libxdf": {"baseline": "0.99.9", "port-version": 0}, "libxdiff": {"baseline": "0.23", "port-version": 4}, "libxdmcp": {"baseline": "1.1.3", "port-version": 0}, "libxext": {"baseline": "1.3.4", "port-version": 0}, "libxfixes": {"baseline": "6.0.0", "port-version": 0}, "libxfont": {"baseline": "2.0.5", "port-version": 1}, "libxft": {"baseline": "2.3.4", "port-version": 0}, "libxi": {"baseline": "1.8", "port-version": 0}, "libxinerama": {"baseline": "1.1.4", "port-version": 0}, "libxkbcommon": {"baseline": "1.7.0", "port-version": 0}, "libxkbfile": {"baseline": "1.1.0", "port-version": 0}, "libxlsxwriter": {"baseline": "1.2.2", "port-version": 0}, "libxml2": {"baseline": "2.13.5", "port-version": 2}, "libxmlmm": {"baseline": "0.6.0", "port-version": 4}, "libxmlpp": {"baseline": "5.4.0", "port-version": 0}, "libxmp": {"baseline": "4.6.0", "port-version": 1}, "libxmu": {"baseline": "1.1.3", "port-version": 1}, "libxpm": {"baseline": "3.5.17", "port-version": 0}, "libxpresent": {"baseline": "1.0.0", "port-version": 0}, "libxrandr": {"baseline": "1.5.2", "port-version": 0}, "libxrender": {"baseline": "0.9.10", "port-version": 0}, "libxres": {"baseline": "1.2.1", "port-version": 0}, "libxscrnsaver": {"baseline": "1.2.3", "port-version": 0}, "libxslt": {"baseline": "1.1.42", "port-version": 2}, "libxt": {"baseline": "1.3.0", "port-version": 0}, "libxtst": {"baseline": "1.2.4", "port-version": 0}, "libxv": {"baseline": "1.0.11", "port-version": 0}, "libxxf86vm": {"baseline": "1.1.5", "port-version": 0}, "libyaml": {"baseline": "0.2.5", "port-version": 5}, "libyuv": {"baseline": "1896", "port-version": 1}, "libzen": {"baseline": "0.4.41", "port-version": 0}, "libzim": {"baseline": "9.0.0", "port-version": 0}, "libzip": {"baseline": "1.11.3", "port-version": 1}, "libzippp": {"baseline": "7.1-1.10.1", "port-version": 0}, "licensepp": {"baseline": "2020-11-24", "port-version": 0}, "lief": {"baseline": "0.16.1", "port-version": 0}, "lightgbm": {"baseline": "4.5.0", "port-version": 0}, "lightningscanner": {"baseline": "1.0.1", "port-version": 0}, "lilv": {"baseline": "0.24.26", "port-version": 0}, "linalg": {"baseline": "2.2", "port-version": 0}, "linmath": {"baseline": "2022-07-30", "port-version": 0}, "lionkor-commandline": {"baseline": "2.4.2", "port-version": 0}, "liquid-dsp": {"baseline": "1.7.0", "port-version": 0}, "litehtml": {"baseline": "0.9.0", "port-version": 0}, "live555": {"baseline": "2024-11-28", "port-version": 0}, "llama-cpp": {"baseline": "4743", "port-version": 0}, "llfio": {"baseline": "2025-01-13", "port-version": 0}, "llgi": {"baseline": "2023-12-19", "port-version": 1}, "llgl": {"baseline": "2023-03-05", "port-version": 0}, "llhttp": {"baseline": "9.2.1", "port-version": 0}, "llnl-units": {"baseline": "0.9.1", "port-version": 0}, "llvm": {"baseline": "18.1.6", "port-version": 4}, "lmdb": {"baseline": "0.9.33", "port-version": 0}, "lockpp": {"baseline": "3.0", "port-version": 0}, "lodepng": {"baseline": "2021-12-04", "port-version": 1}, "lodepng-c": {"baseline": "deprecated", "port-version": 0}, "log4cplus": {"baseline": "2.1.1", "port-version": 0}, "log4cpp-log4cpp": {"baseline": "1.1.4", "port-version": 0}, "log4cxx": {"baseline": "1.4.0", "port-version": 0}, "loguru": {"baseline": "2.1.0", "port-version": 6}, "lpeg": {"baseline": "1.1.0", "port-version": 1}, "ls-qpack": {"baseline": "2.6.0", "port-version": 0}, "ltla-aarand": {"baseline": "2023-03-19", "port-version": 0}, "ltla-cppirlba": {"baseline": "2023-09-20", "port-version": 0}, "ltla-cppkmeans": {"baseline": "2023-03-20", "port-version": 0}, "ltla-knncolle": {"baseline": "2023-05-09", "port-version": 0}, "ltla-powerit": {"baseline": "2023-03-19", "port-version": 0}, "ltla-umappp": {"baseline": "2023-05-11", "port-version": 0}, "lua": {"baseline": "5.4.7", "port-version": 0}, "lua-compat53": {"baseline": "0.10", "port-version": 0}, "luabridge": {"baseline": "2.8", "port-version": 0}, "luabridge3": {"baseline": "3.0-rc3", "port-version": 0}, "luafilesystem": {"baseline": "1.8.0", "port-version": 7}, "luajit": {"baseline": "2023-01-04", "port-version": 6}, "luasec": {"baseline": "1.3.2", "port-version": 2}, "luasocket": {"baseline": "3.1.0", "port-version": 1}, "luau": {"baseline": "0.668", "port-version": 0}, "luminoengine": {"baseline": "0.10.1", "port-version": 1}, "lunarg-vulkantools": {"baseline": "1.4.304.1", "port-version": 0}, "lunasvg": {"baseline": "3.2.1", "port-version": 1}, "luv": {"baseline": "1.44.2", "port-version": 1}, "lv2": {"baseline": "1.18.10", "port-version": 1}, "lwlog": {"baseline": "1.3.0", "port-version": 0}, "lz4": {"baseline": "1.10.0", "port-version": 0}, "lzav": {"baseline": "4.0", "port-version": 0}, "lzfse": {"baseline": "1.0", "port-version": 5}, "lzo": {"baseline": "2.10", "port-version": 9}, "lzokay": {"baseline": "2023-10-22", "port-version": 0}, "maddy": {"baseline": "1.4.0", "port-version": 0}, "magic-enum": {"baseline": "0.9.7", "port-version": 1}, "magic-get": {"baseline": "2019-09-02", "port-version": 3}, "magma": {"baseline": "2.8.0", "port-version": 1}, "magnum": {"baseline": "2020.06", "port-version": 19}, "magnum-extras": {"baseline": "2020.06", "port-version": 2}, "magnum-integration": {"baseline": "2020.06", "port-version": 3}, "magnum-plugins": {"baseline": "2020.06", "port-version": 13}, "mailio": {"baseline": "0.24.0", "port-version": 0}, "makeid": {"baseline": "1.0.3", "port-version": 0}, "manif": {"baseline": "0.0.5", "port-version": 0}, "manifold": {"baseline": "3.0.0", "port-version": 0}, "mapbox-geojson-cpp": {"baseline": "0.5.1", "port-version": 1}, "mapbox-geojson-vt-cpp": {"baseline": "6.6.5", "port-version": 0}, "mapbox-geometry": {"baseline": "2.0.3", "port-version": 0}, "mapbox-polylabel": {"baseline": "2.0.1", "port-version": 0}, "mapbox-variant": {"baseline": "1.2.0", "port-version": 2}, "mapbox-wagyu": {"baseline": "0.5.0", "port-version": 0}, "mapnik": {"baseline": "2024-04-18", "port-version": 0}, "marble": {"baseline": "24.08.2", "port-version": 1}, "marchingcubecpp": {"baseline": "2023-09-11", "port-version": 0}, "mariadb-connector-cpp": {"baseline": "1.1.5", "port-version": 0}, "marisa-trie": {"baseline": "0.2.6+20200926", "port-version": 2}, "marl": {"baseline": "2023-06-28", "port-version": 0}, "matchit": {"baseline": "1.0.1", "port-version": 0}, "materialx": {"baseline": "1.39.1", "port-version": 2}, "mathc": {"baseline": "2019-09-29", "port-version": 3}, "mathgl": {"baseline": "8.0.1", "port-version": 7}, "mathter": {"baseline": "2.0.0", "port-version": 0}, "matio": {"baseline": "1.5.28", "port-version": 0}, "matplotlib-cpp": {"baseline": "2020-08-27", "port-version": 2}, "matplotplusplus": {"baseline": "1.2.1", "port-version": 0}, "matroska": {"baseline": "1.7.1", "port-version": 2}, "mbedtls": {"baseline": "3.6.2", "port-version": 0}, "mchehab-zbar": {"baseline": "0.23.93", "port-version": 0}, "mcpp": {"baseline": "2.7.2.14", "port-version": 5}, "md4c": {"baseline": "0.5.2", "port-version": 0}, "mdl-sdk": {"baseline": "2024.1", "port-version": 0}, "mdns": {"baseline": "1.4.3", "port-version": 0}, "mdnsresponder": {"baseline": "1557.140.5.0.1", "port-version": 1}, "mdspan": {"baseline": "0.6.0", "port-version": 0}, "mecab": {"baseline": "2019-09-25", "port-version": 6}, "meekrosoft-fff": {"baseline": "1.1", "port-version": 0}, "memorymodule": {"baseline": "2019-12-31", "port-version": 3}, "mesa": {"baseline": "24.0.7", "port-version": 3}, "meschach": {"baseline": "1.2b", "port-version": 6}, "meshoptimizer": {"baseline": "0.23", "port-version": 0}, "metis": {"baseline": "2022-07-27", "port-version": 0}, "metrohash": {"baseline": "1.1.3", "port-version": 5}, "mfl": {"baseline": "0.0.1", "port-version": 3}, "mfx-dispatch": {"baseline": "1.35.1", "port-version": 5}, "mgnlibs": {"baseline": "2019-09-29", "port-version": 2}, "mhook": {"baseline": "2.5.1", "port-version": 3}, "michaelmiller-sec21": {"baseline": "1.0.1", "port-version": 0}, "micro-gl": {"baseline": "2024-06-18", "port-version": 0}, "microsoft-signalr": {"baseline": "0.1.0-alpha4", "port-version": 12}, "mikktspace": {"baseline": "2020-10-06", "port-version": 3}, "mimalloc": {"baseline": "2.2.3", "port-version": 0}, "mimicpp": {"baseline": "6", "port-version": 0}, "minc": {"baseline": "2.4.03", "port-version": 3}, "minhook": {"baseline": "1.3.3", "port-version": 5}, "miniaudio": {"baseline": "0.11.22", "port-version": 0}, "minifb": {"baseline": "2023-09-21", "port-version": 0}, "minimp3": {"baseline": "2021-11-30", "port-version": 0}, "minio-cpp": {"baseline": "0.3.0", "port-version": 0}, "miniply": {"baseline": "2022-09-15", "port-version": 1}, "minisat-master-keying": {"baseline": "2.3.6", "port-version": 1}, "minitrace": {"baseline": "2023-04-23", "port-version": 0}, "miniupnpc": {"baseline": "2.3.0", "port-version": 0}, "miniz": {"baseline": "3.0.2", "port-version": 1}, "minizip": {"baseline": "1.3.1", "port-version": 1}, "minizip-ng": {"baseline": "4.0.9", "port-version": 0}, "mio": {"baseline": "2023-03-03", "port-version": 0}, "mlpack": {"baseline": "4.6.0", "port-version": 0}, "mman": {"baseline": "git-f5ff813", "port-version": 5}, "mmloader": {"baseline": "1.0.1", "port-version": 3}, "mmx": {"baseline": "2022-03-27", "port-version": 0}, "mnn": {"baseline": "1.1.0", "port-version": 7}, "modern-cpp-kafka": {"baseline": "2024.07.03", "port-version": 0}, "modp-base64": {"baseline": "2020-09-26", "port-version": 2}, "mongo-c-driver": {"baseline": "1.30.2", "port-version": 0}, "mongo-cxx-driver": {"baseline": "4.0.0", "port-version": 0}, "mongoose": {"baseline": "7.17", "port-version": 0}, "monkeys-audio": {"baseline": "10.08", "port-version": 2}, "moos-core": {"baseline": "10.4.0", "port-version": 10}, "moos-essential": {"baseline": "10.0.1", "port-version": 5}, "moos-ui": {"baseline": "10.0.1", "port-version": 5}, "morphologica": {"baseline": "4.0", "port-version": 0}, "morton-nd": {"baseline": "4.0.0", "port-version": 3}, "mosquitto": {"baseline": "2.0.20", "port-version": 0}, "mozjpeg": {"baseline": "4.1.5", "port-version": 1}, "mp-units": {"baseline": "2.4.0", "port-version": 1}, "mp3lame": {"baseline": "3.100", "port-version": 15}, "mpark-patterns": {"baseline": "2019-10-03", "port-version": 0}, "mpark-variant": {"baseline": "1.4.0", "port-version": 3}, "mpc": {"baseline": "1.3.1", "port-version": 0}, "mpfr": {"baseline": "4.2.1", "port-version": 0}, "mpg123": {"baseline": "1.32.9", "port-version": 1}, "mpi": {"baseline": "1", "port-version": 5}, "mpir": {"baseline": "2022-03-02", "port-version": 3}, "mpmcqueue": {"baseline": "2021-12-01", "port-version": 0}, "mqtt-cpp": {"baseline": "13.2.1", "port-version": 0}, "ms-angle": {"baseline": "alias", "port-version": 1}, "ms-gdk": {"baseline": "2410.1.1897", "port-version": 0}, "ms-gdkx": {"baseline": "1.0.0", "port-version": 1}, "ms-gltf": {"baseline": "2024-09-05", "port-version": 0}, "ms-gsl": {"baseline": "4.2.0", "port-version": 0}, "ms-ifc-sdk": {"baseline": "0.43.1", "port-version": 0}, "msdfgen": {"baseline": "1.12", "port-version": 0}, "msgpack": {"baseline": "7.0.0", "port-version": 0}, "msgpack-c": {"baseline": "6.1.0", "port-version": 0}, "msgpack11": {"baseline": "0.0.10", "port-version": 4}, "msh3": {"baseline": "0.8.0", "port-version": 0}, "msinttypes": {"baseline": "2018-02-25", "port-version": 2}, "msix": {"baseline": "1.7", "port-version": 5}, "msmpi": {"baseline": "10.1.12498", "port-version": 4}, "msquic": {"baseline": "2.4.8", "port-version": 0}, "mstch": {"baseline": "1.0.2", "port-version": 5}, "mtlt": {"baseline": "1.0.0", "port-version": 0}, "mujoco": {"baseline": "3.3.0", "port-version": 0}, "mujs": {"baseline": "1.3.6", "port-version": 0}, "munit": {"baseline": "2019-04-06", "port-version": 5}, "muparser": {"baseline": "2.3.5", "port-version": 0}, "murmur3": {"baseline": "2015-05-02", "port-version": 0}, "murmurhash": {"baseline": "2016-01-09", "port-version": 7}, "mvfst": {"baseline": "2025.03.31.00", "port-version": 0}, "mygui": {"baseline": "3.4.3", "port-version": 3}, "mypaint-brushes": {"baseline": "1.3.1", "port-version": 0}, "mysql-connector-cpp": {"baseline": "9.1.0", "port-version": 4}, "mysvac-jsonlib": {"baseline": "2.0.0", "port-version": 0}, "nameof": {"baseline": "0.10.4", "port-version": 0}, "nana": {"baseline": "1.7.4", "port-version": 5}, "nano-signal-slot": {"baseline": "2.0.1", "port-version": 2}, "nanoarrow": {"baseline": "0.6.0", "port-version": 0}, "nanobench": {"baseline": "4.3.11", "port-version": 0}, "nanobind": {"baseline": "2.5.0", "port-version": 0}, "nanodbc": {"baseline": "2.13.0", "port-version": 8}, "nanoflann": {"baseline": "1.7.1", "port-version": 0}, "nanogui": {"baseline": "2019-09-23", "port-version": 5}, "nanojsonc": {"baseline": "1.1.0", "port-version": 0}, "nanomsg": {"baseline": "1.2.1", "port-version": 2}, "nanopb": {"baseline": "0.4.9", "port-version": 1}, "nanoprintf": {"baseline": "0.5.3", "port-version": 0}, "nanorange": {"baseline": "2020-07-06", "port-version": 0}, "nanort": {"baseline": "2019-08-20", "port-version": 3}, "nanosvg": {"baseline": "2023-12-29", "port-version": 0}, "nanovg": {"baseline": "2019-08-30", "port-version": 6}, "nativefiledialog-extended": {"baseline": "1.2.1", "port-version": 0}, "nayuki-qr-code-generator": {"baseline": "1.8.0", "port-version": 1}, "nccl": {"baseline": "2.4.6", "port-version": 2}, "ncnn": {"baseline": "20241226", "port-version": 0}, "ncurses": {"baseline": "6.4", "port-version": 2}, "ndis-driver-library": {"baseline": "1.2.0", "port-version": 0}, "neargye-semver": {"baseline": "0.3.1", "port-version": 0}, "ned14-internal-quickcpplib": {"baseline": "2023-11-22", "port-version": 0}, "neon2sse": {"baseline": "2024-11-24", "port-version": 0}, "netcdf-c": {"baseline": "4.8.1", "port-version": 5}, "netcdf-cxx4": {"baseline": "4.3.1", "port-version": 5}, "netcpp": {"baseline": "0.5.0", "port-version": 0}, "netgen": {"baseline": "6.2.2401", "port-version": 2}, "nethost": {"baseline": "8.0.3", "port-version": 0}, "nettle": {"baseline": "3.10", "port-version": 1}, "networkdirect-sdk": {"baseline": "2.0.1", "port-version": 4}, "nghttp2": {"baseline": "1.65.0", "port-version": 0}, "nghttp2-asio": {"baseline": "2022-08-11", "port-version": 2}, "nghttp3": {"baseline": "1.8.0", "port-version": 0}, "ngspice": {"baseline": "41", "port-version": 0}, "ngtcp2": {"baseline": "1.11.0", "port-version": 0}, "nifly": {"baseline": "1.0.0", "port-version": 1}, "nifticlib": {"baseline": "2022-07-04", "port-version": 0}, "nlohmann-fifo-map": {"baseline": "2018.05.07", "port-version": 3}, "nlohmann-json": {"baseline": "3.11.3", "port-version": 1}, "nlopt": {"baseline": "2.10.0", "port-version": 0}, "nmslib": {"baseline": "2.1.1", "port-version": 1}, "nng": {"baseline": "1.10.1", "port-version": 0}, "nngpp": {"baseline": "1.3.0", "port-version": 3}, "nnpack": {"baseline": "2021-02-21", "port-version": 3}, "node-addon-api": {"baseline": "8.3.1", "port-version": 0}, "node-api-headers": {"baseline": "1.5.0", "port-version": 0}, "nodesoup": {"baseline": "2023-06-12", "port-version": 0}, "nonius": {"baseline": "2019-04-20", "port-version": 4}, "nowide": {"baseline": "11.3.0", "port-version": 0}, "nrf-ble-driver": {"baseline": "4.1.4", "port-version": 2}, "nspr": {"baseline": "4.35", "port-version": 4}, "nss": {"baseline": "3.99", "port-version": 1}, "nsync": {"baseline": "1.29.2", "port-version": 1}, "nt-wrapper": {"baseline": "2019-08-10", "port-version": 3}, "ntf-core": {"baseline": "2.5.4", "port-version": 0}, "nu-book-zxing-cpp": {"baseline": "2.3.0", "port-version": 0}, "nuklear": {"baseline": "2022-05-12", "port-version": 0}, "numactl": {"baseline": "2.0.19", "port-version": 0}, "numcpp": {"baseline": "2.14.0", "port-version": 0}, "nuspell": {"baseline": "5.1.6", "port-version": 0}, "nvidia-cutlass": {"baseline": "3.3.0", "port-version": 0}, "nvtt": {"baseline": "2.1.2", "port-version": 8}, "nyan-lang": {"baseline": "0.3.1", "port-version": 0}, "oatpp": {"baseline": "1.3.0", "port-version": 2}, "oatpp-consul": {"baseline": "1.3.0", "port-version": 1}, "oatpp-curl": {"baseline": "1.3.0", "port-version": 1}, "oatpp-libressl": {"baseline": "1.3.0", "port-version": 1}, "oatpp-mbedtls": {"baseline": "1.3.0", "port-version": 1}, "oatpp-mongo": {"baseline": "1.3.0", "port-version": 1}, "oatpp-openssl": {"baseline": "1.3.0", "port-version": 0}, "oatpp-postgresql": {"baseline": "1.3.0", "port-version": 1}, "oatpp-sqlite": {"baseline": "1.3.0", "port-version": 2}, "oatpp-ssdp": {"baseline": "1.3.0", "port-version": 1}, "oatpp-swagger": {"baseline": "1.3.0", "port-version": 1}, "oatpp-websocket": {"baseline": "1.3.0", "port-version": 0}, "oatpp-zlib": {"baseline": "1.3.0", "port-version": 2}, "oboe": {"baseline": "1.8.0", "port-version": 0}, "observer-ptr-lite": {"baseline": "0.4.0", "port-version": 3}, "ocilib": {"baseline": "4.7.7", "port-version": 0}, "octave": {"baseline": "9.4.0", "port-version": 0}, "octomap": {"baseline": "1.10.0", "port-version": 0}, "ode": {"baseline": "0.16.6", "port-version": 0}, "offscale-libetcd-cpp": {"baseline": "2019-07-10", "port-version": 3}, "ogdf": {"baseline": "2022-06-30", "port-version": 1}, "ogre": {"baseline": "14.3.2", "port-version": 0}, "ogre-next": {"baseline": "2.3.3", "port-version": 3}, "ois": {"baseline": "1.5.1", "port-version": 1}, "omniorb": {"baseline": "4.3.0", "port-version": 4}, "ompl": {"baseline": "1.6.0", "port-version": 4}, "omplapp": {"baseline": "1.6.0", "port-version": 0}, "onednn": {"baseline": "3.7", "port-version": 0}, "oniguruma": {"baseline": "6.9.10", "port-version": 0}, "onnx": {"baseline": "1.17.0", "port-version": 1}, "onnx-optimizer": {"baseline": "0.3.19", "port-version": 1}, "onnxruntime-gpu": {"baseline": "1.19.2", "port-version": 0}, "oof": {"baseline": "2021-11-23", "port-version": 0}, "open-dis-cpp": {"baseline": "1.0.1", "port-version": 0}, "open62541": {"baseline": "1.3.15", "port-version": 2}, "open62541pp": {"baseline": "0.17.0", "port-version": 0}, "openal-soft": {"baseline": "1.24.3", "port-version": 0}, "openblas": {"baseline": "0.3.29", "port-version": 0}, "opencascade": {"baseline": "7.9.0", "port-version": 0}, "opencc": {"baseline": "1.1.9", "port-version": 0}, "opencensus-cpp": {"baseline": "2021-08-26", "port-version": 2}, "opencl": {"baseline": "2024.10.24", "port-version": 0}, "opencolorio": {"baseline": "2.2.1", "port-version": 3}, "opencsg": {"baseline": "1.8.1", "port-version": 0}, "openctm": {"baseline": "1.0.3", "port-version": 1}, "opencv": {"baseline": "4.11.0", "port-version": 0}, "opencv2": {"baseline": "2.4.13.7", "port-version": 24}, "opencv3": {"baseline": "3.4.20", "port-version": 0}, "opencv4": {"baseline": "4.11.0", "port-version": 0}, "opendnp3": {"baseline": "3.1.1", "port-version": 1}, "openexr": {"baseline": "3.3.2", "port-version": 0}, "openfbx": {"baseline": "2024-05-08", "port-version": 1}, "openfx": {"baseline": "1.4", "port-version": 0}, "opengl": {"baseline": "2022-12-04", "port-version": 3}, "opengl-registry": {"baseline": "2024-02-10", "port-version": 1}, "openh264": {"baseline": "2.6.0", "port-version": 1}, "openigtlink": {"baseline": "3.0", "port-version": 4}, "openimageio": {"baseline": "3.0.1.0", "port-version": 2}, "openjpeg": {"baseline": "2.5.3", "port-version": 0}, "openldap": {"baseline": "2.5.18", "port-version": 0}, "openmama": {"baseline": "6.3.2", "port-version": 3}, "openmesh": {"baseline": "10.0", "port-version": 0}, "openmpi": {"baseline": "4.1.7", "port-version": 0}, "openmvg": {"baseline": "2.1", "port-version": 1}, "openmvs": {"baseline": "2.1.0", "port-version": 6}, "openni2": {"baseline": "2.2.0.33", "port-version": 15}, "openscap": {"baseline": "1.4.0", "port-version": 1}, "openslide": {"baseline": "3.4.1", "port-version": 4}, "openssl": {"baseline": "3.5.0", "port-version": 0}, "opensubdiv": {"baseline": "3.5.0", "port-version": 3}, "opentelemetry-cpp": {"baseline": "1.20.0", "port-version": 0}, "opentelemetry-cpp-contrib-version": {"baseline": "2025-04-02", "port-version": 0}, "opentracing": {"baseline": "1.6.0", "port-version": 4}, "openturns": {"baseline": "1.24", "port-version": 0}, "openvdb": {"baseline": "12.0.0", "port-version": 1}, "openvino": {"baseline": "2025.0.0", "port-version": 0}, "openvpn3": {"baseline": "3.10", "port-version": 1}, "openvr": {"baseline": "2.5.1", "port-version": 0}, "openxr-loader": {"baseline": "1.1.45", "port-version": 0}, "optimus-cpp": {"baseline": "0.3.0", "port-version": 0}, "optional-bare": {"baseline": "1.1.0", "port-version": 3}, "optional-lite": {"baseline": "3.6.0", "port-version": 0}, "opus": {"baseline": "1.5.2", "port-version": 0}, "opusfile": {"baseline": "0.12+20221121", "port-version": 1}, "orange-math": {"baseline": "1.0.1", "port-version": 0}, "orc": {"baseline": "2.1.0", "port-version": 0}, "orocos-kdl": {"baseline": "1.5.1", "port-version": 0}, "oscpack": {"baseline": "1.1.0", "port-version": 0}, "osg": {"baseline": "3.6.5", "port-version": 26}, "osg-qt": {"baseline": "Qt5", "port-version": 3}, "osgearth": {"baseline": "3.7.2", "port-version": 1}, "osmanip": {"baseline": "4.6.1", "port-version": 0}, "otl": {"baseline": "4.0.481", "port-version": 0}, "outcome": {"baseline": "2.2.9", "port-version": 0}, "p-ranav-csv": {"baseline": "2019-07-11", "port-version": 3}, "p-ranav-csv2": {"baseline": "2020-12-14", "port-version": 4}, "p-ranav-glob": {"baseline": "0.0.1", "port-version": 0}, "pagmo2": {"baseline": "2.19.1", "port-version": 0}, "paho-mqtt": {"baseline": "1.3.14", "port-version": 0}, "paho-mqttpp3": {"baseline": "1.5.2", "port-version": 0}, "palsigslot": {"baseline": "1.2.2", "port-version": 0}, "pango": {"baseline": "1.56.1", "port-version": 1}, "pangolin": {"baseline": "0.9.3", "port-version": 2}, "pangomm": {"baseline": "2.56.1", "port-version": 1}, "parallel-hashmap": {"baseline": "2.0.0", "port-version": 0}, "parallelstl": {"baseline": "20200330", "port-version": 3}, "paraview": {"baseline": "5.12.1", "port-version": 4}, "parmetis": {"baseline": "2022-07-27", "port-version": 0}, "parquet": {"baseline": "0", "port-version": 2}, "parsi": {"baseline": "0.1.0", "port-version": 0}, "parson": {"baseline": "2023-10-31", "port-version": 0}, "pbc": {"baseline": "0.5.14", "port-version": 9}, "pcapplusplus": {"baseline": "24.9", "port-version": 0}, "pcg": {"baseline": "2022-04-09", "port-version": 0}, "pciids": {"baseline": "2023-04-11", "port-version": 0}, "pcl": {"baseline": "1.15.0", "port-version": 2}, "pcre": {"baseline": "8.45", "port-version": 7}, "pcre2": {"baseline": "10.45", "port-version": 0}, "pdal": {"baseline": "2.8.4", "port-version": 0}, "pdal-c": {"baseline": "2.2.0", "port-version": 0}, "pdal-dimbuilder": {"baseline": "2.8.4", "port-version": 0}, "pdcurses": {"baseline": "3.9", "port-version": 7}, "pdqsort": {"baseline": "2019-07-30", "port-version": 2}, "pe-parse": {"baseline": "2.1.1", "port-version": 0}, "pegtl": {"baseline": "3.2.8", "port-version": 0}, "pegtl-2": {"baseline": "2.8.3", "port-version": 3}, "perfetto": {"baseline": "49.0", "port-version": 0}, "pffft": {"baseline": "2021-10-09", "port-version": 1}, "pfring": {"baseline": "8.8.0", "port-version": 0}, "pfultz2-linq": {"baseline": "2019-05-14", "port-version": 3}, "phnt": {"baseline": "2025-02-05", "port-version": 0}, "physac": {"baseline": "1.1", "port-version": 0}, "physfs": {"baseline": "3.2.0", "port-version": 1}, "physx": {"baseline": "5.5.0", "port-version": 0}, "picojson": {"baseline": "1.3.0", "port-version": 3}, "picosha2": {"baseline": "2018-07-30", "port-version": 2}, "piex": {"baseline": "2019-07-11", "port-version": 2}, "pipewire": {"baseline": "1.2.7", "port-version": 0}, "pistache": {"baseline": "2021-03-31", "port-version": 3}, "pixel": {"baseline": "2022-03-15", "port-version": 1}, "pixman": {"baseline": "0.44.2", "port-version": 0}, "pkgconf": {"baseline": "2.4.3", "port-version": 0}, "plasma-wayland-protocols": {"baseline": "1.14.0", "port-version": 0}, "platform-folders": {"baseline": "4.2.0", "port-version": 1}, "plf-colony": {"baseline": "7.41", "port-version": 0}, "plf-hive": {"baseline": "2021-12-11", "port-version": 0}, "plf-list": {"baseline": "2019-08-10", "port-version": 2}, "plf-nanotimer": {"baseline": "2019-08-10", "port-version": 2}, "plf-queue": {"baseline": "2.2", "port-version": 0}, "plf-stack": {"baseline": "2019-08-10", "port-version": 2}, "plib": {"baseline": "1.8.5", "port-version": 8}, "plibsys": {"baseline": "0.0.5", "port-version": 0}, "plog": {"baseline": "1.1.10", "port-version": 0}, "plplot": {"baseline": "5.15.0", "port-version": 5}, "plustache": {"baseline": "0.4.0", "port-version": 5}, "plutosvg": {"baseline": "0.0.6", "port-version": 1}, "plutovg": {"baseline": "1.0.0", "port-version": 0}, "pmdk": {"baseline": "1.12.0", "port-version": 2}, "pmp-library": {"baseline": "3.0.0", "port-version": 0}, "pngpp": {"baseline": "0.2.10", "port-version": 2}, "pngwriter": {"baseline": "0.7.0", "port-version": 5}, "pocketfft": {"baseline": "2023-09-25", "port-version": 0}, "pocketpy": {"baseline": "1.4.6", "port-version": 1}, "poco": {"baseline": "1.14.1", "port-version": 1}, "podofo": {"baseline": "0.10.4", "port-version": 0}, "poissonrecon": {"baseline": "2021-09-26", "port-version": 0}, "polyclipping": {"baseline": "6.4.2", "port-version": 13}, "polyhook2": {"baseline": "2024-06-03", "port-version": 0}, "polymorphic-value": {"baseline": "1.3.0", "port-version": 3}, "ponder": {"baseline": "3.0.0", "port-version": 5}, "poolstl": {"baseline": "0.3.5", "port-version": 0}, "poppler": {"baseline": "24.3.0", "port-version": 1}, "popsift": {"baseline": "0.9", "port-version": 5}, "portable-file-dialogs": {"baseline": "0.1.0", "port-version": 0}, "portable-snippets": {"baseline": "2019-09-20", "port-version": 4}, "portaudio": {"baseline": "19.7", "port-version": 6}, "portmidi": {"baseline": "2.0.4", "port-version": 3}, "portsmf": {"baseline": "239", "port-version": 0}, "poselib": {"baseline": "2.0.4", "port-version": 0}, "ppconsul": {"baseline": "0.5", "port-version": 5}, "ppmagic": {"baseline": "2020-07-03", "port-version": 2}, "pprint": {"baseline": "2019-07-19", "port-version": 3}, "pqp": {"baseline": "1.3", "port-version": 8}, "pravila00-enum-string": {"baseline": "2023-10-16", "port-version": 0}, "pravila00-enumflag": {"baseline": "2024-04-12", "port-version": 0}, "pravila00-make-vector": {"baseline": "2023-04-10", "port-version": 0}, "presentmon": {"baseline": "2.3.0", "port-version": 0}, "proj": {"baseline": "9.6.0", "port-version": 0}, "projectm": {"baseline": "4.1.4", "port-version": 0}, "projectm-eval": {"baseline": "1.0.1", "port-version": 0}, "prometheus-cpp": {"baseline": "1.3.0", "port-version": 0}, "promise-cpp": {"baseline": "2.1.2", "port-version": 0}, "protobuf": {"baseline": "5.29.3", "port-version": 0}, "protobuf-c": {"baseline": "1.5.2", "port-version": 0}, "protopuf": {"baseline": "3.0.0", "port-version": 0}, "protozero": {"baseline": "1.8.0", "port-version": 0}, "proxsuite": {"baseline": "0.6.7", "port-version": 0}, "proxy": {"baseline": "3.3.0", "port-version": 0}, "proxygen": {"baseline": "2025.03.31.00", "port-version": 1}, "psimd": {"baseline": "2021-02-21", "port-version": 2}, "ptc-print": {"baseline": "1.4.1", "port-version": 1}, "ptex": {"baseline": "2.4.3", "port-version": 1}, "pthread": {"baseline": "3.0.0", "port-version": 2}, "pthread-stubs": {"baseline": "0.4", "port-version": 1}, "pthreadpool": {"baseline": "2020-04-10", "port-version": 3}, "pthreads": {"baseline": "3.0.0", "port-version": 14}, "ptyqt": {"baseline": "0.7.1", "port-version": 0}, "pugixml": {"baseline": "1.15", "port-version": 0}, "pulsar-client-cpp": {"baseline": "3.7.0", "port-version": 0}, "pulseaudio": {"baseline": "17.0", "port-version": 2}, "pulzed-mini": {"baseline": "0.9.18", "port-version": 0}, "pybind11": {"baseline": "2.13.6", "port-version": 0}, "pystring": {"baseline": "1.1.4", "port-version": 0}, "python2": {"baseline": "2.7.18", "port-version": 7}, "python3": {"baseline": "3.12.9", "port-version": 0}, "qca": {"baseline": "2.3.7", "port-version": 3}, "qcoro": {"baseline": "0.11.0", "port-version": 0}, "qcustomplot": {"baseline": "2.1.1", "port-version": 1}, "qhttpengine": {"baseline": "1.0.2", "port-version": 1}, "qhull": {"baseline": "8.0.2", "port-version": 5}, "qlementine": {"baseline": "1.2.2", "port-version": 0}, "qlementine-icons": {"baseline": "1.8.0", "port-version": 0}, "qmex": {"baseline": "2024-10-31", "port-version": 0}, "qnnpack": {"baseline": "2021-02-26", "port-version": 3}, "qoi": {"baseline": "2023-08-10", "port-version": 0}, "qoixx": {"baseline": "0.1.7", "port-version": 0}, "qpid-proton": {"baseline": "0.38.0", "port-version": 2}, "qscintilla": {"baseline": "2.14.1", "port-version": 1}, "qt": {"baseline": "6.8.3", "port-version": 0}, "qt-advanced-docking-system": {"baseline": "4.4.0", "port-version": 0}, "qt3d": {"baseline": "6.8.3", "port-version": 0}, "qt5": {"baseline": "5.15.16", "port-version": 1}, "qt5-3d": {"baseline": "5.15.16", "port-version": 0}, "qt5-activeqt": {"baseline": "5.15.16", "port-version": 0}, "qt5-androidextras": {"baseline": "5.15.16", "port-version": 0}, "qt5-base": {"baseline": "5.15.16", "port-version": 1}, "qt5-canvas3d": {"baseline": "0", "port-version": 3}, "qt5-charts": {"baseline": "5.15.16", "port-version": 0}, "qt5-connectivity": {"baseline": "5.15.16", "port-version": 0}, "qt5-datavis3d": {"baseline": "5.15.16", "port-version": 0}, "qt5-declarative": {"baseline": "5.15.16", "port-version": 0}, "qt5-doc": {"baseline": "5.15.16", "port-version": 0}, "qt5-gamepad": {"baseline": "5.15.16", "port-version": 0}, "qt5-graphicaleffects": {"baseline": "5.15.16", "port-version": 0}, "qt5-imageformats": {"baseline": "5.15.16", "port-version": 0}, "qt5-location": {"baseline": "5.15.16", "port-version": 0}, "qt5-macextras": {"baseline": "5.15.16", "port-version": 0}, "qt5-modularscripts": {"baseline": "deprecated", "port-version": 1}, "qt5-mqtt": {"baseline": "5.15.16", "port-version": 0}, "qt5-multimedia": {"baseline": "5.15.16", "port-version": 0}, "qt5-networkauth": {"baseline": "5.15.16", "port-version": 0}, "qt5-purchasing": {"baseline": "5.15.16", "port-version": 0}, "qt5-quickcontrols": {"baseline": "5.15.16", "port-version": 0}, "qt5-quickcontrols2": {"baseline": "5.15.16", "port-version": 0}, "qt5-remoteobjects": {"baseline": "5.15.16", "port-version": 0}, "qt5-script": {"baseline": "5.15.16", "port-version": 0}, "qt5-scxml": {"baseline": "5.15.16", "port-version": 0}, "qt5-sensors": {"baseline": "5.15.16", "port-version": 0}, "qt5-serialbus": {"baseline": "5.15.16", "port-version": 0}, "qt5-serialport": {"baseline": "5.15.16", "port-version": 0}, "qt5-speech": {"baseline": "5.15.16", "port-version": 0}, "qt5-svg": {"baseline": "5.15.16", "port-version": 0}, "qt5-tools": {"baseline": "5.15.16", "port-version": 0}, "qt5-translations": {"baseline": "5.15.16", "port-version": 0}, "qt5-virtualkeyboard": {"baseline": "5.15.16", "port-version": 0}, "qt5-wayland": {"baseline": "5.15.16", "port-version": 0}, "qt5-webchannel": {"baseline": "5.15.16", "port-version": 0}, "qt5-webengine": {"baseline": "5.15.16", "port-version": 3}, "qt5-webglplugin": {"baseline": "5.15.16", "port-version": 0}, "qt5-websockets": {"baseline": "5.15.16", "port-version": 0}, "qt5-webview": {"baseline": "5.15.16", "port-version": 0}, "qt5-winextras": {"baseline": "5.15.16", "port-version": 0}, "qt5-x11extras": {"baseline": "5.15.16", "port-version": 0}, "qt5-xmlpatterns": {"baseline": "5.15.16", "port-version": 0}, "qt5compat": {"baseline": "6.8.3", "port-version": 0}, "qtactiveqt": {"baseline": "6.8.3", "port-version": 0}, "qtapplicationmanager": {"baseline": "6.8.3", "port-version": 0}, "qtbase": {"baseline": "6.8.3", "port-version": 0}, "qtcharts": {"baseline": "6.8.3", "port-version": 0}, "qtcoap": {"baseline": "6.8.3", "port-version": 0}, "qtconnectivity": {"baseline": "6.8.3", "port-version": 0}, "qtdatavis3d": {"baseline": "6.8.3", "port-version": 0}, "qtdeclarative": {"baseline": "6.8.3", "port-version": 0}, "qtdeviceutilities": {"baseline": "6.8.3", "port-version": 0}, "qtdoc": {"baseline": "6.8.3", "port-version": 0}, "qtgraphs": {"baseline": "6.8.3", "port-version": 0}, "qtgrpc": {"baseline": "6.8.3", "port-version": 0}, "qthttpserver": {"baseline": "6.8.3", "port-version": 0}, "qtimageformats": {"baseline": "6.8.3", "port-version": 0}, "qtinterfaceframework": {"baseline": "6.8.3", "port-version": 0}, "qtkeychain": {"baseline": "0.14.3", "port-version": 0}, "qtkeychain-qt6": {"baseline": "0.14.3", "port-version": 0}, "qtlanguageserver": {"baseline": "6.8.3", "port-version": 0}, "qtlocation": {"baseline": "6.8.3", "port-version": 0}, "qtlottie": {"baseline": "6.8.3", "port-version": 0}, "qtmqtt": {"baseline": "6.8.3", "port-version": 0}, "qtmultimedia": {"baseline": "6.8.3", "port-version": 0}, "qtnetworkauth": {"baseline": "6.8.3", "port-version": 0}, "qtopcua": {"baseline": "6.8.3", "port-version": 0}, "qtpositioning": {"baseline": "6.8.3", "port-version": 0}, "qtquick3d": {"baseline": "6.8.3", "port-version": 0}, "qtquick3dphysics": {"baseline": "6.8.3", "port-version": 0}, "qtquickcontrols2": {"baseline": "deprecated", "port-version": 1}, "qtquickeffectmaker": {"baseline": "6.8.3", "port-version": 0}, "qtquicktimeline": {"baseline": "6.8.3", "port-version": 0}, "qtremoteobjects": {"baseline": "6.8.3", "port-version": 0}, "qtscxml": {"baseline": "6.8.3", "port-version": 0}, "qtsensors": {"baseline": "6.8.3", "port-version": 0}, "qtserialbus": {"baseline": "6.8.3", "port-version": 0}, "qtserialport": {"baseline": "6.8.3", "port-version": 0}, "qtshadertools": {"baseline": "6.8.3", "port-version": 0}, "qtspeech": {"baseline": "6.8.3", "port-version": 0}, "qtsvg": {"baseline": "6.8.3", "port-version": 0}, "qttools": {"baseline": "6.8.3", "port-version": 0}, "qttranslations": {"baseline": "6.8.3", "port-version": 0}, "qtvirtualkeyboard": {"baseline": "6.8.3", "port-version": 0}, "qtwayland": {"baseline": "6.8.3", "port-version": 0}, "qtwebchannel": {"baseline": "6.8.3", "port-version": 0}, "qtwebengine": {"baseline": "6.8.3", "port-version": 0}, "qtwebsockets": {"baseline": "6.8.3", "port-version": 0}, "qtwebview": {"baseline": "6.8.3", "port-version": 0}, "quadtree": {"baseline": "2022-04-24", "port-version": 0}, "quantlib": {"baseline": "1.37", "port-version": 0}, "quaternions": {"baseline": "1.0.0", "port-version": 2}, "quazip": {"baseline": "1.4", "port-version": 1}, "quickfast": {"baseline": "1.5", "port-version": 5}, "quickfix": {"baseline": "1.15.1", "port-version": 9}, "quill": {"baseline": "9.0.0", "port-version": 0}, "quirc": {"baseline": "1.2", "port-version": 0}, "qwt": {"baseline": "6.3.0", "port-version": 0}, "qwtw": {"baseline": "3.1.0", "port-version": 5}, "rabit": {"baseline": "0.1", "port-version": 5}, "ragel": {"baseline": "6.10", "port-version": 6}, "random123": {"baseline": "1.14.0", "port-version": 0}, "randomstr": {"baseline": "2022-02-03", "port-version": 0}, "rang": {"baseline": "3.2", "port-version": 0}, "range-v3": {"baseline": "0.12.0", "port-version": 4}, "range-v3-vs2015": {"baseline": "20151130-vcpkg5", "port-version": 4}, "rapidcheck": {"baseline": "2023-12-14", "port-version": 0}, "rapidcsv": {"baseline": "8.85", "port-version": 0}, "rapidfuzz": {"baseline": "3.3.2", "port-version": 0}, "rapidhash": {"baseline": "1.0", "port-version": 0}, "rapidjson": {"baseline": "2025-02-26", "port-version": 0}, "rapidxml": {"baseline": "1.13", "port-version": 7}, "rapidxml-ns": {"baseline": "1.13.2", "port-version": 2}, "rappture": {"baseline": "1.9", "port-version": 4}, "raygui": {"baseline": "4.0", "port-version": 0}, "raylib": {"baseline": "5.5", "port-version": 0}, "rbdl": {"baseline": "3.3.0", "port-version": 7}, "rbdl-orb": {"baseline": "3.2.0", "port-version": 2}, "re2": {"baseline": "2024-07-02", "port-version": 0}, "reactiveplusplus": {"baseline": "2.1.1", "port-version": 0}, "readerwriterqueue": {"baseline": "1.0.6", "port-version": 0}, "readline": {"baseline": "0", "port-version": 5}, "readline-osx": {"baseline": "2020-01-04", "port-version": 0}, "readline-unix": {"baseline": "8.2", "port-version": 1}, "readline-win32": {"baseline": "5.0", "port-version": 9}, "readosm": {"baseline": "1.1.0a", "port-version": 4}, "realm-core": {"baseline": "14.10.4", "port-version": 0}, "realsense2": {"baseline": "2.56.2", "port-version": 2}, "recast": {"baseline": "deprecated", "port-version": 0}, "recastnavigation": {"baseline": "1.6.0", "port-version": 1}, "recycle": {"baseline": "7.0.0", "port-version": 0}, "red0124-ssp": {"baseline": "1.8.0", "port-version": 0}, "redis-plus-plus": {"baseline": "1.3.14", "port-version": 0}, "refl-cpp": {"baseline": "0.12.4", "port-version": 0}, "reflectcpp": {"baseline": "0.18.0", "port-version": 0}, "refprop-headers": {"baseline": "2022-12-07", "port-version": 0}, "rendergraph": {"baseline": "1.4.1", "port-version": 0}, "replxx": {"baseline": "0.0.4", "port-version": 1}, "reproc": {"baseline": "14.2.5", "port-version": 0}, "rerun-sdk": {"baseline": "0.22.1", "port-version": 0}, "rest-rpc": {"baseline": "0.12", "port-version": 1}, "restbed": {"baseline": "4.8", "port-version": 3}, "restc-cpp": {"baseline": "1.0.0", "port-version": 1}, "restclient-cpp": {"baseline": "2024-01-09", "port-version": 0}, "restinio": {"baseline": "0.7.5", "port-version": 0}, "resultlib": {"baseline": "1.0.0", "port-version": 0}, "rexo": {"baseline": "0.2.2", "port-version": 0}, "rhash": {"baseline": "1.4.5", "port-version": 0}, "rhasheq": {"baseline": "2023-06-17", "port-version": 0}, "riffcpp": {"baseline": "2.2.4", "port-version": 3}, "ring-span-lite": {"baseline": "0.7.0", "port-version": 0}, "rioki-glow": {"baseline": "0.2.1", "port-version": 0}, "ripper37-libbase": {"baseline": "1.0.1", "port-version": 0}, "rivers": {"baseline": "2022-05-16", "port-version": 0}, "rkcommon": {"baseline": "1.14.2", "port-version": 0}, "rlottie": {"baseline": "2024-08-26", "port-version": 0}, "rmlui": {"baseline": "6.0", "port-version": 1}, "rmqcpp": {"baseline": "1.0.0", "port-version": 2}, "roaring": {"baseline": "4.3.1", "port-version": 0}, "robin-hood-hashing": {"baseline": "3.11.5", "port-version": 0}, "robin-map": {"baseline": "1.4.0", "port-version": 0}, "robotraconteur": {"baseline": "1.2.4", "port-version": 3}, "robotraconteur-companion": {"baseline": "0.4.1", "port-version": 0}, "rocksdb": {"baseline": "9.10.0", "port-version": 1}, "rpclib": {"baseline": "2.3.0", "port-version": 2}, "rply": {"baseline": "1.1.4", "port-version": 4}, "rsasynccpp": {"baseline": "0.0.7", "port-version": 3}, "rsig": {"baseline": "0.1.1", "port-version": 0}, "rsm-binary-io": {"baseline": "2.0.6", "port-version": 0}, "rsm-bsa": {"baseline": "4.1.0", "port-version": 1}, "rsm-mmio": {"baseline": "2.0.0", "port-version": 0}, "rsocket": {"baseline": "2021.08.30.00", "port-version": 5}, "rtabmap": {"baseline": "0.21.4.1", "port-version": 6}, "rtabmap-res-tool": {"baseline": "0.21.4.1", "port-version": 0}, "rtaudio": {"baseline": "6.0.1", "port-version": 1}, "rtlsdr": {"baseline": "2.0.2", "port-version": 0}, "rtmfp-cpp": {"baseline": "1.5.1", "port-version": 0}, "rtmidi": {"baseline": "6.0.0", "port-version": 0}, "rttr": {"baseline": "0.9.6+20210811", "port-version": 1}, "ruapu": {"baseline": "0.1.0", "port-version": 0}, "rubberband": {"baseline": "4.0.0", "port-version": 0}, "ruckig": {"baseline": "0.14.0", "port-version": 0}, "rxcpp": {"baseline": "4.1.1", "port-version": 1}, "rxqt": {"baseline": "d0b1535", "port-version": 1}, "rxspencer": {"baseline": "3.9.0", "port-version": 2}, "ryml": {"baseline": "0.8.0", "port-version": 0}, "ryu": {"baseline": "2.0", "port-version": 10}, "s2geometry": {"baseline": "0.11.1", "port-version": 0}, "s2n": {"baseline": "1.5.9", "port-version": 1}, "safeint": {"baseline": "3.0.28", "port-version": 0}, "safetyhook": {"baseline": "0.6.5", "port-version": 0}, "sail": {"baseline": "0.9.8", "port-version": 0}, "sajson": {"baseline": "2018-09-21", "port-version": 3}, "salome-configuration": {"baseline": "9.10.0", "port-version": 1}, "salome-med-fichier": {"baseline": "4.1.1", "port-version": 3}, "salome-medcoupling": {"baseline": "9.10.0", "port-version": 1}, "sassc": {"baseline": "3.6.2", "port-version": 1}, "saucer": {"baseline": "2.3.0", "port-version": 1}, "sbp": {"baseline": "6.2.1", "port-version": 0}, "scenepic": {"baseline": "1.1.1", "port-version": 0}, "scintilla": {"baseline": "5.5.1", "port-version": 0}, "sciplot": {"baseline": "0.3.1", "port-version": 1}, "sciter": {"baseline": "0", "port-version": 1}, "sciter-js": {"baseline": "*******", "port-version": 0}, "scnlib": {"baseline": "4.0.1", "port-version": 1}, "scope-guard": {"baseline": "1.1.0", "port-version": 0}, "scotch": {"baseline": "7.0.5", "port-version": 1}, "scottt-debugbreak": {"baseline": "1.0", "port-version": 0}, "scylla-wrapper": {"baseline": "2018-08-26-16e6f435", "port-version": 3}, "sdbus-cpp": {"baseline": "2.1.0", "port-version": 0}, "sdflib": {"baseline": "2025-01-25", "port-version": 0}, "sdformat": {"baseline": "15.1.1", "port-version": 1}, "sdformat13": {"baseline": "13.6.0", "port-version": 1}, "sdl1": {"baseline": "1.2.15", "port-version": 21}, "sdl1-mixer": {"baseline": "2023-03-25", "port-version": 2}, "sdl1-net": {"baseline": "1.2.8", "port-version": 6}, "sdl2": {"baseline": "2.32.4", "port-version": 0}, "sdl2-gfx": {"baseline": "1.0.4", "port-version": 11}, "sdl2-image": {"baseline": "2.8.8", "port-version": 1}, "sdl2-mixer": {"baseline": "2.8.1", "port-version": 0}, "sdl2-mixer-ext": {"baseline": "2.6.0", "port-version": 1}, "sdl2-net": {"baseline": "2.2.0", "port-version": 3}, "sdl2-ttf": {"baseline": "2.24.0", "port-version": 0}, "sdl2pp": {"baseline": "0.16.1", "port-version": 11}, "sdl3": {"baseline": "3.2.10", "port-version": 0}, "sdl3-image": {"baseline": "3.2.4", "port-version": 0}, "sdl3-ttf": {"baseline": "3.1.0", "port-version": 0}, "seacas": {"baseline": "2022-11-22", "port-version": 8}, "seal": {"baseline": "4.1.2", "port-version": 0}, "seasocks": {"baseline": "1.4.6", "port-version": 0}, "secp256k1": {"baseline": "2022-07-11", "port-version": 1}, "selene": {"baseline": "0.3.1", "port-version": 8}, "sentencepiece": {"baseline": "0.2.0", "port-version": 1}, "sentry-native": {"baseline": "0.8.3", "port-version": 0}, "septag-dmon": {"baseline": "2022-02-08", "port-version": 0}, "septag-sx": {"baseline": "2019-05-07", "port-version": 5}, "seqan": {"baseline": "2.4.0", "port-version": 3}, "serd": {"baseline": "0.32.4", "port-version": 0}, "serdepp": {"baseline": "0.1.4.1", "port-version": 0}, "serf": {"baseline": "1.3.10", "port-version": 0}, "sese": {"baseline": "2.3.0", "port-version": 3}, "sf2cute": {"baseline": "0.2.0", "port-version": 4}, "sfcgal": {"baseline": "2.0.0", "port-version": 0}, "sfgui": {"baseline": "1.0.0", "port-version": 0}, "sfml": {"baseline": "3.0.0", "port-version": 1}, "sfsexp": {"baseline": "1.4.1", "port-version": 0}, "shader-slang": {"baseline": "2025.5", "port-version": 0}, "shaderc": {"baseline": "2023.8", "port-version": 0}, "shaderwriter": {"baseline": "2.7.0", "port-version": 0}, "shapelib": {"baseline": "1.6.1", "port-version": 0}, "shared-mime-info": {"baseline": "2.4", "port-version": 0}, "shiftmedia-libgcrypt": {"baseline": "1.10.3-1", "port-version": 1}, "shiftmedia-libgnutls": {"baseline": "3.8.7", "port-version": 2}, "shiftmedia-libgpg-error": {"baseline": "1.45", "port-version": 1}, "shogun": {"baseline": "2023-12-19", "port-version": 1}, "si": {"baseline": "2.5.1", "port-version": 0}, "sigmatch": {"baseline": "0.2.0", "port-version": 0}, "signalrclient": {"baseline": "1.0.0-beta1-9", "port-version": 6}, "sigslot": {"baseline": "1.0.0", "port-version": 5}, "simage": {"baseline": "1.8.3", "port-version": 0}, "simbody": {"baseline": "2023-01-10", "port-version": 1}, "simd": {"baseline": "6.1.148", "port-version": 0}, "simde": {"baseline": "0.8.2", "port-version": 0}, "simdjson": {"baseline": "3.12.3", "port-version": 0}, "simdutf": {"baseline": "6.4.2", "port-version": 0}, "simonbrunel-qtpromise": {"baseline": "0.7.0", "port-version": 0}, "simple-fft": {"baseline": "2020-06-14", "port-version": 2}, "simpleble": {"baseline": "2023-07-29", "port-version": 1}, "simpleini": {"baseline": "4.22", "port-version": 0}, "simsimd": {"baseline": "6.0.0", "port-version": 0}, "sjpeg": {"baseline": "2021-10-31", "port-version": 0}, "skcrypter": {"baseline": "2021-12-03", "port-version": 0}, "skia": {"baseline": "134", "port-version": 2}, "skyr-url": {"baseline": "1.13.0", "port-version": 2}, "sleef": {"baseline": "3.8", "port-version": 1}, "sleepy-discord": {"baseline": "2025-02-08", "port-version": 0}, "slikenet": {"baseline": "2021-06-07", "port-version": 3}, "sltbench": {"baseline": "2.4.0", "port-version": 3}, "small-gicp": {"baseline": "1.0.0", "port-version": 0}, "smf": {"baseline": "0.2.0", "port-version": 0}, "smpeg2": {"baseline": "2.0.0", "port-version": 11}, "snap7": {"baseline": "1.4.2", "port-version": 2}, "snappy": {"baseline": "1.2.2", "port-version": 1}, "snitch": {"baseline": "1.2.5", "port-version": 0}, "snowhouse": {"baseline": "5.0.0", "port-version": 2}, "so5extra": {"baseline": "1.6.2", "port-version": 0}, "soapysdr": {"baseline": "0.8.1", "port-version": 0}, "sobjectizer": {"baseline": "5.8.4", "port-version": 0}, "soci": {"baseline": "4.0.3", "port-version": 3}, "socket-io-client": {"baseline": "2023-11-11", "port-version": 0}, "sockpp": {"baseline": "1.0.0", "port-version": 2}, "soem": {"baseline": "2023-06-09", "port-version": 2}, "soil": {"baseline": "2021-04-22", "port-version": 2}, "soil2": {"baseline": "1.3.0", "port-version": 1}, "sokol": {"baseline": "2023-10-07", "port-version": 0}, "sol2": {"baseline": "3.5.0", "port-version": 0}, "solid3": {"baseline": "3.5.8", "port-version": 2}, "sophus": {"baseline": "1.24.6-r1", "port-version": 0}, "soqt": {"baseline": "1.6.0", "port-version": 4}, "sord": {"baseline": "0.16.18", "port-version": 0}, "soundtouch": {"baseline": "2.3.3", "port-version": 0}, "soxr": {"baseline": "0.1.3", "port-version": 8}, "spaceland": {"baseline": "7.8.2", "port-version": 10}, "span-lite": {"baseline": "0.11.0", "port-version": 0}, "sparsehash": {"baseline": "2.0.4", "port-version": 2}, "sparsepp": {"baseline": "1.22", "port-version": 4}, "spatial-hash": {"baseline": "1.0.0", "port-version": 0}, "spatialite-tools": {"baseline": "5.1.0-a", "port-version": 0}, "spdk": {"baseline": "24.01", "port-version": 1}, "spdk-dpdk": {"baseline": "2018-11-24", "port-version": 3}, "spdk-ipsec": {"baseline": "2018-07-11", "port-version": 3}, "spdk-isal": {"baseline": "2018-10-06", "port-version": 3}, "spdlog": {"baseline": "1.15.2", "port-version": 0}, "spectra": {"baseline": "1.0.1", "port-version": 0}, "speex": {"baseline": "1.2.1", "port-version": 1}, "speexdsp": {"baseline": "1.2.1", "port-version": 1}, "spglib": {"baseline": "2.5.0", "port-version": 0}, "spine-runtimes": {"baseline": "4.1.0", "port-version": 0}, "spirit-po": {"baseline": "1.1.2", "port-version": 4}, "spirv-cross": {"baseline": "1.4.304.1", "port-version": 0}, "spirv-headers": {"baseline": "1.4.304.1", "port-version": 0}, "spirv-reflect": {"baseline": "1.4.304.1", "port-version": 0}, "spirv-tools": {"baseline": "1.4.304.1", "port-version": 0}, "spout2": {"baseline": "2.007.010", "port-version": 0}, "sprout": {"baseline": "2019-06-20", "port-version": 2}, "spscqueue": {"baseline": "1.1", "port-version": 3}, "sqlcipher": {"baseline": "4.6.1", "port-version": 1}, "sqlite-modern-cpp": {"baseline": "2023-12-03", "port-version": 0}, "sqlite-orm": {"baseline": "1.9.1", "port-version": 0}, "sqlite3": {"baseline": "3.49.1", "port-version": 0}, "sqlitecpp": {"baseline": "3.3.2", "port-version": 0}, "sqlpp11": {"baseline": "0.64", "port-version": 3}, "sqlpp11-connector-mysql": {"baseline": "0.61", "port-version": 0}, "sqlpp11-connector-sqlite3": {"baseline": "0.61", "port-version": 0}, "squirrel": {"baseline": "2021-09-17", "port-version": 0}, "sratom": {"baseline": "0.6.18", "port-version": 0}, "srell": {"baseline": "3.010", "port-version": 0}, "srpc": {"baseline": "0.10.3", "port-version": 1}, "sse2neon": {"baseline": "1.8.0", "port-version": 0}, "stackwalker": {"baseline": "2023-06-24", "port-version": 0}, "starlink-ast": {"baseline": "9.2.12", "port-version": 0}, "staticjson": {"baseline": "1.0.0", "port-version": 0}, "status-code": {"baseline": "2023-11-06", "port-version": 1}, "status-value-lite": {"baseline": "1.1.0", "port-version": 3}, "stb": {"baseline": "2024-07-29", "port-version": 1}, "stdexec": {"baseline": "2024-06-16", "port-version": 2}, "stduuid": {"baseline": "1.2.3", "port-version": 0}, "steam-audio": {"baseline": "4.5.3", "port-version": 1}, "stftpitchshift": {"baseline": "1.4.1", "port-version": 0}, "stlab": {"baseline": "1.7.1", "port-version": 2}, "stormlib": {"baseline": "9.26", "port-version": 0}, "str-view": {"baseline": "0.5.4", "port-version": 0}, "strict-variant": {"baseline": "0.5", "port-version": 2}, "string-theory": {"baseline": "3.8", "port-version": 0}, "string-view-lite": {"baseline": "1.8.0", "port-version": 1}, "stringzilla": {"baseline": "3.12.3", "port-version": 0}, "strong-type": {"baseline": "15", "port-version": 0}, "stronk": {"baseline": "0.3.1", "port-version": 0}, "strtk": {"baseline": "2020-09-14", "port-version": 4}, "stx": {"baseline": "1.0.5", "port-version": 0}, "stxxl": {"baseline": "2018-11-15", "port-version": 9}, "suitesparse": {"baseline": "7.8.3", "port-version": 0}, "suitesparse-amd": {"baseline": "3.3.3", "port-version": 0}, "suitesparse-btf": {"baseline": "2.3.2", "port-version": 0}, "suitesparse-camd": {"baseline": "3.3.3", "port-version": 0}, "suitesparse-ccolamd": {"baseline": "3.3.4", "port-version": 0}, "suitesparse-cholmod": {"baseline": "5.3.0", "port-version": 0}, "suitesparse-colamd": {"baseline": "3.3.4", "port-version": 0}, "suitesparse-config": {"baseline": "7.8.3", "port-version": 0}, "suitesparse-cxsparse": {"baseline": "4.4.1", "port-version": 0}, "suitesparse-graphblas": {"baseline": "9.3.1", "port-version": 0}, "suitesparse-klu": {"baseline": "2.3.5", "port-version": 0}, "suitesparse-lagraph": {"baseline": "1.1.4", "port-version": 1}, "suitesparse-ldl": {"baseline": "3.3.2", "port-version": 0}, "suitesparse-mongoose": {"baseline": "3.3.4", "port-version": 0}, "suitesparse-paru": {"baseline": "1.0.0", "port-version": 0}, "suitesparse-rbio": {"baseline": "4.3.4", "port-version": 0}, "suitesparse-spex": {"baseline": "3.2.1", "port-version": 0}, "suitesparse-spqr": {"baseline": "4.3.4", "port-version": 0}, "suitesparse-umfpack": {"baseline": "6.3.5", "port-version": 0}, "sundials": {"baseline": "7.1.1", "port-version": 0}, "superlu": {"baseline": "7.0.0", "port-version": 0}, "swenson-sort": {"baseline": "2021-05-22", "port-version": 0}, "symengine": {"baseline": "0.11.2", "port-version": 2}, "systemc": {"baseline": "3.0.1", "port-version": 0}, "tabulate": {"baseline": "1.5", "port-version": 0}, "tacopie": {"baseline": "3.2.0", "port-version": 6}, "taglib": {"baseline": "2.0.2", "port-version": 0}, "talib": {"baseline": "0.4.0", "port-version": 1}, "taocpp-json": {"baseline": "2020-09-14", "port-version": 4}, "tap-windows6": {"baseline": "9.21.2-0e30f5c", "port-version": 2}, "task-thread-pool": {"baseline": "1.0.7", "port-version": 0}, "taskflow": {"baseline": "3.9.0", "port-version": 0}, "tbb": {"baseline": "2022.0.0", "port-version": 2}, "tcb-span": {"baseline": "2022-06-15", "port-version": 0}, "tcl": {"baseline": "core-9-0-a1", "port-version": 8}, "tclap": {"baseline": "1.2.5", "port-version": 0}, "tcp-pubsub": {"baseline": "1.0.3", "port-version": 0}, "tdscpp": {"baseline": "20250301", "port-version": 0}, "telnetpp": {"baseline": "3.1.0", "port-version": 0}, "tensorflow": {"baseline": "2.10.0", "port-version": 0}, "tensorflow-cc": {"baseline": "2.10.0", "port-version": 0}, "tensorflow-common": {"baseline": "2.10.0", "port-version": 4}, "tensorpipe": {"baseline": "2022-03-16", "port-version": 5}, "termcolor": {"baseline": "2.1.0", "port-version": 0}, "tesseract": {"baseline": "5.5.0", "port-version": 1}, "tevclient": {"baseline": "2023-12-04", "port-version": 0}, "tfhe": {"baseline": "1.0.1", "port-version": 5}, "tgbot-cpp": {"baseline": "1.7.3", "port-version": 1}, "tgc": {"baseline": "2019-08-11", "port-version": 4}, "tgui": {"baseline": "1.8.0", "port-version": 2}, "think-cell-range": {"baseline": "2023.1", "port-version": 1}, "thomasmonkman-filewatch": {"baseline": "2023-01-16", "port-version": 2}, "thorvg": {"baseline": "0.15.12", "port-version": 0}, "threadpool": {"baseline": "0.2.5", "port-version": 3}, "thrift": {"baseline": "0.20.0", "port-version": 1}, "tidy-html5": {"baseline": "5.8.0", "port-version": 2}, "tiff": {"baseline": "4.7.0", "port-version": 0}, "tinkerforge": {"baseline": "2.1.25", "port-version": 3}, "tiny-aes-c": {"baseline": "2019-07-31", "port-version": 3}, "tiny-bignum-c": {"baseline": "2019-07-31", "port-version": 3}, "tiny-dnn": {"baseline": "2018-10-25", "port-version": 2}, "tiny-process-library": {"baseline": "2.0.4", "port-version": 3}, "tiny-regex-c": {"baseline": "2019-07-31", "port-version": 3}, "tinycbor": {"baseline": "0.6.1", "port-version": 0}, "tinycthread": {"baseline": "2019-08-06", "port-version": 3}, "tinydir": {"baseline": "1.2.6", "port-version": 0}, "tinyexif": {"baseline": "1.0.3", "port-version": 0}, "tinyexpr": {"baseline": "2020-09-25", "port-version": 2}, "tinyexr": {"baseline": "1.0.9", "port-version": 0}, "tinyfiledialogs": {"baseline": "3.19.1", "port-version": 0}, "tinyfsm": {"baseline": "0.3.3", "port-version": 0}, "tinygltf": {"baseline": "2.9.4", "port-version": 0}, "tinynpy": {"baseline": "1.0.0", "port-version": 6}, "tinyobjloader": {"baseline": "2.0.0rc13", "port-version": 0}, "tinyorm": {"baseline": "0.38.1", "port-version": 0}, "tinyply": {"baseline": "2.3.4", "port-version": 0}, "tinyspline": {"baseline": "0.6.0", "port-version": 0}, "tinythread": {"baseline": "1.1", "port-version": 6}, "tinytiff": {"baseline": "4.0.1.0", "port-version": 0}, "tinytoml": {"baseline": "20180219", "port-version": 3}, "tinyutf8": {"baseline": "4.4.3", "port-version": 1}, "tinyxml": {"baseline": "2.6.2", "port-version": 10}, "tinyxml2": {"baseline": "11.0.0", "port-version": 0}, "tl-expected": {"baseline": "1.1.0", "port-version": 0}, "tl-function-ref": {"baseline": "1.0.0", "port-version": 4}, "tl-generator": {"baseline": "2021-09-28", "port-version": 0}, "tl-optional": {"baseline": "1.1.0", "port-version": 0}, "tl-ranges": {"baseline": "2022-12-07", "port-version": 1}, "tlx": {"baseline": "0.6.1", "port-version": 0}, "tmx": {"baseline": "1.10.0", "port-version": 0}, "tmxlite": {"baseline": "1.4.4", "port-version": 0}, "tmxparser": {"baseline": "2019-10-14", "port-version": 1}, "tobias-loew-flags": {"baseline": "2024-09-10", "port-version": 0}, "toml11": {"baseline": "4.3.0", "port-version": 0}, "tomlplusplus": {"baseline": "3.4.0", "port-version": 1}, "tomsolver": {"baseline": "1.0.1", "port-version": 0}, "torch-th": {"baseline": "2019-04-19", "port-version": 4}, "tracy": {"baseline": "0.11.1", "port-version": 2}, "transwarp": {"baseline": "2.2.3", "port-version": 0}, "trantor": {"baseline": "1.5.23", "port-version": 0}, "tre": {"baseline": "0.8.0", "port-version": 6}, "tree-similarity": {"baseline": "0.1.1", "port-version": 1}, "tree-sitter": {"baseline": "0.25.3", "port-version": 0}, "tree-sitter-c": {"baseline": "0.23.5", "port-version": 0}, "tree-sitter-cli": {"baseline": "0.25.3", "port-version": 0}, "treehh": {"baseline": "3.18", "port-version": 0}, "treehopper": {"baseline": "1.11.3", "port-version": 10}, "triangle": {"baseline": "1.6", "port-version": 4}, "triton": {"baseline": "2025-02-15", "port-version": 0}, "trompeloeil": {"baseline": "48", "port-version": 0}, "try-catcher": {"baseline": "1.0.1", "port-version": 0}, "tsl-hopscotch-map": {"baseline": "2.3.1", "port-version": 0}, "tsl-ordered-map": {"baseline": "1.1.0", "port-version": 0}, "tsl-sparse-map": {"baseline": "0.6.2", "port-version": 3}, "ttauri": {"baseline": "0.5.0", "port-version": 2}, "tuplet": {"baseline": "2.1.1", "port-version": 0}, "turbobase64": {"baseline": "2023.8", "port-version": 0}, "tvision": {"baseline": "2024-05-22", "port-version": 0}, "tweeny": {"baseline": "3.2.0", "port-version": 1}, "type-lite": {"baseline": "0.2.0", "port-version": 0}, "type-safe": {"baseline": "0.2.4", "port-version": 0}, "uchardet": {"baseline": "0.0.8", "port-version": 0}, "ucoro": {"baseline": "1.0", "port-version": 0}, "udt": {"baseline": "4.11", "port-version": 0}, "umock-c": {"baseline": "2022-01-21", "port-version": 1}, "uni-algo": {"baseline": "1.2.0", "port-version": 0}, "unicorn": {"baseline": "2.1.1", "port-version": 0}, "unicorn-lib": {"baseline": "2022-01-24", "port-version": 2}, "units": {"baseline": "2.3.3", "port-version": 1}, "unittest-cpp": {"baseline": "2.0.0", "port-version": 6}, "unixodbc": {"baseline": "2.3.11", "port-version": 2}, "unordered-dense": {"baseline": "4.5.0", "port-version": 0}, "unqlite": {"baseline": "1.1.9", "port-version": 2}, "unrar": {"baseline": "7.0.7", "port-version": 0}, "upa-url": {"baseline": "2.0.0", "port-version": 0}, "urdfdom": {"baseline": "3.1.1", "port-version": 0}, "urdfdom-headers": {"baseline": "1.1.1", "port-version": 0}, "uriparser": {"baseline": "0.9.8", "port-version": 0}, "usbmuxd": {"baseline": "2023-07-21", "port-version": 1}, "usd": {"baseline": "25.2", "port-version": 0}, "usearch": {"baseline": "2.17.2", "port-version": 1}, "usockets": {"baseline": "0.8.8", "port-version": 2}, "usrsctp": {"baseline": "*******", "port-version": 4}, "utf8-range": {"baseline": "5.29.3", "port-version": 0}, "utf8h": {"baseline": "2021-11-18", "port-version": 1}, "utf8proc": {"baseline": "2.10.0", "port-version": 0}, "utfcpp": {"baseline": "4.0.6", "port-version": 0}, "utfz": {"baseline": "1.2", "port-version": 4}, "uthash": {"baseline": "2.3.0", "port-version": 0}, "uthenticode": {"baseline": "2.0.1", "port-version": 1}, "uvatlas": {"baseline": "2025-03-26", "port-version": 0}, "uvw": {"baseline": "3.2.0", "port-version": 0}, "uwebsockets": {"baseline": "20.74.0", "port-version": 0}, "v-hacd": {"baseline": "4.1.0", "port-version": 0}, "v8": {"baseline": "9.1.269.39", "port-version": 8}, "valijson": {"baseline": "1.0.4", "port-version": 0}, "value-ptr-lite": {"baseline": "0.2.1", "port-version": 1}, "vamp-sdk": {"baseline": "2.10", "port-version": 5}, "variant-lite": {"baseline": "2.0.0", "port-version": 0}, "vc": {"baseline": "1.4.4", "port-version": 0}, "vcglib": {"baseline": "2023.12", "port-version": 0}, "vcpkg-boost": {"baseline": "2024-05-15", "port-version": 0}, "vcpkg-cmake": {"baseline": "2024-04-23", "port-version": 0}, "vcpkg-cmake-config": {"baseline": "2024-05-23", "port-version": 0}, "vcpkg-cmake-get-vars": {"baseline": "2024-09-22", "port-version": 0}, "vcpkg-get-python": {"baseline": "2025-02-09", "port-version": 0}, "vcpkg-get-python-packages": {"baseline": "2025-04-05", "port-version": 0}, "vcpkg-gfortran": {"baseline": "3", "port-version": 3}, "vcpkg-gn": {"baseline": "2024-02-22", "port-version": 0}, "vcpkg-make": {"baseline": "2025-04-02", "port-version": 0}, "vcpkg-msbuild": {"baseline": "2023-08-08", "port-version": 0}, "vcpkg-pkgconfig-get-modules": {"baseline": "2024-04-03", "port-version": 0}, "vcpkg-qmake": {"baseline": "2023-03-22", "port-version": 3}, "vcpkg-tool-bazel": {"baseline": "5.2.0", "port-version": 0}, "vcpkg-tool-castxml": {"baseline": "0.6.5", "port-version": 0}, "vcpkg-tool-gn": {"baseline": "2025-01-27", "port-version": 0}, "vcpkg-tool-gyp-next": {"baseline": "2022-10-15", "port-version": 0}, "vcpkg-tool-lessmsi": {"baseline": "1.10.0", "port-version": 1}, "vcpkg-tool-meson": {"baseline": "1.7.2", "port-version": 0}, "vcpkg-tool-mozbuild": {"baseline": "4.0.2", "port-version": 0}, "vcpkg-tool-ninja": {"baseline": "2022-03-31", "port-version": 2}, "vcpkg-tool-nodejs": {"baseline": "16.18.0", "port-version": 1}, "vcpkg-tool-python2": {"baseline": "2.7.18", "port-version": 1}, "vectorclass": {"baseline": "2.02.00", "port-version": 0}, "veigar": {"baseline": "1.4", "port-version": 0}, "velodyne-decoder": {"baseline": "3.0.0", "port-version": 1}, "verdict": {"baseline": "1.4.0", "port-version": 0}, "via-httplib": {"baseline": "1.9.0", "port-version": 0}, "vili": {"baseline": "1.0.0+20221123", "port-version": 1}, "vincentlaucsb-csv-parser": {"baseline": "2.3.0", "port-version": 0}, "visit-struct": {"baseline": "1.1.0", "port-version": 0}, "vit-vit-ctpl": {"baseline": "0.0.2", "port-version": 0}, "vk-bootstrap": {"baseline": "1.3.279", "port-version": 1}, "vkfft": {"baseline": "1.2.31", "port-version": 0}, "vladimirshaleev-ipaddress": {"baseline": "1.1.0", "port-version": 0}, "vlfeat": {"baseline": "2020-07-10", "port-version": 4}, "vlpp": {"baseline": "********", "port-version": 0}, "vmaware-vm-detection": {"baseline": "2.0", "port-version": 0}, "volk": {"baseline": "1.4.304.1", "port-version": 0}, "vowpal-wabbit": {"baseline": "9.10.0", "port-version": 2}, "vs-yasm": {"baseline": "0.5.0", "port-version": 2}, "vsg": {"baseline": "1.1.10", "port-version": 0}, "vsgimgui": {"baseline": "0.3.0", "port-version": 0}, "vsgxchange": {"baseline": "1.1.4", "port-version": 1}, "vst3sdk": {"baseline": "v3.7.12_build_20", "port-version": 2}, "vtk": {"baseline": "9.3.0-pv5.12.1", "port-version": 7}, "vtk-dicom": {"baseline": "0.8.16", "port-version": 2}, "vtk-m": {"baseline": "2.1.0", "port-version": 0}, "vulkan": {"baseline": "2023-12-17", "port-version": 0}, "vulkan-extensionlayer": {"baseline": "1.4.304.1", "port-version": 0}, "vulkan-headers": {"baseline": "1.4.304.1", "port-version": 1}, "vulkan-hpp": {"baseline": "deprecated", "port-version": 0}, "vulkan-loader": {"baseline": "1.4.304.1", "port-version": 0}, "vulkan-memory-allocator": {"baseline": "3.1.0", "port-version": 0}, "vulkan-memory-allocator-hpp": {"baseline": "3.1.0", "port-version": 1}, "vulkan-sdk-components": {"baseline": "1.4.304.1", "port-version": 0}, "vulkan-tools": {"baseline": "1.4.304.1", "port-version": 0}, "vulkan-utility-libraries": {"baseline": "1.4.304.1", "port-version": 0}, "vulkan-validationlayers": {"baseline": "1.4.304.1", "port-version": 0}, "vvenc": {"baseline": "1.7.0", "port-version": 0}, "vxl": {"baseline": "3.5.0", "port-version": 0}, "wabt": {"baseline": "1.0.36", "port-version": 0}, "wampcc": {"baseline": "2024-07-10", "port-version": 0}, "wangle": {"baseline": "2025.03.31.00", "port-version": 0}, "wasmedge": {"baseline": "0.13.5", "port-version": 2}, "wavelib": {"baseline": "2021-11-26", "port-version": 0}, "wavpack": {"baseline": "5.7.0", "port-version": 0}, "wayland": {"baseline": "1.21.0", "port-version": 1}, "wayland-protocols": {"baseline": "1.31", "port-version": 1}, "wcslib": {"baseline": "8.4", "port-version": 0}, "websocketpp": {"baseline": "0.8.2", "port-version": 4}, "webthing-cpp": {"baseline": "1.2.0", "port-version": 0}, "webui": {"baseline": "2.4.2", "port-version": 0}, "webview2": {"baseline": "1.0.2277.86", "port-version": 0}, "wepoll": {"baseline": "1.5.8", "port-version": 3}, "wg21-linear-algebra": {"baseline": "0.7.3", "port-version": 1}, "wg21-sg14": {"baseline": "2019-08-13", "port-version": 2}, "wil": {"baseline": "1.0.250325.1", "port-version": 0}, "wildcards": {"baseline": "1.4.0", "port-version": 0}, "wildmidi": {"baseline": "0.4.6", "port-version": 0}, "wincrypt": {"baseline": "0.0", "port-version": 4}, "winlamb": {"baseline": "2020-10-15", "port-version": 0}, "winpcap": {"baseline": "4.1.3", "port-version": 12}, "winpixevent": {"baseline": "1.0.240308001", "port-version": 0}, "winpty": {"baseline": "0.4.3", "port-version": 0}, "winreg": {"baseline": "6.3.2", "port-version": 0}, "winsock2": {"baseline": "0.0", "port-version": 5}, "winsparkle": {"baseline": "0.9.0", "port-version": 0}, "wintoast": {"baseline": "1.3.1", "port-version": 0}, "wmipp": {"baseline": "1.3.0", "port-version": 0}, "woff2": {"baseline": "1.0.2", "port-version": 4}, "wolf-midi": {"baseline": "1.0.1", "port-version": 0}, "wolfmqtt": {"baseline": "1.19.0", "port-version": 0}, "wolfssl": {"baseline": "5.7.6", "port-version": 0}, "wolftpm": {"baseline": "3.4.0", "port-version": 0}, "wordnet": {"baseline": "3.0", "port-version": 3}, "workflow": {"baseline": "0.11.8", "port-version": 0}, "wpilib": {"baseline": "2023-08-24", "port-version": 2}, "wren": {"baseline": "0.4.0", "port-version": 0}, "wt": {"baseline": "4.11.4", "port-version": 0}, "wtl": {"baseline": "10.0.10320", "port-version": 4}, "wxchartdir": {"baseline": "2.0.0", "port-version": 2}, "wxcharts": {"baseline": "2022-07-05", "port-version": 0}, "wxwidgets": {"baseline": "3.2.7", "port-version": 0}, "wyhash": {"baseline": "2023-12-03", "port-version": 0}, "x-plane": {"baseline": "4.1.1", "port-version": 1}, "x264": {"baseline": "0.164.3108", "port-version": 2}, "x265": {"baseline": "4.1", "port-version": 0}, "x86-simd-sort": {"baseline": "5.0", "port-version": 0}, "xapian": {"baseline": "1.4.22", "port-version": 2}, "xaudio2redist": {"baseline": "1.2.12", "port-version": 0}, "xbitmaps": {"baseline": "1.1.2", "port-version": 0}, "xbyak": {"baseline": "7.24.2", "port-version": 0}, "xcb": {"baseline": "1.14", "port-version": 3}, "xcb-image": {"baseline": "0.4.1", "port-version": 0}, "xcb-keysyms": {"baseline": "0.4.1", "port-version": 0}, "xcb-proto": {"baseline": "1.14.1", "port-version": 2}, "xcb-render-util": {"baseline": "0.3.10", "port-version": 0}, "xcb-util": {"baseline": "0.4.0", "port-version": 0}, "xcb-util-errors": {"baseline": "1.0.1", "port-version": 1}, "xcb-util-m4": {"baseline": "2022-01-24", "port-version": 0}, "xcb-util-wm": {"baseline": "0.4.2", "port-version": 1}, "xerces-c": {"baseline": "3.3.0", "port-version": 0}, "xeus": {"baseline": "0.24.3", "port-version": 3}, "xframe": {"baseline": "0.3.0", "port-version": 3}, "xlnt": {"baseline": "1.6.0", "port-version": 0}, "xlsxio": {"baseline": "0.2.34", "port-version": 0}, "xmlsec": {"baseline": "1.3.7", "port-version": 0}, "xnnpack": {"baseline": "2022-12-22", "port-version": 0}, "xorg-macros": {"baseline": "1.19.3", "port-version": 1}, "xorstr": {"baseline": "2021-11-20", "port-version": 0}, "xpack": {"baseline": "1.0.5", "port-version": 0}, "xproperty": {"baseline": "0.8.1", "port-version": 3}, "xproto": {"baseline": "2021.5", "port-version": 0}, "xqilla": {"baseline": "2.3.4", "port-version": 4}, "xsimd": {"baseline": "13.1.0", "port-version": 0}, "xtensor": {"baseline": "0.26.0", "port-version": 0}, "xtensor-blas": {"baseline": "0.21.0", "port-version": 0}, "xtensor-fftw": {"baseline": "2019-11-30", "port-version": 4}, "xtensor-io": {"baseline": "0.13.0", "port-version": 1}, "xtl": {"baseline": "0.8.0", "port-version": 0}, "xtrans": {"baseline": "1.4.0", "port-version": 2}, "xxhash": {"baseline": "0.8.3", "port-version": 0}, "yajl": {"baseline": "2.1.0", "port-version": 4}, "yalantinglibs": {"baseline": "0.4.0", "port-version": 0}, "yaml-cpp": {"baseline": "0.8.0", "port-version": 2}, "yara": {"baseline": "4.5.2", "port-version": 1}, "yas": {"baseline": "7.1.0", "port-version": 0}, "yasm": {"baseline": "1.3.0", "port-version": 6}, "yasm-tool": {"baseline": "2021-12-14", "port-version": 0}, "yasm-tool-helper": {"baseline": "2020-03-11", "port-version": 1}, "yato": {"baseline": "2022-03-06", "port-version": 0}, "yoga": {"baseline": "3.2.1", "port-version": 0}, "yomm2": {"baseline": "1.6.0", "port-version": 0}, "yyjson": {"baseline": "0.10.0", "port-version": 0}, "z3": {"baseline": "4.14.0", "port-version": 0}, "z4kn4fein-semver": {"baseline": "0.4.0", "port-version": 0}, "z85": {"baseline": "1.0", "port-version": 2}, "zeroc-ice": {"baseline": "3.7.10", "port-version": 0}, "zeromq": {"baseline": "4.3.5", "port-version": 2}, "zfp": {"baseline": "1.0.1", "port-version": 0}, "zimpl": {"baseline": "3.6.1", "port-version": 0}, "zint": {"baseline": "2.12.0", "port-version": 1}, "zix": {"baseline": "0.6.2", "port-version": 0}, "zkpp": {"baseline": "0.2.3", "port-version": 3}, "zlib": {"baseline": "1.3.1", "port-version": 0}, "zlib-ng": {"baseline": "2.2.3", "port-version": 1}, "zlmediakit": {"baseline": "2024-09-29", "port-version": 1}, "zoe": {"baseline": "3.5", "port-version": 0}, "zookeeper": {"baseline": "3.5.6", "port-version": 1}, "zopfli": {"baseline": "1.0.3", "port-version": 4}, "zpp-bits": {"baseline": "4.5", "port-version": 0}, "zserge-webview": {"baseline": "0.12.0", "port-version": 0}, "zstd": {"baseline": "1.5.7", "port-version": 0}, "zstr": {"baseline": "1.0.7", "port-version": 0}, "ztd-cuneicode": {"baseline": "2023-11-03", "port-version": 1}, "ztd-encoding-tables": {"baseline": "2023-06-10", "port-version": 0}, "ztd-idk": {"baseline": "2023-11-03", "port-version": 0}, "ztd-platform": {"baseline": "2022-12-30", "port-version": 0}, "ztd-static-containers": {"baseline": "2022-12-12", "port-version": 1}, "ztd-text": {"baseline": "2023-11-03", "port-version": 1}, "zug": {"baseline": "2024-04-26", "port-version": 0}, "zycore": {"baseline": "1.5.1", "port-version": 0}, "zydis": {"baseline": "4.1.1", "port-version": 0}, "zyre": {"baseline": "2024-04-10", "port-version": 0}, "zziplib": {"baseline": "0.13.78", "port-version": 0}}}