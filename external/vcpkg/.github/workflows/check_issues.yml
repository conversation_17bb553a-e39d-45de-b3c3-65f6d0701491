name: Check For Common Issues

on:
  issues:
    types:
      - opened

jobs:
  check-for-common-issues:
    runs-on: ubuntu-latest
    permissions:
      issues: write
    steps:
      - uses: actions/github-script@v7
        with:
          script: |
            let issue_query = {
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo
            };
            let issue = await github.rest.issues.get(issue_query)
            let issue_body = issue.data.body.replaceAll("\r\n", "\n");
            let commentLabelClose = async (comment, label) => {
              await github.rest.issues.removeAllLabels(issue_query);
              await github.rest.issues.setLabels({...issue_query, labels: [label]});
              await github.rest.issues.createComment({...issue_query, body: comment});
              await github.rest.issues.update({...issue_query, state: "closed"});
            }

            // missing-windows-sdk-issue
            let reg = /RC Pass 1: command "rc .*" failed \(exit code 0\) with the following output:/;
            if (reg.test(issue_body)){
              let body = "Thanks for posting this issue. Please make sure you have the following installed.\n" +
                         "- Visual Studio Desktop development with C++.\n" +
                         "- Windows 10 SDK or Windows 11 SDK.";
              return await commentLabelClose(body, "category:question");
            }

            // msys2 download fails => old vcpkg version
            reg = /error: https:\/\/repo\.msys2\.org\/.*: failed: status code 404/;
            if (reg.test(issue_body)){
              let body = "Try updating your vcpkg version via `git pull` to resolve this issue. MSYS2 downloads are removed from the upstream servers from time to time, so using an up-to-date vcpkg version is necessary."
              return await commentLabelClose(body, "category:question");
            }

            // https://gitlab.freedesktop.org maintenance
            reg = /error: https:\/\/gitlab\.freedesktop\.org\/.*: failed: status code 503/;
            if (reg.test(issue_body)){
              let body = "gitlab.freedesktop.org is undergoing maintenance until 2025-03-22. Tracking issue https://github.com/microsoft/vcpkg/issues/44429"
              return await commentLabelClose(body, "category:question");
            }

            regs = []
            // Issue text is: Copy issue body from .../issue_body.md
            regs.push( /^Copy issue body from .*issue_body.md$/ );
            // Issue to short like #36592 or #36668
            regs.push( /^error: building.* BUILD_FAILED\r\nElapsed time.*\r\nPlease ensure.*(\r\nThen check .*\r\n.*)?$/ );
            // Issues with only the default error message like #41813
            regs.push( /^error: building.* BUILD_FAILED\r\n[\s\S]*issue_body.md$/ );
            if (regs.some(reg => reg.test(issue_body))) {
              let body = "Please see #30604 for how to report a build failure."
              return await commentLabelClose(body, "requires:more-information");
            }

            // pkg-config/ not found issues like #36011
            reg = /CMake Error at scripts\/cmake\/vcpkg_find_acquire_program.*\n(.*Please install it via your package manager:[\s\S]+)Call Stack/;
            match = issue_body.match(reg)
            if (match){
              let body = "From the log:\n```\n" + match[1] + "```\n"
              return await commentLabelClose(body, "category:question");
            }

            // MSVC internal compiler error like #36628
            if (issue_body.indexOf("fatal error C1001: Internal compiler error") !== -1){
              let body = "The build failed due to an internal compiler error. Please update your compiler or revert to an old version."
              return await commentLabelClose(body, "category:question");
            }

            // configure: error: 'autoconf-archive' is missing like #37013
            if (issue_body.indexOf("configure: error: 'autoconf-archive' is missing") !== -1){
              let body = "Please install `autoconf-archive` via `brew install autoconf-archive` (macos) or `sudo apt-get install autoconf-archive` (linux)"
              return await commentLabelClose(body, "category:question");
            }

            // Wrong formatted issues like #36086
            const containsCopyHint = issue_body.indexOf("Copy issue body from") !== -1 || issue_body.indexOf("%2Fissue_body.md") !== -1;
            if (containsCopyHint && issue_body.indexOf("```") === -1){
              let body = "Please see #30604 for how to properly report a build failure."
              return await github.rest.issues.createComment({...issue_query, body: body});
            }
