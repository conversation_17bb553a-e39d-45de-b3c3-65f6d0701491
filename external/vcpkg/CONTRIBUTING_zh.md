# 贡献准则

Vcpkg 是一个尝试由社区驱动，旨在构建一个多产的、健壮的本地库生态系统 - 您的贡献价值不可估量！

## 报告问题

最简单的方法是通过 `vcpkg.exe` 或 [GitHub](https://github.com/Microsoft/vcpkg) 报告已有的包的问题。 当报告 `vcpkg.exe` 的问题时，确保清楚地说明:
- 机器设置: “我用的是Windows 10周年更新。 我的机器正位于fr-fr区域。 我成功地运行了'install boost'。”
- 复现步骤: “运行 'vcpkg list'”
- 预期结果: “我预期看到 'boost:x86-windows'”
- 实际结果: “没有输出” 或 “我得到一个崩溃对话框”

当报告包的问题时，一定要清楚地说明:
- 机器设置 (上述)
- 您正在构建什么包以及它的版本，例如: “opencv 3.1.0”
- 构建过程中的任何相关错误日志

## 贡献 (PR)

我们很乐意接受关于修复、特性、新包和更新现有包的拉取请求。 为了避免浪费您的时间，我们强烈建议您提交一个问题来讨论您想要制作的PR是否能被接受。 对于特性和新包来说也是如此。

### 新包贡献准则

我们很高兴您有兴趣来提交一个新的包! 这里有一些指导方针来帮助您编写一个优秀的端口文件:
- 避免功能补丁。 当没有其他方法时，补丁应该被视为实现兼容性的最后手段。
- 当无法避免补丁时，请不要修改默认行为。 一个补丁的理想生命周期是与上游合并，不再被需要。 在决定如何修补某些内容时，请记住这一目标。
- 相比原始的 `execute_command` 调用，尽量改为通过 `vcpkg_xyz` 函数实现。这使得在添加新特性(如自定义编译器标志或生成器)时更容易进行长期维护。

## 法律声明

在您的拉取请求被接受之前，您需要完成一个贡献者许可协议 (CLA)。 本协议证明您允许我们使用您提交的源代码，并且本作品是在合适的许可下提交的，我们可以使用它。

您可以通过 https://cla.microsoft.com 上的步骤来完成CLA。 一旦我们收到已签署的CLA，我们将审查请求。 您只需要这样做一次。
