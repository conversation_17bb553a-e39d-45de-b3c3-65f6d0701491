{"name": "argtable3", "version-string": "3.2.2.f25c624", "description": "A single-file, ANSI C, command-line parsing library that parses GNU-style command-line options", "homepage": "https://www.argtable.org/", "license": "BSD-2-<PERSON><PERSON>-NetBSD AND TCL", "dependencies": [{"name": "getopt", "platform": "windows & !mingw"}, {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}]}