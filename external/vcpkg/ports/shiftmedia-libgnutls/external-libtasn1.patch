diff --git a/SMP/libgnutls.vcxproj.filters b/SMP/libgnutls.vcxproj.filters
index ef202f4ac..a397e1574 100644
--- a/SMP/libgnutls.vcxproj.filters
+++ b/SMP/libgnutls.vcxproj.filters
@@ -103,9 +103,6 @@
     <Filter Include="Header Files\lib\inih">
       <UniqueIdentifier>{ae0c3eeb-53df-4c72-a85a-6b46de35e7ba}</UniqueIdentifier>
     </Filter>
-    <Filter Include="Source Files\libtasn1">
-      <UniqueIdentifier>{32be60b7-8c65-486e-9df5-7e529443cf07}</UniqueIdentifier>
-    </Filter>
     <Filter Include="Source Files\lib\nettle\gost">
       <UniqueIdentifier>{1f3549a8-3c3d-475f-8fd6-20451d336464}</UniqueIdentifier>
     </Filter>
@@ -1535,30 +1532,6 @@
     <ClCompile Include="..\lib\nettle\sysrng-bcrypt.c">
       <Filter>Source Files\lib\nettle</Filter>
     </ClCompile>
-    <ClCompile Include="..\devel\libtasn1\lib\coding.c">
-      <Filter>Source Files\libtasn1</Filter>
-    </ClCompile>
-    <ClCompile Include="..\devel\libtasn1\lib\decoding.c">
-      <Filter>Source Files\libtasn1</Filter>
-    </ClCompile>
-    <ClCompile Include="..\devel\libtasn1\lib\element.c">
-      <Filter>Source Files\libtasn1</Filter>
-    </ClCompile>
-    <ClCompile Include="..\devel\libtasn1\lib\errors.c">
-      <Filter>Source Files\libtasn1</Filter>
-    </ClCompile>
-    <ClCompile Include="..\devel\libtasn1\lib\gstr.c">
-      <Filter>Source Files\libtasn1</Filter>
-    </ClCompile>
-    <ClCompile Include="..\devel\libtasn1\lib\parser_aux.c">
-      <Filter>Source Files\libtasn1</Filter>
-    </ClCompile>
-    <ClCompile Include="..\devel\libtasn1\lib\structure.c">
-      <Filter>Source Files\libtasn1</Filter>
-    </ClCompile>
-    <ClCompile Include="..\devel\libtasn1\lib\version.c">
-      <Filter>Source Files\libtasn1</Filter>
-    </ClCompile>
     <ClCompile Include="..\lib\nettle\gost\acpkm.c">
       <Filter>Source Files\lib\nettle\gost</Filter>
     </ClCompile>
diff --git a/SMP/libgnutls_files.props b/SMP/libgnutls_files.props
index 55049b70b..2bb76e3b0 100644
--- a/SMP/libgnutls_files.props
+++ b/SMP/libgnutls_files.props
@@ -175,13 +175,6 @@
   <ItemGroup>
     <ClCompile Include="lib\gnutls_asn1_tab.c" />
     <ClCompile Include="lib\pkix_asn1_tab.c" />
-    <ClCompile Include="..\devel\libtasn1\lib\coding.c" />
-    <ClCompile Include="..\devel\libtasn1\lib\decoding.c" />
-    <ClCompile Include="..\devel\libtasn1\lib\element.c" />
-    <ClCompile Include="..\devel\libtasn1\lib\gstr.c" />
-    <ClCompile Include="..\devel\libtasn1\lib\parser_aux.c" />
-    <ClCompile Include="..\devel\libtasn1\lib\structure.c" />
-    <ClCompile Include="..\devel\libtasn1\lib\version.c" />
     <ClCompile Include="..\gnulib\lib\c-strcasecmp.c" />
     <ClCompile Include="..\gnulib\lib\c-strncasecmp.c" />
     <ClCompile Include="..\gnulib\lib\cloexec.c" />
@@ -545,9 +538,6 @@
     </ClCompile>	
     <ClCompile Include="..\gnulib\lib\stdio-write.c">
       <PreprocessorDefinitions>REPLACE_PRINTF_POSIX=1;REPLACE_FPRINTF_POSIX=1;REPLACE_VPRINTF_POSIX=1;REPLACE_VFPRINTF_POSIX=1;%(PreprocessorDefinitions)</PreprocessorDefinitions>
-    </ClCompile>	
-    <ClCompile Include="..\devel\libtasn1\lib\errors.c">
-      <ObjectFileName>$(IntDir)\tasn1_$(filename).obj</ObjectFileName>
     </ClCompile>
     <ClCompile Include="..\lib\algorithms\ecc.c">
       <ObjectFileName>$(IntDir)\alg_%(Filename).obj</ObjectFileName>
diff --git a/SMP/libgnutls_winrt.vcxproj.filters b/SMP/libgnutls_winrt.vcxproj.filters
index f6a355e7e..299749c2a 100644
--- a/SMP/libgnutls_winrt.vcxproj.filters
+++ b/SMP/libgnutls_winrt.vcxproj.filters
@@ -103,9 +103,6 @@
     <Filter Include="Header Files\lib\inih">
       <UniqueIdentifier>{ae0c3eeb-53df-4c72-a85a-6b46de35e7ba}</UniqueIdentifier>
     </Filter>
-    <Filter Include="Source Files\libtasn1">
-      <UniqueIdentifier>{32be60b7-8c65-486e-9df5-7e529443cf07}</UniqueIdentifier>
-    </Filter>
     <Filter Include="Header Files\lib\nettle\gost">
       <UniqueIdentifier>{4fada990-3138-4089-a6c7-ae722a0e7fe9}</UniqueIdentifier>
     </Filter>
@@ -1535,30 +1532,6 @@
     <ClCompile Include="..\lib\nettle\sysrng-bcrypt.c">
       <Filter>Source Files\lib\nettle</Filter>
     </ClCompile>
-    <ClCompile Include="..\devel\libtasn1\lib\coding.c">
-      <Filter>Source Files\libtasn1</Filter>
-    </ClCompile>
-    <ClCompile Include="..\devel\libtasn1\lib\decoding.c">
-      <Filter>Source Files\libtasn1</Filter>
-    </ClCompile>
-    <ClCompile Include="..\devel\libtasn1\lib\element.c">
-      <Filter>Source Files\libtasn1</Filter>
-    </ClCompile>
-    <ClCompile Include="..\devel\libtasn1\lib\errors.c">
-      <Filter>Source Files\libtasn1</Filter>
-    </ClCompile>
-    <ClCompile Include="..\devel\libtasn1\lib\gstr.c">
-      <Filter>Source Files\libtasn1</Filter>
-    </ClCompile>
-    <ClCompile Include="..\devel\libtasn1\lib\parser_aux.c">
-      <Filter>Source Files\libtasn1</Filter>
-    </ClCompile>
-    <ClCompile Include="..\devel\libtasn1\lib\structure.c">
-      <Filter>Source Files\libtasn1</Filter>
-    </ClCompile>
-    <ClCompile Include="..\devel\libtasn1\lib\version.c">
-      <Filter>Source Files\libtasn1</Filter>
-    </ClCompile>
     <ClCompile Include="..\lib\accelerated\afalg.c">
       <Filter>Source Files\lib\accelerated</Filter>
     </ClCompile>
