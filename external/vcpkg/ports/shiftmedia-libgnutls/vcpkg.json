{"name": "shiftmedia-libgnutls", "version": "3.8.7", "port-version": 2, "description": "Unofficial GnuTLS fork with added custom native Visual Studio project build tools. ", "homepage": "https://github.com/ShiftMediaProject/gnutls", "license": "LGPL-2.1-only", "supports": "windows & !arm & !mingw & !xbox", "dependencies": ["gettext", "gmp", "libtasn1", "nettle", {"name": "vs-yasm", "host": true}, {"name": "yasm-tool-helper", "host": true}, "zlib"]}