{"name": "magnum-integration", "version-string": "2020.06", "port-version": 3, "description": "Integrations for magnum, C++11/C++14 graphics middleware for games and data visualization", "homepage": "https://magnum.graphics/", "license": null, "dependencies": [{"name": "magnum", "default-features": false}, {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"bullet": {"description": "BulletIntegration library", "dependencies": ["bullet3"]}, "eigen": {"description": "EigenIntegration library", "dependencies": ["eigen3"]}, "glm": {"description": "GlmIntegration library", "dependencies": ["glm"]}, "imgui": {"description": "ImGuiIntegration library", "dependencies": ["imgui"]}}}