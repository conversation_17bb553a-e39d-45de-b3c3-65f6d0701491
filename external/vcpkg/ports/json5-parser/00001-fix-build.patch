diff --git a/json5_parser/CMakeLists.txt b/json5_parser/CMakeLists.txt
index 3fbc6bb..e278364 100644
--- a/json5_parser/CMakeLists.txt
+++ b/json5_parser/CMakeLists.txt
@@ -1,3 +1,6 @@
+cmake_minimum_required(VERSION 3.5)
+project(json5-parser)
+
 SET(JSON_SPIRIT_SRCS
 json5_parser_reader.cpp json5_parser_reader.h
 json5_parser_value.cpp json5_parser_value.h
@@ -15,3 +18,21 @@ INCLUDE_DIRECTORIES(${Boost_INCLUDE_DIR})
 
 ADD_LIBRARY(json5_parser STATIC ${JSON_SPIRIT_SRCS})
 
+if(MSVC)
+    target_compile_options(json5_parser PRIVATE "/bigobj")
+endif()
+
+target_include_directories(json5_parser PUBLIC $<INSTALL_INTERFACE:include>)
+
+install(TARGETS json5_parser EXPORT json5-parser-config
+    RUNTIME DESTINATION bin
+    ARCHIVE DESTINATION lib
+    LIBRARY DESTINATION lib
+)
+install(EXPORT json5-parser-config DESTINATION share/json5-parser)
+
+file(GLOB HEADERS RELATIVE ${CMAKE_CURRENT_SOURCE_DIR} json5_parser*.h)
+foreach (HEADER ${HEADERS} )
+    get_filename_component(HEADER_DIR ${HEADER} DIRECTORY)
+    install(FILES ${HEADER} DESTINATION include/${HEADER_DIR})
+endforeach()
