{"name": "zeromq", "version": "4.3.5", "port-version": 2, "description": "The ZeroMQ lightweight messaging kernel is a library which extends the standard socket interfaces with features traditionally provided by specialised messaging middleware products", "homepage": "https://github.com/zeromq/libzmq", "license": "MPL-2.0", "supports": "!uwp & !xbox", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"curve": {"description": "Enable CURVE security"}, "draft": {"description": "Build and install draft APIs"}, "sodium": {"description": "Using libsodium for CURVE security", "dependencies": ["libsodium", {"name": "zeromq", "default-features": false, "features": ["curve"]}]}, "websockets": {"description": "Enable WebSocket transport"}, "websockets-secure": {"description": "Enable WebSocket transport with TSL (wss)", "dependencies": ["libgnutls", {"name": "zeromq", "default-features": false, "features": ["websockets"]}]}}}