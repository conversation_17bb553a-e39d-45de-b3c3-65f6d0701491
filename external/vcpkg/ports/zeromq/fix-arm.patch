diff --git a/CMakeLists.txt b/CMakeLists.txt
index dd3d8eb..c08cad9 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -551,6 +551,8 @@ if(ZMQ_HAVE_WINDOWS)
   set(CMAKE_REQUIRED_LIBRARIES "")
   # TODO: This not the symbol we're looking for. What is the symbol?
   check_library_exists(ws2 fopen "" HAVE_WS2)
+  
+  check_cxx_symbol_exists(CryptAcquireContext "windows.h;wincrypt.h" HAVE_ADVAPI32)
 else()
   check_cxx_symbol_exists(if_nametoindex net/if.h HAVE_IF_NAMETOINDEX)
   check_cxx_symbol_exists(SO_PEERCRED sys/socket.h ZMQ_HAVE_SO_PEERCRED)
@@ -1452,6 +1454,10 @@ if(BUILD_SHARED)
   elseif(HAVE_WS2)
     target_link_libraries(libzmq ws2)
   endif()
+  
+  if (HAVE_ADVAPI32)
+    target_link_libraries(libzmq advapi32)
+  endif()
 
   if(HAVE_RPCRT4)
     target_link_libraries(libzmq rpcrt4)
@@ -1497,6 +1503,10 @@ if(BUILD_STATIC)
   elseif(HAVE_WS2)
     target_link_libraries(libzmq-static ws2)
   endif()
+  
+  if (HAVE_ADVAPI32)
+    target_link_libraries(libzmq-static advapi32)
+  endif()
 
   if(HAVE_RPCRT4)
     target_link_libraries(libzmq-static rpcrt4)
diff --git a/src/clock.cpp b/src/clock.cpp
index 79522ad..0667c59 100644
--- a/src/clock.cpp
+++ b/src/clock.cpp
@@ -41,8 +41,10 @@
 #include <cmnintrin.h>
 #else
 #include <intrin.h>
-#if defined(_M_ARM) || defined(_M_ARM64)
+#if defined(_M_ARM)
 #include <arm_neon.h>
+#elif defined(_M_ARM64)
+#include <arm64_neon.h>
 #endif
 #endif
 #endif
