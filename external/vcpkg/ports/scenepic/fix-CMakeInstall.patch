diff --git a/CMakeLists.txt b/CMakeLists.txt
index a2a14bb..7a06c65 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -186,8 +186,8 @@ endif()
 # -------------------- Install ------------------------------------
 
 set(INSTALL_CONFIGDIR cmake)
-set(INSTALL_LIBDIR build/native/lib)
-set(INSTALL_INCLUDEDIR build/native/include)
+set(INSTALL_LIBDIR lib)
+set(INSTALL_INCLUDEDIR include)
 
 install(TARGETS scenepic 
   EXPORT scenepic-targets
@@ -195,7 +195,7 @@ install(TARGETS scenepic
   LIBRARY DESTINATION ${INSTALL_LIBDIR}
 )
 
-if( WIN32 )
+if( 0 )
   if(${CMAKE_BUILD_TYPE} STREQUAL "Debug")
     install(FILES
       $<TARGET_FILE_DIR:scenepic>/scenepic.pdb
diff --git a/src/scenepic/CMakeLists.txt b/src/scenepic/CMakeLists.txt
index f3d9673..916ab28 100644
--- a/src/scenepic/CMakeLists.txt
+++ b/src/scenepic/CMakeLists.txt
@@ -47,7 +47,7 @@ target_compile_features(scenepic PRIVATE cxx_std_14)
 
 target_include_directories( scenepic
   PUBLIC
-    $<INSTALL_INTERFACE:build/native/include>
+    $<INSTALL_INTERFACE:include>
     $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/../include/scenepic>
     $<BUILD_INTERFACE:${CMAKE_CURRENT_BINARY_DIR}>
   PRIVATE
