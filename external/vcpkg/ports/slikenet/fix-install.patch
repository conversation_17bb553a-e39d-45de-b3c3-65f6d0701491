diff --git a/Lib/DLL/CMakeLists.txt b/Lib/DLL/CMakeLists.txt
index 7f6453d..48f9562 100644
--- a/Lib/DLL/CMakeLists.txt
+++ b/Lib/DLL/CMakeLists.txt
@@ -50,6 +50,7 @@ ELSE(WIN32 AND NOT UNIX)
 ENDIF(WIN32 AND NOT UNIX)
 
 target_link_libraries(SLikeNetDLL ${SLIKENET_LIBRARY_LIBS})
+if(0)
 IF(NOT WIN32 OR UNIX)
 	configure_file(../../slikenet-config-version.cmake.in ${CMAKE_CURRENT_BINARY_DIR}/slikenet-config-version.cmake @ONLY)
 
@@ -60,3 +61,12 @@ IF(NOT WIN32 OR UNIX)
 	INSTALL(FILES ../../slikenet-config.cmake ${CMAKE_CURRENT_BINARY_DIR}/slikenet-config-version.cmake DESTINATION lib/slikenet-${SLikeNet_VERSION})
 	INSTALL(EXPORT SLikeNetDLL DESTINATION lib/slikenet-${SLikeNet_VERSION})
 ENDIF(NOT WIN32 OR UNIX)
+endif()
+
+install(TARGETS SLikeNetDLL
+		EXPORT SLikeNetDLL
+		RUNTIME DESTINATION bin
+		LIBRARY DESTINATION lib
+		ARCHIVE DESTINATION lib)
+INSTALL(FILES ${ALL_HEADER_SRCS} DESTINATION include/slikenet)
+install(EXPORT SLikeNetDLL FILE slikenetTargets.cmake DESTINATION share/slikenet)
diff --git a/Lib/LibStatic/CMakeLists.txt b/Lib/LibStatic/CMakeLists.txt
index f936fa5..a5dcc4f 100644
--- a/Lib/LibStatic/CMakeLists.txt
+++ b/Lib/LibStatic/CMakeLists.txt
@@ -50,6 +50,8 @@ ELSE(WIN32 AND NOT UNIX)
 ENDIF(WIN32 AND NOT UNIX)
 
 target_link_libraries(SLikeNetLibStatic ${SLIKENET_LIBRARY_LIBS})
+
+if(0)
 IF(WIN32 AND NOT UNIX)
 	IF(NOT ${CMAKE_GENERATOR} STREQUAL "MSYS Makefiles")
 		set_target_properties(SLikeNetLibStatic PROPERTIES STATIC_LIBRARY_FLAGS "/NODEFAULTLIB:\"LIBCD.lib LIBCMTD.lib MSVCRT.lib\"" )
@@ -64,3 +66,12 @@ ELSE(WIN32 AND NOT UNIX)
 	INSTALL(FILES ../../slikenet-config.cmake ${CMAKE_CURRENT_BINARY_DIR}/slikenet-config-version.cmake DESTINATION lib/slikenet-${SLikeNet_VERSION})
 	INSTALL(EXPORT SLikeNetLibStatic FILE slikenet.cmake DESTINATION lib/slikenet-${SLikeNet_VERSION})
 ENDIF(WIN32 AND NOT UNIX)
+endif()
+
+INSTALL(TARGETS SLikeNetLibStatic
+		EXPORT SLikeNetLibStatic
+		RUNTIME DESTINATION bin
+		LIBRARY DESTINATION lib
+		ARCHIVE DESTINATION lib)
+INSTALL(FILES ${ALL_HEADER_SRCS} DESTINATION include/slikenet)
+INSTALL(EXPORT SLikeNetLibStatic FILE slikenetTargets.cmake DESTINATION share/slikenet)
