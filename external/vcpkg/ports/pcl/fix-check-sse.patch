From c65e034afb538932f4ff55a93332f875ae43a578 Mon Sep 17 00:00:00 2001
From: raa<PERSON><PERSON>-z <<EMAIL>>
Date: Wed, 7 Jul 2021 15:54:33 -0400
Subject: [PATCH] fix check sse

---
 CMakeLists.txt | 2 +-
 1 file changed, 1 insertion(+), 1 deletion(-)

diff --git a/CMakeLists.txt b/CMakeLists.txt
index 29f79d31b..a1d9bb58f 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -86,7 +86,7 @@ endif()
 
 # check for SSE flags
 include("${PCL_SOURCE_DIR}/cmake/pcl_find_sse.cmake")
-if(PCL_ENABLE_SSE AND "${CMAKE_CXX_FLAGS}" STREQUAL "${CMAKE_CXX_FLAGS_DEFAULT}")
+if(PCL_ENABLE_SSE)
   PCL_CHECK_FOR_SSE()
 endif()
 
-- 
2.32.0.windows.1

