diff --git a/apps/src/ppf_object_recognition.cpp b/apps/src/ppf_object_recognition.cpp
index b12ac2a0f..9889d0c6a 100644
--- a/apps/src/ppf_object_recognition.cpp
+++ b/apps/src/ppf_object_recognition.cpp
@@ -9,6 +9,7 @@
 #include <pcl/segmentation/sac_segmentation.h>
 #include <pcl/visualization/pcl_visualizer.h>
 
+#include <chrono>
 #include <thread>
 
 using namespace pcl;
diff --git a/examples/stereo/example_stereo_baseline.cpp b/examples/stereo/example_stereo_baseline.cpp
index bf21c6f8f..594ab2465 100644
--- a/examples/stereo/example_stereo_baseline.cpp
+++ b/examples/stereo/example_stereo_baseline.cpp
@@ -1,3 +1,4 @@
+#include <chrono>
 #include <thread>
 
 #include <pcl/stereo/stereo_matching.h>
diff --git a/tools/obj_rec_ransac_accepted_hypotheses.cpp b/tools/obj_rec_ransac_accepted_hypotheses.cpp
index f75bd66a7..f884cd9c9 100644
--- a/tools/obj_rec_ransac_accepted_hypotheses.cpp
+++ b/tools/obj_rec_ransac_accepted_hypotheses.cpp
@@ -58,6 +58,7 @@
 #include <vtkHedgeHog.h>
 #include <vtkMatrix4x4.h>
 #include <algorithm>
+#include <chrono>
 #include <cstdio>
 #include <thread>
 #include <vector>
diff --git a/tools/obj_rec_ransac_hash_table.cpp b/tools/obj_rec_ransac_hash_table.cpp
index bde3d9413..88b959944 100644
--- a/tools/obj_rec_ransac_hash_table.cpp
+++ b/tools/obj_rec_ransac_hash_table.cpp
@@ -53,6 +53,7 @@
 #include <vtkDataArray.h>
 #include <vtkPointData.h>
 #include <vtkGlyph3D.h>
+#include <chrono>
 #include <cstdio>
 #include <thread>
 
diff --git a/tools/obj_rec_ransac_model_opps.cpp b/tools/obj_rec_ransac_model_opps.cpp
index d82cbfb16..e6b27a67d 100644
--- a/tools/obj_rec_ransac_model_opps.cpp
+++ b/tools/obj_rec_ransac_model_opps.cpp
@@ -52,6 +52,7 @@
 #include <vtkDataArray.h>
 #include <vtkPointData.h>
 #include <vtkHedgeHog.h>
+#include <chrono>
 #include <cstdio>
 #include <thread>
 
diff --git a/tools/obj_rec_ransac_orr_octree.cpp b/tools/obj_rec_ransac_orr_octree.cpp
index c21a193c4..6f55c0916 100644
--- a/tools/obj_rec_ransac_orr_octree.cpp
+++ b/tools/obj_rec_ransac_orr_octree.cpp
@@ -63,6 +63,7 @@
 #include <vtkRenderWindow.h>
 #include <vector>
 #include <list>
+#include <chrono>
 #include <cstdlib>
 #include <cstring>
 #include <cstdio>
diff --git a/tools/obj_rec_ransac_orr_octree_zprojection.cpp b/tools/obj_rec_ransac_orr_octree_zprojection.cpp
index 446d53511..42204cfc6 100644
--- a/tools/obj_rec_ransac_orr_octree_zprojection.cpp
+++ b/tools/obj_rec_ransac_orr_octree_zprojection.cpp
@@ -62,6 +62,7 @@
 #include <vtkCubeSource.h>
 #include <vtkPointData.h>
 #include <vector>
+#include <chrono>
 #include <cstdlib>
 #include <cstring>
 #include <cstdio>
diff --git a/tools/obj_rec_ransac_result.cpp b/tools/obj_rec_ransac_result.cpp
index 884dea61b..78b54eb77 100644
--- a/tools/obj_rec_ransac_result.cpp
+++ b/tools/obj_rec_ransac_result.cpp
@@ -57,6 +57,7 @@
 #include <vtkRenderer.h>
 #include <vtkRenderWindow.h>
 #include <vtkTransform.h>
+#include <chrono>
 #include <cstdio>
 #include <list>
 #include <thread>
diff --git a/tools/obj_rec_ransac_scene_opps.cpp b/tools/obj_rec_ransac_scene_opps.cpp
index 70ddfe42a..a1d5099a8 100644
--- a/tools/obj_rec_ransac_scene_opps.cpp
+++ b/tools/obj_rec_ransac_scene_opps.cpp
@@ -53,6 +53,7 @@
 #include <vtkDataArray.h>
 #include <vtkPointData.h>
 #include <vtkHedgeHog.h>
+#include <chrono>
 #include <cstdio>
 #include <thread>
 
diff --git a/visualization/include/pcl/visualization/impl/registration_visualizer.hpp b/visualization/include/pcl/visualization/impl/registration_visualizer.hpp
index 884735e4a..4a40b11f5 100644
--- a/visualization/include/pcl/visualization/impl/registration_visualizer.hpp
+++ b/visualization/include/pcl/visualization/impl/registration_visualizer.hpp
@@ -38,6 +38,7 @@
 
 #pragma once
 
+#include <chrono>
 #include <thread>
 
 
