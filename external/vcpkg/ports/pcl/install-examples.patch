diff --git a/cmake/pcl_targets.cmake b/cmake/pcl_targets.cmake
index 91f2404..18f5a24 100644
--- a/cmake/pcl_targets.cmake
+++ b/cmake/pcl_targets.cmake
@@ -473,6 +473,8 @@ macro(PCL_ADD_EXAMPLE _name)
   # add target to list of example targets created at the parent scope
   list(APPEND PCL_EXAMPLES_ALL_TARGETS ${_name})
   set(PCL_EXAMPLES_ALL_TARGETS "${PCL_EXAMPLES_ALL_TARGETS}" PARENT_SCOPE)
+
+  install(TARGETS ${_name} RUNTIME DESTINATION ${BIN_INSTALL_DIR})
 endmacro()
 
 ###############################################################################
