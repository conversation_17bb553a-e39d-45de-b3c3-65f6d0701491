From 3ea8aa5ded2782cfb10577348fc9f56144c72d7b Mon Sep 17 00:00:00 2001
From: raahilsha-z <<EMAIL>>
Date: Wed, 7 Jul 2021 15:57:46 -0400
Subject: [PATCH] fix numeric literals flag

---
 CMakeLists.txt | 3 +++
 1 file changed, 3 insertions(+)

diff --git a/CMakeLists.txt b/CMakeLists.txt
index 7cf86f74b..e07fd430a 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -420,6 +420,9 @@ endif()
 
 # Boost (required)
 include("${PCL_SOURCE_DIR}/cmake/pcl_find_boost.cmake")
+if (NOT APPLE AND NOT WIN32 AND CMAKE_CXX_COMPILER_ID STREQUAL "GNU")
+    add_definitions(-fext-numeric-literals)
+endif()
 
 # System zlib (for nurbs on surface)
 option(WITH_SYSTEM_ZLIB "Use system zlib" TRUE)
-- 
2.32.0.windows.1

