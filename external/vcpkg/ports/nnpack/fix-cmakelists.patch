diff --git a/CMakeLists.txt b/CMakeLists.txt
index 5ecd2df..8565044 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -12,8 +12,6 @@ SET_PROPERTY(CACHE NNPACK_BACKEND PROPERTY STRINGS auto psimd scalar)
 OPTION(NNPACK_CONVOLUTION_ONLY "Build only NNPACK functions for convolutional layer" OFF)
 OPTION(NNPACK_INFERENCE_ONLY "Build only NNPACK functions for inference" OFF)
 OPTION(NNPACK_CUSTOM_THREADPOOL "Build NNPACK for custom thread pool" OFF)
-SET(NNPACK_LIBRARY_TYPE "default" CACHE STRING "Type of library (shared, static, or default) to build")
-SET_PROPERTY(CACHE NNPACK_LIBRARY_TYPE PROPERTY STRINGS default static shared)
 OPTION(NNPACK_BUILD_TESTS "Build NNPACK unit tests" ON)
 
 # ---[ CMake options
@@ -442,15 +440,7 @@ ELSE()
   SET(NNPACK_BACKEND_C_SRCS ${NNPACK_BACKEND_SRCS})
 ENDIF()
 
-IF(NNPACK_LIBRARY_TYPE STREQUAL "default")
-  ADD_LIBRARY(nnpack ${NNPACK_INIT_SRCS} ${NNPACK_LAYER_SRCS} ${NNPACK_BACKEND_C_SRCS} ${NNPACK_BACKEND_PEACHPY_OBJS})
-ELSEIF(NNPACK_LIBRARY_TYPE STREQUAL "shared")
-  ADD_LIBRARY(nnpack SHARED ${NNPACK_INIT_SRCS} ${NNPACK_LAYER_SRCS} ${NNPACK_BACKEND_C_SRCS} ${NNPACK_BACKEND_PEACHPY_OBJS})
-ELSEIF(NNPACK_LIBRARY_TYPE STREQUAL "static")
-  ADD_LIBRARY(nnpack STATIC ${NNPACK_INIT_SRCS} ${NNPACK_LAYER_SRCS} ${NNPACK_BACKEND_C_SRCS} ${NNPACK_BACKEND_PEACHPY_OBJS})
-ELSE()
-  MESSAGE(FATAL_ERROR "Unsupported NNPACK library type \"${NNPACK_LIBRARY_TYPE}\". Must be \"static\", \"shared\", or \"default\"")
-ENDIF()
+ADD_LIBRARY(nnpack ${NNPACK_INIT_SRCS} ${NNPACK_LAYER_SRCS} ${NNPACK_BACKEND_C_SRCS} ${NNPACK_BACKEND_PEACHPY_OBJS})
 NNPACK_TARGET_ENABLE_C99(nnpack)
 IF(IOS OR CMAKE_SYSTEM_PROCESSOR MATCHES "^(armv5te|armv7-a|armv7l)$")
   IF(IOS AND NNPACK_BACKEND STREQUAL "neon")
@@ -496,7 +486,7 @@ NNPACK_TARGET_ENABLE_C99(nnpack_reference_layers)
 TARGET_INCLUDE_DIRECTORIES(nnpack_reference_layers PUBLIC include)
 
 # ---[ Configure cpuinfo
-IF(NOT TARGET cpuinfo)
+IF(FALSE)
   SET(CPUINFO_BUILD_TOOLS OFF CACHE BOOL "")
   SET(CPUINFO_BUILD_UNIT_TESTS OFF CACHE BOOL "")
   SET(CPUINFO_BUILD_MOCK_TESTS OFF CACHE BOOL "")
@@ -505,10 +495,11 @@ IF(NOT TARGET cpuinfo)
     "${CPUINFO_SOURCE_DIR}"
     "${CONFU_DEPENDENCIES_BINARY_DIR}/cpuinfo")
 ENDIF()
-TARGET_LINK_LIBRARIES(nnpack PRIVATE cpuinfo)
+find_package(cpuinfo CONFIG REQUIRED)
+target_link_libraries(nnpack PUBLIC cpuinfo::cpuinfo)
 
 # ---[ Configure pthreadpool
-IF(NOT TARGET pthreadpool)
+IF(FALSE)
   SET(PTHREADPOOL_BUILD_TESTS OFF CACHE BOOL "")
   SET(PTHREADPOOL_BUILD_BENCHMARKS OFF CACHE BOOL "")
   ADD_SUBDIRECTORY(
@@ -525,33 +516,37 @@ ENDIF()
 TARGET_LINK_LIBRARIES(nnpack_reference_layers PUBLIC pthreadpool)
 
 # ---[ Configure FXdiv
-IF(NOT TARGET fxdiv)
+IF(FALSE)
   SET(FXDIV_BUILD_TESTS OFF CACHE BOOL "")
   SET(FXDIV_BUILD_BENCHMARKS OFF CACHE BOOL "")
   ADD_SUBDIRECTORY(
     "${FXDIV_SOURCE_DIR}"
     "${CONFU_DEPENDENCIES_BINARY_DIR}/fxdiv")
 ENDIF()
-TARGET_LINK_LIBRARIES(nnpack PRIVATE fxdiv)
+find_path(FXDIV_INCLUDE_DIRS "fxdiv.h")
+target_include_directories(nnpack PRIVATE ${FXDIV_INCLUDE_DIRS})
+
 
 # ---[ Configure psimd
-IF(NOT TARGET psimd)
+IF(FALSE)
   ADD_SUBDIRECTORY(
     "${PSIMD_SOURCE_DIR}"
     "${CONFU_DEPENDENCIES_BINARY_DIR}/psimd")
 ENDIF()
-TARGET_LINK_LIBRARIES(nnpack PRIVATE psimd)
+find_path(PSIMD_INCLUDE_DIRS "psimd.h")
+target_include_directories(nnpack PRIVATE ${PSIMD_INCLUDE_DIRS})
 
 # ---[ Configure FP16
-IF(NOT TARGET fp16)
+IF(FALSE)
   SET(FP16_BUILD_TESTS OFF CACHE BOOL "")
   SET(FP16_BUILD_BENCHMARKS OFF CACHE BOOL "")
   ADD_SUBDIRECTORY(
     "${FP16_SOURCE_DIR}"
     "${CONFU_DEPENDENCIES_BINARY_DIR}/fp16")
 ENDIF()
-TARGET_LINK_LIBRARIES(nnpack PRIVATE fp16)
-TARGET_LINK_LIBRARIES(nnpack_reference_layers PUBLIC fp16)
+find_path(FP16_INCLUDE_DIRS "fp16.h")
+target_include_directories(nnpack PRIVATE ${FP16_INCLUDE_DIRS})
+target_include_directories(nnpack_reference_layers PUBLIC ${FP16_INCLUDE_DIRS})
 
 INSTALL(TARGETS nnpack
     LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
