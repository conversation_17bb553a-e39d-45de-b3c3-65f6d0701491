diff --git a/itpp/base/mat.cpp b/itpp/base/mat.cpp
index 9f2a20e..11b0a58 100644
--- a/itpp/base/mat.cpp
+++ b/itpp/base/mat.cpp
@@ -173,8 +173,7 @@ cmat operator*(const cmat &m1, const cmat &m2)
 template<>
 mat operator*(const mat &m1, const mat &m2)
 {
-  it_assert_debug(m1.rows() == m2.cols(),
-                  "Mat<>::operator*(): Wrong sizes");
+  it_assert_debug(m1.cols() == m2.rows(), "cmat::operator*(): Wrong sizes");
   mat r(m1.rows(), m2.cols());
   double *tr = r._data();
   const double *t1;
