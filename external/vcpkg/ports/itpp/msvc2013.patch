 CMakeLists.txt                 |  2 ++
 itpp/base/algebra/eigen.cpp    |  1 +
 itpp/base/algebra/ls_solve.cpp |  2 +-
 itpp/base/algebra/qr.cpp       |  1 +
 itpp/base/algebra/svd.cpp      |  2 +-
 itpp/base/bessel/gamma.cpp     |  5 +++++
 itpp/base/itcompat.cpp         | 11 ++++++++++-
 itpp/base/itcompat.h           | 14 ++++++++++++--
 itpp/base/matfunc.h            |  1 +
 itpp/base/svec.h               |  1 +
 itpp/comm/siso_dem.cpp         |  6 ++++++
 itpp/comm/siso_eq.cpp          |  6 ++++++
 itpp/comm/siso_mud.cpp         |  7 +++++++
 itpp/comm/siso_nsc.cpp         |  6 ++++++
 itpp/comm/siso_rsc.cpp         |  7 +++++++
 15 files changed, 67 insertions(+), 5 deletions(-)

diff --git a/CMakeLists.txt b/CMakeLists.txt
index 9f6a9a6..2360f86 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -122,7 +122,9 @@ CHECK_CXX_SYMBOL_EXISTS ( isnan "cmath" HAVE_DECL_ISNAN )
 CHECK_CXX_SYMBOL_EXISTS ( signgam "cmath" HAVE_DECL_SIGNGAM )
 
 #check for functions
+if( NOT MSVC )
 set(CMAKE_REQUIRED_LIBRARIES m)
+endif()
 include (CheckFunctionExists)
 CHECK_FUNCTION_EXISTS ( acosh HAVE_ACOSH )
 CHECK_FUNCTION_EXISTS ( asinh HAVE_ASINH )
diff --git a/itpp/base/algebra/eigen.cpp b/itpp/base/algebra/eigen.cpp
index 8c0c36b..54ae3e1 100644
--- a/itpp/base/algebra/eigen.cpp
+++ b/itpp/base/algebra/eigen.cpp
@@ -39,6 +39,7 @@
 #include <itpp/base/algebra/eigen.h>
 #include <itpp/base/converters.h>
 
+#include <algorithm>
 
 namespace itpp
 {
diff --git a/itpp/base/algebra/ls_solve.cpp b/itpp/base/algebra/ls_solve.cpp
index dae38fc..95abf0a 100644
--- a/itpp/base/algebra/ls_solve.cpp
+++ b/itpp/base/algebra/ls_solve.cpp
@@ -37,7 +37,7 @@
 #endif
 
 #include <itpp/base/algebra/ls_solve.h>
-
+#include <algorithm>
 
 namespace itpp
 {
diff --git a/itpp/base/algebra/qr.cpp b/itpp/base/algebra/qr.cpp
index 91dfb2b..0b47294 100644
--- a/itpp/base/algebra/qr.cpp
+++ b/itpp/base/algebra/qr.cpp
@@ -39,6 +39,7 @@
 #include <itpp/base/algebra/qr.h>
 #include <itpp/base/specmat.h>
 
+#include <algorithm>
 
 namespace itpp
 {
diff --git a/itpp/base/algebra/svd.cpp b/itpp/base/algebra/svd.cpp
index aa21e36..b2e499c 100644
--- a/itpp/base/algebra/svd.cpp
+++ b/itpp/base/algebra/svd.cpp
@@ -37,7 +37,7 @@
 #endif
 
 #include <itpp/base/algebra/svd.h>
-
+#include <algorithm>
 
 namespace itpp
 {
diff --git a/itpp/base/bessel/gamma.cpp b/itpp/base/bessel/gamma.cpp
index 162c4ae..9405507 100644
--- a/itpp/base/bessel/gamma.cpp
+++ b/itpp/base/bessel/gamma.cpp
@@ -34,6 +34,11 @@
 #include <itpp/base/itcompat.h>
 #include <itpp/base/math/misc.h>
 
+#if _MSC_VER
+//suppress warnings regarding the use infinity in fp arithmetic
+#pragma warning(disable: 4056)
+#pragma warning(disable: 4756)
+#endif
 
 /*
  * Gamma function
diff --git a/itpp/base/itcompat.cpp b/itpp/base/itcompat.cpp
index f99ec81..aa34b28 100644
--- a/itpp/base/itcompat.cpp
+++ b/itpp/base/itcompat.cpp
@@ -47,12 +47,21 @@ double tgamma(double x)
 }
 #endif
 
-#if !defined(HAVE_LGAMMA) || (HAVE_DECL_SIGNGAM != 1)
+#if !defined(HAVE_LGAMMA) && (HAVE_DECL_SIGNGAM != 1)
 // The sign of the Gamma function is returned in the external integer
 // signgam declared in <math.h>. It is 1 when the Gamma function is positive
 // or zero, -1 when it is negative. However, MinGW definition of lgamma()
 // function does not use the global signgam variable.
+//May 3rd 2015 (Andy Panov):
+//Nonetheless, I guess it would be smarter not to break the ODR rule and not to provide our own definition of lgamma
+//function when HAVE_LGAMMA is defined. If we still provide the definition, two definitions 
+//of the same function will exist in the program (this is undefined behaviour, as C++ standard says),
+//so we can not ensure linker will choose our definition and user's code will work as expected.
+//I guess, error message from linker regarding the unresolved symbol is much better option,
+//since it clearly points to the math library limitations. Otherwise user can get a malfunctioning program 
+//with really subtle error.
 int signgam;
+
 // Logarithm of an absolute value of gamma function
 double lgamma(double x)
 {
diff --git a/itpp/base/itcompat.h b/itpp/base/itcompat.h
index e6be223..dcf2ed7 100644
--- a/itpp/base/itcompat.h
+++ b/itpp/base/itcompat.h
@@ -41,8 +41,12 @@
 #if defined(_MSC_VER)
 #  include <cfloat>
 #  define finite(x) _finite(x)
+#ifndef HAVE_STD_ISFINITE
 #  define isfinite(x) _finite(x)
+#endif
+#ifndef HAVE_STD_ISNAN
 #  define isnan(x) _isnan(x)
+#endif
 #  define fpclass(x) _fpclass(x)
 #  define FP_NINF _FPCLASS_NINF
 #  define FP_PINF _FPCLASS_PINF
@@ -119,13 +123,19 @@ inline bool isfinite(double x)
 double tgamma(double x);
 #endif
 
-#if !defined(HAVE_LGAMMA) || (HAVE_DECL_SIGNGAM != 1)
+#if !defined(HAVE_LGAMMA) && (HAVE_DECL_SIGNGAM != 1)
+//Provide own definitions if both conditions are met:
+//-lgammma is not defined
+//-signgam  was not found
+//See ODR desicussion in itcompat.cpp
+
 //! Lograrithm of an absolute gamma function
 double lgamma(double x);
 //! Global variable needed by \c lgamma function
 extern int signgam;
 #endif
 
+
 #ifndef HAVE_CBRT
 //! Cubic root
 double cbrt(double x);
@@ -141,7 +151,7 @@ inline double log1p(double x) { return std::log(1.0 + x); }
 //! Base-2 logarithm
 inline double log2(double x)
 {
-  static const double one_over_log2 = 1.0 / std::log(2.0);
+  const double one_over_log2 = 1.0 / std::log(2.0);
   return std::log(x) * one_over_log2;
 }
 #endif
diff --git a/itpp/base/matfunc.h b/itpp/base/matfunc.h
index 8ae683e..60cf95f 100644
--- a/itpp/base/matfunc.h
+++ b/itpp/base/matfunc.h
@@ -36,6 +36,7 @@
 #include <itpp/base/algebra/inv.h>
 #include <itpp/base/algebra/svd.h>
 #include <itpp/itexports.h>
+#include <algorithm>
 
 namespace itpp
 {
diff --git a/itpp/base/svec.h b/itpp/base/svec.h
index d105b2f..3a2c15b 100644
--- a/itpp/base/svec.h
+++ b/itpp/base/svec.h
@@ -33,6 +33,7 @@
 #include <itpp/base/math/min_max.h>
 #include <cstdlib>
 #include <itpp/itexports.h>
+#include <algorithm>
 
 namespace itpp
 {
diff --git a/itpp/comm/siso_dem.cpp b/itpp/comm/siso_dem.cpp
index 666f38c..4fa03fd 100644
--- a/itpp/comm/siso_dem.cpp
+++ b/itpp/comm/siso_dem.cpp
@@ -32,6 +32,12 @@
 #define INFINITY std::numeric_limits<double>::infinity()
 #endif
 
+#if _MSC_VER
+//suppress warnings regarding the use infinity in fp arithmetic
+#pragma warning(disable: 4056)
+#endif
+
+
 namespace itpp
 {
 void SISO::find_half_const(int &select_half, itpp::vec &re_part, itpp::bmat &re_bin_part, itpp::vec &im_part, itpp::bmat &im_bin_part)
diff --git a/itpp/comm/siso_eq.cpp b/itpp/comm/siso_eq.cpp
index 856c01a..6553b33 100644
--- a/itpp/comm/siso_eq.cpp
+++ b/itpp/comm/siso_eq.cpp
@@ -32,6 +32,12 @@
 #define INFINITY std::numeric_limits<double>::infinity()
 #endif
 
+#if _MSC_VER
+//suppress warnings regarding the use infinity in fp arithmetic
+#pragma warning(disable: 4056)
+#pragma warning(disable: 4756)
+#endif
+
 namespace itpp
 {
 void SISO::gen_chtrellis(void)
diff --git a/itpp/comm/siso_mud.cpp b/itpp/comm/siso_mud.cpp
index 60aa6d3..a7ff735 100644
--- a/itpp/comm/siso_mud.cpp
+++ b/itpp/comm/siso_mud.cpp
@@ -32,6 +32,13 @@
 #define INFINITY std::numeric_limits<double>::infinity()
 #endif
 
+#if _MSC_VER
+//suppress warnings regarding the use infinity in fp arithmetic
+#pragma warning(disable: 4056)
+#pragma warning(disable: 4756)
+#endif
+
+
 namespace itpp
 {
 void SISO::descrambler(itpp::vec &extrinsic_coded, itpp::vec &extrinsic_data, const itpp::vec &intrinsic_coded, const itpp::vec &apriori_data)
diff --git a/itpp/comm/siso_nsc.cpp b/itpp/comm/siso_nsc.cpp
index ca7d19a..12ca62a 100644
--- a/itpp/comm/siso_nsc.cpp
+++ b/itpp/comm/siso_nsc.cpp
@@ -33,6 +33,12 @@
 #define INFINITY std::numeric_limits<double>::infinity()
 #endif
 
+#if _MSC_VER
+//suppress warnings regarding the use infinity in fp arithmetic
+#pragma warning(disable: 4056)
+#pragma warning(disable: 4756)
+#endif
+
 namespace itpp
 {
 void SISO::gen_nsctrellis(void)
diff --git a/itpp/comm/siso_rsc.cpp b/itpp/comm/siso_rsc.cpp
index e5003c7..9e3eea1 100644
--- a/itpp/comm/siso_rsc.cpp
+++ b/itpp/comm/siso_rsc.cpp
@@ -29,6 +29,13 @@
 #include <itpp/comm/siso.h>
 #include <itpp/base/itcompat.h>
 #include <limits>
+
+#if _MSC_VER
+//suppress warnings regarding the use infinity in fp arithmetic
+#pragma warning(disable: 4056)
+#pragma warning(disable: 4756)
+#endif
+
 #ifndef INFINITY
 #define INFINITY std::numeric_limits<double>::infinity()
 #endif
 
 #Remove this patch if the source has fixed these issues.
