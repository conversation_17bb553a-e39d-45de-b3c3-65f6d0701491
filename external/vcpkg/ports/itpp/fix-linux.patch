diff --git a/itpp/base/random_dsfmt.h b/itpp/base/random_dsfmt.h
index ccbf182..de819fb 100644
--- a/itpp/base/random_dsfmt.h
+++ b/itpp/base/random_dsfmt.h
@@ -299,7 +299,12 @@ private:
    */
   static void do_recursion(typename Context::w128_t *r, typename Context::w128_t *a, typename Context::w128_t *b, typename Context::w128_t *lung) {
 #if defined(__SSE2__)
+
+#ifdef __linux__
+#define SSE2_SHUFF 0x1bU
+#else
     const unsigned int SSE2_SHUFF = 0x1bU;
+#endif
 
     __m128i x = a->si;
     __m128i z = _mm_slli_epi64(x, SL1);
