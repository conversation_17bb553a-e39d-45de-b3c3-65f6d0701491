{"name": "symengine", "version": "0.11.2", "port-version": 2, "description": "SymEngine is a fast symbolic manipulation library", "homepage": "https://github.com/symengine/symengine", "license": "BSD-3-<PERSON><PERSON>", "supports": "!uwp", "dependencies": ["boost-math", "boost-random", {"name": "symengine", "default-features": false, "features": ["integer-class-flint"]}, {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "default-features": ["arb", "llvm", "mpfr"], "features": {"arb": {"description": "Build with arb", "dependencies": ["arb"]}, "flint": {"description": "Build with flint", "dependencies": ["flint"]}, "integer-class-flint": {"description": "Use flint integer class", "dependencies": ["flint"]}, "llvm": {"description": "Build with LLVM", "dependencies": [{"name": "llvm", "default-features": false}]}, "mpfr": {"description": "Build with mpfr", "dependencies": ["mpfr"]}, "tcmalloc": {"description": "Build with tcmalloc", "dependencies": ["gperftools"]}}}