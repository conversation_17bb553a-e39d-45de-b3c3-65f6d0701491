{"name": "cppzmq", "version": "4.10.0", "description": "Header-only C++ binding for ZeroMQ", "homepage": "https://github.com/zeromq/cppzmq", "license": "MIT", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}, "zeromq"], "features": {"draft": {"description": "Build and install draft", "dependencies": [{"name": "zeromq", "default-features": false, "features": ["draft"]}]}}}