diff --git a/core/CMakeLists.txt b/core/CMakeLists.txt
index ab49461..9d53dce 100644
--- a/core/CMakeLists.txt
+++ b/core/CMakeLists.txt
@@ -43,6 +43,8 @@ endif()
 if (IPL_OS_WINDOWS)
     if (CMAKE_GENERATOR_PLATFORM STREQUAL "ARM64")
         set(IPL_CPU_ARMV8 TRUE)
+    elseif (CMAKE_SYSTEM_PROCESSOR STREQUAL "ARM64")
+        set(IPL_CPU_ARMV8 TRUE)
     elseif (CMAKE_SIZEOF_VOID_P EQUAL 8)
         set(IPL_CPU_X64 TRUE)
     else()
