diff --git a/core/CMakeLists.txt b/core/CMakeLists.txt
index ab49461..8b81aca 100644
--- a/core/CMakeLists.txt
+++ b/core/CMakeLists.txt
@@ -192,7 +192,7 @@ endif()
 
 # macOS flags
 if (IPL_OS_MACOS)
-    set(CMAKE_OSX_ARCHITECTURES "x86_64;arm64")
+    set(CMAKE_OSX_ARCHITECTURES "${VCPKG_MACOS_ARCH}")
     set(CMAKE_OSX_DEPLOYMENT_TARGET "10.13")
     add_compile_options(-Wno-extern-c-compat) # Suppress warning about empty API structs.
     add_compile_options(-Wno-unknown-attributes) # Suppress warning in FlatBuffers.
@@ -240,7 +240,7 @@ if (STEAMAUDIO_ENABLE_FFTS)
 endif()
 
 if (NOT FFT_LIBRARY)
-    find_package(PFFFT REQUIRED)
+    find_package(PFFFT CONFIG REQUIRED)
     set(FFT_LIBRARY PFFFT)
 endif()
 
@@ -252,8 +252,8 @@ if (STEAMAUDIO_ENABLE_MKL)
     endif()
 endif()
 
-find_package(MySOFA REQUIRED)
-find_package(FlatBuffers REQUIRED)
+find_package(mysofa CONFIG REQUIRED)
+find_package(FlatBuffers CONFIG REQUIRED)
 
 if (STEAMAUDIO_ENABLE_EMBREE)
     find_package(ISPC 1.12 EXACT)
diff --git a/core/src/core/CMakeLists.txt b/core/src/core/CMakeLists.txt
index 7c9226f..56c4c44 100644
--- a/core/src/core/CMakeLists.txt
+++ b/core/src/core/CMakeLists.txt
@@ -176,7 +180,8 @@ endif()
 #
 
 add_library(hrtf OBJECT hrtf.cpp)
-target_include_directories(hrtf PUBLIC ${CMAKE_CURRENT_SOURCE_DIR})
+target_include_directories(hrtf PUBLIC 
+  "$<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}>")
 
 
 #
@@ -582,7 +587,8 @@ if (STEAMAUDIO_BUILD_CSHARP_BINDINGS)
 endif()
 
 # This is needed so we can include generated headers
-target_include_directories(core PUBLIC ${CMAKE_CURRENT_BINARY_DIR})
+target_include_directories(core PUBLIC 
+  "$<BUILD_INTERFACE:${CMAKE_CURRENT_BINARY_DIR}>")
 
 if (IPL_OS_WINDOWS)
     target_link_libraries(core PUBLIC delayimp)
@@ -596,7 +602,7 @@ if (IPL_OS_LINUX)
     target_link_libraries(core PUBLIC -Wl,--start-group)
 endif()
 
-target_link_libraries(core PUBLIC FlatBuffers::FlatBuffers)
+target_link_libraries(core PUBLIC flatbuffers::flatbuffers)
 
 if (STEAMAUDIO_ENABLE_IPP AND (IPL_OS_WINDOWS OR IPL_OS_LINUX OR IPL_OS_MACOS))
     target_link_libraries(core PUBLIC IPP::IPP)
@@ -610,10 +616,10 @@ if (FFT_LIBRARY STREQUAL "IPP")
 elseif (FFT_LIBRARY STREQUAL "FFTS")
     target_link_libraries(core PUBLIC FFTS::FFTS)
 elseif (FFT_LIBRARY STREQUAL "PFFFT")
-    target_link_libraries(core PUBLIC PFFFT::PFFFT)
+    target_link_libraries(core PUBLIC pffft::pffft)
 endif()
 
-target_link_libraries(core PUBLIC MySOFA::MySOFA)
+target_link_libraries(core PUBLIC $<IF:$<TARGET_EXISTS:mysofa::mysofa-shared>,mysofa::mysofa-shared,mysofa::mysofa-static>)
 add_dependencies(core fbschemas)
 
 if (STEAMAUDIO_ENABLE_EMBREE)
@@ -639,7 +645,7 @@ source_group("OpenCL Files" FILES ${CL_SOURCE})
 
 target_compile_definitions(core PRIVATE STEAMAUDIO_BUILDING_CORE)
 
-target_precompile_headers(core PUBLIC pch.h)
+target_precompile_headers(core PRIVATE pch.h)
 
 
 #
@@ -671,7 +677,7 @@ if (IPL_OS_LINUX)
 endif()
 
 if (IPL_OS_WINDOWS AND IPL_CPU_X64 AND BUILD_SHARED_LIBS)
-    set_target_properties(phonon PROPERTIES LINK_FLAGS "/DELAYLOAD:opencl.dll /DELAYLOAD:gpuutilities.dll /DELAYLOAD:trueaudionext.dll")
+    #set_target_properties(phonon PROPERTIES LINK_FLAGS "/DELAYLOAD:opencl.dll /DELAYLOAD:gpuutilities.dll /DELAYLOAD:trueaudionext.dll")
 endif()
 
 if (IPL_OS_LINUX AND BUILD_SHARED_LIBS AND (NOT IPL_CPU_ARMV8))
@@ -729,8 +735,8 @@ if (NOT FMOD_LIB_DIR STREQUAL "")
             COMMAND     ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/phonon.h ${FMOD_INCLUDE_DIR}
             COMMAND     ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_BINARY_DIR}/phonon_version.h ${FMOD_INCLUDE_DIR}
             COMMAND     ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/phonon_interfaces.h ${FMOD_INCLUDE_DIR}
-            COMMAND     ${CMAKE_COMMAND} -E copy $<TARGET_FILE:PFFFT::PFFFT> ${FMOD_LIB_DIR}
-            COMMAND     ${CMAKE_COMMAND} -E copy $<TARGET_FILE:MySOFA::MySOFA> ${FMOD_LIB_DIR}
+            COMMAND     ${CMAKE_COMMAND} -E copy $<TARGET_FILE:pffft::pffft> ${FMOD_LIB_DIR}
+            COMMAND     ${CMAKE_COMMAND} -E copy $<TARGET_FILE:$<IF:$<TARGET_EXISTS:mysofa::mysofa-shared>,mysofa::mysofa-shared,mysofa::mysofa-static>> ${FMOD_LIB_DIR}
             COMMAND     ${CMAKE_COMMAND} -E copy $<TARGET_FILE:phonon> ${FMOD_LIB_DIR}
         )
     else()
@@ -851,8 +857,8 @@ if (NOT UNITY_PLUGIN_DIR STREQUAL "")
             COMMAND     ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_BINARY_DIR}/phonon_version.h ${UNITY_INCLUDE_DIR}
             COMMAND     ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/phonon_interfaces.h ${UNITY_INCLUDE_DIR}
             COMMAND     ${CMAKE_COMMAND} -E copy $<TARGET_FILE:phonon> ${UNITY_LIB_DIR}
-            COMMAND     ${CMAKE_COMMAND} -E copy $<TARGET_FILE:PFFFT::PFFFT> ${UNITY_PLUGIN_DIR}
-            COMMAND     ${CMAKE_COMMAND} -E copy $<TARGET_FILE:MySOFA::MySOFA> ${UNITY_PLUGIN_DIR}
+            COMMAND     ${CMAKE_COMMAND} -E copy $<TARGET_FILE:pffft::pffft> ${UNITY_PLUGIN_DIR}
+            COMMAND     ${CMAKE_COMMAND} -E copy $<TARGET_FILE:$<IF:$<TARGET_EXISTS:mysofa::mysofa-shared>,mysofa::mysofa-shared,mysofa::mysofa-static>> ${UNITY_PLUGIN_DIR}
             COMMAND     ${CMAKE_COMMAND} -E copy $<TARGET_FILE:phonon> ${UNITY_PLUGIN_DIR}
         )
     else()
@@ -913,8 +919,8 @@ if (NOT UNREAL_PLUGIN_DIR STREQUAL "")
             COMMAND     ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/phonon.h ${UNREAL_INCLUDE_DIR}
             COMMAND     ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_BINARY_DIR}/phonon_version.h ${UNREAL_INCLUDE_DIR}
             COMMAND     ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/phonon_interfaces.h ${UNREAL_INCLUDE_DIR}
-            COMMAND     ${CMAKE_COMMAND} -E copy $<TARGET_FILE:PFFFT::PFFFT> ${UNREAL_PLUGIN_DIR}
-            COMMAND     ${CMAKE_COMMAND} -E copy $<TARGET_FILE:MySOFA::MySOFA> ${UNREAL_PLUGIN_DIR}
+            COMMAND     ${CMAKE_COMMAND} -E copy $<TARGET_FILE:pffft::pffft> ${UNREAL_PLUGIN_DIR}
+            COMMAND     ${CMAKE_COMMAND} -E copy $<TARGET_FILE:$<IF:$<TARGET_EXISTS:mysofa::mysofa-shared>,mysofa::mysofa-shared,mysofa::mysofa-static>> ${UNREAL_PLUGIN_DIR}
             COMMAND     ${CMAKE_COMMAND} -E copy $<TARGET_FILE:phonon> ${UNREAL_PLUGIN_DIR}
         )
     elseif (IPL_OS_MACOS)
@@ -953,30 +959,34 @@ get_bin_subdir(IPL_BIN_SUBDIR)
 
 install(
     TARGETS             phonon
-    ARCHIVE DESTINATION lib/${IPL_BIN_SUBDIR}
-    LIBRARY DESTINATION lib/${IPL_BIN_SUBDIR}
-    RUNTIME DESTINATION lib/${IPL_BIN_SUBDIR}
+    EXPORT steam-audio_exports
+    ARCHIVE DESTINATION lib
+    LIBRARY DESTINATION lib
+    RUNTIME DESTINATION bin
 )
 
 if (IPL_OS_MACOS)
     install(
         TARGETS             phonon_bundle
-        ARCHIVE DESTINATION lib/${IPL_BIN_SUBDIR}
-        LIBRARY DESTINATION lib/${IPL_BIN_SUBDIR}
-        RUNTIME DESTINATION lib/${IPL_BIN_SUBDIR}
+        EXPORT steam-audio_exports
+        ARCHIVE DESTINATION lib
+        LIBRARY DESTINATION lib
+        RUNTIME DESTINATION bin
     )
 endif()
 
-if (IPL_OS_WINDOWS)
-    install(
-        FILES       ${CMAKE_CURRENT_BINARY_DIR}/$<CONFIG>/phonon.pdb
-        DESTINATION symbols/${IPL_BIN_SUBDIR}
-    )
-elseif (IPL_OS_LINUX AND NOT IPL_CPU_ARMV8)
-    install(
-        FILES       ${CMAKE_CURRENT_BINARY_DIR}/libphonon.so.dbg
-        DESTINATION symbols/${IPL_BIN_SUBDIR}
-    )
+if(BUILD_SHARED_LIBS)
+  if (IPL_OS_WINDOWS)
+      install(
+          FILES       ${CMAKE_CURRENT_BINARY_DIR}/phonon.pdb
+          DESTINATION bin
+      )
+  elseif (IPL_OS_LINUX AND NOT IPL_CPU_ARMV8)
+      install(
+          FILES       ${CMAKE_CURRENT_BINARY_DIR}/libphonon.so.dbg
+          DESTINATION bin
+      )
+  endif()
 endif()
 
 install(
@@ -986,6 +996,7 @@ install(
     DESTINATION include
 )
 
+#[[
 if (IPL_OS_WINDOWS AND IPL_CPU_X64)
     install(
         FILES       ${CMAKE_HOME_DIRECTORY}/deps/trueaudionext/bin/windows-x64/$<LOWER_CASE:$<CONFIG>>/TrueAudioNext.dll
@@ -993,5 +1004,30 @@ if (IPL_OS_WINDOWS AND IPL_CPU_X64)
         DESTINATION lib/${IPL_BIN_SUBDIR}
     )
 endif()
+]]#
+
+export(EXPORT steam-audio_exports
+  NAMESPACE unofficial::steam-audio::
+  FILE ${CMAKE_CURRENT_BINARY_DIR}/steam-audioTargets.cmake
+)
+
+# required to resolve object libraries on import
+install(TARGETS core hrtf EXPORT steam-audio_exports)
+
+install(EXPORT steam-audio_exports
+  NAMESPACE unofficial::steam-audio::
+  FILE steam-audioTargets.cmake
+  DESTINATION share/steam-audio
+)
+
+include(CMakePackageConfigHelpers)
+configure_package_config_file(steam-audio-config.cmake.in
+  ${CMAKE_CURRENT_BINARY_DIR}/steam-audio-config.cmake
+  INSTALL_DESTINATION share/steam-audio)
+
+install(FILES ${CMAKE_CURRENT_BINARY_DIR}/steam-audio-config.cmake
+        DESTINATION share/steam-audio )
+
+
 
 include(CMakeListsInternal.txt OPTIONAL)
diff --git a/core/src/core/float4.h b/core/src/core/float4.h
index a221372..03428db 100644
--- a/core/src/core/float4.h
+++ b/core/src/core/float4.h
@@ -41,7 +41,7 @@ namespace float4
 
 }
 
-#if defined(IPL_OS_WINDOWS)
+#if defined(IPL_OS_WINDOWS) && !defined(__clang__)
 
 namespace ipl {
 
diff --git a/core/src/core/pffft_fft.cpp b/core/src/core/pffft_fft.cpp
index 0a37114..4f5debe 100644
--- a/core/src/core/pffft_fft.cpp
+++ b/core/src/core/pffft_fft.cpp
@@ -16,7 +16,7 @@
 
 #include "fft.h"
 
-#include <pffft.h>
+#include <pffft/pffft.h>
 
 #include "array.h"
 #include "array_math.h"
diff --git a/core/src/core/steam-audio-config.cmake.in b/core/src/core/steam-audio-config.cmake.in
new file mode 100644
index 0000000..776f7aa
--- /dev/null
+++ b/core/src/core/steam-audio-config.cmake.in
@@ -0,0 +1,7 @@
+@PACKAGE_INIT@
+
+include(CMakeFindDependencyMacro)
+
+include(${CMAKE_CURRENT_LIST_DIR}/steam-audioTargets.cmake)
+
+check_required_components(steam-audio)
\ No newline at end of file
