diff --git a/config/compiler/BUILD.gn b/config/compiler/BUILD.gn
index 1904a2559..e66586c88 100644
--- a/config/compiler/BUILD.gn
+++ b/config/compiler/BUILD.gn
@@ -1571,6 +1571,7 @@ config("default_warnings") {
       # Disables.
       "-Wno-missing-field-initializers",  # "struct foo f = {0};"
       "-Wno-unused-parameter",  # Unused function parameters.
+      "-Wno-invalid-offsetof", # Use of "conditionally-supported" offsetof in c++17
     ]
   }
 
@@ -1987,8 +1988,17 @@ config("no_incompatible_pointer_warnings") {
 # Shared settings for both "optimize" and "optimize_max" configs.
 # IMPORTANT: On Windows "/O1" and "/O2" must go before the common flags.
 if (is_win) {
-  common_optimize_on_cflags = [
-    "/Ob2",  # Both explicit and auto inlining.
+  common_optimize_on_cflags = []
+  if(is_clang) {
+    common_optimize_on_cflags += [
+     "/Ob2",  # Both explicit and auto inlining.
+    ]
+  } else {
+    common_optimize_on_cflags += [
+      "/Ob3",  # Both explicit and auto inlining.
+    ]
+  }
+  common_optimize_on_cflags += [
     "/Oy-",  # Disable omitting frame pointers, must be after /O2.
     "/Zc:inline",  # Remove unreferenced COMDAT (faster links).
   ]
diff --git a/config/linux/pkg-config.py b/config/linux/pkg-config.py
index 5adf70cc3..dab159f98 100755
--- a/config/linux/pkg-config.py
+++ b/config/linux/pkg-config.py
@@ -41,6 +41,11 @@ from optparse import OptionParser
 # Additionally, you can specify the option --atleast-version. This will skip
 # the normal outputting of a dictionary and instead print true or false,
 # depending on the return value of pkg-config for the given package.
+#
+# --pkg_config_libdir=<path> allows direct override
+# of the PKG_CONFIG_LIBDIR environment library.
+#
+# --full-path-libs causes lib names to include their full path.
 
 
 def SetConfigPath(options):
@@ -105,11 +110,32 @@ def RewritePath(path, strip_prefix, sysroot):
     return path
 
 
+flag_regex = re.compile("(-.)(.+)")
+
+
+def FlagReplace(matchobj):
+  if matchobj.group(1) == '-I':
+     return matchobj.group(1) + subprocess.check_output([u'cygpath',u'-w',matchobj.group(2)]).strip().decode("utf-8")
+  if matchobj.group(1) == '-L':
+     return matchobj.group(1) + subprocess.check_output([u'cygpath',u'-w',matchobj.group(2)]).strip().decode("utf-8")
+  if matchobj.group(1) == '-l':
+     return matchobj.group(1) + matchobj.group(2) + '.lib'
+  return matchobj.group(0)
+
+
+def ConvertGCCToMSVC(flags):
+  """Rewrites GCC flags into MSVC flags."""
+  # need a better way to determine mingw vs msvc build
+  if 'win32' not in sys.platform or "GCC" in sys.version:
+    return flags
+  return [ flag_regex.sub(FlagReplace,flag) for flag in flags]
+
+
 def main():
   # If this is run on non-Linux platforms, just return nothing and indicate
   # success. This allows us to "kind of emulate" a Linux build from other
   # platforms.
-  if "linux" not in sys.platform:
+  if "linux" not in sys.platform and 'win32' not in sys.platform:
     print("[[],[],[],[],[]]")
     return 0
 
@@ -128,6 +154,9 @@ def main():
   parser.add_option('--dridriverdir', action='store_true', dest='dridriverdir')
   parser.add_option('--version-as-components', action='store_true',
                     dest='version_as_components')
+  parser.add_option('--pkg_config_libdir', action='store', dest='pkg_config_libdir',
+                    type='string')
+  parser.add_option('--full-path-libs', action='store_true', dest='full_path_libs')
   (options, args) = parser.parse_args()
 
   # Make a list of regular expressions to strip out.
@@ -144,6 +173,10 @@ def main():
   else:
     prefix = ''
 
+  # Override PKG_CONFIG_LIBDIR
+  if options.pkg_config_libdir:
+    os.environ['PKG_CONFIG_LIBDIR'] = options.pkg_config_libdir
+
   if options.atleast_version:
     # When asking for the return value, just run pkg-config and print the return
     # value, no need to do other work.
@@ -203,7 +236,7 @@ def main():
   # For now just split on spaces to get the args out. This will break if
   # pkgconfig returns quoted things with spaces in them, but that doesn't seem
   # to happen in practice.
-  all_flags = flag_string.strip().split(' ')
+  all_flags = ConvertGCCToMSVC(flag_string.strip().split(' '))
 
 
   sysroot = options.sysroot
@@ -220,7 +253,10 @@ def main():
       continue;
 
     if flag[:2] == '-l':
-      libs.append(RewritePath(flag[2:], prefix, sysroot))
+      library = RewritePath(flag[2:], prefix, sysroot)
+      # Skip math library on MSVC
+      if library != 'm.lib':
+        libs.append(library)
     elif flag[:2] == '-L':
       lib_dirs.append(RewritePath(flag[2:], prefix, sysroot))
     elif flag[:2] == '-I':
@@ -237,6 +273,14 @@ def main():
     else:
       cflags.append(flag)
 
+  if options.full_path_libs:
+    full_path_libs = []
+    for lib_dir in lib_dirs:
+      for lib in libs:
+        if os.path.isfile(lib_dir+"/"+lib):
+          full_path_libs.append(lib_dir+"/"+lib)
+    libs = full_path_libs
+
   # Output a GN array, the first one is the cflags, the second are the libs. The
   # JSON formatter prints GN compatible lists when everything is a list of
   # strings.
diff --git a/config/linux/pkg_config.gni b/config/linux/pkg_config.gni
index 428e44ac0..a0d2175ee 100644
--- a/config/linux/pkg_config.gni
+++ b/config/linux/pkg_config.gni
@@ -45,6 +45,9 @@ declare_args() {
   # in similar fashion by setting the `system_libdir` variable in the build's
   # args.gn file to 'lib' or 'lib64' as appropriate for the target architecture.
   system_libdir = "lib"
+
+  # Allow directly overriding the PKG_CONFIG_LIBDIR enviroment variable
+  pkg_config_libdir = ""
 }
 
 pkg_config_script = "//build/config/linux/pkg-config.py"
@@ -87,6 +90,17 @@ if (host_pkg_config != "") {
   host_pkg_config_args = pkg_config_args
 }
 
+if (pkg_config_libdir != "") {
+  pkg_config_args += [
+    "--pkg_config_libdir",
+    pkg_config_libdir,
+  ]
+  host_pkg_config_args += [
+    "--pkg_config_libdir",
+    pkg_config_libdir,
+  ]
+}
+
 template("pkg_config") {
   assert(defined(invoker.packages),
          "Variable |packages| must be defined to be a list in pkg_config.")
diff --git a/util/lastchange.py b/util/lastchange.py
index 02a36642b..78934f1b0 100755
--- a/util/lastchange.py
+++ b/util/lastchange.py
@@ -192,7 +192,10 @@ def GetGitTopDirectory(source_dir):
   Returns:
     The output of "git rev-parse --show-toplevel" as a string
   """
-  return _RunGitCommand(source_dir, ['rev-parse', '--show-toplevel'])
+  directory = _RunGitCommand(source_dir, ['rev-parse', '--show-toplevel'])
+  if "GCC" in sys.version and sys.platform=='win32':
+    return subprocess.check_output(["cygpath", "-w", directory]).strip(b"\n").decode()
+  return directory
 
 
 def WriteIfChanged(file_name, contents):
