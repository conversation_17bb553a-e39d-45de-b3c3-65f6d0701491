import("//build/config/linux/pkg_config.gni")

declare_args() {
  # Tells icu to load an external data file rather than rely on the icudata
  # being linked directly into the binary.
  icu_use_data_file = true
  # If true, compile icu into a standalone static library. Currently this is
  # only useful on Chrome OS.
  icu_disable_thin_archive = false
}

pkg_config("system_icui18n") {
  packages = [ "icu-i18n" ]
  if (is_win) {
    extra_args = ["--full-path-libs"] # Workaround the WinSDK having an older version of ICU
  }
}

pkg_config("system_icuuc") {
  packages = [ "icu-uc" ]
  if (is_win) {
    extra_args = ["--full-path-libs"] # Workaround the WinSDK having an older version of ICU
  }
}

group("icu") {
  public_deps = [
    ":icui18n",
    ":icuuc",
  ]
}

config("icu_config") {
  defines = [
    "USING_SYSTEM_ICU=1",
  ]
  if(is_win){
    if(!is_component_build) {
      ldflags = ["/ignore:4099"] # needed on CI builds
    }
    defines += [ "UCHAR_TYPE=wchar_t" ]
  }
  else{
    defines += [ "UCHAR_TYPE=uint16_t" ]
  }
  if (icu_use_data_file) {
    defines += [ "ICU_UTIL_DATA_IMPL=ICU_UTIL_DATA_FILE" ]
  } else {
    defines += [ "ICU_UTIL_DATA_IMPL=ICU_UTIL_DATA_STATIC" ]
  }
}

source_set("icui18n") {
  public_configs = [
    ":icu_config",
    ":system_icui18n"
  ]
}

source_set("icuuc") {
  public_configs = [
    ":icu_config",
    ":system_icuuc"
  ]
}

source_set("icudata") {
  public_configs = [
    ":icu_config",
  ]
}
