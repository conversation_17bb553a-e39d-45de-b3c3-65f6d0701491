{"name": "fltk", "version": "1.3.11", "description": "FLTK (pronounced fulltick) is a cross-platform C++ GUI toolkit for UNIX/Linux (X11), Microsoft Windows, and MacOS X. FLTK provides modern GUI functionality without the bloat and supports 3D graphics via OpenGL and its built-in GLUT emulation.", "homepage": "https://www.fltk.org/", "license": null, "supports": "!uwp", "dependencies": [{"name": "fltk", "host": true, "default-features": false}, {"name": "fontconfig", "platform": "!osx"}, {"name": "freetype", "default-features": false}, "libjpeg-turbo", "libpng", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}, "zlib"], "default-features": ["opengl"], "features": {"opengl": {"description": "OpenGL support", "dependencies": ["opengl"]}}}