diff --git a/CMake/fl_add_library.cmake b/CMake/fl_add_library.cmake
index 7e3490b..682a000 100644
--- a/CMake/fl_add_library.cmake
+++ b/CMake/fl_add_library.cmake
@@ -39,6 +39,11 @@ macro (FL_ADD_LIBRARY LIBNAME LIBTYPE LIBFILES)
 
   target_include_directories(${TARGET_NAME} PUBLIC $<INSTALL_INTERFACE:include>)
 
+    if (APPLE)
+        find_library(Cocoa Cocoa)
+        target_link_libraries(${TARGET_NAME} $<$<PLATFORM_ID:Darwin>:${Cocoa}>)
+    endif()
+
   # target properties for all libraries
 
   set_target_properties(${TARGET_NAME}
