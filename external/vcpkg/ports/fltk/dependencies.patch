diff --git a/CMake/options.cmake b/CMake/options.cmake
index d1e299f..d20e9c8 100644
--- a/CMake/options.cmake
+++ b/CMake/options.cmake
@@ -101,11 +101,14 @@ endif ()
 #######################################################################
 
 if (OPTION_USE_SYSTEM_ZLIB)
-  find_package (ZLIB)
+  find_package(ZLIB REQUIRED)
+  set(LIB_zlib "${ZLIB_LIBRARIES}" CACHE INTERNAL "")
 endif ()
 
 if (OPTION_USE_SYSTEM_LIBPNG)
-  find_package (PNG)
+  find_package(PNG REQUIRED)
+  set(HAVE_PNG_H "${PNG_FOUND}")
+  set(LIB_png "${PNG_LIBRARIES}" CACHE INTERNAL "")
 endif ()
 
 # If we use the system zlib, we must also use the system png zlib and vice versa
@@ -148,7 +151,8 @@ set (HAVE_LIBZ 1)
 #######################################################################
 
 if (OPTION_USE_SYSTEM_LIBJPEG)
-  find_package (JPEG)
+  find_package(JPEG REQUIRED)
+  set(LIB_jpeg "${JPEG_LIBRARIES}" CACHE INTERNAL "")
 endif ()
 
 if (OPTION_USE_SYSTEM_LIBJPEG AND JPEG_FOUND)
@@ -228,7 +232,7 @@ endif (APPLE)
 # find X11 libraries and headers
 set (PATH_TO_XLIBS)
 if (NOT APPLE AND NOT WIN32)
-  include (FindX11)
+  find_package(X11)
   if (X11_FOUND)
     set (USE_X11 1)
     list (APPEND FLTK_LDLIBS -lX11)
@@ -351,7 +355,7 @@ if (HAVE_GL)
 endif (HAVE_GL)
 
 if (OPTION_USE_GL)
-  include (FindOpenGL)
+  find_package(OpenGL REQUIRED)
   if (APPLE)
     set (HAVE_GL_GLU_H ${HAVE_OPENGL_GLU_H})
   endif (APPLE)
@@ -421,7 +425,7 @@ set (FLTK_PTHREADS_FOUND FALSE)
 
 if (OPTION_USE_THREADS)
 
-  include (FindThreads)
+  find_package(Threads REQUIRED)
 
   if (CMAKE_HAVE_THREADS_LIBRARY)
     add_definitions ("-D_THREAD_SAFE -D_REENTRANT")
diff --git a/CMake/resources.cmake b/CMake/resources.cmake
index a763fb6..5fdb3d5 100644
--- a/CMake/resources.cmake
+++ b/CMake/resources.cmake
@@ -39,7 +39,7 @@ endmacro (fl_find_header)
 # Include FindPkgConfig for later use of pkg-config
 #######################################################################
 
-include (FindPkgConfig)
+find_package(PkgConfig)
 
 # fl_debug_var (PKG_CONFIG_FOUND)
 # fl_debug_var (PKG_CONFIG_EXECUTABLE)
@@ -131,8 +131,7 @@ mark_as_advanced (HAVE_X11_XREGION_H)
 
 # where to find freetype headers
 
-find_path (FREETYPE_PATH freetype.h PATH_SUFFIXES freetype2)
-find_path (FREETYPE_PATH freetype/freetype.h PATH_SUFFIXES freetype2)
+set(FREETYPE_PATH "" CACHE INTERNAL "Obsolete")
 
 if (FREETYPE_PATH)
   include_directories (${FREETYPE_PATH})
@@ -142,11 +141,12 @@ mark_as_advanced (FREETYPE_PATH)
 
 #######################################################################
 # libraries
-find_library (LIB_dl dl)
+set(LIB_dl "${CMAKE_DL_LIBS}" CACHE STRING "")
 if (NOT APPLE)
-  find_library (LIB_fontconfig fontconfig)
+  find_package(Fontconfig REQUIRED)
+  set(LIB_fontconfig "${Fontconfig_LIBRARIES}" CACHE INTERNAL "")
 endif (NOT APPLE)
-find_library (LIB_freetype freetype)
+set(LIB_freetype "" CACHE INTERNAL "Obsolete")
 find_library (LIB_GL GL)
 find_library (LIB_MesaGL MesaGL)
 find_library (LIB_GLEW NAMES GLEW glew32)
