ffmpeg provides CMake integration:

  find_package(FFMPEG REQUIRED)
  target_include_directories(main PRIVATE ${FFMPEG_INCLUDE_DIRS})
  target_link_directories(main PRIVATE ${FFMPEG_LIBRARY_DIRS})
  target_link_libraries(main PRIVATE ${FFMPEG_LIBRARIES})

ffmpeg provides pkg-config modules:

  # FFmpeg codec library
  libavcodec

  # FFmpeg device handling library
  libavdevice

  # FFmpeg audio/video filtering library
  libavfilter

  # FFmpeg container format library
  libavformat

  # FFmpeg utility library
  libavutil

  # FFmpeg audio resampling library
  libswresample

  # FFmpeg image rescaling library
  libswscale
