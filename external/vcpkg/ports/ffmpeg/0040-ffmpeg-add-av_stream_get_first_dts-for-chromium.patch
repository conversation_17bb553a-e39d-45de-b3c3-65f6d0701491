diff --git a/libavformat/avformat.h b/libavformat/avformat.h
index cd7b0d941c..b4a6dce885 100644
--- a/libavformat/avformat.h
+++ b/libavformat/avformat.h
@@ -1169,7 +1169,11 @@ typedef struct AVStreamGroup {
 } AVStreamGroup;
 
 struct AVCodecParserContext *av_stream_get_parser(const AVStream *s);
 
+// Chromium: We use the internal field first_dts vvv
+int64_t    av_stream_get_first_dts(const AVStream *st);
+// Chromium: We use the internal field first_dts ^^^
+
 #define AV_PROGRAM_RUNNING 1
 
 /**
diff --git a/libavformat/mux_utils.c b/libavformat/mux_utils.c
index de7580c32d..0ef0fe530e 100644
--- a/libavformat/mux_utils.c
+++ b/libavformat/mux_utils.c
@@ -29,7 +29,14 @@ #include "avformat.h"
 #include "avio.h"
 #include "internal.h"
 #include "mux.h"
 
+// Chromium: We use the internal field first_dts vvv
+int64_t av_stream_get_first_dts(const AVStream *st)
+{
+  return cffstream(st)->first_dts;
+}
+// Chromium: We use the internal field first_dts ^^^
+
 int avformat_query_codec(const AVOutputFormat *ofmt, enum AVCodecID codec_id,
                          int std_compliance)
 {
