diff --git a/libavcodec/x86/mlpdsp.asm b/libavcodec/x86/mlpdsp.asm
index 3dc641e..609b834 100644
--- a/libavcodec/x86/mlpdsp.asm
+++ b/libavcodec/x86/mlpdsp.asm
@@ -23,7 +23,9 @@
 
 SECTION .text
 
-%if ARCH_X86_64
+%ifn ARCH_X86_64
+mlpdsp_placeholder: times 4 db 0
+%else
 
 %macro SHLX 2
 %if cpuflag(bmi2)
diff --git a/libavcodec/x86/proresdsp.asm b/libavcodec/x86/proresdsp.asm
index 65c9fad..5ad73f3 100644
--- a/libavcodec/x86/proresdsp.asm
+++ b/libavcodec/x86/proresdsp.asm
@@ -24,7 +24,10 @@
 
 %include "libavutil/x86/x86util.asm"
 
-%if ARCH_X86_64
+%ifn ARCH_X86_64
+SECTION .rdata
+proresdsp_placeholder: times 4 db 0
+%else
 
 SECTION_RODATA
 
diff --git a/libavcodec/x86/vvc/vvc_mc.asm b/libavcodec/x86/vvc/vvc_mc.asm
index 30aa97c..3975f98 100644
--- a/libavcodec/x86/vvc/vvc_mc.asm
+++ b/libavcodec/x86/vvc/vvc_mc.asm
@@ -31,7 +31,9 @@
 
 SECTION_RODATA 32
 
-%if ARCH_X86_64
+%ifn ARCH_X86_64
+vvc_mc_placeholder: times 4 db 0
+%else
 
 %if HAVE_AVX2_EXTERNAL
 
diff --git a/libavfilter/x86/vf_atadenoise.asm b/libavfilter/x86/vf_atadenoise.asm
index 4945ad3..748b65a 100644
--- a/libavfilter/x86/vf_atadenoise.asm
+++ b/libavfilter/x86/vf_atadenoise.asm
@@ -20,7 +20,10 @@
 ;* Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA 02110-1301 USA
 ;******************************************************************************
 
-%if ARCH_X86_64
+%ifn ARCH_X86_64
+SECTION .rdata
+vf_atadenoise_placeholder: times 4 db 0
+%else
 
 %include "libavutil/x86/x86util.asm"
 
diff --git a/libavfilter/x86/vf_nlmeans.asm b/libavfilter/x86/vf_nlmeans.asm
index 8f57801..9aef3a4 100644
--- a/libavfilter/x86/vf_nlmeans.asm
+++ b/libavfilter/x86/vf_nlmeans.asm
@@ -21,7 +21,10 @@
 
 %include "libavutil/x86/x86util.asm"
 
-%if HAVE_AVX2_EXTERNAL && ARCH_X86_64
+%ifn HAVE_AVX2_EXTERNAL && ARCH_X86_64
+SECTION .rdata
+vf_nlmeans_placeholder: times 4 db 0
+%else
 
 SECTION_RODATA 32
 
