{"name": "cunit", "version": "2.1.3", "port-version": 8, "description": "CUnit is a lightweight system for writing, administering, and running unit tests in C.  It provides C programmers a basic testing functionality with a flexible variety of user interfaces", "homepage": "https://sourceforge.net/projects/cunit/", "license": "LGPL-2.0-only", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}]}