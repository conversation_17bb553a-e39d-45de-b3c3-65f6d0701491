{"name": "kf5parts", "version": "5.98.0", "port-version": 1, "description": "Plugin framework for user interface component", "homepage": "https://api.kde.org/frameworks/kparts/html/index.html", "license": "LGPL-2.0-or-later", "dependencies": ["ecm", {"name": "gettext", "host": true, "features": ["tools"]}, "kf5config", "kf5coreaddons", "kf5i18n", "kf5iconthemes", "kf5jobwidgets", "kf5kio", "kf5service", "kf5textwidgets", "kf5widgetsaddons", "kf5xmlgui", "qt5-base", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}]}