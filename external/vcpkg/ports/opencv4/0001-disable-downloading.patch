--- a/cmake/OpenCVDownload.cmake
+++ b/cmake/OpenCVDownload.cmake
@@ -206,6 +206,9 @@ function(ocv_download)
   # Download
   if(NOT EXISTS "${CACHE_CANDIDATE}")
     ocv_download_log("#cmake_download \"${CACHE_CANDIDATE}\" \"${DL_URL}\"")
+    string(REPLACE "${OPENCV_DOWNLOAD_PATH}/" "opencv-cache/" CACHE_SUBPATH "${CACHE_CANDIDATE}")
+    message(FATAL_ERROR "    Downloads are not permitted during configure. Please pre-download the file \"${CACHE_CANDIDATE}\":\n    \n    vcpkg_download_distfile(OCV_DOWNLOAD\n        URLS \"${DL_URL}\"\n        FILENAME \"${CACHE_SUBPATH}\"\n        SHA512 0\n    )")
+
     foreach(try ${OPENCV_DOWNLOAD_TRIES_LIST})
       ocv_download_log("#try ${try}")
       file(DOWNLOAD "${DL_URL}" "${CACHE_CANDIDATE}"
