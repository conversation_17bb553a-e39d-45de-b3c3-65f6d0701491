--- a/cmake/OpenCVFindLibsGrfmt.cmake
+++ b/cmake/OpenCVFindLibsGrfmt.cmake
@@ -16,6 +16,7 @@ if(WITH_ZLIB_NG)
 
   set(HAVE_ZLIB_NG YES)
 else()
+  find_package(ZLIB "${MIN_VER_ZLIB}" REQUIRED)
   if(BUILD_ZLIB)
     ocv_clear_vars(ZLIB_FOUND)
   else()
@@ -28,8 +29,6 @@ else()
     set(ZLIB_FOUND TRUE)
     set(ZLIB_LIBRARY z)
     set(ZLIB_LIBRARIES z)
-  else()
-    find_package(ZLIB "${MIN_VER_ZLIB}")
   endif()
   if(ANDROID)
     set(CMAKE_FIND_LIBRARY_SUFFIXES ${_zlib_ORIG_CMAKE_FIND_LIBRARY_SUFFIXES})
@@ -61,7 +60,7 @@ endif()
 
 if(WITH_AVIF)
   ocv_clear_internal_cache_vars(AVIF_LIBRARY AVIF_INCLUDE_DIR)
-  include(cmake/OpenCVFindAVIF.cmake)
+  find_package(libavif REQUIRED)
   if(AVIF_FOUND)
     set(HAVE_AVIF 1)
   endif()
@@ -78,7 +77,7 @@ if(WITH_JPEG)
       set(JPEG_LIBRARIES jpeg)
       set(JPEG_FOUND TRUE)
     else()
-      include(FindJPEG)
+      find_package(JPEG REQUIRED)
     endif()
   endif()
 
@@ -130,7 +129,7 @@ if(WITH_TIFF)
       set(TIFF_LIBRARIES tiff)
       set(TIFF_FOUND TRUE)
     else()
-      include(FindTIFF)
+      find_package(TIFF REQUIRED)
     endif()
     if(TIFF_FOUND)
       ocv_parse_header("${TIFF_INCLUDE_DIR}/tiff.h" TIFF_VERSION_LINES TIFF_VERSION_CLASSIC TIFF_VERSION_BIG TIFF_VERSION TIFF_BIGTIFF_VERSION)
@@ -175,7 +174,7 @@ if(WITH_WEBP)
     ocv_clear_vars(WEBP_FOUND WEBP_LIBRARY WEBP_LIBRARIES WEBP_INCLUDE_DIR)
   else()
     ocv_clear_internal_cache_vars(WEBP_LIBRARY WEBP_INCLUDE_DIR)
-    include(cmake/OpenCVFindWebP.cmake)
+    find_package(WEBP NAMES WebP REQUIRED)
     if(WEBP_FOUND)
       set(HAVE_WEBP 1)
     endif()
@@ -234,6 +233,8 @@ if(WITH_JPEGXL)
   if(JPEGXL_FOUND)
     set(HAVE_JPEGXL YES)
     message(STATUS "Found system JPEG-XL: ver ${JPEGXL_VERSION}")
+  else()
+    message(FATAL_ERROR "JPEG-XL is required but not found. Please install it.")
   endif()
 endif()
 
@@ -242,7 +243,7 @@ if(WITH_OPENJPEG)
   if(BUILD_OPENJPEG)
     ocv_clear_vars(OpenJPEG_FOUND)
   else()
-    find_package(OpenJPEG QUIET)
+    find_package(OpenJPEG REQUIRED)
   endif()
 
   if(NOT OpenJPEG_FOUND OR OPENJPEG_MAJOR_VERSION LESS 2)
@@ -271,7 +272,7 @@ if(WITH_JASPER AND NOT HAVE_OPENJPEG)
   if(BUILD_JASPER)
     ocv_clear_vars(JASPER_FOUND)
   else()
-    include(FindJasper)
+    find_package(Jasper REQUIRED)
   endif()
 
   if(NOT JASPER_FOUND)
@@ -297,9 +298,9 @@ if(WITH_SPNG)
     # CMakeConfig bug in SPNG, include is missing there in version 0.7.4 and older
     # See https://github.com/randy408/libspng/pull/264
     include(CMakeFindDependencyMacro)
-    find_package(SPNG QUIET)
+    find_package(SPNG REQUIRED)
     if(SPNG_FOUND)
-      set(SPNG_LIBRARY "spng::spng" CACHE INTERNAL "")
+      set(SPNG_LIBRARY "$<IF:$<TARGET_EXISTS:spng::spng>,spng::spng,spng::spng_static>" CACHE INTERNAL "")
       set(SPNG_LIBRARIES ${SPNG_LIBRARY})
     else()
       if(PkgConfig_FOUND)
@@ -331,7 +332,7 @@ if(NOT HAVE_SPNG AND WITH_PNG)
     ocv_clear_vars(PNG_FOUND)
   else()
     ocv_clear_internal_cache_vars(PNG_LIBRARY PNG_INCLUDE_DIR)
-    find_package(PNG QUIET)
+    find_package(PNG REQUIRED)
   endif()
 
   if(NOT PNG_FOUND)
@@ -354,7 +355,8 @@ if(WITH_OPENEXR)
   ocv_clear_vars(HAVE_OPENEXR)
   if(NOT BUILD_OPENEXR)
     ocv_clear_internal_cache_vars(OPENEXR_INCLUDE_PATHS OPENEXR_LIBRARIES OPENEXR_ILMIMF_LIBRARY OPENEXR_VERSION)
-    include("${OpenCV_SOURCE_DIR}/cmake/OpenCVFindOpenEXR.cmake")
+    find_package(OpenEXR CONFIG REQUIRED)
+    set(OPENEXR_FOUND TRUE)
   endif()
 
   if(OPENEXR_FOUND)
@@ -374,7 +376,7 @@ endif()
 
 # --- GDAL (optional) ---
 if(WITH_GDAL)
-    find_package(GDAL QUIET)
+    find_package(GDAL REQUIRED)
 
     if(NOT GDAL_FOUND)
         set(HAVE_GDAL NO)
@@ -386,7 +388,7 @@ if(WITH_GDAL)
 endif()
 
 if(WITH_GDCM)
-  find_package(GDCM QUIET)
+  find_package(GDCM REQUIRED)
   if(NOT GDCM_FOUND)
     set(HAVE_GDCM NO)
     ocv_clear_vars(GDCM_VERSION GDCM_LIBRARIES)
--- a/modules/imgcodecs/CMakeLists.txt
+++ b/modules/imgcodecs/CMakeLists.txt
@@ -30,7 +30,7 @@ endif()
 
 if(HAVE_WEBP)
   add_definitions(-DHAVE_WEBP)
-  ocv_include_directories(${WEBP_INCLUDE_DIR})
+  ocv_include_directories(${WEBP_INCLUDE_DIRS})
   list(APPEND GRFMT_LIBS ${WEBP_LIBRARIES})
 endif()
 
