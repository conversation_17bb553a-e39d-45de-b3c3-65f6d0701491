--- a/modules/text/cmake/init.cmake
+++ b/modules/text/cmake/init.cmake
@@ -4,6 +4,8 @@ OCV_OPTION(WITH_TESSERACT "Include Tesseract OCR library support" (NOT CMAKE_CRO
 if(NOT HAVE_TESSERACT
     AND (WITH_TESSERACT OR OPENCV_FIND_TESSERACT)
 )
+  find_package(Tesseract CONFIG REQUIRED)
+  set(Tesseract_LIBRARIES Tesseract::libtesseract)
   if(NOT Tesseract_FOUND)
     find_package(Tesseract QUIET)  # Prefer CMake's standard locations (including Tesseract_DIR)
   endif()
