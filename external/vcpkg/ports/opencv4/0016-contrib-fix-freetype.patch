--- a/modules/freetype/CMakeLists.txt
+++ b/modules/freetype/CMakeLists.txt
@@ -3,8 +3,11 @@ if(APPLE_FRAMEWORK)
   ocv_module_disable(freetype)
 endif()
 
-ocv_check_modules(FREETYPE freetype2)
-ocv_check_modules(HARFBUZZ harfbuzz)
+if(WITH_FREETYPE)
+  find_package(Freetype REQUIRED)
+  find_package(HARFBUZZ NAMES harfbuzz REQUIRED)
+  set(HARFBUZZ_LIBRARIES harfbuzz::harfbuzz)
+endif()
 
 if(OPENCV_INITIAL_PASS)
   if(NOT FREETYPE_FOUND)
