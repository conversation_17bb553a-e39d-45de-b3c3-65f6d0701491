{"name": "opencv4", "version": "4.11.0", "description": "computer vision library", "homepage": "https://github.com/opencv/opencv", "license": "Apache-2.0", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}, {"name": "vcpkg-get-python-packages", "host": true}, "zlib"], "default-features": ["calib3d", {"name": "directml", "platform": "windows & !uwp & !mingw"}, {"name": "dnn", "platform": "!android"}, {"name": "dshow", "platform": "windows & !uwp"}, "fs", {"name": "<PERSON>i", "platform": "!uwp"}, {"name": "gtk", "platform": "linux"}, "<PERSON><PERSON><PERSON>", "intrinsics", "jpeg", {"name": "msmf", "platform": "windows & !uwp & !mingw"}, "png", "quirc", "thread", "tiff", "webp", {"name": "win32ui", "platform": "windows & !uwp"}], "features": {"ade": {"description": "graph api", "dependencies": ["ade"]}, "aravis": {"description": "aravis", "dependencies": [{"name": "aravis", "default-features": false}]}, "calib3d": {"description": "calib3d module"}, "carotene": {"description": "carotene module"}, "contrib": {"description": "opencv_contrib module", "dependencies": [{"name": "hdf5", "platform": "!uwp & !(windows & (arm | arm64)) & !ios"}, {"name": "opencv4", "default-features": false, "features": ["calib3d"]}, {"name": "tesseract", "platform": "!uwp & !(windows & (arm | arm64)) & !ios"}]}, "cuda": {"description": "CUDA support for opencv", "dependencies": ["cuda", {"name": "opencv4", "default-features": false, "features": ["contrib"]}]}, "cudnn": {"description": "cuDNN support for opencv", "dependencies": ["cudnn", {"name": "opencv4", "default-features": false, "features": ["cuda"]}]}, "dc1394": {"description": "Dc1394 support for opencv", "dependencies": ["libdc1394"]}, "directml": {"description": "Build with DirectML support", "supports": "windows & !uwp & !mingw"}, "dnn": {"description": "Enable dnn module", "supports": "!android", "dependencies": ["flatbuffers", {"name": "flatbuffers", "host": true, "default-features": false}, "protobuf"]}, "dnn-cuda": {"description": "Build dnn module with CUDA support", "dependencies": [{"name": "opencv4", "default-features": false, "features": ["cudnn", "dnn"]}]}, "dshow": {"description": "Enable DirectShow"}, "eigen": {"description": "Eigen support for opencv", "dependencies": ["eigen3"]}, "ffmpeg": {"description": "ffmpeg support for opencv", "dependencies": [{"name": "ffmpeg", "default-features": false, "features": ["avcodec", "avformat", "swresample", "swscale"]}]}, "freetype": {"description": "Freetype support for opencv", "dependencies": [{"name": "freetype", "default-features": false}, {"name": "harfbuzz", "default-features": false}]}, "fs": {"description": "Enable filesystem support"}, "gapi": {"description": "Enable gapi module", "supports": "!uwp"}, "gdcm": {"description": "GDCM support for opencv", "dependencies": ["gdcm"]}, "gstreamer": {"description": "gstreamer support for opencv", "dependencies": [{"name": "gstreamer", "default-features": false, "features": ["plugins-base"]}]}, "gtk": {"description": "GTK support for opencv", "supports": "!windows & !osx & !ios & !android", "dependencies": [{"name": "gtk3", "default-features": false}]}, "halide": {"description": "Halide support for opencv", "dependencies": ["halide", {"name": "opencv4", "default-features": false}, {"name": "opencv4", "features": ["dnn"]}]}, "highgui": {"description": "highgui module"}, "intrinsics": {"description": "Enable intrinsics"}, "ipp": {"description": "Enable Intel Integrated Performance Primitives", "supports": "(osx & x64) | (windows & (x64 | x86)) | (linux & (x64 | x86))"}, "jpeg": {"description": "JPEG support for opencv", "dependencies": ["libjpeg-turbo"]}, "jpegxl": {"description": "JPEGXL support for opencv", "dependencies": ["libjxl"]}, "msmf": {"description": "Microsoft Media Foundation support for opencv", "supports": "windows & !uwp & !mingw"}, "nonfree": {"description": "allow nonfree and unredistributable libraries"}, "opencl": {"description": "Enable opencl support", "supports": "!osx", "dependencies": ["opencl"]}, "openexr": {"description": "OpenEXR support for opencv", "dependencies": ["openexr"]}, "opengl": {"description": "opengl support for opencv", "supports": "!osx", "dependencies": ["opengl"]}, "openjpeg": {"description": "JPEG 2000 support for opencv", "dependencies": ["openjpeg"]}, "openmp": {"description": "Enable OpenMP support", "supports": "!osx"}, "openvino": {"description": "OpenVINO support for OpenCV DNN", "supports": "!uwp & !x86", "dependencies": [{"name": "opencv4", "default-features": false, "features": ["dnn"]}, {"name": "<PERSON>vin<PERSON>", "default-features": false, "features": ["auto", "cpu", "hetero"]}, {"name": "<PERSON>vin<PERSON>", "default-features": false, "features": ["gpu"], "platform": "x64 & !(osx | uwp)"}]}, "ovis": {"description": "opencv_ovis module", "supports": "!(windows & static)", "dependencies": ["ogre", {"name": "opencv4", "default-features": false, "features": ["contrib"]}]}, "png": {"description": "PNG support for opencv", "dependencies": ["libpng"]}, "python": {"description": "Python wrapper support for opencv", "dependencies": [{"name": "python3", "default-features": false, "features": ["extensions"]}]}, "qt": {"description": "Qt GUI support for opencv", "dependencies": ["qt5compat", {"name": "qtbase", "default-features": false, "features": ["concurrent", "gui", "testlib", "widgets"]}]}, "quality": {"description": "Build opencv_quality module", "supports": "!uwp", "dependencies": [{"name": "opencv4", "default-features": false, "features": ["contrib"]}]}, "quirc": {"description": "Enable QR code module", "dependencies": ["quirc"]}, "rgbd": {"description": "Build opencv_rgbd module", "dependencies": [{"name": "opencv4", "default-features": false, "features": ["contrib"]}]}, "sfm": {"description": "opencv_sfm module", "dependencies": ["ceres", "gflags", "glog", {"name": "opencv4", "default-features": false, "features": ["contrib"]}, {"name": "opencv4", "default-features": false, "features": ["eigen"]}]}, "tbb": {"description": "Enable Intel Threading Building Blocks", "supports": "!static", "dependencies": ["tbb"]}, "thread": {"description": "Enable thread support"}, "tiff": {"description": "TIFF support for opencv", "dependencies": [{"name": "tiff", "default-features": false}]}, "vtk": {"description": "vtk support for opencv", "dependencies": [{"name": "opencv4", "features": ["contrib"]}, {"name": "vtk", "default-features": false}]}, "vulkan": {"description": "Vulkan support for opencv dnn", "dependencies": [{"name": "opencv4", "default-features": false, "features": ["dnn"]}]}, "webp": {"description": "WebP support for opencv", "dependencies": ["libwebp"]}, "win32ui": {"description": "Enable win32ui", "supports": "windows & !uwp"}, "world": {"description": "Compile to a single package support for opencv"}}}