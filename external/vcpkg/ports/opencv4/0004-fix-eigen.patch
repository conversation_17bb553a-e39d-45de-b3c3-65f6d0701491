--- a/cmake/OpenCVFindLibsPerf.cmake
+++ b/cmake/OpenCVFindLibsPerf.cmake
@@ -59,7 +59,7 @@ if(WITH_EIGEN AND NOT HAVE_EIGEN)
       OR NOT (CMAKE_VERSION VERSION_LESS "3.0.0")  # Eigen3Targets.cmake required CMake 3.0.0+
       ) AND NOT OPENCV_SKIP_EIGEN_FIND_PACKAGE_CONFIG
   )
-    find_package(Eigen3 CONFIG QUIET)  # Ceres 2.0.0 CMake scripts doesn't work with CMake's FindEigen3.cmake module (due to missing EIGEN3_VERSION_STRING)
+    find_package(Eigen3 CONFIG REQUIRED)  # Ceres 2.0.0 CMake scripts doesn't work with CMake's FindEigen3.cmake module (due to missing EIGEN3_VERSION_STRING)
   endif()
   if(NOT Eigen3_FOUND)
     find_package(Eigen3 QUIET)
