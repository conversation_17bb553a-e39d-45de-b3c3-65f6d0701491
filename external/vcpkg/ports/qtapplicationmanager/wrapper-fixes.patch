diff --git a/cmake/FindWrapLibArchive.cmake b/cmake/FindWrapLibArchive.cmake
index 58c13f4..7be4931 100644
--- a/cmake/FindWrapLibArchive.cmake
+++ b/cmake/FindWrapLibArchive.cmake
@@ -16,3 +16,24 @@ add_library(WrapLibArchive::WrapLibArchive INTERFACE IMPORTED)
 target_link_libraries(WrapLibArchive::WrapLibArchive INTERFACE ${LibArchive_LIBRARIES})
 target_include_directories(WrapLibArchive::WrapLibArchive INTERFACE ${LibArchive_INCLUDE_DIRS})
 set(WrapLibArchive_FOUND TRUE)
+if(TARGET BZip2::BZip2)
+	set_property(TARGET BZip2::BZip2 PROPERTY _qt_no_promote_global TRUE)
+endif()
+if(TARGET Threads::Threads)
+	set_property(TARGET Threads::Threads PROPERTY _qt_no_promote_global TRUE)
+endif()
+if(TARGET OpenSSL::Crypto)
+	set_property(TARGET OpenSSL::Crypto PROPERTY _qt_no_promote_global TRUE)
+endif()
+if(TARGET zstd::libzstd)
+	set_property(TARGET zstd::libzstd PROPERTY _qt_no_promote_global TRUE)
+endif()
+if(TARGET zstd::libzstd_shared)
+	set_property(TARGET zstd::libzstd_shared PROPERTY _qt_no_promote_global TRUE)
+endif()
+if(TARGET zstd::libzstd_static)
+	set_property(TARGET zstd::libzstd_static PROPERTY _qt_no_promote_global TRUE)
+endif()
+if(TARGET ZLIB::ZLIB)
+	set_property(TARGET ZLIB::ZLIB PROPERTY _qt_no_promote_global TRUE)
+endif()
diff --git a/cmake/FindWrapLibSystemd.cmake b/cmake/FindWrapLibSystemd.cmake
index c681dae..c6252b1 100644
--- a/cmake/FindWrapLibSystemd.cmake
+++ b/cmake/FindWrapLibSystemd.cmake
@@ -15,5 +15,6 @@ if (NOT pc_libsystemd_FOUND)
 endif()
 
 add_library(WrapLibSystemd::WrapLibSystemd INTERFACE IMPORTED)
-target_link_libraries(WrapLibSystemd::WrapLibSystemd INTERFACE ${pc_libsystemd_LIBRARIES})
+target_link_libraries(WrapLibSystemd::WrapLibSystemd INTERFACE ${pc_libsystemd_LINK_LIBRARIES})
+target_include_directories(WrapLibSystemd::WrapLibSystemd INTERFACE ${pc_libsystemd_INCLUDE_DIRS})
 set(WrapLibSystemd_FOUND TRUE)
diff --git a/cmake/FindWrapLibYaml.cmake b/cmake/FindWrapLibYaml.cmake
index c2a2c62..80285b5 100644
--- a/cmake/FindWrapLibYaml.cmake
+++ b/cmake/FindWrapLibYaml.cmake
@@ -5,6 +5,14 @@ if(TARGET WrapLibYaml::WrapLibYaml)
     return()
 endif()
 
+find_package(yaml CONFIG)
+if(yaml_FOUND)
+    add_library(WrapLibYaml::WrapLibYaml INTERFACE IMPORTED)
+    target_link_libraries(WrapLibYaml::WrapLibYaml INTERFACE yaml)
+    set(WrapLibYaml_FOUND TRUE)
+    return()
+endif()
+
 find_package(PkgConfig)
 pkg_check_modules(pc_libyaml yaml-0.1>=0.2.2 IMPORTED_TARGET)
 
