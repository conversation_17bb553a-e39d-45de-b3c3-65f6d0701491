set(SCRIPT_PATH "${CURRENT_INSTALLED_DIR}/share/qtbase")
include("${SCRIPT_PATH}/qt_install_submodule.cmake")

set(${PORT}_PATCHES 
        wrapper-fixes.patch
        stack-walker-arm64.patch
    )

set(TOOL_NAMES appman
               appman-controller
               appman-dumpqmltypes
               appman-packager
               appman-qmltestrunner
               appman-launcher-qml
               appman-package-server
               package-uploader
    )

# cf. src/common-lib/configure.cmake
set(options "")
if("installer" IN_LIST FEATURES)
    list(APPEND options -DINPUT_installer=yes -DINPUT_libarchive=system)
else()
    list(APPEND options -DINPUT_installer=no  -DINPUT_libarchive=no)
endif()
if("multi-process" IN_LIST FEATURES)
    list(APPEND options -DINPUT_force_mode=multi)
else()
    list(APPEND options -DINPUT_force_mode=single)
endif()
if("package-server" IN_LIST FEATURES)
    list(APPEND options -DINPUT_package_server=yes)
else()
    list(APPEND options -DINPUT_package_server=no)
endif()
if("systemd-watchdog" IN_LIST FEATURES)
    list(APPEND options -DINPUT_systemd_watchdog=yes)
    vcpkg_find_acquire_program(PKGCONFIG)
    list(APPEND options "-DPKG_CONFIG_EXECUTABLE=${PKGCONFIG}")
else()
    list(APPEND options -DINPUT_systemd_watchdog=no)
endif()

qt_download_submodule(PATCHES ${${PORT}_PATCHES})
if(QT_UPDATE_VERSION)
    return()
endif()

file(REMOVE_RECURSE
    "${SOURCE_PATH}/src/3rdparty/libarchive"
    "${SOURCE_PATH}/src/3rdparty/libbacktrace"
    "${SOURCE_PATH}/src/3rdparty/libdbus"
    "${SOURCE_PATH}/src/3rdparty/libyaml"
    "${SOURCE_PATH}/src/3rdparty/stackwalker"
)

set(qt_plugindir ${QT6_DIRECTORY_PREFIX}plugins)
set(qt_qmldir ${QT6_DIRECTORY_PREFIX}qml)
qt_cmake_configure(OPTIONS
                        ${options}
                        -DCMAKE_FIND_PACKAGE_TARGETS_GLOBAL=ON
                        -DINPUT_libbacktrace=no
                        -DINPUT_libdbus=no  # disable bundled libdbus
                        -DINPUT_libyaml=system
                        -DINPUT_stackwalker=no
                   TOOL_NAMES ${TOOL_NAMES}
)

### Fix debug post-build.bat generated by CMake.
### Maybe related: https://gitlab.kitware.com/cmake/cmake/-/issues/22124.
if(VCPKG_TARGET_IS_WINDOWS)
    set(scriptfile "${CURRENT_BUILDTREES_DIR}/${TARGET_TRIPLET}-dbg/src/tools/dumpqmltypes/CMakeFiles/appman-dumpqmltypes.dir/post-build.bat")
    file(TO_NATIVE_PATH "${CURRENT_INSTALLED_DIR}" CURRENT_INSTALLED_DIR_NATIVE)
    if(EXISTS "${scriptfile}")
        vcpkg_replace_string("${scriptfile}" "${CURRENT_INSTALLED_DIR_NATIVE}\\bin" "${CURRENT_INSTALLED_DIR_NATIVE}\\debug\\bin")
    endif()
endif()
vcpkg_cmake_install(ADD_BIN_TO_PATH)

qt_fixup_and_cleanup(TOOL_NAMES ${TOOL_NAMES})

qt_install_copyright("${SOURCE_PATH}")

### ^^^ Using the more verbose code due to the post-build.bat script fixup.
### vvv Usual short version follows.

#qt_install_submodule(PATCHES    ${${PORT}_PATCHES}
#                     TOOL_NAMES ${TOOL_NAMES}
#                     CONFIGURE_OPTIONS
#                        ...
#                     CONFIGURE_OPTIONS_RELEASE
#                     CONFIGURE_OPTIONS_DEBUG
#                    )


file(GLOB_RECURSE qttools "${CURRENT_PACKAGES_DIR}/tools/Qt6/bin/*")
if(NOT qttools AND VCPKG_CROSSCOMPILING)
  file(REMOVE_RECURSE "${CURRENT_PACKAGES_DIR}/tools/Qt6/bin/")
 endif()

if(VCPKG_TARGET_IS_WINDOWS AND VCPKG_CROSSCOMPILING AND VCPKG_TARGET_ARCHITECTURE STREQUAL "arm64")
  file(REMOVE_RECURSE
        "${CURRENT_PACKAGES_DIR}/bin/"
        "${CURRENT_PACKAGES_DIR}/debug/bin/"
        "${CURRENT_PACKAGES_DIR}/tools/"
  )
endif()

set(VCPKG_POLICY_MISMATCHED_NUMBER_OF_BINARIES enabled) #Debug tracing libraries are only build if CMAKE_BUILD_TYPE is equal to Debug
