diff --git a/src/3rdparty/stackwalker/stackwalker.cpp b/src/3rdparty/stackwalker/stackwalker.cpp
index 7008ac6..cfa0d0e 100644
--- a/src/3rdparty/stackwalker/stackwalker.cpp
+++ b/src/3rdparty/stackwalker/stackwalker.cpp
@@ -1121,6 +1121,14 @@ BOOL StackWalker::ShowCallstack(HANDLE                    hThread,
   s.AddrBStore.Mode = AddrModeFlat;
   s.AddrStack.Offset = c.IntSp;
   s.AddrStack.Mode = AddrModeFlat;
+#elif _M_ARM64
+  imageType = IMAGE_FILE_MACHINE_ARM64;
+  s.AddrPC.Offset = c.Pc;
+  s.AddrPC.Mode = AddrModeFlat;
+  s.AddrFrame.Offset = c.Fp;
+  s.AddrFrame.Mode = AddrModeFlat;
+  s.AddrStack.Offset = c.Sp;
+  s.AddrStack.Mode = AddrModeFlat;
 #else
 #error "Platform not supported!"
 #endif
