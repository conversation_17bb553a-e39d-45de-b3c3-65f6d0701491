{"$comment": "Automatically generated by scripts/boost/generate-ports.ps1", "name": "boost-graph-parallel", "version": "1.87.0", "description": "Boost graph_parallel module", "homepage": "https://www.boost.org/libs/graph_parallel", "license": "BSL-1.0", "dependencies": [{"name": "boost-algorithm", "version>=": "1.87.0"}, {"name": "boost-assert", "version>=": "1.87.0"}, {"name": "boost-cmake", "version>=": "1.87.0"}, {"name": "boost-concept-check", "version>=": "1.87.0"}, {"name": "boost-config", "version>=": "1.87.0"}, {"name": "boost-container-hash", "version>=": "1.87.0"}, {"name": "boost-core", "version>=": "1.87.0"}, {"name": "boost-detail", "version>=": "1.87.0"}, {"name": "boost-dynamic-bitset", "version>=": "1.87.0"}, {"name": "boost-filesystem", "platform": "!uwp", "version>=": "1.87.0"}, {"name": "boost-foreach", "version>=": "1.87.0"}, {"name": "boost-function", "version>=": "1.87.0"}, {"name": "boost-graph", "version>=": "1.87.0"}, {"name": "boost-headers", "version>=": "1.87.0"}, {"name": "boost-iterator", "version>=": "1.87.0"}, {"name": "boost-lexical-cast", "version>=": "1.87.0"}, {"name": "boost-mpi", "platform": "!uwp", "version>=": "1.87.0"}, {"name": "boost-mpl", "version>=": "1.87.0"}, {"name": "boost-optional", "version>=": "1.87.0"}, {"name": "boost-property-map", "version>=": "1.87.0"}, {"name": "boost-property-map-parallel", "version>=": "1.87.0"}, {"name": "boost-random", "platform": "!uwp", "version>=": "1.87.0"}, {"name": "boost-serialization", "version>=": "1.87.0"}, {"name": "boost-smart-ptr", "version>=": "1.87.0"}, {"name": "boost-static-assert", "version>=": "1.87.0"}, {"name": "boost-tuple", "version>=": "1.87.0"}, {"name": "boost-type-traits", "version>=": "1.87.0"}, {"name": "boost-variant", "version>=": "1.87.0"}, "mpi"]}