diff --git a/CMakeLists.txt b/CMakeLists.txt
index 2e61060..02c714f 100755
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -91,7 +91,8 @@ if(NOT CMAKE_CONFIGURATION_TYPES)
 endif()
 
 ## Dependencies
-find_package(MATIO REQUIRED)
+find_package(PkgConfig REQUIRED)
+pkg_check_modules(matio REQUIRED IMPORTED_TARGET matio)
 find_package(Eigen3 QUIET)
 
 if (Eigen3_FOUND)
@@ -100,10 +101,10 @@ endif()
 
 # Fetching visit_struct
 include(CMakeDependentOption)
-find_package(visit_struct QUIET)
-option(USE_SYSTEM_visit_struct "Use system visit_struct" ${visit_struct_FOUND})
+find_package(unofficial-visit_struct QUIET)
+option(USE_SYSTEM_visit_struct "Use system visit_struct" ${unofficial-visit_struct_FOUND})
 if(USE_SYSTEM_visit_struct)
-     find_package(visit_struct REQUIRED)
+     find_package(unofficial-visit_struct REQUIRED)
 else()
     include(FetchContent)
     FetchContent_Declare(visit_struct
@@ -182,8 +183,14 @@ target_include_directories(matioCpp PUBLIC "$<BUILD_INTERFACE:${CMAKE_CURRENT_SO
                                            "$<BUILD_INTERFACE:${CMAKE_CURRENT_BINARY_DIR}/Autogenerated>"
                                            "$<INSTALL_INTERFACE:$<INSTALL_PREFIX>/${CMAKE_INSTALL_INCLUDEDIR}>")
 
-target_link_libraries(matioCpp PUBLIC MATIO::MATIO visit_struct::visit_struct)
-list(APPEND MATIOCPP_DEPENDENCIES MATIO visit_struct)
+
+target_link_libraries(matioCpp PUBLIC unofficial::visit_struct::visit_struct)
+# Avoid propagating pkg-config dependencies down the line.
+get_target_property(matio_LINK_LIBRARIES PkgConfig::matio INTERFACE_LINK_LIBRARIES)
+target_include_directories(matioCpp PUBLIC ${matio_INCLUDE_DIRS})
+target_link_libraries(matioCpp PUBLIC ${matio_LINK_LIBRARIES})
+
+list(APPEND MATIOCPP_DEPENDENCIES PkgConfig::matio unofficial-visit_struct)
 
 if (Eigen3_FOUND)
     target_link_libraries(matioCpp PUBLIC Eigen3::Eigen)
@@ -208,7 +215,7 @@ add_library(matioCpp::matioCpp ALIAS matioCpp)
 
 # Specify installation targets, typology and destination folders.
 install(TARGETS    matioCpp
-  EXPORT           matioCppTargets
+  EXPORT           matioCppTargets DESTINATION share/matioCpp
   COMPONENT        runtime
   LIBRARY          DESTINATION "${CMAKE_INSTALL_LIBDIR}"                   COMPONENT shlib
   ARCHIVE          DESTINATION "${CMAKE_INSTALL_LIBDIR}"                   COMPONENT lib
@@ -216,22 +223,15 @@ install(TARGETS    matioCpp
   PUBLIC_HEADER    DESTINATION "${CMAKE_INSTALL_INCLUDEDIR}/matioCpp/"     COMPONENT dev
   PRIVATE_HEADER   DESTINATION "${CMAKE_INSTALL_INCLUDEDIR}/matioCpp/impl" COMPONENT dev)
 
-install(FILES "cmake/FindMATIO.cmake" DESTINATION "${CMAKE_INSTALL_PREFIX}/share/matioCpp/cmake")
-file(COPY "cmake/FindMATIO.cmake" DESTINATION "${CMAKE_CURRENT_BINARY_DIR}/share/matioCpp/cmake")
-
-install(FILES "cmake/Findvisit_struct.cmake" DESTINATION "${CMAKE_INSTALL_PREFIX}/share/matioCpp/cmake")
-file(COPY "cmake/Findvisit_struct.cmake" DESTINATION "${CMAKE_CURRENT_BINARY_DIR}/share/matioCpp/cmake")
-
-include(InstallBasicPackageFiles)
+include(CMakePackageConfigHelpers)
+configure_package_config_file(matioCppConfig.cmake.in ${CMAKE_CURRENT_BINARY_DIR}/matioCppConfig.cmake
+  INSTALL_DESTINATION share/matioCpp
+)
+install(FILES "${CMAKE_CURRENT_BINARY_DIR}/matioCppConfig.cmake" DESTINATION share/matioCpp)
+install(EXPORT matioCppTargets
+  NAMESPACE        matioCpp::
+  DESTINATION share/matioCpp)
 
-install_basic_package_files(${PROJECT_NAME}
-                            VERSION ${${PROJECT_NAME}_VERSION}
-                            EXPORT matioCppTargets
-                            COMPATIBILITY AnyNewerVersion
-                            VARS_PREFIX ${PROJECT_NAME}
-                            NO_CHECK_REQUIRED_COMPONENTS_MACRO
-                            DEPENDENCIES ${MATIOCPP_DEPENDENCIES}
-                            OVERRIDE_MODULE_PATH ${CMAKE_INSTALL_PREFIX}/share/matioCpp/cmake)
 # Add the uninstall target
 include(AddUninstallTarget)
 
