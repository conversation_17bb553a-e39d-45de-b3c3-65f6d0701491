{"name": "libeventheader-tracepoint", "version": "1.4.0", "description": "C/C++ interface for generating EventHeader-encoded Linux Tracepoints", "homepage": "https://github.com/microsoft/LinuxTracepoints/", "license": "MIT", "supports": "linux | windows", "dependencies": [{"name": "libtracepoint", "version>=": "1.4.0"}, {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}]}