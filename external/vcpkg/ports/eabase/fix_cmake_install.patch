diff --git a/CMakeLists.txt b/CMakeLists.txt
index 652f07f..264aaf5 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -4,6 +4,9 @@
 cmake_minimum_required(VERSION 3.1)
 project(EABase CXX)
 
+include(GNUInstallDirs)
+include(CMakePackageConfigHelpers)
+
 #-------------------------------------------------------------------------------------------
 # Options
 #-------------------------------------------------------------------------------------------
@@ -24,14 +27,38 @@ add_definitions(-D_CHAR16T)
 #-------------------------------------------------------------------------------------------
 # Header only library 
 #-------------------------------------------------------------------------------------------
-add_library(EABase INTERFACE) 
-
+add_library(EABase INTERFACE)
+add_library(EABase::EABase ALIAS EABase)
 #-------------------------------------------------------------------------------------------
 # Include dirs
 #-------------------------------------------------------------------------------------------
-target_include_directories(EABase INTERFACE include/Common)
-
-#-------------------------------------------------------------------------------------------
-# Installation
-#-------------------------------------------------------------------------------------------
-install(DIRECTORY include/Common/EABase DESTINATION include)
+target_include_directories(EABase INTERFACE
+    $<BUILD_INTERFACE:${CMAKE_CURRENT_LIST_DIR}/include/Common>
+    $<INSTALL_INTERFACE:${CMAKE_INSTALL_INCLUDEDIR}>
+)
+# create and install an export set for eabase target as EABase::EABase
+set(EABase_CMAKE_CONFIG_DESTINATION "${CMAKE_INSTALL_LIBDIR}/cmake/EABase")
+configure_package_config_file(
+    EABaseConfig.cmake.in
+    ${CMAKE_CURRENT_BINARY_DIR}/EABaseConfig.cmake
+    INSTALL_DESTINATION ${EABase_CMAKE_CONFIG_DESTINATION}
+)
+# create and install an export set for Terra target as Terra
+install(
+    TARGETS EABase EXPORT EABaseTargets
+    DESTINATION ${CMAKE_INSTALL_LIBDIR}
+)
+install(EXPORT EABaseTargets DESTINATION ${EABase_CMAKE_CONFIG_DESTINATION})
+write_basic_package_version_file(
+  "${CMAKE_CURRENT_BINARY_DIR}/EABaseConfigVersion.cmake"
+  VERSION 2.09.12
+  COMPATIBILITY SameMajorVersion
+)
+install(TARGETS EABase LIBRARY DESTINATION "${CMAKE_INSTALL_LIBDIR}")
+install(DIRECTORY "include/Common/" DESTINATION "${CMAKE_INSTALL_INCLUDEDIR}")
+install(
+    FILES
+        "${CMAKE_CURRENT_BINARY_DIR}/EABaseConfig.cmake"
+        "${CMAKE_CURRENT_BINARY_DIR}/EABaseConfigVersion.cmake"
+    DESTINATION ${EABase_CMAKE_CONFIG_DESTINATION}
+)
