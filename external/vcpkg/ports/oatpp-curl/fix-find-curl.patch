diff --git a/CMakeLists.txt b/CMakeLists.txt
index 85edfe7..a8aa02b 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -94,7 +94,7 @@ message("## ${OATPP_THIS_MODULE_NAME} module. Resolving dependencies...\n")
 
 ##############################
 ## Find libcurl dependency
-
+if(0)
 include(FindPkgConfig)
 pkg_check_modules(PKG_CURL REQUIRED libcurl)
 
@@ -103,9 +103,10 @@ message("[libcurl] LIBRARIES=${PKG_CURL_LIBRARIES}")
 message("[libcurl] LIBRARY_DIRS=${PKG_CURL_LIBRARY_DIRS}")
 message("[libcurl] INCLUDE_DIRS=${PKG_CURL_INCLUDE_DIRS}")
 message("[libcurl] VERSION=${PKG_CURL_VERSION}\n")
-
+endif()
+find_package(CURL CONFIG REQUIRED)
 link_directories(
-        ${PKG_CURL_LIBRARY_DIRS}
+        CURL::libcurl
 )
 
 message("\n############################################################################\n")
diff --git a/cmake/module-config.cmake.in b/cmake/module-config.cmake.in
index 5cc12b0..4ff34bc 100644
--- a/cmake/module-config.cmake.in
+++ b/cmake/module-config.cmake.in
@@ -1,5 +1,6 @@
 @PACKAGE_INIT@
-
+include(CMakeFindDependencyMacro)
+find_dependency(CURL)
 if(NOT TARGET oatpp::@OATPP_MODULE_NAME@)
     include("${CMAKE_CURRENT_LIST_DIR}/@<EMAIL>")
 endif()
diff --git a/src/CMakeLists.txt b/src/CMakeLists.txt
index 8c2087f..1da41c6 100644
--- a/src/CMakeLists.txt
+++ b/src/CMakeLists.txt
@@ -43,7 +43,7 @@ target_include_directories(${OATPP_THIS_MODULE_NAME}
 #)
 
 target_link_libraries(${OATPP_THIS_MODULE_NAME}
-        PRIVATE ${PKG_CURL_LIBRARIES}
+        PRIVATE CURL::libcurl
 )
 
 #######################################################################################################
