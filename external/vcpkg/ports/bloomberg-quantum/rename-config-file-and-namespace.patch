diff --git a/CMakeLists.txt b/CMakeLists.txt
index 5d22f0f..55d6f65 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -33,8 +33,9 @@ endif()
 if (NOT QUANTUM_PKGCONFIG_DIR)
     set(QUANTUM_PKGCONFIG_DIR share/pkgconfig)
 endif()
+set(PROJECT_NAMESPACE_NAME unofficial-bloomberg-${PROJECT_NAME})
 if (NOT QUANTUM_CMAKE_CONFIG_DIR)
-    set(QUANTUM_CMAKE_CONFIG_DIR share/cmake/${PROJECT_NAME})
+    set(QUANTUM_CMAKE_CONFIG_DIR share/cmake/${PROJECT_NAMESPACE_NAME})
 endif()
 
 #Global options
diff --git a/cmake/QuantumConfig.cmake.in b/cmake/unofficial-bloomberg-QuantumConfig.cmake.in
similarity index 100%
rename from cmake/QuantumConfig.cmake.in
rename to cmake/unofficial-bloomberg-QuantumConfig.cmake.in
diff --git a/quantum/CMakeLists.txt b/quantum/CMakeLists.txt
index 1a721f9..1e93bc4 100644
--- a/quantum/CMakeLists.txt
+++ b/quantum/CMakeLists.txt
@@ -21,10 +21,10 @@ make_quantum_header()
 
 set(PKG_DIR             "${CMAKE_BINARY_DIR}/package")
 set(PKGCONFIG_FILE      "${PKG_DIR}/${PROJECT_TARGET_NAME}.pc")
-set(TARGET_CONFIG_FILE  "${PKG_DIR}/${PROJECT_NAME}Config.cmake")
-set(TARGET_VERSION_FILE "${PKG_DIR}/${PROJECT_NAME}ConfigVersion.cmake")
+set(TARGET_CONFIG_FILE  "${PKG_DIR}/${PROJECT_NAMESPACE_NAME}Config.cmake")
+set(TARGET_VERSION_FILE "${PKG_DIR}/${PROJECT_NAMESPACE_NAME}ConfigVersion.cmake")
 set(TARGET_EXPORT_NAME   ${PROJECT_NAME}Targets)
-set(NAMESPACE           "${PROJECT_NAME}::")
+set(NAMESPACE           "${PROJECT_NAMESPACE_NAME}::")
 
 add_library(${PROJECT_TARGET_NAME} INTERFACE)
 set(QUANTUM_DEPENDENCIES Boost::context pthread)
@@ -70,7 +70,7 @@ if (QUANTUM_EXPORT_CMAKE_CONFIG)
 
     # Generate CMAKE configuration file and exported targets
     configure_package_config_file(
-        "${PROJECT_SOURCE_DIR}/cmake/${PROJECT_NAME}Config.cmake.in"
+        "${PROJECT_SOURCE_DIR}/cmake/${PROJECT_NAMESPACE_NAME}Config.cmake.in"
         "${TARGET_CONFIG_FILE}"
         INSTALL_DESTINATION "${QUANTUM_CMAKE_CONFIG_DIR}"
         PATH_VARS CMAKE_INSTALL_PREFIX CMAKE_INSTALL_INCLUDEDIR
