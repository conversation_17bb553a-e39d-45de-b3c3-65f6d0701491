import("//build/config/gclient_args.gni")

# Some non-Chromium builds don't support building java targets.
enable_java_templates = true

# Don't use Chromium's third_party/binutils.
linux_use_bundled_binutils_override = false

# Tracing requires //third_party/perfetto.
enable_base_tracing = false

# Skip assertions about 4GiB file size limit. See https://crbug.com/648948.
ignore_elf32_limitations = false

# Use the system install of Xcode for tools like ibtool, libtool, etc.
use_system_xcode = true
