diff --git a/config/BUILDCONFIG.gn b/config/BUILDCONFIG.gn
index 4cc6bfc..9ea83a9 100644
--- a/config/BUILDCONFIG.gn
+++ b/config/BUILDCONFIG.gn
@@ -657,6 +657,7 @@ template("component") {
     # See http://crbug.com/594610
     forward_variables_from(invoker, [ "visibility" ])
     forward_variables_from(invoker, "*", [ "visibility" ])
+    not_needed("*")
   }
 }
 
diff --git a/config/clang/BUILD.gn b/config/clang/BUILD.gn
index 960726e..87886dc 100644
--- a/config/clang/BUILD.gn
+++ b/config/clang/BUILD.gn
@@ -38,5 +38,7 @@ config("extra_warnings") {
     "-Wstring-conversion",
 
     "-Wtautological-overlap-compare",
+    "-Wno-unknown-warning-option",
+    "-Wno-unknown-pragmas"
   ]
 }
diff --git a/config/mac/sdk_info.py b/config/mac/sdk_info.py
index 1ad11d3..99b21f9 100644
--- a/config/mac/sdk_info.py
+++ b/config/mac/sdk_info.py
@@ -61,10 +61,15 @@ def FillXcodeVersion(settings, developer_dir):
     settings['xcode_build'] = version_plist['ProductBuildVersion']
     return
 
-  lines = subprocess.check_output(['xcodebuild', '-version']).splitlines()
-  settings['xcode_version'] = FormatVersion(lines[0].split()[-1])
-  settings['xcode_version_int'] = int(settings['xcode_version'], 10)
-  settings['xcode_build'] = lines[-1].split()[-1]
+  try:
+    lines = subprocess.check_output(['xcodebuild', '-version']).splitlines()
+    settings['xcode_version'] = FormatVersion(lines[0].split()[-1])
+    settings['xcode_version_int'] = int(settings['xcode_version'], 10)
+    settings['xcode_build'] = lines[-1].split()[-1]
+  except:
+    settings['xcode_version'] = ""
+    settings['xcode_version_int'] = 0
+    settings['xcode_build'] = ""
 
 
 def FillMachineOSBuild(settings):
diff --git a/mac/find_sdk.py b/mac/find_sdk.py
index 58362bf..a6069b6 100755
--- a/mac/find_sdk.py
+++ b/mac/find_sdk.py
@@ -68,6 +68,8 @@ def main():
   dev_dir = out.rstrip()
   sdk_dir = os.path.join(
       dev_dir, 'Platforms/MacOSX.platform/Developer/SDKs')
+  if not os.path.isdir(sdk_dir):
+    sdk_dir = os.path.join(dev_dir, 'SDKs')
 
   if not os.path.isdir(sdk_dir):
     raise SdkError('Install Xcode, launch it, accept the license ' +
@@ -87,7 +89,10 @@ def main():
 
   if options.print_bin_path:
     bin_path = 'Toolchains/XcodeDefault.xctoolchain/usr/bin/'
-    print(os.path.join(dev_dir, bin_path))
+    if os.path.isdir(os.path.join(dev_dir, bin_path)):
+      print(os.path.join(dev_dir, bin_path))
+    else:
+      print(os.path.join(dev_dir, 'usr/bin/'))
 
   return best_sdk
 
diff --git a/toolchain/mac/BUILD.gn b/toolchain/mac/BUILD.gn
index 8fdada0..8d5e6b7 100644
--- a/toolchain/mac/BUILD.gn
+++ b/toolchain/mac/BUILD.gn
@@ -184,9 +184,9 @@ template("mac_toolchain") {
     # If dSYMs are enabled, this flag will be added to the link tools.
     if (_enable_dsyms) {
       dsym_switch = " -Wcrl,dsym,{{root_out_dir}} "
-      dsym_switch += "-Wcrl,dsymutilpath," +
-                     rebase_path("//tools/clang/dsymutil/bin/dsymutil",
-                                 root_build_dir) + " "
+      # dsym_switch += "-Wcrl,dsymutilpath," +
+      #                rebase_path("//tools/clang/dsymutil/bin/dsymutil",
+      #                            root_build_dir) + " "
 
       dsym_output_dir =
           "{{root_out_dir}}/{{target_output_name}}{{output_extension}}.dSYM"
diff --git a/toolchain/win/BUILD.gn b/toolchain/win/BUILD.gn
index be08930..04fb374 100644
--- a/toolchain/win/BUILD.gn
+++ b/toolchain/win/BUILD.gn
@@ -98,7 +98,7 @@ template("msvc_toolchain") {
       } else {
         lld_link = "lld-link"
       }
-      prefix = rebase_path("$clang_base_path/bin", root_build_dir)
+      prefix = "$clang_base_path/bin"
 
       # lld-link includes a replacement for lib.exe that can produce thin
       # archives and understands bitcode (for lto builds).
@@ -190,7 +190,7 @@ template("msvc_toolchain") {
       # amount of data to parse and store in .ninja_deps. We do this on non-Windows too,
       # and already make sure rebuilds after win sdk / libc++ / clang header updates happen via
       # changing commandline flags.
-      show_includes = "/showIncludes:user"
+      show_includes = ""
     } else {
       show_includes = "/showIncludes"
     }
@@ -235,7 +235,7 @@ template("msvc_toolchain") {
 
       if (toolchain_args.current_cpu == "arm64") {
         if (is_clang) {
-          prefix = rebase_path("$clang_base_path/bin", root_build_dir)
+          prefix = "$clang_base_path/bin"
           ml = "${clang_prefix}${prefix}/${clang_cl} --target=arm64-windows"
           if (host_os == "win") {
             # Flip the slashes so that copy/paste of the command works.
@@ -444,11 +444,9 @@ template("win_toolchains") {
 
   msvc_toolchain("win_clang_" + target_name) {
     environment = "environment." + toolchain_arch
-    prefix = rebase_path("$clang_base_path/bin", root_build_dir)
+    prefix = "$clang_base_path/bin"
     cl = "${clang_prefix}$prefix/${clang_cl}"
-    _clang_lib_dir =
-        rebase_path("$clang_base_path/lib/clang/$clang_version/lib/windows",
-                    root_build_dir)
+    _clang_lib_dir = "$clang_base_path/lib/clang/$clang_version/lib/windows"
     if (host_os == "win") {
       # Flip the slashes so that copy/paste of the command works.
       cl = string_replace(cl, "/", "\\")
@@ -459,7 +457,7 @@ template("win_toolchains") {
 
     sys_include_flags = "${win_toolchain_data.include_flags_imsvc}"
     sys_lib_flags =
-        "-libpath:$_clang_lib_dir ${win_toolchain_data.libpath_flags}"
+        "-libpath:\"$_clang_lib_dir\" ${win_toolchain_data.libpath_flags}"
 
     toolchain_args = {
       if (defined(invoker.toolchain_args)) {
