diff --git a/CMakeLists.txt b/CMakeLists.txt
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -33,7 +33,8 @@ SET(CONFU_DEPENDENCIES_SOURCE_DIR ${CMAKE_SOURCE_DIR}/deps
 SET(CONFU_DEPENDENCIES_BINARY_DIR ${CMAKE_BINARY_DIR}/deps
   CACHE PATH "Confu-style dependencies binary directory")
 
-IF(NOT DEFINED PSIMD_SOURCE_DIR)
+find_path(PSIMD_INCLUDE_DIRS "psimd.h")
+IF(FALSE)
   MESSAGE(STATUS "Downloading PSimd to ${CONFU_DEPENDENCIES_SOURCE_DIR}/psimd (define PSIMD_SOURCE_DIR to avoid it)")
   CONFIGURE_FILE(cmake/DownloadPSimd.cmake "${CONFU_DEPENDENCIES_BINARY_DIR}/psimd-download/CMakeLists.txt")
   EXECUTE_PROCESS(COMMAND "${CMAKE_COMMAND}" -G "${CMAKE_GENERATOR}" .
@@ -76,7 +77,7 @@ ELSE()
 ENDIF()
 TARGET_INCLUDE_DIRECTORIES(fp16 INTERFACE
     $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
-    $<INSTALL_INTERFACE:include>)
+    $<INSTALL_INTERFACE:include> $<INSTALL_INTERFACE:${PSIMD_INCLUDE_DIRS}>)
 
 INSTALL(FILES include/fp16.h
   DESTINATION ${CMAKE_INSTALL_INCLUDEDIR})
@@ -90,7 +91,7 @@ INSTALL(FILES
   DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}/fp16)
 
 # ---[ Configure psimd
-IF(NOT TARGET psimd)
+IF(FALSE)
   ADD_SUBDIRECTORY(
     "${PSIMD_SOURCE_DIR}"
     "${CONFU_DEPENDENCIES_BINARY_DIR}/psimd")
