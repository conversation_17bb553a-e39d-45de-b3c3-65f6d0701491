{"name": "sfcgal", "version": "2.0.0", "description": "sfcgal is a C++ wrapper library around CGAL with the aim of supporting ISO 191007:2013 and OGC Simple Features for 3D operations.", "homepage": "https://gitlab.com/SFCGAL/SFCGAL", "license": "LGPL-2.0-or-later", "supports": "(x64 & (windows | osx | linux)) | (arm64 & osx)", "dependencies": ["cgal", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}]}