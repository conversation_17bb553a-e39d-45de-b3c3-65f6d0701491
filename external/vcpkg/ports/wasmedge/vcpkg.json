{"name": "wasmedge", "version": "0.13.5", "port-version": 2, "description": "WasmEdge is a high-performance WebAssembly runtime for edge computing.", "homepage": "https://WasmEdge.org", "license": "Apache-2.0", "supports": "!windows", "dependencies": ["boost-algorithm", "boost-align", "boost-predef", "spdlog", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "default-features": ["tools"], "features": {"aot": {"description": "Build with the Ahead-of-Time compiler supporting", "dependencies": ["llvm"]}, "plugin-wasi-nn-backend-openvino": {"description": "Build the OpenVINO backend plugin for the WasmEdge WASI-NN extension", "supports": "linux & !static"}, "plugin-wasi-nn-backend-pytorch": {"description": "Build the PyTorch backend plugin for the WasmEdge WASI-NN extension", "supports": "linux & !static"}, "plugin-wasi-nn-backend-tensorflowlite": {"description": "Build the TensorFlow Lite backend plugin for the WasmEdge WASI-NN extension", "supports": "linux & !static"}, "plugins": {"description": "Build plugins", "supports": "!static"}, "tools": {"description": "Build tools"}}}