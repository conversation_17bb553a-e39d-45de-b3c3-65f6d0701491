{"name": "mnn", "version": "1.1.0", "port-version": 7, "description": "MNN is a blazing fast, lightweight deep learning framework, battle-tested by business-critical use cases in Alibaba", "homepage": "https://www.mnn.zone/", "license": "Apache-2.0", "supports": "!(arm | uwp | ios | android)", "dependencies": ["flatbuffers", {"name": "flatbuffers", "host": true}, "<PERSON><PERSON><PERSON>", "stb", {"name": "vcpkg-cmake", "host": true}], "features": {"cuda": {"description": "Enable CUDA API backend", "dependencies": ["cuda", "cudnn", {"name": "mnn", "features": ["gpu"]}]}, "gpu": {"description": "Enable MNN Gpu Debug"}, "metal": {"description": "Enable Metal API backend for Apple platforms", "dependencies": [{"name": "mnn", "features": ["gpu"]}]}, "opencl": {"description": "Enable OpenCL API backend", "dependencies": [{"name": "mnn", "features": ["system"]}, "opencl"]}, "system": {"description": "For opencl and vulkan, use system lib or use dlopen"}, "test": {"description": "Build MNN test programs"}, "tools": {"description": "Build MNN tools", "dependencies": ["protobuf"]}, "vulkan": {"description": "Enable Vulkan API backend", "dependencies": [{"name": "mnn", "features": ["gpu", "system"]}, "vulkan"]}}}