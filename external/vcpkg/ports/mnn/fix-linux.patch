diff --git a/source/backend/cpu/CPUFixedPoint.hpp b/source/backend/cpu/CPUFixedPoint.hpp
index a5c44f9..fd2e979 100644
--- a/source/backend/cpu/CPUFixedPoint.hpp
+++ b/source/backend/cpu/CPUFixedPoint.hpp
@@ -17,7 +17,7 @@ limitations under the License.
 #define CPUFixedPoint_HPP
 
 #include <math.h>
-#include <stdint.h>
+#include <cstdint>
 #include <limits>
 #include <stdexcept>
 #include <algorithm>
diff --git a/source/backend/opencl/schema/current/CLCache_generated.h b/source/backend/opencl/schema/current/CLCache_generated.h
index 434a666..fc972b5 100644
--- a/source/backend/opencl/schema/current/CLCache_generated.h
+++ b/source/backend/opencl/schema/current/CLCache_generated.h
@@ -403,7 +403,7 @@ inline const flatbuffers::TypeTable *ShaderTypeTable() {
     "buildInfo"
   };
   static const flatbuffers::TypeTable tt = {
-    flatbuffers::ST_TABLE, 3, type_codes, nullptr, nullptr, names
+    flatbuffers::ST_TABLE, 3, type_codes, nullptr, nullptr, nullptr, names
   };
   return &tt;
 }
@@ -420,7 +420,7 @@ inline const flatbuffers::TypeTable *AutotuningTypeTable() {
     "localSize"
   };
   static const flatbuffers::TypeTable tt = {
-    flatbuffers::ST_TABLE, 3, type_codes, nullptr, nullptr, names
+    flatbuffers::ST_TABLE, 3, type_codes, nullptr, nullptr, nullptr, names
   };
   return &tt;
 }
@@ -439,7 +439,7 @@ inline const flatbuffers::TypeTable *CacheTypeTable() {
     "tunings"
   };
   static const flatbuffers::TypeTable tt = {
-    flatbuffers::ST_TABLE, 2, type_codes, type_refs, nullptr, names
+    flatbuffers::ST_TABLE, 2, type_codes, type_refs, nullptr, nullptr, names
   };
   return &tt;
 }
