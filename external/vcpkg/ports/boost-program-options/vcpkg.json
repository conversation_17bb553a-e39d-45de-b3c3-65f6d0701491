{"$comment": "Automatically generated by scripts/boost/generate-ports.ps1", "name": "boost-program-options", "version": "1.87.0", "description": "Boost program_options module", "homepage": "https://www.boost.org/libs/program_options", "license": "BSL-1.0", "dependencies": [{"name": "boost-any", "version>=": "1.87.0"}, {"name": "boost-bind", "version>=": "1.87.0"}, {"name": "boost-cmake", "version>=": "1.87.0"}, {"name": "boost-config", "version>=": "1.87.0"}, {"name": "boost-core", "version>=": "1.87.0"}, {"name": "boost-detail", "version>=": "1.87.0"}, {"name": "boost-function", "version>=": "1.87.0"}, {"name": "boost-headers", "version>=": "1.87.0"}, {"name": "boost-iterator", "version>=": "1.87.0"}, {"name": "boost-lexical-cast", "version>=": "1.87.0"}, {"name": "boost-smart-ptr", "version>=": "1.87.0"}, {"name": "boost-static-assert", "version>=": "1.87.0"}, {"name": "boost-throw-exception", "version>=": "1.87.0"}, {"name": "boost-tokenizer", "version>=": "1.87.0"}, {"name": "boost-type-traits", "version>=": "1.87.0"}]}