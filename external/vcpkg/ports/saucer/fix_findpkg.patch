diff --git a/CMakeLists.txt b/CMakeLists.txt
index 5ac3f20..d4e3864 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -134,57 +134,17 @@ endif()
 include("cmake/cpm.cmake")
 include("cmake/nuget.cmake")
 
-CPMFindPackage(
-  NAME           lockpp
-  VERSION        2.7
-  GIT_REPOSITORY "https://github.com/Soundux/lockpp"
-)
-
-CPMFindPackage(
-  NAME           boost_callable_traits
-  GIT_TAG        boost-1.84.0
-  GIT_REPOSITORY "https://github.com/boostorg/callable_traits"
-)
-
-CPMFindPackage(
-  NAME           tl-expected
-  VERSION        1.1.0
-  GIT_REPOSITORY "https://github.com/TartanLlama/expected"
-  OPTIONS        "EXPECTED_BUILD_TESTS OFF"
-)
-
-CPMFindPackage(
-  NAME           ereignis
-  VERSION        2.3
-  GIT_REPOSITORY "https://github.com/Soundux/ereignis"
-)
-
-CPMFindPackage(
-  NAME           fmt
-  GIT_TAG        10.1.1
-  GIT_REPOSITORY "https://github.com/fmtlib/fmt"
-)
-
-CPMFindPackage(
-  NAME           glaze
-  VERSION        1.9.9
-  GIT_REPOSITORY "https://github.com/stephenberry/glaze"
-)
-
-CPMFindPackage(
-  NAME           flagpp
-  VERSION        2.1
-  GIT_REPOSITORY "https://github.com/Curve/flagpp"
-)
-
-CPMFindPackage(
-  NAME           boost_preprocessor
-  GIT_TAG        boost-1.84.0
-  GIT_REPOSITORY "https://github.com/boostorg/preprocessor"
-)
+find_path(LOCKPP_INCLUDE_DIRS "lockpp")
+find_path(BOOST_CALLTRA_INCLUDE_DIRS "boost/callable_traits")
+find_package(tl-expected CONFIG REQUIRED)
+find_path(EREIGNIS_INCLUDE_DIRS "ereignis")
+find_package(fmt CONFIG REQUIRED)
+find_package(glaze CONFIG REQUIRED)
+find_path(FLAGPP_INCLUDE_DIRS "flagpp")
+find_path(BOOST_PREPROCESSOR_INCLUDE_DIRS "boost/preprocessor")
 
-target_link_libraries(${PROJECT_NAME} PRIVATE boost_preprocessor cr::flagpp)
-target_link_libraries(${PROJECT_NAME} PUBLIC  lockpp boost_callable_traits tl::expected glaze::glaze ereignis fmt)
+target_include_directories(${PROJECT_NAME} PRIVATE ${EREIGNIS_INCLUDE_DIRS} ${LOCKPP_INCLUDE_DIRS} ${BOOST_CALLTRA_INCLUDE_DIRS} ${FLAGPP_INCLUDE_DIRS} ${BOOST_PREPROCESSOR_INCLUDE_DIRS})
+target_link_libraries(${PROJECT_NAME} PRIVATE glaze::glaze fmt::fmt tl::expected)
 
 # --------------------------------------------------------------------------------------------------------
 # Setup Linkage
@@ -227,7 +187,7 @@ if (saucer_backend STREQUAL "WebView2")
   target_compile_definitions(${PROJECT_NAME} PRIVATE UNICODE=1 _UNICODE=1 NOMINMAX=1)
 
   include("cmake/webview2.cmake")
-  target_link_libraries(${PROJECT_NAME} ${saucer_linkage} Shlwapi webview2::webview2)
+  target_link_libraries(${PROJECT_NAME} ${saucer_linkage} Shlwapi unofficial::webview2::webview2)
 endif()
 
 # --------------------------------------------------------------------------------------------------------
@@ -267,6 +227,4 @@ if (saucer_prefer_remote AND saucer_backend STREQUAL "WebView2")
 endif()
 
 install(DIRECTORY "include/saucer" TYPE INCLUDE)
-install(DIRECTORY "$<TARGET_PROPERTY:fmt,INTERFACE_INCLUDE_DIRECTORIES>/fmt" TYPE INCLUDE)
-install(DIRECTORY "$<TARGET_PROPERTY:glaze_glaze,INTERFACE_INCLUDE_DIRECTORIES>/glaze" TYPE INCLUDE)
-install(DIRECTORY "$<TARGET_PROPERTY:boost_callable_traits,INTERFACE_INCLUDE_DIRECTORIES>/boost" TYPE INCLUDE)
+install(DIRECTORY "${BOOST_CALLTRA_INCLUDE_DIRS}/boost/callable_traits" DESTINATION "include/boost/callable_traits")
diff --git a/cmake/webview2.cmake b/cmake/webview2.cmake
index 9ee3dbc..3827e94 100644
--- a/cmake/webview2.cmake
+++ b/cmake/webview2.cmake
@@ -1,11 +1,11 @@
 include("cmake/nuget.cmake")
 
 if (NOT saucer_prefer_remote)
-    find_package(webview2 CONFIG REQUIRED)
+    find_package(unofficial-webview2 CONFIG REQUIRED)
     return()
 endif()
 
-if (NOT TARGET webview2::webview2)
+if (NOT TARGET unofficial::webview2::webview2)
     nuget_add(WebView2 "Microsoft.Web.WebView2" ${saucer_webview2_version})
 
     add_library(webview2 STATIC IMPORTED)
diff --git a/src/serializer.glaze.cpp b/src/serializer.glaze.cpp
index 38e891c..a4f8768 100644
--- a/src/serializer.glaze.cpp
+++ b/src/serializer.glaze.cpp
@@ -38,7 +38,7 @@ namespace saucer::serializers
     }
 
     template <typename T>
-    tl::expected<T, glz::parse_error> parse_as(const std::string &buffer)
+    tl::expected<T, glz::error_ctx> parse_as(const std::string &buffer)
     {
         static constexpr auto opts = glz::opts{.error_on_missing_keys = true, .raw_string = false};
 
