{"name": "saucer", "version": "2.3.0", "port-version": 1, "description": "Next-gen desktop apps with web-frontend in C++", "homepage": "https://saucer.github.io/", "license": "MIT", "supports": "!uwp", "dependencies": ["boost-callable-traits", "boost-preprocessor", "erei<PERSON>s", "flagpp", "fmt", "glaze", "lockpp", "<PERSON><PERSON><PERSON>-<PERSON><PERSON>", {"name": "qtwebchannel", "platform": "!windows"}, {"name": "qtwebengine", "platform": "!windows"}, "tl-expected", {"name": "vcpkg-cmake", "host": true}, {"name": "webview2", "platform": "windows"}]}