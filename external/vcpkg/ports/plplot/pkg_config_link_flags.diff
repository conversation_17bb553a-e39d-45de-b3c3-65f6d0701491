diff --git a/cmake/modules/pkg-config.cmake b/cmake/modules/pkg-config.cmake
index a0ca6b2..32e8566 100644
--- a/cmake/modules/pkg-config.cmake
+++ b/cmake/modules/pkg-config.cmake
@@ -194,10 +194,14 @@ function(pkg_config_link_flags link_flags_out link_flags_in)
   # Linux, Mac OS X, and MinGW/MSYS but it may need some
   # generalization on other platforms such as Cygwin.
 
+  set(prefix_literal "lib")
   if(CMAKE_SYSTEM_NAME STREQUAL "Linux")
     set(suffix_pattern "(\\.so.*|\\.a)")
   elseif(CMAKE_SYSTEM_NAME STREQUAL "Darwin")
     set(suffix_pattern "(\\.so.*|\\.a|\\.dylib)")
+  elseif(MSVC)
+    set(prefix_literal "")
+    set(suffix_pattern "(\\.[Ll][Ii][Bb])")
   elseif(WIN32_OR_CYGWIN)
     # Order is important here.
     set(suffix_pattern "(\\.dll\\.a|\\.a)")
@@ -210,12 +214,35 @@ function(pkg_config_link_flags link_flags_out link_flags_in)
   set(_link_flags)
   foreach(link_flag IN LISTS link_flags)
     #message(STATUS "link_flag = ${link_flag}")
+    if(TARGET "${link_flag}")
+      string(TOUPPER "${CMAKE_BUILD_TYPE}" config)
+      foreach(key IN ITEMS IMPORTED_IMPLIB_${config} IMPORTED_LOCATION_${config} IMPORTED_IMPLIB IMPORTED_LOCATION)
+        get_target_property(lib "${link_flag}" "${key}")
+        if(lib)
+          set(link_flag "${lib}")
+          break()
+        endif()
+      endforeach()
+      # sorry, no transitive deps for now.
+    endif()
     if(WIN32_OR_CYGWIN)
       # Look for colon-delimited drive-letter form on these platforms.
-      string(REGEX REPLACE "^([a-zA-Z]:/.*)/lib(.*)${suffix_pattern}$" "-L\"\\1\" -l\\2" link_flag "${link_flag}")
     endif(WIN32_OR_CYGWIN)
     # Look for form starting with "/" on all platforms.
-    string(REGEX REPLACE "^(/.*)/lib(.*)${suffix_pattern}$" "-L\"\\1\" -l\\2" link_flag "${link_flag}")
+    if(link_flag MATCHES "^([a-zA-Z]:/.*|/.*)/${prefix_literal}(.+)${suffix_pattern}$")
+      if(CMAKE_MATCH_2 IN_LIST CMAKE_C_IMPLICIT_LINK_LIBRARIES)
+        continue()
+      endif()
+      set(link_flag "-l${CMAKE_MATCH_2}")
+      if(NOT CMAKE_MATCH_1 IN_LIST CMAKE_C_IMPLICIT_LINK_DIRECTORIES)
+        set(lib_dir_flag "-L\"${CMAKE_MATCH_1}\"")
+        if(NOT lib_dir_flag IN_LIST _link_flags)
+          list(APPEND _link_flags "${lib_dir_flag}")
+        endif()
+      endif()
+    elseif(NOT link_flag MATCHES "^-|/")  # e.g. 'm'
+      set(link_flag "-l${link_flag}")
+    endif()
     #message(STATUS "(-L form of link_flag = ${link_flag}")
     list(APPEND _link_flags ${link_flag})
   endforeach(link_flag IN LISTS link_flags)
@@ -290,6 +317,7 @@ macro(cmake_link_flags _link_flags_out _link_flags_in)
     #message("_link_directory_list = ${_link_directory_list}")
     #message("_link_flags_list (without -L options) = ${_link_flags_list}")
 
+    string(REPLACE "-framework;" "-framework " _link_flags_list "${_link_flags_list}")
     # Derive ${_link_flags_out} from _link_flags_list with -l options
     # replaced by complete pathname of library.
     list(LENGTH _link_flags_list _link_flags_length)
