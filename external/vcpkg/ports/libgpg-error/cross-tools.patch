diff --git a/src/Makefile.am b/src/Makefile.am
index 9a86251..00cc2fd 100644
--- a/src/Makefile.am
+++ b/src/Makefile.am
@@ -17,6 +17,12 @@
 # License along with this program; if not, see <https://www.gnu.org/licenses/>.
 # SPDX-License-Identifier: LGPL-2.1+
 
+# Building host tools for native triplet, or not at all
+CPPFLAGS_FOR_BUILD = $(CPPFLAGS)
+CFLAGS_FOR_BUILD = $(CFLAGS)
+LDFLAGS_FOR_BUILD = $(LDFLAGS)
+# Using native tools, either this build or from host triplet
+HOST_TOOLS_PREFIX ?= .
 
 #
 # We distribute the generated sources err-sources.h and err-codes.h,
@@ -261,8 +267,8 @@ mkerrcodes$(EXEEXT_FOR_BUILD): mkerrcodes.c mkerrcodes.h Makefile
 	$(CC_FOR_BUILD) $(CFLAGS_FOR_BUILD) $(LDFLAGS_FOR_BUILD) \
 	$(CPPFLAGS_FOR_BUILD) -I. -I$(srcdir) -o $@ $(srcdir)/mkerrcodes.c
 
-code-from-errno.h: mkerrcodes$(EXEEXT_FOR_BUILD) Makefile
-	./mkerrcodes$(EXEEXT_FOR_BUILD) | $(AWK) -f $(srcdir)/mkerrcodes2.awk >$@
+code-from-errno.h: $(HOST_TOOLS_PREFIX)/mkerrcodes$(EXEEXT_FOR_BUILD) Makefile
+	$(HOST_TOOLS_PREFIX)/mkerrcodes$(EXEEXT_FOR_BUILD) | $(AWK) -f $(srcdir)/mkerrcodes2.awk >$@
 
 errnos-sym.h: Makefile mkstrtable.awk errnos.in
 	$(AWK) -f $(srcdir)/mkstrtable.awk -v textidx=2 -v nogettext=1 \
@@ -306,10 +312,10 @@ endif
 
 # We also depend on versioninfo.rc because that is build by
 # config.status and thus has up-to-date version numbers.
-gpg-error.h: Makefile mkheader$(EXEEXT_FOR_BUILD) $(parts_of_gpg_error_h) \
+gpg-error.h: Makefile $(HOST_TOOLS_PREFIX)/mkheader$(EXEEXT_FOR_BUILD) $(parts_of_gpg_error_h) \
              versioninfo.rc ../config.h
 	$(pre_mkheader_cmds)
-	./mkheader$(EXEEXT_FOR_BUILD) $(mkheader_opts)       \
+	$(HOST_TOOLS_PREFIX)/mkheader$(EXEEXT_FOR_BUILD) $(mkheader_opts)       \
                    $(host_triplet)  $(srcdir)/gpg-error.h.in \
                    ../config.h $(PACKAGE_VERSION) $(VERSION_NUMBER) >$@
 
