{"name": "qtsensors", "version": "6.8.3", "description": "The Qt Sensors API provides access to sensor hardware via QML and C++ interfaces. The Qt Sensors API also provides a motion gesture recognition API for devices.", "homepage": "https://www.qt.io/", "license": null, "dependencies": [{"name": "qtbase", "default-features": false}, {"name": "qtbase", "default-features": false, "features": ["dbus"], "platform": "!(windows & static) & !android & !ios"}, {"name": "qtconnectivity", "default-features": false}], "features": {"qml": {"description": "Build QML imports", "dependencies": [{"name": "qtdeclarative", "default-features": false}]}}}