diff --git a/configure b/configure
index 4794306..76e6334 100755
--- a/configure
+++ b/configure
@@ -20,7 +20,7 @@ for opt do
   ;;
   --mandir=*) mandir="$optarg"
   ;;
-  --datadir=*) datadir="$optarg"
+  --datarootdir=*) datadir="$optarg"
   ;;
   --cc=*) cc="$optarg"
   ;;
@@ -30,10 +30,12 @@ for opt do
   ;;
   --enable-sanitizer) use_sanitizer=yes
   ;;
+  --enable-shared) ENABLE_SHARED=1
+  ;;
+  --enable-static) ENABLE_SHARED=0
+  ;;
   *)
     echo "ERROR: unknown option $opt"
-    echo "Try '$0 --help' for more information"
-    exit 1
   ;;
   esac
 done
@@ -133,6 +135,7 @@ echo "# Automatically generated by configure - do not modify" > $config_host_mak
 printf "# Configured with:" >> $config_host_mak
 printf " '%s'" "$0" "$@" >> $config_host_mak
 echo >> $config_host_mak
+echo "ENABLE_SHARED=${ENABLE_SHARED}" >> $config_host_mak
 
 do_cxx() {
     # Run the compiler, capturing its output to the log.
