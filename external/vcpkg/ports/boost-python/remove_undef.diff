diff --git a/fabscript b/fabscript
index 8188779fd..6a136d82a 100644
--- a/fabscript	
+++ b/fabscript
@@ -28,7 +28,7 @@ class has_numpy(try_run):
     src = r"""
 // If defined, enforces linking against PythonXXd.lib, which
 // is usually not included in Python environments.
-#undef _DEBUG
+
 #include "Python.h"
 #define NPY_NO_DEPRECATED_API NPY_1_7_API_VERSION
 #include "numpy/arrayobject.h"
diff --git a/include/boost/detail/wrap_python.hpp b/include/boost/detail/wrap_python.hpp
index 037e4bf2e..a690913f2 100644
--- a/include/boost/python/detail/wrap_python.hpp	
+++ b/include/boost/python/detail/wrap_python.hpp
@@ -42,8 +42,8 @@
 #   include <math.h>
 #   include <time.h>
 #  endif
-#  undef _DEBUG // Don't let <PERSON> force the debug library just because we're debugging.
-#  define DEBUG_UNDEFINED_FROM_WRAP_PYTHON_H
+//#  undef _DEBUG // Don't let Python force the debug library just because we're debugging.
+//#  define DEBUG_UNDEFINED_FROM_WRAP_PYTHON_H
 # endif
 #endif
 
