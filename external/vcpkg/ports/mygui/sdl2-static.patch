diff --git a/CMake/Packages/FindSDL2.cmake b/CMake/Packages/FindSDL2.cmake
index 4dd33abe4..3911acef3 100644
--- a/CMake/Packages/FindSDL2.cmake
+++ b/CMake/Packages/FindSDL2.cmake
@@ -49,7 +49,7 @@ IF(NOT SDL2_FOUND)
     )
 
     find_library(SDL2_LIBRARY
-      NAMES SDL2
+      NAMES SDL2 SDL2-static
       HINTS
         ${PC_SDL2_LIBDIR}
         ${PC_SDL2_LIBRARY_DIRS}
@@ -87,7 +87,7 @@ IF(NOT SDL2_FOUND)
     set(SDL2_INCLUDE_DIRS ${SDL2_INCLUDE_DIR})
     set(SDL2_LIBRARIES ${SDL2MAIN_LIBRARY} ${SDL2_LIBRARY})
     if (WIN32)
-        set(SDL2_LIBRARIES ${SDL2_LIBRARIES} winmm imm32 version)
+        set(SDL2_LIBRARIES ${SDL2_LIBRARIES} winmm imm32 version setupapi)
     endif()
     include(FindPackageHandleStandardArgs)
 
diff --git a/CMake/Packages/FindSDL2_image.cmake b/CMake/Packages/FindSDL2_image.cmake
index 4f0bc5f17..857806b1f 100644
--- a/CMake/Packages/FindSDL2_image.cmake
+++ b/CMake/Packages/FindSDL2_image.cmake
@@ -41,7 +41,7 @@ IF(NOT SDL2_IMAGE_FOUND)
     )
 
     find_library(SDL2_IMAGE_LIBRARY
-      NAMES SDL2_image
+      NAMES SDL2_image SDL2_image-static
       HINTS
         ${PC_SDL2_IMAGE_LIBDIR}
         ${PC_SDL2_IMAGE_LIBRARY_DIRS}
