diff --git a/Platforms/DirectX/DirectXPlatform/CMakeLists.txt b/Platforms/DirectX/DirectXPlatform/CMakeLists.txt
index 169a2afe4..2ea45af2b 100644
--- a/Platforms/DirectX/DirectXPlatform/CMakeLists.txt
+++ b/Platforms/DirectX/DirectXPlatform/CMakeLists.txt
@@ -9,7 +9,7 @@ include_directories(
 
 include(${PROJECTNAME}.list)
 
-add_library(${PROJECTNAME} ${HEADER_FILES} ${SOURCE_FILES})
+add_library(${PROJECTNAME} STATIC ${HEADER_FILES} ${SOURCE_FILES})
 
 add_dependencies(${PROJECTNAME} MyGUIEngine)
 
diff --git a/Platforms/DirectX11/DirectX11Platform/CMakeLists.txt b/Platforms/DirectX11/DirectX11Platform/CMakeLists.txt
index 251fcdbe3..16d4f33a5 100644
--- a/Platforms/DirectX11/DirectX11Platform/CMakeLists.txt
+++ b/Platforms/DirectX11/DirectX11Platform/CMakeLists.txt
@@ -9,7 +9,7 @@ include_directories(
 
 include(${PROJECTNAME}.list)
 
-add_library(${PROJECTNAME} ${HEADER_FILES} ${SOURCE_FILES})
+add_library(${PROJECTNAME} STATIC ${HEADER_FILES} ${SOURCE_FILES})
 
 add_dependencies(${PROJECTNAME} MyGUIEngine)
 
diff --git a/Platforms/Dummy/DummyPlatform/CMakeLists.txt b/Platforms/Dummy/DummyPlatform/CMakeLists.txt
index 1055694f2..625391132 100644
--- a/Platforms/Dummy/DummyPlatform/CMakeLists.txt
+++ b/Platforms/Dummy/DummyPlatform/CMakeLists.txt
@@ -8,7 +8,7 @@ include_directories(
 
 include(${PROJECTNAME}.list)
 
-add_library(${PROJECTNAME} ${HEADER_FILES} ${SOURCE_FILES})
+add_library(${PROJECTNAME} STATIC ${HEADER_FILES} ${SOURCE_FILES})
 
 add_dependencies(${PROJECTNAME} MyGUIEngine)
 
diff --git a/Platforms/Ogre/OgrePlatform/CMakeLists.txt b/Platforms/Ogre/OgrePlatform/CMakeLists.txt
index a151abcf8..23f8a2373 100644
--- a/Platforms/Ogre/OgrePlatform/CMakeLists.txt
+++ b/Platforms/Ogre/OgrePlatform/CMakeLists.txt
@@ -8,7 +8,7 @@ include_directories(
 
 include(${PROJECTNAME}.list)
 
-add_library(${PROJECTNAME} ${HEADER_FILES} ${SOURCE_FILES})
+add_library(${PROJECTNAME} STATIC ${HEADER_FILES} ${SOURCE_FILES})
 
 add_dependencies(${PROJECTNAME} MyGUIEngine)
 
diff --git a/Platforms/OpenGL/OpenGLPlatform/CMakeLists.txt b/Platforms/OpenGL/OpenGLPlatform/CMakeLists.txt
index 0d58d3d71..78eaf0267 100644
--- a/Platforms/OpenGL/OpenGLPlatform/CMakeLists.txt
+++ b/Platforms/OpenGL/OpenGLPlatform/CMakeLists.txt
@@ -19,7 +19,7 @@ if (NOT MYGUI_USE_SYSTEM_GLEW)
 endif ()
 add_definitions(-DGL_GLEXT_PROTOTYPES)
 
-add_library(${PROJECTNAME} ${HEADER_FILES} ${SOURCE_FILES})
+add_library(${PROJECTNAME} STATIC ${HEADER_FILES} ${SOURCE_FILES})
 
 add_dependencies(${PROJECTNAME} MyGUIEngine)
 
diff --git a/Platforms/OpenGL3/OpenGL3Platform/CMakeLists.txt b/Platforms/OpenGL3/OpenGL3Platform/CMakeLists.txt
index 27d96da3e..4dcee1601 100644
--- a/Platforms/OpenGL3/OpenGL3Platform/CMakeLists.txt
+++ b/Platforms/OpenGL3/OpenGL3Platform/CMakeLists.txt
@@ -19,7 +19,7 @@ if (NOT MYGUI_USE_SYSTEM_GLEW)
 endif ()
 add_definitions(-DGL_GLEXT_PROTOTYPES)
 
-add_library(${PROJECTNAME} ${HEADER_FILES} ${SOURCE_FILES})
+add_library(${PROJECTNAME} STATIC ${HEADER_FILES} ${SOURCE_FILES})
 
 add_dependencies(${PROJECTNAME} MyGUIEngine)
 
diff --git a/Platforms/OpenGLES/OpenGLESPlatform/CMakeLists.txt b/Platforms/OpenGLES/OpenGLESPlatform/CMakeLists.txt
index bd6d9657b..ad31fc158 100644
--- a/Platforms/OpenGLES/OpenGLESPlatform/CMakeLists.txt
+++ b/Platforms/OpenGLES/OpenGLESPlatform/CMakeLists.txt
@@ -9,7 +9,7 @@ include_directories(
 
 include(${PROJECTNAME}.list)
 add_definitions(-DGL_GLEXT_PROTOTYPES)
-add_library(${PROJECTNAME} ${HEADER_FILES} ${SOURCE_FILES})
+add_library(${PROJECTNAME} STATIC ${HEADER_FILES} ${SOURCE_FILES})
 
 add_dependencies(${PROJECTNAME} MyGUIEngine)
 
From 32e39f8de4b750ed5d2332ee18846f23b7e98df5 Mon Sep 17 00:00:00 2001
From: AnyOldName3 <<EMAIL>>
Date: Tue, 29 Oct 2024 14:53:52 +0000
Subject: [PATCH] Explicitly mark another library as static

I've also figured out why this problem wasn't obvious.
It only happens when someone's used the CMake-standard BUILD_SHARED_LIBS option, which controls the default behaviour for add_library.
When MyGUI is built as a standalone project, this won't be set (unless someone's done it manually) so it's fine.
When MyGUI is built as part of a larger project (e.g. package manager like vcpkg or CPM, or as a nested project with FetchContent), it's likely to have been set by something else.

It might be a good idea to pick the default value of MYGUI_STATIC to match BUILD_SHARED_LIBS if it's set, but I've not done that in this PR.
---
 Common/CMakeLists.txt | 2 +-
 1 file changed, 1 insertion(+), 1 deletion(-)

diff --git a/Common/CMakeLists.txt b/Common/CMakeLists.txt
index e97507019..87a16290f 100644
--- a/Common/CMakeLists.txt
+++ b/Common/CMakeLists.txt
@@ -99,7 +99,7 @@ SOURCE_GROUP("Base" FILES
 	Input/SDL/ResourceSDLPointer.cpp
 )
 
-add_library(${PROJECTNAME} ${HEADER_FILES} ${SOURCE_FILES})
+add_library(${PROJECTNAME} STATIC ${HEADER_FILES} ${SOURCE_FILES})
 
 mygui_set_platform_name(${MYGUI_RENDERSYSTEM})
 add_dependencies(${PROJECTNAME} MyGUI.${MYGUI_PLATFORM_NAME}Platform)
