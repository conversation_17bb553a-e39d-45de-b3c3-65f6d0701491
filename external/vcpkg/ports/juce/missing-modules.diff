diff --git a/extras/Build/CMake/JUCEConfig.cmake.in b/extras/Build/CMake/JUCEConfig.cmake.in
index c0c1eef..03a09ef 100644
--- a/extras/Build/CMake/JUCEConfig.cmake.in
+++ b/extras/Build/CMake/JUCEConfig.cmake.in
@@ -39,6 +39,7 @@ include("@PACKAGE_UTILS_INSTALL_DIR@/JUCEUtils.cmake")
 
 set(_juce_modules
     juce_analytics
+    juce_animation
     juce_audio_basics
     juce_audio_devices
     juce_audio_formats
@@ -55,6 +56,7 @@ set(_juce_modules
     juce_gui_basics
     juce_gui_extra
     juce_javascript
+    juce_midi_ci
     juce_opengl
     juce_osc
     juce_product_unlocking
