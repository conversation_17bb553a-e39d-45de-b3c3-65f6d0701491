{"$schema": "https://raw.githubusercontent.com/microsoft/vcpkg-tool/main/docs/vcpkg.schema.json", "name": "juce", "version": "8.0.4", "port-version": 2, "description": "Open-source cross-platform C++ application framework for desktop and mobile applications, including VST, VST3, AU, AUv3, AAX and LV2 audio plug-ins and plug-in hosts", "homepage": "https://juce.com", "license": null, "supports": "!uwp", "dependencies": [{"name": "juce", "host": true, "default-features": false, "features": ["juce<PERSON>e"]}, {"name": "oboe", "platform": "android"}, "opengl", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "default-features": [{"name": "alsa", "platform": "linux"}, {"name": "curl", "platform": "freebsd | linux | openbsd"}, {"name": "fontconfig", "platform": "freebsd | linux | openbsd"}, {"name": "xcursor", "platform": "linux"}, {"name": "xinerama", "platform": "linux"}, {"name": "x<PERSON>r", "platform": "linux"}, {"name": "xrender", "platform": "linux"}], "features": {"alsa": {"description": "Enable ALSA support", "dependencies": ["alsa"]}, "curl": {"description": "Use CURL for network support", "supports": "freebsd | linux | openbsd", "dependencies": [{"name": "curl", "default-features": false}]}, "extras": {"description": "Enable JUCE extras such as Projucer, AudioPluginHost, BinaryBuilder.", "dependencies": [{"name": "juce", "default-features": false, "features": ["freetype"]}, {"name": "juce", "default-features": false, "features": ["alsa"], "platform": "linux"}, {"name": "juce", "default-features": false, "features": ["curl"], "platform": "freebsd | linux | openbsd"}]}, "fontconfig": {"description": "Enable Fontconfig support", "dependencies": ["fontconfig", {"name": "juce", "default-features": false, "features": ["freetype"]}]}, "freetype": {"description": "Enable FreeType support", "dependencies": ["freetype"]}, "jack": {"description": "Enable JACK audio support", "dependencies": ["jack2"]}, "juceaide": {"description": "Build the juceaide tool", "supports": "native", "dependencies": [{"name": "juce", "default-features": false, "features": ["freetype"]}]}, "ladspa": {"description": "Enable LADSPA plugin host support."}, "web-browser": {"description": "Enable WebBrowser support", "dependencies": [{"name": "webview2", "platform": "windows"}]}, "xcursor": {"description": "Enable XCursor support"}, "xinerama": {"description": "Enable Xinerama support", "dependencies": ["libxinerama"]}, "xrandr": {"description": "Enable XRandR support", "dependencies": ["libxrandr"]}, "xrender": {"description": "Enable XRender support", "dependencies": ["libxrender"]}}}