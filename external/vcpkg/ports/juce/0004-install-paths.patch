diff --git a/CMakeLists.txt b/CMakeLists.txt
index 4262a9852..a46b51f1a 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -167,14 +167,14 @@ if(("${CMAKE_SOURCE_DIR}" STREQUAL "${JUCE_SOURCE_DIR}") AND (NOT JUCE_BUILD_HEL
     _juce_add_lv2_manifest_helper_target()

     if(TARGET juce_lv2_helper)
-        install(TARGETS juce_lv2_helper EXPORT LV2_HELPER DESTINATION "bin/JUCE-${JUCE_VERSION}")
+        install(TARGETS juce_lv2_helper EXPORT LV2_HELPER DESTINATION "${JUCE_TOOL_INSTALL_DIR}")
         install(EXPORT LV2_HELPER NAMESPACE juce:: DESTINATION "${JUCE_INSTALL_DESTINATION}")
     endif()

     _juce_add_vst3_manifest_helper_target()

     if(TARGET juce_vst3_helper)
-        install(TARGETS juce_vst3_helper EXPORT VST3_HELPER DESTINATION "bin/JUCE-${JUCE_VERSION}")
+        install(TARGETS juce_vst3_helper EXPORT VST3_HELPER DESTINATION "${JUCE_TOOL_INSTALL_DIR}")
         install(EXPORT VST3_HELPER NAMESPACE juce:: DESTINATION "${JUCE_INSTALL_DESTINATION}")
     endif()
 endif()
--
2.34.1
