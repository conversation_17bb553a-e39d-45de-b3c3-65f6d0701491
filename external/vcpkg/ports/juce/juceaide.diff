diff --git a/CMakeLists.txt b/CMakeLists.txt
index 49d85c6..76209f5 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -1,3 +1,5 @@
+# Save cmake input for nested juceaide build
+get_cmake_property(vcpkg_input_vars CACHE_VARIABLES)
 # ==============================================================================
 #
 #  This file is part of the JUCE framework.
@@ -142,7 +144,10 @@ set(JUCE_INSTALL_DESTINATION "lib/cmake/JUCE-${JUCE_VERSION}" CACHE STRING
 
 set(JUCE_MODULE_PATH "include/JUCE-${JUCE_VERSION}/modules")
 set(UTILS_INSTALL_DIR "${JUCE_INSTALL_DESTINATION}")
-set(JUCEAIDE_PATH "${JUCE_TOOL_INSTALL_DIR}/${JUCE_JUCEAIDE_NAME}")
+set(JUCEAIDE_PATH "${VCPKG_INSTALLED_DIR}/${VCPKG_TARGET_TRIPLET}/tools/juce/${JUCE_JUCEAIDE_NAME}")
+if(WITH_JUCEAIDE)
+    set(JUCEAIDE_PATH "${WITH_JUCEAIDE}")
+endif()
 configure_package_config_file("${JUCE_CMAKE_UTILS_DIR}/JUCEConfig.cmake.in"
     "${JUCE_BINARY_DIR}/JUCEConfig.cmake"
     PATH_VARS UTILS_INSTALL_DIR JUCEAIDE_PATH JUCE_MODULE_PATH
diff --git a/extras/Build/juceaide/CMakeLists.txt b/extras/Build/juceaide/CMakeLists.txt
index 651aa53..9ff0fba 100644
--- a/extras/Build/juceaide/CMakeLists.txt
+++ b/extras/Build/juceaide/CMakeLists.txt
@@ -55,12 +55,16 @@ if(JUCE_BUILD_HELPER_TOOLS)
         juce::juce_recommended_lto_flags
         juce::juce_recommended_warning_flags)
 
-    set_target_properties(juceaide PROPERTIES
-        MSVC_RUNTIME_LIBRARY "MultiThreaded$<$<CONFIG:Debug>:Debug>")
 
     export(TARGETS juceaide
            NAMESPACE juce_tools::
            FILE "${JUCE_BINARY_DIR}/JUCEToolsExport.cmake")
+
+elseif(WITH_JUCEAIDE)
+    add_executable(juceaide IMPORTED GLOBAL)
+    set_target_properties(juceaide PROPERTIES IMPORTED_LOCATION "${WITH_JUCEAIDE}")
+    add_executable(juce::juceaide ALIAS juceaide)
+
 else()
     message(STATUS "Configuring juceaide")
 
@@ -111,13 +115,18 @@ else()
         set(ENV{CMAKE_GENERATOR_PLATFORM} "${CMAKE_HOST_SYSTEM_PROCESSOR}")
     endif()
 
+    set(options "")
+    list(REMOVE_ITEM vcpkg_input_vars "JUCE_BUILD_EXTRAS")
+    foreach(var IN LISTS vcpkg_input_vars)
+        list(APPEND options "-D${var}=$CACHE{${var}}")
+    endforeach()
     # Looks like we're bootstrapping, reinvoke CMake
     execute_process(COMMAND "${CMAKE_COMMAND}"
             "."
             "-B${JUCE_BINARY_DIR}/tools"
             "-G${CMAKE_GENERATOR}"
             "-DCMAKE_MAKE_PROGRAM=${CMAKE_MAKE_PROGRAM}"
-            "-DCMAKE_BUILD_TYPE=Debug"
+            ${options}
             "-DJUCE_BUILD_HELPER_TOOLS=ON"
             "-DCMAKE_INSTALL_PREFIX=${CMAKE_INSTALL_PREFIX}"
         WORKING_DIRECTORY "${JUCE_SOURCE_DIR}"
@@ -133,7 +142,7 @@ else()
 
     execute_process(COMMAND "${CMAKE_COMMAND}"
             --build "${JUCE_BINARY_DIR}/tools"
-            --config Debug
+            --target juceaide
         OUTPUT_VARIABLE command_output
         ERROR_VARIABLE command_output
         RESULT_VARIABLE result_variable)
@@ -148,7 +157,7 @@ else()
     include("${JUCE_BINARY_DIR}/tools/JUCEToolsExport.cmake")
 
     add_executable(juceaide IMPORTED GLOBAL)
-    get_target_property(imported_location juce_tools::juceaide IMPORTED_LOCATION_DEBUG)
+    get_target_property(imported_location juce_tools::juceaide IMPORTED_LOCATION_RELEASE)
     set_target_properties(juceaide PROPERTIES IMPORTED_LOCATION "${imported_location}")
 
     add_executable(juce::juceaide ALIAS juceaide)
