{"name": "open62541", "version": "1.3.15", "port-version": 2, "description": "open62541 is an open source C (C99) implementation of OPC UA licensed under the Mozilla Public License v2.0.", "homepage": "https://open62541.org", "license": "MPL-2.0", "supports": "!xbox", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "default-features": ["methodcalls", "subscriptions", "subscriptions-events"], "features": {"amalgamation": {"description": "Concatenate the library to a single file open62541.h/.c"}, "diagnostics": {"description": "Enable diagnostics information exposed by the server", "dependencies": [{"name": "open62541", "features": ["subscriptions-events"]}]}, "discovery": {"description": "Enable Discovery Service (LDS)"}, "historizing": {"description": "Enable basic support for historical access (client and server)", "dependencies": [{"name": "open62541", "features": ["subscriptions-events"]}]}, "mbedtls": {"description": "Enable encryption support (uses MbedTLS)", "dependencies": ["mbedtls"]}, "methodcalls": {"description": "Enable the Method service set"}, "multithreading": {"description": "Enable multi threading support", "dependencies": [{"name": "open62541", "features": ["methodcalls"]}]}, "openssl": {"description": "Enable encryption support (uses OpenSSL)", "dependencies": ["openssl"]}, "subscriptions": {"description": "Enable subscriptions support"}, "subscriptions-events": {"description": "Enable event monitoring", "dependencies": [{"name": "open62541", "features": ["subscriptions"]}]}}}