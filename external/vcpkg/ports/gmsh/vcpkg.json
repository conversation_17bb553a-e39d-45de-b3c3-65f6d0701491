{"name": "gmsh", "version": "4.13.1", "description": "Gmsh is an open source 3D finite element mesh generator with a built-in CAD engine and post-processor.", "homepage": "https://gmsh.info", "license": "LGPL-2.0-or-later", "supports": "!uwp", "dependencies": ["blas", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"graphics": {"description": "Enable building graphics lib even without GUI (advanced)", "dependencies": ["libjpeg-turbo", "libpng", "opengl", "zlib"]}, "mpi": {"description": "Enable MPI (experimental, not used for meshing)", "dependencies": ["openmpi"]}, "occ": {"description": "Enable OpenCASCADE modules", "dependencies": ["caf", "freetype", "opencascade", "tbb"]}, "zipper": {"description": "Enable Zip file compression/decompression", "dependencies": ["zlib"]}}}