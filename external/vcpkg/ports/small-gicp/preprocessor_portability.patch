diff --git a/include/small_gicp/registration/reduction_omp.hpp b/include/small_gicp/registration/reduction_omp.hpp
index 8c11267..7edf4a5 100644
--- a/include/small_gicp/registration/reduction_omp.hpp
+++ b/include/small_gicp/registration/reduction_omp.hpp
@@ -7,7 +7,7 @@
 namespace small_gicp {
 
 #ifndef _OPENMP
-#warning "OpenMP is not available. Parallel reduction will be disabled."
+#pragma message ( "OpenMP is not available. Parallel reduction will be disabled." )
 inline int omp_get_thread_num() {
   return 0;
 }
