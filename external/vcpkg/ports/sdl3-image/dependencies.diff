diff --git a/CMakeLists.txt b/CMakeLists.txt
index 0a0cc03..27a009d 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -293,6 +293,8 @@ target_compile_definitions(${sdl3_image_target_name} PRIVATE
 target_link_libraries(${sdl3_image_target_name} PUBLIC SDL3::Headers)
 if(SDLIMAGE_BUILD_SHARED_LIBS)
     target_link_libraries(${sdl3_image_target_name} PRIVATE SDL3::SDL3-shared)
+else()
+    target_link_libraries(${sdl3_image_target_name} PRIVATE SDL3::SDL3)
 endif()
 sdl_add_warning_options(${sdl3_image_target_name} WARNING_AS_ERROR ${SDLIMAGE_WERROR})
 if(WIN32 AND SDLIMAGE_BUILD_SHARED_LIBS)
@@ -971,7 +973,7 @@ if(SDLIMAGE_WEBP)
         message(STATUS "${PROJECT_NAME}: Using libwebp from CMake variable")
         set(SDLIMAGE_WEBP_ENABLED TRUE)
     else()
-        find_package(webp ${required})
+        find_package(webp NAMES WebP ${required})
         if(webp_FOUND)
             message(STATUS "${PROJECT_NAME}: Using system libwebp")
             set(SDLIMAGE_WEBP_ENABLED TRUE)
diff --git a/cmake/SDL3_imageConfig.cmake.in b/cmake/SDL3_imageConfig.cmake.in
index db72fd7..d8f91ff 100644
--- a/cmake/SDL3_imageConfig.cmake.in
+++ b/cmake/SDL3_imageConfig.cmake.in
@@ -6,6 +6,9 @@ set_package_properties(SDL3_image PROPERTIES
     DESCRIPTION "SDL_image is an image file loading library"
 )
 
+include(CMakeFindDependencyMacro)
+find_dependency(SDL3 CONFIG)
+
 set(SDL3_image_FOUND ON)
 
 set(SDLIMAGE_AVIF          @SDLIMAGE_AVIF_ENABLED@)
@@ -110,8 +113,7 @@ if(EXISTS "${CMAKE_CURRENT_LIST_DIR}/SDL3_image-static-targets.cmake")
         endif()
 
         if(SDLIMAGE_WEBP AND NOT TARGET WebP::webp AND NOT SDLIMAGE_WEBP_SHARED)
-            list(APPEND webp_ROOT "${CMAKE_CURRENT_LIST_DIR}")
-            find_dependency(webp)
+            find_dependency(WebP)
         endif()
 
         set(CMAKE_MODULE_PATH "${_sdl_cmake_module_path}")
