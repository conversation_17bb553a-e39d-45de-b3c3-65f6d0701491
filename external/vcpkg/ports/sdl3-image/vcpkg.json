{"name": "sdl3-image", "version": "3.2.4", "description": "SDL_image is an image file loading library. It loads images as SDL surfaces and textures, and supports the following formats: BMP, GIF, JPEG, LBM, PCX, PNG, PNM, TGA, TIFF, WEBP, XCF, XPM, XV", "homepage": "https://github.com/libsdl-org/SDL_image", "license": "<PERSON><PERSON><PERSON>", "dependencies": [{"name": "sdl3", "default-features": false}, {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"jpeg": {"description": "Support for JPEG image format", "dependencies": ["libjpeg-turbo"]}, "png": {"description": "Support for PNG image format", "dependencies": ["libpng"]}, "tiff": {"description": "Support for TIFF image format", "dependencies": [{"name": "tiff", "default-features": false}]}, "webp": {"description": "Support for WEBP image format.", "dependencies": ["libwebp"]}}}