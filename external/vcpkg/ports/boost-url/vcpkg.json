{"$comment": "Automatically generated by scripts/boost/generate-ports.ps1", "name": "boost-url", "version": "1.87.0", "description": "Boost url module", "homepage": "https://www.boost.org/libs/url", "license": "BSL-1.0", "dependencies": [{"name": "boost-align", "version>=": "1.87.0"}, {"name": "boost-assert", "version>=": "1.87.0"}, {"name": "boost-cmake", "version>=": "1.87.0"}, {"name": "boost-config", "version>=": "1.87.0"}, {"name": "boost-core", "version>=": "1.87.0"}, {"name": "boost-headers", "version>=": "1.87.0"}, {"name": "boost-mp11", "version>=": "1.87.0"}, {"name": "boost-optional", "version>=": "1.87.0"}, {"name": "boost-static-assert", "version>=": "1.87.0"}, {"name": "boost-system", "version>=": "1.87.0"}, {"name": "boost-throw-exception", "version>=": "1.87.0"}, {"name": "boost-type-traits", "version>=": "1.87.0"}, {"name": "boost-variant2", "version>=": "1.87.0"}]}