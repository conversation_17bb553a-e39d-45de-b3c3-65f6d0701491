diff --git a/CMakeLists.txt b/CMakeLists.txt
index 78d96ff..3215aca 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -65,7 +65,39 @@ endif()
 ##################################################
 add_library(boost_flags INTERFACE)
 #target_include_directories(boost_flags PUBLIC ${Boost_INCLUDE_DIRS}) 
-target_include_directories(boost_flags INTERFACE ${CMAKE_SOURCE_DIR}/include)
+target_include_directories(boost_flags
+                           INTERFACE
+                            $<BUILD_INTERFACE:${CMAKE_SOURCE_DIR}/include>
+                            $<INSTALL_INTERFACE:include>
+                           )
+set_target_properties(boost_flags PROPERTIES
+  EXPORT_NAME flags
+)
+include(CMakePackageConfigHelpers)
+
+configure_package_config_file(
+  cmake/unofficial-tobias-loew-flags-config.cmake.in
+  "${CMAKE_CURRENT_BINARY_DIR}/cmake/unofficial-tobias-loew-flags-config.cmake"
+  INSTALL_DESTINATION share/unofficial-tobias-loew-flags/
+  NO_CHECK_REQUIRED_COMPONENTS_MACRO)
+
+# Install.
+install(TARGETS boost_flags EXPORT unofficial-tobias-loew-flags)
+
+install(
+  EXPORT unofficial-tobias-loew-flags
+  NAMESPACE unofficial::tobias-loew-flags::
+  FILE unofficial-tobias-loew-flags-targets.cmake
+  DESTINATION share/unofficial-tobias-loew-flags/)
+
+install(DIRECTORY include/boost DESTINATION include)
+
+install(
+  FILES
+  "${CMAKE_CURRENT_BINARY_DIR}/cmake/unofficial-tobias-loew-flags-config.cmake"
+  DESTINATION share/unofficial-tobias-loew-flags)
+
+if(0)
 if (Boost_FOUND)
    target_include_directories(boost_flags INTERFACE ${Boost_INCLUDE_DIRS})
 endif()
@@ -79,4 +111,5 @@ endif()
 # Subdirectories
 ##################################################
 add_subdirectory(test)
+endif()
 # add_subdirectory(example)
diff --git a/cmake/unofficial-tobias-loew-flags-config.cmake.in b/cmake/unofficial-tobias-loew-flags-config.cmake.in
new file mode 100644
index 0000000..9a784e1
--- /dev/null
+++ b/cmake/unofficial-tobias-loew-flags-config.cmake.in
@@ -0,0 +1,5 @@
+
+@PACKAGE_INIT@
+
+include("${CMAKE_CURRENT_LIST_DIR}/unofficial-tobias-loew-flags-targets.cmake")
+
