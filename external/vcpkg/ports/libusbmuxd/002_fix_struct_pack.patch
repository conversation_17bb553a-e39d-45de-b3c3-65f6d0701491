diff --git a/include/usbmuxd-proto.h b/include/usbmuxd-proto.h
index 38473f5..d004232 100644
--- a/include/usbmuxd-proto.h
+++ b/include/usbmuxd-proto.h
@@ -57,28 +57,30 @@ enum usbmuxd_msgtype {
 	MESSAGE_PLIST = 8,
 };
 
+#pragma pack(push, 1)
+
 struct usbmuxd_header {
 	uint32_t length;    // length of message, including header
 	uint32_t version;   // protocol version
 	uint32_t message;   // message type
 	uint32_t tag;       // responses to this query will echo back this tag
-} __attribute__((__packed__));
+};
 
 struct usbmuxd_result_msg {
 	struct usbmuxd_header header;
 	uint32_t result;
-} __attribute__((__packed__));
+};
 
 struct usbmuxd_connect_request {
 	struct usbmuxd_header header;
 	uint32_t device_id;
 	uint16_t port;   // TCP port number
 	uint16_t reserved;   // set to zero
-} __attribute__((__packed__));
+};
 
 struct usbmuxd_listen_request {
 	struct usbmuxd_header header;
-} __attribute__((__packed__));
+};
 
 struct usbmuxd_device_record {
 	uint32_t device_id;
@@ -86,7 +88,9 @@ struct usbmuxd_device_record {
 	char serial_number[256];
 	uint16_t padding;
 	uint32_t location;
-} __attribute__((__packed__));
+};
+
+#pragma pack(pop)
 
 #ifdef __cplusplus
 }
