From 363737b9e0e5c2f9de85a0caf641204e638115e3 Mon Sep 17 00:00:00 2001
From: <PERSON><PERSON> <mark.ros<PERSON><PERSON>@gmail.com>
Date: Thu, 12 Aug 2021 22:09:34 -0400
Subject: [PATCH] Remove post build events that copy files out of the tree

---
 visualc/vngspice-fftw.vcxproj | 24 ------------------------
 visualc/vngspice.vcxproj      | 17 +++++------------
 2 <USER> <GROUP>, 5 insertions(+), 36 deletions(-)

diff --git a/visualc/vngspice-fftw.vcxproj b/visualc/vngspice-fftw.vcxproj
index 14773c8..178aa73 100644
--- a/visualc/vngspice-fftw.vcxproj
+++ b/visualc/vngspice-fftw.vcxproj
@@ -238,8 +238,6 @@ lib /machine:x86 /def:..\..\fftw-3.3-dll32\libfftw3-3.def /out:$(IntDir)libfftw3
     </Link>
     <PostBuildEvent>
       <Command>
-      copy /y "..\..\fftw-3.3-dll32\libfftw3-3.dll" "$(OutDir)"
-      make-install-vngspiced.bat $(OutDir) fftw
       </Command>
     </PostBuildEvent>
   </ItemDefinitionGroup>
@@ -289,8 +287,6 @@ lib /machine:x86 /def:..\..\fftw-3.3-dll32\libfftw3-3.def /out:$(IntDir)libfftw3
     </Link>
     <PostBuildEvent>
       <Command>
-      copy /y "..\..\fftw-3.3-dll32\libfftw3-3.dll" "$(OutDir)"
-      make-install-vngspice.bat $(OutDir) fftw
       </Command>
     </PostBuildEvent>
   </ItemDefinitionGroup>
@@ -337,8 +333,6 @@ lib /machine:x64 /def:..\..\fftw-3.3-dll64\libfftw3-3.def /out:$(IntDir)libfftw3
     </Link>
     <PostBuildEvent>
       <Command>
-      copy /y "..\..\fftw-3.3-dll64\libfftw3-3.dll" "$(OutDir)"
-      make-install-vngspiced.bat $(OutDir) fftw 64
       </Command>
     </PostBuildEvent>
   </ItemDefinitionGroup>
@@ -393,8 +387,6 @@ lib /machine:x64 /def:..\..\fftw-3.3-dll64\libfftw3-3.def /out:$(IntDir)libfftw3
     </Link>
     <PostBuildEvent>
       <Command>
-      copy /y "..\..\fftw-3.3-dll64\libfftw3-3.dll" "$(OutDir)"
-      make-install-vngspice.bat $(OutDir) fftw 64
       </Command>
     </PostBuildEvent>
   </ItemDefinitionGroup>
@@ -438,8 +430,6 @@ lib /machine:x86 /def:..\..\fftw-3.3-dll32\libfftw3-3.def /out:$(IntDir)libfftw3
     </Link>
     <PostBuildEvent>
       <Command>
-      copy /y "..\..\fftw-3.3-dll32\libfftw3-3.dll" "$(OutDir)"
-      make-install-vngspiced.bat $(OutDir) fftw
       </Command>
     </PostBuildEvent>
   </ItemDefinitionGroup>
@@ -489,8 +479,6 @@ lib /machine:x86 /def:..\..\fftw-3.3-dll32\libfftw3-3.def /out:$(IntDir)libfftw3
     </Link>
     <PostBuildEvent>
       <Command>
-      copy /y "..\..\fftw-3.3-dll32\libfftw3-3.dll" "$(OutDir)"
-      make-install-vngspice.bat $(OutDir) fftw
       </Command>
     </PostBuildEvent>
   </ItemDefinitionGroup>
@@ -537,8 +525,6 @@ lib /machine:x64 /def:..\..\fftw-3.3-dll64\libfftw3-3.def /out:$(IntDir)libfftw3
     </Link>
     <PostBuildEvent>
       <Command>
-      copy /y "..\..\fftw-3.3-dll64\libfftw3-3.dll" "$(OutDir)"
-      make-install-vngspiced.bat $(OutDir) fftw 64
       </Command>
     </PostBuildEvent>
   </ItemDefinitionGroup>
@@ -591,8 +577,6 @@ lib /machine:x64 /def:..\..\fftw-3.3-dll64\libfftw3-3.def /out:$(IntDir)libfftw3
     </Link>
     <PostBuildEvent>
       <Command>
-      copy /y "..\..\fftw-3.3-dll64\libfftw3-3.dll" "$(OutDir)"
-      make-install-vngspice.bat $(OutDir) fftw 64
       </Command>
     </PostBuildEvent>
   </ItemDefinitionGroup>
@@ -643,8 +627,6 @@ lib /machine:x86 /def:..\..\fftw-3.3-dll32\libfftw3-3.def /out:$(IntDir)libfftw3
     </Link>
     <PostBuildEvent>
       <Command>
-      copy /y "..\..\fftw-3.3-dll32\libfftw3-3.dll" "$(OutDir)"
-      make-install-vngspice.bat $(OutDir) fftw
       </Command>
     </PostBuildEvent>
   </ItemDefinitionGroup>
@@ -700,8 +682,6 @@ lib /machine:x64 /def:..\..\fftw-3.3-dll64\libfftw3-3.def /out:$(IntDir)libfftw3
     </Link>
     <PostBuildEvent>
       <Command>
-      copy /y "..\..\fftw-3.3-dll64\libfftw3-3.dll" "$(OutDir)"
-      make-install-vngspice.bat $(OutDir) fftw 64
       </Command>
     </PostBuildEvent>
   </ItemDefinitionGroup>
@@ -752,8 +732,6 @@ lib /machine:x86 /def:..\..\fftw-3.3-dll32\libfftw3-3.def /out:$(IntDir)libfftw3
     </Link>
     <PostBuildEvent>
       <Command>
-      copy /y "..\..\fftw-3.3-dll32\libfftw3-3.dll" "$(OutDir)"
-      make-install-vngspice.bat $(OutDir) fftw
       </Command>
     </PostBuildEvent>
   </ItemDefinitionGroup>
@@ -807,8 +785,6 @@ lib /machine:x64 /def:..\..\fftw-3.3-dll64\libfftw3-3.def /out:$(IntDir)libfftw3
     </Link>
     <PostBuildEvent>
       <Command>
-      copy /y "..\..\fftw-3.3-dll64\libfftw3-3.dll" "$(OutDir)"
-      make-install-vngspice.bat $(OutDir) fftw 64
       </Command>
     </PostBuildEvent>
   </ItemDefinitionGroup>
diff --git a/visualc/vngspice.vcxproj b/visualc/vngspice.vcxproj
index 7b6ac0b..7190c1e 100644
--- a/visualc/vngspice.vcxproj
+++ b/visualc/vngspice.vcxproj
@@ -242,7 +242,7 @@
       <LargeAddressAware>true</LargeAddressAware>
     </Link>
     <PostBuildEvent>
-      <Command>make-install-vngspiced.bat $(OutDir)</Command>
+      <Command></Command>
     </PostBuildEvent>
     <Manifest>
       <AdditionalManifestFiles>$(ProjectDir)ngspice-x86.exe.manifest</AdditionalManifestFiles>
@@ -293,7 +293,6 @@
     </Link>
     <PostBuildEvent>
       <Command>
-      make-install-vngspice.bat $(OutDir)
       </Command>
     </PostBuildEvent>
     <Manifest>
@@ -342,7 +341,6 @@
     </Link>
     <PostBuildEvent>
       <Command>
-      make-install-vngspiced.bat $(OutDir) 64
       </Command>
     </PostBuildEvent>
     <Manifest>
@@ -399,7 +397,6 @@
     </Link>
     <PostBuildEvent>
       <Command>
-      make-install-vngspice.bat $(OutDir) 64
       </Command>
     </PostBuildEvent>
     <Manifest>
@@ -444,7 +441,7 @@
       <LargeAddressAware>true</LargeAddressAware>
     </Link>
     <PostBuildEvent>
-      <Command>make-install-vngspiced.bat $(OutDir)</Command>
+      <Command></Command>
     </PostBuildEvent>
     <Manifest>
       <AdditionalManifestFiles>$(ProjectDir)ngspice-x86.exe.manifest</AdditionalManifestFiles>
@@ -494,7 +491,7 @@
       <LargeAddressAware>true</LargeAddressAware>
     </Link>
     <PostBuildEvent>
-      <Command>make-install-vngspice.bat $(OutDir)</Command>
+      <Command></Command>
     </PostBuildEvent>
     <Manifest>
       <AdditionalManifestFiles>$(ProjectDir)ngspice-x86.exe.manifest</AdditionalManifestFiles>
@@ -542,7 +539,6 @@
     </Link>
     <PostBuildEvent>
       <Command>
-      make-install-vngspiced.bat $(OutDir) 64
       </Command>
     </PostBuildEvent>
     <Manifest>
@@ -597,7 +593,6 @@
     </Link>
     <PostBuildEvent>
       <Command>
-      make-install-vngspice.bat $(OutDir) 64
       </Command>
     </PostBuildEvent>
     <Manifest>
@@ -649,7 +644,7 @@
       <LargeAddressAware>true</LargeAddressAware>
     </Link>
     <PostBuildEvent>
-      <Command>make-install-vngspice.bat $(OutDir)</Command>
+      <Command></Command>
     </PostBuildEvent>
     <Manifest>
       <AdditionalManifestFiles>$(ProjectDir)ngspice-x86.exe.manifest</AdditionalManifestFiles>
@@ -706,7 +701,6 @@
     </Link>
     <PostBuildEvent>
       <Command>
-      make-install-vngspice.bat $(OutDir) 64
       </Command>
     </PostBuildEvent>
     <Manifest>
@@ -758,7 +752,7 @@
       <LargeAddressAware>true</LargeAddressAware>
     </Link>
     <PostBuildEvent>
-      <Command>make-install-vngspice.bat $(OutDir)</Command>
+      <Command></Command>
     </PostBuildEvent>
     <Manifest>
       <AdditionalManifestFiles>$(ProjectDir)ngspice-x86.exe.manifest</AdditionalManifestFiles>
@@ -813,7 +807,6 @@
     </Link>
     <PostBuildEvent>
       <Command>
-      make-install-vngspice.bat $(OutDir) 64
       </Command>
     </PostBuildEvent>
     <Manifest>
-- 
2.32.0.windows.2

