diff --git a/visualc/vngspice.vcxproj b/visualc/vngspice.vcxproj
index 2d1aa81..cf0f0c7 100644
--- a/visualc/vngspice.vcxproj
+++ b/visualc/vngspice.vcxproj
@@ -2681,12 +2681,12 @@
   <ItemGroup>
     <CustomBuild Include="..\src\frontend\parse-bison.y">
       <Message>invoke win_bison.exe for %(Identity)</Message>
-      <Command>..\..\flex-bison\win_bison.exe --output=.\tmp-bison\%(Filename).c --defines=.\tmp-bison\%(Filename).h %(Identity) || exit 1</Command>
+      <Command>win_bison.exe --output=.\tmp-bison\%(Filename).c --defines=.\tmp-bison\%(Filename).h %(Identity) || exit 1</Command>
       <Outputs>.\tmp-bison\%(Filename).c;.\tmp-bison\%(Filename).h</Outputs>
     </CustomBuild>
     <CustomBuild Include="..\src\spicelib\parser\inpptree-parser.y">
       <Message>invoke win_bison.exe for %(Identity)</Message>
-      <Command>..\..\flex-bison\win_bison.exe --output=.\tmp-bison\%(Filename).c --defines=.\tmp-bison\%(Filename).h %(Identity) || exit 1</Command>
+      <Command>win_bison.exe --output=.\tmp-bison\%(Filename).c --defines=.\tmp-bison\%(Filename).h %(Identity) || exit 1</Command>
       <Outputs>.\tmp-bison\%(Filename).c;.\tmp-bison\%(Filename).h</Outputs>
     </CustomBuild>
   </ItemGroup>
diff --git a/visualc/xspice/cmpp/cmpp.vcxproj b/visualc/xspice/cmpp/cmpp.vcxproj
index 78607a3..7bcc1a4 100644
--- a/visualc/xspice/cmpp/cmpp.vcxproj
+++ b/visualc/xspice/cmpp/cmpp.vcxproj
@@ -157,22 +157,22 @@
   <ItemGroup>
     <CustomBuild Include="..\..\..\src\xspice\cmpp\ifs_lex.l">
       <Message>invoke win_flex.exe for %(Identity)</Message>
-      <Command>..\..\..\..\flex-bison\win_flex.exe --outfile=.\tmp-bison\%(Filename).c --header-file=.\tmp-bison\%(Filename).h %(Identity) || exit 1</Command>
+      <Command>win_flex.exe --outfile=.\tmp-bison\%(Filename).c --header-file=.\tmp-bison\%(Filename).h %(Identity) || exit 1</Command>
       <Outputs>.\tmp-bison\%(Filename).c;.\tmp-bison\%(Filename).h</Outputs>
     </CustomBuild>
     <CustomBuild Include="..\..\..\src\xspice\cmpp\ifs_yacc.y">
       <Message>invoke win_bison.exe for %(Identity)</Message>
-      <Command>..\..\..\..\flex-bison\win_bison.exe --output=.\tmp-bison\%(Filename).c --defines=.\tmp-bison\%(Filename).h %(Identity) || exit 1</Command>
+      <Command>win_bison.exe --output=.\tmp-bison\%(Filename).c --defines=.\tmp-bison\%(Filename).h %(Identity) || exit 1</Command>
       <Outputs>.\tmp-bison\%(Filename).c;.\tmp-bison\%(Filename).h</Outputs>
     </CustomBuild>
     <CustomBuild Include="..\..\..\src\xspice\cmpp\mod_lex.l">
       <Message>invoke win_flex.exe for %(Identity)</Message>
-      <Command>..\..\..\..\flex-bison\win_flex.exe --outfile=.\tmp-bison\%(Filename).c --header-file=.\tmp-bison\%(Filename).h %(Identity) || exit 1</Command>
+      <Command>win_flex.exe --outfile=.\tmp-bison\%(Filename).c --header-file=.\tmp-bison\%(Filename).h %(Identity) || exit 1</Command>
       <Outputs>.\tmp-bison\%(Filename).c;.\tmp-bison\%(Filename).h</Outputs>
     </CustomBuild>
     <CustomBuild Include="..\..\..\src\xspice\cmpp\mod_yacc.y">
       <Message>invoke win_bison.exe for %(Identity)</Message>
-      <Command>..\..\..\..\flex-bison\win_bison.exe --output=.\tmp-bison\%(Filename).c --defines=.\tmp-bison\%(Filename).h %(Identity) || exit 1</Command>
+      <Command>win_bison.exe --output=.\tmp-bison\%(Filename).c --defines=.\tmp-bison\%(Filename).h %(Identity) || exit 1</Command>
       <Outputs>.\tmp-bison\%(Filename).c;.\tmp-bison\%(Filename).h</Outputs>
     </CustomBuild>
     <None Include="..\src\xspice\icm\objects.inc" />
