From dc6b6c0aa4205047e9fd052c401f3f357d83ef57 Mon Sep 17 00:00:00 2001
From: <PERSON><PERSON> <mark.rosz<PERSON>@gmail.com>
Date: Thu, 12 Aug 2021 22:26:43 -0400
Subject: [PATCH] Remove 64 in filename output of codemodels

---
 visualc/xspice/analog.vcxproj     | 4 ++--
 visualc/xspice/digital.vcxproj    | 4 ++--
 visualc/xspice/spice2poly.vcxproj | 4 ++--
 visualc/xspice/table.vcxproj      | 4 ++--
 visualc/xspice/xtradev.vcxproj    | 4 ++--
 visualc/xspice/xtraevt.vcxproj    | 4 ++--
 6 files changed, 12 insertions(+), 12 deletions(-)

diff --git a/visualc/xspice/analog.vcxproj b/visualc/xspice/analog.vcxproj
index cf65a37..8b320ce 100644
--- a/visualc/xspice/analog.vcxproj
+++ b/visualc/xspice/analog.vcxproj
@@ -77,12 +77,12 @@
     <GenerateManifest>false</GenerateManifest>
   </PropertyGroup>
   <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
-    <TargetName>$(ProjectName)64</TargetName>
+    <TargetName>$(ProjectName)</TargetName>
     <GenerateManifest>true</GenerateManifest>
     <LinkIncremental>false</LinkIncremental>
   </PropertyGroup>
   <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
-    <TargetName>$(ProjectName)64</TargetName>
+    <TargetName>$(ProjectName)</TargetName>
     <GenerateManifest>false</GenerateManifest>
   </PropertyGroup>
   <ItemDefinitionGroup>
diff --git a/visualc/xspice/digital.vcxproj b/visualc/xspice/digital.vcxproj
index 87f4802..7722ce6 100644
--- a/visualc/xspice/digital.vcxproj
+++ b/visualc/xspice/digital.vcxproj
@@ -77,12 +77,12 @@
     <GenerateManifest>false</GenerateManifest>
   </PropertyGroup>
   <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
-    <TargetName>$(ProjectName)64</TargetName>
+    <TargetName>$(ProjectName)</TargetName>
     <GenerateManifest>true</GenerateManifest>
     <LinkIncremental>false</LinkIncremental>
   </PropertyGroup>
   <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
-    <TargetName>$(ProjectName)64</TargetName>
+    <TargetName>$(ProjectName)</TargetName>
     <GenerateManifest>false</GenerateManifest>
   </PropertyGroup>
   <ItemDefinitionGroup>
diff --git a/visualc/xspice/spice2poly.vcxproj b/visualc/xspice/spice2poly.vcxproj
index c92471b..786ff7e 100644
--- a/visualc/xspice/spice2poly.vcxproj
+++ b/visualc/xspice/spice2poly.vcxproj
@@ -77,12 +77,12 @@
     <GenerateManifest>false</GenerateManifest>
   </PropertyGroup>
   <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
-    <TargetName>$(ProjectName)64</TargetName>
+    <TargetName>$(ProjectName)</TargetName>
     <GenerateManifest>true</GenerateManifest>
     <LinkIncremental>false</LinkIncremental>
   </PropertyGroup>
   <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
-    <TargetName>$(ProjectName)64</TargetName>
+    <TargetName>$(ProjectName)</TargetName>
     <GenerateManifest>false</GenerateManifest>
   </PropertyGroup>
   <ItemDefinitionGroup>
diff --git a/visualc/xspice/table.vcxproj b/visualc/xspice/table.vcxproj
index fb4791c..ad3ee03 100644
--- a/visualc/xspice/table.vcxproj
+++ b/visualc/xspice/table.vcxproj
@@ -77,12 +77,12 @@
     <GenerateManifest>false</GenerateManifest>
   </PropertyGroup>
   <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
-    <TargetName>$(ProjectName)64</TargetName>
+    <TargetName>$(ProjectName)</TargetName>
     <GenerateManifest>true</GenerateManifest>
     <LinkIncremental>false</LinkIncremental>
   </PropertyGroup>
   <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
-    <TargetName>$(ProjectName)64</TargetName>
+    <TargetName>$(ProjectName)</TargetName>
     <GenerateManifest>false</GenerateManifest>
   </PropertyGroup>
   <ItemDefinitionGroup>
diff --git a/visualc/xspice/xtradev.vcxproj b/visualc/xspice/xtradev.vcxproj
index 6f0197f..53975b0 100644
--- a/visualc/xspice/xtradev.vcxproj
+++ b/visualc/xspice/xtradev.vcxproj
@@ -77,12 +77,12 @@
     <GenerateManifest>false</GenerateManifest>
   </PropertyGroup>
   <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
-    <TargetName>$(ProjectName)64</TargetName>
+    <TargetName>$(ProjectName)</TargetName>
     <GenerateManifest>true</GenerateManifest>
     <LinkIncremental>false</LinkIncremental>
   </PropertyGroup>
   <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
-    <TargetName>$(ProjectName)64</TargetName>
+    <TargetName>$(ProjectName)</TargetName>
     <GenerateManifest>false</GenerateManifest>
   </PropertyGroup>
   <ItemDefinitionGroup>
diff --git a/visualc/xspice/xtraevt.vcxproj b/visualc/xspice/xtraevt.vcxproj
index d5741e7..3177a91 100644
--- a/visualc/xspice/xtraevt.vcxproj
+++ b/visualc/xspice/xtraevt.vcxproj
@@ -77,12 +77,12 @@
     <GenerateManifest>false</GenerateManifest>
   </PropertyGroup>
   <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
-    <TargetName>$(ProjectName)64</TargetName>
+    <TargetName>$(ProjectName)</TargetName>
     <GenerateManifest>true</GenerateManifest>
     <LinkIncremental>false</LinkIncremental>
   </PropertyGroup>
   <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
-    <TargetName>$(ProjectName)64</TargetName>
+    <TargetName>$(ProjectName)</TargetName>
     <GenerateManifest>false</GenerateManifest>
   </PropertyGroup>
   <ItemDefinitionGroup>
-- 
2.32.0.windows.2

