diff --git a/lib/CMakeLists.txt b/lib/CMakeLists.txt
index 3a64deb..233c89f 100644
--- a/lib/CMakeLists.txt
+++ b/lib/CMakeLists.txt
@@ -112,7 +112,7 @@ install(TARGETS ${libname}
 )
 
 install(TARGETS ${libname}++
-    EXPORT libconfig++Targets
+    EXPORT libconfigTargets
     ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR}
     LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
     RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
@@ -121,7 +121,7 @@ install(TARGETS ${libname}++
 
 
 include(CMakePackageConfigHelpers)
-foreach(target_name libconfig libconfig++)
+foreach(target_name libconfig)
   write_basic_package_version_file("${target_name}ConfigVersion.cmake"
     VERSION ${PACKAGE_VERSION}
     COMPATIBILITY SameMajorVersion
