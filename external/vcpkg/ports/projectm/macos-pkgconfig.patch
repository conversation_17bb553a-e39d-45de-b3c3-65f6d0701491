--- a/src/libprojectM/CMakeLists.txt      2025-01-24 16:19:05.235818993 +0100
+++ b/src/libprojectM/CMakeLists.txt      2025-01-24 16:18:52.445824000 +0100
@@ -212,7 +212,9 @@

         set(PKGCONFIG_PACKAGE_NAME "${PROJECTM_LIBRARY_BASE_OUTPUT_NAME}")
         set(PKGCONFIG_PACKAGE_DESCRIPTION "projectM Music Visualizer")
-        set(PKGCONFIG_PACKAGE_REQUIREMENTS_ALL "opengl")
+        if(NOT APPLE)
+            set(PKGCONFIG_PACKAGE_REQUIREMENTS_ALL "opengl")
+        endif()

         generate_pkg_config_files(projectM ${PROJECTM_LIBRARY_BASE_OUTPUT_NAME})

