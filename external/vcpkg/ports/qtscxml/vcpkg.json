{"name": "qtscxml", "version": "6.8.3", "description": "The Qt SCXML module provides functionality to create state machines from SCXML files.", "homepage": "https://www.qt.io/", "license": null, "dependencies": [{"name": "qtbase", "default-features": false, "features": ["gui"]}, {"name": "qtscxml", "host": true, "default-features": false}], "features": {"qml": {"description": "Build QML imports", "dependencies": [{"name": "qtdeclarative", "default-features": false}]}}}