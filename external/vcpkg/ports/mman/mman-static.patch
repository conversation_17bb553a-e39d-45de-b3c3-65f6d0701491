diff --git a/CMakeLists.txt b/CMakeLists.txt
index 4696ad4..a72bbce 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -8,6 +8,10 @@ option (BUILD_TESTS "tests?" OFF)
 set (headers mman.h)
 set (sources mman.c)
 
+if(NOT BUILD_SHARED_LIBS)
+  add_definitions(-DMMANSHARED_EXPORT=)
+endif()
+
 add_library (mman ${sources})
 
 install (TARGETS mman RUNTIME DESTINATION bin
diff --git a/mman.h b/mman.h
index 4065bc6..435ea26 100644
--- a/mman.h
+++ b/mman.h
@@ -15,11 +15,13 @@
 #include <_mingw.h>
 #endif
 
+#if !defined(MMANSHARED_EXPORT)
 #if defined(MMAN_LIBRARY)
 #define MMANSHARED_EXPORT __declspec(dllexport)
 #else
 #define MMANSHARED_EXPORT __declspec(dllimport)
 #endif
+#endif
 
 /* Determine offset type */
 #include <stdint.h>
