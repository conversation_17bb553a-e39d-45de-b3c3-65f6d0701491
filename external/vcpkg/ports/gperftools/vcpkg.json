{"name": "gperftools", "version": "2.16", "description": "A high-performance multi-threaded malloc() implementation, plus some performance analysis tools.", "homepage": "https://github.com/gperftools/gperftools", "license": "BSD-3-<PERSON><PERSON>", "supports": "((x86 | x64) & windows & !uwp) | !windows | mingw", "dependencies": [{"name": "vcpkg-cmake", "host": true}], "features": {"libunwind": {"description": "Support libunwind for stack traces", "dependencies": ["libunwind"]}, "override": {"description": "Override Windows allocators", "supports": "windows & staticcrt"}, "tools": {"description": "Install tools"}}}