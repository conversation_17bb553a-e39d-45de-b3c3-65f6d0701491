{"name": "date", "version": "3.0.3", "description": "A date and time library based on the C++17 <chrono> header", "homepage": "https://github.com/HowardHinnant/date", "license": "MIT", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"remote-api": {"description": "support automatic download of tz data", "supports": "!uwp", "dependencies": ["curl"]}}}