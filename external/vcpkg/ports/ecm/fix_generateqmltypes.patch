diff --git a/modules/ECMGenerateQmlTypes.cmake b/modules/ECMGenerateQmlTypes.cmake
index d6e124266308028b8533203da63f572f6e99b308..7d7cecb5201521019764102eba0da2abf8b4d911 100644
--- a/modules/ECMGenerateQmlTypes.cmake
+++ b/modules/ECMGenerateQmlTypes.cmake
@@ -1,4 +1,5 @@
 # SPDX-FileCopyrightText: 2017 Alei<PERSON> <<EMAIL>>
+# SPDX-FileCopyrightText: 2021 Dawid <PERSON>l <<EMAIL>>
 #
 # SPDX-License-Identifier: BSD-3-Clause
 
@@ -18,10 +19,7 @@ our project offers. These files offer introspection upon our plugin and are
 useful for integrating with IDE language support of our plugin. It offers
 information about the objects its methods and their argument types.
 
-The developer will be in charge of making sure that these files are up to date.
-The plugin.qmltypes file will sit in the source directory. This function will
-include the code that installs the file in the right place and a small unit
-test named qmltypes-pluginname-version that makes sure that it doesn't need updating.
+This function installs the file in DESTINATION folder.
 
 
 Since 5.33.0
@@ -40,7 +38,7 @@ function(ecm_generate_qmltypes)
     set(targetname "qmltypes-${ARG_UNPARSED_ARGUMENTS}")
     string(REPLACE ";" - targetname "${targetname}")
 
-    set(generatedFile ${CMAKE_CURRENT_SOURCE_DIR}/plugins.qmltypes)
+    set(generatedFile plugins.qmltypes)
     add_custom_target(${targetname}
         BYPRODUCTS ${generatedFile}
         COMMAND qmlplugindump -nonrelocatable ${ARG_UNPARSED_ARGUMENTS} ${KDE_INSTALL_QMLDIR} > ${generatedFile}
