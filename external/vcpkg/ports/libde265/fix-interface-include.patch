diff --git a/libde265/CMakeLists.txt b/libde265/CMakeLists.txt
index d0d6762..06e1660 100644
--- a/libde265/CMakeLists.txt
+++ b/libde265/CMakeLists.txt
@@ -114,7 +114,7 @@ endif()
 
 add_library(de265 ${libde265_sources} ${ENCODER_OBJECTS} ${X86_OBJECTS})
 target_link_libraries(de265 PRIVATE Threads::Threads)
-target_include_directories(de265 PRIVATE ${CMAKE_BINARY_DIR} ${CMAKE_CURRENT_BINARY_DIR})
+target_include_directories(de265 PRIVATE $<BUILD_INTERFACE:${CMAKE_BINARY_DIR} ${CMAKE_CURRENT_BINARY_DIR}> PUBLIC $<INSTALL_INTERFACE:include>)
 
 write_basic_package_version_file(libde265ConfigVersion.cmake COMPATIBILITY ExactVersion)
 
