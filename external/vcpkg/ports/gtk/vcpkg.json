{"name": "gtk", "version": "4.16.3", "port-version": 2, "description": "Portable library for creating graphical user interfaces.", "homepage": "https://www.gtk.org/", "license": "LGPL-2.0-only", "supports": "!xbox & !(arm64 & windows)", "dependencies": ["atk", {"name": "cairo", "default-features": false, "features": ["gobject"]}, {"name": "cairo", "default-features": false, "features": ["x11"], "platform": "linux"}, "gdk-pixbuf", {"name": "gettext", "host": true, "default-features": false, "features": ["tools"]}, "gettext-libintl", "glib", {"name": "glib", "host": true}, "graphene", {"name": "harfbuzz", "features": ["glib"]}, "libepoxy", "pango", {"name": "sassc", "host": true}, {"name": "vcpkg-tool-meson", "host": true}], "features": {"introspection": {"description": "build with introspection", "dependencies": [{"name": "atk", "default-features": false, "features": ["introspection"]}, {"name": "gdk-pixbuf", "default-features": false, "features": ["introspection"]}, "gobject-introspection", {"name": "graphene", "default-features": false, "features": ["introspection"]}, {"name": "pango", "default-features": false, "features": ["introspection"]}]}}}