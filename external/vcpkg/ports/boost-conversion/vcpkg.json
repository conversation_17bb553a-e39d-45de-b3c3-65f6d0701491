{"$comment": "Automatically generated by scripts/boost/generate-ports.ps1", "name": "boost-conversion", "version": "1.87.0", "description": "Boost conversion module", "homepage": "https://www.boost.org/libs/conversion", "license": "BSL-1.0", "dependencies": [{"name": "boost-assert", "version>=": "1.87.0"}, {"name": "boost-cmake", "version>=": "1.87.0"}, {"name": "boost-config", "version>=": "1.87.0"}, {"name": "boost-headers", "version>=": "1.87.0"}, {"name": "boost-smart-ptr", "version>=": "1.87.0"}, {"name": "boost-throw-exception", "version>=": "1.87.0"}]}