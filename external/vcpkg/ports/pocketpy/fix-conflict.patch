diff --git a/CMakeLists.txt b/CMakeLists.txt
index ed02678..9297b20 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -86,7 +86,8 @@ if (PK_INSTALL)
         DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/include/ 
         DESTINATION include 
         FILES_MATCHING PATTERN "*.h"
-        PATTERN "typings" EXCLUDE
+        PATTERN "typings" EXCLUDE
+        PATTERN "pybind11" EXCLUDE
     )
     
     # generate config.cmake
