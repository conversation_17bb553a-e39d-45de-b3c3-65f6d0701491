diff --git a/src/freexl.c b/src/freexl.c
index f73021d..cc76d63 100644
--- a/src/freexl.c
+++ b/src/freexl.c
@@ -61,7 +61,7 @@ extern const char *locale_charset (void);
 #include <localcharset.h>
 #endif /* end localcharset */
 #else /* not WINDOWS */
-#if defined(__APPLE__) || defined(__ANDROID__)
+#if defined(__APPLE__) || (defined(__ANDROID__) && __ANDROID_API__ < 28)
 #include <iconv.h>
 #include <localcharset.h>
 #else /* neither Mac OsX nor Android */
diff --git a/src/freexl_ods.c b/src/freexl_ods.c
index 07265eb..ec29b51 100644
--- a/src/freexl_ods.c
+++ b/src/freexl_ods.c
@@ -61,7 +61,7 @@ extern const char *locale_charset (void);
 #include <localcharset.h>
 #endif /* end localcharset */
 #else /* not WINDOWS */
-#if defined(__APPLE__) || defined(__ANDROID__)
+#if defined(__APPLE__) || (defined(__ANDROID__) && __ANDROID_API__ < 28)
 #include <iconv.h>
 #include <localcharset.h>
 #else /* neither Mac OsX nor Android */
diff --git a/src/freexl_xlsx.c b/src/freexl_xlsx.c
index dc8891d..de56436 100644
--- a/src/freexl_xlsx.c
+++ b/src/freexl_xlsx.c
@@ -62,7 +62,7 @@ extern const char *locale_charset (void);
 #include <localcharset.h>
 #endif /* end localcharset */
 #else /* not WINDOWS */
-#if defined(__APPLE__) || defined(__ANDROID__)
+#if defined(__APPLE__) || (defined(__ANDROID__) && __ANDROID_API__ < 28)
 #include <iconv.h>
 #include <localcharset.h>
 #else /* neither Mac OsX nor Android */
