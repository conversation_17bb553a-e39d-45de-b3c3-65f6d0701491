{"name": "ngtcp2", "version": "1.11.0", "description": "ngtcp2 project is an effort to implement RFC9000 QUIC protocol.", "homepage": "https://github.com/ngtcp2/ngtcp2", "license": "MIT", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"gnutls": {"description": "Compile with gnutls", "dependencies": [{"name": "libgnutls", "platform": "!windows | mingw"}, {"name": "shiftmedia-libgnutls", "platform": "windows & !mingw"}]}, "libressl": {"description": "Compile with libressl", "dependencies": ["libressl"]}, "wolfssl": {"description": "Compile with wolfssl", "dependencies": [{"name": "wolfssl", "features": ["quic"]}]}}}