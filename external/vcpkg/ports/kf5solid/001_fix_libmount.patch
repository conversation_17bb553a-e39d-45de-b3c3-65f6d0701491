diff --git a/KF5SolidConfig.cmake.in b/KF5SolidConfig.cmake.in
index 0a23f44..7020804 100644
--- a/KF5SolidConfig.cmake.in
+++ b/KF5SolidConfig.cmake.in
@@ -23,6 +23,10 @@ if (NOT @BUILD_SHARED_LIBS@)
     find_dependency(Qt@QT_MAJOR_VERSION@Xml @REQUIRED_QT_VERSION@)
     find_dependency(Qt@QT_MAJOR_VERSION@Gui @REQUIRED_QT_VERSION@)
 
+    if (@HAVE_LIBMOUNT@)
+        find_dependency(LibMount)
+    endif()
+
     if ("@Qt5DBus_FOUND@" OR "@Qt6DBus_FOUND@")
         find_dependency(Qt@QT_MAJOR_VERSION@DBus @REQUIRED_QT_VERSION@)
     endif()
