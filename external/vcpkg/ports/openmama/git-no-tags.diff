diff --git a/CMakeLists.txt b/CMakeLists.txt
index 1332a55..2ed3770 100755
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -23,6 +23,8 @@ if (EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/.git)
             COMMAND "${GIT_BIN}" diff-index --quiet HEAD --
             WORKING_DIRECTORY "${CMAKE_CURRENT_SOURCE_DIR}"
             RESULT_VARIABLE is_current_source_dir_dirty)
+endif()
+if(OPENMAMA_VERSION_GIT)
     # Strip out unwanted part of version from git
     message(STATUS "OPENMAMA_VERSION_GIT: ${OPENMAMA_VERSION_GIT}")
     STRING(REGEX REPLACE "^OpenMAMA-" "" OPENMAMA_VERSION ${OPENMAMA_VERSION_GIT})
