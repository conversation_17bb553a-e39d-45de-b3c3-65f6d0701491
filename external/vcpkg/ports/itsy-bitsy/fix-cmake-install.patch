diff --git a/CMakeLists.txt b/CMakeLists.txt
index c7b2e2a..9219be1 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -31,6 +31,8 @@ project(itsy.bitsy
 	DESCRIPTION "Standard bit utilities to supplement the C and C++ standard libraries."
 	LANGUAGES C CXX)
 
+include(CMakePackageConfigHelpers)
+include(GNUInstallDirs)
 # # Pre-dependencies
 # this is for all the deps we may or may not need before-hand to make things right
 if(ITSY_BITSY_READTHEDOCS)
@@ -100,10 +102,7 @@ option(ITSY_BITSY_EXAMPLES "Enable build of examples" OFF)
 
 # # Dependencies
 # ztd.idk
-FetchContent_Declare(ztd.idk
-	GIT_REPOSITORY https://github.com/soasis/idk.git
-	GIT_TAG main)
-FetchContent_MakeAvailable(ztd.idk)
+find_package(ztd.idk CONFIG REQUIRED)
 
 # # itsy.bitsy Target
 # Build Target
@@ -136,6 +135,10 @@ export(TARGETS itsy.bitsy FILE "${CMAKE_CURRENT_BINARY_DIR}/cmake/itsy.bitsy-tar
 
 install(TARGETS itsy.bitsy
 	EXPORT itsy.bitsy)
+install(EXPORT itsy.bitsy
+  FILE itsy.bitsy-targets.cmake
+  DESTINATION lib/cmake/itsy.bitsy
+)
 
 install(DIRECTORY include/itsy
 	DESTINATION "${CMAKE_INSTALL_INCLUDEDIR}")
diff --git a/cmake/itsy.bitsy-config.cmake.in b/cmake/itsy.bitsy-config.cmake.in
index 9f87935..6e18425 100644
--- a/cmake/itsy.bitsy-config.cmake.in
+++ b/cmake/itsy.bitsy-config.cmake.in
@@ -9,10 +9,13 @@
 #  See https://github.com/ThePhD/itsy_bitsy#using-the-library for documentation.
 
 @PACKAGE_INIT@
+include(CMakeFindDependencyMacro)
+find_dependency(ztd.idk CONFIG)
+include("${CMAKE_CURRENT_LIST_DIR}/itsy.bitsy-targets.cmake")
 
-include("${CMAKE_CURRENT_LIST_DIR}/sol2-targets.cmake")
 
 MESSAGE(STATUS ${CMAKE_CURRENT_LIST_DIR})
+add_library(itsy::bitsy ALIAS itsy.bitsy)
 
 if (TARGET sol2)
     get_target_property(SOL2_INCLUDE_DIRS
