{"name": "proj", "version": "9.6.0", "description": "PROJ library for cartographic projections", "homepage": "https://proj.org/", "license": "MIT", "dependencies": ["<PERSON><PERSON><PERSON>-<PERSON><PERSON>", {"name": "sqlite3", "default-features": false}, {"name": "sqlite3", "host": true, "default-features": false, "features": ["tool"]}, {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "default-features": ["net", "tiff"], "features": {"net": {"description": "Enable network support", "dependencies": ["curl"]}, "tiff": {"description": "Enable TIFF support to read some grids", "dependencies": [{"name": "tiff", "default-features": false, "features": ["lzma", "zip"]}]}, "tools": {"description": "Build tools"}}}