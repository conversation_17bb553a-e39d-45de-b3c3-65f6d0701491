{"name": "<PERSON><PERSON><PERSON>", "version-date": "2025-03-26", "description": "UVAtlas isochart texture atlas", "homepage": "https://github.com/Microsoft/UVAtlas", "documentation": "https://github.com/Microsoft/UVAtlas/wiki", "license": "MIT", "supports": "(windows & !arm32) | linux", "dependencies": [{"name": "directx-headers", "platform": "mingw | linux"}, "directxmath", {"name": "ms-gdkx", "platform": "xbox"}, {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"eigen": {"description": "Use Eigen & Spectra for eigen-value computations", "dependencies": ["eigen3", "spectra"]}, "spectre": {"description": "Build Spectre-mitigated library"}, "tools": {"description": "meshconvert command-line tool", "supports": "windows & !uwp & !xbox", "dependencies": [{"name": "directxmesh", "default-features": false}, {"name": "directxtex", "default-features": false}]}}}