{"name": "frugally-deep", "version-semver": "0.18.0", "description": "Header-only library for using Keras models in C++.", "homepage": "https://github.com/Dobiasd/frugally-deep", "license": "MIT", "dependencies": ["eigen3", "functionalplus", "<PERSON><PERSON><PERSON>-<PERSON><PERSON>", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"double": {"description": "Use double precision"}}}