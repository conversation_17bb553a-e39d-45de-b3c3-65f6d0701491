/* This file was generated automatically by predicates_init
 *
 * This file is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Library General Public
 * License as published by the Free Software Foundation; either
 * version 2 of the License, or (at your option) any later version.
 *
 * This file is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.
 */

static double splitter = 134217729.000000;
static double resulterrbound = 3.330669073875471e-16;
static double ccwerrboundA = 3.330669073875472e-16;
static double ccwerrboundB = 2.220446049250315e-16;
static double ccwerrboundC = 1.109335647967049e-31;
static double o3derrboundA = 7.771561172376103e-16;
static double o3derrboundB = 3.330669073875473e-16;
static double o3derrboundC = 3.204747427460364e-31;
static double iccerrboundA = 1.110223024625158e-15;
static double iccerrboundB = 4.440892098500632e-16;
static double iccerrboundC = 5.423418723394464e-31;
static double isperrboundA = 1.776356839400253e-15;
static double isperrboundB = 5.551115123125792e-16;
static double isperrboundC = 8.751425667295619e-31;
