diff --git a/CMakeLists.txt b/CMakeLists.txt
index baf26bd..7fa930c 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -22,15 +22,13 @@ endif()
 
 if(WIN32)
   set(OS "win32")
-  include_directories(oshw/win32/wpcap/Include)
-  if(CMAKE_SIZEOF_VOID_P EQUAL 8)
-    link_directories(${CMAKE_CURRENT_LIST_DIR}/oshw/win32/wpcap/Lib/x64)
-  elseif(CMAKE_SIZEOF_VOID_P EQUAL 4)
-    link_directories(${CMAKE_CURRENT_LIST_DIR}/oshw/win32/wpcap/Lib)
-  endif()
+  find_path(winpcap_INCLUDE_DIRS NAMES pcap.h)
+  find_library(winpcap_LIBRARY NAMES wpcap)
+  find_library(packet_LIBRARY NAMES packet)
+  include_directories(${winpcap_INCLUDE_DIRS})
   set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} /D _CRT_SECURE_NO_WARNINGS")
   set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS}  /WX")
-  set(OS_LIBS wpcap.lib Packet.lib Ws2_32.lib Winmm.lib)
+  set(OS_LIBS ${winpcap_LIBRARY} ${packet_LIBRARY} Ws2_32.lib Winmm.lib)
 elseif(UNIX AND NOT APPLE)
   set(OS "linux")
   set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -Wall -Wextra -Werror")
