diff --git a/kissfft-config.cmake.in b/kissfft-config.cmake.in
index cd7139a..c387fe6 100644
--- a/kissfft-config.cmake.in
+++ b/kissfft-config.cmake.in
@@ -28,7 +28,7 @@ cmake_minimum_required(VERSION 3.3)
 
 # Set include glob of config files using SHARED/static component, BUILD_SHARED_LIBS by default
 set(_kissfft_shared_detected OFF)
-set(_kissfft_shared ${BUILD_SHARED_LIBS})
+set(_kissfft_shared @BUILD_SHARED_LIBS@)
 if("SHARED" IN_LIST kissfft_FIND_COMPONENTS)
     set(_kissfft_shared_detected ON)
     set(_kissfft_shared ON)
