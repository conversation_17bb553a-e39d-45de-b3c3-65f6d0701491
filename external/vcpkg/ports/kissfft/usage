kissfft provides CMake targets:

    # Using distinct targets
    find_package(kissfft CONFIG REQUIRED)
    target_link_libraries(main PRIVATE kissfft::kissfft-float)
    # Alternative targets: kissfft::kissfft-double, kissfft::int16_t, kissfft::int32_t

    # Using a distinct package component
    find_package(kissfft CONFIG REQUIRED COMPONENTS float)
    target_link_libraries(main PRIVATE kissfft::kissfft)
    # Alternative components: double, int16_t, int32_t
