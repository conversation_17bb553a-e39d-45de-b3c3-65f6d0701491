diff --git a/cmd/gvedit/main.cpp b/cmd/gvedit/main.cpp
index 98a2a4a..e810c36 100644
--- a/cmd/gvedit/main.cpp
+++ b/cmd/gvedit/main.cpp
@@ -30,6 +30,7 @@
 
 
 #ifdef _MSC_VER
+#elif 0
 #pragma comment( lib, "cgraph.lib" )
 #pragma comment( lib, "gvc.lib" )
 #endif
diff --git a/plugin/gdk/gvloadimage_gdk.c b/plugin/gdk/gvloadimage_gdk.c
index 84c6a6a..0d3ec64 100644
--- a/plugin/gdk/gvloadimage_gdk.c
+++ b/plugin/gdk/gvloadimage_gdk.c
@@ -22,6 +22,7 @@
 #include <gdk/gdkcairo.h>
 
 #ifdef _MSC_VER //*dependencies
+#elif 0
     #pragma comment( lib, "gvc.lib" )
     #pragma comment( lib, "glib-2.0.lib" )
     #pragma comment( lib, "cairo.lib" )
diff --git a/plugin/webp/gvloadimage_webp.c b/plugin/webp/gvloadimage_webp.c
index c983556..446d43f 100644
--- a/plugin/webp/gvloadimage_webp.c
+++ b/plugin/webp/gvloadimage_webp.c
@@ -24,6 +24,7 @@
 #include <webp/decode.h>
 
 #ifdef _MSC_VER //*dependencies
+#elif 0
     #pragma comment( lib, "gvc.lib" )
     #pragma comment( lib, "glib-2.0.lib" )
     #pragma comment( lib, "pango-1.0.lib" )
