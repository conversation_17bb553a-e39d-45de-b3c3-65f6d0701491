diff --git a/plugin/webp/CMakeLists.txt b/plugin/webp/CMakeLists.txt
index 21913a8..4811930 100644
--- a/plugin/webp/CMakeLists.txt
+++ b/plugin/webp/CMakeLists.txt
@@ -45,7 +45,7 @@ if(WEBP_FOUND)
   install(
     TARGETS gvplugin_webp
     RUNTIME DESTINATION ${BINARY_INSTALL_DIR}
-    LIBRARY DESTINATION ${LIBRARY_INSTALL_DIR}
+    LIBRARY DESTINATION ${PLUGIN_INSTALL_DIR}
     ARCHIVE DESTINATION ${LIBRARY_INSTALL_DIR}
   )
 
