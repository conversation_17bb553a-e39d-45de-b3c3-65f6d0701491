diff --git a/cmake/module/Findmbedtls.cmake b/cmake/module/Findmbedtls.cmake
index 8c76d0a..f0ed1e2 100644
--- a/cmake/module/Findmbedtls.cmake
+++ b/cmake/module/Findmbedtls.cmake
@@ -160,6 +160,9 @@ if(MBEDTLS_INCLUDE_DIR AND EXISTS "${MBEDTLS_INCLUDE_DIR}/mbedtls/version.h")
 
     # Get Version From File
     file(STRINGS "${MBEDTLS_INCLUDE_DIR}/mbedtls/version.h" VERSIONH REGEX "#define MBEDTLS_VERSION_STRING[ ]+\".*\"")
+    if(VERSIONH STREQUAL "")
+        file(STRINGS "${MBEDTLS_INCLUDE_DIR}/mbedtls/build_info.h" VERSIONH REGEX "#define MBEDTLS_VERSION_STRING[ ]+\".*\"")
+    endif()
 
     # Match Version String
     string(REGEX REPLACE ".*\".*([0-9]+)\\.([0-9]+)\\.([0-9]+)\"" "\\1;\\2;\\3" MBEDTLS_VERSION_LIST "${VERSIONH}")
diff --git a/src/oatpp-mbedtls/Config.cpp b/src/oatpp-mbedtls/Config.cpp
index 63fe9d4..8d3c9ce 100644
--- a/src/oatpp-mbedtls/Config.cpp
+++ b/src/oatpp-mbedtls/Config.cpp
@@ -90,7 +90,11 @@ std::shared_ptr<Config> Config::createDefaultServerConfigShared(const char* serv
     throw std::runtime_error("[oatpp::mbedtls::Config::createDefaultServerConfigShared()]: Error. Can't parse serverCertFile");
   }
 
+#if MBEDTLS_VERSION_MAJOR < 3
   res = mbedtls_pk_parse_keyfile(&result->m_privateKey, privateKeyFile, pkPassword);
+#else
+  res = mbedtls_pk_parse_keyfile(&result->m_privateKey, privateKeyFile, pkPassword, mbedtls_ctr_drbg_random, &result->m_ctr_drbg);
+#endif
   if(res != 0) {
     OATPP_LOGD("[oatpp::mbedtls::Config::createDefaultServerConfigShared()]", "Error. Can't parse privateKeyFile path='%s', return value=%d", privateKeyFile, res);
     throw std::runtime_error("[oatpp::mbedtls::Config::createDefaultServerConfigShared()]: Error. Can't parse privateKeyFile");
diff --git a/src/oatpp-mbedtls/Config.hpp b/src/oatpp-mbedtls/Config.hpp
index d4d6072..47c1c2d 100644
--- a/src/oatpp-mbedtls/Config.hpp
+++ b/src/oatpp-mbedtls/Config.hpp
@@ -28,7 +28,9 @@
 
 #include "mbedtls/entropy.h"
 #include "mbedtls/ctr_drbg.h"
+#if MBEDTLS_VERSION_MAJOR < 3
 #include "mbedtls/certs.h"
+#endif
 #include "mbedtls/x509.h"
 #include "mbedtls/ssl.h"
 #include "mbedtls/net_sockets.h"
