{"name": "flashlight-cuda", "version": "0.3", "port-version": 7, "description": "A C++ standalone library for machine learning. CUDA backend.", "homepage": "https://github.com/facebookresearch/flashlight", "license": "MIT", "supports": "!(windows | osx)", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "default-features": ["fl"], "features": {"asr": {"description": "flashlight asr app", "dependencies": [{"name": "flashlight-cuda", "features": ["fl", "lib"]}, "gflags", {"name": "libsndfile", "default-features": false, "features": ["external-libs"]}]}, "fl": {"description": "flashlight core autograd and neural net library", "dependencies": [{"name": "arrayfire", "features": ["cuda"]}, "cereal", "cuda", "cudnn", "nccl", "openmpi", "stb"]}, "imgclass": {"description": "flashlight image classification app", "dependencies": [{"name": "flashlight-cuda", "features": ["fl", "lib"]}, "gflags"]}, "lib": {"description": "flashlight libraries", "dependencies": ["cuda", "fftw3", "intel-mkl", "kenlm"]}, "lm": {"description": "flashlight lm app", "dependencies": [{"name": "flashlight-cuda", "features": ["fl", "lib"]}, "gflags"]}, "objdet": {"description": "flashlight object detection app", "dependencies": [{"name": "flashlight-cuda", "features": ["fl", "imgclass", "lib"]}, "gflags"]}}}