mbedtls provides CMake targets:

  find_package(MbedTLS CONFIG REQUIRED)
  # everything
  target_link_libraries(main PRIVATE MbedTLS::mbedtls)
  # X.509 certificate manipulation
  target_link_libraries(main PRIVATE MbedTLS::mbedx509)
  # cryptographic primitives
  target_link_libraries(main PRIVATE MbedTLS::mbedcrypto)

mbedtls provides pkg-config modules:

  # everything
  mbedtls
  # X.509 certificate manipulation
  mbedx509
  # cryptographic primitives
  mbedcrypto
