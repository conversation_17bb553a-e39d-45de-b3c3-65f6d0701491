diff --git a/CMakeLists.txt b/CMakeLists.txt
index 2eba16d..a46cb3d 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -123,7 +123,17 @@ endif()
 # We now potentially need to link all executables against PThreads, if available
 set(CMAKE_THREAD_PREFER_PTHREAD TRUE)
 set(THREADS_PREFER_PTHREAD_FLAG TRUE)
-find_package(Threads)
+if(NOT LINK_WITH_PTHREAD)
+    set(CMAKE_DISABLE_FIND_PACKAGE_Threads ON)
+elseif(WIN32 AND NOT MINGW)
+    find_package(PThreads4W REQUIRED)
+    set(CMAKE_THREAD_LIBS_INIT PThreads4W::PThreads4W)
+    add_definitions(-DLINK_WITH_PTHREAD)
+else()
+    find_package(Threads REQUIRED)
+    set(CMAKE_THREAD_LIBS_INIT Threads::Threads)
+    add_definitions(-DL<PERSON><PERSON>_WITH_PTHREAD)
+endif()
 
 # If this is the root project add longer list of available CMAKE_BUILD_TYPE values
 if(CMAKE_SOURCE_DIR STREQUAL CMAKE_CURRENT_SOURCE_DIR)
diff --git a/cmake/MbedTLSConfig.cmake.in b/cmake/MbedTLSConfig.cmake.in
index b65bbab..5919c37 100644
--- a/cmake/MbedTLSConfig.cmake.in
+++ b/cmake/MbedTLSConfig.cmake.in
@@ -1,3 +1,11 @@
 @PACKAGE_INIT@
 
+if("@LINK_WITH_PTHREAD@")
+  include(CMakeFindDependencyMacro)
+  if(WIN32 AND NOT MINGW)
+    find_dependency(PThreads4W)
+  else()
+    find_dependency(Threads)
+  endif()
+endif()
 include("${CMAKE_CURRENT_LIST_DIR}/MbedTLSTargets.cmake")
diff --git a/include/mbedtls/mbedtls_config.h b/include/mbedtls/mbedtls_config.h
index 3592141..174cabc 100644
--- a/include/mbedtls/mbedtls_config.h
+++ b/include/mbedtls/mbedtls_config.h
@@ -2091,6 +2091,10 @@
  * Uncomment this to enable pthread mutexes.
  */
 //#define MBEDTLS_THREADING_PTHREAD
+#ifdef LINK_WITH_PTHREAD
+#define MBEDTLS_THREADING_C
+#define MBEDTLS_THREADING_PTHREAD
+#endif
 
 /**
  * \def MBEDTLS_USE_PSA_CRYPTO
