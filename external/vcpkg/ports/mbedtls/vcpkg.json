{"name": "mbedtls", "version": "3.6.2", "description": "An open source, portable, easy to use, readable and flexible SSL library", "homepage": "https://www.trustedfirmware.org/projects/mbed-tls/", "license": "Apache-2.0 OR GPL-2.0-or-later", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"pthreads": {"description": "Multi-threading support", "dependencies": [{"name": "pthreads", "platform": "windows"}]}}}