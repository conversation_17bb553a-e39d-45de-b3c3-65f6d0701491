{"name": "luminoengine", "version": "0.10.1", "port-version": 1, "description": "C++17 games and visualization toolkit.", "homepage": "https://github.com/LuminoEngine/Lumino", "license": "MIT", "supports": "x64 & windows & !uwp", "dependencies": ["gtest", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"engine": {"description": "Make engine module.", "dependencies": ["box2d", "bullet3", {"name": "freetype", "features": ["png", "zlib"]}, "glad", "glfw3", "glslang", {"name": "imgui", "features": ["docking-experimental"]}, "libogg", "libpng", "libvorbis", "nanovg", "openal-soft", "pcre2", "spirv-cross", "stb", "tinygltf", "tinyobjloader", "toml11", "vulkan-headers", "yaml-cpp", "zlib"]}}}