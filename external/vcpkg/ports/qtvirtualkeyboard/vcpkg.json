{"name": "qtvirtualkeyboard", "version": "6.8.3", "description": "The Qt Virtual Keyboard project provides an input framework and reference keyboard frontend for Qt 6 on Linux Desktop/X11, Windows Desktop, and Boot2Qt targets.", "homepage": "https://www.qt.io/", "license": null, "dependencies": [{"name": "qtbase", "default-features": false, "features": ["gui"]}, {"name": "qtdeclarative", "default-features": false}, {"name": "qtsvg", "default-features": false}], "default-features": ["hunspell"], "features": {"hunspell": {"description": "Use hunspell", "dependencies": ["hunspell"]}}}