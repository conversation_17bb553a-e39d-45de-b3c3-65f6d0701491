diff --git a/CMakeLists.txt b/CMakeLists.txt
index 3a48eaf..7b8bc7c 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -1049,8 +1049,7 @@ if(USE_MIMALLOC)
   set(MI_BUILD_OBJECT OFF)
   set(MI_BUILD_TESTS OFF)
   add_definitions(-DUSE_MIMALLOC)
-  add_subdirectory(third_party/mimalloc)
-  include_directories(third_party/mimalloc/include)
+  find_package(mimalloc REQUIRED)
 endif()
 
 # ---[ Main build
diff --git a/aten/src/ATen/native/quantized/cpu/qnnpack/CMakeLists.txt b/aten/src/ATen/native/quantized/cpu/qnnpack/CMakeLists.txt
index fd6b7ff..d185233 100644
--- a/aten/src/ATen/native/quantized/cpu/qnnpack/CMakeLists.txt
+++ b/aten/src/ATen/native/quantized/cpu/qnnpack/CMakeLists.txt
@@ -368,7 +368,7 @@ elseif(NOT TARGET pthreadpool AND USE_SYSTEM_PTHREADPOOL)
   if(NOT PTHREADPOOL_LIBRARY)
     message(FATAL_ERROR "Cannot find pthreadpool")
   endif()
-  message("-- Found pthreadpool: ${PTHREADPOOL_LIBRARY}")
+  message("-- Found pthreadpool cpu qnnpack: ${PTHREADPOOL_LIBRARY}")
   set_target_properties(pthreadpool PROPERTIES
     IMPORTED_LOCATION "${PTHREADPOOL_LIBRARY}")
   add_library(pthreadpool_interface INTERFACE)
diff --git a/c10/CMakeLists.txt b/c10/CMakeLists.txt
index feebad7..ad407e1 100644
--- a/c10/CMakeLists.txt
+++ b/c10/CMakeLists.txt
@@ -109,8 +109,7 @@ else()
 endif()
 
 if(USE_MIMALLOC)
-  target_link_libraries(c10 PRIVATE "mimalloc-static")
-  add_dependencies(c10 mimalloc-static)
+  target_link_libraries(c10 PRIVATE "mimalloc")
 endif()
 
 if(ANDROID)
diff --git a/cmake/Dependencies.cmake b/cmake/Dependencies.cmake
index c3abce5..63f665f 100644
--- a/cmake/Dependencies.cmake
+++ b/cmake/Dependencies.cmake
@@ -285,7 +285,7 @@ endif()
 set(AT_FFTW_ENABLED 0)
 set(USE_FFTW OFF)
 if(USE_FFTW OR NOT MKL_FOUND)
-  find_library(LIBFFTW3 fftw3)
+  find_library(LIBFFTW3 NAMES fftw3 REQUIRED)
   if(LIBFFTW3)
     find_path(FFTW3_INCLUDE_DIR NAMES fftw3.h ONLY_CMAKE_FIND_ROOT_PATH)
     if(FFTW3_INCLUDE_DIR)
@@ -415,8 +415,8 @@ if(INTERN_BUILD_MOBILE OR NOT DISABLE_NNPACK_AND_FAMILY)
 
   if(NOT TARGET pthreadpool)
     if(USE_SYSTEM_PTHREADPOOL)
-      add_library(pthreadpool SHARED IMPORTED)
-      find_library(PTHREADPOOL_LIBRARY pthreadpool)
+      add_library(pthreadpool UNKNOWN IMPORTED)
+      find_library(PTHREADPOOL_LIBRARY pthreadpool PATH_SUFFIXES lib)
       set_property(TARGET pthreadpool PROPERTY IMPORTED_LOCATION "${PTHREADPOOL_LIBRARY}")
       if(NOT PTHREADPOOL_LIBRARY)
         message(FATAL_ERROR "Cannot find pthreadpool")
@@ -450,13 +450,15 @@ endif()
 
 # ---[ Caffe2 uses cpuinfo library in the thread pool
 if(NOT TARGET cpuinfo AND USE_SYSTEM_CPUINFO)
-  add_library(cpuinfo SHARED IMPORTED)
-  find_library(CPUINFO_LIBRARY cpuinfo)
+  add_library(cpuinfo UNKNOWN IMPORTED)
+  find_library(CPUINFO_LIBRARY NAMES cpuinfo REQUIRED)
+  find_library(CLOG_LIBRARY NAMES clog REQUIRED)
   if(NOT CPUINFO_LIBRARY)
     message(FATAL_ERROR "Cannot find cpuinfo")
   endif()
   message("Found cpuinfo: ${CPUINFO_LIBRARY}")
   set_target_properties(cpuinfo PROPERTIES IMPORTED_LOCATION "${CPUINFO_LIBRARY}")
+  target_link_libraries(cpuinfo INTERFACE "${CLOG_LIBRARY}")
 elseif(NOT TARGET cpuinfo)
   if(NOT DEFINED CPUINFO_SOURCE_DIR)
     set(CPUINFO_SOURCE_DIR "${CMAKE_CURRENT_LIST_DIR}/../third_party/cpuinfo" CACHE STRING "cpuinfo source directory")
@@ -659,8 +661,8 @@ if(USE_XNNPACK AND NOT USE_SYSTEM_XNNPACK)
   include_directories(SYSTEM ${XNNPACK_INCLUDE_DIR})
   list(APPEND Caffe2_DEPENDENCY_LIBS XNNPACK)
 elseif(NOT TARGET XNNPACK AND USE_SYSTEM_XNNPACK)
-  add_library(XNNPACK SHARED IMPORTED)
-  find_library(XNNPACK_LIBRARY XNNPACK)
+  add_library(XNNPACK UNKNOWN IMPORTED)
+  find_library(XNNPACK_LIBRARY NAMES XNNPACK REQUIRED)
   set_property(TARGET XNNPACK PROPERTY IMPORTED_LOCATION "${XNNPACK_LIBRARY}")
   if(NOT XNNPACK_LIBRARY)
     message(FATAL_ERROR "Cannot find XNNPACK")
@@ -858,7 +860,7 @@ endif()
 
 # ---[ LMDB
 if(USE_LMDB)
-  find_package(LMDB)
+  find_package(LMDB REQUIRED)
   if(LMDB_FOUND)
     include_directories(SYSTEM ${LMDB_INCLUDE_DIR})
     list(APPEND Caffe2_DEPENDENCY_LIBS ${LMDB_LIBRARIES})
@@ -1002,8 +1004,9 @@ if(NOT TARGET fp16 AND NOT USE_SYSTEM_FP16)
     "${FP16_SOURCE_DIR}"
     "${CONFU_DEPENDENCIES_BINARY_DIR}/FP16")
 elseif(NOT TARGET fp16 AND USE_SYSTEM_FP16)
-  add_library(fp16 STATIC "/usr/include/fp16.h")
-  set_target_properties(fp16 PROPERTIES LINKER_LANGUAGE C)
+  find_path(FP16_INCLUDE_DIR NAMES fp16.h PATH_SUFFIXES include)
+  add_library(fp16 INTERFACE)
+  target_include_directories(fp16 INTERFACE "${FP16_INCLUDE_DIR}")
 endif()
 list(APPEND Caffe2_DEPENDENCY_LIBS fp16)
 
@@ -1127,7 +1130,7 @@ if(BUILD_PYTHON)
     # Observers are required in the python build
     caffe2_update_option(USE_OBSERVERS ON)
   else()
-    message(WARNING "Python dependencies not met. Not compiling with python. Suppress this warning with -DBUILD_PYTHON=OFF")
+    message(FATAL_ERROR "Python dependencies not met. Not compiling with python. Suppress this warning with -DBUILD_PYTHON=OFF")
     caffe2_update_option(BUILD_PYTHON OFF)
   endif()
 endif()
@@ -1231,7 +1234,7 @@ endif()
 # ---[ LLVM
 if(USE_LLVM)
   message(STATUS "Looking for LLVM in ${USE_LLVM}")
-  find_package(LLVM PATHS ${USE_LLVM} NO_DEFAULT_PATH)
+  find_package(LLVM)
 
   if(LLVM_FOUND)
     message(STATUS "Found LLVM ${LLVM_PACKAGE_VERSION}")
@@ -1244,8 +1247,10 @@ endif(USE_LLVM)
 
 # ---[ cuDNN
 if(USE_CUDNN)
+  find_package(CUDNN REQUIRED)
   set(CUDNN_FRONTEND_INCLUDE_DIR ${CMAKE_CURRENT_LIST_DIR}/../third_party/cudnn_frontend/include)
   target_include_directories(torch::cudnn INTERFACE ${CUDNN_FRONTEND_INCLUDE_DIR})
+  target_include_directories(torch::cudnn INTERFACE "${CUDNN_INCLUDE_DIR}")
 endif()
 
 # ---[ HIP
@@ -1555,7 +1560,10 @@ if(CAFFE2_CMAKE_BUILDING_WITH_MAIN_REPO AND NOT INTERN_DISABLE_ONNX)
       set_target_properties(onnx_proto PROPERTIES CXX_STANDARD 17)
     endif()
   endif()
-  add_subdirectory(${CMAKE_CURRENT_LIST_DIR}/../third_party/foxi EXCLUDE_FROM_ALL)
+
+  # setup foxi
+  find_library(FOXI_LOADER_LIBPATH NAMES foxi_loader REQUIRED)
+  list(APPEND Caffe2_DEPENDENCY_LIBS "${FOXI_LOADER_LIBPATH}")
 
   add_definitions(-DONNX_NAMESPACE=${ONNX_NAMESPACE})
   if(NOT USE_SYSTEM_ONNX)
@@ -1573,23 +1581,13 @@ if(CAFFE2_CMAKE_BUILDING_WITH_MAIN_REPO AND NOT INTERN_DISABLE_ONNX)
       target_compile_options(onnx PRIVATE -Wno-deprecated-declarations)
     endif()
   else()
-    add_library(onnx SHARED IMPORTED)
-    find_library(ONNX_LIBRARY onnx)
-    if(NOT ONNX_LIBRARY)
-      message(FATAL_ERROR "Cannot find onnx")
-    endif()
-    set_property(TARGET onnx PROPERTY IMPORTED_LOCATION ${ONNX_LIBRARY})
-    add_library(onnx_proto SHARED IMPORTED)
-    find_library(ONNX_PROTO_LIBRARY onnx_proto)
-    if(NOT ONNX_PROTO_LIBRARY)
-      message(FATAL_ERROR "Cannot find onnx")
-    endif()
-    set_property(TARGET onnx_proto PROPERTY IMPORTED_LOCATION ${ONNX_PROTO_LIBRARY})
-    message("-- Found onnx: ${ONNX_LIBRARY} ${ONNX_PROTO_LIBRARY}")
-    list(APPEND Caffe2_DEPENDENCY_LIBS onnx_proto onnx)
+    find_package(ONNX CONFIG REQUIRED)
+    set(ONNX_LIBRARY ONNX::onnx)
+    set(ONNX_PROTO_LIBRARY ONNX::onnx_proto)
+    list(APPEND Caffe2_DEPENDENCY_LIBS ${ONNX_LIBRARY} ${ONNX_PROTO_LIBRARY})
   endif()
   include_directories(${FOXI_INCLUDE_DIRS})
-  list(APPEND Caffe2_DEPENDENCY_LIBS foxi_loader)
+  list(APPEND Caffe2_DEPENDENCY_LIBS "${FOXI_LOADER_LIBPATH}")
   # Recover the build shared libs option.
   set(BUILD_SHARED_LIBS ${TEMP_BUILD_SHARED_LIBS})
 endif()
@@ -1834,8 +1832,8 @@ endif()
 #
 set(TEMP_BUILD_SHARED_LIBS ${BUILD_SHARED_LIBS})
 set(BUILD_SHARED_LIBS OFF CACHE BOOL "Build shared libs" FORCE)
-add_subdirectory(${PROJECT_SOURCE_DIR}/third_party/fmt)
-
+#add_subdirectory(${PROJECT_SOURCE_DIR}/third_party/fmt)
+find_package(fmt REQUIRED)
 # Disable compiler feature checks for `fmt`.
 #
 # CMake compiles a little program to check compiler features. Some of our build
@@ -1843,7 +1841,6 @@ add_subdirectory(${PROJECT_SOURCE_DIR}/third_party/fmt)
 # CMAKE_CXX_FLAGS in ways that break feature checks. Since we already know
 # `fmt` is compatible with a superset of the compilers that PyTorch is, it
 # shouldn't be too bad to just disable the checks.
-set_target_properties(fmt-header-only PROPERTIES INTERFACE_COMPILE_FEATURES "")
 
 list(APPEND Caffe2_DEPENDENCY_LIBS fmt::fmt-header-only)
 set(BUILD_SHARED_LIBS ${TEMP_BUILD_SHARED_LIBS} CACHE BOOL "Build shared libs" FORCE)
diff --git a/cmake/Modules/FindLMDB.cmake b/cmake/Modules/FindLMDB.cmake
index 2f0adb1..8a817fd 100644
--- a/cmake/Modules/FindLMDB.cmake
+++ b/cmake/Modules/FindLMDB.cmake
@@ -12,12 +12,8 @@
 # <AUTHOR> <EMAIL>
 # Aug 31, 2013
 
-if(MSVC)
-  find_package(LMDB NO_MODULE)
-else()
-  find_path(LMDB_INCLUDE_DIR NAMES  lmdb.h PATHS "$ENV{LMDB_DIR}/include")
-  find_library(LMDB_LIBRARIES NAMES lmdb   PATHS "$ENV{LMDB_DIR}/lib" )
-endif()
+find_path(LMDB_INCLUDE_DIR NAMES  lmdb.h PATHS "$ENV{LMDB_DIR}/include")
+find_library(LMDB_LIBRARIES NAMES lmdb   PATHS "$ENV{LMDB_DIR}/lib" )
 
 include(FindPackageHandleStandardArgs)
 find_package_handle_standard_args(LMDB DEFAULT_MSG LMDB_INCLUDE_DIR LMDB_LIBRARIES)
diff --git a/cmake/public/cuda.cmake b/cmake/public/cuda.cmake
index 32f3ba3..4112937 100644
--- a/cmake/public/cuda.cmake
+++ b/cmake/public/cuda.cmake
@@ -67,8 +67,8 @@ if(NOT CMAKE_CUDA_COMPILER_VERSION STREQUAL CUDAToolkit_VERSION OR
                       "V${CUDAToolkit_VERSION} in '${CUDAToolkit_INCLUDE_DIR}'")
 endif()
 
-if(NOT TARGET CUDA::nvToolsExt)
-  message(FATAL_ERROR "Failed to find nvToolsExt")
+if(NOT TARGET CUDA::nvToolsExt AND TARGET CUDA::nvtx3)
+  add_library(CUDA::nvToolsExt ALIAS CUDA::nvtx3)
 endif()
 
 message(STATUS "Caffe2: CUDA detected: " ${CUDA_VERSION})
diff --git a/cmake/public/utils.cmake b/cmake/public/utils.cmake
index 4d48c0f..ebdd39a 100644
--- a/cmake/public/utils.cmake
+++ b/cmake/public/utils.cmake
@@ -185,9 +185,9 @@ endfunction()
 macro(caffe2_update_option variable value)
   if(CAFFE2_CMAKE_BUILDING_WITH_MAIN_REPO)
     get_property(__help_string CACHE ${variable} PROPERTY HELPSTRING)
-    set(${variable} ${value} CACHE BOOL ${__help_string} FORCE)
+    set("${variable}" "${value}" CACHE BOOL "${__help_string}" FORCE)
   else()
-    set(${variable} ${value})
+    set("${variable}" "${value}")
   endif()
 endmacro()
 
