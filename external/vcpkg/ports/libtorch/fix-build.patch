diff --git a/c10/cuda/CMakeLists.txt b/c10/cuda/CMakeLists.txt
index c0628d0..5dfb7f2 100644
--- a/c10/cuda/CMakeLists.txt
+++ b/c10/cuda/CMakeLists.txt
@@ -74,7 +74,11 @@ add_subdirectory(test)
 # Note: for now, we will put all export path into one single Caffe2Targets group
 # to deal with the cmake deployment need. Inside the Caffe2Targets set, the
 # individual libraries like libc10.so and libcaffe2.so are still self-contained.
-install(TARGETS c10_cuda EXPORT Caffe2Targets DESTINATION lib)
+install(TARGETS c10_cuda EXPORT Caffe2Targets 
+  RUNTIME DESTINATION bin
+  ARCHIVE DESTINATION lib
+  LIBRARY DESTINATION lib
+  )
 foreach(file ${C10_CUDA_HEADERS})
   get_filename_component( dir ${file} DIRECTORY )
   install( FILES ${file} DESTINATION include/c10/cuda/${dir} )
diff --git a/c10/util/Exception.h b/c10/util/Exception.h
index ecbc7c1..2dd1001 100644
--- a/c10/util/Exception.h
+++ b/c10/util/Exception.h
@@ -14,6 +14,9 @@
 #define __func__ __FUNCTION__
 #endif
 
+#ifdef _MSC_VER
+#pragma warning(suppress : 4251)
+#endif
 namespace c10 {
 
 /// The primary ATen error class.
diff --git a/pyproject.toml b/pyproject.toml
index eb764cb..c70f317 100644
--- a/pyproject.toml
+++ b/pyproject.toml
@@ -4,11 +4,9 @@ requires = [
     "wheel",
     "astunparse",
     "numpy",
-    "ninja",
     "pyyaml",
-    "cmake",
     "typing-extensions",
-    "requests",
+#    "requests",
 ]
 # Use legacy backend to import local packages in setup.py
 build-backend = "setuptools.build_meta:__legacy__"
diff --git a/setup.py b/setup.py
index 7016441..a6416c7 100644
--- a/setup.py
+++ b/setup.py
@@ -263,7 +263,7 @@ from tools.setup_helpers.env import build_type, IS_DARWIN, IS_LINUX, IS_WINDOWS
 ################################################################################
 
 VERBOSE_SCRIPT = True
-RUN_BUILD_DEPS = True
+RUN_BUILD_DEPS = False
 # see if the user passed a quiet flag to setup.py arguments and respect
 # that in our parts of the build
 EMIT_BUILD_WARNING = False
@@ -307,9 +307,9 @@ else:
 
 # Constant known variables used throughout this file
 cwd = os.path.dirname(os.path.abspath(__file__))
-lib_path = os.path.join(cwd, "torch", "lib")
+lib_path = os.path.join(cwd, "../../@TARGET_TRIPLET@/", "lib")
 third_party_path = os.path.join(cwd, "third_party")
-caffe2_build_dir = os.path.join(cwd, "build")
+caffe2_build_dir = os.path.join(cwd, "../../@TARGET_TRIPLET@")
 
 # CMAKE: full path to python library
 if IS_WINDOWS:
@@ -759,7 +759,7 @@ class build_ext(setuptools.command.build_ext.build_ext):
             with open(filename) as f:
                 return json.load(f)
 
-        ninja_files = glob.glob("build/*compile_commands.json")
+        ninja_files = glob.glob("../../@TARGET_TRIPLET@/*compile_commands.json")
         cmake_files = glob.glob("torch/lib/build/*/compile_commands.json")
         all_commands = [entry for f in ninja_files + cmake_files for entry in load(f)]
 
diff --git a/third_party/nvfuser/csrc/instrumentation.h b/third_party/nvfuser/csrc/instrumentation.h
index cd57825..75e81e9 100644
--- a/third_party/nvfuser/csrc/instrumentation.h
+++ b/third_party/nvfuser/csrc/instrumentation.h
@@ -2,7 +2,7 @@
 
 #include <utils.h>
 
-#include <nvToolsExt.h>
+#include <nvtx3/nvToolsExt.h>
 
 // NOLINTNEXTLINE(modernize-deprecated-headers)
 #include <stdio.h>
diff --git a/tools/setup_helpers/env.py b/tools/setup_helpers/env.py
index d87e97a..6c52f39 100644
--- a/tools/setup_helpers/env.py
+++ b/tools/setup_helpers/env.py
@@ -19,7 +19,7 @@ CONDA_DIR = os.path.join(os.path.dirname(sys.executable), "..")
 
 IS_64BIT = struct.calcsize("P") == 8
 
-BUILD_DIR = "build"
+BUILD_DIR = "../../@TARGET_TRIPLET@"
 
 
 def check_env_flag(name: str, default: str = "") -> bool:
diff --git a/torch/lib/libshm/CMakeLists.txt b/torch/lib/libshm/CMakeLists.txt
index 20158a9..7d3c475 100644
--- a/torch/lib/libshm/CMakeLists.txt
+++ b/torch/lib/libshm/CMakeLists.txt
@@ -71,6 +71,10 @@ target_link_libraries(torch_shm_manager PRIVATE shm)
 set_target_properties(torch_shm_manager PROPERTIES
   INSTALL_RPATH "${_rpath_portable_origin}/../lib")
 
-install(TARGETS shm LIBRARY DESTINATION ${LIBSHM_INSTALL_LIB_SUBDIR})
+install(TARGETS shm 
+  RUNTIME DESTINATION bin
+  ARCHIVE DESTINATION lib
+  LIBRARY DESTINATION lib
+)
 install(FILES libshm.h DESTINATION "include")
 install(TARGETS torch_shm_manager DESTINATION "bin")
diff --git a/torch/lib/libshm_windows/CMakeLists.txt b/torch/lib/libshm_windows/CMakeLists.txt
index df2a106..b86e0b6 100644
--- a/torch/lib/libshm_windows/CMakeLists.txt
+++ b/torch/lib/libshm_windows/CMakeLists.txt
@@ -19,9 +19,12 @@ target_include_directories(shm PRIVATE
 target_link_libraries(shm torch c10)
 
 
-install(TARGETS shm DESTINATION "${LIBSHM_INSTALL_LIB_SUBDIR}")
+install(TARGETS shm 
+  LIBRARY DESTINATION "${LIBSHM_INSTALL_LIB_SUBDIR}"
+  ARCHIVE DESTINATION "${LIBSHM_INSTALL_LIB_SUBDIR}"
+  RUNTIME DESTINATION "${LIBSHM_INSTALL_BIN_SUBDIR}")
 install(FILES libshm.h DESTINATION "include")
 
 if(MSVC AND BUILD_SHARED_LIBS)
-  install(FILES $<TARGET_PDB_FILE:shm> DESTINATION "${LIBSHM_INSTALL_LIB_SUBDIR}" OPTIONAL)
+  install(FILES $<TARGET_PDB_FILE:shm> DESTINATION "${LIBSHM_INSTALL_BIN_SUBDIR}" OPTIONAL)
 endif()
diff --git a/torch/utils/cpp_extension.py b/torch/utils/cpp_extension.py
index 6600eb3..15ddc8c 100644
--- a/torch/utils/cpp_extension.py
+++ b/torch/utils/cpp_extension.py
@@ -38,7 +38,7 @@ SHARED_FLAG = '/DLL' if IS_WINDOWS else '-shared'
 
 _HERE = os.path.abspath(__file__)
 _TORCH_PATH = os.path.dirname(os.path.dirname(_HERE))
-TORCH_LIB_PATH = os.path.join(_TORCH_PATH, 'lib')
+TORCH_LIB_PATH = os.path.join(_TORCH_PATH, '../../../@TARGET_TRIPLET@', 'lib')
 
 
 SUBPROCESS_DECODE_ARGS = ('oem',) if IS_WINDOWS else ()
