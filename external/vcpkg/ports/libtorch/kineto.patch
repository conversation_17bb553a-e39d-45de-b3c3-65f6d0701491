diff --git a/libkineto/CMakeLists.txt b/libkineto/CMakeLists.txt
index 7d36ffb9d4..8f97998114 100644
--- a/libkineto/CMakeLists.txt
+++ b/libkineto/CMakeLists.txt
@@ -111,27 +111,8 @@ endif()
 target_compile_options(kineto_base PRIVATE "${KINETO_COMPILE_OPTIONS}")
 target_compile_options(kineto_api PRIVATE "${KINETO_COMPILE_OPTIONS}")
 
-if(NOT TARGET fmt)
-  if(NOT FMT_SOURCE_DIR)
-    set(FMT_SOURCE_DIR "${LIBKINETO_THIRDPARTY_DIR}/fmt"
-      CACHE STRING "fmt source directory from submodules")
-  endif()
-
-  # Build FMT.
-  # FMT and some other libraries use BUILD_SHARED_LIBS to control
-  # the library type.
-  # Save and restore the value after configuring FMT
-  set(TEMP_BUILD_SHARED_LIBS ${BUILD_SHARED_LIBS})
-  set(BUILD_SHARED_LIBS OFF CACHE BOOL "Build shared libs" FORCE)
-  set(FMT_LIBRARY_TYPE static CACHE STRING "Set lib type to static")
-  add_subdirectory("${FMT_SOURCE_DIR}" "${LIBKINETO_BINARY_DIR}/fmt")
-  set_property(TARGET fmt PROPERTY POSITION_INDEPENDENT_CODE ON)
-  set(BUILD_SHARED_LIBS ${TEMP_BUILD_SHARED_LIBS} CACHE BOOL "Build shared libs" FORCE)
-endif()
-
-set(FMT_INCLUDE_DIR "${FMT_SOURCE_DIR}/include")
-message(STATUS "Kineto: FMT_SOURCE_DIR = ${FMT_SOURCE_DIR}")
-message(STATUS "Kineto: FMT_INCLUDE_DIR = ${FMT_INCLUDE_DIR}")
+find_package(fmt REQUIRED)
+
 if (NOT CUPTI_INCLUDE_DIR)
     set(CUPTI_INCLUDE_DIR "${CUDA_SOURCE_DIR}/extras/CUPTI/include")
 endif()
