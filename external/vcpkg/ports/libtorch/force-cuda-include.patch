diff --git a/cmake/public/cuda.cmake b/cmake/public/cuda.cmake
index 5964429..d528597 100644
--- a/cmake/public/cuda.cmake
+++ b/cmake/public/cuda.cmake
@@ -57,6 +57,7 @@ find_package(CUDAToolkit REQUIRED)
 
 cmake_policy(POP)
 
+set(CUDAToolkit_INCLUDE_DIR "${CUDA_INCLUDE_DIRS}")
 if(NOT CMAKE_CUDA_COMPILER_VERSION STREQUAL CUDAToolkit_VERSION OR
     NOT CUDA_INCLUDE_DIRS STREQUAL CUDAToolkit_INCLUDE_DIR)
   message(FATAL_ERROR "Found two conflicting CUDA installs:\n"
