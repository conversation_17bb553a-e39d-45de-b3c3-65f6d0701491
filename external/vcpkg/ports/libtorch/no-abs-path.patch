diff --git a/caffe2/core/macros.h.in b/caffe2/core/macros.h.in
index 997752ede3..7209b9a63e 100644
--- a/caffe2/core/macros.h.in	
+++ b/caffe2/core/macros.h.in
@@ -35,7 +35,7 @@
 // Useful build settings that are recorded in the compiled binary
 #define CAFFE2_BUILD_STRINGS { \
   {"TORCH_VERSION", "${TORCH_VERSION}"}, \
-  {"CXX_COMPILER", "${CMAKE_CXX_COMPILER}"}, \
+  {"CXX_COMPILER", "${cxx_compiler}"}, \
   {"CXX_FLAGS", "${CMAKE_CXX_FLAGS}"}, \
   {"BUILD_TYPE", "${CMAKE_BUILD_TYPE}"}, \
   {"BLAS_INFO", "${BLAS_INFO}"}, \
diff --git a/cmake/Codegen.cmake b/cmake/Codegen.cmake
index 6fa2ab6ea1..07c449c02b 100644
--- a/cmake/Codegen.cmake	
+++ b/cmake/Codegen.cmake
@@ -36,6 +36,7 @@ endfunction()
 ################################################################################
 
 # ---[ Write the macros file
+cmake_path(GET CMAKE_CXX_COMPILER FILENAME cxx_compiler)
 configure_file(
     ${CMAKE_CURRENT_LIST_DIR}/../caffe2/core/macros.h.in
     ${CMAKE_BINARY_DIR}/caffe2/core/macros.h)
