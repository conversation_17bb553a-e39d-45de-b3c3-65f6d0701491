diff --git a/aten/src/ATen/Context.h b/aten/src/ATen/Context.h
index 285c6f7..1040acc 100644
--- a/aten/src/ATen/Context.h
+++ b/aten/src/ATen/Context.h
@@ -28,7 +28,7 @@ namespace at {
 
 class Tensor;
 
-enum class TORCH_API Float32MatmulPrecision { HIGHEST, HIGH, MEDIUM };
+enum class Float32MatmulPrecision { HIGHEST, HIGH, MEDIUM };
 
 class TORCH_API Context {
  public:
diff --git a/aten/src/ATen/core/function_schema.h b/aten/src/ATen/core/function_schema.h
index c48e9b0..cd8d4dd 100644
--- a/aten/src/ATen/core/function_schema.h
+++ b/aten/src/ATen/core/function_schema.h
@@ -206,7 +206,7 @@ inline bool operator!=(const Argument& lhs, const Argument& rhs) {
   return !(lhs == rhs);
 }
 
-enum struct TORCH_API SchemaArgType { input, output };
+enum struct SchemaArgType { input, output };
 
 /**
  * struct SchemaArgument
diff --git a/torch/csrc/jit/runtime/static/native_ops.cpp b/torch/csrc/jit/runtime/static/native_ops.cpp
index 7f3cbcf..fe80ab1 100644
--- a/torch/csrc/jit/runtime/static/native_ops.cpp
+++ b/torch/csrc/jit/runtime/static/native_ops.cpp
@@ -1055,7 +1055,7 @@ namespace {
   execution is completed, future is marked as complete to
   indicate aten::wait() to proceed
 */
-class TORCH_API ForkedSubgraphSRLauncher {
+class ForkedSubgraphSRLauncher {
  public:
   ForkedSubgraphSRLauncher(
       std::shared_ptr<StaticModule> smodule,
