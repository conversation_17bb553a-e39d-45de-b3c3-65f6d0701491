diff --git a/c10/util/Logging.cpp b/c10/util/Logging.cpp
index ff8e1d6cc..306dac51f 100644
--- a/c10/util/Logging.cpp	
+++ b/c10/util/Logging.cpp
@@ -218,24 +218,11 @@ C10_DEFINE_int(
     google::GLOG_WARNING,
     "The minimum log level that caffe2 will output.");
 
-// Google glog's api does not have an external function that allows one to check
-// if glog is initialized or not. It does have an internal function - so we are
-// declaring it here. This is a hack but has been used by a bunch of others too
-// (e.g. <PERSON><PERSON>).
-namespace google {
-namespace glog_internal_namespace_ {
-bool IsGoogleLoggingInitialized();
-} // namespace glog_internal_namespace_
-} // namespace google
-
 namespace c10 {
 namespace {
 
 void initGoogleLogging(char const* name) {
-#if !defined(_MSC_VER)
-  // This trick can only be used on UNIX platforms
-  if (!::google::glog_internal_namespace_::IsGoogleLoggingInitialized())
-#endif
+  if (!::google::IsGoogleLoggingInitialized())
   {
     ::google::InitGoogleLogging(name);
 #if !defined(_MSC_VER)
