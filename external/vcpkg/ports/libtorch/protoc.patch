diff --git a/CMakeLists.txt b/CMakeLists.txt
index fa6da328b..80bbfc6bb 100644
--- a/CMakeLists.txt	
+++ b/CMakeLists.txt
@@ -686,7 +686,7 @@ if(NOT CMAKE_BUILD_TYPE)
 endif()
 
 # The below means we are cross compiling for arm64 or x86_64 on MacOSX
-if(NOT IOS AND CMAKE_SYSTEM_NAME STREQUAL "Darwin" AND CMAKE_OSX_ARCHITECTURES MATCHES "^(x86_64|arm64)$")
+if(0)
   set(CROSS_COMPILING_MACOSX TRUE)
   # We need to compile a universal protoc to not fail protobuf build
   # We set CMAKE_TRY_COMPILE_TARGET_TYPE to STATIC_LIBRARY (vs executable) to succeed the cmake compiler check for cross-compiling
