diff --git a/CMakeLists.txt b/CMakeLists.txt
index 6b2175e..2aa699b 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -1,8 +1,7 @@
 cmake_minimum_required(VERSION 3.2.0)
 project(LRCTools VERSION 1.0.0)
 
-set(CMAKE_CXX_FLAGS
-    "${CMAKE_CXX_FLAGS} -std=c++11 -Wall -Wextra -Wno-missing-braces")
+set(CMAKE_CXX_STANDARD 11)
 
 include_directories(${CMAKE_CURRENT_LIST_DIR})
 
@@ -12,7 +11,22 @@ include(CPack)
 
 enable_testing()
 
-add_subdirectory(third_party)
 add_subdirectory(liblrc)
-add_subdirectory(cli)
-add_subdirectory(test)
+# headers
+install(FILES ${PROJECT_SOURCE_DIR}/liblrc/lrc_parser.h
+              ${PROJECT_SOURCE_DIR}/liblrc/lyrics.h
+              ${PROJECT_SOURCE_DIR}/liblrc/utils.h
+        DESTINATION include/liblrc
+)
+# library with unofficial config
+install(TARGETS lrc
+    EXPORT unofficial-liblrcConfig
+    RUNTIME DESTINATION bin
+    LIBRARY DESTINATION lib
+    ARCHIVE DESTINATION lib
+)
+# share/liblrc will be fixed in portfile.cmake
+install(EXPORT unofficial-liblrcConfig
+    NAMESPACE unofficial::liblrc::
+     DESTINATION share/unofficial-liblrc
+)
