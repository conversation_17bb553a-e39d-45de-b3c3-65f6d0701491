diff --git a/CMakeLists.txt b/CMakeLists.txt
index aaac4e38..58bafedb 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -95,7 +95,7 @@ add_subdirectory(src/render)
 add_subdirectory(src/thread)
 add_subdirectory(src/ui)
 
-target_compile_definitions(mujoco PRIVATE _GNU_SOURCE CCD_STATIC_DEFINE MUJOCO_DLL_EXPORTS -DMC_IMPLEM_ENABLE)
+target_compile_definitions(mujoco PRIVATE _GNU_SOURCE MUJOCO_DLL_EXPORTS -DMC_IMPLEM_ENABLE)
 if(MUJOCO_ENABLE_AVX_INTRINSICS)
   target_compile_definitions(mujoco PUBLIC mjUSEPLATFORMSIMD)
 endif()
@@ -118,9 +118,9 @@ target_link_libraries(
   mujoco
   PRIVATE ccd
           lodepng
-          qhullstatic_r
-          tinyobjloader
-          tinyxml2
+          Qhull::qhull_r
+          tinyobjloader::tinyobjloader
+          tinyxml2::tinyxml2
 )
 
 set_target_properties(
diff --git a/cmake/MujocoDependencies.cmake b/cmake/MujocoDependencies.cmake
index 23e4e71e..e4cfad28 100644
--- a/cmake/MujocoDependencies.cmake
+++ b/cmake/MujocoDependencies.cmake
@@ -90,7 +90,7 @@ set(BUILD_SHARED_LIBS
     CACHE INTERNAL "Build SHARED libraries"
 )
 
-if(NOT TARGET lodepng)
+if(0)
   FetchContent_Declare(
     lodepng
     GIT_REPOSITORY https://github.com/lvandeve/lodepng.git
@@ -110,6 +110,7 @@ if(NOT TARGET lodepng)
   endif()
 endif()
 
+if(0)
 if(NOT TARGET marchingcubecpp)
   FetchContent_Declare(
     marchingcubecpp
@@ -123,36 +124,60 @@ if(NOT TARGET marchingcubecpp)
     include_directories(${marchingcubecpp_SOURCE_DIR})
   endif()
 endif()
+endif()
+
+findorfetch(
+  USE_SYSTEM_PACKAGE
+  ON
+  PACKAGE_NAME
+  lodepng
+  LIBRARY_NAME
+  lodepng
+  GIT_REPO
+  https://github.com/lvandeve/lodepng.git
+  GIT_TAG
+  ${MUJOCO_DEP_VERSION_lodepng}
+  TARGETS
+  lodepng
+  EXCLUDE_FROM_ALL
+)
 
 set(QHULL_ENABLE_TESTING OFF)
 
 findorfetch(
   USE_SYSTEM_PACKAGE
-  OFF
+  ON
   PACKAGE_NAME
-  qhull
+  Qhull
   LIBRARY_NAME
-  qhull
+  Qhull
   GIT_REPO
   https://github.com/qhull/qhull.git
   GIT_TAG
   ${MUJOCO_DEP_VERSION_qhull}
   TARGETS
-  qhull
+  Qhull
   EXCLUDE_FROM_ALL
 )
 # MuJoCo includes a file from libqhull_r which is not exported by the qhull include directories.
 # Add it to the target.
+if(0)
 target_include_directories(
   qhullstatic_r INTERFACE $<BUILD_INTERFACE:${qhull_SOURCE_DIR}/src/libqhull_r>
 )
 target_compile_options(qhullstatic_r PRIVATE ${MUJOCO_MACOS_COMPILE_OPTIONS})
 target_link_options(qhullstatic_r PRIVATE ${MUJOCO_MACOS_LINK_OPTIONS})
+endif()
+
+include_directories(
+  ${Qhull_DIR}/../../include/libqhull_r
+  ${Qhull_DIR}/../../include/marchingcubecpp
+)
 
 set(tinyxml2_BUILD_TESTING OFF)
 findorfetch(
   USE_SYSTEM_PACKAGE
-  OFF
+  ON
   PACKAGE_NAME
   tinyxml2
   LIBRARY_NAME
@@ -165,12 +190,14 @@ findorfetch(
   tinyxml2
   EXCLUDE_FROM_ALL
 )
+if(0)
 target_compile_options(tinyxml2 PRIVATE ${MUJOCO_MACOS_COMPILE_OPTIONS})
 target_link_options(tinyxml2 PRIVATE ${MUJOCO_MACOS_LINK_OPTIONS})
+endif()
 
 findorfetch(
   USE_SYSTEM_PACKAGE
-  OFF
+  ON
   PACKAGE_NAME
   tinyobjloader
   LIBRARY_NAME
@@ -189,9 +216,9 @@ option(SDFLIB_USE_OPENMP OFF)
 option(SDFLIB_USE_ENOKI OFF)
 findorfetch(
   USE_SYSTEM_PACKAGE
-  OFF
+  ON
   PACKAGE_NAME
-  sdflib
+  SdfLib
   LIBRARY_NAME
   sdflib
   GIT_REPO
@@ -202,14 +229,19 @@ findorfetch(
   SdfLib
   EXCLUDE_FROM_ALL
 )
+
+add_library(SdfLib ALIAS SdfLib::SdfLib)
+
+if(0)
 target_compile_options(SdfLib PRIVATE ${MUJOCO_MACOS_COMPILE_OPTIONS})
 target_link_options(SdfLib PRIVATE ${MUJOCO_MACOS_LINK_OPTIONS})
+endif()
 
 set(ENABLE_DOUBLE_PRECISION ON)
 set(CCD_HIDE_ALL_SYMBOLS ON)
 findorfetch(
   USE_SYSTEM_PACKAGE
-  OFF
+  ON
   PACKAGE_NAME
   ccd
   LIBRARY_NAME
@@ -222,11 +254,14 @@ findorfetch(
   ccd
   EXCLUDE_FROM_ALL
 )
+if(0)
 target_compile_options(ccd PRIVATE ${MUJOCO_MACOS_COMPILE_OPTIONS})
 target_link_options(ccd PRIVATE ${MUJOCO_MACOS_LINK_OPTIONS})
+endif()
 
 # libCCD has an unconditional `#define _CRT_SECURE_NO_WARNINGS` on Windows.
 # TODO(stunya): Remove this after https://github.com/danfis/libccd/pull/77 is merged.
+if(0)
 if(WIN32)
   if(MSVC)
     # C4005 is the MSVC equivalent of -Wmacro-redefined.
@@ -235,6 +270,7 @@ if(WIN32)
     target_compile_options(ccd PRIVATE -Wno-macro-redefined)
   endif()
 endif()
+endif()
 
 if(MUJOCO_BUILD_TESTS)
   set(ABSL_PROPAGATE_CXX_STD ON)
diff --git a/simulate/cmake/SimulateDependencies.cmake b/simulate/cmake/SimulateDependencies.cmake
index 5141406c..41f399b7 100644
--- a/simulate/cmake/SimulateDependencies.cmake
+++ b/simulate/cmake/SimulateDependencies.cmake
@@ -86,7 +86,7 @@ findorfetch(
   GIT_TAG
   ${MUJOCO_DEP_VERSION_glfw3}
   TARGETS
-  glfw
+  glfw3
   EXCLUDE_FROM_ALL
 )
 
@@ -98,7 +98,7 @@ if(MUJOCO_EXTRAS_STATIC_GLFW)
   unset(BUILD_SHARED_LIBS_OLD)
 endif()
 
-if(NOT SIMULATE_STANDALONE)
+if(0)
   target_compile_options(glfw PRIVATE ${MUJOCO_MACOS_COMPILE_OPTIONS})
   target_link_options(glfw PRIVATE ${MUJOCO_MACOS_LINK_OPTIONS})
 endif()
