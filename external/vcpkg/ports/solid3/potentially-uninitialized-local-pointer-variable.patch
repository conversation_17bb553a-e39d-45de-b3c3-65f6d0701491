diff --git a/src/qhull/geom2.c b/src/qhull/geom2.c
index bd58ce1..c4798d2 100644
--- a/src/qhull/geom2.c
+++ b/src/qhull/geom2.c
@@ -2080,7 +2080,7 @@ boolT qh_sharpnewfacets () {
 pointT *qh_voronoi_center (int dim, setT *points) {
   pointT *point, **pointp, *point0;
   pointT *center= (pointT*)qh_memalloc (qh center_size);
-  setT *simplex;
+  setT *simplex= NULL;
   int i, j, k, size= qh_setsize(points);
   coordT *gmcoord;
   realT *diffp, sum2, *sum2row, *sum2p, det, factor;
diff --git a/src/qhull/io.c b/src/qhull/io.c
index 79ca799..41c18bc 100644
--- a/src/qhull/io.c
+++ b/src/qhull/io.c
@@ -3740,7 +3740,7 @@ coordT *qh_readpoints(int *numpoints, int *dimension, boolT *ismalloc) {
   coordT *points, *coords, *infinity= NULL;
   realT paraboloid, maxboloid= -REALmax, value;
   realT *coordp= NULL, *offsetp= NULL, *normalp= NULL;
-  char *s, *t, firstline[qh_MAXfirst+1];
+  char *s= NULL, *t, firstline[qh_MAXfirst+1];
   int diminput=0, numinput=0, dimfeasible= 0, newnum, k, tempi;
   int firsttext=0, firstshort=0, firstlong=0, firstpoint=0;
   int tokcount= 0, linecount=0, maxcount, coordcount=0;
