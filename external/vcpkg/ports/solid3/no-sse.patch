diff --git a/CMakeLists.txt b/CMakeLists.txt
index be43838..fe71394 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -87,8 +87,12 @@ if(MSVC)
 endif(MSVC)
 
 if(UNIX)
-  set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -Wall -ffast-math -msse2 -mfpmath=sse")
-  set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fno-rtti -Wall -ffast-math -msse2 -mfpmath=sse")
+  set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -Wall -ffast-math")
+  set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fno-rtti -Wall -ffast-math")
+  if (NOT CMAKE_SYSTEM_PROCESSOR MATCHES "arm")    
+    set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -msse2 -mfpmath=sse")
+    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -msse2 -mfpmath=sse")
+  endif()
   if (DYNAMIC_SOLID)
     set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -fPIC")
     set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fPIC")
