diff --git a/cmake/liblas-config.cmake.in b/cmake/liblas-config.cmake.in
index 175e997..8a55804 100644
--- a/cmake/liblas-config.cmake.in
+++ b/cmake/liblas-config.cmake.in
@@ -19,6 +19,8 @@ set (libLAS_INCLUDE_DIRS "${PROJECT_ROOT_DIR}/include")
 set (libLAS_LIBRARY_DIRS "${PROJECT_ROOT_DIR}/lib")
 set (libLAS_BINARY_DIRS "${PROJECT_ROOT_DIR}/bin")
 
+include(CMakeFindDependencyMacro)
+find_dependency(GeoTIFF CONFIG)
-include ("${_DIR}/liblas-depends.cmake")
+include ("${CMAKE_CURRENT_LIST_DIR}/liblas-depends.cmake")
 if(WIN32)
   set (libLAS_LIBRARIES liblas liblas_c)
