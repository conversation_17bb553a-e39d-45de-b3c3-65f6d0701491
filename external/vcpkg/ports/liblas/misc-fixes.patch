diff --git a/CMakeLists.txt b/CMakeLists.txt
index d246a88..3da2106 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -6,6 +6,7 @@
 #
 ###############################################################################
 # libLAS general settings
+cmake_minimum_required(VERSION 3.7.0)
 project(libLAS)
 
 
@@ -64,7 +65,6 @@ set(WITH_ENDIANAWARE FALSE CACHE BOOL "Choose whether or not libLAS should do ru
 
 ###############################################################################
 # CMake settings
-cmake_minimum_required(VERSION 2.6.0)
 
 set(CMAKE_COLOR_MAKEFILE ON)
 
@@ -107,7 +107,6 @@ set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${LIBLAS_BUILD_OUTPUT_DIRECTORY})
 set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${LIBLAS_BUILD_OUTPUT_DIRECTORY})
 
 file(READ "doc/index.txt" README )
-file(WRITE "README.txt" "${README}")
 
 ###############################################################################
 # Platform and compiler specific settings
@@ -138,8 +137,6 @@ if(WIN32)
 
   endif()
 
-  set(CMAKE_INCLUDE_PATH c:/osgeo4w64/include;$ENV{CMAKE_INCLUDE_PATH})
-  set(CMAKE_LIBRARY_PATH c:/osgeo4w64/lib;$ENV{CMAKE_LIBRARY_PATH})
 else()
 
   # Recommended C++ compilation flags
@@ -337,8 +334,6 @@ set(LIBLAS_DATA_DIR ${LIBLAS_DATA_SUBDIR})
 ###############################################################################
 # Installation commands
 
-install(FILES AUTHORS COPYING INSTALL LICENSE.txt README.txt
-  DESTINATION ${LIBLAS_DATA_DIR}/doc)
 
 ###############################################################################
 # Processing of project directories
diff --git a/apps/CMakeLists.txt b/apps/CMakeLists.txt
index beb83e2..e328c95 100644
--- a/apps/CMakeLists.txt
+++ b/apps/CMakeLists.txt
@@ -160,7 +160,7 @@ install(TARGETS ${LIBLAS_UTILITIES}
     ARCHIVE DESTINATION ${LIBLAS_LIB_DIR})
 
 
-if(UNIX)
+if(0)
 
   set(LIBLAS_UTILS_RPATH ${CMAKE_INSTALL_PREFIX}/lib ${Boost_LIBRARY_DIRS})
   if(LASZIP_FOUND)
