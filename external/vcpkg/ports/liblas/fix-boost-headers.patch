diff --git a/include/liblas/detail/binary.hpp b/include/liblas/detail/binary.hpp
index d3116a1..ab20e06 100644
--- a/include/liblas/detail/binary.hpp
+++ b/include/liblas/detail/binary.hpp
@@ -17,6 +17,8 @@
 #ifndef LIBLAS_DETAIL_BINARY_HPP_INCLUDED
 #define LIBLAS_DETAIL_BINARY_HPP_INCLUDED
 
+#include <liblas/detail/endian.hpp>
+
 #include <cassert>
 #include <climits>
 #include <cstring>
@@ -25,7 +27,6 @@
 
 #include <boost/config.hpp>
 #include <boost/static_assert.hpp>
-#include <boost/detail/endian.hpp>
 #include <boost/type_traits/is_signed.hpp>
 
 #if CHAR_BIT != 8
@@ -43,7 +44,7 @@ namespace detail { namespace binary {
 struct big_endian_tag {};
 struct little_endian_tag {};
 
-#ifdef BOOST_BIG_ENDIAN
+#ifdef LIBLAS_BIG_ENDIAN
 typedef big_endian_tag native_endian_tag;
 #else
 typedef little_endian_tag native_endian_tag;
diff --git a/src/c_api.cpp b/src/c_api.cpp
index a70fe74..e8be785 100644
--- a/src/c_api.cpp
+++ b/src/c_api.cpp
@@ -96,6 +96,7 @@ using namespace liblas;
 #endif
 
 #include <boost/lambda/lambda.hpp>
+#include <boost/bind.hpp>
 
 bool IsReprojectionTransform(liblas::TransformPtr const& p)
 {
diff --git a/src/header.cpp b/src/header.cpp
index 9987ce1..a5c8f7d 100644
--- a/src/header.cpp
+++ b/src/header.cpp
@@ -54,7 +54,7 @@
 // boost
 #include <boost/cstdint.hpp>
 #include <boost/lambda/lambda.hpp>
-
+#include <boost/bind.hpp>
 //std
 #include <algorithm>
 #include <fstream>
