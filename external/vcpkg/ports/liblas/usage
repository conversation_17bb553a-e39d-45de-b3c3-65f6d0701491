liblas provides CMake targets:

    # C API
    find_package(libLAS CONFIG REQUIRED)
    target_link_libraries(main PRIVATE $<$<PLATFORM_ID:Windows>:lib>las_c)
    target_include_directories(main SYSTEM PRIVATE ${libLAS_INCLUDE_DIRS})

    # C++ API
    find_package(libLAS CONFIG REQUIRED)
    target_link_libraries(main PRIVATE $<$<PLATFORM_ID:Windows>:lib>las)
    target_include_directories(main SYSTEM PRIVATE ${libLAS_INCLUDE_DIRS})
