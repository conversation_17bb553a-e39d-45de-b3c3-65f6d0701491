diff --git a/CMakeLists.txt b/CMakeLists.txt
index d246a88d..634157c0 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -190,11 +190,13 @@ if(WIN32)
 endif(WIN32)
 
 find_package(Threads)
-find_package(Boost 1.42 COMPONENTS program_options thread system iostreams filesystem REQUIRED)
+find_package(Boost 1.42 COMPONENTS iostreams program_options serialization thread REQUIRED)
+ 
+# The following header-only and their dependencies are additionally required,
+# but cannot be explicitly requested via find_package, so make sure they exists:
+# - foreach interprocess lambda property_tree uuid
 
-if(Boost_FOUND AND Boost_PROGRAM_OPTIONS_FOUND)
-  include_directories(${Boost_INCLUDE_DIRS})
-endif()
+include_directories(${Boost_INCLUDE_DIRS})
 
 # make these available for the user to set.
 mark_as_advanced(CLE<PERSON> Boost_INCLUDE_DIR)
