{"name": "liblas", "version": "1.8.1", "port-version": 15, "description": "A C/C++ library for reading and writing the very common LAS LiDAR format.", "license": null, "supports": "!arm & !staticcrt", "dependencies": ["boost-foreach", "boost-interprocess", "boost-iostreams", "boost-lambda", "boost-program-options", "boost-property-tree", "boost-uuid", "libgeotiff", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"jpeg": {"description": "This feature does nothing. It is retained for compatibility."}, "tools": {"description": "Build utilities."}, "zlib": {"description": "This feature does nothing. It is retained for compatibility."}}}