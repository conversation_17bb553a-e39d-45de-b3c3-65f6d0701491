diff --git a/cmake/unix_compiler_options.cmake b/cmake/unix_compiler_options.cmake
index 0a6550f..65a9f4c 100644
--- a/cmake/unix_compiler_options.cmake
+++ b/cmake/unix_compiler_options.cmake
@@ -1,4 +1,4 @@
-set(LASZIP_COMMON_CXX_FLAGS "-Wextra -Wall -Wno-unused-parameter -Wno-unused-variable -Wpointer-arith -Wcast-qual -Wredundant-decls -Wno-long-long -Wno-unknown-pragmas -isystem /usr/local/include"
+set(LASZIP_COMMON_CXX_FLAGS "-Wextra -Wall -Wno-unused-parameter -Wno-unused-variable -Wpointer-arith -Wcast-qual -Wredundant-decls -Wno-long-long -Wno-unknown-pragmas"
 )
 
 if (${CMAKE_CXX_COMPILER_ID} MATCHES "GNU")
diff --git a/cmake/win32_compiler_options.cmake b/cmake/win32_compiler_options.cmake
index 95049b8..d252d83 100644
--- a/cmake/win32_compiler_options.cmake
+++ b/cmake/win32_compiler_options.cmake
@@ -45,8 +45,6 @@ if (MSVC)
         include(ProcessorCount)
         ProcessorCount(N)
         if(NOT N EQUAL 0)
-            set(CMAKE_C_FLAGS   "${CMAKE_C_FLAGS}   /MP${N}")
-            set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /MP${N}")
         endif()
     endif()
 
@@ -77,9 +75,6 @@ add_definitions(-DWIN32_LEAN_AND_MEAN)
 #  message(STATUS "Setting PDAL build type - ${CMAKE_BUILD_TYPE}")
 #endif()
 
-set(CMAKE_INCLUDE_PATH "c:/OSGeo4W64/include;$ENV{CMAKE_INCLUDE_PATH}")
-set(CMAKE_LIBRARY_PATH "c:/OSGeo4W64/lib;$ENV{CMAKE_LIBRARY_PATH}")
-set(CMAKE_PREFIX_PATH "c:/OSGeo4W64/cmake;$ENV{CMAKE_LIBRARY_PATH}")
 
 #ABELL - WHY?
 set(PDAL_PLATFORM_WIN32 1)
