{"$comment": "Keep the platform expressions in sync with the wrappers installed by the portfiles!", "name": "lapack", "version-date": "2023-06-10", "port-version": 2, "description": "Metapackage for packages which provide LAPACK", "license": null, "supports": "!android", "dependencies": [{"name": "clapack", "platform": "uwp | (arm & windows)"}, {"name": "lapack-reference", "platform": "!osx & !ios & !uwp & !(arm & windows)"}, {"name": "vcpkg-cmake", "host": true}]}