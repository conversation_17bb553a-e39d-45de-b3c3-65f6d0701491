diff --git a/cmake/FindOpen62541.cmake b/cmake/FindOpen62541.cmake
index b4fcea410..91958e554 100644
--- a/cmake/FindOpen62541.cmake
+++ b/cmake/FindOpen62541.cmake
@@ -21,6 +21,7 @@
 # ``open62541``
 #     The open62541 library
 
+if(0)
 find_path(Open62541_INCLUDE_DIRS
     NAMES open62541.h
     HINTS "${OPEN62541_INCDIR}")
@@ -46,6 +47,15 @@ if (Open62541_FOUND)
 endif()
 
 mark_as_advanced(Open62541_INCLUDE_DIRS Open62541_LIBRARIES)
+else()
+    find_package(open62541 CONFIG REQUIRED)
+    if(NOT TARGET open62541)
+        add_library(open62541 INTERFACE IMPORTED)
+        set_property(TARGET open62541 APPEND PROPERTY
+            INTERFACE_LINK_LIBRARIES open62541::open62541)
+    endif()
+    set(Open62541_FOUND ON)
+endif()
 
 include(FeatureSummary)
 set_package_properties(Open62541 PROPERTIES
