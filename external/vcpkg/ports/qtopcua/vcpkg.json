{"name": "qtopcua", "version": "6.8.3", "description": "The Qt OPC UA module implements a Qt API to interact with OPC UA on top of a 3rd party OPC UA stack.", "homepage": "https://www.qt.io/", "license": null, "dependencies": [{"name": "qtbase", "default-features": false, "features": ["network"]}, {"name": "qtopcua", "host": true, "default-features": false}], "default-features": ["gds", "ns0idnames", "open62541"], "features": {"gds": {"description": "Support for global discovery server"}, "ns0idgenerator": {"description": "Namespace 0 NodeIds generator from the NodeIds.csv file.", "supports": "native"}, "ns0idnames": {"description": "Support for namespace 0 NodeId names"}, "open62541": {"description": "Open62541 with plugin to connect to servers with signing and encryption", "dependencies": [{"name": "open62541", "default-features": false, "features": ["amalgamation", "historizing", "openssl"]}]}, "qml": {"description": "Build QML imports", "dependencies": [{"name": "qtbase", "default-features": false, "features": ["gui"]}, {"name": "qtdeclarative", "default-features": false}]}, "uacpp": {"description": "Unified Automation C++ SDK"}}}