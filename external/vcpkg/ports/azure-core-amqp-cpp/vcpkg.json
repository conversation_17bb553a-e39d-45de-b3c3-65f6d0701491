{"$comment": ["NOTE: All changes made to this file will get overwritten by the next port release.", "Please contribute your changes to https://github.com/Azure/azure-sdk-for-cpp."], "name": "azure-core-amqp-cpp", "version-semver": "1.0.0-beta.11", "port-version": 1, "description": ["Microsoft Azure AMQP SDK for C++", "This library provides AMQP functionality to Azure SDK services."], "homepage": "https://github.com/Azure/azure-sdk-for-cpp/tree/main/sdk/core/azure-core-amqp", "license": "MIT", "supports": "!uwp", "dependencies": ["azure-c-shared-utility", {"name": "azure-core-cpp", "default-features": false, "version>=": "1.11.3"}, "azure-macro-utils-c", "umock-c", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}]}