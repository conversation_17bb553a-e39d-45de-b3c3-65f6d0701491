{"$comment": "Automatically generated by scripts/boost/generate-ports.ps1", "name": "boost-ptr-container", "version": "1.87.0", "description": "Boost ptr_container module", "homepage": "https://www.boost.org/libs/ptr_container", "license": "BSL-1.0", "dependencies": [{"name": "boost-array", "version>=": "1.87.0"}, {"name": "boost-assert", "version>=": "1.87.0"}, {"name": "boost-circular-buffer", "version>=": "1.87.0"}, {"name": "boost-cmake", "version>=": "1.87.0"}, {"name": "boost-config", "version>=": "1.87.0"}, {"name": "boost-core", "version>=": "1.87.0"}, {"name": "boost-headers", "version>=": "1.87.0"}, {"name": "boost-iterator", "version>=": "1.87.0"}, {"name": "boost-mpl", "version>=": "1.87.0"}, {"name": "boost-range", "version>=": "1.87.0"}, {"name": "boost-smart-ptr", "version>=": "1.87.0"}, {"name": "boost-static-assert", "version>=": "1.87.0"}, {"name": "boost-type-traits", "version>=": "1.87.0"}, {"name": "boost-unordered", "version>=": "1.87.0"}, {"name": "boost-utility", "version>=": "1.87.0"}]}