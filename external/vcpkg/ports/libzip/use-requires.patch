diff --git a/CMakeLists.txt b/CMakeLists.txt
index 472a7a2..f9479be 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -48,12 +48,14 @@ endif()
 if(ENABLE_GNUTLS)
   find_package(Nettle 3.0)
   find_package(GnuTLS)
+  list(APPEND REQUIRES nettle)
 endif()
 if(ENABLE_MBEDTLS)
   find_package(MbedTLS 1.0)
 endif()
 if(ENABLE_OPENSSL)
   find_package(OpenSSL)
+  list(APPEND REQUIRES openssl)
 endif()
 if(WIN32)
   if(ENABLE_WINDOWS_CRYPTO)
@@ -196,6 +198,7 @@ int main(int argc, char *argv[]) { unsigned long x = FICLONERANGE; }" HAVE_FICLO
 test_big_endian(WORDS_BIGENDIAN)
 
 find_package(ZLIB 1.1.2 REQUIRED)
+list(APPEND REQUIRES zlib)
 # so developers on systems where zlib is named differently (Windows, sometimes)
 # can override the name used in the pkg-config file
 if (NOT ZLIB_LINK_LIBRARY_NAME)
@@ -232,6 +235,7 @@ if(ENABLE_BZIP2)
   else()
     message(WARNING "-- bzip2 library not found; bzip2 support disabled")
   endif(BZIP2_FOUND)
+  list(APPEND REQUIRES bzip2)
 endif(ENABLE_BZIP2)
 
 if(ENABLE_LZMA)
@@ -241,6 +245,7 @@ if(ENABLE_LZMA)
   else()
     message(WARNING "-- lzma library not found; lzma/xz support disabled")
   endif(LIBLZMA_FOUND)
+  list(APPEND REQUIRES liblzma)
 endif(ENABLE_LZMA)
 
 if(ENABLE_ZSTD)
@@ -255,6 +260,7 @@ if(ENABLE_ZSTD)
   else()
     message(WARNING "-- zstd library not found; zstandard support disabled")
   endif(zstd_FOUND)
+  list(APPEND REQUIRES libzstd)
 endif(ENABLE_ZSTD)
 
 if (COMMONCRYPTO_FOUND)
@@ -356,15 +362,16 @@ foreach(LIB ${LIBS_PRIVATE})
   set(LIBS "${LIBS} -l${LIB}")
 endforeach()
 STRING(CONCAT zlib_link_name "-l" ${ZLIB_LINK_LIBRARY_NAME})
-string(REGEX REPLACE "-lBZip2::BZip2" "-lbz2" LIBS ${LIBS})
-string(REGEX REPLACE "-lLibLZMA::LibLZMA" "-llzma" LIBS ${LIBS})
+string(REGEX REPLACE "-lBZip2::BZip2" "" LIBS ${LIBS})
+string(REGEX REPLACE "-lLibLZMA::LibLZMA" "" LIBS ${LIBS})
 if(zstd_TARGET)
-  string(REGEX REPLACE "-l${zstd_TARGET}" "-lzstd" LIBS ${LIBS})
+  string(REGEX REPLACE "-l${zstd_TARGET}" "" LIBS ${LIBS})
 endif()
-string(REGEX REPLACE "-lOpenSSL::Crypto" "-lssl -lcrypto" LIBS ${LIBS})
-string(REGEX REPLACE "-lZLIB::ZLIB" ${zlib_link_name} LIBS ${LIBS})
+string(REGEX REPLACE "-lOpenSSL::Crypto" "" LIBS ${LIBS})
+string(REGEX REPLACE "-lZLIB::ZLIB" "" LIBS ${LIBS})
 string(REGEX REPLACE "-lGnuTLS::GnuTLS" "-lgnutls" LIBS ${LIBS})
 string(REGEX REPLACE "-lNettle::Nettle" "-lnettle" LIBS ${LIBS})
+list(JOIN REQUIRES " " REQUIRES)
 configure_file(libzip.pc.in libzip.pc @ONLY)
 if(LIBZIP_DO_INSTALL)
   install(FILES ${PROJECT_BINARY_DIR}/libzip.pc DESTINATION ${CMAKE_INSTALL_LIBDIR}/pkgconfig)
diff --git a/libzip.pc.in b/libzip.pc.in
index d51b0ab..07b3b50 100644
--- a/libzip.pc.in
+++ b/libzip.pc.in
@@ -9,6 +9,7 @@ zipcmp=${bindir}/zipcmp
 Name: libzip
 Description: library for handling zip archives
 Version: @PROJECT_VERSION@
+Requires.private: @REQUIRES@
 Libs: @PKG_CONFIG_RPATH@ -L${libdir} -lzip
 Libs.private: @LIBS@
 Cflags: -I${includedir}
