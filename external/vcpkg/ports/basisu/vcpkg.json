{"name": "basisu", "version": "1.50.0", "description": "Basis Universal is a supercompressed GPU texture and video compression format that outputs a highly compressed intermediate file format (.basis) that can be quickly transcoded to a wide variety of GPU texture compression formats.", "homepage": "https://github.com/BinomialLLC/basis_universal", "license": "Apache-2.0", "supports": "!android & !arm", "dependencies": ["lodepng", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}]}