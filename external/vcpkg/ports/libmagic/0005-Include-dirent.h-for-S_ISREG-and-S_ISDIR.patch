From a8f8fbfc2736419c65992cbf24de963c3b1f3107 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Sat, 8 May 2021 22:07:55 +0700
Subject: [PATCH 05/14] Include dirent.h for S_ISREG and S_ISDIR

---
 src/file.h | 1 +
 1 file changed, 1 insertion(+)

diff --git a/src/file.h b/src/file.h
index 0332506..4aa9f60 100644
--- a/src/file.h
+++ b/src/file.h
@@ -88,7 +88,8 @@
 /* Do this here and now, because struct stat gets re-defined on solaris */
 #include <sys/stat.h>
 #include <stdarg.h>
+#include <dirent.h>
 #include <locale.h>
 #if defined(HAVE_XLOCALE_H)
 #include <xlocale.h>
 #endif
-- 
2.29.2.windows.2

