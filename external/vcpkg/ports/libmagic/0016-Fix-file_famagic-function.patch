diff --git a/src/fsmagic.c b/src/fsmagic.c
index 7244841..2c553c1 100644
--- a/src/fsmagic.c
+++ b/src/fsmagic.c
@@ -66,7 +66,7 @@ # define major(dev)  (((dev) >> 8) & 0xff)
 # define minor(dev)  ((dev) & 0xff)
 #endif
 #undef HAVE_MAJOR
-#if S_IFLNK != 0
+#if S_IFLNK != 0 && ! defined(_WIN32)
 file_private int
 bad_link(struct magic_set *ms, int err, char *buf)
 {
@@ -108,7 +108,7 @@ file_fsmagic(struct magic_set *ms, const char *fn, struct stat *sb)
 	int ret, did = 0;
 	int mime = ms->flags & MAGIC_MIME;
 	int silent = ms->flags & (MAGIC_APPLE|MAGIC_EXTENSION);
-#if S_IFLNK != 0
+#if S_IFLNK != 0 && ! defined(_WIN32)
 	char buf[BUFSIZ+4];
 	ssize_t nch;
 	struct stat tstatbuf;
@@ -122,7 +122,7 @@ file_fsmagic(struct magic_set *ms, const char *fn, struct stat *sb)
 	 * Fstat is cheaper but fails for files you don't have read perms on.
 	 * On 4.2BSD and similar systems, use lstat() to identify symlinks.
 	 */
-#if S_IFLNK != 0
+#if S_IFLNK != 0 && ! defined(_WIN32)
 	if ((ms->flags & MAGIC_SYMLINK) == 0)
 		ret = lstat(fn, sb);
 	else
@@ -290,7 +290,7 @@ file_fsmagic(struct magic_set *ms, const char *fn, struct stat *sb)
 			return -1;
 		break;
 #endif
-#if S_IFLNK != 0
+#if S_IFLNK != 0 && ! defined(_WIN32)
 	case S_IFLNK:
 		if ((nch = readlink(fn, buf, BUFSIZ-1)) <= 0) {
 			if (ms->flags & MAGIC_ERROR) {
