From 1106d1523ed44bcbcca90fc1fe9fa9400777d8fe Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Sun, 9 May 2021 10:28:49 +0700
Subject: [PATCH 07/14] Substitute unistd macros for MSVC

---
 src/file.h | 7 +++++++
 1 file changed, 7 insertions(+)

diff --git a/src/file.h b/src/file.h
index 4aa9f60..5da838c 100644
--- a/src/file.h
+++ b/src/file.h
@@ -85,6 +85,13 @@
 #ifndef _WIN32
 #include <sys/param.h>
 #endif
+#ifdef _MSC_VER
+#include <io.h>
+#define R_OK 4
+#define W_OK 2
+#define X_OK R_OK
+#define F_OK 0
+#endif
 /* Do this here and now, because struct stat gets re-defined on solaris */
 #include <sys/stat.h>
 #include <stdarg.h>
-- 
2.29.2.windows.2

