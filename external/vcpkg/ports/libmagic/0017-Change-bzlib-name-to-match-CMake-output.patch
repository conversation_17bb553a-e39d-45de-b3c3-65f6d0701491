diff --git a/configure.ac b/configure.ac
index 8b54efda..d043fb06 100644
--- a/configure.ac
+++ b/configure.ac
@@ -191,7 +191,7 @@ if test "$enable_zlib" != "no"; then
   AC_SEARCH_LIBS(gzopen, [z zlib zlibd], have_zlib="yes", have_zlib="no")
 fi
 if test "$enable_bzlib" != "no"; then
-  AC_CHECK_LIB(bz2, BZ2_bzCompressInit)
+  AC_SEARCH_LIBS(BZ2_bzCompressInit, [bz2 bz2d], have_bzlib="yes", have_bzlib="no")
 fi
 if test "$enable_xzlib" != "no"; then
   AC_CHECK_LIB(lzma, lzma_stream_decoder)
@@ -222,11 +222,11 @@ if  test "$ac_cv_header_zlib_h$have_zlib" = "yesyes"; then
   AC_DEFINE([ZLIBSUPPORT], 1, [Enable zlib compression support])
 fi
 if test "$enable_bzlib" = "yes"; then
-  if test "$ac_cv_header_bzlib_h$ac_cv_lib_bz2_BZ2_bzCompressInit" != "yesyes"; then
+  if test "$ac_cv_header_bzlib_h$have_bzlib" != "yesyes"; then
     AC_MSG_ERROR([bzlib support requested but not found])
   fi
 fi
-if  test "$ac_cv_header_bzlib_h$ac_cv_lib_bz2_BZ2_bzCompressInit" = "yesyes"; then
+if  test "$ac_cv_header_bzlib_h$have_bzlib" = "yesyes"; then
   AC_DEFINE([BZLIBSUPPORT], 1, [Enable bzlib compression support])
 fi
 if test "$enable_xzlib" = "yes"; then
