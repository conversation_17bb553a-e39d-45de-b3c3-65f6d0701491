From b20c8e62851f3cb53083a3e7811518092f98dc26 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Sat, 8 May 2021 22:47:32 +0700
Subject: [PATCH 06/14] Remove/Wrap POSIX headers

---
 src/buffer.c   | 2 ++
 src/cdf.c      | 2 ++
 src/dprintf.c  | 2 ++
 src/file.c     | 1 -
 src/getline.c  | 2 ++
 src/is_csv.c   | 2 ++
 src/is_json.c  | 2 ++
 src/magic.c    | 2 ++
 src/memtest.c  | 2 ++
 src/mygetopt.h | 2 ++
 src/pread.c    | 2 ++
 src/readcdf.c  | 2 ++
 12 files changed, 22 insertions(+), 1 deletion(-)

diff --git a/src/buffer.c b/src/buffer.c
index 227015a..f3d8efa 100644
--- a/src/buffer.c
+++ b/src/buffer.c
@@ -31,7 +31,9 @@ FILE_RCSID("@(#)$File: buffer.c,v 1.8 2020/02/16 15:52:49 christos Exp $")
 #endif	/* lint */
 
 #include "magic.h"
+#ifdef HAVE_UNISTD_H
 #include <unistd.h>
+#endif
 #include <string.h>
 #include <stdlib.h>
 #include <sys/stat.h>
diff --git a/src/cdf.c b/src/cdf.c
index bb81d63..dc9ec56 100644
--- a/src/cdf.c
+++ b/src/cdf.c
@@ -43,7 +43,9 @@ FILE_RCSID("@(#)$File: cdf.c,v 1.116 2019/08/26 14:31:39 christos Exp $")
 #include <err.h>
 #endif
 #include <stdlib.h>
+#ifdef HAVE_UNISTD_H
 #include <unistd.h>
+#endif
 #include <string.h>
 #include <time.h>
 #include <ctype.h>
diff --git a/src/dprintf.c b/src/dprintf.c
index 027a64f..873c547 100644
--- a/src/dprintf.c
+++ b/src/dprintf.c
@@ -32,7 +32,9 @@ FILE_RCSID("@(#)$File: dprintf.c,v 1.2 2018/09/09 20:33:28 christos Exp $")
 #endif	/* lint */
 
 #include <assert.h>
+#ifdef HAVE_UNISTD_H
 #include <unistd.h>
+#endif
 #include <stdio.h>
 #include <stdarg.h>
 
diff --git a/src/file.c b/src/file.c
index 87b9ab1..2889f8a 100644
--- a/src/file.c
+++ b/src/file.c
@@ -38,7 +38,6 @@ FILE_RCSID("@(#)$File: file.c,v 1.189 2021/02/05 21:33:49 christos Exp $")
 #include "magic.h"
 
 #include <stdlib.h>
-#include <unistd.h>
 #include <string.h>
 #ifdef RESTORE_TIME
 # if (__COHERENT__ >= 0x420)
diff --git a/src/getline.c b/src/getline.c
index b00de01..c2d41ca 100644
--- a/src/getline.c
+++ b/src/getline.c
@@ -33,7 +33,9 @@
 #if !HAVE_GETLINE
 #include <stdlib.h>
 #include <stdio.h>
+#ifdef HAVE_UNISTD_H
 #include <unistd.h>
+#endif
 #include <errno.h>
 #include <string.h>
 
diff --git a/src/is_csv.c b/src/is_csv.c
index 937ab5f..2f704c5 100644
--- a/src/is_csv.c
+++ b/src/is_csv.c
@@ -166,7 +166,9 @@ file_is_csv(struct magic_set *ms, const struct buffer *b, int looks_text)
 #include <sys/stat.h>
 #include <stdio.h>
 #include <fcntl.h>
+#ifdef HAVE_UNISTD_H
 #include <unistd.h>
+#endif
 #include <stdlib.h>
 #include <stdint.h>
 #include <err.h>
diff --git a/src/is_json.c b/src/is_json.c
index 0b12438..997a276 100644
--- a/src/is_json.c
+++ b/src/is_json.c
@@ -436,7 +436,9 @@ file_is_json(struct magic_set *ms, const struct buffer *b)
 #include <sys/stat.h>
 #include <stdio.h>
 #include <fcntl.h>
+#ifdef HAVE_UNISTD_H
 #include <unistd.h>
+#endif
 #include <stdlib.h>
 #include <stdint.h>
 #include <err.h>
diff --git a/src/magic.c b/src/magic.c
index 81a0840..e9aeafa 100644
--- a/src/magic.c
+++ b/src/magic.c
@@ -39,7 +39,9 @@ FILE_RCSID("@(#)$File: magic.c,v 1.114 2021/02/05 21:33:49 christos Exp $")
 #include "magic.h"
 
 #include <stdlib.h>
+#ifdef HAVE_UNISTD_H
 #include <unistd.h>
+#endif
 #include <string.h>
 #ifdef QUICK
 #include <sys/mman.h>
diff --git a/src/memtest.c b/src/memtest.c
index f9506f6..ea50242 100644
--- a/src/memtest.c
+++ b/src/memtest.c
@@ -34,7 +34,9 @@
 #include <stdlib.h>
 #include <err.h>
 #include <fcntl.h>
+#ifdef HAVE_UNISTD_H
 #include <unistd.h>
+#endif
 #include <dlfcn.h>
 #include <magic.h>
 
diff --git a/src/mygetopt.h b/src/mygetopt.h
index d766762..b8e044c 100644
--- a/src/mygetopt.h
+++ b/src/mygetopt.h
@@ -39,7 +39,9 @@
 #ifndef _GETOPT_H_
 #define _GETOPT_H_
 
+#ifdef HAVE_UNISTD_H
 #include <unistd.h>
+#endif
 
 /*
  * Gnu like getopt_long() and BSD4.4 getsubopt()/optreset extensions
diff --git a/src/pread.c b/src/pread.c
index 3ab52d1..fc48d84 100644
--- a/src/pread.c
+++ b/src/pread.c
@@ -3,7 +3,9 @@
 FILE_RCSID("@(#)$File: pread.c,v 1.5 2022/09/24 20:30:13 christos Exp $")
 #endif  /* lint */
 #include <fcntl.h>
+#ifdef HAVE_UNISTD_H
 #include <unistd.h>
+#endif
 
 ssize_t
 pread(int fd, void *buf, size_t len, off_t off) {
diff --git a/src/readcdf.c b/src/readcdf.c
index 7622c7b..977cb75 100644
--- a/src/readcdf.c
+++ b/src/readcdf.c
@@ -31,7 +31,9 @@ FILE_RCSID("@(#)$File: readcdf.c,v 1.74 2019/09/11 15:46:30 christos Exp $")
 
 #include <assert.h>
 #include <stdlib.h>
+#ifdef HAVE_UNISTD_H
 #include <unistd.h>
+#endif
 #include <string.h>
 #include <time.h>
 #include <ctype.h>
-- 
2.29.2.windows.2

