{"name": "lib<PERSON><PERSON><PERSON>", "version": "4.5.0", "port-version": 1, "description": "A modern C++ MIDI real-time & file I/O library", "homepage": "https://github.com/jcelerier/libremidi", "license": "BSD-2-<PERSON><PERSON>", "dependencies": [{"name": "alsa", "platform": "linux"}, {"name": "readerwriter<PERSON>ue", "platform": "linux"}, {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}]}