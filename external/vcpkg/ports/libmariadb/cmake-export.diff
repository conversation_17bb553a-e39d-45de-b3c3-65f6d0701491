diff --git a/libmariadb/CMakeLists.txt b/libmariadb/CMakeLists.txt
index bbad89a..71662d3 100644
--- a/libmariadb/CMakeLists.txt
+++ b/libmariadb/CMakeLists.txt
@@ -547,3 +547,22 @@ IF(0)
            CONFIGURATIONS Debug RelWithDebInfo
            COMPONENT Development)
 ENDIF()
+
+set_target_properties(mariadbclient PROPERTIES EXPORT_NAME libmariadb)
+
+install(EXPORT unofficial-libmariadb-targets
+  NAMESPACE unofficial::
+  DESTINATION share/unofficial-libmariadb
+)
+
+file(WRITE "${CMAKE_CURRENT_BINARY_DIR}/unofficial-libmariadb-config.cmake.in" [[
+if(NOT "@BUILD_SHARED_LIBS@")
+  include(CMakeFindDependencyMacro)
+  if("@WITH_ZSTD@")
+    find_dependency(zstd CONFIG)
+  endif()
+endif()
+include("${CMAKE_CURRENT_LIST_DIR}/unofficial-libmariadb-targets.cmake")
+]])
+configure_file("${CMAKE_CURRENT_BINARY_DIR}/unofficial-libmariadb-config.cmake.in" "${CMAKE_CURRENT_BINARY_DIR}/unofficial-libmariadb-config.cmake" @ONLY)
+install(FILES "${CMAKE_CURRENT_BINARY_DIR}/unofficial-libmariadb-config.cmake" DESTINATION share/unofficial-libmariadb)
