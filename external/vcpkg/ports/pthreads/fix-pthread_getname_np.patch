diff --git a/pthread_getname_np.c b/pthread_getname_np.c
index 8fc32b1..7c18655 100644
--- a/pthread_getname_np.c
+++ b/pthread_getname_np.c
@@ -59,10 +59,18 @@ pthread_getname_np(pthread_t thr, char *name, int len)
 
   __ptw32_mcs_lock_acquire (&tp->threadLock, &threadLock);
 
-  for (s = tp->name, d = name; *s && d < &name[len - 1]; *d++ = *s++)
-    {}
+  if(tp->name)
+    {
+      for (s = tp->name, d = name; *s && d < &name[len - 1]; *d++ = *s++)
+        {}
+
+      *d = '\0';
+    }
+  else
+    {
+      *name = '\0';
+    }
 
-  *d = '\0';
   __ptw32_mcs_lock_release (&threadLock);
 
   return result;
