diff --git a/Makefile b/Makefile
index a703b9c..b7b5ea7 100644
--- a/Makefile
+++ b/Makefile
@@ -80,18 +80,27 @@ help:
 	@ echo nmake clean VSE-static-debug
 
 all:
+!IF DEFINED(BUILD_STATIC)
+!  IF DEFINED(BUILD_RELEASE)
 	$(MAKE) /E clean VC-static
 	$(MAKE) /E clean VCE-static
 	$(MAKE) /E clean VSE-static
+!  ELSE
 	$(MAKE) /E clean VC-static-debug
 	$(MAKE) /E clean VCE-static-debug
 	$(MAKE) /E clean VSE-static-debug
+!  ENDIF
+!ELSE
+!  IF DEFINED(BUILD_RELEASE)
 	$(MAKE) /E clean VC
 	$(MAKE) /E clean VCE
 	$(MAKE) /E clean VSE
+!  ELSE
 	$(MAKE) /E clean VC-debug
 	$(MAKE) /E clean VCE-debug
 	$(MAKE) /E clean VSE-debug
+!  ENDIF
+!ENDIF
 	$(MAKE) /E clean
 
 TEST_ENV = CFLAGS="$(CFLAGS) /DNO_ERROR_DIALOGS"
@@ -218,8 +227,7 @@ install:
 	if not exist $(LIBDEST) mkdir $(LIBDEST)
 	if not exist $(HDRDEST) mkdir $(HDRDEST)
 	if exist pthreadV*.dll copy pthreadV*.dll $(DLLDEST)
-	copy pthreadV*.lib $(LIBDEST)
-	copy libpthreadV*.lib $(LIBDEST)
+	if exist pthreadV*.lib copy pthreadV*.lib $(LIBDEST)
 	copy _ptw32.h $(HDRDEST)
 	copy pthread.h $(HDRDEST)
 	copy sched.h $(HDRDEST)
@@ -229,8 +237,8 @@ $(DLLS): $(DLL_OBJS)
 	$(CC) /LDd /Zi $(DLL_OBJS) /link /implib:$*.lib $(XLIBS) /out:$@
 
 $(INLINED_STATIC_STAMPS): $(STATIC_OBJS)
-	if exist lib$*.lib del lib$*.lib
-	lib $(STATIC_OBJS) /out:lib$*.lib
+	if exist $*.lib del $*.lib
+	lib $(STATIC_OBJS) /out:$*.lib
 	echo. >$@
 
 $(SMALL_STATIC_STAMPS): $(STATIC_OBJS_SMALL)
