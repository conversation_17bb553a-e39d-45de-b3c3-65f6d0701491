/*
 * This file was generated to inject vcpkg feature selections into the installed copy of
 * sqlite so that consumers need not get the values from pkgconfig or CMake configs.
 *
 * No include guard: intentionally reuses the include guard from sqlite3.h.
 */

#ifndef SQLITE_API
#cmakedefine SQLITE_API @SQLITE_API@
#endif

#define SQLITE_ENABLE_UNLOCK_NOTIFY 1
#cmakedefine SQLITE_ENABLE_FTS3
#cmakedefine SQLITE_ENABLE_FTS4
#cmakedefine SQLITE_ENABLE_FTS5
#cmakedefine SQLITE_ENABLE_MEMSYS3
#cmakedefine SQLITE_ENABLE_MEMSYS5
#cmakedefine SQLITE_ENABLE_MATH_FUNCTIONS
#cmakedefine SQLITE_ENABLE_UPDATE_DELETE_LIMIT
#cmakedefine SQLITE_ENABLE_DBPAGE_VTAB
#cmakedefine SQLITE_ENABLE_RTREE
#cmakedefine SQLITE_ENABLE_SESSION
#cmakedefine SQLITE_ENABLE_SNAPSHOT
#cmakedefine SQLITE_ENABLE_PREUPDATE_HOOK
#cmakedefine SQLITE_OMIT_LOAD_EXTENSION
#cmakedefine SQLITE_ENABLE_GEOPOLY
#cmakedefine SQLITE_OMIT_JSON
#cmakedefine SQLITE_OS_WIN @SQLITE_OS_WIN@
#cmakedefine SQLITE_OS_WINRT @SQLITE_OS_WINRT@
#define SQLITE_ENABLE_COLUMN_METADATA 1
#cmakedefine SQLITE_OS_UNIX @SQLITE_OS_UNIX@
#cmakedefine SQLITE_ENABLE_DBSTAT_VTAB
#cmakedefine SQLITE_ENABLE_ICU
