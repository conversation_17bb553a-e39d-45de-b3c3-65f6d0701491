{"name": "vsgxchange", "version": "1.1.4", "port-version": 1, "description": "Utility library for converting 3rd party images, models and fonts formats to/from VulkanSceneGraph.", "homepage": "https://github.com/vsg-dev/vsgXchange", "license": "MIT", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}, "vsg"], "features": {"assimp": {"description": "Enable support for reading 3D model formats as vsg::Node via Assimp", "dependencies": ["assimp"]}, "curl": {"description": "Enable support for reading image and model files from http:// and https://", "dependencies": [{"name": "curl", "default-features": false}]}, "freetype": {"description": "Enable support for reading fonts as vsg::Font via Freetype", "dependencies": [{"name": "freetype", "default-features": false}]}, "gdal": {"description": "Enable support for reading geospatial data formats as vsg::Data via GDAL", "dependencies": [{"name": "gdal", "default-features": false}]}, "openexr": {"description": "Enable support for reading EXR files as vsg::Data", "dependencies": ["openexr"]}}}