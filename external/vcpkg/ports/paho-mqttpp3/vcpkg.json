{"name": "paho-mqttpp3", "version": "1.5.2", "description": "Paho project provides open-source C++ wrapper for Paho C library", "homepage": "https://github.com/eclipse/paho.mqtt.cpp", "license": "EPL-1.0", "dependencies": ["paho-mqtt", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "default-features": ["ssl"], "features": {"ssl": {"description": "Build with SSL support", "dependencies": ["openssl"]}}}