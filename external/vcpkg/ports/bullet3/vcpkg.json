{"name": "bullet3", "version": "3.25", "port-version": 3, "description": "Bullet Physics is a professional collision detection, rigid body, and soft body dynamics library", "homepage": "https://github.com/bulletphysics/bullet3", "license": "<PERSON><PERSON><PERSON>", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"double-precision": {"description": "Use float64 doubles for bullet3"}, "extras": {"description": "Build selected extras", "dependencies": ["tinyxml2"]}, "multithreading": {"description": "Multithreading functionality for bullet3"}, "opencl": {"description": "Build Bullet3OpenCL_clew library", "supports": "!uwp"}, "rtti": {"description": "Enable RTTI on windows"}}}