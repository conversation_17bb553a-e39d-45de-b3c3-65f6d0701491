diff --git a/CMakeLists.txt b/CMakeLists.txt
index 819c36f..847489e 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -52,6 +52,6 @@ if (JSON11_BUILD_TESTS)
   target_link_libraries(json11_test json11)
 endif()
 
-install(TARGETS json11 DESTINATION lib/${CMAKE_LIBRARY_ARCHITECTURE})
-install(FILES "${CMAKE_CURRENT_SOURCE_DIR}/json11.hpp" DESTINATION include/${CMAKE_LIBRARY_ARCHITECTURE})
-install(FILES "${CMAKE_CURRENT_BINARY_DIR}/json11.pc" DESTINATION lib/${CMAKE_LIBRARY_ARCHITECTURE}/pkgconfig)
+install(TARGETS json11 DESTINATION lib)
+install(FILES "${CMAKE_CURRENT_SOURCE_DIR}/json11.hpp" DESTINATION include)
+install(FILES "${CMAKE_CURRENT_BINARY_DIR}/json11.pc" DESTINATION lib/pkgconfig)
