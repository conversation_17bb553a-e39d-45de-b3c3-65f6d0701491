get_filename_component(_DIR "${CMAKE_CURRENT_LIST_FILE}" PATH)

if(NOT JSON11_FIND_COMPONENTS)
    set(JSON11_FIND_COMPONENTS json11 json11)
    if(JSON11_FIND_REQUIRED)
        set(JSON11_FIND_REQUIRED_json11 TRUE)
    endif()

    set(JSON11_FOUND TRUE)
endif()

set(JSON11_INCLUDE_DIRS ${_DIR}/../../include)
set(JSON11_LIBRARIES)
if (EXISTS ${_DIR}/../../lib/libjson11.a)
    list(APPEND JSON11_LIBRARIES optimized ${_DIR}/../../lib/libjson11.a)
endif()
if (EXISTS ${_DIR}/../../debug/lib/libjson11.a)
    list(APPEND JSON11_LIBRARIES debug ${_DIR}/../../debug/lib/libjson11.a)
endif()
if (EXISTS ${_DIR}/../../lib/json11.lib)
    list(APPEND JSON11_LIBRARIES optimized ${_DIR}/../../lib/json11.lib)
endif()
if (EXISTS ${_DIR}/../../debug/lib/json11.lib)
    list(APPEND JSON11_LIBRARIES debug ${_DIR}/../../debug/lib/json11.lib)
endif()
