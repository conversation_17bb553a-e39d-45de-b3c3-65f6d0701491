diff --git a/meson.build b/meson.build
index 1a93dbb95..2f5f7c891 100644
--- a/meson.build	
+++ b/meson.build
@@ -55,7 +55,7 @@ conf = configuration_data()
 common_cflags = []
 common_ldflags = []
 
-if cc.get_id() == 'msvc'
+if cc.get_argument_syntax() == 'msvc'
   # Make MSVC more pedantic, this is a recommended pragma list
   # from _Win32_Programming_ by Rector and Newcomer.  Taken from
   # glib's msvc_recommended_pragmas.h--please see that file for
@@ -117,7 +117,7 @@ endif
 common_cflags = cc.get_supported_arguments(test_cflags)
 
 # MSVC: Let C4819 error out if we do not have the -utf-8 compiler flag
-if cc.get_id() == 'msvc'
+if cc.get_argument_syntax() == 'msvc'
   if not common_cflags.contains('-utf-8')
     common_cflags += cc.get_supported_arguments('-we4819')
   endif
@@ -202,7 +202,7 @@ if get_option('default_library') != 'static'
   if host_system == 'windows'
     conf.set('DLL_EXPORT', true)
     conf.set('_GRAPHENE_PUBLIC', '__declspec(dllexport) extern')
-    if cc.get_id() != 'msvc'
+    if cc.get_argument_syntax() != 'msvc'
       extra_args += ['-fvisibility=hidden']
     endif
   else
@@ -226,7 +226,7 @@ if get_option('gobject_types')
   )
   build_gobject = gobject.found()
   if build_gobject
-    if cc.get_id() == 'msvc'
+    if cc.get_argument_syntax() == 'msvc'
       extra_args += ['/FImsvc_recommended_pragmas.h']
     endif
   endif
