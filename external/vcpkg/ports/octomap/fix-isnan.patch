diff --git a/octomap/src/compare_octrees.cpp b/octomap/src/compare_octrees.cpp
index c02ceea8..e6890f59 100644
--- a/octomap/src/compare_octrees.cpp
+++ b/octomap/src/compare_octrees.cpp
@@ -39,7 +39,7 @@
 #include <list>
 #include <cmath>
 
-#ifdef _MSC_VER // fix missing isnan for VC++
+#if defined(_MSC_VER) && !((defined(_MSVC_LANG) && _MSVC_LANG >= 201103L) || __cplusplus >= 201103L)
 #define isnan(x) _isnan(x)  
 #endif
 
@@ -132,11 +132,7 @@ int main(int argc, char** argv) {
       else
         kld +=log(p1/p2)*p1 + log((1-p1)/(1-p2))*(1-p1);
 
-#if ((defined(_MSVC_LANG) && _MSVC_LANG >= 201103L) || __cplusplus >= 201103L)
-      if (std::isnan(kld)){
-#else
       if (isnan(kld)){
-#endif
         OCTOMAP_ERROR("KLD is nan! KLD(%f,%f)=%f; sum = %f", p1, p2, kld, kld_sum);
         exit(-1);
       }
