{"name": "discord-game-sdk", "version": "3.2.1", "description": "The Discord GameSDK is an easy drop-in SDK to help you manage all the hard things that come with making a game.", "homepage": "https://discord.com/developers/docs/game-sdk/sdk-starter-guide", "supports": "((x64 & (windows | osx | linux)) | (x86 & windows) | (arm64 & osx)) & !uwp & !static", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}]}