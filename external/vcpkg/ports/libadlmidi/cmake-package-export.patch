diff --git a/CMakeLists.txt b/CMakeLists.txt
index 271bb9b..1b6ba8f 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -313,7 +313,7 @@ if(libADLMIDI_STATIC OR WITH_VLC_PLUGIN)
     else()
         set_target_properties(ADLMIDI_static PROPERTIES OUTPUT_NAME ADLMIDI)
     endif()
-    target_include_directories(ADLMIDI_static PUBLIC ${libADLMIDI_SOURCE_DIR}/include)
+    target_include_directories(ADLMIDI_static PUBLIC $<BUILD_INTERFACE:${libADLMIDI_SOURCE_DIR}/include> $<INSTALL_INTERFACE:include>)
     set_legacy_standard(ADLMIDI_static)
     set_visibility_hidden(ADLMIDI_static)
     handle_options(ADLMIDI_static)
@@ -336,7 +336,7 @@ if(libADLMIDI_SHARED)
         VERSION ${libADLMIDI_VERSION}
         SOVERSION ${libADLMIDI_VERSION_MAJOR}
     )
-    target_include_directories(ADLMIDI_shared PUBLIC ${libADLMIDI_SOURCE_DIR}/include)
+    target_include_directories(ADLMIDI_shared PUBLIC $<BUILD_INTERFACE:${libADLMIDI_SOURCE_DIR}/include> $<INSTALL_INTERFACE:include>)
     set_legacy_standard(ADLMIDI_shared)
     set_visibility_hidden(ADLMIDI_shared)
     handle_options(ADLMIDI_shared)
@@ -418,22 +418,44 @@ if(WIN32 AND WITH_WINMMDRV)
     add_subdirectory(utils/winmm_drv)
 endif()
 
-set(libADLMIDI_INSTALLS )
-foreach(lib ADLMIDI_static ADLMIDI_shared)
-    if(TARGET ${lib})
-        list(APPEND libADLMIDI_INSTALLS ${lib})
-    endif()
-endforeach()
+if(libADLMIDI_STATIC)
+    install(TARGETS ADLMIDI_static
+            EXPORT libADLMIDIStaticTargets
+            RUNTIME DESTINATION "${CMAKE_INSTALL_BINDIR}"
+            LIBRARY DESTINATION "${CMAKE_INSTALL_LIBDIR}"
+            ARCHIVE DESTINATION "${CMAKE_INSTALL_LIBDIR}")
+
+    install(EXPORT libADLMIDIStaticTargets
+            FILE libADLMIDI-static-targets.cmake
+            NAMESPACE libADLMIDI::
+            DESTINATION "${CMAKE_INSTALL_LIBDIR}/cmake/libADLMIDI")
+endif()
+
+if(libADLMIDI_SHARED)
+    install(TARGETS ADLMIDI_shared
+            EXPORT libADLMIDISharedTargets
+            RUNTIME DESTINATION "${CMAKE_INSTALL_BINDIR}"
+            LIBRARY DESTINATION "${CMAKE_INSTALL_LIBDIR}"
+            ARCHIVE DESTINATION "${CMAKE_INSTALL_LIBDIR}")
 
-install(TARGETS ${libADLMIDI_INSTALLS}
-        RUNTIME DESTINATION "${CMAKE_INSTALL_BINDIR}"
-        LIBRARY DESTINATION "${CMAKE_INSTALL_LIBDIR}"
-        ARCHIVE DESTINATION "${CMAKE_INSTALL_LIBDIR}"
-        INCLUDES DESTINATION "${CMAKE_INSTALL_INCLUDEDIR}")
+    install(EXPORT libADLMIDISharedTargets
+            FILE libADLMIDI-shared-targets.cmake
+            NAMESPACE libADLMIDI::
+            DESTINATION "${CMAKE_INSTALL_LIBDIR}/cmake/libADLMIDI")
+endif()
 
 install(FILES
         include/adlmidi.h
         DESTINATION "${CMAKE_INSTALL_INCLUDEDIR}")
+        
+include(CMakePackageConfigHelpers)
+configure_package_config_file(libADLMIDIConfig.cmake.in "${CMAKE_CURRENT_BINARY_DIR}/libADLMIDIConfig.cmake"
+    PATH_VARS CMAKE_INSTALL_PREFIX CMAKE_INSTALL_FULL_BINDIR CMAKE_INSTALL_FULL_INCLUDEDIR CMAKE_INSTALL_FULL_LIBDIR
+    INSTALL_DESTINATION "${CMAKE_INSTALL_LIBDIR}/cmake/libADLMIDI"
+)
+install(FILES ${CMAKE_CURRENT_BINARY_DIR}/libADLMIDIConfig.cmake
+    DESTINATION "${CMAKE_INSTALL_LIBDIR}/cmake/libADLMIDI"
+)
 
 file(GLOB DOCTXT_FILES
     "${libADLMIDI_SOURCE_DIR}/LICENSE*.txt"
diff --git a/libADLMIDIConfig.cmake.in b/libADLMIDIConfig.cmake.in
new file mode 100644
index 0000000..bd875fc
--- /dev/null
+++ b/libADLMIDIConfig.cmake.in
@@ -0,0 +1,30 @@
+include(FeatureSummary)
+set_package_properties(libADLMIDI PROPERTIES
+  URL "https://github.com/Wohlstand/libADLMIDI"
+  DESCRIPTION "libADLMIDI is a free Software MIDI synthesizer library with OPL3 emulation"
+)
+
+@PACKAGE_INIT@
+
+if(EXISTS "${CMAKE_CURRENT_LIST_DIR}/libADLMIDI-shared-targets.cmake")
+    include("${CMAKE_CURRENT_LIST_DIR}/libADLMIDI-shared-targets.cmake")
+endif()
+if(EXISTS "${CMAKE_CURRENT_LIST_DIR}/libADLMIDI-static-targets.cmake")
+    include("${CMAKE_CURRENT_LIST_DIR}/libADLMIDI-static-targets.cmake")
+endif()
+
+if(TARGET libADLMIDI::ADLMIDI_shared)
+    if(CMAKE_VERSION VERSION_LESS "3.18")
+        add_library(libADLMIDI::ADLMIDI INTERFACE IMPORTED)
+        set_target_properties(libADLMIDI::ADLMIDI PROPERTIES INTERFACE_LINK_LIBRARIES "libADLMIDI::ADLMIDI_shared")
+    else()
+        add_library(libADLMIDI::ADLMIDI ALIAS libADLMIDI::ADLMIDI_shared)
+    endif()
+else()
+    if(CMAKE_VERSION VERSION_LESS "3.18")
+        add_library(libADLMIDI::ADLMIDI INTERFACE IMPORTED)
+        set_target_properties(libADLMIDI::ADLMIDI PROPERTIES INTERFACE_LINK_LIBRARIES "libADLMIDI::ADLMIDI_static")
+    else()
+        add_library(libADLMIDI::ADLMIDI ALIAS libADLMIDI::ADLMIDI_static)
+    endif()
+endif()
