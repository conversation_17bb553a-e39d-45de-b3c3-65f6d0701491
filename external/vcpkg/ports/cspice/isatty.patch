--- a/cspice/src/cspice/fio.h	2019-11-01 20:51:53.198400000 +0300
+++ b/cspice/src/cspice/fio.h	2019-11-01 21:19:58.123200000 +0300
@@ -1,3 +1,6 @@
+#ifdef _WIN32
+#include <io.h> /* for isatty() */
+#endif
 #include "stdio.h"
 #include "errno.h"
 #ifndef NULL
@@ -75,7 +76,9 @@
 extern int (*f__donewrec)(void), t_putc(int), x_wSL(void);
 extern void b_char(char*,char*,ftnlen), g_char(char*,ftnlen,char*);
 extern int c_sfe(cilist*), z_rnew(void);
+#ifndef _WIN32
 extern int isatty(int);
+#endif
 extern int err__fl(int,int,char*);
 extern int xrd_SL(void);
 extern int f__putbuf(int);
