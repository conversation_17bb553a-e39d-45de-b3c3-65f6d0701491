diff --git a/cmake/FindPCRE.cmake b/cmake/FindPCRE.cmake
index dbbd60a..68042fe 100644
--- a/cmake/FindPCRE.cmake
+++ b/cmake/FindPCRE.cmake
@@ -19,7 +19,11 @@
 FIND_PATH(PCRE_INCLUDE_DIR NAMES pcre.h)
 
 # Look for the library.
-FIND_LIBRARY(PCRE_LIBRARY NAMES pcre)
+SET(pcre_DEBUG_NAME "")
+IF(CMAKE_BUILD_TYPE MATCHES "Debug")
+    SET(pcre_DEBUG_NAME pcred)
+ENDIF()
+FIND_LIBRARY(PCRE_LIBRARY NAMES ${pcre_DEBUG_NAME} pcre)
 
 # Handle the QUIETLY and REQUIRED arguments and set PCRE_FOUND to TRUE if all listed variables are TRUE.
 INCLUDE(FindPackageHandleStandardArgs)
