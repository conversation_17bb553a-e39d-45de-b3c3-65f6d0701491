diff --git a/src/src.pro b/src/src.pro
index 0ce903fe7..16524b8e2 100644
--- a/src/src.pro
+++ b/src/src.pro
@@ -20,6 +20,7 @@ TEMPLATE          = lib
 TARGET            = $$qwtLibraryTarget(qwt)
 
 DESTDIR           = $${QWT_OUT_ROOT}/lib
+DLLDESTDIR        = $${QWT_OUT_ROOT}/bin
 
 contains(QWT_CONFIG, QwtDll) {
 
@@ -49,9 +50,13 @@ contains(QWT_CONFIG, QwtFramework) {
 include ( $${PWD}/src.pri )
 
 # Install directives
-
+win32 {
+    dlltarget.path = $${QWT_INSTALL_BINS}
+    INSTALLS += dlltarget
+}
 target.path    = $${QWT_INSTALL_LIBS}
-INSTALLS       = target 
+!static: target.CONFIG = no_dll
+INSTALLS     += target 
 
 CONFIG(lib_bundle) {
 
