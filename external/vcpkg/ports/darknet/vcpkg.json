{"name": "darknet", "version-date": "2024-10-10", "description": "Darknet is an open source neural network framework written in C and CUDA. You only look once (YOLO) is a state-of-the-art, real-time object detection system, best example of darknet functionalities.", "homepage": "https://github.com/alexeyab/darknet", "license": null, "dependencies": [{"name": "getopt", "platform": "windows & !mingw"}, "pthreads", "stb", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"cuda": {"description": "Build darknet with support for CUDA", "dependencies": ["cuda"]}, "cudnn": {"description": "Build darknet with support for cuDNN", "dependencies": ["cuda", "cudnn"]}, "full": {"description": "Build darknet fully featured", "dependencies": [{"name": "darknet", "features": ["cuda", "cudnn", "opencv-cuda"]}]}, "opencv-base": {"description": "Build darknet with support for latest version of OpenCV", "dependencies": [{"name": "opencv", "features": ["ffmpeg"]}]}, "opencv-cuda": {"description": "Build darknet with support for latest version of CUDA-enabled OpenCV", "dependencies": [{"name": "darknet", "features": ["cuda"]}, {"name": "opencv", "features": ["contrib", "cuda", "dnn", "ffmpeg"]}]}}}