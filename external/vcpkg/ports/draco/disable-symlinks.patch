diff --git a/cmake/draco_targets.cmake b/cmake/draco_targets.cmake
index c8c79f5..1cec5b8 100644
--- a/cmake/draco_targets.cmake
+++ b/cmake/draco_targets.cmake
@@ -111,9 +111,6 @@ macro(draco_add_executable)
 
     target_compile_features(${exe_NAME} PUBLIC cxx_std_11)
 
-  if(NOT EMSCRIPTEN)
-    set_target_properties(${exe_NAME} PROPERTIES VERSION ${DRACO_VERSION})
-  endif()
 
   if(exe_OUTPUT_NAME)
     set_target_properties(${exe_NAME} PROPERTIES OUTPUT_NAME ${exe_OUTPUT_NAME})
@@ -364,14 +361,6 @@ macro(draco_add_library)
     set_target_properties(${lib_NAME} PROPERTIES PREFIX "")
   endif()
 
-  if(NOT EMSCRIPTEN)
-    # VERSION and SOVERSION as necessary
-    if((lib_TYPE STREQUAL BUNDLE OR lib_TYPE STREQUAL SHARED) AND NOT MSVC)
-      set_target_properties(
-        ${lib_NAME} PROPERTIES VERSION ${DRACO_SOVERSION}
-                               SOVERSION ${DRACO_SOVERSION_MAJOR})
-    endif()
-  endif()
 
   if(BUILD_SHARED_LIBS AND (MSVC OR WIN32))
     if(lib_TYPE STREQUAL SHARED)
