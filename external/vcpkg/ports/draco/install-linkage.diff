diff --git a/cmake/draco_install.cmake b/cmake/draco_install.cmake
index 3be1ba1..b91938c 100644
--- a/cmake/draco_install.cmake
+++ b/cmake/draco_install.cmake
@@ -65,10 +65,14 @@ macro(draco_setup_install_target)
       ARCHIVE DESTINATION "${libs_path}"
       LIBRARY DESTINATION "${libs_path}")
   else()
+    if(BUILD_SHARED_LIBS)
+      set_target_properties(draco_static PROPERTIES EXCLUDE_FROM_ALL 1)
+    else()
     install(
       TARGETS draco_static
       EXPORT dracoExport
       DESTINATION "${libs_path}")
+    endif()
 
     if(BUILD_SHARED_LIBS)
       install(
