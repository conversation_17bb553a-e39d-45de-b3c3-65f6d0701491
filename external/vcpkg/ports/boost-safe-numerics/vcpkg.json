{"$comment": "Automatically generated by scripts/boost/generate-ports.ps1", "name": "boost-safe-numerics", "version": "1.87.0", "description": "Boost safe_numerics module", "homepage": "https://www.boost.org/libs/safe_numerics", "license": "BSL-1.0", "dependencies": [{"name": "boost-cmake", "version>=": "1.87.0"}, {"name": "boost-concept-check", "version>=": "1.87.0"}, {"name": "boost-config", "version>=": "1.87.0"}, {"name": "boost-core", "version>=": "1.87.0"}, {"name": "boost-headers", "version>=": "1.87.0"}, {"name": "boost-integer", "version>=": "1.87.0"}, {"name": "boost-logic", "version>=": "1.87.0"}, {"name": "boost-mp11", "version>=": "1.87.0"}]}