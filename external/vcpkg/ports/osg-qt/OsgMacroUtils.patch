--- a/CMakeModules/OsgMacroUtils.cmake
+++ b/CMakeModules/OsgMacroUtils.cmake
@@ -92,7 +92,7 @@ MACRO(LINK_CORELIB_DEFAULT CORELIB_NAME)
     ENDIF()

     LINK_EXTERNAL(${CORELIB_NAME} ${ALL_GL_LIBRARIES})
-    LINK_WITH_VARIABLES(${CORELIB_NAME} OPENTHREADS_LIBRARY)
+    #LINK_WITH_VARIABLES(${CORELIB_NAME} OPENTHREADS_LIBRARY)
     IF(OPENSCENEGRAPH_SONAMES)
       SET_TARGET_PROPERTIES(${CORELIB_NAME} PROPERTIES VERSION ${OPENSCENEGRAPH_VERSION} SOVERSION ${OPENSCENEGRAPH_SOVERSION})
     ENDIF(OPENSCENEGRAPH_SONAMES)
