diff --git a/CMakeModules/ModuleInstall.cmake b/CMakeModules/ModuleInstall.cmake
index eb26ba2..14b8bca 100644
--- a/CMakeModules/ModuleInstall.cmake
+++ b/CMakeModules/ModuleInstall.cmake
@@ -40,10 +40,10 @@
     ARCHIVE DESTINATION ${INSTALL_ARCHIVEDIR} COMPONENT libopenscenegraph-dev    
 )

-IF(MSVC AND NOT CMAKE_BUILD_TYPE STREQUAL "Release")
+IF(MSVC AND NOT CMAKE_BUILD_TYPE STREQUAL "Release" AND DYNAMIC_OPENSCENEGRAPH)
     GET_TARGET_PROPERTY(PREFIX ${LIB_NAME} PREFIX)
     INSTALL(FILES ${CMAKE_CURRENT_BINARY_DIR}/${PREFIX}${LIB_NAME}${CMAKE_BUILD_POSTFIX}.pdb DESTINATION ${INSTALL_BINDIR} COMPONENT libopenscenegraph)
-ENDIF(MSVC AND NOT CMAKE_BUILD_TYPE STREQUAL "Release")
+ENDIF(MSVC AND NOT CMAKE_BUILD_TYPE STREQUAL "Release" AND DYNAMIC_OPENSCENEGRAPH)

 IF(NOT OSG_COMPILE_FRAMEWORKS)
     INSTALL (
diff --git a/CMakeModules/OsgMacroUtils.cmake b/CMakeModules/OsgMacroUtils.cmake
index 5688f8f..329f862 100644
--- a/CMakeModules/OsgMacroUtils.cmake
+++ b/CMakeModules/OsgMacroUtils.cmake
@@ -333,9 +333,9 @@ MACRO(SETUP_PLUGIN PLUGIN_NAME)
             RUNTIME DESTINATION bin COMPONENT ${PACKAGE_COMPONENT}
             ARCHIVE DESTINATION lib/${OSG_PLUGINS} COMPONENT libopenscenegraph-dev
             LIBRARY DESTINATION bin/${OSG_PLUGINS} COMPONENT ${PACKAGE_COMPONENT})
-        IF(MSVC AND NOT CMAKE_BUILD_TYPE STREQUAL "Release")
+        IF(MSVC AND NOT CMAKE_BUILD_TYPE STREQUAL "Release" AND DYNAMIC_OPENSCENEGRAPH)
             INSTALL(FILES ${CMAKE_CURRENT_BINARY_DIR}/${TARGET_TARGETNAME}${CMAKE_BUILD_POSTFIX}.pdb DESTINATION bin/${OSG_PLUGINS} COMPONENT ${PACKAGE_COMPONENT})
-        ENDIF(MSVC AND NOT CMAKE_BUILD_TYPE STREQUAL "Release")
+        ENDIF(MSVC AND NOT CMAKE_BUILD_TYPE STREQUAL "Release" AND DYNAMIC_OPENSCENEGRAPH)
     ELSE(WIN32)
         INSTALL(TARGETS ${TARGET_TARGETNAME}
             RUNTIME DESTINATION bin COMPONENT ${PACKAGE_COMPONENT}
@@ -436,9 +436,9 @@ MACRO(SETUP_APPLICATION APPLICATION_NAME)
             INSTALL(TARGETS ${TARGET_TARGETNAME} RUNTIME DESTINATION bin BUNDLE DESTINATION bin)
         ELSE(APPLE)
             INSTALL(TARGETS ${TARGET_TARGETNAME} RUNTIME DESTINATION bin COMPONENT openscenegraph  )
-            IF(MSVC AND NOT CMAKE_BUILD_TYPE STREQUAL "Release")
+            IF(MSVC AND NOT CMAKE_BUILD_TYPE STREQUAL "Release" AND DYNAMIC_OPENSCENEGRAPH)
                 INSTALL(FILES ${CMAKE_BINARY_DIR}/bin/${TARGET_NAME}${CMAKE_BUILD_POSTFIX}.pdb DESTINATION bin COMPONENT openscenegraph)
-            ENDIF(MSVC AND NOT CMAKE_BUILD_TYPE STREQUAL "Release")
+            ENDIF(MSVC AND NOT CMAKE_BUILD_TYPE STREQUAL "Release" AND DYNAMIC_OPENSCENEGRAPH)
         ENDIF(APPLE)

 ENDMACRO(SETUP_APPLICATION)
@@ -468,9 +468,9 @@ MACRO(SETUP_EXAMPLE EXAMPLE_NAME)
             INSTALL(TARGETS ${TARGET_TARGETNAME} RUNTIME DESTINATION share/OpenSceneGraph/bin BUNDLE DESTINATION share/OpenSceneGraph/bin )
         ELSE(APPLE)
             INSTALL(TARGETS ${TARGET_TARGETNAME} RUNTIME DESTINATION share/OpenSceneGraph/bin COMPONENT openscenegraph-examples )
-            IF(MSVC AND NOT CMAKE_BUILD_TYPE STREQUAL "Release")
+            IF(MSVC AND NOT CMAKE_BUILD_TYPE STREQUAL "Release" AND DYNAMIC_OPENSCENEGRAPH)
                 INSTALL(FILES ${CMAKE_CURRENT_BINARY_DIR}/${TARGET_TARGETNAME}${CMAKE_BUILD_POSTFIX}.pdb DESTINATION share/OpenSceneGraph/bin COMPONENT openscenegraph-examples)
-            ENDIF(MSVC AND NOT CMAKE_BUILD_TYPE STREQUAL "Release")
+            ENDIF(MSVC AND NOT CMAKE_BUILD_TYPE STREQUAL "Release" AND DYNAMIC_OPENSCENEGRAPH)
         ENDIF(APPLE)

 ENDMACRO(SETUP_EXAMPLE)
