diff --git a/CMakeLists.txt b/CMakeLists.txt
index 66e989a..3569cec 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -467,7 +467,7 @@ ENDIF()
 
 IF(UNIX AND NOT WIN32 AND NOT APPLE)
   IF(CMAKE_SIZEOF_VOID_P MATCHES "8")
-      SET(LIB_POSTFIX "64" CACHE STRING "suffix for 32/64 dir placement")
+      SET(LIB_POSTFIX "" CACHE STRING "suffix for 32/64 dir placement")
       MARK_AS_ADVANCED(LIB_POSTFIX)
   ENDIF()
 ENDIF()
