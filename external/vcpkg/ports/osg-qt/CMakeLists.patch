diff --git a/src/osgQOpenGL/CMakeLists.txt b/src/osgQOpenGL/CMakeLists.txt
--- a/src/osgQOpenGL/CMakeLists.txt
+++ b/src/osgQOpenGL/CMakeLists.txt
@@ -14,7 +14,11 @@
         ${HEADER_PATH}/<PERSON><PERSON><PERSON>er
     )
 
-    qt5_wrap_cpp(SOURCES_H_MOC ${SOURCE_H} #[[OPTIONS ${MOC_OPTIONS}]])
+    FOREACH(HEADER_FILE ${SOURCE_H})
+        get_filename_component(HEADER_FILE_NAME "${CMAKE_CURRENT_LIST_DIR}/${HEADER_FILE}" NAME)
+        qt5_wrap_cpp(HEADER_FILE_MOC ${HEADER_FILE} OPTIONS "-f<osgQOpenGL/${HEADER_FILE_NAME}>")
+        LIST(APPEND SOURCES_H_MOC ${HEADER_FILE_MOC})
+    END<PERSON>OREACH()
 
     SET(TARGET_H
         ${SOURCE_H}
