--- a/sharedlib-compilation/sharedlib-compilation.pro
+++ b/sharedlib-compilation/sharedlib-compilation.pro
@@ -10,7 +10,6 @@
 
 DEFINES += QCUSTOMPLOT_COMPILE_LIBRARY
 TEMPLATE = lib
-CONFIG += debug_and_release build_all
 static {
   CONFIG += static
 } else {
@@ -33,3 +32,14 @@
 
 SOURCES += ../../qcustomplot.cpp
 HEADERS += ../../qcustomplot.h
+win32 {
+    dlltarget.path = $$[QT_INSTALL_BINS]
+    INSTALLS += dlltarget
+}
+target.path    = $$[QT_INSTALL_LIBS]
+!static: target.CONFIG = no_dll
+INSTALLS     += target
+
+headers.files += ../../qcustomplot.h
+headers.path = $$[QT_INSTALL_PREFIX]/include
+INSTALLS     += headers
