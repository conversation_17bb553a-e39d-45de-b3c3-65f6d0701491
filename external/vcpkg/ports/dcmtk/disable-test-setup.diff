diff --git a/CMake/dcmtkUseWine.cmake b/CMake/dcmtkUseWine.cmake
index 6dbd7dc..cf529e2 100644
--- a/CMake/dcmtkUseWine.cmake
+++ b/CMake/dcmtkUseWine.cmake
@@ -1,3 +1,14 @@
+if(CMAKE_HOST_WIN32)
+    function(DCMTK_SETUP_WINE)
+    endfunction()
+    function(WINE_COMMAND)
+        message(FATAL_ERROR "Not implemented")
+    endfunction()
+    function(WINE_DETACHED)
+        message(FATAL_ERROR "Not implemented")
+    endfunction()
+    return()
+endif()
 #
 # Functions for detection and usage of Wine
 # Used when cross compiling
