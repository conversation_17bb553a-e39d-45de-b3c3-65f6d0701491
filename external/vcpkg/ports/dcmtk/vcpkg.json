{"name": "dcmtk", "version": "3.6.9", "port-version": 1, "description": "This DICOM ToolKit (DCMTK) package consists of source code, documentation and installation instructions for a set of software libraries and applications implementing part of the DICOM/MEDICOM Standard.", "homepage": "https://github.com/DCMTK/dcmtk", "license": null, "supports": "!uwp", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}, {"name": "vcpkg-cmake-get-vars", "host": true}], "features": {"external-dict": {"description": "Enable external dictionary"}, "iconv": {"description": "Enable Iconv support", "dependencies": ["libiconv"]}, "openssl": {"description": "Enable OpenSSL", "dependencies": ["openssl"]}, "png": {"description": "Enable PNG support", "dependencies": ["libpng"]}, "tiff": {"description": "Enable TIFF support", "dependencies": [{"name": "tiff", "default-features": false}]}, "tools": {"description": "Build apps"}, "xml2": {"description": "Enable XML support", "dependencies": ["libxml2"]}, "zlib": {"description": "Enable zlib support", "dependencies": ["zlib"]}}}