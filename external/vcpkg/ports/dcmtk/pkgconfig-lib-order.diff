diff --git a/CMakeLists.txt b/CMakeLists.txt
index 284d40e..8f8acfd 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -158,7 +158,12 @@ endif()
 
 if(DCMTK_WITH_ICONV)
   # libiconv does not provide a .pc file
-  set(PKGCONF_LIBS_PRIV "${PKGCONF_LIBS_PRIV} ${Iconv_LIBRARY} ${LIBCHARSET_LIBRARY}")
+  if(Iconv_LIBRARIES)
+    string(APPEND PKGCONF_LIBS_PRIV " -liconv")
+  endif()
+  if(CHARSET_LIBRARIES)
+    string(APPEND PKGCONF_LIBS_PRIV " -lcharset")
+  endif()
 endif()
 
 if(DCMTK_WITH_ICU)
@@ -200,6 +205,41 @@ endif()
 
 get_property(DCMTK_LIBRARY_TARGETS GLOBAL PROPERTY DCMTK_LIBRARY_TARGETS)
 
+foreach(lib IN LISTS WIN32_STD_LIBRARIES)
+  string(APPEND PKGCONF_LIBS_PRIV " -l${lib}")
+endforeach()
+configure_file("${DCMTK_SOURCE_DIR}/CMake/dcmtk.pc.in" "${DCMTK_BINARY_DIR}/dcmtk-deps.pc" @ONLY)
+install(FILES "${DCMTK_BINARY_DIR}/dcmtk-deps.pc" DESTINATION ${CMAKE_INSTALL_LIBDIR}/pkgconfig)
+set(PKGCONF_LIBS "")
+set(PKGCONF_LIBS_PRIV "")
+set(PKGCONF_REQ_PUB dcmtk-deps)
+set(PKGCONF_REQ_PRIV "")
+
+foreach(lib IN ITEMS
+    dcmfg       # for dcmseg, dcmpmap, dcmect
+    dcmiod      # for dcmpstat, dcmfg, dcmseg
+    dcmdsig     # for dcmpstat
+    dcmtls      # for dcmpstat
+    dcmqrdb     # for dcmpstat
+    dcmsr       # for cmr
+    dcmnet      # for dcmtls, dcmwlm, dcmqrdb
+    dcmtkcharls # for dcmjpls
+    ijg8     # for dcmjpeg
+    ijg12    # fpr dcmjpeg
+    ijg16    # for dcmjpeg
+    dcmimage # for dcmjpeg etc.
+    dcmimgle # for dcmimage etc.
+    dcmxml   # for i2d
+    dcmdata # fpr dcmxml, dcmimgle, etc.
+    oflog   # for most libs
+    ofstd   # for oflog
+    oficonv # for ofstd
+  )
+  if(lib IN_LIST DCMTK_LIBRARY_TARGETS)
+    list(REMOVE_ITEM DCMTK_LIBRARY_TARGETS ${lib})
+    list(APPEND DCMTK_LIBRARY_TARGETS ${lib})
+  endif()
+endforeach()
 foreach(T ${DCMTK_LIBRARY_TARGETS})
   set(PKGCONF_LIBS "${PKGCONF_LIBS} -l${T}")
 endforeach()
