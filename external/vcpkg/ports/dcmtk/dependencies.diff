diff --git a/CMake/3rdparty.cmake b/CMake/3rdparty.cmake
index 510027c..eb8fe90 100644
--- a/CMake/3rdparty.cmake
+++ b/CMake/3rdparty.cmake
@@ -1,3 +1,10 @@
+set(CMAKE_REQUIRE_FIND_PACKAGE_Iconv "${DCMTK_WITH_ICONV}")
+set(CMAKE_REQUIRE_FIND_PACKAGE_LibXml2 "${DCMTK_WITH_XML}")
+set(CMAKE_REQUIRE_FIND_PACKAGE_OpenSSL "${DCMTK_WITH_OPENSSL}")
+set(CMAKE_REQUIRE_FIND_PACKAGE_PNG "${DCMTK_WITH_PNG}")
+set(CMAKE_REQUIRE_FIND_PACKAGE_TIFF "${DCMTK_WITH_TIFF}")
+set(CMAKE_REQUIRE_FIND_PACKAGE_ZLIB "${DCMTK_WITH_ZLIB}")
+
 set(USE_FIND_PACKAGE_DOCS "Control whether libraries are searched via CM<PERSON>'s find_package() mechanism or a Windows specific fallback")
 # Advanced user (eg. vcpkg) may want to override this:
 if(NOT DEFINED DCMTK_USE_FIND_PACKAGE_WIN_DEFAULT)
@@ -42,7 +49,7 @@ if(DCMTK_USE_FIND_PACKAGE)
       else()
         message(STATUS "Info: DCMTK TIFF support will be enabled")
         include_directories(${TIFF_INCLUDE_DIR} ${JPEG_INCLUDE_DIR})
-        set(LIBTIFF_LIBS ${TIFF_LIBRARIES} ${TIFF_EXTRA_LIBS_STATIC} ${JPEG_LIBRARIES})
+        set(LIBTIFF_LIBS ${TIFF_LIBRARIES})
       endif()
     endif()
   endif()
@@ -58,7 +65,7 @@ if(DCMTK_USE_FIND_PACKAGE)
       message(STATUS "Info: DCMTK PNG support will be enabled")
       set(WITH_LIBPNG 1)
       include_directories(${PNG_INCLUDE_DIR})
-      set(LIBPNG_LIBS ${PNG_LIBRARY})
+      set(LIBPNG_LIBS ${PNG_LIBRARIES})
     endif()
   endif()
 
@@ -103,7 +110,7 @@ if(DCMTK_USE_FIND_PACKAGE)
     else()
       message(STATUS "Info: DCMTK XML support will be enabled")
       set(WITH_LIBXML 1)
-      include_directories(${LIBXML2_INCLUDE_DIR})
+      include_directories(${LIBXML2_INCLUDE_DIRS})
       set(LIBXML_LIBS ${LIBXML2_LIBRARIES} ${LIBXML2_EXTRA_LIBS_STATIC})
     endif()
   endif()
@@ -141,7 +148,10 @@ if(DCMTK_USE_FIND_PACKAGE)
   # Find libiconv
   if(DCMTK_WITH_ICONV)
     find_package(Iconv QUIET)
-    find_package(LIBCHARSET QUIET)
+    set(ICONV_FOUND 1)
+    if(CHARSET_LIBRARIES)
+      set(LIBCHARSET_FOUND 1)
+    endif()
     if(ICONV_FOUND)
         if(NOT Iconv_IS_BUILT_IN)
             set(LIBICONV_FOUND ${ICONV_FOUND})
