diff --git a/CMake/GenerateDCMTKConfigure.cmake b/CMake/GenerateDCMTKConfigure.cmake
index 8a59d44fa..8d3b4fc60 100644
--- a/CMake/GenerateDCMTKConfigure.cmake
+++ b/CMake/GenerateDCMTKConfigure.cmake
@@ -173,19 +173,11 @@ if(WIN32 AND NOT CYGWIN)
 
   # Set dictionary path to the data dir inside install main dir (prefix)
   if(DCMTK_DEFAULT_DICT STREQUAL "external")
-    if(DCMTK_USE_WIN32_PROGRAMDATA)
-      set(DCM_DICT_DEFAULT_PATH "${CMAKE_INSTALL_FULL_DATADIR}\\\\dicom.dic")
-    else()
-      set(DCM_DICT_DEFAULT_PATH "${DCMTK_PREFIX}\\\\${CMAKE_INSTALL_DATADIR}\\\\dcmtk\\\\dicom.dic")
-    endif()
+    set(DCM_DICT_DEFAULT_PATH "${CMAKE_INSTALL_FULL_DATADIR}\\\\dicom.dic")
 
     # If private dictionary should be utilized, add it to default dictionary path.
     if(ENABLE_PRIVATE_TAGS)
-      if(DCMTK_USE_WIN32_PROGRAMDATA)
-        set(DCM_DICT_DEFAULT_PATH "${DCM_DICT_DEFAULT_PATH};${CMAKE_INSTALL_FULL_DATADIR}\\\\private.dic")
-      else()
-        set(DCM_DICT_DEFAULT_PATH "${DCM_DICT_DEFAULT_PATH};${DCMTK_PREFIX}\\\\${CMAKE_INSTALL_DATADIR}\\\\dcmtk\\\\private.dic")
-      endif()
+      set(DCM_DICT_DEFAULT_PATH "${DCM_DICT_DEFAULT_PATH};${CMAKE_INSTALL_FULL_DATADIR}\\\\private.dic")
     endif()
 
      # Again, for Windows strip all / from path and replace it with \\.
