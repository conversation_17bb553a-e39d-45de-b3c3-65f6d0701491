{"name": "rtabmap", "version": "********", "port-version": 6, "description": "Real-Time Appearance-Based Mapping", "homepage": "https://introlab.github.io/rtabmap/", "license": null, "dependencies": ["ceres", "g2o", {"name": "opencv", "default-features": false, "features": ["fs", "intrinsics", "thread"]}, {"name": "pcl", "default-features": false}, {"name": "rtabmap-res-tool", "host": true}, "sqlite3", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}, "zlib"], "default-features": ["gui", "opencv-contrib", "opencv-nonfree", {"name": "openmp", "platform": "!osx"}], "features": {"gui": {"description": "Build RTAB-Map with GUI support (Qt)", "dependencies": [{"name": "pcl", "default-features": false, "features": ["qt", "visualization"]}, {"name": "qtbase", "default-features": false, "features": ["gui", "opengl", "widgets"]}, {"name": "vtk", "default-features": false}]}, "k4w2": {"description": "Build RTAB-Map with Kinect For Windows SDK 2.0 camera driver", "dependencies": ["kinectsdk2"]}, "octomap": {"description": "Build RTAB-Map with OctoMap support", "dependencies": ["octomap"]}, "opencv-contrib": {"description": "Use OpenCV with contrib modules (e.g., xfeatures2d, aruco)", "dependencies": [{"name": "opencv", "default-features": false, "features": ["contrib", "fs", "intrinsics", "thread"]}]}, "opencv-cuda": {"description": "Use OpenCV with CUDA (accelerated SIFT ad SURF GPU options)", "dependencies": [{"name": "opencv", "default-features": false, "features": ["cuda", "fs", "intrinsics", "thread"]}]}, "opencv-nonfree": {"description": "Use OpenCV with nonfree module (surf feature)", "dependencies": [{"name": "opencv", "default-features": false, "features": ["fs", "intrinsics", "nonfree", "thread"]}]}, "openmp": {"description": "Enable OpenMP parallel execution"}, "openni2": {"description": "Build RTAB-Map with OpenNI2 camera driver", "dependencies": ["kinectsdk1", "openni2"]}, "realsense2": {"description": "Build RTAB-Map with RealSense2 camera driver", "dependencies": [{"name": "realsense2", "default-features": false}]}, "tools": {"description": "Build RTAB-Map's tools", "dependencies": ["yaml-cpp"]}}}