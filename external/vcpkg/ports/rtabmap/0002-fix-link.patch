--- a/corelib/src/global_map/OctoMap.cpp
+++ b/corelib/src/global_map/OctoMap.cpp
@@ -278,10 +278,8 @@ RtabmapColorOcTree::StaticMemberInitializer::StaticMemberInitializer() {
 	 AbstractOcTree::registerTreeType(tree);
  }
 
-#ifndef _WIN32
 // On Windows, the app freezes on start if the following is defined
 RtabmapColorOcTree::StaticMemberInitializer RtabmapColorOcTree::RtabmapColorOcTreeMemberInit;
-#endif
 
 
 //////////////////////////////////////
