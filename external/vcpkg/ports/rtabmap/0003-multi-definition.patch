--- a/corelib/include/rtabmap/core/stereo/stereoRectifyFisheye.h
+++ b/corelib/include/rtabmap/core/stereo/stereoRectifyFisheye.h
@@ -39,6 +39,10 @@ SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 #if CV_MAJOR_VERSION >= 4
 #include <opencv2/core/core_c.h>
 
+#include <rtabmap/core/rtabmap_core_export.h>
+
+namespace {
+
 // Opencv4 doesn't expose those functions below anymore, we should recopy all of them!
 int cvRodrigues2( const CvMat* src, CvMat* dst, CvMat* jacobian CV_DEFAULT(0))
 {
@@ -915,6 +919,8 @@ void cvConvertPointsHomogeneous( const CvMat* _src, CvMat* _dst )
     }
 }
 
+} // namespace
+
 #endif // OpenCV4
 
 #endif // OpenCV3
@@ -923,9 +929,11 @@ namespace rtabmap
 {
 
 void
+RTABMAP_CORE_EXPORT
 icvGetRectanglesFisheye( const CvMat* cameraMatrix, const CvMat* distCoeffs,
                  const CvMat* R, const CvMat* newCameraMatrix, CvSize imgSize,
                  cv::Rect_<float>& inner, cv::Rect_<float>& outer )
+#ifdef RTABMAP_STEREORECTIFIYFISHEYE_IMPLEMENTATION
 {
     const int N = 9;
     int x, y, k;
@@ -967,12 +975,17 @@ icvGetRectanglesFisheye( const CvMat* cameraMatrix, const CvMat* distCoeffs,
     inner = cv::Rect_<float>(iX0, iY0, iX1-iX0, iY1-iY0);
     outer = cv::Rect_<float>(oX0, oY0, oX1-oX0, oY1-oY0);
 }
+#else
+;
+#endif
 
-void cvStereoRectifyFisheye( const CvMat* _cameraMatrix1, const CvMat* _cameraMatrix2,
+void RTABMAP_CORE_EXPORT
+     cvStereoRectifyFisheye( const CvMat* _cameraMatrix1, const CvMat* _cameraMatrix2,
                       const CvMat* _distCoeffs1, const CvMat* _distCoeffs2,
                       CvSize imageSize, const CvMat* matR, const CvMat* matT,
                       CvMat* _R1, CvMat* _R2, CvMat* _P1, CvMat* _P2,
                       CvMat* matQ, int flags, double alpha, CvSize newImgSize )
+#ifdef RTABMAP_STEREORECTIFIYFISHEYE_IMPLEMENTATION
 {
     double _om[3], _t[3] = {0}, _uu[3]={0,0,0}, _r_r[3][3], _pp[3][4];
     double _ww[3], _wr[3][3], _z[3] = {0,0,0}, _ri[3][3], _w3[3];
@@ -1177,19 +1190,25 @@ void cvStereoRectifyFisheye( const CvMat* _cameraMatrix1, const CvMat* _cameraMa
         cvConvert( &Q, matQ );
     }
 }
+#else
+;
+#endif
+
 
-void stereoRectifyFisheye( cv::InputArray _cameraMatrix1, cv::InputArray _distCoeffs1,
+void RTABMAP_CORE_EXPORT
+     stereoRectifyFisheye( cv::InputArray _cameraMatrix1, cv::InputArray _distCoeffs1,
 				cv::InputArray _cameraMatrix2, cv::InputArray _distCoeffs2,
 				cv::Size imageSize, cv::InputArray _Rmat, cv::InputArray _Tmat,
 				cv::OutputArray _Rmat1, cv::OutputArray _Rmat2,
 				cv::OutputArray _Pmat1, cv::OutputArray _Pmat2,
 				cv::OutputArray _Qmat, int flags,
 				double alpha, cv::Size newImageSize)
+#ifdef RTABMAP_STEREORECTIFIYFISHEYE_IMPLEMENTATION
 {
     cv::Mat cameraMatrix1 = _cameraMatrix1.getMat(), cameraMatrix2 = _cameraMatrix2.getMat();
     cv::Mat distCoeffs1 = _distCoeffs1.getMat(), distCoeffs2 = _distCoeffs2.getMat();
     cv::Mat Rmat = _Rmat.getMat(), Tmat = _Tmat.getMat();
-	
+
 #if CV_MAJOR_VERSION > 3 || (CV_MAJOR_VERSION >= 3 && (CV_MINOR_VERSION>4 || (CV_MINOR_VERSION>=4 && CV_SUBMINOR_VERSION>=4)))
     CvMat c_cameraMatrix1 = cvMat(cameraMatrix1);
     CvMat c_cameraMatrix2 = cvMat(cameraMatrix2);
@@ -1238,6 +1257,9 @@ void stereoRectifyFisheye( cv::InputArray _cameraMatrix1, cv::InputArray _distCo
         CvSize(newImageSize));
 #endif
 }
+#else
+;
+#endif
 
 }
 
--- a/corelib/src/StereoCameraModel.cpp
+++ b/corelib/src/StereoCameraModel.cpp
@@ -34,6 +34,7 @@ SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 #include <opencv2/imgproc/imgproc.hpp>
 
 #if CV_MAJOR_VERSION > 2 or (CV_MAJOR_VERSION == 2 and (CV_MINOR_VERSION >4 or (CV_MINOR_VERSION == 4 and CV_SUBMINOR_VERSION >=10)))
+#define RTABMAP_STEREORECTIFIYFISHEYE_IMPLEMENTATION
 #include <rtabmap/core/stereo/stereoRectifyFisheye.h>
 #endif
 
