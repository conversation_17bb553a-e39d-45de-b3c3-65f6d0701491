--- a/corelib/src/optimizer/OptimizerCeres.cpp
+++ b/corelib/src/optimizer/OptimizerCeres.cpp
@@ -197,7 +197,7 @@ std::map<int, Transform> OptimizerCeres::optimize(
 
 					if(angle_local_manifold == NULL)
 					{
-						angle_local_manifold = ceres::examples::AngleManfold::Create();
+						angle_local_manifold = ceres::examples::AngleManifold::Create();
 					}
 					SetCeresProblemManifold(problem, &pose_begin_iter->second.yaw_radians, angle_local_manifold);
 					SetCeresProblemManifold(problem, &pose_end_iter->second.yaw_radians, angle_local_manifold);
