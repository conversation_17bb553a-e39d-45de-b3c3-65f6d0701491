--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -229,12 +229,12 @@ ENDIF()
 set(RTABMAP_QT_VERSION AUTO CACHE STRING "Force a specific Qt version.")
 set_property(CACHE RTABMAP_QT_VERSION PROPERTY STRINGS AUTO 4 5 6)
 
-FIND_PACKAGE(OpenCV REQUIRED QUIET COMPONENTS core calib3d imgproc highgui stitching photo video videoio OPTIONAL_COMPONENTS aruco xfeatures2d nonfree gpu cudafeatures2d)
+FIND_PACKAGE(OpenCV REQUIRED COMPONENTS core calib3d imgproc highgui stitching photo video videoio OPTIONAL_COMPONENTS aruco xfeatures2d nonfree gpu cudafeatures2d)
 
 IF(WITH_QT)
-FIND_PACKAGE(PCL 1.7 REQUIRED QUIET COMPONENTS common io kdtree search surface filters registration sample_consensus segmentation visualization)
+FIND_PACKAGE(PCL 1.7 REQUIRED COMPONENTS common io kdtree search surface filters registration sample_consensus segmentation visualization)
 ELSE()
-FIND_PACKAGE(PCL 1.7 REQUIRED QUIET COMPONENTS common io kdtree search surface filters registration sample_consensus segmentation )
+FIND_PACKAGE(PCL 1.7 REQUIRED COMPONENTS common io kdtree search surface filters registration sample_consensus segmentation )
 ENDIF()
 if(PCL_COMPILE_OPTIONS)
   if("${PCL_COMPILE_OPTIONS}" MATCHES "-march=native")
@@ -250,9 +250,9 @@ else()
   endif()
 endif()
 
-FIND_PACKAGE(ZLIB REQUIRED QUIET)
+FIND_PACKAGE(ZLIB REQUIRED )
 
-FIND_PACKAGE(SQLite3 QUIET)
+FIND_PACKAGE(SQLite3 REQUIRED)
 IF(SQLite3_FOUND)
    MESSAGE(STATUS "Found SQLite3: ${SQLite3_INCLUDE_DIRS} ${SQLite3_LIBRARIES}")
 ENDIF(SQLite3_FOUND)
@@ -264,8 +264,8 @@ endif()
 
 # OpenMP ("-fopenmp" should be added for flann included in PCL)
 # the gcc-4.2.1 coming with MacOS X is not compatible with the OpenMP pragmas we use, so disabling OpenMP for it
-if(((NOT APPLE) OR (NOT CMAKE_COMPILER_IS_GNUCXX) OR (GCC_VERSION VERSION_GREATER 4.2.1) OR (CMAKE_CXX_COMPILER_ID STREQUAL "Clang")) AND WITH_OPENMP)
-  find_package(OpenMP COMPONENTS C CXX)
+if(WITH_OPENMP)
+  find_package(OpenMP REQUIRED COMPONENTS C CXX)
 endif()
 if(OPENMP_FOUND)
   set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} ${OpenMP_C_FLAGS}")
@@ -292,7 +292,7 @@ ENDIF(ZLIB_FOUND)
 
 SET(ADD_VTK_GUI_SUPPORT_QT_TO_CONF FALSE)
 IF(WITH_QT)
-    FIND_PACKAGE(VTK)
+    FIND_PACKAGE(VTK REQUIRED)
     IF(NOT VTK_FOUND)
       MESSAGE(FATAL_ERROR "VTK is required when using Qt. Set -DWITH_QT=OFF if you don't want gui tools.")
     ENDIF(NOT VTK_FOUND)
@@ -310,29 +310,29 @@ IF(WITH_QT)
 
       MESSAGE(STATUS "VTK>=9 detected, will use VTK_QT_VERSION=${VTK_QT_VERSION} for Qt version.")
       IF(${VTK_QT_VERSION} EQUAL 6)
-        FIND_PACKAGE(Qt6 COMPONENTS Widgets Core Gui OpenGL PrintSupport QUIET OPTIONAL_COMPONENTS Svg)
+        FIND_PACKAGE(Qt6 REQUIRED COMPONENTS Widgets Core Gui OpenGL PrintSupport Svg)
       ELSEIF(${VTK_QT_VERSION} EQUAL 5)
-        FIND_PACKAGE(Qt5 COMPONENTS Widgets Core Gui OpenGL PrintSupport QUIET OPTIONAL_COMPONENTS Svg)
+        FIND_PACKAGE(Qt5 REQUIRED COMPONENTS Widgets Core Gui OpenGL PrintSupport Svg)
       ELSE()
-        FIND_PACKAGE(Qt4 COMPONENTS QtCore QtGui OPTIONAL_COMPONENTS QtSvg)
+        FIND_PACKAGE(Qt4 REQUIRED COMPONENTS QtCore QtGui QtSvg)
       ENDIF()
     ELSE()
       # look for Qt5 (if vtk>5 is installed) before Qt4
       IF("${VTK_MAJOR_VERSION}" GREATER 5)
         IF(RTABMAP_QT_VERSION STREQUAL "AUTO" OR RTABMAP_QT_VERSION STREQUAL "5")
-          FIND_PACKAGE(Qt5 COMPONENTS Widgets Core Gui OpenGL PrintSupport QUIET OPTIONAL_COMPONENTS Svg)
+          FIND_PACKAGE(Qt5 REQUIRED COMPONENTS Widgets Core Gui OpenGL PrintSupport Svg)
         ENDIF(RTABMAP_QT_VERSION STREQUAL "AUTO" OR RTABMAP_QT_VERSION STREQUAL "5")
       ENDIF("${VTK_MAJOR_VERSION}" GREATER 5)
 
       IF(NOT Qt5_FOUND)
         IF(RTABMAP_QT_VERSION STREQUAL "AUTO" OR RTABMAP_QT_VERSION STREQUAL "4")
-          FIND_PACKAGE(Qt4 COMPONENTS QtCore QtGui OPTIONAL_COMPONENTS QtSvg)
+          FIND_PACKAGE(Qt4 REQUIRED COMPONENTS QtCore QtGui QtSvg)
         ENDIF(RTABMAP_QT_VERSION STREQUAL "AUTO" OR RTABMAP_QT_VERSION STREQUAL "4")
       ENDIF(NOT Qt5_FOUND)
     ENDIF()
-        
+
     IF(QT4_FOUND OR Qt5_FOUND OR Qt6_FOUND)
-        # For VCPKG build, set those global variables to off, 
+        # For VCPKG build, set those global variables to off,
         # we will enable them for jsut specific targets
         set(CMAKE_AUTOMOC OFF)
         set(CMAKE_AUTORCC OFF)
@@ -391,14 +391,14 @@ IF(NOT VTK_FOUND)
 ENDIF(NOT VTK_FOUND)
 
 IF(WITH_TORCH)
-    FIND_PACKAGE(Torch QUIET)
+    FIND_PACKAGE(Torch REQUIRED)
     IF(TORCH_FOUND)
         MESSAGE(STATUS "Found Torch: ${TORCH_INCLUDE_DIRS}")
     ENDIF(TORCH_FOUND)
 ENDIF(WITH_TORCH)
 
 IF(WITH_PYTHON)
-    FIND_PACKAGE(Python3 COMPONENTS Interpreter Development NumPy)
+    FIND_PACKAGE(Python3 REQUIRED COMPONENTS Interpreter Development NumPy)
     IF(Python3_FOUND)
         MESSAGE(STATUS "Found Python3")
         FIND_PACKAGE(pybind11 REQUIRED)
@@ -406,28 +406,28 @@ IF(WITH_PYTHON)
 ENDIF(WITH_PYTHON)
 
 IF(WITH_PDAL)
-    FIND_PACKAGE(PDAL QUIET)
+    FIND_PACKAGE(PDAL REQUIRED)
     IF(PDAL_FOUND)
        MESSAGE(STATUS "Found PDAL ${PDAL_VERSION}: ${PDAL_INCLUDE_DIRS}")
     ENDIF(PDAL_FOUND)
 ENDIF(WITH_PDAL)
 
 IF(WITH_FREENECT)
-    FIND_PACKAGE(Freenect QUIET)
+    FIND_PACKAGE(Freenect REQUIRED)
     IF(Freenect_FOUND)
        MESSAGE(STATUS "Found Freenect: ${Freenect_INCLUDE_DIRS}")
     ENDIF(Freenect_FOUND)
 ENDIF(WITH_FREENECT)
 
 IF(WITH_FREENECT2)
-    FIND_PACKAGE(freenect2 QUIET)
+    FIND_PACKAGE(freenect2 REQUIRED)
     IF(freenect2_FOUND)
        IF(NOT freenect2_INCLUDE_DIRS)
           SET(freenect2_INCLUDE_DIRS ${freenect2_INCLUDE_DIR})
        ENDIF(NOT freenect2_INCLUDE_DIRS)
        MESSAGE(STATUS "Found freenect2: ${freenect2_INCLUDE_DIRS}")
        # Explicitly link to OpenCL (in case of CUDA installed)
-       FIND_PACKAGE(OpenCL QUIET)
+       FIND_PACKAGE(OpenCL REQUIRED)
        IF(OpenCL_FOUND)
           SET(freenect2_LIBRARIES
             ${OpenCL_LIBRARIES}
@@ -438,7 +438,7 @@ IF(WITH_FREENECT2)
 ENDIF(WITH_FREENECT2)
 
 IF(WITH_K4W2 AND WIN32)
-    FIND_PACKAGE(KinectSDK2 QUIET)
+    FIND_PACKAGE(KinectSDK2 REQUIRED)
     IF(KinectSDK2_FOUND)
        MESSAGE(STATUS "Found Kinect for Windows 2: ${KinectSDK2_INCLUDE_DIRS}")
     ENDIF(KinectSDK2_FOUND)
@@ -446,10 +446,10 @@ ENDIF(WITH_K4W2 AND WIN32)
 
 IF(WITH_K4A)
     IF(WIN32)
-        FIND_PACKAGE(K4A QUIET)
+        FIND_PACKAGE(K4A REQUIRED)
     ELSE()
-        FIND_PACKAGE(k4a QUIET)
-        FIND_PACKAGE(k4arecord QUIET)
+        FIND_PACKAGE(k4a REQUIRED)
+        FIND_PACKAGE(k4arecord REQUIRED)
         IF(NOT (k4a_FOUND AND k4arecord_FOUND))
             SET(k4a_FOUND FALSE)
         ENDIF(NOT (k4a_FOUND AND k4arecord_FOUND))
@@ -461,27 +461,27 @@ ENDIF(WITH_K4A)
 
 # IF PCL depends on OpenNI2 (already found), ignore WITH_OPENNI2
 IF(WITH_OPENNI2 OR OpenNI2_FOUND)
-    FIND_PACKAGE(OpenNI2 QUIET)
+    FIND_PACKAGE(OpenNI2 REQUIRED)
     IF(OpenNI2_FOUND)
        MESSAGE(STATUS "Found OpenNI2: ${OpenNI2_INCLUDE_DIRS}")
     ENDIF(OpenNI2_FOUND)
 ENDIF(WITH_OPENNI2 OR OpenNI2_FOUND)
 
 IF(WITH_DC1394)
-    FIND_PACKAGE(DC1394 QUIET)
+    FIND_PACKAGE(DC1394 REQUIRED)
     IF(DC1394_FOUND)
        MESSAGE(STATUS "Found DC1394: ${DC1394_INCLUDE_DIRS}")
     ENDIF(DC1394_FOUND)
 ENDIF(WITH_DC1394)
 
 IF(WITH_G2O)
-    FIND_PACKAGE(g2o NO_MODULE)
+    FIND_PACKAGE(g2o REQUIRED NO_MODULE)
     IF(g2o_FOUND)
        MESSAGE(STATUS "Found g2o (targets)")
          SET(G2O_FOUND ${g2o_FOUND})
          get_target_property(G2O_INCLUDES g2o::core INTERFACE_INCLUDE_DIRECTORIES)
          MESSAGE(STATUS "g2o include dir: ${G2O_INCLUDES}")
-         FIND_FILE(G2O_FACTORY_FILE g2o/core/factory.h 
+         FIND_FILE(G2O_FACTORY_FILE g2o/core/factory.h
          PATHS ${G2O_INCLUDES}
          NO_DEFAULT_PATH)
        FILE(READ ${G2O_FACTORY_FILE} TMPTXT)
@@ -494,7 +494,7 @@ IF(WITH_G2O)
            SET(G2O_CPP11 1)
        ENDIF()
     ELSE()
-        FIND_PACKAGE(G2O QUIET)
+        FIND_PACKAGE(G2O REQUIRED)
         IF(G2O_FOUND)
             MESSAGE(STATUS "Found g2o: ${G2O_INCLUDE_DIRS}")
         ENDIF(G2O_FOUND)
@@ -503,11 +503,11 @@ ENDIF(WITH_G2O)
 
 IF(WITH_GTSAM)
     # Force config mode to ignore PCL's findGTSAM.cmake file
-    FIND_PACKAGE(GTSAM CONFIG QUIET)
+    FIND_PACKAGE(GTSAM CONFIG REQUIRED)
 ENDIF(WITH_GTSAM)
 
 IF(WITH_MRPT)
-    FIND_PACKAGE(MRPT COMPONENTS poses QUIET)
+    FIND_PACKAGE(MRPT COMPONENTS poses REQUIRED)
     IF(MRPT_FOUND)
         message(STATUS "MRPT_VERSION: ${MRPT_VERSION}")
         message(STATUS "MRPT_LIBRARIES: ${MRPT_LIBRARIES}")
@@ -515,21 +515,21 @@ IF(WITH_MRPT)
 ENDIF(WITH_MRPT)
 
 IF(WITH_FLYCAPTURE2)
-    FIND_PACKAGE(FlyCapture2 QUIET)
+    FIND_PACKAGE(FlyCapture2 REQUIRED)
     IF(FlyCapture2_FOUND)
        MESSAGE(STATUS "Found FlyCapture2: ${FlyCapture2_INCLUDE_DIRS}")
     ENDIF(FlyCapture2_FOUND)
 ENDIF(WITH_FLYCAPTURE2)
 
 IF(WITH_CVSBA)
-    FIND_PACKAGE(cvsba QUIET)
+    FIND_PACKAGE(cvsba REQUIRED)
     IF(cvsba_FOUND)
        MESSAGE(STATUS "Found cvsba: ${cvsba_INCLUDE_DIRS}")
     ENDIF(cvsba_FOUND)
 ENDIF(WITH_CVSBA)
 
 IF(WITH_POINTMATCHER)
-    find_package(libpointmatcher QUIET)
+    find_package(libpointmatcher REQUIRED)
     IF(libpointmatcher_FOUND)
         MESSAGE(STATUS "Found libpointmatcher: ${libpointmatcher_INCLUDE_DIRS}")
         string(FIND "${libpointmatcher_LIBRARIES}" "libnabo" value)
@@ -554,19 +554,19 @@ IF(libpointmatcher_FOUND OR GTSAM_FOUND)
 ENDIF(libpointmatcher_FOUND OR GTSAM_FOUND)
 
 IF(WITH_CCCORELIB)
-    find_package(CCCoreLib QUIET)
+    find_package(CCCoreLib REQUIRED)
     IF(CCCoreLib_FOUND)
        MESSAGE(STATUS "Found CCCoreLib: ${CCCoreLib_INCLUDE_DIRS}")
     ENDIF(CCCoreLib_FOUND)
 ENDIF(WITH_CCCORELIB)
 
 IF(WITH_OPEN3D)
-    IF(${CMAKE_VERSION} VERSION_LESS "3.19.0") 
+    IF(${CMAKE_VERSION} VERSION_LESS "3.19.0")
        MESSAGE(WARNING "Open3D requires CMake version >=3.19 (current is ${CMAKE_VERSION})")
     ELSE()
        # Build Open3D like this to avoid linker errors in rtabmap:
        # cmake -DBUILD_SHARED_LIBS=ON -DGLIBCXX_USE_CXX11_ABI=ON -DCMAKE_BUILD_TYPE=Release ..
-       find_package(Open3D QUIET)
+       find_package(Open3D REQUIRED)
        IF(Open3D_FOUND)
           MESSAGE(STATUS "Found Open3D: ${Open3DINCLUDE_DIRS}")
        ENDIF(Open3D_FOUND)
@@ -574,28 +574,28 @@ IF(WITH_OPEN3D)
 ENDIF(WITH_OPEN3D)
 
 IF(WITH_LOAM)
-    find_package(loam_velodyne QUIET)
+    find_package(loam_velodyne REQUIRED)
     IF(loam_velodyne_FOUND)
        MESSAGE(STATUS "Found loam_velodyne: ${loam_velodyne_INCLUDE_DIRS}")
     ENDIF(loam_velodyne_FOUND)
 ENDIF(WITH_LOAM)
 
 IF(WITH_FLOAM)
-    find_package(floam QUIET)
+    find_package(floam REQUIRED)
     IF(floam_FOUND)
        MESSAGE(STATUS "Found floam: ${floam_INCLUDE_DIRS}")
-       FIND_PACKAGE(Ceres QUIET REQUIRED)
+       FIND_PACKAGE(Ceres REQUIRED)
     ENDIF(floam_FOUND)
 ENDIF(WITH_FLOAM)
 
 SET(ZED_FOUND FALSE)
 IF(WITH_ZED)
-    find_package(ZED 2 QUIET)
-    
+    find_package(ZED 2 REQUIRED)
+
     IF(ZED_FOUND)
        MESSAGE(STATUS "Found ZED sdk: ${ZED_INCLUDE_DIRS}")
        ## look for CUDA
-       find_package(CUDA)
+       find_package(CUDA REQUIRED)
        IF(CUDA_FOUND)
         MESSAGE(STATUS "Found CUDA: ${CUDA_INCLUDE_DIRS}")
        ELSE()
@@ -605,11 +605,11 @@ IF(WITH_ZED)
 ENDIF(WITH_ZED)
 
 IF(WITH_ZEDOC)
-    find_package(ZEDOC QUIET)
+    find_package(ZEDOC REQUIRED)
     IF(ZEDOC_FOUND)
        MESSAGE(STATUS "Found ZED Open Capture: ${ZEDOC_INCLUDE_DIRS}")
        ## look for HIDAPI
-       find_package(HIDAPI)
+       find_package(HIDAPI REQUIRED)
        IF(HIDAPI_FOUND)
           MESSAGE(STATUS "Found HIDAPI: ${HIDAPI_INCLUDE_DIRS}")
        ELSE()
@@ -620,9 +620,9 @@ ENDIF(WITH_ZEDOC)
 
 IF(WITH_REALSENSE)
     IF(WITH_REALSENSE_SLAM)
-       FIND_PACKAGE(RealSense QUIET COMPONENTS slam)
+       FIND_PACKAGE(RealSense REQUIRED COMPONENTS slam)
     ELSE()
-       FIND_PACKAGE(RealSense QUIET)
+       FIND_PACKAGE(RealSense REQUIRED)
     ENDIF()
     IF(RealSense_FOUND)
        MESSAGE(STATUS "Found RealSense: ${RealSense_INCLUDE_DIRS}")
@@ -634,9 +634,9 @@ ENDIF(WITH_REALSENSE)
 
 IF(WITH_REALSENSE2)
     IF(WIN32)
-        FIND_PACKAGE(RealSense2 QUIET)
+        FIND_PACKAGE(RealSense2 REQUIRED)
     ELSE()
-        FIND_PACKAGE(realsense2 QUIET)
+        FIND_PACKAGE(realsense2 REQUIRED)
     ENDIF()
     IF(realsense2_FOUND)
        MESSAGE(STATUS "Found RealSense2: ${realsense2_INCLUDE_DIRS}")
@@ -644,21 +644,21 @@ IF(WITH_REALSENSE2)
 ENDIF(WITH_REALSENSE2)
 
 IF(WITH_MYNTEYE)
-    FIND_PACKAGE(mynteye QUIET)
+    FIND_PACKAGE(mynteye REQUIRED)
     IF(mynteye_FOUND)
        MESSAGE(STATUS "Found mynteye-s: ${mynteye_INCLUDE_DIRS}")
     ENDIF(mynteye_FOUND)
 ENDIF(WITH_MYNTEYE)
 
 IF(WITH_DEPTHAI)
-    FIND_PACKAGE(depthai 2 QUIET)
+    FIND_PACKAGE(depthai 2 REQUIRED)
     IF(depthai_FOUND)
        MESSAGE(STATUS "Found depthai-core (targets)")
     ENDIF(depthai_FOUND)
 ENDIF(WITH_DEPTHAI)
 
 IF(WITH_OCTOMAP)
-    FIND_PACKAGE(octomap QUIET)
+    FIND_PACKAGE(octomap REQUIRED)
     IF(octomap_FOUND)
        MESSAGE(STATUS "Found octomap ${octomap_VERSION}: ${OCTOMAP_INCLUDE_DIRS}")
        IF(octomap_VERSION VERSION_LESS 1.8)
@@ -668,35 +668,35 @@ IF(WITH_OCTOMAP)
 ENDIF(WITH_OCTOMAP)
 
 IF(WITH_GRIDMAP)
-    FIND_PACKAGE(grid_map_core QUIET)
+    FIND_PACKAGE(grid_map_core REQUIRED)
     IF(grid_map_core_FOUND)
        MESSAGE(STATUS "Found grid_map_core ${grid_map_core_VERSION}: ${grid_map_core_INCLUDE_DIRS}")
     ENDIF(grid_map_core_FOUND)
 ENDIF(WITH_GRIDMAP)
 
 IF(WITH_CPUTSDF)
-    FIND_PACKAGE(CPUTSDF QUIET)
+    FIND_PACKAGE(CPUTSDF REQUIRED)
     IF(CPUTSDF_FOUND)
        MESSAGE(STATUS "Found CPUTSDF: ${CPUTSDF_INCLUDE_DIRS}")
     ENDIF(CPUTSDF_FOUND)
 ENDIF(WITH_CPUTSDF)
 
 IF(WITH_OPENCHISEL)
-    find_package(open_chisel QUIET)
+    find_package(open_chisel REQUIRED)
     if(open_chisel_FOUND)
         MESSAGE(STATUS "Found open_chisel: ${open_chisel_INCLUDE_DIRS}")
     endif(open_chisel_FOUND)
 ENDIF(WITH_OPENCHISEL)
 
 IF(WITH_ALICE_VISION)
-    find_package(AliceVision CONFIG QUIET)
+    find_package(AliceVision CONFIG REQUIRED)
     IF(AliceVision_FOUND)
         IF(${AliceVision_VERSION} VERSION_LESS_EQUAL "2.2")
            find_package(Boost COMPONENTS log log_setup container REQUIRED)
         ENDIF(${AliceVision_VERSION} VERSION_LESS_EQUAL "2.2")
         SET(CMAKE_MODULE_PATH "${CMAKE_MODULE_PATH};/usr/local/lib/cmake/modules")
-        find_package(Geogram REQUIRED QUIET)
-        find_package(assimp QUIET)
+        find_package(Geogram REQUIRED)
+        find_package(assimp REQUIRED)
         add_definitions("-DRTABMAP_ALICE_VISION_MAJOR=${AliceVision_VERSION_MAJOR}")
         add_definitions("-DRTABMAP_ALICE_VISION_MINOR=${AliceVision_VERSION_MINOR}")
         add_definitions("-DRTABMAP_ALICE_VISION_PATCH=${AliceVision_VERSION_PATCH}")
@@ -704,28 +704,28 @@ IF(WITH_ALICE_VISION)
 ENDIF(WITH_ALICE_VISION)
 
 IF(WITH_FOVIS)
-    FIND_PACKAGE(libfovis QUIET)
+    FIND_PACKAGE(libfovis REQUIRED)
     IF(libfovis_FOUND)
        MESSAGE(STATUS "Found libfovis: ${libfovis_INCLUDE_DIRS}")
     ENDIF(libfovis_FOUND)
 ENDIF(WITH_FOVIS)
 
 IF(WITH_VISO2)
-    FIND_PACKAGE(libviso2 QUIET)
+    FIND_PACKAGE(libviso2 REQUIRED)
     IF(libviso2_FOUND)
        MESSAGE(STATUS "Found libviso2: ${libviso2_INCLUDE_DIRS}")
     ENDIF(libviso2_FOUND)
 ENDIF(WITH_VISO2)
 
 IF(WITH_DVO)
-    FIND_PACKAGE(dvo_core QUIET)
+    FIND_PACKAGE(dvo_core REQUIRED)
     IF(dvo_core_FOUND)
        MESSAGE(STATUS "Found dvo_core: ${dvo_core_INCLUDE_DIRS}")
     ENDIF(dvo_core_FOUND)
 ENDIF(WITH_DVO)
 
 IF(WITH_OKVIS)
-    FIND_PACKAGE(okvis 1.1 QUIET)
+    FIND_PACKAGE(okvis 1.1 REQUIRED)
     IF(okvis_FOUND)
        MESSAGE(STATUS "Found okvis: ${OKVIS_INCLUDE_DIRS}")
        find_package(brisk 2 REQUIRED)
@@ -740,7 +740,7 @@ ENDIF(WITH_OKVIS)
 # If built with okvis, we found already ceres above
 IF(WITH_CERES)
   IF(NOT okvis_FOUND AND NOT floam_FOUND)
-    FIND_PACKAGE(Ceres QUIET)
+    FIND_PACKAGE(Ceres REQUIRED)
     MESSAGE(STATUS "Found ceres ${Ceres_VERSION}: ${CERES_INCLUDE_DIRS}")
   ENDIF(NOT okvis_FOUND AND NOT floam_FOUND)
 ELSEIF(Ceres_FOUND)
@@ -748,14 +748,14 @@ ELSEIF(Ceres_FOUND)
 ENDIF()
 
 IF(WITH_MSCKF_VIO)
-    FIND_PACKAGE(msckf_vio QUIET)
+    FIND_PACKAGE(msckf_vio REQUIRED)
     IF(msckf_vio_FOUND)
        MESSAGE(STATUS "Found msckf_vio: ${msckf_vio_INCLUDE_DIRS}")
     ENDIF(msckf_vio_FOUND)
 ENDIF(WITH_MSCKF_VIO)
 
 IF(WITH_VINS)
-    FIND_PACKAGE(vins QUIET)
+    FIND_PACKAGE(vins REQUIRED)
     IF(vins_FOUND)
        MESSAGE(STATUS "Found vins: ${vins_INCLUDE_DIRS}")
        IF(okvis_FOUND)
@@ -765,28 +765,28 @@ IF(WITH_VINS)
 ENDIF(WITH_VINS)
 
 IF(WITH_OPENVINS)
-    FIND_PACKAGE(ov_msckf QUIET)
+    FIND_PACKAGE(ov_msckf REQUIRED)
     IF(ov_msckf_FOUND)
        MESSAGE(STATUS "Found ov_msckf: ${ov_msckf_INCLUDE_DIRS}")
     ENDIF(ov_msckf_FOUND)
 ENDIF(WITH_OPENVINS)
 
 IF(WITH_FASTCV)
-    FIND_PACKAGE(FastCV QUIET)
+    FIND_PACKAGE(FastCV REQUIRED)
     IF(FastCV_FOUND)
        MESSAGE(STATUS "Found FastCV: ${FastCV_INCLUDE_DIRS}")
     ENDIF(FastCV_FOUND)
 ENDIF(WITH_FASTCV)
 
 IF(WITH_OPENGV)
-    FIND_PACKAGE(opengv QUIET)
+    FIND_PACKAGE(opengv REQUIRED)
     IF(opengv_FOUND)
        MESSAGE(STATUS "Found OpenGV: ${opengv_INCLUDE_DIRS}")
     ENDIF(opengv_FOUND)
 ENDIF(WITH_OPENGV)
 
 IF(WITH_ORB_SLAM AND NOT G2O_FOUND)
-    FIND_PACKAGE(ORB_SLAM QUIET)
+    FIND_PACKAGE(ORB_SLAM REQUIRED)
     IF(ORB_SLAM_FOUND)
        MESSAGE(STATUS "Found ORB_SLAM${ORB_SLAM_VERSION}: ${ORB_SLAM_INCLUDE_DIRS}")
     ENDIF(ORB_SLAM_FOUND)
--- a/app/android/CMakeLists.txt
+++ b/app/android/CMakeLists.txt
@@ -5,25 +5,25 @@ option(WITH_ARENGINE      "Include AREngine support"             ON)
 option(DISABLE_LOG        "Disable Android logging (should be true in release)"    ON)
 option(DEPTH_TEST         "Enable depth test on ARCore"    OFF)
 
-# Google Tango needs access to system shared 
-# libraries (e.g. libbinder.so) that are not accessible 
+# Google Tango needs access to system shared
+# libraries (e.g. libbinder.so) that are not accessible
 # with android >=24
 IF(WITH_TANGO AND ${ANDROID_NATIVE_API_LEVEL} LESS 24)
-    FIND_PACKAGE(Tango QUIET)
+    FIND_PACKAGE(Tango REQUIRED)
     IF(Tango_FOUND)
        MESSAGE(STATUS "Found Tango: ${Tango_INCLUDE_DIRS}")
     ENDIF(Tango_FOUND)
 ENDIF(WITH_TANGO AND ${ANDROID_NATIVE_API_LEVEL} LESS 24)
 
 IF(WITH_ARCORE AND ${ANDROID_NATIVE_API_LEVEL} GREATER 22)
-    FIND_PACKAGE(ARCore QUIET)
+    FIND_PACKAGE(ARCore REQUIRED)
     IF(ARCore_FOUND)
        MESSAGE(STATUS "Found ARCore: ${ARCore_INCLUDE_DIRS}")
     ENDIF(ARCore_FOUND)
 ENDIF(WITH_ARCORE AND ${ANDROID_NATIVE_API_LEVEL} GREATER 22)
 
 IF(WITH_ARENGINE AND ${ANDROID_NATIVE_API_LEVEL} GREATER 23)
-    FIND_PACKAGE(AREngine QUIET)
+    FIND_PACKAGE(AREngine REQUIRED)
     IF(AREngine_FOUND)
        MESSAGE(STATUS "Found AREngine: ${AREngine_INCLUDE_DIRS}")
     ENDIF(AREngine_FOUND)
--- a/cmake_modules/FindEigen3.cmake
+++ b/cmake_modules/FindEigen3.cmake
@@ -71,16 +71,16 @@ if (EIGEN3_INCLUDE_DIR)
   set(Eigen3_FOUND ${EIGEN3_VERSION_OK})
 
 else ()
-  
+
   # search first if an Eigen3Config.cmake is available in the system,
   # if successful this would set EIGEN3_INCLUDE_DIR and the rest of
   # the script will work as usual
-  find_package(Eigen3 ${Eigen3_FIND_VERSION} NO_MODULE QUIET)
+  find_package(Eigen3 ${Eigen3_FIND_VERSION} NO_MODULE REQUIRED)
 
   if(NOT EIGEN3_INCLUDE_DIR)
     find_path(EIGEN3_INCLUDE_DIR NAMES signature_of_eigen3_matrix_library
         HINTS
-        ENV EIGEN3_ROOT 
+        ENV EIGEN3_ROOT
         ENV EIGEN3_ROOT_DIR
         PATHS
         ${CMAKE_INSTALL_PREFIX}/include
--- a/cmake_modules/FindHIDAPI.cmake
+++ b/cmake_modules/FindHIDAPI.cmake
@@ -75,7 +75,7 @@ if(NOT "${HIDAPI_FIND_COMPONENTS}")
 endif()
 
 # Ask pkg-config for hints
-find_package(PkgConfig QUIET)
+find_package(PkgConfig REQUIRED)
 if(PKG_CONFIG_FOUND)
     set(_old_prefix_path "${CMAKE_PREFIX_PATH}")
     # So pkg-config uses HIDAPI_ROOT_DIR too.
@@ -116,7 +116,7 @@ find_path(
     PATH_SUFFIXES hidapi include include/hidapi
     HINTS ${PC_HIDAPI_HIDRAW_INCLUDE_DIRS} ${PC_HIDAPI_LIBUSB_INCLUDE_DIRS})
 
-find_package(Threads QUIET)
+find_package(Threads REQUIRED)
 
 ###
 # Compute the "I don't care which backend" library
--- a/cmake_modules/FindORB_SLAM.cmake
+++ b/cmake_modules/FindORB_SLAM.cmake
@@ -33,7 +33,7 @@ IF (ORB_SLAM_INCLUDE_DIR AND ORB_SLAM_LIBRARY AND DBoW2_LIBRARY AND g2o_INCLUDE_
    SET(ORB_SLAM_LIBRARIES ${g2o_LIBRARY} ${ORB_SLAM_LIBRARY} ${DBoW2_LIBRARY})
 ENDIF (ORB_SLAM_INCLUDE_DIR AND ORB_SLAM_LIBRARY AND DBoW2_LIBRARY AND g2o_INCLUDE_DIR AND g2o_LIBRARY)
 
-FIND_PACKAGE(Pangolin QUIET)
+FIND_PACKAGE(Pangolin REQUIRED)
 IF(NOT Pangolin_FOUND)
   SET(ORB_SLAM_FOUND FALSE)
   MESSAGE(STATUS "Found ORB_SLAM but not Pangolin, disabling ORB_SLAM.")
@@ -54,4 +54,3 @@ ELSE (ORB_SLAM_FOUND)
       MESSAGE(FATAL_ERROR "Could not find ORB_SLAM")
    ENDIF (ORB_SLAM_FIND_REQUIRED)
 ENDIF (ORB_SLAM_FOUND)
-
--- a/corelib/src/CMakeLists.txt
+++ b/corelib/src/CMakeLists.txt
@@ -167,13 +167,11 @@ SET(LIBRARIES
 # Issue that qhull dependency uses optimized and debug keywords,
 # which are converted to \$<\$<NOT:\$<CONFIG:DEBUG>> and \$<\$<CONFIG:DEBUG>
 # in RTABMap_coreTargets.cmake (not sure why?!).
-list(REMOVE_ITEM PCL_LIBRARIES "debug" "optimized")
 SET(PUBLIC_LIBRARIES
 	${OpenCV_LIBS}
 	${PCL_LIBRARIES}
 )
 
-IF(SQLite3_FOUND)
     SET(INCLUDE_DIRS
 		${INCLUDE_DIRS}
 		${SQLite3_INCLUDE_DIRS}
@@ -182,16 +180,7 @@ IF(SQLite3_FOUND)
 		${LIBRARIES}
 		${SQLite3_LIBRARIES}
 	)
-ELSE()
-	SET(SRC_FILES
-		${SRC_FILES}
-		sqlite3/sqlite3.c
-	)
-	 SET(INCLUDE_DIRS
-	    ${CMAKE_CURRENT_SOURCE_DIR}/sqlite3
-		${INCLUDE_DIRS}
-	)
-ENDIF()
+
 
 IF(TORCH_FOUND)
 	SET(LIBRARIES
@@ -748,8 +734,8 @@ IF(GTSAM_FOUND)
 	)
 	IF(WIN32)
 		#explicitly add metis target on windows (after gtsam target)
-		SET(LIBRARIES
-			${LIBRARIES}
+		SET(PUBLIC_LIBRARIES
+			${PUBLIC_LIBRARIES}
 			metis
 		)
 	ENDIF(WIN32)
--- a/utilite/resource_generator/CMakeLists.txt
+++ b/utilite/resource_generator/CMakeLists.txt
@@ -1,5 +1,5 @@
 
-if (CMAKE_CROSSCOMPILING OR ANDROID OR IOS)
+if (TRUE)
     # See this page about tools being required in the build:
     # https://gitlab.kitware.com/cmake/community/-/wikis/doc/cmake/CrossCompiling#using-executables-in-the-build-created-during-the-build
 
