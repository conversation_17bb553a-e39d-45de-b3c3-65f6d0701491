diff --git a/srclib/relocatable.c b/srclib/relocatable.c
index 365c085..4e08fdc 100644
--- a/srclib/relocatable.c
+++ b/srclib/relocatable.c
@@ -338,7 +338,7 @@ DllMain (HINSTANCE module_handle, DWORD event, LPVOID reserved)
       /* The DLL is being loaded into an application's address range.  */
       static char location[MAX_PATH];
 
-      if (!GetModuleFileName (module_handle, location, sizeof (location)))
+      if (!GetModuleFileNameA (module_handle, location, sizeof (location)))
         /* Shouldn't happen.  */
         return FALSE;
 
