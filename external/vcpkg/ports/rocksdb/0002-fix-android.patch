 env/io_posix.h | 2 +-
 1 file changed, 1 insertion(+), 1 deletion(-)

diff --git a/env/io_posix.h b/env/io_posix.h
index 60788df9b..8ddfb3456 100644
--- a/env/io_posix.h
+++ b/env/io_posix.h
@@ -30,7 +30,7 @@
 // For non linux platform, the following macros are used only as place
 // holder.
 #if !(defined OS_LINUX) && !(defined OS_FREEBSD) && !(defined CYGWIN) && \
-    !(defined OS_AIX) && !(defined OS_ANDROID)
+    !(defined OS_AIX) && !(defined OS_ANDROID && __ANDROID_API__ >= 23)
 #define POSIX_FADV_NORMAL 0     /* [MC1] no further special treatment */
 #define POSIX_FADV_RANDOM 1     /* [MC1] expect random page refs */
 #define POSIX_FADV_SEQUENTIAL 2 /* [MC1] expect sequential page refs */
