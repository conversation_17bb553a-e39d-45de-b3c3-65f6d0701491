{"name": "rocksdb", "version": "9.10.0", "port-version": 1, "description": "A library that provides an embeddable, persistent key-value store for fast storage", "homepage": "https://github.com/facebook/rocksdb", "license": "GPL-2.0-only OR Apache-2.0", "supports": "!uwp & !(arm & !arm64 & android)", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "default-features": ["zlib"], "features": {"bzip2": {"description": "build with bzip2", "dependencies": ["bzip2"]}, "liburing": {"description": "build with liburing", "supports": "linux", "dependencies": [{"name": "liburing", "platform": "linux"}]}, "lz4": {"description": "build with lz4", "dependencies": ["lz4"]}, "numa": {"description": "build with NUMA policy support", "supports": "linux", "dependencies": [{"name": "numactl", "platform": "linux"}]}, "snappy": {"description": "build with SNAPPY", "dependencies": ["snappy"]}, "tbb": {"description": "build with Threading Building Blocks (TBB)", "dependencies": ["tbb"]}, "zlib": {"description": "build with zlib", "dependencies": ["zlib"]}, "zstd": {"description": "build with zstd", "dependencies": ["zstd"]}}}