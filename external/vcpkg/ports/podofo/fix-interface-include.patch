diff --git a/src/podofo/CMakeLists.txt b/src/podofo/CMakeLists.txt
index 697e475..6c12036 100644
--- a/src/podofo/CMakeLists.txt
+++ b/src/podofo/CMakeLists.txt
@@ -72,6 +72,7 @@ if(PODOFO_BUILD_STATIC)
     add_library(podofo_static STATIC ${PODOFO_SOURCES})
     add_library(podofo::podofo ALIAS podofo_static)
     target_link_libraries(podofo_static podofo_private ${PODOFO_LIB_DEPENDS})
+    target_include_directories(podofo_static PUBLIC "$<INSTALL_INTERFACE:include>")
     set_target_properties(podofo_static PROPERTIES
         VERSION "${PODOFO_VERSION}"
         SOVERSION "${PODOFO_SOVERSION}"
@@ -96,6 +97,7 @@ if(PODOFO_BUILD_SHARED)
     add_library(podofo_shared SHARED ${PODOFO_SOURCES})
     add_library(podofo::podofo ALIAS podofo_shared)
     target_link_libraries(podofo_shared PRIVATE podofo_private ${PODOFO_LIB_DEPENDS})
+    target_include_directories(podofo_shared PUBLIC "$<INSTALL_INTERFACE:include>")
     # TODO: set /wd4251 flag if we're doing a debug build with
     # Visual Studio, since it produces invalid warnings about STL
     # use.
