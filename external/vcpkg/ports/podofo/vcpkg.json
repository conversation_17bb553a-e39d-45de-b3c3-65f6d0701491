{"name": "podofo", "version": "0.10.4", "description": "PoDoFo is a library to work with the PDF file format", "homepage": "https://github.com/podofo/podofo", "license": "LGPL-2.0-or-later", "supports": "!uwp & !xbox", "dependencies": ["freetype", "libjpeg-turbo", "libpng", {"name": "libxml2", "default-features": false}, "openssl", {"name": "tiff", "default-features": false}, {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}, "zlib"], "default-features": ["fontmanager"], "features": {"fontconfig": {"description": "Use Fontconfig", "dependencies": ["fontconfig"]}, "fontmanager": {"description": "Enable font manager", "dependencies": [{"name": "podofo", "default-features": false, "features": ["fontconfig"], "platform": "!windows"}]}}}