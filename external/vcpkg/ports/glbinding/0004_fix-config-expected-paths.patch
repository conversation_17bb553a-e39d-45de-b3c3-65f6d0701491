diff --git a/glbinding-config.cmake b/glbinding-config.cmake
index 03fc505..01b6cc8 100644
--- a/glbinding-config.cmake
+++ b/glbinding-config.cmake
@@ -1,7 +1,7 @@
 
 # This config script tries to locate the project either in its source tree
 # or from an install location.
-# 
+#
 # Please adjust the list of submodules to search for.
 
 
@@ -35,20 +35,11 @@ endmacro()
 
 # Try install location
 set(MODULE_FOUND FALSE)
-find_modules("cmake")
+find_modules(".")
 
 if(MODULE_FOUND)
     return()
 endif()
 
-# Try common build locations
-if("${CMAKE_BUILD_TYPE}" STREQUAL "Debug")
-    find_modules("build-debug/cmake")
-    find_modules("build/cmake")
-else()
-    find_modules("build/cmake")
-    find_modules("build-debug/cmake")
-endif()
-
 # Signal success/failure to CMake
 set(glbinding_FOUND ${MODULE_FOUND})
