From ad0c57a370a15e8114447b8170ecddf14f21d352 Mon Sep 17 00:00:00 2001
From: <PERSON><PERSON> <<EMAIL>>
Date: Tue, 3 May 2022 20:27:09 +0200
Subject: [PATCH 4/4] Change install dir for pkgconfig

---
 CMakeLists.txt | 2 +-
 1 file changed, 1 insertion(+), 1 deletion(-)

diff --git a/CMakeLists.txt b/CMakeLists.txt
index fd40e68..4f8d7dd 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -172,7 +172,7 @@ if(NOT MSVC)
 	set(PKG_CONFIG_LIBDIR "\${exec_prefix}/lib")
 	set(PKG_CONFIG_INCLUDEDIR "\${prefix}/include")
 	configure_file(freetype-gl.pc.in ${CMAKE_CURRENT_BINARY_DIR}/freetype-gl.pc @ONLY)
-	install(FILES ${CMAKE_CURRENT_BINARY_DIR}/freetype-gl.pc DESTINATION ${PKG_CONFIG_PREFIX}/share/pkgconfig)
+	install(FILES ${CMAKE_CURRENT_BINARY_DIR}/freetype-gl.pc DESTINATION ${PKG_CONFIG_PREFIX}/lib/pkgconfig)
 endif()
 
 if(freetype-gl_BUILD_SHARED)
-- 
2.27.0

