From 9af89db45ef951d5fd8e8be5b4029136f59e94a1 Mon Sep 17 00:00:00 2001
From: <PERSON><PERSON> <<EMAIL>>
Date: Sun, 1 May 2022 22:00:38 +0200
Subject: [PATCH 2/4] Remove duplicate installs

---
 CMakeLists.txt | 11 -----------
 1 <USER> <GROUP>, 11 deletions(-)

diff --git a/CMakeLists.txt b/CMakeLists.txt
index b5db60a..09163ec 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -249,14 +249,3 @@ if ("${LIB64}" STREQUAL "TRUE")
 else()
     set(LIBSUFFIX "")
 endif()
-
-set(INSTALL_LIB_DIR     lib${LIBSUFFIX} CACHE PATH "Installation directory for libraries")
-mark_as_advanced(INSTALL_LIB_DIR)
-
-install(TARGETS freetype-gl
-	ARCHIVE DESTINATION ${INSTALL_LIB_DIR}
-	LIBRARY DESTINATION ${INSTALL_LIB_DIR}
-	COMPONENT library)
-install(FILES ${FREETYPE_GL_HDR} DESTINATION include
-	COMPONENT headers)
-
-- 
2.27.0

