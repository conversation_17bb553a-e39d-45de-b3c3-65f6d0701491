diff --git a/scripts/ud_opcode.py b/scripts/ud_opcode.py
index fe1833d..30c9f43 100644
--- a/scripts/ud_opcode.py
+++ b/scripts/ud_opcode.py
@@ -550,10 +550,10 @@ def printWalk(tbl, indent=""):
             entries = tbl.entries()
             for k, e in entries:
                 if isinstance(e, UdOpcodeTable):
-                    self.log("%s    |-<%02x> %s" % (indent, k, e))
+                    self.log("%s    |-<%02x> %s" % (indent, int(k), e))
                     printWalk(e, indent + "    |")
                 elif isinstance(e, UdInsnDef):
-                    self.log("%s    |-<%02x> %s" % (indent, k, e))
+                    self.log("%s    |-<%02x> %s" % (indent, int(k), e))
         printWalk(self.root)
 
 
