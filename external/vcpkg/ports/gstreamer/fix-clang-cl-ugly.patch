diff --git a/subprojects/gst-plugins-ugly/meson.build b/subprojects/gst-plugins-ugly/meson.build
index 14be48c4c..83d019874 100644
--- a/subprojects/gst-plugins-ugly/meson.build
+++ b/subprojects/gst-plugins-ugly/meson.build
@@ -31,7 +31,7 @@ if have_cxx
   cxx = meson.get_compiler('cpp')
 endif
 
-if cc.get_id() == 'msvc'
+if cc.get_argument_syntax() == 'msvc'
   msvc_args = [
       # Ignore several spurious warnings for things gstreamer does very commonly
       # If a warning is completely useless and spammy, use '/wdXXXX' to suppress it
