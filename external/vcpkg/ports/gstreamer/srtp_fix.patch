diff --git a/subprojects/gst-plugins-bad/ext/srtp/meson.build b/subprojects/gst-plugins-bad/ext/srtp/meson.build
index d15d58c..0a3b1a2 100644
--- a/subprojects/gst-plugins-bad/ext/srtp/meson.build
+++ b/subprojects/gst-plugins-bad/ext/srtp/meson.build
@@ -6,6 +6,8 @@ srtp_sources = [
   'gstsrtpenc.c',
 ]
 
+gst_plugins_install_dir = join_paths(get_option('libdir'), 'gstreamer-1.0')
+
 srtp_cargs = []
 srtp_option = get_option('srtp').enable_if(get_option('webrtc').enabled(), error_message: 'webrtc option is enabled')
 if srtp_option.disabled()
@@ -13,7 +15,7 @@ if srtp_option.disabled()
   subdir_done()
 endif
 
-srtp_dep = dependency('libsrtp2', version : '>= 2.1.0', required : false, allow_fallback: true)
+srtp_dep = dependency('libSRTP', modules: ['libSRTP::srtp2'], version : '>= 2.1.0', required : false)
 if srtp_dep.found()
   srtp_cargs += ['-DHAVE_SRTP2']
 else
@@ -39,7 +41,7 @@ if srtp_dep.found()
     include_directories : [configinc],
     dependencies : [gstrtp_dep, gstvideo_dep, srtp_dep],
     install : true,
-    install_dir : plugins_install_dir,
+    install_dir : gst_plugins_install_dir,
   )
   plugins += [gstsrtp]
 endif
