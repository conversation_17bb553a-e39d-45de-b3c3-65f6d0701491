diff --git a/subprojects/gst-plugins-base/meson.build b/subprojects/gst-plugins-base/meson.build
index 2c09e7b..e51f5a8 100644
--- a/subprojects/gst-plugins-base/meson.build
+++ b/subprojects/gst-plugins-base/meson.build
@@ -412,6 +412,7 @@ int32x4_t testfunc(int16_t *a, int16_t *b) {
 endif
 
 # Disable compiler warnings for unused variables and args if gst debug system is disabled
+build_system = build_machine.system()
 if gst_dep.type_name() == 'internal'
   gst_proj = subproject('gstreamer')
   gst_debug_disabled = not gst_proj.get_variable('gst_debug')
@@ -421,7 +422,7 @@ else
   gst_debug_disabled = cc.has_header_symbol('gst/gstconfig.h', 'GST_DISABLE_GST_DEBUG', dependencies: gst_dep)
 endif
 
-if gst_debug_disabled
+if gst_debug_disabled and build_system != 'windows'
   message('GStreamer debug system is disabled')
   if cc.get_argument_syntax() == 'msvc'
     msvc_args = cc.get_supported_arguments([
