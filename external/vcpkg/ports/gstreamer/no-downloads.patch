diff --git a/meson.build b/meson.build
index 3815aa3..6aeeab4 100644
--- a/meson.build
+++ b/meson.build
@@ -125,20 +125,7 @@ subprojects = [
 ]

 if get_option('build-tools-source') == 'subproject'
-  if build_system == 'windows'
-    subproject('win-flex-bison-binaries')
-    subproject('win-nasm')
-    subproject('win-pkgconfig')
-  elif build_system == 'darwin'
-    subproject('macos-bison-binary')
-    # Newer macOS provides /usr/lib/pkgconfig/libpcre2-8.pc which is broken
-    # because it says headers are in /usr/include but that directory doesn't
-    # exist. It can only be used to find the library, which only exists on
-    # newer macOS at /usr/lib/libpcre2-8.dylib, so it's also unusable.
-    #
-    # jit support requires macOS 11.0 or newer, so disable it by default
-    subproject('pcre2', default_options: ['default_library=static', 'jit=disabled'])
-  endif
+
 endif

 orc_option = get_option('orc')
