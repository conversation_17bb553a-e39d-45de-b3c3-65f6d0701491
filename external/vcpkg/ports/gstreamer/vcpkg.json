{"name": "gstreamer", "version": "1.24.12", "description": "GStreamer open-source multimedia framework core library", "homepage": "https://gstreamer.freedesktop.org/", "license": "LGPL-2.0-only", "supports": "!uwp & !xbox", "dependencies": ["glib", {"name": "glib", "host": true}, {"name": "opengl", "platform": "windows | osx"}, {"name": "vcpkg-tool-meson", "host": true}], "default-features": ["plugins-base"], "features": {"aes": {"description": "Enable support for AES encryption/decryption", "dependencies": [{"name": "gstreamer", "default-features": false, "features": ["plugins-bad"]}, "openssl"]}, "alsa": {"description": "Enable support for ALSA (Advanced Linux Sound Architecture)", "supports": "linux", "dependencies": [{"name": "alsa", "platform": "linux"}, {"name": "gstreamer", "default-features": false, "features": ["plugins-base"]}]}, "aom": {"description": "Enable support for the Alliance for Open Media (AOM) AV1 encoder and decoder", "supports": "!windows", "dependencies": ["aom", {"name": "gstreamer", "default-features": false, "features": ["plugins-bad"]}]}, "asio": {"description": "Enable support for the Steinberg Audio Streaming Input Output (ASIO) library (Windows only)", "dependencies": [{"name": "asiosdk", "platform": "windows"}, {"name": "gstreamer", "default-features": false, "features": ["plugins-bad"]}]}, "assrender": {"description": "Enable support for the ASS/SSA subtitle renderer", "dependencies": [{"name": "gstreamer", "default-features": false, "features": ["plugins-bad"]}, "libass"]}, "bzip2-bad": {"description": "Enable bzip2 stream compression in bad plugins", "dependencies": ["bzip2", {"name": "gstreamer", "default-features": false, "features": ["plugins-bad"]}]}, "bzip2-good": {"description": "Enable bzip2 stream compression in good plugins", "dependencies": ["bzip2", {"name": "gstreamer", "default-features": false, "features": ["plugins-good"]}]}, "cairo": {"description": "Enable support for the cairo graphics library", "dependencies": [{"name": "cairo", "features": ["gobject"]}, {"name": "gstreamer", "default-features": false, "features": ["plugins-good"]}]}, "chromaprint": {"description": "Enable support for the Chromaprint audio fingerprint library", "dependencies": ["chromaprint", {"name": "gstreamer", "default-features": false, "features": ["plugins-bad"]}]}, "closedcaption": {"description": "Enable support for the closed caption extractor, decoder, and overlay", "dependencies": [{"name": "gstreamer", "default-features": false, "features": ["plugins-bad"]}, "pango"]}, "colormanagement": {"description": "Enable support for the color management correction", "dependencies": [{"name": "gstreamer", "default-features": false, "features": ["plugins-bad"]}, "lcms"]}, "dash": {"description": "Enable support for the DASH demuxer", "dependencies": [{"name": "gstreamer", "default-features": false, "features": ["plugins-bad"]}, "libxml2"]}, "dc1394": {"description": "Enable support for the libdc1394 IIDC camera source", "supports": "!windows", "dependencies": [{"name": "gstreamer", "default-features": false, "features": ["plugins-bad"]}, "libdc1394"]}, "dtls": {"description": "Enable support for the DTLS encoder and decoder", "dependencies": [{"name": "gstreamer", "default-features": false, "features": ["plugins-bad"]}, "openssl"]}, "faad": {"description": "Enable support for the free AAC audio decoder (GPL licensed)", "dependencies": ["faad2", {"name": "gstreamer", "default-features": false, "features": ["gpl", "plugins-bad"]}]}, "fdkaac": {"description": "Enable support for the Fraunhofer AAC audio codec", "dependencies": ["fdk-aac", {"name": "gstreamer", "default-features": false, "features": ["plugins-bad"]}]}, "flac": {"description": "Enable support for FLAC: Free Lossless Audio Codec", "dependencies": [{"name": "gstreamer", "default-features": false, "features": ["ogg", "plugins-good"]}, "libflac"]}, "fluidsynth": {"description": "Enable support for the Fluidsynth MIDI decoder", "supports": "!windows", "dependencies": [{"name": "fluidsynth", "platform": "!windows"}, {"name": "gstreamer", "default-features": false, "features": ["plugins-bad"]}]}, "gdk-pixbuf": {"description": "Enable support for gdk-pixbuf image loader", "dependencies": ["gdk-pixbuf", {"name": "gstreamer", "default-features": false, "features": ["plugins-good"]}]}, "ges": {"description": "Enable support for GStreamer Editing Services", "dependencies": [{"name": "gstreamer", "default-features": false, "features": ["plugins-base"]}]}, "gl-graphene": {"description": "Use Graphene in OpenGL plugin", "dependencies": ["graphene", {"name": "gstreamer", "default-features": false, "features": ["plugins-base"]}]}, "gpl": {"description": "Allow build of plugins that have (A)GPL-licensed dependencies", "dependencies": [{"name": "gstreamer", "default-features": false, "features": ["plugins-base"]}]}, "jpeg": {"description": "Enable support for the JPEG file format", "dependencies": [{"name": "gstreamer", "default-features": false, "features": ["plugins-base", "plugins-good"]}, "libjpeg-turbo"]}, "libav": {"description": "libav plugins", "dependencies": [{"name": "ffmpeg", "default-features": false}, {"name": "gstreamer", "default-features": false, "features": ["plugins-base"]}]}, "libde265": {"description": "Enable support for the HEVC/H.265 video decoder", "dependencies": [{"name": "gstreamer", "default-features": false, "features": ["plugins-bad"]}, "libde265"]}, "microdns": {"description": "Enable support for the microdns device provider", "dependencies": [{"name": "gstreamer", "default-features": false, "features": ["plugins-bad"]}, "libmicrodns"]}, "modplug": {"description": "Enable support for the ModPlug audio decoder", "supports": "!uwp", "dependencies": [{"name": "gstreamer", "default-features": false, "features": ["plugins-bad"]}, {"name": "libmodplug", "platform": "!uwp"}]}, "mpg123": {"description": "Enable support for the MPG123 decoding library", "dependencies": [{"name": "gstreamer", "default-features": false, "features": ["plugins-good"]}, "mpg123"]}, "nls": {"description": "National language support", "dependencies": [{"name": "gettext", "host": true, "default-features": false, "features": ["tools"]}, "gettext-libintl", {"name": "gstreamer", "default-features": false, "features": ["plugins-base"]}]}, "nvcodec": {"description": "Enable support for the NVCODEC encoders and decoders", "supports": "!osx & !ios & !android & !emscripten", "dependencies": [{"name": "gstreamer", "default-features": false, "features": ["plugins-bad"]}]}, "ogg": {"description": "Enable support for the Ogg container format (commonly used by Vorbis, Theora and flac)", "dependencies": [{"name": "gstreamer", "default-features": false, "features": ["plugins-base"]}, "libogg"]}, "openal": {"description": "Enable support for the OpenAL audio library", "dependencies": [{"name": "gstreamer", "default-features": false, "features": ["plugins-bad"]}, "openal-soft"]}, "openh264": {"description": "Enable support for the OpenH264 codec", "dependencies": [{"name": "gstreamer", "default-features": false, "features": ["plugins-bad"]}, "openh264"]}, "openjpeg": {"description": "Enable support for the JPEG2000 codec", "dependencies": [{"name": "gstreamer", "default-features": false, "features": ["plugins-bad"]}, "openjpeg"]}, "openmpt": {"description": "Enable support for the OpenMPT codec", "dependencies": [{"name": "gstreamer", "default-features": false, "features": ["plugins-bad"]}, "libopenmpt"]}, "opus-bad": {"description": "Enable support for the Opus codec in bad plugins", "dependencies": [{"name": "gstreamer", "default-features": false, "features": ["plugins-bad"]}, "opus"]}, "opus-base": {"description": "Enable support for the Opus codec in base plugins", "dependencies": [{"name": "gstreamer", "default-features": false, "features": ["plugins-base"]}, "opus"]}, "pango": {"description": "Enable support for pango font rendering", "dependencies": [{"name": "gstreamer", "default-features": false, "features": ["plugins-base"]}, "pango"]}, "plugins-bad": {"description": "'Bad' GStreamer plugins and helper libraries", "dependencies": [{"name": "gstreamer", "default-features": false, "features": ["plugins-base"]}]}, "plugins-base": {"description": "'Base' GStreamer plugins and helper libraries", "dependencies": ["zlib"]}, "plugins-good": {"description": "'Good' GStreamer plugins and helper libraries", "dependencies": [{"name": "gstreamer", "default-features": false, "features": ["plugins-base"]}, "zlib"]}, "plugins-ugly": {"description": "'Ugly' GStreamer plugins and helper libraries", "dependencies": [{"name": "gstreamer", "default-features": false, "features": ["plugins-base"]}]}, "png": {"description": "Enable support for the PNG image format", "dependencies": [{"name": "gstreamer", "default-features": false, "features": ["plugins-good"]}, "libpng"]}, "smoothstreaming": {"description": "Enable support for the Microsoft Smooth Streaming format", "dependencies": [{"name": "gstreamer", "default-features": false, "features": ["plugins-bad"]}, "libxml2"]}, "sndfile": {"description": "Enable support for the SndFile file reader/writer", "dependencies": [{"name": "gstreamer", "default-features": false, "features": ["plugins-bad"]}, "libsndfile"]}, "soundtouch": {"description": "Enable support for the SoundTouch audio processing library", "dependencies": [{"name": "gstreamer", "default-features": false, "features": ["plugins-bad"]}, "soundtouch"]}, "soup": {"description": "Enable support for the soup plugin", "dependencies": [{"name": "gstreamer", "default-features": false, "features": ["plugins-good"]}, "lib<PERSON><PERSON>"]}, "speex": {"description": "Enable support for the speex codec", "dependencies": [{"name": "gstreamer", "default-features": false, "features": ["plugins-good"]}, "speex"]}, "srt": {"description": "Enable support for the SRT protocol", "dependencies": [{"name": "gstreamer", "default-features": false, "features": ["plugins-bad"]}, "libsrt"]}, "srtp": {"description": "Enable support for the SRTP protocol", "dependencies": [{"name": "gstreamer", "default-features": false, "features": ["plugins-bad"]}, "libsrtp"]}, "taglib": {"description": "Enable support for the taglib library", "dependencies": [{"name": "gstreamer", "default-features": false, "features": ["plugins-good"]}, "taglib"]}, "vorbis": {"description": "Enable support for the OggVorbis audio codec", "dependencies": [{"name": "gstreamer", "default-features": false, "features": ["ogg", "plugins-base"]}, "libvorbis"]}, "vpx": {"description": "Enable support for the VP8 and VP9 codecs", "dependencies": [{"name": "gstreamer", "default-features": false, "features": ["plugins-good"]}, "libvpx"]}, "webp": {"description": "Enable support for WebP image format", "dependencies": [{"name": "gstreamer", "default-features": false, "features": ["plugins-bad"]}, "libwebp"]}, "webrtc": {"description": "Enable support for WebRTC", "dependencies": [{"name": "gstreamer", "default-features": false, "features": ["plugins-bad"]}, "libnice"]}, "wildmidi": {"description": "Enable support for the WildMIDI synthesizer", "dependencies": [{"name": "gstreamer", "default-features": false, "features": ["plugins-bad"]}, "<PERSON><PERSON><PERSON>"]}, "x11-bad": {"description": "Enable support for X11 in bad plugins", "supports": "!windows", "dependencies": [{"name": "gstreamer", "default-features": false, "features": ["plugins-bad"]}, "libxkbcommon", "xcb"]}, "x11-base": {"description": "Enable support for X11 in base plugins", "supports": "!windows", "dependencies": [{"name": "gstreamer", "default-features": false, "features": ["plugins-good"]}, "libx11", "libxext"]}, "x264": {"description": "Enable support for the x264 encoder (GPL license)", "dependencies": [{"name": "gstreamer", "default-features": false, "features": ["gpl", "plugins-ugly"]}, "x264"]}, "x265": {"description": "Enable support for the x265 encoder (GPL license)", "dependencies": [{"name": "gstreamer", "default-features": false, "features": ["gpl", "plugins-bad"]}, "x265"]}}}