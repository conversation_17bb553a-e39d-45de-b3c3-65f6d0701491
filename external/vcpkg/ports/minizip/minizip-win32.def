EXPORTS
     fill_win32_filefunc
     fill_win32_filefunc64
     fill_win32_filefunc64A
     fill_win32_filefunc64W
     win32_open64_file_func
     win32_open64_file_funcA
     win32_open64_file_funcW
     win32_open_file_func
     win32_read_file_func
     win32_write_file_func
     win32_tell_file_func
     win32_tell64_file_func
     win32_seek_file_func
     win32_seek64_file_func
     win32_close_file_func
     win32_error_file_func
     unzRepair
     zip_copyright
     zipOpen
     zipOpen64
     zipOpen2
     zipOpen2_64
     zipOpen3
     zipOpenNewFileInZip
     zipOpenNewFileInZip64
     zipOpenNewFileInZip2
     zipOpenNewFileInZip2_64
     zipOpenNewFileInZip3
     zipOpenNewFileInZip3_64
     zipOpenNewFileInZip4
     zipOpenNewFileInZip4_64
     zipWriteInFileInZip
     zipCloseFileInZip
     zipCloseFileInZipRaw
     zipCloseFileInZipRaw64
     zipClose
     zipRemoveExtraInfoBlock
     unz_copyright
     unzStringFileNameCompare
     unzOpen
     unzOpen64
     unzOpen2
     unzOpen2_64
     unzClose
     unzGetGlobalInfo
     unzGetGlobalInfo64
     unzGetGlobalComment
     unzGoToFirstFile
     unzGoToNextFile
     unzLocateFile
     unzGetFilePos
     unzGoToFilePos
     unzGetFilePos64
     unzGoToFilePos64
     unzGetCurrentFileInfo64
     unzGetCurrentFileInfo
     unzGetCurrentFileZStreamPos64
     unzOpenCurrentFile
     unzOpenCurrentFilePassword
     unzOpenCurrentFile2
     unzOpenCurrentFile3
     unzCloseCurrentFile
     unzReadCurrentFile
     unztell
     unztell64
     unzeof
     unzGetLocalExtrafield
     unzGetOffset64
     unzGetOffset
     unzSetOffset64
     unzSetOffset
     fill_fopen64_filefunc
     fill_fopen_filefunc
     call_zopen64
     call_zseek64
     call_ztell64
     fill_zlib_filefunc64_32_def_from_filefunc32
