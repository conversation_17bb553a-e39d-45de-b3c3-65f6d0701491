message(WARNING "'find_package(minizip CONFIG)' is deprecated. Please use 'find_package(unofficial-minizip CONFIG)' instead.")

include(CMakeFindDependencyMacro)
find_dependency(unofficial-minizip ${${CMAKE_FIND_PACKAGE_NAME}_FIND_VERSION} CONFIG)
if(NOT TARGET unofficial::minizip::minizip)
    set(${CMAKE_FIND_PACKAGE_NAME}_FOUND FALSE)
elseif(NOT TARGET minizip::minizip)
    add_library(minizip::minizip INTERFACE IMPORTED)
    set_target_properties(minizip::minizip PROPERTIES INTERFACE_LINK_LIBRARIES unofficial::minizip::minizip)
endif()
