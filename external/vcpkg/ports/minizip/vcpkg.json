{"name": "minizip", "version": "1.3.1", "port-version": 1, "description": "Minizip zip file manipulation library", "homepage": "https://github.com/madler/zlib", "license": "<PERSON><PERSON><PERSON>", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}, {"name": "vcpkg-cmake-get-vars", "host": true}, "zlib"], "features": {"bzip2": {"description": "Support compression using bzip2 library", "dependencies": ["bzip2"]}, "tools": {"description": "Install tools", "supports": "!ios"}}}