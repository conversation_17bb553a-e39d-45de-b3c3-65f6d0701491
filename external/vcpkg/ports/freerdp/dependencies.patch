diff --git a/cmake/FindFeature.cmake b/cmake/FindFeature.cmake
index bd25fd4..dd180a3 100644
--- a/cmake/FindFeature.cmake
+++ b/cmake/FindFeature.cmake
@@ -15,7 +15,7 @@ macro(find_feature _feature _type _purpose _description)
 			set(_feature_default "ON")
 			message(STATUS "Finding ${_type_lower} feature ${_feature} for ${_purpose} (${_description})")
 			find_package(${_feature} REQUIRED)
-		elseif(${_type} STREQUAL "RECOMMENDED")
+		elseif(0)
 			if(NOT ${WITH_${_feature_upper}})
 				set(_feature_default "OFF")
 				message(STATUS "Skipping ${_type_lower} feature ${_feature} for ${_purpose} (${_description})")
@@ -29,7 +29,7 @@ macro(find_feature _feature _type _purpose _description)
 					message(STATUS "Not detected ${_type_lower} feature ${_feature} for ${_purpose} (${_description}), feature disabled")
 				endif()
 			endif()
-		elseif(${_type} STREQUAL "OPTIONAL")
+		elseif(1)
 			if(${WITH_${_feature_upper}})
 				set(_feature_default "ON")
 				message(STATUS "Finding ${_type_lower} feature ${_feature} for ${_purpose} (${_description})")
