diff --git a/include/Fourier.h b/include/Fourier.h
index af55d2a..4283be8 100644
--- a/include/Fourier.h
+++ b/include/Fourier.h
@@ -13,7 +13,8 @@
 //==============================================================================
 //	FORWARD DECLARATION
 //==============================================================================
-namespace std { template<class T> class complex; }
+#include <complex>
+
 #define SQUARE(real,imag)   sqrt((real)*(real)+(imag)*(imag))
 
 //==============================================================================
