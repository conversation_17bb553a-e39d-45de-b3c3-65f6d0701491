diff --git a/CMakeLists.txt b/CMakeLists.txt
index ba6f117..8d2e9f9 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -424,7 +424,7 @@ if((DEFINED JAS_CROSSCOMPILING AND JAS_CROSSCOMPILING) OR
 	# (e.g., using -DJAS_STDC_VERSION=YYYYMML) or by changing the line below.
 if(NOT JAS_WASM)
 	set(JAS_STDC_VERSION "0L" CACHE INTERNAL "The value of __STDC_VERSION__.")
-	if (JAS_STDC_VERSION STREQUAL "0L")
+	if (0)
 		message(FATAL_ERROR
 		  "The value of __STDC_VERSION__ cannot be automatically determined "
 		  "when cross-compiling.  Please set JAS_STDC_VERSION to the value "
@@ -434,8 +434,9 @@ if(NOT JAS_WASM)
 	endif()
 endif()
 else()
-	jas_get_stdc_version(status JAS_STDC_VERSION)
-	if(NOT status)
+	#jas_get_stdc_version(status JAS_STDC_VERSION)
+	set(JAS_STDC_VERSION 0L)
+	if(0)
 		message(FATAL_ERROR "Cannot determine the value of __STDC_VERSION__.")
 	endif()
 endif()
