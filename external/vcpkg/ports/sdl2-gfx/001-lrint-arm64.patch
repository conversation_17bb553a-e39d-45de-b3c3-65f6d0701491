diff --git a/SDL2_gfxPrimitives.c b/SDL2_gfxPrimitives.c
index f81e6da..91817fd 100755
--- a/SDL2_gfxPrimitives.c
+++ b/SDL2_gfxPrimitives.c
@@ -1771,8 +1771,20 @@ __declspec(naked) long int
 	__emit(0xE12FFF1E); // bx     lr
 }
 #pragma warning(pop)
+#elif defined(_M_ARM64)
+#include <intrin.h>
+#pragma warning(push)
+#pragma warning(disable: 4716)
+__declspec(naked) long
+	lrint (double f)
+{
+	__emit(0x1e674000); // frintx d0, d0
+	__emit(0x9e780000); // fcvtzs x0, d0
+	__emit(0xd65f03c0); // ret
+}
+#pragma warning(pop)
 #else
-#error lrint needed for MSVC on non X86/AMD64/ARM targets.
+#error lrint needed for MSVC on non X86/AMD64/ARM/ARM64 targets.
 #endif
 #endif
 
