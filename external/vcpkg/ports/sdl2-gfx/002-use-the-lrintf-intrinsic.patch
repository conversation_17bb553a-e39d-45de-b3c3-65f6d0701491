diff --git a/SDL2_gfxPrimitives.c b/SDL2_gfxPrimitives.c
index d8afb10..6c88288 100644
--- a/SDL2_gfxPrimitives.c
+++ b/SDL2_gfxPrimitives.c
@@ -1757,7 +1757,8 @@ int filledCircleRGBA(SDL_Renderer * renderer, Sint16 x, Sint16 y, Sint16 rad, Ui
 /* ----- AA Ellipse */
 
 /* Windows targets do not have lrint, so provide a local inline version */
-#if defined(_MSC_VER)
+/* MSVC pre 16.8 do not have lrint */
+#if defined(_MSC_VER) && _MSC_VER < 1928
 /* Detect 64bit and use intrinsic version */
 #ifdef _M_X64
 #include <emmintrin.h>
