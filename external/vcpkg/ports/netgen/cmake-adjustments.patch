diff --git a/libsrc/core/CMakeLists.txt b/libsrc/core/CMakeLists.txt
index c4f4795..cd5ad50 100644
--- a/libsrc/core/CMakeLists.txt	
+++ b/libsrc/core/CMakeLists.txt
@@ -28,8 +28,7 @@ endif(USE_PYTHON)
 
 if(WIN32)
   target_compile_options(ngcore PUBLIC /bigobj /MP /W1 /wd4068)
-  get_WIN32_WINNT(ver)
-  target_compile_definitions(ngcore PUBLIC _WIN32_WINNT=${ver} WNT WNT_WINDOW NOMINMAX MSVC_EXPRESS _CRT_SECURE_NO_WARNINGS HAVE_STRUCT_TIMESPEC WIN32)
+  target_compile_definitions(ngcore PUBLIC WNT WNT_WINDOW NOMINMAX MSVC_EXPRESS _CRT_SECURE_NO_WARNINGS HAVE_STRUCT_TIMESPEC WIN32)
   target_link_options(ngcore PUBLIC /ignore:4273 /ignore:4217 /ignore:4049)
 endif(WIN32)
 
