diff --git a/cmake/generate_version_file.cmake b/cmake/generate_version_file.cmake
index c4a579d1..c47b6039 100644
--- a/cmake/generate_version_file.cmake
+++ b/cmake/generate_version_file.cmake
@@ -39,7 +39,7 @@ string(REGEX REPLACE "^v[0-9]+\\.[0-9]+\\.[0-9]+\\-[0-9]+\\-([0-9a-z]+).*" "\\1"
 set(NETGEN_VERSION_SHORT ${NETGEN_VERSION_MAJOR}.${NETGEN_VERSION_MINOR}.${NETGEN_VERSION_PATCH})
 set(NETGEN_VERSION_LONG ${NETGEN_VERSION_SHORT}-${NETGEN_VERSION_TWEAK}-${NETGEN_VERSION_HASH})
 
-if(NETGEN_VERSION_TWEAK)
+if(NETGEN_VERSION_TWEAK AND NOT NETGEN_VERSION_TWEAK STREQUAL git_version_string)
   # no release version - nightly build
   set(NETGEN_VERSION ${NETGEN_VERSION_LONG})
 else()
