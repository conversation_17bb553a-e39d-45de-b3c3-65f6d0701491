diff --git a/CMakeLists.txt b/CMakeLists.txt
index 0846f39bd..f7516afa4 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -372,25 +372,20 @@ if (USE_OCC)
       TKGeomAlgo
       TKGeomBase
       TKHLR
-      TKIGES
       TKLCAF
       TKMath
       TKMesh
       TKOffset
       TKPrim
-      TKSTEP
-      TKSTEP209
-      TKSTEPAttr
-      TKSTEPBase
-      TKSTL
+      TKDESTL
       TKService
       TKShHealing
       TKTopAlgo
       TKV3d
       TKVCAF
       TKXCAF
-      TKXDEIGES
-      TKXDESTEP
+      TKDEIGES
+      TKDESTEP
       TKXSBase
       TKernel
     )
diff --git a/libsrc/occ/Partition_Loop3d.hxx b/libsrc/occ/Partition_Loop3d.hxx
index e1716691c..e8a434911 100644
--- a/libsrc/occ/Partition_Loop3d.hxx
+++ b/libsrc/occ/Partition_Loop3d.hxx
@@ -10,27 +10,16 @@
 #ifndef _Partition_Loop3d_HeaderFile
 #define _Partition_Loop3d_HeaderFile
 
-#ifndef _TopTools_ListOfShape_HeaderFile
-#include <TopTools_ListOfShape.hxx>
-#endif
-#ifndef _TopTools_IndexedDataMapOfShapeListOfShape_HeaderFile
-#include <TopTools_IndexedDataMapOfShapeListOfShape.hxx>
-#endif
-#ifndef _Standard_Boolean_HeaderFile
+#include <Standard_Version.hxx>
 #include <Standard_Boolean.hxx>
-#endif
-#ifndef _Standard_Real_HeaderFile
 #include <Standard_Real.hxx>
-#endif
-#ifndef _Standard_Version_HeaderFile
-#include <Standard_Version.hxx>
-#endif
+#include <TopTools_ListOfShape.hxx>
+#include <TopTools_IndexedDataMapOfShapeListOfShape.hxx>
 
-#if OCC_VERSION_HEX < 0x070000
+#if OCC_VERSION_HEX < 0x070000 || OCC_VERSION_HEX > 0x070799
 #else
    #include <TopTools_ShapeMapHasher.hxx>
    #include <TopTools_OrientedShapeMapHasher.hxx>
-   #include <TopTools_MapOfOrientedShape.hxx>
 #endif
 
 class TopoDS_Shape;
@@ -38,6 +27,8 @@ class TopoDS_Shape;
 #if OCC_VERSION_HEX < 0x070000
    class TopTools_ListOfShape;
    class TopTools_MapOfOrientedShape;
+#else
+#include <TopTools_MapOfOrientedShape.hxx>
 #endif
 
 class TopoDS_Edge;
diff --git a/libsrc/occ/occ_edge.cpp b/libsrc/occ/occ_edge.cpp
index 0c907d78b..fd64c9a42 100644
--- a/libsrc/occ/occ_edge.cpp
+++ b/libsrc/occ/occ_edge.cpp
@@ -55,7 +55,11 @@ namespace netgen
 
     size_t OCCEdge::GetHash() const
     {
+#if OCC_VERSION_HEX < 0x070800
       return edge.HashCode(std::numeric_limits<Standard_Integer>::max());
+#else
+      return std::hash<TopoDS_Shape>{}(edge); 
+#endif
     }
 
     void OCCEdge::ProjectPoint(Point<3>& p, EdgePointGeomInfo* gi) const
diff --git a/libsrc/occ/occ_face.cpp b/libsrc/occ/occ_face.cpp
index 239982aa8..ea4dd15b4 100644
--- a/libsrc/occ/occ_face.cpp
+++ b/libsrc/occ/occ_face.cpp
@@ -32,7 +32,11 @@ namespace netgen
 
     size_t OCCFace::GetHash() const
     {
+#if OCC_VERSION_HEX < 0x070800
       return face.HashCode(std::numeric_limits<Standard_Integer>::max());
+#else
+      return std::hash<TopoDS_Shape>{}(face); 
+#endif
     }
 
     Point<3> OCCFace::GetCenter() const
diff --git a/libsrc/occ/occ_solid.hpp b/libsrc/occ/occ_solid.hpp
index d598de4a2..66f28d73a 100644
--- a/libsrc/occ/occ_solid.hpp
+++ b/libsrc/occ/occ_solid.hpp
@@ -16,8 +16,11 @@ namespace netgen
         OCCSolid(TopoDS_Shape dshape)
             : solid(TopoDS::Solid(dshape))
         { }
-
+#if OCC_VERSION_HEX < 0x070800
         size_t GetHash() const override { return solid.HashCode(std::numeric_limits<Standard_Integer>::max()); }
+#else
+        size_t GetHash() const override { return std::hash<TopoDS_Solid>{}(solid); }
+#endif
     };
 }
 
diff --git a/libsrc/occ/occ_vertex.cpp b/libsrc/occ/occ_vertex.cpp
index 6e83c8944..be8e38732 100644
--- a/libsrc/occ/occ_vertex.cpp
+++ b/libsrc/occ/occ_vertex.cpp
@@ -19,6 +19,10 @@ namespace netgen
 
     size_t OCCVertex::GetHash() const
     {
+#if OCC_VERSION_HEX < 0x070800
       return vertex.HashCode(std::numeric_limits<Standard_Integer>::max());
+#else
+      return std::hash<TopoDS_Shape>{}(vertex); 
+#endif
     }
 }
diff --git a/libsrc/occ/occgeom.cpp b/libsrc/occ/occgeom.cpp
index bc0383f99..826134ba0 100644
--- a/libsrc/occ/occgeom.cpp
+++ b/libsrc/occ/occgeom.cpp
@@ -1716,9 +1716,13 @@ namespace netgen
     /*
     // enumerate shapes and archive only integers
     auto my_hash = [](const TopoDS_Shape & key) {
+#if OCC_VERSION_HEX < 0x070800
         auto occ_hash = key.HashCode(1<<31UL);
         return std::hash<decltype(occ_hash)>()(occ_hash);
+#else
+        return std::hash<TopoDS_Shape>{}(key);
+#endif
     };
     */
     TopTools_IndexedMapOfShape shape_map;
     Array<TopoDS_Shape> shape_list;
