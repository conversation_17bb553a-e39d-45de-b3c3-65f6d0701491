{"name": "cpu-features", "version": "0.9.0", "description": "A cross-platform C library to retrieve CPU features (such as available instructions) at runtime", "homepage": "https://github.com/google/cpu_features", "license": "Apache-2.0", "supports": "!uwp", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"tools": {"description": "list_cpu_features command line tool"}}}