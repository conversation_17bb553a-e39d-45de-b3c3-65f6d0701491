{"name": "lcms", "version": "2.16", "description": "Little CMS.", "homepage": "https://github.com/mm2/Little-CMS", "license": "MIT", "dependencies": [{"name": "vcpkg-tool-meson", "host": true}], "features": {"fastfloat": {"description": "Build the fast float plugin", "supports": "!(x86 & windows)", "license": "GPL-3.0-or-later"}, "threaded": {"description": "Build the multi threaded plugin", "license": "GPL-3.0-or-later"}, "tools": {"description": "Build the utilities", "dependencies": ["libjpeg-turbo", {"name": "tiff", "default-features": false}]}}}