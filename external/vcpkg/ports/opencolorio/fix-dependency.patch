diff --git a/share/cmake/modules/FindExtPackages.cmake b/share/cmake/modules/FindExtPackages.cmake
index 5455a08..3b9530e 100644
--- a/share/cmake/modules/FindExtPackages.cmake
+++ b/share/cmake/modules/FindExtPackages.cmake
@@ -37,20 +37,20 @@ endif()
 
 # expat
 # https://github.com/libexpat/libexpat
-find_package(expat 2.4.1 REQUIRED)
+find_package(expat CONFIG REQUIRED)
 
 # yaml-cpp
 # https://github.com/jbeder/yaml-cpp
-find_package(yaml-cpp 0.7.0 REQUIRED)
+find_package(yaml-cpp CONFIG REQUIRED)
 
 # pystring
 # https://github.com/imageworks/pystring
-find_package(pystring 1.1.3 REQUIRED)
+find_package(pystring CONFIG REQUIRED)
 
 # Imath (>=3.1)
 # https://github.com/AcademySoftwareFoundation/Imath
 set(_Imath_ExternalProject_VERSION "3.1.5")
-find_package(Imath 3.0 REQUIRED)
+find_package(Imath CONFIG REQUIRED)
 
 ###############################################################################
 ### ZLIB (https://github.com/madler/zlib)
@@ -138,7 +138,7 @@ endif()
 
 # minizip-ng
 # https://github.com/zlib-ng/minizip-ng
-find_package(minizip-ng 3.0.7 REQUIRED)
+find_package(minizip-ng CONFIG REQUIRED)
 
 if(OCIO_BUILD_APPS)
 
@@ -149,7 +149,7 @@ if(OCIO_BUILD_APPS)
 
     # lcms2
     # https://github.com/mm2/Little-CMS
-    find_package(lcms2 2.2 REQUIRED)
+    find_package(lcms2 CONFIG REQUIRED)
 endif()
 
 if(OCIO_BUILD_OPENFX)
diff --git a/src/cmake/Config.cmake.in b/src/cmake/Config.cmake.in
index 6a4932a..0fe7c89 100644
--- a/src/cmake/Config.cmake.in
+++ b/src/cmake/Config.cmake.in
@@ -1,6 +1,11 @@
 @PACKAGE_INIT@
 
 include(CMakeFindDependencyMacro)
+find_dependency(expat CONFIG)
+find_dependency(Imath CONFIG)
+find_dependency(pystring CONFIG)
+find_dependency(yaml-cpp CONFIG)
+find_dependency(minizip-ng CONFIG)
 
 if (NOT @BUILD_SHARED_LIBS@) # NOT @BUILD_SHARED_LIBS@
     if (APPLE)
@@ -50,7 +55,8 @@ if (NOT @BUILD_SHARED_LIBS@) # NOT @BUILD_SHARED_LIBS@
         find_dependency(yaml-cpp @yaml-cpp_VERSION@)
     endif()
 
-    if (NOT ZLIB::ZLIB)
+    find_dependency(ZLIB)
+    if (NOT TARGET ZLIB::ZLIB)
         # ZLIB_VERSION is available starting CMake 3.26+.
         # ZLIB_VERSION_STRING is still available for backward compatibility.
         # See https://cmake.org/cmake/help/git-stage/module/FindZLIB.html
