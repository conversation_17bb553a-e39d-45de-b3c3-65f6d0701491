{"name": "opencolorio", "version-semver": "2.2.1", "port-version": 3, "description": "OpenColorIO (OCIO) is a complete color management solution geared towards motion picture production with an emphasis on visual effects and computer animation. OCIO provides a straightforward and consistent user experience across all supporting applications while allowing for sophisticated back-end configuration options suitable for high-end production usage. OCIO is compatible with the Academy Color Encoding Specification (ACES) and is LUT-format agnostic, supporting many popular formats.", "homepage": "https://opencolorio.org/", "license": "BSD-3-<PERSON><PERSON>", "supports": "!uwp", "dependencies": ["expat", "imath", "minizip-ng", "pystring", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}, "yaml-cpp"], "features": {"tools": {"description": "Installs tools", "dependencies": ["glew", "lcms", "openexr"]}}}