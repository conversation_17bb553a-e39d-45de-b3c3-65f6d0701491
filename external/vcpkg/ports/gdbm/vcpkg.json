{"name": "gdbm", "version": "1.24", "description": "GDBM is a library of database functions that use extensible hashing and works similar to the standard UNIX dbm.", "homepage": "https://www.gnu.org.ua/software/gdbm/gdbm.html", "license": "GPL-3.0-only", "supports": "linux", "dependencies": [{"name": "vcpkg-make", "host": true}], "features": {"libgdbm-compat": {"description": "Build and install libgdbm_compat, a compatibility layer which provides UNIX-like dbm and ndbm interfaces."}, "memory-mapped-io": {"description": "Enable the use of mmap(2) for I/O optimizations."}, "readline": {"description": "Enable GNU Readline support.", "dependencies": ["readline"]}}}