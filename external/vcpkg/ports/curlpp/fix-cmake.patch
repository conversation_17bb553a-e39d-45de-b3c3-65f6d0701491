diff --git a/CMakeLists.txt b/CMakeLists.txt
index 8b183a0..a801ae8 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -85,11 +85,12 @@ endif()
 
 file(GLOB_RECURSE HeaderFileList "${CMAKE_CURRENT_SOURCE_DIR}/include/*")
 file(GLOB_RECURSE SourceFileList "${CMAKE_CURRENT_SOURCE_DIR}/src/*")
+if(BUILD_SHARED_LIBS)
 add_library(${PROJECT_NAME} SHARED ${HeaderFileList} ${SourceFileList})
 target_link_libraries(${PROJECT_NAME} ${CURL_LIBRARIES} ${CONAN_LIBS})
 set_target_properties(${PROJECT_NAME} PROPERTIES SOVERSION 1 VERSION 1.0.0)
-
-add_library(${PROJECT_NAME}_static STATIC ${HeaderFileList} ${SourceFileList})
+else()
+add_library(${PROJECT_NAME} STATIC ${HeaderFileList} ${SourceFileList})
 
 # Make sure that on unix-platforms shared and static libraries have
 # the same root name, but different suffixes.
@@ -97,19 +98,28 @@ add_library(${PROJECT_NAME}_static STATIC ${HeaderFileList} ${SourceFileList})
 #  (solution taken from https://cmake.org/Wiki/CMake_FAQ#How_do_I_make_my_shared_and_static_libraries_have_the_same_root_name.2C_but_different_suffixes.3F)
 # 
 # Making shared and static libraries have the same root name, but different suffixes
-SET_TARGET_PROPERTIES(${PROJECT_NAME}_static PROPERTIES OUTPUT_NAME ${PROJECT_NAME})
+SET_TARGET_PROPERTIES(${PROJECT_NAME} PROPERTIES OUTPUT_NAME ${PROJECT_NAME})
 # Now the library target "curlpp_static" will be named "curlpp.lib" with MS tools.
 # This conflicts with the "curlpp.lib" import library corresponding to "curlpp.dll",
 # so we add a "lib" prefix (which is default on other platforms anyway):
-SET_TARGET_PROPERTIES(${PROJECT_NAME}_static PROPERTIES PREFIX "lib")
-target_link_libraries(${PROJECT_NAME}_static ${CURL_LIBRARIES} ${CONAN_LIBS})
+SET_TARGET_PROPERTIES(${PROJECT_NAME} PROPERTIES PREFIX "lib")
+target_link_libraries(${PROJECT_NAME} CURL::libcurl)
+endif()
+
+target_include_directories(${PROJECT_NAME} PUBLIC $<INSTALL_INTERFACE:include>)
 
 # install headers
 install(DIRECTORY include/utilspp/ DESTINATION "${CMAKE_INSTALL_INCLUDEDIR}/utilspp")
 install(DIRECTORY include/curlpp/ DESTINATION "${CMAKE_INSTALL_INCLUDEDIR}/curlpp")
 
-install(TARGETS ${PROJECT_NAME} ${PROJECT_NAME}_static
+install(TARGETS ${PROJECT_NAME}
+        EXPORT ${PROJECT_NAME}-config
         RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
         LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
         ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR})
 
+install(EXPORT ${PROJECT_NAME}-config
+    FILE unofficial-${PROJECT_NAME}-config.cmake
+    NAMESPACE unofficial::${PROJECT_NAME}::
+    DESTINATION ${CMAKE_INSTALL_LIBDIR}/cmake/${PROJECT_NAME}
+)
