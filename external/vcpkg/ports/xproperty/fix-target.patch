diff --git a/CMakeLists.txt b/CMakeLists.txt
index 5dcddc2..9d99227 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -7,7 +7,7 @@
 ############################################################################
 
 cmake_minimum_required(VERSION 3.1)
-project(xproperty)
+project(xproperty CXX)
 
 set(XPROPERTY_INCLUDE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/include)
 
@@ -28,7 +28,8 @@ message(STATUS "xproperty v${${PROJECT_NAME}_VERSION}")
 # Dependencies
 # ============
 
-find_package(xtl 0.5.3 REQUIRED)
+set(xtl_REQUIRED_VERSION 0.5.3)
+find_package(xtl ${xtl_REQUIRED_VERSION} REQUIRED)
 message(STATUS "Found xtl: ${xtl_INCLUDE_DIRS}/xtl")
 
 # Build
diff --git a/xpropertyConfig.cmake.in b/xpropertyConfig.cmake.in
index 192c04f..38b305a 100644
--- a/xpropertyConfig.cmake.in
+++ b/xpropertyConfig.cmake.in
@@ -15,7 +15,12 @@
 
 @PACKAGE_INIT@
 
-set(PN xproperty)
-set_and_check(${PN}_INCLUDE_DIR "${PACKAGE_PREFIX_DIR}/@CMAKE_INSTALL_INCLUDEDIR@")
-set(${PN}_LIBRARY "")
-check_required_components(${PN})
+include(CMakeFindDependencyMacro)
+find_dependency(xtl @xtl_REQUIRED_VERSION@)
+
+if(NOT TARGET @PROJECT_NAME@)
+    include("${CMAKE_CURRENT_LIST_DIR}/@<EMAIL>")
+    get_target_property(@PROJECT_NAME@_INCLUDE_DIR @PROJECT_NAME@ INTERFACE_INCLUDE_DIRECTORIES)
+endif()
+
+set(@PROJECT_NAME@_LIBRARY "")
