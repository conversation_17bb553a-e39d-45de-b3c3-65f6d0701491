{"name": "mongo-c-driver", "version": "1.30.2", "description": "Client library written in C for MongoDB.", "homepage": "https://github.com/mongodb/mongo-c-driver", "license": "Apache-2.0", "supports": "!uwp", "dependencies": ["lib<PERSON>", {"name": "mongo-c-driver", "features": ["openssl"], "platform": "!windows & !osx & !ios"}, "utf8proc", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}, "zlib"], "features": {"openssl": {"description": "Use OpenSSL (even on Windows or Apple systems)", "dependencies": ["openssl"]}, "snappy": {"description": "Enables snappy compressor support", "dependencies": ["snappy"]}, "zstd": {"description": "Enables zstd compressor support", "dependencies": ["zstd"]}}}