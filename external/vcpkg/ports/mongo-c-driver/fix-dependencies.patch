diff --git a/build/cmake/libmongoc-static-1.0-config.cmake.in b/build/cmake/libmongoc-static-1.0-config.cmake.in
index bec3163..a32d5f3 100644
--- a/build/cmake/libmongoc-static-1.0-config.cmake.in
+++ b/build/cmake/libmongoc-static-1.0-config.cmake.in
@@ -24,6 +24,9 @@ set (MONGOC_STATIC_VERSION_FULL @libmongoc_VERSION_FULL@)
 
 include(CMakeFindDependencyMacro)
 find_dependency (mongoc-1.0)
+if("@ENABLE_SNAPPY@" STREQUAL "ON")
+    find_dependency(Snappy CONFIG)
+endif()
 
 set(MONGOC_STATIC_LIBRARY mongo::mongoc_static)
 set(MONGOC_STATIC_LIBRARIES mongo::mongoc_static)
diff --git a/src/libmongoc/CMakeLists.txt b/src/libmongoc/CMakeLists.txt
index 0a18f5a..5346a9d 100644
--- a/src/libmongoc/CMakeLists.txt
+++ b/src/libmongoc/CMakeLists.txt
@@ -50,7 +50,7 @@ endif ()
 set (ZLIB_INCLUDE_DIRS "")
 if (ENABLE_ZLIB MATCHES "SYSTEM|AUTO")
    message (STATUS "Searching for zlib CMake packages")
-   include (FindZLIB)
+   find_package(ZLIB REQUIRED)
    if (ZLIB_FOUND)
       message (STATUS "   zlib found version \"${ZLIB_VERSION_STRING}\"")
       message (STATUS "   zlib include path \"${ZLIB_INCLUDE_DIRS}\"")
@@ -468,10 +468,10 @@ else ()
 endif ()
 
 # Sets SNAPPY_LIBRARIES and SNAPPY_INCLUDE_DIRS.
-include (FindSnappy)
-if (SNAPPY_INCLUDE_DIRS)
+if(ENABLE_SNAPPY)
+   find_package(Snappy CONFIG REQUIRED)
+   set(SNAPPY_LIBRARIES Snappy::snappy)
    set (MONGOC_ENABLE_COMPRESSION 1)
-   include_directories ("${SNAPPY_INCLUDE_DIRS}")
 endif ()
 
 mongo_bool01 (MONGOC_ENABLE_SHM_COUNTERS ENABLE_SHM_COUNTERS)
@@ -759,7 +759,7 @@ set (STATIC_LIBRARIES
 )
 
 # utf8proc configuration
-find_package(Utf8Proc REQUIRED)
+find_package(unofficial-utf8proc CONFIG REQUIRED)
 set(UTF8PROC_INCLUDE_DIRS, "")
 if (USE_BUNDLED_UTF8PROC)
    set (
@@ -955,7 +955,7 @@ if (MONGOC_ENABLE_STATIC_BUILD)
    set_target_properties (mongoc_static PROPERTIES
       VERSION 0.0.0
       OUTPUT_NAME "${MONGOC_OUTPUT_BASENAME}-static-${MONGOC_API_VERSION}"
-      pkg_config_REQUIRES "libbson-static-1.0"
+      pkg_config_REQUIRES "libbson-static-1.0 libutf8proc"
       )
    if(MONGOC_ENABLE_STATIC_INSTALL)
       mongo_generate_pkg_config (mongoc_static FILENAME libmongoc-static-${MONGOC_API_VERSION}.pc INSTALL)
diff --git a/src/libmongoc/src/mongoc-config.cmake b/src/libmongoc/src/mongoc-config.cmake
index 31e6cbc..cdbb756 100644
--- a/src/libmongoc/src/mongoc-config.cmake
+++ b/src/libmongoc/src/mongoc-config.cmake
@@ -1,5 +1,8 @@
 include(CMakeFindDependencyMacro)
 find_dependency(bson-1.0 @libmongoc_VERSION@)
+if("@ENABLE_SNAPPY@")
+    find_dependency(Snappy CONFIG)
+endif()
 
 # If we need to import a TLS package for our imported targets, do that now:
 set(MONGOC_TLS_BACKEND [[@TLS_BACKEND@]])
@@ -13,8 +16,6 @@ if(_tls_package)
   set(CMAKE_MODULE_PATH "${_prev_path}")
 endif()
 
-include("${CMAKE_CURRENT_LIST_DIR}/mongoc-targets.cmake")
-
 unset(_required)
 unset(_quiet)
 if(${CMAKE_FIND_PACKAGE_NAME}_FIND_REQUIRED)
@@ -29,9 +30,8 @@ if(NOT _mongoc_built_with_bundled_utf8proc AND NOT TARGET PkgConfig::PC_UTF8PROC
   # libmongoc was compiled against an external utf8proc and links against a
   # FindPkgConfig-generated IMPORTED target. Find that package and generate that
   # imported target here:
-  find_dependency(PkgConfig)
-  pkg_check_modules(PC_UTF8PROC ${_required} ${_quiet} libutf8proc IMPORTED_TARGET GLOBAL)
 endif()
+find_dependency(unofficial-utf8proc CONFIG)
 
 # Find dependencies for SASL
 set(_sasl_backend [[@SASL_BACKEND@]])
@@ -43,3 +43,5 @@ if(_sasl_backend STREQUAL "Cyrus")
   find_dependency(SASL2 2.0)
   set(CMAKE_MODULE_PATH "${_prev_path}")
 endif()
+
+include("${CMAKE_CURRENT_LIST_DIR}/mongoc-targets.cmake")
