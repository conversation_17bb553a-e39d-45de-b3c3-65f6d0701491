{"name": "lua<PERSON>", "version-date": "2023-01-04", "port-version": 6, "description": "LuaJIT is a Just-In-Time (JIT) compiler for the Lua programming language.", "homepage": "https://github.com/LuaJIT/LuaJIT", "license": "MIT", "supports": "!uwp & !(arm64 & windows)", "dependencies": [{"name": "lua<PERSON>", "host": true, "features": ["buildvm-64"], "platform": "!native & (arm64 | x64) & (!windows | mingw)"}, {"name": "lua<PERSON>", "host": true, "features": ["buildvm-32"], "platform": "!native & ((arm & !arm64) | x86) & (!windows | mingw)"}, {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}, {"name": "vcpkg-cmake-get-vars", "host": true}], "features": {"buildvm-32": {"description": "Install host tools for 32 bit targets", "supports": "native & ((arm & !arm64) | x86) & (!windows | mingw)"}, "buildvm-64": {"description": "Install host tools for 64 bit targets", "supports": "native & (arm64 | x64) & (!windows | mingw)"}}}