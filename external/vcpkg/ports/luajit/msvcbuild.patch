diff --git a/src/msvcbuild.bat b/src/msvcbuild.bat
index aab4ef1..e92c486 100644
--- a/src/msvcbuild.bat
+++ b/src/msvcbuild.bat
@@ -79,10 +79,9 @@ buildvm -m folddef -o lj_folddef.h lj_opt_fold.c
 @set LJCOMPILE=%LJCOMPILE% /Zi %DEBUGCFLAGS%
 @set LJLINK=%LJLINK% /opt:ref /opt:icf /incremental:no
 :NODEBUG
-@set LJLINK=%LJLINK% /%BUILDTYPE%
 @if "%1"=="amalg" goto :AMALGDLL
 @if "%1"=="static" goto :STATIC
-%LJCOMPILE% /MD /DLUA_BUILD_AS_DLL lj_*.c lib_*.c
+%LJCOMPILE%     /DLUA_BUILD_AS_DLL lj_*.c lib_*.c /Fdlua51.pdb
 @if errorlevel 1 goto :BAD
 %LJLINK% /DLL /out:%LJDLLNAME% lj_*.obj lib_*.obj
 @if errorlevel 1 goto :BAD
@@ -102,7 +101,7 @@ buildvm -m folddef -o lj_folddef.h lj_opt_fold.c
 if exist %LJDLLNAME%.manifest^
   %LJMT% -manifest %LJDLLNAME%.manifest -outputresource:%LJDLLNAME%;2
 
-%LJCOMPILE% luajit.c
+%LJCOMPILE% luajit.c /Fdluajit.pdb
 @if errorlevel 1 goto :BAD
 %LJLINK% /out:luajit.exe luajit.obj %LJLIBNAME%
 @if errorlevel 1 goto :BAD
@@ -124,4 +123,5 @@ if exist luajit.exe.manifest^
 @goto :END
 :FAIL
 @echo You must open a "Visual Studio Command Prompt" to run this script
+@exit 1
 :END
