diff --git a/configure.ac b/configure.ac
index 8a05bf1aa..1f57cc688 100644
--- a/configure.ac	
+++ b/configure.ac
@@ -22,6 +22,7 @@ AC_USE_SYSTEM_EXTENSIONS
 # Initialize libtool
 LT_PREREQ([2.2])
 LT_INIT([win32-dll])
+AC_LIBTOOL_WIN32_DLL
 
 # Require xorg-macros minimum of 1.18 - Initial version
 m4_ifndef([XORG_MACROS_VERSION],
@@ -88,11 +88,11 @@ AC_MSG_RESULT($XCBPROTO_XCBPYTHONDIR)
 AC_SUBST(XCBPROTO_XCBPYTHONDIR)
 
 AC_HEADER_STDC
-AC_SEARCH_LIBS(getaddrinfo, socket)
-AC_SEARCH_LIBS(connect, socket)
+AC_SEARCH_LIBS(getaddrinfo, [socket Ws2_32])
+AC_SEARCH_LIBS(connect, [socket Ws2_32])
 
 # Find support for sending a message from a socket
-AC_SEARCH_LIBS(sendmsg, socket, [have_sendmsg="yes"], [have_sendmsg="no"])
+AC_SEARCH_LIBS(sendmsg, [socket Ws2_32], [have_sendmsg="yes"], [have_sendmsg="no"])
 
 # XPG4v2/UNIX95 added msg_control - check to see if we need to define
 # _XOPEN_SOURCE to get it (such as on Solaris)
@@ -140,6 +141,10 @@ mingw*)
         have_win32="yes"
         lt_enable_auto_import="-Wl,--enable-auto-import"
         ;;
+msys*)
+        have_win32="yes"
+        lt_enable_auto_import="-Wl,--enable-auto-import"
+        ;;
 linux*)
         AC_DEFINE([HAVE_ABSTRACT_SOCKETS], 1, [Define if your platform supports abstract sockets])
         ;;
@@ -208,7 +213,7 @@ esac
 
 dnl Link with winsock for socket functions on MinGW
 case $host_os in
-        *mingw*)
+        *mingw*| *msys* )
                 AC_CHECK_LIB([ws2_32],[main])
                 ;;
         *)
