diff --git a/include/ode/odeconfig.h b/include/ode/odeconfig.h
index 5ac58e7..21481b3 100644
--- a/include/ode/odeconfig.h
+++ b/include/ode/odeconfig.h
@@ -142,10 +142,17 @@ typedef unsigned long     duint64;
   typedef signed char     dint8;
   typedef unsigned char   duint8;
 
+# if defined(_M_ARM64)
+  typedef dint64          dintptr;
+  typedef duint64         duintptr;
+  typedef dint64          ddiffint;
+  typedef duint64         dsizeint;
+# else
   typedef dint32          dintptr;
   typedef duint32         duintptr;
   typedef dint32          ddiffint;
   typedef duint32         dsizeint;
+# endif
 
 #endif
 
