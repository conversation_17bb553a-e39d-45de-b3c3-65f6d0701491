static struct kw blocktags[] = {
   { "P", 1, 0 },
   { "DL", 2, 0 },
   { "H1", 2, 0 },
   { "H2", 2, 0 },
   { "H3", 2, 0 },
   { "H4", 2, 0 },
   { "H5", 2, 0 },
   { "H6", 2, 0 },
   { "HR", 2, 1 },
   { "OL", 2, 0 },
   { "UL", 2, 0 },
   { "BDO", 3, 0 },
   { "DFN", 3, 0 },
   { "DIV", 3, 0 },
   { "MAP", 3, 0 },
   { "PRE", 3, 0 },
   { "WBR", 3, 0 },
   { "XMP", 3, 0 },
   { "FORM", 4, 0 },
   { "NOBR", 4, 0 },
   { "STYLE", 5, 0 },
   { "TABLE", 5, 0 },
   { "CENTER", 6, 0 },
   { "IFRAME", 6, 0 },
   { "OBJECT", 6, 0 },
   { "SCRIPT", 6, 0 },
   { "ADDRESS", 7, 0 },
   { "LISTING", 7, 0 },
   { "PLAINTEXT", 9, 0 },
   { "BLOCKQUOTE", 10, 0 },
};

#define NR_blocktags 30
