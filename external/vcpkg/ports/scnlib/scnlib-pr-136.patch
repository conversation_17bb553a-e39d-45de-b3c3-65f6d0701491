diff --git a/src/scn/impl.cpp b/src/scn/impl.cpp
index aa0d334..ab859a4 100644
--- a/src/scn/impl.cpp
+++ b/src/scn/impl.cpp
@@ -721,15 +721,17 @@ scan_expected<std::ptrdiff_t> fast_float_fallback(impl_init_data<CharT> data,
 struct fast_float_impl_base : impl_base {
     fast_float::chars_format get_flags() const
     {
-        unsigned format_flags{};
+        fast_float::chars_format format_flags{};
         if ((m_options & float_reader_base::allow_fixed) != 0) {
-            format_flags |= fast_float::fixed;
+            format_flags =
+                static_cast<fast_float::chars_format>(format_flags | fast_float::chars_format::fixed);
         }
         if ((m_options & float_reader_base::allow_scientific) != 0) {
-            format_flags |= fast_float::scientific;
+            format_flags =
+                static_cast<fast_float::chars_format>(format_flags | fast_float::chars_format::scientific);
         }
 
-        return static_cast<fast_float::chars_format>(format_flags);
+        return format_flags;
     }
 };
 
