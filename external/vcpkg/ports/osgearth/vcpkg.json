{"name": "osgearth", "version": "3.7.2", "port-version": 1, "description": "osgEarth - 3D Maps for OpenSceneGraph / C++14", "homepage": "https://github.com/gwaldron/osgearth", "license": "LGPL-3.0-or-later", "supports": "!(arm32 | x86 | wasm32 | xbox)", "dependencies": ["blend2d", "draco", "geos", "glew", "libwebp", "opengl", {"name": "osg", "default-features": false, "features": ["plugins"]}, "protobuf", "pthreads", "sqlite3", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"controls": {"description": "Support for the old Controls API (superceded by imgui)"}, "tools": {"description": "Build command-line tools"}}}