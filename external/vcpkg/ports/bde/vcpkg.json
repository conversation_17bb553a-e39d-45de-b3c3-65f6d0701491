{"$schema": "https://raw.githubusercontent.com/microsoft/vcpkg-tool/main/docs/vcpkg.schema.json", "name": "bde", "version": "********", "description": "Basic Development Environment - a set of foundational C++ libraries used at Bloomberg.", "homepage": "https://techatbloomberg.com/", "documentation": "https://bloomberg.github.io/bde/", "license": "Apache-2.0", "supports": "!android & !(arm64 & windows) & !uwp", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}]}