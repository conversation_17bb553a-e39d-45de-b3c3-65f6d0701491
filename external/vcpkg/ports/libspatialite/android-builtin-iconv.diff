diff --git a/src/gaiaaux/gg_utf8.c b/src/gaiaaux/gg_utf8.c
index f11e604..620696e 100644
--- a/src/gaiaaux/gg_utf8.c
+++ b/src/gaiaaux/gg_utf8.c
@@ -73,7 +73,7 @@ extern const char *locale_charset (void);
 #include <localcharset.h>
 #endif /* end localcharset */
 #else /* not MINGW32 - WIN32 */
-#if defined(__APPLE__) || defined(__ANDROID__)
+#if defined(__APPLE__) || (defined(__ANDROID__) && __ANDROID_API__ < 28)
 #include <iconv.h>
 #include <localcharset.h>
 #else /* neither Mac OsX nor Android */
@@ -89,7 +89,7 @@ gaiaGetLocaleCharset ()
 #if defined(__MINGW32__) || defined(_WIN32)
     return locale_charset ();
 #else /* not MINGW32 - WIN32 */
-#if defined(__APPLE__) || defined(__ANDROID__)
+#if defined(__APPLE__) || (defined(__ANDROID__) && __ANDROID_API__ < 28)
     return locale_charset ();
 #else /* neither Mac OsX nor Android */
     return nl_langinfo (CODESET);
diff --git a/src/gaiageo/gg_shape.c b/src/gaiageo/gg_shape.c
index 8917535..e5e0240 100644
--- a/src/gaiageo/gg_shape.c
+++ b/src/gaiageo/gg_shape.c
@@ -75,7 +75,7 @@ extern const char *locale_charset (void);
 #include <localcharset.h>
 #endif /* end localcharset */
 #else /* not MINGW32 */
-#if defined(__APPLE__) || defined(__ANDROID__)
+#if defined(__APPLE__) || (defined(__ANDROID__) && __ANDROID_API__ < 28)
 #include <iconv.h>
 #include <localcharset.h>
 #else /* neither Mac OsX nor Android */
