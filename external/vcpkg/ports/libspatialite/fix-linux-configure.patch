diff --git a/configure.ac b/configure.ac
index ead87caff..47abb90f0 100644
--- a/configure.ac
+++ b/configure.ac
@@ -302,6 +302,7 @@ if test x"$enable_geos" != "xno"; then
   #-----------------------------------------------------------------------
   #   --with-geosconfig
   #
+ if 0; then
   AC_ARG_WITH([geosconfig],
         [AS_HELP_STRING([--with-geosconfig=FILE], [specify an alternative geos-config file])],
 	[GEOSCONFIG="$withval"], [GEOSCONFIG=""])
@@ -327,14 +328,17 @@ if test x"$enable_geos" != "xno"; then
   # Ensure that we can parse geos_c.h
   CPPFLAGS_SAVE="$CPPFLAGS"
   CPPFLAGS="$GEOS_CFLAGS"
+ fi
   AC_CHECK_HEADERS([geos_c.h],, [AC_MSG_ERROR([could not find geos_c.h - you may need to specify the directory of a geos-config file using --with-geosconfig])])
+ if 0; then
   CPPFLAGS="$CPPFLAGS_SAVE"	
   # Ensure we can link against libgeos_c
   LIBS_SAVE="$LIBS"
   LIBS="$GEOS_LDFLAGS"
   AC_SEARCH_LIBS(GEOSCoveredBy,geos_c,,AC_MSG_ERROR([could not find libgeos_c (or obsolete 'libgeos_c' < v.3.3.0 found) - you may need to specify the directory of a geos-config file using --with-geosconfig]))
   LIBS="$LIBS_SAVE"
   LIBS="$LIBS $GEOS_LDFLAGS -lgeos_c"
+ fi
 
   #-----------------------------------------------------------------------
   #   --enable-controlpoints
