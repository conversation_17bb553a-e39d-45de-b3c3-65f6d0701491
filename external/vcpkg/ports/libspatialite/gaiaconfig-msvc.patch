diff --git a/src/headers/spatialite/gaiaconfig-msvc.h b/src/headers/spatialite/gaiaconfig-msvc.h
index 37f0bd1..0053258 100644
--- a/src/headers/spatialite/gaiaconfig-msvc.h
+++ b/src/headers/spatialite/gaiaconfig-msvc.h
@@ -2,7 +2,7 @@
 /* ./src/headers/spatialite/gaiaconfig-msvc.h.in - manually maintained */
 
 /* Should be defined in order to enable GCP support. */
-#define ENABLE_GCP 1
+// #define ENABLE_GCP 1
 
 /* Should be defined in order to enable GeoPackage support. */
 #define ENABLE_GEOPACKAGE 1
@@ -11,7 +11,7 @@
 #define ENABLE_LIBXML2 1
 
 /* Should be defined in order to enable RTTOPO support. */
-#define ENABLE_RTTOPO 1
+// #define ENABLE_RTTOPO 1
 
 /* Should be defined in order to enable GEOS_370 support. */
 #define GEOS_370 1
@@ -32,7 +32,7 @@
 /* #undef OMIT_FREEXL */
 
 /* Should be defined in order to disable GEOCALLBACKS support. */
-#define OMIT_GEOCALLBACKS 1
+// #define OMIT_GEOCALLBACKS 1
 
 /* Should be defined in order to disable GEOS support. */
 /* #undef OMIT_GEOS */
