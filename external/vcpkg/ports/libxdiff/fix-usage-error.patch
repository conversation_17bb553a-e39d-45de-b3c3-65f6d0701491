diff --git a/CMakeLists.txt b/CMakeLists.txt
index 33c34c2..1d93cde 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -87,8 +87,9 @@ INSTALL (
     EXPORT XDiffTargets
     ARCHIVE DESTINATION lib
     LIBRARY DESTINATION lib
-    RUNTIME DESTINATION lib
+    RUNTIME DESTINATION bin
 )
+target_include_directories(xdiff INTERFACE "$<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>" "$<INSTALL_INTERFACE:include>") 
 
 WRITE_BASIC_PACKAGE_VERSION_FILE (
     "${CMAKE_CURRENT_BINARY_DIR}/XDiff/XDiffConfigVersion.cmake"
