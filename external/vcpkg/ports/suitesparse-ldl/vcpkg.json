{"$comment": "Use scripts/update_suitesparse.py to update all SuiteSparse ports", "name": "suitesparse-ldl", "version-semver": "3.3.2", "description": "LDL: A sparse LDL' factorization and solve package in SuiteSparse", "homepage": "https://people.engr.tamu.edu/davis/suitesparse.html", "license": "LGPL-2.1-or-later", "dependencies": ["suitesparse-amd", "suitesparse-config", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}]}