diff --git a/src/KDReportsConfig.cmake.in b/src/KDReportsConfig.cmake.in
index fa26517..49b9054 100644
--- a/src/KDReportsConfig.cmake.in
+++ b/src/KDReportsConfig.cmake.in
@@ -9,10 +9,10 @@
 
 include(CMakeFindDependencyMacro)
 
-find_dependency(Qt@Qt_VERSION_MAJOR@Core @QT_MIN_VERSION@)
-find_dependency(Qt@Qt_VERSION_MAJOR@Widgets @QT_MIN_VERSION@)
-find_dependency(Qt@Qt_VERSION_MAJOR@PrintSupport @QT_MIN_VERSION@)
-find_dependency(Qt@Qt_VERSION_MAJOR@Xml @QT_MIN_VERSION@)
+find_dependency(Qt@QT_VERSION_MAJOR@Core @QT_MIN_VERSION@)
+find_dependency(Qt@QT_VERSION_MAJOR@Widgets @QT_MIN_VERSION@)
+find_dependency(Qt@QT_VERSION_MAJOR@PrintSupport @QT_MIN_VERSION@)
+find_dependency(Qt@QT_VERSION_MAJOR@Xml @QT_MIN_VERSION@)
 
 if (@KDChart_FOUND@)
     find_dependency(KDChart)
