diff --git a/CMakeLists.txt b/CMakeLists.txt
index 39b9ad3..ed33e3c 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -342,7 +342,7 @@ if(JAN<PERSON><PERSON>_BUILD_SHARED_LIBS)
    )
       list(REMOVE_ITEM CMAKE_REQUIRED_LIBRARIES "-Wl,--version-script,${CMAKE_CURRENT_BINARY_DIR}/jansson.sym")
       if (VSCRIPT_WORKS)
-         set(CMAKE_SHARED_LINKER_FLAGS "-Wl,--version-script,${CMAKE_CURRENT_BINARY_DIR}/jansson.sym")
+         set(CMAKE_SHARED_LINKER_FLAGS "${CMAKE_SHARED_LINKER_FLAGS} -Wl,--version-script,${CMAKE_CURRENT_BINARY_DIR}/jansson.sym")
       endif()
    endif()
 
