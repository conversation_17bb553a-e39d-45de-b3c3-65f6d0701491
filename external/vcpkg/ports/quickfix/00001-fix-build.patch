diff --git a/CMakeLists.txt b/CMakeLists.txt
index 48908ead..90c17759 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -92,13 +92,13 @@ include_directories(${PYTHON_INCLUDE_DIRS})
 endif ()
 
 if( WIN32 OR ${CMAKE_SYSTEM_NAME} STREQUAL "Linux" )
-add_subdirectory(UnitTest++)
+#add_subdirectory(UnitTest++)
 endif()
 
 add_subdirectory(src)
-add_subdirectory(examples)
+#add_subdirectory(examples)
 if( WIN32)
-add_subdirectory(test)
+#add_subdirectory(test)
 endif()
 
 install(DIRECTORY ${CMAKE_SOURCE_DIR}/spec/ DESTINATION share/quickfix
diff --git a/src/CMakeLists.txt b/src/CMakeLists.txt
index cb4a60c6..d21fa995 100644
--- a/src/CMakeLists.txt
+++ b/src/CMakeLists.txt
@@ -1,6 +1,6 @@
 add_subdirectory(C++)
 
-if( WIN32 OR ${CMAKE_SYSTEM_NAME} STREQUAL "Linux" )
+if(0)
 
 add_executable(at at.cpp getopt.c)
 
diff --git a/src/C++/CMakeLists.txt b/src/C++/CMakeLists.txt
index 07774c97..27692631 100644
--- a/src/C++/CMakeLists.txt
+++ b/src/C++/CMakeLists.txt
@@ -1,3 +1,4 @@
+project(quickfix)
 set(quickfix_VERSION_MAJOR 16)
 set(quickfix_VERSION_MINOR 0)
 set(quickfix_VERSION_PATCH 1)
@@ -64,19 +65,22 @@ endif()
 
 if (WIN32)
   add_library(${PROJECT_NAME} STATIC  ${quickfix_SOURCES})
-  set_target_properties (${PROJECT_NAME} PROPERTIES DEBUG_POSTFIX d)
   target_link_libraries(${PROJECT_NAME} ${OPENSSL_LIBRARIES} ${MYSQL_CLIENT_LIBS} ${PostgreSQL_LIBRARIES} ws2_32)
+  target_compile_definitions(${PROJECT_NAME} PRIVATE _CRT_SECURE_NO_WARNINGS _CRT_NONSTDC_NO_DEPRECATE)
 else()
-  add_library(${PROJECT_NAME} SHARED  ${quickfix_SOURCES})
+  add_library(${PROJECT_NAME} STATIC  ${quickfix_SOURCES})
   target_link_libraries(${PROJECT_NAME} ${OPENSSL_LIBRARIES} ${MYSQL_CLIENT_LIBS} ${PostgreSQL_LIBRARIES} pthread)
 endif()
 
 target_include_directories(${PROJECT_NAME} PRIVATE ${CMAKE_SOURCE_DIR} ${CMAKE_SOURCE_DIR}/src ${CMAKE_SOURCE_DIR}/src/C++)
+target_include_directories(${PROJECT_NAME} PUBLIC $<INSTALL_INTERFACE:include>)
 
 set_target_properties(${PROJECT_NAME} PROPERTIES VERSION ${quickfix_VERSION} SOVERSION ${quickfix_VERSION_MAJOR} )
+target_compile_definitions(${PROJECT_NAME} PUBLIC HAVE_STD_UNIQUE_PTR HAVE_STD_SHARED_PTR HAVE_SSL=1)
 
-install(TARGETS ${PROJECT_NAME} DESTINATION lib)
+install(TARGETS ${PROJECT_NAME} EXPORT quickfix-config DESTINATION lib)
 install(DIRECTORY ${CMAKE_SOURCE_DIR}/src/C++/ DESTINATION include/quickfix
         FILES_MATCHING PATTERN "*.h"
         PATTERN double-conversion EXCLUDE
         PATTERN test EXCLUDE)
+install(EXPORT quickfix-config DESTINATION share/cmake/quickfix)
diff --git a/src/C++/Utility.h b/src/C++/Utility.h
index 0aa2cd3e..2829e110 100644
--- a/src/C++/Utility.h
+++ b/src/C++/Utility.h
@@ -242,4 +242,10 @@ using std::strtol;
 using std::strerror;
 #endif
 
+#ifdef __cpp_noexcept_function_type
+#define QUICKFIX_THROW(...) noexcept
+#else
+#define QUICKFIX_THROW(...) throw(__VA_ARGS__)
+#endif
+
 #endif
diff --git a/src/C++/UtilitySSL.cpp b/src/C++/UtilitySSL.cpp
index a01606ac..aee41bbf 100644
--- a/src/C++/UtilitySSL.cpp
+++ b/src/C++/UtilitySSL.cpp
@@ -498,7 +498,11 @@ static void ssl_rand_seed(void)
   /*
   * seed in the current process id (usually just 4 bytes)
   */
+#ifdef _MSC_VER
+  pid = GetCurrentProcessId();
+#else
   pid = getpid();
+#endif
   l = sizeof(pid);
   RAND_seed((unsigned char *)&pid, l);
   /*
diff --git a/src/C++/double-conversion/utils.h b/src/C++/double-conversion/utils.h
index aef2f166..779b8ab7 100644
--- a/src/C++/double-conversion/utils.h
+++ b/src/C++/double-conversion/utils.h
@@ -61,7 +61,7 @@
 // disabled.)
 // On Linux,x86 89255e-22 != Div_double(89255.0/1e22)
 #if defined(_M_X64) || defined(__x86_64__) || \
-    defined(__ARMEL__) || defined(__avr32__) || \
+    defined(__ARMEL__) || defined(__avr32__) || defined(_M_ARM) || defined(_M_ARM64) || \
     defined(__hppa__) || defined(__ia64__) || \
     defined(__mips__) || \
     defined(__powerpc__) || defined(__ppc__) || defined(__ppc64__) || \
