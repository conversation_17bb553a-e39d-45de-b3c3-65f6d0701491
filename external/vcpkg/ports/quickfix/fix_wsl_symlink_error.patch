diff --git a/cmake/QuickfixPrebuildSetup.cmake b/cmake/QuickfixPrebuildSetup.cmake
index 9e53126ec..1c3368fcb 100644
--- a/cmake/QuickfixPrebuildSetup.cmake
+++ b/cmake/QuickfixPrebuildSetup.cmake
@@ -3,8 +3,9 @@
 if (NOT WIN32)
 add_custom_target(QUICKFIX_HEADERS_LINK ALL
     COMMAND ${CMAKE_COMMAND} -E make_directory ${CMAKE_SOURCE_DIR}/include/
-    COMMAND ${CMAKE_COMMAND} -E create_symlink ${CMAKE_SOURCE_DIR}/src/C++ ${CMAKE_SOURCE_DIR}/include/quickfix
+    #COMMAND ${CMAKE_COMMAND} -E create_symlink ${CMAKE_SOURCE_DIR}/src/C++ ${CMAKE_SOURCE_DIR}/include/quickfix
 )
+    include_directories(${CMAKE_SOURCE_DIR}/src/C++)
 else()
 add_custom_target(QUICKFIX_HEADERS_COPY ALL 
 COMMAND ${CMAKE_COMMAND} -E make_directory ${CMAKE_SOURCE_DIR}/include/
