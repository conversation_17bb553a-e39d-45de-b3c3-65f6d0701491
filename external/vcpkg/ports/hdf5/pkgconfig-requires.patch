diff --git a/CMakeFilters.cmake b/CMakeFilters.cmake
index 141ab44..66b7efa 100644
--- a/CMakeFilters.cmake
+++ b/CMakeFilters.cmake
@@ -79,6 +79,7 @@ if (HDF5_ENABLE_Z_LIB_SUPPORT)
         set (ZLIB_INCLUDE_DIR_GEN ${ZLIB_INCLUDE_DIR})
         set (ZLIB_INCLUDE_DIRS ${ZLIB_INCLUDE_DIRS} ${ZLIB_INCLUDE_DIR})
         set (LINK_COMP_LIBS ${LINK_COMP_LIBS} ${ZLIB_LIBRARIES})
+        set (zlib_PC_LIBS_PRIVATE "${ZLIB_LIBRARIES}")
       endif ()
     else ()
       if (HDF5_ALLOW_EXTERNAL_SUPPORT MATCHES "GIT" OR HDF5_ALLOW_EXTERNAL_SUPPORT MATCHES "TGZ")
diff --git a/CMakeLists.txt b/CMakeLists.txt
index b75fdd2..a2e88fd 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -711,5 +711,8 @@ if (H5_HAVE_PARALLEL)
   if (MPI_C_LINK_FLAGS)
     set (CMAKE_EXE_LINKER_FLAGS "${MPI_C_LINK_FLAGS} ${CMAKE_EXE_LINKER_FLAGS}")
   endif ()
+  if(UNIX)
+    set(ompi-c_PC_LIBS_PRIVATE ${MPI_C_LIBRARIES})
+  endif()
 endif ()
 
diff --git a/config/cmake/HDFMacros.cmake b/config/cmake/HDFMacros.cmake
index 1af513b47..05d56ccce 100644
--- a/config/cmake/HDFMacros.cmake
+++ b/config/cmake/HDFMacros.cmake
@@ -425,7 +425,7 @@ macro (HDF_DIR_PATHS package_prefix)
   endif ()
 
   #set the default debug suffix for all library targets
-  if(NOT CMAKE_DEBUG_POSTFIX)
+  if(NOT DEFINED CMAKE_DEBUG_POSTFIX)
     if (WIN32)
       set (CMAKE_DEBUG_POSTFIX "_D")
     else ()
diff --git a/src/CMakeLists.txt b/src/CMakeLists.txt
index b3c2226..ea80d60 100644
--- a/src/CMakeLists.txt
+++ b/src/CMakeLists.txt
@@ -1321,6 +1321,16 @@ foreach (libs ${LINK_LIBS} ${LINK_COMP_LIBS})
 endif ()
 
 set (_PKG_CONFIG_REQUIRES)
+set (_PKG_CONFIG_REQUIRES_PRIVATE)
+set (_PKG_CONFIG_LIBS_PRIVATE " ${_PKG_CONFIG_LIBS_PRIVATE} ")
+foreach(_module IN ITEMS ompi-c zlib)
+    if(${_module}_PC_LIBS_PRIVATE)
+        foreach(_lib IN LISTS ${_module}_PC_LIBS_PRIVATE)
+            string(REPLACE " -l${_lib} " " " _PKG_CONFIG_LIBS_PRIVATE "${_PKG_CONFIG_LIBS_PRIVATE}")
+        endforeach()
+        string(APPEND _PKG_CONFIG_REQUIRES_PRIVATE " ${_module}")
+    endif()
+endforeach()
 
 configure_file (
     ${HDF_CONFIG_DIR}/libhdf5.pc.in
