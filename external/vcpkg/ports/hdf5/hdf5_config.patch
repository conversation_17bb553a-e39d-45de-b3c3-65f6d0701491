diff --git a/config/cmake/hdf5-config.cmake.in b/config/cmake/hdf5-config.cmake.in
index 1a3fb7bbf2..79081ce040 100644
--- a/config/cmake/hdf5-config.cmake.in
+++ b/config/cmake/hdf5-config.cmake.in
@@ -120,12 +114,22 @@ set (${HDF5_PACKAGE_NAME}_VERSION_MINOR  @HDF5_VERSION_MINOR@)
 # Don't include targets if this file is being picked up by another
 # project which has already built hdf5 as a subproject
 #-----------------------------------------------------------------------------
+include(CMakeFindDependencyMacro)
 if (NOT TARGET "@HDF5_PACKAGE@")
   if (${HDF5_PACKAGE_NAME}_ENABLE_Z_LIB_SUPPORT AND ${HDF5_PACKAGE_NAME}_PACKAGE_EXTLIBS)
     include (@PACKAGE_SHARE_INSTALL_DIR@/@ZLIB_PACKAGE_NAME@@<EMAIL>)
+  elseif (${HDF5_PACKAGE_NAME}_ENABLE_Z_LIB_SUPPORT)
+    find_dependency(ZLIB)
   endif ()
   if (${HDF5_PACKAGE_NAME}_ENABLE_SZIP_SUPPORT AND ${HDF5_PACKAGE_NAME}_PACKAGE_EXTLIBS)
     include (@PACKAGE_SHARE_INSTALL_DIR@/@LIBAEC_PACKAGE_NAME@@<EMAIL>)
+  elseif (${HDF5_PACKAGE_NAME}_ENABLE_SZIP_SUPPORT)
+    if (${HDF5_PACKAGE_NAME}_BUILD_STATIC_LIBS)
+      set(libaec_USE_STATIC_LIBS ON)
+    else()
+      set(libaec_USE_STATIC_LIBS OFF)
+    endif()
+    find_dependency(libaec)
   endif ()
   include (@PACKAGE_SHARE_INSTALL_DIR@/@HDF5_PACKAGE@@<EMAIL>)
 endif ()
