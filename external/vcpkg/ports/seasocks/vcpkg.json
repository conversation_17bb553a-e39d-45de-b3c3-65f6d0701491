{"name": "seasocks", "version": "1.4.6", "description": "Simple, small, C++ embeddable webserver with WebSockets support", "homepage": "https://github.com/mattgodbolt/seasocks", "license": "BSD-2-<PERSON><PERSON>", "supports": "!osx & !android", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "default-features": ["zlib"], "features": {"zlib": {"description": "Build with Deflate support via zlib", "dependencies": ["zlib"]}}}