diff --git a/rsocket/transports/tcp/TcpConnectionFactory.cpp b/rsocket/transports/tcp/TcpConnectionFactory.cpp
index b970cd75..cba4405e 100644
--- a/rsocket/transports/tcp/TcpConnectionFactory.cpp
+++ b/rsocket/transports/tcp/TcpConnectionFactory.cpp
@@ -41,7 +41,7 @@ class ConnectCallback : public folly::AsyncSocket::ConnectCallback {
     DCHECK(evb);
 
     if (sslContext) {
-#if !FOLLY_OPENSSL_HAS_ALPN
+#if 0
       // setAdvertisedNextProtocols() is unavailable
 #error ALPN is required for rsockets. \
       Your version of OpenSSL is likely too old.
diff --git a/yarpl/CMakeLists.txt b/yarpl/CMakeLists.txt
index f4159b82..33364f58 100644
--- a/yarpl/CMakeLists.txt
+++ b/yarpl/CMakeLists.txt
@@ -9,7 +9,7 @@ set(CMAKE_MODULE_PATH
   ${CMAKE_MODULE_PATH}
 )
 
-add_definitions(-std=c++14)
+add_definitions(-std=c++17)
 option(BUILD_TESTS "BUILD_TESTS" ON)
 
 # Generate compilation database
@@ -17,7 +17,7 @@ set(CMAKE_EXPORT_COMPILE_COMMANDS 1)
 
 # Common configuration for all build modes.
 if (NOT MSVC)
-  set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++14")
+  set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++17")
   set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra -Wno-unused-parameter")
   set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wno-weak-vtables -Wno-padded")
   set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fno-omit-frame-pointer")
diff --git a/yarpl/flowable/Flowable_FromObservable.h b/yarpl/flowable/Flowable_FromObservable.h
index e191ad7c..ce8565c8 100644
--- a/yarpl/flowable/Flowable_FromObservable.h
+++ b/yarpl/flowable/Flowable_FromObservable.h
@@ -180,7 +180,7 @@ class BufferBackpressureStrategy : public BackpressureStrategyBase<T> {
   static constexpr size_t kNoLimit = 0;
 
   explicit BufferBackpressureStrategy(size_t bufferSizeLimit = kNoLimit)
-      : buffer_(folly::in_place, bufferSizeLimit) {}
+      : buffer_(std::in_place, bufferSizeLimit) {}
 
  private:
   using Super = BackpressureStrategyBase<T>;
