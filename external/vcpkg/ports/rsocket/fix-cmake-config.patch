diff --git a/cmake/rsocket-config.cmake.in b/cmake/rsocket-config.cmake.in
index d5579a85..ea12752a 100644
--- a/cmake/rsocket-config.cmake.in
+++ b/cmake/rsocket-config.cmake.in
@@ -4,7 +4,7 @@
 @PACKAGE_INIT@

 if(NOT TARGET rsocket::ReactiveSocket)
-    include("${PACKAGE_PREFIX_DIR}/lib/cmake/rsocket/rsocket-exports.cmake")
+    include("${CMAKE_CURRENT_LIST_DIR}/rsocket-exports.cmake")
 endif()

 if (NOT rsocket_FIND_QUIETLY)
diff --git a/yarpl/cmake/yarpl-config.cmake.in b/yarpl/cmake/yarpl-config.cmake.in
index d557b213..1b83fffc 100644
--- a/yarpl/cmake/yarpl-config.cmake.in
+++ b/yarpl/cmake/yarpl-config.cmake.in
@@ -4,7 +4,7 @@
 @PACKAGE_INIT@

 if(NOT TARGET yarpl::yarpl)
-    include("${PACKAGE_PREFIX_DIR}/lib/cmake/yarpl/yarpl-exports.cmake")
+    include("${CMAKE_CURRENT_LIST_DIR}/yarpl-exports.cmake")
 endif()

 set(YARPL_LIBRARIES yarpl::yarpl)
