diff --git a/meson.build b/meson.build
index bf43d32ca..6798c43a4 100644
--- a/meson.build
+++ b/meson.build
@@ -270,7 +270,7 @@ w3m               = find_program('w3m',
 # Required Dependencies
 
 config.set('HAVE_UNISTD_H',    cc.has_header('unistd.h'))
-config.set('HAVE_EXECINFO_H',  cc.has_header('execinfo.h'))
+config.set('HAVE_EXECINFO_H',  target_machine.system() != 'android' and cc.has_header('execinfo.h'))
 config.set('HAVE_FSYNC',       cc.has_function('fsync'))
 config.set('HAVE_MALLOC_TRIM', cc.has_function('malloc_trim'))
 config.set('HAVE_STRPTIME',    cc.has_function('strptime'))
