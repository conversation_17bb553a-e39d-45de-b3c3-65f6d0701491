{"name": "numcpp", "version": "2.14.0", "description": "C++ implementation of the Python Numpy library", "homepage": "https://dpilger26.github.io/NumCpp", "license": "MIT", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"boost": {"description": "Enable use boost", "dependencies": ["boost-algorithm", "boost-date-time", "boost-endian", "boost-integer", "boost-log", "boost-math", "boost-predef", "boost-python", "boost-random", "boost-type-traits"]}, "python": {"description": "Interacting with Python with pybind11 interface", "dependencies": ["boost-python", "pybind11"]}}}