diff --git a/CMakeLists.txt b/CMakeLists.txt
index a07945c..6df83d1 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -75,7 +75,8 @@ ELSE()
   LIST(APPEND PTHREADPOOL_SRCS src/portable-api.c src/memory.c)
   IF(APPLE AND (PTHREADPOOL_SYNC_PRIMITIVE STREQUAL "default" OR PTHREADPOOL_SYNC_PRIMITIVE STREQUAL "gcd"))
     LIST(APPEND PTHREADPOOL_SRCS src/gcd.c)
-  ELSEIF(CMAKE_SYSTEM_NAME MATCHES "^(Windows|CYGWIN|MSYS)$" AND (PTHREADPOOL_SYNC_PRIMITIVE STREQUAL "default" OR PTHREADPOOL_SYNC_PRIMITIVE STREQUAL "event"))
+  ELSEIF(CMAKE_SYSTEM_NAME MATCHES "^(Windows|WindowsStore|CYGWIN|MSYS)$" AND (PTH<PERSON>ADPO<PERSON>_SYNC_PRIMITIVE STREQUAL "default" OR <PERSON>H<PERSON><PERSON><PERSON>OL_SYNC_PRIMITIVE STREQUAL "event"))
+    add_compile_options(-wd4146)
     LIST(APPEND PTHREADPOOL_SRCS src/windows.c)
   ELSE()
     LIST(APPEND PTHREADPOOL_SRCS src/pthreads.c)
