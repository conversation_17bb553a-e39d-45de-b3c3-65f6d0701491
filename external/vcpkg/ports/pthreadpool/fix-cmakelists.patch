diff --git a/CMakeLists.txt b/CMakeLists.txt
index 09d57b2..57d3a2c 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -4,8 +4,6 @@ CMAKE_MINIMUM_REQUIRED(VERSION 3.5 FATAL_ERROR)
 PROJECT(pthreadpool C CXX)
 
 # ---[ Options.
-SET(PTHREADPOOL_LIBRARY_TYPE "default" CACHE STRING "Type of library (shared, static, or default) to build")
-SET_PROPERTY(CACHE PTHREADPOOL_LIBRARY_TYPE PROPERTY STRINGS default static shared)
 OPTION(PTHREADPOOL_ALLOW_DEPRECATED_API "Enable deprecated API functions" ON)
 SET(PTHREADPOOL_SYNC_PRIMITIVE "default" CACHE STRING "Synchronization primitive (condvar, futex, gcd, event, or default) for worker threads")
 SET_PROPERTY(CACHE PTHREADPOOL_SYNC_PRIMITIVE PROPERTY STRINGS default condvar futex gcd event)
@@ -14,7 +12,7 @@ IF(CMAKE_SYSTEM_PROCESSOR MATCHES "^(i[3-6]86|AMD64|x86(_64)?)$")
 ELSE()
   OPTION(PTHREADPOOL_ENABLE_FASTPATH "Enable fast path using atomic decrement instead of atomic compare-and-swap" OFF)
 ENDIF()
-IF("${CMAKE_SOURCE_DIR}" STREQUAL "${PROJECT_SOURCE_DIR}")
+IF(FALSE)
   OPTION(PTHREADPOOL_BUILD_TESTS "Build pthreadpool unit tests" ON)
   OPTION(PTHREADPOOL_BUILD_BENCHMARKS "Build pthreadpool micro-benchmarks" ON)
 ELSE()
@@ -36,7 +34,8 @@ MACRO(PTHREADPOOL_TARGET_ENABLE_CXX11 target)
 ENDMACRO()
 
 # ---[ Download deps
-IF(NOT DEFINED FXDIV_SOURCE_DIR)
+find_path(FXDIV_INCLUDE_DIRS "fxdiv.h")
+IF(FALSE)
   MESSAGE(STATUS "Downloading FXdiv to ${CMAKE_BINARY_DIR}/FXdiv-source (define FXDIV_SOURCE_DIR to avoid it)")
   CONFIGURE_FILE(cmake/DownloadFXdiv.cmake "${CMAKE_BINARY_DIR}/FXdiv-download/CMakeLists.txt")
   EXECUTE_PROCESS(COMMAND "${CMAKE_COMMAND}" -G "${CMAKE_GENERATOR}" .
@@ -87,21 +86,13 @@ ELSE()
 ENDIF()
 
 ADD_LIBRARY(pthreadpool_interface INTERFACE)
-TARGET_INCLUDE_DIRECTORIES(pthreadpool_interface INTERFACE include)
+TARGET_INCLUDE_DIRECTORIES(pthreadpool_interface INTERFACE $<BUILD_INTERFACE:${PROJECT_SOURCE_DIR}/include> $<INSTALL_INTERFACE:${CMAKE_INSTALL_INCLUDEDIR}>)
 IF(NOT PTHREADPOOL_ALLOW_DEPRECATED_API)
   TARGET_COMPILE_DEFINITIONS(pthreadpool_interface INTERFACE PTHREADPOOL_NO_DEPRECATED_API=1)
 ENDIF()
 INSTALL(FILES include/pthreadpool.h DESTINATION ${CMAKE_INSTALL_INCLUDEDIR})
 
-IF(PTHREADPOOL_LIBRARY_TYPE STREQUAL "default")
-  ADD_LIBRARY(pthreadpool ${PTHREADPOOL_SRCS})
-ELSEIF(PTHREADPOOL_LIBRARY_TYPE STREQUAL "shared")
-  ADD_LIBRARY(pthreadpool SHARED ${PTHREADPOOL_SRCS})
-ELSEIF(PTHREADPOOL_LIBRARY_TYPE STREQUAL "static")
-  ADD_LIBRARY(pthreadpool STATIC ${PTHREADPOOL_SRCS})
-ELSE()
-  MESSAGE(FATAL_ERROR "Unsupported library type ${PTHREADPOOL_LIBRARY_TYPE}")
-ENDIF()
+ADD_LIBRARY(pthreadpool ${PTHREADPOOL_SRCS})
 
 IF(PTHREADPOOL_SYNC_PRIMITIVE STREQUAL "condvar")
   TARGET_COMPILE_DEFINITIONS(pthreadpool PRIVATE PTHREADPOOL_USE_FUTEX=0)
@@ -150,18 +141,22 @@ IF(CMAKE_SYSTEM_NAME STREQUAL "Linux")
 ENDIF()
 
 # ---[ Configure FXdiv
-IF(NOT TARGET fxdiv)
+IF(FALSE)
   SET(FXDIV_BUILD_TESTS OFF CACHE BOOL "")
   SET(FXDIV_BUILD_BENCHMARKS OFF CACHE BOOL "")
   ADD_SUBDIRECTORY(
     "${FXDIV_SOURCE_DIR}"
     "${CMAKE_BINARY_DIR}/FXdiv")
 ENDIF()
-TARGET_LINK_LIBRARIES(pthreadpool PRIVATE fxdiv)
+TARGET_INCLUDE_DIRECTORIES(pthreadpool PRIVATE ${FXDIV_INCLUDE_DIRS})
 
-INSTALL(TARGETS pthreadpool
+INSTALL(TARGETS pthreadpool pthreadpool_interface
+  EXPORT unofficial-pthreadpool-config
+  RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
   LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
   ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR})
+install(EXPORT unofficial-pthreadpool-config NAMESPACE unofficial::
+  DESTINATION ${CMAKE_INSTALL_DATAROOTDIR}/unofficial-${PROJECT_NAME}) # share/unofficial-pthreadpool
 
 IF(PTHREADPOOL_BUILD_TESTS)
   # ---[ Build google test
