{"$comment": "Automatically generated by scripts/boost/generate-ports.ps1", "name": "boost-process", "version": "1.87.0", "port-version": 1, "description": "Boost process module", "homepage": "https://www.boost.org/libs/process", "license": "BSL-1.0", "supports": "!uwp & !emscripten & !android", "dependencies": [{"name": "boost-algorithm", "version>=": "1.87.0"}, {"name": "boost-asio", "version>=": "1.87.0"}, {"name": "boost-assert", "version>=": "1.87.0"}, {"name": "boost-cmake", "version>=": "1.87.0"}, {"name": "boost-config", "version>=": "1.87.0"}, {"name": "boost-core", "version>=": "1.87.0"}, {"name": "boost-filesystem", "platform": "!uwp", "version>=": "1.87.0"}, {"name": "boost-fusion", "version>=": "1.87.0"}, {"name": "boost-headers", "version>=": "1.87.0"}, {"name": "boost-io", "version>=": "1.87.0"}, {"name": "boost-iterator", "version>=": "1.87.0"}, {"name": "boost-move", "version>=": "1.87.0"}, {"name": "boost-optional", "version>=": "1.87.0"}, {"name": "boost-system", "version>=": "1.87.0"}, {"name": "boost-throw-exception", "version>=": "1.87.0"}, {"name": "boost-tokenizer", "version>=": "1.87.0"}, {"name": "boost-type-index", "version>=": "1.87.0"}, {"name": "boost-type-traits", "version>=": "1.87.0"}, {"name": "boost-utility", "version>=": "1.87.0"}, {"name": "boost-winapi", "version>=": "1.87.0"}]}