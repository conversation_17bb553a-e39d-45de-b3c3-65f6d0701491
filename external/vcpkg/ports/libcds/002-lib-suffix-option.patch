diff --git a/CMakeLists.txt b/CMakeLists.txt
index aa287ff8..20fe7b49 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -118,7 +118,7 @@ if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID STREQUAL "Clang" OR CMAKE_C
 
     if(CMAKE_TARGET_ARCHITECTURE STREQUAL "x86_64")
         list(APPEND LIBCDS_PUBLIC_CXX_FLAGS "-mcx16")
-        set(LIB_SUFFIX "64")
+        set(LIB_SUFFIX "64" CACHE STRING "")
 
         # GCC-7: 128-bit atomics support is implemented via libatomic on amd64
         #        see https://gcc.gnu.org/ml/gcc/2017-01/msg00167.html
