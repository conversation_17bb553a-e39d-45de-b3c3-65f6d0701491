{"name": "ncnn", "version": "20241226", "description": "ncnn is a high-performance neural network inference computing framework.", "homepage": "https://github.com/Tencent/ncnn", "license": "BSD-3-<PERSON><PERSON>", "supports": "!(windows & arm)", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"vulkan": {"description": "Enable Vulkan support", "dependencies": ["glslang", "vulkan"]}}}