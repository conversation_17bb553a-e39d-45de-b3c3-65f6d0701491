--- a/interface/coroutine/frame.h
+++ a/interface/coroutine/frame.h
@@ -119,8 +119,8 @@
         return *this;
     }
     // *********, export/import
-    static /*constexpr*/ coroutine_handle from_address(void* _Addr) {
+    static /*constexpr*/ coroutine_handle from_address(void* _Addr) noexcept {
         coroutine_handle _Result{};
         _Result._Ptr = reinterpret_cast<portable_coro_prefix*>(_Addr);
         return _Result;
     }
 
