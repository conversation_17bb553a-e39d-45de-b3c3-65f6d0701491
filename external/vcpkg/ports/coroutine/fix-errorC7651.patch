diff --git a/modules/portable/frame.cpp b/modules/portable/frame.cpp
index 2cedf81..f413f28 100644
--- a/modules/portable/frame.cpp
+++ b/modules/portable/frame.cpp
@@ -77,13 +77,31 @@ size_t _coro_done(void*);
 //
 // intrinsic: Clang/GCC
 //
-extern "C" {
-bool __builtin_coro_done(void*);
-void __builtin_coro_resume(void*);
-void __builtin_coro_destroy(void*);
-// void* __builtin_coro_promise(void* ptr, int align, bool p);
+//extern "C" {
+template <bool B>
+void resume_wrapper(void *p)
+{
+    if constexpr (B)
+        __builtin_coro_resume(p);
+}
+
+template <bool B>
+void destroy_wrapper(void *p)
+{
+    if constexpr(B)
+        __builtin_coro_destroy(p);
 }
 
+template <bool B>
+bool done_wrapper(void *p)
+{
+    if constexpr(B)
+        return __builtin_coro_done(p);
+    return false;
+}
+// void* __builtin_coro_promise(void* ptr, int align, bool p);
+//}
+
 bool _coro_finished(portable_coro_prefix* _Handle);
 
 #if defined(__clang__)
@@ -124,7 +142,7 @@ bool portable_coro_done(portable_coro_prefix* _Handle) {
     if constexpr (is_msvc) {
         return _coro_finished(_Handle);
     } else if constexpr (is_clang) {
-        return __builtin_coro_done(_Handle);
+        return done_wrapper<true>(_Handle);
     }
     return false; // follow `noop_coroutine`
 }
@@ -133,7 +151,7 @@ void portable_coro_resume(portable_coro_prefix* _Handle) {
     if constexpr (is_msvc) {
         _coro_resume(_Handle);
     } else if constexpr (is_clang) {
-        __builtin_coro_resume(_Handle);
+       resume_wrapper<true>(_Handle); 
     }
 }
 
@@ -141,7 +159,7 @@ void portable_coro_destroy(portable_coro_prefix* _Handle) {
     if constexpr (is_msvc) {
         _coro_destroy(_Handle);
     } else if constexpr (is_clang) {
-        __builtin_coro_destroy(_Handle);
+        destroy_wrapper<true>(_Handle);
     }
 }
 
