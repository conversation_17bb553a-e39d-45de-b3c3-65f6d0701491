{"name": "ctbench", "version": "1.3.4", "port-version": 1, "description": "Compiler-assisted variable size benchmarking for the study of C++ metaprogram compile times.", "homepage": "https://github.com/JPenuchot/ctbench", "documentation": "https://jpenuchot.github.io/ctbench-docs/", "license": "MIT", "supports": "!windows", "dependencies": ["boost-container", "boost-process", "fmt", {"name": "llvm", "default-features": false}, "<PERSON><PERSON><PERSON>-<PERSON><PERSON>", "sciplot", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}]}