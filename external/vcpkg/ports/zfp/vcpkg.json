{"name": "zfp", "version": "1.0.1", "description": "Zfp is an open source C/C++ library for compressed numerical arrays that support high throughput read and write random access. zfp also supports streaming compression of integer and floating-point data, e.g., for applications that read and write large data sets to and from disk. zfp is primarily written in C and C++ but also includes Python and Fortran bindings.", "homepage": "https://github.com/LLNL/zfp", "license": "BSD-3-<PERSON><PERSON>", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"all": {"description": "Obsolete. This feature is left for compatibility", "dependencies": [{"name": "zfp", "features": ["cfp", "utility"]}]}, "cfp": {"description": "cfp support for cfp"}, "utility": {"description": "Build utility"}}}