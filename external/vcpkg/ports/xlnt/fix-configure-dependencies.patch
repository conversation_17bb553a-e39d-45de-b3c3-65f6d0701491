diff --git a/cmake/XlntConfig.cmake.in b/cmake/XlntConfig.cmake.in
index 2100b91b..c3a1f1d8 100644
--- a/cmake/XlntConfig.cmake.in
+++ b/cmake/XlntConfig.cmake.in
@@ -11,3 +11,8 @@ check_required_components(xlnt)
 if(NOT TARGET xlnt::xlnt)
   include("${XLNT_CMAKE_DIR}/XlntTargets.cmake")
 endif()
+
+include(CMakeFindDependencyMacro)
+find_dependency(FastFloat CONFIG)
+find_dependency(fmt CONFIG)
+find_dependency(utf8cpp CONFIG)
diff --git a/source/CMakeLists.txt b/source/CMakeLists.txt
index 752fb3fb..3362deb3 100644
--- a/source/CMakeLists.txt
+++ b/source/CMakeLists.txt
@@ -182,6 +182,16 @@ else()
   target_compile_definitions(xlnt PUBLIC XLNT_STATIC=1)
 endif()
 
+# configure dependencies (outside vcpkg they are included as git submodules)
+find_package(FastFloat CONFIG REQUIRED)
+target_link_libraries(xlnt PRIVATE FastFloat::fast_float)
+
+find_package(fmt CONFIG REQUIRED)
+target_link_libraries(xlnt PRIVATE fmt::fmt-header-only)
+
+find_package(utf8cpp CONFIG REQUIRED)
+target_link_libraries(xlnt PRIVATE utf8cpp::utf8cpp)
+
 # hide all symbols by default
 set_target_properties(xlnt PROPERTIES CXX_VISIBILITY_PRESET hidden)
 
