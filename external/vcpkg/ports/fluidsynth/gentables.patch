diff --git a/src/CMakeLists.txt b/src/CMakeLists.txt
index e86a6429..9240e091 100644
--- a/src/CMakeLists.txt
+++ b/src/CMakeLists.txt
@@ -538,6 +538,9 @@ install(EXPORT FluidSynthTargets
 
 # ******* Auto Generated Lookup Tables ******
 
+if(VCPKG_BUILD_MAKE_TABLES)
+    add_subdirectory(gentables)
+elseif(0)
 include(ExternalProject)
 
 set (GENTAB_SDIR ${CMAKE_CURRENT_SOURCE_DIR}/gentables)
@@ -557,4 +560,11 @@ ExternalProject_Add(gentables
         "${CMAKE_COMMAND}" --build "${GENTAB_BDIR}"
     INSTALL_COMMAND ${GENTAB_BDIR}/make_tables.exe "${FluidSynth_BINARY_DIR}/"
 )
+endif()
+if(TARGET make_tables AND NOT CMAKE_CROSSCOMPILING)
+    set(GENTABLES make_tables)
+else()
+    find_program(GENTABLES make_tables REQUIRED)
+endif()
+add_custom_target(gentables COMMAND "${GENTABLES}" "${CMAKE_BINARY_DIR}/")
 add_dependencies(libfluidsynth-OBJ gentables)
diff --git a/src/gentables/CMakeLists.txt b/src/gentables/CMakeLists.txt
index 9cb69f2b..c393a670 100644
--- a/src/gentables/CMakeLists.txt
+++ b/src/gentables/CMakeLists.txt
@@ -12,6 +12,7 @@ unset(ENV{LDFLAGS})
 
 project (gentables C)
 
+if (0)
 set ( CMAKE_BUILD_TYPE Debug )
 
 # hardcode ".exe" as suffix to the binary, else in case of cross-platform cross-compiling the calling cmake will not know the suffix used here and fail to find the binary
@@ -20,6 +21,7 @@ set ( CMAKE_EXECUTABLE_SUFFIX ".exe" )
 set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR})
 set(CMAKE_RUNTIME_OUTPUT_DIRECTORY_DEBUG ${CMAKE_BINARY_DIR})
 set(CMAKE_RUNTIME_OUTPUT_DIRECTORY_RELEASE ${CMAKE_BINARY_DIR})
+endif()
 
 # Add the executable that generates the table
 add_executable( make_tables
@@ -34,3 +36,4 @@ if ( WIN32 )
 else ( WIN32 )
     target_link_libraries (make_tables "m")
 endif ()
+install(TARGETS make_tables DESTINATION bin)
