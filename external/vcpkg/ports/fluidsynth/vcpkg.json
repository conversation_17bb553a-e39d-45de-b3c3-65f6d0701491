{"name": "fluidsynth", "version": "2.4.4", "port-version": 1, "description": "FluidSynth reads and handles MIDI events from the MIDI input device. It is the software analogue of a MIDI synthesizer. FluidSynth can also play midifiles using a Soundfont.", "homepage": "https://github.com/FluidSynth/fluidsynth", "license": "LGPL-2.1-or-later", "supports": "!xbox", "dependencies": [{"name": "alsa", "platform": "linux"}, {"name": "fluidsynth", "host": true, "default-features": false, "features": ["buildtools"]}, "glib", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"buildtools": {"description": "Build tools gentables"}, "pulseaudio": {"description": "Build with PulseAudio support", "supports": "linux"}, "sndfile": {"description": "Enable rendering to file and SF3 support", "dependencies": [{"name": "libsndfile", "default-features": false, "features": ["external-libs"]}]}}}