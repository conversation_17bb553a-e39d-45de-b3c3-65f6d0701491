diff --git a/configure.ac b/configure.ac
index 92b740b88..083e37ee5 100644
--- a/configure.ac	
+++ b/configure.ac
@@ -106,8 +106,12 @@ if test "x$ac_cv_search_dlopen" = xno; then
 		AC_DEFINE(HAVE_SHL_LOAD,1,
 			  [Use shl_load to load shared libraries])
 		AC_CHECK_HEADERS([dl.h])
+		XLDLIB="$ac_cv_search_shl_load"
 	fi
 else
+	if test "x$ac_cv_search_dlopen" != 'xnone required'; then
+		XLDLIB="$ac_cv_search_dlopen"
+	fi
 	AC_DEFINE(HAVE_DLOPEN,1,[Use dlopen to load shared libraries])
 	AC_CHECK_HEADERS([dlfcn.h])
 fi
@@ -116,6 +120,7 @@ if test "x$ac_cv_header_dlfcn_h" = xyes -o "x$ac_cv_header_dl_h" = xyes; then
 else
 	HAVE_LOADABLE_MODULES=no
 fi
+AC_SUBST(XLDLIB)
 AC_MSG_RESULT($HAVE_LOADABLE_MODULES)
 
 AC_MSG_CHECKING([if loadable i18n module support should be enabled])
diff --git a/x11.pc.in b/x11.pc.in
index 25c7e7cd0..4ecf23e8d 100644
--- a/x11.pc.in	
+++ b/x11.pc.in
@@ -11,5 +11,5 @@ Version: @PACKAGE_VERSION@
 Requires: xproto @XKBPROTO_REQUIRES@
 Requires.private: @X11_EXTRA_DEPS@
 Cflags: -I${includedir} @XTHREAD_CFLAGS@
-Libs: -L${libdir} -lX11
+Libs: -L${libdir} -lX11 @XLDLIB@
 Libs.private: @XTHREADLIB@
