{"name": "<PERSON><PERSON><PERSON><PERSON>", "version-semver": "1.2.4", "port-version": 3, "description": "The Robot Raconteur communication framework core library", "homepage": "https://www.robotraconteur.com", "license": "Apache-2.0", "supports": "(windows & (x86 | x64)) | (linux & (x86 | x64 | arm64 | arm32)) | (osx & (x64 | arm64))", "dependencies": ["boost-algorithm", "boost-array", "boost-asio", "boost-assign", "boost-atomic", "boost-bind", "boost-config", "boost-container", "boost-date-time", "boost-filesystem", "boost-foreach", "boost-format", "boost-function", "boost-interprocess", "boost-intrusive", "boost-lexical-cast", "boost-locale", "boost-program-options", "boost-random", "boost-range", "boost-regex", "boost-scope-exit", "boost-signals2", "boost-smart-ptr", "boost-thread", "boost-tuple", "boost-unordered", "boost-utility", "boost-uuid", {"name": "dbus", "platform": "linux"}, {"name": "libusb", "platform": "linux"}, {"name": "openssl", "platform": "!windows"}, {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}]}