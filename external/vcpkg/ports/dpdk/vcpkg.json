{"name": "dpdk", "version-string": "24.07", "description": "A set of libraries and drivers for fast packet processing", "homepage": "https://www.dpdk.org/", "documentation": "https://doc.dpdk.org/guides/index.html", "license": "BSD-3-<PERSON><PERSON>", "supports": "linux | freebsd", "dependencies": [{"name": "libarchive", "default-features": false}, {"name": "numactl", "platform": "linux"}, "python3", {"name": "vcpkg-get-python-packages", "host": true}, {"name": "vcpkg-tool-meson", "host": true}], "features": {"docs": {"description": "Build and install docs"}, "kmods": {"description": "Build and install kernel modules"}, "tests": {"description": "Build and install tests"}, "trace": {"description": "Build with fast path traces enabled"}}}