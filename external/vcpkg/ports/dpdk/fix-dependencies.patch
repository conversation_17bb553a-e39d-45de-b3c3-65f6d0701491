diff --git a/config/meson.build b/config/meson.build
index 722e114..0eb717b 100644
--- a/config/meson.build
+++ b/config/meson.build
@@ -234,12 +234,10 @@ if meson.is_cross_build() and not meson.get_cross_property('numa', true)
     find_libnuma = false
 endif
 if find_libnuma
-    numa_dep = cc.find_library('numa', required: false)
-    if numa_dep.found() and cc.has_header('numaif.h')
+    numa_dep = dependency('numa', method: 'pkg-config')
+    if numa_dep.found()
         dpdk_conf.set10('RTE_HAS_LIBNUMA', true)
         has_libnuma = true
-        add_project_link_arguments('-lnuma', language: 'c')
-        dpdk_extra_ldflags += '-lnuma'
     endif
 endif
 
diff --git a/lib/eal/linux/meson.build b/lib/eal/linux/meson.build
index e99ebed..672c705 100644
--- a/lib/eal/linux/meson.build
+++ b/lib/eal/linux/meson.build
@@ -21,5 +21,6 @@ sources += files(
 
 deps += ['kvargs', 'telemetry']
 if has_libnuma
+    ext_deps += numa_dep
     dpdk_conf.set10('RTE_EAL_NUMA_AWARE_HUGEPAGES', true)
 endif
diff --git a/lib/vhost/meson.build b/lib/vhost/meson.build
index 41b622a..afff033 100644
--- a/lib/vhost/meson.build
+++ b/lib/vhost/meson.build
@@ -6,6 +6,7 @@ if not is_linux
     reason = 'only supported on Linux'
 endif
 if has_libnuma
+    ext_deps += numa_dep
     dpdk_conf.set10('RTE_LIBRTE_VHOST_NUMA', true)
 endif
 if (toolchain == 'gcc' and cc.version().version_compare('>=8.3.0'))
