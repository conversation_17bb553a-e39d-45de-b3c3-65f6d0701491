diff --git a/config/meson.build b/config/meson.build
index 7f7b6c92fd..51c3572793 100644
--- a/config/meson.build
+++ b/config/meson.build
@@ -375,7 +375,7 @@ if not dpdk_conf.has('RTE_MAX_NUMA_NODES')
 endif
 
 # set the install path for the drivers
-dpdk_conf.set_quoted('RTE_EAL_PMD_PATH', eal_pmd_path)
+dpdk_conf.set_quoted('RTE_EAL_PMD_PATH', '')
 
 install_headers(['rte_config.h'],
         subdir: get_option('include_subdir_arch'))
