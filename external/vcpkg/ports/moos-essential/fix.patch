diff --git a/Essentials/pShare/CMakeLists.txt b/Essentials/pShare/CMakeLists.txt
index 5128a50..7fee919 100644
--- a/Essentials/pShare/CMakeLists.txt
+++ b/Essentials/pShare/CMakeLists.txt
@@ -20,4 +20,4 @@ file(COPY ${CMAKE_CURRENT_SOURCE_DIR}/pshare_test_scripts DESTINATION ${CMAKE_RU
 
 add_custom_command(TARGET pShare POST_BUILD
                    COMMAND ${CMAKE_COMMAND} -E copy_directory
-                       ${CMAKE_SOURCE_DIR}/pshare_test_scripts ${CMAKE_RUNTIME_OUTPUT_DIRECTORY})
+                       ${CMAKE_SOURCE_DIR}/Essentials/pShare/pshare_test_scripts ${CMAKE_RUNTIME_OUTPUT_DIRECTORY})
diff --git a/Essentials/pShare/Share.cpp b/Essentials/pShare/Share.cpp
index 7825e00..756a760 100644
--- a/Essentials/pShare/Share.cpp
+++ b/Essentials/pShare/Share.cpp
@@ -17,7 +17,7 @@
 
 #include "MOOS/libMOOS/Utils/MOOSUtilityFunctions.h"
 #include "MOOS/libMOOS/Utils/IPV4Address.h"
-#include "MOOS/libMOOS/Thirdparty/getpot/GetPot"
+#include "MOOS/libMOOS/Thirdparty/getpot/GetPot.hpp"
 #include "MOOS/libMOOS/Utils/SafeList.h"
 #include "MOOS/libMOOS/Utils/ConsoleColours.h"
 #include "MOOS/libMOOS/Utils/KeyboardCapture.h"
