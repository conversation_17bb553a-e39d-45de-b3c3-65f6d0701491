file(READ "${CMAKE_CURRENT_LIST_DIR}/../lmdb/usage" usage)
message(WARNING "find_package(lmdb) is deprecated.\n${usage}")

include(CMakeFindDependencyMacro)
find_dependency(unofficial-lmdb ${${CMAKE_FIND_PACKAGE_NAME}_FIND_VERSION})

set(${CMAKE_FIND_PACKAGE_NAME}_INCLUDE_DIR "${_VCPKG_INSTALLED_DIR}/${VCPKG_TARGET_TRIPLET}/include")
set(${CMAKE_FIND_PACKAGE_NAME}_INCLUDE_DIRS "${${CMAKE_FIND_PACKAGE_NAME}_INCLUDE_DIR}")
set(${CMAKE_FIND_PACKAGE_NAME}_LIBRARIES lmdb)

string(TOUPPER "${CMAKE_FIND_PACKAGE_NAME}" UPPER_PACKAGE_NAME)
set(${UPPER_PACKAGE_NAME}_INCLUDE_DIR "${${CMAKE_FIND_PACKAGE_NAME}_INCLUDE_DIR}")
set(${UPPER_PACKAGE_NAME}_INCLUDE_DIRS "${${CMAKE_FIND_PACKAGE_NAME}_INCLUDE_DIR}")
set(${UPPER_PACKAGE_NAME}_LIBRARIES lmdb)
