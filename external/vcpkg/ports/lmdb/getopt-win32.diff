diff --git a/libraries/liblmdb/mdb_dump.c b/libraries/liblmdb/mdb_dump.c
index 7ea72e8..198230c 100644
--- a/libraries/liblmdb/mdb_dump.c
+++ b/libraries/liblmdb/mdb_dump.c
@@ -16,7 +16,11 @@
 #include <stdlib.h>
 #include <string.h>
 #include <ctype.h>
+#ifdef _MSC_VER
+#include <getopt.h>
+#else
 #include <unistd.h>
+#endif
 #include <signal.h>
 #include "lmdb.h"
 
diff --git a/libraries/liblmdb/mdb_load.c b/libraries/liblmdb/mdb_load.c
index d2a3cec..f050286 100644
--- a/libraries/liblmdb/mdb_load.c
+++ b/libraries/liblmdb/mdb_load.c
@@ -16,7 +16,11 @@
 #include <errno.h>
 #include <string.h>
 #include <ctype.h>
+#ifdef _MSC_VER
+#include <getopt.h>
+#else
 #include <unistd.h>
+#endif
 #include "lmdb.h"
 
 #define PRINT	1
diff --git a/libraries/liblmdb/mdb_stat.c b/libraries/liblmdb/mdb_stat.c
index 3a81175..f1f4a75 100644
--- a/libraries/liblmdb/mdb_stat.c
+++ b/libraries/liblmdb/mdb_stat.c
@@ -14,7 +14,13 @@
 #include <stdio.h>
 #include <stdlib.h>
 #include <string.h>
+#ifdef _MSC_VER
+#include <getopt.h>
+#include <BaseTsd.h>
+typedef SSIZE_T ssize_t;
+#else
 #include <unistd.h>
+#endif
 #include "lmdb.h"
 
 #ifdef	_WIN32
