LIBRARY   lmdb
EXPORTS
   mdb_version
   mdb_strerror
   mdb_env_create
   mdb_env_open
   mdb_env_copy
   mdb_env_copyfd
   mdb_env_copy2
   mdb_env_copyfd2
   mdb_env_stat
   mdb_env_info
   mdb_env_sync
   mdb_env_close
   mdb_env_set_flags
   mdb_env_get_flags
   mdb_env_get_path
   mdb_env_get_fd
   mdb_env_set_mapsize
   mdb_env_set_maxreaders
   mdb_env_get_maxreaders
   mdb_env_set_maxdbs
   mdb_env_get_maxkeysize
   mdb_env_set_userctx
   mdb_env_get_userctx
   mdb_env_set_assert
   mdb_txn_begin
   mdb_txn_env
   mdb_txn_id
   mdb_txn_commit
   mdb_txn_abort
   mdb_txn_reset
   mdb_txn_renew
   mdb_dbi_open
   mdb_stat
   mdb_dbi_flags
   mdb_dbi_close
   mdb_drop
   mdb_set_compare
   mdb_set_dupsort
   mdb_set_relfunc
   mdb_set_relctx
   mdb_get
   mdb_put
   mdb_del
   mdb_cursor_open
   mdb_cursor_close
   mdb_cursor_renew
   mdb_cursor_txn
   mdb_cursor_dbi
   mdb_cursor_get
   mdb_cursor_put
   mdb_cursor_del
   mdb_cursor_count
   mdb_cmp
   mdb_dcmp
   mdb_reader_list
   mdb_reader_check
