{"$comment": "Use scripts/update_suitesparse.py to update all SuiteSparse ports", "name": "suitesparse-cholmod", "version-semver": "5.3.0", "description": "CHOLMOD: <PERSON><PERSON>ines for factorizing sparse symmetric positive definite matrices in SuiteSparse", "homepage": "https://people.engr.tamu.edu/davis/suitesparse.html", "license": "LGPL-2.1-or-later AND Apache-2.0", "dependencies": ["suitesparse-amd", "suitesparse-camd", "suitesparse-ccolamd", "suitesparse-colamd", "suitesparse-config", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "default-features": ["partition"], "features": {"cuda": {"description": "Enable CUDA support for the current compute architecture of this machine", "supports": "!(windows & static)", "license": "GPL-2.0-or-later AND LGPL-2.1-or-later AND Apache-2.0", "dependencies": ["cuda", {"name": "suitesparse-cholmod", "features": ["matrixops"]}]}, "matrixops": {"description": "Build MatrixOps module", "license": "GPL-2.0-or-later AND LGPL-2.1-or-later AND Apache-2.0"}, "modify": {"description": "Build Modify module", "license": "GPL-2.0-or-later AND LGPL-2.1-or-later AND Apache-2.0"}, "openmp": {"description": "Enable OpenMP support", "dependencies": [{"name": "suitesparse-config", "features": ["openmp"]}]}, "partition": {"description": "Build Partition module"}, "supernodal": {"description": "Build SuperNodal module", "license": "GPL-2.0-or-later AND LGPL-2.1-or-later AND Apache-2.0", "dependencies": ["lapack"]}}}