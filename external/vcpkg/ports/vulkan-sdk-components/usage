vulkan-sdk-components is compatible with built-in CMake targets:

    # https://cmake.org/cmake/help/latest/module/FindVulkan.html
    find_package(Vulkan REQUIRED)
    # SPIR-V generator library
    target_link_libraries(main PRIVATE Vulkan::glslang)
    # DirectX Shader Compiler
    target_link_libraries(main PRIVATE Vulkan::dxc)

The vulkan-sdk-components package does not provide direct Visual Studio
integration. For manual integration, add $(VULKAN_SDK)/include to your
include path.
