diff --git a/CMakeLists.txt b/CMakeLists.txt
index 9bb8ea3..c445c4b 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -5,6 +5,7 @@ cmake_minimum_required(VERSION 3.8)
 SET(CMAKE_CXX_STANDARD 17)
 SET(CMAKE_CXX_STANDARD_REQUIRED ON)
 
+option(BUILD_TOOLS "Build libaaplus tools" OFF)
 #-----------------------------------------------------------------------------
 # Output directories.
 SET(EXECUTABLE_OUTPUT_PATH ${PROJECT_BINARY_DIR}/bin CACHE PATH
@@ -261,18 +262,66 @@ SET ( SRCS
 #-----------------------------------------------------------------------------
 # Executables.
 #-----------------------------------------------------------------------------
+if(BUILD_TOOLS)
 ADD_EXECUTABLE(AATest ${SRCS} )
-
+endif()
 #-----------------------------------------------------------------------------
 # Libraries.
+if(NOT BUILD_SHARED_LIBS)
 ADD_LIBRARY(aaplus-static STATIC ${SRCS_LIB} ${INCS})
 SET_TARGET_PROPERTIES(aaplus-static PROPERTIES OUTPUT_NAME "aaplus")
-
+target_include_directories(aaplus-static PUBLIC $<BUILD_INTERFACE:${PROJECT_SOURCE_DIR}> $<INSTALL_INTERFACE:include/aaplus>)
+else()
 ADD_LIBRARY(aaplus-shared SHARED ${SRCS_LIB} ${INCS})
 SET_TARGET_PROPERTIES(aaplus-shared PROPERTIES OUTPUT_NAME "aaplus")
+target_include_directories(aaplus-shared PUBLIC $<BUILD_INTERFACE:${PROJECT_SOURCE_DIR}> $<INSTALL_INTERFACE:include/aaplus>)
+endif()
+
+if (MSVC AND BUILD_SHARED_LIBS)
+target_compile_definitions(aaplus-shared
+    PRIVATE
+        -DAAPLUS_EXT_CLASS\=__declspec\(dllexport\)
+)
+endif (MSVC AND BUILD_SHARED_LIBS)
+
+if(BUILD_SHARED_LIBS)
+    install(
+        TARGETS aaplus-shared
+        EXPORT aaplus
+        ARCHIVE DESTINATION lib
+        LIBRARY DESTINATION lib
+        RUNTIME DESTINATION bin
+    )
+else()
+    install(
+        TARGETS aaplus-static
+        EXPORT aaplus
+        ARCHIVE DESTINATION lib
+        LIBRARY DESTINATION lib
+        RUNTIME DESTINATION bin
+    )
+endif()
 
-install(TARGETS aaplus-shared aaplus-static 
-		DESTINATION "${CMAKE_INSTALL_PREFIX}/lib" )
+if(BUILD_TOOLS)
+    if(BUILD_SHARED_LIBS)
+        TARGET_LINK_LIBRARIES(AATest aaplus-shared)
+        if(MSVC)
+            target_compile_definitions(AATest
+                PUBLIC
+                -DAAPLUS_EXT_CLASS\=__declspec\(dllimport\)
+        )
+        endif()
+        
+    else()
+        TARGET_LINK_LIBRARIES(AATest aaplus-static)
+    endif()    
+endif()
 
-TARGET_LINK_LIBRARIES(AATest aaplus-static)
+install(
+    EXPORT aaplus
+    DESTINATION share/aaplus
+    FILE aaplusConfig.cmake
+    NAMESPACE aaplus::
+)
 
+install(FILES ${INCS} DESTINATION include/aaplus)
\ No newline at end of file
