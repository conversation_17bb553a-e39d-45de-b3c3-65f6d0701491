diff --git a/CMakeLists.txt b/CMakeLists.txt
index 26ae5f7..10a58fa 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -407,7 +407,8 @@ if(H<PERSON>L<PERSON><PERSON>GUI_INSTALL)
         VERSION ${hello_imgui_VERSION}
         COMPATIBILITY AnyNewerVersion)
 
-    install(FILES "hello_imgui_cmake/hello-imguiConfig.cmake" "${CMAKE_CURRENT_BINARY_DIR}/hello-imguiConfigVersion.cmake"
+    configure_file("${PROJECT_SOURCE_DIR}/hello_imgui_cmake/hello-imguiConfig.cmake" "${CMAKE_CURRENT_BINARY_DIR}/hello-imguiConfig.cmake" @ONLY)
+    install(FILES "${CMAKE_CURRENT_BINARY_DIR}/hello-imguiConfig.cmake" "${CMAKE_CURRENT_BINARY_DIR}/hello-imguiConfigVersion.cmake"
         DESTINATION lib/cmake/hello_imgui)
 endif()
 
diff --git a/hello_imgui_cmake/hello-imguiConfig.cmake b/hello_imgui_cmake/hello-imguiConfig.cmake
index 2b93540..6cfa3f8 100644
--- a/hello_imgui_cmake/hello-imguiConfig.cmake
+++ b/hello_imgui_cmake/hello-imguiConfig.cmake
@@ -1,6 +1,9 @@
 include(CMakeFindDependencyMacro)
-find_dependency(imgui CONFIG REQUIRED)
-find_dependency(glad CONFIG REQUIRED)
+find_dependency(imgui CONFIG)
+find_dependency(nlohmann_json CONFIG)
+if("@HELLOIMGUI_HAS_OPENGL3@")
+    find_dependency(glad CONFIG)
+endif()
 
 include(${CMAKE_CURRENT_LIST_DIR}/hello_imgui_cmake/hello_imgui_add_app.cmake)
 include(${CMAKE_CURRENT_LIST_DIR}/hello-imgui-targets.cmake)
