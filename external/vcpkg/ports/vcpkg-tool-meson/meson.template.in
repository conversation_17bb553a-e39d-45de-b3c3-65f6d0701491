[binaries]
cmake = ['@CMAKE_COMMAND@']
ninja = ['@NINJA@']
pkg-config = ['@PKGCONFIG@']
@MESON_MT@
@MESON_AR@
@MESON_RC@
@MESON_C@
@MESON_C_LD@
@MESON_CXX@
@MESON_CXX_LD@
@MESON_OBJC@
@MESON_OBJC_LD@
@MESON_OBJCPP@
@MESON_OBJCPP_LD@
@MESON_FC@
@MESON_FC_LD@
@MESON_WINDRES@
@MESON_ADDITIONAL_BINARIES@
[properties]
cmake_toolchain_file  = '@SCRIPTS@/buildsystems/vcpkg.cmake'
@MESON_ADDITIONAL_PROPERTIES@
[cmake]
CMAKE_BUILD_TYPE = '@MESON_CMAKE_BUILD_TYPE@'
VCPKG_TARGET_TRIPLET = '@TARGET_TRIPLET@'
VCPKG_HOST_TRIPLET = '@_HOST_TRIPLET@'
VCPKG_CHAINLOAD_TOOLCHAIN_FILE = '@VCPKG_CHAINLOAD_TOOLCHAIN_FILE@'
VCPKG_CRT_LINKAGE = '@VCPKG_CRT_LINKAGE@'
_VCPKG_INSTALLED_DIR = '@_VCPKG_INSTALLED_DIR@'
@MESON_HOST_MACHINE@
@MESON_BUILD_MACHINE@
[built-in options]
default_library = '@MESON_DEFAULT_LIBRARY@'
werror = false
@MESON_CFLAGS@
@MESON_CXXFLAGS@
@MESON_FCFLAGS@
@MESON_OBJCFLAGS@
@MESON_OBJCPPFLAGS@
# b_vscrt
@MESON_VSCRT_LINKAGE@
# c_winlibs/cpp_winlibs
@MESON_WINLIBS@