diff --git a/mesonbuild/dependencies/python.py b/mesonbuild/dependencies/python.py
index 883a29a..d9a82af 100644
--- a/mesonbuild/dependencies/python.py
+++ b/mesonbuild/dependencies/python.py
@@ -232,8 +232,10 @@ class _PythonDependencyBase(_Base):
                 else:
                     if self.is_freethreaded:
                         libpath = Path('libs') / f'python{vernum}t.lib'
+                        libpath = Path('libs') / f'..' / f'..' / f'..' / f'lib' / f'python{vernum}t.lib'
                     else:
                         libpath = Path('libs') / f'python{vernum}.lib'
+                        libpath = Path('libs') / f'..' / f'..' / f'..' / f'lib' / f'python{vernum}.lib'
                     # For a debug build, pyconfig.h may force linking with
                     # pythonX_d.lib (see meson#10776). This cannot be avoided
                     # and won't work unless we also have a debug build of
@@ -250,6 +252,8 @@ class _PythonDependencyBase(_Base):
                         vscrt = self.env.coredata.optstore.get_value('b_vscrt')
                         if vscrt in {'mdd', 'mtd', 'from_buildtype', 'static_from_buildtype'}:
                             vscrt_debug = True
+                    if is_debug_build:
+                       libpath = Path('libs') / f'..' / f'..' / f'..' / f'debug/lib' / f'python{vernum}_d.lib'
                     if is_debug_build and vscrt_debug and not self.variables.get('Py_DEBUG'):
                         mlog.warning(textwrap.dedent('''\
                             Using a debug build type with MSVC or an MSVC-compatible compiler
@@ -350,9 +354,10 @@ class PythonSystemDependency(SystemDependency, _PythonDependencyBase):
             self.is_found = True
 
         # compile args
+        verdot = self.variables.get('py_version_short')
         inc_paths = mesonlib.OrderedSet([
             self.variables.get('INCLUDEPY'),
-            self.paths.get('include'),
+            self.paths.get('include') + f'/../../../include/python${verdot}',
             self.paths.get('platinclude')])
 
         self.compile_args += ['-I' + path for path in inc_paths if path]
@@ -416,7 +421,7 @@ def python_factory(env: 'Environment', for_machine: 'MachineChoice',
             candidates.append(functools.partial(wrap_in_pythons_pc_dir, pkg_name, env, kwargs, installation))
             # We only need to check both, if a python install has a LIBPC. It might point to the wrong location,
             # e.g. relocated / cross compilation, but the presence of LIBPC indicates we should definitely look for something.
-            if pkg_libdir is not None:
+            if True or pkg_libdir is not None:
                 candidates.append(functools.partial(PythonPkgConfigDependency, pkg_name, env, kwargs, installation))
         else:
             candidates.append(functools.partial(PkgConfigDependency, 'python3', env, kwargs))
