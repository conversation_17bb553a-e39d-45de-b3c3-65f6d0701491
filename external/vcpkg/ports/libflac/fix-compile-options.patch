diff --git a/CMakeLists.txt b/CMakeLists.txt
index c83dd83..2d1a7e6 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -82,9 +82,11 @@ if(HAVE_WERROR_FLAG)
     option(ENABLE_WERROR "Enable -Werror in all Makefiles" OFF)
 endif()
 
+if(MSVC AND NOT WITH_ASM)
+    add_compile_options(/wd4267 /wd4996)
+endif()
+
 add_compile_options(
-    $<$<BOOL:${MSVC}>:/wd4267>
-    $<$<BOOL:${MSVC}>:/wd4996>
     $<$<BOOL:${ENABLE_WERROR}>:-Werror>
     $<$<AND:$<COMPILE_LANGUAGE:CXX>,$<BOOL:${HAVE_WEFFCXX_FLAG}>>:-Weffc++>
     $<$<AND:$<COMPILE_LANGUAGE:C>,$<BOOL:${HAVE_DECL_AFTER_STMT_FLAG}>>:-Wdeclaration-after-statement>)
