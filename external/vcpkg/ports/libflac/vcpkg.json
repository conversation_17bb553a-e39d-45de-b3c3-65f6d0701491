{"name": "libflac", "version": "1.4.3", "port-version": 2, "description": "Library for manipulating FLAC files", "homepage": "https://xiph.org/flac/", "license": "BSD-3-<PERSON><PERSON>", "dependencies": ["libogg", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "default-features": [{"name": "stack-protector", "platform": "!emscripten"}], "features": {"asm": {"description": "Use any assembly optimization routines", "supports": "x86"}, "stack-protector": {"description": "Build with stack smashing protection", "supports": "!emscripten"}}}