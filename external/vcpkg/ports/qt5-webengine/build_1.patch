diff --git a/src/3rdparty/chromium/third_party/angle/BUILD.gn b/src/3rdparty/chromium/third_party/angle/BUILD.gn
index fb57176..fa84cc5 100644
--- a/src/3rdparty/chromium/third_party/angle/BUILD.gn
+++ b/src/3rdparty/chromium/third_party/angle/BUILD.gn
@@ -131,7 +131,6 @@ config("extra_warnings") {
   # Enable more default warnings on Windows.
   if (is_win) {
     cflags += [
-      "/we4244",  # Conversion: possible loss of data.
       "/we4312",  # Conversion: greater size.
       "/we4456",  # Variable shadowing.
       "/we4458",  # declaration hides class member.
