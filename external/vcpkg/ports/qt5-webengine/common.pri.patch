diff --git a/src/buildtools/config/common.pri b/src/buildtools/config/common.pri
index cf990c7..5af9edf 100644
--- a/src/buildtools/config/common.pri
+++ b/src/buildtools/config/common.pri
@@ -26,6 +26,7 @@ gn_args += \
     skia_use_dawn=false \
     toolkit_views=false \
     treat_warnings_as_errors=false \
+    fatal_linker_warnings=false \
     use_allocator_shim=false \
     use_allocator=\"none\" \
     use_custom_libcxx=false \
@@ -56,7 +57,7 @@ greaterThan(QMAKE_JUMBO_MERGE_LIMIT,0) {
 }
 
 precompile_header {
-    gn_args += enable_precompiled_headers=true
+    gn_args += enable_precompiled_headers=false
 } else {
     gn_args += enable_precompiled_headers=false
 }
