diff --git a/src/3rdparty/chromium/ui/gl/gl_bindings_autogen_gl.h b/src/3rdparty/chromium/ui/gl/gl_bindings_autogen_gl.h
index d54583e8b..1921185f8 100644
--- a/src/3rdparty/chromium/ui/gl/gl_bindings_autogen_gl.h	
+++ b/src/3rdparty/chromium/ui/gl/gl_bindings_autogen_gl.h
@@ -16,7 +16,7 @@
 namespace gl {
 
 class GLContext;
-
+typedef void *GLeglImageOES;
 typedef void(GL_BINDING_CALL* glActiveShaderProgramProc)(G<PERSON>uint pipeline,
                                                          G<PERSON>uint program);
 typedef void(GL_BINDING_CALL* glActiveTextureProc)(GLenum texture);
