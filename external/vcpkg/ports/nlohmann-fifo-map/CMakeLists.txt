cmake_minimum_required(VERSION 3.1)
project(nlohmann-fifo-map LANGUAGES CXX)

include(GNUInstallDirs)

set(NLOHMANN_FIFO_MAP_TARGET_NAME ${PROJECT_NAME})
set(NLOHMANN_FIFO_MAP_INCLUDE_INSTALL_DIR "${CMAKE_INSTALL_INCLUDEDIR}/nlohmann")
set(NLOHMANN_FIFO_MAP_CONFIG_INSTALL_DIR "${CMAKE_INSTALL_PREFIX}/share/nlohmann-fifo-map")
set(NLOHMANN_FIFO_MAP_CONFIG_EXPORT_NAME "${PROJECT_NAME}-config")
set(NLOHMANN_FIFO_MAP_INCLUDE_BUILD_DIR "${CMAKE_SOURCE_DIR}/src/")

add_library(${NLOHMANN_FIFO_MAP_TARGET_NAME} INTERFACE)
add_library(${PROJECT_NAME}::${NLOHMANN_FIFO_MAP_TARGET_NAME} ALIAS ${NLOHMANN_FIFO_MAP_TARGET_NAME})

install(
    DIRECTORY ${NLOHMANN_FIFO_MAP_INCLUDE_BUILD_DIR}
    DESTINATION ${NLOHMANN_FIFO_MAP_INCLUDE_INSTALL_DIR}
)

install(
    TARGETS ${NLOHMANN_FIFO_MAP_TARGET_NAME} EXPORT ${NLOHMANN_FIFO_MAP_CONFIG_EXPORT_NAME}
    INCLUDES DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}
)

install(
    EXPORT ${NLOHMANN_FIFO_MAP_CONFIG_EXPORT_NAME}
    DESTINATION ${NLOHMANN_FIFO_MAP_CONFIG_INSTALL_DIR}
    NAMESPACE ${PROJECT_NAME}::
)

