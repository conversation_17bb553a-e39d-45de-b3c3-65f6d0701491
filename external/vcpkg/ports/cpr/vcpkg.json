{"name": "cpr", "version-semver": "1.11.2", "description": "C++ Requests is a simple wrapper around libcurl inspired by the excellent Python Requests project.", "homepage": "https://github.com/libcpr/cpr", "license": "MIT", "dependencies": [{"name": "curl", "default-features": false}, {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "default-features": ["ssl"], "features": {"ssl": {"description": "Enable SSL support", "dependencies": [{"name": "curl", "default-features": false, "features": ["ssl"]}, {"name": "openssl", "platform": "linux"}]}}}