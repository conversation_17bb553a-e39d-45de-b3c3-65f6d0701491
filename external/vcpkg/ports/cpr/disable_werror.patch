diff --git a/CMakeLists.txt b/CMakeLists.txt
index 70d3296..293ce95 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -379,7 +379,7 @@ if(CPR_BUILD_TESTS)
     restore_variable(DESTINATION CMAKE_CXX_CLANG_TIDY BACKUP CMAKE_CXX_CLANG_TIDY_BKP)
 endif()
 
-if ("${CMAKE_CXX_COMPILER_ID}" STREQUAL "MSVC")
+if (1)
 else()
     set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra -Wpedantic -Werror")
     if (CMAKE_CXX_COMPILER_ID STREQUAL "Clang")
