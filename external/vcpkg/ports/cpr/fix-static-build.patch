diff --git a/CMakeLists.txt b/CMakeLists.txt
index 2fc6152..b953f8c 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -82,7 +82,7 @@ if (MSVC)
         message(STATUS "Build windows dynamic libs.")
     else()
         # Add this to build windows pure static library.
-        set(CMAKE_MSVC_RUNTIME_LIBRARY "MultiThreaded$<$<CONFIG:Debug>:Debug>")
+        # set(CMAKE_MSVC_RUNTIME_LIBRARY "MultiThreaded$<$<CONFIG:Debug>:Debug>")
         message(STATUS "Build windows static libs.")
     endif()
 endif()
