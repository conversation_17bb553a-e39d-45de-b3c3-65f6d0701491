diff --git a/CMakeLists.txt b/CMakeLists.txt
index 99daa24..9a18ee5 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -259,6 +259,9 @@ endforeach(SD_API_VER)
 # Additional special linkage libraries
 foreach(SD_API_VER ${SD_API_VERS})
     if(WIN32)
+        # arm64-windows support
+        target_link_libraries(${NRF_BLE_DRIVER_${SD_API_VER}_STATIC_LIB} PRIVATE "advapi32")
+        target_link_libraries(${NRF_BLE_DRIVER_${SD_API_VER}_SHARED_LIB} PRIVATE "advapi32")
     elseif(APPLE)
         target_link_libraries(${NRF_BLE_DRIVER_${SD_API_VER}_STATIC_LIB} PRIVATE "-framework CoreFoundation" "-framework IOKit")
         target_link_libraries(${NRF_BLE_DRIVER_${SD_API_VER}_SHARED_LIB} PRIVATE "-framework CoreFoundation" "-framework IOKit")
