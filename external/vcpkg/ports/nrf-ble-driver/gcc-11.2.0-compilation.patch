From 00791acc7c23ac2421102edd42a7fa562cc98bfd Mon Sep 17 00:00:00 2001
From: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
Date: Wed, 29 Dec 2021 09:47:40 +0100
Subject: [PATCH] fix compilation issue (gcc-11.2.0)

error: 'sleep_for' is not a member of 'std::this_thread'
---
 src/common/transport/uart_transport.cpp | 1 +
 1 file changed, 1 insertion(+)

diff --git a/src/common/transport/uart_transport.cpp b/src/common/transport/uart_transport.cpp
index 1f063ff2..96e045fb 100644
--- a/src/common/transport/uart_transport.cpp
+++ b/src/common/transport/uart_transport.cpp
@@ -45,6 +45,7 @@
 #include <mutex>
 #include <sstream>
 #include <system_error>
+#include <thread>
 
 #if defined(__APPLE__)
 #include <IOKit/serial/ioss.h>
