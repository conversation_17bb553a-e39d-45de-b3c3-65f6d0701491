{"name": "directx-headers", "version": "1.615.0", "description": "Official DirectX 12 Headers", "homepage": "https://devblogs.microsoft.com/directx/", "documentation": "https://devblogs.microsoft.com/directx/gettingstarted-dx12agility/", "license": "MIT", "supports": "(windows & !arm32 & !xbox) | linux", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}]}