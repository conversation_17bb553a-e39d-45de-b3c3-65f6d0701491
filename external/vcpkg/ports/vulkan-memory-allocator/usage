VulkanMemoryAllocator provides official find_package support. However, it requires the user to provide the include directory containing `vulkan/vulkan.h`. There are multiple ways to achieve this and VulkanMemoryAllocator is compatible with all of them.

    find_package(Vulkan) # https://cmake.org/cmake/help/latest/module/FindVulkan.html, CMake 3.21+
    find_package(VulkanMemoryAllocator CONFIG REQUIRED)
    target_link_libraries(main PRIVATE Vulkan::Vulkan GPUOpen::VulkanMemoryAllocator)

or

    find_package(Vulkan) # CMake 3.21+
    find_package(VulkanMemoryAllocator CONFIG REQUIRED)
    target_link_libraries(main PRIVATE Vulkan::Headers GPUOpen::VulkanMemoryAllocator)

or

    find_package(VulkanHeaders CONFIG) # From the vulkan-headers port
    find_package(VulkanMemoryAllocator CONFIG REQUIRED)
    target_link_libraries(main PRIVATE Vulkan::Headers GPUOpen::VulkanMemoryAllocator)

See the documentation for more information on setting up your project: https://gpuopen-librariesandsdks.github.io/VulkanMemoryAllocator/html/index.html
