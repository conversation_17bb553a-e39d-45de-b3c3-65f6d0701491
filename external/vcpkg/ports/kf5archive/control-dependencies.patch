diff --git a/CMakeLists.txt b/CMakeLists.txt
index 958e22d..00b9736 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -36,6 +36,7 @@ set_package_properties(BZip2 PROPERTIES
     TYPE RECOMMENDED
     PURPOSE "Support for BZip2 compressed files and data streams"
 )
+set(BZIP2_FOUND "${VCPKG_USE_BZIP2}")
 
 find_package(LibLZMA)
 set_package_properties(LibLZMA PROPERTIES
@@ -43,6 +44,7 @@ set_package_properties(LibLZMA PROPERTIES
     DESCRIPTION "Support for xz compressed files and data streams"
     PURPOSE "Support for xz compressed files and data streams"
 )
+set(LIBLZMA_FOUND "${VCPKG_USE_LIBLZMA}")
 
 find_package(PkgConfig)
 if (PkgConfig_FOUND)
@@ -51,6 +53,8 @@ endif()
 add_feature_info(LibZstd LibZstd_FOUND
                 "Support for zstd compressed files and data streams"
 )
+find_package(zstd CONFIG)
+set(LibZstd_FOUND "${VCPKG_USE_ZSTD}")
 
 include(ECMSetupVersion)
 include(ECMGenerateHeaders)
diff --git a/KF5ArchiveConfig.cmake.in b/KF5ArchiveConfig.cmake.in
index 0a738c2..b4d96a5 100644
--- a/KF5ArchiveConfig.cmake.in
+++ b/KF5ArchiveConfig.cmake.in
@@ -23,8 +23,7 @@ if (NOT @BUILD_SHARED_LIBS@)
     endif()
 
     if (@LibZstd_FOUND@)
-        find_package(PkgConfig)
-        pkg_check_modules(LibZstd IMPORTED_TARGET "libzstd")
+        find_dependency(zstd CONFIG)
     endif()
 endif()
 
diff --git a/src/CMakeLists.txt b/src/CMakeLists.txt
index 675ddf4..9363637 100644
--- a/src/CMakeLists.txt
+++ b/src/CMakeLists.txt
@@ -24,7 +24,7 @@ endif()
 
 if (LibZstd_FOUND)
     target_sources(KF5Archive PRIVATE kzstdfilter.cpp)
-    target_link_libraries(KF5Archive PRIVATE PkgConfig::LibZstd)
+    target_link_libraries(KF5Archive PRIVATE $<IF:$<TARGET_EXISTS:zstd::libzstd_shared>,zstd::libzstd_shared,zstd::libzstd_static>)
 endif()
 
 
