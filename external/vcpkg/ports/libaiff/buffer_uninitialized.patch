diff --git "a/libaiff.c" "b/libaiff.c"
index e266802..21179f9 100644
--- "a/libaiff.c"
+++ "b/libaiff.c"
@@ -168,6 +168,9 @@ AIFF_ReadOpenW(const wchar_t *file, int flags)
 	r->buffer = NULL;
 	r->buflen = 0;
 
+	r->buffer2 = NULL;
+	r->buflen2 = 0;
+
 	return r;
 }
 
@@ -237,6 +240,9 @@ AIFF_ReadOpen(const char *file, int flags)
 	r->buffer = NULL;
 	r->buflen = 0;
 
+	r->buffer2 = NULL;
+	r->buflen2 = 0;
+
 	return r;
 }
 
@@ -577,6 +583,8 @@ err2:
 	w->segmentSize = 0;
 	w->buffer = NULL;
 	w->buflen = 0;
+	w->buffer2 = NULL;
+	w->buflen2 = 0;
 	w->tics = 0;
 
 	/*
@@ -659,6 +667,8 @@ err2:
 	w->segmentSize = 0;
 	w->buffer = NULL;
 	w->buflen = 0;
+	w->buffer2 = NULL;
+	w->buflen2 = 0;
 	w->tics = 0;
 
 	/*
