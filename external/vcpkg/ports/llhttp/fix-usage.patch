diff --git a/CMakeLists.txt b/CMakeLists.txt
index bdef288..72555c6 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -77,6 +77,10 @@ function(config_library target)
     NAMESPACE llhttp::
     DESTINATION ${CMAKE_INSTALL_LIBDIR}/cmake/llhttp
   )
+  target_include_directories(${target}
+    PRIVATE include ${CMAKE_CURRENT_BINARY_DIR}
+    INTERFACE $<INSTALL_INTERFACE:include>
+  )
 endfunction(config_library target)
 
 if(BUILD_SHARED_LIBS)
