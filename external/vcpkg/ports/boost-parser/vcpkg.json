{"$comment": "Automatically generated by scripts/boost/generate-ports.ps1", "name": "boost-parser", "version": "1.87.0", "port-version": 1, "description": "Boost parser module", "homepage": "https://www.boost.org/libs/parser", "license": "BSL-1.0", "dependencies": [{"name": "boost-assert", "version>=": "1.87.0"}, {"name": "boost-charconv", "version>=": "1.87.0"}, {"name": "boost-cmake", "version>=": "1.87.0"}, {"name": "boost-config", "version>=": "1.87.0"}, {"name": "boost-hana", "version>=": "1.87.0"}, {"name": "boost-headers", "version>=": "1.87.0"}, {"name": "boost-type-index", "version>=": "1.87.0"}]}