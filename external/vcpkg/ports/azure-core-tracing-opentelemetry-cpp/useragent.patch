diff --git a/sdk/core/azure-core-tracing-opentelemetry/CMakeLists.txt b/sdk/core/azure-core-tracing-opentelemetry/CMakeLists.txt
index 6d0dc1515..86381bb44 100644
--- a/sdk/core/azure-core-tracing-opentelemetry/CMakeLists.txt
+++ b/sdk/core/azure-core-tracing-opentelemetry/CMakeLists.txt
@@ -59,6 +59,8 @@ if (BUILD_AZURE_CORE_TRACING_OPENTELEMETRY)
           $<INSTALL_INTERFACE:include>
     )
 
+    target_compile_definitions(azure-core-tracing-opentelemetry PRIVATE _azure_BUILDING_SDK)
+
     # make sure that users can consume the project as a library.
     add_library(Azure::azure-core-tracing-opentelemetry ALIAS azure-core-tracing-opentelemetry)
 
