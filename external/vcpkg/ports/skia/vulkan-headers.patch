diff --git a/BUILD.gn b/BUILD.gn
index 3626895824..31a7fdbc33 100644
--- a/BUILD.gn
+++ b/BUILD.gn
@@ -938,6 +938,7 @@ optional("gpu_shared") {
   }

   if (skia_use_vulkan) {
+    public_deps += [ "//third_party/externals/vulkan-headers:vulkan_headers" ]
     public_defines += [ "SK_VULKAN" ]
     sources += skia_shared_vk_sources
     configs += [ ":use_skia_vulkan_headers" ]
diff --git a/include/private/gpu/vk/SkiaVulkan.h b/include/private/gpu/vk/SkiaVulkan.h
index 1e962ad60a..535ac6b88f 100644
--- a/include/private/gpu/vk/SkiaVulkan.h
+++ b/include/private/gpu/vk/SkiaVulkan.h
@@ -12,6 +12,10 @@

 // IWYU pragma: begin_exports

+#ifdef SK_USE_INTERNAL_VULKAN_HEADERS
+# undef SK_USE_INTERNAL_VULKAN_HEADERS
+#endif
+
 #if defined(SK_USE_INTERNAL_VULKAN_HEADERS) && !defined(SK_BUILD_FOR_GOOGLE3)
 #include "include/third_party/vulkan/vulkan/vulkan_core.h"
 #else
diff --git a/src/gpu/vk/vulkanmemoryallocator/VulkanMemoryAllocatorWrapper.h b/src/gpu/vk/vulkanmemoryallocator/VulkanMemoryAllocatorWrapper.h
index 487ea7cee4..9662c8cbf6 100644
--- a/src/gpu/vk/vulkanmemoryallocator/VulkanMemoryAllocatorWrapper.h
+++ b/src/gpu/vk/vulkanmemoryallocator/VulkanMemoryAllocatorWrapper.h
@@ -39,7 +39,11 @@
 // VMA outside of Skia, the client should instead tell Skia not to use VMA.
 // Then they should wrap their own instance of VMA into an implementation of
 // Skia's VulkanMemoryAllocator interface, and pass that object into context creation.
+#ifdef _WIN32
+#include "vma/vk_mem_alloc.h"  // NO_G3_REWRITE
+#else
 #include "vk_mem_alloc.h"  // NO_G3_REWRITE
+#endif
 #ifdef GR_NEEDED_TO_DEFINE_VULKAN_H
 #undef VULKAN_H_
 #endif
