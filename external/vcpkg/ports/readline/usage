the package readline can be used under windows via:

    find_package(unofficial-readline-win32 CONFIG REQUIRED)
    target_link_libraries(main PRIVATE unofficial::readline-win32::readline)

The package readline can be imported via the CMake FindPkgConfig module:
    
    find_package(PkgConfig REQUIRED)
    pkg_check_modules(readline REQUIRED IMPORTED_TARGET readline)
    target_link_libraries(main PRIVATE PkgConfig::readline)
