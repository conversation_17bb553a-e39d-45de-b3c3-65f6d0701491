diff --git a/CMakeLists.txt b/CMakeLists.txt
index fcc3f41..18859e1 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -41,6 +41,9 @@ function (set_cpp_target tgtname files)
 			"${PROJECT_SOURCE_DIR}/platform/c++11.futex"
 		)
 	endif ()
+    
+    target_include_directories("${tgtname}" PUBLIC $<INSTALL_INTERFACE:include>)
+    
 
 	target_compile_definitions ("${tgtname}" PRIVATE "${NSYNC_CPP_DEFINITIONS}")
 
@@ -88,7 +91,7 @@ if ("${CMAKE_C_COMPILER_ID}X" STREQUAL "MSVCX")
 endif ()
 
 # Pick the include directory for the operating system.
-if ("${CMAKE_SYSTEM_NAME}X" STREQUAL "WindowsX")
+if ("${CMAKE_SYSTEM_NAME}X" STREQUAL "WindowsX" OR "${CMAKE_SYSTEM_NAME}X" STREQUAL "WindowsStoreX")
 	include_directories ("${PROJECT_SOURCE_DIR}/platform/win32")
 	set (NSYNC_CPP_FLAGS "/TP")
 
@@ -232,7 +235,7 @@ elseif (("${CMAKE_SYSTEM_PROCESSOR}X" STREQUAL "ppc64X"))
 endif ()
 
 # Windows uses some include files from the posix directory also.
-if ("${CMAKE_SYSTEM_NAME}X" STREQUAL "WindowsX")
+if ("${CMAKE_SYSTEM_NAME}X" STREQUAL "WindowsX" OR "${CMAKE_SYSTEM_NAME}X" STREQUAL "WindowsStoreX")
 	include_directories ("${PROJECT_SOURCE_DIR}/platform/posix")
 endif ()
 
