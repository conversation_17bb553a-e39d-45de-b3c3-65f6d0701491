{"name": "qtquickeffectmaker", "version": "6.8.3", "description": "Qt Quick Effect Maker is a tool for creating shader effects for Qt Quick with high productivity and performance.", "homepage": "https://www.qt.io/", "license": null, "supports": "native", "dependencies": [{"name": "qtbase", "default-features": false, "features": ["gui"]}, {"name": "qtdeclarative", "default-features": false}, {"name": "qtquick3d", "default-features": false}, {"name": "qtshadertools", "default-features": false}]}