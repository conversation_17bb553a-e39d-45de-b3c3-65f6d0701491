diff --git a/proj/dll/ocilib_dll.vcxproj b/proj/dll/ocilib_dll.vcxproj
index 1caf1d3..b7d36ea 100644
--- a/proj/dll/ocilib_dll.vcxproj
+++ b/proj/dll/ocilib_dll.vcxproj
@@ -116,7 +116,7 @@
       <PrecompiledHeader>
       </PrecompiledHeader>
       <WarningLevel>EnableAllWarnings</WarningLevel>
-      <TreatWarningAsError>true</TreatWarningAsError>
+      <TreatWarningAsError>false</TreatWarningAsError>
       <DebugInformationFormat>
       </DebugInformationFormat>
       <CompileAs>CompileAsC</CompileAs>
@@ -173,7 +173,7 @@
       </DebugInformationFormat>
       <CompileAs>CompileAsC</CompileAs>
       <DisableSpecificWarnings>4255;4668;4996;4710;4711;4738;4774;4820;5045</DisableSpecificWarnings>
-      <TreatWarningAsError>true</TreatWarningAsError>
+      <TreatWarningAsError>false</TreatWarningAsError>
     </ClCompile>
     <ResourceCompile>
       <PreprocessorDefinitions>OCI_CHARSET_ANSI;%(PreprocessorDefinitions)</PreprocessorDefinitions>
@@ -223,7 +223,7 @@
       </DebugInformationFormat>
       <CompileAs>CompileAsC</CompileAs>
       <DisableSpecificWarnings>4255;4668;4996;4710;4711;4738;4774;4820;5045</DisableSpecificWarnings>
-      <TreatWarningAsError>true</TreatWarningAsError>
+      <TreatWarningAsError>false</TreatWarningAsError>
     </ClCompile>
     <ResourceCompile>
       <PreprocessorDefinitions>OCI_CHARSET_WIDE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
@@ -276,7 +276,7 @@
       </DebugInformationFormat>
       <CompileAs>CompileAsC</CompileAs>
       <DisableSpecificWarnings>4255;4668;4996;4710;4711;4738;4774;4820;5045</DisableSpecificWarnings>
-      <TreatWarningAsError>true</TreatWarningAsError>
+      <TreatWarningAsError>false</TreatWarningAsError>
     </ClCompile>
     <ResourceCompile>
       <PreprocessorDefinitions>OCI_CHARSET_WIDE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
