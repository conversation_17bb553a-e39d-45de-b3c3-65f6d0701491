diff --git a/CMakeLists.txt b/CMakeLists.txt
index 016b46d37..4caa88fce 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -50,14 +50,12 @@ set(CMAKE_VISIBILITY_INLINES_HIDDEN 1)
 
 set(CMAKE_CXX_STANDARD 14)
 
-if((CMAKE_C_COMPILER_ID STREQUAL "GNU") OR
-   (CMAKE_C_COMPILER_ID MATCHES "Clang" AND CMAKE_C_COMPILER_FRONTEND_VARIANT STREQUAL "GNU"))
+if(0)
     set(CMAKE_C_FLAGS "-Wall -Wextra -Wmissing-prototypes -Wstrict-prototypes -Wno-unused-parameter -fno-strict-aliasing")
     set(CMAKE_C_FLAGS_DEBUG "-g")
     set(CMAKE_C_FLAGS_RELEASE "-O2")
 endif()
-if((CMAKE_CXX_COMPILER_ID STREQUAL "GNU") OR
-   (CMAKE_CXX_COMPILER_ID MATCHES "Clang" AND CMAKE_CXX_COMPILER_FRONTEND_VARIANT STREQUAL "GNU"))
+if(0)
     set(CMAKE_CXX_FLAGS "-Wall -Wextra -Wno-unused-parameter -fno-strict-aliasing")
     set(CMAKE_CXX_FLAGS_DEBUG "-g")
     set(CMAKE_CXX_FLAGS_RELEASE "-O2")
@@ -146,7 +144,8 @@ endif()
 ## Configure input files
 #############################################################################
 
-include_directories(. abseil-cpp ${CMAKE_CURRENT_BINARY_DIR})
+find_package(absl REQUIRED)
+include_directories(. ${CMAKE_CURRENT_BINARY_DIR})
 
 set(ilbc_source_files
     modules/audio_coding/codecs/ilbc/abs_quant.c
@@ -310,6 +310,7 @@ add_library(ilbc ${ilbc_source_files})
 generate_export_header(ilbc)
 set_target_properties(ilbc PROPERTIES VERSION ${PROJECT_VERSION} SOVERSION 3)
 set_target_properties(ilbc PROPERTIES CLEAN_DIRECT_OUTPUT 1)
+target_link_libraries(ilbc PRIVATE absl::core_headers)
 
 add_executable(ilbc_test modules/audio_coding/codecs/ilbc/test/iLBC_test.c)
 target_link_libraries(ilbc_test ilbc)
