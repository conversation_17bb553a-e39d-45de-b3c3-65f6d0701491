diff --git a/CMakeLists.txt b/CMakeLists.txt
index 684abe0..32a5cf6 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -75,7 +75,9 @@ if (FU2_IS_TOP_LEVEL_PROJECT)
   install(EXPORT "${PROJECT_NAME}Targets"
           NAMESPACE ${PROJECT_NAME}::
           DESTINATION "${CMAKE_INSTALL_LIBDIR}/cmake/${PROJECT_NAME}")
+endif()
 
+if(0)
   # Setup CPack for bundling
   set(CPACK_GENERATOR "ZIP")
   set(CPACK_PACKAGE_VERSION_MAJOR ${PROJECT_VERSION_MAJOR})
