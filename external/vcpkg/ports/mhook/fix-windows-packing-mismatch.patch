diff --git a/CMakeLists.txt b/CMakeLists.txt
index 6e1df9f..e1e6ced 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -4,7 +4,7 @@ project(mhook)
 set_property(GLOBAL PROPERTY USE_FOLDERS ON)
 set_property(GLOBAL PROPERTY PREDEFINED_TARGETS_FOLDER "CMake")
 
-add_definitions(-DNO_SANITY_CHECKS -DUNICODE -D_UNICODE -DWIN32_LEAN_AND_MEAN)
+add_definitions(-DNO_SANITY_CHECKS -DUNICODE -D_UNICODE -DWIN32_LEAN_AND_MEAN -DWINDOWS_IGNORE_PACKING_MISMATCH)
 
 file(GLOB DisasmSrc disasm-lib/*.c disasm-lib/*.h)
 file(GLOB MhookSrc mhook-lib/*.cpp mhook-lib/*.h)
