diff --git a/opensubdiv/osd/tbbEvaluator.cpp b/opensubdiv/osd/tbbEvaluator.cpp
index c98db9f..5a4bc98 100644
--- a/opensubdiv/osd/tbbEvaluator.cpp
+++ b/opensubdiv/osd/tbbEvaluator.cpp
@@ -25,7 +25,10 @@
 #include "../osd/tbbEvaluator.h"
 #include "../osd/tbbKernel.h"
 
+#if defined(TBB_INTERFACE_VERSION_MAJOR) && (TBB_INTERFACE_VERSION_MAJOR < 12)
+// This is deprecated functionality.
 #include <tbb/task_scheduler_init.h>
+#endif
 
 namespace OpenSubdiv {
 namespace OPENSUBDIV_VERSION {
@@ -215,11 +218,15 @@ TbbEvaluator::Synchronize(void *) {
 /* static */
 void
 TbbEvaluator::SetNumThreads(int numThreads) {
+#if defined(TBB_INTERFACE_VERSION_MAJOR) && (TBB_INTERFACE_VERSION_MAJOR < 12)
+    // This is deprecated functionality. We preserve the existing behavior
+    // for consistency (when using older versions of tbb).
     if (numThreads == -1) {
         tbb::task_scheduler_init init;
     } else {
         tbb::task_scheduler_init init(numThreads);
     }
+#endif
 }
 
 }  // end namespace Osd
