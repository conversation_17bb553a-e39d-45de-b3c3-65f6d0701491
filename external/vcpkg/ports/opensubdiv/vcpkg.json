{"name": "opensubdiv", "version-semver": "3.5.0", "port-version": 3, "description": "An Open-Source subdivision surface library.", "homepage": "https://github.com/PixarAnimationStudios/OpenSubdiv", "license": "Apache-2.0", "supports": "!(arm & android) & !uwp", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"cuda": {"description": "Enable CUDA backend", "dependencies": ["cuda"]}, "dx": {"description": "Enable DirectX support", "dependencies": [{"name": "directxsdk", "features": ["xp"]}]}, "examples": {"description": "Build examples"}, "glew": {"description": "Enable GLEW wrapper library", "dependencies": ["glew"]}, "glfw": {"description": "Build components depending on GLFW", "dependencies": ["glfw3"]}, "omp": {"description": "Enable OpenMP backend"}, "opencl": {"description": "Enable OpenCL backend", "dependencies": ["opencl"]}, "opengl": {"description": "Enable OpenGL backend", "dependencies": ["opengl"]}, "ptex": {"description": "Enable Ptex support", "dependencies": ["ptex"]}, "tbb": {"description": "Enable TBB backend", "dependencies": ["tbb"]}, "true-deriv-eval": {"description": "Enable true derivative evaluation for Gregory basis patches"}, "tutorials": {"description": "Enable tutorials build"}}}