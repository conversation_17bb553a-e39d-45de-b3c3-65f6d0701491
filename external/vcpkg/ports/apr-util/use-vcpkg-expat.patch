diff --git a/CMakeLists.txt b/CMakeLists.txt
index fcbfc58..7781131 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -21,16 +21,14 @@ CMAKE_MINIMUM_REQUIRED(VERSION 2.8)
 
 FIND_PACKAGE(OpenSSL)
 
-FIND_PACKAGE(EXPAT)
-
 OPTION(APU_HAVE_CRYPTO      "Crypto support"                            OFF)
 OPTION(APU_HAVE_ODBC        "Build ODBC DBD driver"                     ON)
 OPTION(APR_HAS_LDAP         "LDAP support"                              ON)
 OPTION(INSTALL_PDB          "Install .pdb files (if generated)"         ON)
 OPTION(APR_BUILD_TESTAPR    "Build the test suite"                      OFF)
 OPTION(TEST_STATIC_LIBS     "Test programs use APR static libraries instead of shared libraries?" OFF)
-SET(APR_INCLUDE_DIR         "${CMAKE_INSTALL_PREFIX}/include"           CACHE STRING "Directory with APR include files")
-SET(APR_LIBRARIES           "${CMAKE_INSTALL_PREFIX}/lib/libapr-1.lib"  CACHE STRING "APR library to link with")
+find_path(APR_INCLUDE_DIR apr.h)
+find_library(APR_LIBRARIES NAMES libapr-1 apr-1)
 
 IF(NOT EXISTS "${APR_INCLUDE_DIR}/apr.h")
   MESSAGE(FATAL_ERROR "APR include directory ${APR_INCLUDE_DIR} is not correct.")
@@ -61,13 +59,13 @@ IF(APR_HAS_LDAP)
   SET(apr_has_ldap_10 1)
 ENDIF()
 
-IF(NOT EXPAT_FOUND)
+find_package(expat)
+set(XMLLIB_INCLUDE_DIR ${EXPAT_INCLUDE_DIRS})
+set(XMLLIB_LIBRARIES ${EXPAT_LIBRARIES})
+IF(NOT XMLLIB_LIBRARIES)
   MESSAGE(FATAL_ERROR "Expat is required, and it wasn't found!")
 ENDIF()
 
-SET(XMLLIB_INCLUDE_DIR   ${EXPAT_INCLUDE_DIRS})
-SET(XMLLIB_LIBRARIES     ${EXPAT_LIBRARIES})
-
 SET(LDAP_LIBRARIES)
 IF(APR_HAS_LDAP)
   SET(LDAP_LIBRARIES wldap32)
@@ -229,17 +227,21 @@ SET(dbd_drivers)
 # Note: The WINNT definition on some targets is used only by libaprutil.rc.
 
 # libaprutil-1 is shared, aprutil-1 is static
+if(BUILD_SHARED_LIBS)
 ADD_LIBRARY(libaprutil-1 SHARED ${APR_SOURCES} ${APR_PUBLIC_HEADERS_GENERATED} libaprutil.rc)
 SET(install_targets ${install_targets} libaprutil-1)
 SET(install_bin_pdb ${install_bin_pdb} ${PROJECT_BINARY_DIR}/libaprutil-1.pdb)
 TARGET_LINK_LIBRARIES(libaprutil-1 ${APR_LIBRARIES} ${XMLLIB_LIBRARIES})
 SET_TARGET_PROPERTIES(libaprutil-1 PROPERTIES COMPILE_DEFINITIONS "APU_DECLARE_EXPORT;APR_DECLARE_IMPORT;XML_STATIC;WINNT")
 
+else(BUILD_SHARED_LIBS)
 ADD_LIBRARY(aprutil-1 STATIC ${APR_SOURCES} ${APR_PUBLIC_HEADERS_GENERATED})
 SET(install_targets ${install_targets} aprutil-1)
 TARGET_LINK_LIBRARIES(aprutil-1 ${APR_LIBRARIES} ${XMLLIB_LIBRARIES})
 SET_TARGET_PROPERTIES(aprutil-1 PROPERTIES COMPILE_DEFINITIONS "APU_DECLARE_STATIC;APR_DECLARE_STATIC;APU_DSO_MODULE_BUILD;XML_STATIC")
+endif()
 
+if(BUILD_SHARED_LIBS)
 IF(APU_HAVE_CRYPTO)
   IF(NOT OPENSSL_FOUND)
     MESSAGE(FATAL_ERROR "Only OpenSSL-based crypto is currently implemented in the cmake build")
@@ -265,7 +267,7 @@ IF(APU_HAVE_ODBC)
 ENDIF()
 
 IF(APR_HAS_LDAP)
-  ADD_LIBRARY(apr_ldap-1 SHARED ldap/apr_ldap_init.c ldap/apr_ldap_option.c 
+  ADD_LIBRARY(apr_ldap-1 SHARED ldap/apr_ldap_init.c ldap/apr_ldap_option.c
               ldap/apr_ldap_rebind.c libaprutil.rc)
   SET(install_targets ${install_targets} apr_ldap-1)
   SET(install_bin_pdb ${install_bin_pdb} ${PROJECT_BINARY_DIR}/apr_ldap-1.pdb)
@@ -276,6 +278,7 @@ IF(APR_HAS_LDAP)
 ELSE()
   SET(apr_ldap_libraries)
 ENDIF()
+endif()
 
 IF(APR_BUILD_TESTAPR)
   ENABLE_TESTING()
@@ -283,13 +286,13 @@ IF(APR_BUILD_TESTAPR)
   ADD_CUSTOM_TARGET(check COMMAND ${CMAKE_CTEST_COMMAND} --verbose)
 
   # copy data files to build directory so that we can run programs from there
-  EXECUTE_PROCESS(COMMAND ${CMAKE_COMMAND} -E make_directory 
+  EXECUTE_PROCESS(COMMAND ${CMAKE_COMMAND} -E make_directory
                   ${PROJECT_BINARY_DIR}/data)
-  EXECUTE_PROCESS(COMMAND ${CMAKE_COMMAND} -E copy_if_different 
+  EXECUTE_PROCESS(COMMAND ${CMAKE_COMMAND} -E copy_if_different
                   ${PROJECT_SOURCE_DIR}/test/data/billion-laughs.xml
                   ${PROJECT_BINARY_DIR}/data/billion-laughs.xml)
 
-  IF(TEST_STATIC_LIBS)
+  IF(NOT BUILD_SHARED_LIBS)
     SET(whichapr    aprutil-1)
     SET(apiflag     "-DAPR_DECLARE_STATIC -DAPU_DECLARE_STATIC")
   ELSE()
@@ -325,13 +328,9 @@ INSTALL(TARGETS ${install_targets}
         ARCHIVE DESTINATION lib
        )
 
-IF(INSTALL_PDB)
-  INSTALL(FILES ${install_bin_pdb}
-          DESTINATION bin
-          CONFIGURATIONS RelWithDebInfo Debug)
-ENDIF()
-
-INSTALL(FILES ${APR_PUBLIC_HEADERS_STATIC} ${APR_PUBLIC_HEADERS_GENERATED} DESTINATION include)
+if(NOT DISABLE_INSTALL_HEADERS)
+  INSTALL(FILES ${APR_PUBLIC_HEADERS_STATIC} ${APR_PUBLIC_HEADERS_GENERATED} DESTINATION include)
+endif()
 
 STRING(TOUPPER "${CMAKE_BUILD_TYPE}" buildtype)
 MESSAGE(STATUS "")
