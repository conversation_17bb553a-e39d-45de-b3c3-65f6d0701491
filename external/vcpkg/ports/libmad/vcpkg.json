{"name": "lib<PERSON>", "version": "0.16.4", "port-version": 3, "description": "high-quality MPEG audio decoder", "homepage": "http://codeberg.org/tenacityteam/libmad", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "default-features": [{"name": "aso", "platform": "x86 | x64 | arm"}], "features": {"aso": {"description": "Enable CPU architecture-specific optimizations (x86, ARM and MIPS only)", "supports": "x86 | x64 | arm"}}}