diff --git a/lib/TH/cmake/FindARM.cmake b/lib/TH/cmake/FindARM.cmake
index 2dcb2a2..9d68601 100644
--- a/lib/TH/cmake/FindARM.cmake
+++ b/lib/TH/cmake/FindARM.cmake
@@ -45,7 +45,7 @@ ELSEIF(CMAKE_SYSTEM_NAME MATCHES "Darwin")
       CPUINFO)
 
    #neon instruction can be found on the majority part of modern ARM processor
-   STRING(REGEX REPLACE "^.*(neon).*$" "\\1" NEON_THERE ${CPUINFO})
+   STRING(REGEX REPLACE "^.*(neon).*$" "\\1" NEON_THERE "${CPUINFO}")
    STRING(COMPARE EQUAL "neon" "${NEON_THERE}" NEON_TRUE)
    IF (NEON_TRUE)
       set(NEON_FOUND true CACHE BOOL "NEON available on host")
