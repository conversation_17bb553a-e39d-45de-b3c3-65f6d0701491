{"name": "redis-plus-plus", "version-semver": "1.3.14", "description": "This is a C++ client for Redis. It's based on hiredis, and written in C++ 11", "homepage": "https://github.com/sewenew/redis-plus-plus", "license": "Apache-2.0", "dependencies": ["<PERSON><PERSON>", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"async": {"description": "Build with async", "dependencies": ["libuv"]}, "async-std": {"description": "Build async with std::future", "dependencies": [{"name": "redis-plus-plus", "default-features": false, "features": ["async"]}]}, "cxx17": {"description": "Build redis-plus-plus with cxx 17 standard"}, "tls": {"description": "Build with TLS support", "dependencies": [{"name": "<PERSON><PERSON>", "default-features": false, "features": ["ssl"]}]}}}