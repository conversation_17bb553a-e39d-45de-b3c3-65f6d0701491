diff --git a/CMakeLists.txt b/CMakeLists.txt
index ae2507e..c329f9b 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -45,8 +45,8 @@ if(REDIS_PLUS_PLUS_BUILD_ASYNC)
         message(STATUS "redis-plus-plus build async interface with libuv")
 
         # libuv dependency
-        find_path(REDIS_PLUS_PLUS_ASYNC_LIB_HEADER NAMES uv.h)
-        find_library(REDIS_PLUS_PLUS_ASYNC_LIB uv)
+        find_package(libuv CONFIG REQUIRED)
+        set(REDIS_PLUS_PLUS_ASYNC_LIB $<IF:$<TARGET_EXISTS:libuv::uv_a>,libuv::uv_a,libuv::uv>)
     else()
         message(FATAL_ERROR "invalid REDIS_PLUS_PLUS_BUILD_ASYNC")
     endif()
@@ -228,7 +228,6 @@ if(REDIS_PLUS_PLUS_BUILD_STATIC)
 
     if(REDIS_PLUS_PLUS_BUILD_ASYNC)
         target_include_directories(${STATIC_LIB} PUBLIC $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/${REDIS_PLUS_PLUS_ASYNC_FUTURE_HEADER}>)
-        target_include_directories(${STATIC_LIB} PUBLIC $<BUILD_INTERFACE:${REDIS_PLUS_PLUS_ASYNC_LIB_HEADER}>)
         if(REDIS_PLUS_PLUS_ASYNC_FUTURE STREQUAL "boost")
             target_include_directories(${STATIC_LIB} SYSTEM PUBLIC $<BUILD_INTERFACE:${Boost_INCLUDE_DIR}>)
         endif()
