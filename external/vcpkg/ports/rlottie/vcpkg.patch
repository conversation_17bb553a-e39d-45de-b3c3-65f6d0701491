diff --git a/CMakeLists.txt b/CMakeLists.txt
index 38a9862..eca77d5 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -16,6 +16,10 @@ endif()
 add_library( rlottie )
 set_target_properties( rlottie PROPERTIES DEFINE_SYMBOL RLOTTIE_BUILD )
 
+#use vcpkg ports
+find_package(RapidJSON CONFIG REQUIRED)
+target_link_libraries(rlottie PRIVATE rapidjson)
+
 #declare version of the target
 set(player_version_major 0)
 set(player_version_minor 2)
@@ -75,8 +79,6 @@ endif()
 
 if (WIN32 AND NOT BUILD_SHARED_LIBS)
     target_compile_definitions(rlottie PUBLIC -DRLOTTIE_BUILD=0)
-    set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} /MT")
-    set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} /MTd")
 endif()
 
 #declare dependancy
@@ -88,13 +90,6 @@ target_link_libraries(rlottie
                         "${CMAKE_THREAD_LIBS_INIT}"
                      )
 
-if (NOT APPLE AND NOT WIN32)
-    target_link_libraries(rlottie
-                        PRIVATE
-                            "-Wl,--version-script=${CMAKE_CURRENT_SOURCE_DIR}/rlottie.expmap"
-                          )
-endif()
-
 if (LOTTIE_MODULE)
     # for dlopen, dlsym and dlclose dependancy
     target_link_libraries(rlottie PRIVATE ${CMAKE_DL_LIBS})
@@ -165,6 +160,7 @@ install( TARGETS rlottie EXPORT rlottie-targets
          LIBRARY     DESTINATION    ${LIB_INSTALL_DIR}
          ARCHIVE     DESTINATION    ${LIB_INSTALL_DIR}
          INCLUDES    DESTINATION    include
+         RUNTIME     DESTINATION    bin
        )
 
 #install config file.
diff --git a/src/lottie/lottieparser.cpp b/src/lottie/lottieparser.cpp
index b33effa..c7bb421 100644
--- a/src/lottie/lottieparser.cpp
+++ b/src/lottie/lottieparser.cpp
@@ -56,7 +56,7 @@
 #include <array>
 
 #include "lottiemodel.h"
-#include "rapidjson/document.h"
+#include <rapidjson/document.h>
 #include "zip/zip.h"
 
 RAPIDJSON_DIAG_PUSH
diff --git a/src/vector/CMakeLists.txt b/src/vector/CMakeLists.txt
index 3ae96e6..15b86df 100644
--- a/src/vector/CMakeLists.txt
+++ b/src/vector/CMakeLists.txt
@@ -1,5 +1,4 @@
 add_subdirectory(freetype)
-add_subdirectory(pixman)
 add_subdirectory(stb)
 
 
diff --git a/src/vector/vdrawhelper_neon.cpp b/src/vector/vdrawhelper_neon.cpp
index 681eabb..e178012 100644
--- a/src/vector/vdrawhelper_neon.cpp
+++ b/src/vector/vdrawhelper_neon.cpp
@@ -2,28 +2,20 @@
 
 #include "vdrawhelper.h"
 
-extern "C" void pixman_composite_src_n_8888_asm_neon(int32_t w, int32_t h,
-                                                     uint32_t *dst,
-                                                     int32_t   dst_stride,
-                                                     uint32_t  src);
-
-extern "C" void pixman_composite_over_n_8888_asm_neon(int32_t w, int32_t h,
-                                                      uint32_t *dst,
-                                                      int32_t   dst_stride,
-                                                      uint32_t  src);
-
 void memfill32(uint32_t *dest, uint32_t value, int length)
 {
-    pixman_composite_src_n_8888_asm_neon(length, 1, dest, length, value);
+    memset(dest, value, length);
 }
 
 static void color_SourceOver(uint32_t *dest, int length,
                                       uint32_t color,
                                      uint32_t const_alpha)
 {
+    int ialpha, i;
     if (const_alpha != 255) color = BYTE_MUL(color, const_alpha);
 
-    pixman_composite_over_n_8888_asm_neon(length, 1, dest, length, color);
+    ialpha = 255 - vAlpha(color);
+    for (i = 0; i < length; ++i) dest[i] = color + BYTE_MUL(dest[i], ialpha);
 }
 
 void RenderFuncTable::neon()
