
set(@PORT@_VERSION "@VERSION@")

if(TARGET unofficial-@PORT@)
    set(unofficial-@PORT@_FOUND TRUE)
    return()
endif()

include(CMakeFindDependencyMacro)

find_dependency(Qt@USE_QT_VERSION@ REQUIRED COMPONENTS Core)

add_library(unofficial-@PORT@ INTERFACE)

target_link_libraries(unofficial-@PORT@ INTERFACE Qt::Core)
target_include_directories(unofficial-@PORT@ INTERFACE "${CMAKE_CURRENT_LIST_DIR}/../../include/@PORT@/include")

set(unofficial-@PORT@_FOUND TRUE)

