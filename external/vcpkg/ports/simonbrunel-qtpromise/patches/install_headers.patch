--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -21,6 +21,14 @@ add_library(qtpromise::qtpromise ALIAS qtpromise)
 target_link_libraries(qtpromise INTERFACE Qt${QT_VERSION_MAJOR}::Core)
 target_include_directories(qtpromise INTERFACE "${CMAKE_CURRENT_LIST_DIR}/include")
 
+if(QTPROMISE_HEADER_INSTALL_DESTINATION)
+    install(
+        DIRECTORY "include" "src"
+        DESTINATION "${QTPROMISE_HEADER_INSTALL_DESTINATION}" 
+        CONFIGURATIONS "${QTPROMISE_HEADER_INSTALL_COMPONENTS}"
+    )
+endif()
+
 add_definitions(
     -DQT_DEPRECATED_WARNINGS
     -DQT_NO_KEYWORDS
