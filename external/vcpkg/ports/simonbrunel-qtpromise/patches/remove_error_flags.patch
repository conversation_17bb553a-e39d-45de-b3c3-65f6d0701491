--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -34,6 +34,7 @@ add_definitions(
     -DQT_NO_KEYWORDS
 )
 
+#[[ remove error flags
 # https://github.com/simonbrunel/qtpromise/issues/10
 if(CMAKE_CXX_COMPILER_ID MATCHES "GNU")
     # https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html
@@ -83,8 +84,10 @@ elseif(CMAKE_CXX_COMPILER_ID MATCHES "MSVC")
         /WX
     )
 endif()
+]]
 
 if(NOT SUBPROJECT)
     enable_testing()
     add_subdirectory(tests)
 endif()
+
