{"name": "libftdi1", "version": "1.5", "port-version": 5, "description": "FTDI USB driver with bitbang mode", "homepage": "https://www.intra2net.com/en/developer/libftdi/", "license": "LGPL-2.1-only AND MIT", "dependencies": [{"name": "libusb", "default-features": false}, {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"cpp": {"description": "C++ bindings", "license": "GPL-2.0-only WITH eCos-exception-2.0", "dependencies": ["boost-smart-ptr"]}}}