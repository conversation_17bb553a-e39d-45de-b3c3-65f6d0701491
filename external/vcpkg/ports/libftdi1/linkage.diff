diff --git a/ftdipp/CMakeLists.txt b/ftdipp/CMakeLists.txt
index fac5bcc..8a20a04 100644
--- a/ftdipp/CMakeLists.txt
+++ b/ftdipp/CMakeLists.txt
@@ -11,6 +11,11 @@ include_directories(BEFORE ${CMAKE_CURRENT_BINARY_DIR}
 
 include_directories(${Boost_INCLUDE_DIRS})
 
+if(WIN32)
+	set(BUILD_SHARED_LIBS OFF)
+	set(STATICLIBS ON)
+endif()
+if(BUILD_SHARED_LIBS)
 # Shared library
 add_library(ftdipp1 SHARED ${cpp_sources})
 
@@ -28,6 +33,7 @@ install ( TARGETS ftdipp1
 					LIBRARY DESTINATION lib${LIB_SUFFIX}
 					ARCHIVE DESTINATION lib${LIB_SUFFIX}
 				)
+endif()
 
 # Static library
 if ( STATICLIBS )
diff --git a/src/CMakeLists.txt b/src/CMakeLists.txt
index 17b3617..087fba8 100644
--- a/src/CMakeLists.txt
+++ b/src/CMakeLists.txt
@@ -19,8 +19,12 @@ configure_file(ftdi_version_i.h.in "${CMAKE_CURRENT_BINARY_DIR}/ftdi_version_i.h
 
 # Targets
 set(c_sources     ${CMAKE_CURRENT_SOURCE_DIR}/ftdi.c ${CMAKE_CURRENT_SOURCE_DIR}/ftdi_stream.c CACHE INTERNAL "List of c sources" )
+if(WIN32)
+  set(c_sources     ${CMAKE_CURRENT_SOURCE_DIR}/ftdi.c ${CMAKE_CURRENT_SOURCE_DIR}/exports.def CACHE INTERNAL "List of c sources" )
+endif()
 set(c_headers     ${CMAKE_CURRENT_SOURCE_DIR}/ftdi.h CACHE INTERNAL "List of c headers" )
 
+if(BUILD_SHARED_LIBS)
 add_library(ftdi1 SHARED ${c_sources})
 
 math(EXPR VERSION_FIXUP "${MAJOR_VERSION} + 1")    # Compatibility with previous releases
@@ -37,6 +41,7 @@ install ( TARGETS ftdi1
           LIBRARY DESTINATION lib${LIB_SUFFIX}
           ARCHIVE DESTINATION lib${LIB_SUFFIX}
         )
+endif()
 
 if ( STATICLIBS )
   add_library(ftdi1-static STATIC ${c_sources})
