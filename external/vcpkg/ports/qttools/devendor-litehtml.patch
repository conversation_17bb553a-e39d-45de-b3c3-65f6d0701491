diff --git a/src/assistant/CMakeLists.txt b/src/assistant/CMakeLists.txt
index 5d2bf2b67c..abd9b513d3 100644
--- a/src/assistant/CMakeLists.txt
+++ b/src/assistant/CMakeLists.txt
@@ -34,7 +34,7 @@ if(TARGET qlitehtml)
     # but found in the system, because they are imported only to the subdirectory scope
     # where find_package was called. But that's fine, we wouldn't be able to set compiler flags
     # on them anyway.
-    if(TARGET litehtml)
+    if(0)
         qt_internal_set_exceptions_flags(litehtml OFF)
         qt_disable_warnings(litehtml)
     endif()
diff --git a/src/assistant/qlitehtml/src/container_qpainter_p.h b/src/assistant/qlitehtml/src/container_qpainter_p.h
index 23c55b4..4ae1cc1 100644
--- a/src/assistant/qlitehtml/src/container_qpainter_p.h
+++ b/src/assistant/qlitehtml/src/container_qpainter_p.h
@@ -14,7 +14,6 @@
 #include <QString>
 #include <QVector>
 
-#include <litehtml/types.h>
 #include <unordered_map>
 
 class Selection
