diff --git a/KF5I18nConfig.cmake.in b/KF5I18nConfig.cmake.in
index 364cc43..af7cd3b 100644
--- a/KF5I18nConfig.cmake.in
+++ b/KF5I18nConfig.cmake.in
@@ -8,3 +8,4 @@ include("${CMAKE_CURRENT_LIST_DIR}/KF5I18nTargets.cmake")
 @PACKAGE_INCLUDE_QCHTARGETS@
 include("${CMAKE_CURRENT_LIST_DIR}/KF5I18nMacros.cmake")
 
+find_dependency(Iconv)
diff --git a/src/i18n/CMakeLists.txt b/src/i18n/CMakeLists.txt
index c6b510c..0a25eca 100644
--- a/src/i18n/CMakeLists.txt
+++ b/src/i18n/CMakeLists.txt
@@ -40,12 +40,16 @@ ecm_generate_export_header(KF5I18n
     EXCLUDE_DEPRECATED_BEFORE_AND_AT ${EXCLUDE_DEPRECATED_BEFORE_AND_AT}
 )
 
+find_package(Iconv REQUIRED)
+
 target_include_directories(KF5I18n INTERFACE "$<INSTALL_INTERFACE:${KDE_INSTALL_INCLUDEDIR_KF}/KI18n>")
 target_include_directories(KF5I18n PRIVATE ${LibIntl_INCLUDE_DIRS})
+target_include_directories(KF5I18n PRIVATE ${Iconv_INCLUDE_DIRS})
 
 target_link_libraries(KF5I18n PUBLIC Qt${QT_MAJOR_VERSION}::Core)
 # This is only required for platforms which don't use glibc (with glibc LibIntl_LIBRARIES will be empty)
 target_link_libraries(KF5I18n PRIVATE ${LibIntl_LIBRARIES})
+target_link_libraries(KF5I18n PRIVATE ${Iconv_LIBRARIES})
 if (ANDROID)
     if (QT_MAJOR_VERSION EQUAL "5")
         target_link_libraries(KF5I18n PRIVATE Qt5::AndroidExtras)
