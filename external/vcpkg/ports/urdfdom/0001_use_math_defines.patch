diff --git a/urdf_parser/CMakeLists.txt b/urdf_parser/CMakeLists.txt
index ac7c079..67f175d 100644
--- a/urdf_parser/CMakeLists.txt
+++ b/urdf_parser/CMakeLists.txt
@@ -23,6 +23,10 @@ macro(add_urdfdom_library)
     SOVERSION ${URDF_MAJOR_MINOR_VERSION})
 endmacro()
 
+if(MSVC)
+  add_definitions(-D_USE_MATH_DEFINES)
+endif()
+
 if(TARGET console_bridge::console_bridge)
   set(console_bridge_link_libs "console_bridge::console_bridge")
 else()
