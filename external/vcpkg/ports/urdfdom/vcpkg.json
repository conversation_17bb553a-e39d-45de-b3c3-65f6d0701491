{"name": "urd<PERSON><PERSON>", "version": "3.1.1", "description": "Provides core data structures and a simple XML parsers for populating the class data structures from an URDF file.", "homepage": "https://github.com/ros/urdfdom", "license": "BSD-3-<PERSON><PERSON>", "supports": "!staticcrt", "dependencies": ["console-bridge", "tinyxml", "urdfdom-headers", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}]}