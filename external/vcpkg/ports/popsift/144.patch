From 7b664e27ca2865f3e06101d50415f2943d2de58c Mon Sep 17 00:00:00 2001
From: Azhng <<EMAIL>>
Date: Tue, 21 Feb 2023 19:24:57 +0000
Subject: [PATCH] add required thrust include for s_filtergrid.cu

---
 src/popsift/s_filtergrid.cu | 2 ++
 1 file changed, 2 insertions(+)

diff --git a/src/popsift/s_filtergrid.cu b/src/popsift/s_filtergrid.cu
index 301c6a96..a766c2de 100644
--- a/src/popsift/s_filtergrid.cu
+++ b/src/popsift/s_filtergrid.cu
@@ -19,9 +19,11 @@
 #if ! POPSIFT_IS_DEFINED(POPSIFT_DISABLE_GRID_FILTER)
 
 #include <thrust/copy.h>
+#include <thrust/count.h>
 #include <thrust/device_vector.h>
 #include <thrust/execution_policy.h>
 #include <thrust/host_vector.h>
+#include <thrust/iterator/constant_iterator.h>
 #include <thrust/iterator/discard_iterator.h>
 #include <thrust/sequence.h>
 #include <thrust/sort.h>
