{"name": "popsift", "version": "0.9", "port-version": 5, "description": "PopSift is an implementation of the SIFT algorithm in CUDA.", "homepage": "https://github.com/alicevision/popsift", "supports": "!(uwp | arm | arm64 | android | x86)", "dependencies": ["cuda", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"apps": {"description": "Application programs for popsift (detection and matching)", "dependencies": ["boost-algorithm", "boost-filesystem", "boost-program-options", "boost-system"]}}}