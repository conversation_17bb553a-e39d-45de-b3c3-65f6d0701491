diff --git a/cmake/ChooseCudaCC.cmake b/cmake/ChooseCudaCC.cmake
index d9bc6c2..0ac4fa9 100755
--- a/cmake/ChooseCudaCC.cmake
+++ b/cmake/ChooseCudaCC.cmake
@@ -65,7 +65,8 @@ function(choose<PERSON><PERSON>CC SUPPORTED_CC SUPPORTED_GENCODE_FLAGS)
 
   set(CC_LIST_BY_SYSTEM_PROCESSOR "")
   if(CMAKE_SYSTEM_PROCESSOR IN_LIST OTHER_SUPPORTED_PROCESSORS)
-    list(APPEND CC_LIST_BY_SYSTEM_PROCESSOR "20;21;30;35;50;52;60;61;70;75;80;86")
+    # 87 is intentionally omitted, see discussion in https://github.com/alicevision/popsift/pull/146
+    list(APPEND CC_LIST_BY_SYSTEM_PROCESSOR "20;21;30;35;50;52;60;61;70;75;80;86;89;90")
   endif()
   if(CMAKE_SYSTEM_PROCESSOR IN_LIST TEGRA_SUPPORTED_PROCESSORS)
     list(APPEND CC_LIST_BY_SYSTEM_PROCESSOR "32;53;62;72")
@@ -78,10 +79,20 @@ function(chooseCudaCC SUPPORTED_CC SUPPORTED_GENCODE_FLAGS)
   # Default setting of the CUDA CC versions to compile.
   # Shortening the lists saves a lot of compile time.
   #
-  set(CUDA_MIN_CC 20)
-  set(CUDA_MAX_CC 86)
-  if(CUDA_VERSION_MAJOR GREATER_EQUAL 11)
+
+  # The current version last time this list was updated was CUDA 12.1.
+  if(CUDA_VERSION VERSION_GREATER_EQUAL 12)
+    set(CUDA_MIN_CC 50)
+    set(CUDA_MAX_CC 90)
+  elseif(CUDA_VERSION VERSION_GREATER_EQUAL 11.8)
+    set(CUDA_MIN_CC 35)
+    set(CUDA_MAX_CC 90)
+  elseif(CUDA_VERSION VERSION_GREATER_EQUAL 11.1)
+    set(CUDA_MIN_CC 35)
+    set(CUDA_MAX_CC 86)
+  elseif(CUDA_VERSION_MAJOR GREATER_EQUAL 11)
     set(CUDA_MIN_CC 35)
+    set(CUDA_MAX_CC 80)
   elseif(CUDA_VERSION_MAJOR GREATER_EQUAL 10)
     set(CUDA_MIN_CC 30)
     set(CUDA_MAX_CC 75)
@@ -89,8 +100,10 @@ function(chooseCudaCC SUPPORTED_CC SUPPORTED_GENCODE_FLAGS)
     set(CUDA_MIN_CC 30)
     set(CUDA_MAX_CC 72)
   elseif(CUDA_VERSION_MAJOR GREATER_EQUAL 8)
+    set(CUDA_MIN_CC 20)
     set(CUDA_MAX_CC 62)
   elseif(CUDA_VERSION_MAJOR GREATER_EQUAL 7)
+    set(CUDA_MIN_CC 20)
     set(CUDA_MAX_CC 53)
   else()
     message(FATAL_ERROR "We do not support a CUDA SDK below version 7.0")
