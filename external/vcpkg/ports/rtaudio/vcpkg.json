{"name": "rtaudio", "version": "6.0.1", "port-version": 1, "description": "A set of C++ classes that provide a common API for realtime audio input/output across Linux (native ALSA, JACK, PulseAudio and OSS), Macintosh OS X (CoreAudio and JACK), and Windows (DirectSound, ASIO and WASAPI) operating systems.", "homepage": "https://github.com/thestk/rtaudio", "license": null, "supports": "!uwp", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"alsa": {"description": "Build ALSA API", "supports": "linux", "dependencies": ["alsa"]}, "asio": {"description": "Build with ASIO backend", "supports": "windows"}, "pulse": {"description": "Build with PulseAudio backend", "supports": "linux", "dependencies": ["pulseaudio"]}}}