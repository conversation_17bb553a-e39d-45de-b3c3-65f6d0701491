{"name": "gz-physics", "version": "8.0.0", "port-version": 2, "description": "component of Gazebo, provides an abstract physics interface designed to support simulation and rapid development of robot applications.", "homepage": "https://gazebosim.org/libs/physics", "license": "Apache-2.0", "dependencies": ["bullet3", {"name": "<PERSON><PERSON>", "features": ["collision-bullet", "collision-ode", "utils", "utils-urdf"]}, "eigen3", "gz-cmake", "gz-common", "gz-math", "gz-plugin", "gz-utils", {"name": "ignition-modularscripts", "host": true}, "sdformat"]}