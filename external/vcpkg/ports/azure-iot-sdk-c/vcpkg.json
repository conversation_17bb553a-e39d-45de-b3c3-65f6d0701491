{"name": "azure-iot-sdk-c", "version-date": "2025-03-31", "description": "A C99 SDK for connecting devices to Microsoft Azure IoT services", "homepage": "https://github.com/Azure/azure-iot-sdk-c", "license": "MIT", "dependencies": ["azure-c-shared-utility", "azure-macro-utils-c", "azure-uamqp-c", "azure-uhttp-c", "azure-umqtt-c", "parson", "umock-c", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"use-prov-client": {"description": "Enables device provisioning client for DPS"}}}