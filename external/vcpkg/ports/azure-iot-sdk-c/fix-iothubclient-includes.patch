diff --git a/iothub_client/CMakeLists.txt b/iothub_client/CMakeLists.txt
index 5a8f5573c..07ab3b7cb 100644
--- a/iothub_client/CMakeLists.txt
+++ b/iothub_client/CMakeLists.txt
@@ -442,8 +442,6 @@ target_include_directories(iothub_client
     PUBLIC
     $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/inc>
-    $<INSTALL_INTERFACE:${CMAKE_INSTALL_INCLUDEDIR}/azureiot/include>
+    $<INSTALL_INTERFACE:${CMAKE_INSTALL_INCLUDEDIR}/azureiot>
-    $<BUILD_INTERFACE:${PROJECT_SOURCE_DIR}/deps/umock-c/inc>
-    $<BUILD_INTERFACE:${PROJECT_SOURCE_DIR}/deps/azure-macro-utils-c/inc>
 )
 applyXcodeBuildFlagsIfNeeded(iothub_client)
 target_link_libraries(iothub_client ${iothub_client_libs})
