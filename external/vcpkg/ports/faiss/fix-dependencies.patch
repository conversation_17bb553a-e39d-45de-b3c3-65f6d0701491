diff --git a/cmake/faiss-config.cmake.in b/cmake/faiss-config.cmake.in
index 43ea9d4..437a7f8 100644
--- a/cmake/faiss-config.cmake.in
+++ b/cmake/faiss-config.cmake.in
@@ -4,4 +4,7 @@
 # This source code is licensed under the BSD-style license found in the
 # LICENSE file in the root directory of this source tree.
 
+find_dependency(OpenMP REQUIRED)
+find_dependency(BLAS REQUIRED)
+find_dependency(LAPACK REQUIRED)
 include("${CMAKE_CURRENT_LIST_DIR}/faiss-targets.cmake")
diff --git a/faiss/CMakeLists.txt b/faiss/CMakeLists.txt
index a890a46..5a67a92 100644
--- a/faiss/CMakeLists.txt
+++ b/faiss/CMakeLists.txt
@@ -300,20 +300,20 @@ target_link_libraries(faiss_avx2 PRIVATE OpenMP::OpenMP_CXX)
 target_link_libraries(faiss_avx512 PRIVATE OpenMP::OpenMP_CXX)
 
 find_package(MKL)
-if(MKL_FOUND)
+if(0)
   target_link_libraries(faiss PRIVATE ${MKL_LIBRARIES})
   target_link_libraries(faiss_avx2 PRIVATE ${MKL_LIBRARIES})
   target_link_libraries(faiss_avx512 PRIVATE ${MKL_LIBRARIES})
 else()
   find_package(BLAS REQUIRED)
-  target_link_libraries(faiss PRIVATE ${BLAS_LIBRARIES})
-  target_link_libraries(faiss_avx2 PRIVATE ${BLAS_LIBRARIES})
-  target_link_libraries(faiss_avx512 PRIVATE ${BLAS_LIBRARIES})
+  target_link_libraries(faiss PRIVATE BLAS::BLAS)
+  target_link_libraries(faiss_avx2 PRIVATE BLAS::BLAS)
+  target_link_libraries(faiss_avx512 PRIVATE BLAS::BLAS)
 
   find_package(LAPACK REQUIRED)
-  target_link_libraries(faiss PRIVATE ${LAPACK_LIBRARIES})
-  target_link_libraries(faiss_avx2 PRIVATE ${LAPACK_LIBRARIES})
-  target_link_libraries(faiss_avx512 PRIVATE ${LAPACK_LIBRARIES})
+  target_link_libraries(faiss PRIVATE LAPACK::LAPACK)
+  target_link_libraries(faiss_avx2 PRIVATE LAPACK::LAPACK)
+  target_link_libraries(faiss_avx512 PRIVATE LAPACK::LAPACK)
 endif()
 
 install(TARGETS faiss
