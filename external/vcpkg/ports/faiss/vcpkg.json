{"name": "faiss", "version": "1.8.0", "description": "Faiss is a library for efficient similarity search and clustering of dense vectors.", "homepage": "https://github.com/facebookresearch/faiss", "license": "MIT", "supports": "!uwp & !osx & !x86 & !(arm64 & windows)", "dependencies": ["lapack", "openblas", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"gpu": {"description": "Whether to enable GPU support", "supports": "!uwp & !osx & !x86 & !windows", "dependencies": ["cuda"]}}}