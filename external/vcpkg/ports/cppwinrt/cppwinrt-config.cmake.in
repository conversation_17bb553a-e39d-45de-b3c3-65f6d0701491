get_filename_component(_cppwinrt_root "${CMAKE_CURRENT_LIST_DIR}" PATH)
get_filename_component(_cppwinrt_root "${_cppwinrt_root}" PATH)

set(_cppwinrt_exe "${_cppwinrt_root}/@tool_path@")
if (EXISTS "${_cppwinrt_exe}")

   add_library(Microsoft::CppWinRT INTERFACE IMPORTED)
   set_target_properties(Microsoft::CppWinRT PROPERTIES
      INTERFACE_COMPILE_FEATURES cxx_std_17
      INTERFACE_INCLUDE_DIRECTORIES "${_cppwinrt_root}/include"
      INTERFACE_LINK_LIBRARIES "${_cppwinrt_root}/lib/@lib_name@"
   )

   set(cppwinrt_FOUND TRUE)
   set(CPPWINRT_TOOL ${_cppwinrt_exe})

else()

    set(cppwinrt_FOUND FALSE)

endif()

unset(_cppwinrt_root)
unset(_cppwinrt_exe)
