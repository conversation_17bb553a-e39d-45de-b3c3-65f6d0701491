cmake_policy(PUSH)
cmake_policy(SET CMP0012 NEW)
cmake_policy(SET CMP0057 NEW)
set(z_vcpkg_tiff_find_options "")
if("REQUIRED" IN_LIST ARGS)
    list(APPEND z_vcpkg_tiff_find_options "REQUIRED")
endif()
if("QUIET" IN_LIST ARGS)
    list(APPEND z_vcpkg_tiff_find_options "QUIET")
endif()

_find_package(${ARGS})

if(TIFF_FOUND AND "@VCPKG_LIBRARY_LINKAGE@" STREQUAL "static")
    include(SelectLibraryConfigurations)
    set(z_vcpkg_tiff_link_libraries "")
    set(z_vcpkg_tiff_libraries "")
    if("@webp@")
        find_package(WebP CONFIG ${z_vcpkg_tiff_find_options})
        list(APPEND z_vcpkg_tiff_link_libraries "\$<LINK_ONLY:WebP::webp>")
        list(APPEND z_vcpkg_tiff_libraries ${WebP_LIBRARIES})
    endif()
    if("@lzma@")
        find_package(LibLZMA ${z_vcpkg_tiff_find_options})
        list(APPEND z_vcpkg_tiff_link_libraries "\$<LINK_ONLY:LibLZMA::LibLZMA>")
        list(APPEND z_vcpkg_tiff_libraries ${LIBLZMA_LIBRARIES})
    endif()
    if("@jpeg@")
        find_package(JPEG ${z_vcpkg_tiff_find_options})
        list(APPEND z_vcpkg_tiff_link_libraries "\$<LINK_ONLY:JPEG::JPEG>")
        list(APPEND z_vcpkg_tiff_libraries ${JPEG_LIBRARIES})
    endif()
    if("@zstd@")
        find_package(zstd CONFIG ${z_vcpkg_tiff_find_options})
        set(z_vcpkg_tiff_zstd_target_property "IMPORTED_LOCATION_")
        if(TARGET zstd::libzstd_shared)
            set(z_vcpkg_tiff_zstd "\$<LINK_ONLY:zstd::libzstd_shared>")
            set(z_vcpkg_tiff_zstd_target zstd::libzstd_shared)
            if(WIN32)
                set(z_vcpkg_tiff_zstd_target_property "IMPORTED_IMPLIB_")
            endif()
        else()
            set(z_vcpkg_tiff_zstd "\$<LINK_ONLY:zstd::libzstd_static>")
            set(z_vcpkg_tiff_zstd_target zstd::libzstd_static)
        endif()
        get_target_property(z_vcpkg_tiff_zstd_configs "${z_vcpkg_tiff_zstd_target}" IMPORTED_CONFIGURATIONS)
        foreach(z_vcpkg_config IN LISTS z_vcpkg_tiff_zstd_configs)
            get_target_property(ZSTD_LIBRARY_${z_vcpkg_config} "${z_vcpkg_tiff_zstd_target}" "${z_vcpkg_tiff_zstd_target_property}${z_vcpkg_config}")
        endforeach()
        select_library_configurations(ZSTD)
        if(NOT TARGET ZSTD::ZSTD)
            add_library(ZSTD::ZSTD INTERFACE IMPORTED)
            set_property(TARGET ZSTD::ZSTD APPEND PROPERTY INTERFACE_LINK_LIBRARIES ${z_vcpkg_tiff_zstd})
        endif()
        list(APPEND z_vcpkg_tiff_link_libraries ${z_vcpkg_tiff_zstd})
        list(APPEND z_vcpkg_tiff_libraries ${ZSTD_LIBRARIES})
        unset(z_vcpkg_tiff_zstd)
        unset(z_vcpkg_tiff_zstd_configs)
        unset(z_vcpkg_config)
        unset(z_vcpkg_tiff_zstd_target)
    endif()
    if("@lerc@")
        find_package(unofficial-lerc ${z_vcpkg_tiff_find_options})
        get_target_property(z_vcpkg_lerc_configs unofficial::Lerc::Lerc IMPORTED_CONFIGURATIONS)
        foreach(z_vcpkg_property IN ITEMS IMPORTED_IMPLIB_ IMPORTED_LOCATION_)
            foreach(z_vcpkg_config IN LISTS z_vcpkg_lerc_configs)
                get_target_property(Z_VCPKG_LERC_LIBRARY_${z_vcpkg_config} unofficial::Lerc::Lerc "${z_vcpkg_property}${z_vcpkg_config}")
            endforeach()
            select_library_configurations(Z_VCPKG_LERC)
            if(Z_VCPKG_LERC_LIBRARIES)
                break()
            endif()
        endforeach()
        list(APPEND z_vcpkg_tiff_link_libraries "\$<LINK_ONLY:unofficial::Lerc::Lerc>")
        list(APPEND z_vcpkg_tiff_libraries ${Z_VCPKG_LERC_LIBRARIES})
        unset(z_vcpkg_config)
        unset(z_vcpkg_lerc_configs)
        unset(z_vcpkg_property)
        unset(Z_VCPKG_LERC_FOUND)
    endif()
    if("@libdeflate@")
        find_package(libdeflate ${z_vcpkg_tiff_find_options})
        set(z_vcpkg_property "IMPORTED_LOCATION_")
        if(TARGET libdeflate::libdeflate_shared)
            set(z_vcpkg_libdeflate_target libdeflate::libdeflate_shared)
            if(WIN32)
                set(z_vcpkg_property "IMPORTED_IMPLIB_")
            endif()
        else()
            set(z_vcpkg_libdeflate_target libdeflate::libdeflate_static)
        endif()
        get_target_property(z_vcpkg_libdeflate_configs "${z_vcpkg_libdeflate_target}" IMPORTED_CONFIGURATIONS)
        foreach(z_vcpkg_config IN LISTS z_vcpkg_libdeflate_configs)
            get_target_property(Z_VCPKG_DEFLATE_LIBRARY_${z_vcpkg_config} "${z_vcpkg_libdeflate_target}" "${z_vcpkg_property}${z_vcpkg_config}")
        endforeach()
        select_library_configurations(Z_VCPKG_DEFLATE)
        list(APPEND z_vcpkg_tiff_link_libraries "\$<LINK_ONLY:${z_vcpkg_libdeflate_target}>")
        list(APPEND z_vcpkg_tiff_libraries ${Z_VCPKG_DEFLATE_LIBRARIES})
        unset(z_vcpkg_config)
        unset(z_vcpkg_libdeflate_configs)
        unset(z_vcpkg_libdeflate_target)
        unset(z_vcpkg_property)
        unset(Z_VCPKG_DEFLATE_FOUND)
    endif()
    if("@zlib@")
        find_package(ZLIB ${z_vcpkg_tiff_find_options})
        list(APPEND z_vcpkg_tiff_link_libraries "\$<LINK_ONLY:ZLIB::ZLIB>")
        list(APPEND z_vcpkg_tiff_libraries ${ZLIB_LIBRARIES})
    endif()
    if(UNIX)
        list(APPEND z_vcpkg_tiff_link_libraries m)
        list(APPEND z_vcpkg_tiff_libraries m)
    endif()

    if(TARGET TIFF::TIFF)
        set_property(TARGET TIFF::TIFF APPEND PROPERTY INTERFACE_LINK_LIBRARIES ${z_vcpkg_tiff_link_libraries})
    endif()
    list(APPEND TIFF_LIBRARIES ${z_vcpkg_tiff_libraries})
    unset(z_vcpkg_tiff_link_libraries)
    unset(z_vcpkg_tiff_libraries)
endif()
unset(z_vcpkg_tiff_find_options)
cmake_policy(POP)
