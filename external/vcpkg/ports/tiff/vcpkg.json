{"name": "tiff", "version": "4.7.0", "description": "A library that supports the manipulation of TIFF image files", "homepage": "https://libtiff.gitlab.io/libtiff/", "license": "libtiff", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "default-features": ["jpeg", "lzma", "zip"], "features": {"cxx": {"description": "Build C++ libtiffxx library"}, "jpeg": {"description": "Support JPEG compression in TIFF image files", "dependencies": ["libjpeg-turbo"]}, "lerc": {"description": "Support LERC compression", "dependencies": ["lerc", {"name": "tiff", "default-features": false, "features": ["zip"]}]}, "libdeflate": {"description": "Use libdeflate for faster ZIP support", "dependencies": ["libdeflate", {"name": "tiff", "default-features": false, "features": ["zip"]}]}, "lzma": {"description": "Support LZMA compression in TIFF image files", "dependencies": ["liblzma"]}, "tools": {"description": "Build tools"}, "webp": {"description": "Support WEBP compression in TIFF image files", "dependencies": ["libwebp"]}, "zip": {"description": "Support ZIP/deflate compression in TIFF image files", "dependencies": ["zlib"]}, "zstd": {"description": "Support ZSTD compression in TIFF image files", "dependencies": ["zstd"]}}}