diff --git a/CMakeLists.txt b/CMakeLists.txt
index 8efec25..d2edac2 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -508,7 +508,7 @@ endif ()
 # Create man pages
 #################################################
 
-if (UNIX AND SUPPORT_CONSOLE_APP)
+if (0)
     find_program( XSLTPROC_FOUND xsltproc )
     if (XSLTPROC_FOUND)
         ## NOTE: man name must match exe ie currently `${LIB_NAME}.1` not `tidy.1`
