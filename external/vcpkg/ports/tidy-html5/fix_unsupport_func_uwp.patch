diff --git a/src/sprtf.c b/src/sprtf.c
index 3468ac1..1630405 100644
--- a/src/sprtf.c
+++ b/src/sprtf.c
@@ -387,7 +387,7 @@ static void wprt( PTSTR ps )
 {
    static char _s_woibuf[1024];
    char * cp = _s_woibuf;
-   int len = (int)lstrlen(ps);
+   int len = (int)wcslen(ps);
    if(len) {
       int ret = WideCharToMultiByte( CP_ACP, /* UINT CodePage, // code page */
          0, /* DWORD dwFlags,            // performance and mapping flags */
