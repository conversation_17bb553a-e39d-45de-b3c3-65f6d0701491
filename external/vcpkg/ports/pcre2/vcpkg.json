{"name": "pcre2", "version": "10.45", "description": "Regular Expression pattern matching using the same syntax and semantics as Perl 5.", "homepage": "https://github.com/PCRE2Project/pcre2", "license": "BSD-3-<PERSON><PERSON>", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "default-features": ["platform-default-features"], "features": {"jit": {"description": "Enable support for Just-In-Time compiling regex matchers", "supports": "!emscripten & !ios"}, "platform-default-features": {"description": "Enable default features", "dependencies": [{"name": "pcre2", "features": ["jit"], "platform": "!emscripten & !ios"}]}}}