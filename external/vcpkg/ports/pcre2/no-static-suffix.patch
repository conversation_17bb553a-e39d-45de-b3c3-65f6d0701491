diff --git a/CMakeLists.txt b/CMakeLists.txt
index 52cd05d..1888c14 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -812,8 +812,8 @@ if(PCRE2_BUILD_PCRE2_8)
     set(TARGETS ${TARGETS} pcre2-posix-static)
 
     if(MSVC)
-      set_target_properties(pcre2-8-static PROPERTIES OUTPUT_NAME pcre2-8-static)
-      set_target_properties(pcre2-posix-static PROPERTIES OUTPUT_NAME pcre2-posix-static)
+      set_target_properties(pcre2-8-static PROPERTIES OUTPUT_NAME pcre2-8)
+      set_target_properties(pcre2-posix-static PROPERTIES OUTPUT_NAME pcre2-posix)
     else()
       set_target_properties(pcre2-8-static PROPERTIES OUTPUT_NAME pcre2-8)
       set_target_properties(pcre2-posix-static PROPERTIES OUTPUT_NAME pcre2-posix)
@@ -904,7 +904,7 @@ if(PCRE2_BUILD_PCRE2_16)
     set(TARGETS ${TARGETS} pcre2-16-static)
 
     if(MSVC)
-      set_target_properties(pcre2-16-static PROPERTIES OUTPUT_NAME pcre2-16-static)
+      set_target_properties(pcre2-16-static PROPERTIES OUTPUT_NAME pcre2-16)
     else()
       set_target_properties(pcre2-16-static PROPERTIES OUTPUT_NAME pcre2-16)
     endif()
@@ -974,7 +974,7 @@ if(PCRE2_BUILD_PCRE2_32)
     set(TARGETS ${TARGETS} pcre2-32-static)
 
     if(MSVC)
-      set_target_properties(pcre2-32-static PROPERTIES OUTPUT_NAME pcre2-32-static)
+      set_target_properties(pcre2-32-static PROPERTIES OUTPUT_NAME pcre2-32)
     else()
       set_target_properties(pcre2-32-static PROPERTIES OUTPUT_NAME pcre2-32)
     endif()
