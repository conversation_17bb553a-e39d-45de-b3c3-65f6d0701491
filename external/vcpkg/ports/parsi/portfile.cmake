vcpkg_from_github(
    OUT_SOURCE_PATH SOURCE_PATH
    REPO cthulhu-irl/parsi
    REF "v${VERSION}"
    SHA512 193927b3b2e50d358752c6b58798d4050101d634d5231bf3e5c354edaca846a4e05f8b862c8fc461116f8ddecda0b0ebac7ee936579868a816e6404cedf964ec
    HEAD_REF main
)

vcpkg_cmake_configure(SOURCE_PATH "${SOURCE_PATH}" OPTIONS -DPARSI_MAIN_PROJECT=OFF -DPARSI_INSTALL=ON)
vcpkg_cmake_install()

file(REMOVE_RECURSE
    "${CURRENT_PACKAGES_DIR}/debug"
)

file(INSTALL "${CMAKE_CURRENT_LIST_DIR}/usage" DESTINATION "${CURRENT_PACKAGES_DIR}/share/${PORT}")
vcpkg_install_copyright(FILE_LIST "${SOURCE_PATH}/LICENSE")
