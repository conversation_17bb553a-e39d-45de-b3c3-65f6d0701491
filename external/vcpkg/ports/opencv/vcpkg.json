{"name": "opencv", "version": "4.11.0", "description": "computer vision library", "homepage": "https://github.com/opencv/opencv", "license": "Apache-2.0", "dependencies": [{"name": "opencv4", "default-features": false}], "features": {"ade": {"description": "graph api", "dependencies": [{"name": "opencv4", "default-features": false, "features": ["ade"]}]}, "aravis": {"description": "aravis", "dependencies": [{"name": "opencv4", "default-features": false, "features": ["aravis"]}]}, "calib3d": {"description": "calib3d module", "dependencies": [{"name": "opencv4", "default-features": false, "features": ["calib3d"]}]}, "contrib": {"description": "opencv_contrib module", "dependencies": [{"name": "opencv4", "default-features": false, "features": ["contrib"]}]}, "cuda": {"description": "CUDA support for opencv", "dependencies": [{"name": "opencv4", "default-features": false, "features": ["cuda"]}]}, "cudnn": {"description": "cuDNN support for opencv", "dependencies": [{"name": "opencv4", "default-features": false, "features": ["cudnn"]}]}, "dc1394": {"description": "Dc1394 support for opencv", "dependencies": [{"name": "opencv4", "default-features": false, "features": ["dc1394"]}]}, "directml": {"description": "Build with DirectML support", "dependencies": [{"name": "opencv4", "default-features": false, "features": ["directml"]}]}, "dnn": {"description": "Enable dnn module", "dependencies": [{"name": "opencv4", "default-features": false, "features": ["dnn"]}]}, "dnn-cuda": {"description": "Build dnn module with CUDA support", "dependencies": [{"name": "opencv4", "default-features": false, "features": ["dnn-cuda"]}]}, "dshow": {"description": "enable DirectShow", "dependencies": [{"name": "opencv4", "default-features": false, "features": ["dshow"]}]}, "eigen": {"description": "Eigen support for opencv", "dependencies": [{"name": "opencv4", "default-features": false, "features": ["eigen"]}]}, "ffmpeg": {"description": "ffmpeg support for opencv", "dependencies": [{"name": "opencv4", "default-features": false, "features": ["ffmpeg"]}]}, "freetype": {"description": "Freetype support for opencv", "dependencies": [{"name": "opencv4", "default-features": false, "features": ["freetype"]}]}, "fs": {"description": "Enable filesystem support", "dependencies": [{"name": "opencv4", "default-features": false, "features": ["fs"]}]}, "gapi": {"description": "Enable gapi module", "dependencies": [{"name": "opencv4", "default-features": false, "features": ["<PERSON>i"]}]}, "gdcm": {"description": "GDCM support for opencv", "dependencies": [{"name": "opencv4", "default-features": false, "features": ["gdcm"]}]}, "gstreamer": {"description": "gstreamer support for opencv", "dependencies": [{"name": "opencv4", "default-features": false, "features": ["gstreamer"]}]}, "gtk": {"description": "GTK support for opencv", "dependencies": [{"name": "opencv4", "default-features": false, "features": ["gtk"]}]}, "halide": {"description": "Halide support for opencv", "dependencies": [{"name": "opencv4", "default-features": false, "features": ["halide"]}]}, "highgui": {"description": "highgui module", "dependencies": [{"name": "opencv4", "default-features": false, "features": ["<PERSON><PERSON><PERSON>"]}]}, "intrinsics": {"description": "enable intrinsics", "dependencies": [{"name": "opencv4", "default-features": false, "features": ["intrinsics"]}]}, "ipp": {"description": "Enable Intel Integrated Performance Primitives", "dependencies": [{"name": "opencv4", "default-features": false, "features": ["ipp"]}]}, "jpeg": {"description": "JPEG support for opencv", "dependencies": [{"name": "opencv4", "default-features": false, "features": ["jpeg"]}]}, "jpegxl": {"description": "JPEGXL support for opencv", "dependencies": [{"name": "opencv4", "default-features": false, "features": ["jpegxl"]}]}, "msmf": {"description": "Microsoft Media Foundation support for opencv", "dependencies": [{"name": "opencv4", "default-features": false, "features": ["msmf"]}]}, "nonfree": {"description": "Allow nonfree and unredistributable libraries", "dependencies": [{"name": "opencv4", "default-features": false, "features": ["nonfree"]}]}, "opencl": {"description": "Enable OpenCL support", "dependencies": [{"name": "opencv4", "default-features": false, "features": ["opencl"]}]}, "openexr": {"description": "OpenEXR support for opencv", "dependencies": [{"name": "opencv4", "default-features": false, "features": ["openexr"]}]}, "opengl": {"description": "opengl support for opencv", "dependencies": [{"name": "opencv4", "default-features": false, "features": ["opengl"]}]}, "openjpeg": {"description": "JPEG 2000 support for opencv", "dependencies": [{"name": "opencv4", "default-features": false, "features": ["openjpeg"]}]}, "openmp": {"description": "Enable openmp support for opencv", "dependencies": [{"name": "opencv4", "default-features": false, "features": ["openmp"]}]}, "openvino": {"description": "OpenVINO support for OpenCV DNN", "dependencies": [{"name": "opencv4", "default-features": false, "features": ["<PERSON>vin<PERSON>"]}]}, "ovis": {"description": "opencv_ovis module", "dependencies": [{"name": "opencv4", "default-features": false, "features": ["ovis"]}]}, "png": {"description": "PNG support for opencv", "dependencies": [{"name": "opencv4", "default-features": false, "features": ["png"]}]}, "python": {"description": "Python wrapper support for opencv", "dependencies": [{"name": "opencv4", "default-features": false, "features": ["python"]}]}, "qt": {"description": "Qt GUI support for opencv", "dependencies": [{"name": "opencv4", "default-features": false, "features": ["qt"]}]}, "quality": {"description": "Build opencv_quality module", "dependencies": [{"name": "opencv4", "default-features": false, "features": ["quality"]}]}, "quirc": {"description": "Enable QR code module", "dependencies": [{"name": "opencv4", "default-features": false, "features": ["quirc"]}]}, "rgbd": {"description": "Build opencv_rgbd module", "dependencies": [{"name": "opencv4", "default-features": false, "features": ["rgbd"]}]}, "sfm": {"description": "opencv_sfm module", "dependencies": [{"name": "opencv4", "default-features": false, "features": ["sfm"]}]}, "tbb": {"description": "Enable Intel Threading Building Blocks", "dependencies": [{"name": "opencv4", "default-features": false, "features": ["tbb"]}]}, "thread": {"description": "Enable thread support", "dependencies": [{"name": "opencv4", "default-features": false, "features": ["thread"]}]}, "tiff": {"description": "TIFF support for opencv", "dependencies": [{"name": "opencv4", "default-features": false, "features": ["tiff"]}]}, "vtk": {"description": "vtk support for opencv", "dependencies": [{"name": "opencv4", "default-features": false, "features": ["vtk"]}]}, "vulkan": {"description": "Vulkan support for opencv dnn", "dependencies": [{"name": "opencv4", "default-features": false, "features": ["vulkan"]}]}, "webp": {"description": "WebP support for opencv", "dependencies": [{"name": "opencv4", "default-features": false, "features": ["webp"]}]}, "win32ui": {"description": "Enable win32ui", "dependencies": [{"name": "opencv4", "default-features": false, "features": ["win32ui"]}]}, "world": {"description": "Compile to a single package support for opencv", "dependencies": [{"name": "opencv4", "default-features": false, "features": ["world"]}]}}}