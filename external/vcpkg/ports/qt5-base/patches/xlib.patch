diff --git a/src/gui/configure.json b/src/gui/configure.json
index 9a749516b..242f47504 100644
--- a/src/gui/configure.json
+++ b/src/gui/configure.json
@@ -568,7 +568,9 @@
             },
             "headers": "X11/Xlib.h",
             "sources": [
-                { "type": "makeSpec", "spec": "X11" }
+                { "type": "makeSpec", "spec": "X11" },
+                { "type": "pkgConfig", "args": "x11" },
+                { "type": "pkgConfig", "args": "x11 --static" }
             ]
         },
         "x11sm": {
@@ -590,6 +592,7 @@
             "headers": "xcb/xcb.h",
             "sources": [
                 { "type": "pkgConfig", "args": "xcb >= 1.11" },
+                { "type": "pkgConfig", "args": "xcb >= 1.11 --static" },
                 "-lxcb"
             ]
         },
@@ -691,6 +694,7 @@
             "headers": "X11/Xlib-xcb.h",
             "sources": [
                 { "type": "pkgConfig", "args": "x11-xcb" },
+                { "type": "pkgConfig", "args": "x11-xcb --static" },
                 "-lX11-xcb"
             ],
             "use": "xcb xlib"
@@ -711,6 +715,7 @@
             "headers": "xcb/xkb.h",
             "sources": [
                 { "type": "pkgConfig", "args": "xcb-xkb" },
+                { "type": "pkgConfig", "args": "xcb-xkb --static" },
                 "-lxcb-xkb"
             ],
             "use": "xcb"
