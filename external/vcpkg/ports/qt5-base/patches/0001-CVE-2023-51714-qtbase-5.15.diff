From ea63c28efc1d2ecb467b83a34923d12462efa96f Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Tue, 12 Dec 2023 20:51:56 +0100
Subject: [PATCH] HPack: fix a Yoda Condition

Putting the variable on the LHS of a relational operation makes the
expression easier to read. In this case, we find that the whole
expression is nonsensical as an overflow protection, because if
name.size() + value.size() overflows, the result will exactly _not_
be > max() - 32, because UB will have happened.

To be fixed in a follow-up commit.

As a drive-by, add parentheses around the RHS.

Change-Id: I35ce598884c37c51b74756b3bd2734b9aad63c09
Reviewed-by: <PERSON> <<EMAIL>>
(cherry picked from commit 658607a34ead214fbacbc2cca44915655c318ea9)
Reviewed-by: Qt Cherry-pick Bo<PERSON> <<EMAIL>>
(cherry picked from commit 4f7efd41740107f90960116700e3134f5e433867)
(cherry picked from commit 13c16b756900fe524f6d9534e8a07aa003c05e0c)
(cherry picked from commit 1d4788a39668fb2dc5912a8d9c4272dc40e99f92)
(cherry picked from commit 87de75b5cc946d196decaa6aef4792a6cac0b6db)
---

diff --git a/src/network/access/http2/hpacktable.cpp b/src/network/access/http2/hpacktable.cpp
index 834214f..ab166a6 100644
--- a/src/network/access/http2/hpacktable.cpp
+++ b/src/network/access/http2/hpacktable.cpp
@@ -63,7 +63,7 @@
     // 32 octets of overhead."
 
     const unsigned sum = unsigned(name.size() + value.size());
-    if (std::numeric_limits<unsigned>::max() - 32 < sum)
+    if (sum > (std::numeric_limits<unsigned>::max() - 32))
         return HeaderSize();
     return HeaderSize(true, quint32(sum + 32));
 }
