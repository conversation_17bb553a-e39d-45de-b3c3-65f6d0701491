diff --git a/qmake/library/qmakeevaluator.cpp b/qmake/library/qmakeevaluator.cpp
index df3f92d7d5df..0ee8cdbf11e1 100644
--- a/qmake/library/qmakeevaluator.cpp
+++ b/qmake/library/qmakeevaluator.cpp
@@ -1046,6 +1046,11 @@ void QMakeEvaluator::loadDefaults()
     case PROCESSOR_ARCHITECTURE_AMD64:
         archStr = ProString("x86_64");
         break;
+# endif
+# ifdef PROCESSOR_ARCHITECTURE_ARM64
+    case PROCESSOR_ARCHITECTURE_ARM64:
+        archStr = ProString("arm64");
+        break;
 # endif
     case PROCESSOR_ARCHITECTURE_INTEL:
         archStr = ProString("x86");
-- 
2.16.3
