From 23c3fc483e8b6e21012a61f0bea884446f727776 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Tue, 12 Dec 2023 22:08:07 +0100
Subject: [PATCH] HPack: fix incorrect integer overflow check

This code never worked:

For the comparison with max() - 32 to trigger, on 32-bit platforms (or
Qt 5) signed interger overflow would have had to happen in the
addition of the two sizes. The compiler can therefore remove the
overflow check as dead code.

On Qt 6 and 64-bit platforms, the signed integer addition would be
very unlikely to overflow, but the following truncation to uint32
would yield the correct result only in a narrow 32-value window just
below UINT_MAX, if even that.

Fix by using the proper tool, qAddOverflow.

Manual conflict resolutions:
 - qAddOverflow doesn't exist in Qt 5, use private add_overflow
   predecessor API instead

Change-Id: I7599f2e75ff7f488077b0c60b81022591005661c
Reviewed-by: <PERSON> <<EMAIL>>
(cherry picked from commit ee5da1f2eaf8932aeca02ffea6e4c618585e29e3)
Reviewed-by: Qt Cherry-pick Bo<PERSON> <<EMAIL>>
(cherry picked from commit debeb8878da2dc706ead04b6072ecbe7e5313860)
Reviewed-by: Thiago Macieira <<EMAIL>>
Reviewed-by: Marc Mutz <<EMAIL>>
(cherry picked from commit 811b9eef6d08d929af8708adbf2a5effb0eb62d7)
(cherry picked from commit f931facd077ce945f1e42eaa3bead208822d3e00)
(cherry picked from commit 9ef4ca5ecfed771dab890856130e93ef5ceabef5)
Reviewed-by: Mårten Nordheim <<EMAIL>>
---

diff --git a/src/network/access/http2/hpacktable.cpp b/src/network/access/http2/hpacktable.cpp
index ab166a6..de91fc0 100644
--- a/src/network/access/http2/hpacktable.cpp
+++ b/src/network/access/http2/hpacktable.cpp
@@ -40,6 +40,7 @@
 #include "hpacktable_p.h"
 
 #include <QtCore/qdebug.h>
+#include <QtCore/private/qnumeric_p.h>
 
 #include <algorithm>
 #include <cstddef>
@@ -62,7 +63,9 @@
     // for counting the number of references to the name and value would have
     // 32 octets of overhead."
 
-    const unsigned sum = unsigned(name.size() + value.size());
+    size_t sum;
+    if (add_overflow(size_t(name.size()), size_t(value.size()), &sum))
+        return HeaderSize();
     if (sum > (std::numeric_limits<unsigned>::max() - 32))
         return HeaderSize();
     return HeaderSize(true, quint32(sum + 32));
