diff --git a/src/gui/configure.json b/src/gui/configure.json
index 5fceb1150..7702cb261 100644
--- a/src/gui/configure.json	
+++ b/src/gui/configure.json
@@ -220,7 +220,10 @@
             "headers": "EGL/egl.h",
             "sources": [
                 { "type": "pkgConfig", "args": "egl" },
-                { "type": "makeSpec", "spec": "EGL" }
+                { "type": "makeSpec", "spec": "EGL" },
+                { "libs": "-lEGL -lGLESv2 -lGL -lANGLE -lGL -lX11 -ldl -lm -lpthread" },
+                { "libs": "-DGL_GLES_PROTOTYPES=1 -DGL_GLEXT_PROTOTYPES -DEGL_EGL_PROTOTYPES=1 -DEGL_EGLEXT_PROTOTYPES -lEGL -lGLESv2 -lGL -lANGLE -lGL -lX11 -ldl -lm -lpthread" },
+                { "libs": "-DANGLE_EXPORT -DANGLE_UTIL_EXPORT -DGL_API -DGL_APICALL -DEGLAPI -DGL_GLES_PROTOTYPES=1 -DGL_GLEXT_PROTOTYPES -DEGL_EGL_PROTOTYPES=1 -DEGL_EGLEXT_PROTOTYPES -lEGL -lGLESv2 -lGL -lANGLE -lGL -lX11 -ldl -lm -lpthread" }
             ]
         },
         "freetype": {
