--- a/src/gui/configure.json
+++ b/src/gui/configure.json
@@ -842,7 +842,8 @@                     "// Check if EGL is compatible with X. Some EGL implementations, typically on",
                     "// embedded devices, are not intended to be used together with X. EGL support",
                     "// has to be disabled in plugins like xcb in this case since the native display,",
                     "// window and pixmap types will be different than what an X-based platform",
-                    "// plugin would expect."
+                    "// plugin would expect.",
+                    "#define USE_X11"
                 ],
                 "include": [ "EGL/egl.h", "X11/Xlib.h" ],
                 "main": [
--- a/src/platformsupport/eglconvenience/qt_egl_p.h
+++ b/src/platformsupport/eglconvenience/qt_egl_p.h
@@ -61,7 +61,11 @@ # endif
 # if !defined(Q_OS_INTEGRITY)
 #  define WIN_INTERFACE_CUSTOM   // NV
 # endif // Q_OS_INTEGRITY
-#endif  // QT_EGL_NO_X11
+#else // QT_EGL_NO_X11
+// If one has an eglplatform.h with https://github.com/KhronosGroup/EGL-Registry/pull/130
+// that needs USE_X11 to be defined.
+# define USE_X11
+#endif
 
 #ifdef QT_EGL_WAYLAND
 # define WAYLAND // NV
