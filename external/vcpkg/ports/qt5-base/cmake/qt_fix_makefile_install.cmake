#Could probably be a vcpkg_fix_makefile_install for other ports?
function(qt_fix_makefile_install BUILD_DIR)
    #Fix the installation location
    set(MSYS_HACK "")
    if(VCPKG_HOST_IS_WINDOWS)
        file(TO_NATIVE_PATH "${CURRENT_INSTALLED_DIR}" NATIVE_INSTALLED_DIR)
        file(TO_NATIVE_PATH "${CURRENT_PACKAGES_DIR}" NATIVE_PACKAGES_DIR)
        if(VCPKG_TARGET_IS_MINGW)
            set(MSYS_HACK ":@msyshack@%=%")
            file(STRINGS "${BUILD_DIR}/Makefile" using_sh REGEX "= rm -f")
            if(using_sh)
                set(NATIVE_INSTALLED_DIR "${CURRENT_INSTALLED_DIR}")
                set(NATIVE_PACKAGES_DIR "${CURRENT_PACKAGES_DIR}")
            endif()
        endif()
        string(SUBSTRING "${NATIVE_INSTALLED_DIR}" 0 2 INSTALLED_DRIVE)
        string(SUBSTRING "${NATIVE_PACKAGES_DIR}" 0 2 PACKAGES_DRIVE)
        string(SUBSTRING "${NATIVE_INSTALLED_DIR}" 2 -1 INSTALLED_DIR_WITHOUT_DRIVE)
        string(SUBSTRING "${NATIVE_PACKAGES_DIR}" 2 -1 PACKAGES_DIR_WITHOUT_DRIVE)
    else()
        set(INSTALLED_DRIVE "") 
        set(PACKAGES_DRIVE "")
        set(INSTALLED_DIR_WITHOUT_DRIVE "${CURRENT_INSTALLED_DIR}")
        set(PACKAGES_DIR_WITHOUT_DRIVE "${CURRENT_PACKAGES_DIR}")
    endif()
    set(installed "${INSTALLED_DRIVE}$(INSTALL_ROOT${MSYS_HACK})${INSTALLED_DIR_WITHOUT_DRIVE}")
    set(packages  "${PACKAGES_DRIVE}$(INSTALL_ROOT${MSYS_HACK})${PACKAGES_DIR_WITHOUT_DRIVE}")

    file(GLOB_RECURSE MAKEFILES "${BUILD_DIR}/*Makefile*")
    foreach(MAKEFILE ${MAKEFILES})
        vcpkg_replace_string("${MAKEFILE}" "${installed}" "${packages}" IGNORE_UNCHANGED)
    endforeach()
endfunction()
