# Every update requires an update of these hashes and the version within the control file of each of the 32 ports.
# So it is probably better to have a central location for these hashes and let the ports update via a script
set(QT_MAJOR_MINOR_VER 5.15)
set(QT_PATCH_VER 16)
set(QT_UPDATE_VERSION OFF) # Switch to update qt and not build qt. Creates a file cmake/qt_new_hashes.cmake in qt5-base with the new hashes.

set(QT_PORT_LIST base 3d activeqt charts connectivity datavis3d declarative gamepad graphicaleffects imageformats location macextras mqtt multimedia networkauth
                 purchasing quickcontrols quickcontrols2 remoteobjects script scxml sensors serialport speech svg tools virtualkeyboard webchannel websockets
                 webview winextras xmlpatterns doc x11extras androidextras translations serialbus webengine webglplugin wayland)

set(QT_HASH_qt5-3d bdb13bd73a8b706eae8ad07d506ca21469a138dadb47b604a23ebe4640b593cba1353efa081a51a6d869a3d75e1b2b25c432bc7bc4621991f2fd2f5d18914fc6)
set(QT_HASH_qt5-activeqt 682044800cf359ac24abfde94e45db7ca31c3c0162ca14d952990377e03dcaba490f02ec59b015fc77e6c2bce19bd65ff9fd4a19d960d549dcc4574ccaba2561)
set(QT_HASH_qt5-androidextras 257e7eb264df7cff6076a1e57bcf0ed23291cbd00ff1da8acee2993088b336ca5cba28c60691ece1c9a1afa869421388681cf0e9606f54ff38211991c6deaa36)
set(QT_HASH_qt5-base 190581c7df9763c2550466f884784d6f38817a087ab44fe57b99c1d1ac2ea3bae94ced8a6d0873a7999e523d63831d135cd7407812c8814ac4ef957840b7058e)
set(QT_HASH_qt5-charts f57df425e767ad5e18d5bf2439c137c0b656acf4ae998d42a41e59acf8a7b3d6733ef1c4ee4b11e1d54801df1550af5ee53cb190f05db223568b31fb4fcaa96d)
set(QT_HASH_qt5-connectivity a927d74f37bfec9f55a13077786cbec56f34145fb744bb2e0f9096acdb1b4c57e24b6bc60cfb775e6c18e352e6ddd9d085268ce2e810fabe8bca54cc3edf5282)
set(QT_HASH_qt5-datavis3d ec2425d4f5d3c8b26f1682f8d3e236ad4885194946e3720c54e5afdc3c8d8cb4322cd83aa381b7c1802f6348d1cdbfc4abf7c928686ef3e4c52d223fa2a314f5)
set(QT_HASH_qt5-declarative be492d95d11ab13e13d27ca2024b5b5860d515d0b66c6d1c201bdba155841996bfdb3b813313dc75578228b6d3e661220dcc5db037624fe73d6e5e1c3ec84aa7)
set(QT_HASH_qt5-doc c9988a6c82d5fd3f61c0df1a8b16db4f087fe7f75d968aaa472f08e8a481e6ec69b56c13776000248ceab386004ba2441282db92be2be691da965d50f9a176f7)
set(QT_HASH_qt5-gamepad 4651355c59d1977c7895d4bb751a9aa66088fb6173713f90e7a188ef4e314e00971ec285f5a6779cb2b23bcb61a9555c9f44892f44c44429fc4e54c9f7ff517a)
set(QT_HASH_qt5-graphicaleffects 25f2940428bed7b85f5ae16df286f8effc30ef9f68f6db5c559b18d247e8f391ba8c7e00a927024b0c84593648391fa63d16a23f1e8ae237b198fd3a1e7aa818)
set(QT_HASH_qt5-imageformats 922c513f1d3e46b37cd87aadd06c993128c428773aad12e8fd252258840b969996e911248530b135400e43538783d8fc1c69b4fab53ab526fc5d38478d11e6a9)
set(QT_HASH_qt5-location 321a8e68f731a97c7ef0209d6db0ff4891dd14dc43e14f5c4c5ac763069c7f17298fbc6410326df9265ccd631372cdba662fc82e26a324936d371c8572e19a48)
set(QT_HASH_qt5-macextras c04cb4fc8f6f68d360bba50e2c1b061e6cc164f958725fa53951bcf54ea366128a5aa195d705510e743f890e6321e05f4ad2a4afa24194cf0d4c14ae7eb2b684)
set(QT_HASH_qt5-mqtt 1a737a5e2bb4a2ae32652ca2ccfb09b40a62a1e92f683a86ee456eefb670c06f722dd324346606da32c5839be334fcc9143aa4a279ad5631ee2e61307e71d15f)
set(QT_HASH_qt5-multimedia a848affbc38a532455a34bdf887948210ff9794dae312115be0622246993324902b81209c2cac89ca5db63e6fcc29690f47c1255b1b7c4de68bf6ad5a6ecc5e7)
set(QT_HASH_qt5-networkauth cbf112c1f9c03f6cf78de148da44639393a3a2df56116e85d51d674cb31c37ea45823c44b9839af021c1c7e7733eefd646fbaae52459088b03e3b00fd30b1d49)
set(QT_HASH_qt5-purchasing b8dd83106ded528b7fd60bd29e0d68147de782e0303affede7127ef6d48b80c0f1645dc926d6d8b45d6fdde3a519bd604850766e66d79d7fba8e4807b87578af)
set(QT_HASH_qt5-quickcontrols 530cdddab279497c9edfcecbd97f1db35054bf9fd3321a5306808447f2616062076ee98f05164f7b8a7cc3cb03fd9eec4f7fba050344db00edaeb5925677cc84)
set(QT_HASH_qt5-quickcontrols2 0b98b92eaf250a5f1d2a742c431ca95888a5985908f4fc97495ac18927338b67b23b88a3f8f87aba79dd9e96b5381313b303e2c3827939caa401671c2169eddf)
set(QT_HASH_qt5-remoteobjects 7e08a482f91e4fa6ada03ba8c24697151527df74ba7110bdff1bddc6a6a25828cfcace5fc025db60c8e61e3b1a05668871e4b5a9674c1bd090c7c6bd3a096fd6)
set(QT_HASH_qt5-script 6f209db647823084c3a9894e25e65b3161d3e14f2dc06f79c2503a319bb7449259450e8845fb76461a75be7faa1ad18fec8c4724da2754e626d7797717915e0c)
set(QT_HASH_qt5-scxml fbfb054e07767975fc44a41cc015fd12aa15cec5abda1fd3f01425eaa1fd191c529f49dd5a97b6bdf9b83e841d069e1b3190f35d396c0ea5e44d5df66337102d)
set(QT_HASH_qt5-sensors 2bd63e07a996f5377bda885e7218fc218fc981bf2a637b3d939ae5913bbffe2a797716b0aaa63e61c7e1384f07712de8683787590649a8f01a424b7f4526502b)
set(QT_HASH_qt5-serialbus 7153d0d14545394c4217d0fff62b508358335f87cfa873955f7a6618773fb796231636fd73d681f6105e3d7f6ed1b18ffd59b831c6053afc91ea625b584cec7a)
set(QT_HASH_qt5-serialport d386d528aacce7d4e015110d814e852511db83a3648bc68116e4d300168ac826e83a145d9dd38e4a40ec2d4fd48ba89ac1eb9558afb895f7b39271ad760f2b17)
set(QT_HASH_qt5-speech d0ff73b35e6d94751a31b77bcdc084623d947ace092bdddc98f29d79bf932425fd5adbef74edceb4e8dc9065bfda49efa651cef63c72fcb42171ff083b29b335)
set(QT_HASH_qt5-svg 227b6b4f0d6ad7d9405c0bceabadfab28e591b4b02d7575de0ee7679280fc7115ec04751a8e839af5e9958e1800e6d4529bf8bae2251b579abbc688fdb99b9e8)
set(QT_HASH_qt5-tools 5e81de37a5d429dd14d254a0257e6d1ca37c04a92b506d13d918fd3eb799c026978d3d673f6db0f393b598f520cf3a5d647c66de76308f9de9ac5c6951ed9f6b)
set(QT_HASH_qt5-translations 9702390b89696211b1d85a11143e3432231085c2d96b298ea584f43e8db215bf5cdb5fb5355c8c4d900d1f761dfd3dc0d5c16df2ed0cca483557f8b867e3b2ac)
set(QT_HASH_qt5-virtualkeyboard af7ca963784773b94bfed000cabf5dbfb6363211d6b2601ccb6aed26eccd2eaa1e34dc8e7a6f1bbc678432f4086284df82c66e8da1c7fc7c8f5ec37983f687b9)
set(QT_HASH_qt5-wayland 9ce2bca54aa0e17be17383fccd6caca721db5b54f8905ec86cf871ed0e2414e6bc86878b9cc5167c322153467076b2afdcd6163a8fb2feb6b19cef11c3a29247)
set(QT_HASH_qt5-webchannel 106c72c0f49e79d92b4aa2aed235f57aef358aecdc0f30eb9b5ab2c28a56ab44df709143d2755ea5f35026e5aa3994d187da14838a2a542e878d6f5b70a7fe1b)
set(QT_HASH_qt5-webengine 6bfa7aba34cd5537e6a9e69f03504da1414625315565f053c1ce1fa2875f9b327b5a73e7e9bc8f69c706732f8917ecb6c88bc958ddd4e5848c225b4129cda404)
set(QT_HASH_qt5-webglplugin caf3cc278fa0486339f8a4f75ba9c0183452b55030d4c7cac8214bc38a1e9f9318a8c832935a593b53eea9d342eeae015763a7dd47a92e91f62afed1f540147d)
set(QT_HASH_qt5-websockets 9bb15c9277f96acee0d5a49e961483706c0d6c709c587400c402b7864fe2635cf688222448bdabd5ef53568e6d8c1c32e54a9410301eede0507b975989f30b2b)
set(QT_HASH_qt5-webview 2d6761a6b01a8791dae785f829f429f336b8ba42f0675226ab09a05a7f4e170a3bc569e92e845b9416f4a0eef93f87d2e34c0a18eed025847445bb794e457982)
set(QT_HASH_qt5-winextras 9af7c7eb9f2e37052c9a866d2902ba931be16eeaa43f7430326a5e7c1c9b65fc8c11dc2ba31379d1893939f800954e0079027d08f1e79a19acaf384cd8a9c4b4)
set(QT_HASH_qt5-x11extras edfb4e43214be861c5df71675ac9209b75ba3164c0caf5ec975528d60358a49707567ba4d1862ecb91e1c37d8169ef311426977962f02f2477c5d5ad7d5f4184)
set(QT_HASH_qt5-xmlpatterns ae603468dcb399f99b3843afcbd13ca05baed2ee42e12154be5c2c64ab18c1daddc003af01cf948e57c8654b65e8890101e45b412431ad930f7d697e4b806564)

if(QT_UPDATE_VERSION)
    message(STATUS "Running Qt in automatic version port update mode!")
    set(_VCPKG_INTERNAL_NO_HASH_CHECK 1)
    if("${PORT}" MATCHES "qt5-base")
        function(update_qt_version_in_manifest _port_name)
            set(_current_control "${VCPKG_ROOT_DIR}/ports/${_port_name}/vcpkg.json")
            file(READ ${_current_control} _control_contents)
            #message(STATUS "Before: \n${_control_contents}")
            string(REGEX REPLACE "\"version.*\": \"[0-9]+\.[0-9]+\.[0-9]+\",\n" "\"version\": \"${QT_MAJOR_MINOR_VER}.${QT_PATCH_VER}\",\n" _control_contents "${_control_contents}")
            string(REGEX REPLACE "\n  \"port-version\": [0-9]+," "" _control_contents "${_control_contents}")
            #message(STATUS "After: \n${_control_contents}")
            file(WRITE ${_current_control} "${_control_contents}")
            configure_file("${_current_control}" "${_current_control}" @ONLY NEWLINE_STYLE LF)
        endfunction()

        update_qt_version_in_manifest("qt5")
        foreach(_current_qt_port_basename ${QT_PORT_LIST})
            update_qt_version_in_manifest("qt5-${_current_qt_port_basename}")
        endforeach()
    endif()
endif()
