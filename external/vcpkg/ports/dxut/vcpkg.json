{"name": "dxut", "version": "11.32", "description": "A \"GLUT\"-like framework for Direct3D 11.x Win32 desktop applications", "homepage": "https://github.com/Microsoft/DXUT", "documentation": "https://github.com/microsoft/DXUT/wiki", "license": "MIT", "supports": "windows & !uwp & !xbox", "dependencies": ["directxmath", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"dxtk": {"description": "Support integration with DirectX Tool Kit for DX11", "dependencies": ["directxtk"]}, "spectre": {"description": "Build Spectre-mitigated library"}}}