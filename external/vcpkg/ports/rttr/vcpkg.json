{"name": "rttr", "version": "0.9.6+20210811", "port-version": 1, "description": "an easy and intuitive way to use reflection in C++", "homepage": "https://github.com/rttrorg/rttr", "license": "MIT", "supports": "(static & staticcrt) | (!static & staticcrt) | (!static & !staticcrt)", "dependencies": ["<PERSON><PERSON><PERSON>", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}]}