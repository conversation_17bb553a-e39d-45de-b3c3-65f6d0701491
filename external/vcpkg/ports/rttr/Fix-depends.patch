diff --git a/CMake/3rd_party_libs.cmake b/CMake/3rd_party_libs.cmake
index dca5071..4dd4471 100644
--- a/CMake/3rd_party_libs.cmake
+++ b/CMake/3rd_party_libs.cmake
@@ -51,7 +51,8 @@ if (BUILD_BENCHMARKS)
     find_package(Threads REQUIRED)
 endif()
 
-set(RAPID_JSON_DIR ${RTTR_3RD_PARTY_DIR}/rapidjson-1.1.0)
+find_package(RapidJSON CONFIG REQUIRED)
+set(RAPID_JSON_DIR ${RAPIDJSON_INCLUDE_DIRS})
 set(NONIUS_DIR ${RTTR_3RD_PARTY_DIR}/nonius-1.1.2)
 
 # Prepare "Catch" library for other executables
