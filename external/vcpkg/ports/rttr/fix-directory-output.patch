diff --git a/CMake/config.cmake b/CMake/config.cmake
index a718199..eab64c2 100644
--- a/CMake/config.cmake
+++ b/CMake/config.cmake
@@ -47,7 +47,7 @@ set(CMAKE_EXECUTABLE_OUTPUT_DIRECTORY "${PROJECT_BINARY_DIR}/bin")
 
 # here we specify the installation directory
 if(CMAKE_INSTALL_PREFIX_INITIALIZED_TO_DEFAULT)
-  set(CMAKE_INSTALL_PREFIX "${PROJECT_BINARY_DIR}/install" CACHE PATH  "RTTR install prefix" FORCE)
+  #set(CMAKE_INSTALL_PREFIX "${PROJECT_BINARY_DIR}/install" CACHE PATH  "RTTR install prefix")
 endif()
 
 # in order to group in visual studio the targets into solution filters
