diff --git a/CMakeLists.txt b/CMakeLists.txt
index 90f64d6..17f2929 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -222,9 +222,7 @@ add_test(NAME test1 COMMAND src/test1)
 ###############################################################################
 # Installation
 if(MSVC)
-    install (FILES "${CMAKE_CURRENT_BINARY_DIR}/${PROJECT_BINARY_DIR}/kea-config.bat" DESTINATION bin PERMISSIONS OWNER_READ OWNER_WRITE OWNER_EXECUTE GROUP_READ GROUP_EXECUTE WORLD_READ WORLD_EXECUTE)
 else()
-    install (FILES "${CMAKE_CURRENT_BINARY_DIR}/${PROJECT_BINARY_DIR}/kea-config" DESTINATION bin PERMISSIONS OWNER_READ OWNER_WRITE OWNER_EXECUTE GROUP_READ GROUP_EXECUTE WORLD_READ WORLD_EXECUTE)
 endif(MSVC)    
 install (FILES "${CMAKE_CURRENT_BINARY_DIR}/${PROJECT_HEADER_DIR}/libkea/kea-config.h" DESTINATION include/libkea)
 ###############################################################################
