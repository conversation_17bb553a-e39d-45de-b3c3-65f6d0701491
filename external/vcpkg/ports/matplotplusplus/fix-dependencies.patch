diff --git a/Matplot++Config.cmake.in b/Matplot++Config.cmake.in
index 5c402ad..1601015 100644
--- a/Matplot++Config.cmake.in
+++ b/Matplot++Config.cmake.in
@@ -1,4 +1,5 @@
 @PACKAGE_INIT@
+include(CMakeFindDependencyMacro)
 
 # How this Matplot++ installation was built
 set(MATPLOT_BUILT_SHARED "@MATPLOTPP_BUILD_SHARED_LIBS@")
@@ -10,16 +11,44 @@ if (NOT CMAKE_CXX_COMPILER_ID STREQUAL MATPLOT_BUILT_CXX_COMPILER_ID)
     message(WARNING "This installation of Matplot++ was built with ${MATPLOT_BUILT_CXX_COMPILER_ID}.")
 endif()
 
+if ("@MATPLOTPP_BUILD_EXPERIMENTAL_OPENGL_BACKEND@")
+  find_dependency(glad CONFIG)
+  find_dependency(glfw3 CONFIG)
+endif()
+find_dependency(nodesoup CONFIG )
+if ("@WITH_JPEG@")
+find_dependency(JPEG)
+endif()
+
+if ("@WITH_TIFF@")
+find_dependency(TIFF)
+endif()
+
+if ("@WITH_ZLIB@")
+find_dependency(ZLIB)
+find_dependency(libpng CONFIG)
+endif()
+
+if ("@WITH_LAPACK@")
+find_dependency(LAPACK)
+endif()
+
+if ("@WITH_BLAS@")
+find_dependency(BLAS)
+endif()
+
+if ("@WITH_FFTW3@")
+find_dependency(FFTW3 CONFIG)
+endif()
+
+if ("@WITH_OPENCV@")
+find_dependency(OpenCV CONFIG)
+endif()
+
 # Find dependencies
 if(NOT ${MATPLOT_BUILT_SHARED})
-    include(CMakeFindDependencyMacro)
     list(APPEND CMAKE_MODULE_PATH ${MATPLOT_CONFIG_INSTALL_DIR})
     list(APPEND CMAKE_MODULE_PATH "${CMAKE_CURRENT_LIST_DIR}")
-    # OpenGL backend
-    if (@MATPLOTPP_BUILD_EXPERIMENTAL_OPENGL_BACKEND@)
-        find_dependency(glad)
-        find_dependency(glfw3)
-    endif()
     list(POP_BACK CMAKE_MODULE_PATH)
 endif()
 
diff --git a/source/3rd_party/CMakeLists.txt b/source/3rd_party/CMakeLists.txt
index b5656e1..7c2d1f3 100644
--- a/source/3rd_party/CMakeLists.txt
+++ b/source/3rd_party/CMakeLists.txt
@@ -1,6 +1,7 @@
 #######################################################
 ### NodeSoup                                        ###
 #######################################################
+if(0)
 if(WITH_SYSTEM_NODESOUP)
   find_path(NODESOUP_INCLUDE_DIR nodesoup.hpp REQUIRED)
   find_library(NODESOUP_LIB nodesoup REQUIRED)
@@ -46,7 +47,7 @@ if(MASTER_PROJECT AND NOT BUILD_SHARED_LIBS)
       EXPORT Matplot++Targets
       ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR}/Matplot++)
 endif()
-
+endif()
 
 #######################################################
 ### CImg                                            ###
@@ -69,57 +70,52 @@ find_package(PkgConfig)
 #            Lots of optional packages are not a good idea in general.
 #            It makes the library much less "packagable" (https://youtu.be/sBP17HQAQjk)
 #            and much more difficult to make sure it works on multiple OSs
-find_package(JPEG)
-if(JPEG_FOUND)
+if(WITH_JPEG)
+  find_package(JPEG REQUIRED)
   target_compile_definitions(cimg INTERFACE cimg_use_jpeg)
   target_link_libraries(cimg INTERFACE ${JPEG_LIBRARIES})
   target_include_directories(cimg INTERFACE ${JPEG_INCLUDE_DIRS})
 endif()
 
-find_package(TIFF)
-if(TIFF_FOUND)
+if(WITH_TIFF)
+  find_package(TIFF REQUIRED)
   target_compile_definitions(cimg INTERFACE cimg_use_tiff)
   target_link_libraries(cimg INTERFACE ${TIFF_LIBRARIES})
   target_include_directories(cimg INTERFACE ${TIFF_INCLUDE_DIRS})
 endif()
 
-find_package(ZLIB)
-if(ZLIB_FOUND)
-  find_package(PNG)
-  if (PNG_FOUND)
-    target_compile_definitions(cimg INTERFACE cimg_use_zlib cimg_use_png)
-    target_include_directories(cimg INTERFACE ${ZLIB_INCLUDE_DIRS} ${PNG_INCLUDE_DIRS})
-    target_link_libraries(cimg INTERFACE ${ZLIB_LIBRARIES} ${PNG_LIBRARIES})
-  endif ()
+if(WITH_ZLIB)
+  find_package(ZLIB REQUIRED)
+  find_package(libpng CONFIG REQUIRED)
+  target_compile_definitions(cimg INTERFACE cimg_use_zlib cimg_use_png)
+  target_link_libraries(cimg INTERFACE ZLIB::ZLIB png)
 endif()
 
-find_package(LAPACK)
-if(LAPACK_FOUND)
+if(WITH_LAPACK)
+  find_package(LAPACK REQUIRED)
   target_compile_definitions(cimg INTERFACE cimg_use_lapack)
   target_link_libraries(cimg INTERFACE ${LAPACK_LIBRARIES})
   target_include_directories(cimg INTERFACE ${LAPACK_INCLUDE_DIRS})
 endif()
 
-find_package(BLAS)
-if(BLAS_FOUND)
+if(WITH_BLAS)
+  find_package(BLAS REQUIRED)
   target_compile_definitions(cimg INTERFACE cimg_use_blas)
   target_link_libraries(cimg INTERFACE ${BLAS_LIBRARIES})
   target_include_directories(cimg INTERFACE ${BLAS_INCLUDE_DIRS})
 endif()
 
-find_package(FFTW)
-if(FFTW_FOUND)
+if(WITH_FFTW3)
+  find_package(FFTW3 CONFIG REQUIRED)
   target_compile_definitions(cimg INTERFACE cimg_use_fftw3)
-  target_link_libraries(cimg INTERFACE ${FFTW_LIBRARIES})
-  target_include_directories(cimg INTERFACE ${FFTW_INCLUDE_DIRS})
+  target_link_libraries(cimg INTERFACE FFTW3::fftw3)
 endif()
 
 if (CMAKE_MODULE_PATH)
-  find_package(OpenCV QUIET)
-  if (OpenCV_FOUND)
+  if (WITH_OPENCV)
+    find_package(OpenCV CONFIG REQUIRED)
     target_compile_definitions(cimg INTERFACE cimg_use_opencv)
-    target_link_libraries(cimg INTERFACE ${OpenCV_LIBRARIES})
-    target_include_directories(cimg INTERFACE ${OpenCV_INCLUDE_DIRS})
+    target_link_libraries(cimg INTERFACE opencv_core)
   endif()
 else()
   message("No CMAKE_MODULE_PATH path for OpenCV configured")
diff --git a/source/matplot/CMakeLists.txt b/source/matplot/CMakeLists.txt
index a0428ac..b6abc8e 100644
--- a/source/matplot/CMakeLists.txt
+++ b/source/matplot/CMakeLists.txt
@@ -112,7 +112,8 @@ target_include_directories(matplot
 install(FILES ${CMAKE_CURRENT_BINARY_DIR}/matplot/detail/exports.h DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}/matplot/detail)
 
 # Dependencies
-target_link_libraries_system(matplot PRIVATE cimg nodesoup)
+find_package(nodesoup CONFIG REQUIRED)
+target_link_libraries_system(matplot PRIVATE cimg nodesoup::nodesoup)
 
 # Required compiler features required
 # https://cmake.org/cmake/help/v3.14/manual/cmake-compile-features.7.html#requiring-language-standards
@@ -215,7 +216,7 @@ if (MATPLOTPP_BUILD_EXPERIMENTAL_OPENGL_BACKEND)
     find_package(OpenGL)
 
     # https://github.com/Dav1dde/glad
-    find_package(glad CONFIG)
+    find_package(GLAD NAMES glad CONFIG REQUIRED)
     if (NOT glad_FOUND)
         find_package(GLAD QUIET)
     endif()
@@ -238,7 +239,7 @@ if (MATPLOTPP_BUILD_EXPERIMENTAL_OPENGL_BACKEND)
     endif()
 
     # https://github.com/glfw/glfw
-    find_package(glfw3 QUIET)
+    find_package(GLFW3 NAMES glfw3 CONFIG REQUIRED)
     if ((NOT glfw3_FOUND AND NOT GLFW3_FOUND) OR NOT TARGET glfw)
         # Use CPM only if not found, to avoid ODR violations
         # find_package(glfw3 REQUIRE) would suffice if it worked well
