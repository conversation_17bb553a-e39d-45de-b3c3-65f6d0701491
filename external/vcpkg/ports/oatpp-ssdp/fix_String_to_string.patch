diff --git i/src/oatpp-ssdp/SimpleUdpStreamProvider.cpp w/src/oatpp-ssdp/SimpleUdpStreamProvider.cpp
index d351c1e..7c430e9 100644
--- i/src/oatpp-ssdp/SimpleUdpStreamProvider.cpp
+++ w/src/oatpp-ssdp/SimpleUdpStreamProvider.cpp
@@ -106,7 +106,7 @@ v_io_handle SimpleUdpStreamProvider::instantiateServer() {
     hints.ai_flags = AI_PASSIVE;
     auto portStr = oatpp::utils::conversion::int32ToStr(m_port);

-    ret = getaddrinfo(NULL, (const char*)portStr->getData(), &hints, &result);
+    ret = getaddrinfo(NULL, (const char*)portStr->c_str(), &hints, &result);
     if (ret != 0) {
         OATPP_LOGE("[oatpp::ssdp::SimpleUdpStreamProvider::instantiateServer()]", "Error. Call to getaddrinfo() failed with result=%d: %s", ret, strerror(errno));
         throw std::runtime_error("[oatpp::ssdp::SimpleUdpStreamProvider::instantiateServer()]: Error. Call to getaddrinfo() failed.");
@@ -233,4 +233,4 @@ provider::ResourceHandle<data::stream::IOStream> SimpleUdpStreamProvider::get()
   );
 }

-}}
\ No newline at end of file
+}}
