diff --git i/src/oatpp-ssdp/SimpleUdpStreamProvider.cpp w/src/oatpp-ssdp/SimpleUdpStreamProvider.cpp
index d351c1e..f5774bd 100644
--- i/src/oatpp-ssdp/SimpleUdpStreamProvider.cpp
+++ w/src/oatpp-ssdp/SimpleUdpStreamProvider.cpp
@@ -129,7 +129,7 @@ v_io_handle SimpleUdpStreamProvider::instantiateServer() {

     ret = bind(serverHandle, result->ai_addr, (int)result->ai_addrlen);
     if (ret != 0) {
-        ::close(serverHandle);
+        ::_close(serverHandle);
         OATPP_LOGE("[oatpp::ssdp::SimpleUdpStreamProvider::instantiateServer()]", "Error. Failed to bind port %d: %s", m_port, strerror(errno));
         throw std::runtime_error("[oatpp::ssdp::SimpleUdpStreamProvider::instantiateServer()]: Error. Can't bind to address: %s");
     }
@@ -233,4 +233,4 @@ provider::ResourceHandle<data::stream::IOStream> SimpleUdpStreamProvider::get()
   );
 }

-}}
\ No newline at end of file
+}}
