# Automatically generated by scripts/boost/generate-ports.ps1

vcpkg_from_github(
    OUT_SOURCE_PATH SOURCE_PATH
    REPO boostorg/compute
    REF boost-${VERSION}
    SHA512 75e986e9082a72293649514604d35aa95eda1e2a9824d4d374c5fd407822a36f37021b50921661608cd97f55e2931d07ba65bc4c24e192b4f58235f80ee2ca1f
    HEAD_REF master
    PATCHES
        opt-filesystem.diff
)

set(FEATURE_OPTIONS "")
boost_configure_and_install(
    SOURCE_PATH "${SOURCE_PATH}"
    OPTIONS ${FEATURE_OPTIONS}
)
