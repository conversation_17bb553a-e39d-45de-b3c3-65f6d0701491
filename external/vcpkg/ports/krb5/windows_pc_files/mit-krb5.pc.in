prefix=${pcfiledir}/../..
exec_prefix=${prefix}
libdir=${prefix}/lib
includedir=${prefix}/include

defccname=FILE:/tmp/krb5cc_%{uid}
defktname=FILE:/etc/krb5.keytab
defcktname=FILE:${prefix}/var/krb5/user/%{euid}/client.keytab

Name: mit-krb5
Description: An implementation of Kerberos network authentication
Version: @VERSION@
Cflags: -I${includedir}
Libs: -L${libdir} -lkrb5_64 -lkrbcc64 -lxpprof64 -lkfwlogon -lcomerr64 -lk5sprt64
Libs.private: -lk5sprt64
