--- a/libvmaf/src/meson.build
+++ b/libvmaf/src/meson.build
@@ -13,6 +13,13 @@ if cc.get_id() != 'msvc'
         '-pedantic',
         '-DOC_NEW_STYLE_INCLUDES',
     ]
+    # If the target is Android, define _LIBCPP_HAS_NO_OFF_T_FUNCTIONS unconditionally
+    # to work around the fact that meson always defines _FILE_OFFSET_BITS=64, which
+    # causes issues for API levels below 24 in 32-bit architectures.
+    # See https://github.com/mesonbuild/meson/issues/3049 for more details.
+    if target_machine.system() == 'android'
+        vmaf_cflags_common += '-D_LIBCPP_HAS_NO_OFF_T_FUNCTIONS'
+    endif
 else
     vmaf_cflags_common = [
       '-wd4028', # parameter different from declaration
