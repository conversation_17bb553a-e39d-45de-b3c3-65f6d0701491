diff --git a/libcfile/libcfile_file.c b/libcfile/libcfile_file.c
index 45e3fc8..5195b7a 100755
--- a/libcfile/libcfile_file.c
+++ b/libcfile/libcfile_file.c
@@ -56,7 +56,7 @@
 #elif defined( HAVE_CYGWIN_FS_H )
 #include <cygwin/fs.h>
 
-#elif defined( HAVE_LINUX_FS_H )
+#elif defined( __linux__ ) && defined( HAVE_LINUX_FS_H )
 /* Required for Linux platforms that use a sizeof( u64 )
  * in linux/fs.h but have no typedef of it
  */
@@ -4194,6 +4194,12 @@ ssize_t libcfile_file_io_control_read(
 	return( read_count );
 }
 
+// Force disable on Darwin, it can be erroneously defined
+#if defined ( __APPLE__ )
+#undef HAVE_POSIX_FADVISE
+#endif
+
+
 /* Read data from a device file using IO control
  * Returns the number of bytes read if successful or -1 on error
  */
diff --git a/libclocale/libclocale_support.c b/libclocale/libclocale_support.c
index f5e29c2..56c4724 100755
--- a/libclocale/libclocale_support.c
+++ b/libclocale/libclocale_support.c
@@ -68,7 +68,7 @@ int libclocale_initialize(
 
 		return( -1 );
 	}
-#if defined( HAVE_BINDTEXTDOMAIN ) && defined( HAVE_TEXTDOMAIN )
+#if !defined( __APPLE__) && defined( HAVE_BINDTEXTDOMAIN ) && defined( HAVE_TEXTDOMAIN )
 	if( bindtextdomain(
 	     domain_name,
 	     LOCALEDIR ) == NULL )
diff --git a/libvhdi/libvhdi_i18n.c b/libvhdi/libvhdi_i18n.c
index fb33e05..13e8c39 100755
--- a/libvhdi/libvhdi_i18n.c
+++ b/libvhdi/libvhdi_i18n.c
@@ -40,7 +40,7 @@ int libvhdi_i18n_initialize(
 
 	if( libvhdi_i18n_initialized == 0 )
 	{
-#if defined( HAVE_BINDTEXTDOMAIN ) && defined( LOCALEDIR )
+#if !defined( __APPLE__ ) && defined( HAVE_BINDTEXTDOMAIN ) && defined( LOCALEDIR )
 		if( bindtextdomain(
 		     "libvhdi",
 		     LOCALEDIR ) == NULL )
