{"name": "qnnpack", "version-date": "2021-02-26", "port-version": 3, "description": "Quantized Neural Network PACKage - mobile-optimized implementation of quantized neural network operators", "homepage": "https://github.com/pytorch/QNNPACK", "license": "BSD-3-<PERSON><PERSON>", "supports": "!windows", "dependencies": ["cpuinfo", "fp16", "fxdiv", "psimd", "pthreadpool", {"name": "vcpkg-cmake", "host": true}]}