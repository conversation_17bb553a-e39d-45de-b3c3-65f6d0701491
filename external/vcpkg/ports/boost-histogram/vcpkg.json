{"$comment": "Automatically generated by scripts/boost/generate-ports.ps1", "name": "boost-histogram", "version": "1.87.0", "description": "Boost histogram module", "homepage": "https://www.boost.org/libs/histogram", "license": "BSL-1.0", "dependencies": [{"name": "boost-cmake", "version>=": "1.87.0"}, {"name": "boost-config", "version>=": "1.87.0"}, {"name": "boost-core", "version>=": "1.87.0"}, {"name": "boost-headers", "version>=": "1.87.0"}, {"name": "boost-math", "version>=": "1.87.0"}, {"name": "boost-mp11", "version>=": "1.87.0"}, {"name": "boost-serialization", "version>=": "1.87.0"}, {"name": "boost-throw-exception", "version>=": "1.87.0"}, {"name": "boost-variant2", "version>=": "1.87.0"}]}