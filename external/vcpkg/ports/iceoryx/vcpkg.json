{"name": "iceoryx", "version": "2.0.6", "port-version": 1, "description": "True zero-copy inter-process-communication", "homepage": "https://iceoryx.io", "license": "Apache-2.0", "supports": "!uwp", "dependencies": [{"name": "acl", "platform": "linux"}, {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "default-features": ["many-to-many", "toml-config"], "features": {"many-to-many": {"description": "Using the m:n pattern for communication"}, "toml-config": {"description": "TOML support for RouDi with dynamic configuration", "dependencies": ["cpptoml"]}}}