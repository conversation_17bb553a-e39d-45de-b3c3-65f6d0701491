vcpkg_from_github(
    OUT_SOURCE_PATH SOURCE_PATH
    REPO z4kn4fein/cpp-semver
    REF "v${VERSION}"
    SHA512 115cb6ab37e20c03db8f1052fa09872bb3b081c10a310fea82c8b42fad79361d4d7597511538c79553411c88f8d55926d72f1db79163aa0c97b87100db186acb
    HEAD_REF main
)

vcpkg_cmake_configure(
    SOURCE_PATH "${SOURCE_PATH}"
    OPTIONS
        -DSEMVER_BUILD_TESTS=OFF
)
vcpkg_cmake_install()

file(REMOVE_RECURSE ${CURRENT_PACKAGES_DIR}/debug)
file(INSTALL "${SOURCE_PATH}/LICENSE" DESTINATION "${CURRENT_PACKAGES_DIR}/share/${PORT}" RENAME copyright)
