diff --git a/src/qdoc/configure.pri b/src/qdoc/configure.pri
index 4f3d776..547d6a6 100644
--- a/src/qdoc/configure.pri
+++ b/src/qdoc/configure.pri
@@ -87,10 +87,7 @@ defineTest(qtConfTest_libclang) {
     }
     LLVM_INSTALL_DIR = $$clean_path($$LLVM_INSTALL_DIR)
 
-    contains(QMAKE_HOST.arch, x86_64): \
-        clangInstallDir = $$replace(LLVM_INSTALL_DIR, _ARCH_, 64)
-    else: \
-        clangInstallDir = $$replace(LLVM_INSTALL_DIR, _ARCH_, 32)
+    clangInstallDir = $$LLVM_INSTALL_DIR
     isEmpty(LLVM_INSTALL_DIR) {
         win32 {
             return(false)
@@ -113,7 +110,13 @@ defineTest(qtConfTest_libclang) {
     } else {
         CLANG_LIBDIR = $$clangInstallDir/lib
         CLANG_INCLUDEPATH = $$clangInstallDir/include
+        exists($$clangInstallDir/tools/llvm): {
+            output = $$system("$$clangInstallDir/tools/llvm/llvm-config --version")
+            CLANG_VERSION = $$extractVersion($$output)
+        }
+        else: {
         CLANG_VERSION = $$findLLVMVersionFromLibDir($$CLANG_LIBDIR)
+        }
     }
     isEmpty(CLANG_VERSION) {
         !isEmpty(LLVM_INSTALL_DIR): \
