{"name": "micro-gl", "version-date": "2024-06-18", "description": "Fast, Super Slim, Embeddable, Headers only C++11 vector graphics library, that can run on any 32/64 bits computer without FPU or GPU. No standard library required.", "homepage": "https://micro-gl.github.io/docs/microgl", "license": null, "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}]}