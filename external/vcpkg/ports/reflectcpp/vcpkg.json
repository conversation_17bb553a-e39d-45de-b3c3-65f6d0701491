{"name": "reflectcpp", "version": "0.18.0", "description": "A C++ library for serialization and deserialization using reflection. Supports JSON, Avro, BSON, Cap'n Proto, CBOR, flexbuffers, msgpack, TOML, UBJSON, XML, YAML.", "homepage": "https://github.com/getml/reflect-cpp/", "license": "MIT", "dependencies": [{"name": "ctre", "version>=": "3.9.0"}, {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "version>=": "0.10.0"}], "features": {"bson": {"description": "Support for the BSON format", "dependencies": [{"name": "lib<PERSON>", "version>=": "1.25.1"}]}, "capnproto": {"description": "Support for the Cap'n Proto format", "dependencies": [{"name": "capnproto", "version>=": "1.0.2#1"}]}, "cbor": {"description": "Support for the CBOR format", "dependencies": [{"name": "jsoncons", "version>=": "0.176.0"}]}, "flexbuffers": {"description": "Support for the flexbuffers format (part of flatbuffers)", "dependencies": [{"name": "flatbuffers", "version>=": "23.5.26#1"}]}, "msgpack": {"description": "Support for the msgpack format", "dependencies": [{"name": "msgpack-c", "version>=": "6.0.0"}]}, "toml": {"description": "Support for the TOML format", "dependencies": [{"name": "toml11", "version>=": "4.2.0"}]}, "ubjson": {"description": "Support for the UBJSON format", "dependencies": [{"name": "jsoncons", "version>=": "0.176.0"}]}, "xml": {"description": "Support for the XML format", "dependencies": [{"name": "pugixml", "version>=": "1.14"}]}, "yaml": {"description": "Support for the YAML format", "dependencies": [{"name": "yaml-cpp", "version>=": "0.8.0#1"}]}}}