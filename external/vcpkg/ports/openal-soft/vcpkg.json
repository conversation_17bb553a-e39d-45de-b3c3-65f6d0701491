{"name": "openal-soft", "version": "1.24.3", "description": "OpenAL Soft is an LGPL-licensed, cross-platform, software implementation of the OpenAL 3D audio API.", "homepage": "https://github.com/kcat/openal-soft", "license": "LGPL-2.0-or-later", "supports": "!xbox", "dependencies": [{"name": "alsa", "platform": "linux"}, {"name": "cppwinrt", "platform": "uwp"}, {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"pipewire": {"description": "Native support for Pipewire", "supports": "linux", "dependencies": ["pipewire"]}}}