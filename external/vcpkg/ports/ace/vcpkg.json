{"name": "ace", "version": "8.0.2", "maintainers": "<PERSON> <j<PERSON><PERSON><PERSON>@remedy.nl>", "description": "The ADAPTIVE Communication Environment", "homepage": "https://github.com/DOCGroup/ACE_TAO", "license": "DOC", "supports": "!uwp", "dependencies": [{"name": "vcpkg-msbuild", "host": true, "platform": "windows"}], "features": {"ssl": {"description": "Enable SSL/TLS features in ACE", "dependencies": ["openssl"]}, "tao": {"description": "The ACE ORB", "supports": "native | !(windows & arm)"}, "wchar": {"description": "Enable extra wide char functions in ACE", "supports": "!osx"}, "xml": {"description": "Enable XML features in ACE"}, "xml-utils": {"description": "Include the ACE_XML_Utils library", "dependencies": [{"name": "xerces-c", "features": ["xmlch-wchar"]}]}, "zlib": {"description": "Enable zlib support", "dependencies": ["zlib"]}}}