{"name": "eastl", "version": "3.21.23", "description": "Electronic Arts Standard Template Library. It is a C++ template library of containers, algorithms, and iterators useful for runtime and tool development across multiple platforms. It is a fairly extensive and robust implementation of such a library and has an emphasis on high performance above all other considerations.", "homepage": "https://github.com/electronicarts/EASTL", "license": "BSD-3-<PERSON><PERSON>", "dependencies": ["eabase", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}]}