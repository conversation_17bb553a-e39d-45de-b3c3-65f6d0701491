diff --git a/scripts/CMake/CommonCppFlags.cmake b/scripts/CMake/CommonCppFlags.cmake
index 08b6af5..281665e 100644
--- a/scripts/CMake/CommonCppFlags.cmake
+++ b/scripts/CMake/CommonCppFlags.cmake
@@ -74,6 +74,9 @@ elseif(CMAKE_CXX_COMPILER_ID MATCHES "GNU")
     endif()
 elseif(CMAKE_CXX_COMPILER_ID MATCHES "MSVC")
     set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /std:c++latest /W4 /permissive-")
+    if(CMAKE_CXX_COMPILER_VERSION VERSION_GREATER_EQUAL "19.31.30911.95")
+        set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /Zc:static_assert-")
+    endif()
 endif()
 
 
