diff --git a/CMakeLists.txt b/CMakeLists.txt
index a5870f8..1553513 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -5,6 +5,9 @@ cmake_minimum_required(VERSION 3.11)
 include(Fetch<PERSON>ontent)
 project(EASTL CXX)
 
+include(GNUInstallDirs)
+include(CMakePackageConfigHelpers)
+
 #-------------------------------------------------------------------------------------------
 # Options
 #-------------------------------------------------------------------------------------------
@@ -17,6 +20,8 @@ option(EASTL_DISABLE_APRIL_2024_DEPRECATIONS "Enable use of API marked for remov
 option(EASTL_DISABLE_SEPT_2024_DEPRECATIONS "Enable use of API marked for removal in September 2024." OFF)
 option(EASTL_DISABLE_APRIL_2025_DEPRECATIONS "Enable use of API marked for removal in April 2025." OFF)
 
+find_package(EABase CONFIG REQUIRED)
+
 #-------------------------------------------------------------------------------------------
 # Compiler Flags
 #-------------------------------------------------------------------------------------------
@@ -65,21 +70,49 @@ endif()
 #-------------------------------------------------------------------------------------------
 # Include dirs
 #-------------------------------------------------------------------------------------------
-target_include_directories(EASTL PUBLIC include)
+target_include_directories(EASTL PUBLIC
+        $<BUILD_INTERFACE:${CMAKE_CURRENT_LIST_DIR}/include>
+        $<INSTALL_INTERFACE:${CMAKE_INSTALL_INCLUDEDIR}>
+    )
 
 #-------------------------------------------------------------------------------------------
 # Dependencies
 #-------------------------------------------------------------------------------------------
-FetchContent_Declare(
-  EABase
-  GIT_REPOSITORY https://github.com/electronicarts/EABase.git
-  GIT_TAG        123363eb82e132c0181ac53e43226d8ee76dea12
-  GIT_SUBMODULES "" # This should be temporary until we update the cyclic submodule dependencies in EABase.
+
+target_link_libraries(EASTL PUBLIC EABase)
+
+# create and install an export set for eabase target as EABase::EABase
+set(EASTL_CMAKE_CONFIG_DESTINATION "${CMAKE_INSTALL_LIBDIR}/cmake/EASTL")
+
+configure_package_config_file(
+    EASTLConfig.cmake.in
+    ${CMAKE_CURRENT_BINARY_DIR}/EASTLConfig.cmake
+    INSTALL_DESTINATION ${EASTL_CMAKE_CONFIG_DESTINATION}
 )
 
-FetchContent_MakeAvailable(EABase)
+# create and install an export set for Terra target as Terra
+install(
+    TARGETS EASTL EXPORT EASTLTargets
+    DESTINATION ${CMAKE_INSTALL_LIBDIR}
+)
+
+install(EXPORT EASTLTargets DESTINATION ${EASTL_CMAKE_CONFIG_DESTINATION})
+
+write_basic_package_version_file(
+  "${CMAKE_CURRENT_BINARY_DIR}/EASTLConfigVersion.cmake"
+  VERSION 3.16.05
+  COMPATIBILITY SameMajorVersion
+)
+
+install(TARGETS EASTL LIBRARY DESTINATION "${CMAKE_INSTALL_LIBDIR}")
+install(DIRECTORY "include/" DESTINATION "${CMAKE_INSTALL_INCLUDEDIR}")
 
-target_link_libraries(EASTL EABase)
+install(
+    FILES
+        "${CMAKE_CURRENT_BINARY_DIR}/EASTLConfig.cmake"
+        "${CMAKE_CURRENT_BINARY_DIR}/EASTLConfigVersion.cmake"
+    DESTINATION ${EASTL_CMAKE_CONFIG_DESTINATION}
+)
 
 #-------------------------------------------------------------------------------------------
 # Deprecations
