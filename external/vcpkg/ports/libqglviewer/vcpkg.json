{"name": "libqglviewer", "version": "2.9.1", "port-version": 3, "description": "libQGLViewer is an open source C++ library based on Qt that eases the creation of OpenGL 3D viewers.", "homepage": "http://libqglviewer.com/", "license": "GPL-2.0-or-later", "supports": "!xbox", "dependencies": ["opengl", {"name": "qtbase", "default-features": false, "features": ["gui", "opengl", "widgets"]}, {"name": "vcpkg-cmake", "host": true}]}