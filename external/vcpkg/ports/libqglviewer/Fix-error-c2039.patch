diff --git a/QGLViewer/VRender/NVector3.h b/QGLViewer/VRender/NVector3.h
index 40b7f98..f2d8305 100644
--- a/QGLViewer/VRender/NVector3.h
+++ b/QGLViewer/VRender/NVector3.h
@@ -7,6 +7,8 @@
 namespace vrender
 {
   class Vector3;
+  class NVector3;
+  std::ostream& operator<<(std::ostream &out,const NVector3 &u);
 
   class NVector3
   {
diff --git a/QGLViewer/VRender/Primitive.h b/QGLViewer/VRender/Primitive.h
index 88ab11d..d38470d 100644
--- a/QGLViewer/VRender/Primitive.h
+++ b/QGLViewer/VRender/Primitive.h
@@ -21,6 +21,7 @@ namespace vrender
 {
 	class Feedback3DColor ;
 	class Primitive ;
+    std::ostream& operator<<(std::ostream&, const Feedback3DColor&) ;
 
 #define EPS_SMOOTH_LINE_FACTOR 0.06  /* Lower for better smooth lines. */
 
diff --git a/QGLViewer/VRender/Vector2.h b/QGLViewer/VRender/Vector2.h
index f6aaaf3..7b9b82a 100644
--- a/QGLViewer/VRender/Vector2.h
+++ b/QGLViewer/VRender/Vector2.h
@@ -6,7 +6,9 @@
 
 namespace vrender
 {
+  class Vector2;
   class Vector3;
+  std::ostream& operator<< (std::ostream&,const Vector2&);
 
   class Vector2
 	{
diff --git a/QGLViewer/VRender/Vector3.h b/QGLViewer/VRender/Vector3.h
index 32597e8..f6d5099 100644
--- a/QGLViewer/VRender/Vector3.h
+++ b/QGLViewer/VRender/Vector3.h
@@ -10,6 +10,8 @@
 namespace vrender
 {
   class NVector3;
+  class Vector3;
+  std::ostream& operator<< (std::ostream&, const Vector3&);
 
 	class Vector3
 	{
