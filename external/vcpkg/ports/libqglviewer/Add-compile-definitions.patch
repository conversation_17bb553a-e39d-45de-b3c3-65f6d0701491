--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -58,9 +58,19 @@
    "${PROJECT_SOURCE_DIR}/QGLViewer/quaternion.cpp"
    "${PROJECT_SOURCE_DIR}/QGLViewer/saveSnapshot.cpp"
    "${PROJECT_SOURCE_DIR}/QGLViewer/vec.cpp")
-add_library(QGLViewer SHARED ${QGLViewer_SRC})
+add_library(QGLViewer ${QGLViewer_SRC})
 target_include_directories(QGLViewer INTERFACE ${CMAKE_CURRENT_SOURCE_DIR})
 target_link_libraries(QGLViewer PRIVATE ${QtLibs} OpenGL::GL OpenGL::GLU)
+if(MSVC)
+  target_compile_definitions(QGLViewer PRIVATE NOMINMAX)
+endif()
+if(WIN32)
+  if(BUILD_SHARED_LIBS)
+    target_compile_definitions(QGLViewer PRIVATE CREATE_QGLVIEWER_DLL)
+  else()
+    target_compile_definitions(QGLViewer PUBLIC QGLVIEWER_STATIC)
+  endif()
+endif()
 
 # Example: animation.
 set(animation_SRC
