diff --git a/CMakeLists.txt b/CMakeLists.txt
index b7815ae..86d79d1 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -555,7 +555,7 @@ if (CMAKE_INSTALL_PREFIX_INITIALIZED_TO_DEFAULT)
 endif (CMAKE_INSTALL_PREFIX_INITIALIZED_TO_DEFAULT)
 
 # Set the installation paths for headers, libraries, and documentation.
-set (CMAKE_INSTALL_DOCDIR "share/doc/systemc" CACHE PATH
+set (CMAKE_INSTALL_DOCDIR "share/systemc/doc" CACHE PATH
      "Documentation installation directory") # otherwise mixed-case
 if (INSTALL_TO_LIB_BUILD_TYPE_DIR)
   # Install libraries to lib-${CMAKE_BUILD_TYPE} so that different build variants
@@ -584,14 +584,14 @@ if (INSTALL_LIB_TARGET_ARCH_SYMLINK AND CMAKE_HOST_UNIX)
 endif (INSTALL_LIB_TARGET_ARCH_SYMLINK AND CMAKE_HOST_UNIX)
 
 if (INSTALL_TO_LIB_BUILD_TYPE_DIR OR INSTALL_TO_LIB_TARGET_ARCH_DIR)
-  set (SystemCLanguage_INSTALL_CMAKEDIR share/cmake/SystemCLanguage CACHE PATH
+  set (SystemCLanguage_INSTALL_CMAKEDIR lib/cmake/SystemCLanguage CACHE PATH
        "CMake package configuration installation directory" FORCE)
   set (SystemCTLM_INSTALL_CMAKEDIR share/cmake/SystemCTLM CACHE PATH
        "CMake package configuration installation directory" FORCE)
 else (INSTALL_TO_LIB_BUILD_TYPE_DIR OR INSTALL_TO_LIB_TARGET_ARCH_DIR)
-  set (SystemCLanguage_INSTALL_CMAKEDIR ${CMAKE_INSTALL_LIBDIR}/cmake/SystemCLanguage CACHE PATH
+  set (SystemCLanguage_INSTALL_CMAKEDIR lib/cmake/SystemCLanguage CACHE PATH
        "CMake package configuration installation directory for the SystemCLanguage package.")
-  set (SystemCTLM_INSTALL_CMAKEDIR ${CMAKE_INSTALL_LIBDIR}/cmake/SystemCTLM CACHE PATH
+  set (SystemCTLM_INSTALL_CMAKEDIR share/cmake/SystemCTLM CACHE PATH
        "CMake package configuration installation directory for the SystemCTLM package.")
 endif (INSTALL_TO_LIB_BUILD_TYPE_DIR OR INSTALL_TO_LIB_TARGET_ARCH_DIR)
 mark_as_advanced(SystemCLanguage_INSTALL_CMAKEDIR SystemCTLM_INSTALL_CMAKEDIR)
