{"name": "gtsam", "version": "4.2.0", "port-version": 1, "description": "GTSAM is a library of C++ classes that implement smoothing and mapping (SAM) in robotics and vision, using factor graphs and Bayes networks as the underlying computing paradigm rather than sparse matrices.", "homepage": "https://github.com/borglab/gtsam", "license": "BSD-3-<PERSON><PERSON>", "dependencies": ["boost-assign", "boost-bimap", "boost-date-time", "boost-filesystem", "boost-format", "boost-graph", "boost-math", "boost-program-options", "boost-regex", "boost-serialization", "boost-system", "boost-thread", "boost-timer", "eigen3", "metis", "tbb", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}]}