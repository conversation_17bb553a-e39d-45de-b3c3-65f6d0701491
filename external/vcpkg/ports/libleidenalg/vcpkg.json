{"name": "libleidenalg", "version": "0.11.1", "maintainers": "<PERSON> <<EMAIL>>", "description": "Leiden is a general algorithm for methods of community detection in large networks.", "homepage": "https://github.com/vtraag/libleidenalg", "license": "GPL-3.0-or-later", "supports": "!xbox", "dependencies": [{"name": "igraph", "version>=": "0.10.1"}, {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}]}