diff --git a/src/CMakeLists.txt b/src/CMakeLists.txt
index d983089..19736d8 100644
--- a/src/CMakeLists.txt
+++ b/src/CMakeLists.txt
@@ -264,7 +264,6 @@ if (NOT MSVC)
   if (SPM_COVERAGE)
     set(CMAKE_CXX_FLAGS "-O0 -Wall -fPIC -coverage ${CMAKE_CXX_FLAGS}")
   else()
-    set(CMAKE_CXX_FLAGS "-O3 -Wall -fPIC ${CMAKE_CXX_FLAGS}")
   endif()
   if (SPM_ENABLE_TENSORFLOW_SHARED)
     add_definitions(-D_GLIBCXX_USE_CXX11_ABI=0)
