diff --git a/xorg-macros.m4.in b/xorg-macros.m4.in
index f0a16da3a..e1c0ff1ed 100644
--- a/xorg-macros.m4.in
+++ b/xorg-macros.m4.in
@@ -63,38 +63,6 @@ AC_PATH_TOOL(RAWCPP, [cpp], [${CPP}],
 # Check for flag to avoid builtin definitions - assumes unix is predefined,
 # which is not the best choice for supporting other OS'es, but covers most
 # of the ones we need for now.
-AC_MSG_CHECKING([if $RAWCPP requires -undef])
-AC_LANG_CONFTEST([AC_LANG_SOURCE([[Does cpp redefine unix ?]])])
-if test `${RAWCPP} < conftest.$ac_ext | grep -c 'unix'` -eq 1 ; then
-	AC_MSG_RESULT([no])
-else
-	if test `${RAWCPP} -undef < conftest.$ac_ext | grep -c 'unix'` -eq 1 ; then
-		RAWCPPFLAGS=-undef
-		AC_MSG_RESULT([yes])
-	# under Cygwin unix is still defined even with -undef
-	elif test `${RAWCPP} -undef -ansi < conftest.$ac_ext | grep -c 'unix'` -eq 1 ; then
-		RAWCPPFLAGS="-undef -ansi"
-		AC_MSG_RESULT([yes, with -ansi])
-	else
-		AC_MSG_ERROR([${RAWCPP} defines unix with or without -undef.  I don't know what to do.])
-	fi
-fi
-rm -f conftest.$ac_ext
-
-AC_MSG_CHECKING([if $RAWCPP requires -traditional])
-AC_LANG_CONFTEST([AC_LANG_SOURCE([[Does cpp preserve   "whitespace"?]])])
-if test `${RAWCPP} < conftest.$ac_ext | grep -c 'preserve   \"'` -eq 1 ; then
-	AC_MSG_RESULT([no])
-else
-	if test `${RAWCPP} -traditional < conftest.$ac_ext | grep -c 'preserve   \"'` -eq 1 ; then
-		TRADITIONALCPPFLAGS="-traditional"
-		RAWCPPFLAGS="${RAWCPPFLAGS} -traditional"
-		AC_MSG_RESULT([yes])
-	else
-		AC_MSG_ERROR([${RAWCPP} does not preserve whitespace with or without -traditional.  I don't know what to do.])
-	fi
-fi
-rm -f conftest.$ac_ext
 AC_SUBST(RAWCPPFLAGS)
 AC_SUBST(TRADITIONALCPPFLAGS)
 ]) # XORG_PROG_RAWCPP
