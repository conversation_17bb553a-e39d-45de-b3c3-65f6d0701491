cmake_minimum_required(VERSION 3.21)
cmake_policy(VERSION 3.21)

if (BUILD_SHARED_LIBS)
  add_definitions("-DXQILLA_APIS=")
else()
  add_definitions("-DXQILLA_API=")
endif()

project(xqilla C CXX)

set(CMAKE_CXX_STANDARD 11) # 17 does not allow 'register'

add_library(xqilla  
./src/exceptions/XQException.cpp
./src/exceptions/XQillaException.cpp
./src/fastxdm/FastXDMSequenceBuilder.cpp
./src/fastxdm/FastXDMNodeImpl.cpp
./src/fastxdm/FastXDMDocument.cpp
./src/fastxdm/FastXDMUpdateFactory.cpp
./src/fastxdm/FastXDMConfiguration.cpp
./src/ast/XQAttributeConstructor.cpp
./src/ast/XQOrderingChange.cpp
./src/ast/XQReturn.cpp
./src/ast/XQPredicate.cpp
./src/ast/XQNamespaceBinding.cpp
./src/ast/XQCopy.cpp
./src/ast/XQQuantified.cpp
./src/ast/NumericFunction.cpp
./src/ast/XQOperator.cpp
./src/ast/XQVariable.cpp
./src/ast/StaticAnalysis.cpp
./src/ast/XQFunctionRef.cpp
./src/ast/XQCastAs.cpp
./src/ast/XQStep.cpp
./src/ast/XQLiteral.cpp
./src/ast/XQCopyOf.cpp
./src/ast/XQElementConstructor.cpp
./src/ast/XQDocumentConstructor.cpp
./src/ast/XQTreatAs.cpp
./src/ast/XQCallTemplate.cpp
./src/ast/XQTextConstructor.cpp
./src/ast/XQNamespaceConstructor.cpp
./src/ast/XQMap.cpp
./src/ast/XQFunctionCoercion.cpp
./src/ast/XQTypeswitch.cpp
./src/ast/XQAtomize.cpp
./src/ast/XQInlineFunction.cpp
./src/ast/XQSequence.cpp
./src/ast/LetTuple.cpp
./src/ast/WhereTuple.cpp
./src/ast/XQPIConstructor.cpp
./src/ast/XQFunctionDeref.cpp
./src/ast/StaticType.cpp
./src/ast/ContextTuple.cpp
./src/ast/XQGlobalVariable.cpp
./src/ast/OrderByTuple.cpp
./src/ast/ForTuple.cpp
./src/ast/XQFunction.cpp
./src/ast/CountTuple.cpp
./src/ast/XQFunctionConversion.cpp
./src/ast/XQApplyTemplates.cpp
./src/ast/XQCommentConstructor.cpp
./src/ast/XQCastableAs.cpp
./src/ast/XQIf.cpp
./src/ast/XQContextItem.cpp
./src/ast/XQDOMConstructor.cpp
./src/ast/ASTNodeImpl.cpp
./src/ast/TupleNode.cpp
./src/ast/XQNav.cpp
./src/ast/XQEffectiveBooleanValue.cpp
./src/ast/XQDocumentOrder.cpp
./src/ast/XQFunctionCall.cpp
./src/ast/XQValidate.cpp
./src/ast/ConvertFunctionArg.cpp
./src/ast/XQPartialApply.cpp
./src/ast/XQAnalyzeString.cpp
./src/axis/NodeTest.cpp
./src/fulltext/FTContent.cpp
./src/fulltext/FTOrder.cpp
./src/fulltext/DefaultTokenStore.cpp
./src/fulltext/DefaultTokenizer.cpp
./src/fulltext/FTWords.cpp
./src/fulltext/FTOr.cpp
./src/fulltext/FTContains.cpp
./src/fulltext/FTScope.cpp
./src/fulltext/FTWindow.cpp
./src/fulltext/FTMildnot.cpp
./src/fulltext/FTAnd.cpp
./src/fulltext/FTUnaryNot.cpp
./src/fulltext/FTDistance.cpp
./src/framework/ProxyMemoryManager.cpp
./src/framework/XPath2MemoryManagerImpl.cpp
./src/framework/StringPool.cpp
./src/framework/BaseMemoryManager.cpp
./src/framework/ReferenceCounted.cpp
./src/xqc/XQillaXQCExpression.cpp
./src/xqc/XQillaXQCStaticContext.cpp
./src/xqc/XQillaXQCDynamicContext.cpp
./src/xqc/XQillaXQCImplementation.cpp
./src/xqc/XQillaXQCSequence.cpp
./src/schema/SequenceType.cpp
./src/schema/FaxppDocumentCacheImpl.cpp
./src/schema/AnyAtomicTypeDatatypeValidator.cpp
./src/schema/DocumentCacheImpl.cpp
./src/schema/SchemaValidatorFilter.cpp
./src/optimizer/Optimizer.cpp
./src/optimizer/StaticTyper.cpp
./src/optimizer/ASTVisitor.cpp
./src/optimizer/ASTReleaser.cpp
./src/optimizer/ASTCopier.cpp
./src/optimizer/QueryPathNode.cpp
./src/optimizer/QueryPathTreeGenerator.cpp
./src/optimizer/ASTToXML.cpp
./src/optimizer/PartialEvaluator.cpp
./src/xqts/TestSuiteRunner.cpp
./src/xqts/TestSuiteParser.cpp
./src/xqts/TestSuiteResultListener.cpp
./src/items/Numeric.cpp
./src/items/DatatypeFactory.cpp
./src/items/AnyAtomicType.cpp
./src/items/DateOrTimeType.cpp
./src/items/DatatypeLookup.cpp
./src/items/Timezone.cpp
./src/items/ATUntypedAtomic.cpp
./src/items/impl/ATUntypedAtomicImpl.cpp
./src/items/impl/FunctionRefImpl.cpp
./src/items/impl/ATStringOrDerivedImpl.cpp
./src/items/impl/ATDateTimeOrDerivedImpl.cpp
./src/items/impl/ATDurationOrDerivedImpl.cpp
./src/items/impl/ATAnySimpleTypeImpl.cpp
./src/items/impl/ATTimeOrDerivedImpl.cpp
./src/items/impl/ATGYearOrDerivedImpl.cpp
./src/items/impl/ATFloatOrDerivedImpl.cpp
./src/items/impl/ATGMonthOrDerivedImpl.cpp
./src/items/impl/ATGDayOrDerivedImpl.cpp
./src/items/impl/ATBooleanOrDerivedImpl.cpp
./src/items/impl/ATQNameOrDerivedImpl.cpp
./src/items/impl/ATDoubleOrDerivedImpl.cpp
./src/items/impl/ATAnyURIOrDerivedImpl.cpp
./src/items/impl/ATGYearMonthOrDerivedImpl.cpp
./src/items/impl/ATNotationOrDerivedImpl.cpp
./src/items/impl/ATHexBinaryOrDerivedImpl.cpp
./src/items/impl/ATBase64BinaryOrDerivedImpl.cpp
./src/items/impl/ATDecimalOrDerivedImpl.cpp
./src/items/impl/ATGMonthDayOrDerivedImpl.cpp
./src/items/impl/ATDateOrDerivedImpl.cpp
./src/items/Item.cpp
./src/items/Node.cpp
./src/items/ATDurationOrDerived.cpp
./src/lexer/XSLT2Lexer.cpp
./src/lexer/XQLexer.cpp
./src/lexer/XQLexer2.cpp
./src/simple-api/XQilla.cpp
./src/simple-api/XQQuery.cpp
./src/utils/UTF8Str.cpp
./src/utils/XMLChCompare.cpp
./src/utils/XStr.cpp
./src/utils/DelayedModule.cpp
./src/utils/ContextUtils.cpp
./src/utils/UnicodeTransformer.cpp
./src/utils/NumUtils.cpp
./src/utils/XPath2Utils.cpp
./src/utils/XPath2NSUtils.cpp
./src/utils/DateUtils.cpp
./src/utils/XQillaPlatformUtils.cpp
./src/mapm/mapm_mt.cpp
./src/mapm/m_apm.cpp
./src/xerces/XercesNodeImpl.cpp
./src/xerces/AncestorAxis.cpp
./src/xerces/FollowingAxis.cpp
./src/xerces/XercesURIResolver.cpp
./src/xerces/AttributeAxis.cpp
./src/xerces/NamespaceAxis.cpp
./src/xerces/DescendantOrSelfAxis.cpp
./src/xerces/XercesConfiguration.cpp
./src/xerces/PrecedingAxis.cpp
./src/xerces/ParentAxis.cpp
./src/xerces/PrecedingSiblingAxis.cpp
./src/xerces/ChildAxis.cpp
./src/xerces/DescendantAxis.cpp
./src/xerces/Axis.cpp
./src/xerces/XercesUpdateFactory.cpp
./src/xerces/AncestorOrSelfAxis.cpp
./src/xerces/XercesSequenceBuilder.cpp
./src/xerces/FollowingSiblingAxis.cpp
./src/parser/XQParser.cpp
./src/parser/QName.cpp
./src/functions/FunctionMinutesFromTime.cpp
./src/functions/FunctionWords.cpp
./src/functions/FunctionMonthFromDateTime.cpp
./src/functions/FunctionLocalNameFromQName.cpp
./src/functions/FunctionDistinctValues.cpp
./src/functions/FunctionMonthsFromDuration.cpp
./src/functions/FunctionCos.cpp
./src/functions/FunctionTrace.cpp
./src/functions/FunctionTimezoneFromDate.cpp
./src/functions/FunctionCodepointsToString.cpp
./src/functions/FunctionMinutesFromDateTime.cpp
./src/functions/FunctionYearFromDate.cpp
./src/functions/FunctionStaticBaseURI.cpp
./src/functions/BuiltInModules.cpp
./src/functions/FunctionAtan.cpp
./src/functions/FunctionCurrentDateTime.cpp
./src/functions/FunctionAdjustTimeToTimezone.cpp
./src/functions/FunctionCeiling.cpp
./src/functions/FunctionExplain.cpp
./src/functions/FunctionImplicitTimezone.cpp
./src/functions/FunctionStartsWith.cpp
./src/functions/FunctionLowerCase.cpp
./src/functions/FunctionString.cpp
./src/functions/FunctionSentences.cpp
./src/functions/FunctionYearsFromDuration.cpp
./src/functions/FunctionNamespaceURIFromQName.cpp
./src/functions/XQillaFunction.cpp
./src/functions/FunctionParseHTML.cpp
./src/functions/FunctionDayFromDate.cpp
./src/functions/FunctionId.cpp
./src/functions/FunctionEmpty.cpp
./src/functions/FunctionUnparsedText.cpp
./src/functions/FunctionAdjustDateTimeToTimezone.cpp
./src/functions/FunctionTimezoneFromTime.cpp
./src/functions/FunctionRoot.cpp
./src/functions/FunctionCount.cpp
./src/functions/FunctionPosition.cpp
./src/functions/FunctionCurrentDate.cpp
./src/functions/FunctionFunctionArity.cpp
./src/functions/EXSLTMathFunction.cpp
./src/functions/FunctionRegexGroup.cpp
./src/functions/FunctionError.cpp
./src/functions/FunctionSerializeJSON.cpp
./src/functions/FunctionTimezoneFromDateTime.cpp
./src/functions/FunctionHead.cpp
./src/functions/FunctionStringLength.cpp
./src/functions/FunctionFloor.cpp
./src/functions/FunctionResolveURI.cpp
./src/functions/FunctionLog.cpp
./src/functions/FunctionContains.cpp
./src/functions/FunctionMatches.cpp
./src/functions/FunctionMonthFromDate.cpp
./src/functions/FunctionSecondsFromTime.cpp
./src/functions/FunctionSubstring.cpp
./src/functions/FunctionDateTime.cpp
./src/functions/FunctionCollection.cpp
./src/functions/FunctionConcat.cpp
./src/functions/FunctionParseXML.cpp
./src/functions/FunctionAnalyzeString.cpp
./src/functions/FunctionTime.cpp
./src/functions/FunctionDocument.cpp
./src/functions/FunctionCompare.cpp
./src/functions/FunctionTokenize.cpp
./src/functions/FunctionSecondsFromDuration.cpp
./src/functions/FunctionSignature.cpp
./src/functions/FunctionHoursFromDateTime.cpp
./src/functions/FunctionDayFromDateTime.cpp
./src/functions/FunctionAsin.cpp
./src/functions/FunctionUpperCase.cpp
./src/functions/FunctionMinutesFromDuration.cpp
./src/functions/RegExpFunction.cpp
./src/functions/FunctionCurrentTime.cpp
./src/functions/FunctionYearFromDateTime.cpp
./src/functions/FunctionLast.cpp
./src/functions/FunctionLookup.cpp
./src/functions/FunctionDaysFromDuration.cpp
./src/functions/XQUserFunction.cpp
./src/functions/FunctionHoursFromTime.cpp
./src/functions/FunctionNilled.cpp
./src/functions/FunctionLang.cpp
./src/functions/FunctionDoc.cpp
./src/functions/FunctionParseJSON.cpp
./src/functions/FunctionFunctionName.cpp
./src/functions/FunctionEndsWith.cpp
./src/functions/FunctionDocAvailable.cpp
./src/functions/FunctionDefaultCollation.cpp
./src/functions/FunctionNormalizeUnicode.cpp
./src/functions/FunctionAbs.cpp
./src/functions/FunctionStringToCodepoints.cpp
./src/functions/FunctionPower.cpp
#./src/functions/FunctionSubstringBeforeAfter.cpp
./src/functions/FunctionUnordered.cpp
./src/functions/FunctionNodeName.cpp
./src/functions/FunctionSin.cpp
./src/functions/FunctionPrefixFromQName.cpp
./src/functions/FunctionAdjustDateToTimezone.cpp
./src/functions/FunctionQName.cpp
./src/functions/FunctionRoundHalfToEven.cpp
./src/functions/FunctionNot.cpp
./src/functions/FunctionReplace.cpp
./src/functions/FunctionBaseURI.cpp
./src/functions/FunctionExp.cpp
./src/functions/ExternalFunction.cpp
./src/functions/FunctionLocalname.cpp
./src/functions/FuncFactory.cpp
./src/functions/FunctionNamespaceUri.cpp
./src/functions/FunctionSecondsFromDateTime.cpp
./src/functions/FunctionHoursFromDuration.cpp
./src/functions/FunctionDocumentURI.cpp
./src/functions/FunctionNumber.cpp
./src/functions/FunctionRound.cpp
./src/functions/FunctionAcos.cpp
./src/functions/FunctionIdref.cpp
./src/functions/FunctionName.cpp
./src/functions/FunctionCaseFold.cpp
./src/functions/FunctionSqrt.cpp
./src/functions/FunctionTail.cpp
./src/functions/FunctionTan.cpp
./src/functions/FunctionNormalizeSpace.cpp
./src/debug/DebugHookDecorator.cpp
./src/debug/InputParser.cpp
./src/debug/TupleDebugHook.cpp
./src/debug/InteractiveDebugger.cpp
./src/debug/StackFrame.cpp
./src/debug/ASTDebugHook.cpp
./src/dom-api/impl/XQillaXMLGrammarPoolImpl.cpp
./src/dom-api/impl/XQillaExpressionImpl.cpp
./src/dom-api/impl/XPath2ResultImpl.cpp
./src/dom-api/impl/XPathNamespaceImpl.cpp
./src/dom-api/impl/XQillaDocumentImpl.cpp
./src/dom-api/impl/XQillaBuilderImpl.cpp
./src/dom-api/impl/XPathDocumentImpl.cpp
./src/dom-api/impl/XQillaNSResolverImpl.cpp
./src/dom-api/XQillaImplementation.cpp
./src/dom-api/XPath2NodeSerializer.cpp
./src/runtime/EmptyResult.cpp
./src/runtime/ResultBufferImpl.cpp
./src/runtime/ClosureResult.cpp
./src/runtime/Result.cpp
./src/runtime/SequenceResult.cpp
./src/runtime/ResultImpl.cpp
./src/runtime/Sequence.cpp
./src/runtime/ResultBuffer.cpp
./src/events/NSFixupFilter.cpp
./src/events/EventGenerator.cpp
./src/events/NoInheritFilter.cpp
./src/events/EventSerializer.cpp
./src/events/QueryPathTreeFilter.cpp
./src/events/ContentSequenceFilter.cpp
./src/context/impl/CollationImpl.cpp
./src/context/impl/ItemFactoryImpl.cpp
./src/context/impl/XQContextImpl.cpp
./src/context/impl/XQDynamicContextImpl.cpp
./src/context/impl/VarTypeStoreImpl.cpp
./src/context/impl/VarStoreImpl.cpp
./src/context/impl/VarHashEntryImpl.cpp
./src/context/impl/CodepointCollation.cpp
./src/context/UpdateFactory.cpp
./src/tools/compile-delayed-module.cpp
./src/update/UInsertAfter.cpp
./src/update/UTransform.cpp
./src/update/UInsertAsFirst.cpp
./src/update/UInsertBefore.cpp
./src/update/UInsertInto.cpp
./src/update/UReplace.cpp
./src/update/UDelete.cpp
./src/update/UInsertAsLast.cpp
./src/update/UReplaceValueOf.cpp
./src/update/URename.cpp
./src/update/PendingUpdateList.cpp
./src/update/FunctionPut.cpp
./src/update/UApplyUpdates.cpp
./src/operators/UnaryMinus.cpp
./src/operators/GeneralComp.cpp
./src/operators/Plus.cpp
./src/operators/ComparisonOperator.cpp
./src/operators/And.cpp
./src/operators/OrderComparison.cpp
./src/operators/LessThanEqual.cpp
./src/operators/Minus.cpp
./src/operators/LessThan.cpp
./src/operators/GreaterThan.cpp
./src/operators/Equals.cpp
./src/operators/NodeComparison.cpp
./src/operators/Union.cpp
./src/operators/Or.cpp
./src/operators/NotEquals.cpp
./src/operators/IntegerDivide.cpp
./src/operators/Except.cpp
./src/operators/Divide.cpp
./src/operators/GreaterThanEqual.cpp
./src/operators/Multiply.cpp
./src/operators/ArithmeticOperator.cpp
./src/operators/Mod.cpp
./src/operators/Intersect.cpp
./src/yajl/yajl_encode.c
./src/yajl/yajl.c
./src/yajl/yajl_parser.c
./src/yajl/yajl_buf.c
./src/yajl/yajl_gen.c
./src/yajl/yajl_lex.c
./src/utils/utf8proc/utf8proc.c
./src/mapm/mapm_log.c
./src/mapm/mapm_div.c
./src/mapm/mapmfmul.c
./src/mapm/mapm_add.c
./src/mapm/mapmhsin.c
./src/mapm/mapm_fft.c
./src/mapm/mapmipwr.c
./src/mapm/mapmasin.c
./src/mapm/mapm_rnd.c
./src/mapm/mapmutl2.c
./src/mapm/mapm_fam.c
./src/mapm/mapm_fpf.c
./src/mapm/mapmutil.c
./src/mapm/mapm5sin.c
./src/mapm/mapmstck.c
./src/mapm/mapm_exp.c
./src/mapm/mapm_mul.c
./src/mapm/mapmfact.c
./src/mapm/mapmgues.c
./src/mapm/mapm_flr.c
./src/mapm/mapmcbrt.c
./src/mapm/mapm_lg3.c
./src/mapm/mapm_rcp.c
./src/mapm/mapm_sin.c
./src/mapm/mapmistr.c
./src/mapm/mapmhasn.c
./src/mapm/mapmasn0.c
./src/mapm/mapmrsin.c
./src/mapm/mapm_set.c
./src/mapm/mapmsqrt.c
./src/mapm/mapm_gcd.c
./src/mapm/mapm_lg2.c
./src/mapm/mapmcnst.c
./src/mapm/mapm_pow.c
./src/mapm/mapmpwr2.c
./src/mapm/mapm_cpi.c
)

include_directories(include)
find_package(XercesC REQUIRED)

target_include_directories(xqilla PRIVATE "${XercesC_INCLUDE_DIRS}")
target_include_directories(xqilla PRIVATE "${CMAKE_CURRENT_SOURCE_DIR}/include")

if (BUILD_SHARED_LIBS)
  if(XercesC_LIBRARY_DEBUG)
    target_link_libraries(xqilla PUBLIC debug "${XercesC_LIBRARY_DEBUG}")
  endif()
  target_link_libraries(xqilla PUBLIC optimized "${XercesC_LIBRARY_RELEASE}")
endif()

install(
    TARGETS xqilla
    EXPORT  unofficial-xqilla
    RUNTIME DESTINATION bin
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
)

install(
    DIRECTORY "${CMAKE_CURRENT_SOURCE_DIR}/include/xqilla"
    CONFIGURATIONS Release
    DESTINATION include
)

install(
    EXPORT unofficial-xqilla
    DESTINATION share/unofficial-xqilla
    FILE unofficial-xqillaConfig.cmake
    NAMESPACE unofficial::xqilla::
)
