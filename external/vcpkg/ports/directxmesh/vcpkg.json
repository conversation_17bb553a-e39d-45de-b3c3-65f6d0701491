{"name": "directxmesh", "version-date": "2025-03-24", "description": "DirectXMesh geometry processing library", "homepage": "https://github.com/Microsoft/DirectXMesh", "documentation": "https://github.com/microsoft/DirectXMesh/wiki", "license": "MIT", "supports": "(windows & !arm32) | linux", "dependencies": [{"name": "directx-headers", "platform": "mingw | linux"}, "directxmath", {"name": "ms-gdkx", "platform": "xbox"}, {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"dx12": {"description": "Build with DirectX12 support for Windows 10/Windows 11", "dependencies": [{"name": "directx-headers", "platform": "windows & !xbox"}]}, "spectre": {"description": "Build Spectre-mitigated library"}, "tools": {"description": "meshconvert command-line tool", "supports": "windows & !uwp & !xbox"}}}