diff --git a/src/bin/CMakeLists.txt b/src/bin/CMakeLists.txt
index 18048e3..4fcd161 100644
--- a/src/bin/CMakeLists.txt
+++ b/src/bin/CMakeLists.txt
@@ -11,6 +11,7 @@ endif()
 
 if(BUILD_SHARED_LIBS)
     add_library(msquic SHARED ${SOURCES})
+    target_include_directories(msquic PUBLIC $<INSTALL_INTERFACE:include>)
     target_link_libraries(msquic PRIVATE core msquic_platform inc warnings logging base_link main_binary_link_args)
     set_target_properties(msquic PROPERTIES OUTPUT_NAME ${QUIC_LIBRARY_NAME})
     if (NOT WIN32)
@@ -261,7 +262,10 @@ if(WIN32)
 endif()
 
 if(BUILD_SHARED_LIBS)
-  install(TARGETS msquic msquic_platform inc logging_inc warnings main_binary_link_args ${OTHER_TARGETS} EXPORT msquic DESTINATION lib)
+  install(TARGETS msquic EXPORT msquic
+    RUNTIME DESTINATION bin
+    ARCHIVE DESTINATION lib
+    LIBRARY DESTINATION lib)
 else()
   install(FILES ${QUIC_STATIC_LIBRARY} DESTINATION lib)
 endif()
