diff --git a/CMakeLists.txt b/CMakeLists.txt
index 3f8f4d58f..df689627c 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -598,9 +598,9 @@ if(WIN32)
     endif()
 
     if (NOT QUIC_STATIC_LINK_CRT AND NOT QUIC_STATIC_LINK_PARTIAL_CRT)
-        # We are using dynamic linking. Ensure that only the release version of CRT is used.
-        message(STATUS "Configuring for release version of dynamically linked CRT")
-        set(CMAKE_MSVC_RUNTIME_LIBRARY "MultiThreadedDLL")
+        # We are using dynamic linking. Ensure that only the dynamic CRT is used.
+        message(STATUS "Configuring for dynamically linked CRT")
+        set(CMAKE_MSVC_RUNTIME_LIBRARY "MultiThreaded$<$<CONFIG:Debug>:Debug>DLL")
     endif()
 
 else() #!WIN32
