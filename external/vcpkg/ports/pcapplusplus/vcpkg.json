{"name": "pcapplusplus", "version": "24.9", "description": "PcapPlusPlus is a multi-platform C++ library for capturing, parsing and crafting of network packets", "homepage": "https://github.com/seladb/PcapPlusPlus", "documentation": "https://pcapplusplus.github.io", "license": null, "dependencies": [{"name": "libp<PERSON>", "platform": "!windows"}, {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}, {"name": "winpcap", "platform": "windows"}]}