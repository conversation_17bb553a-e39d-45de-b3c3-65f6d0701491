diff --git a/CMakeLists.txt b/CMakeLists.txt
index 0f24d43..858424c 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -219,6 +219,7 @@ if(PCAPPP_TARGET_COMPILER_MSVC)
   # Disable VS warnings: Unknown pragma (4068), Zero-sized array in struct/union (4200), Possible loss of data (4244),
   # Possible loss of data (4267), Character may not be represented (4819)
   add_definitions("/wd4068 /wd4200 /wd4244 /wd4267 /wd4819")
+  add_compile_definitions(_SILENCE_STDEXT_ARR_ITERS_DEPRECATION_WARNING)
 endif()
 
 if(PCAPPP_BUILD_COVERAGE)
