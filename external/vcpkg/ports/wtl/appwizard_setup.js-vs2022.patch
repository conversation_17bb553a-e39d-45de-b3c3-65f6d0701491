--- a/AppWizard/Setup.js
+++ b/AppWizard/Setup.js
@@ -185,26 +185,30 @@
 		}
 	}
 
-// Search for Visual Studio 2017-2019
-	var nVersionsNew = 2;
+// Search for Visual Studio 2017/2019/2022
+	var nVersionsNew = 3;
 
 	var strWizardFolderNew = "Common7\\IDE\\VC\\vcprojects";
 
 	var astrVersionsNew = new Array();
 	astrVersionsNew[0] = "Visual Studio 2017 (15.0)";
 	astrVersionsNew[1] = "Visual Studio 2019 (16.0)";
+	astrVersionsNew[2] = "Visual Studio 2022 (17.0)";
 
 	var astrWizVerNew = new Array();
 	astrWizVerNew[0] = "15.0";
 	astrWizVerNew[1] = "16.0";
+	astrWizVerNew[2] = "17.0";
 
 	var astrParamVerNew = new Array();
 	astrParamVerNew[0] = "15";
 	astrParamVerNew[1] = "16";
+	astrParamVerNew[2] = "17";
 
 	var astrFilterVer = new Array();
 	astrFilterVer[0] = "[15.0,16.0]";
 	astrFilterVer[1] = "[16.0,17.0]";
+	astrFilterVer[2] = "[17.0,18.0]";
 
 	// Ensure that we can run vswhere.exe
 	var strProgFileRegKey_x86 = "HKLM\\Software\\Microsoft\\Windows\\CurrentVersion\\ProgramFilesDir (x86)";
