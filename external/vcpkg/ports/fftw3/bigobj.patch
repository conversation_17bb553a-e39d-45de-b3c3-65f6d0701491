diff --git a/CMakeLists.txt b/CMakeLists.txt
index 64db20b6a..ce438a379 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -315,7 +315,7 @@ endif ()
 add_library (${fftw3_lib} ${SOURCEFILES})
 target_include_directories (${fftw3_lib} INTERFACE $<INSTALL_INTERFACE:include>)
 if (MSVC AND NOT (CMAKE_C_COMPILER_ID STREQUAL "Intel"))
-  target_compile_definitions (${fftw3_lib} PRIVATE /bigobj)
+  target_compile_options (${fftw3_lib} PRIVATE "/bigobj")
 endif ()
 if (HAVE_SSE)
   target_compile_options (${fftw3_lib} PRIVATE ${SSE_FLAG})
