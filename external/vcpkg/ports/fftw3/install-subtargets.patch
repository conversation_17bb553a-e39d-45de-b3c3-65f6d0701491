diff --git a/CMakeLists.txt b/CMakeLists.txt
index d1e4dff..ea5d579 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -361,12 +361,8 @@ endif ()
 
 foreach(subtarget ${subtargets})
   set_target_properties (${subtarget} PROPERTIES SOVERSION 3.6.9 VERSION 3)
-  install (TARGETS ${subtarget}
-	  RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
-	  LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
-          ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR})
 endforeach ()
-install(TARGETS ${fftw3_lib}
+install(TARGETS ${subtargets}
           EXPORT FFTW3LibraryDepends
           RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
           LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
