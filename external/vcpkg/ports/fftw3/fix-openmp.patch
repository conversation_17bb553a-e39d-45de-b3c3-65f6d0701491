diff --git a/CMakeLists.txt b/CMakeLists.txt
index ce438a3..43c74be 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -356,7 +356,7 @@ if (OPENMP_FOUND)
   target_link_libraries (${fftw3_lib}_omp ${fftw3_lib})
   target_link_libraries (${fftw3_lib}_omp ${CMAKE_THREAD_LIBS_INIT})
   list (APPEND subtargets ${fftw3_lib}_omp)
-  target_compile_options (${fftw3_lib}_omp PRIVATE ${OpenMP_C_FLAGS})
+  target_link_libraries (${fftw3_lib}_omp OpenMP::OpenMP_C)
 endif ()
 
 foreach(subtarget ${subtargets})
