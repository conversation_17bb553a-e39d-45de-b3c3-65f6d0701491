vcpkg_from_github(
    OUT_SOURCE_PATH SOURCE_PATH
    REPO jl2922/hps
    REF 8d1403697a2fad6ddf02c7afb190596ca68b2105
    SHA512 e0c22de8a684891a5b6faa968c72782ffb44c5359ce53a4cbd74abf5e1b6d5d1ff30ce96a4fc4c38fc7a0222d6874eab47b76c5a87fce1c43285a915d0f55814
    HEAD_REF master
)

# Install header files
file(INSTALL 
    DIRECTORY 
        "${SOURCE_PATH}/src/"
    DESTINATION 
        "${CURRENT_PACKAGES_DIR}/include/${PORT}"
    FILES_MATCHING PATTERN "*.h" )

file(REMOVE_RECURSE "${CURRENT_PACKAGES_DIR}/include/${PORT}/benchmark")

file(INSTALL "${CMAKE_CURRENT_LIST_DIR}/usage" DESTINATION "${CURRENT_PACKAGES_DIR}/share/${PORT}")
file(INSTALL "${SOURCE_PATH}/LICENSE" DESTINATION "${CURRENT_PACKAGES_DIR}/share/${PORT}" RENAME copyright)
