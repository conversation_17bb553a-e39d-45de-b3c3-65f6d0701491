diff --git a/src/ausearch-lookup.c b/src/ausearch-lookup.c
index 2d6f48c..8c610e9 100644
--- a/src/ausearch-lookup.c
+++ b/src/ausearch-lookup.c
@@ -242,7 +242,7 @@ const char *aulookup_uid(uid_t uid, char *buf, size_t size)
 	return buf;
 }
 
-void aulookup_destroy_uid_list(void)
+void aulookup_destroy_uid_nvl_list(void)
 {
 	if (uid_list_created == 0)
 		return;
diff --git a/src/ausearch-lookup.h b/src/ausearch-lookup.h
index c80f782..67f1e56 100644
--- a/src/ausearch-lookup.h
+++ b/src/ausearch-lookup.h
@@ -38,7 +38,7 @@ const char *aulookup_syscall(llist *l, char *buf, size_t size)
 	__attr_access ((__write_only__, 2, 3));
 const char *aulookup_uid(uid_t uid, char *buf, size_t size)
 	__attr_access ((__write_only__, 2, 3));
-void aulookup_destroy_uid_list(void);
+void aulookup_destroy_uid_nvl_list(void);
 char *unescape(const char *buf);
 void print_tty_data(const char *val);
 void safe_print_string_n(const char *s, unsigned int len, int ret)
