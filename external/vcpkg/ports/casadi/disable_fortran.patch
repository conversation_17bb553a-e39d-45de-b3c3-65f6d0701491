diff --git a/CMakeLists.txt b/CMakeLists.txt
index 0200a56df..69b699626 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -84,23 +84,7 @@ if(NOT CMAKE_BUILD_TYPE AND NOT CMAKE_CONFIGURATION_TYPES)
   set(CMAKE_BUILD_TYPE "Release" CACHE STRING "Build type (default Release)" FORCE)
 endif()
 
-########################################################################
-############################# Fortran ##################################
-########################################################################
-option(FORTRAN_REQUIRED "Make fortran a required dependency" OFF)
-
-# http://public.kitware.com/Bug/print_bug_page.php?bug_id=9220
 set(CMAKE_MODULE_PATH ${PROJECT_SOURCE_DIR}/cmake)
-include(language_support)
-workaround_9220(Fortran Fortran_language_works)
-if(FORTRAN_REQUIRED)
-  enable_language(Fortran)
-  set(Fortran_language_works ON)
-else()
-  if(Fortran_language_works)
-    enable_language(Fortran OPTIONAL)
-  endif()
-endif()
 
 ########################################################################
 ############################# C++11 ####################################
