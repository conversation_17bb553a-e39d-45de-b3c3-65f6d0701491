{"name": "sqlpp11-connector-sqlite3", "version": "0.61", "description": "A C++ wrapper for sqlite3 meant to be used in combination with sqlpp11 (obsolete, use sqlpp11 with the sqlite3 feature instead)", "homepage": "https://github.com/rbock/sqlpp11-connector-sqlite3", "license": "BSD-2-<PERSON><PERSON>", "dependencies": [{"name": "sqlpp11", "default-features": false, "features": ["sqlite3"], "version>=": "0.61"}]}