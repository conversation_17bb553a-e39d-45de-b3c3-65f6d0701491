diff --git a/utils/CMakeLists.txt b/utils/CMakeLists.txt
index 0e16a8d..e2af57e 100644
--- a/utils/CMakeLists.txt
+++ b/utils/CMakeLists.txt
@@ -1,5 +1,5 @@
 install(PROGRAMS "oscap-run-sce-script"
-	DESTINATION ${CMAKE_INSTALL_BINDIR}
+	DESTINATION tools/openscap
 )
 
 if(ENABLE_OSCAP_UTIL)
@@ -24,7 +24,7 @@ if(ENABLE_OSCAP_UTIL)
 			target_link_libraries(oscap ${GETOPT_LIBRARY})
 		endif()
 		
-		set(OSCAP_UTIL_DESTINATION ".")
+		set(OSCAP_UTIL_DESTINATION bin)
 		# Install the 'oscap' utility
 		install(TARGETS "oscap"
 			DESTINATION ${OSCAP_UTIL_DESTINATION}
@@ -110,7 +110,7 @@ if(ENABLE_OSCAP_UTIL_SSH)
 endif()
 if(ENABLE_OSCAP_UTIL_AUTOTAILOR)
 	install(PROGRAMS "autotailor"
-		DESTINATION ${CMAKE_INSTALL_BINDIR}
+		DESTINATION tools/openscap
 	)
 	install(FILES "autotailor.8"
 		DESTINATION "${CMAKE_INSTALL_MANDIR}/man8"
