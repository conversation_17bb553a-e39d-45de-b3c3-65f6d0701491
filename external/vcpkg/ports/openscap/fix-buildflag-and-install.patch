diff --git a/CMakeLists.txt b/CMakeLists.txt
index dff0a45..494512e 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -147,9 +147,7 @@ if (APPLE)
 endif()
 find_package(OpenSSL REQUIRED)
 add_definitions(${XMLSEC_DEFINITIONS})
-if (WIN32)
-       add_compile_definitions("XMLSEC_CRYPTO_OPENSSL")
-endif()
+add_compile_definitions("XMLSEC_CRYPTO_OPENSSL")
 find_package(BZip2)

 # PThread
@@ -614,11 +612,6 @@ if(NOT WIN32)
        if(WITH_SYSTEMD)
                if(ENABLE_OSCAP_REMEDIATE_SERVICE)
                        # systemd service for offline (boot-time) remediation
-                       configure_file("oscap-remediate.service.in" "oscap-remediate.service" @ONLY)
-                       install(FILES
-                               ${CMAKE_CURRENT_BINARY_DIR}/oscap-remediate.service
-                               DESTINATION ${CMAKE_INSTALL_PREFIX}/${SYSTEMD_UNITDIR}
-                       )
                endif()
        endif()
 endif()
