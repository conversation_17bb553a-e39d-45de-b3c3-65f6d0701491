{"name": "openscap", "version": "1.4.0", "port-version": 1, "description": "The oscap program is a command line tool that allows users to load, scan, validate, edit, and export SCAP documents.", "homepage": "https://github.com/OpenSCAP/openscap", "license": "LGPL-2.1-or-later", "supports": "!xbox", "dependencies": [{"name": "acl", "platform": "linux"}, "curl", "glib", "lib<PERSON>t", "libxml2", "libxslt", "libzip", "openssl", "pcre", "pthreads", {"name": "vcpkg-cmake", "host": true}, "xmlsec", "zlib"], "features": {"python": {"description": "build with python3"}, "util": {"description": "build available utils", "dependencies": [{"name": "getopt-win32", "platform": "windows"}]}}}