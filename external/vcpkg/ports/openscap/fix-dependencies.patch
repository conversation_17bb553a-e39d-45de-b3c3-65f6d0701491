diff --git a/CMakeLists.txt b/CMakeLists.txt
index 494512e..22ab043 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -84,8 +84,12 @@ include(CMakeDependentOption)
 find_package(ACL)
 if(ACL_FOUND)
        check_library_exists("${ACL_LIBRARY}" acl_extended_file "" HAVE_ACL_EXTENDED_FILE)
-       check_include_file(acl/libacl.h HAVE_ACL_LIBACL_H)
-       check_include_file(sys/acl.h HAVE_SYS_ACL_H)
+       if(EXISTS "${ACL_INCLUDE_DIR}/acl/libacl.h")
+                set(HAVE_ACL_LIBACL_H 1)
+        endif()
+       if(EXISTS "${ACL_INCLUDE_DIR}/sys/acl.h")
+                set(HAVE_SYS_ACL_H 1)
+        endif()
 endif()

 find_package(Blkid)
diff --git a/cmake/FindACL.cmake b/cmake/FindACL.cmake
index a41f2c1..9675498 100644
--- a/cmake/FindACL.cmake
+++ b/cmake/FindACL.cmake
@@ -12,7 +12,7 @@ libfind_pkg_check_modules(ACL_PKGCONF libacl)

 # Include dir
 find_path(ACL_INCLUDE_DIR
-       NAMES "acl/libacl.h" "sys/libacl.h"
+       NAMES "acl/libacl.h" "sys/acl.h"
        PATHS ${ACL_PKGCONF_INCLUDE_DIRS}
 )

