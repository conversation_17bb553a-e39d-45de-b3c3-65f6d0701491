{"name": "blend2d", "version-date": "2025-03-08", "description": "2D Vector Graphics Engine Powered by a JIT Compiler", "homepage": "https://github.com/blend2d/blend2d", "documentation": "https://blend2d.com/doc/index.html", "license": "<PERSON><PERSON><PERSON>", "supports": "!wasm32", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "default-features": [{"name": "jit", "platform": "!arm32"}], "features": {"jit": {"description": "Enables JIT compiler to generate optimized pipelines.", "supports": "!arm32", "dependencies": ["<PERSON><PERSON><PERSON>"]}}}