{"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "0.8.1", "port-version": 2, "maintainers": "@takev", "description": "A portable, low latency, retained-mode GUI framework written in C++.", "homepage": "https://github.com/hikogui/hikogui", "license": "BSL-1.0", "supports": "windows & x64", "dependencies": [{"name": "glslang", "features": ["tools"]}, "shaderc", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}, "vulkan", "vulkan-memory-allocator"]}