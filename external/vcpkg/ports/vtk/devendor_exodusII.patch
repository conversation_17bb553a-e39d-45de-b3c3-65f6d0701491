diff --git a/ThirdParty/exodusII/CMakeLists.txt b/ThirdParty/exodusII/CMakeLists.txt
index 7a0cefb..3139114 100644
--- a/ThirdParty/exodusII/CMakeLists.txt
+++ b/ThirdParty/exodusII/CMakeLists.txt
@@ -1,20 +1,27 @@
-vtk_module_third_party_internal(
-  LICENSE_FILES
-    "vtkexodusII/COPYRIGHT"
-  SPDX_LICENSE_IDENTIFIER
-    "BSD-3-Clause"
-  SPDX_COPYRIGHT_TEXT
-    "Copyright (c) 2005-2017 National Technology & Engineering Solutions of Sandia, LLC (NTESS)"
-  SPDX_DOWNLOAD_LOCATION
-    "git+https://gitlab.kitware.com/third-party/seacas.git@exodus/for/vtk-20220706-v2021-05-12#packages/seacas/libraries/exodus"
-  VERSION
-    "8.11f"
-  STANDARD_INCLUDE_DIRS)
+vtk_module_third_party(
+  INTERNAL
+    LICENSE_FILES
+      "vtkexodusII/COPYRIGHT"
+    SPDX_LICENSE_IDENTIFIER
+      "BSD-3-Clause"
+    SPDX_COPYRIGHT_TEXT
+      "Copyright (c) 2005-2017 National Technology & Engineering Solutions of Sandia, LLC (NTESS)"
+    SPDX_DOWNLOAD_LOCATION
+      "git+https://gitlab.kitware.com/third-party/seacas.git@exodus/for/vtk-20220706-v2021-05-12#packages/seacas/libraries/exodus"
+    VERSION
+      "8.11f"
+    STANDARD_INCLUDE_DIRS
+  EXTERNAL
+    PACKAGE SEACASExodus
+    CONFIG_MODE
+    TARGETS SEACASExodus::exodus
+    STANDARD_INCLUDE_DIRS
+)
 
-#configure_file(
-#  "${CMAKE_CURRENT_SOURCE_DIR}/vtk_exodusII.h.in"
-#  "${CMAKE_CURRENT_BINARY_DIR}/vtk_exodusII.h")
+configure_file(
+  "${CMAKE_CURRENT_SOURCE_DIR}/vtk_exodusII.h.in"
+  "${CMAKE_CURRENT_BINARY_DIR}/vtk_exodusII.h")
 
 vtk_module_install_headers(
-#  FILES "${CMAKE_CURRENT_BINARY_DIR}/vtk_exodusII.h")
-  FILES "${CMAKE_CURRENT_SOURCE_DIR}/vtk_exodusII.h")
+  FILES "${CMAKE_CURRENT_BINARY_DIR}/vtk_exodusII.h")
+#  FILES "${CMAKE_CURRENT_SOURCE_DIR}/vtk_exodusII.h")
diff --git a/ThirdParty/exodusII/vtk_exodusII.h.in b/ThirdParty/exodusII/vtk_exodusII.h.in
new file mode 100644
index 0000000..ac2ab78
--- /dev/null
+++ b/ThirdParty/exodusII/vtk_exodusII.h.in
@@ -0,0 +1,15 @@
+// SPDX-FileCopyrightText: Copyright (c) Ken Martin, Will Schroeder, Bill Lorensen
+// SPDX-License-Identifier: BSD-3-Clause
+#ifndef vtk_exodusII_h
+#define vtk_exodusII_h
+
+/* Use the libproj library configured for VTK.  */
+#cmakedefine01 VTK_MODULE_USE_EXTERNAL_vtkexodusII
+
+#if VTK_MODULE_USE_EXTERNAL_vtkexodusII
+# include <exodusII.h>
+#else
+# include <vtkexodusII/include/exodusII.h>
+#endif
+
+#endif
