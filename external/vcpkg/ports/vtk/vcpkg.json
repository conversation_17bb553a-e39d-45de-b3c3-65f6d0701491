{"name": "vtk", "version-semver": "9.3.0-pv5.12.1", "port-version": 7, "description": "Software system for 3D computer graphics, image processing, and visualization", "homepage": "https://github.com/Kitware/VTK", "license": null, "dependencies": ["double-conversion", "eigen3", "expat", "exprtk", "fast-float", "fmt", {"name": "freetype", "default-features": false, "features": ["zlib"]}, "glew", {"name": "hdf5", "default-features": false, "features": ["zlib"]}, "jsoncpp", "libjpeg-turbo", "liblzma", "libpng", {"name": "libxml2", "default-features": false, "features": ["zlib"]}, "lz4", "<PERSON><PERSON><PERSON>-<PERSON><PERSON>", "pegtl", "pugixml", {"name": "tiff", "default-features": false}, "utfcpp", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}, "verdict", "zlib"], "default-features": ["cgns", "lib<PERSON><PERSON>", "libtheora", "netcdf", "proj", "seacas", "sql"], "features": {"all": {"description": "Build all vtk modules", "dependencies": ["ffmpeg", "libmysql", {"name": "vtk", "default-features": false, "features": ["cgns", "gdal", "g<PERSON><PERSON><PERSON>", "lib<PERSON><PERSON>", "libtheora", "mpi", "netcdf", "proj", "python", "qt", "seacas", "sql"]}, {"name": "vtk", "default-features": false, "features": ["atlmfc", "utf8"], "platform": "windows"}]}, "atlmfc": {"description": "Mfc functionality for vtk on Windows", "dependencies": [{"name": "atlmfc", "platform": "windows"}]}, "cgns": {"description": "CGNS functionality for VTK", "dependencies": [{"name": "cgns", "default-features": false}]}, "cuda": {"description": "Support CUDA compilation", "dependencies": ["cuda"]}, "gdal": {"description": "Support GDAL compilation", "dependencies": [{"name": "gdal", "default-features": false}]}, "geojson": {"description": "Convert Geo JSON format to vtkPolyData"}, "ioocct": {"description": "Build with IOOCCT module", "dependencies": [{"name": "opencascade", "default-features": false}]}, "libharu": {"description": "PDF functionality for VTK", "dependencies": [{"name": "lib<PERSON><PERSON>", "version>=": "2.4.3"}]}, "libtheora": {"description": "Compressed ogg functionality for VTK", "dependencies": ["libtheora"]}, "mpi": {"description": "MPI functionality for VTK", "dependencies": [{"name": "hdf5", "default-features": false, "features": ["parallel"]}, "mpi", {"name": "seacas", "default-features": false, "features": ["mpi"]}, {"name": "vtk", "default-features": false, "features": ["seacas", "vtkm"]}, {"name": "vtk-m", "default-features": false, "features": ["mpi"]}]}, "netcdf": {"description": "NetCDF functionality for VTK", "dependencies": [{"name": "netcdf-c", "default-features": false, "features": ["hdf5"]}]}, "opengl": {"description": "All opengl related modules", "dependencies": [{"name": "vtk", "default-features": false}]}, "openmp": {"description": "Use openmp multithreading parallel implementation"}, "openvr": {"description": "OpenVR functionality for VTK", "dependencies": ["openvr", "sdl2"]}, "paraview": {"description": "Build vtk modules required by paraview", "dependencies": ["cli11", "openturns", {"name": "vtk", "default-features": false, "features": ["qt"]}, {"name": "vtk", "default-features": false, "features": ["atlmfc", "libtheora", "seacas"], "platform": "windows"}]}, "proj": {"description": "Geographic projection functionality for VTK", "dependencies": [{"name": "proj", "default-features": false}, {"name": "vtk", "default-features": false, "features": ["sql"]}]}, "python": {"description": "Python functionality for VTK", "dependencies": ["python3"]}, "qt": {"description": "Qt functionality for VTK", "dependencies": [{"name": "qtbase", "default-features": false, "features": ["gui", "opengl", "sql-sqlite", "widgets"]}, "qtdeclarative", {"name": "vtk", "default-features": false, "features": ["sql"]}]}, "seacas": {"description": "Exodus and IOSS functionality for VTK", "dependencies": [{"name": "seacas", "default-features": false}, {"name": "vtk", "default-features": false, "features": ["cgns", "netcdf"]}]}, "sql": {"description": "SQL functionality for VTK", "dependencies": ["sqlite3"]}, "tbb": {"description": "Use TBB multithreading parallel implementation", "dependencies": ["tbb"]}, "utf8": {"description": "Enables vtk reader/writer with utf-8 path support", "dependencies": [{"name": "vtk", "default-features": false}]}, "vtkm": {"description": "Build with vtk-m accelerator and module.", "dependencies": [{"name": "vtk-m", "default-features": false}]}}}