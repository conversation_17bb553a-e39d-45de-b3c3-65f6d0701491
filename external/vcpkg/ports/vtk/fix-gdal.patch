diff --git a/CMake/vtkInstallCMakePackage.cmake b/CMake/vtkInstallCMakePackage.cmake
index bcb2044..c87bb9c 100644
--- a/CMake/vtkInstallCMakePackage.cmake
+++ b/CMake/vtkInstallCMakePackage.cmake
@@ -113,7 +113,6 @@ set(vtk_cmake_patch_files
   patches/3.18/FindPython/Support.cmake
   patches/3.18/FindPython2.cmake
   patches/3.18/FindPython3.cmake
-  patches/99/FindGDAL.cmake
   patches/99/FindHDF5.cmake
   patches/99/FindJPEG.cmake
   patches/99/FindLibArchive.cmake
diff --git a/Geovis/GDAL/CMakeLists.txt b/Geovis/GDAL/CMakeLists.txt
index dfd58f0..f46177f 100644
--- a/Geovis/GDAL/CMakeLists.txt
+++ b/Geovis/GDAL/CMakeLists.txt
@@ -9,4 +9,7 @@ vtk_module_add_module(VTK::GeovisGDAL
   CLASSES ${classes})
 vtk_module_link(VTK::GeovisGDAL
   PRIVATE
-    GDAL::GDAL)
+    ${GDAL_LIBRARIES})
+vtk_module_include(VTK::GeovisGDAL
+  PRIVATE
+    ${GDAL_INCLUDE_DIRS})
diff --git a/IO/GDAL/CMakeLists.txt b/IO/GDAL/CMakeLists.txt
index 0a1248a..621a060 100644
--- a/IO/GDAL/CMakeLists.txt
+++ b/IO/GDAL/CMakeLists.txt
@@ -9,4 +9,7 @@ vtk_module_add_module(VTK::IOGDAL
   CLASSES ${classes})
 vtk_module_link(VTK::IOGDAL
   PRIVATE
-    GDAL::GDAL)
+    ${GDAL_LIBRARIES})
+vtk_module_include(VTK::IOGDAL
+  PRIVATE
+    ${GDAL_INCLUDE_DIRS})
\ No newline at end of file
