diff --git a/CMake/vtkModuleWrapPython.cmake b/CMake/vtkModuleWrapPython.cmake
index 5d2c2e1bf..e33a16d68 100644
--- a/CMake/vtkModuleWrapPython.cmake
+++ b/CMake/vtkModuleWrapPython.cmake
@@ -152,23 +152,26 @@ function (_vtk_module_wrap_python_sources module sources classes)
   set(_vtk_python_genex_compile_definitions "")
   set(_vtk_python_genex_include_directories "")
   if (_vtk_python_genex_allowed)
     set(_vtk_python_genex_compile_definitions
       "$<TARGET_PROPERTY:${_vtk_python_target_name},COMPILE_DEFINITIONS>")
     set(_vtk_python_genex_include_directories
       "$<TARGET_PROPERTY:${_vtk_python_target_name},INCLUDE_DIRECTORIES>")
+    set(_vtk_python_genex_interface_include_directories
+      "$<TARGET_PROPERTY:${_vtk_python_target_name},INTERFACE_INCLUDE_DIRECTORIES>")
   else ()
     if (NOT DEFINED ENV{CI})
       message(AUTHOR_WARNING
         "Python wrapping is not using target-local compile definitions or "
         "include directories. This may affect generation of the Python "
         "wrapper sources for the ${module} module. Use CMake 3.19+ to "
         "guarantee intended behavior.")
     endif ()
   endif ()
   file(GENERATE
     OUTPUT  "${_vtk_python_args_file}"
     CONTENT "$<$<BOOL:${_vtk_python_genex_compile_definitions}>:\n-D\'$<JOIN:${_vtk_python_genex_compile_definitions},\'\n-D\'>\'>\n
 $<$<BOOL:${_vtk_python_genex_include_directories}>:\n-I\'$<JOIN:${_vtk_python_genex_include_directories},\'\n-I\'>\'>\n
+$<$<BOOL:${_vtk_python_genex_interface_include_directories}>:\n-I\'$<JOIN:${_vtk_python_genex_interface_include_directories},\'\n-I\'>\'>\n
 $<$<BOOL:${_vtk_python_hierarchy_files}>:\n--types \'$<JOIN:${_vtk_python_hierarchy_files},\'\n--types \'>\'>\n")
 
   set(_vtk_python_sources)

