diff --git a/IO/MotionFX/vtkMotionFXCFGGrammar.h b/IO/MotionFX/vtkMotionFXCFGGrammar.h
index dba137386..4cb03e054 100644
--- a/IO/MotionFX/vtkMotionFXCFGGrammar.h
+++ b/IO/MotionFX/vtkMotionFXCFGGrammar.h
@@ -23,7 +23,7 @@
 
 // for debugging
 // clang-format off
-#include VTK_PEGTL(pegtl/contrib/tracer.hpp)
+#include VTK_PEGTL(pegtl/contrib/trace.hpp)
 // clang-format on
 
 namespace MotionFX
diff --git a/IO/MotionFX/vtkMotionFXCFGReader.cxx b/IO/MotionFX/vtkMotionFXCFGReader.cxx
index 338aa736e..374e54b94 100644
--- a/IO/MotionFX/vtkMotionFXCFGReader.cxx
+++ b/IO/MotionFX/vtkMotionFXCFGReader.cxx
@@ -1213,7 +1213,7 @@ bool PositionFileMotion::read_position_file(const std::string& rootDir) const
     }
     return true;
   }
-  catch (const tao::pegtl::input_error& e)
+  catch (const tao::pegtl::parse_error& e)
   {
     vtkGenericWarningMacro("PositionFileMotion::read_position_file failed: " << e.what());
   }
@@ -1232,7 +1232,7 @@ bool UniversalTransformMotion::read_universaltransform_file(const std::string& r
       in, numbers, this->transforms);
     return true;
   }
-  catch (const tao::pegtl::input_error& e)
+  catch (const tao::pegtl::parse_error& e)
   {
     vtkGenericWarningMacro(
       "UniversalTransformMotion::read_universaltransform_file failed: " << e.what());
@@ -1267,7 +1267,7 @@ public:
       if (getenv("MOTIONFX_DEBUG_GRAMMAR") != nullptr)
       {
         tao::pegtl::read_input<> in2(filename);
-        tao::pegtl::parse<MotionFX::CFG::Grammar, tao::pegtl::nothing, tao::pegtl::tracer>(in2);
+        tao::pegtl::complete_trace<MotionFX::CFG::Grammar>(in2);
       }
       return false;
     }
diff --git a/ThirdParty/pegtl/CMakeLists.txt b/ThirdParty/pegtl/CMakeLists.txt
index 9bbd4c828..0cdb1f53d 100644
--- a/ThirdParty/pegtl/CMakeLists.txt
+++ b/ThirdParty/pegtl/CMakeLists.txt
@@ -5,8 +5,7 @@ vtk_module_third_party(
     VERSION
       "2.8.3"
   EXTERNAL
     PACKAGE PEGTL
-    VERSION 2.0.0
     TARGETS PEGTL::PEGTL
     STANDARD_INCLUDE_DIRS)
 
diff --git a/CMake/FindPEGTL.cmake b/CMake/FindPEGTL.cmake
index 73eee02f7..22d8bc159 100644
--- a/CMake/FindPEGTL.cmake	
+++ b/CMake/FindPEGTL.cmake
@@ -19,31 +19,45 @@
 #
 #  PEGTL::PEGTL
 
+message(STATUS "Searching for PEGTL")
+find_package(PEGTL CONFIG REQUIRED)
+if(TARGET taocpp::pegtl)
+    message(STATUS "Searching for PEGTL - found target taocpp::pegtl")
+    get_target_property(TARGET_IMPORTED_GLOBAL taocpp::pegtl IMPORTED_GLOBAL)
+    if(NOT TARGET_IMPORTED_GLOBAL)
+      set_target_properties(taocpp::pegtl PROPERTIES IMPORTED_GLOBAL TRUE)
+    endif()
+    if(NOT TARGET PEGTL::PEGTL)
+       add_library(PEGTL::PEGTL IMPORTED INTERFACE)
+       target_link_libraries(PEGTL::PEGTL INTERFACE taocpp::pegtl)
+    endif()
+else()
 find_path(PEGTL_INCLUDE_DIR
   NAMES pegtl/version.hpp
   PATH_SUFFIXES tao
   DOC "Path to PEGTL headers")
 mark_as_advanced(PEGTL_INCLUDE_DIR)
 
 if (PEGTL_INCLUDE_DIR)
   file(STRINGS "${PEGTL_INCLUDE_DIR}/pegtl/version.hpp" _pegtl_version_header
     REGEX "TAO_PEGTL_VERSION")
   string(REGEX MATCH "define[ \t]+TAO_PEGTL_VERSION[ \t]+\"([0-9.]+)\"" _pegtl_version_match "${_pegtl_version_header}")
   set(PEGTL_VERSION "${CMAKE_MATCH_1}")
   unset(_pegtl_version_header)
   unset(_pegtl_version_match)
 endif ()
 
 include(FindPackageHandleStandardArgs)
 find_package_handle_standard_args(PEGTL
   REQUIRED_VARS PEGTL_INCLUDE_DIR
   VERSION_VAR PEGTL_VERSION)
 
 if (PEGTL_FOUND)
   set(PEGTL_INCLUDE_DIRS "${PEGTL_INCLUDE_DIR}")
   if (NOT TARGET PEGTL::PEGTL)
     add_library(PEGTL::PEGTL INTERFACE IMPORTED)
     set_target_properties(PEGTL::PEGTL PROPERTIES
       INTERFACE_INCLUDE_DIRECTORIES "${PEGTL_INCLUDE_DIR}")
   endif ()
 endif ()
+endif ()

