diff --git a/IO/NetCDF/vtk.module b/IO/NetCDF/vtk.module
index a0cc0741..07f3606b 100644
--- a/IO/NetCDF/vtk.module
+++ b/IO/NetCDF/vtk.module
@@ -26,7 +26,6 @@ PRIVATE_DEPENDS
   VTK::CommonDataModel
   VTK::netcdf
   VTK::vtksys
-  VTK::libproj
 TEST_DEPENDS
   VTK::CommonExecutionModel
   VTK::FiltersGeometry
diff --git a/IO/NetCDF/vtkNetCDFCFWriter.cxx b/IO/NetCDF/vtkNetCDFCFWriter.cxx
index 756ff87..d71de89 100644
--- a/IO/NetCDF/vtkNetCDFCFWriter.cxx
+++ b/IO/NetCDF/vtkNetCDFCFWriter.cxx
@@ -33,7 +33,6 @@
 #include <sstream>
 #include <vector>
 
-#include "vtk_libproj.h"
 #include "vtk_netcdf.h"
 
 VTK_ABI_NAMESPACE_BEGIN
