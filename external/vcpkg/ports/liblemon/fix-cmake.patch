diff --git a/CMakeLists.txt b/CMakeLists.txt
index 4406bc2..5717680 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -295,7 +295,7 @@ CONFIGURE_FILE(
   ${PROJECT_BINARY_DIR}/cmake/LEMONConfig.cmake
   @ONLY
 )
-IF(UNIX)
+IF(UNIX OR TRUE)
   INSTALL(
     FILES ${PROJECT_BINARY_DIR}/cmake/LEMONConfig.cmake
     DESTINATION share/lemon/cmake
@@ -307,6 +307,13 @@ ELSEIF(WIN32)
   )
 ENDIF()
 
+install(
+    EXPORT lemon-targets
+    FILE lemon-targets.cmake
+    NAMESPACE unofficial::lemon::
+    DESTINATION share/lemon/cmake
+)
+
 CONFIGURE_FILE(
   ${PROJECT_SOURCE_DIR}/cmake/version.cmake.in
   ${PROJECT_BINARY_DIR}/cmake/version.cmake
diff --git a/cmake/LEMONConfig.cmake.in b/cmake/LEMONConfig.cmake.in
index b0d2d8b..6bb662a 100644
--- a/cmake/LEMONConfig.cmake.in
+++ b/cmake/LEMONConfig.cmake.in
@@ -1,4 +1,4 @@
-SET(LEMON_INCLUDE_DIR "@CMAKE_INSTALL_PREFIX@/include" CACHE PATH "LEMON include directory")
+SET(LEMON_INCLUDE_DIR "${CMAKE_CURRENT_LIST_DIR}/../../include" CACHE PATH "LEMON include directory")
 SET(LEMON_INCLUDE_DIRS "${LEMON_INCLUDE_DIR}")
 
 IF(UNIX)
@@ -7,7 +7,12 @@ ELSEIF(WIN32)
   SET(LEMON_LIB_NAME "lemon.lib")
 ENDIF(UNIX)
 
-SET(LEMON_LIBRARY "@CMAKE_INSTALL_PREFIX@/lib/${LEMON_LIB_NAME}" CACHE FILEPATH "LEMON library")
+SET(LEMON_LIBRARY
+  optimized "${CMAKE_CURRENT_LIST_DIR}/../../lib/${LEMON_LIB_NAME}"
+  debug "${CMAKE_CURRENT_LIST_DIR}/../../debug/lib/${LEMON_LIB_NAME}"
+  CACHE FILEPATH "LEMON library")
 SET(LEMON_LIBRARIES "${LEMON_LIBRARY}")
 
 MARK_AS_ADVANCED(LEMON_LIBRARY LEMON_INCLUDE_DIR)
+
+INCLUDE(${CMAKE_CURRENT_LIST_DIR}/lemon-targets.cmake)
diff --git a/lemon/CMakeLists.txt b/lemon/CMakeLists.txt
index 4e6567e..ad6f5bf 100644
--- a/lemon/CMakeLists.txt
+++ b/lemon/CMakeLists.txt
@@ -56,6 +56,10 @@ ENDIF()
 
 ADD_LIBRARY(lemon ${LEMON_SOURCES})
 
+INCLUDE(GNUInstallDirs)
+
+TARGET_INCLUDE_DIRECTORIES(lemon PUBLIC $<INSTALL_INTERFACE:${CMAKE_INSTALL_INCLUDEDIR}>)
+
 TARGET_LINK_LIBRARIES(lemon
   ${GLPK_LIBRARIES} ${COIN_LIBRARIES} ${ILOG_LIBRARIES} ${SOPLEX_LIBRARIES}
   )
@@ -71,6 +75,11 @@ INSTALL(
   COMPONENT library
 )
 
+install(TARGETS lemon EXPORT lemon-targets
+    LIBRARY DESTINATION lib
+    ARCHIVE DESTINATION lib
+)
+
 INSTALL(
   DIRECTORY . bits concepts
   DESTINATION include/lemon
