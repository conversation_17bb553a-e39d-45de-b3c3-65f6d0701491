diff --git a/include/CPmsgpack.h b/include/CPmsgpack.h
index e11f6be..948cc65 100644
--- a/include/CPmsgpack.h
+++ b/include/CPmsgpack.h
@@ -6,7 +6,6 @@
 
 // Workaround MSVC endiannes issues
 #if defined(_MSC_VER) && ( defined(_M_ARM) || defined(_M_ARM64) )
-#    define MSGPACK_ENDIAN_LITTLE_BYTE
 #endif
 
 #include "msgpack.hpp"
diff --git a/src/ODEIntegrators.cpp b/src/ODEIntegrators.cpp
index 23bd4c4..beab710 100644
--- a/src/ODEIntegrators.cpp
+++ b/src/ODEIntegrators.cpp
@@ -4,6 +4,8 @@
 #include "CPstrings.h"
 #include "Exceptions.h"
 #include <algorithm>
+#undef max
+#undef min
 
 bool ODEIntegrators::AdaptiveRK54(AbstractODEIntegrator& ode, double tstart, double tend, double hmin, double hmax, double eps_allowed,
                                   double step_relax) {
