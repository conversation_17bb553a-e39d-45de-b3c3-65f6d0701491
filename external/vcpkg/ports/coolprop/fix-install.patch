diff --git a/CMakeLists.txt b/CMakeLists.txt
index 619dfeb..f856613 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -543,21 +543,24 @@ if(COOLPROP_OBJECT_LIBRARY
     endif(MSVC)
     install(
       TARGETS ${LIB_NAME}
-      DESTINATION
-        static_library/${CMAKE_SYSTEM_NAME}/${BITNESS}bit_${CMAKE_CXX_COMPILER_ID}_${CMAKE_CXX_COMPILER_VERSION}
+      RUNTIME DESTINATION bin
+      LIBRARY DESTINATION lib
+      ARCHIVE DESTINATION lib
     )
     install(FILES ${CMAKE_CURRENT_SOURCE_DIR}/${COOLPROP_LIBRARY_HEADER}
-            DESTINATION static_library)
+            DESTINATION include)
   elseif(COOLPROP_SHARED_LIBRARY)
     list(APPEND APP_SOURCES
          "${CMAKE_CURRENT_SOURCE_DIR}/${COOLPROP_LIBRARY_SOURCE}")
     add_library(${LIB_NAME} SHARED ${APP_SOURCES} ${COOLPROP_LIBRARY_EXPORTS})
     install(
       TARGETS ${LIB_NAME}
-      DESTINATION shared_library/${CMAKE_SYSTEM_NAME}/${BITNESS}bit${CONVENTION}
+      RUNTIME DESTINATION bin
+      LIBRARY DESTINATION lib
+      ARCHIVE DESTINATION lib
     )
     install(FILES ${CMAKE_CURRENT_SOURCE_DIR}/${COOLPROP_LIBRARY_HEADER}
-            DESTINATION shared_library)
+            DESTINATION include)
     set_property(
       TARGET ${LIB_NAME}
       APPEND_STRING
@@ -889,7 +892,9 @@ if(COOLPROP_EES_MODULE)
     VERBATIM)
   # install the generated library and the other files
   install(TARGETS COOLPROP_EES
-          DESTINATION "${CMAKE_INSTALL_PREFIX}/EES/${CMAKE_SYSTEM_NAME}")
+          RUNTIME DESTINATION bin
+          LIBRARY DESTINATION lib
+          ARCHIVE DESTINATION lib)
   install(FILES "${CMAKE_CURRENT_SOURCE_DIR}/wrappers/EES/CoolProp.htm"
           DESTINATION "${CMAKE_INSTALL_PREFIX}/EES/${CMAKE_SYSTEM_NAME}")
   install(FILES "${CMAKE_CURRENT_SOURCE_DIR}/wrappers/EES/CoolProp.LIB"
