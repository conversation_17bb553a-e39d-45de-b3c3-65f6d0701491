diff --git a/configure b/configure
index b2451c4..a7fb4da 100755
--- a/configure
+++ b/configure
@@ -19562,6 +19562,10 @@ $as_echo "$as_me: WARNING: This usually indicates an error in configure." >&2;}
                 opal_found=0
                 opal_i=`expr $opal_count + 1`
                 ;;
+        -isystem)
+                opal_found=0
+                opal_i=`expr $opal_count + 1`
+                ;;
         --param)
                 opal_found=0
                 opal_i=`expr $opal_count + 1`
@@ -19653,6 +19657,10 @@ $as_echo "$as_me: WARNING: This usually indicates an error in configure." >&2;}
                 opal_found=0
                 opal_i=`expr $opal_count + 1`
                 ;;
+        -isystem)
+                opal_found=0
+                opal_i=`expr $opal_count + 1`
+                ;;
         --param)
                 opal_found=0
                 opal_i=`expr $opal_count + 1`
@@ -19759,6 +19767,10 @@ $as_echo "$as_me: WARNING: Code coverage functionality is currently available on
                 opal_found=0
                 opal_i=`expr $opal_count + 1`
                 ;;
+        -isystem)
+                opal_found=0
+                opal_i=`expr $opal_count + 1`
+                ;;
         --param)
                 opal_found=0
                 opal_i=`expr $opal_count + 1`
@@ -19946,6 +19958,10 @@ $as_echo "$opal_cv_cc_wno_long_double" >&6; }
                 opal_found=0
                 opal_i=`expr $opal_count + 1`
                 ;;
+        -isystem)
+                opal_found=0
+                opal_i=`expr $opal_count + 1`
+                ;;
         --param)
                 opal_found=0
                 opal_i=`expr $opal_count + 1`
@@ -20127,6 +20143,10 @@ $as_echo "$opal_cv_cc_fno_strict_aliasing" >&6; }
                 opal_found=0
                 opal_i=`expr $opal_count + 1`
                 ;;
+        -isystem)
+                opal_found=0
+                opal_i=`expr $opal_count + 1`
+                ;;
         --param)
                 opal_found=0
                 opal_i=`expr $opal_count + 1`
@@ -20267,6 +20287,10 @@ $as_echo "$opal_cv_cc_restrict_cflags" >&6; }
                 opal_found=0
                 opal_i=`expr $opal_count + 1`
                 ;;
+        -isystem)
+                opal_found=0
+                opal_i=`expr $opal_count + 1`
+                ;;
         --param)
                 opal_found=0
                 opal_i=`expr $opal_count + 1`
@@ -26120,6 +26144,10 @@ $as_echo "$as_me: WARNING: Code coverage functionality is currently available on
                 opal_found=0
                 opal_i=`expr $opal_count + 1`
                 ;;
+        -isystem)
+                opal_found=0
+                opal_i=`expr $opal_count + 1`
+                ;;
         --param)
                 opal_found=0
                 opal_i=`expr $opal_count + 1`
@@ -26297,6 +26325,10 @@ ac_compiler_gnu=$ac_cv_c_compiler_gnu
                 opal_found=0
                 opal_i=`expr $opal_count + 1`
                 ;;
+        -isystem)
+                opal_found=0
+                opal_i=`expr $opal_count + 1`
+                ;;
         --param)
                 opal_found=0
                 opal_i=`expr $opal_count + 1`
@@ -26441,6 +26473,10 @@ ac_compiler_gnu=$ac_cv_c_compiler_gnu
                 opal_found=0
                 opal_i=`expr $opal_count + 1`
                 ;;
+        -isystem)
+                opal_found=0
+                opal_i=`expr $opal_count + 1`
+                ;;
         --param)
                 opal_found=0
                 opal_i=`expr $opal_count + 1`
@@ -27799,6 +27835,10 @@ $as_echo "$as_me: WARNING: Code coverage functionality is currently available on
                 opal_found=0
                 opal_i=`expr $opal_count + 1`
                 ;;
+        -isystem)
+                opal_found=0
+                opal_i=`expr $opal_count + 1`
+                ;;
         --param)
                 opal_found=0
                 opal_i=`expr $opal_count + 1`
@@ -27973,6 +28013,10 @@ ac_compiler_gnu=$ac_cv_c_compiler_gnu
                 opal_found=0
                 opal_i=`expr $opal_count + 1`
                 ;;
+        -isystem)
+                opal_found=0
+                opal_i=`expr $opal_count + 1`
+                ;;
         --param)
                 opal_found=0
                 opal_i=`expr $opal_count + 1`
@@ -28117,6 +28161,10 @@ ac_compiler_gnu=$ac_cv_c_compiler_gnu
                 opal_found=0
                 opal_i=`expr $opal_count + 1`
                 ;;
+        -isystem)
+                opal_found=0
+                opal_i=`expr $opal_count + 1`
+                ;;
         --param)
                 opal_found=0
                 opal_i=`expr $opal_count + 1`
@@ -44127,6 +44175,10 @@ fi
                 opal_found=0
                 opal_i=`expr $opal_count + 1`
                 ;;
+        -isystem)
+                opal_found=0
+                opal_i=`expr $opal_count + 1`
+                ;;
         --param)
                 opal_found=0
                 opal_i=`expr $opal_count + 1`
@@ -60612,6 +60664,10 @@ $as_echo_n "checking if intel compiler _Quad == REAL*16... " >&6; }
                 opal_found=0
                 opal_i=`expr $opal_count + 1`
                 ;;
+        -isystem)
+                opal_found=0
+                opal_i=`expr $opal_count + 1`
+                ;;
         --param)
                 opal_found=0
                 opal_i=`expr $opal_count + 1`
@@ -60842,6 +60898,10 @@ $as_echo_n "checking if gnu compiler __float128 == REAL*16... " >&6; }
                 opal_found=0
                 opal_i=`expr $opal_count + 1`
                 ;;
+        -isystem)
+                opal_found=0
+                opal_i=`expr $opal_count + 1`
+                ;;
         --param)
                 opal_found=0
                 opal_i=`expr $opal_count + 1`
diff --git a/opal/mca/pmix/pmix3x/pmix/configure b/opal/mca/pmix/pmix3x/pmix/configure
index 0326a68..07df146 100755
--- a/opal/mca/pmix/pmix3x/pmix/configure
+++ b/opal/mca/pmix/pmix3x/pmix/configure
@@ -19386,6 +19386,10 @@ $as_echo "$pmix_cv_cc_coverage" >&6; }
                 pmix_found=0
                 pmix_i=`expr $pmix_count + 1`
                 ;;
+        -isystem)
+                pmix_found=0
+                pmix_i=`expr $pmix_count + 1`
+                ;;
         --param)
                 pmix_found=0
                 pmix_i=`expr $pmix_count + 1`
@@ -19477,6 +19481,10 @@ $as_echo "$pmix_cv_cc_coverage" >&6; }
                 pmix_found=0
                 pmix_i=`expr $pmix_count + 1`
                 ;;
+        -isystem)
+                pmix_found=0
+                pmix_i=`expr $pmix_count + 1`
+                ;;
         --param)
                 pmix_found=0
                 pmix_i=`expr $pmix_count + 1`
@@ -19583,6 +19591,10 @@ $as_echo "$as_me: WARNING: Code coverage functionality is currently available on
                 pmix_found=0
                 pmix_i=`expr $pmix_count + 1`
                 ;;
+        -isystem)
+                pmix_found=0
+                pmix_i=`expr $pmix_count + 1`
+                ;;
         --param)
                 pmix_found=0
                 pmix_i=`expr $pmix_count + 1`
@@ -19770,6 +19782,10 @@ $as_echo "$pmix_cv_cc_wno_long_double" >&6; }
                 pmix_found=0
                 pmix_i=`expr $pmix_count + 1`
                 ;;
+        -isystem)
+                pmix_found=0
+                pmix_i=`expr $pmix_count + 1`
+                ;;
         --param)
                 pmix_found=0
                 pmix_i=`expr $pmix_count + 1`
@@ -19951,6 +19967,10 @@ $as_echo "$pmix_cv_cc_fno_strict_aliasing" >&6; }
                 pmix_found=0
                 pmix_i=`expr $pmix_count + 1`
                 ;;
+        -isystem)
+                pmix_found=0
+                pmix_i=`expr $pmix_count + 1`
+                ;;
         --param)
                 pmix_found=0
                 pmix_i=`expr $pmix_count + 1`
@@ -20091,6 +20111,10 @@ $as_echo "$pmix_cv_cc_restrict_cflags" >&6; }
                 pmix_found=0
                 pmix_i=`expr $pmix_count + 1`
                 ;;
+        -isystem)
+                pmix_found=0
+                pmix_i=`expr $pmix_count + 1`
+                ;;
         --param)
                 pmix_found=0
                 pmix_i=`expr $pmix_count + 1`
@@ -53914,6 +53938,10 @@ fi
                 pmix_found=0
                 pmix_i=`expr $pmix_count + 1`
                 ;;
+        -isystem)
+                pmix_found=0
+                pmix_i=`expr $pmix_count + 1`
+                ;;
         --param)
                 pmix_found=0
                 pmix_i=`expr $pmix_count + 1`
@@ -54005,6 +54033,10 @@ fi
                 pmix_found=0
                 pmix_i=`expr $pmix_count + 1`
                 ;;
+        -isystem)
+                pmix_found=0
+                pmix_i=`expr $pmix_count + 1`
+                ;;
         --param)
                 pmix_found=0
                 pmix_i=`expr $pmix_count + 1`
@@ -54096,6 +54128,10 @@ fi
                 pmix_found=0
                 pmix_i=`expr $pmix_count + 1`
                 ;;
+        -isystem)
+                pmix_found=0
+                pmix_i=`expr $pmix_count + 1`
+                ;;
         --param)
                 pmix_found=0
                 pmix_i=`expr $pmix_count + 1`
@@ -54187,6 +54223,10 @@ fi
                 pmix_found=0
                 pmix_i=`expr $pmix_count + 1`
                 ;;
+        -isystem)
+                pmix_found=0
+                pmix_i=`expr $pmix_count + 1`
+                ;;
         --param)
                 pmix_found=0
                 pmix_i=`expr $pmix_count + 1`
