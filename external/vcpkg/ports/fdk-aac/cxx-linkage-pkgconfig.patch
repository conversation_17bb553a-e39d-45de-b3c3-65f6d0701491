diff --git a/CMakeLists.txt b/CMakeLists.txt
index 4db8c7e..da3297c 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -528,6 +528,15 @@ if(FDK_AAC_INSTALL_PKGCONFIG_MODULE)
       set(LIBS_PUBLIC "-lm")
     endif()
   endif()
+  foreach(lib IN LISTS CMAKE_CXX_IMPLICIT_LINK_LIBRARIES)
+    if(lib IN_LIST CMAKE_C_IMPLICIT_LINK_LIBRARIES)
+      continue()
+    elseif(EXISTS "${lib}")
+      string(APPEND LIBS_PRIVATE " ${CMAKE_LINK_LIBRARY_FILE_FLAG}${lib}")
+    else()
+      string(APPEND LIBS_PRIVATE " ${CMAKE_LINK_LIBRARY_FLAG}${lib}")
+    endif()
+  endforeach()
   configure_file(fdk-aac.pc.in fdk-aac.pc @ONLY)
   install(
     FILES
