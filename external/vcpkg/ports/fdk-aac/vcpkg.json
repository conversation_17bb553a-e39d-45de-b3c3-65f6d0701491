{"name": "fdk-aac", "version-semver": "2.0.2", "port-version": 4, "description": "A standalone Third-Party Modified Version of the Fraunhofer FDK AAC Codec Library for Android. Uses a fork without HE-AAC, HE-AACv2, or xHE-AAC support to avoid patent licensing and GPL compatibility issues when built without the he-aac option.", "homepage": "https://gitlab.freedesktop.org/wtaymans/fdk-aac-stripped", "license": "FDK-AAC", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"he-aac": {"description": "Support patent-encumbered HE-AAC, HE-AACv2, and xHE-AAC codec profiles. Do not distribute binaries with this option without the relevant patent licenses if you are in a jurisdiction that recognizes software patents. Might not be compatible with the GPL depending on legal interpretation. Refer to https://bugzilla.redhat.com/show_bug.cgi?id=1501522#c112", "supports": "!android"}}}