diff --git a/CMakeLists.txt b/CMakeLists.txt
index 57c97c4..d0e0da9 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -49,6 +49,8 @@ project(ztd.platform
 	HOMEPAGE_URL "https://ztdplatform.readthedocs.io/en/latest/"
 	LANGUAGES C CXX)
 
+include(CMakePackageConfigHelpers)
+include(GNUInstallDirs)
 if(ZTD_PLATFORM_READTHEDOCS)
 	# ReadTheDocs seems unable to handle the include at the project level: something must be going wrong?
 	include(CheckCXXCompilerFlag)
@@ -121,10 +123,7 @@ option(ZTD_PLATFORM_USE_ENCODING_C "Enable encoding_rs's C bindings with ztd::pl
 
 # # Dependencies
 # ztd.idk
-FetchContent_Declare(ztd.idk
-	GIT_REPOSITORY https://github.com/soasis/idk.git
-	GIT_TAG main)
-FetchContent_MakeAvailable(ztd.idk)
+find_package(ztd.idk CONFIG REQUIRED)
 
 # encoding_c
 if (ZTD_PLATFORM_USE_ENCODING_C)
@@ -408,11 +407,8 @@ install(DIRECTORY include/
 	DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}
 )
 install(TARGETS ztd.platform
-	PUBLIC_HEADER
-	RUNTIME
-	ARCHIVE
-	LIBRARY
-)
+        EXPORT ztd.platform-targets
+        DESTINATION lib)
 
 # # Config / Version packaging
 # Version configurations
@@ -430,6 +426,15 @@ export(TARGETS ztd.platform
 	FILE
 	"${CMAKE_CURRENT_BINARY_DIR}/cmake/ztd.platform/ztd.platform-targets.cmake")
 
+install(FILES
+  ${CMAKE_CURRENT_BINARY_DIR}/cmake/ztd.platform/ztd.platform-config.cmake
+  ${CMAKE_CURRENT_BINARY_DIR}/cmake/ztd.platform/ztd.platform-config-version.cmake
+  DESTINATION share/ztd.platform
+  )
+install(EXPORT ztd.platform-targets
+  FILE ztd.platform-targets.cmake
+  DESTINATION share/ztd.platform
+)
 # # Benchmarks, Tests, Examples
 if(ZTD_PLATFORM_TESTS)
 	include(CTest)
diff --git a/cmake/ztd.platform-config.cmake.in b/cmake/ztd.platform-config.cmake.in
index 37629fc..5d5886c 100644
--- a/cmake/ztd.platform-config.cmake.in
+++ b/cmake/ztd.platform-config.cmake.in
@@ -1,6 +1,11 @@
 @PACKAGE_INIT@
 
-if (TARGET ztd::platform)
+include(CMakeFindDependencyMacro)
+find_dependency(ztd.idk CONFIG)
+
+include(${CMAKE_CURRENT_LIST_DIR}/ztd.platform-targets.cmake)
+if (TARGET ztd.platform)
+	add_library(ztd::platform ALIAS ztd.platform)
 	get_target_property(ZTD_PLATFORM_INCLUDE_DIRS
 		ztd.platform INTERFACE_INCLUDE_DIRECTORIES)
 	set_and_check(ZTD_PLATFORM_INCLUDE_DIRS "${ZTD_PLATFORM_INCLUDE_DIRS}")
