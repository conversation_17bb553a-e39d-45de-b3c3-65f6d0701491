diff --git a/meson.build b/meson.build
index b1a110c..6a7d930 100644
--- a/meson.build
+++ b/meson.build
@@ -2074,12 +2074,11 @@ libsystemd_includes = [basic_includes, include_directories(
 includes = [libsystemd_includes, include_directories('src/shared')]
 
 subdir('po')
-subdir('catalog')
+support_url=''
 subdir('src/fundamental')
 subdir('src/basic')
 subdir('src/libsystemd')
-subdir('src/shared')
-subdir('src/libudev')
+subdir('src/systemd') # headers
 
 libsystemd = shared_library(
         'systemd',
@@ -2095,7 +2094,8 @@ libsystemd = shared_library(
                         threads,
                         userspace],
         link_depends : libsystemd_sym,
-        install : true,
+        build_by_default : static_libsystemd == 'false',
+        install : static_libsystemd == 'false',
         install_tag: 'libsystemd',
         install_dir : libdir)
 
@@ -2130,6 +2130,8 @@ else
         alias_target('libsystemd', libsystemd)
 endif
 
+if false
+
 libudev = shared_library(
         'udev',
         version : libudev_version,
@@ -2141,7 +2143,8 @@ libudev = shared_library(
         dependencies : [threads,
                         userspace],
         link_depends : libudev_sym,
-        install : true,
+        build_by_default : static_libudev == 'false',
+        install : static_libudev == 'false',
         install_tag: 'libudev',
         install_dir : libdir)
 
@@ -2905,6 +2908,13 @@ custom_target('installed-unit-files.txt',
               install_dir : testdata_dir,
               command : [meson_extract_unit_files, project_build_root])
 
+endif # false
+static_libudev='unused'
+bashcompletiondir='no'
+zshcompletiondir='no'
+want_html=false
+want_man=false
+
 #####################################################################
 
 alt_time_epoch = run_command('date', '-Is', '-u', '-d', '@@0@'.format(time_epoch),
