diff --git a/src/basic/memory-util.h b/src/basic/memory-util.h
index 1179513..fc39e06 100644
--- a/src/basic/memory-util.h
+++ b/src/basic/memory-util.h
@@ -41,7 +41,9 @@ static inline int memcmp_safe(const void *s1, const void *s2, size_t n) {
                 return 0;
         assert(s1);
         assert(s2);
+DISABLE_WARNING_NONNULL
         return memcmp(s1, s2, n);
+REENABLE_WARNING
 }
 
 /* Compare s1 (length n1) with s2 (length n2) in lexicographic order. */
