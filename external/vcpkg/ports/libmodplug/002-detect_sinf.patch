diff --git a/CMakeLists.txt b/CMakeLists.txt
index 2ada51b..468f1a3 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -4,6 +4,7 @@ project(libmodplug)
 add_definitions(-DMODPLUG_BUILD)
 
 include (CheckFunctionExists)
+include (CheckIncludeFile)
 
 include_directories(AFTER
   src
@@ -11,6 +12,10 @@ include_directories(AFTER
   ${PROJECT_BINARY_DIR}
   )
 
+if (UNIX)
+  set (CMAKE_REQUIRED_LIBRARIES m)
+endif()
+
 if (WIN32)
   add_definitions(-D_USE_MATH_DEFINES)
   add_definitions(-DNOMINMAX)
@@ -44,6 +49,11 @@ if (WIN32 AND NOT (MINGW OR MSYS))
       "Compilation may fail if inttypes.h is not natively supported by the compiler."
       "You can get inttypes.h from http://code.google.com/p/msinttypes/")
   endif()
+else()
+  check_include_file("stdint.h" HAVE_STDINT)
+  if (HAVE_STDINT)
+    add_definitions(-DHAVE_STDINT_H)
+  endif()
 endif()
 
 check_function_exists("setenv" HAVE_SETENV)
