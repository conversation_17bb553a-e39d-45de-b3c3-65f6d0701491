diff --git a/CMakeLists.txt b/CMakeLists.txt
index 2ada51b..a0a28d5 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -1,4 +1,4 @@
-cmake_minimum_required(VERSION 2.8.0)
+cmake_minimum_required(VERSION 3.5)
 
 project(libmodplug)
 add_definitions(-DMODPLUG_BUILD)
@@ -131,7 +131,24 @@ if(HAVE_SINF)
   add_definitions(-DHAVE_SINF)
 endif(HAVE_SINF)
 
-if (NOT WIN32)
+if(WIN32)
+  set(LIBS_PRIVATE "-luser32")
+else(WIN32)
+  set(FAKE_CXX_LINKAGE "")
+  foreach(lib IN LISTS CMAKE_CXX_IMPLICIT_LINK_LIBRARIES)
+    if(lib IN_LIST CMAKE_C_IMPLICIT_LINK_LIBRARIES)
+      continue()
+    elseif(EXISTS "${lib}")
+      string(APPEND FAKE_CXX_LINKAGE " ${CMAKE_LINK_LIBRARY_FILE_FLAG}${lib}")
+    else()
+      string(APPEND FAKE_CXX_LINKAGE " ${CMAKE_LINK_LIBRARY_FLAG}${lib}")
+    endif()
+  endforeach()
+  set(LIBS_PRIVATE " ${FAKE_CXX_LINKAGE} ")
+  if(NOT LIBS_PRIVATE MATCHES " -lm ")
+    string(APPEND LIBS_PRIVATE "-lm")
+  endif()
+endif(WIN32)
   set(prefix "${CMAKE_INSTALL_PREFIX}")
   set(exec_prefix "${CMAKE_INSTALL_PREFIX}")
   set(libdir "${CMAKE_INSTALL_PREFIX}/lib")
@@ -142,4 +159,3 @@ if (NOT WIN32)
   install(FILES "${PROJECT_BINARY_DIR}/libmodplug.pc"
     DESTINATION lib/pkgconfig
   )
-endif (NOT WIN32)
diff --git a/libmodplug.pc.in b/libmodplug.pc.in
index bbf05f9..e4a43cc 100644
--- a/libmodplug.pc.in
+++ b/libmodplug.pc.in
@@ -8,5 +8,5 @@ Description: The ModPlug mod file playing library.
 Version: @VERSION@
 Requires: 
 Libs: -L${libdir} -lmodplug 
-Libs.private: -lstdc++ -lm
+Libs.private: @LIBS_PRIVATE@
 Cflags: -I${includedir}
