diff --git a/ewmh/ewmh.c.m4 b/ewmh/ewmh.c.m4
index b986105f6..a0ce37c2a 100644
--- a/ewmh/ewmh.c.m4	
+++ b/ewmh/ewmh.c.m4
@@ -45,7 +45,7 @@
 
 #include <sys/types.h>
 
-#define ssizeof(foo)            (ssize_t)sizeof(foo)
+#define ssizeof(foo)            (ptrdiff_t)sizeof(foo)
 #define countof(foo)            (ssizeof(foo) / ssizeof(foo[0]))
 
 /**
@@ -1119,12 +1119,13 @@ xcb_ewmh_append_wm_icon_checked(xcb_ewmh_connection_t *ewmh,
                                 uint32_t img_len, uint32_t *img)
 {
   const uint32_t data_len = img_len + 2;
-  uint32_t data[data_len];
+  uint32_t *data = (uint32_t*)malloc(data_len*sizeof(uint32_t));
 
-  set_wm_icon_data(data, width, height, img_len, img);
-
-  return xcb_ewmh_set_wm_icon_checked(ewmh, XCB_PROP_MODE_APPEND, window,
-                                      data_len, data);
+  set_wm_icon_data(data, width, height, img_len, img);
+  const xcb_void_cookie_t res = xcb_ewmh_set_wm_icon_checked(ewmh, XCB_PROP_MODE_APPEND, window,
+                                      data_len, data);
+  free(data);
+  return res;
 }
 
 xcb_void_cookie_t
@@ -1134,12 +1135,13 @@ xcb_ewmh_append_wm_icon(xcb_ewmh_connection_t *ewmh,
                         uint32_t img_len, uint32_t *img)
 {
   const uint32_t data_len = img_len + 2;
-  uint32_t data[data_len];
-
-  set_wm_icon_data(data, width, height, img_len, img);
+  uint32_t *data = (uint32_t*)malloc(data_len*sizeof(uint32_t));
 
-  return xcb_ewmh_set_wm_icon(ewmh, XCB_PROP_MODE_APPEND, window,
-                              data_len, data);
+  set_wm_icon_data(data, width, height, img_len, img);
+  const xcb_void_cookie_t res = xcb_ewmh_set_wm_icon(ewmh, XCB_PROP_MODE_APPEND, window,
+                              data_len, data);
+  free(data);
+  return res;
 }
 
 DO_GET_PROPERTY(wm_icon, _NET_WM_ICON, XCB_ATOM_CARDINAL, UINT_MAX)
