diff --git a/tools/nasm-filter.sh b/tools/nasm-filter.sh
index 5ec9ba3..fcc4971 100755
--- a/tools/nasm-filter.sh
+++ b/tools/nasm-filter.sh
@@ -15,6 +15,11 @@ while [ -n "$*" ]; do
 	    shift
 	    shift
 	    ;;
+	-isysroot )
+	    # Unsupported options with arg
+	    shift
+	    shift
+	    ;;
 	--prefix* )
 	    # Supported options without arg
 	    options="$options $1"
diff --git a/tools/yasm-filter.sh b/tools/yasm-filter.sh
index c33952a..b99bfc3 100755
--- a/tools/yasm-filter.sh
+++ b/tools/yasm-filter.sh
@@ -10,6 +10,11 @@ while [ -n "$*" ]; do
 	    shift
 	    shift
 	    ;;
+	-isysroot )
+	    # Unsupported options with arg
+	    shift
+	    shift
+	    ;;
 	-I* | -i* | --prefix* )
 	    # Supported options without arg
 	    options="$options $1"
