{"name": "exiv2", "version": "0.28.5", "description": "Image metadata library and tools", "homepage": "https://exiv2.org", "license": "GPL-2.0-or-later", "supports": "!xbox", "dependencies": ["inih", {"name": "libiconv", "platform": "!windows"}, {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"bmff": {"description": "Support for BMFF files (e.g., CR3, HEIF, HEIC, AVIF, and JPEG XL)", "dependencies": ["brotli"]}, "nls": {"description": "Build native language support", "dependencies": [{"name": "gettext", "host": true, "features": ["tools"]}, "gettext-libintl"]}, "png": {"description": "Build with png support", "dependencies": ["zlib"]}, "video": {"description": "Deprecated. it will be removed in the future."}, "xmp": {"description": "Build with XMP metadata support", "dependencies": ["expat"]}}}