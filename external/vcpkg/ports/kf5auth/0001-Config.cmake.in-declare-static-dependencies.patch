From cf1e5df1117958db8dfeb13796cc63359aaf3bb1 Mon Sep 17 00:00:00 2001
From: =?UTF-8?q?Dawid=20Wro=CC=81bel?= <<EMAIL>>
Date: Fri, 16 Sep 2022 00:39:30 +0200
Subject: [PATCH] Config.cmake.in: declare static dependencies

---
 KF5AuthConfig.cmake.in    | 17 +++++++++++++++++
 src/ConfigureChecks.cmake |  2 +-
 2 files changed, 18 insertions(+), 1 deletion(-)

diff --git a/KF5AuthConfig.cmake.in b/KF5AuthConfig.cmake.in
index e461162..14f79aa 100644
--- a/KF5AuthConfig.cmake.in
+++ b/KF5AuthConfig.cmake.in
@@ -9,8 +9,25 @@ set(KAUTH_HELPER_INSTALL_DIR "@KAUTH_HELPER_INSTALL_DIR@")
 set(KAUTH_HELPER_INSTALL_ABSOLUTE_DIR "@KAUTH_HELPER_INSTALL_ABSOLUTE_DIR@")
 
 include(CMakeFindDependencyMacro)
+
 find_dependency(KF5CoreAddons "@KF_DEP_VERSION@")
 
+if(NOT @BUILD_SHARED_LIBS@)
+    find_dependency(Qt@QT_MAJOR_VERSION@DBus "@REQUIRED_QT_VERSION@")
+
+    if(NOT @KAUTH_BUILD_CODEGENERATOR_ONLY@)
+        find_dependency(Qt@QT_MAJOR_VERSION@Widgets "@REQUIRED_QT_VERSION@")
+    endif()
+
+    if(NOT KAUTH_BACKEND_NAME AND @UNIX@ AND NOT @APPLE@)
+        find_dependency(PolkitQt@QT_MAJOR_VERSION@ "@POLKITQT_MIN_VERSION@")
+    endif()
+
+    if(KAUTH_BACKEND_NAME AND NOT @KAUTH_BUILD_CODEGENERATOR_ONLY@ AND "${KAUTH_BACKEND_NAME}" STREQUAL "POLKITQT@QT_MAJOR_VERSION@-1")
+        find_dependency(PolkitQt@QT_MAJOR_VERSION@ "@POLKITQT_MIN_VERSION@")
+    endif()
+endif()
+
 include("${CMAKE_CURRENT_LIST_DIR}/KF5AuthTargets.cmake")
 @PACKAGE_INCLUDE_QCHTARGETS@
 include("${CMAKE_CURRENT_LIST_DIR}/KF5AuthMacros.cmake")
diff --git a/src/ConfigureChecks.cmake b/src/ConfigureChecks.cmake
index 23c5d28..bb5546d 100644
--- a/src/ConfigureChecks.cmake
+++ b/src/ConfigureChecks.cmake
@@ -9,7 +9,7 @@ string(TOUPPER "${KAUTH_BACKEND_NAME}" KAUTH_BACKEND_NAME)
 
 set(KAUTH_BACKEND ${KAUTH_BACKEND_NAME})
 
-set(POLKITQT_MIN_VERSION 0.112.0)
+set(POLKITQT_MIN_VERSION 0.112.0 PARENT_SCOPE)
 
 ## Check if the user did not specify a backend to be built. If that is the case,
 ## we check what is the best backend to build on this system.
-- 
2.38.0

