{"name": "gz-common", "version": "6.0.2", "port-version": 1, "description": "Common libraries for robotics applications", "homepage": "https://gazebosim.org/libs/common/", "license": "Apache-2.0", "dependencies": ["assimp", {"name": "dlfcn-win32", "platform": "windows | uwp"}, {"name": "ffmpeg", "default-features": false, "features": ["avcodec", "avdevice", "avformat", "swscale"]}, "freeimage", {"name": "gdal", "default-features": false}, "gz-cmake", "gz-math", "gz-utils", {"name": "ignition-modularscripts", "host": true}, {"name": "libuuid", "platform": "!windows & !osx"}, "tinyxml2"]}