diff --git a/profiler/src/CMakeLists.txt b/profiler/src/CMakeLists.txt
--- a/profiler/src/CMakeLists.txt
+++ b/profiler/src/CMakeLists.txt
@@ -116,7 +116,7 @@
   install(PROGRAMS
     ${CMAKE_CURRENT_BINARY_DIR}/gz_remotery_vis
-    DESTINATION ${GZ_PROFILER_SCRIPT_PATH})
+    DESTINATION ${CMAKE_INSTALL_PREFIX}/tools/gz-common${PROJECT_VERSION_MAJOR})
 
   install(DIRECTORY Remotery/vis/
           DESTINATION ${GZ_PROFILER_VIS_PATH})
 endif()
