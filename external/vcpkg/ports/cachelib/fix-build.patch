diff --git a/cachelib/cachebench/util/JSONConfig.h b/cachelib/cachebench/util/JSONConfig.h
index 97cbe3fe..7e9bbdda 100644
--- a/cachelib/cachebench/util/JSONConfig.h
+++ b/cachelib/cachebench/util/JSONConfig.h
@@ -146,10 +146,6 @@ struct Options {};
 
 template <typename Type, size_t size>
 constexpr void checkCorrectSize() {
-#ifndef SKIP_OPTION_SIZE_VERIFY
-  Options<sizeof(Type)> var = Options<size>{};
-  (void)var;
-#endif
 }
 } // namespace cachebench
 } // namespace cachelib
diff --git a/cachelib/common/CMakeLists.txt b/cachelib/common/CMakeLists.txt
index 927f2fa3..fcf6a5a9 100644
--- a/cachelib/common/CMakeLists.txt
+++ b/cachelib/common/CMakeLists.txt
@@ -37,9 +37,6 @@ target_link_libraries(cachelib_common PUBLIC
   FBThrift::thriftprotocol
   Folly::folly
   Folly::follybenchmark
-  Folly::folly_exception_tracer
-  Folly::folly_exception_tracer_base
-  Folly::folly_exception_counter
 )
 
 install(TARGETS cachelib_common
diff --git a/cachelib/common/Utils.cpp b/cachelib/common/Utils.cpp
index 57a1beb9..204a432b 100644
--- a/cachelib/common/Utils.cpp
+++ b/cachelib/common/Utils.cpp
@@ -15,7 +15,6 @@
  */
 
 #include <dirent.h>
-#include <folly/debugging/exception_tracer/ExceptionTracer.h>
 #include <sys/mman.h>
 #include <sys/resource.h>
 #include <sys/stat.h>
@@ -428,10 +427,6 @@ size_t getMemAvailable() {
 }
 
 void printExceptionStackTraces() {
-  auto exceptions = folly::exception_tracer::getCurrentExceptions();
-  for (auto& exc : exceptions) {
-    std::cerr << exc << std::endl;
-  }
 }
 
 } // namespace util
