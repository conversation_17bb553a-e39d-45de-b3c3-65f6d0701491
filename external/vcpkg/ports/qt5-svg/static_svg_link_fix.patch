diff --git a/src/plugins/iconengines/svgiconengine/svgiconengine.pro b/src/plugins/iconengines/svgiconengine/svgiconengine.pro
index bfc739fa8..c0df08194 100644
--- a/src/plugins/iconengines/svgiconengine/svgiconengine.pro
+++ b/src/plugins/iconengines/svgiconengine/svgiconengine.pro
@@ -8,6 +8,6 @@ OTHER_FILES += qsvgiconengine-nocompress.json
 QT += svg core-private gui-private
 
 PLUGIN_TYPE = iconengines
-PLUGIN_EXTENDS = svg
+PLUGIN_EXTENDS = gui
 PLUGIN_CLASS_NAME = QSvgIconPlugin
 load(qt_plugin)
diff --git a/src/plugins/imageformats/svg/svg.pro b/src/plugins/imageformats/svg/svg.pro
index 9db6a9ab0..dcfc4b555 100644
--- a/src/plugins/imageformats/svg/svg.pro
+++ b/src/plugins/imageformats/svg/svg.pro
@@ -6,6 +6,6 @@ SOURCES += main.cpp \
 QT += svg
 
 PLUGIN_TYPE = imageformats
-PLUGIN_EXTENDS = svg
+PLUGIN_EXTENDS = gui
 PLUGIN_CLASS_NAME = QSvgPlugin
 load(qt_plugin)
