diff --git a/CMakeLists.txt b/CMakeLists.txt
index 4768ac81..eda857af 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -615,9 +615,16 @@ if(MINGW AND BUILD_SHARED_LIBS)
     set_target_properties(poppler PROPERTIES SUFFIX "-${POPPLER_SOVERSION}${CMAKE_SHARED_LIBRARY_SUFFIX}")
 endif()
 target_link_libraries(poppler LINK_PRIVATE ${poppler_LIBS})
-install(TARGETS poppler RUNTIME DESTINATION bin LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR} ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR})
+target_include_directories(poppler PUBLIC $<INSTALL_INTERFACE:include/poppler>)
+set_target_properties(poppler PROPERTIES EXPORT_NAME poppler-private)
+install(TARGETS poppler EXPORT unofficial-poppler-targets RUNTIME DESTINATION bin LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR} ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR})
+install(EXPORT unofficial-poppler-targets NAMESPACE unofficial::poppler:: DESTINATION share/unofficial-poppler)
 
 if(ENABLE_UNSTABLE_API_ABI_HEADERS)
+  target_include_directories(poppler PUBLIC
+      $<INSTALL_INTERFACE:include/poppler/fofi>
+      $<INSTALL_INTERFACE:include/poppler/goo>
+  )
   install(FILES
     poppler/Annot.h
     poppler/AnnotStampImageHelper.h
diff --git a/cpp/CMakeLists.txt b/cpp/CMakeLists.txt
index 48851454..ad936f6b 100644
--- a/cpp/CMakeLists.txt
+++ b/cpp/CMakeLists.txt
@@ -31,7 +31,8 @@ if(MINGW AND BUILD_SHARED_LIBS)
     set_target_properties(poppler-cpp PROPERTIES SUFFIX "-${POPPLER_CPP_SOVERSION}${CMAKE_SHARED_LIBRARY_SUFFIX}")
 endif()
 target_link_libraries(poppler-cpp poppler Iconv::Iconv)
-install(TARGETS poppler-cpp RUNTIME DESTINATION bin LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR} ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR})
+install(TARGETS poppler-cpp EXPORT unofficial-poppler-cpp-targets RUNTIME DESTINATION bin LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR} ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR})
+install(EXPORT unofficial-poppler-cpp-targets NAMESPACE unofficial::poppler:: DESTINATION share/unofficial-poppler)
 
 install(FILES
   poppler-destination.h
diff --git a/qt6/src/CMakeLists.txt b/qt6/src/CMakeLists.txt
index cd919753..1816270f 100644
--- a/qt6/src/CMakeLists.txt
+++ b/qt6/src/CMakeLists.txt
@@ -51,7 +51,8 @@ if(USE_CMS)
     target_link_libraries(poppler-qt6 poppler ${LCMS2_LIBRARIES})
     target_include_directories(poppler-qt6 SYSTEM PRIVATE ${LCMS2_INCLUDE_DIR})
 endif()
-install(TARGETS poppler-qt6 RUNTIME DESTINATION bin LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR} ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR})
+install(TARGETS poppler-qt6 EXPORT unofficial-poppler-qt6-targets RUNTIME DESTINATION bin LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR} ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR})
+install(EXPORT unofficial-poppler-qt6-targets NAMESPACE unofficial::poppler:: DESTINATION share/unofficial-poppler)
 
 install(FILES
   poppler-qt6.h
