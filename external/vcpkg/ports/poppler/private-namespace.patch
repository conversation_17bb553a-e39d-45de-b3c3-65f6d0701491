diff --git a/poppler/Gfx.h b/poppler/Gfx.h
index 81a620b..ecfb449 100644
--- a/poppler/Gfx.h
+++ b/poppler/Gfx.h
@@ -48,7 +48,10 @@ class PDFDoc;
 class XRef;
 class Array;
 class Stream;
+namespace poppler_private {
 class Parser;
+}
+using namespace poppler_private;
 class Dict;
 class Function;
 class OutputDev;
diff --git a/poppler/Parser.h b/poppler/Parser.h
index b379b67..c3d85bd 100644
--- a/poppler/Parser.h
+++ b/poppler/Parser.h
@@ -33,6 +33,7 @@
 // Parser
 //------------------------------------------------------------------------
 
+namespace poppler_private {
 class POPPLER_PRIVATE_EXPORT Parser
 {
 public:
@@ -72,5 +73,7 @@ private:
     void shift(int objNum = -1);
     void shift(const char *cmdA, int objNum);
 };
+}
+using namespace poppler_private;
 
 #endif
diff --git a/poppler/XRef.h b/poppler/XRef.h
index e2b2ca8..7d6acbb 100644
--- a/poppler/XRef.h
+++ b/poppler/XRef.h
@@ -46,7 +46,10 @@
 
 class Dict;
 class Stream;
+namespace poppler_private {
 class Parser;
+}
+using namespace poppler_private;
 class ObjectStream;
 
 //------------------------------------------------------------------------
