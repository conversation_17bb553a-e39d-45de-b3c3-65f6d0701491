diff --git a/CMakeLists.txt b/CMakeLists.txt
index 087928c..d1664ce 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -102,7 +102,7 @@ IF(BUILD_FOR_SSE2)
   ENDIF()
 ENDIF()
 
-IF(WIN32)
+IF(0)
   INCLUDE_DIRECTORIES(${CMAKE_SOURCE_DIR}/libs/getopt)
   # base path for searching for glib on windows
   IF(NOT GLIB2_BASE_DIR)
@@ -205,7 +205,7 @@ IF(BUILD_DOC)
   ADD_SUBDIRECTORY(docs)
 ENDIF()
 
-IF(WIN32 AND NOT BUILD_STATIC)
+IF(0)
   FIND_FILE(GLIB2_DLL
             NAMES glib-2.0-0.dll glib-2.dll glib-2-vs9.dll
             PATHS "${GLIB2_BASE_DIR}/bin"
