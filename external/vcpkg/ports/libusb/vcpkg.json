{"name": "libusb", "version": "1.0.27", "port-version": 2, "description": "a cross-platform library to access USB devices", "homepage": "https://github.com/libusb/libusb", "license": "LGPL-2.1-or-later", "supports": "!uwp", "dependencies": [{"name": "vcpkg-msbuild", "host": true, "platform": "windows & !mingw"}], "default-features": [{"name": "udev", "platform": "linux"}], "features": {"udev": {"description": "Enable udev", "supports": "linux"}}}