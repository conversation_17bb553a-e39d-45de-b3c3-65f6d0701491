diff --git a/CMakeLists.txt b/CMakeLists.txt
index 715adb7c2e..a06f8a8c88 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -1375,6 +1375,11 @@ foreach (OCCT_MODULE ${OCCT_MODULES})
 endforeach()
 set (OCCT_MODULES_ENABLED ${OCCT_MODULES_ENABLED_SORTED})
 
+foreach (OCCT_LIBRARY IN LISTS OCCT_LIBRARIES)
+  target_include_directories(${OCCT_LIBRARY} INTERFACE $<INSTALL_INTERFACE:include/opencascade>) # OpenCASCADE_INCLUDE_DIR
+  target_include_directories(${OCCT_LIBRARY} INTERFACE $<INSTALL_INTERFACE:include>) # vcpkg lock-in
+endforeach()
+
 # Add all targets to the build-tree export set
 export (TARGETS ${OCCT_LIBRARIES} FILE "${CMAKE_BINARY_DIR}/OpenCASCADETargets.cmake")
 
