diff --git a/CMakeLists.txt b/CMakeLists.txt
index 2dc81a3..4d53fa6 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -260,11 +260,11 @@ install(
     EXPORT
         telnetpp-targets
     ARCHIVE DESTINATION
-        ${CMAKE_INSTALL_LIBDIR}/telnetpp-${TELNETPP_VERSION}
+        lib
     RUNTIME DESTINATION
-        ${CMAKE_INSTALL_LIBDIR}/telnetpp-${TELNETPP_VERSION}
+        bin
     LIBRARY DESTINATION
-        ${CMAKE_INSTALL_BINDIR}/telnetpp-${TELNETPP_VERSION}
+        lib
 )
 
 install(
@@ -273,14 +273,14 @@ install(
     NAMESPACE 
         KazDragon::
     DESTINATION
-        ${CMAKE_INSTALL_DATADIR}/telnetpp-${TELNETPP_VERSION}
+        share/telnetpp
 )
 
 install(
     DIRECTORY
         include/
     DESTINATION
-        ${CMAKE_INSTALL_INCLUDEDIR}/telnetpp-${TELNETPP_VERSION}
+        include
 )
 
 install(
@@ -288,7 +288,7 @@ install(
         "${CMAKE_CURRENT_BINARY_DIR}/telnetpp-config.cmake"
         "${CMAKE_CURRENT_BINARY_DIR}/telnetpp-config-version.cmake"
     DESTINATION
-        ${CMAKE_INSTALL_DATADIR}/telnetpp-${TELNETPP_VERSION}
+        share/telnetpp
 )
 
 if (TELNETPP_WITH_TESTS)
