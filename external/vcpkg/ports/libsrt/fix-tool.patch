diff --git a/CMakeLists.txt b/CMakeLists.txt
index 7ed11ef..794028a 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -1384,11 +1384,13 @@ if (ENABLE_APPS)
 
 	# Applications
 
-	srt_add_application(srt-live-transmit ${VIRTUAL_srtsupport})
+	srt_add_application(srt-live-transmit ${VIRTUAL_srtsupport} ${VIRTUAL_srt})
+	target_link_libraries(srt-live-transmit ${SSL_LIBRARIES})
 	if (DEFINED EXTRA_stransmit)
 		set_target_properties(srt-live-transmit PROPERTIES COMPILE_FLAGS "${EXTRA_stransmit}")
 	endif()
-	srt_add_application(srt-file-transmit ${VIRTUAL_srtsupport})
+	srt_add_application(srt-file-transmit ${VIRTUAL_srtsupport} ${VIRTUAL_srt})
+	target_link_libraries(srt-file-transmit ${SSL_LIBRARIES})
 
 	if (MINGW)
 		# FIXME: with MINGW, it fails to build apps that require C++11
@@ -1397,7 +1399,8 @@ if (ENABLE_APPS)
 	else()
 		# srt-multiplex temporarily blocked
 		#srt_add_application(srt-multiplex ${VIRTUAL_srtsupport})
-		srt_add_application(srt-tunnel ${VIRTUAL_srtsupport})
+		srt_add_application(srt-tunnel ${VIRTUAL_srtsupport} ${VIRTUAL_srt})
+		target_link_libraries(srt-tunnel ${SSL_LIBRARIES})
 	endif()
 
 	if (ENABLE_TESTING)
