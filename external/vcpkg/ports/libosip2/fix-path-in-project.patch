diff --git a/platform/vsnet/osip2.vcxproj b/platform/vsnet/osip2.vcxproj
index b13cbe9..40b8e23 100644
--- a/platform/vsnet/osip2.vcxproj
+++ b/platform/vsnet/osip2.vcxproj
@@ -79,7 +79,7 @@
   <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
     <ClCompile>
       <Optimization>Disabled</Optimization>
-      <AdditionalIncludeDirectories>..\..\..\osip\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
+      <AdditionalIncludeDirectories>..\..\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
       <PreprocessorDefinitions>ENABLE_TRACE;SYSTEM_LOGGER_ENABLED;_CRT_SECURE_NO_DEPRECATE;WIN32;_DEBUG;_LIB;%(PreprocessorDefinitions)</PreprocessorDefinitions>
       <MinimalRebuild>true</MinimalRebuild>
       <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
@@ -97,7 +97,7 @@
   <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
     <ClCompile>
       <Optimization>Disabled</Optimization>
-      <AdditionalIncludeDirectories>..\..\..\osip\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
+      <AdditionalIncludeDirectories>..\..\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
       <PreprocessorDefinitions>ENABLE_TRACE;SYSTEM_LOGGER_ENABLED;_CRT_SECURE_NO_DEPRECATE;WIN32;_DEBUG;_LIB;%(PreprocessorDefinitions)</PreprocessorDefinitions>
       <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
       <RuntimeLibrary>MultiThreadedDebug</RuntimeLibrary>
@@ -113,7 +113,7 @@
   </ItemDefinitionGroup>
   <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
     <ClCompile>
-      <AdditionalIncludeDirectories>..\..\..\osip\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
+      <AdditionalIncludeDirectories>..\..\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
       <PreprocessorDefinitions>ENABLE_TRACE;SYSTEM_LOGGER_ENABLED;_CRT_SECURE_NO_DEPRECATE;WIN32;NDEBUG;_LIB;%(PreprocessorDefinitions)</PreprocessorDefinitions>
       <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
       <PrecompiledHeader>
@@ -129,7 +129,7 @@
   </ItemDefinitionGroup>
   <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
     <ClCompile>
-      <AdditionalIncludeDirectories>..\..\..\osip\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
+      <AdditionalIncludeDirectories>..\..\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
       <PreprocessorDefinitions>ENABLE_TRACE;SYSTEM_LOGGER_ENABLED;_CRT_SECURE_NO_DEPRECATE;WIN32;NDEBUG;_LIB;%(PreprocessorDefinitions)</PreprocessorDefinitions>
       <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
       <PrecompiledHeader>
@@ -144,37 +144,37 @@
     </Lib>
   </ItemDefinitionGroup>
   <ItemGroup>
-    <ClCompile Include="..\..\..\osip\src\osip2\fsm_misc.c" />
-    <ClCompile Include="..\..\..\osip\src\osip2\ict.c" />
-    <ClCompile Include="..\..\..\osip\src\osip2\ict_fsm.c" />
-    <ClCompile Include="..\..\..\osip\src\osip2\ist.c" />
-    <ClCompile Include="..\..\..\osip\src\osip2\ist_fsm.c" />
-    <ClCompile Include="..\..\..\osip\src\osip2\nict.c" />
-    <ClCompile Include="..\..\..\osip\src\osip2\nict_fsm.c" />
-    <ClCompile Include="..\..\..\osip\src\osip2\nist.c" />
-    <ClCompile Include="..\..\..\osip\src\osip2\nist_fsm.c" />
-    <ClCompile Include="..\..\..\osip\src\osip2\osip.c" />
-    <ClCompile Include="..\..\..\osip\src\osip2\osip_dialog.c" />
-    <ClCompile Include="..\..\..\osip\src\osip2\osip_event.c" />
-    <ClCompile Include="..\..\..\osip\src\osip2\osip_time.c" />
-    <ClCompile Include="..\..\..\osip\src\osip2\osip_transaction.c" />
-    <ClCompile Include="..\..\..\osip\src\osip2\port_condv.c" />
-    <ClCompile Include="..\..\..\osip\src\osip2\port_fifo.c" />
-    <ClCompile Include="..\..\..\osip\src\osip2\port_sema.c" />
-    <ClCompile Include="..\..\..\osip\src\osip2\port_thread.c" />
+    <ClCompile Include="..\..\src\osip2\fsm_misc.c" />
+    <ClCompile Include="..\..\src\osip2\ict.c" />
+    <ClCompile Include="..\..\src\osip2\ict_fsm.c" />
+    <ClCompile Include="..\..\src\osip2\ist.c" />
+    <ClCompile Include="..\..\src\osip2\ist_fsm.c" />
+    <ClCompile Include="..\..\src\osip2\nict.c" />
+    <ClCompile Include="..\..\src\osip2\nict_fsm.c" />
+    <ClCompile Include="..\..\src\osip2\nist.c" />
+    <ClCompile Include="..\..\src\osip2\nist_fsm.c" />
+    <ClCompile Include="..\..\src\osip2\osip.c" />
+    <ClCompile Include="..\..\src\osip2\osip_dialog.c" />
+    <ClCompile Include="..\..\src\osip2\osip_event.c" />
+    <ClCompile Include="..\..\src\osip2\osip_time.c" />
+    <ClCompile Include="..\..\src\osip2\osip_transaction.c" />
+    <ClCompile Include="..\..\src\osip2\port_condv.c" />
+    <ClCompile Include="..\..\src\osip2\port_fifo.c" />
+    <ClCompile Include="..\..\src\osip2\port_sema.c" />
+    <ClCompile Include="..\..\src\osip2\port_thread.c" />
   </ItemGroup>
   <ItemGroup>
-    <ClInclude Include="..\..\..\osip\src\osip2\fsm.h" />
-    <ClInclude Include="..\..\..\osip\include\osip2\internal.h" />
-    <ClInclude Include="..\..\..\osip\include\osip2\osip.h" />
-    <ClInclude Include="..\..\..\osip\include\osip2\osip_condv.h" />
-    <ClInclude Include="..\..\..\osip\include\osip2\osip_dialog.h" />
-    <ClInclude Include="..\..\..\osip\include\osip2\osip_fifo.h" />
-    <ClInclude Include="..\..\..\osip\include\osip2\osip_mt.h" />
-    <ClInclude Include="..\..\..\osip\src\osip2\xixt.h" />
+    <ClInclude Include="..\..\src\osip2\fsm.h" />
+    <ClInclude Include="..\..\include\osip2\internal.h" />
+    <ClInclude Include="..\..\include\osip2\osip.h" />
+    <ClInclude Include="..\..\include\osip2\osip_condv.h" />
+    <ClInclude Include="..\..\include\osip2\osip_dialog.h" />
+    <ClInclude Include="..\..\include\osip2\osip_fifo.h" />
+    <ClInclude Include="..\..\include\osip2\osip_mt.h" />
+    <ClInclude Include="..\..\src\osip2\xixt.h" />
   </ItemGroup>
   <ItemGroup>
-    <ProjectReference Include="..\..\..\osip\platform\vsnet\osipparser2.vcxproj">
+    <ProjectReference Include="..\..\platform\vsnet\osipparser2.vcxproj">
       <Project>{44f46b7e-0e51-4304-9735-330dfbab41e5}</Project>
       <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
     </ProjectReference>
diff --git a/platform/vsnet/osipparser2.vcxproj b/platform/vsnet/osipparser2.vcxproj
index 6c73222..bda0fe5 100644
--- a/platform/vsnet/osipparser2.vcxproj
+++ b/platform/vsnet/osipparser2.vcxproj
@@ -79,7 +79,7 @@
   <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
     <ClCompile>
       <Optimization>Disabled</Optimization>
-      <AdditionalIncludeDirectories>..\..\..\osip\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
+      <AdditionalIncludeDirectories>..\..\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
       <PreprocessorDefinitions>ENABLE_TRACE;SYSTEM_LOGGER_ENABLED;_CRT_SECURE_NO_DEPRECATE;WIN32;_DEBUG;_LIB;%(PreprocessorDefinitions)</PreprocessorDefinitions>
       <MinimalRebuild>true</MinimalRebuild>
       <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
@@ -97,7 +97,7 @@
   <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
     <ClCompile>
       <Optimization>Disabled</Optimization>
-      <AdditionalIncludeDirectories>..\..\..\osip\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
+      <AdditionalIncludeDirectories>..\..\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
       <PreprocessorDefinitions>ENABLE_TRACE;SYSTEM_LOGGER_ENABLED;_CRT_SECURE_NO_DEPRECATE;WIN32;_DEBUG;_LIB;%(PreprocessorDefinitions)</PreprocessorDefinitions>
       <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
       <RuntimeLibrary>MultiThreadedDebug</RuntimeLibrary>
@@ -113,7 +113,7 @@
   </ItemDefinitionGroup>
   <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
     <ClCompile>
-      <AdditionalIncludeDirectories>..\..\..\osip\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
+      <AdditionalIncludeDirectories>..\..\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
       <PreprocessorDefinitions>ENABLE_TRACE;SYSTEM_LOGGER_ENABLED;_CRT_SECURE_NO_DEPRECATE;WIN32;NDEBUG;_LIB;%(PreprocessorDefinitions)</PreprocessorDefinitions>
       <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
       <PrecompiledHeader>
@@ -129,7 +129,7 @@
   </ItemDefinitionGroup>
   <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
     <ClCompile>
-      <AdditionalIncludeDirectories>..\..\..\osip\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
+      <AdditionalIncludeDirectories>..\..\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
       <PreprocessorDefinitions>ENABLE_TRACE;SYSTEM_LOGGER_ENABLED;_CRT_SECURE_NO_DEPRECATE;WIN32;NDEBUG;_LIB;%(PreprocessorDefinitions)</PreprocessorDefinitions>
       <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
       <PrecompiledHeader>
@@ -144,83 +144,83 @@
     </Lib>
   </ItemDefinitionGroup>
   <ItemGroup>
-    <ClCompile Include="..\..\..\osip\src\osipparser2\osip_accept.c" />
-    <ClCompile Include="..\..\..\osip\src\osipparser2\osip_accept_encoding.c" />
-    <ClCompile Include="..\..\..\osip\src\osipparser2\osip_accept_language.c" />
-    <ClCompile Include="..\..\..\osip\src\osipparser2\osip_alert_info.c" />
-    <ClCompile Include="..\..\..\osip\src\osipparser2\osip_allow.c" />
-    <ClCompile Include="..\..\..\osip\src\osipparser2\osip_authentication_info.c" />
-    <ClCompile Include="..\..\..\osip\src\osipparser2\osip_authorization.c" />
-    <ClCompile Include="..\..\..\osip\src\osipparser2\osip_body.c" />
-    <ClCompile Include="..\..\..\osip\src\osipparser2\osip_call_id.c" />
-    <ClCompile Include="..\..\..\osip\src\osipparser2\osip_call_info.c" />
-    <ClCompile Include="..\..\..\osip\src\osipparser2\osip_contact.c" />
-    <ClCompile Include="..\..\..\osip\src\osipparser2\osip_content_disposition.c" />
-    <ClCompile Include="..\..\..\osip\src\osipparser2\osip_content_encoding.c" />
-    <ClCompile Include="..\..\..\osip\src\osipparser2\osip_content_length.c" />
-    <ClCompile Include="..\..\..\osip\src\osipparser2\osip_content_type.c" />
-    <ClCompile Include="..\..\..\osip\src\osipparser2\osip_cseq.c" />
-    <ClCompile Include="..\..\..\osip\src\osipparser2\osip_error_info.c" />
-    <ClCompile Include="..\..\..\osip\src\osipparser2\osip_from.c" />
-    <ClCompile Include="..\..\..\osip\src\osipparser2\osip_header.c" />
-    <ClCompile Include="..\..\..\osip\src\osipparser2\osip_list.c" />
-    <ClCompile Include="..\..\..\osip\src\osipparser2\osip_md5c.c" />
-    <ClCompile Include="..\..\..\osip\src\osipparser2\osip_message.c" />
-    <ClCompile Include="..\..\..\osip\src\osipparser2\osip_message_parse.c" />
-    <ClCompile Include="..\..\..\osip\src\osipparser2\osip_message_to_str.c" />
-    <ClCompile Include="..\..\..\osip\src\osipparser2\osip_mime_version.c" />
-    <ClCompile Include="..\..\..\osip\src\osipparser2\osip_parser_cfg.c" />
-    <ClCompile Include="..\..\..\osip\src\osipparser2\osip_port.c" />
-    <ClCompile Include="..\..\..\osip\src\osipparser2\osip_proxy_authenticate.c" />
-    <ClCompile Include="..\..\..\osip\src\osipparser2\osip_proxy_authentication_info.c" />
-    <ClCompile Include="..\..\..\osip\src\osipparser2\osip_proxy_authorization.c" />
-    <ClCompile Include="..\..\..\osip\src\osipparser2\osip_record_route.c" />
-    <ClCompile Include="..\..\..\osip\src\osipparser2\osip_route.c" />
-    <ClCompile Include="..\..\..\osip\src\osipparser2\osip_to.c" />
-    <ClCompile Include="..\..\..\osip\src\osipparser2\osip_uri.c" />
-    <ClCompile Include="..\..\..\osip\src\osipparser2\osip_via.c" />
-    <ClCompile Include="..\..\..\osip\src\osipparser2\osip_www_authenticate.c" />
-    <ClCompile Include="..\..\..\osip\src\osipparser2\sdp_accessor.c" />
-    <ClCompile Include="..\..\..\osip\src\osipparser2\sdp_message.c" />
+    <ClCompile Include="..\..\src\osipparser2\osip_accept.c" />
+    <ClCompile Include="..\..\src\osipparser2\osip_accept_encoding.c" />
+    <ClCompile Include="..\..\src\osipparser2\osip_accept_language.c" />
+    <ClCompile Include="..\..\src\osipparser2\osip_alert_info.c" />
+    <ClCompile Include="..\..\src\osipparser2\osip_allow.c" />
+    <ClCompile Include="..\..\src\osipparser2\osip_authentication_info.c" />
+    <ClCompile Include="..\..\src\osipparser2\osip_authorization.c" />
+    <ClCompile Include="..\..\src\osipparser2\osip_body.c" />
+    <ClCompile Include="..\..\src\osipparser2\osip_call_id.c" />
+    <ClCompile Include="..\..\src\osipparser2\osip_call_info.c" />
+    <ClCompile Include="..\..\src\osipparser2\osip_contact.c" />
+    <ClCompile Include="..\..\src\osipparser2\osip_content_disposition.c" />
+    <ClCompile Include="..\..\src\osipparser2\osip_content_encoding.c" />
+    <ClCompile Include="..\..\src\osipparser2\osip_content_length.c" />
+    <ClCompile Include="..\..\src\osipparser2\osip_content_type.c" />
+    <ClCompile Include="..\..\src\osipparser2\osip_cseq.c" />
+    <ClCompile Include="..\..\src\osipparser2\osip_error_info.c" />
+    <ClCompile Include="..\..\src\osipparser2\osip_from.c" />
+    <ClCompile Include="..\..\src\osipparser2\osip_header.c" />
+    <ClCompile Include="..\..\src\osipparser2\osip_list.c" />
+    <ClCompile Include="..\..\src\osipparser2\osip_md5c.c" />
+    <ClCompile Include="..\..\src\osipparser2\osip_message.c" />
+    <ClCompile Include="..\..\src\osipparser2\osip_message_parse.c" />
+    <ClCompile Include="..\..\src\osipparser2\osip_message_to_str.c" />
+    <ClCompile Include="..\..\src\osipparser2\osip_mime_version.c" />
+    <ClCompile Include="..\..\src\osipparser2\osip_parser_cfg.c" />
+    <ClCompile Include="..\..\src\osipparser2\osip_port.c" />
+    <ClCompile Include="..\..\src\osipparser2\osip_proxy_authenticate.c" />
+    <ClCompile Include="..\..\src\osipparser2\osip_proxy_authentication_info.c" />
+    <ClCompile Include="..\..\src\osipparser2\osip_proxy_authorization.c" />
+    <ClCompile Include="..\..\src\osipparser2\osip_record_route.c" />
+    <ClCompile Include="..\..\src\osipparser2\osip_route.c" />
+    <ClCompile Include="..\..\src\osipparser2\osip_to.c" />
+    <ClCompile Include="..\..\src\osipparser2\osip_uri.c" />
+    <ClCompile Include="..\..\src\osipparser2\osip_via.c" />
+    <ClCompile Include="..\..\src\osipparser2\osip_www_authenticate.c" />
+    <ClCompile Include="..\..\src\osipparser2\sdp_accessor.c" />
+    <ClCompile Include="..\..\src\osipparser2\sdp_message.c" />
   </ItemGroup>
   <ItemGroup>
-    <ClInclude Include="..\..\..\osip\include\osipparser2\headers\osip_accept.h" />
-    <ClInclude Include="..\..\..\osip\include\osipparser2\headers\osip_accept_encoding.h" />
-    <ClInclude Include="..\..\..\osip\include\osipparser2\headers\osip_accept_language.h" />
-    <ClInclude Include="..\..\..\osip\include\osipparser2\headers\osip_alert_info.h" />
-    <ClInclude Include="..\..\..\osip\include\osipparser2\headers\osip_allow.h" />
-    <ClInclude Include="..\..\..\osip\include\osipparser2\headers\osip_authorization.h" />
-    <ClInclude Include="..\..\..\osip\include\osipparser2\internal.h" />
-    <ClInclude Include="..\..\..\osip\include\osipparser2\osip_body.h" />
-    <ClInclude Include="..\..\..\osip\include\osipparser2\headers\osip_call_id.h" />
-    <ClInclude Include="..\..\..\osip\include\osipparser2\headers\osip_call_info.h" />
-    <ClInclude Include="..\..\..\osip\include\osipparser2\osip_const.h" />
-    <ClInclude Include="..\..\..\osip\include\osipparser2\headers\osip_contact.h" />
-    <ClInclude Include="..\..\..\osip\include\osipparser2\headers\osip_content_disposition.h" />
-    <ClInclude Include="..\..\..\osip\include\osipparser2\headers\osip_content_encoding.h" />
-    <ClInclude Include="..\..\..\osip\include\osipparser2\headers\osip_content_length.h" />
-    <ClInclude Include="..\..\..\osip\include\osipparser2\headers\osip_content_type.h" />
-    <ClInclude Include="..\..\..\osip\include\osipparser2\headers\osip_cseq.h" />
-    <ClInclude Include="..\..\..\osip\include\osipparser2\headers\osip_error_info.h" />
-    <ClInclude Include="..\..\..\osip\include\osipparser2\headers\osip_from.h" />
-    <ClInclude Include="..\..\..\osip\include\osipparser2\headers\osip_header.h" />
-    <ClInclude Include="..\..\..\osip\include\osipparser2\osip_headers.h" />
-    <ClInclude Include="..\..\..\osip\include\osipparser2\osip_list.h" />
-    <ClInclude Include="..\..\..\osip\include\osipparser2\osip_md5.h" />
-    <ClInclude Include="..\..\..\osip\include\osipparser2\osip_message.h" />
-    <ClInclude Include="..\..\..\osip\include\osipparser2\headers\osip_mime_version.h" />
-    <ClInclude Include="..\..\..\osip\include\osipparser2\osip_parser.h" />
-    <ClInclude Include="..\..\..\osip\include\osipparser2\osip_port.h" />
-    <ClInclude Include="..\..\..\osip\include\osipparser2\headers\osip_proxy_authenticate.h" />
-    <ClInclude Include="..\..\..\osip\include\osipparser2\headers\osip_proxy_authorization.h" />
-    <ClInclude Include="..\..\..\osip\include\osipparser2\headers\osip_record_route.h" />
-    <ClInclude Include="..\..\..\osip\include\osipparser2\headers\osip_route.h" />
-    <ClInclude Include="..\..\..\osip\include\osipparser2\headers\osip_to.h" />
-    <ClInclude Include="..\..\..\osip\include\osipparser2\osip_uri.h" />
-    <ClInclude Include="..\..\..\osip\include\osipparser2\headers\osip_via.h" />
-    <ClInclude Include="..\..\..\osip\include\osipparser2\headers\osip_www_authenticate.h" />
-    <ClInclude Include="..\..\..\osip\src\osipparser2\parser.h" />
-    <ClInclude Include="..\..\..\osip\include\osipparser2\sdp_message.h" />
+    <ClInclude Include="..\..\include\osipparser2\headers\osip_accept.h" />
+    <ClInclude Include="..\..\include\osipparser2\headers\osip_accept_encoding.h" />
+    <ClInclude Include="..\..\include\osipparser2\headers\osip_accept_language.h" />
+    <ClInclude Include="..\..\include\osipparser2\headers\osip_alert_info.h" />
+    <ClInclude Include="..\..\include\osipparser2\headers\osip_allow.h" />
+    <ClInclude Include="..\..\include\osipparser2\headers\osip_authorization.h" />
+    <ClInclude Include="..\..\include\osipparser2\internal.h" />
+    <ClInclude Include="..\..\include\osipparser2\osip_body.h" />
+    <ClInclude Include="..\..\include\osipparser2\headers\osip_call_id.h" />
+    <ClInclude Include="..\..\include\osipparser2\headers\osip_call_info.h" />
+    <ClInclude Include="..\..\include\osipparser2\osip_const.h" />
+    <ClInclude Include="..\..\include\osipparser2\headers\osip_contact.h" />
+    <ClInclude Include="..\..\include\osipparser2\headers\osip_content_disposition.h" />
+    <ClInclude Include="..\..\include\osipparser2\headers\osip_content_encoding.h" />
+    <ClInclude Include="..\..\include\osipparser2\headers\osip_content_length.h" />
+    <ClInclude Include="..\..\include\osipparser2\headers\osip_content_type.h" />
+    <ClInclude Include="..\..\include\osipparser2\headers\osip_cseq.h" />
+    <ClInclude Include="..\..\include\osipparser2\headers\osip_error_info.h" />
+    <ClInclude Include="..\..\include\osipparser2\headers\osip_from.h" />
+    <ClInclude Include="..\..\include\osipparser2\headers\osip_header.h" />
+    <ClInclude Include="..\..\include\osipparser2\osip_headers.h" />
+    <ClInclude Include="..\..\include\osipparser2\osip_list.h" />
+    <ClInclude Include="..\..\include\osipparser2\osip_md5.h" />
+    <ClInclude Include="..\..\include\osipparser2\osip_message.h" />
+    <ClInclude Include="..\..\include\osipparser2\headers\osip_mime_version.h" />
+    <ClInclude Include="..\..\include\osipparser2\osip_parser.h" />
+    <ClInclude Include="..\..\include\osipparser2\osip_port.h" />
+    <ClInclude Include="..\..\include\osipparser2\headers\osip_proxy_authenticate.h" />
+    <ClInclude Include="..\..\include\osipparser2\headers\osip_proxy_authorization.h" />
+    <ClInclude Include="..\..\include\osipparser2\headers\osip_record_route.h" />
+    <ClInclude Include="..\..\include\osipparser2\headers\osip_route.h" />
+    <ClInclude Include="..\..\include\osipparser2\headers\osip_to.h" />
+    <ClInclude Include="..\..\include\osipparser2\osip_uri.h" />
+    <ClInclude Include="..\..\include\osipparser2\headers\osip_via.h" />
+    <ClInclude Include="..\..\include\osipparser2\headers\osip_www_authenticate.h" />
+    <ClInclude Include="..\..\src\osipparser2\parser.h" />
+    <ClInclude Include="..\..\include\osipparser2\sdp_message.h" />
     <ClInclude Include="..\..\include\osipparser2\headers\osip_authentication_info.h" />
     <ClInclude Include="..\..\include\osipparser2\headers\osip_proxy_authentication_info.h" />
   </ItemGroup>
