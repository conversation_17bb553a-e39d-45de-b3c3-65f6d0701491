# Automatically generated by scripts/boost/generate-ports.ps1

vcpkg_from_github(
    OUT_SOURCE_PATH SOURCE_PATH
    REPO boostorg/static_string
    REF boost-${VERSION}
    SHA512 0d2895a9f60dad02c9c3d0cc1bb828ac4a7630191f6f3520a7cd0c21982a097a43745afb2ccfc32836f6a9a61192a0aad2d123760502d4b3772c72188ad36386
    HEAD_REF master
)

set(FEATURE_OPTIONS "")
boost_configure_and_install(
    SOURCE_PATH "${SOURCE_PATH}"
    OPTIONS ${FEATURE_OPTIONS}
)
