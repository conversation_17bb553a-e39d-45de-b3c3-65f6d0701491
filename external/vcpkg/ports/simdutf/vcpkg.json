{"name": "simdutf", "version-semver": "6.4.2", "description": "Unicode validation and transcoding at billions of characters per second", "homepage": "https://github.com/simdutf/simdutf", "license": "Apache-2.0 OR MIT", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"tools": {"description": "Build the fastbase64 and simdutf command line tools for transcoding strings"}}}