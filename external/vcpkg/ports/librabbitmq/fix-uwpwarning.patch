diff --git a/CMakeLists.txt b/CMakeLists.txt
index c88943f..4fc1411 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -140,6 +140,10 @@ if(PROJECT_IS_TOP_LEVEL)
   include(CTest)
 endif()
 
+if(MSVC)
+  set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} /wd4996")
+endif()
+
 option(BUILD_SHARED_LIBS "Build rabbitmq-c as a shared library" ON)
 option(BUILD_STATIC_LIBS "Build rabbitmq-c as a static library" ON)
 option(INSTALL_STATIC_LIBS "Install rabbitmq-c static library" ON)
diff --git a/librabbitmq/CMakeLists.txt b/librabbitmq/CMakeLists.txt
index 4466c9f..e8825dd 100644
--- a/librabbitmq/CMakeLists.txt
+++ b/librabbitmq/CMakeLists.txt
@@ -64,6 +64,8 @@ set(RMQ_SOURCES
   amqp_url.c
 )
 
+add_definitions(-DAMQP_BUILD -D_CRT_SECURE_NO_WARNINGS)
+
 set(RMQ_LIBRARIES ${AMQP_SSL_LIBS} ${SOCKET_LIBRARIES} ${LIBRT} ${CMAKE_THREAD_LIBS_INIT})
 
 if(BUILD_SHARED_LIBS)
