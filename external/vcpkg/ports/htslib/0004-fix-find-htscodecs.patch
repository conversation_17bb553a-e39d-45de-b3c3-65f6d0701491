diff --git a/configure.ac b/configure.ac
index 49f2cbc..434086f 100644
--- a/configure.ac
+++ b/configure.ac
@@ -156,7 +156,7 @@ m4_ifdef([PKG_PROG_PKG_CONFIG], [PKG_PROG_PKG_CONFIG], [PKG_CONFIG=""])
 need_crypto=no
 pc_requires=
 static_LDFLAGS=$LDFLAGS
-static_LIBS='-lpthread -lz -lm'
+static_LIBS='-pthread -lz -lm'
 private_LIBS=$LDFLAGS
 
 AC_ARG_ENABLE([versioned-symbols],
@@ -404,7 +404,7 @@ AS_IF([test "x$with_external_htscodecs" != "xno"],
    AC_CHECK_HEADER([htscodecs/rANS_static4x16.h],[],
                    [libhtscodecs='missing header'],[;])
    AC_CHECK_LIB([htscodecs],[rans_compress_bound_4x16],
-                [:],[libhtscodecs='missing library'])
+                [:],[libhtscodecs='missing library'], ["$static_LIBS"])
    AS_IF([test "$libhtscodecs" = "ok"],
      [AC_DEFINE([HAVE_EXTERNAL_LIBHTSCODECS], 1, [Define if using an external libhtscodecs])
       LIBS="-lhtscodecs $LIBS"
