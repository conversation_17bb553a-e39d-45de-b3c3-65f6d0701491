diff --git a/modules/FindMagnum.cmake b/modules/FindMagnum.cmake
index 8761448..17321af 100644
--- a/modules/FindMagnum.cmake
+++ b/modules/FindMagnum.cmake
@@ -647,9 +647,9 @@ foreach(_component ${Magnum_FIND_COMPONENTS})
 
             # GLFW application dependencies
             elseif(_component STREQUAL GlfwApplication)
-                find_package(GLFW)
+                find_package(glfw3 CONFIG REQUIRED)
                 set_property(TARGET Magnum::${_component} APPEND PROPERTY
-                    INTERFACE_LINK_LIBRARIES GLFW::GLFW)
+                    INTERFACE_LINK_LIBRARIES glfw)
                 # Use the Foundation framework on Apple to query the DPI awareness
                 if(CORRADE_TARGET_APPLE)
                     find_library(_MAGNUM_APPLE_FOUNDATION_FRAMEWORK_LIBRARY Foundation)
