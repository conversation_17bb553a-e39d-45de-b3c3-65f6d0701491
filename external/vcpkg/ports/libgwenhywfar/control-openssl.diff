diff --git a/configure.ac b/configure.ac
index 4e43f5c..7ce9511 100644
--- a/configure.ac
+++ b/configure.ac
@@ -709,7 +709,14 @@ PKG_CHECK_MODULES([LIBGNUTLS], [gnutls >= 2.9.8])
 #
 # check for OpenSSL
 #
-PKG_CHECK_MODULES([OPENSSL], [openssl], [ssl_available=yes], [ssl_available=no])
+AC_ARG_WITH(openssl, [  --with-openssl=yes      enable OpenSSL in gct-tool],
+  [with_openssl="$withval"],
+  [with_openssl="no"])
+if test "x$with_openssl" = "xyes"; then
+  PKG_CHECK_MODULES([OPENSSL], [openssl], [ssl_available=yes], [AC_MSG_ERROR([unable to find OpenSSL (pkg-config)])])
+else
+  ssl_available=no
+fi
 if test "$ssl_available" = "yes"; then
   AC_DEFINE_UNQUOTED(HAVE_OPENSSL, 1, [Defined if OpenSSL is available])
 fi
