{"name": "libgwenhywfar", "version-semver": "5.12.0", "description": "A helper library for networking and security applications and libraries", "homepage": "https://www.aquamaniac.de/rdm/", "supports": "!windows | mingw", "dependencies": [{"name": "gettext", "host": true}, "libgcrypt", "libgnutls", {"name": "vcpkg-cmake-config", "host": true}, {"name": "vcpkg-make", "host": true}], "default-features": ["cpp", "libxml2"], "features": {"cpp": {"description": "A C++ binding for the C module GWEN_GUI"}, "libxml2": {"description": "Enables libXML2-depending functionality", "dependencies": [{"name": "libxml2", "default-features": false}]}, "openssl": {"description": "OpenSSL support in gct-tool", "supports": "!arm64", "dependencies": ["openssl"]}, "qt5": {"description": "Qt bindings", "supports": "!(osx & static)", "dependencies": [{"name": "qt5-base", "default-features": false}]}}}