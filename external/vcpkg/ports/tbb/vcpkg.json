{"$schema": "https://raw.githubusercontent.com/microsoft/vcpkg-tool/main/docs/vcpkg.schema.json", "name": "tbb", "version": "2022.0.0", "port-version": 2, "description": "Intel's Threading Building Blocks.", "homepage": "https://github.com/oneapi-src/oneTBB", "license": "Apache-2.0", "supports": "!uwp", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "default-features": [{"name": "hwl<PERSON>", "platform": "!static & !osx & !uwp"}], "features": {"hwloc": {"description": "Builds TBB with TBBBind support for Hybrid CPUs or NUMA architectures.", "supports": "!static & !osx & !uwp", "dependencies": ["hwl<PERSON>"]}}}