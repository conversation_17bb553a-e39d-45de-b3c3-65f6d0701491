diff --git a/cmake/thirdParties.cmake b/cmake/thirdParties.cmake
index 41d8529..392d455 100644
--- a/cmake/thirdParties.cmake
+++ b/cmake/thirdParties.cmake
@@ -19,7 +19,7 @@ if(MATIO_WITH_HDF5)
         endif()
         set(HDF5_FOUND TRUE)
     else()
-        find_package(HDF5)
+        find_package(HDF5 CONFIG REQUIRED)
         if(HDF5_FOUND)
             set(HDF_MIN_VER 1.8)
             if(HDF5_VERSION VERSION_LESS ${HDF_MIN_VER})
@@ -44,9 +44,12 @@ if(HDF5_FOUND)
     elseif(TARGET hdf5)
         # target from hdf5 1.8 config
         target_link_libraries(MATIO::HDF5 INTERFACE hdf5)
-    elseif(TARGET HDF5::HDF5)
+    elseif(TARGET hdf5::hdf5-shared)
         # target defined in CMake FindHDF5 (since 3.19)
-        target_link_libraries(MATIO::HDF5 INTERFACE HDF5::HDF5)
+        target_link_libraries(MATIO::HDF5 INTERFACE hdf5::hdf5-shared)
+    elseif(TARGET hdf5::hdf5-static)
+        # target defined in CMake FindHDF5 (since 3.19)
+        target_link_libraries(MATIO::HDF5 INTERFACE hdf5-static)
     else()
         # results from CMake FindHDF5
         set_target_properties(MATIO::HDF5 PROPERTIES
