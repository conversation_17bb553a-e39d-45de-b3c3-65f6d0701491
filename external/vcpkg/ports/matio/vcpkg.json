{"name": "matio", "version": "1.5.28", "description": "MATLAB MAT File I/O Library", "homepage": "https://github.com/tbeu/matio", "license": "BSD-2-<PERSON><PERSON>", "dependencies": [{"name": "vcpkg-cmake", "host": true}], "default-features": ["mat73", "zlib"], "features": {"extended-sparse": {"description": "Enable extended sparse matrix data types not supported in MATLAB"}, "mat73": {"description": "Enable support for version 7.3 MAT files using the HDF5 library", "dependencies": [{"name": "hdf5", "default-features": false}, {"name": "matio", "default-features": false, "features": ["zlib"]}]}, "pic": {"description": "Enable position-independent code (PIC), i.e., compilation with the -fPIC flag"}, "zlib": {"description": "Check for zlib library", "dependencies": ["zlib"]}}}