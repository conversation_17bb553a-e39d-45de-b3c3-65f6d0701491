diff --git a/secblock.h b/secblock.h
index 5ab920f9..74d939cf 100644
--- a/secblock.h
+++ b/secblock.h
@@ -270,7 +270,7 @@ public:
 	/// \details VS.NET STL enforces the policy of "All STL-compliant allocators
 	///  have to provide a template class member called rebind".
     template <class V> struct rebind { typedef AllocatorWithCleanup<V, T_Align16> other; };
-#if (CRYPTOPP_MSC_VERSION >= 1500)
+#if (CRYPTOPP_MSC_VERSION >= 1500) || defined(__clang__)
 	AllocatorWithCleanup() {}
 	template <class V, bool A> AllocatorWithCleanup(const AllocatorWithCleanup<V, A> &) {}
 #endif
diff --git a/zdeflate.cpp b/zdeflate.cpp
index b3514b55..20717c24 100644
--- a/zdeflate.cpp
+++ b/zdeflate.cpp
@@ -413,7 +413,7 @@ unsigned int Deflator::LongestMatch(unsigned int &bestMatch) const
 		{
 			CRYPTOPP_ASSERT(scan[2] == match[2]);
 			unsigned int len = (unsigned int)(
-#if defined(_STDEXT_BEGIN) && !(defined(CRYPTOPP_MSC_VERSION) && (CRYPTOPP_MSC_VERSION < 1400 || CRYPTOPP_MSC_VERSION >= 1600)) && !defined(_STLPORT_VERSION)
+#if defined(_STDEXT_BEGIN) && !(defined(CRYPTOPP_MSC_VERSION) && (CRYPTOPP_MSC_VERSION < 1400 || CRYPTOPP_MSC_VERSION >= 1600)) && !defined(_STLPORT_VERSION) && !defined(__clang__)
 				stdext::unchecked_mismatch
 #else
 				std::mismatch
