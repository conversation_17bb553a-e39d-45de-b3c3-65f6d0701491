diff --git a/config_cxx.h b/config_cxx.h
index ffd57ad..353d7ce 100644
--- a/config_cxx.h
+++ b/config_cxx.h
@@ -217,7 +217,7 @@
 // Also see https://github.com/weidai11/cryptopp/issues/980. I'm not sure what
 // to do when the compiler defines __cpp_lib_uncaught_exceptions but the platform
 // does not support std::uncaught_exceptions. What was Apple thinking???
-#if defined(__clang__)
+#if defined(__clang__) && !defined(CRYPTOPP_MSC_VERSION)
 # if __EXCEPTIONS && __has_feature(cxx_exceptions)
 #  if __cpp_lib_uncaught_exceptions >= 201411L
 #   define CRYPTOPP_CXX17_UNCAUGHT_EXCEPTIONS 1
diff --git a/config_os.h b/config_os.h
index 0994563..4546585 100644
--- a/config_os.h
+++ b/config_os.h
@@ -29,7 +29,7 @@
 // https://www.cryptopp.com/wiki/Release_Process#Self_Tests
 // The problems with Clang pretending to be other compilers is
 // discussed at http://github.com/weidai11/cryptopp/issues/147.
-#if (defined(_MSC_VER) && defined(__clang__))
+#if (defined(_MSC_VER) && _MSC_VER < 1930 && defined(__clang__))
 # error: "Unsupported configuration"
 #endif
 
@@ -126,6 +126,7 @@
 #endif
 
 #ifdef CRYPTOPP_WIN32_AVAILABLE
+#include <winapifamily.h>
 # if !defined(WINAPI_FAMILY)
 #	define THREAD_TIMER_AVAILABLE
 # elif defined(WINAPI_FAMILY)
