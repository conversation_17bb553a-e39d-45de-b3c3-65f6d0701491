diff --git a/angelscript/projects/cmake/CMakeLists.txt b/angelscript/projects/cmake/CMakeLists.txt
index 7c800c5..982ad8b 100644
--- a/angelscript/projects/cmake/CMakeLists.txt
+++ b/angelscript/projects/cmake/CMakeLists.txt
@@ -145,7 +145,7 @@ endif()
 
 # Don't override the default library output path to avoid conflicts when building for multiple target platforms
 #set(LIBRARY_OUTPUT_PATH ${PROJECT_SOURCE_DIR}/../../lib)
-target_link_libraries(${ANGELSCRIPT_LIBRARY_NAME} Threads::Threads)
+target_link_libraries(${ANGELSCRIPT_LIBRARY_NAME} PRIVATE Threads::Threads)
 
 set_target_properties(${ANGELSCRIPT_LIBRARY_NAME} PROPERTIES VERSION ${PROJECT_VERSION})
 
