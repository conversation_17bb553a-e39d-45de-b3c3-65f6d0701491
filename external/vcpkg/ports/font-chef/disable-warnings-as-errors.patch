diff --git a/src/font-chef/CMakeLists.txt b/src/font-chef/CMakeLists.txt
--- a/src/font-chef/CMakeLists.txt
+++ b/src/font-chef/CMakeLists.txt
@@ -68,7 +68,7 @@ target_include_directories(font-chef++ INTERFACE
 if (NOT CMAKE_BUILD_TYPE MATCHES "Release")
   target_compile_options(font-chef PRIVATE
     $<$<OR:$<C_COMPILER_ID:Clang>,$<C_COMPILER_ID:GNU>>:-Wall -Wextra -pedantic -fvisibility=hidden -Werror>
-    $<$<C_COMPILER_ID:MSVC>:/W3 /WX /wd4820 /wd4668 /wd4204>
+    $<$<C_COMPILER_ID:MSVC>:/W3 /wd4820 /wd4668 /wd4204>
   )
 endif()
 
