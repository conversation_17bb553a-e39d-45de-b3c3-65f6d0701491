{"name": "joltphysics", "version": "5.3.0", "description": "A multi core friendly rigid body physics and collision detection library suitable for games and VR applications", "homepage": "https://github.com/jrouwe/JoltPhysics", "license": "MIT", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"debugrenderer": {"description": "Enable debug renderer in Debug and Release builds"}, "profiler": {"description": "Enable the profiler in Debug and Release builds"}, "rtti": {"description": "Enable C++ RTTI"}}}