{"name": "cityhash", "version-date": "2013-01-08", "port-version": 3, "description": "CityHash, a family of hash functions for strings.", "homepage": "https://github.com/google/cityhash", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"sse": {"description": "Build CityHash variants that depend on the _mm_crc32_u64 intrinsic.", "supports": "x64 | (x86 & !windows)"}}}