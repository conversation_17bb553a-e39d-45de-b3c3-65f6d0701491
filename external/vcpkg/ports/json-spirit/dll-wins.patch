diff --git a/.travis.yml b/.travis.yml
index e5b56eb..87f2926 100644
--- a/.travis.yml
+++ b/.travis.yml
@@ -14,4 +14,4 @@ script:
   - cmake --build . --target all --
 
 after_script:
-  - ctest
+  - ctest 
diff --git a/CMakeLists.txt b/CMakeLists.txt
index fb24877..a10a49f 100755
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -60,15 +60,17 @@ endif()
 
 message(${CMAKE_INSTALL_INCLUDEDIR})
 
-
+
 set(CPACK_PACKAGE_VERSION_MAJOR 4)
 set(CPACK_PACKAGE_VERSION_MINOR 0)
 set(CPACK_PACKAGE_VERSION_PATCH 8)
 set(CPACK_PACKAGE_DESCRIPTION "A C++ JSON Parser/Generator Implemented with Boost Spirit")
 include(CPack)
-
+
+set(EXPORT_HEADERS )
 INSTALL(
   FILES
+  json_spirit/json_spirit_global.h
   json_spirit/json_spirit.h
   json_spirit/json_spirit_error_position.h
   json_spirit/json_spirit_reader.h
@@ -80,4 +82,4 @@ INSTALL(
   json_spirit/json_spirit_writer_template.h
   json_spirit/json_spirit_writer_options.h
   DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}/json_spirit)
-
+
diff --git a/json_spirit/CMakeLists.txt b/json_spirit/CMakeLists.txt
index 23da64c..10d4e2b 100755
--- a/json_spirit/CMakeLists.txt
+++ b/json_spirit/CMakeLists.txt
@@ -15,13 +15,19 @@ SET(JSON_SPIRIT_SRCS
 FIND_PACKAGE(Boost 1.34 REQUIRED)
 INCLUDE_DIRECTORIES(${Boost_INCLUDE_DIR})
 
-
+
 OPTION(BUILD_STATIC_LIBS "Build static libraries" OFF)
 
 if(BUILD_STATIC_LIBS)
   ADD_LIBRARY(json_spirit STATIC ${JSON_SPIRIT_SRCS})
-  install(TARGETS json_spirit ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR})
+  install(TARGETS json_spirit ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR})  
 else(BUILD_STATIC_LIBS)
   ADD_LIBRARY(json_spirit SHARED ${JSON_SPIRIT_SRCS})
-  install(TARGETS json_spirit LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR})
+  if(WIN32)
+    install(TARGETS json_spirit RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR})
+    install(TARGETS json_spirit ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR})  
+  else()
+    install(TARGETS json_spirit LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR})
+  endif()
+  
 endif(BUILD_STATIC_LIBS)
diff --git a/json_spirit/json_spirit_global.h b/json_spirit/json_spirit_global.h
new file mode 100644
index 0000000..07a7d2c
--- /dev/null
+++ b/json_spirit/json_spirit_global.h
@@ -0,0 +1,18 @@
+﻿#ifndef JSON_SPIRIT_GLOBAL_H
+#define JSON_SPIRIT_GLOBAL_H
+
+#if (defined _WIN32) || (defined _WIN64)
+    #define DECL_EXPORT __declspec(dllexport)
+    #define DECL_IMPORT __declspec(dllimport)
+#else
+    #define DECL_EXPORT
+    #define DECL_IMPORT
+#endif
+
+#if defined(json_spirit_EXPORTS)
+#  define json_spirit_EXPORT DECL_EXPORT
+#else
+#  define json_spirit_EXPORT DECL_IMPORT
+#endif
+
+#endif
\ No newline at end of file
diff --git a/json_spirit/json_spirit_reader.h b/json_spirit/json_spirit_reader.h
index a6cfac7..50cc6f5 100755
--- a/json_spirit/json_spirit_reader.h
+++ b/json_spirit/json_spirit_reader.h
@@ -11,6 +11,7 @@
 # pragma once
 #endif
 
+#include "json_spirit_global.h"
 #include "json_spirit_value.h"
 #include "json_spirit_error_position.h"
 #include <iostream>
@@ -20,43 +21,43 @@ namespace json_spirit
     // functions to reads a JSON values
 
 #ifdef JSON_SPIRIT_VALUE_ENABLED
-    bool read( const std::string& s, Value& value );
-    bool read( std::istream& is,     Value& value );
-    bool read( std::string::const_iterator& begin, std::string::const_iterator end, Value& value );
+    json_spirit_EXPORT bool read( const std::string& s, Value& value );
+    json_spirit_EXPORT bool read( std::istream& is,     Value& value );
+    json_spirit_EXPORT bool read( std::string::const_iterator& begin, std::string::const_iterator end, Value& value );
 
-    void read_or_throw( const std::string& s, Value& value );  
-    void read_or_throw( std::istream& is,     Value& value );
-    void read_or_throw( std::string::const_iterator& begin, std::string::const_iterator end, Value& value );
+    json_spirit_EXPORT void read_or_throw( const std::string& s, Value& value );  
+    json_spirit_EXPORT void read_or_throw( std::istream& is,     Value& value );
+    json_spirit_EXPORT void read_or_throw( std::string::const_iterator& begin, std::string::const_iterator end, Value& value );
 #endif
 
 #if defined( JSON_SPIRIT_WVALUE_ENABLED ) && !defined( BOOST_NO_STD_WSTRING )
-    bool read( const std::wstring& s, wValue& value );
-    bool read( std::wistream&  is,    wValue& value );
-    bool read( std::wstring::const_iterator& begin, std::wstring::const_iterator end, wValue& value );    
+    json_spirit_EXPORT bool read( const std::wstring& s, wValue& value );
+    json_spirit_EXPORT bool read( std::wistream&  is,    wValue& value );
+    json_spirit_EXPORT bool read( std::wstring::const_iterator& begin, std::wstring::const_iterator end, wValue& value );    
 
-    void read_or_throw( const std::wstring& s, wValue& value );
-    void read_or_throw( std::wistream& is,     wValue& value );
-    void read_or_throw( std::wstring::const_iterator& begin, std::wstring::const_iterator end, wValue& value );
+    json_spirit_EXPORT void read_or_throw( const std::wstring& s, wValue& value );
+    json_spirit_EXPORT void read_or_throw( std::wistream& is,     wValue& value );
+    json_spirit_EXPORT void read_or_throw( std::wstring::const_iterator& begin, std::wstring::const_iterator end, wValue& value );
 #endif
 
 #ifdef JSON_SPIRIT_MVALUE_ENABLED
-    bool read( const std::string& s, mValue& value );
-    bool read( std::istream& is,     mValue& value );
-    bool read( std::string::const_iterator& begin, std::string::const_iterator end, mValue& value );
+    json_spirit_EXPORT bool read( const std::string& s, mValue& value );
+    json_spirit_EXPORT bool read( std::istream& is,     mValue& value );
+    json_spirit_EXPORT bool read( std::string::const_iterator& begin, std::string::const_iterator end, mValue& value );
 
-    void read_or_throw( const std::string& s, mValue& value );  
-    void read_or_throw( std::istream& is,     mValue& value );
-    void read_or_throw( std::string::const_iterator& begin, std::string::const_iterator end, mValue& value );
+    json_spirit_EXPORT void read_or_throw( const std::string& s, mValue& value );  
+    json_spirit_EXPORT void read_or_throw( std::istream& is,     mValue& value );
+    json_spirit_EXPORT void read_or_throw( std::string::const_iterator& begin, std::string::const_iterator end, mValue& value );
 #endif
 
 #if defined( JSON_SPIRIT_WMVALUE_ENABLED ) && !defined( BOOST_NO_STD_WSTRING )
-    bool read( const std::wstring& s, wmValue& value );
-    bool read( std::wistream& is,     wmValue& value );
-    bool read( std::wstring::const_iterator& begin, std::wstring::const_iterator end, wmValue& value );    
+    json_spirit_EXPORT bool read( const std::wstring& s, wmValue& value );
+    json_spirit_EXPORT bool read( std::wistream& is,     wmValue& value );
+    json_spirit_EXPORT bool read( std::wstring::const_iterator& begin, std::wstring::const_iterator end, wmValue& value );    
 
-    void read_or_throw( const std::wstring& s, wmValue& value );
-    void read_or_throw( std::wistream& is,     wmValue& value );
-    void read_or_throw( std::wstring::const_iterator& begin, std::wstring::const_iterator end, wmValue& value );
+    json_spirit_EXPORT void read_or_throw( const std::wstring& s, wmValue& value );
+    json_spirit_EXPORT void read_or_throw( std::wistream& is,     wmValue& value );
+    json_spirit_EXPORT void read_or_throw( std::wstring::const_iterator& begin, std::wstring::const_iterator end, wmValue& value );
 #endif
 }
 
diff --git a/json_spirit/json_spirit_writer.h b/json_spirit/json_spirit_writer.h
index 2722780..f60dc07 100755
--- a/json_spirit/json_spirit_writer.h
+++ b/json_spirit/json_spirit_writer.h
@@ -11,6 +11,7 @@
 # pragma once
 #endif
 
+#include "json_spirit_global.h"
 #include "json_spirit_value.h"
 #include "json_spirit_writer_options.h"
 #include <iostream>
@@ -20,44 +21,44 @@ namespace json_spirit
     // these functions to convert JSON Values to text
 
 #ifdef JSON_SPIRIT_VALUE_ENABLED
-    void         write( const Value&  value, std::ostream&  os, unsigned int options = 0 );
-    std::string  write( const Value&  value, unsigned int options = 0 );
+    json_spirit_EXPORT void         write( const Value&  value, std::ostream&  os, unsigned int options = 0 );
+    json_spirit_EXPORT std::string  write( const Value&  value, unsigned int options = 0 );
 #endif
 
 #ifdef JSON_SPIRIT_MVALUE_ENABLED
-    void         write( const mValue& value, std::ostream&  os, unsigned int options = 0 );
-    std::string  write( const mValue& value, unsigned int options = 0 );
+    json_spirit_EXPORT void         write( const mValue& value, std::ostream&  os, unsigned int options = 0 );
+    json_spirit_EXPORT std::string  write( const mValue& value, unsigned int options = 0 );
 #endif
 
 #if defined( JSON_SPIRIT_WVALUE_ENABLED ) && !defined( BOOST_NO_STD_WSTRING )
-    void         write( const wValue&  value, std::wostream& os, unsigned int options = 0 );
-    std::wstring write( const wValue&  value, unsigned int options = 0 );
+    json_spirit_EXPORT void         write( const wValue&  value, std::wostream& os, unsigned int options = 0 );
+    json_spirit_EXPORT std::wstring write( const wValue&  value, unsigned int options = 0 );
 #endif
 
 #if defined( JSON_SPIRIT_WMVALUE_ENABLED ) && !defined( BOOST_NO_STD_WSTRING )
-    void         write( const wmValue& value, std::wostream& os, unsigned int options = 0 );
-    std::wstring write( const wmValue& value, unsigned int options = 0 );
+    json_spirit_EXPORT void         write( const wmValue& value, std::wostream& os, unsigned int options = 0 );
+    json_spirit_EXPORT std::wstring write( const wmValue& value, unsigned int options = 0 );
 #endif
 
     // these "formatted" versions of the "write" functions are the equivalent of the above functions
     // with option "pretty_print"
     
 #ifdef JSON_SPIRIT_VALUE_ENABLED
-    void         write_formatted( const Value& value, std::ostream&  os );
-    std::string  write_formatted( const Value& value );
+    json_spirit_EXPORT void         write_formatted( const Value& value, std::ostream&  os );
+    json_spirit_EXPORT std::string  write_formatted( const Value& value );
 #endif
 #ifdef JSON_SPIRIT_MVALUE_ENABLED
-    void         write_formatted( const mValue& value, std::ostream&  os );
-    std::string  write_formatted( const mValue& value );
+    json_spirit_EXPORT void         write_formatted( const mValue& value, std::ostream&  os );
+    json_spirit_EXPORT std::string  write_formatted( const mValue& value );
 #endif
 
 #if defined( JSON_SPIRIT_WVALUE_ENABLED ) && !defined( BOOST_NO_STD_WSTRING )
-    void         write_formatted( const wValue& value, std::wostream& os );
-    std::wstring write_formatted( const wValue& value );
+    json_spirit_EXPORT void         write_formatted( const wValue& value, std::wostream& os );
+    json_spirit_EXPORT std::wstring write_formatted( const wValue& value );
 #endif
 #if defined( JSON_SPIRIT_WMVALUE_ENABLED ) && !defined( BOOST_NO_STD_WSTRING )
-    void         write_formatted( const wmValue& value, std::wostream& os );
-    std::wstring write_formatted( const wmValue& value );
+    json_spirit_EXPORT void         write_formatted( const wmValue& value, std::wostream& os );
+    json_spirit_EXPORT std::wstring write_formatted( const wmValue& value );
 #endif
 }
 
diff --git a/json_test/json_spirit_value_test.cpp b/json_test/json_spirit_value_test.cpp
index e055dae..faab022 100755
--- a/json_test/json_spirit_value_test.cpp
+++ b/json_test/json_spirit_value_test.cpp
@@ -381,9 +381,15 @@ namespace
         Container_constructor_runner()
         {
             vector< double > vd = list_of( 1.2 )( 1.3 );  test_container_constructor( vd );
+            #if __cplusplus >= 201103L || (defined(_MSC_VER) && _MSC_VER >= 1900)
+            vector< int >    vi = { 1 };                  test_container_constructor( vi );
+                             vi = { 1, 2 };               test_container_constructor( vi );
+                             vi = { 1, 2, 3 };            test_container_constructor( vi );
+            #else
             vector< int >    vi = list_of( 1 );           test_container_constructor( vi );
                              vi = list_of( 1 )( 2 );      test_container_constructor( vi );
                              vi = list_of( 1 )( 2 )( 3 ); test_container_constructor( vi );
+            #endif                             
             list< double >   ld = list_of( 1.2 )( 1.3 );  test_container_constructor( ld );
             list< int >      li = list_of( 1 );           test_container_constructor( li );
                              li = list_of( 1 )( 2 );      test_container_constructor( li );
@@ -447,9 +453,15 @@ namespace
             }
             
             vector< double > vd = list_of( 1.2 )( 1.3 );   test_variant_array_constructor< double > ( vd );
+            #if __cplusplus >= 201103L || (defined(_MSC_VER) && _MSC_VER >= 1900)
+            vector< int >    vi = { 1 };                   test_variant_array_constructor< int >( vi );
+                             vi = { 1, 2 };                test_variant_array_constructor< int >( vi );
+                             vi = { 1, 2, 3 };             test_variant_array_constructor< int >( vi );
+            #else
             vector< int >    vi = list_of( 1 );            test_variant_array_constructor< int >( vi );
                              vi = list_of( 1 )( 2 );       test_variant_array_constructor< int >( vi );
                              vi = list_of( 1 )( 2 )( 3 );  test_variant_array_constructor< int >( vi );
+            #endif
             list< double >   ld = list_of( 1.2 )( 1.3 );   test_variant_array_constructor< double >( ld );
             list< int >      li = list_of( 1 );            test_variant_array_constructor< int >( li );
                              li = list_of( 1 )( 2 );       test_variant_array_constructor< int >( li );
