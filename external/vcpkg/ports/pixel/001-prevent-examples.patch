diff --git a/CMakeLists.txt b/CMakeLists.txt
index b910231..71f7ddf 100755
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -34,6 +34,8 @@ else()
   target_link_libraries(pixel PUBLIC SDL2::SDL2)
 endif()
 
+option(BUILD_EXAMPLES "Build examples" OFF)
+if(BUILD_EXAMPLES)
 add_executable(image_swap examples/image_swap/src/image_swap.cpp)
 target_link_libraries(image_swap PRIVATE pixel)
 add_executable(randomdots examples/randomdots/src/randomdots.cpp)
@@ -50,7 +52,11 @@ add_executable(simple examples/simple/src/simple.cpp)
 target_link_libraries(simple PRIVATE pixel)
 add_executable(starfield examples/starfield/src/starfield.cpp)
 target_link_libraries(starfield PRIVATE pixel)
+endif()
 
+if(MSVC)
+  add_compile_definitions(_CRT_SECURE_NO_WARNINGS)
+endif()
 
 # Install Section
 include(GNUInstallDirs)
@@ -87,12 +93,6 @@ install(
   DESTINATION  ${CMAKE_INSTALL_PREFIX}
   )
 
-install(
-  FILES
-  ${PROJECT_SOURCE_DIR}/LICENSE
-  DESTINATION  ${CMAKE_INSTALL_DATADIR}/pixel/copyright
-  )
-
 install(
   FILES
   ${PROJECT_BINARY_DIR}/pixelConfig.cmake
