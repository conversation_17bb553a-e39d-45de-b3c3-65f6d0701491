{"name": "serde<PERSON>", "version": "*******", "description": "c++ 17 universal serialize deserialize library like rust serde, support libraries: [n<PERSON><PERSON>_<PERSON><PERSON>, fmt, yaml-cpp, toml11, rapidjson]", "homepage": "https://github.com/injae/serdepp", "license": "MIT", "supports": "!(osx & arm)", "dependencies": ["magic-enum", "nameof", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}]}