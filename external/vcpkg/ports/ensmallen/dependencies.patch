diff --git a/CMake/ensmallen-config.cmake.in b/CMake/ensmallen-config.cmake.in
index 3a9a93c..a067e08 100644
--- a/CMake/ensmallen-config.cmake.in
+++ b/CMake/ensmallen-config.cmake.in
@@ -1,4 +1,6 @@
 @PACKAGE_INIT@
+include(CMakeFindDependencyMacro)
+find_dependency(Armadillo CONFIG)
 
 include(${CMAKE_CURRENT_LIST_DIR}/@TARGETS_EXPORT_NAME@.cmake)
 check_required_components(ensmallen)
diff --git a/CMakeLists.txt b/CMakeLists.txt
index 205fe1b..7fcd207 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -57,8 +57,8 @@ if(USE_OPENMP)
 endif()
 
 # Find Armadillo and link it.
-find_package(Armadillo 9.800.0 REQUIRED)
-target_link_libraries(ensmallen INTERFACE Armadillo::Armadillo)
+find_package(Armadillo CONFIG REQUIRED)
+target_link_libraries(ensmallen INTERFACE armadillo)
 
 # Set helper variables for creating the version, config and target files.
 include(CMakePackageConfigHelpers)
