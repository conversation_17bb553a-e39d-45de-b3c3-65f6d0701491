diff --git a/src/tools/msvc/pgbison.pl b/src/tools/msvc/pgbison.pl
index 25df669..373bedd 100644
--- a/src/tools/msvc/pgbison.pl
+++ b/src/tools/msvc/pgbison.pl
@@ -13,7 +13,7 @@ use File::Basename;
 
 do './src/tools/msvc/buildenv.pl' if -e 'src/tools/msvc/buildenv.pl';
 
-my ($bisonver) = `bison -V`;                 # grab first line
+my ($bisonver) = `win_bison -V`;    # grab first line
 $bisonver = (split(/\s+/, $bisonver))[3];    # grab version number
 
 unless ($bisonver ge '2.3')
@@ -51,5 +51,5 @@ my $headerflag = ($make =~ /^$basetarg:\s+BISONFLAGS\b.*-d/m ? '-d' : '');
 
 my $nodep = $bisonver ge '3.0' ? "-Wno-deprecated" : "";
 
-system("bison $nodep $headerflag $input -o $output");
+system("win_bison $nodep $headerflag $input -o $output");
 exit $? >> 8;
diff --git a/src/tools/msvc/pgflex.pl b/src/tools/msvc/pgflex.pl
index c308a08..0807ce7 100644
--- a/src/tools/msvc/pgflex.pl
+++ b/src/tools/msvc/pgflex.pl
@@ -16,7 +16,7 @@ $ENV{CYGWIN} = 'nodosfilewarning';
 
 do './src/tools/msvc/buildenv.pl' if -e 'src/tools/msvc/buildenv.pl';
 
-my ($flexver) = `flex -V`;    # grab first line
+my ($flexver) = `win_flex -V`;    # grab first line
 $flexver = (split(/\s+/, $flexver))[1];
 $flexver =~ s/[^0-9.]//g;
 my @verparts = split(/\./, $flexver);
@@ -52,7 +52,7 @@ close($mf);
 my $basetarg = basename($output);
 my $flexflags = ($make =~ /^$basetarg:\s*FLEXFLAGS\s*=\s*(\S.*)/m ? $1 : '');
 
-system("flex $flexflags -o$output $input");
+system("win_flex $flexflags -o$output $input");
 if ($? == 0)
 {
 
