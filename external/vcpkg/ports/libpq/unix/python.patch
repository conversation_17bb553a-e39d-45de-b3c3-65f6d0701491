diff --git a/configure.ac b/configure.ac
index 7f97248992..33b6c84fc4 100644
--- a/configure.ac
+++ b/configure.ac
@@ -891,7 +891,9 @@ AC_SUBST(with_perl)
 # Optionally build Python modules (PL/Python)
 #
 AC_MSG_CHECKING([whether to build Python modules])
-PGAC_ARG_BOOL(with, python, no, [build Python modules (PL/Python)])
+PGAC_ARG_OPTARG(with, python, [PYTHON_VERSION], [build Python modules (PL/Python)],
+                [],
+                [python_version=$withval])
 AC_MSG_RESULT([$with_python])
 AC_SUBST(with_python)
 
@@ -1214,7 +1216,18 @@ fi
 
 if test "$with_python" = yes; then
   PGAC_PATH_PYTHON
-  PGAC_CHECK_PYTHON_EMBED_SETUP
+  python_majorversion=3
+  PKG_CHECK_MODULES(PYTHON_EMBED, python-${python_version}-embed)
+  python_includespec="${PYTHON_EMBED_CFLAGS}"
+  python_libdir=[$(echo " ${PYTHON_EMBED_LIBS}" | sed -e 's/\( -L[^ ]*\).*/\1/' -e 's/^.* -L//')]
+  python_libspec="${PYTHON_EMBED_LIBS}"
+  python_additional_libs=""
+  AC_SUBST(python_majorversion)
+  AC_SUBST(python_version)
+  AC_SUBST(python_includespec)
+  AC_SUBST(python_libdir)
+  AC_SUBST(python_libspec)
+  AC_SUBST(python_additional_libs)
 fi
 
 if test x"$cross_compiling" = x"yes" && test -z "$with_system_tzdata"; then
