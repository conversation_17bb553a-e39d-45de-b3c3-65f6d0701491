diff --git a/src/bin/Makefile b/src/bin/Makefile
index 7f9dde9..bc6d835 100644
--- a/src/bin/Makefile
+++ b/src/bin/Makefile
@@ -13,29 +13,20 @@ subdir = src/bin
 top_builddir = ../..
 include $(top_builddir)/src/Makefile.global
 
+.NOTPARALLEL:
+# incl. https://www.postgresql.org/docs/current/reference-client.html
+# excl. https://www.postgresql.org/docs/current/reference-server.html
 SUBDIRS = \
-	initdb \
 	pg_amcheck \
-	pg_archivecleanup \
 	pg_basebackup \
-	pg_checksums \
 	pg_config \
-	pg_controldata \
-	pg_ctl \
 	pg_dump \
-	pg_resetwal \
-	pg_rewind \
-	pg_test_fsync \
-	pg_test_timing \
-	pg_upgrade \
 	pg_verifybackup \
-	pg_waldump \
 	pgbench \
 	psql \
 	scripts
 
 ifeq ($(PORTNAME), win32)
-SUBDIRS += pgevent
 else
 ALWAYS_SUBDIRS += pgevent
 endif
