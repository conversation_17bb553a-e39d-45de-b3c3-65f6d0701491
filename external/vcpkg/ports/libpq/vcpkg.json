{"name": "libpq", "version": "16.4", "description": "The official database access API of postgresql", "homepage": "https://www.postgresql.org/", "license": "PostgreSQL", "supports": "!uwp", "dependencies": [{"name": "libpq", "default-features": false, "features": ["bonjour"], "platform": "osx"}, {"name": "vcpkg-cmake-get-vars", "host": true}, {"name": "vcpkg-pkgconfig-get-modules", "host": true, "platform": "windows & !mingw"}], "default-features": ["lz4", "openssl", "zlib"], "features": {"all": {"description": "Build all supported features", "dependencies": [{"name": "libpq", "features": ["client", "icu", "xml", "xslt", "zstd"]}, {"name": "libpq", "features": ["bonjour"], "platform": "osx"}, {"name": "libpq", "features": ["nls"], "platform": "!osx"}, {"name": "libpq", "features": ["readline"], "platform": "!windows"}, {"name": "libpq", "features": ["python"], "platform": "!android & !mingw"}, {"name": "libpq", "features": ["tcl"], "platform": "windows & !mingw & !arm"}]}, "bonjour": {"description": "Build with Bonjour support", "supports": "osx"}, "client": {"description": "Build all client tools and libraries"}, "icu": {"description": "Build with support for the ICU library", "dependencies": ["icu"]}, "lz4": {"description": "Use lz4", "dependencies": ["lz4"]}, "nls": {"description": "Native Language Support", "supports": "!osx", "dependencies": ["gettext", {"name": "gettext", "host": true, "features": ["tools"], "platform": "!windows, mingw"}]}, "openssl": {"description": "support for encrypted client connections and random number generation on platforms that do not have \"/dev/urandom\" (except windows)", "dependencies": ["openssl"]}, "python": {"$supports": "!(windows & (static | mingw))", "description": "build the PL/Python server programming language", "dependencies": [{"name": "libpq", "default-features": false, "features": ["client"]}, "python3"]}, "readline": {"description": "Use readline", "supports": "!windows, mingw", "dependencies": ["readline"]}, "tcl": {"$supports": "!(windows & (static | mingw))", "description": "build the PL/Tcl procedural language", "supports": "windows & !mingw", "dependencies": [{"name": "libpq", "default-features": false, "features": ["client"]}, "tcl"]}, "xml": {"description": "Build with libxml", "dependencies": [{"name": "libxml2", "default-features": false}]}, "xslt": {"description": "Build with libxslt", "dependencies": [{"name": "libpq", "default-features": false, "features": ["xml"]}, "libxslt"]}, "zlib": {"description": "Use zlib", "dependencies": ["zlib"]}, "zstd": {"description": "Use zstd", "dependencies": ["zstd"]}}}