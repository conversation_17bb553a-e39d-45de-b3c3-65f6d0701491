<?xml version="1.0" encoding="utf-8"?>
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemDefinitionGroup Condition="'$(Configuration)'=='Debug'">
    <ClCompile>
      <AdditionalOptions>%(AdditionalOptions) @VCPKG_COMBINED_C_FLAGS_DEBUG@</AdditionalOptions>
    </ClCompile>
    <Link>
      <AdditionalOptions>%(AdditionalOptions) @VCPKG_COMBINED_SHARED_LINKER_FLAGS_DEBUG@</AdditionalOptions>
    </Link>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) @VCPKG_COMBINED_STATIC_LINKER_FLAGS_DEBUG@</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)'=='Release'">
    <ClCompile>
      <AdditionalOptions>%(AdditionalOptions) @VCPKG_COMBINED_C_FLAGS_RELEASE@</AdditionalOptions>
    </ClCompile>
    <Link>
      <AdditionalOptions>%(AdditionalOptions) @VCPKG_COMBINED_SHARED_LINKER_FLAGS_RELEASE@</AdditionalOptions>
    </Link>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) @VCPKG_COMBINED_STATIC_LINKER_FLAGS_RELEASE@</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
</Project>
