<?xml version="1.0" encoding="utf-8"?>
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup Condition="'$(Configuration)'=='Debug'">
    <VcpkgIcuLibs>@CURRENT_INSTALLED_DIR@/debug/lib/icuind.lib;@CURRENT_INSTALLED_DIR@/debug/lib/icuucd.lib;@CURRENT_INSTALLED_DIR@/debug/lib/icudtd.lib;</VcpkgIcuLibs>
    <VcpkgLz4Libs>@CURRENT_INSTALLED_DIR@/debug/lib/lz4d.lib</VcpkgLz4Libs>
    <VcpkgNlsLibs>@CURRENT_INSTALLED_DIR@/debug/lib/intl.lib;@CURRENT_INSTALLED_DIR@/debug/lib/iconv.lib;@CURRENT_INSTALLED_DIR@/debug/lib/charset.lib</VcpkgNlsLibs>
    <VcpkgOpensslLibs>@CURRENT_INSTALLED_DIR@/debug/lib/libssl.lib;@CURRENT_INSTALLED_DIR@/debug/lib/libcrypto.lib;crypt32.lib;ws2_32.lib;secur32.lib</VcpkgOpensslLibs>
    <VcpkgPythonLibs>@CURRENT_INSTALLED_DIR@/debug/lib/python3@PYTHON_VERSION_MINOR@_d.lib</VcpkgPythonLibs>
    <VcpkgTcl90Libs>@CURRENT_INSTALLED_DIR@/debug/lib/tcl90g.lib</VcpkgTcl90Libs>
    <VcpkgTcl90sLibs>@CURRENT_INSTALLED_DIR@/debug/lib/tcl90sg.lib</VcpkgTcl90sLibs>
    <VcpkgTcl90sxLibs>@CURRENT_INSTALLED_DIR@/debug/lib/tcl90sgx.lib</VcpkgTcl90sxLibs>
    <VcpkgXmlLibs>@LIBXML2_LIBS_DEBUG@</VcpkgXmlLibs>
    <VcpkgXsltLibs>@LIBXSLT_LIBS_DEBUG@</VcpkgXsltLibs>
    <VcpkgZlibLibs>@CURRENT_INSTALLED_DIR@/debug/lib/zlibd.lib</VcpkgZlibLibs>
    <VcpkgZstdLibs>@CURRENT_INSTALLED_DIR@/debug/lib/zstd.lib</VcpkgZstdLibs>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)'=='Release'">
    <VcpkgIcuLibs>@CURRENT_INSTALLED_DIR@/lib/icuin.lib;@CURRENT_INSTALLED_DIR@/lib/icuuc.lib;@CURRENT_INSTALLED_DIR@/lib/icudt.lib;</VcpkgIcuLibs>
    <VcpkgLz4Libs>@CURRENT_INSTALLED_DIR@/lib/lz4.lib</VcpkgLz4Libs>
    <VcpkgNlsLibs>@CURRENT_INSTALLED_DIR@/lib/intl.lib;@CURRENT_INSTALLED_DIR@/lib/iconv.lib;@CURRENT_INSTALLED_DIR@/lib/charset.lib</VcpkgNlsLibs>
    <VcpkgOpensslLibs>@CURRENT_INSTALLED_DIR@/lib/libssl.lib;@CURRENT_INSTALLED_DIR@/lib/libcrypto.lib;crypt32.lib;ws2_32.lib;secur32.lib</VcpkgOpensslLibs>
    <VcpkgPythonLibs>@CURRENT_INSTALLED_DIR@/lib/python3@PYTHON_VERSION_MINOR@.lib</VcpkgPythonLibs>
    <VcpkgTcl90Libs>@CURRENT_INSTALLED_DIR@/lib/tcl90.lib</VcpkgTcl90Libs>
    <VcpkgTcl90sLibs>@CURRENT_INSTALLED_DIR@/lib/tcl90s.lib</VcpkgTcl90sLibs>
    <VcpkgTcl90sxLibs>@CURRENT_INSTALLED_DIR@/lib/tcl90sx.lib</VcpkgTcl90sxLibs>
    <VcpkgXmlLibs>@LIBXML2_LIBS_RELEASE@</VcpkgXmlLibs>
    <VcpkgXsltLibs>@LIBXSLT_LIBS_RELEASE@</VcpkgXsltLibs>
    <VcpkgZlibLibs>@CURRENT_INSTALLED_DIR@/lib/zlib.lib</VcpkgZlibLibs>
    <VcpkgZstdLibs>@CURRENT_INSTALLED_DIR@/lib/zstd.lib</VcpkgZstdLibs>
  </PropertyGroup>
</Project>
