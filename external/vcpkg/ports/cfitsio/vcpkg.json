{"name": "cfitsio", "version": "3.49", "port-version": 6, "description": "Library of C and Fortran subroutines for reading and writing data files in FITS (Flexible Image Transport System) data format", "homepage": "https://heasarc.gsfc.nasa.gov/fitsio/", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}, "zlib"], "features": {"curl": {"description": "UseCurl", "dependencies": ["curl"]}, "pthreads": {"description": "Thread-safe build (using pthreads)", "dependencies": [{"name": "pthreads", "platform": "windows"}]}}}