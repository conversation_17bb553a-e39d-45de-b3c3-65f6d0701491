diff --git a/CMakeLists.txt b/CMakeLists.txt
index 9e7d2ee..5b37adc 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -24,7 +24,7 @@ OPTION(UseCurl "UseCurl" ON)
 
 set(CMAKE_MODULE_PATH ${CMAKE_MODULE_PATH} "${CMAKE_SOURCE_DIR}")
 set (LIB_DESTINATION "${CMAKE_INSTALL_PREFIX}/lib${LIB_SUFFIX}")
-set (INCLUDE_INSTALL_DIR "${CMAKE_INSTALL_PREFIX}/include/")
+set (INCLUDE_INSTALL_DIR "${CMAKE_INSTALL_PREFIX}/include/cfitsio")
 
 # Define project version
 SET(${PROJECT_NAME}_MAJOR_VERSION 3)
@@ -138,19 +138,31 @@ ELSE()
 ENDIF()
 
 ADD_LIBRARY(${LIB_NAME} ${LIB_TYPE} ${H_FILES} ${SRC_FILES})
-TARGET_LINK_LIBRARIES(${LIB_NAME} ${PTHREADS_LIBRARY} ${M_LIB} ZLIB::ZLIB)
+TARGET_INCLUDE_DIRECTORIES(${LIB_NAME} PUBLIC $<INSTALL_INTERFACE:include/cfitsio>)
+TARGET_LINK_LIBRARIES(${LIB_NAME} PUBLIC ${PTHREADS_LIBRARY} ${M_LIB} ZLIB::ZLIB)
 IF (CURL_FOUND)
-    TARGET_LINK_LIBRARIES(${LIB_NAME} CURL::libcurl)
+    TARGET_LINK_LIBRARIES(${LIB_NAME} PUBLIC CURL::libcurl)
 ENDIF(CURL_FOUND)
 
 SET_TARGET_PROPERTIES(${LIB_NAME} PROPERTIES VERSION ${${PROJECT_NAME}_VERSION} SOVERSION ${${PROJECT_NAME}_MAJOR_VERSION})
-install(TARGETS ${LIB_NAME} DESTINATION ${LIB_DESTINATION})
 install(TARGETS ${LIB_NAME}
+                            EXPORT unofficial-${LIB_NAME}-config
                             RUNTIME DESTINATION bin
                             LIBRARY DESTINATION lib
                             ARCHIVE DESTINATION lib)
 install(FILES ${H_FILES} DESTINATION ${INCLUDE_INSTALL_DIR} COMPONENT Devel)
 
+set(CFITSIO_VERSION 3.49)
+include(CMakePackageConfigHelpers)
+write_basic_package_version_file(unofficial-${LIB_NAME}-version.cmake VERSION ${CFITSIO_VERSION} COMPATIBILITY ExactVersion)
+
+install(FILES ${CMAKE_BINARY_DIR}/unofficial-${LIB_NAME}-version.cmake DESTINATION share/unofficial-${LIB_NAME})
+
+install(
+    EXPORT unofficial-${LIB_NAME}-config
+    DESTINATION share/unofficial-${LIB_NAME}
+)
+
 # Only build test code and executables if building a shared library:
 IF (BUILD_SHARED_LIBS)
 
