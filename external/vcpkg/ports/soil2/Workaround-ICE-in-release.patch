diff --git a/src/SOIL2/SOIL2.c b/src/SOIL2/SOIL2.c
index 3a10885..01bbb23 100644
--- a/src/SOIL2/SOIL2.c
+++ b/src/SOIL2/SOIL2.c
@@ -2027,6 +2027,7 @@ const char*
 	return result_string_pointer;
 }
 
+#pragma optimize("", off)
 unsigned int SOIL_direct_load_DDS_from_memory(
 		const unsigned char *const buffer,
 		int buffer_length,
@@ -2409,6 +2410,7 @@ unsigned int SOIL_direct_load_DDS_from_memory(
 quick_exit:
 	return tex_ID;
 }
+#pragma optimize("", on)
 
 unsigned int SOIL_direct_load_DDS(
 		const char *filename,
