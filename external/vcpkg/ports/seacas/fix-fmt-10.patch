From 5a576de57ee1664d845c83f552f2100cc9303a26 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Wed, 10 May 2023 15:28:53 -0600
Subject: [PATCH] IOSS: Fix enum printing to work with fmt-10

---
 .../seacas/libraries/ioss/src/text_mesh/Iotm_DatabaseIO.C     | 4 ++--
 1 file changed, 2 insertions(+), 2 deletions(-)

diff --git a/packages/seacas/libraries/ioss/src/text_mesh/Iotm_DatabaseIO.C b/packages/seacas/libraries/ioss/src/text_mesh/Iotm_DatabaseIO.C
index b6827aa177..59507085fc 100644
--- a/packages/seacas/libraries/ioss/src/text_mesh/Iotm_DatabaseIO.C
+++ b/packages/seacas/libraries/ioss/src/text_mesh/Iotm_DatabaseIO.C
@@ -1,4 +1,4 @@
-// Copyright(C) 1999-2020, 2022 National Technology & Engineering Solutions
+// Copyright(C) 1999-2020, 2022, 2023 National Technology & Engineering Solutions
 // of Sandia, LLC (NTESS).  Under the terms of Contract DE-NA0003525 with
 // NTESS, the U.S. Government retains certain rights in this software.
 //
@@ -802,7 +802,7 @@ namespace Iotm {
           std::ostringstream errmsg;
           fmt::print(errmsg,
                      "Error: Failed to find entity of type {} with name {} for Assembly {}.\n",
-                     type, members[j], assem->name());
+                     Ioss::Utils::entity_type_to_string(type), members[j], assem->name());
           IOSS_ERROR(errmsg);
         }
       }
