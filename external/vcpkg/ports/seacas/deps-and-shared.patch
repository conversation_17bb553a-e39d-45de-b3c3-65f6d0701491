diff --git a/cmake/TPLs/FindTPLHDF5.cmake b/cmake/TPLs/FindTPLHDF5.cmake
index 3799818f2..3e17b46ac 100644
--- a/cmake/TPLs/FindTPLHDF5.cmake
+++ b/cmake/TPLs/FindTPLHDF5.cmake
@@ -55,7 +55,7 @@
 
 find_package(HDF5 REQUIRED COMPONENTS C HL)
 
-TRIBITS_TPL_FIND_INCLUDE_DIRS_AND_LIBRARIES(HDF5
-  REQUIRED_HEADERS hdf5.h
-  REQUIRED_LIBS_NAMES hdf5_hl hdf5 z
-  )
+tribits_extpkg_create_imported_all_libs_target_and_config_file(
+  HDF5
+  INNER_FIND_PACKAGE_NAME HDF5
+  IMPORTED_TARGETS_FOR_ALL_LIBS HDF5::HDF5 )
diff --git a/cmake/TPLs/FindTPLMETIS.cmake b/cmake/TPLs/FindTPLMETIS.cmake
index 90911a3cc..bb57e3bf6 100644
--- a/cmake/TPLs/FindTPLMETIS.cmake
+++ b/cmake/TPLs/FindTPLMETIS.cmake
@@ -53,8 +53,8 @@
 # ************************************************************************
 # @HEADER
 
-
-TRIBITS_TPL_FIND_INCLUDE_DIRS_AND_LIBRARIES( METIS
-  REQUIRED_HEADERS metis.h
-  REQUIRED_LIBS_NAMES "metis"
-  )
+find_package(metis REQUIRED)
+tribits_extpkg_create_imported_all_libs_target_and_config_file(
+  METIS
+  INNER_FIND_PACKAGE_NAME metis
+  IMPORTED_TARGETS_FOR_ALL_LIBS metis )
diff --git a/cmake/TPLs/FindTPLMatio.cmake b/cmake/TPLs/FindTPLMatio.cmake
index c76713ab9..f5b8bd702 100644
--- a/cmake/TPLs/FindTPLMatio.cmake
+++ b/cmake/TPLs/FindTPLMatio.cmake
@@ -56,7 +56,7 @@ if (${CMAKE_VERSION} GREATER "3.13")
      cmake_policy(SET CMP0074 NEW)
 endif()
 
-find_package(Matio REQUIRED)
+list(APPEND CMAKE_FIND_LIBRARY_PREFIXES lib)
 TRIBITS_TPL_FIND_INCLUDE_DIRS_AND_LIBRARIES( Matio
   REQUIRED_HEADERS matio.h
   REQUIRED_LIBS_NAMES "matio")
diff --git a/cmake/TPLs/FindTPLNetcdf.cmake b/cmake/TPLs/FindTPLNetcdf.cmake
index 2c65d60bc..1a8bacf75 100644
--- a/cmake/TPLs/FindTPLNetcdf.cmake
+++ b/cmake/TPLs/FindTPLNetcdf.cmake
@@ -55,7 +55,7 @@
 
 
 find_package(NetCDF REQUIRED)
-TRIBITS_TPL_FIND_INCLUDE_DIRS_AND_LIBRARIES( Netcdf
-  REQUIRED_HEADERS netcdf.h
-  REQUIRED_LIBS_NAMES netcdf
-  )
+tribits_extpkg_create_imported_all_libs_target_and_config_file(
+  NetCDF
+  INNER_FIND_PACKAGE_NAME NetCDF
+  IMPORTED_TARGETS_FOR_ALL_LIBS NetCDF::NetCDF )
diff --git a/cmake/TPLs/FindTPLZlib.cmake b/cmake/TPLs/FindTPLZlib.cmake
index cc607e9d8..069a37c23 100644
--- a/cmake/TPLs/FindTPLZlib.cmake
+++ b/cmake/TPLs/FindTPLZlib.cmake
@@ -53,8 +53,8 @@
 # ************************************************************************
 # @HEADER
 
-
-TRIBITS_TPL_FIND_INCLUDE_DIRS_AND_LIBRARIES( Zlib
-  REQUIRED_HEADERS zlib.h
-  REQUIRED_LIBS_NAMES  z
-  )
+find_package(ZLIB REQUIRED)
+tribits_extpkg_create_imported_all_libs_target_and_config_file(
+  Zlib
+  INNER_FIND_PACKAGE_NAME ZLIB
+  IMPORTED_TARGETS_FOR_ALL_LIBS ZLIB::ZLIB )
diff --git a/cmake/TPLs/FindTPLfmt.cmake b/cmake/TPLs/FindTPLfmt.cmake
index c6ec212f9..e38054d9a 100644
--- a/cmake/TPLs/FindTPLfmt.cmake
+++ b/cmake/TPLs/FindTPLfmt.cmake
@@ -53,8 +53,8 @@
 # ************************************************************************
 # @HEADER
 
-
-TRIBITS_TPL_FIND_INCLUDE_DIRS_AND_LIBRARIES( fmt
-  REQUIRED_HEADERS fmt/format.h
-  REQUIRED_LIBS_NAMES  fmt
-  )
+find_package(fmt REQUIRED)
+tribits_extpkg_create_imported_all_libs_target_and_config_file(
+  fmt
+  INNER_FIND_PACKAGE_NAME fmt
+  IMPORTED_TARGETS_FOR_ALL_LIBS fmt::fmt )
diff --git a/cmake/tribits/common_tpls/FindTPLCGNS.cmake b/cmake/tribits/common_tpls/FindTPLCGNS.cmake
index 37c329cd4..9d221f64f 100644
--- a/cmake/tribits/common_tpls/FindTPLCGNS.cmake
+++ b/cmake/tribits/common_tpls/FindTPLCGNS.cmake
@@ -45,14 +45,14 @@ if ((CGNS_ALLOW_MODERN AND HDF5_FOUND_MODERN_CONFIG_FILE) OR CGNS_FORCE_MODERN)
   print_var(CGNS_ALLOW_MODERN)
   print_var(CGNS_FORCE_MODERN)
   message("-- Using find_package(CGNS ${minimum_modern_CGNS_version} CONFIG) ...")
-  find_package(CGNS ${minimum_modern_CGNS_version} CONFIG)
+  find_package(cgns CONFIG)
   if (CGNS_FOUND)
     message("-- Found CGNS_CONFIG=${CGNS_CONFIG}")
     message("-- Generating CGNS::all_libs and CGNSConfig.cmake")
     tribits_extpkg_create_imported_all_libs_target_and_config_file(
       CGNS
-      INNER_FIND_PACKAGE_NAME  CGNS
-      IMPORTED_TARGETS_FOR_ALL_LIBS   CGNS::cgns)
+      INNER_FIND_PACKAGE_NAME  cgns
+      IMPORTED_TARGETS_FOR_ALL_LIBS   CGNS::CGNS)
     set(TPL_CGNS_NOT_FOUND FALSE)
   endif()
 
diff --git a/cmake/tribits/common_tpls/FindTPLHDF5.cmake b/cmake/tribits/common_tpls/FindTPLHDF5.cmake
index 716068c28..3d8fc8e76 100644
--- a/cmake/tribits/common_tpls/FindTPLHDF5.cmake
+++ b/cmake/tribits/common_tpls/FindTPLHDF5.cmake
@@ -9,7 +9,7 @@ if (Netcdf_ALLOW_MODERN)
   set(minimum_modern_HDF5_version 1.10.2)
   print_var(Netcdf_ALLOW_MODERN)
   message("-- Using find_package(HDF5 ${minimum_modern_HDF5_version} CONFIG) ...")
-  find_package(HDF5  ${minimum_modern_HDF5_version}  CONFIG)
+  find_package(HDF5)
   if (HDF5_FOUND)
     message("-- Found HDF5_CONFIG=${HDF5_CONFIG}")
     message("-- Generating Netcdf::all_libs and NetcdfConfig.cmake")
@@ -17,7 +17,7 @@ if (Netcdf_ALLOW_MODERN)
     tribits_extpkg_create_imported_all_libs_target_and_config_file(
       HDF5
       INNER_FIND_PACKAGE_NAME  HDF5
-      IMPORTED_TARGETS_FOR_ALL_LIBS   ${HDF5_EXPORT_LIBRARIES})
+      IMPORTED_TARGETS_FOR_ALL_LIBS  hdf5::hdf5)
     set(HDF5_INTERNAL_IS_MODERN TRUE)
   else()
     message("-- Could not find HDF5_CONFIG (FindTPLHDF5.cmake)")
@@ -27,7 +27,7 @@ endif()
 
 set(HDF5_FOUND_MODERN_CONFIG_FILE ${HDF5_INTERNAL_IS_MODERN} CACHE INTERNAL "True if HDF5 was found by the modern method")
 
-if (NOT TARGET HDF5::all_libs)
+if (0)
 
   # First, set up the variables for the (backward-compatible) TriBITS way of
   # finding HDF5.  These are used in case find_package(HDF5 ...) is not called
diff --git a/cmake/tribits/common_tpls/find_modules/FindCGNS.cmake b/cmake/tribits/common_tpls/find_modules/FindCGNS.cmake
index c2c98f4be..9626cdb92 100644
--- a/cmake/tribits/common_tpls/find_modules/FindCGNS.cmake
+++ b/cmake/tribits/common_tpls/find_modules/FindCGNS.cmake
@@ -180,7 +180,7 @@ else(CGNS_LIBRARIES AND CGNS_INCLUDE_DIRS)
         if (EXISTS "${CGNS_LIBRARY_DIR}")
 
             find_library(CGNS_LIBRARY
-                         NAMES cgns
+                         NAMES cgnsdll cgns
                          HINTS ${CGNS_LIBRARY_DIR}
                          NO_DEFAULT_PATH)
 
@@ -196,7 +196,7 @@ else(CGNS_LIBRARIES AND CGNS_INCLUDE_DIRS)
             if (EXISTS "${CGNS_ROOT}" )
 
                 find_library(CGNS_LIBRARY
-                             NAMES cgns
+                             NAMES cgnsdll cgns
                              HINTS ${CGNS_ROOT}
                              PATH_SUFFIXES "lib" "Lib"
                              NO_DEFAULT_PATH)
@@ -210,7 +210,7 @@ else(CGNS_LIBRARIES AND CGNS_INCLUDE_DIRS)
         else()
 
             find_library(CGNS_LIBRARY
-                         NAMES cgns
+                         NAMES cgnsdll cgns
                          PATH_SUFFIXES ${cgns_lib_suffixes})
 
         endif()
diff --git a/packages/seacas/libraries/aprepro_lib/CMakeLists.txt b/packages/seacas/libraries/aprepro_lib/CMakeLists.txt
index ef391f1c6..a4869415b 100644
--- a/packages/seacas/libraries/aprepro_lib/CMakeLists.txt
+++ b/packages/seacas/libraries/aprepro_lib/CMakeLists.txt
@@ -104,6 +104,10 @@ if (${CMAKE_PROJECT_NAME} STREQUAL "Seacas")
    endif()
 endif()
 
+if(BUILD_SHARED_LIBS)
+    set_target_properties(aprepro_lib PROPERTIES WINDOWS_EXPORT_ALL_SYMBOLS ON)
+endif()
+
 if (${PACKAGE_NAME}_ENABLE_TESTS)
   TRIBITS_ADD_EXECUTABLE(aprepro_lib_test NOEXEPREFIX NOEXESUFFIX INSTALLABLE SOURCES apr_test.cc LINKER_LANGUAGE CXX)
 
diff --git a/packages/seacas/libraries/chaco/CMakeLists.txt b/packages/seacas/libraries/chaco/CMakeLists.txt
index 5f43a4e0c..ff8570ce3 100644
--- a/packages/seacas/libraries/chaco/CMakeLists.txt
+++ b/packages/seacas/libraries/chaco/CMakeLists.txt
@@ -56,5 +56,7 @@ TRIBITS_ADD_LIBRARY(
   HEADERS ${HEADERS}
   SOURCES ${SOURCES}
   )
-
+if(BUILD_SHARED_LIBS)
+    set_target_properties(chaco PROPERTIES WINDOWS_EXPORT_ALL_SYMBOLS ON)
+endif()
 TRIBITS_SUBPACKAGE_POSTPROCESS()
diff --git a/packages/seacas/libraries/exodus/CMakeLists.txt b/packages/seacas/libraries/exodus/CMakeLists.txt
index 2ed3ec4ab..f80f697f1 100644
--- a/packages/seacas/libraries/exodus/CMakeLists.txt
+++ b/packages/seacas/libraries/exodus/CMakeLists.txt
@@ -13,6 +13,7 @@ FILE(GLOB SOURCES src/ex_*.c)
 if (NOT ${PACKAGE_NAME}_HIDE_DEPRECATED_CODE)
   FILE(GLOB DEP_SOURCES src/deprecated/ex_*.c)
 endif()
+set(EXODUSII_BUILD_SHARED_LIBS "${BUILD_SHARED_LIBS}")
 TRIBITS_CONFIGURE_FILE(exodus_config.h)
 
 if (NOT ${EXODUS_THREADSAFE})
@@ -32,6 +33,9 @@ TRIBITS_ADD_LIBRARY(
   HEADERS ${HEADERS}
   SOURCES ${SOURCES} ${DEP_SOURCES}
 )
+if(BUILD_SHARED_LIBS)
+  target_compile_definitions(exodus PRIVATE exoIIc_EXPORTS)
+endif()
 
 set_property(TARGET exodus PROPERTY C_STANDARD 99)
 
diff --git a/packages/seacas/libraries/exodus/cmake/exodus_config.h.in b/packages/seacas/libraries/exodus/cmake/exodus_config.h.in
index e4dcd51f2..a88254ab7 100644
--- a/packages/seacas/libraries/exodus/cmake/exodus_config.h.in
+++ b/packages/seacas/libraries/exodus/cmake/exodus_config.h.in
@@ -4,4 +4,17 @@
 
 @SEACAS_DEPRECATED_DECLARATIONS@
 
+#cmakedefine EXODUSII_BUILD_SHARED_LIBS
+#if defined(_WIN32)
+# if defined(EXODUSII_BUILD_SHARED_LIBS)
+#  if defined(exoIIc_EXPORTS)
+#    define EXODUS_EXPORT __declspec( dllexport ) extern
+#  else
+#    define EXODUS_EXPORT __declspec( dllimport ) extern
+#  endif
+# endif
+#else
+#  define EXODUS_EXPORT extern
+#endif
+
 #endif
diff --git a/packages/seacas/libraries/exodus/include/exodusII.h b/packages/seacas/libraries/exodus/include/exodusII.h
index ce636683a..5caa88f45 100644
--- a/packages/seacas/libraries/exodus/include/exodusII.h
+++ b/packages/seacas/libraries/exodus/include/exodusII.h
@@ -715,7 +715,7 @@ ex_put_loadbal_param_cc(int             exoid,          /* NetCDF/Exodus file ID
 );
 
 /* Utility function to replace strncpy, strcpy -- guarantee null termination */
-char *ex_copy_string(char *dest, char const *source, size_t elements);
+EXODUS_EXPORT char *ex_copy_string(char *dest, char const *source, size_t elements);
 
 /*!
  * \addtogroup ModelDescription
diff --git a/packages/seacas/libraries/exodus/include/exodusII_int.h b/packages/seacas/libraries/exodus/include/exodusII_int.h
index cec2d7555..e2a21f8d4 100644
--- a/packages/seacas/libraries/exodus/include/exodusII_int.h
+++ b/packages/seacas/libraries/exodus/include/exodusII_int.h
@@ -86,7 +86,7 @@ extern "C" {
 #define EX_FILE_ID_MASK (0xffff0000) /**< Must match FILE_ID_MASK in NetCDF nc4internal.h */
 #define EX_GRP_ID_MASK  (0x0000ffff) /**< Must match GRP_ID_MASK in NetCDF nc4internal.h */
 
-void ex__reset_error_status(void);
+EXODUS_EXPORT void ex__reset_error_status(void);
 
 #if defined(EXODUS_THREADSAFE)
 #if !defined(exerrval)
@@ -791,9 +791,9 @@ extern struct ex__obj_stats *exoII_edm;
 extern struct ex__obj_stats *exoII_fam;
 extern struct ex__obj_stats *exoII_nm;
 
-struct ex__file_item *ex__find_file_item(int exoid);
-struct ex__file_item *ex__add_file_item(int exoid);
-struct ex__obj_stats *ex__get_stat_ptr(int exoid, struct ex__obj_stats **obj_ptr);
+EXODUS_EXPORT struct ex__file_item *ex__find_file_item(int exoid);
+EXODUS_EXPORT struct ex__file_item *ex__add_file_item(int exoid);
+EXODUS_EXPORT struct ex__obj_stats *ex__get_stat_ptr(int exoid, struct ex__obj_stats **obj_ptr);
 
 EXODUS_EXPORT void ex__rm_stat_ptr(int exoid, struct ex__obj_stats **obj_ptr);
 
diff --git a/packages/seacas/libraries/ioss/src/Ioss_StructuredBlock.h b/packages/seacas/libraries/ioss/src/Ioss_StructuredBlock.h
index e7323b8ec..ec974dbc8 100644
--- a/packages/seacas/libraries/ioss/src/Ioss_StructuredBlock.h
+++ b/packages/seacas/libraries/ioss/src/Ioss_StructuredBlock.h
@@ -81,7 +81,7 @@ namespace Ioss {
       archive(m_bcName, m_famName, m_rangeBeg, m_rangeEnd, m_face);
     }
 
-    friend std::ostream &operator<<(std::ostream &os, const BoundaryCondition &bc);
+    friend IOSS_EXPORT std::ostream &operator<<(std::ostream &os, const BoundaryCondition &bc);
 
   private:
     bool equal_(const Ioss::BoundaryCondition &rhs, bool quiet) const;
diff --git a/packages/seacas/libraries/ioss/src/Ioss_ZoneConnectivity.h b/packages/seacas/libraries/ioss/src/Ioss_ZoneConnectivity.h
index 015cc51d3..97bec74b0 100644
--- a/packages/seacas/libraries/ioss/src/Ioss_ZoneConnectivity.h
+++ b/packages/seacas/libraries/ioss/src/Ioss_ZoneConnectivity.h
@@ -86,7 +86,7 @@ namespace Ioss {
     Ioss::IJK_t                inverse_transform(const Ioss::IJK_t &index_1) const;
 
     std::vector<int>     get_range(int ordinal) const;
-    friend std::ostream &operator<<(std::ostream &os, const ZoneConnectivity &zgc);
+    friend IOSS_EXPORT std::ostream &operator<<(std::ostream &os, const ZoneConnectivity &zgc);
 
     /* COMPARE two ZoneConnectivity objects  */
     bool operator==(const Ioss::ZoneConnectivity &rhs) const;
diff --git a/packages/seacas/libraries/ioss/src/text_mesh/Iotm_TextMesh.C b/packages/seacas/libraries/ioss/src/text_mesh/Iotm_TextMesh.C
index 872050daa..a3a0baf9f 100644
--- a/packages/seacas/libraries/ioss/src/text_mesh/Iotm_TextMesh.C
+++ b/packages/seacas/libraries/ioss/src/text_mesh/Iotm_TextMesh.C
@@ -51,12 +51,6 @@ namespace Iotm {
     initialize();
   }
 
-  TextMesh::TextMesh()
-  {
-    m_errorHandler = [](const std::ostringstream &errmsg) { error_handler(errmsg); };
-    initialize();
-  }
-
   unsigned TextMesh::spatial_dimension() const { return m_data.spatialDim; }
 
   void TextMesh::initialize()
diff --git a/packages/seacas/libraries/ioss/src/text_mesh/Iotm_TextMesh.h b/packages/seacas/libraries/ioss/src/text_mesh/Iotm_TextMesh.h
index 530b51e62..73ea2a5c6 100644
--- a/packages/seacas/libraries/ioss/src/text_mesh/Iotm_TextMesh.h
+++ b/packages/seacas/libraries/ioss/src/text_mesh/Iotm_TextMesh.h
@@ -58,7 +58,6 @@ namespace Iotm {
   public:
     explicit TextMesh(const std::string &parameters, int proc_count = 1, int my_proc = 0);
     TextMesh(int proc_count = 1, int my_proc = 0);
-    TextMesh();
     TextMesh(const TextMesh &)            = delete;
     TextMesh &operator=(const TextMesh &) = delete;
 
diff --git a/packages/seacas/libraries/nemesis/CMakeLists.txt b/packages/seacas/libraries/nemesis/CMakeLists.txt
index 154cf9e9e..ab5d87daa 100644
--- a/packages/seacas/libraries/nemesis/CMakeLists.txt
+++ b/packages/seacas/libraries/nemesis/CMakeLists.txt
@@ -1,5 +1,5 @@
 TRIBITS_SUBPACKAGE(Nemesis)
-
+set(NEMESIS_BUILD_SHARED_LIBS "${BUILD_SHARED_LIBS}" CACHE BOOL "")
 SET(NEMESIS_VERSION_MAJOR "5")
 SET(NEMESIS_VERSION_MINOR "15")
 SET(NEMESIS_VERSION_PATCH "0")
@@ -27,7 +27,7 @@ TRIBITS_ADD_LIBRARY(
   HEADERS ${HEADERS}
   SOURCES ${SOURCES}
   )
-
+target_compile_definitions(nemesis PRIVATE nemc_EXPORTS)
 TRIBITS_ADD_EXECUTABLE_AND_TEST(
   ne_ctest_wrap
   NAME ne_ctest_wrap
diff --git a/packages/seacas/libraries/nemesis/ne_nemesisI.h b/packages/seacas/libraries/nemesis/ne_nemesisI.h
index dc78c2fe4..ae43c1f3f 100644
--- a/packages/seacas/libraries/nemesis/ne_nemesisI.h
+++ b/packages/seacas/libraries/nemesis/ne_nemesisI.h
@@ -14,6 +14,8 @@
 #ifndef _NE_NEMESIS_H
 #define _NE_NEMESIS_H
 
+#include "nemesis_cfg.h"
+
 #ifdef __cplusplus
 extern "C" {
 #endif
@@ -34,24 +36,24 @@ typedef void void_int;
 /*=============================================================================
  *     Initial Information Routines
  *===========================================================================*/
-extern int ne_get_init_info(int   neid,          /* NemesisI file ID */
+NEMESIS_EXPORT int ne_get_init_info(int   neid,          /* NemesisI file ID */
                             int * num_proc,      /* Number of processors */
                             int * num_proc_in_f, /* Number of procs in this file */
                             char *ftype);
 
-extern int ne_put_init_info(int   neid,          /* NemesisI file ID */
+NEMESIS_EXPORT int ne_put_init_info(int   neid,          /* NemesisI file ID */
                             int   num_proc,      /* Number of processors */
                             int   num_proc_in_f, /* Number of procs in this file */
                             char *ftype);
 
-extern int ne_get_init_global(int       neid,            /* NemesisI file ID */
+NEMESIS_EXPORT int ne_get_init_global(int       neid,            /* NemesisI file ID */
                               void_int *num_nodes_g,     /* Number of global FEM nodes */
                               void_int *num_elems_g,     /* Number of global FEM elements */
                               void_int *num_elem_blks_g, /* Number of global elem blocks */
                               void_int *num_node_sets_g, /* Number of global node sets */
                               void_int *num_side_sets_g  /* Number of global side sets */
 );
-extern int ne_put_init_global(int     neid,            /* NemesisI file ID */
+NEMESIS_EXPORT int ne_put_init_global(int     neid,            /* NemesisI file ID */
                               int64_t num_nodes_g,     /* Number of global FEM nodes */
                               int64_t num_elems_g,     /* Number of global FEM elements */
                               int64_t num_elem_blks_g, /* Number of global elem blocks */
@@ -59,12 +61,12 @@ extern int ne_put_init_global(int     neid,            /* NemesisI file ID */
                               int64_t num_side_sets_g  /* Number of global side sets */
 );
 
-extern int ne_put_version(int neid);
+NEMESIS_EXPORT int ne_put_version(int neid);
 
 /*=============================================================================
  *     Loadbalance Parameter Routines
  *===========================================================================*/
-extern int ne_get_loadbal_param(int       neid,           /* NetCDF/Exodus file ID */
+NEMESIS_EXPORT int ne_get_loadbal_param(int       neid,           /* NetCDF/Exodus file ID */
                                 void_int *num_int_nodes,  /* Number of internal FEM nodes */
                                 void_int *num_bor_nodes,  /* Number of border FEM nodes */
                                 void_int *num_ext_nodes,  /* Number of external FEM nodes */
@@ -75,7 +77,7 @@ extern int ne_get_loadbal_param(int       neid,           /* NetCDF/Exodus file
                                 int       processor       /* Processor ID */
 );
 
-extern int ne_put_loadbal_param(int     neid,           /* NemesisI file ID  */
+NEMESIS_EXPORT int ne_put_loadbal_param(int     neid,           /* NemesisI file ID  */
                                 int64_t num_int_nodes,  /* Number of internal FEM nodes */
                                 int64_t num_bor_nodes,  /* Number of border FEM nodes */
                                 int64_t num_ext_nodes,  /* Number of external FEM nodes */
@@ -86,7 +88,7 @@ extern int ne_put_loadbal_param(int     neid,           /* NemesisI file ID  */
                                 int     processor       /* Processor ID */
 );
 
-extern int ne_put_loadbal_param_cc(int       neid,           /* NetCDF/Exodus file ID */
+NEMESIS_EXPORT int ne_put_loadbal_param_cc(int       neid,           /* NetCDF/Exodus file ID */
                                    void_int *num_int_nodes,  /* Number of internal node IDs */
                                    void_int *num_bor_nodes,  /* Number of border node IDs */
                                    void_int *num_ext_nodes,  /* Number of external node IDs */
@@ -99,26 +101,26 @@ extern int ne_put_loadbal_param_cc(int       neid,           /* NetCDF/Exodus fi
 /*=============================================================================
  *     NS, SS & EB Global Parameter Routines
  *===========================================================================*/
-extern int ne_get_ns_param_global(int       neid,          /* NetCDF/Exodus file ID */
+NEMESIS_EXPORT int ne_get_ns_param_global(int       neid,          /* NetCDF/Exodus file ID */
                                   void_int *ns_ids_glob,   /* Global IDs of node sets */
                                   void_int *ns_n_cnt_glob, /* Count of nodes in node sets */
                                   void_int *ns_df_cnt_glob /* Count of dist. factors in ns */
 );
 
-extern int
+NEMESIS_EXPORT int
 ne_put_ns_param_global(int       neid,          /* NemesisI file ID */
                        void_int *global_ids,    /* Vector of global node-set IDs */
                        void_int *global_n_cnts, /* Vector of node counts in node-sets */
                        void_int *global_df_cnts /* Vector of dist factor counts in node-sets */
 );
 
-extern int ne_get_ss_param_global(int       neid,          /* NetCDF/Exodus file ID */
+NEMESIS_EXPORT int ne_get_ss_param_global(int       neid,          /* NetCDF/Exodus file ID */
                                   void_int *ss_ids_glob,   /* Global side-set IDs */
                                   void_int *ss_s_cnt_glob, /* Global side count */
                                   void_int *ss_df_cnt_glob /* Global dist. factor count */
 );
 
-extern int ne_put_ss_param_global(int       neid,           /* NemesisI file ID */
+NEMESIS_EXPORT int ne_put_ss_param_global(int       neid,           /* NemesisI file ID */
                                   void_int *global_ids,     /* Vector of global side-set IDs */
                                   void_int *global_el_cnts, /* Vector of element/side */
                                                             /* counts in each side set */
@@ -126,12 +128,12 @@ extern int ne_put_ss_param_global(int       neid,           /* NemesisI file ID
                                                             /* counts in each side set */
 );
 
-extern int ne_get_eb_info_global(int       neid,       /* NemesisI file ID                 */
+NEMESIS_EXPORT int ne_get_eb_info_global(int       neid,       /* NemesisI file ID                 */
                                  void_int *el_blk_ids, /* Vector of global element IDs     */
                                  void_int *el_blk_cnts /* Vector of global element counts  */
 );
 
-extern int ne_put_eb_info_global(int       neid,       /* NemesisI file ID */
+NEMESIS_EXPORT int ne_put_eb_info_global(int       neid,       /* NemesisI file ID */
                                  void_int *el_blk_ids, /* Vector of global element IDs     */
                                  void_int *el_blk_cnts /* Vector of global element counts  */
 );
@@ -139,7 +141,7 @@ extern int ne_put_eb_info_global(int       neid,       /* NemesisI file ID */
 /*=============================================================================
  *     NS, SS & EB Subset Routines
  *===========================================================================*/
-extern int ne_get_n_side_set(int          neid,               /* NetCDF/Exodus file ID */
+NEMESIS_EXPORT int ne_get_n_side_set(int          neid,               /* NetCDF/Exodus file ID */
                              ex_entity_id side_set_id,        /* Side-set ID to read */
                              int64_t      start_side_num,     /* Starting element number */
                              int64_t      num_sides,          /* Number of sides to read */
@@ -147,7 +149,7 @@ extern int ne_get_n_side_set(int          neid,               /* NetCDF/Exodus f
                              void_int *   side_set_side_list  /* List of side IDs */
 );
 
-extern int ne_put_n_side_set(int             neid,               /* NetCDF/Exodus file ID */
+NEMESIS_EXPORT int ne_put_n_side_set(int             neid,               /* NetCDF/Exodus file ID */
                              ex_entity_id    side_set_id,        /* Side-set ID to write */
                              int64_t         start_side_num,     /* Starting element number */
                              int64_t         num_sides,          /* Number of sides to write */
@@ -155,49 +157,49 @@ extern int ne_put_n_side_set(int             neid,               /* NetCDF/Exodu
                              const void_int *side_set_side_list  /* List of side IDs */
 );
 
-extern int ne_get_n_side_set_df(int          neid,          /* NetCDF/Exodus file ID */
+NEMESIS_EXPORT int ne_get_n_side_set_df(int          neid,          /* NetCDF/Exodus file ID */
                                 ex_entity_id side_set_id,   /* Side-set ID */
                                 int64_t      start_num,     /* Starting df number */
                                 int64_t      num_df_to_get, /* Number of df's to read */
                                 void *       side_set_df    /* Distribution factors */
 );
 
-extern int ne_put_n_side_set_df(int          neid,          /* NetCDF/Exodus file ID */
+NEMESIS_EXPORT int ne_put_n_side_set_df(int          neid,          /* NetCDF/Exodus file ID */
                                 ex_entity_id side_set_id,   /* Side-set ID */
                                 int64_t      start_num,     /* Starting df number */
                                 int64_t      num_df_to_get, /* Number of df's to write */
                                 void *       side_set_df    /* Distribution factors */
 );
 
-extern int ne_get_n_node_set(int          neid,              /* NetCDF/Exodus file ID */
+NEMESIS_EXPORT int ne_get_n_node_set(int          neid,              /* NetCDF/Exodus file ID */
                              ex_entity_id node_set_id,       /* Node set ID */
                              int64_t      start_node_num,    /* Node index to start reading at */
                              int64_t      num_node,          /* Number of nodes to read */
                              void_int *   node_set_node_list /* List of nodes in node set */
 );
 
-extern int ne_put_n_node_set(int             neid,              /* NetCDF/Exodus file ID */
+NEMESIS_EXPORT int ne_put_n_node_set(int             neid,              /* NetCDF/Exodus file ID */
                              ex_entity_id    node_set_id,       /* Node set ID */
                              int64_t         start_node_num,    /* Node index to start writing at */
                              int64_t         num_node,          /* Number of nodes to write */
                              const void_int *node_set_node_list /* List of nodes in node set */
 );
 
-extern int ne_get_n_node_set_df(int          neid,          /* NetCDF/Exodus file ID */
+NEMESIS_EXPORT int ne_get_n_node_set_df(int          neid,          /* NetCDF/Exodus file ID */
                                 ex_entity_id node_set_id,   /* Node-set ID */
                                 int64_t      start_num,     /* Starting df number */
                                 int64_t      num_df_to_get, /* Number of df's to read */
                                 void *       node_set_df    /* Distribution factors */
 );
 
-extern int ne_put_n_node_set_df(int          neid,          /* NetCDF/Exodus file ID */
+NEMESIS_EXPORT int ne_put_n_node_set_df(int          neid,          /* NetCDF/Exodus file ID */
                                 ex_entity_id node_set_id,   /* Node-set ID */
                                 int64_t      start_num,     /* Starting df number */
                                 int64_t      num_df_to_get, /* Number of df's to write */
                                 void *       node_set_df    /* Distribution factors */
 );
 
-extern int ne_get_n_coord(int     neid,           /* NetCDF/Exodus file ID */
+NEMESIS_EXPORT int ne_get_n_coord(int     neid,           /* NetCDF/Exodus file ID */
                           int64_t start_node_num, /* Starting position to read from */
                           int64_t num_nodes,      /* Number of coords to read */
                           void *  x_coor,         /* Vector of X coordinates */
@@ -205,7 +207,7 @@ extern int ne_get_n_coord(int     neid,           /* NetCDF/Exodus file ID */
                           void *  z_coor          /* Vector of Z coordinates */
 );
 
-extern int ne_put_n_coord(int     neid,           /* NetCDF/Exodus file ID */
+NEMESIS_EXPORT int ne_put_n_coord(int     neid,           /* NetCDF/Exodus file ID */
                           int64_t start_node_num, /* Starting position to write to */
                           int64_t num_nodes,      /* Number of coords to write */
                           void *  x_coor,         /* Vector of X coordinates */
@@ -213,35 +215,35 @@ extern int ne_put_n_coord(int     neid,           /* NetCDF/Exodus file ID */
                           void *  z_coor          /* Vector of Z coordinates */
 );
 
-extern int ne_get_n_elem_conn(int          neid,           /* NetCDF/Exodus file ID */
+NEMESIS_EXPORT int ne_get_n_elem_conn(int          neid,           /* NetCDF/Exodus file ID */
                               ex_entity_id elem_blk_id,    /* Element block ID */
                               int64_t      start_elem_num, /* Starting position to read from */
                               int64_t      num_elems,      /* Number of elements to read */
                               void_int *   connect         /* Connectivity vector */
 );
 
-extern int ne_put_n_elem_conn(int             neid,           /* NetCDF/Exodus file ID */
+NEMESIS_EXPORT int ne_put_n_elem_conn(int             neid,           /* NetCDF/Exodus file ID */
                               ex_entity_id    elem_blk_id,    /* Element block ID */
                               int64_t         start_elem_num, /* Starting position to write to */
                               int64_t         num_elems,      /* Number of elements to write */
                               const void_int *connect         /* Connectivity vector */
 );
 
-extern int ne_get_n_elem_attr(int          neid,           /* NetCDF/Exodus file ID */
+NEMESIS_EXPORT int ne_get_n_elem_attr(int          neid,           /* NetCDF/Exodus file ID */
                               ex_entity_id elem_blk_id,    /* Element block ID */
                               int64_t      start_elem_num, /* Starting position to read from */
                               int64_t      num_elems,      /* Number of elements to read */
                               void *       attrib          /* Attribute */
 );
 
-extern int ne_put_n_elem_attr(int          neid,           /* NetCDF/Exodus file ID */
+NEMESIS_EXPORT int ne_put_n_elem_attr(int          neid,           /* NetCDF/Exodus file ID */
                               ex_entity_id elem_blk_id,    /* Element block ID */
                               int64_t      start_elem_num, /* Starting position to write to */
                               int64_t      num_elems,      /* Number of elements to write */
                               void *       attrib          /* Attribute */
 );
 
-extern int ne_get_elem_type(int          neid,        /* NetCDF/Exodus file ID */
+NEMESIS_EXPORT int ne_get_elem_type(int          neid,        /* NetCDF/Exodus file ID */
                             ex_entity_id elem_blk_id, /* Element block ID */
                             char *       elem_type    /* The name of the element type */
 );
@@ -249,7 +251,7 @@ extern int ne_get_elem_type(int          neid,        /* NetCDF/Exodus file ID *
 /*=============================================================================
  *     Variable Routines
  *===========================================================================*/
-extern int ne_get_n_elem_var(int          neid,              /* NetCDF/Exodus file ID */
+NEMESIS_EXPORT int ne_get_n_elem_var(int          neid,              /* NetCDF/Exodus file ID */
                              int          time_step,         /* time index */
                              int          elem_var_index,    /* elemental variable index */
                              ex_entity_id elem_blk_id,       /* elemental block id */
@@ -259,7 +261,7 @@ extern int ne_get_n_elem_var(int          neid,              /* NetCDF/Exodus fi
                              void *       elem_var_vals      /* variable values */
 );
 
-extern int ne_put_elem_var_slab(int          neid,           /* NetCDF/Exodus file ID */
+NEMESIS_EXPORT int ne_put_elem_var_slab(int          neid,           /* NetCDF/Exodus file ID */
                                 int          time_step,      /* time index */
                                 int          elem_var_index, /* elemental variable index */
                                 ex_entity_id elem_blk_id,    /* elemental block id */
@@ -268,7 +270,7 @@ extern int ne_put_elem_var_slab(int          neid,           /* NetCDF/Exodus fi
                                 void *       elem_var_vals   /* variable values */
 );
 
-extern int ne_get_n_nodal_var(int     neid,            /* NetCDF/Exodus file ID */
+NEMESIS_EXPORT int ne_get_n_nodal_var(int     neid,            /* NetCDF/Exodus file ID */
                               int     time_step,       /* whole time step number */
                               int     nodal_var_index, /* index of desired nodal var */
                               int64_t start_node_num,  /* starting node number */
@@ -276,7 +278,7 @@ extern int ne_get_n_nodal_var(int     neid,            /* NetCDF/Exodus file ID
                               void *  nodal_vars       /* array of nodal var values */
 );
 
-extern int ne_put_nodal_var_slab(int     neid,            /* NetCDF/Exodus file ID */
+NEMESIS_EXPORT int ne_put_nodal_var_slab(int     neid,            /* NetCDF/Exodus file ID */
                                  int     time_step,       /* The time step index */
                                  int     nodal_var_index, /* Nodal variable index */
                                  int64_t start_pos,       /* Start position for write */
@@ -287,51 +289,51 @@ extern int ne_put_nodal_var_slab(int     neid,            /* NetCDF/Exodus file
 /*=============================================================================
  *     Number Map Routines
  *===========================================================================*/
-extern int ne_get_n_elem_num_map(int       neid,      /* NetCDF/Exodus file ID */
+NEMESIS_EXPORT int ne_get_n_elem_num_map(int       neid,      /* NetCDF/Exodus file ID */
                                  int64_t   start_ent, /* Starting position to read from */
                                  int64_t   num_ents,  /* Number of elements to read */
                                  void_int *elem_map   /* element map numbers */
 );
 
-extern int ne_put_n_elem_num_map(int             neid,      /* NetCDF/Exodus file ID */
+NEMESIS_EXPORT int ne_put_n_elem_num_map(int             neid,      /* NetCDF/Exodus file ID */
                                  int64_t         start_ent, /* Starting position to read from */
                                  int64_t         num_ents,  /* Number of elements to read */
                                  const void_int *elem_map   /* element map numbers */
 );
 
-extern int ne_get_n_node_num_map(int       neid,      /* NetCDF/Exodus file ID */
+NEMESIS_EXPORT int ne_get_n_node_num_map(int       neid,      /* NetCDF/Exodus file ID */
                                  int64_t   start_ent, /* starting node number */
                                  int64_t   num_ents,  /* number of nodes to read */
                                  void_int *node_map   /* vector for node map */
 );
 
-extern int ne_put_n_node_num_map(int             neid,      /* NetCDF/Exodus file ID */
+NEMESIS_EXPORT int ne_put_n_node_num_map(int             neid,      /* NetCDF/Exodus file ID */
                                  int64_t         start_ent, /* starting node number */
                                  int64_t         num_ents,  /* number of nodes to read */
                                  const void_int *node_map   /* vector for node map */
 );
 
-extern int ne_get_node_map(int       neid,      /* NetCDF/Exodus file ID */
+NEMESIS_EXPORT int ne_get_node_map(int       neid,      /* NetCDF/Exodus file ID */
                            void_int *node_mapi, /* Internal FEM node IDs */
                            void_int *node_mapb, /* Border FEM node IDs */
                            void_int *node_mape, /* External FEM node IDs */
                            int       processor  /* Processor IDs */
 );
 
-extern int ne_put_node_map(int       neid,      /* NetCDF/Exodus file ID */
+NEMESIS_EXPORT int ne_put_node_map(int       neid,      /* NetCDF/Exodus file ID */
                            void_int *node_mapi, /* Internal FEM node IDs */
                            void_int *node_mapb, /* Border FEM node IDs */
                            void_int *node_mape, /* External FEM node IDs */
                            int       processor  /* This processor ID */
 );
 
-extern int ne_get_elem_map(int       neid,      /* NetCDF/Exodus file ID */
+NEMESIS_EXPORT int ne_get_elem_map(int       neid,      /* NetCDF/Exodus file ID */
                            void_int *elem_mapi, /* Internal element IDs */
                            void_int *elem_mapb, /* Border element IDs */
                            int       processor  /* Processor ID */
 );
 
-extern int ne_put_elem_map(int       neid,      /* NetCDF/Exodus file ID */
+NEMESIS_EXPORT int ne_put_elem_map(int       neid,      /* NetCDF/Exodus file ID */
                            void_int *elem_mapi, /* Internal FEM element IDs */
                            void_int *elem_mapb, /* Border FEM element IDs */
                            int       processor  /* This processor ID */
@@ -341,7 +343,7 @@ extern int ne_put_elem_map(int       neid,      /* NetCDF/Exodus file ID */
  *     Communications Maps Routines
  *===========================================================================*/
 
-extern int ne_get_cmap_params(int       neid,                /* NetCDF/Exodus file ID */
+NEMESIS_EXPORT int ne_get_cmap_params(int       neid,                /* NetCDF/Exodus file ID */
                               void_int *node_cmap_ids,       /* Nodal comm. map IDs */
                               void_int *node_cmap_node_cnts, /* Number of nodes in each map */
                               void_int *elem_cmap_ids,       /* Elemental comm. map IDs */
@@ -349,7 +351,7 @@ extern int ne_get_cmap_params(int       neid,                /* NetCDF/Exodus fi
                               int       processor            /* This processor ID */
 );
 
-extern int ne_put_cmap_params(int       neid,               /* NetCDF/Exodus file ID */
+NEMESIS_EXPORT int ne_put_cmap_params(int       neid,               /* NetCDF/Exodus file ID */
                               void_int *node_map_ids,       /* Node map IDs */
                               void_int *node_map_node_cnts, /* Nodes in nodal comm */
                               void_int *elem_map_ids,       /* Elem map IDs */
@@ -357,7 +359,7 @@ extern int ne_put_cmap_params(int       neid,               /* NetCDF/Exodus fil
                               int64_t   processor           /* This processor ID */
 );
 
-extern int ne_put_cmap_params_cc(int       neid,               /* NetCDF/Exodus file ID */
+NEMESIS_EXPORT int ne_put_cmap_params_cc(int       neid,               /* NetCDF/Exodus file ID */
                                  void_int *node_map_ids,       /* Node map IDs */
                                  void_int *node_map_node_cnts, /* Nodes in nodal comm */
                                  void_int *node_proc_ptrs,     /* Pointer into array for */
@@ -368,21 +370,21 @@ extern int ne_put_cmap_params_cc(int       neid,               /* NetCDF/Exodus
                                                                /* elem maps               */
 );
 
-extern int ne_get_node_cmap(int          neid,     /* NetCDF/Exodus file ID */
+NEMESIS_EXPORT int ne_get_node_cmap(int          neid,     /* NetCDF/Exodus file ID */
                             ex_entity_id map_id,   /* Map ID */
                             void_int *   node_ids, /* FEM node IDs */
                             void_int *   proc_ids, /* Processor IDs */
                             int          processor /* This processor ID */
 );
 
-extern int ne_put_node_cmap(int          neid,     /* NetCDF/Exodus file ID */
+NEMESIS_EXPORT int ne_put_node_cmap(int          neid,     /* NetCDF/Exodus file ID */
                             ex_entity_id map_id,   /* Nodal comm map ID */
                             void_int *   node_ids, /* FEM node IDs */
                             void_int *   proc_ids, /* Processor IDs */
                             int          processor /* This processor ID */
 );
 
-extern int ne_get_elem_cmap(int          neid,     /* NetCDF/Exodus file ID */
+NEMESIS_EXPORT int ne_get_elem_cmap(int          neid,     /* NetCDF/Exodus file ID */
                             ex_entity_id map_id,   /* Elemental comm map ID */
                             void_int *   elem_ids, /* Element IDs */
                             void_int *   side_ids, /* Element side IDs */
@@ -390,7 +392,7 @@ extern int ne_get_elem_cmap(int          neid,     /* NetCDF/Exodus file ID */
                             int          processor /* This processor ID */
 );
 
-extern int ne_put_elem_cmap(int          neid,     /* NetCDF/Exodus file ID */
+NEMESIS_EXPORT int ne_put_elem_cmap(int          neid,     /* NetCDF/Exodus file ID */
                             ex_entity_id map_id,   /* Elemental comm map ID */
                             void_int *   elem_ids, /* Vector of element IDs */
                             void_int *   side_ids, /* Vector of side IDs */
diff --git a/packages/seacas/libraries/nemesis/nemesis_cfg.h.in b/packages/seacas/libraries/nemesis/nemesis_cfg.h.in
index 37b04f5cd..05f2db341 100644
--- a/packages/seacas/libraries/nemesis/nemesis_cfg.h.in
+++ b/packages/seacas/libraries/nemesis/nemesis_cfg.h.in
@@ -1,8 +1,8 @@
-# Copyright(C) 1999-2020 National Technology & Engineering Solutions
-# of Sandia, LLC (NTESS).  Under the terms of Contract DE-********* with
-# NTESS, the U.S. Government retains certain rights in this software.
-#
-# See packages/seacas/LICENSE for details
+// Copyright(C) 1999-2020 National Technology & Engineering Solutions
+// of Sandia, LLC (NTESS).  Under the terms of Contract DE-********* with
+// NTESS, the U.S. Government retains certain rights in this software.
+//
+// See packages/seacas/LICENSE for details
 
 #ifndef __nemesis_cfg_h
 #define __nemesis_cfg_h
diff --git a/packages/seacas/libraries/suplib_c/CMakeLists.txt b/packages/seacas/libraries/suplib_c/CMakeLists.txt
index c5c0d0fd3..b047ea4cd 100644
--- a/packages/seacas/libraries/suplib_c/CMakeLists.txt
+++ b/packages/seacas/libraries/suplib_c/CMakeLists.txt
@@ -29,5 +29,7 @@ TRIBITS_ADD_LIBRARY(
   NOINSTALLHEADERS
   SOURCES ${SOURCES}
   )
-
+if(BUILD_SHARED_LIBS)
+    set_target_properties(suplib_c PROPERTIES WINDOWS_EXPORT_ALL_SYMBOLS ON)
+endif()
 TRIBITS_SUBPACKAGE_POSTPROCESS()
diff --git a/packages/seacas/libraries/suplib_cpp/CMakeLists.txt b/packages/seacas/libraries/suplib_cpp/CMakeLists.txt
index 5eb15b5a7..640b2a8b9 100644
--- a/packages/seacas/libraries/suplib_cpp/CMakeLists.txt
+++ b/packages/seacas/libraries/suplib_cpp/CMakeLists.txt
@@ -30,5 +30,7 @@ TRIBITS_ADD_LIBRARY(
   NOINSTALLHEADERS
   SOURCES ${SOURCES}
   )
-
+if(BUILD_SHARED_LIBS)
+    set_target_properties(suplib_cpp PROPERTIES WINDOWS_EXPORT_ALL_SYMBOLS ON)
+endif()
 TRIBITS_SUBPACKAGE_POSTPROCESS()
