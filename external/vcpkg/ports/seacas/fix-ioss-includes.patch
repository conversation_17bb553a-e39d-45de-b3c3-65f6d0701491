diff --git a/packages/seacas/libraries/ioss/src/exodus/Ioex_DecompositionData.C b/packages/seacas/libraries/ioss/src/exodus/Ioex_DecompositionData.C
index eab77e9a1..6cec47422 100644
--- a/packages/seacas/libraries/ioss/src/exodus/Ioex_DecompositionData.C
+++ b/packages/seacas/libraries/ioss/src/exodus/Ioex_DecompositionData.C
@@ -14,6 +14,8 @@
 #include <Ioss_SmartAssert.h>
 #include <Ioss_Sort.h>
 #include <Ioss_Utils.h>
+#include <Ioss_Decomposition.h>
+#include <Ioss_ParallelUtils.h>
 #include <exodus/Ioex_Utils.h>
 
 #include <algorithm> // for lower_bound, copy, etc
diff --git a/packages/seacas/libraries/ioss/src/exodus/Ioex_ParallelDatabaseIO.C b/packages/seacas/libraries/ioss/src/exodus/Ioex_ParallelDatabaseIO.C
index 0be442435..fccf2d4d4 100644
--- a/packages/seacas/libraries/ioss/src/exodus/Ioex_ParallelDatabaseIO.C
+++ b/packages/seacas/libraries/ioss/src/exodus/Ioex_ParallelDatabaseIO.C
@@ -74,6 +74,7 @@
 #include <Ioss_SurfaceSplit.h>
 #include <Ioss_Utils.h>
 #include <Ioss_VariableType.h>
+#include <Ioss_SerializeIO.h>
 
 #include <Ioss_FileInfo.h>
 #undef MPICPP
