{"name": "seacas", "version-date": "2022-11-22", "port-version": 8, "description": "The Sandia Engineering Analysis Code Access System (SEACAS) is a suite of preprocessing, postprocessing, translation, and utility applications supporting finite element analysis software using the Exodus database file format.", "homepage": "https://github.com/sandialabs/seacas", "license": null, "dependencies": ["cereal", {"name": "cgns", "default-features": false, "features": ["hdf5"]}, "fmt", {"name": "hdf5", "default-features": false}, "metis", {"name": "netcdf-c", "default-features": false, "features": ["hdf5"]}, {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}, "zlib"], "features": {"matio": {"description": "Enable Matio support", "dependencies": [{"name": "matio", "default-features": false}]}, "mpi": {"description": "Enable MPI support", "dependencies": [{"name": "cgns", "default-features": false, "features": ["mpi"]}, {"name": "hdf5", "default-features": false, "features": ["parallel"]}, {"name": "parmetis", "default-features": false, "platform": "!osx"}]}}}