diff --git a/cmake/tribits/core/package_arch/TribitsAddExecutable.cmake b/cmake/tribits/core/package_arch/TribitsAddExecutable.cmake
index f08e7b78b..1274e8fce 100644
--- a/cmake/tribits/core/package_arch/TribitsAddExecutable.cmake
+++ b/cmake/tribits/core/package_arch/TribitsAddExecutable.cmake
@@ -419,7 +419,11 @@ function(tribits_add_executable EXE_NAME)
   foreach(depPkg IN LISTS ${PACKAGE_NAME}_LIB_ENABLED_DEPENDENCIES
       ${PACKAGE_NAME}_TEST_ENABLED_DEPENDENCIES
     )
-    target_link_libraries(${EXE_BINARY_NAME} PUBLIC ${depPkg}::all_libs)
+    if(TARGET TPL::${depPkg}::all_libs)
+        target_link_libraries(${EXE_BINARY_NAME} PUBLIC TPL::${depPkg}::all_libs)
+    else()
+        target_link_libraries(${EXE_BINARY_NAME} PUBLIC ${depPkg}::all_libs)
+    endif()
   endforeach()
   foreach(testOnlyLib ${PARSE_TESTONLYLIBS})
     target_link_libraries(${EXE_BINARY_NAME} PUBLIC
diff --git a/cmake/tribits/core/package_arch/TribitsAddLibrary.cmake b/cmake/tribits/core/package_arch/TribitsAddLibrary.cmake
index 4bac9f943..398002e33 100644
--- a/cmake/tribits/core/package_arch/TribitsAddLibrary.cmake
+++ b/cmake/tribits/core/package_arch/TribitsAddLibrary.cmake
@@ -468,7 +468,11 @@ function(tribits_add_library  LIBRARY_NAME_IN)
     target_link_libraries(${LIBRARY_NAME} PUBLIC ${${PACKAGE_NAME}_LIBRARIES})
     # ${PACKAGE_NAME}_LIB_ENABLED_DEPENDENCIES
     foreach(depPkg IN LISTS ${PACKAGE_NAME}_LIB_ENABLED_DEPENDENCIES)
-      target_link_libraries(${LIBRARY_NAME} PUBLIC ${depPkg}::all_libs)
+      if(TARGET TPL::${depPkg}::all_libs)
+        target_link_libraries(${LIBRARY_NAME} PUBLIC TPL::${depPkg}::all_libs)
+      else()
+        target_link_libraries(${LIBRARY_NAME} PUBLIC ${depPkg}::all_libs)
+      endif()
     endforeach()
     # ${PACKAGE_NAME}_TEST_ENABLED_DEPENDENCIES (TESTONLY lib)
     if (PARSE_TESTONLY)
diff --git a/cmake/tribits/core/package_arch/TribitsExternalPackageWriteConfigFile.cmake b/cmake/tribits/core/package_arch/TribitsExternalPackageWriteConfigFile.cmake
index 2d041a2ca..c49a2c019 100644
--- a/cmake/tribits/core/package_arch/TribitsExternalPackageWriteConfigFile.cmake
+++ b/cmake/tribits/core/package_arch/TribitsExternalPackageWriteConfigFile.cmake
@@ -132,7 +132,7 @@ endfunction()
 function(tribits_extpkg_install_config_file  tplName  tplConfigFile)
  install(
     FILES "${tplConfigFile}"
-    DESTINATION "${${PROJECT_NAME}_INSTALL_LIB_DIR}/external_packages/${tplName}"
+    DESTINATION "${${PROJECT_NAME}_INSTALL_LIB_DIR}/external_packages/TPL-Seacas-${tplName}"
     )
 endfunction()
 
@@ -159,7 +159,7 @@ function(tribits_extpkg_install_config_version_file  tplName
   )
  install(
     FILES "${tplConfigVersionFile}"
-    DESTINATION "${${PROJECT_NAME}_INSTALL_LIB_DIR}/external_packages/${tplName}"
+    DESTINATION "${${PROJECT_NAME}_INSTALL_LIB_DIR}/external_packages/TPL-Seacas-${tplName}"
     )
 endfunction()
 
@@ -258,7 +258,7 @@ function(tribits_extpkg_write_config_file_str  tplName  tplConfigFileStrOut)
     "# Generated by CMake, do not edit!\n"
     "\n"
     "# Guard against multiple inclusion\n"
-    "if (TARGET ${tplName}::all_libs)\n"
+    "if (TARGET TPL::${tplName}::all_libs)\n"
     "  return()\n"
     "endif()\n"
     "\n"
@@ -333,9 +333,9 @@ function(tribits_extpkg_add_find_upstream_dependencies_str
         message(FATAL_ERROR "ERROR: ${upstreamTplDepName}_DIR is empty!")
       endif()
       string(APPEND configFileFragStr
-        "if (NOT TARGET ${upstreamTplDepName}::all_libs)\n"
+        "if (NOT TARGET TPL::${upstreamTplDepName}::all_libs)\n"
         "  set(${upstreamTplDepName}_DIR \"\${CMAKE_CURRENT_LIST_DIR}/../${upstreamTplDepName}\")\n"
-        "  find_dependency(${upstreamTplDepName} REQUIRED CONFIG \${${tplName}_SearchNoOtherPathsArgs})\n"
+        "  find_dependency(TPL-Seacas-${upstreamTplDepName} REQUIRED CONFIG \${${tplName}_SearchNoOtherPathsArgs})\n"
         "  unset(${upstreamTplDepName}_DIR)\n"
         "endif()\n"
         "\n"
@@ -440,6 +440,7 @@ function(tribits_extpkg_process_libraries_list  tplName)
     elseif (libEntryType STREQUAL "GENERAL_LINK_OPTION")
       message_wrapper("-- NOTE: Moving the general link argument '${libentry}' in TPL_${tplName}_LIBRARIES forward on the link line which may change the link and break the link!")
       list(APPEND libLinkFlagsList "${libentry}")
+    elseif(libEntryType STREQUAL "TARGET_NAME")
     else()
       tribits_extpkg_process_libraries_list_library_entry(
         ${tplName}  "${libentry}"  ${libEntryType}  libTargets  lastLibProcessed
@@ -501,6 +501,8 @@ function(tribits_tpl_libraries_entry_type  libentry  libEntryTypeOut)
     set(libEntryType FULL_LIB_PATH)
   elseif (libentry MATCHES "^[a-zA-Z_][a-zA-Z0-9_-]*$")
     set(libEntryType LIB_NAME)
+  elseif (TARGET "${libentry}")
+    set(libEntryType TARGET_NAME)
   else()
     set(libEntryType UNSUPPORTED_LIB_ENTRY)
   endif()
@@ -529,7 +531,7 @@ function(tribits_extpkg_process_libraries_list_library_entry
   tribits_extpkg_get_libname_and_path_from_libentry(
     "${libentry}"  ${libEntryType}  libname  libpath)
   # Create IMPORTED library target
-  set(prefixed_libname "${tplName}::${libname}")
+  set(prefixed_libname "TPL::${tplName}::${libname}")
   if (NOT (prefixed_libname IN_LIST libTargets))
     tribits_extpkg_append_add_library_str (${libname} ${prefixed_libname}
       ${libEntryType} "${libpath}" configFileStr)
@@ -571,6 +573,9 @@ function(tribits_extpkg_get_libname_and_path_from_libentry
   elseif (libEntryType STREQUAL "LIB_NAME")
     set(libname "${libentry}")
     set(libpath "")
+  elseif (libEntryType STREQUAL "TARGET_NAME")
+    string(REGEX REPLACE "([^:]*::)+" "" libname "${libentry}")
+    set(libpath "")
   else()
     message(FATAL_ERROR "Error libEntryType='${libEntryType}' not supported here!")
   endif()
@@ -605,6 +610,13 @@ function(tribits_extpkg_append_add_library_str
       "set_target_properties(${prefixed_libname} PROPERTIES\n"
       "  IMPORTED_LIBNAME \"${libname}\")\n"
       )
+  elseif (libEntryType STREQUAL "TARGET_NAME")
+      string(REGEX REPLACE "([^:]*::)+" "" package_name "${libname}")
+      string(APPEND configFileStr 
+      "#add_library(${prefixed_libname} IMPORTED INTERFACE)\n"
+      "#find_package(${package_name} REQUIRED)\n"
+      "#target_link_libraries(${prefixed_libname} INTERFACE ${libentry})\n"
+      )
   else()
     message(FATAL_ERROR "Error libEntryType='${libEntryType}' not supported here!")
   endif()
@@ -784,11 +796,11 @@ function(tribits_extpkg_create_all_libs_target  tplName)
 
   # add_library()
   string(APPEND configFileStr
-    "add_library(${tplName}::all_libs INTERFACE IMPORTED)\n")
+    "add_library(TPL::${tplName}::all_libs INTERFACE IMPORTED)\n")
   # target_link_libraries()
   if (libTargets)
     string(APPEND configFileStr
-      "target_link_libraries(${tplName}::all_libs\n")
+      "target_link_libraries(TPL::${tplName}::all_libs\n")
     foreach (libTarget IN LISTS libTargets)
       string(APPEND configFileStr
         "  INTERFACE ${libTarget}\n")
@@ -799,7 +811,7 @@ function(tribits_extpkg_create_all_libs_target  tplName)
   # target_include_directories()
   if (TPL_${tplName}_INCLUDE_DIRS)
     string(APPEND configFileStr
-      "target_include_directories(${tplName}::all_libs SYSTEM\n")
+      "target_include_directories(TPL::${tplName}::all_libs SYSTEM\n")
     foreach (inclDir IN LISTS TPL_${tplName}_INCLUDE_DIRS)
       string(APPEND configFileStr
         "  INTERFACE \"${inclDir}\"\n")
@@ -810,7 +822,7 @@ function(tribits_extpkg_create_all_libs_target  tplName)
   # target_link_options()
   if (libLinkFlags)
     string(APPEND configFileStr
-      "target_link_options(${tplName}::all_libs\n")
+      "target_link_options(TPL::${tplName}::all_libs\n")
     foreach (likLinkFlag IN LISTS libLinkFlags)
       string(APPEND configFileStr
         "  INTERFACE \"${likLinkFlag}\"\n")
diff --git a/cmake/tribits/core/package_arch/TribitsProcessEnabledTpl.cmake b/cmake/tribits/core/package_arch/TribitsProcessEnabledTpl.cmake
index dcdde4ce6..aaec95d49 100644
--- a/cmake/tribits/core/package_arch/TribitsProcessEnabledTpl.cmake
+++ b/cmake/tribits/core/package_arch/TribitsProcessEnabledTpl.cmake
@@ -154,9 +154,9 @@ function(tribits_process_enabled_tpl  TPL_NAME)
     set(buildDirExternalPkgsDir
       "${${PROJECT_NAME}_BINARY_DIR}/${${PROJECT_NAME}_BUILD_DIR_EXTERNAL_PKGS_DIR}")
     set(tplConfigFile
-      "${buildDirExternalPkgsDir}/${TPL_NAME}/${TPL_NAME}Config.cmake")
+      "${buildDirExternalPkgsDir}/TPL-Seacas-${TPL_NAME}/TPL-Seacas-${TPL_NAME}Config.cmake")
     set(tplConfigVersionFile
-      "${buildDirExternalPkgsDir}/${TPL_NAME}/${TPL_NAME}ConfigVersion.cmake")
+      "${buildDirExternalPkgsDir}/TPL-Seacas-${TPL_NAME}/TPL-Seacas-${TPL_NAME}ConfigVersion.cmake")
     tribits_extpkg_write_config_version_file(${TPL_NAME}
       "${tplConfigVersionFile}")
     tribits_extpkg_install_config_file(${TPL_NAME} "${tplConfigFile}")
diff --git a/cmake/tribits/core/package_arch/TribitsTplFindIncludeDirsAndLibraries.cmake b/cmake/tribits/core/package_arch/TribitsTplFindIncludeDirsAndLibraries.cmake
index d3cb102cf..3641799ac 100644
--- a/cmake/tribits/core/package_arch/TribitsTplFindIncludeDirsAndLibraries.cmake
+++ b/cmake/tribits/core/package_arch/TribitsTplFindIncludeDirsAndLibraries.cmake
@@ -423,9 +423,7 @@ function(tribits_tpl_find_include_dirs_and_libraries TPL_NAME)
     set(TPL_CMAKE_FIND_LIBRARY_SUFFIXES ${TPL_CMAKE_FIND_LIBRARY_SUFFIXES_DEFAULT})
   else()
     if (WIN32)
-      set(CMAKE_FIND_LIBRARY_SUFFIXES .lib .a)
     else()
-      set(CMAKE_FIND_LIBRARY_SUFFIXES .a )
     endif()
   endif()
   #print_var(CMAKE_FIND_LIBRARY_SUFFIXES)
@@ -717,7 +717,7 @@ function(tribits_tpl_find_include_dirs_and_libraries TPL_NAME)
   set(buildDirExternalPkgsDir
     "${${PROJECT_NAME}_BINARY_DIR}/${${PROJECT_NAME}_BUILD_DIR_EXTERNAL_PKGS_DIR}")
-  set(tplConfigFileBaseDir "${buildDirExternalPkgsDir}/${TPL_NAME}")
+  set(tplConfigFileBaseDir "${buildDirExternalPkgsDir}/TPL-Seacas-${TPL_NAME}")
-  set(tplConfigFile "${tplConfigFileBaseDir}/${TPL_NAME}Config.cmake")
+  set(tplConfigFile "${tplConfigFileBaseDir}/TPL-Seacas-${TPL_NAME}Config.cmake")
   tribits_extpkg_write_config_file(${TPL_NAME} "${tplConfigFile}")
   if (NOT ${PROJECT_NAME}_ENABLE_INSTALLATION_TESTING)
     include("${tplConfigFile}")
diff --git a/cmake/tribits/core/package_arch/TribitsWriteClientExportFiles.cmake b/cmake/tribits/core/package_arch/TribitsWriteClientExportFiles.cmake
index 1f31b6ec0..9bc1057b7 100644
--- a/cmake/tribits/core/package_arch/TribitsWriteClientExportFiles.cmake
+++ b/cmake/tribits/core/package_arch/TribitsWriteClientExportFiles.cmake
@@ -289,7 +289,11 @@ function(tribits_write_flexible_package_client_export_files)
 
   set(${PACKAGE_NAME}_TPL_LIBRARIES "")
   foreach(TPL ${ORDERED_FULL_TPL_SET})
-    list(APPEND ${PACKAGE_NAME}_TPL_LIBRARIES ${TPL}::all_libs)
+    if(TARGET TPL::${TPL}::all_libs)
+        list(APPEND ${PACKAGE_NAME}_TPL_LIBRARIES TPL::${TPL}::all_libs)
+    else()
+        list(APPEND ${PACKAGE_NAME}_TPL_LIBRARIES ${TPL}::all_libs)
+    endif()
   endforeach()
 
   # Generate a note discouraging editing of the <package>Config.cmake file
@@ -587,17 +591,21 @@ function(tribits_append_dependent_package_config_file_includes_and_enables packa
     "\n# Include configuration of dependent packages\n")
   foreach(depPkg IN LISTS ${packageName}_LIB_ENABLED_DEPENDENCIES)
     set(packageConfigBaseDir "") # Initially, no add include()
+    set(packageTarget "${depPkg}::all_libs")
+    set(packageConfig "${depPkg}Config.cmake")
     if (${depPkg}_PACKAGE_BUILD_STATUS STREQUAL "INTERNAL")
       set(packageConfigBaseDir "${pkgConfigFileBaseDir}/${depPkg}")
     elseif (${depPkg}_PACKAGE_BUILD_STATUS STREQUAL "EXTERNAL")
-      set(packageConfigBaseDir "${extPkgConfigFileBaseDir}/${depPkg}")
+      set(packageConfigBaseDir "${extPkgConfigFileBaseDir}/TPL-Seacas-${depPkg}")
+      set(packageTarget "TPL::${depPkg}::all_libs")
+      set(packageConfig "TPL-Seacas-${depPkg}Config.cmake")
     else()
       message(FATAL_ERROR "ERROR: ${depPkg}_PACKAGE_BUILD_STATUS='${${depPkg}_PACKAGE_BUILD_STATUS}' invalid!")
     endif()
     if (packageConfigBaseDir)
       string(APPEND configFileStr
-        "if (NOT TARGET ${depPkg}::all_libs)\n"
-        "  include(\"${packageConfigBaseDir}/${depPkg}Config.cmake\")\n"
+        "if (NOT TARGET ${packageTarget})\n"
+        "  include(\"${packageConfigBaseDir}/${packageConfig}\")\n"
         "endif()\n"
         )
     endif()
diff --git a/cmake/tribits/core/package_arch/TribitsExternalPackageFindTplHelpers.cmake b/cmake/tribits/core/package_arch/TribitsExternalPackageFindTplHelpers.cmake
index 1873267bd..ed6254632 100644
--- a/cmake/tribits/core/package_arch/TribitsExternalPackageFindTplHelpers.cmake
+++ b/cmake/tribits/core/package_arch/TribitsExternalPackageFindTplHelpers.cmake
@@ -120,17 +120,21 @@ function(tribits_extpkg_create_package_config_file_with_imported_targets
       "set(${externalPkg}_DIR \"${${externalPkg}_DIR}\")\n" )
   endif()
   string(APPEND configFileStr
-    "find_dependency(${externalPkg})\n"
-    "add_library(${tplName}::all_libs  INTERFACE  IMPORTED  GLOBAL)\n"
+    "find_dependency(${externalPkg})\n" # This is outside TPL
+    "if(NOT TARGET ${tplName}::all_libs)\n"
+    "  add_library(${tplName}::all_libs  INTERFACE  IMPORTED  GLOBAL)\n" 
     )
   foreach (importedTarget  IN LISTS  PARSE_IMPORTED_TARGETS_FOR_ALL_LIBS)
     string(APPEND configFileStr
-      "target_link_libraries(${tplName}::all_libs  INTERFACE  ${importedTarget})\n")
+      "  target_link_libraries(${tplName}::all_libs  INTERFACE  ${importedTarget})\n")
   endforeach()
+  string(APPEND configFileStr
+    "endif()\n"
+  )
   set(buildDirExternalPkgsDir
     "${${PROJECT_NAME}_BINARY_DIR}/${${PROJECT_NAME}_BUILD_DIR_EXTERNAL_PKGS_DIR}")
   set(tplConfigFile
-    "${buildDirExternalPkgsDir}/${tplName}/${tplName}Config.cmake")
+    "${buildDirExternalPkgsDir}/TPL-Seacas-${tplName}/TPL-Seacas-${tplName}Config.cmake")
   file(WRITE "${tplConfigFile}" "${configFileStr}")
 
 endfunction()
diff --git a/cmake/tribits/core/package_arch/TribitsExternalPackageWriteConfigFile.cmake b/cmake/tribits/core/package_arch/TribitsExternalPackageWriteConfigFile.cmake
index c49a2c019..7c906ce26 100644
--- a/cmake/tribits/core/package_arch/TribitsExternalPackageWriteConfigFile.cmake
+++ b/cmake/tribits/core/package_arch/TribitsExternalPackageWriteConfigFile.cmake
@@ -334,9 +334,10 @@ function(tribits_extpkg_add_find_upstream_dependencies_str
       endif()
       string(APPEND configFileFragStr
         "if (NOT TARGET TPL::${upstreamTplDepName}::all_libs)\n"
-        "  set(${upstreamTplDepName}_DIR \"\${CMAKE_CURRENT_LIST_DIR}/../${upstreamTplDepName}\")\n"
+        "  set(TPL-Seacas-${upstreamTplDepName}_DIR \"\${CMAKE_CURRENT_LIST_DIR}/../TPL-Seacas-${upstreamTplDepName}\")\n" # Looks for internal dep
+        "  # Really only upstream ? NO ! THis is for ::all_libs targets in internal consumers\n"
         "  find_dependency(TPL-Seacas-${upstreamTplDepName} REQUIRED CONFIG \${${tplName}_SearchNoOtherPathsArgs})\n"
-        "  unset(${upstreamTplDepName}_DIR)\n"
+        "  unset(TPL-Seacas-${upstreamTplDepName}_DIR)\n"
         "endif()\n"
         "\n"
         )
