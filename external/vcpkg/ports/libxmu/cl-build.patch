diff --git a/src/EditresCom.c b/src/EditresCom.c
index 716a2b3c6..d570e19e4 100644
--- a/src/EditresCom.c
+++ b/src/EditresCom.c
@@ -34,6 +34,9 @@ in this Software without prior written authorization from The Open Group.
 #include <X11/IntrinsicP.h>	/* To get into the composite and core widget
 				   structures. */
 #include <X11/ObjectP.h>	/* For XtIs<Classname> macros. */
+#ifdef _MSC_VER
+#define XTSTRINGDEFINES // Otherwise XtRImmediate is not a constant expression
+#endif
 #include <X11/StringDefs.h>	/* for XtRString. */
 #include <X11/ShellP.h>		/* for Application Shell Widget class. */
 
