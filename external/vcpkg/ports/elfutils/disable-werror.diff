diff --git a/config/eu.am b/config/eu.am
index e6c241f..4136e7c 100644
--- a/config/eu.am
+++ b/config/eu.am
@@ -99,7 +99,6 @@ AM_CFLAGS = -std=gnu99 -Wall -Wshadow -Wformat=2 \
 	    $(LOGICAL_OP_WARNING) $(DUPLICATED_COND_WARNING) \
 	    $(NULL_DEREFERENCE_WARNING) $(IMPLICIT_FALLTHROUGH_WARNING) \
 	    $(USE_AFTER_FREE3_WARNING) \
-	    $(if $($(*F)_no_Werror),,-Werror) \
 	    $(if $($(*F)_no_Wunused),,-Wunused -Wextra) \
 	    $(if $($(*F)_no_Wstack_usage),,$(STACK_USAGE_WARNING)) \
 	    $(if $($(*F)_no_Wpacked_not_aligned),$(NO_PACKED_NOT_ALIGNED_WARNING),) \
@@ -109,7 +108,6 @@ AM_CXXFLAGS = -std=c++11 -Wall -Wshadow \
 	   $(TRAMPOLINES_WARNING) \
 	   $(LOGICAL_OP_WARNING) $(DUPLICATED_COND_WARNING) \
 	   $(NULL_DEREFERENCE_WARNING) $(IMPLICIT_FALLTHROUGH_WARNING) \
-	   $(if $($(*F)_no_Werror),,-Werror) \
 	   $(if $($(*F)_no_Wunused),,-Wunused -Wextra) \
 	   $(if $($(*F)_no_Wstack_usage),,$(STACK_USAGE_WARNING)) \
 	   $(if $($(*F)_no_Wpacked_not_aligned),$(NO_PACKED_NOT_ALIGNED_WARNING),) \
