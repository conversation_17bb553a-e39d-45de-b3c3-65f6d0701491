diff --git a/config/libdw.pc.in b/config/libdw.pc.in
index 2e83a43..bdfc808 100644
--- a/config/libdw.pc.in
+++ b/config/libdw.pc.in
@@ -18,5 +18,4 @@ Requires: libelf = @VERSION@
 # We support various compressed ELF images, but don't export any of the
 # data structures or functions.  zlib (gz) is always required, bzip2 (bz2)
 # lzma (xz) and zstd () are optional. But bzip2 doesn't have a pkg-config file.
-Requires.private: zlib @LIBLZMA@ @LIBZSTD@
-Libs.private: @BZ2_LIB@
+Requires.private: zlib @LIBLZMA@ @LIBZSTD@ bzip2
diff --git a/configure.ac b/configure.ac
index f191488..fe617ac 100644
--- a/configure.ac
+++ b/configure.ac
@@ -429,6 +429,8 @@ AM_CONDITIONAL(TESTS_RPATH, test "$tests_use_rpath" = yes)
 dnl zlib is mandatory.
 save_LIBS="$LIBS"
 LIBS=
+PKG_PROG_PKG_CONFIG
+PKG_CHECK_MODULES([ZLIB], [zlib], [LIBS="$ZLIB_LIBS $LIBS"], AC_MSG_ERROR([zlib not found]))
 eu_ZIPLIB(zlib,ZLIB,z,gzdirect,gzip)
 AS_IF([test "x$with_zlib" = xno], [AC_MSG_ERROR([zlib not found but is required])])
 LIBS="$save_LIBS"
@@ -437,18 +439,21 @@ dnl Test for bzlib and xz/lzma/zstd, gives BZLIB/LZMALIB/ZSTD .am
 dnl conditional and config.h USE_BZLIB/USE_LZMALIB/USE_ZSTD #define.
 save_LIBS="$LIBS"
 LIBS=
+PKG_CHECK_MODULES([BZLIB], [bzip2], [LIBS="$BZLIB_LIBS $LIBS"], AC_MSG_ERROR([bzip2 not found]))
 eu_ZIPLIB(bzlib,BZLIB,bz2,BZ2_bzdopen,bzip2)
 # We need this since bzip2 doesn't have a pkgconfig file.
 BZ2_LIB="$LIBS"
 AC_SUBST([BZ2_LIB])
 save_LIBS="$LIBS"
 LIBS=
+PKG_CHECK_MODULES([LZMA], [liblzma], [LIBS="$LZMA_LIBS $LIBS"], AC_MSG_ERROR([liblzma not found]))
 eu_ZIPLIB(lzma,LZMA,lzma,lzma_auto_decoder,[LZMA (xz)])
 lzma_LIBS="$LIBS"
 LIBS="$lzma_LIBS $save_LIBS"
 AS_IF([test "x$with_lzma" = xyes], [LIBLZMA="liblzma"], [LIBLZMA=""])
 AC_SUBST([lzma_LIBS])
 AC_SUBST([LIBLZMA])
+PKG_CHECK_MODULES([ZSTD], [libzstd], [LIBS="$ZSTDLIBS $LIBS"], AC_MSG_ERROR([libzstd not found]))
 eu_ZIPLIB(zstd,ZSTD,zstd,ZSTD_decompress,[ZSTD (zst)])
 AS_IF([test "x$with_zstd" = xyes], [LIBZSTD="libzstd"], [LIBLZSTD=""])
 AC_SUBST([LIBZSTD])
diff --git a/debuginfod/Makefile.am b/debuginfod/Makefile.am
index 0a129fa..b90ed23 100644
--- a/debuginfod/Makefile.am
+++ b/debuginfod/Makefile.am
@@ -42,8 +42,8 @@ program_transform_name = s,x,x,
 
 if BUILD_STATIC
 libasm = ../libasm/libasm.a
-libdw = ../libdw/libdw.a -lz $(zip_LIBS) $(libelf) $(libebl) -ldl -lpthread
-libelf = ../libelf/libelf.a -lz
+libdw = ../libdw/libdw.a $(ZLIB_LIBS) $(zip_LIBS) $(libelf) $(libebl) -ldl -lpthread
+libelf = ../libelf/libelf.a $(ZLIB_LIBS) $(zip_LIBS)
 if DUMMY_LIBDEBUGINFOD
 libdebuginfod = ./libdebuginfod.a
 else
diff --git a/libdw/Makefile.am b/libdw/Makefile.am
index 5363c02..045cde5 100644
--- a/libdw/Makefile.am
+++ b/libdw/Makefile.am
@@ -110,7 +110,7 @@ libdw_so_LIBS = ../libebl/libebl_pic.a ../backends/libebl_backends_pic.a \
 		../libcpu/libcpu_pic.a libdw_pic.a ../libdwelf/libdwelf_pic.a \
 		../libdwfl/libdwfl_pic.a
 libdw_so_DEPS = ../lib/libeu.a ../libelf/libelf.so
-libdw_so_LDLIBS = $(libdw_so_DEPS) -ldl -lz $(argp_LDADD) $(fts_LIBS) $(obstack_LIBS) $(zip_LIBS) -pthread
+libdw_so_LDLIBS = $(libdw_so_DEPS) -ldl $(ZLIB_LIBS) $(argp_LDADD) $(fts_LIBS) $(obstack_LIBS) $(zip_LIBS) -pthread
 libdw.so: $(srcdir)/libdw.map $(libdw_so_LIBS) $(libdw_so_DEPS)
 	$(AM_V_CCLD)$(LINK) $(dso_LDFLAGS) -o $@ \
 		-Wl,--soname,$@.$(VERSION),--enable-new-dtags \
diff --git a/libelf/Makefile.am b/libelf/Makefile.am
index 3402863..30a331e 100644
--- a/libelf/Makefile.am
+++ b/libelf/Makefile.am
@@ -106,7 +106,7 @@ libelf_pic_a_SOURCES =
 am_libelf_pic_a_OBJECTS = $(libelf_a_SOURCES:.c=.os)
 
 libelf_so_DEPS = ../lib/libeu.a
-libelf_so_LDLIBS = $(libelf_so_DEPS) -lz $(zstd_LIBS)
+libelf_so_LDLIBS = $(libelf_so_DEPS) $(ZLIB_LIBS) $(zstd_LIBS)
 if USE_LOCKS
 libelf_so_LDLIBS += -lpthread
 endif
diff --git a/src/Makefile.am b/src/Makefile.am
index 6bdf2df..2fd5b42 100644
--- a/src/Makefile.am
+++ b/src/Makefile.am
@@ -47,8 +47,8 @@ CLEANFILES += make-debug-archive
 
 if BUILD_STATIC
 libasm = ../libasm/libasm.a
-libdw = ../libdw/libdw.a -lz $(zip_LIBS) $(libelf) -ldl -lpthread
-libelf = ../libelf/libelf.a -lz $(zstd_LIBS)
+libdw = ../libdw/libdw.a $(ZLIB_LIBS) $(zip_LIBS) $(libelf) -ldl -lpthread
+libelf = ../libelf/libelf.a $(ZLIB_LIBS) $(zstd_LIBS)
 if LIBDEBUGINFOD
 libdebuginfod = ../debuginfod/libdebuginfod.a -lpthread $(libcurl_LIBS) $(crypto_LIBS) $(jsonc_LIBS)
 else
