{"name": "netcdf-c", "version": "4.8.1", "port-version": 5, "description": "A set of self-describing, machine-independent data formats that support the creation, access, and sharing of array-oriented scientific data.", "homepage": "https://github.com/Unidata/netcdf-c", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "default-features": ["dap", {"name": "hdf5", "platform": "!uwp & !(arm64 & windows)"}, "nczarr", "netcdf-4"], "features": {"dap": {"description": "Build with DAP remote access client support", "dependencies": [{"name": "curl", "default-features": false}]}, "hdf5": {"description": "Build with HDF5 support", "dependencies": [{"name": "hdf5", "default-features": false, "features": ["zlib"]}, {"name": "netcdf-c", "default-features": false, "features": ["netcdf-4"]}, {"name": "vcpkg-pkgconfig-get-modules", "host": true}]}, "nczarr": {"description": "Build with NCZarr cloud storage access support", "dependencies": [{"name": "curl", "default-features": false}, {"name": "netcdf-c", "default-features": false, "features": ["netcdf-4"]}]}, "nczarr-zip": {"description": "Build with NCZarr ZIP support", "dependencies": [{"name": "libzip", "default-features": false}, {"name": "netcdf-c", "default-features": false, "features": ["nczarr"]}]}, "netcdf-4": {"description": "Build with netCDF-4 support"}, "tools": {"description": "Build utilities"}}}