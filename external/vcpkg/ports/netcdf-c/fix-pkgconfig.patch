diff --git a/CMakeLists.txt b/CMakeLists.txt
index 390ac0a..4bcd909 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -2100,6 +2100,21 @@ STRING(REPLACE ";" " " LINKFLAGS "${LINKFLAGS}")
 SET(LIBS ${NC_LIBS})
 SET(NC_LIBS "-lnetcdf")
 
+set(NC_LIBS_PRIVATE " ${LIBS} ")
+set(NC_REQUIRES_PRIVATE "")
+macro(replace_pkgconfig_module PATTERN MODULE)
+  if(NC_LIBS_PRIVATE MATCHES " ${PATTERN} ")
+    string(REPLACE "${CMAKE_MATCH_0}" " " NC_LIBS_PRIVATE "${NC_LIBS_PRIVATE}")
+    string(APPEND NC_REQUIRES_PRIVATE " ${MODULE}")
+  endif()
+endmacro()
+replace_pkgconfig_module("-lhdf5_hl"   "hdf5_hl")
+replace_pkgconfig_module("-lhdf5"      "hdf5")
+replace_pkgconfig_module("-lmpi"       "ompi-c")
+replace_pkgconfig_module("-lzip::zip"  "libzip")
+replace_pkgconfig_module("-lCURL[^ ]*" "libcurl")
+replace_pkgconfig_module("-lZLIB[^ ]*" "zlib")
+
 configure_file(
   ${netCDF_SOURCE_DIR}/netcdf.pc.in
   ${netCDF_BINARY_DIR}/netcdf.pc @ONLY)
diff --git a/netcdf.pc.in b/netcdf.pc.in
index 22b5594..a48b7c2 100644
--- a/netcdf.pc.in
+++ b/netcdf.pc.in
@@ -9,5 +9,6 @@ Description: NetCDF Client Library for C
 URL: http://www.unidata.ucar.edu/netcdf
 Version: @PACKAGE_VERSION@
 Libs: -L${libdir} @NC_LIBS@
-Libs.private: @LIBS@
+Libs.private: @NC_LIBS_PRIVATE@
 Cflags: -I${includedir}
+Requires.private: @NC_REQUIRES_PRIVATE@
