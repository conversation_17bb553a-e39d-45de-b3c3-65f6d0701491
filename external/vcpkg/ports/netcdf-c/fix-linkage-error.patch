diff --git a/liblib/CMakeLists.txt b/liblib/CMakeLists.txt
index 9e126ec..1363d35 100644
--- a/liblib/CMakeLists.txt
+++ b/liblib/CMakeLists.txt
@@ -79,8 +79,14 @@ ENDIF()
 IF(USE_HDF5)
   if(TARGET hdf5::hdf5-shared)
     SET(TLL_LIBS ${TLL_LIBS} hdf5::hdf5-shared hdf5::hdf5_hl-shared)
+    if(USE_PARALLEL)
+      list(APPEND TLL_LIBS ${MPI_C_LIBRARIES})
+    endif()
   else()
     SET(TLL_LIBS ${TLL_LIBS} hdf5::hdf5-static hdf5::hdf5_hl-static)
+    if(USE_PARALLEL)
+      list(APPEND TLL_LIBS ${MPI_C_LIBRARIES})
+    endif()
   endif()
 elseif(0)
   IF(NOT MSVC)
