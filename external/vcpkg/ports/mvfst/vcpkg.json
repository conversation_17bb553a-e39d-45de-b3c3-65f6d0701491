{"name": "mvfst", "version-string": "2025.03.31.00", "description": "mvfst (Pronounced move fast) is a client and server implementation of IETF QUIC protocol in C++ by Facebook.", "homepage": "https://github.com/facebook/mvfst", "license": "MIT", "supports": "!(windows & !static)", "dependencies": ["boost-context", "boost-date-time", "boost-filesystem", "boost-iostreams", "boost-program-options", "boost-regex", "boost-system", "boost-thread", "fizz", "fmt", "folly", "glog", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}]}