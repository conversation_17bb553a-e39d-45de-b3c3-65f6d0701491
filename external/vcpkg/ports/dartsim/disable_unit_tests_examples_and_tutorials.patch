﻿diff --git a/CMakeLists.txt b/CMakeLists.txt
index fc5249444a5c..39c29e92bb33 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -376,7 +376,7 @@ add_subdirectory(dart)
 
 set(DART_IN_SOURCE_BUILD TRUE)
 
-if(TARGET dart)
+if(0)
 
   # Add a "tests" target to build unit tests.
   include(CTest)
diff --git a/python/CMakeLists.txt b/python/CMakeLists.txt
index 37cadf4f8de1..fcfbd13cf793 100644
--- a/python/CMakeLists.txt
+++ b/python/CMakeLists.txt
@@ -22,9 +22,11 @@ endif()
 set(DART_DARTPY_BUILD_DIR "${CMAKE_CURRENT_BINARY_DIR}/dartpy")
 
 add_subdirectory(dartpy)
+if(0)
 add_subdirectory(tests)
 add_subdirectory(examples)
 add_subdirectory(tutorials)
+endif()
 
 message(STATUS "")
 message(STATUS "[ dartpy ]")
