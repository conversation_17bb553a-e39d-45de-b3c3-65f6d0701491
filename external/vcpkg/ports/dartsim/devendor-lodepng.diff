diff --git a/cmake/dart_Component.cmake.in b/cmake/dart_Component.cmake.in
index 2a2d54e..a421adb 100644
--- a/cmake/dart_Component.cmake.in
+++ b/cmake/dart_Component.cmake.in
@@ -1,3 +1,7 @@
+if("@component@" STREQUAL "external-lodepng")
+  include(CMakeFindDependencyMacro)
+  find_dependency(lodepng)
+endif()
 set("@PROJECT_NAME@_@component@_DEPENDENCIES" @internal_dependencies@)
 set("@PROJECT_NAME@_@component@_LIBRARIES" @libraries@)
 
diff --git a/dart/external/lodepng/CMakeLists.txt b/dart/external/lodepng/CMakeLists.txt
index 93b4e73..5309d7b 100644
--- a/dart/external/lodepng/CMakeLists.txt
+++ b/dart/external/lodepng/CMakeLists.txt
@@ -6,10 +6,12 @@ file(GLOB srcs "*.cpp")
 set(target_name ${PROJECT_NAME}-external-lodepng)
 set(component_name external-lodepng)
 
+find_package(lodepng CONFIG)
+set(hdrs "")
+set(srcs "${CMAKE_CURRENT_BINARY_DIR}/empty.cpp")
+file(WRITE "${srcs}" "")
 dart_add_library(${target_name} ${hdrs} ${srcs})
-target_include_directories(${target_name}
-  PUBLIC $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}>
-)
+target_link_libraries(${target_name} PUBLIC $<IF:$<TARGET_EXISTS:lodepng>,lodepng,lodepng::NOTFOUND>)
 
 # Component
 add_component(${PROJECT_NAME} ${component_name})
diff --git a/dart/external/lodepng/lodepng.h b/dart/external/lodepng/lodepng.h
index fcf9f71..974d826 100644
--- a/dart/external/lodepng/lodepng.h
+++ b/dart/external/lodepng/lodepng.h
@@ -23,8 +23,11 @@ freely, subject to the following restrictions:
     distribution.
 */
 
-#ifndef LODEPNG_H
-#define LODEPNG_H
+#ifndef LODEPNG_H_DEVENDORED
+#define LODEPNG_H_DEVENDORED
+#include <lodepng.h>
+#endif
+#if 0
 
 #include <string.h> /*for size_t*/
 
