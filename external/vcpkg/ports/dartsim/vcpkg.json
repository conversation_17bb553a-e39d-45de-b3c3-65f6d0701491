{"name": "<PERSON><PERSON>", "version": "6.15.0", "port-version": 4, "description": "Dynamic Animation and Robotics Toolkit", "homepage": "https://dartsim.github.io/", "license": "BSD-2-<PERSON><PERSON>", "supports": "!x86 & !arm32", "dependencies": ["assimp", "eigen3", "fcl", "fmt", "lodepng", "octomap", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"collision-bullet": {"description": "Build dart-collision-bullet library", "dependencies": ["bullet3"]}, "collision-ode": {"description": "Build dart-collision-ode library", "dependencies": ["ode"]}, "gui": {"description": "Build dart-gui library", "dependencies": [{"name": "<PERSON><PERSON>", "default-features": false, "features": ["utils"]}, {"name": "freeglut", "platform": "!osx"}, "opengl"]}, "gui-osg": {"description": "Build dart-gui-osg library", "dependencies": [{"name": "<PERSON><PERSON>", "default-features": false, "features": ["gui"]}, {"name": "imgui", "default-features": false, "features": ["opengl2-binding"]}, {"name": "osg", "default-features": false}]}, "spdlog": {"description": "Support spdlog as underlying logging framework", "dependencies": ["spdlog"]}, "utils": {"description": "Build dart-utils library", "dependencies": ["tinyxml2"]}, "utils-urdf": {"description": "Build dart-utils-urdf library", "dependencies": [{"name": "<PERSON><PERSON>", "default-features": false, "features": ["utils"]}, "urd<PERSON><PERSON>"]}}}