diff --git a/util/cairo-script/cairo-script-objects.c b/util/cairo-script/cairo-script-objects.c
index 2d7937be3..4d55ca848 100644
--- a/util/cairo-script/cairo-script-objects.c	
+++ b/util/cairo-script/cairo-script-objects.c
@@ -127,7 +127,7 @@ csi_array_append (csi_t *ctx,
     return _csi_stack_push (ctx, &array->stack, csi_object_reference (obj));
 }
 
-inline csi_status_t
+csi_status_t
 _csi_array_execute (csi_t *ctx, csi_array_t *array)
 {
     csi_integer_t i;
diff --git a/meson.build b/meson.build
index 7b20c0c48..9cb4a82ba 100644
--- a/meson.build	
+++ b/meson.build
@@ -38,7 +38,7 @@ cc = meson.get_compiler('c')
 
 # Compiler flags
 cflags = []
-if cc.get_id() != 'msvc'
+if cc.get_argument_syntax() != 'msvc'
   cflags += [
     '-Wmissing-declarations',
     '-Werror-implicit-function-declaration',
@@ -159,8 +159,7 @@ check_headers = [
 
 check_types = [
   ['uint64_t', {'headers': ['stdint.h']}],
-  ['uint128_t', {'headers': ['stdint.h']}],
-  ['__uint128_t']
+  ['uint128_t', {'headers': ['stdint.h']}]
 ]
 
 check_funcs = [
 