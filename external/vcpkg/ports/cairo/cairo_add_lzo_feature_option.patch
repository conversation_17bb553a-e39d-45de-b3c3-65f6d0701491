diff --git a/meson.build b/meson.build
index 03eb140f0..eadd32088 100644
--- a/meson.build
+++ b/meson.build
@@ -196,7 +196,7 @@ else
   endif
 endif
 
-lzo_dep = dependency('lzo2', required: false)
+lzo_dep = dependency('lzo2', required: get_option('lzo'))
 if lzo_dep.found()
   conf.set('HAVE_LZO', 1)
 endif
diff --git a/meson_options.txt b/meson_options.txt
index f481ef3ea..3ba4dacb1 100644
--- a/meson_options.txt
+++ b/meson_options.txt
@@ -23,6 +23,7 @@ option('glib', type : 'feature', value : 'auto')
 option('spectre', type : 'feature', value : 'auto')
 option('symbol-lookup', type: 'feature', value : 'auto',
        description: 'Symbol lookup in debug utils via binutils/bfd')
+option('lzo', type : 'feature', value : 'auto')
 
 # Documentation
 option('gtk_doc', type : 'boolean', value : false,
