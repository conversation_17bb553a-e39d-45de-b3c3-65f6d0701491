diff --git a/CMakeLists.txt b/CMakeLists.txt
index 86510b3..acca8be 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -36,10 +36,9 @@ if(LSQPACK_XXH)
     target_sources(ls-qpack PRIVATE deps/xxhash/xxhash.c)
     set(LSQPACK_DEPENDS "")
 else()
-	find_package(PkgConfig REQUIRED)
-	pkg_check_modules(XXH REQUIRED IMPORTED_TARGET libxxhash)
-	target_link_libraries(ls-qpack PUBLIC PkgConfig::XXH)
-	set(LSQPACK_DEPENDS "libxxhash")
+    find_package(xxHash CONFIG REQUIRED)
+    target_link_libraries(ls-qpack PRIVATE xxHash::xxhash)
+    set(LSQPACK_DEPENDS "libxxhash")
 endif()
 
 if(WIN32)
@@ -125,10 +124,14 @@ endif()
 include(GNUInstallDirs)
 configure_file(lsqpack.pc.in lsqpack.pc @ONLY)
 
+configure_file(ls-qpack-config.cmake.in ls-qpack-config.cmake @ONLY)
 install(TARGETS ls-qpack EXPORT ls-qpack-config
         LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR})
+install(FILES "${CMAKE_CURRENT_BINARY_DIR}/ls-qpack-config.cmake"
+    DESTINATION ${CMAKE_INSTALL_LIBDIR}/cmake/ls-qpack)
 install(
     EXPORT ls-qpack-config
+    FILE ls-qpack-targets.cmake
     NAMESPACE ls-qpack::
     DESTINATION ${CMAKE_INSTALL_LIBDIR}/cmake/ls-qpack
 )
diff --git a/ls-qpack-config.cmake.in b/ls-qpack-config.cmake.in
new file mode 100644
index 0000000..533e076
--- /dev/null
+++ b/ls-qpack-config.cmake.in
@@ -0,0 +1,5 @@
+if(NOT "@BUILD_SHARED_LIBS@" AND NOT "@LSQPACK_XXH@")
+    include(CMakeFindDependencyMacro)
+    find_dependency(xxHash CONFIG)
+endif()
+include("${CMAKE_CURRENT_LIST_DIR}/ls-qpack-targets.cmake")
