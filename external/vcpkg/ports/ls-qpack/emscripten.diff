diff --git a/CMakeLists.txt b/CMakeLists.txt
index bcb58e3..320144b 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -41,7 +41,7 @@ else()
     set(LSQPACK_DEPENDS "libxxhash")
 endif()
 
-if(WIN32)
+if(WIN32 OR EMSCRIPTEN)
     target_include_directories(ls-qpack PUBLIC
         $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/wincompat>
         $<INSTALL_INTERFACE:include>
@@ -149,6 +149,6 @@ if(LSQPACK_BIN)
           DESTINATION ${CMAKE_INSTALL_BINDIR})
 endif()
 
-if(WIN32)
+if(WIN32 OR EMSCRIPTEN)
     install(DIRECTORY wincompat/sys DESTINATION include)
 endif()
