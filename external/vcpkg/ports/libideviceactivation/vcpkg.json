{"name": "libideviceactivation", "version-date": "2023-05-01", "port-version": 1, "description": "A library to handle the activation process of iOS devices", "homepage": "https://libimobiledevice.org/", "license": "LGPL-2.1-or-later", "supports": "!uwp", "dependencies": ["curl", "libimobiledevice", "libplist", "libxml2", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"tools": {"description": "build command line tool", "supports": "!android & !ios & !xbox"}}}