{"name": "gdk-pixbuf", "version": "2.42.12", "port-version": 2, "description": "Image loading library.", "homepage": "https://gitlab.gnome.org/GNOME/gdk-pixbuf", "license": "LGPL-2.1-or-later", "supports": "!xbox", "dependencies": [{"name": "gettext", "host": true, "features": ["tools"]}, "gettext-libintl", "glib", {"name": "glib", "host": true}, {"name": "vcpkg-tool-meson", "host": true}], "default-features": ["jpeg", "png", "tiff"], "features": {"introspection": {"description": "build with introspection", "dependencies": ["gobject-introspection"]}, "jpeg": {"description": "Enable JPEG loader (requires libjpeg)", "dependencies": ["libjpeg-turbo"]}, "others": {"description": "Enable other loaders, which are weakly maintained (ani, bmp, icns, ico, pnm, qtif, tga, xbm, xpm)"}, "png": {"description": "Enable PNG loader (requires libpng)", "dependencies": ["libpng"]}, "tiff": {"description": "Enable TIFF loader (requires libtiff)", "dependencies": [{"name": "tiff", "default-features": false}]}}}