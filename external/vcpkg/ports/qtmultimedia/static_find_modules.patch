diff --git a/cmake/FindFFmpeg.cmake b/cmake/FindFFmpeg.cmake
index 47d8769..46a5c9b 100644
--- a/cmake/FindFFmpeg.cmake
+++ b/cmake/FindFFmpeg.cmake
@@ -215,7 +215,7 @@ foreach (_component ${FFmpeg_FIND_COMPONENTS})
   find_component(${_component} "lib${library}" ${library} "lib${library}/${library}.h")
 
   if (${_component}_FOUND)
-    list(APPEND FFMPEG_LIBRARIES    ${${_component}_LIBRARY_NAME})
+    list(APPEND FFMPEG_LIBRARIES    ${${_component}_LIBRARY})
     list(APPEND FFMPEG_DEFINITIONS  ${${_component}_DEFINITIONS})
     list(APPEND FFMPEG_INCLUDE_DIRS ${${_component}_INCLUDE_DIR})
     list(APPEND FFMPEG_LIBRARY_DIRS ${${_component}_LIBRARY_DIR})
@@ -306,18 +306,19 @@ endfunction()
         string(TOLOWER ${_component} _lowerComponent)
         if (NOT TARGET FFmpeg::${_lowerComponent})
-            add_library(FFmpeg::${_lowerComponent} INTERFACE IMPORTED)
+            add_library(FFmpeg::${_lowerComponent} UNKNOWN IMPORTED)
             set_target_properties(FFmpeg::${_lowerComponent} PROPERTIES
                 INTERFACE_COMPILE_OPTIONS "${${_component}_DEFINITIONS}"
                 INTERFACE_INCLUDE_DIRECTORIES ${${_component}_INCLUDE_DIR}
-                INTERFACE_LINK_LIBRARIES "${${_component}_LIBRARY_NAME}"
-                INTERFACE_LINK_DIRECTORIES "${${_component}_LIBRARY_DIR}"
+                IMPORTED_LOCATION "${${_component}_LIBRARY}"
             )
 
              __ffmpeg_internal_set_dependencies(${_component})
-             target_link_libraries(FFmpeg::${_lowerComponent} INTERFACE "${${_component}_LIBRARY_NAME}")
+             if(WIN32 AND _lowerComponent STREQUAL "avutil")
+               target_link_libraries(FFmpeg::${_lowerComponent} INTERFACE "Bcrypt.lib")
+            endif()
             if (UNIX AND NOT APPLE)
                 target_link_options(FFmpeg::${_lowerComponent} INTERFACE  "-Wl,--exclude-libs=lib${_lowerComponent}")
             endif ()
         endif()
     endif()
 endforeach ()
@@ -361,14 +364,18 @@ if (shared_libs_desired AND NOT FFMPEG_SHARED_COMPONENTS)
 endif()
 
 if (NOT TARGET FFmpeg::FFmpeg)
-  add_library(FFmpeg INTERFACE)
+  add_library(FFmpeg INTERFACE IMPORTED)
   set_target_properties(FFmpeg PROPERTIES
       INTERFACE_COMPILE_OPTIONS "${FFMPEG_DEFINITIONS}"
       INTERFACE_INCLUDE_DIRECTORIES "${FFMPEG_INCLUDE_DIRS}"
-      INTERFACE_LINK_LIBRARIES "${FFMPEG_LIBRARIES}"
       INTERFACE_LINK_DIRECTORIES "${FFMPEG_LIBRARY_DIRS}"
   )
+  target_link_libraries(FFmpeg INTERFACE ${FFMPEG_LIBRARIES})
-  add_library(FFmpeg::FFmpeg ALIAS FFmpeg)
+  if(WIN32)
+    target_link_libraries(FFmpeg INTERFACE "Bcrypt.lib")
+  endif()
+  add_library(FFmpeg::FFmpeg INTERFACE IMPORTED)
+  target_link_libraries(FFmpeg::FFmpeg INTERFACE FFmpeg)
 endif()
 
 # Compile the list of required vars
diff --git a/cmake/FindGObject.cmake b/cmake/FindGObject.cmake
index 19a8a67..09e95d1 100644
--- a/cmake/FindGObject.cmake
+++ b/cmake/FindGObject.cmake
@@ -13,8 +13,8 @@
 # ``GObject::GObject``
 #     The gobject-2.0 library
 
-include(CMakeFindDependencyMacro)
-find_dependency(GLIB2)
+find_package(GLIB2)
+find_package(libffi)
 qt_internal_disable_find_package_global_promotion(GLIB2::GLIB2)
 
 if(NOT TARGET GObject::GObject)
@@ -40,6 +40,7 @@ if(NOT TARGET GObject::GObject)
             target_link_libraries(GObject::GObject INTERFACE
                                 ${GObject_LIBRARY}
                                 GLIB2::GLIB2
+                                libffi
             )
         endif()
         include(FindPackageHandleStandardArgs)
