diff --git a/CMakeLists.txt b/CMakeLists.txt
index 3492289b..6284b6e0 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -115,18 +115,17 @@ if(USE_SWIG)
   endif()
 endif()
 
-set(EIGEN_DIR "${PROJECT_SOURCE_DIR}/external_libs/eigen")
-include_directories(${EIGEN_DIR})
+find_package(Eigen3 CONFIG REQUIRED)
 
 # See https://gitlab.com/libeigen/eigen/-/blob/master/COPYING.README
 add_definitions(-DEIGEN_MPL2_ONLY)
 add_definitions(-DEIGEN_DONT_PARALLELIZE)
 
-set(FAST_DOUBLE_PARSER_INCLUDE_DIR "${PROJECT_SOURCE_DIR}/external_libs/fast_double_parser/include")
-include_directories(${FAST_DOUBLE_PARSER_INCLUDE_DIR})
+find_package(fmt CONFIG REQUIRED)
+get_target_property(VCPKG_INCLUDE_DIR fmt::fmt INTERFACE_INCLUDE_DIRECTORIES)
+set(FMT_INCLUDE_DIR ${VCPKG_INCLUDE_DIR}/fmt)
 
-set(FMT_INCLUDE_DIR "${PROJECT_SOURCE_DIR}/external_libs/fmt/include")
-include_directories(${FMT_INCLUDE_DIR})
+find_path(FAST_DOUBLE_PARSER_INCLUDE_DIR fast_double_parser.h)
 
 if(__BUILD_FOR_R)
     find_package(LibR REQUIRED)
@@ -181,15 +180,13 @@ if(USE_OPENMP)
 endif()
 
 if(USE_GPU)
-    set(BOOST_COMPUTE_HEADER_DIR ${PROJECT_SOURCE_DIR}/external_libs/compute/include)
-    include_directories(${BOOST_COMPUTE_HEADER_DIR})
     find_package(OpenCL REQUIRED)
     include_directories(${OpenCL_INCLUDE_DIRS})
     message(STATUS "OpenCL include directory: " ${OpenCL_INCLUDE_DIRS})
     if(WIN32)
         set(Boost_USE_STATIC_LIBS ON)
     endif()
-    find_package(Boost 1.56.0 COMPONENTS filesystem system REQUIRED)
+    find_package(Boost 1.56.0 COMPONENTS filesystem system compute REQUIRED)
     if(WIN32)
         # disable autolinking in boost
         add_definitions(-DBOOST_ALL_NO_LIB)
@@ -458,9 +455,11 @@ endif()
 
 add_library(lightgbm_objs OBJECT ${SOURCES})
 
+target_link_libraries(lightgbm_objs PUBLIC Eigen3::Eigen fmt::fmt)
+
 if(BUILD_CLI)
     add_executable(lightgbm src/main.cpp src/application/application.cpp)
-    target_link_libraries(lightgbm PRIVATE lightgbm_objs)
+    target_link_libraries(lightgbm PRIVATE lightgbm_objs Eigen3::Eigen fmt::fmt)
 endif()
 
 set(API_SOURCES "src/c_api.cpp")
@@ -471,6 +470,7 @@ if(__BUILD_FOR_R)
 endif()
 
 add_library(lightgbm_capi_objs OBJECT ${API_SOURCES})
+target_link_libraries(lightgbm_capi_objs PUBLIC Eigen3::Eigen fmt::fmt)
 
 if(BUILD_STATIC_LIB)
   add_library(_lightgbm STATIC)
