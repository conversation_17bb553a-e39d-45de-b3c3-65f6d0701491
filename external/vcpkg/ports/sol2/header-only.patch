diff --git a/CMakeLists.txt b/CMakeLists.txt
index 120dd38..0b069dc 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -174,7 +174,7 @@ else()
 endif()
 
 # # # Tests, Examples and other CI suites that come with sol2
-if (sol2-is-top-level-project)
+if (0)
 	# # # General project output locations
 	if (CMAKE_SIZEOF_VOID_P EQUAL 4)
 		set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY "${CMAKE_CURRENT_BINARY_DIR}/x86/lib")
