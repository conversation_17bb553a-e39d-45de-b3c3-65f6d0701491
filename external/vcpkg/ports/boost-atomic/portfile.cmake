# Automatically generated by scripts/boost/generate-ports.ps1

vcpkg_from_github(
    OUT_SOURCE_PATH SOURCE_PATH
    REPO boostorg/atomic
    REF boost-${VERSION}
    SHA512 c9be2087ffed6b1711c4777328732a91526eab985c767c3357284cb05e56da4a2ea64554ef2d070d01d9a2e457f413c27016c4050114fdb76d3b609089f2c80b
    HEAD_REF master
    PATCHES
        fix-include.patch
)

set(FEATURE_OPTIONS "")
boost_configure_and_install(
    SOURCE_PATH "${SOURCE_PATH}"
    OPTIONS ${FEATURE_OPTIONS}
)
