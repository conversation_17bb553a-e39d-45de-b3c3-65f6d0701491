diff --git a/CMakeLists.txt b/CMakeLists.txt
index cf6dc658f2..4e976a3676 100644
--- a/CMakeLists.txt	
+++ b/CMakeLists.txt
@@ -20,6 +20,8 @@ find_package(Threads REQUIRED)
 # official monolithic Boost distribution tree).
 include(cmake/BoostLibraryIncludes.cmake)
 
+list(APPEND BOOST_LIBRARY_INCLUDES "${VCPKG_INSTALLED_DIR}/${VCPKG_TARGET_TRIPLET}/include")
+
 set(boost_atomic_sources src/lock_pool.cpp)
 
 set(CMAKE_REQUIRED_INCLUDES ${BOOST_LIBRARY_INCLUDES})
