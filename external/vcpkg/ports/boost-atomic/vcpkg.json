{"$comment": "Automatically generated by scripts/boost/generate-ports.ps1", "name": "boost-atomic", "version": "1.87.0", "port-version": 1, "description": "Boost atomic module", "homepage": "https://www.boost.org/libs/atomic", "license": "BSL-1.0", "dependencies": [{"name": "boost-align", "version>=": "1.87.0"}, {"name": "boost-assert", "version>=": "1.87.0"}, {"name": "boost-cmake", "version>=": "1.87.0"}, {"name": "boost-config", "version>=": "1.87.0"}, {"name": "boost-headers", "version>=": "1.87.0"}, {"name": "boost-predef", "version>=": "1.87.0"}, {"name": "boost-preprocessor", "version>=": "1.87.0"}, {"name": "boost-type-traits", "version>=": "1.87.0"}, {"name": "boost-winapi", "version>=": "1.87.0"}]}