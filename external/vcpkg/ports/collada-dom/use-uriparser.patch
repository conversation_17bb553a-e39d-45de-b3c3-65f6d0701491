diff --git a/CMakeLists.txt b/CMakeLists.txt
index 3ec1527..3f044d3 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -237,23 +237,29 @@ else()
   set(MINIZIP_INCLUDE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/dom/external-libs/minizip-1.1 ${ZLIB_INCLUDE_DIR})
 endif()
 
-pkg_check_modules(liburiparser liburiparser)
-
-if(liburiparser_FOUND)
-  set(liburiparser_CFLAGS_OTHERS "-DUSE_URIPARSER")
+find_package(UriParser)
+if(URIPARSER_FOUND)
+  set(URIPARSER_CFLAGS_OTHERS "-DUSE_URIPARSER")
+  set(URIPARSER_LIBRARIES ${URIPARSER_LIBRARY})
+  message(STATUS "liburiparser found")
 else()
-  # try using pcrecpp
-  pkg_check_modules(libpcrecpp libpcrecpp)
-  if( libpcrecpp_FOUND )
+  pkg_check_modules(liburiparser liburiparser)
+  if(liburiparser_FOUND)
+    set(URIPARSER_CFLAGS_OTHERS "-DUSE_URIPARSER")
+    message(STATUS "liburiparser found")
+  else()
+    # try using pcrecpp
+    pkg_check_modules(libpcrecpp libpcrecpp)
+    if( libpcrecpp_FOUND )
     set(CMAKE_REQUIRED_INCLUDES ${libpcrecpp_INCLUDE_DIRS})
     check_include_file_cxx(pcrecpp.h HAVE_PCRECPP_H)
     set(CMAKE_REQUIRED_INCLUDES)
     if( NOT HAVE_PCRECPP_H )
-      set(libpcrecpp_FOUND 0)
+        set(libpcrecpp_FOUND 0)
+    endif()
     endif()
-  endif()
 
-  if( NOT libpcrecpp_FOUND )
+    if( NOT libpcrecpp_FOUND )
     message(STATUS "System pcre not found, using local from sources")
     # include the local pcre
     add_subdirectory(dom/external-libs/pcre-8.02)
@@ -262,6 +268,7 @@ else()
     set(libpcrecpp_LIBRARIES pcrecpp_local)
     set(libpcrecpp_CFLAGS_OTHERS "-DPCRE_STATIC")
     set(libpcrecpp_LDFLAGS_OTHERS)
+    endif()
   endif()
 endif()
 
diff --git a/cmake-modules/FindUriParser.cmake b/cmake-modules/FindUriParser.cmake
new file mode 100644
index 0000000..91f466b
--- /dev/null
+++ b/cmake-modules/FindUriParser.cmake
@@ -0,0 +1,35 @@
+# Find the UriParser library
+# Defines:
+
+#  URIPARSER_INCLUDE_DIR - uriparser include directory
+#  URIPARSER_LIBRARY     - uriparser library file
+#  URIPARSER_FOUND       - TRUE if uriparser is found
+
+if (URIPARSER_INCLUDE_DIR)
+ #check cache 
+  set(URIPARSER_FIND_QUIETLY TRUE)
+endif ()
+
+if (NOT URIPARSER_INCLUDE_DIR)
+    find_path(URIPARSER_INCLUDE_DIR NAMES Uri.h PATH_SUFFIXES uriparser)
+    set(URIPARSER_INCLUDE_DIR ${URIPARSER_INCLUDE_DIR}/uriparser CACHE PATH "uriparser includes")
+endif ()
+
+find_library(URIPARSER_LIBRARY NAMES uriparser)
+
+if (URIPARSER_INCLUDE_DIR AND URIPARSER_LIBRARY)
+  set(URIPARSER_FOUND TRUE)
+  set(UriParser_FOUND TRUE)
+endif ()
+
+if (URIPARSER_FOUND)
+   if (NOT URIPARSER_FIND_QUIETLY)
+      message(STATUS "Found UriParser library: ${URIPARSER_LIBRARY}")
+   endif ()
+else ()
+   if (NOT URIPARSER_FIND_QUIETLY)
+      message(FATAL_ERROR "Could NOT find UriParser library")
+    else ()
+      message(STATUS "Could NOT find UriParser library")
+    endif ()
+endif ()
diff --git a/dom/CMakeLists.txt b/dom/CMakeLists.txt
index f7ffb27..62e1b8a 100644
--- a/dom/CMakeLists.txt
+++ b/dom/CMakeLists.txt
@@ -1,6 +1,6 @@
 set(COLLADA_INTERNAL_INCLUDE ${CMAKE_CURRENT_SOURCE_DIR}/include)
 include_directories(${COLLADA_INTERNAL_INCLUDE})
-set(COLLADA_LIBS minizip ${liburiparser_LIBRARIES} ${libpcrecpp_LIBRARIES} ${ZLIB_LIBRARIES} ${Boost_FILESYSTEM_LIBRARY} ${Boost_SYSTEM_LIBRARY})
+set(COLLADA_LIBS minizip ${URIPARSER_LIBRARIES} ${libpcrecpp_LIBRARIES} ${ZLIB_LIBRARIES} ${Boost_FILESYSTEM_LIBRARY} ${Boost_SYSTEM_LIBRARY})
 
 file(GLOB dae_files ${CMAKE_CURRENT_SOURCE_DIR}/src/dae/*.cpp)
 if( LIBXML2_FOUND )
@@ -13,7 +13,7 @@ file(GLOB stddatabase_files ${CMAKE_CURRENT_SOURCE_DIR}/src/modules/STLDatabase/
 file(GLOB stderrplugin_files ${CMAKE_CURRENT_SOURCE_DIR}/src/modules/stdErrPlugin/*.cpp)
 
 set(COLLADA_BASE_SOURCES ${dae_files} ${libxmlplugin_files} ${stddatabase_files} ${stderrplugin_files})
-set(COLLADA_COMPILE_FLAGS "${liburiparser_CFLAGS_OTHERS} ${libpcrecpp_CFLAGS_OTHERS} ${EXTRA_COMPILE_FLAGS} ${Boost_CFLAGS}")
+set(COLLADA_COMPILE_FLAGS "${URIPARSER_CFLAGS_OTHERS} ${libpcrecpp_CFLAGS_OTHERS} ${EXTRA_COMPILE_FLAGS} ${Boost_CFLAGS}")
 # create dynamic libraries
 set(COLLADA_COMPILE_FLAGS "${COLLADA_COMPILE_FLAGS} -DDOM_DYNAMIC -DDOM_EXPORT")
 
diff --git a/dom/src/dae/daeURI.cpp b/dom/src/dae/daeURI.cpp
index 3a8f815..1623753 100644
--- a/dom/src/dae/daeURI.cpp
+++ b/dom/src/dae/daeURI.cpp
@@ -790,7 +790,7 @@ bool cdom::parseUriRef(const string& uriRef,
         scheme = fromRange(uri.scheme);
         authority = fromRange(uri.hostText);
         path = fromList(uri.pathHead, "/");
-        if (uri.absolutePath != URI_TRUE and uri.hostText.first == NULL)
+        if (uri.absolutePath != URI_TRUE && uri.hostText.first == NULL)
             path = path.erase(0, 1);
         query = fromRange(uri.query);
         fragment = fromRange(uri.fragment);
