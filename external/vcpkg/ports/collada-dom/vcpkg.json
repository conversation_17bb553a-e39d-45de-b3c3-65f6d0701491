{"name": "collada-dom", "version": "2.5.0", "port-version": 11, "description": "The COLLADA Document Object Model (DOM) is an application programming interface (API) that provides a C++ object representation of a COLLADA XML instance document.", "homepage": "https://github.com/rdiankov/collada-dom", "license": null, "dependencies": ["boost-filesystem", "boost-system", "libxml2", "minizip", "<PERSON><PERSON><PERSON><PERSON>", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}, "zlib"]}