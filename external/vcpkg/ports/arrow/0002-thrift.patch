diff --git a/cpp/cmake_modules/FindThriftAlt.cmake b/cpp/cmake_modules/FindThriftAlt.cmake
index f3e4902..65ceac8 100644
--- a/cpp/cmake_modules/FindThriftAlt.cmake
+++ b/cpp/cmake_modules/FindThriftAlt.cmake
@@ -45,7 +45,7 @@ endif()
 #   * https://github.com/apache/thrift/pull/2725
 #   * https://github.com/apache/thrift/pull/2726
 #   * https://github.com/conda-forge/thrift-cpp-feedstock/issues/68
-if(NOT WIN32)
+
   set(find_package_args "")
   if(ThriftAlt_FIND_VERSION)
     list(APPEND find_package_args ${ThriftAlt_FIND_VERSION})
@@ -61,7 +61,7 @@ if(NOT WIN32)
                                                       "${THRIFT_COMPILER}")
     return()
   endif()
-endif()
+
 
 function(extract_thrift_version)
   if(ThriftAlt_INCLUDE_DIR)
