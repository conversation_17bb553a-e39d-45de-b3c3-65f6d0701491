{"$comment": "Automatically generated by scripts/boost/generate-ports.ps1", "name": "boost-date-time", "version": "1.87.0", "description": "Boost date_time module", "homepage": "https://www.boost.org/libs/date_time", "license": "BSL-1.0", "dependencies": [{"name": "boost-algorithm", "version>=": "1.87.0"}, {"name": "boost-assert", "version>=": "1.87.0"}, {"name": "boost-cmake", "version>=": "1.87.0"}, {"name": "boost-config", "version>=": "1.87.0"}, {"name": "boost-core", "version>=": "1.87.0"}, {"name": "boost-headers", "version>=": "1.87.0"}, {"name": "boost-io", "version>=": "1.87.0"}, {"name": "boost-lexical-cast", "version>=": "1.87.0"}, {"name": "boost-numeric-conversion", "version>=": "1.87.0"}, {"name": "boost-range", "version>=": "1.87.0"}, {"name": "boost-smart-ptr", "version>=": "1.87.0"}, {"name": "boost-static-assert", "version>=": "1.87.0"}, {"name": "boost-throw-exception", "version>=": "1.87.0"}, {"name": "boost-tokenizer", "version>=": "1.87.0"}, {"name": "boost-type-traits", "version>=": "1.87.0"}, {"name": "boost-utility", "version>=": "1.87.0"}, {"name": "boost-winapi", "version>=": "1.87.0"}]}