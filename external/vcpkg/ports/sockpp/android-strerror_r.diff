diff --git a/src/exception.cpp b/src/exception.cpp
index 72aae7e..7a56c60 100644
--- a/src/exception.cpp
+++ b/src/exception.cpp
@@ -66,7 +66,7 @@ string sys_error::error_str(int err)
 			buf, sizeof(buf), NULL);
     #else
     	#ifdef _GNU_SOURCE
-			#if !defined(__GLIBC__)
+			#if !defined(__GLIBC__) && !defined(__ANDROID__)
 			// use the XSI standard behavior.
 				int e = strerror_r(err, buf, sizeof(buf));
 				auto s = strerror(e);
