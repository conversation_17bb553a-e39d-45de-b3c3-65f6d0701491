diff --git a/cmake/PangolinConfig.cmake.in b/cmake/PangolinConfig.cmake.in
index 3ceea9d..e5bf9f0 100644
--- a/cmake/PangolinConfig.cmake.in
+++ b/cmake/PangolinConfig.cmake.in
@@ -12,6 +12,7 @@ SET( Pangolin_LIBRARY      "${Pangolin_LIBRARIES}" )
 
 include(CMakeFindDependencyMacro)
 find_dependency(Eigen3)
+find_dependency(PalSigslot CONFIG)
 
 if (UNIX)
   find_dependency(Threads)
diff --git a/components/pango_core/CMakeLists.txt b/components/pango_core/CMakeLists.txt
index e1d931b..6c6ad4d 100644
--- a/components/pango_core/CMakeLists.txt
+++ b/components/pango_core/CMakeLists.txt
@@ -49,6 +49,9 @@ install(DIRECTORY "${CMAKE_CURRENT_LIST_DIR}/include"
   DESTINATION ${CMAKE_INSTALL_PREFIX}
 )
 
+find_package(PalSigslot CONFIG REQUIRED)
+target_link_libraries(${COMPONENT} PUBLIC Pal::Sigslot)
+
 find_package(Threads QUIET)
 if(Threads_FOUND)
     target_link_libraries(${COMPONENT} PUBLIC Threads::Threads)
