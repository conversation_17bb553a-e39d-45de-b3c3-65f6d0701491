diff --git a/CMakeLists.txt b/CMakeLists.txt
index 985f5f5..bf6600e 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -476,6 +476,10 @@ if (CYGWIN OR WIN32)
   target_compile_definitions (glog PUBLIC GLOG_NO_ABBREVIATED_SEVERITIES)
 endif (CYGWIN OR WIN32)
 
+if ((MSVC) AND (MSVC_VERSION GREATER_EQUAL 1914))
+    target_compile_options(glog INTERFACE "$<$<NOT:$<COMPILE_LANGUAGE:CUDA>>:/Zc:__cplusplus>")
+endif()
+
 set_target_properties (glog PROPERTIES PUBLIC_HEADER "${GLOG_PUBLIC_H}")
 
 target_include_directories (glog BEFORE PUBLIC
