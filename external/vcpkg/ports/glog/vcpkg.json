{"name": "glog", "version": "0.7.1", "description": "C++ implementation of the Google logging module", "homepage": "https://github.com/google/glog", "license": "BSD-3-<PERSON><PERSON>", "dependencies": ["gflags", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"customprefix": {"description": "Enable support for user-generated message prefixes"}, "unwind": {"description": "Enable libunwind support", "supports": "linux", "dependencies": ["libunwind"]}}}