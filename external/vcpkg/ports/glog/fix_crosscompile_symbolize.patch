diff --git a/CMakeLists.txt b/CMakeLists.txt
index fe1d85f..985f5f5 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -243,6 +243,9 @@ endif (HAVE_EXECINFO_BACKTRACE AND HAVE_EXECINFO_BACKTRACE_SYMBOLS)
 
 if (WITH_SYMBOLIZE)
   if (WIN32 OR CYGWIN)
+    if(CMAKE_CROSSCOMPILING)
+      set(HAVE_SYMBOLIZE 0)
+    else()
     cmake_push_check_state (RESET)
     set (CMAKE_REQUIRED_LIBRARIES DbgHelp)
 
@@ -273,6 +276,7 @@ if (WITH_SYMBOLIZE)
     ]=] HAVE_SYMBOLIZE)
 
     cmake_pop_check_state ()
+    endif()
 
     if (HAVE_SYMBOLIZE)
       set (HAVE_STACKTRACE 1)
