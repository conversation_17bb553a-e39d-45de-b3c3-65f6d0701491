lpeg provides CMake targets:

  find_package(unofficial-lpeg CONFIG REQUIRED)
  target_link_libraries(main PRIVATE unofficial::lpeg::lpeg)

UNOFFICIAL_LPEG_LUA_FILES will be set to the path of re.lua

  add_custom_command(TARGET main POST_BUILD
    COMMAND ${CMAKE_COMMAND} -E copy
    ${UNOFFICIAL_LPEG_LUA_FILES}
    $<TARGET_FILE_DIR:main>)

  install(FILES ${UNOFFICIAL_LPEG_LUA_FILES} DESTINATION my_lua_path)
