diff --git a/CMakeLists.txt b/CMakeLists.txt
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -117,7 +117,7 @@ include(CMakePackageConfigHelpers)
 include(GNUInstallDirs)
 
 set(concurrencpp_directory "concurrencpp-${PROJECT_VERSION}")
-set(concurrencpp_include_directory "${CMAKE_INSTALL_INCLUDEDIR}/${concurrencpp_directory}")
+set(concurrencpp_include_directory "${CMAKE_INSTALL_INCLUDEDIR}")
 
 install(
   TARGETS concurrencpp
