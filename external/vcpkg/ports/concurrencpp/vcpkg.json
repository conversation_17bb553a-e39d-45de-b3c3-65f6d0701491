{"name": "concurrenc<PERSON>", "version": "0.1.7", "port-version": 2, "description": "concurrencpp is a tasking library for C++ allowing developers to write highly concurrent applications easily and safely by using tasks, executors and coroutines.", "homepage": "https://github.com/<PERSON>-<PERSON>/concurrencpp/", "license": "MIT", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}]}