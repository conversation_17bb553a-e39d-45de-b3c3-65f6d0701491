diff --git a/Source/FreeImage/PluginEXR.cpp b/Source/FreeImage/PluginEXR.cpp
index b286430..82b3b72 100644
--- a/Source/FreeImage/PluginEXR.cpp
+++ b/Source/FreeImage/PluginEXR.cpp
@@ -28,16 +28,16 @@
 #pragma warning (disable : 4800) // ImfVersion.h - 'const int' : forcing value to bool 'true' or 'false' (performance warning)
 #endif 
 
-#include "../OpenEXR/IlmImf/ImfIO.h"
-#include "../OpenEXR/Iex/Iex.h"
-#include "../OpenEXR/IlmImf/ImfOutputFile.h"
-#include "../OpenEXR/IlmImf/ImfInputFile.h"
-#include "../OpenEXR/IlmImf/ImfRgbaFile.h"
-#include "../OpenEXR/IlmImf/ImfChannelList.h"
-#include "../OpenEXR/IlmImf/ImfRgba.h"
-#include "../OpenEXR/IlmImf/ImfArray.h"
-#include "../OpenEXR/IlmImf/ImfPreviewImage.h"
-#include "../OpenEXR/Half/half.h"
+#include <OpenEXR/ImfIO.h>
+#include <OpenEXR/Iex.h>
+#include <OpenEXR/ImfOutputFile.h>
+#include <OpenEXR/ImfInputFile.h>
+#include <OpenEXR/ImfRgbaFile.h>
+#include <OpenEXR/ImfChannelList.h>
+#include <OpenEXR/ImfRgba.h>
+#include <OpenEXR/ImfArray.h>
+#include <OpenEXR/ImfPreviewImage.h>
+#include <Imath/half.h>
 
 
 // ==========================================================
@@ -66,11 +66,11 @@ public:
 		return ((unsigned)n != _io->read_proc(c, 1, n, _handle));
 	}
 
-	virtual Imath::Int64 tellg() {
+	virtual uint64_t tellg() {
 		return _io->tell_proc(_handle);
 	}
 
-	virtual void seekg(Imath::Int64 pos) {
+	virtual void seekg(uint64_t pos) {
 		_io->seek_proc(_handle, (unsigned)pos, SEEK_SET);
 	}
 
@@ -100,11 +100,11 @@ public:
 		}
 	}
 
-	virtual Imath::Int64 tellp() {
+	virtual uint64_t tellp() {
 		return _io->tell_proc(_handle);
 	}
 
-	virtual void seekp(Imath::Int64 pos) {
+	virtual void seekp(uint64_t pos) {
 		_io->seek_proc(_handle, (unsigned)pos, SEEK_SET);
 	}
 };
diff --git a/Source/FreeImage/PluginTIFF.cpp b/Source/FreeImage/PluginTIFF.cpp
index 562fdd7..ce12649 100644
--- a/Source/FreeImage/PluginTIFF.cpp
+++ b/Source/FreeImage/PluginTIFF.cpp
@@ -39,7 +39,7 @@
 #include "Utilities.h"
 #include <tiffio.h>
 #include "../Metadata/FreeImageTag.h"
-#include "../OpenEXR/Half/half.h"
+#include <Imath/half.h>
 
 #include "FreeImageIO.h"
 #include "PSDParser.h"
