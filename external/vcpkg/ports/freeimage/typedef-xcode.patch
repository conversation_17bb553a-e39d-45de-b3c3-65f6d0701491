diff --git a/Source/FreeImage.h b/Source/FreeImage.h
index ad2574d..9588944 100644
--- a/Source/FreeImage.h
+++ b/Source/FreeImage.h
@@ -155,7 +155,9 @@ FI_STRUCT (FIMULTIBITMAP) { void *data; };
 #ifndef _MSC_VER
 // define portable types for 32-bit / 64-bit OS
 #include <inttypes.h>
+#ifndef OBJC_BOOL_DEFINED
 typedef int32_t BOOL;
+#endif
 typedef uint8_t BYTE;
 typedef uint16_t WORD;
 typedef uint32_t DWORD;
