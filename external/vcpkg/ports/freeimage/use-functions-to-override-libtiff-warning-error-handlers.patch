diff --git a/Source/FreeImage/PluginTIFF.cpp b/Source/FreeImage/PluginTIFF.cpp
index a9fcf0e..b1f7b32 100644
--- a/Source/FreeImage/PluginTIFF.cpp
+++ b/Source/FreeImage/PluginTIFF.cpp
@@ -258,7 +258,7 @@ static void
 msdosWarningHandler(const char* module, const char* fmt, va_list ap) {
 }
 
-TIFFErrorHandler _TIFFwarningHandler = msdosWarningHandler;
+//TIFFErrorHandler _TIFFwarningHandler = msdosWarningHandler;
 
 static void
 msdosErrorHandler(const char* module, const char* fmt, va_list ap) {
@@ -273,7 +273,7 @@ msdosErrorHandler(const char* module, const char* fmt, va_list ap) {
 	*/
 }
 
-TIFFErrorHandler _TIFFerrorHandler = msdosErrorHandler;
+//TIFFErrorHandler _TIFFerrorHandler = msdosErrorHandler;
 
 // ----------------------------------------------------------
 
@@ -2659,6 +2659,8 @@ InitTIFF(Plugin *plugin, int format_id) {
     // Set up the callback for extended TIFF directory tag support (see XTIFF.cpp)
 	// Must be called before using libtiff
     XTIFFInitialize();	
+	TIFFSetWarningHandler(msdosWarningHandler);
+	TIFFSetErrorHandler(msdosErrorHandler);
 
 	plugin->format_proc = Format;
 	plugin->description_proc = Description;
