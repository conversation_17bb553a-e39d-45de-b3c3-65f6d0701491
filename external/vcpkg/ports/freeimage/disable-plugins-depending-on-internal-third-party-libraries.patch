diff --git a/Source/FreeImage.h b/Source/FreeImage.h
index 12182cd..ebd0453 100644
--- a/Source/FreeImage.h
+++ b/Source/FreeImage.h
@@ -410,16 +410,20 @@ FI_ENUM(FREE_IMAGE_FORMAT) {
 	FIF_DDS		= 24,
 	FIF_GIF     = 25,
 	FIF_HDR		= 26,
-	FIF_FAXG3	= 27,
-	FIF_SGI		= 28,
-	FIF_EXR		= 29,
-	FIF_J2K		= 30,
-	FIF_JP2		= 31,
-	FIF_PFM		= 32,
-	FIF_PICT	= 33,
-	FIF_RAW		= 34,
-	FIF_WEBP	= 35,
-	FIF_JXR		= 36
+/* vcpkg: The G3 fax format plugin is deliberately disabled in our build of
+   FreeImage, since it requires usage of the vendored copy of libtiff. */
+#if 0
+ 	FIF_FAXG3	= 27,
+#endif
+	FIF_SGI		= 27,
+	FIF_EXR		= 28,
+	FIF_J2K		= 29,
+	FIF_JP2		= 30,
+	FIF_PFM		= 31,
+	FIF_PICT	= 32,
+	FIF_RAW		= 33,
+	FIF_WEBP	= 34,
+	FIF_JXR		= 35
 };
 
 /** Image type used in FreeImage.
@@ -476,6 +480,9 @@ FI_ENUM(FREE_IMAGE_DITHER) {
 /** Lossless JPEG transformations
 Constants used in FreeImage_JPEGTransform
 */
+/* vcpkg: The JPEGTransform functions are deliberately disabled in our build
+   of FreeImage, since they require usage of the vendored copy of libjpeg. */
+#if 0
 FI_ENUM(FREE_IMAGE_JPEG_OPERATION) {
 	FIJPEG_OP_NONE			= 0,	//! no transformation
 	FIJPEG_OP_FLIP_H		= 1,	//! horizontal flip
@@ -486,6 +493,7 @@ FI_ENUM(FREE_IMAGE_JPEG_OPERATION) {
 	FIJPEG_OP_ROTATE_180	= 6,	//! 180-degree rotation
 	FIJPEG_OP_ROTATE_270	= 7		//! 270-degree clockwise (or 90 ccw)
 };
+#endif
 
 /** Tone mapping operators.
 Constants used in FreeImage_ToneMapping.
@@ -1089,6 +1097,9 @@ DLL_API const char* DLL_CALLCONV FreeImage_TagToString(FREE_IMAGE_MDMODEL model,
 // JPEG lossless transformation routines
 // --------------------------------------------------------------------------
 
+/* vcpkg: The JPEGTransform functions are deliberately disabled in our build
+   of FreeImage, since they require usage of the vendored copy of libjpeg. */
+#if 0
 DLL_API BOOL DLL_CALLCONV FreeImage_JPEGTransform(const char *src_file, const char *dst_file, FREE_IMAGE_JPEG_OPERATION operation, BOOL perfect FI_DEFAULT(TRUE));
 DLL_API BOOL DLL_CALLCONV FreeImage_JPEGTransformU(const wchar_t *src_file, const wchar_t *dst_file, FREE_IMAGE_JPEG_OPERATION operation, BOOL perfect FI_DEFAULT(TRUE));
 DLL_API BOOL DLL_CALLCONV FreeImage_JPEGCrop(const char *src_file, const char *dst_file, int left, int top, int right, int bottom);
@@ -1097,7 +1108,7 @@ DLL_API BOOL DLL_CALLCONV FreeImage_JPEGTransformFromHandle(FreeImageIO* src_io,
 DLL_API BOOL DLL_CALLCONV FreeImage_JPEGTransformCombined(const char *src_file, const char *dst_file, FREE_IMAGE_JPEG_OPERATION operation, int* left, int* top, int* right, int* bottom, BOOL perfect FI_DEFAULT(TRUE));
 DLL_API BOOL DLL_CALLCONV FreeImage_JPEGTransformCombinedU(const wchar_t *src_file, const wchar_t *dst_file, FREE_IMAGE_JPEG_OPERATION operation, int* left, int* top, int* right, int* bottom, BOOL perfect FI_DEFAULT(TRUE));
 DLL_API BOOL DLL_CALLCONV FreeImage_JPEGTransformCombinedFromMemory(FIMEMORY* src_stream, FIMEMORY* dst_stream, FREE_IMAGE_JPEG_OPERATION operation, int* left, int* top, int* right, int* bottom, BOOL perfect FI_DEFAULT(TRUE));
-
+#endif
 
 // --------------------------------------------------------------------------
 // Image manipulation toolkit
diff --git a/Source/FreeImage/Plugin.cpp b/Source/FreeImage/Plugin.cpp
index 11e7294..0119ba7 100644
--- a/Source/FreeImage/Plugin.cpp
+++ b/Source/FreeImage/Plugin.cpp
@@ -263,7 +263,11 @@ FreeImage_Initialise(BOOL load_local_plugins_only) {
 			s_plugins->AddNode(InitDDS);
 	        s_plugins->AddNode(InitGIF);
 	        s_plugins->AddNode(InitHDR);
-			s_plugins->AddNode(InitG3);
+/* vcpkg: The G3 fax format plugin is deliberately disabled in our build of FreeImage
++   since it requires usage of the vendored copy of libtiff. */
+#if 0
+ 			s_plugins->AddNode(InitG3);
+#endif
 			s_plugins->AddNode(InitSGI);
 			s_plugins->AddNode(InitEXR);
 			s_plugins->AddNode(InitJ2K);
