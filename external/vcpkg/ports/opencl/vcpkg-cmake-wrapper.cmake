string(FIND ";${ARGS};" ";CONFIG;" z_vcpkg_opencl_config)
_find_package(${ARGS})
if("@VCPKG_LIBRARY_LINKAGE@" STREQUAL "static" AND (NOT CMAKE_SYSTEM_NAME MATCHES "Darwin") AND z_vcpkg_opencl_config EQUAL "-1")
  find_package(Threads REQUIRED)
  set(OpenCL_Extra_Libs ${CMAKE_DL_LIBS} ${CMAKE_THREAD_LIBS_INIT})
  if(CMAKE_SYSTEM_NAME MATCHES "Windows")
    list(APPEND OpenCL_Extra_Libs cfgmgr32)
    if("$ENV{WindowsSDKVersion}" MATCHES "^10")
      list(APPEND OpenCL_Extra_Libs OneCoreUAP)
    endif()
  endif()

  if(TARGET OpenCL::OpenCL)
      set_property(TARGET OpenCL::OpenCL APPEND PROPERTY INTERFACE_LINK_LIBRARIES ${OpenCL_Extra_Libs})
  endif()
  if(OpenCL_LIBRARIES)
      list(APPEND OpenCL_LIBRARIES ${OpenCL_Extra_Libs})
  endif()
  unset(OpenCL_Extra_Libs)
endif()
unset(z_vcpkg_opencl_config)
