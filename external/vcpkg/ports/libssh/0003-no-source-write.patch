diff --git a/CMakeLists.txt b/CMakeLists.txt
index d066cde..d2a11cb 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -224,9 +224,6 @@ endif (WITH_SYMBOL_VERSIONING AND AB<PERSON><PERSON>_FOUND)
 add_custom_target(dist COMMAND ${CMAKE_MAKE_PROGRAM} package_source DEPENDS ${_SYMBOL_TARGET} VERBATIM)
 
 # Link compile database for clangd
-execute_process(COMMAND ${CMAKE_COMMAND} -E create_symlink
-                "${CMAKE_BINARY_DIR}/compile_commands.json"
-                "${CMAKE_SOURCE_DIR}/compile_commands.json")
 
 message(STATUS "********************************************")
 message(STATUS "********** ${PROJECT_NAME} build options : **********")
