diff --git a/source/fitz/geometry.c b/source/fitz/geometry.c
index 473d1dc..b02ab39 100644
--- a/source/fitz/geometry.c
+++ b/source/fitz/geometry.c
@@ -29,6 +29,8 @@
 #define MAX4(a,b,c,d) fz_max(fz_max(a,b), fz_max(c,d))
 #define MIN4(a,b,c,d) fz_min(fz_min(a,b), fz_min(c,d))
 
+#define MY_NAN (0.0 / 0.0)
+
 /*	A useful macro to add with overflow detection and clamping.
 
 	We want to do "b = a + x", but to allow for overflow. Consider the
@@ -388,7 +390,7 @@ const fz_irect fz_invalid_irect = { 0, 0, -1, -1 };
 const fz_irect fz_unit_bbox = { 0, 0, 1, 1 };
 
 const fz_quad fz_infinite_quad = { { -INFINITY, INFINITY}, {INFINITY, INFINITY}, {-INFINITY, -INFINITY}, {INFINITY, -INFINITY} };
-const fz_quad fz_invalid_quad = { {NAN, NAN}, {NAN, NAN}, {NAN, NAN}, {NAN, NAN} };
+const fz_quad fz_invalid_quad = { {MY_NAN, MY_NAN}, {MY_NAN, MY_NAN}, {MY_NAN, MY_NAN}, {MY_NAN, MY_NAN} };
 
 fz_irect
 fz_irect_from_rect(fz_rect r)
