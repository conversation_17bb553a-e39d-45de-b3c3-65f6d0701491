diff --git a/src/asn1Coding.c b/src/asn1Coding.c
index 86c1d07..6fcfc4d 100644
--- a/src/asn1Coding.c
+++ b/src/asn1Coding.c
@@ -23,7 +23,12 @@
 #include <stdio.h>
 #include <string.h>
 #include <stdlib.h>
+#ifdef HAVE_UNISTD_H
 #include <unistd.h>
+#endif
+#ifdef _MSC_VER
+#include <io.h>
+#endif
 #include <getopt.h>
 #include <assert.h>
 
diff --git a/src/asn1Decoding.c b/src/asn1Decoding.c
index c6f192e..3e4e21d 100644
--- a/src/asn1Decoding.c
+++ b/src/asn1Decoding.c
@@ -23,7 +23,12 @@
 #include <stdio.h>
 #include <string.h>
 #include <stdlib.h>
+#ifdef HAVE_UNISTD_H
 #include <unistd.h>
+#endif
+#ifdef _MSC_VER
+#include <io.h>
+#endif
 #include <getopt.h>
 #include <sys/types.h>
 #include <sys/stat.h>
diff --git a/src/asn1Parser.c b/src/asn1Parser.c
index b6844a8..445c716 100644
--- a/src/asn1Parser.c
+++ b/src/asn1Parser.c
@@ -23,7 +23,12 @@
 #include <stdio.h>
 #include <string.h>
 #include <stdlib.h>
+#ifdef HAVE_UNISTD_H
 #include <unistd.h>
+#endif
+#ifdef _MSC_VER
+#include <io.h>
+#endif
 #include <getopt.h>
 #include <assert.h>
 
diff --git a/src/benchmark.c b/src/benchmark.c
index 010d58e..6c613df 100644
--- a/src/benchmark.c
+++ b/src/benchmark.c
@@ -21,9 +21,16 @@
 #include <stdio.h>
 #include <string.h>
 #include <signal.h>
+#ifndef _MSC_VER
 #include <sys/time.h>
+#endif
 #include <time.h>
+#ifdef HAVE_UNISTD_H
 #include <unistd.h>
+#endif
+#ifdef _MSC_VER
+#include <io.h>
+#endif
 #include "benchmark.h"
 
 int benchmark_must_finish = 0;
diff --git a/src/benchmark.h b/src/benchmark.h
index 3272649..6b6bf32 100644
--- a/src/benchmark.h
+++ b/src/benchmark.h
@@ -21,7 +21,9 @@
 #ifndef BENCHMARK_H
 # define BENCHMARK_H
 
+#ifndef _MSC_VER
 # include <sys/time.h>
+#endif
 # include <time.h>
 # include <signal.h>
 # if defined _WIN32
