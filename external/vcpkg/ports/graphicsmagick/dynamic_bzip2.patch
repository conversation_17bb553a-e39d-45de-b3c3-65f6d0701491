From: <PERSON><PERSON><PERSON> <<EMAIL>>
Date: <PERSON><PERSON>, 18 Apr 2017 09:56:53 -0300
Subject: [PATCH] dynamic bzip2 only if dynamic gm

---

diff -ur a/magick/nt_base.h b/magick/nt_base.h
--- a/magick/nt_base.h	Mon May 30 14:19:55 2016
+++ b/magick/nt_base.h	Mon Apr 17 15:10:01 2017
@@ -275,7 +275,9 @@
 */
 #if defined(HasBZLIB)
 #  if defined(_WIN32)
-#    define BZ_IMPORT 1
+#    if defined(_DLL)    // For vcpkg. Building GM DLL requires BZIP2 DLL too, otherwise static
+#      define BZ_IMPORT 1
+#    endif
 #  endif
 #endif /* defined(HasBZLIB) */
 
