diff --git a/cmake_include/ConfigureCompileFlags.cmake b/cmake_include/ConfigureCompileFlags.cmake
index 63bdfec..80879f9 100644
--- a/cmake_include/ConfigureCompileFlags.cmake
+++ b/cmake_include/ConfigureCompileFlags.cmake
@@ -25,7 +25,6 @@ if(UNIX)
     set(build_flags_COMMON_LIST
       ${build_flags_COMMON_LIST}
       "-Weverything"
-      "-Werror"
       "-Wno-c++98-compat"
       "-Wno-c++98-compat-pedantic"
       "-Wno-deprecated"
@@ -44,7 +43,6 @@ if(UNIX)
   elseif(${CMAKE_CXX_COMPILER_ID} STREQUAL GNU)
     set(build_flags_COMMON_LIST
       ${build_flags_COMMON_LIST}
-      "-Werror"
       "-Wno-multichar"
     )
   endif()
@@ -87,7 +85,6 @@ elseif(${CMAKE_CXX_COMPILER_ID} STREQUAL MSVC)
     ${build_flags_COMMON_LIST}
     "/MP"
     "/Wall"
-    "/WX"
     "/EHsc"
 
     #############################
