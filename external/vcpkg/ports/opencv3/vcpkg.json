{"name": "opencv3", "version": "3.4.20", "description": "Open Source Computer Vision Library", "homepage": "https://github.com/opencv/opencv", "license": "BSD-3-<PERSON><PERSON>", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}, {"name": "vcpkg-get-python-packages", "host": true}, "zlib"], "default-features": [{"name": "dnn", "platform": "!android"}, {"name": "<PERSON>i", "platform": "!uwp"}, {"name": "gtk", "platform": "linux"}, "jpeg", {"name": "msmf", "platform": "windows & !uwp & !mingw"}, "png", "quirc", "tiff", "webp"], "features": {"bgsegm": {"description": "opencv_bgsegm module", "supports": "!arm"}, "contrib": {"description": "opencv_contrib module", "dependencies": [{"name": "hdf5", "platform": "!uwp & !(windows & (arm | arm64))"}, {"name": "opencv3", "features": ["dnn"], "platform": "!android"}, {"name": "tesseract", "platform": "!uwp & !(windows & (arm | arm64))"}]}, "dc1394": {"description": "Dc1394 support for opencv", "dependencies": ["libdc1394"]}, "dnn": {"description": "Enable dnn module", "supports": "!android", "dependencies": [{"name": "opencv3", "features": ["flann"]}]}, "eigen": {"description": "Eigen support for opencv", "dependencies": ["eigen3"]}, "flann": {"description": "opencv_flann module", "dependencies": ["protobuf"]}, "freetype": {"description": "Freetype support for opencv", "dependencies": ["freetype", "harfbuzz"]}, "gapi": {"description": "Enable gapi module", "supports": "!uwp"}, "gdcm": {"description": "GDCM support for opencv", "dependencies": ["gdcm"]}, "gstreamer": {"description": "gstreamer support for opencv", "dependencies": ["gstreamer"]}, "gtk": {"description": "GTK support for opencv", "supports": "linux", "dependencies": ["gtk3"]}, "halide": {"description": "Halide support for opencv", "dependencies": ["halide", {"name": "opencv3", "default-features": false}, {"name": "opencv3", "features": ["dnn"]}]}, "ipp": {"description": "Enable Intel Integrated Performance Primitives", "supports": "(osx & x64) | (windows & (x64 | x86)) | (linux & (x64 | x86))"}, "jasper": {"description": "JPEG 2000 support for opencv", "dependencies": ["jasper"]}, "jpeg": {"description": "JPEG support for opencv", "dependencies": ["libjpeg-turbo"]}, "line-descriptor": {"description": "opencv_line_descriptor module", "supports": "!arm"}, "msmf": {"description": "Microsoft Media Foundation support for opencv", "supports": "windows & !uwp & !mingw"}, "nonfree": {"description": "allow nonfree and unredistributable libraries"}, "openexr": {"description": "OpenEXR support for opencv", "dependencies": ["openexr"]}, "opengl": {"description": "opengl support for opencv", "dependencies": ["opengl"]}, "openmp": {"description": "Enable openmp support for opencv", "supports": "!osx"}, "ovis": {"description": "opencv_ovis module", "supports": "!(windows & static) & !android", "dependencies": ["ogre", {"name": "opencv3", "default-features": false, "features": ["contrib"]}]}, "png": {"description": "PNG support for opencv", "dependencies": ["libpng"]}, "python": {"description": "Python wrapper support for opencv", "dependencies": [{"name": "opencv3", "default-features": false, "features": ["flann"]}, {"name": "python3", "default-features": false, "features": ["extensions"]}]}, "qt": {"description": "Qt GUI support for opencv", "dependencies": [{"name": "qt5-base", "default-features": false}]}, "quality": {"description": "Build opencv_quality module", "supports": "!uwp", "dependencies": [{"name": "opencv3", "default-features": false, "features": ["contrib"]}]}, "quirc": {"description": "Enable QR code module", "dependencies": ["quirc"]}, "salicency": {"description": "opencv_salicency module", "supports": "!arm"}, "sfm": {"description": "opencv_sfm module", "dependencies": ["ceres", "gflags", "glog", {"name": "opencv3", "default-features": false, "features": ["contrib"]}, {"name": "opencv3", "default-features": false, "features": ["eigen"]}]}, "tbb": {"description": "Enable Intel Threading Building Blocks", "supports": "!static", "dependencies": ["tbb"]}, "tiff": {"description": "TIFF support for opencv", "dependencies": [{"name": "tiff", "default-features": false}]}, "vtk": {"description": "vtk support for opencv", "dependencies": [{"name": "opencv3", "features": ["contrib"]}, {"name": "vtk", "default-features": false}]}, "webp": {"description": "WebP support for opencv", "dependencies": ["libwebp"]}, "world": {"description": "Compile to a single package support for opencv"}}}