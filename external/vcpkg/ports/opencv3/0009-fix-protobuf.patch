--- a/cmake/OpenCVFindProtobuf.cmake
+++ b/cmake/OpenCVFindProtobuf.cmake
@@ -31,7 +31,7 @@ if(BUILD_PROTOBUF)
   set(HAVE_PROTOBUF TRUE)
 else()
   unset(Protobuf_VERSION CACHE)
-  find_package(Protobuf QUIET)
+  find_package(Protobuf CONFIG REQUIRED)
 
   # Backwards compatibility
   # Define camel case versions of input variables
@@ -76,6 +76,7 @@ if(HAVE_PROTOBUF)
   if(NOT BUILD_PROTOBUF)
     if(TARGET "${Protobuf_LIBRARIES}")
       get_target_property(__location "${Protobuf_LIBRARIES}" IMPORTED_LOCATION_RELEASE)
+      get_target_property(__location_debug "${Protobuf_LIBRARIES}" IMPORTED_LOCATION_DEBUG)
       if(NOT __location)
         get_target_property(__location "${Protobuf_LIBRARIES}" IMPORTED_LOCATION)
       endif()
@@ -87,5 +88,5 @@ if(HAVE_PROTOBUF)
   endif()
   list(APPEND CUSTOM_STATUS_protobuf "    Protobuf:"
     BUILD_PROTOBUF THEN "build (${Protobuf_VERSION})"
-    ELSE "${__location} (${Protobuf_VERSION})")
+    ELSE "optimized ${__location} debug ${__location_debug} ; version (${Protobuf_VERSION})")
 endif()
