--- a/cmake/OpenCVFindLibsGrfmt.cmake
+++ b/cmake/OpenCVFindLibsGrfmt.cmake
@@ -11,7 +11,7 @@ else()
     set(_zlib_ORIG_CMAKE_FIND_LIBRARY_SUFFIXES ${CMAKE_FIND_LIBRARY_SUFFIXES})
     set(CMAKE_FIND_LIBRARY_SUFFIXES .so)
   endif()
-  find_package(ZLIB "${MIN_VER_ZLIB}")
+  find_package(ZLIB "${MIN_VER_ZLIB}" REQUIRED)
   if(ANDROID)
     set(CMAKE_FIND_LIBRARY_SUFFIXES ${_zlib_ORIG_CMAKE_FIND_LIBRARY_SUFFIXES})
     unset(_zlib_ORIG_CMAKE_FIND_LIBRARY_SUFFIXES)
@@ -41,7 +41,7 @@ if(WITH_JPEG)
     ocv_clear_vars(JPEG_FOUND)
   else()
     ocv_clear_internal_cache_vars(JPEG_LIBRARY JPEG_INCLUDE_DIR)
-    include(FindJPEG)
+    find_package(JPEG REQUIRED)
   endif()
 
   if(NOT JPEG_FOUND)
@@ -87,7 +87,7 @@ if(WITH_TIFF)
     ocv_clear_vars(TIFF_FOUND)
   else()
     ocv_clear_internal_cache_vars(TIFF_LIBRARY TIFF_INCLUDE_DIR)
-    include(FindTIFF)
+    find_package(TIFF REQUIRED)
     if(TIFF_FOUND)
       ocv_parse_header("${TIFF_INCLUDE_DIR}/tiff.h" TIFF_VERSION_LINES TIFF_VERSION_CLASSIC TIFF_VERSION_BIG TIFF_VERSION TIFF_BIGTIFF_VERSION)
     endif()
@@ -131,7 +131,7 @@ if(WITH_WEBP)
     ocv_clear_vars(WEBP_FOUND WEBP_LIBRARY WEBP_LIBRARIES WEBP_INCLUDE_DIR)
   else()
     ocv_clear_internal_cache_vars(WEBP_LIBRARY WEBP_INCLUDE_DIR)
-    include(cmake/OpenCVFindWebP.cmake)
+    find_package(WEBP NAMES WebP REQUIRED)
     if(WEBP_FOUND)
       set(HAVE_WEBP 1)
     endif()
@@ -172,7 +172,7 @@ if(WITH_JASPER)
   if(BUILD_JASPER)
     ocv_clear_vars(JASPER_FOUND)
   else()
-    include(FindJasper)
+    find_package(Jasper REQUIRED)
   endif()
 
   if(NOT JASPER_FOUND)
@@ -197,7 +197,7 @@ if(WITH_PNG)
     ocv_clear_vars(PNG_FOUND)
   else()
     ocv_clear_internal_cache_vars(PNG_LIBRARY PNG_INCLUDE_DIR)
-    include(FindPNG)
+    find_package(PNG REQUIRED)
     if(PNG_FOUND)
       include(CheckIncludeFile)
       check_include_file("${PNG_PNG_INCLUDE_DIR}/libpng/png.h" HAVE_LIBPNG_PNG_H)
@@ -249,7 +249,7 @@ endif()
 
 # --- GDAL (optional) ---
 if(WITH_GDAL)
-    find_package(GDAL QUIET)
+    find_package(GDAL REQUIRED)
 
     if(NOT GDAL_FOUND)
         set(HAVE_GDAL NO)
@@ -261,7 +261,7 @@ if(WITH_GDAL)
 endif()
 
 if(WITH_GDCM)
-  find_package(GDCM QUIET)
+  find_package(GDCM REQUIRED)
   if(NOT GDCM_FOUND)
     set(HAVE_GDCM NO)
     ocv_clear_vars(GDCM_VERSION GDCM_LIBRARIES)
--- a/modules/imgcodecs/CMakeLists.txt
+++ b/modules/imgcodecs/CMakeLists.txt
@@ -20,7 +20,7 @@ endif()
 
 if(HAVE_WEBP)
   add_definitions(-DHAVE_WEBP)
-  ocv_include_directories(${WEBP_INCLUDE_DIR})
+  ocv_include_directories(${WEBP_INCLUDE_DIRS})
   list(APPEND GRFMT_LIBS ${WEBP_LIBRARIES})
 endif()
 
