cmake_minimum_required(VERSION 3.21)

include(CMakePackageConfigHelpers)

project(Boost VERSION @VERSION@ LANGUAGES NONE)

set(CONFIG_VERSION_FILE_NAME "${CMAKE_CURRENT_BINARY_DIR}/tmpinst/BoostConfigVersion.cmake")

write_basic_package_version_file("${CONFIG_VERSION_FILE_NAME}" COMPATIBILITY SameMajorVersion ARCH_INDEPENDENT)

install(FILES 
          "${CONFIG_VERSION_FILE_NAME}"
          "${CMAKE_CURRENT_SOURCE_DIR}/config/BoostConfig.cmake"
        DESTINATION "share/boost")