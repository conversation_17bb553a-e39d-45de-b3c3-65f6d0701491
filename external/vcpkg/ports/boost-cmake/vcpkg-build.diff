diff --git a/include/BoostRoot.cmake b/include/BoostRoot.cmake
index 98a19df..e80bbbc 100644
--- a/include/BoostRoot.cmake
+++ b/include/BoostRoot.cmake
@@ -21,7 +21,7 @@ set(__boost_incompatible_libraries "")
 
 # Define cache variables if root project
 
-if(CMAKE_SOURCE_DIR STREQUAL Boost_SOURCE_DIR)
+if(1)
 
   # --with-<library>
   set(BOOST_INCLUDE_LIBRARIES "" CACHE STRING
@@ -200,6 +200,7 @@ endfunction()
 function(__boost_scan_dependencies lib var)
 
   set(result "")
+  set(required_components "")
 
   if(EXISTS "${BOOST_SUPERPROJECT_SOURCE_DIR}/libs/${lib}/CMakeLists.txt")
 
@@ -209,6 +210,7 @@ function(__boost_scan_dependencies lib var)
 
       if(line MATCHES "^[ ]*Boost::([A-Za-z0-9_]+)[ ]*$")
 
+        list(APPEND required_components ${CMAKE_MATCH_1})
         string(REGEX REPLACE "^numeric_" "numeric/" dep ${CMAKE_MATCH_1})
         list(APPEND result ${dep})
 
@@ -218,6 +220,11 @@ function(__boost_scan_dependencies lib var)
 
   endif()
 
+  list(REMOVE_DUPLICATES required_components)
+  list(REMOVE_ITEM required_components boost ${lib}) # due to property_tree and python
+  if(required_components)
+    find_package(Boost COMPONENTS ${required_components} REQUIRED CONFIG)
+  endif()
   set(${var} ${result} PARENT_SCOPE)
 
 endfunction()
@@ -280,10 +287,10 @@ while(__boost_libs_to_scan)
 
   list(REMOVE_DUPLICATES __boost_dependencies)
 
-  set(__boost_libs_to_scan ${__boost_dependencies})
 
   if(__boost_libs_to_scan)
     list(REMOVE_ITEM __boost_libs_to_scan ${__boost_include_libraries})
+    list(REMOVE_ITEM __boost_libs_to_scan ${__boost_lib})
   endif()
 
   list(APPEND __boost_include_libraries ${__boost_libs_to_scan})
@@ -430,6 +437,7 @@ if(CMAKE_SKIP_INSTALL_RULES)
 
 endif()
 
+if(0)
 set(CONFIG_INSTALL_DIR "${BOOST_INSTALL_CMAKEDIR}/Boost-${BOOST_SUPERPROJECT_VERSION}")
 set(CONFIG_FILE_NAME "${CMAKE_CURRENT_LIST_DIR}/../config/BoostConfig.cmake")
 
@@ -453,9 +461,5 @@ else()
 endif()
 
 install(FILES "${CONFIG_VERSION_FILE_NAME}" DESTINATION "${CONFIG_INSTALL_DIR}")
+endif()
 
-set(CPACK_PACKAGE_VENDOR "Boost")
-set(CPACK_GENERATOR "TGZ")
-set(CPACK_RESOURCE_FILE_LICENSE "${Boost_SOURCE_DIR}/LICENSE_1_0.txt")
-set(CPACK_RESOURCE_FILE_README "${Boost_SOURCE_DIR}/README.md")
-include(CPack)
