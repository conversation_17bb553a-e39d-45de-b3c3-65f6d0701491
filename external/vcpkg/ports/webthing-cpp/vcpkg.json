{"name": "webthing-cpp", "version": "1.2.0", "description": "Webthing-CPP is a modern CPP/C++17 implementation of the WebThings API.", "homepage": "https://github.com/bw-hro/webthing-cpp", "license": "MIT", "dependencies": ["json-schema-validator", "mdns", "<PERSON><PERSON><PERSON>-<PERSON><PERSON>", "uwebsockets", {"name": "vcpkg-cmake", "host": true}], "features": {"ssl": {"description": "Support HTTPS via uwebsockets", "dependencies": [{"name": "uwebsockets", "features": ["ssl"]}]}}}