diff --git "a/include/pbc_curve.h" "b/include/pbc_curve.h"
--- "a/include/pbc_curve.h"
+++ "b/include/pbc_curve.h"
@@ -60,7 +60,9 @@
 
 void field_curve_set_quotient_cmp(field_ptr c, mpz_t quotient_cmp);
 
+#ifdef __GNUC__
 #pragma GCC visibility push(hidden)
+#endif
 // Internal:
 
 element_ptr curve_x_coord(element_t e);
@@ -74,6 +76,8 @@
 void curve_set_si(element_t R, long int x, long int y);
 void curve_set_gen_no_cofac(element_ptr a);
 
+#ifdef __GNUC__
 #pragma GCC visibility pop
+#endif
 
 #endif //__PBC_CURVE_H__
diff --git "a/include/pbc_vc_compat.win32.h" "b/include/pbc_vc_compat.win32.h"
index 27d3bba..7f772d4 100644
--- "a/include/pbc_vc_compat.win32.h"
+++ "b/include/pbc_vc_compat.win32.h"
@@ -3,7 +3,3 @@
 #define __attribute__(X)
 #define inline
 #define __func__ __FUNCTION__
-
-#define NULL 0
-
-#define snprintf _snprintf
\ No newline at end of file
diff --git "a/pbcwin/projects/pbclib.vcxproj" "b/pbcwin/projects/pbclib.vcxproj"
index f0a9b3f..507c24d 100644
--- "a/pbcwin/projects/pbclib.vcxproj"
+++ "b/pbcwin/projects/pbclib.vcxproj"
@@ -214,13 +214,13 @@ ﻿<?xml version="1.0" encoding="utf-8"?>
   </PropertyGroup>
   <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
     <IncludePath>$(SolutionDir)..\include;$(SolutionDir)..;$(SolutionDir)..\..\mpir\lib\$(Platform)\Release;$(IncludePath)</IncludePath>
-    <LibraryPath>$(SolutionDir)..\..\mpir\lib\x64\Release;$(LibraryPath)</LibraryPath>
+    <LibraryPath>$(SolutionDir)..\..\mpir\lib\$(Platform)\Release;$(LibraryPath)</LibraryPath>
     <OutDir>$(SolutionDir)lib\$(Platform)\Release\</OutDir>
     <IntDir>$(SolutionDir)obj\$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
   </PropertyGroup>
   <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
-    <IncludePath>$(SolutionDir)..\include;$(SolutionDir)..;$(SolutionDir)..\..\mpir\lib\x64\Release;$(IncludePath)</IncludePath>
-    <LibraryPath>$(SolutionDir)..\..\mpir\lib\x64\Release;$(LibraryPath)</LibraryPath>
+    <IncludePath>$(SolutionDir)..\include;$(SolutionDir)..;$(SolutionDir)..\..\mpir\lib\$(Platform)\Release;$(IncludePath)</IncludePath>
+    <LibraryPath>$(SolutionDir)..\..\mpir\lib\$(Platform)\Release;$(LibraryPath)</LibraryPath>
     <OutDir>$(SolutionDir)lib\$(Platform)\Release\</OutDir>
     <IntDir>$(SolutionDir)obj\$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
   </PropertyGroup>
@@ -245,7 +245,8 @@ ﻿<?xml version="1.0" encoding="utf-8"?>
       <PreprocessorDefinitions>_CRT_SECURE_NO_WARNINGS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
       <ForcedIncludeFiles>pbc_vc_compat.win32.h</ForcedIncludeFiles>
       <DisableSpecificWarnings>4068</DisableSpecificWarnings>
-      <RuntimeLibrary>MultiThreadedDebug</RuntimeLibrary>
+      <RuntimeLibrary>$(RuntimeLibrary)</RuntimeLibrary>
+      <DebugInformationFormat>OldStyle</DebugInformationFormat>
     </ClCompile>
     <Link>
       <GenerateDebugInformation>true</GenerateDebugInformation>
@@ -275,7 +276,8 @@ echo ***************************************************************************
       <PreprocessorDefinitions>_CRT_SECURE_NO_WARNINGS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
       <ForcedIncludeFiles>pbc_vc_compat.win32.h</ForcedIncludeFiles>
       <DisableSpecificWarnings>4068</DisableSpecificWarnings>
-      <RuntimeLibrary>MultiThreadedDebug</RuntimeLibrary>
+      <RuntimeLibrary>$(RuntimeLibrary)</RuntimeLibrary>
+      <DebugInformationFormat>OldStyle</DebugInformationFormat>
     </ClCompile>
     <Link>
       <GenerateDebugInformation>true</GenerateDebugInformation>
@@ -305,7 +307,7 @@ echo ***************************************************************************
       <PreprocessorDefinitions>_CRT_SECURE_NO_WARNINGS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
       <ForcedIncludeFiles>pbc_vc_compat.win32.h</ForcedIncludeFiles>
       <DisableSpecificWarnings>4068</DisableSpecificWarnings>
-      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
+      <RuntimeLibrary>$(RuntimeLibrary)</RuntimeLibrary>
     </ClCompile>
     <Link>
       <GenerateDebugInformation>true</GenerateDebugInformation>
@@ -332,7 +334,7 @@ echo ***************************************************************************
       <PreprocessorDefinitions>_CRT_SECURE_NO_WARNINGS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
       <ForcedIncludeFiles>pbc_vc_compat.win32.h</ForcedIncludeFiles>
       <DisableSpecificWarnings>4068</DisableSpecificWarnings>
-      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
+      <RuntimeLibrary>$(RuntimeLibrary)</RuntimeLibrary>
     </ClCompile>
     <Link>
       <GenerateDebugInformation>true</GenerateDebugInformation>
@@ -361,7 +363,7 @@ echo ***************************************************************************
       <PreprocessorDefinitions>_CRT_SECURE_NO_WARNINGS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
       <ForcedIncludeFiles>pbc_vc_compat.win32.h</ForcedIncludeFiles>
       <DisableSpecificWarnings>4068</DisableSpecificWarnings>
-      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
+      <RuntimeLibrary>$(RuntimeLibrary)</RuntimeLibrary>
     </ClCompile>
     <Link>
       <GenerateDebugInformation>true</GenerateDebugInformation>
@@ -395,7 +397,7 @@ echo ***************************************************************************
       <PreprocessorDefinitions>_CRT_SECURE_NO_WARNINGS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
       <ForcedIncludeFiles>pbc_vc_compat.win32.h</ForcedIncludeFiles>
       <DisableSpecificWarnings>4068</DisableSpecificWarnings>
-      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
+      <RuntimeLibrary>$(RuntimeLibrary)</RuntimeLibrary>
     </ClCompile>
     <Link>
       <GenerateDebugInformation>true</GenerateDebugInformation>
@@ -429,7 +431,7 @@ echo ***************************************************************************
       <PreprocessorDefinitions>_CRT_SECURE_NO_WARNINGS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
       <ForcedIncludeFiles>pbc_vc_compat.win32.h</ForcedIncludeFiles>
       <DisableSpecificWarnings>4068</DisableSpecificWarnings>
-      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
+      <RuntimeLibrary>$(RuntimeLibrary)</RuntimeLibrary>
     </ClCompile>
     <Link>
       <GenerateDebugInformation>true</GenerateDebugInformation>
@@ -460,7 +462,7 @@ echo ***************************************************************************
       <PreprocessorDefinitions>_CRT_SECURE_NO_WARNINGS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
       <ForcedIncludeFiles>pbc_vc_compat.win32.h</ForcedIncludeFiles>
       <DisableSpecificWarnings>4068</DisableSpecificWarnings>
-      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
+      <RuntimeLibrary>$(RuntimeLibrary)</RuntimeLibrary>
     </ClCompile>
     <Link>
       <GenerateDebugInformation>true</GenerateDebugInformation>
