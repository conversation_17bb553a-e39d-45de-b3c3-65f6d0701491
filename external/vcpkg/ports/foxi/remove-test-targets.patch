diff --git a/CMakeLists.txt b/CMakeLists.txt
index b406d0f..c5d04d5 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -65,7 +65,7 @@ if(MSVC)
   add_msvc_runtime_flag(foxi_loader)
 endif()
 
-if (NOT ANDROID AND NOT IOS)
+if (FALSE)
   # ---[ FOXI wrapper
   add_library(foxi_wrapper MODULE foxi/onnxifi_wrapper.c)
   target_include_directories(foxi_wrapper PRIVATE
@@ -93,17 +93,6 @@ if (NOT ANDROID AND NOT IOS)
   endif()
 endif()
 
-# ---[ FOXI dummy backend
-add_library(foxi_dummy SHARED foxi/onnxifi_dummy.c)
-target_include_directories(foxi_dummy PRIVATE
-  $<BUILD_INTERFACE:${FOXI_ROOT}>
-  $<INSTALL_INTERFACE:include>)
-target_link_libraries(foxi_dummy PUBLIC foxi ${CMAKE_DL_LIBS})
-target_compile_definitions(foxi_dummy PRIVATE ONNXIFI_BUILD_LIBRARY=TRUE)
-if(MSVC)
-  add_msvc_runtime_flag(foxi_dummy)
-endif()
-
 install(DIRECTORY ${FOXI_ROOT}/foxi
         DESTINATION include
         FILES_MATCHING
@@ -114,10 +103,10 @@ install(DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}/foxi
         PATTERN "*.h")
 
 install(TARGETS
-  foxi foxi_dummy foxi_loader
+  foxi_loader
   EXPORT ONNXTargets DESTINATION lib)
 
-if(NOT ANDROID AND NOT IOS)
+if(FALSE)
   install(TARGETS foxi_wrapper
     EXPORT ONNXTargets DESTINATION lib)
 endif()

diff --git a/CMakeLists.txt b/CMakeLists.txt
index c5d04d5..cc7d18a 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -61,9 +61,6 @@ set_target_properties(foxi_loader
                                  C_EXTENSIONS
                                  NO)
 target_link_libraries(foxi_loader PUBLIC foxi ${CMAKE_DL_LIBS})
-if(MSVC)
-  add_msvc_runtime_flag(foxi_loader)
-endif()
 
 if (FALSE)
   # ---[ FOXI wrapper

