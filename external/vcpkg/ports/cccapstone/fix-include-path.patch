diff --git a/cppbindings/CsCapstoneHelper.hh b/cppbindings/CsCapstoneHelper.hh
index daf7a73..8ed5194 100644
--- a/cppbindings/CsCapstoneHelper.hh
+++ b/cppbindings/CsCapstoneHelper.hh
@@ -1,6 +1,6 @@
 #pragma once
 
-#include <capstone.h>
+#include <capstone/capstone.h>
 #include <memory>
 
 struct CS_HANDLE :
diff --git a/cppbindings/CsIns.hpp b/cppbindings/CsIns.hpp
index 6e8ba71..c723be9 100644
--- a/cppbindings/CsIns.hpp
+++ b/cppbindings/CsIns.hpp
@@ -1,6 +1,6 @@
 #pragma once
 
-#include <capstone.h>
+#include <capstone/capstone.h>
 #include "CsCapstoneHelper.hh"
 
 //x86_insn_group, x86_reg, x86_op_type, x86_insn
