diff --git a/CMake/RmlUiConfig.cmake.in b/CMake/RmlUiConfig.cmake.in
index a69348fd..9d758860 100644
--- a/CMake/RmlUiConfig.cmake.in
+++ b/CMake/RmlUiConfig.cmake.in
@@ -27,8 +27,6 @@ macro(report_dependency_found_or_error friendly_name target_name)
 	message(STATUS "Found ${friendly_name} target ${target_name}${success_message}")
 endmacro()
 
-list(APPEND CMAKE_MODULE_PATH "${CMAKE_CURRENT_LIST_DIR}/Modules")
-
 include("${CMAKE_CURRENT_LIST_DIR}/Dependencies.cmake")
 
 include("${CMAKE_CURRENT_LIST_DIR}/RmlUiTargets.cmake")
diff --git a/CMakeLists.txt b/CMakeLists.txt
index f13434fc..2aef42fb 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -189,9 +189,6 @@ install(FILES
 	DESTINATION
 	"${RMLUI_INSTALL_TARGETS_DIR}"
 )
-install(DIRECTORY "${CMAKE_CURRENT_SOURCE_DIR}/CMake/Modules"
-	DESTINATION "${RMLUI_INSTALL_TARGETS_DIR}"
-)
 
 if(RMLUI_IS_ROOT_PROJECT)
 	# Export build tree targets if RmlUi is the top-level project.
