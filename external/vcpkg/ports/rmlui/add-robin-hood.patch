diff --git a/Include/RmlUi/Config/Config.h b/Include/RmlUi/Config/Config.h
index 15d984a3..019db32c 100644
--- a/Include/RmlUi/Config/Config.h
+++ b/Include/RmlUi/Config/Config.h
@@ -58,7 +58,7 @@
 	#else
 		#include "../Core/Containers/itlib/flat_map.hpp"
 		#include "../Core/Containers/itlib/flat_set.hpp"
-		#include "../Core/Containers/robin_hood.h"
+		#include <robin_hood.h>
 	#endif // RMLUI_NO_THIRDPARTY_CONTAINERS
 
 namespace Rml {
diff --git a/Source/Core/CMakeLists.txt b/Source/Core/CMakeLists.txt
index c377ff49..7dff6a07 100644
--- a/Source/Core/CMakeLists.txt
+++ b/Source/Core/CMakeLists.txt
@@ -233,7 +233,6 @@ target_sources(rmlui_core PRIVATE
 	"${PROJECT_SOURCE_DIR}/Include/RmlUi/Core/ComputedValues.h"
 	"${PROJECT_SOURCE_DIR}/Include/RmlUi/Core/Containers/itlib/flat_map.hpp"
 	"${PROJECT_SOURCE_DIR}/Include/RmlUi/Core/Containers/itlib/flat_set.hpp"
-	"${PROJECT_SOURCE_DIR}/Include/RmlUi/Core/Containers/robin_hood.h"
 	"${PROJECT_SOURCE_DIR}/Include/RmlUi/Core/Context.h"
 	"${PROJECT_SOURCE_DIR}/Include/RmlUi/Core/ContextInstancer.h"
 	"${PROJECT_SOURCE_DIR}/Include/RmlUi/Core/ConvolutionFilter.h"
@@ -379,6 +378,9 @@ elseif(rmlui_core_TYPE STREQUAL "SHARED_LIBRARY")
 endif()
 unset(rmlui_core_TYPE)
 
+find_path(ROBIN_HOOD_INCLUDE_DIR robin_hood.h)
+target_include_directories(rmlui_core PUBLIC ${ROBIN_HOOD_INCLUDE_DIR})
+
 if(RMLUI_FONT_ENGINE STREQUAL "freetype")
 	# Include the source files for the default font engine.
 	add_subdirectory("FontEngineDefault")
