{"name": "rmlui", "version": "6.0", "port-version": 1, "maintainers": "<PERSON> <<EMAIL>>", "description": "RmlUi is the C++ user interface library based on the HTML and CSS standards, designed as a complete solution for any project's interface needs.", "homepage": "https://github.com/mikke89/RmlUi", "documentation": "https://mikke89.github.io/RmlUiDoc/", "license": "MIT", "dependencies": ["robin-hood-hashing", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "default-features": ["freetype"], "features": {"freetype": {"description": "Include font engine based on FreeType", "dependencies": [{"name": "freetype", "default-features": false}]}, "lua": {"description": "Build Lua bindings", "dependencies": ["lua"]}, "svg": {"description": "Enable plugin for SVG images", "dependencies": ["lunasvg"]}}}