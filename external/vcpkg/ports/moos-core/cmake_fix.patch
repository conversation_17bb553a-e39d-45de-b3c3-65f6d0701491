diff --git a/CMakeLists.txt b/CMakeLists.txt
index 75f367f..6ef3dc2 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -190,7 +190,7 @@ add_subdirectory(Core)
 export(
     TARGETS MOOS
     NAMESPACE MOOS::
-    FILE UseMOOS.cmake
+    FILE UseMOOSTargets.cmake
 )
 
 # Support existing projects that expect to find MOOS_LIBRARIES and
@@ -222,7 +222,7 @@ set(PROJECT_CONFIG_PATH "lib/cmake/MOOS")
 install(
     EXPORT MOOS
     NAMESPACE MOOS::
-    FILE UseMOOS.cmake
+    FILE UseMOOSTargets.cmake
     DESTINATION ${PROJECT_CONFIG_PATH}
 )
 
diff --git a/Core/libMOOS/CMakeLists.txt b/Core/libMOOS/CMakeLists.txt
index b74a30f..492df50 100644
--- a/Core/libMOOS/CMakeLists.txt
+++ b/Core/libMOOS/CMakeLists.txt
@@ -196,7 +196,7 @@ mark_as_advanced(TIME_WARP_AGGLOMERATION_CONSTANT)
 install(
     DIRECTORY ${INCLUDE_ROOTS}
     DESTINATION .
-    FILES_MATCHING PATTERN "*.h" PATTERN "*.hxx"
+    FILES_MATCHING PATTERN "*.h" PATTERN "*.hxx" PATTERN "*.hpp"
 )
 
 # install libraries
diff --git a/MOOSConfig.cmake.in b/MOOSConfig.cmake.in
index 10af599..dff769a 100644
--- a/MOOSConfig.cmake.in
+++ b/MOOSConfig.cmake.in
@@ -2,7 +2,7 @@
 
 # Pick up the auto-generated file which knows how to add the imported library
 # targets for the libraries that MOOS exports.
-set(exports_file "${CMAKE_CURRENT_LIST_DIR}/UseMOOS.cmake")
+set(exports_file "${CMAKE_CURRENT_LIST_DIR}/UseMOOSTargets.cmake")
 include(${exports_file})
 
 include(FindPackageHandleStandardArgs)
