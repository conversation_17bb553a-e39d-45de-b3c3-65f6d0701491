{"name": "status-value-lite", "version": "1.1.0", "port-version": 3, "description": "status_value is a single-file header-only library for objects that represent a status and an optional value. It is intended for use with C++11 and later.", "homepage": "https://github.com/martinmoene/status-value-lite", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"test": {"description": "Build with test"}}}