diff --git a/CMakeLists.txt b/CMakeLists.txt
index 9e4b1f50..cb910688 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -60,3 +60,38 @@ endif()
 # USE_FOLDERS flag will organize godot-cpp targets under the subfolder
 # 'godot-cpp'. This is enable by default from CMake version 3.26
 set_property(GLOBAL PROPERTY USE_FOLDERS ON)
+
+
+
+include("CMakePackageConfigHelpers")
+include("GNUInstallDirs")
+
+string(TOLOWER "godot-cpp.template_${CMAKE_BUILD_TYPE}" INSTALL_TARGET)
+install(TARGETS "${INSTALL_TARGET}"
+    EXPORT "unofficial-godot-cpp-config"
+    ARCHIVE
+        DESTINATION "${CMAKE_INSTALL_LIBDIR}"
+)
+
+install(
+    DIRECTORY
+        "${CMAKE_CURRENT_SOURCE_DIR}/include/"
+        "${CMAKE_CURRENT_BINARY_DIR}/gen/include/"
+    DESTINATION "${CMAKE_INSTALL_INCLUDEDIR}"
+)
+install(FILES "${GODOTCPP_GDEXTENSION_DIR}/gdextension_interface.h"
+    DESTINATION "${CMAKE_INSTALL_INCLUDEDIR}"
+)
+
+install(EXPORT "unofficial-godot-cpp-config"
+    NAMESPACE "unofficial::"
+    DESTINATION "${CMAKE_INSTALL_DATADIR}/unofficial-godot-cpp"
+)
+
+write_basic_package_version_file("${CMAKE_CURRENT_BINARY_DIR}/unofficial-godot-cpp-config-version.cmake"
+    VERSION "4.4"
+    COMPATIBILITY "SameMinorVersion"
+)
+install(FILES "${CMAKE_CURRENT_BINARY_DIR}/unofficial-godot-cpp-config-version.cmake"
+    DESTINATION "${CMAKE_INSTALL_DATADIR}/unofficial-godot-cpp"
+)
diff --git a/cmake/GodotCPPModule.cmake b/cmake/GodotCPPModule.cmake
index ce087468..04b4679d 100644
--- a/cmake/GodotCPPModule.cmake
+++ b/cmake/GodotCPPModule.cmake
@@ -18,7 +18,6 @@ godot-cpp
     include( GodotCPPModule )
 
 ]=======================================================================]
-find_package(Python3 3.4 REQUIRED) # pathlib should be present
 
 #[[ Generate Trimmed API
 
diff --git a/cmake/godotcpp.cmake b/cmake/godotcpp.cmake
index c10d835d..cb2e8f70 100644
--- a/cmake/godotcpp.cmake
+++ b/cmake/godotcpp.cmake
@@ -105,7 +105,7 @@ function( godotcpp_options )
     # Except for macos universal, which can be set by GODOTCPP_MACOS_UNIVERSAL=YES
 
     # Input from user for GDExtension interface header and the API JSON file
-    set( GODOTCPP_GDEXTENSION_DIR "gdextension" CACHE PATH
+    set( GODOTCPP_GDEXTENSION_DIR "${CMAKE_CURRENT_SOURCE_DIR}/gdextension" CACHE PATH
             "Path to a custom directory containing GDExtension interface header and API JSON file ( /path/to/gdextension_dir )" )
     set( GODOTCPP_CUSTOM_API_FILE "" CACHE FILEPATH
             "Path to a custom GDExtension API JSON file (takes precedence over `GODOTCPP_GDEXTENSION_DIR`) ( /path/to/custom_api_file )")
@@ -288,7 +288,8 @@ function( godotcpp_generate )
     set( IS_DEV_BUILD "$<BOOL:${GODOTCPP_DEV_BUILD}>")
 
     ### Define our godot-cpp library targets
-    foreach ( TARGET_ALIAS template_debug template_release editor )
+    block()
+        string(TOLOWER "template_${CMAKE_BUILD_TYPE}" TARGET_ALIAS)
         set( TARGET_NAME "godot-cpp.${TARGET_ALIAS}" )
 
         # Generator Expressions that rely on the target
@@ -307,7 +308,7 @@ function( godotcpp_generate )
         )
 
         # the godot-cpp.* library targets
-        add_library( ${TARGET_NAME} STATIC EXCLUDE_FROM_ALL )
+        add_library( ${TARGET_NAME} STATIC )
         add_library( godot-cpp::${TARGET_ALIAS} ALIAS ${TARGET_NAME} )
 
         file( GLOB_RECURSE GODOTCPP_SOURCES LIST_DIRECTORIES NO CONFIGURE_DEPENDS src/*.cpp )
@@ -319,9 +320,10 @@ function( godotcpp_generate )
         )
 
         target_include_directories( ${TARGET_NAME} ${GODOTCPP_SYSTEM_HEADERS_ATTRIBUTE} PUBLIC
-                include
-                ${CMAKE_CURRENT_BINARY_DIR}/gen/include
-                ${GODOTCPP_GDEXTENSION_DIR}
+            "$<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>"
+            "$<BUILD_INTERFACE:${CMAKE_CURRENT_BINARY_DIR}/gen/include>"
+            "$<BUILD_INTERFACE:${GODOTCPP_GDEXTENSION_DIR}>"
+            "$<INSTALL_INTERFACE:include>"
         )
 
         set_target_properties( ${TARGET_NAME}
@@ -348,6 +350,8 @@ function( godotcpp_generate )
 
                 # Some IDE's respect this property to logically group targets
                 FOLDER "godot-cpp"
+
+                EXPORT_NAME "godot::cpp"
         )
 
         if( CMAKE_SYSTEM_NAME STREQUAL Android )
@@ -364,10 +368,9 @@ function( godotcpp_generate )
             windows_generate()
         endif ()
 
-    endforeach ()
+    endblock()
 
     # Added for backwards compatibility with prior cmake solution so that builds dont immediately break
     # from a missing target.
-    add_library( godot::cpp ALIAS godot-cpp.template_debug )
 
 endfunction()
diff --git a/cmake/web.cmake b/cmake/web.cmake
index 996a1e52..170c6ac9 100644
--- a/cmake/web.cmake
+++ b/cmake/web.cmake
@@ -26,7 +26,6 @@ function( web_generate )
             PUBLIC
             -sSIDE_MODULE
             -sSUPPORT_LONGJMP=wasm
-            -fno-exceptions
             $<${THREADS_ENABLED}:-sUSE_PTHREADS=1>
     )
 
diff --git a/cmake/windows.cmake b/cmake/windows.cmake
index 8e37e7e4..02a24fde 100644
--- a/cmake/windows.cmake
+++ b/cmake/windows.cmake
@@ -60,9 +60,9 @@ function( windows_options )
     message( STATUS "If not already cached, setting CMAKE_MSVC_RUNTIME_LIBRARY.\n"
             "\tFor more information please read godot-cpp/cmake/windows.cmake")
 
-    set( CMAKE_MSVC_RUNTIME_LIBRARY
-            "MultiThreaded$<IF:$<BOOL:${GODOTCPP_DEBUG_CRT}>,DebugDLL,$<$<NOT:$<BOOL:${GODOTCPP_USE_STATIC_CPP}>>:DLL>>"
-            CACHE STRING "Select the MSVC runtime library for use by compilers targeting the MSVC ABI.")
+    # set( CMAKE_MSVC_RUNTIME_LIBRARY
+    #         "MultiThreaded$<IF:$<BOOL:${GODOTCPP_DEBUG_CRT}>,DebugDLL,$<$<NOT:$<BOOL:${GODOTCPP_USE_STATIC_CPP}>>:DLL>>"
+    #         CACHE STRING "Select the MSVC runtime library for use by compilers targeting the MSVC ABI.")
 endfunction()
 
 
