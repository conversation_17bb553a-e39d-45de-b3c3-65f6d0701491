{"name": "usearch", "version": "2.17.2", "port-version": 1, "description": "Fastest Search & Clustering engine × for Vectors & Strings", "homepage": "https://github.com/unum-cloud/usearch", "license": "Apache-2.0", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"fp16": {"description": "Use software emulation for half-precision types", "dependencies": ["fp16"]}, "jemalloc": {"description": "Use JeMalloc for faster memory allocations", "dependencies": ["<PERSON><PERSON><PERSON><PERSON>"]}, "simsimd": {"description": "Use SimSIMD hardware-accelerated metrics", "dependencies": ["simsimd"]}}}