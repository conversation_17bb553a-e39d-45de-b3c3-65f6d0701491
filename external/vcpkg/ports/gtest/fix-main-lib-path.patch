diff --git a/googlemock/CMakeLists.txt b/googlemock/CMakeLists.txt
index 99b2411f..74610b12 100644
--- a/googlemock/CMakeLists.txt
+++ b/googlemock/CMakeLists.txt
@@ -112,8 +112,9 @@ target_include_directories(gmock_main SYSTEM INTERFACE
 
 ########################################################################
 #
-# Install rules.
-install_project(gmock gmock_main)
+# Install rules
+install_project(gmock)
+install_project(gmock_main)
 
 ########################################################################
 #
diff --git a/googletest/CMakeLists.txt b/googletest/CMakeLists.txt
index dce6a7c9..d8faf644 100644
--- a/googletest/CMakeLists.txt
+++ b/googletest/CMakeLists.txt
@@ -154,8 +154,9 @@ target_link_libraries(gtest_main PUBLIC gtest)
 
 ########################################################################
 #
-# Install rules.
-install_project(gtest gtest_main)
+# Install rules
+install_project(gtest)
+install_project(gtest_main)
 
 ########################################################################
 #
diff --git a/googletest/cmake/internal_utils.cmake b/googletest/cmake/internal_utils.cmake
index 580ac1cb..78a5b659 100644
--- a/googletest/cmake/internal_utils.cmake
+++ b/googletest/cmake/internal_utils.cmake
@@ -302,12 +302,16 @@ function(install_project)
       COMPONENT "${PROJECT_NAME}"
       DESTINATION "${CMAKE_INSTALL_INCLUDEDIR}")
     # Install the project targets.
+    set (LIB_INSTALL_DST ${CMAKE_INSTALL_LIBDIR})
+    if (${ARGN} MATCHES "_main")
+      set (LIB_INSTALL_DST ${CMAKE_INSTALL_LIBDIR}/manual-link)
+    endif()
     install(TARGETS ${ARGN}
       EXPORT ${targets_export_name}
       COMPONENT "${PROJECT_NAME}"
       RUNTIME DESTINATION "${CMAKE_INSTALL_BINDIR}"
-      ARCHIVE DESTINATION "${CMAKE_INSTALL_LIBDIR}"
-      LIBRARY DESTINATION "${CMAKE_INSTALL_LIBDIR}")
+      ARCHIVE DESTINATION "${LIB_INSTALL_DST}"
+      LIBRARY DESTINATION "${LIB_INSTALL_DST}")
     if(CMAKE_CXX_COMPILER_ID MATCHES "MSVC")
       # Install PDBs.
       foreach(t ${ARGN})
@@ -317,7 +321,7 @@ function(install_project)
         install(FILES
           "${t_pdb_output_directory}/\${CMAKE_INSTALL_CONFIG_NAME}/$<$<CONFIG:Debug>:${t_pdb_name_debug}>$<$<NOT:$<CONFIG:Debug>>:${t_pdb_name}>.pdb"
           COMPONENT "${PROJECT_NAME}"
-          DESTINATION ${CMAKE_INSTALL_LIBDIR}
+          DESTINATION ${LIB_INSTALL_DST}
           OPTIONAL)
       endforeach()
     endif()
