{"name": "flashlight-sequence", "version": "0.0.1", "description": "Flashlight Sequence is a library containing fast implementations of sequence loss algorithms.", "homepage": "https://github.com/flashlight/sequence", "license": "MIT", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"cuda": {"description": "Build CUDA components", "dependencies": ["cuda"]}, "openmp": {"description": "Build with OpenMP"}}}