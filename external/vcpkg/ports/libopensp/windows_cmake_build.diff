diff -Nru -x '*~' OpenSP-1.5.2.orig/CMakeLists.txt OpenSP-1.5.2/CMakeLists.txt
--- OpenSP-1.5.2.orig/CMakeLists.txt	1970-01-01 02:00:00.000000000 +0200
+++ OpenSP-1.5.2/CMakeLists.txt	2014-08-24 17:23:19.941495700 +0300
@@ -0,0 +1,20 @@
+project(opensp)
+
+cmake_minimum_required(VERSION 2.6)
+
+include_directories(
+    .
+    include
+    generic
+)
+
+file(GLOB SRC_SOURCES lib/*.cxx)
+file(GLOB libopensp_HEADERS config.h generic/*.h include/*.h)
+
+add_library(opensp ${SRC_SOURCES})
+set_target_properties(opensp PROPERTIES OUTPUT_NAME "osp")
+target_link_libraries(opensp)
+
+install(TARGETS opensp RUNTIME DESTINATION bin ARCHIVE DESTINATION lib LIBRARY DESTINATION lib)
+install(FILES ${libopensp_HEADERS} DESTINATION include/opensp)
+
diff -Nru -x '*~' OpenSP-1.5.2.orig/config.h OpenSP-1.5.2/config.h
--- OpenSP-1.5.2.orig/config.h	2005-12-23 16:16:30.000000000 +0200
+++ OpenSP-1.5.2/config.h	2014-08-24 17:26:33.724698900 +0300
@@ -26,6 +26,12 @@
 #endif
 #endif /* __GNUG__ */
 
+#ifdef __MINGW32__
+#define SP_HAVE_BOOL
+#define SP_HAVE_TYPENAME
+#define SP_DEFINE_TEMPLATES
+#endif /* __MINGW32__ */
+
 #if defined(sun) || defined(__sun)
 // struct stat has st_blksize member
 #define SP_STAT_BLKSIZE
@@ -80,6 +86,11 @@
 #define SP_HAVE_TYPENAME
 #endif
 
+#if _MSC_VER >=1800
+// Visual Studio 2013
+#define SP_ANSI_FOR_SCOPE
+#endif
+
 #define SP_HAVE_SETMODE
 #define SP_DLLEXPORT __declspec(dllexport)
 #define SP_DLLIMPORT __declspec(dllimport)
@@ -301,12 +312,5 @@
 #define PATH_SEPARATOR ':'
 #endif
 
-
-/* new stuff */
-
-#ifndef HAVE_MUTABLE
-#define mutable
-#endif
-
 // NOTE: This is processed as a Makefile, not as a header by autoconf.
 #define SP_PACKAGE "OpenSP"
diff -Nru -x '*~' OpenSP-1.5.2.orig/generic/SGMLApplication.h OpenSP-1.5.2/generic/SGMLApplication.h
--- OpenSP-1.5.2.orig/generic/SGMLApplication.h	2005-05-14 12:17:41.000000000 +0300
+++ OpenSP-1.5.2/generic/SGMLApplication.h	2014-08-24 17:23:19.957120700 +0300
@@ -269,6 +269,7 @@
     unsigned count_;
     friend class OpenEntityPtr;
   };
+  SGMLApplication();
   virtual ~SGMLApplication();
   virtual void appinfo(const AppinfoEvent &);
   virtual void startDtd(const StartDtdEvent &);
diff -Nru -x '*~' OpenSP-1.5.2.orig/include/OutputCharStream.h OpenSP-1.5.2/include/OutputCharStream.h
--- OpenSP-1.5.2.orig/include/OutputCharStream.h	2005-07-21 17:04:39.000000000 +0300
+++ OpenSP-1.5.2/include/OutputCharStream.h	2014-08-24 17:23:19.957120700 +0300
@@ -31,6 +31,7 @@
   OutputCharStream &operator<<(const char *);
   OutputCharStream &operator<<(const StringC &);
   OutputCharStream &operator<<(unsigned long);
+  OutputCharStream &operator<<(unsigned long long);
   OutputCharStream &operator<<(int);
   OutputCharStream &operator<<(Newline);
 private:
diff -Nru -x '*~' OpenSP-1.5.2.orig/lib/MessageReporter.cxx OpenSP-1.5.2/lib/MessageReporter.cxx
--- OpenSP-1.5.2.orig/lib/MessageReporter.cxx	2005-07-21 17:05:17.000000000 +0300
+++ OpenSP-1.5.2/lib/MessageReporter.cxx	2014-08-24 17:23:19.957120700 +0300
@@ -123,7 +123,11 @@
     os() << ':';
   }
   if (options_ & messageNumbers)
+#ifdef _WIN64
+    os() << (unsigned long long)message.type->module() << "." 
+#else
     os() << (unsigned long)message.type->module() << "." 
+#endif
       << (unsigned long)message.type->number() << ":";
   switch (message.type->severity()) {
   case MessageType::info:
diff -Nru -x '*~' OpenSP-1.5.2.orig/lib/OutputCharStream.cxx OpenSP-1.5.2/lib/OutputCharStream.cxx
--- OpenSP-1.5.2.orig/lib/OutputCharStream.cxx	2005-07-21 17:05:17.000000000 +0300
+++ OpenSP-1.5.2/lib/OutputCharStream.cxx	2014-08-24 17:23:19.957120700 +0300
@@ -61,6 +61,13 @@
   return *this << buf;
 }
 
+OutputCharStream &OutputCharStream::operator<<(unsigned long long n)
+{
+  char buf[sizeof(unsigned long long)*3 + 1];
+  sprintf(buf, "%I64u", n);
+  return *this << buf;
+}
+
 OutputCharStream &OutputCharStream::operator<<(int n)
 {
   char buf[sizeof(int)*3 + 2];
diff -Nru -x '*~' OpenSP-1.5.2.orig/lib/SGMLApplication.cxx OpenSP-1.5.2/lib/SGMLApplication.cxx
--- OpenSP-1.5.2.orig/lib/SGMLApplication.cxx	2005-07-21 17:05:18.000000000 +0300
+++ OpenSP-1.5.2/lib/SGMLApplication.cxx	2014-08-24 17:23:19.972745700 +0300
@@ -9,6 +9,10 @@
 #include "Boolean.h"
 #include "SGMLApplication.h"
 
+SGMLApplication::SGMLApplication()
+{
+}
+
 SGMLApplication::~SGMLApplication()
 {
 }
