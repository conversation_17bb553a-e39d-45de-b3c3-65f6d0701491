diff --git a/coin.m4 b/coin.m4
index a0ff20a6f..46e0519d1 100644
--- a/coin.m4
+++ b/coin.m4
@@ -773,7 +773,6 @@ dnl Create a automake conditional and PKG_CONFIG variable
   COIN_PKG_CONFIG_PATH="${PKG_CONFIG_PATH}"
   AC_SUBST(COIN_PKG_CONFIG_PATH)
 
-  COIN_PKG_CONFIG_PATH="${expanded_libdir}/pkgconfig:${COIN_PKG_CONFIG_PATH}"
   if test -n "$PKG_CONFIG"; then
     AC_MSG_NOTICE([$PKG_CONFIG path is "$COIN_PKG_CONFIG_PATH"])
   fi
@@ -1316,7 +1315,7 @@ AC_DEFUN([AC_COIN_CHK_ZLIB],
     fi
     if test x$coin_has_zlib = xyes ; then
       m4_foreach_w([myvar],[$1],
-        [m4_toupper(myvar)_LFLAGS="-l$ac_cv_search_gzopen $m4_toupper(myvar)_LFLAGS"
+        [m4_toupper(myvar)_LFLAGS="$ac_cv_search_gzopen $m4_toupper(myvar)_LFLAGS"
         ])
       AC_DEFINE(m4_toupper(AC_PACKAGE_NAME)_HAS_ZLIB,[1],[Define to 1 if zlib is available])
     fi
@@ -1357,7 +1356,7 @@ AC_DEFUN([AC_COIN_CHK_BZLIB],
     fi
     if test $coin_has_bzlib = yes ; then
       m4_foreach_w([myvar],[$1],
-        [m4_toupper(myvar)_LFLAGS="-l$ac_cv_search_BZ2_bzReadOpen $m4_toupper(myvar)_LFLAGS"
+        [m4_toupper(myvar)_LFLAGS="$ac_cv_search_BZ2_bzReadOpen $m4_toupper(myvar)_LFLAGS"
         ])
       AC_DEFINE(m4_toupper(AC_PACKAGE_NAME)_HAS_BZLIB,[1],[Define to 1 if bzlib is available])
     fi
diff --git a/coin_chk_lapack.m4 b/coin_chk_lapack.m4
index 6085069af..3314f97e2 100644
--- a/coin_chk_lapack.m4
+++ b/coin_chk_lapack.m4
@@ -186,9 +186,7 @@ dnl links. We check for both to ensure that blas lib also appears on link line
 dnl in case someone wants to use Blas functions but tests only for Lapack.
 dnl We skip this if int64
   if test "$lapack_keep_looking" = yes ; then
-    case " $2 " in
-      *\ int64\ * ) ;;
-      *) AC_COIN_CHK_MOD_EXISTS([lapack],[lapack blas],
+    AC_COIN_CHK_MOD_EXISTS([lapack],[lapack blas],
            [lapack_what="generic module (lapack.pc blas.pc)"
             AC_COIN_TRY_LINK([dsyev],[],[lapack],
               [coin_has_lapack=yes
@@ -196,8 +194,6 @@ dnl We skip this if int64
                lapack_pcfiles="lapack blas"],
               [AC_MSG_WARN([lapack.pc and blas.pc present, but could not find dsyev when trying to link with LAPACK.])],no)
            ])
-         ;;
-    esac
   fi
 dnl TODO do we need another check with lapack.pc only?
 
