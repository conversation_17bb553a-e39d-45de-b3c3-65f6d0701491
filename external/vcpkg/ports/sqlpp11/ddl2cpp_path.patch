diff --git a/CMakeLists.txt b/CMakeLists.txt
index bb05cd8..4e83094 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -113,7 +113,7 @@ endif()
 ### Packaging
 install(PROGRAMS ${PROJECT_SOURCE_DIR}/scripts/ddl2cpp
     RENAME sqlpp11-ddl2cpp
-    DESTINATION ${CMAKE_INSTALL_BINDIR}
+    DESTINATION tools/sqlpp11
 )
 
 write_basic_package_version_file(Sqlpp11ConfigVersion.cmake
diff --git a/cmake/configs/Sqlpp11Config.cmake b/cmake/configs/Sqlpp11Config.cmake
index bf340d6..249014d 100644
--- a/cmake/configs/Sqlpp11Config.cmake
+++ b/cmake/configs/Sqlpp11Config.cmake
@@ -55,7 +55,7 @@ endforeach()
 
 # Import "ddl2cpp" script
 if(NOT TARGET sqlpp11::ddl2cpp)
-    get_filename_component(sqlpp11_ddl2cpp_location "${CMAKE_CURRENT_LIST_DIR}/../../../bin/sqlpp11-ddl2cpp" REALPATH)
+    get_filename_component(sqlpp11_ddl2cpp_location "${CMAKE_CURRENT_LIST_DIR}/../../tools/sqlpp11/sqlpp11-ddl2cpp" REALPATH)
     if(NOT EXISTS "${sqlpp11_ddl2cpp_location}")
         message(FATAL_ERROR "The imported target sqlpp11::ddl2cpp references the file '${sqlpp11_ddl2cpp_location}' but this file does not exists.")
     endif()
