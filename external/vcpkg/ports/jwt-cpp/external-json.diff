diff --git a/CMakeLists.txt b/CMakeLists.txt
index 5cd876f..8e3dfe6 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -66,9 +66,8 @@ if(JWT_EXTERNAL_PICOJSON)
   find_package(picojson 1.3.0 REQUIRED)
 endif()
 
-find_package(nlohmann_json CONFIG)
 
-if(NOT nlohmann_json_FOUND)
+if(0)
   message(STATUS "jwt-cpp: using FetchContent for nlohmann json")
   include(FetchContent)
   FetchContent_Declare(nlohmann_json
@@ -121,6 +120,9 @@ endif()
 
 if(JWT_EXTERNAL_PICOJSON)
   target_link_libraries(jwt-cpp INTERFACE picojson::picojson>)
+else()
+  find_path(PICOJSON_INCLUDE_DIR "picojson/picojson.h" REQUIRED)
+  target_include_directories(jwt-cpp INTERFACE "${PICOJSON_INCLUDE_DIR}")
 endif()
 
 # <PERSON> needs relative paths so the files are placed correctly
@@ -139,7 +141,6 @@ install(EXPORT jwt-cpp-targets NAMESPACE jwt-cpp:: FILE jwt-cpp-targets.cmake
         DESTINATION ${JWT_CMAKE_FILES_INSTALL_DIR})
 install(DIRECTORY ${JWT_INCLUDE_PATH}/jwt-cpp DESTINATION ${CMAKE_INSTALL_INCLUDEDIR})
 if(NOT JWT_EXTERNAL_PICOJSON AND NOT JWT_DISABLE_PICOJSON)
-  install(FILES ${JWT_INCLUDE_PATH}/picojson/picojson.h DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}/picojson)
 endif()
 install(FILES ${CMAKE_CURRENT_BINARY_DIR}/jwt-cpp-config.cmake ${CMAKE_CURRENT_BINARY_DIR}/jwt-cpp-config-version.cmake
         DESTINATION ${JWT_CMAKE_FILES_INSTALL_DIR})
