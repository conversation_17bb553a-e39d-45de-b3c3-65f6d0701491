From 1004fb82914b8295fe4ec43afb510994babf2161 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Fri, 17 Mar 2023 15:09:17 -0700
Subject: [PATCH] workaround MSVC codegen bug in 17.5 on ARM64

---
 src/engine/glob.cpp | 3 +++
 1 file changed, 3 insertions(+)

diff --git a/src/engine/glob.cpp b/src/engine/glob.cpp
index bec00ee56..1ce66c7e6 100644
--- a/src/engine/glob.cpp
+++ b/src/engine/glob.cpp
@@ -111,6 +111,9 @@ int glob( const char * c, const char * s )
  * globchars() - build a bitlist to check for character group match.
  */
 
+#if defined(_MSC_VER) && _MSC_VER == 1935 && defined(_M_ARM64)
+__declspec(noinline)
+#endif
 static void globchars( const char * s,  const char * e, char * b )
 {
     int neg = 0;
-- 
2.33.0.vfs.0.0

