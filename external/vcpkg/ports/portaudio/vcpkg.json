{"name": "portaudio", "version": "19.7", "port-version": 6, "description": "PortAudio Portable Cross-platform Audio I/O API PortAudio is a free, cross-platform, open-source, audio I/O library.  It lets you write simple audio programs in 'C' or C++ that will compile and run on many platforms including Windows, Macintosh OS X, and Unix (OSS/ALSA). It is intended to promote the exchange of audio software between developers on different platforms. Many applications use PortAudio for Audio I/O.", "homepage": "http://www.portaudio.com", "license": "MIT", "supports": "!uwp", "dependencies": [{"name": "jack2", "platform": "!ios & !osx & !windows"}, {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}]}