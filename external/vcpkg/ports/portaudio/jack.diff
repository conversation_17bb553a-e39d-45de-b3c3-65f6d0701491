diff --git a/CMakeLists.txt b/CMakeLists.txt
index 122fe93..8983f85 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -288,7 +288,8 @@ ELSE()
       SET(PA_SOURCES ${PA_SOURCES} ${PA_JACK_SOURCES})
       SET(PA_PRIVATE_COMPILE_DEFINITIONS ${PA_PRIVATE_COMPILE_DEFINITIONS} PA_USE_JACK)
       SET(PA_LIBRARY_DEPENDENCIES ${PA_LIBRARY_DEPENDENCIES} ${JACK_LIBRARIES})
-      SET(PA_PKGCONFIG_LDFLAGS "${PA_PKGCONFIG_LDFLAGS} -ljack")
+      SET(PA_LIBRARY_DEPENDENCIES ${PA_LIBRARY_DEPENDENCIES} ${CMAKE_DL_LIBS})
+      SET(PA_PKGCONFIG_REQUIRES_PRIVATE "${PA_PKGCONFIG_REQUIRES_PRIVATE} jack")
     ENDIF()
 
     FIND_PACKAGE(ALSA)
diff --git a/cmake_support/portaudio-2.0.pc.in b/cmake_support/portaudio-2.0.pc.in
index 738803d..24b211c 100644
--- a/cmake_support/portaudio-2.0.pc.in
+++ b/cmake_support/portaudio-2.0.pc.in
@@ -6,6 +6,7 @@ includedir=${prefix}/include
 Name: PortAudio
 Description: Portable audio I/O
 Requires:
+Requires.private: @PA_PKGCONFIG_REQUIRES_PRIVATE@
 Version: @PA_PKGCONFIG_VERSION@
 
 Libs: -L${libdir} -lportaudio @PA_PKGCONFIG_LDFLAGS@
