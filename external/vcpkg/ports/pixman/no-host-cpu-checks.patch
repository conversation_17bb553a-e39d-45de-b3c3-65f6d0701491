diff --git a/meson.build b/meson.build
index 1cec227..1b07ca8 100644
--- a/meson.build
+++ b/meson.build
@@ -95,44 +95,7 @@ else
   mmx_flags = ['-mmmx', '-Winline']
 endif
 if not use_mmx.disabled()
-  if host_machine.cpu_family() == 'x86_64' or cc.get_id() == 'msvc'
-    have_mmx = true
-  elif host_machine.cpu_family() == 'x86' and cc.compiles('''
-      #include <mmintrin.h>
-      #include <stdint.h>
-
-      /* Check support for block expressions */
-      #define _mm_shuffle_pi16(A, N)                    \
-        ({                                              \
-        __m64 ret;                                      \
-                                                        \
-        /* Some versions of clang will choke on K */    \
-        asm ("pshufw %2, %1, %0\n\t"                    \
-             : "=y" (ret)                               \
-             : "y" (A), "K" ((const int8_t)N)           \
-        );                                              \
-                                                        \
-        ret;                                            \
-        })
-
-      int main () {
-          __m64 v = _mm_cvtsi32_si64 (1);
-          __m64 w;
-
-          w = _mm_shuffle_pi16(v, 5);
-
-          /* Some versions of clang will choke on this */
-          asm ("pmulhuw %1, %0\n\t"
-               : "+y" (w)
-               : "y" (v)
-          );
-
-          return _mm_cvtsi64_si32 (v);
-      }''',
-      args : mmx_flags,
-      name : 'MMX Intrinsic Support')
-    have_mmx = true
-  endif
+  have_mmx = true
 endif
 
 if have_mmx
@@ -154,29 +117,7 @@ elif cc.get_id() != 'msvc'
   sse2_flags = ['-msse2', '-Winline']
 endif
 if not use_sse2.disabled()
-  if host_machine.cpu_family() == 'x86'
-    if cc.compiles('''
-        #if defined(__GNUC__) && (__GNUC__ < 4 || (__GNUC__ == 4 && __GNUC_MINOR__ < 2))
-        #   if !defined(__amd64__) && !defined(__x86_64__)
-        #      error "Need GCC >= 4.2 for SSE2 intrinsics on x86"
-        #   endif
-        #endif
-        #include <mmintrin.h>
-        #include <xmmintrin.h>
-        #include <emmintrin.h>
-        int param;
-        int main () {
-          __m128i a = _mm_set1_epi32 (param), b = _mm_set1_epi32 (param + 1), c;
-          c = _mm_xor_si128 (a, b);
-          return _mm_cvtsi128_si32(c);
-        }''',
-        args : sse2_flags,
-        name : 'SSE2 Intrinsic Support')
-      have_sse2 = true
-    endif
-  elif host_machine.cpu_family() == 'x86_64'
-    have_sse2 = true
-  endif
+  have_sse2 = true
 endif
 
 if have_sse2
@@ -192,24 +133,8 @@ if cc.get_id() != 'msvc'
   ssse3_flags = ['-mssse3', '-Winline']
 endif
 
-# x64 pre-2010 MSVC compilers crashes when building the ssse3 code
-if not use_ssse3.disabled() and not (cc.get_id() == 'msvc' and cc.version().version_compare('<16') and host_machine.cpu_family() == 'x86_64')
-  if host_machine.cpu_family().startswith('x86')
-    if cc.compiles('''
-        #include <mmintrin.h>
-        #include <xmmintrin.h>
-        #include <emmintrin.h>
-        int param;
-        int main () {
-          __m128i a = _mm_set1_epi32 (param), b = _mm_set1_epi32 (param + 1), c;
-          c = _mm_xor_si128 (a, b);
-          return _mm_cvtsi128_si32(c);
-        }''',
-        args : ssse3_flags,
-        name : 'SSSE3 Intrinsic Support')
-      have_ssse3 = true
-    endif
-  endif
+if use_ssse3.enabled()
+  have_ssse3 = true
 endif
 
 if have_ssse3
