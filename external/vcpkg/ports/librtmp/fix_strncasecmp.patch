diff --git a/librtmp/rtmp_sys.h b/librtmp/rtmp_sys.h
index 6a3f215..1b5ac2a 100644
--- a/librtmp/rtmp_sys.h
+++ b/librtmp/rtmp_sys.h
@@ -28,11 +28,13 @@
 #include <ws2tcpip.h>
 
 #ifdef _MSC_VER	/* MSVC */
+#if _MSC_VER < 1900
 #define snprintf _snprintf
-#define strcasecmp stricmp
-#define strncasecmp strnicmp
 #define vsnprintf _vsnprintf
 #endif
+#define strcasecmp _stricmp
+#define strncasecmp _strnicmp
+#endif
 
 #define GetSockError()	WSAGetLastError()
 #define SetSockError(e)	WSASetLastError(e)
