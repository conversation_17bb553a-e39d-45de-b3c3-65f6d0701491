 include/mapnik/filesystem.hpp | 1 +
 1 file changed, 1 insertion(+)

diff --git a/include/mapnik/filesystem.hpp b/include/mapnik/filesystem.hpp
index 98ec5f8..1f57a3c 100644
--- a/include/mapnik/filesystem.hpp
+++ b/include/mapnik/filesystem.hpp
@@ -26,6 +26,7 @@
 #ifdef USE_BOOST_FILESYSTEM
 #include <boost/filesystem/operations.hpp> // for absolute, exists, etc
 #include <boost/filesystem/path.hpp>       // for path, operator/
+#include <boost/filesystem/directory.hpp>  // for directory_iterator
 #else
 #include <filesystem>
 #endif
