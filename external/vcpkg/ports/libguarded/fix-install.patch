diff --git a/CMakeLists.txt b/CMakeLists.txt
index 20eaf53..b81f056 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -32,20 +32,7 @@ include(CheckIncludeFiles)
 include(CheckTypeSize)
 
 # location for install or package
-if (CMAKE_SYSTEM_NAME MATCHES "Darwin")
-   include(GNUInstallDirs)
-   set(CMAKE_INSTALL_RPATH "@executable_path")
-
-elseif (CMAKE_SYSTEM_NAME MATCHES "(Linux|OpenBSD|FreeBSD|NetBSD|DragonFly)")
-   include(GNUInstallDirs)
-   set(CMAKE_INSTALL_RPATH "\$ORIGIN")
-
-elseif (CMAKE_SYSTEM_NAME MATCHES "Windows")
-   set(CMAKE_INSTALL_BINDIR bin)
-   set(CMAKE_INSTALL_LIBDIR lib)
-   set(CMAKE_INSTALL_INCLUDEDIR include)
-
-endif()
+include(GNUInstallDirs)
 
 set(PACKAGE           "cs_libguarded")
 set(PACKAGE_NAME      "CsLibGuarded")
@@ -98,13 +85,7 @@ else()
 endif()
 
 # destination for cmake export files
-if (CMAKE_SYSTEM_NAME MATCHES "Windows")
-   set(PKG_PREFIX "cmake/CsLibGuarded")
-
-else()
-   set(PKG_PREFIX "${CMAKE_INSTALL_LIBDIR}/cmake/CsLibGuarded")
-
-endif()
+set(PKG_PREFIX "${CMAKE_INSTALL_LIBDIR}/cmake/CsLibGuarded")
 
 # catch2 set up
 if(BUILD_TESTS)
