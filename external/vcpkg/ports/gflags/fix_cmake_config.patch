diff --git a/cmake/config.cmake.in b/cmake/config.cmake.in
index a512c2a..e7f50d0 100644
--- a/cmake/config.cmake.in
+++ b/cmake/config.cmake.in
@@ -78,7 +78,7 @@ if (NOT DEFINED @PACKAGE_PREFIX@_NOTHREADS)
     else ()
       set (@PACKAGE_PREFIX@_NOTHREADS FALSE)
     endif ()
-  elseif (TARGET ${@PACKAGE_PREFIX@_TARGET_PREFIX}PACKAGE_NAME@_static OR TARGET ${@PACKAGE_PREFIX@_TARGET_PREFIX}@PACKAGE_NAME@_shared)
+  elseif (TARGET ${@PACKAGE_PREFIX@_TARGET_PREFIX}@PACKAGE_NAME@_static OR TARGET ${@PACKAGE_PREFIX@_TARGET_PREFIX}@PACKAGE_NAME@_shared)
     set (@PACKAGE_PREFIX@_NOTHREADS FALSE)
   else ()
     set (@PACKAGE_PREFIX@_NOTHREADS TRUE)
