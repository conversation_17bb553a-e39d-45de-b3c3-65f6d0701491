{"name": "restinio", "version": "0.7.5", "description": "A header-only C++14 library that gives you an embedded HTTP/Websocket server targeted primarily for asynchronous processing of HTTP-requests.", "homepage": "https://github.com/Stiffstream/restinio", "license": "BSD-3-<PERSON><PERSON>", "dependencies": ["asio", "expected-lite", "fmt", "llhttp", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}]}