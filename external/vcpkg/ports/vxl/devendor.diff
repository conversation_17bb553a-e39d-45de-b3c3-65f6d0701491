diff --git a/config/cmake/Modules/FindGEOTIFF.cmake b/config/cmake/Modules/FindGEOTIFF.cmake
index bcc8fc5..ed9420e 100644
--- a/config/cmake/Modules/FindGEOTIFF.cmake
+++ b/config/cmake/Modules/FindGEOTIFF.cmake
@@ -18,7 +18,7 @@ if (${VXL_USE_GEOTIFF})
 
     # If this FORCE variable is unset or is FALSE, try to find a native library.
     if( NOT VXL_FORCE_V3P_GEOTIFF )
-      include( ${MODULE_PATH}/NewCMake/FindGEOTIFF.cmake )
+      find_package(GeoTIFF CONFIG REQUIRED)
       if( GEOTIFF_FOUND )
         set(VXL_USING_NATIVE_GEOTIFF "YES")
       endif()
diff --git a/config/cmake/Modules/VXLConfig_export.cmake.in b/config/cmake/Modules/VXLConfig_export.cmake.in
index 86bb564..c69b7d8 100644
--- a/config/cmake/Modules/VXLConfig_export.cmake.in
+++ b/config/cmake/Modules/VXLConfig_export.cmake.in
@@ -152,6 +152,10 @@ set(VXL_TIFF_INCLUDE_DIR "@TIFF_INCLUDE_DIR@")
 set(VXL_TIFF_LIBRARIES "@TIFF_LIBRARIES@")
 
 set(VXL_GEOTIFF_FOUND "@GEOTIFF_FOUND@")
+if(VXL_GEOTIFF_FOUND)
+    include(CMakeFindDependencyMacro)
+    find_dependency(GeoTIFF CONFIG)
+endif()
 set(VXL_GEOTIFF_INCLUDE_DIR "@GEOTIFF_INCLUDE_DIR@")
 set(VXL_GEOTIFF_LIBRARIES "@GEOTIFF_LIBRARIES@")
 
diff --git a/core/vgl/vgl_clip.hxx b/core/vgl/vgl_clip.hxx
index 9ff64f8..10c986a 100644
--- a/core/vgl/vgl_clip.hxx
+++ b/core/vgl/vgl_clip.hxx
@@ -144,7 +144,7 @@ namespace {
 
 #elif HAS_CLIPPER
 
-#include <clipper.hxx>
+#include <clipper.hpp>
 
 namespace {
   //: Creates a Clipper polygon from a vgl_polygon.
diff --git a/v3p/CMakeLists.txt b/v3p/CMakeLists.txt
index 251ef9a..78a84fc 100644
--- a/v3p/CMakeLists.txt
+++ b/v3p/CMakeLists.txt
@@ -48,6 +48,9 @@ CMAKE_DEPENDENT_OPTION( VXL_FORCE_V3P_RPLY "Use V3P instead of any native RPLY l
 mark_as_advanced( VXL_FORCE_V3P_RPLY )
 
 if(VXL_BUILD_CORE_IMAGING)
+  add_subdirectory(openjpeg2)
+elseif(0)
+  # not used by core and vcl
   include(${VXL_CMAKE_DIR}/FindZLIB.cmake)
   if (VXL_FORCE_V3P_ZLIB OR ( NOT VXL_USING_NATIVE_ZLIB ) AND IS_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/zlib)
     add_subdirectory(zlib)
