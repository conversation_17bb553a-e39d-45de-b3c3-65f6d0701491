diff --git a/config/cmake/Modules/FindOpenJPEG2.cmake b/config/cmake/Modules/FindOpenJPEG2.cmake
index c76bc44..41dc198 100644
--- a/config/cmake/Modules/FindOpenJPEG2.cmake
+++ b/config/cmake/Modules/FindOpenJPEG2.cmake
@@ -62,6 +62,8 @@ set(OPENJPEG2_DEFINITIONS "")
 
 if(OPENJPEG2_FOUND)
   set(VXL_USING_NATIVE_OPENJPEG2 "YES")
+elseif(NOT ENABLE_OPENJPEG)
+  set(VXL_USING_NATIVE_OPENJPEG2 "YES")  # to disable internal lib, too
 else()
   if( EXISTS ${VXL_ROOT_SOURCE_DIR}/v3p/openjpeg2/openjpeg.h)
     set(OPENJPEG2_FOUND TRUE)
diff --git a/v3p/openjpeg2/CMakeLists.txt b/v3p/openjpeg2/CMakeLists.txt
index 67b184e..354180b 100644
--- a/v3p/openjpeg2/CMakeLists.txt
+++ b/v3p/openjpeg2/CMakeLists.txt
@@ -49,6 +49,7 @@ if(VXL_MATH_LIBRARY_FOUND)
 endif()
 mark_as_advanced(VXL_MATH_LIBRARY_FOUND)
 set_target_properties(openjpeg2 PROPERTIES
+  OUTPUT_NAME   vxl_openjpeg
   VERSION       2.0.0
   DEFINE_SYMBOL OPJ_EXPORTS)
 endif()
