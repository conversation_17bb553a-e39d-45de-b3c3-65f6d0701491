diff --git a/src/D3D12MemAlloc.cpp b/src/D3D12MemAlloc.cpp
index 2555ef6..e2d444e 100644
--- a/src/D3D12MemAlloc.cpp
+++ b/src/D3D12MemAlloc.cpp
@@ -7181,11 +7181,11 @@ void AllocatorPimpl::BuildStatsString(WCHAR** ppStatsString, BOOL detailedMap)
                 json.WriteString(m_AdapterDesc.Description);
 
                 json.WriteString(L"DedicatedVideoMemory");
-                json.WriteNumber(m_AdapterDesc.DedicatedVideoMemory);
+                json.WriteNumber((UINT64)m_AdapterDesc.DedicatedVideoMemory);
                 json.WriteString(L"DedicatedSystemMemory");
-                json.WriteNumber(m_AdapterDesc.DedicatedSystemMemory);
+                json.WriteNumber((UINT64)m_AdapterDesc.DedicatedSystemMemory);
                 json.WriteString(L"SharedSystemMemory");
-                json.WriteNumber(m_AdapterDesc.SharedSystemMemory);
+                json.WriteNumber((UINT64)m_AdapterDesc.SharedSystemMemory);
                 
                 json.WriteString(L"ResourceHeapTier");
                 json.WriteNumber(static_cast<UINT>(m_D3D12Options.ResourceHeapTier));

