diff --git a/configure b/configure
index 1f0b9497cb..3243e23021 100644
--- a/src/CMakeLists.txt
+++ b/src/CMakeLists.txt
@@ -40,8 +40,12 @@         D3D12MA_IMPORTS
     )
 endif()
 
-install(TARGETS D3D12MemoryAllocator DESTINATION "lib")
+install(TARGETS D3D12MemoryAllocator EXPORT d3d12-memory-allocator-config
+    RUNTIME DESTINATION "${CMAKE_INSTALL_BINDIR}"
+    ARCHIVE DESTINATION "${CMAKE_INSTALL_LIBDIR}"
+    LIBRARY DESTINATION "${CMAKE_INSTALL_LIBDIR}"
+    PUBLIC_HEADER DESTINATION "${INSTALL_INCLUDE_DIR}")
 install(FILES "${PROJECT_SOURCE_DIR}/include/D3D12MemAlloc.h" DESTINATION "include")
 
 if(D3D12MA_BUILD_SAMPLE)
     if(WIN32)
@@ -126,3 +130,11 @@     else()
         message(STATUS "D3D12Sample application is not supported to Linux")
     endif()
 endif()
+
+target_include_directories(D3D12MemoryAllocator PUBLIC $<INSTALL_INTERFACE:include> $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}>)
+set_target_properties(D3D12MemoryAllocator PROPERTIES PUBLIC_HEADER "${PROJECT_SOURCE_DIR}/include/D3D12MemAlloc.h")
+
+install(
+    EXPORT d3d12-memory-allocator-config DESTINATION cmake/
+    NAMESPACE "unofficial::"
+)
