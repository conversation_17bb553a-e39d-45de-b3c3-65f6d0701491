diff --git a/CMakeLists.txt b/CMakeLists.txt
index 75660ca..fa1ee84 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -99,14 +99,14 @@ endif ()
 
 if (SHADERC_ENABLE_COPYRIGHT_CHECK)
   add_custom_target(check-copyright ALL
-    ${PYTHON_EXECUTABLE} ${CMAKE_CURRENT_SOURCE_DIR}/utils/add_copyright.py
+    ${Python_EXECUTABLE} ${CMAKE_CURRENT_SOURCE_DIR}/utils/add_copyright.py
     --check
     WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
     COMMENT "Check copyright")
 endif()
 
 add_custom_target(add-copyright
-  ${PYTHON_EXECUTABLE} ${CMAKE_CURRENT_SOURCE_DIR}/utils/add_copyright.py
+  ${Python_EXECUTABLE} ${CMAKE_CURRENT_SOURCE_DIR}/utils/add_copyright.py
   WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
   COMMENT "Add copyright")
 
diff --git a/utils/add_copyright.py b/utils/add_copyright.py
index ab3c5f1..7ea0897 100755
--- a/utils/add_copyright.py
+++ b/utils/add_copyright.py
@@ -1,4 +1,4 @@
-#!/usr/bin/env python
+#!/usr/bin/env python3
 # Copyright 2015 The Shaderc Authors. All rights reserved.
 #
 # Licensed under the Apache License, Version 2.0 (the "License");
diff --git a/utils/remove-file-by-suffix.py b/utils/remove-file-by-suffix.py
index 39af161..ce7c658 100755
--- a/utils/remove-file-by-suffix.py
+++ b/utils/remove-file-by-suffix.py
@@ -1,4 +1,4 @@
-#!/usr/bin/env python
+#!/usr/bin/env python3
 
 # Copyright 2015 The Shaderc Authors. All rights reserved.
 #
diff --git a/utils/update_build_version.py b/utils/update_build_version.py
index 11ee53e..b7ce5b8 100755
--- a/utils/update_build_version.py
+++ b/utils/update_build_version.py
@@ -1,4 +1,4 @@
-#!/usr/bin/env python
+#!/usr/bin/env python3
 
 # Copyright 2016 The Shaderc Authors. All rights reserved.
 #
