diff --git a/libshaderc/CMakeLists.txt b/libshaderc/CMakeLists.txt
index e0eea6c..b54ace4 100644
--- a/libshaderc/CMakeLists.txt
+++ b/libshaderc/CMakeLists.txt
@@ -28,7 +28,9 @@ if (NOT BUILD_SHARED_LIBS)
 add_library(shaderc STATIC ${SHADERC_SOURCES})
 shaderc_default_compile_options(shaderc)
 target_include_directories(shaderc
-    PUBLIC include
+    PUBLIC
+    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
+    $<INSTALL_INTERFACE:include>
     PRIVATE ${glslang_SOURCE_DIR}
             ${SPIRV-Headers_SOURCE_DIR}/include)
 set(shaderc_install_target shaderc)
@@ -37,7 +39,9 @@ else()
 add_library(shaderc_shared SHARED ${SHADERC_SOURCES})
 shaderc_default_compile_options(shaderc_shared)
 target_include_directories(shaderc_shared
-    PUBLIC include
+    PU<PERSON><PERSON>
+    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
+    $<INSTALL_INTERFACE:include>
     PRIVATE ${glslang_SOURCE_DIR}
             ${SPIRV-Headers_SOURCE_DIR}/include)
 target_compile_definitions(shaderc_shared
@@ -60,10 +64,15 @@ if(SHADERC_ENABLE_INSTALL)
       ${CMAKE_INSTALL_INCLUDEDIR}/shaderc)
 
   install(TARGETS ${shaderc_install_target} shaderc_util
+    EXPORT unofficial-shaderc-targets
     LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
     RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
     BUNDLE DESTINATION ${CMAKE_INSTALL_BINDIR}
     ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR})
+  install(EXPORT unofficial-shaderc-targets
+    NAMESPACE unofficial::shaderc::
+    DESTINATION share/unofficial-shaderc
+  )
 endif(SHADERC_ENABLE_INSTALL)
 
 find_package(Threads)
@@ -80,6 +89,15 @@ else()
 target_link_libraries(shaderc_shared PRIVATE ${SHADERC_LIBS})
 endif()
 
+file(WRITE "${CMAKE_CURRENT_BINARY_DIR}/unofficial-shaderc-config.cmake" [[
+include(CMakeFindDependencyMacro)
+find_dependency(glslang CONFIG)
+find_dependency(SPIRV-Tools CONFIG)
+find_dependency(SPIRV-Tools-opt CONFIG)
+include("${CMAKE_CURRENT_LIST_DIR}/unofficial-shaderc-targets.cmake")
+]])
+install(FILES ${CMAKE_CURRENT_BINARY_DIR}/unofficial-shaderc-config.cmake DESTINATION share/unofficial-shaderc)
+
 shaderc_add_tests(
   TEST_PREFIX shaderc
   LINK_LIBS shaderc
diff --git a/libshaderc_util/CMakeLists.txt b/libshaderc_util/CMakeLists.txt
index 99ce3c4..3d5a222 100644
--- a/libshaderc_util/CMakeLists.txt
+++ b/libshaderc_util/CMakeLists.txt
@@ -39,7 +39,7 @@ add_library(shaderc_util STATIC
 
 shaderc_default_compile_options(shaderc_util)
 target_include_directories(shaderc_util
-  PUBLIC include PRIVATE ${glslang_SOURCE_DIR})
+  PUBLIC $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include> PRIVATE ${glslang_SOURCE_DIR})
 # We use parts of Glslang's HLSL compilation interface, which
 # now requires this preprocessor definition.
 add_definitions(-DENABLE_HLSL)
