{"name": "libalk<PERSON><PERSON>", "version": "8.1.72", "description": "Common functionality for finance applications.", "homepage": "https://community.kde.org/Alkimia/libalkimia", "license": "LGPL-2.1-or-later", "supports": "!xbox", "dependencies": ["ecm", {"name": "gettext", "host": true, "features": ["tools"]}, "gmp", "kf5completion", "kf5config", "kf5coreaddons", "kf5i18n", "kf5iconthemes", "kf5kio", "kf5newstuff", "kf5textwidgets", "kf5xmlgui", "qt5-base", "qt5-tools", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"tools": {"description": "Enables tools, including Online Quotes Editor"}, "webengine": {"description": "Enables JavaScript support for parsing feature-rich websites", "dependencies": ["qt5-webengine"]}}}