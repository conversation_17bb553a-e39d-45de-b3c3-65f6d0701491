diff --git a/src/util.cpp b/src/util.cpp
index aabc23a..06e9ebe 100644
--- a/src/util.cpp
+++ b/src/util.cpp
@@ -434,9 +434,15 @@ bool ReadEnvironmentVariable(const char *var_name, std::string *_value) {
 #ifdef _MSC_VER
   __pragma(warning(disable : 4996));  // _CRT_SECURE_NO_WARNINGS
 #endif
+#if _WIN32_WINNT < 0x0A00
   auto env_str = std::getenv(var_name);
   if (!env_str) return false;
   if (_value) *_value = std::string(env_str);
+#else
+  //There is no support for environment variables in UWP
+  var_name; // Do nothing
+  *_value = std::string("");
+#endif
   return true;
 }
 
