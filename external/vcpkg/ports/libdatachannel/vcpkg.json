{"name": "libdatachannel", "version-semver": "0.22.6", "description": "libdatachannel is a standalone implementation of WebRTC Data Channels, WebRTC Media Transport, and WebSockets in C++17 with C bindings for POSIX platforms (including GNU/Linux, Android, and Apple macOS) and Microsoft Windows.", "homepage": "https://github.com/paullouisageneau/libdatachannel", "license": "MPL-2.0", "supports": "!xbox", "dependencies": ["libjuice", "<PERSON><PERSON><PERSON>-<PERSON><PERSON>", "openssl", "plog", "usrsctp", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "default-features": ["ws"], "features": {"srtp": {"description": "Use Cisco's libSRTP for media transport.", "dependencies": ["libsrtp"]}, "stdcall": {"description": "Use stdcall convention in callbacks"}, "ws": {"description": "Web Socket support"}}}