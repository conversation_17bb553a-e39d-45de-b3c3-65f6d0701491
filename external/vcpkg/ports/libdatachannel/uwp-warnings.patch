diff --git a/CMakeLists.txt b/CMakeLists.txt
index 1b5190b..763b49e 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -315,6 +315,10 @@ target_link_libraries(datachannel-static PRIVATE Usrsctp::Usrsctp plog::plog)
 if(WIN32)
 	target_link_libraries(datachannel PUBLIC ws2_32) # winsock2
 	target_link_libraries(datachannel-static PUBLIC ws2_32) # winsock2
+	if(CMAKE_SYSTEM_NAME STREQUAL "WindowsStore")
+		target_compile_options(datachannel PRIVATE /wd4996)
+		target_compile_options(datachannel-static PRIVATE /wd4996)
+	endif()
 endif()
 
 if (NO_WEBSOCKET)
