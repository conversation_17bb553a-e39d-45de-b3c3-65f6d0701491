diff --git a/CMakeLists.txt b/CMakeLists.txt
index 963c530..656ce14 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -1,6 +1,6 @@
 # cmake project file by <PERSON>
 # improved by <PERSON>
-cmake_minimum_required(VERSION 2.6)
+cmake_minimum_required(VERSION 2.8.12)
 
 project(Alut C)
 
@@ -27,7 +27,6 @@ include_directories(${Alut_SOURCE_DIR}/include)
 
 # What to build?
 option(BUILD_EXAMPLES "build example applications" ON)
-option(BUILD_STATIC "build static library too" OFF)
 option(BUILD_TESTS "build the test-suite" ON)
 
 # How to build it?
diff --git a/src/CMakeLists.txt b/src/CMakeLists.txt
index b72d1a1..5fc6cdc 100644
--- a/src/CMakeLists.txt
+++ b/src/CMakeLists.txt
@@ -39,35 +39,7 @@ set(ALUT_HEADERS
 	../include/AL/alut.h)
 source_group(APIHeader FILES ${ALUT_HEADERS})
 
-
-if(BUILD_STATIC)
-	# we can't create a static library with the same name
-	# as the shared one, so we copy it over after creation
-	add_library(alut_static STATIC ${ALUT_SOURCES} ${ALUT_INTERNAL_HEADERS} ${ALUT_HEADERS})
-	target_link_libraries(alut_static ${OPENAL_LIBRARY} ${ADD_LIBS})
-	if(UNIX)
-		target_link_libraries(alut_static m)
-	endif()	
-	if(NOT WIN32)
-		# TODO this is an inelegant hack...
-		add_custom_command(TARGET
-			alut_static
-			POST_BUILD
-			COMMAND
-			${CMAKE_COMMAND}
-			ARGS
-			-E
-			copy
-			${CMAKE_BINARY_DIR}/src/${CMAKE_STATIC_LIBRARY_PREFIX}alut_static${CMAKE_STATIC_LIBRARY_SUFFIX}
-			${CMAKE_BINARY_DIR}/src/${CMAKE_STATIC_LIBRARY_PREFIX}alut${CMAKE_STATIC_LIBRARY_SUFFIX})
-		install_files(/lib${LIB_SUFFIX}
-			FILES
-			${CMAKE_STATIC_LIBRARY_PREFIX}alut${CMAKE_STATIC_LIBRARY_SUFFIX})
-	endif()
-endif()
-
-
-add_library(alut SHARED ${ALUT_SOURCES} ${ALUT_INTERNAL_HEADERS} ${ALUT_HEADERS})
+add_library(alut ${ALUT_SOURCES} ${ALUT_INTERNAL_HEADERS} ${ALUT_HEADERS})
 set_property(TARGET
 	alut
 	PROPERTY
@@ -80,12 +52,40 @@ set_target_properties(alut
 	SOVERSION
 	${MAJOR_VERSION})
 target_link_libraries(alut ${OPENAL_LIBRARY})
+target_include_directories(alut PUBLIC ${OPENAL_INCLUDE_DIR})
 if(UNIX)
 	target_link_libraries(alut m)
 endif()	
+if(NOT BUILD_SHARED_LIBS)
+    target_compile_definitions(alut PUBLIC ALUT_BUILD_STATIC)
+endif()
 
 install(TARGETS alut
+	EXPORT "FreeALUTTargets"
 	LIBRARY DESTINATION lib${LIB_SUFFIX}
 	ARCHIVE DESTINATION lib${LIB_SUFFIX}
 	RUNTIME DESTINATION bin
-	PUBLIC_HEADER DESTINATION include/AL)
+	PUBLIC_HEADER DESTINATION include/AL
+	)
+
+include(CMakePackageConfigHelpers)
+
+set(version_config "${CMAKE_CURRENT_BINARY_DIR}/temp/FreeALUTConfigVersion.cmake")
+set(project_config "${CMAKE_CURRENT_BINARY_DIR}/temp/FreeALUTConfig.cmake")
+set(namespace "FreeALUT::")
+
+write_basic_package_version_file("${version_config}"
+									COMPATIBILITY SameMajorVersion
+									VERSION ${PACKAGE_VERSION})
+
+configure_package_config_file("Config.cmake.in"
+								"${project_config}"
+								INSTALL_DESTINATION
+								lib${LIB_SUFFIX}/cmake/freealut)
+
+install(FILES "${project_config}" "${version_config}"
+		DESTINATION lib${LIB_SUFFIX}/cmake/freealut)
+
+install(EXPORT "FreeALUTTargets"
+		NAMESPACE "${namespace}"
+		DESTINATION lib${LIB_SUFFIX}/cmake/freealut)
diff --git a/src/Config.cmake.in b/src/Config.cmake.in
new file mode 100644
index 0000000..2a20f59
--- /dev/null
+++ b/src/Config.cmake.in
@@ -0,0 +1,5 @@
+
+@PACKAGE_INIT@
+
+include("${CMAKE_CURRENT_LIST_DIR}/FreeALUTTargets.cmake")
+check_required_components("FreeALUT")
