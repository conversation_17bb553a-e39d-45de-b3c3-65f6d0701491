{"name": "freealut", "version": "1.1.0", "port-version": 4, "description": ["FreeALUT is a free implementation of OpenAL's ALUT standard.", "ALUT is a set of portable functions which remove the annoying details of getting an audio application started. It is the OpenAL counterpart of what GLUT is for OpenGL. "], "homepage": "https://github.com/vancegroup/freealut", "license": "LGPL-2.0-only", "supports": "!uwp & !xbox", "dependencies": ["openal-soft", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}]}