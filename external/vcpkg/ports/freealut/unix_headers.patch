diff --git a/include/AL/alut.h b/include/AL/alut.h
index 4b05a3c..1084604 100644
--- a/include/AL/alut.h
+++ b/include/AL/alut.h
@@ -1,24 +1,18 @@
 #if !defined(AL_ALUT_H)
 #define AL_ALUT_H
 
-#if defined(_MSC_VER)
 #include <alc.h>
 #include <al.h>
-#elif defined(__APPLE__)
-#include <OpenAL/alc.h>
-#include <OpenAL/al.h>
-#else
-#include <AL/al.h>
-#include <AL/alc.h>
-#endif
 
 #if defined(__cplusplus)
 extern "C" {
 #endif
 
 #if defined(_WIN32) && !defined(_XBOX)
- #if defined (ALUT_BUILD_LIBRARY)
+ #if defined(ALUT_BUILD_LIBRARY)
   #define ALUT_API __declspec(dllexport)
+ #elif defined(ALUT_BUILD_STATIC)
+  #define ALUT_API extern
  #else
   #define ALUT_API __declspec(dllimport)
  #endif
