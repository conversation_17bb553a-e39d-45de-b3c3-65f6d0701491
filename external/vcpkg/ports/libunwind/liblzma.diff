diff --git a/configure.ac b/configure.ac
index 82caaa7..a17b7a1 100644
--- a/configure.ac
+++ b/configure.ac
@@ -443,9 +443,11 @@ AC_MSG_CHECKING([whether to support LZMA-compressed symbol tables])
 AC_ARG_ENABLE(minidebuginfo,
 AS_HELP_STRING([--enable-minidebuginfo], [Enables support for LZMA-compressed symbol tables]),, [enable_minidebuginfo=auto])
 AC_MSG_RESULT([$enable_minidebuginfo])
+PKG_PROG_PKG_CONFIG
 if test x$enable_minidebuginfo != xno; then
-   AC_CHECK_LIB([lzma], [lzma_mf_is_supported],
-   [LIBLZMA=-llzma
+   PKG_CHECK_MODULES([LZMA],[liblzma],
+   [LIBLZMA="$LZMA_LIBS"
+    CFLAGS="$CFLAGS $LZMA_CFLAGS"
     AC_DEFINE([HAVE_LZMA], [1], [Define if you have liblzma])
     enable_minidebuginfo=yes],
    [if test x$enable_minidebuginfo = xyes; then
diff --git a/src/unwind/libunwind.pc.in b/src/unwind/libunwind.pc.in
index 9a65faf..bb351f7 100644
--- a/src/unwind/libunwind.pc.in
+++ b/src/unwind/libunwind.pc.in
@@ -7,5 +7,6 @@ Name: libunwind
 Description: libunwind base library
 Version: @VERSION@
 Libs: -L${libdir} -lunwind
-Libs.private: @LIBLZMA@ @LIBZ@
+Libs.private:           @LIBZ@
+Requires.private: liblzma
 Cflags: -I${includedir}
