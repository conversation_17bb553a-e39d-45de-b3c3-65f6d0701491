diff --git a/CMakeLists.txt b/CMakeLists.txt
index c7193fbb..50c997f8 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -152,8 +152,7 @@ add_subdirectory("common")
 add_subdirectory("${_OSNAME}")
 add_library(libui ${_LIBUI_SOURCES})
 target_include_directories(libui
-	PUBLIC .
-	PRIVATE ${_LIBUI_INCLUEDIRS})
+    PUBLIC $<INSTALL_INTERFACE:include>)
 target_compile_definitions(libui
 	PRIVATE ${_LIBUI_DEFS})
 # cmake produces this for us by default but only for shared libraries
@@ -229,10 +228,30 @@ macro(_add_exec _name)
 	# TODOfor some reason these don't propagate
 	if(NOT WIN32)
 		target_include_directories(${_name}
-			PUBLIC .)
+			PUBLIC $<INSTALL_INTERFACE:include>)
 		target_compile_options(${_name}
 			PUBLIC ${_COMMON_CFLAGS})
 	endif()
 endmacro()
 add_subdirectory("test")
 add_subdirectory("examples")
+
+if(B<PERSON>LD_SHARED_LIBS)
+    install(TARGETS ${PROJECT_NAME}
+        EXPORT ${PROJECT_NAME}-config
+        ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR}
+        LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
+        RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR})
+else()
+    install(TARGETS ${PROJECT_NAME}
+        EXPORT ${PROJECT_NAME}-config
+        DESTINATION ${CMAKE_INSTALL_LIBDIR})
+endif()
+
+install(EXPORT ${PROJECT_NAME}-config
+    FILE unofficial-${PROJECT_NAME}-config.cmake
+    NAMESPACE unofficial::libui::
+    DESTINATION ${CMAKE_INSTALL_LIBDIR}/cmake/${PROJECT_NAME}
+)
+
+install(FILES ui.h DESTINATION include)
