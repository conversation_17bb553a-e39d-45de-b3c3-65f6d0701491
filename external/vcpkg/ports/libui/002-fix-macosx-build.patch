diff --git a/CMakeLists.txt b/CMakeLists.txt
index 50c997f..a23b84d 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -16,7 +16,13 @@ cmake_minimum_required(VERSION 3.1.0 FATAL_ERROR)
 # TODO figure out what other variables must be set with CACHE
 # TODO figure out if FORCE is needed here
 # TODO figure out whether STRING "" is best or if something else is better; also what FORCE does because I forget and later I say it's needed
-set(CMAKE_OSX_DEPLOYMENT_TARGET "10.8" CACHE STRING "" FORCE)
+
+# VCPKG PATCH NOTE: Fix build on MacOS 
+# Due to a bug in CMake (https://gitlab.kitware.com/cmake/cmake/issues/18396) we change CMAKE_OSX_DEPLOYMENT_TARGET to "10.9".
+# See the discussion here: 
+# 	* https://github.com/andlabs/libui/issues/422
+#	* https://github.com/andlabs/libui/issues/457 
+set(CMAKE_OSX_DEPLOYMENT_TARGET "10.9" CACHE STRING "" FORCE)
 
 # we want to disable incremental linking
 # see also:
