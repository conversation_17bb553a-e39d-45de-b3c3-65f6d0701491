qcoro-qt6 provides CMake targets:

    # Generic coroutine types and tools
    find_package(QCoro6Coro CONFIG REQUIRED)
    target_link_libraries(main PRIVATE QCoro6::Coro)

    # Coroutine support for QtCore types
    find_package(QCoro6Core CONFIG REQUIRED)
    target_link_libraries(main PRIVATE QCoro6::Core)

    # Coroutine supports for QtDBus types
    find_package(QCoro6DBus CONFIG REQUIRED)
    target_link_libraries(main PRIVATE QCoro6::DBus)

    # Coroutine support for QtNetwork types
    find_package(QCoro6Network CONFIG REQUIRED)
    target_link_libraries(main PRIVATE QCoro6::Network)

    # Coroutine support for QtQml types
    find_package(QCoro6Qml CONFIG REQUIRED)
    target_link_libraries(main PRIVATE QCoro6::Qml)

    # Coroutine support for QML
    find_package(QCoro6Quick CONFIG REQUIRED)
    target_link_libraries(main PRIVATE QCoro6::Quick)

    # Coroutine support for developing tests with QtTest
    find_package(QCoro6Test CONFIG REQUIRED)
    target_link_libraries(main PRIVATE QCoro6::Test)

    # Coroutine support for QtWebSockets types
    find_package(QCoro6WebSockets CONFIG REQUIRED)
    target_link_libraries(main PRIVATE QCoro6::WebSockets)

You can also use `QCoro` target namespace for transparent
support of both Qt5 and Qt6.
