{"name": "qcoro", "version": "0.11.0", "description": "Coroutine support for Qt", "homepage": "https://github.com/qcoro/qcoro", "documentation": "https://qcoro.dev/", "license": "MIT", "dependencies": [{"name": "qtbase", "default-features": false, "features": ["thread"]}, {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "default-features": ["dbus", "network", "qml", "quick", "test", "websockets"], "features": {"dbus": {"description": "Coroutine support for QtDBus module", "dependencies": [{"name": "qtbase", "default-features": false, "features": ["dbus"]}]}, "network": {"description": "Coroutine support for QtNetwork module", "dependencies": [{"name": "qtbase", "default-features": false, "features": ["network"]}]}, "qml": {"description": "Coroutine support for QtQml module", "dependencies": [{"name": "qtdeclarative", "default-features": false}]}, "quick": {"description": "Coroutine support for QtQuick module", "dependencies": [{"name": "qtdeclarative", "default-features": false}]}, "test": {"description": "Support code for easier testing of coroutines with QtTest.", "dependencies": [{"name": "qtbase", "default-features": false, "features": ["testlib"]}]}, "websockets": {"description": "Coroutine support for QtWebSockets module", "dependencies": [{"name": "qtwebsockets", "default-features": false}]}}}