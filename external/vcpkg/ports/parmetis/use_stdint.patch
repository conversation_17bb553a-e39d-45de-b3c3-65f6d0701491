diff --git a/metis/GKlib/ms_stdint.h b/metis/GKlib/ms_stdint.h
index 7e200dc..0d07a7a 100644
--- a/metis/GKlib/ms_stdint.h
+++ b/metis/GKlib/ms_stdint.h
@@ -42,6 +42,10 @@
 
 #include <limits.h>
 
+#if(_MSC_VER >= 1900)
+	#include <stdint.h>
+#else
+
 // For Visual Studio 6 in C++ mode wrap <wchar.h> include with 'extern "C++" {}'
 // or compiler give many errors like this:
 //   error C2733: second C linkage of overloaded function 'wmemchr' not allowed
@@ -218,5 +222,6 @@ typedef uint64_t  uintmax_t;
 
 #endif // __STDC_CONSTANT_MACROS ]
 
+#endif
 
 #endif // _MSC_STDINT_H_ ]
