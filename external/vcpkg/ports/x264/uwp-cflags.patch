diff --git a/configure b/configure
index f7b14d9..2c92b2a 100644
--- a/configure
+++ b/configure
@@ -821,7 +821,6 @@ if [ $SYS = WINDOWS ]; then
     if cpp_check "winapifamily.h" "" "!WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)" ; then
         [ $compiler = CL ] || die "WinRT requires MSVC"
         define HAVE_WINRT
-        CFLAGS="$CFLAGS -MD"
         LDFLAGS="$LDFLAGS -appcontainer"
         if ! cpp_check "" "" "defined(_WIN32_WINNT) && _WIN32_WINNT >= 0x0603" ; then
             die "_WIN32_WINNT must be defined to at least 0x0603 (Windows 8.1) for WinRT"
