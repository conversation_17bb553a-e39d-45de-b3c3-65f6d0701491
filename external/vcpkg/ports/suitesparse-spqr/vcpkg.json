{"$comment": "Use scripts/update_suitesparse.py to update all SuiteSparse ports", "name": "suitesparse-spqr", "version-semver": "4.3.4", "description": "SPQR: Multithreaded, multifrontal, rank-revealing sparse QR factorization method in SuiteSparse", "homepage": "https://people.engr.tamu.edu/davis/suitesparse.html", "license": "GPL-2.0-or-later", "dependencies": ["lapack", {"name": "suitesparse-cholmod", "features": ["supernodal"]}, "suitesparse-config", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"cuda": {"description": "Enable CUDA support for the current compute architecture of this machine", "dependencies": ["cuda"]}}}