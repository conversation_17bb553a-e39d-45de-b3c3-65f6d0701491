diff --git a/makefile.vc b/makefile.vc
index 53ef75197..d48fb02db 100644
--- a/makefile.vc
+++ b/makefile.vc
@@ -16,7 +16,7 @@ SPATIALITE_OSM_RAW_EXE    = spatialite_osm_raw.exe
 SPATIALITE_OSM_FILTER_EXE = spatialite_osm_filter.exe
 SPATIALITE_GML_EXE        = spatialite_gml.exe
 
-CFLAGS	=	/nologo -IC:\OSGeo4W\include $(OPTFLAGS)
+CFLAGS	=	/nologo $(OPTFLAGS)
 
 default:	all
 
@@ -27,96 +27,63 @@ all: $(SPATIALITE_EXE) $(SHP_DOCTOR_EXE) $(SPATIALITE_TOOL_EXE) \
 	$(SPATIALITE_OSM_FILTER_EXE) $(SHP_SANITIZE_EXE)
 
 $(SPATIALITE_EXE): shell.obj
-	cl shell.obj C:\OSGeo4W\lib\proj_i.lib \
-		C:\OSGeo4W\lib\iconv.lib C:\OSGeo4W\lib\geos_c.lib \
-		C:\OSGeo4W\lib\spatialite_i.lib C:\OSGeo4W\lib\sqlite3_i.lib \
-		/Fe$(SPATIALITE_EXE)
+	cl shell.obj /Fe$(SPATIALITE_EXE) $(LIBS_ALL)
 	if exist $(SPATIALITE_EXE).manifest mt -manifest \
 		$(SPATIALITE_EXE).manifest -outputresource:$(SPATIALITE_EXE);1
 
 $(EXIF_LOADER_EXE):	exif_loader.obj
-	cl exif_loader.obj C:\OSGeo4W\lib\proj_i.lib \
-		C:\OSGeo4W\lib\iconv.lib C:\OSGeo4W\lib\geos_c.lib \
-		C:\OSGeo4W\lib\spatialite_i.lib C:\OSGeo4W\lib\sqlite3_i.lib 
+	cl exif_loader.obj $(LIBS_ALL)
 	if exist $(EXIF_LOADER_EXE).manifest mt -manifest \
 		$(EXIF_LOADER_EXE).manifest -outputresource:$(EXIF_LOADER_EXE);1
 
 $(SHP_DOCTOR_EXE):	shp_doctor.obj
-	cl shp_doctor.obj  C:\OSGeo4W\lib\proj_i.lib \
-		C:\OSGeo4W\lib\iconv.lib C:\OSGeo4W\lib\geos_c.lib \
-		C:\OSGeo4W\lib\spatialite_i.lib C:\OSGeo4W\lib\sqlite3_i.lib 
+	cl shp_doctor.obj $(LIBS_ALL)
 	if exist $(SHP_DOCTOR_EXE).manifest mt -manifest \
 		$(SHP_DOCTOR_EXE).manifest -outputresource:$(SHP_DOCTOR_EXE);1
 
 $(SHP_SANITIZE_EXE):	shp_sanitize.obj
-	cl shp_sanitize.obj  C:\OSGeo4W\lib\proj_i.lib \
-		C:\OSGeo4W\lib\iconv.lib C:\OSGeo4W\lib\geos_c.lib \
-		C:\OSGeo4W\lib\spatialite_i.lib C:\OSGeo4W\lib\sqlite3_i.lib 
+	cl shp_sanitize.obj $(LIBS_ALL)
 	if exist $(SHP_SANITIZE_EXE).manifest mt -manifest \
 		$(SHP_SANITIZE_EXE).manifest -outputresource:$(SHP_SANITIZE_EXE);1
 
 $(SPATIALITE_NETWORK_EXE):	spatialite_network.obj
-	cl spatialite_network.obj C:\OSGeo4W\lib\proj_i.lib \
-		C:\OSGeo4W\lib\iconv.lib \
-		C:\OSGeo4W\lib\spatialite_i.lib C:\OSGeo4W\lib\sqlite3_i.lib 
+	cl spatialite_network.obj $(LIBS_ALL)
 	if exist $(SPATIALITE_NETWORK_EXE).manifest mt -manifest \
 		$(SPATIALITE_TOOL_EXE).manifest \
 		-outputresource:$(SPATIALITE_TOOL_EXE);1
 
 $(SPATIALITE_TOOL_EXE):	spatialite_tool.obj
-	cl spatialite_tool.obj C:\OSGeo4W\lib\proj_i.lib \
-		C:\OSGeo4W\lib\iconv.lib C:\OSGeo4W\lib\geos_c.lib \
-		C:\OSGeo4W\lib\spatialite_i.lib C:\OSGeo4W\lib\sqlite3_i.lib 
+	cl spatialite_tool.obj $(LIBS_ALL)
 	if exist $(SPATIALITE_TOOL_EXE).manifest mt -manifest \
 		$(SPATIALITE_TOOL_EXE).manifest \
 		-outputresource:$(SPATIALITE_TOOL_EXE);1
 
 $(SPATIALITE_OSM_NET_EXE):	spatialite_osm_net.obj
-	cl spatialite_osm_net.obj C:\OSGeo4W\lib\proj_i.lib \
-		C:\OSGeo4W\lib\iconv.lib \
-		C:\OSGeo4W\lib\readosm_i.lib \
-		C:\OSGeo4W\lib\libexpat.lib \
-		C:\OSGeo4W\lib\zlib.lib \
-		C:\OSGeo4W\lib\spatialite_i.lib C:\OSGeo4W\lib\sqlite3_i.lib 
+	cl spatialite_osm_net.obj $(LIBS_ALL)
 	if exist $(SPATIALITE_OSM_EXE).manifest mt -manifest \
 		$(SPATIALITE_OSM_EXE).manifest \
 		-outputresource:$(SPATIALITE_OSM_NET_EXE);1
 
 $(SPATIALITE_OSM_MAP_EXE):	spatialite_osm_map.obj
-	cl spatialite_osm_map.obj C:\OSGeo4W\lib\proj_i.lib \
-		C:\OSGeo4W\lib\iconv.lib \
-		C:\OSGeo4W\lib\readosm_i.lib \
-		C:\OSGeo4W\lib\libexpat.lib \
-		C:\OSGeo4W\lib\zlib.lib \
-		C:\OSGeo4W\lib\spatialite_i.lib C:\OSGeo4W\lib\sqlite3_i.lib 
+	cl spatialite_osm_map.obj $(LIBS_ALL)
 	if exist $(SPATIALITE_OSM_MAP_EXE).manifest mt -manifest \
 		$(SPATIALITE_OSM_MAP_EXE).manifest \
 		-outputresource:$(SPATIALITE_OSM_MAP_EXE);1
 
 $(SPATIALITE_GML_EXE):	spatialite_gml.obj
-	cl spatialite_gml.obj C:\OSGeo4W\lib\proj_i.lib \
-		C:\OSGeo4W\lib\iconv.lib \
-		C:\OSGeo4W\lib\libexpat.lib \
-		C:\OSGeo4W\lib\spatialite_i.lib C:\OSGeo4W\lib\sqlite3_i.lib 
+	cl spatialite_gml.obj $(LIBS_ALL)
 	if exist $(SPATIALITE_GML_EXE).manifest mt -manifest \
 		$(SPATIALITE_GML_EXE).manifest \
 		-outputresource:$(SPATIALITE_GML_EXE);1
 
 $(SPATIALITE_OSM_RAW_EXE):	spatialite_osm_raw.obj
-	cl spatialite_osm_raw.obj C:\OSGeo4W\lib\proj_i.lib \
-		C:\OSGeo4W\lib\iconv.lib \
-		C:\OSGeo4W\lib\readosm_i.lib \
-		C:\OSGeo4W\lib\libexpat.lib \
-		C:\OSGeo4W\lib\zlib.lib \
-		C:\OSGeo4W\lib\spatialite_i.lib C:\OSGeo4W\lib\sqlite3_i.lib 
+	cl spatialite_osm_raw.obj $(LIBS_ALL)
 	if exist $(SPATIALITE_OSM_RAW_EXE).manifest mt -manifest \
 		$(SPATIALITE_OSM_RAW_EXE).manifest \
 		-outputresource:$(SPATIALITE_OSM_RAW_EXE);1
 
 $(SPATIALITE_OSM_FILTER_EXE):	spatialite_osm_filter.obj
-	cl spatialite_osm_filter.obj C:\OSGeo4W\lib\proj_i.lib \
-		C:\OSGeo4W\lib\iconv.lib \
-		C:\OSGeo4W\lib\spatialite_i.lib C:\OSGeo4W\lib\sqlite3_i.lib 
+	cl spatialite_osm_filter.obj $(LIBS_ALL)
 	if exist $(SPATIALITE_OSM_FILTER_EXE).manifest mt -manifest \
 		$(SPATIALITE_OSM_FILTER_EXE).manifest \
 		-outputresource:$(SPATIALITE_OSM_FILTER_EXE);1
diff --git a/nmake.opt b/nmake.opt
index 4f4a9538e..d9efecf7b 100644
--- a/nmake.opt
+++ b/nmake.opt
@@ -2,7 +2,7 @@
 INSTDIR=C:\OSGeo4W
 
 # Uncomment the first for an optimized build, or the second for debug.
-OPTFLAGS=	/nologo /Ox /fp:precise /W3 /MD /D_CRT_SECURE_NO_WARNINGS \
+OPTFLAGS=	/nologo /fp:precise /W3 $(CL_FLAGS) /D_CRT_SECURE_NO_WARNINGS \
 	/D_LARGE_FILE=1 /D_FILE_OFFSET_BITS=64 /D_LARGEFILE_SOURCE=1
 #OPTFLAGS=	/nologo /Zi /MD /Fdspatialite.pdb
 
