diff --git a/libheif/bitstream.cc b/libheif/bitstream.cc
index 9063718..f459fcc 100644
--- a/libheif/bitstream.cc
+++ b/libheif/bitstream.cc
@@ -25,6 +25,12 @@
 #include <cassert>
 #include <bit>
 
+#if ((defined(__GNUC__) && !defined(__clang__) && !defined(__INTEL_COMPILER) && !defined(__PGI)) && __GNUC__ < 9) || (defined(__clang__) && __clang_major__ < 10)
+#include <type_traits>
+#else
+#include <bit>
+#endif
+
 #define MAX_UVLC_LEADING_ZEROS 20
 
 #define AVOID_FUZZER_FALSE_POSITIVE 0
diff --git a/libheif/codecs/uncompressed/decoder_abstract.cc b/libheif/codecs/uncompressed/decoder_abstract.cc
index bbe4692..3c9aacd 100644
--- a/libheif/codecs/uncompressed/decoder_abstract.cc
+++ b/libheif/codecs/uncompressed/decoder_abstract.cc
@@ -35,6 +35,11 @@
 #include "unc_codec.h"
 #include "decoder_abstract.h"
 
+#if ((defined(__GNUC__) && !defined(__clang__) && !defined(__INTEL_COMPILER) && !defined(__PGI)) && __GNUC__ < 9) || (defined(__clang__) && __clang_major__ < 10)
+#include <type_traits>
+#else
+#include <bit>
+#endif
 
 AbstractDecoder::AbstractDecoder(uint32_t width, uint32_t height, const std::shared_ptr<Box_cmpd> cmpd, const std::shared_ptr<Box_uncC> uncC) :
     m_width(width),
