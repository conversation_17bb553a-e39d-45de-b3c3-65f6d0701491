﻿diff --git a/src/runtime/CMakeLists.txt b/src/runtime/CMakeLists.txt
index 127e2a4..f827a79 100644
--- a/src/runtime/CMakeLists.txt
+++ b/src/runtime/CMakeLists.txt
@@ -12,5 +12,8 @@ add_library(flatccrt
 )
 
 if (FLATCC_INSTALL)
-    install(TARGETS flatccrt DESTINATION ${lib_dir})
+    install(TARGETS flatccrt
+    RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
+    LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
+    ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR})
 endif()
