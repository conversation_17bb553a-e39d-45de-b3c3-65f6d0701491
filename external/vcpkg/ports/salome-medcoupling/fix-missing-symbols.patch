diff --git a/src/MEDCoupling/MEDCouplingMemArray.cxx b/src/MEDCoupling/MEDCouplingMemArray.cxx
index 60d040806..8cb6748d2 100644
--- a/src/MEDCoupling/MEDCouplingMemArray.cxx
+++ b/src/MEDCoupling/MEDCouplingMemArray.cxx
@@ -44,6 +44,8 @@ using namespace MEDCoupling;
 
 template class MEDCOUPLING_EXPORT MEDCoupling::MemArray<mcIdType>;
 template class MEDCOUPLING_EXPORT MEDCoupling::MemArray<double>;
+template class MEDCOUPLING_EXPORT MEDCoupling::MemArray<Int32>;
+template class MEDCOUPLING_EXPORT MEDCoupling::MemArray<Int64>;
 template class MEDCOUPLING_EXPORT MEDCoupling::DataArrayTemplate<mcIdType>;
 template class MEDCOUPLING_EXPORT MEDCoupling::Data<PERSON>rrayTemplate<double>;
 template class MEDCOUPLING_EXPORT MEDCoupling::DataArrayTemplateClassic<Int32>;
