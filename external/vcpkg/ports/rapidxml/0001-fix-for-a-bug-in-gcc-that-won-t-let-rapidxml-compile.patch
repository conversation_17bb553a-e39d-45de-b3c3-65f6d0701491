From 2cf47bb8fb1de71bb2d2e059c15a30ffa4ca4cd6 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Sat, 21 Sep 2013 01:55:58 +0200
Subject: [PATCH] fix for a bug in gcc that won't let rapidxml compile on clang

---
 rapidxml_print.hpp | 14 ++++++++++++++
 1 file changed, 14 insertions(+)

diff --git a/rapidxml_print.hpp b/rapidxml_print.hpp
index d03d5f5..7ef305e 100755
--- a/rapidxml_print.hpp
+++ b/rapidxml_print.hpp
@@ -102,6 +102,20 @@ namespace rapidxml
         ///////////////////////////////////////////////////////////////////////////
         // Internal printing operations
     
+        // =====================================
+        // fix for clang for this bug in gcc and others: http://sourceforge.net/p/rapidxml/bugs/16/
+
+        template<class OutIt, class Ch> inline OutIt print_children(OutIt out, const xml_node<Ch> *node, int flags, int indent);
+        template<class OutIt, class Ch> inline OutIt print_element_node(OutIt out, const xml_node<Ch> *node, int flags, int indent);
+        template<class OutIt, class Ch> inline OutIt print_data_node(OutIt out, const xml_node<Ch> *node, int flags, int indent);
+        template<class OutIt, class Ch> inline OutIt print_cdata_node(OutIt out, const xml_node<Ch> *node, int flags, int indent);
+        template<class OutIt, class Ch> inline OutIt print_declaration_node(OutIt out, const xml_node<Ch> *node, int flags, int indent);
+        template<class OutIt, class Ch> inline OutIt print_comment_node(OutIt out, const xml_node<Ch> *node, int flags, int indent);
+        template<class OutIt, class Ch> inline OutIt print_doctype_node(OutIt out, const xml_node<Ch> *node, int flags, int indent);
+        template<class OutIt, class Ch> inline OutIt print_pi_node(OutIt out, const xml_node<Ch> *node, int flags, int indent);
+
+        // =====================================
+
         // Print node
         template<class OutIt, class Ch>
         inline OutIt print_node(OutIt out, const xml_node<Ch> *node, int flags, int indent)
-- 
1.8.4

