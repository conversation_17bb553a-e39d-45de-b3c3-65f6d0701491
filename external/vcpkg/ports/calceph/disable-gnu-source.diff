diff --git a/CMakeLists.txt b/CMakeLists.txt
index a1cb297..9448623 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -147,8 +147,10 @@ IF ("${CMAKE_C_COMPILER_ID}" STREQUAL "Intel")
   MESSAGE(STATUS "Add the option '-fp-model precise' for the Intel compilers")
   string(APPEND CMAKE_C_FLAGS " -fp-model precise ")
 ENDIF ()
+if(NOT ANDROID)
 # Define for the function strod_l
 string(APPEND CMAKE_C_FLAGS " -D_GNU_SOURCE=1 ")
+endif()
 # Define to 1 to make fseeko visible on some hosts (e.g. glibc 2.2).
 string(APPEND CMAKE_C_FLAGS " -D_LARGEFILE_SOURCE=1 ")
 string(APPEND CMAKE_C_FLAGS " -D_LARGE_FILES=1 ")
