{"name": "qtserialbus", "version": "6.8.3", "description": "The Qt Serial Bus API provides classes and functions to access the various industrial serial buses and protocols, such as CAN, ModBus, and others.", "homepage": "https://www.qt.io/", "license": null, "dependencies": [{"name": "qtbase", "default-features": false, "features": ["network"]}, {"name": "qtserialbus", "host": true, "default-features": false}, {"name": "qtserialport", "default-features": false}]}