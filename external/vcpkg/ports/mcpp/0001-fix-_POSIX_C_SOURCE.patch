From d2625f80e08ff811a173ca3dbe76a0e004c26c65 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Tu<PERSON>, 22 Sep 2020 16:21:43 -0700
Subject: [PATCH] fix _POSIX_C_SOURCE

set it to correctly ask for 200112L, for readlink
---
 configed.H | 8 +-------
 1 file changed, 1 insertion(+), 7 deletions(-)

diff --git a/configed.H b/configed.H
index 2e01c15..250f441 100644
--- a/configed.H
+++ b/configed.H
@@ -189,19 +189,13 @@
 #define _POSIX_             1
 #define _POSIX_SOURCE       1
 #ifndef _POSIX_C_SOURCE
-#define _POSIX_C_SOURCE     1
+#define _POSIX_C_SOURCE     200112L
 #define _POSIX_C_SOURCE_defined     1
 #endif
 #include    "limits.h"
 #ifdef _AIX
 #include    "sys/stat.h"
 #endif
-#undef  _POSIX_
-#undef  _POSIX_SOURCE
-#ifdef  _POSIX_C_SOURCE_defined
-#undef  _POSIX_C_SOURCE
-#undef  _POSIX_C_SOURCE_defined
-#endif
 #define CHARBIT             CHAR_BIT
 #define UCHARMAX            UCHAR_MAX
 #define USHRTMAX            USHRT_MAX
-- 
2.24.3 (Apple Git-128)

