diff --git a/CMakeLists.txt b/CMakeLists.txt
index b6cae16..fdc48f3 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -52,3 +52,20 @@ if(WILDCARDS_CLANGFORMAT)
 
   clangformat_setup("${clangformat_srcs}")
 endif()
+
+add_library(wildcards INTERFACE)
+
+install(TARGETS wildcards
+    EXPORT unofficial-wildcards-targets
+    INCLUDES DESTINATION include)
+
+install(EXPORT unofficial-wildcards-targets
+    FILE unofficial-wildcards-config.cmake
+    NAMESPACE unofficial::wildcards::
+    DESTINATION share/unofficial-wildcards)
+
+include(GNUInstallDirs)
+install(
+    DIRECTORY include/
+    DESTINATION "${CMAKE_INSTALL_INCLUDEDIR}/wildcards"
+)
