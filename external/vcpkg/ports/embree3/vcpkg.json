{"name": "embree3", "version": "3.13.5", "port-version": 4, "description": "High Performance Ray Tracing Kernels.", "homepage": "https://github.com/embree/embree", "license": "Apache-2.0", "supports": "!arm | osx", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "default-features": ["filter-function", "geometry-curve", "geometry-grid", "geometry-instance", "geometry-point", "geometry-quad", "geometry-subdivision", "geometry-triangle", "geometry-user", "ray-packets", "tasking-tbb"], "features": {"backface-culling": {"description": "Enables backface culling."}, "compact-polys": {"description": "Enables double indexed poly layout."}, "filter-function": {"description": "Enables filter functions."}, "geometry-curve": {"description": "Enables support for curve geometries."}, "geometry-grid": {"description": "Enables support for grid geometries."}, "geometry-instance": {"description": "Enables support for instances."}, "geometry-point": {"description": "Enables support for point geometries."}, "geometry-quad": {"description": "Enables support for quad geometries."}, "geometry-subdivision": {"description": "Enables support for subdiv geometries."}, "geometry-triangle": {"description": "Enables support for triangle geometries."}, "geometry-user": {"description": "Enables support for user geometries."}, "ray-mask": {"description": "Enables ray mask support."}, "ray-packets": {"description": "Enabled support for ray packets."}, "tasking-tbb": {"description": "Use oneTBB as task system.", "dependencies": ["tbb"]}}}