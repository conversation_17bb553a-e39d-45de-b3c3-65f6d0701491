diff --git a/common/tasking/CMakeLists.txt b/common/tasking/CMakeLists.txt
index 2aeb736..3b4d856 100644
--- a/common/tasking/CMakeLists.txt
+++ b/common/tasking/CMakeLists.txt
@@ -20,7 +20,7 @@ ELSEIF (TASKING_TBB)
     TARGET_LINK_LIBRARIES(tasking PUBLIC TBB::${EMBREE_TBB_COMPONENT})
   else()
     # Try getting TBB via config first
-    find_package(TBB 2021 COMPONENTS ${EMBREE_TBB_COMPONENT} CONFIG ${TBB_FIND_PACKAGE_OPTION})
+    find_package(TBB COMPONENTS ${EMBREE_TBB_COMPONENT} CONFIG ${TBB_FIND_PACKAGE_OPTION})
     if (TBB_FOUND)
       TARGET_LINK_LIBRARIES(tasking PUBLIC TBB::${EMBREE_TBB_COMPONENT})
       message("-- Found TBB: ${TBB_VERSION} at ${TBB_DIR} via TBBConfig.cmake")
