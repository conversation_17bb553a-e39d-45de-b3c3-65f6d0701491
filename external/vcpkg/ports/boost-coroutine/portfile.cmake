# Automatically generated by scripts/boost/generate-ports.ps1

vcpkg_from_github(
    OUT_SOURCE_PATH SOURCE_PATH
    REPO boostorg/coroutine
    REF boost-${VERSION}
    SHA512 ae7e47c706f1fdc809d094c7035fc435f13d0e810b9266dff32d7926824ac0c9a49a60b9c45b349c320b55470a0f90bf1d805d1af439894423b315c75d2bad77
    HEAD_REF master
)

set(FEATURE_OPTIONS "")
boost_configure_and_install(
    SOURCE_PATH "${SOURCE_PATH}"
    OPTIONS ${FEATURE_OPTIONS}
)
