{"$comment": "Automatically generated by scripts/boost/generate-ports.ps1", "name": "boost-coroutine", "version": "1.87.0", "description": "Boost coroutine module", "homepage": "https://www.boost.org/libs/coroutine", "license": "BSL-1.0", "supports": "!(arm & windows) & !uwp & !emscripten", "dependencies": [{"name": "boost-assert", "version>=": "1.87.0"}, {"name": "boost-cmake", "version>=": "1.87.0"}, {"name": "boost-config", "version>=": "1.87.0"}, {"name": "boost-context", "platform": "!uwp & !emscripten", "version>=": "1.87.0"}, {"name": "boost-core", "version>=": "1.87.0"}, {"name": "boost-exception", "version>=": "1.87.0"}, {"name": "boost-headers", "version>=": "1.87.0"}, {"name": "boost-move", "version>=": "1.87.0"}, {"name": "boost-system", "version>=": "1.87.0"}, {"name": "boost-throw-exception", "version>=": "1.87.0"}, {"name": "boost-type-traits", "version>=": "1.87.0"}, {"name": "boost-utility", "version>=": "1.87.0"}]}