{"name": "<PERSON><PERSON>i", "version-semver": "0.14.0", "port-version": 1, "description": "A Simple library for communicating with USB and Bluetooth HID devices on Linux, Mac and Windows.", "homepage": "https://github.com/libusb/hidapi", "license": "BSD-3-<PERSON><PERSON>-<PERSON>", "supports": "!uwp", "dependencies": [{"name": "libusb", "platform": "!(windows | osx)"}, {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"pp-data-dump": {"description": "Build pp_data_dump.exe tool, to store WIN32 HidD Preparsed Data as file", "supports": "windows"}}}