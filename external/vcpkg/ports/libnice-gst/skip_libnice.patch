diff --git a/meson.build b/meson.build
index badce49..5641673 100644
--- a/meson.build
+++ b/meson.build
@@ -283,7 +283,10 @@ libm = cc.find_library('m', required: false)
 
 nice_incs = include_directories('.', 'agent', 'random', 'socket', 'stun')
 
-nice_deps = gio_deps + [gthread_dep, crypto_dep, gupnp_igd_dep] + syslibs
+# libnice dependency using pkg-config
+pkg = import('pkgconfig')
+libnice_dep = dependency('nice', version: '>=0.1.18', method: 'pkg-config', required: true)
+cdata.set('HAVE_LIBNICE', libnice_dep.found(), description: 'Use the libnice library')
 
 ignored_iface_prefix = get_option('ignored-network-interface-prefix')
 if ignored_iface_prefix != []
@@ -296,12 +299,6 @@ endif
 
 gir = find_program('g-ir-scanner', required : get_option('introspection'))
 
-subdir('agent')
-subdir('stun')
-subdir('socket')
-subdir('random')
-subdir('nice')
-
 if gst_dep.found()
   subdir('gst')
 endif
