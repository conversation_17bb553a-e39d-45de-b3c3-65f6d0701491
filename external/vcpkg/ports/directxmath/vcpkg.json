{"name": "directxmath", "version-date": "2025-04-03", "description": "DirectXMath SIMD C++ math library", "homepage": "https://github.com/Microsoft/DirectXMath", "documentation": "https://docs.microsoft.com/windows/win32/dxmath/directxmath-portal", "license": "MIT", "supports": "!arm32", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"dx11": {"description": "C++ Spherical Harmonics Math Library for DirectX 11", "supports": "windows & !xbox"}, "dx12": {"description": "C++ Spherical Harmonics Math Library for DirectX 12", "supports": "(windows & !arm32) | linux", "dependencies": [{"name": "directx-headers", "platform": "windows & !xbox"}]}, "xdsp": {"description": "XDSP Digital Signal Processing (DSP) for DirectXMath"}}}