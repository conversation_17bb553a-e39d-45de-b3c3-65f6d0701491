diff --git a/CMakeLists.txt b/CMakeLists.txt
index c6459b6..63aef98 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -89,14 +89,25 @@ add_library(enet STATIC
     ${SOURCE_FILES}
 )
 
+target_include_directories(enet PUBLIC
+    $<BUILD_INTERFACE:${PROJECT_SOURCE_DIR}/include>
+    $<INSTALL_INTERFACE:include>
+)
+
 if (MINGW)
     target_link_libraries(enet winmm ws2_32)
 endif()
 
 install(TARGETS enet
+    EXPORT unofficial-enet-config
     RUNTIME DESTINATION bin
-    ARCHIVE DESTINATION lib/static
+    ARCHIVE DESTINATION lib
     LIBRARY DESTINATION lib)
 
+INSTALL(EXPORT unofficial-enet-config
+    NAMESPACE unofficial::enet::
+    DESTINATION share/unofficial-enet
+)
+
 install(DIRECTORY include/
         DESTINATION include)
