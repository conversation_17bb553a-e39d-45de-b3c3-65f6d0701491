diff --git a/CMakeLists.txt b/CMakeLists.txt
index 0d9aad9..d68c5f7 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -70,7 +70,7 @@ endif()
 target_include_directories(etcdcpp PRIVATE "generated/")
 target_include_directories(etcdcpp INTERFACE "${CMAKE_INSTALL_PREFIX}/include")
 
-target_link_libraries(etcdcpp PRIVATE gRPC::gpr gRPC::grpc gRPC::grpc++ gRPC::grpc_cronet)
+target_link_libraries(etcdcpp PRIVATE gRPC::gpr gRPC::grpc gRPC::grpc++ gRPC::grpc++_alts)
 target_link_libraries(etcdcpp PRIVATE protobuf::libprotoc protobuf::libprotobuf)
 
 install(TARGETS etcdcpp EXPORT etcdcpp DESTINATION lib)
