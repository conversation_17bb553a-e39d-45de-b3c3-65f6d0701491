diff --git a/CMakeLists.txt b/CMakeLists.txt
index 0d9aad9..7bdde1b 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -77,5 +77,5 @@ install(TARGETS etcdcpp EXPORT etcdcpp DESTINATION lib)
 
 if (${CMAKE_BUILD_TYPE} STREQUAL "Release")
 	install(FILES ${proto_hs} DESTINATION include/libetcd/)
-	install(EXPORT etcdcpp DESTINATION share/etcdcpp/ FILE etcdcppConfig.cmake)
 endif()
+install(EXPORT etcdcpp DESTINATION share/etcdcpp/ FILE etcdcppConfig.cmake)
