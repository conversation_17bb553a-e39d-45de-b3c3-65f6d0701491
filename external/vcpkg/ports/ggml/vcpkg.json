{"name": "ggml", "version-date": "2025-02-12", "description": "Tensor library for machine learning", "homepage": "https://github.com/ggml-org/ggml", "license": "MIT", "supports": "!(windows & arm) & !uwp & !android", "dependencies": ["blas", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"cuda": {"description": "Enable CUDA support", "dependencies": ["cuda"]}, "metal": {"description": "Enable Metal support", "supports": "osx"}, "opencl": {"description": "Enable OpenCL support", "dependencies": ["opencl"]}, "openmp": {"description": "Enable OpenMP support", "supports": "!osx"}, "vulkan": {"description": "Enable Vulkan support", "supports": "!x86", "dependencies": ["vulkan-sdk-components"]}}}