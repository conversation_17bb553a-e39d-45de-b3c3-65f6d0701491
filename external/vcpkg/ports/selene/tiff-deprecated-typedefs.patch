diff --git a/selene/CMakeLists.txt b/selene/CMakeLists.txt
index ab2760c..d1e1e6c 100644
--- a/selene/CMakeLists.txt
+++ b/selene/CMakeLists.txt
@@ -229,6 +229,9 @@ if(TIFF_FOUND)
             )
 
     target_compile_options(selene_img_io_tiff PRIVATE ${SELENE_COMPILER_OPTIONS} ${SELENE_IMG_COMPILER_OPTIONS})
+    if(MSVC)
+        target_compile_options(selene_img_io_tiff PRIVATE /wd4996)
+    endif()
 
     target_compile_definitions(selene_img_io_tiff PRIVATE ${SELENE_COMPILER_DEFINITIONS})
 
