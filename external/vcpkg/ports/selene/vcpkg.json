{"name": "selene", "version": "0.3.1", "port-version": 8, "description": "A C++17 image representation, processing and I/O library.", "homepage": "https://github.com/kmhofmann/selene", "dependencies": ["libjpeg-turbo", "libpng", {"name": "tiff", "default-features": false}, {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}, "zlib"], "features": {"opencv": {"description": "Enable using OpenCV", "dependencies": [{"name": "opencv", "default-features": false, "features": ["fs", "intrinsics", "thread"]}]}}}