include(SelectLibraryConfigurations)
_find_package(<PERSON><PERSON><PERSON> CONFIG)
if(GLEW_FOUND AND TARGET GLEW::GLEW AND NOT DEFINED GLEW_INCLUDE_DIRS)
    get_target_property(GLE<PERSON>_INCLUDE_DIRS GLEW::GLE<PERSON> INTERFACE_INCLUDE_DIRECTORIES)
    set(GLE<PERSON>_INCLUDE_DIR ${GLEW_INCLUDE_DIRS})
    get_target_property(_GLEW_DEFS GLEW::GLEW INTERFACE_COMPILE_DEFINITIONS)
    if("${_GLEW_DEFS}" MATCHES "GLEW_STATIC")
        get_target_property(GLEW_LIBRARY_DEBUG GLEW::GLEW IMPORTED_LOCATION_DEBUG)
        get_target_property(GLEW_LIBRARY_RELEASE GLEW::GLEW IMPORTED_LOCATION_RELEASE)
    else()
        get_target_property(GLEW_LIBRARY_DEBUG GLEW::GLEW IMPORTED_IMPLIB_DEBUG)
        get_target_property(G<PERSON><PERSON>_LIBRARY_RELEASE GLEW::GLE<PERSON> IMPORTED_IMPLIB_RELEASE)
    endif()
    get_target_property(_GLEW_LINK_INTERFACE GLEW::GLEW IMPORTED_LINK_INTERFACE_LIBRARIES_RELEASE) # same for debug and release
    list(APPEND GLEW_LIBRARIES ${_GLEW_LINK_INTERFACE})
    list(APPEND GLEW_LIBRARY ${_GLEW_LINK_INTERFACE})
    select_library_configurations(GLEW)
    if("${_GLEW_DEFS}" MATCHES "GLEW_STATIC")
        set(GLEW_STATIC_LIBRARIES ${GLEW_LIBRARIES})
    else()
        set(GLEW_SHARED_LIBRARIES ${GLEW_LIBRARIES})
    endif()
    unset(_GLEW_DEFS)
    unset(_GLEW_LINK_INTERFACE)
endif()