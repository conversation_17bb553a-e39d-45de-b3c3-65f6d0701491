--- a/build/cmake/CMakeLists.txt	Mon Apr 24 15:54:00 2023
+++ b/build/cmake/CMakeLists.txt	Mon Apr 24 16:06:57 2023
@@ -143,7 +143,9 @@
   # add options from visual studio project
   target_compile_definitions (glew PRIVATE "GLEW_BUILD;VC_EXTRALEAN")
   target_compile_definitions (glew_s PRIVATE "GLEW_STATIC;VC_EXTRALEAN")
-  target_link_libraries (glew LINK_PRIVATE -BASE:0x62AA0000)
+  if(CMAKE_SYSTEM_PROCESSOR MATCHES "^(i?86|x86|x86_32)$")
+	target_link_libraries (glew LINK_PRIVATE -BASE:0x62AA0000)
+  endif()
   # kill security checks which are dependent on stdlib
   target_compile_options (glew PRIVATE -GS-)
   target_compile_options (glew_s PRIVATE -GS-)
