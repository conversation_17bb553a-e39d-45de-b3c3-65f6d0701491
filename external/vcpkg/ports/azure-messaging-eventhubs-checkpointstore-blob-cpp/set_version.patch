diff --git a/sdk/eventhubs/azure-messaging-eventhubs-checkpointstore-blob/CMakeLists.txt b/sdk/eventhubs/azure-messaging-eventhubs-checkpointstore-blob/CMakeLists.txt
index 215269090..9fbc1062a 100644
--- a/sdk/eventhubs/azure-messaging-eventhubs-checkpointstore-blob/CMakeLists.txt
+++ b/sdk/eventhubs/azure-messaging-eventhubs-checkpointstore-blob/CMakeLists.txt
@@ -91,6 +91,7 @@ target_link_libraries(azure-messaging-eventhubs-checkpointstore-blob
 create_code_coverage(eventhubs azure-messaging-eventhubs-checkpointstore-blob azure-messaging-eventhubs-blobcheckpointstore-test "tests?/*;samples?/*")
 
 get_az_version("${CMAKE_CURRENT_SOURCE_DIR}/src/private/package_version.hpp")
+set_target_properties(azure-messaging-eventhubs-checkpointstore-blob PROPERTIES VERSION ${AZ_LIBRARY_VERSION})
 #generate_documentation(azure-messaging-eventhubs-checkpointstore-blob ${AZ_LIBRARY_VERSION})
 generate_documentation(azure-messaging-eventhubs-checkpointstore-blob ${AZ_LIBRARY_VERSION})
 
