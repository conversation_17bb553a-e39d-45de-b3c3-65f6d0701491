{"$note": "Automatically generated by generateFeatures.ps1 from vcpkg.in.json, do not edit manually", "name": "aws-sdk-cpp", "version": "1.11.534", "description": "AWS SDK for C++", "homepage": "https://github.com/aws/aws-sdk-cpp", "license": "Apache-2.0", "dependencies": ["aws-crt-cpp", {"name": "curl", "default-features": false, "features": ["ssl"], "platform": "!uwp & !windows"}, {"name": "openssl", "platform": "!uwp & !windows"}, {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}, "zlib"], "default-features": ["dynamodb", "kinesis", "s3"], "features": {"access-management": {"description": "C++ SDK for the AWS access-management service", "dependencies": [{"name": "aws-sdk-cpp", "default-features": false, "features": ["cognito-identity", "iam"]}]}, "accessanalyzer": {"description": "C++ SDK for the AWS accessanalyzer service"}, "account": {"description": "C++ SDK for the AWS account service"}, "acm": {"description": "C++ SDK for the AWS acm service"}, "acm-pca": {"description": "C++ SDK for the AWS acm-pca service"}, "amp": {"description": "C++ SDK for the AWS amp service"}, "amplify": {"description": "C++ SDK for the AWS amplify service"}, "amplifybackend": {"description": "C++ SDK for the AWS amplifybackend service"}, "amplifyuibuilder": {"description": "C++ SDK for the AWS amplifyuibuilder service"}, "apigateway": {"description": "C++ SDK for the AWS apigateway service"}, "apigatewaymanagementapi": {"description": "C++ SDK for the AWS apigatewaymanagementapi service"}, "apigatewayv2": {"description": "C++ SDK for the AWS apigatewayv2 service"}, "appconfig": {"description": "C++ SDK for the AWS appconfig service"}, "appconfigdata": {"description": "C++ SDK for the AWS appconfigdata service"}, "appfabric": {"description": "C++ SDK for the AWS appfabric service"}, "appflow": {"description": "C++ SDK for the AWS appflow service"}, "appintegrations": {"description": "C++ SDK for the AWS appintegrations service"}, "application-autoscaling": {"description": "C++ SDK for the AWS application-autoscaling service"}, "application-insights": {"description": "C++ SDK for the AWS application-insights service"}, "application-signals": {"description": "C++ SDK for the AWS application-signals service"}, "applicationcostprofiler": {"description": "C++ SDK for the AWS applicationcostprofiler service"}, "appmesh": {"description": "C++ SDK for the AWS appmesh service"}, "apprunner": {"description": "C++ SDK for the AWS apprunner service"}, "appstream": {"description": "C++ SDK for the AWS appstream service"}, "appsync": {"description": "C++ SDK for the AWS appsync service"}, "apptest": {"description": "C++ SDK for the AWS apptest service"}, "arc-zonal-shift": {"description": "C++ SDK for the AWS arc-zonal-shift service"}, "artifact": {"description": "C++ SDK for the AWS artifact service"}, "athena": {"description": "C++ SDK for the AWS athena service"}, "auditmanager": {"description": "C++ SDK for the AWS auditmanager service"}, "autoscaling": {"description": "C++ SDK for the AWS autoscaling service"}, "autoscaling-plans": {"description": "C++ SDK for the AWS autoscaling-plans service"}, "awsmigrationhub": {"description": "C++ SDK for the AWS AWSMigrationHub service"}, "awstransfer": {"description": "C++ SDK for the AWS awstransfer service"}, "b2bi": {"description": "C++ SDK for the AWS b2bi service"}, "backup": {"description": "C++ SDK for the AWS backup service"}, "backup-gateway": {"description": "C++ SDK for the AWS backup-gateway service"}, "backupsearch": {"description": "C++ SDK for the AWS backupsearch service"}, "batch": {"description": "C++ SDK for the AWS batch service"}, "bcm-data-exports": {"description": "C++ SDK for the AWS bcm-data-exports service"}, "bcm-pricing-calculator": {"description": "C++ SDK for the AWS bcm-pricing-calculator service"}, "bedrock": {"description": "C++ SDK for the AWS bedrock service"}, "bedrock-agent": {"description": "C++ SDK for the AWS bedrock-agent service"}, "bedrock-agent-runtime": {"description": "C++ SDK for the AWS bedrock-agent-runtime service"}, "bedrock-data-automation": {"description": "C++ SDK for the AWS bedrock-data-automation service"}, "bedrock-data-automation-runtime": {"description": "C++ SDK for the AWS bedrock-data-automation-runtime service"}, "bedrock-runtime": {"description": "C++ SDK for the AWS bedrock-runtime service"}, "billing": {"description": "C++ SDK for the AWS billing service"}, "billingconductor": {"description": "C++ SDK for the AWS billingconductor service"}, "braket": {"description": "C++ SDK for the AWS braket service"}, "budgets": {"description": "C++ SDK for the AWS budgets service"}, "ce": {"description": "C++ SDK for the AWS ce service"}, "chatbot": {"description": "C++ SDK for the AWS chatbot service"}, "chime": {"description": "C++ SDK for the AWS chime service"}, "chime-sdk-identity": {"description": "C++ SDK for the AWS chime-sdk-identity service"}, "chime-sdk-media-pipelines": {"description": "C++ SDK for the AWS chime-sdk-media-pipelines service"}, "chime-sdk-meetings": {"description": "C++ SDK for the AWS chime-sdk-meetings service"}, "chime-sdk-messaging": {"description": "C++ SDK for the AWS chime-sdk-messaging service"}, "chime-sdk-voice": {"description": "C++ SDK for the AWS chime-sdk-voice service"}, "cleanrooms": {"description": "C++ SDK for the AWS cleanrooms service"}, "cleanroomsml": {"description": "C++ SDK for the AWS cleanroomsml service"}, "cloud9": {"description": "C++ SDK for the AWS cloud9 service"}, "cloudcontrol": {"description": "C++ SDK for the AWS cloudcontrol service"}, "clouddirectory": {"description": "C++ SDK for the AWS clouddirectory service"}, "cloudformation": {"description": "C++ SDK for the AWS cloudformation service"}, "cloudfront": {"description": "C++ SDK for the AWS cloudfront service"}, "cloudfront-keyvaluestore": {"description": "C++ SDK for the AWS cloudfront-keyvaluestore service"}, "cloudhsm": {"description": "C++ SDK for the AWS cloudhsm service"}, "cloudhsmv2": {"description": "C++ SDK for the AWS cloudhsmv2 service"}, "cloudsearch": {"description": "C++ SDK for the AWS cloudsearch service"}, "cloudsearchdomain": {"description": "C++ SDK for the AWS cloudsearchdomain service"}, "cloudtrail": {"description": "C++ SDK for the AWS cloudtrail service"}, "cloudtrail-data": {"description": "C++ SDK for the AWS cloudtrail-data service"}, "codeartifact": {"description": "C++ SDK for the AWS codeartifact service"}, "codebuild": {"description": "C++ SDK for the AWS codebuild service"}, "codecatalyst": {"description": "C++ SDK for the AWS codecatalyst service"}, "codecommit": {"description": "C++ SDK for the AWS codecommit service"}, "codeconnections": {"description": "C++ SDK for the AWS codeconnections service"}, "codedeploy": {"description": "C++ SDK for the AWS codedeploy service"}, "codeguru-reviewer": {"description": "C++ SDK for the AWS codeguru-reviewer service"}, "codeguru-security": {"description": "C++ SDK for the AWS codeguru-security service"}, "codeguruprofiler": {"description": "C++ SDK for the AWS codeguruprofiler service"}, "codepipeline": {"description": "C++ SDK for the AWS codepipeline service"}, "codestar-connections": {"description": "C++ SDK for the AWS codestar-connections service"}, "codestar-notifications": {"description": "C++ SDK for the AWS codestar-notifications service"}, "cognito-identity": {"description": "C++ SDK for the AWS cognito-identity service"}, "cognito-idp": {"description": "C++ SDK for the AWS cognito-idp service"}, "cognito-sync": {"description": "C++ SDK for the AWS cognito-sync service"}, "comprehend": {"description": "C++ SDK for the AWS comprehend service"}, "comprehendmedical": {"description": "C++ SDK for the AWS comprehendmedical service"}, "compute-optimizer": {"description": "C++ SDK for the AWS compute-optimizer service"}, "config": {"description": "C++ SDK for the AWS config service"}, "connect": {"description": "C++ SDK for the AWS connect service"}, "connect-contact-lens": {"description": "C++ SDK for the AWS connect-contact-lens service"}, "connectcampaigns": {"description": "C++ SDK for the AWS connectcampaigns service"}, "connectcampaignsv2": {"description": "C++ SDK for the AWS connectcampaignsv2 service"}, "connectcases": {"description": "C++ SDK for the AWS connectcases service"}, "connectparticipant": {"description": "C++ SDK for the AWS connectparticipant service"}, "controlcatalog": {"description": "C++ SDK for the AWS controlcatalog service"}, "controltower": {"description": "C++ SDK for the AWS controltower service"}, "cost-optimization-hub": {"description": "C++ SDK for the AWS cost-optimization-hub service"}, "cur": {"description": "C++ SDK for the AWS cur service"}, "customer-profiles": {"description": "C++ SDK for the AWS customer-profiles service"}, "databrew": {"description": "C++ SDK for the AWS databrew service"}, "dataexchange": {"description": "C++ SDK for the AWS dataexchange service"}, "datapipeline": {"description": "C++ SDK for the AWS datapipeline service"}, "datasync": {"description": "C++ SDK for the AWS datasync service"}, "datazone": {"description": "C++ SDK for the AWS datazone service"}, "dax": {"description": "C++ SDK for the AWS dax service"}, "deadline": {"description": "C++ SDK for the AWS deadline service"}, "detective": {"description": "C++ SDK for the AWS detective service"}, "devicefarm": {"description": "C++ SDK for the AWS devicefarm service"}, "devops-guru": {"description": "C++ SDK for the AWS devops-guru service"}, "directconnect": {"description": "C++ SDK for the AWS directconnect service"}, "directory-service-data": {"description": "C++ SDK for the AWS directory-service-data service"}, "discovery": {"description": "C++ SDK for the AWS discovery service"}, "dlm": {"description": "C++ SDK for the AWS dlm service"}, "dms": {"description": "C++ SDK for the AWS dms service"}, "docdb": {"description": "C++ SDK for the AWS docdb service"}, "docdb-elastic": {"description": "C++ SDK for the AWS docdb-elastic service"}, "drs": {"description": "C++ SDK for the AWS drs service"}, "ds": {"description": "C++ SDK for the AWS ds service"}, "dsql": {"description": "C++ SDK for the AWS dsql service"}, "dynamodb": {"description": "C++ SDK for the AWS dynamodb service"}, "dynamodbstreams": {"description": "C++ SDK for the AWS dynamodbstreams service"}, "ebs": {"description": "C++ SDK for the AWS ebs service"}, "ec2": {"description": "C++ SDK for the AWS ec2 service"}, "ec2-instance-connect": {"description": "C++ SDK for the AWS ec2-instance-connect service"}, "ecr": {"description": "C++ SDK for the AWS ecr service"}, "ecr-public": {"description": "C++ SDK for the AWS ecr-public service"}, "ecs": {"description": "C++ SDK for the AWS ecs service"}, "eks": {"description": "C++ SDK for the AWS eks service"}, "eks-auth": {"description": "C++ SDK for the AWS eks-auth service"}, "elasticache": {"description": "C++ SDK for the AWS elasticache service"}, "elasticbeanstalk": {"description": "C++ SDK for the AWS elasticbeanstalk service"}, "elasticfilesystem": {"description": "C++ SDK for the AWS elasticfilesystem service"}, "elasticloadbalancing": {"description": "C++ SDK for the AWS elasticloadbalancing service"}, "elasticloadbalancingv2": {"description": "C++ SDK for the AWS elasticloadbalancingv2 service"}, "elasticmapreduce": {"description": "C++ SDK for the AWS elasticmapreduce service"}, "elastictranscoder": {"description": "C++ SDK for the AWS elastictranscoder service"}, "email": {"description": "C++ SDK for the AWS email service"}, "emr-containers": {"description": "C++ SDK for the AWS emr-containers service"}, "emr-serverless": {"description": "C++ SDK for the AWS emr-serverless service"}, "entityresolution": {"description": "C++ SDK for the AWS entityresolution service"}, "es": {"description": "C++ SDK for the AWS es service"}, "eventbridge": {"description": "C++ SDK for the AWS eventbridge service"}, "events": {"description": "C++ SDK for the AWS events service"}, "evidently": {"description": "C++ SDK for the AWS evidently service"}, "finspace": {"description": "C++ SDK for the AWS finspace service"}, "finspace-data": {"description": "C++ SDK for the AWS finspace-data service"}, "firehose": {"description": "C++ SDK for the AWS firehose service"}, "fis": {"description": "C++ SDK for the AWS fis service"}, "fms": {"description": "C++ SDK for the AWS fms service"}, "forecast": {"description": "C++ SDK for the AWS forecast service"}, "forecastquery": {"description": "C++ SDK for the AWS forecastquery service"}, "frauddetector": {"description": "C++ SDK for the AWS frauddetector service"}, "freetier": {"description": "C++ SDK for the AWS freetier service"}, "fsx": {"description": "C++ SDK for the AWS fsx service"}, "gamelift": {"description": "C++ SDK for the AWS gamelift service"}, "gameliftstreams": {"description": "C++ SDK for the AWS gameliftstreams service"}, "geo-maps": {"description": "C++ SDK for the AWS geo-maps service"}, "geo-places": {"description": "C++ SDK for the AWS geo-places service"}, "geo-routes": {"description": "C++ SDK for the AWS geo-routes service"}, "glacier": {"description": "C++ SDK for the AWS glacier service"}, "globalaccelerator": {"description": "C++ SDK for the AWS globalaccelerator service"}, "glue": {"description": "C++ SDK for the AWS glue service"}, "grafana": {"description": "C++ SDK for the AWS grafana service"}, "greengrass": {"description": "C++ SDK for the AWS greengrass service"}, "greengrassv2": {"description": "C++ SDK for the AWS greengrassv2 service"}, "groundstation": {"description": "C++ SDK for the AWS groundstation service"}, "guardduty": {"description": "C++ SDK for the AWS guardduty service"}, "health": {"description": "C++ SDK for the AWS health service"}, "healthlake": {"description": "C++ SDK for the AWS healthlake service"}, "iam": {"description": "C++ SDK for the AWS iam service"}, "identity-management": {"description": "C++ SDK for the AWS identity-management service", "dependencies": [{"name": "aws-sdk-cpp", "default-features": false, "features": ["cognito-identity", "sts"]}]}, "identitystore": {"description": "C++ SDK for the AWS identitystore service"}, "imagebuilder": {"description": "C++ SDK for the AWS imagebuilder service"}, "importexport": {"description": "C++ SDK for the AWS importexport service"}, "inspector": {"description": "C++ SDK for the AWS inspector service"}, "inspector-scan": {"description": "C++ SDK for the AWS inspector-scan service"}, "inspector2": {"description": "C++ SDK for the AWS inspector2 service"}, "internetmonitor": {"description": "C++ SDK for the AWS internetmonitor service"}, "invoicing": {"description": "C++ SDK for the AWS invoicing service"}, "iot": {"description": "C++ SDK for the AWS iot service"}, "iot-data": {"description": "C++ SDK for the AWS iot-data service"}, "iot-jobs-data": {"description": "C++ SDK for the AWS iot-jobs-data service"}, "iot-managed-integrations": {"description": "C++ SDK for the AWS iot-managed-integrations service"}, "iot1click-devices": {"description": "C++ SDK for the AWS iot1click-devices service"}, "iot1click-projects": {"description": "C++ SDK for the AWS iot1click-projects service"}, "iotanalytics": {"description": "C++ SDK for the AWS iotanalytics service"}, "iotdeviceadvisor": {"description": "C++ SDK for the AWS iotdeviceadvisor service"}, "iotevents": {"description": "C++ SDK for the AWS iotevents service"}, "iotevents-data": {"description": "C++ SDK for the AWS iotevents-data service"}, "iotfleethub": {"description": "C++ SDK for the AWS iotfleethub service"}, "iotfleetwise": {"description": "C++ SDK for the AWS iotfleetwise service"}, "iotsecuretunneling": {"description": "C++ SDK for the AWS iotsecuretunneling service"}, "iotsitewise": {"description": "C++ SDK for the AWS iotsitewise service"}, "iotthingsgraph": {"description": "C++ SDK for the AWS iotthingsgraph service"}, "iottwinmaker": {"description": "C++ SDK for the AWS iottwinmaker service"}, "iotwireless": {"description": "C++ SDK for the AWS iotwireless service"}, "ivs": {"description": "C++ SDK for the AWS ivs service"}, "ivs-realtime": {"description": "C++ SDK for the AWS ivs-realtime service"}, "ivschat": {"description": "C++ SDK for the AWS ivschat service"}, "kafka": {"description": "C++ SDK for the AWS kafka service"}, "kafkaconnect": {"description": "C++ SDK for the AWS kafkaconnect service"}, "kendra": {"description": "C++ SDK for the AWS kendra service"}, "kendra-ranking": {"description": "C++ SDK for the AWS kendra-ranking service"}, "keyspaces": {"description": "C++ SDK for the AWS keyspaces service"}, "kinesis": {"description": "C++ SDK for the AWS kinesis service"}, "kinesis-video-archived-media": {"description": "C++ SDK for the AWS kinesis-video-archived-media service"}, "kinesis-video-media": {"description": "C++ SDK for the AWS kinesis-video-media service"}, "kinesis-video-signaling": {"description": "C++ SDK for the AWS kinesis-video-signaling service"}, "kinesis-video-webrtc-storage": {"description": "C++ SDK for the AWS kinesis-video-webrtc-storage service"}, "kinesisanalytics": {"description": "C++ SDK for the AWS kinesisanalytics service"}, "kinesisanalyticsv2": {"description": "C++ SDK for the AWS kinesisanalyticsv2 service"}, "kinesisvideo": {"description": "C++ SDK for the AWS kinesisvideo service"}, "kms": {"description": "C++ SDK for the AWS kms service"}, "lakeformation": {"description": "C++ SDK for the AWS lakeformation service"}, "lambda": {"description": "C++ SDK for the AWS lambda service"}, "launch-wizard": {"description": "C++ SDK for the AWS launch-wizard service"}, "lex": {"description": "C++ SDK for the AWS lex service"}, "lex-models": {"description": "C++ SDK for the AWS lex-models service"}, "lexv2-models": {"description": "C++ SDK for the AWS lexv2-models service"}, "lexv2-runtime": {"description": "C++ SDK for the AWS lexv2-runtime service"}, "license-manager": {"description": "C++ SDK for the AWS license-manager service"}, "license-manager-linux-subscriptions": {"description": "C++ SDK for the AWS license-manager-linux-subscriptions service"}, "license-manager-user-subscriptions": {"description": "C++ SDK for the AWS license-manager-user-subscriptions service"}, "lightsail": {"description": "C++ SDK for the AWS lightsail service"}, "location": {"description": "C++ SDK for the AWS location service"}, "logs": {"description": "C++ SDK for the AWS logs service"}, "lookoutequipment": {"description": "C++ SDK for the AWS lookoutequipment service"}, "lookoutmetrics": {"description": "C++ SDK for the AWS lookoutmetrics service"}, "lookoutvision": {"description": "C++ SDK for the AWS lookoutvision service"}, "m2": {"description": "C++ SDK for the AWS m2 service"}, "machinelearning": {"description": "C++ SDK for the AWS machinelearning service"}, "macie2": {"description": "C++ SDK for the AWS macie2 service"}, "mailmanager": {"description": "C++ SDK for the AWS mailmanager service"}, "managedblockchain": {"description": "C++ SDK for the AWS managedblockchain service"}, "managedblockchain-query": {"description": "C++ SDK for the AWS managedblockchain-query service"}, "marketplace-agreement": {"description": "C++ SDK for the AWS marketplace-agreement service"}, "marketplace-catalog": {"description": "C++ SDK for the AWS marketplace-catalog service"}, "marketplace-deployment": {"description": "C++ SDK for the AWS marketplace-deployment service"}, "marketplace-entitlement": {"description": "C++ SDK for the AWS marketplace-entitlement service"}, "marketplace-reporting": {"description": "C++ SDK for the AWS marketplace-reporting service"}, "marketplacecommerceanalytics": {"description": "C++ SDK for the AWS marketplacecommerceanalytics service"}, "mediaconnect": {"description": "C++ SDK for the AWS mediaconnect service"}, "mediaconvert": {"description": "C++ SDK for the AWS mediaconvert service"}, "medialive": {"description": "C++ SDK for the AWS medialive service"}, "mediapackage": {"description": "C++ SDK for the AWS mediapackage service"}, "mediapackage-vod": {"description": "C++ SDK for the AWS mediapackage-vod service"}, "mediapackagev2": {"description": "C++ SDK for the AWS mediapackagev2 service"}, "mediastore": {"description": "C++ SDK for the AWS mediastore service"}, "mediastore-data": {"description": "C++ SDK for the AWS mediastore-data service"}, "mediatailor": {"description": "C++ SDK for the AWS mediatailor service"}, "medical-imaging": {"description": "C++ SDK for the AWS medical-imaging service"}, "memorydb": {"description": "C++ SDK for the AWS memorydb service"}, "meteringmarketplace": {"description": "C++ SDK for the AWS meteringmarketplace service"}, "mgn": {"description": "C++ SDK for the AWS mgn service"}, "migration-hub-refactor-spaces": {"description": "C++ SDK for the AWS migration-hub-refactor-spaces service"}, "migrationhub-config": {"description": "C++ SDK for the AWS migrationhub-config service"}, "migrationhuborchestrator": {"description": "C++ SDK for the AWS migrationhuborchestrator service"}, "migrationhubstrategy": {"description": "C++ SDK for the AWS migrationhubstrategy service"}, "monitoring": {"description": "C++ SDK for the AWS monitoring service"}, "mq": {"description": "C++ SDK for the AWS mq service"}, "mturk-requester": {"description": "C++ SDK for the AWS mturk-requester service"}, "mwaa": {"description": "C++ SDK for the AWS mwaa service"}, "neptune": {"description": "C++ SDK for the AWS neptune service"}, "neptune-graph": {"description": "C++ SDK for the AWS neptune-graph service"}, "neptunedata": {"description": "C++ SDK for the AWS neptunedata service"}, "network-firewall": {"description": "C++ SDK for the AWS network-firewall service"}, "networkflowmonitor": {"description": "C++ SDK for the AWS networkflowmonitor service"}, "networkmanager": {"description": "C++ SDK for the AWS networkmanager service"}, "networkmonitor": {"description": "C++ SDK for the AWS networkmonitor service"}, "notifications": {"description": "C++ SDK for the AWS notifications service"}, "notificationscontacts": {"description": "C++ SDK for the AWS notificationscontacts service"}, "oam": {"description": "C++ SDK for the AWS oam service"}, "observabilityadmin": {"description": "C++ SDK for the AWS observabilityadmin service"}, "omics": {"description": "C++ SDK for the AWS omics service"}, "opensearch": {"description": "C++ SDK for the AWS opensearch service"}, "opensearchserverless": {"description": "C++ SDK for the AWS opensearchserverless service"}, "opsworks": {"description": "C++ SDK for the AWS opsworks service"}, "opsworkscm": {"description": "C++ SDK for the AWS opsworkscm service"}, "organizations": {"description": "C++ SDK for the AWS organizations service"}, "osis": {"description": "C++ SDK for the AWS osis service"}, "outposts": {"description": "C++ SDK for the AWS outposts service"}, "panorama": {"description": "C++ SDK for the AWS panorama service"}, "partnercentral-selling": {"description": "C++ SDK for the AWS partnercentral-selling service"}, "payment-cryptography": {"description": "C++ SDK for the AWS payment-cryptography service"}, "payment-cryptography-data": {"description": "C++ SDK for the AWS payment-cryptography-data service"}, "pca-connector-ad": {"description": "C++ SDK for the AWS pca-connector-ad service"}, "pca-connector-scep": {"description": "C++ SDK for the AWS pca-connector-scep service"}, "pcs": {"description": "C++ SDK for the AWS pcs service"}, "personalize": {"description": "C++ SDK for the AWS personalize service"}, "personalize-events": {"description": "C++ SDK for the AWS personalize-events service"}, "personalize-runtime": {"description": "C++ SDK for the AWS personalize-runtime service"}, "pi": {"description": "C++ SDK for the AWS pi service"}, "pinpoint": {"description": "C++ SDK for the AWS pinpoint service"}, "pinpoint-email": {"description": "C++ SDK for the AWS pinpoint-email service"}, "pinpoint-sms-voice-v2": {"description": "C++ SDK for the AWS pinpoint-sms-voice-v2 service"}, "pipes": {"description": "C++ SDK for the AWS pipes service"}, "polly": {"description": "C++ SDK for the AWS polly service"}, "pricing": {"description": "C++ SDK for the AWS pricing service"}, "privatenetworks": {"description": "C++ SDK for the AWS privatenetworks service"}, "proton": {"description": "C++ SDK for the AWS proton service"}, "qapps": {"description": "C++ SDK for the AWS qapps service"}, "qbusiness": {"description": "C++ SDK for the AWS qbusiness service"}, "qconnect": {"description": "C++ SDK for the AWS qconnect service"}, "qldb": {"description": "C++ SDK for the AWS qldb service"}, "qldb-session": {"description": "C++ SDK for the AWS qldb-session service"}, "queues": {"description": "C++ SDK for the AWS queues service", "dependencies": [{"name": "aws-sdk-cpp", "default-features": false, "features": ["sqs"]}]}, "quicksight": {"description": "C++ SDK for the AWS quicksight service"}, "ram": {"description": "C++ SDK for the AWS ram service"}, "rbin": {"description": "C++ SDK for the AWS rbin service"}, "rds": {"description": "C++ SDK for the AWS rds service"}, "rds-data": {"description": "C++ SDK for the AWS rds-data service"}, "redshift": {"description": "C++ SDK for the AWS redshift service"}, "redshift-data": {"description": "C++ SDK for the AWS redshift-data service"}, "redshift-serverless": {"description": "C++ SDK for the AWS redshift-serverless service"}, "rekognition": {"description": "C++ SDK for the AWS rekognition service"}, "repostspace": {"description": "C++ SDK for the AWS repostspace service"}, "resiliencehub": {"description": "C++ SDK for the AWS resiliencehub service"}, "resource-explorer-2": {"description": "C++ SDK for the AWS resource-explorer-2 service"}, "resource-groups": {"description": "C++ SDK for the AWS resource-groups service"}, "resourcegroupstaggingapi": {"description": "C++ SDK for the AWS resourcegroupstaggingapi service"}, "robomaker": {"description": "C++ SDK for the AWS robomaker service"}, "rolesanywhere": {"description": "C++ SDK for the AWS rolesanywhere service"}, "route53": {"description": "C++ SDK for the AWS route53 service"}, "route53-recovery-cluster": {"description": "C++ SDK for the AWS route53-recovery-cluster service"}, "route53-recovery-control-config": {"description": "C++ SDK for the AWS route53-recovery-control-config service"}, "route53-recovery-readiness": {"description": "C++ SDK for the AWS route53-recovery-readiness service"}, "route53domains": {"description": "C++ SDK for the AWS route53domains service"}, "route53profiles": {"description": "C++ SDK for the AWS route53profiles service"}, "route53resolver": {"description": "C++ SDK for the AWS route53resolver service"}, "rum": {"description": "C++ SDK for the AWS rum service"}, "s3": {"description": "C++ SDK for the AWS s3 service"}, "s3-crt": {"description": "C++ SDK for the AWS s3-crt service"}, "s3-encryption": {"description": "C++ SDK for the AWS s3-encryption service", "dependencies": [{"name": "aws-sdk-cpp", "default-features": false, "features": ["kms", "s3"]}]}, "s3control": {"description": "C++ SDK for the AWS s3control service"}, "s3outposts": {"description": "C++ SDK for the AWS s3outposts service"}, "s3tables": {"description": "C++ SDK for the AWS s3tables service"}, "sagemaker": {"description": "C++ SDK for the AWS sagemaker service"}, "sagemaker-a2i-runtime": {"description": "C++ SDK for the AWS sagemaker-a2i-runtime service"}, "sagemaker-edge": {"description": "C++ SDK for the AWS sagemaker-edge service"}, "sagemaker-featurestore-runtime": {"description": "C++ SDK for the AWS sagemaker-featurestore-runtime service"}, "sagemaker-geospatial": {"description": "C++ SDK for the AWS sagemaker-geospatial service"}, "sagemaker-metrics": {"description": "C++ SDK for the AWS sagemaker-metrics service"}, "sagemaker-runtime": {"description": "C++ SDK for the AWS sagemaker-runtime service"}, "savingsplans": {"description": "C++ SDK for the AWS savingsplans service"}, "scheduler": {"description": "C++ SDK for the AWS scheduler service"}, "schemas": {"description": "C++ SDK for the AWS schemas service"}, "sdb": {"description": "C++ SDK for the AWS sdb service"}, "secretsmanager": {"description": "C++ SDK for the AWS secretsmanager service"}, "security-ir": {"description": "C++ SDK for the AWS security-ir service"}, "securityhub": {"description": "C++ SDK for the AWS securityhub service"}, "securitylake": {"description": "C++ SDK for the AWS securitylake service"}, "serverlessrepo": {"description": "C++ SDK for the AWS serverlessrepo service"}, "service-quotas": {"description": "C++ SDK for the AWS service-quotas service"}, "servicecatalog": {"description": "C++ SDK for the AWS servicecatalog service"}, "servicecatalog-appregistry": {"description": "C++ SDK for the AWS servicecatalog-appregistry service"}, "servicediscovery": {"description": "C++ SDK for the AWS servicediscovery service"}, "sesv2": {"description": "C++ SDK for the AWS sesv2 service"}, "shield": {"description": "C++ SDK for the AWS shield service"}, "signer": {"description": "C++ SDK for the AWS signer service"}, "simspaceweaver": {"description": "C++ SDK for the AWS simspaceweaver service"}, "sms": {"description": "C++ SDK for the AWS sms service"}, "sms-voice": {"description": "C++ SDK for the AWS sms-voice service"}, "snow-device-management": {"description": "C++ SDK for the AWS snow-device-management service"}, "snowball": {"description": "C++ SDK for the AWS snowball service"}, "sns": {"description": "C++ SDK for the AWS sns service"}, "socialmessaging": {"description": "C++ SDK for the AWS socialmessaging service"}, "sqs": {"description": "C++ SDK for the AWS sqs service"}, "ssm": {"description": "C++ SDK for the AWS ssm service"}, "ssm-contacts": {"description": "C++ SDK for the AWS ssm-contacts service"}, "ssm-incidents": {"description": "C++ SDK for the AWS ssm-incidents service"}, "ssm-quicksetup": {"description": "C++ SDK for the AWS ssm-quicksetup service"}, "ssm-sap": {"description": "C++ SDK for the AWS ssm-sap service"}, "sso": {"description": "C++ SDK for the AWS sso service"}, "sso-admin": {"description": "C++ SDK for the AWS sso-admin service"}, "sso-oidc": {"description": "C++ SDK for the AWS sso-oidc service"}, "states": {"description": "C++ SDK for the AWS states service"}, "storagegateway": {"description": "C++ SDK for the AWS storagegateway service"}, "sts": {"description": "C++ SDK for the AWS sts service"}, "supplychain": {"description": "C++ SDK for the AWS supplychain service"}, "support": {"description": "C++ SDK for the AWS support service"}, "support-app": {"description": "C++ SDK for the AWS support-app service"}, "swf": {"description": "C++ SDK for the AWS swf service"}, "synthetics": {"description": "C++ SDK for the AWS synthetics service"}, "taxsettings": {"description": "C++ SDK for the AWS taxsettings service"}, "text-to-speech": {"description": "C++ SDK for the AWS text-to-speech service", "dependencies": [{"name": "aws-sdk-cpp", "default-features": false, "features": ["polly"]}]}, "textract": {"description": "C++ SDK for the AWS textract service"}, "timestream-influxdb": {"description": "C++ SDK for the AWS timestream-influxdb service"}, "timestream-query": {"description": "C++ SDK for the AWS timestream-query service"}, "timestream-write": {"description": "C++ SDK for the AWS timestream-write service"}, "tnb": {"description": "C++ SDK for the AWS tnb service"}, "transcribe": {"description": "C++ SDK for the AWS transcribe service"}, "transcribestreaming": {"description": "C++ SDK for the AWS transcribestreaming service"}, "transfer": {"description": "C++ SDK for the AWS transfer service", "dependencies": [{"name": "aws-sdk-cpp", "default-features": false, "features": ["s3"]}]}, "translate": {"description": "C++ SDK for the AWS translate service"}, "trustedadvisor": {"description": "C++ SDK for the AWS trustedadvisor service"}, "verifiedpermissions": {"description": "C++ SDK for the AWS verifiedpermissions service"}, "voice-id": {"description": "C++ SDK for the AWS voice-id service"}, "vpc-lattice": {"description": "C++ SDK for the AWS vpc-lattice service"}, "waf": {"description": "C++ SDK for the AWS waf service"}, "waf-regional": {"description": "C++ SDK for the AWS waf-regional service"}, "wafv2": {"description": "C++ SDK for the AWS wafv2 service"}, "wellarchitected": {"description": "C++ SDK for the AWS wellarchitected service"}, "wisdom": {"description": "C++ SDK for the AWS wisdom service"}, "workdocs": {"description": "C++ SDK for the AWS workdocs service"}, "worklink": {"description": "C++ SDK for the AWS worklink service"}, "workmail": {"description": "C++ SDK for the AWS workmail service"}, "workmailmessageflow": {"description": "C++ SDK for the AWS workmailmessageflow service"}, "workspaces": {"description": "C++ SDK for the AWS workspaces service"}, "workspaces-thin-client": {"description": "C++ SDK for the AWS workspaces-thin-client service"}, "workspaces-web": {"description": "C++ SDK for the AWS workspaces-web service"}, "xray": {"description": "C++ SDK for the AWS xray service"}}}