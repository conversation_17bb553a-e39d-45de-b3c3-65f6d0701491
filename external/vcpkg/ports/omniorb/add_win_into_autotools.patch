diff --git a/configure.ac b/configure.ac
index d485463..2ba9c82 100644
--- a/configure.ac
+++ b/configure.ac
@@ -231,6 +231,7 @@ case "$host" in
   *-*-openbsd*)  plat_name="OpenBSD";  plat_def="__openbsd__";  os_v="3";;
   *-*-sco*)      plat_name="OSR5";     plat_def="__osr5__";     os_v="5";;
   *-*-gnu*)      plat_name="GNU";      plat_def="__hurd__" ;    os_v="0";;
+  *-*-mingw*)    plat_name="Win32Platform";      plat_def="__WIN32__" ;    os_v="4";;
 esac
 
 AC_SUBST(PLATFORM_NAME, $plat_name)
diff --git a/mk/beforeauto.mk.in b/mk/beforeauto.mk.in
index f078912..9aef646 100644
--- a/mk/beforeauto.mk.in
+++ b/mk/beforeauto.mk.in
@@ -1311,3 +1314,7 @@ endif
 
 lib_depend := $(patsubst %,$(LibPattern),omnithread)
 OMNITHREAD_LIB_DEPEND := $(GENERATE_LIB_DEPEND)
+
+ifdef Win32Platform
+include $(THIS_IMPORT_TREE)/mk/platforms/vcpkg.mk
+endif
\ No newline at end of file
diff --git a/mk/win32.mk b/mk/win32.mk
index 94c9aad..f788d41 100644
--- a/mk/win32.mk
+++ b/mk/win32.mk
@@ -81,7 +81,6 @@ endif
 
 
 ifndef WINVER
-WINVER = 0x0400
 endif
 
 IMPORT_CPPFLAGS += -D__WIN32__ -D_WIN32_WINNT=$(WINVER)
@@ -179,7 +179,7 @@ define CXXExecutable
  $(RM) $@; \
  $(CXXLINK) -out:$@ $(CXXLINKOPTIONS) -PDB:$@.pdb $(IMPORT_LIBRARY_FLAGS) \
       $(filter-out $(LibPattern),$^) $$libs; \
- $(MANIFESTTOOL) /outputresource:"$@;#1" /manifest $@.manifest; \
+ $(MANIFESTTOOL) -outputresource:"$@;#1" -manifest $@.manifest; \
 )
 endef
 
@@ -187,7 +187,7 @@ define CExecutable
 (set -x; \
  $(RM) $@; \
  $(CLINK) -out:$@ $(CLINKOPTIONS) -PDB:$@.pdb $(IMPORT_LIBRARY_FLAGS) $(filter-out $(LibPattern),$^) $$libs; \
- $(MANIFESTTOOL) /outputresource:"$@;#1" /manifest $@.manifest; \
+ $(MANIFESTTOOL) -outputresource:"$@;#1" -manifest $@.manifest; \
 )
 endef
 
@@ -356,18 +356,18 @@ extralinkoption="$(MSVC_DLL_CXXLINKDEBUGOPTIONS)"; \
 else \
 extralinkoption="$(MSVC_DLL_CXXLINKNODEBUGOPTIONS)"; \
 fi; \
 if [ -z "$$nodeffile" ]; then \
 $(MakeCXXExportSymbolDefinitionFile) \
 defflag="-def:$$defname"; \
 fi; \
 $(MakeResourceDefinitionFile) \
 set -x; \
 $(RM) $@; \
 $(CXXLINK) -out:$$dllname -DLL $$extralinkoption \
 $$defflag -IMPLIB:$@ $(IMPORT_LIBRARY_FLAGS) \
 $^ $$extralibs $$resname; \
-$(MANIFESTTOOL) /outputresource:"$$dllname;#2" /manifest $$dllname.manifest; \
+$(MANIFESTTOOL) -outputresource:"$$dllname;#2" -manifest $$dllname.manifest; \
 $(CP) $@ $$slibname;
 endef
 
 # Export SharedLibrary
diff --git a/acinclude.m4 b/acinclude.m4
index cef87f8bd..cf61556bd 100644
--- a/acinclude.m4
+++ b/acinclude.m4
@@ -433,8 +433,8 @@ AC_SUBST(ENABLE_LONGDOUBLE, $omni_cv_enable_longdouble)
 
 dnl Enable ZIOP
 AC_DEFUN([OMNI_ENABLE_ZIOP],
-[AC_CHECK_LIB(z,compressBound,omni_cv_enable_ziop_zlib=yes,omni_cv_enable_ziop_zlib=no)
-AC_CHECK_LIB(zstd,ZSTD_compress,omni_cv_enable_ziop_zstd=yes,omni_cv_enable_ziop_zstd=no)
+[AC_SEARCH_LIBS(compressBound,[z zlib zlibd],omni_cv_enable_ziop_zlib=yes,omni_cv_enable_ziop_zlib=no)
+AC_SEARCH_LIBS(ZSTD_compress,[zstd zstdd],omni_cv_enable_ziop_zstd=yes,omni_cv_enable_ziop_zstd=no)
 omni_cv_enable_ziop="no"
 if test "$omni_cv_enable_ziop_zlib" = "yes"; then
   omni_cv_enable_ziop="yes"
diff --git a/mk/win32.mk b/mk/win32.mk
index 741835b4b..7a5bff934 100644
--- a/mk/win32.mk
+++ b/mk/win32.mk
@@ -580,8 +580,7 @@ OMNIORB_IDL = $(OMNIORB_IDL_ONLY) $(OMNIORB_IDL_ANY_FLAGS)
 OMNIORB_CPPFLAGS = -D__OMNIORB4__ -I$(CORBA_STUB_DIR) $(OMNITHREAD_CPPFLAGS)
 OMNIORB_IDL_OUTPUTDIR_PATTERN = -C%
 
-msvc_work_around_stub = $(patsubst %,$(LibPattern),msvcstub) \
-                        -NODEFAULTLIB:libcmt.lib -NODEFAULTLIB:libcmtd.lib 
+msvc_work_around_stub = $(patsubst %,$(LibPattern),msvcstub) 
 
 OMNIORB_LIB = $(omniorb_dll_name) \
 		$(omnidynamic_dll_name) \
diff --git a/src/tool/dir.mk b/src/tool/dir.mk
index c6cb127d9..d75ba288a 100644
--- a/src/tool/dir.mk
+++ b/src/tool/dir.mk
@@ -11,7 +11,7 @@ SUBDIRS = omkdepend omniidl
 endif
 
 ifdef Win32Platform
-SUBDIRS = omniidl
+SUBDIRS = omkdepend omniidl
 endif
 
 
diff --git a/mk/win32.mk b/mk/win32.mk
index 6d320a2bd..3983eba5c 100644
--- a/mk/win32.mk
+++ b/mk/win32.mk
@@ -40,12 +40,7 @@ SORT = sort
 endif
 
 
-AR = $(BASE_OMNI_TREE)/$(WRAPPER_FPATH)/libwrapper $(XLN)
-CXX = $(BASE_OMNI_TREE)/$(WRAPPER_FPATH)/clwrapper $(XLN)
-CXXLINK	= $(BASE_OMNI_TREE)/$(WRAPPER_FPATH)/linkwrapper $(XLN)
 CXXMAKEDEPEND = $(BASE_OMNI_TREE)/$(WRAPPER_FPATH)/omkdepend $(MKDEPOPT) -D__cplusplus -D_MSC_VER
-CC = $(BASE_OMNI_TREE)/$(WRAPPER_FPATH)/clwrapper $(XLN)
-CLINK = $(BASE_OMNI_TREE)/$(WRAPPER_FPATH)/linkwrapper $(XLN)
 CMAKEDEPEND = $(BASE_OMNI_TREE)/$(WRAPPER_FPATH)/omkdepend $(MKDEPOPT) -D_MSC_VER
 
 RCTOOL          = rc.exe
@@ -83,7 +83,7 @@ endif
 ifndef WINVER
 endif
 
-IMPORT_CPPFLAGS += -D__WIN32__ -D_WIN32_WINNT=$(WINVER)
+IMPORT_CPPFLAGS += -D__WIN32__
 
 SOCKET_LIB = ws2_32.lib mswsock.lib
 
diff --git a/mk/beforeauto.mk.in b/mk/beforeauto.mk.in
index b7087ea36..d7a826fa9 100644
--- a/mk/beforeauto.mk.in
+++ b/mk/beforeauto.mk.in
@@ -36,6 +36,8 @@ BINDIR  = bin
 
 prefix        	 := @prefix@
 exec_prefix   	 := @exec_prefix@
+includedir   	   := @includedir@
+libdir   	       := @libdir@
 INSTALLTARGET 	 := 1
 INSTALLINCDIR 	 := $(DESTDIR)@includedir@
 INSTALLBINDIR 	 := $(DESTDIR)@bindir@
@@ -362,6 +364,9 @@ endef
 #
 # Patterns for various file types
 #
+
+ifndef Win32Platform
+
 LibPathPattern    = -L%
 LibNoDebugPattern = lib%.a
 LibDebugPattern = lib%.a
@@ -371,6 +376,37 @@ LibSearchPattern = -l%
 BinPattern = %
 TclScriptPattern = %
 
+else
+
+LibPathPattern = -libpath:%
+LibNoDebugPattern = %.lib
+LibDebugPattern = %d.lib
+DLLNoDebugPattern = %_rt.lib
+DLLDebugPattern = %_rtd.lib
+LibNoDebugSearchPattern = %.lib
+LibDebugSearchPattern = %d.lib
+DLLNoDebugSearchPattern = %_rt.lib
+DLLDebugSearchPattern = %_rtd.lib
+
+
+ifndef BuildDebugBinary
+
+LibPattern = $(LibNoDebugPattern)
+DLLPattern = $(DLLNoDebugPattern)
+LibSearchPattern = $(LibNoDebugSearchPattern)
+DLLSearchPattern = $(DLLNoDebugSearchPattern)
+
+else
+
+LibPattern = $(LibDebugPattern)
+DLLPattern = $(DLLDebugPattern)
+LibSearchPattern = $(LibDebugSearchPattern)
+DLLSearchPattern = $(DLLDebugSearchPattern)
+endif
+
+endif
+
+
 
 #
 # Stuff to generate statically-linked libraries.
diff --git a/src/services/mklib/dynstublib/dir.mk b/src/services/mklib/dynstublib/dir.mk
index e128aa87a..f2e1448b3 100644
--- a/src/services/mklib/dynstublib/dir.mk
+++ b/src/services/mklib/dynstublib/dir.mk
@@ -17,6 +17,14 @@ ifdef Win32Platform
   MSVC_STATICLIB_CXXDEBUGFLAGS += -D_WINSTATIC
 endif
 
+ifdef Win32Platform
+# in case of Win32 lossage:
+dynimps := COS$(OMNIORB_MAJOR_VERSION)$(OMNIORB_MINOR_VERSION)$(OMNIORB_MICRO_VERSION)_rt.lib \
+           $(patsubst $(DLLDebugSearchPattern),$(DLLNoDebugSearchPattern), $(OMNIORB_LIB))
+else
+dynimps := $(OMNIORB_COS_LIB) $(OMNIORB_LIB)
+endif
+
 ifndef NoReleaseBuild
 all:: mkstatic mkshared
 
@@ -75,14 +83,6 @@ dynskshared   = shared/$(shell $(SharedLibraryFullName) $(dynsknamespec))
 
 MDFLAGS += -p shared/
 
-ifdef Win32Platform
-# in case of Win32 lossage:
-dynimps := COS$(OMNIORB_MAJOR_VERSION)$(OMNIORB_MINOR_VERSION)$(OMNIORB_MICRO_VERSION)_rt.lib \
-           $(patsubst $(DLLDebugSearchPattern),$(DLLNoDebugSearchPattern), $(OMNIORB_LIB))
-else
-dynimps := $(OMNIORB_COS_LIB) $(OMNIORB_LIB)
-endif
-
 mkshared::
 	@(dir=shared; $(CreateDir))
 
diff --git a/mk/win32.mk b/mk/win32.mk
index 83f1b1687..741835b4b 100644
--- a/mk/win32.mk
+++ b/mk/win32.mk
@@ -43,5 +43,5 @@ endif
-CXXMAKEDEPEND = $(BASE_OMNI_TREE)/$(WRAPPER_FPATH)/omkdepend $(MKDEPOPT) -D__cplusplus -D_MSC_VER
+CXXMAKEDEPEND = $(OMKDEPEND) -D__cplusplus -D_MSC_VER
-CMAKEDEPEND = $(BASE_OMNI_TREE)/$(WRAPPER_FPATH)/omkdepend $(MKDEPOPT) -D_MSC_VER
+CMAKEDEPEND = $(OMKDEPEND) $(MKDEPOPT) -D_MSC_VER
 
 RCTOOL          = rc.exe
 MANIFESTTOOL    = true
@@ -145,7 +145,7 @@ BinPattern = %.exe
 define StaticLinkLibrary
 (set -x; \
  $(RM) $@; \
- $(AR) $@ $^; \
+ $(AR) -OUT:$@ $^; \
 )
 endef
 
@@ -439,12 +444,11 @@ endef
 # The pattern rules below ensured that the right compiler flags are used
 # to compile the source for the library.
 
+ifndef NoReleaseBuild
+
 static/%.o: %.cc
 	$(CXX) -c $(CXXDEBUGFLAGS) $(MSVC_STATICLIB_CXXNODEBUGFLAGS) $(CPPFLAGS) -Fo$@ -Fdstatic\\$(LIB_NAME)$(major).pdb $<
 
-debug/%.o: %.cc
-	$(CXX) -c  $(MSVC_STATICLIB_CXXDEBUGFLAGS) $(CPPFLAGS) -Fo$@ -Fddebug\\$(LIB_NAME)$(major)d.pdb $<
-
 shared/%DynSK.o: %DynSK.cc
 	$(CXX) -c $(CXXDEBUGFLAGS) -DUSE_core_stub_in_nt_dll $(MSVC_DLL_CXXNODEBUGFLAGS) $(CPPFLAGS) -Fo$@ -Fdshared\\ $<
 
@@ -454,6 +458,18 @@ shared/%SK.o: %SK.cc
 shared/%.o: %.cc
 	$(CXX) -c $(CXXDEBUGFLAGS) $(MSVC_DLL_CXXNODEBUGFLAGS) $(CPPFLAGS) -Fo$@ -Fdshared\\ $<
 
+static/%.o: %.c
+	$(CC) -c $(CDEBUGFLAGS) $(MSVC_STATICLIB_CXXNODEBUGFLAGS) $(CPPFLAGS) -Fo$@ -Fdstatic\\$(LIB_NAME)$(major).pdb $<
+
+shared/%.o: %.c
+	$(CC) -c $(CDEBUGFLAGS) $(MSVC_DLL_CXXNODEBUGFLAGS) $(CPPFLAGS) -Fo$@ $<
+
+endif
+
+ifndef NoDebugBuild
+
+debug/%.o: %.cc
+	$(CXX) -c  $(MSVC_STATICLIB_CXXDEBUGFLAGS) $(CPPFLAGS) -Fo$@ -Fddebug\\$(LIB_NAME)$(major)d.pdb $<
 
 shareddebug/%DynSK.o: %DynSK.cc
 	$(CXX) -c  -DUSE_core_stub_in_nt_dll $(MSVC_DLL_CXXDEBUGFLAGS) $(CPPFLAGS) -Fo$@ -Fdshareddebug\\ $<
@@ -464,19 +480,14 @@ shareddebug/%SK.o: %SK.cc
 shareddebug/%.o: %.cc
 	$(CXX) -c  $(MSVC_DLL_CXXDEBUGFLAGS) $(CPPFLAGS) -Fo$@ -Fdshareddebug\\ $<
 
-static/%.o: %.c
-	$(CC) -c $(CDEBUGFLAGS) $(MSVC_STATICLIB_CXXNODEBUGFLAGS) $(CPPFLAGS) -Fo$@ -Fdstatic\\$(LIB_NAME)$(major).pdb $<
-
 debug/%.o: %.c
 	$(CC) -c $(MSVC_STATICLIB_CXXDEBUGFLAGS) $(CPPFLAGS) -Fo$@ -Fdstatic\\$(LIB_NAME)$(major)d.pdb $<
 
-shared/%.o: %.c
-	$(CC) -c $(CDEBUGFLAGS) $(MSVC_DLL_CXXNODEBUGFLAGS) $(CPPFLAGS) -Fo$@ $<
-
-
 shareddebug/%.o: %.c
 	$(CC) -c  $(MSVC_DLL_CXXDEBUGFLAGS) $(CPPFLAGS) -Fo$@ $<
 
+endif
+
 
 #
 # Replacements for implicit rules
