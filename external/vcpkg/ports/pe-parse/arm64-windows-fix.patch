find_package(Filesystem) fails on arm64 Windows.
We can remove it as it is only used for tests which are not built in vcpkg.
diff --git a/CMakeLists.txt b/CMakeLists.txt
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -15,7 +15,6 @@ endif ()
 include(cmake/compilation_flags.cmake)
 # Greater c++17 filesystem compatibility (like with experimental)
 list(APPEND CMAKE_MODULE_PATH "${CMAKE_CURRENT_SOURCE_DIR}/cmake/modules")
-find_package(Filesystem COMPONENTS Experimental Final REQUIRED)
 list(APPEND GLOBAL_CXXFLAGS ${DEFAULT_CXX_FLAGS})
 
 option(BUILD_SHARED_LIBS "Build Shared Libraries" ON)
