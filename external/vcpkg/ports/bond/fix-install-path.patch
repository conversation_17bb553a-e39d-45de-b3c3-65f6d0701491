diff --git a/CMakeLists.txt b/CMakeLists.txt
index 7b22038a..400421c7 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -16,7 +16,7 @@ set (BOND_FIND_RAPIDJSON
     "FALSE"
     CACHE BOOL "If FALSE, then use and install rapidjson from the /thirdparty subdirectory")
 
-enable_testing()
+#enable_testing()
 
 set (BOND_IDL ${CMAKE_CURRENT_SOURCE_DIR}/idl)
 set (BOND_INCLUDE ${CMAKE_CURRENT_SOURCE_DIR}/cpp/inc)
@@ -78,6 +78,6 @@ if (BOND_GBC_PATH)
 
   install (
     FILES ${BOND_GBC_PATH}
-    DESTINATION bin
+    DESTINATION tools
     RENAME ${INSTALLED_GBC_NAME})
 endif()
diff --git a/compiler/CMakeLists.txt b/compiler/CMakeLists.txt
index c8d06afd..6440e9fd 100644
--- a/compiler/CMakeLists.txt
+++ b/compiler/CMakeLists.txt
@@ -107,7 +107,7 @@ set (test_sources
     tests/TestMain.hs
     ${tests})
 
-set (completion_dir etc/bash_completion.d)
+set (completion_dir tools/bond)
 set (completion ${CMAKE_CURRENT_BINARY_DIR}/gbc.comp)
 set (output ${CMAKE_CURRENT_BINARY_DIR}/build/gbc/gbc${CMAKE_EXECUTABLE_SUFFIX})
 set (GBC_EXECUTABLE ${output} PARENT_SCOPE)
@@ -129,7 +129,7 @@ endif()
 
 install (FILES ${output}
     PERMISSIONS OWNER_READ OWNER_EXECUTE GROUP_READ GROUP_EXECUTE WORLD_READ WORLD_EXECUTE
-    DESTINATION bin)
+    DESTINATION ${completion_dir})
 
 install (FILES ${completion}
          RENAME gbc
diff --git a/cpp/CMakeLists.txt b/cpp/CMakeLists.txt
index 15203f66..c15becd5 100644
--- a/cpp/CMakeLists.txt
+++ b/cpp/CMakeLists.txt
@@ -91,7 +91,7 @@ target_include_directories (bond_apply BEFORE PRIVATE
 
 install (TARGETS bond bond_apply
     EXPORT bond
-    ARCHIVE DESTINATION lib/bond
+    ARCHIVE DESTINATION lib
     INCLUDES DESTINATION include)
 
 install (DIRECTORY ${BOND_IDL}/bond/core DESTINATION include/bond)
