diff --git a/src/CMakeLists.txt b/src/CMakeLists.txt
index 18c3821..52f4991 100644
--- a/src/CMakeLists.txt
+++ b/src/CMakeLists.txt
@@ -103,8 +103,12 @@ add_executable(rtl_power rtl_power.c)
 add_executable(rtl_biast rtl_biast.c)
 set(INSTALL_TARGETS rtlsdr rtlsdr_static rtl_sdr rtl_tcp rtl_test rtl_fm rtl_eeprom rtl_adsb rtl_power rtl_biast)
 
+option(BUILD_TOOLS "Build tools" ON)
 foreach(executable IN ITEMS rtl_sdr rtl_tcp rtl_test rtl_fm rtl_eeprom rtl_adsb rtl_power rtl_biast)
   target_link_libraries(${executable} convenience_static ${rtlsdr_target} ${THREADS_PTHREADS_LIBRARY})
+  if(NOT BUILD_TOOLS)
+    set_target_properties(${executable} PROPERTIES EXCLUDE_FROM_ALL 1)
+  endif()
 endforeach()
 if(0)
 target_link_libraries(rtl_sdr rtlsdr convenience_static
@@ -175,6 +179,8 @@ endif()
 # Install built library files & utilities
 ########################################################################
 install(TARGETS ${rtlsdr_target} EXPORT RTLSDR-export)
+if(BUILD_TOOLS)
 install(TARGETS rtl_sdr rtl_tcp rtl_test rtl_fm rtl_eeprom rtl_adsb rtl_power rtl_biast
   DESTINATION ${CMAKE_INSTALL_BINDIR}
   )
+endif()
