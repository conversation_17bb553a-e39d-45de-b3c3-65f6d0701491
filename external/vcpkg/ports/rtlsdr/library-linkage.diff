diff --git a/librtlsdr.pc.in b/librtlsdr.pc.in
index 92bbddf..20a2735 100644
--- a/librtlsdr.pc.in
+++ b/librtlsdr.pc.in
@@ -7,6 +7,7 @@ Name: RTL-SDR Library
 Description: C Utility Library
 Version: @VERSION@
 Cflags: -I${includedir}/
+Cflags.private: -Drtlsdr_STATIC
 Libs: -L${libdir} -lrtlsdr
 Libs.private:            @RTLSDR_PC_LIBS@
 Requires.private: libusb-1.0
diff --git a/src/CMakeLists.txt b/src/CMakeLists.txt
index 3c02e6c..e6585ee 100644
--- a/src/CMakeLists.txt
+++ b/src/CMakeLists.txt
@@ -45,12 +45,11 @@ target_include_directories(rtlsdr_static PUBLIC
   ${LIBUSB_INCLUDE_DIRS}
   ${THREADS_PTHREADS_INCLUDE_DIR}
   )
-set_property(TARGET rtlsdr_static APPEND PROPERTY COMPILE_DEFINITIONS "rtlsdr_STATIC" )
+target_compile_definitions(rtlsdr_static PUBLIC "rtlsdr_STATIC")
 if(NOT WIN32)
 # Force same library filename for static and shared variants of the library
 set_target_properties(rtlsdr_static PROPERTIES OUTPUT_NAME rtlsdr)
 endif()
-generate_export_header(rtlsdr_static)
 
 ########################################################################
 # Set up Windows DLL resource files
@@ -66,6 +65,14 @@ IF(MSVC)
   target_sources(rtlsdr_static PRIVATE ${CMAKE_CURRENT_BINARY_DIR}/rtlsdr.rc)
 ENDIF(MSVC)
 
+if(BUILD_SHARED_LIBS)
+  set(rtlsdr_target rtlsdr)
+  set_target_properties(rtlsdr_static PROPERTIES EXCLUDE_FROM_ALL 1)
+else()
+  set(rtlsdr_target rtlsdr_static)
+  set_target_properties(rtlsdr PROPERTIES EXCLUDE_FROM_ALL 1)
+endif()
+
 ########################################################################
 # Setup libraries used in executables
 ########################################################################
@@ -79,7 +86,7 @@ add_library(libgetopt_static STATIC
     getopt/getopt.c
 )
 target_link_libraries(convenience_static
-    rtlsdr
+    ${rtlsdr_target}
 )
 endif()
 
@@ -96,6 +103,10 @@ add_executable(rtl_power rtl_power.c)
 add_executable(rtl_biast rtl_biast.c)
 set(INSTALL_TARGETS rtlsdr rtlsdr_static rtl_sdr rtl_tcp rtl_test rtl_fm rtl_eeprom rtl_adsb rtl_power rtl_biast)
 
+foreach(executable IN ITEMS rtl_sdr rtl_tcp rtl_test rtl_fm rtl_eeprom rtl_adsb rtl_power rtl_biast)
+  target_link_libraries(${executable} convenience_static ${rtlsdr_target} ${THREADS_PTHREADS_LIBRARY})
+endforeach()
+if(0)
 target_link_libraries(rtl_sdr rtlsdr convenience_static
     ${LIBUSB_LIBRARIES}
     ${CMAKE_THREAD_LIBS_INIT}
@@ -128,6 +139,7 @@ target_link_libraries(rtl_biast rtlsdr convenience_static
     ${LIBUSB_LIBRARIES}
     ${CMAKE_THREAD_LIBS_INIT}
 )
+endif(0)
 if(UNIX)
 target_link_libraries(rtl_fm m)
 target_link_libraries(rtl_adsb m)
@@ -148,6 +160,7 @@ target_link_libraries(rtl_eeprom libgetopt_static)
 target_link_libraries(rtl_adsb libgetopt_static)
 target_link_libraries(rtl_power libgetopt_static)
 target_link_libraries(rtl_biast libgetopt_static)
+if(0)
 set_property(TARGET rtl_sdr APPEND PROPERTY COMPILE_DEFINITIONS "rtlsdr_STATIC" )
 set_property(TARGET rtl_tcp APPEND PROPERTY COMPILE_DEFINITIONS "rtlsdr_STATIC" )
 set_property(TARGET rtl_test APPEND PROPERTY COMPILE_DEFINITIONS "rtlsdr_STATIC" )
@@ -156,16 +169,12 @@ set_property(TARGET rtl_eeprom APPEND PROPERTY COMPILE_DEFINITIONS "rtlsdr_STATI
 set_property(TARGET rtl_adsb APPEND PROPERTY COMPILE_DEFINITIONS "rtlsdr_STATIC" )
 set_property(TARGET rtl_power APPEND PROPERTY COMPILE_DEFINITIONS "rtlsdr_STATIC" )
 set_property(TARGET rtl_biast APPEND PROPERTY COMPILE_DEFINITIONS "rtlsdr_STATIC" )
+endif(0)
 endif()
 ########################################################################
 # Install built library files & utilities
 ########################################################################
-install(TARGETS rtlsdr EXPORT RTLSDR-export
-  LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR} # .so/.dylib file
-  )
-install(TARGETS rtlsdr_static EXPORT RTLSDR-export
-  ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR} # .so/.dylib file
-  )
+install(TARGETS ${rtlsdr_target} EXPORT RTLSDR-export)
 install(TARGETS rtl_sdr rtl_tcp rtl_test rtl_fm rtl_eeprom rtl_adsb rtl_power rtl_biast
   DESTINATION ${CMAKE_INSTALL_BINDIR}
   )
