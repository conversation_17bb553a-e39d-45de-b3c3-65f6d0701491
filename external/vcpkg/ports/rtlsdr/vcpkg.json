{"$schema": "https://raw.githubusercontent.com/microsoft/vcpkg-tool/main/docs/vcpkg.schema.json", "name": "rtlsdr", "version": "2.0.2", "description": "rtl-sdr is a library that turns your Realtek RTL2832 based DVB dongle into a SDR receiver", "homepage": "https://osmocom.org/projects/rtl-sdr", "license": "GPL-2.0-or-later", "supports": "!uwp", "dependencies": ["libusb", {"name": "pthreads", "platform": "windows & !mingw"}, {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"tools": {"description": "Build tools", "supports": "!android"}}}