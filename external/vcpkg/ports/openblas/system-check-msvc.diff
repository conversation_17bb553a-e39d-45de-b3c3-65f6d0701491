diff --git a/cmake/system_check.cmake b/cmake/system_check.cmake
index e94497a..d884727 100644
--- a/cmake/system_check.cmake
+++ b/cmake/system_check.cmake
@@ -36,6 +36,16 @@ if(CMAKE_CL_64 OR MINGW64)
   else()
     set(X86_64 1)
   endif()
+elseif(MSVC)
+  if(CMAKE_SYSTEM_PROCESSOR STREQUAL "AMD64")
+    set(X86_64 1)
+  elseif(CMAKE_SYSTEM_PROCESSOR STREQUAL "ARM")
+    set(ARM 1)
+  elseif(CMAKE_SYSTEM_PROCESSOR STREQUAL "ARM64")
+    set(ARM64 1)
+  else()
+    set(X86 1)
+  endif()
 elseif(MINGW OR (MSVC AND NOT CMAKE_CROSSCOMPILING))
   set(X86 1)
 elseif(CMAKE_SYSTEM_PROCESSOR MATCHES "ppc.*|power.*|Power.*" OR (CMAKE_SYSTEM_NAME MATCHES "Darwin" AND CMAKE_OSX_ARCHITECTURES MATCHES "ppc.*"))
