diff --git a/cmake/os.cmake b/cmake/os.cmake
index 2effbe0..538ede2 100644
--- a/cmake/os.cmake
+++ b/cmake/os.cmake
@@ -18,7 +18,7 @@ if (${CMAKE_SYSTEM_NAME} STREQUAL "AIX")
 endif ()
 
 # TODO: this is probably meant for mingw, not other windows compilers
-if (${CMAKE_SYSTEM_NAME} STREQUAL "Windows")
+if (WIN32)
 
   set(NEED_PIC 0)
   set(NO_EXPRECISION 1)
@@ -69,7 +69,7 @@ if (CYGWIN)
   set(NO_EXPRECISION 1)
 endif ()
 
-if (NOT ${CMAKE_SYSTEM_NAME} STREQUAL "Windows" AND NOT ${CMAKE_SYSTEM_NAME} STREQUAL "Interix" AND NOT ${CMAKE_SYSTEM_NAME} STREQUAL "Android")
+if (NOT WIN32 AND NOT ${CMAKE_SYSTEM_NAME} STREQUAL "Interix" AND NOT ${CMAKE_SYSTEM_NAME} STREQUAL "Android")
   if (USE_THREAD)
     set(EXTRALIB "${EXTRALIB} -lpthread")
   endif ()
diff --git a/cmake/system.cmake b/cmake/system.cmake
index 683c318..eae7436 100644
--- a/cmake/system.cmake
+++ b/cmake/system.cmake
@@ -507,7 +507,7 @@ if (USE_SIMPLE_THREADED_LEVEL3)
   set(CCOMMON_OPT "${CCOMMON_OPT} -DUSE_SIMPLE_THREADED_LEVEL3")
 endif ()
 
-if (NOT ${CMAKE_SYSTEM_NAME} STREQUAL "Windows")
+if (NOT WIN32)
 if (DEFINED MAX_STACK_ALLOC)
 if (NOT ${MAX_STACK_ALLOC} EQUAL 0)
 set(CCOMMON_OPT "${CCOMMON_OPT} -DMAX_STACK_ALLOC=${MAX_STACK_ALLOC}")
@@ -516,7 +516,7 @@ else ()
 set(CCOMMON_OPT "${CCOMMON_OPT} -DMAX_STACK_ALLOC=2048")
 endif ()
 endif ()
-if (NOT ${CMAKE_SYSTEM_NAME} STREQUAL "Windows")
+if (NOT WIN32)
 if (DEFINED BLAS3_MEM_ALLOC_THRESHOLD)
 if (NOT ${BLAS3_MEM_ALLOC_THRESHOLD} EQUAL 32)
 set(CCOMMON_OPT "${CCOMMON_OPT} -DBLAS3_MEM_ALLOC_THRESHOLD=${BLAS3_MEM_ALLOC_THRESHOLD}")
@@ -633,7 +633,7 @@ endif()
 set(LAPACK_FPFLAGS "${LAPACK_FPFLAGS} ${FPFLAGS}")
 
 #Disable -fopenmp for LAPACK Fortran codes on Windows.
-if (${CMAKE_SYSTEM_NAME} STREQUAL "Windows")
+if (WIN32)
   set(FILTER_FLAGS "-fopenmp;-mp;-openmp;-xopenmp=parallel")
   foreach (FILTER_FLAG ${FILTER_FLAGS})
     string(REPLACE ${FILTER_FLAG} "" LAPACK_FFLAGS ${LAPACK_FFLAGS})
@@ -665,11 +665,11 @@ if (INTERFACE64)
   set(LAPACK_CFLAGS "${LAPACK_CFLAGS} -DLAPACK_ILP64")
 endif ()
 
-if (${CMAKE_SYSTEM_NAME} STREQUAL "Windows")
+if (WIN32)
   set(LAPACK_CFLAGS "${LAPACK_CFLAGS} -DOPENBLAS_OS_WINDOWS")
 endif ()
 
-if (${CMAKE_C_COMPILER} STREQUAL "LSB" OR ${CMAKE_SYSTEM_NAME} STREQUAL "Windows")
+if (${CMAKE_C_COMPILER} STREQUAL "LSB" OR WIN32)
   set(LAPACK_CFLAGS "${LAPACK_CFLAGS} -DLAPACK_COMPLEX_STRUCTURE")
 endif ()
 if (${CMAKE_C_COMPILER_ID} MATCHES "IntelLLVM" AND ${CMAKE_SYSTEM_NAME} STREQUAL "Windows")
