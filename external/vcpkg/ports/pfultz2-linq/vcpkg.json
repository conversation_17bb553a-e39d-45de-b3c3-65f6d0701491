{"name": "pfultz2-linq", "version-date": "2019-05-14", "port-version": 3, "description": "Linq for list comprehension in C++", "dependencies": ["boost-fusion", "boost-iterator", "boost-mpl", "boost-optional", "boost-preprocessor", "boost-range", "boost-static-assert", "boost-type-traits", "boost-unordered", "boost-utility", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}]}