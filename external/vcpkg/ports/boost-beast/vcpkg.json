{"$comment": "Automatically generated by scripts/boost/generate-ports.ps1", "name": "boost-beast", "version": "1.87.0", "description": "Boost beast module", "homepage": "https://www.boost.org/libs/beast", "license": "BSL-1.0", "supports": "!emscripten", "dependencies": [{"name": "boost-asio", "version>=": "1.87.0"}, {"name": "boost-assert", "version>=": "1.87.0"}, {"name": "boost-bind", "version>=": "1.87.0"}, {"name": "boost-cmake", "version>=": "1.87.0"}, {"name": "boost-config", "version>=": "1.87.0"}, {"name": "boost-container", "version>=": "1.87.0"}, {"name": "boost-container-hash", "version>=": "1.87.0"}, {"name": "boost-core", "version>=": "1.87.0"}, {"name": "boost-endian", "version>=": "1.87.0"}, {"name": "boost-headers", "version>=": "1.87.0"}, {"name": "boost-intrusive", "version>=": "1.87.0"}, {"name": "boost-logic", "version>=": "1.87.0"}, {"name": "boost-mp11", "version>=": "1.87.0"}, {"name": "boost-optional", "version>=": "1.87.0"}, {"name": "boost-preprocessor", "version>=": "1.87.0"}, {"name": "boost-smart-ptr", "version>=": "1.87.0"}, {"name": "boost-static-assert", "version>=": "1.87.0"}, {"name": "boost-static-string", "version>=": "1.87.0"}, {"name": "boost-system", "version>=": "1.87.0"}, {"name": "boost-throw-exception", "version>=": "1.87.0"}, {"name": "boost-type-index", "version>=": "1.87.0"}, {"name": "boost-type-traits", "version>=": "1.87.0"}, {"name": "boost-winapi", "version>=": "1.87.0"}]}