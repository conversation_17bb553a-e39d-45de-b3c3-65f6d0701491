{"name": "configcat", "version": "4.0.4", "description": "ConfigCat SDK for C++ provides easy integration for your application to ConfigCat. ConfigCat is a feature flag and configuration management service that lets you separate feature releases from deployments. Alternative to LaunchDarkly.", "homepage": "https://configcat.com/", "license": "MIT", "dependencies": ["<PERSON><PERSON><PERSON>-<PERSON><PERSON>", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}, "z4kn4fein-semver"], "default-features": ["network", "sha"], "features": {"network": {"description": "Use curl for network", "dependencies": [{"name": "curl", "default-features": false, "features": ["ssl"]}, {"name": "openssl", "platform": "linux"}]}, "sha": {"description": "Use hash-library for SHA calculations", "dependencies": ["hash-library"]}}}