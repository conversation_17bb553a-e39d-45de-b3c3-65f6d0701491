diff --git a/CMakeLists.txt b/CMakeLists.txt
index dc65543..5bccbce 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -158,12 +158,9 @@ if (WITH_TESTS)
 endif()
 add_subdirectory(dimbuilder)
 add_subdirectory(vendor/arbiter)
-add_subdirectory(vendor/schema-validator)
 add_subdirectory(vendor/kazhdan)
 add_subdirectory(vendor/lazperf)
-add_subdirectory(vendor/utfcpp)
 add_subdirectory(vendor/lepcc)
-add_subdirectory(vendor/h3)
 add_subdirectory(tools)
 add_subdirectory(apps)
 
@@ -220,12 +217,13 @@ add_library(PDAL::PDAL ALIAS ${PDAL_LIB_NAME})
 
 
 
+find_package(Eigen3 CONFIG REQUIRED)
+target_link_libraries(${PDAL_LIB_NAME} PRIVATE Eigen3::Eigen)
 target_include_directories(${PDAL_LIB_NAME}
     SYSTEM BEFORE PRIVATE
-        ${PDAL_VENDOR_EIGEN_DIR}
         ${PDAL_VENDOR_DIR}/kazhdan
-        ${PDAL_VENDOR_DIR}/h3/include
 )
+target_link_libraries(${PDAL_LIB_NAME} PRIVATE h3::h3)
 target_include_directories(${PDAL_LIB_NAME}
     PRIVATE
         ${ROOT_DIR}
@@ -263,6 +261,8 @@ target_link_libraries(${PDAL_LIB_NAME}
         ${PDAL_LIBDIR}
         ${WINSOCK_LIBRARY}
 )
+find_package(nanoflann CONFIG REQUIRED)
+target_link_libraries(${PDAL_LIB_NAME} PRIVATE nanoflann::nanoflann)
 
 #
 # Allow downstream cmake projects to find PDAL header files without
@@ -274,7 +274,7 @@ target_include_directories(${PDAL_LIB_NAME}
 
 target_compile_definitions(${PDAL_LIB_NAME}
     PRIVATE
-    H3_PREFIX=PDALH3)
+)
 
 if(WIN32)
     target_compile_definitions(${PDAL_LIB_NAME}
diff --git a/cmake/gdal.cmake b/cmake/gdal.cmake
index 5f68579..a407c60 100644
--- a/cmake/gdal.cmake
+++ b/cmake/gdal.cmake
@@ -12,7 +12,8 @@ function(gdal_find_version _version)
     set(${_version} ${MAJOR}.${MINOR}.${REV} PARENT_SCOPE)
 endfunction(gdal_find_version)
 
-find_package(GDAL 3.4 REQUIRED)
+find_package(GDAL CONFIG REQUIRED)
+set(GDAL_LIBRARY GDAL::GDAL)
 set_package_properties(GDAL PROPERTIES TYPE REQUIRED
     PURPOSE "Provides general purpose raster, vector, and reference system support")
 if (GDAL_FOUND)
diff --git a/cmake/geotiff.cmake b/cmake/geotiff.cmake
index 1d1e8af..f713c05 100644
--- a/cmake/geotiff.cmake
+++ b/cmake/geotiff.cmake
@@ -2,7 +2,8 @@
 # GeoTIFF support
 #
 
-find_package(GeoTIFF REQUIRED 1.3.0)
+find_package(GeoTIFF CONFIG REQUIRED)
+set(GEOTIFF_LIBRARY ${GEOTIFF_LIBRARIES})
 set_package_properties(GeoTIFF PROPERTIES TYPE REQUIRED)
 if (GEOTIFF_FOUND)
     include_directories("${GEOTIFF_INCLUDE_DIR}")
diff --git a/cmake/h3.cmake b/cmake/h3.cmake
index 398ad6d..fb3c9ad 100644
--- a/cmake/h3.cmake
+++ b/cmake/h3.cmake
@@ -2,5 +2,6 @@
 # H3 configuration.
 #
 
-set(PDAL_H3_LIB_NAME pdal_h3)
+find_package(h3 CONFIG REQUIRED)
+set(PDAL_H3_LIB_NAME h3::h3)
 
diff --git a/cmake/schema-validator.cmake b/cmake/schema-validator.cmake
index 1f9f996..aeb598c 100644
--- a/cmake/schema-validator.cmake
+++ b/cmake/schema-validator.cmake
@@ -1,5 +1,5 @@
 #
 # N Lohmann JSON schema validator handler
 #
-include(${PDAL_CMAKE_DIR}/nlohmann.cmake)
-set(JSON_SCHEMA_LIB_NAME pdal_json_schema)
+find_package(nlohmann_json_schema_validator CONFIG REQUIRED)
+set(JSON_SCHEMA_LIB_NAME nlohmann_json_schema_validator::validator)
diff --git a/cmake/utfcpp.cmake b/cmake/utfcpp.cmake
index 6543ff6..dc6fac8 100644
--- a/cmake/utfcpp.cmake
+++ b/cmake/utfcpp.cmake
@@ -1,6 +1,7 @@
 #
 # UTF CPP
 #
-set(UTFCPP_INCLUDE_DIR ${PDAL_VENDOR_DIR}/utfcpp/source)
+find_package(utf8cpp CONFIG REQUIRED)
+set(UTFCPP_INCLUDE_DIR "")
 set(UTFCPP_LIB_NAME utf8::cpp)
 
diff --git a/cmake/zstd.cmake b/cmake/zstd.cmake
index dfe3dc0..9f699e6 100644
--- a/cmake/zstd.cmake
+++ b/cmake/zstd.cmake
@@ -4,7 +4,10 @@
 option(WITH_ZSTD
     "Build support for compression/decompression with Zstd." TRUE)
 if (WITH_ZSTD)
-    find_package(ZSTD QUIET)
+    find_package(ZSTD NAMES zstd REQUIRED)
+    set(ZSTD_INCLUDE_DIRS "")
+    set(ZSTD_LIBRARIES zstd::libzstd)
+    set(ZSTD_STATIC_LIB zstd::libzstd)
     set_package_properties(ZSTD PROPERTIES TYPE
         PURPOSE "General compression support")
     if (ZSTD_FOUND)
diff --git a/filters/H3Filter.cpp b/filters/H3Filter.cpp
index 6eb10a2..153c3ee 100644
--- a/filters/H3Filter.cpp
+++ b/filters/H3Filter.cpp
@@ -45,7 +45,9 @@
 #include <string>
 #include <vector>
 
-#include <h3api.h>
+#include <h3/h3api.h>
+#define PDALH3degsToRads    degsToRads
+#define PDALH3latLngToCell  latLngToCell
 
 namespace pdal
 {
diff --git a/pdal/JsonFwd.hpp b/pdal/JsonFwd.hpp
index f764961..f64dcc6 100644
--- a/pdal/JsonFwd.hpp
+++ b/pdal/JsonFwd.hpp
@@ -1,3 +1,4 @@
+#include <nlohmann/json_fwd.hpp>
 #ifndef INCLUDE_NLOHMANN_JSON_FWD_HPP_
 #define INCLUDE_NLOHMANN_JSON_FWD_HPP_
 
diff --git a/pdal/private/KDImpl.hpp b/pdal/private/KDImpl.hpp
index 473ffba..a69431f 100644
--- a/pdal/private/KDImpl.hpp
+++ b/pdal/private/KDImpl.hpp
@@ -34,7 +34,8 @@
 
 #pragma once
 
-#include <nanoflann/nanoflann.hpp>
+#include <nanoflann.hpp>
+#define SearchParams SearchParameters
 
 namespace pdal
 {
@@ -116,7 +117,7 @@ public:
     PointIdList radius(double const& x, double const& y, double const& r) const
     {
         PointIdList output;
-        std::vector<std::pair<std::size_t, double>> ret_matches;
+        std::vector<nanoflann::ResultItem<std::size_t, double>> ret_matches;
         nanoflann::SearchParams params;
         params.sorted = true;
 
@@ -247,7 +248,7 @@ public:
     PointIdList radius(double x, double y, double z, double r) const
     {
         PointIdList output;
-        std::vector<std::pair<std::size_t, double>> ret_matches;
+        std::vector<nanoflann::ResultItem<std::size_t, double>> ret_matches;
         nanoflann::SearchParams params;
         params.sorted = true;
 
@@ -330,7 +331,7 @@ public:
     PointIdList radius(PointId idx, double r) const
     {
         PointIdList output;
-        std::vector<std::pair<std::size_t, double>> ret_matches;
+        std::vector<nanoflann::ResultItem<std::size_t, double>> ret_matches;
         nanoflann::SearchParams params;
         params.sorted = true;
 
diff --git a/tools/lasdump/CMakeLists.txt b/tools/lasdump/CMakeLists.txt
index b15d595..a452d14 100644
--- a/tools/lasdump/CMakeLists.txt
+++ b/tools/lasdump/CMakeLists.txt
@@ -10,6 +10,7 @@ add_executable(lasdump
 )
 
 target_link_libraries(lasdump PRIVATE
+    ${UTFCPP_LIB_NAME}
     ${PDAL_LAZPERF_LIB_NAME}
 )
 target_include_directories(lasdump PRIVATE
