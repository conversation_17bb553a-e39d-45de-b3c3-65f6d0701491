{"name": "pdal", "version": "2.8.4", "description": "PDAL - Point Data Abstraction Library is a library for manipulating point cloud data.", "homepage": "https://pdal.io/", "license": null, "dependencies": [{"name": "curl", "default-features": false}, "eigen3", {"name": "gdal", "default-features": false}, "h3", "json-schema-validator", "libgeotiff", {"name": "libxml2", "features": ["http"]}, "nanoflann", "<PERSON><PERSON><PERSON>-<PERSON><PERSON>", {"name": "pdal-dimbuilder", "host": true}, "utfcpp", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}, "zlib"], "default-features": ["supported-plugins"], "features": {"draco": {"description": "Build the Draco plugin", "dependencies": ["draco"]}, "e57": {"description": "Build the E57 plugin", "dependencies": ["xerces-c"]}, "hdf": {"description": "Build the HDF plugin", "dependencies": [{"name": "hdf5", "default-features": false}]}, "liblzma": {"description": "Support for compression/decompression with LZMA", "dependencies": ["liblzma"]}, "pgpointcloud": {"description": "Build the PgPointCloud plugin", "dependencies": [{"name": "libpq", "default-features": false}]}, "supported-plugins": {"description": "Default set of plugins, dependent on platform", "dependencies": [{"name": "pdal", "default-features": false, "features": ["draco", "e57"]}, {"name": "pdal", "default-features": false, "features": ["hdf", "pgpointcloud"], "platform": "!uwp"}]}, "zstd": {"description": "Support for ZSTD compression/decompression", "dependencies": ["zstd"]}}}