{"name": "libeventheader-decode", "version": "1.4.0", "description": "C++ classes for decoding EventHeader-encoded Linux Tracepoints", "homepage": "https://github.com/microsoft/LinuxTracepoints/", "license": "MIT", "supports": "linux | windows", "dependencies": [{"name": "libeventheader-tracepoint", "version>=": "1.4.0"}, {"name": "libtracepoint-decode", "version>=": "1.4.0"}, {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"tools": {"description": "Build user tools: perf-decode"}}}