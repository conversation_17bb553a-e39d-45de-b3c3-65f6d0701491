diff --git a/Makefile.am b/Makefile.am
index c0aae53..769aef5 100644
--- a/Makefile.am
+++ b/Makefile.am
@@ -1,6 +1,6 @@
 ACLOCAL_AMFLAGS = -I m4
 
-SUBDIRS= po src man tests
+SUBDIRS= po src
 
 pkgconfdir = $(libdir)/pkgconfig
 pkgconf_DATA = hunspell.pc
diff --git a/configure.ac b/configure.ac
index 358cb1d..152adef 100644
--- a/configure.ac
+++ b/configure.ac
@@ -38,6 +38,9 @@ dnl internationalization macros
 AM_GNU_GETTEXT_VERSION(0.18)
 AM_GNU_GETTEXT([external])
 
+AC_ARG_ENABLE(tools,[])
+AM_CONDITIONAL(DISABLE_TOOLS, test x$enable_tools != xyes)
+
 AC_ARG_WITH(warnings,[  --with-warnings         compile with warning messages],[
 	AC_DEFINE(HUNSPELL_WARNING_ON,1,"Define if you need warning messages")
 ])
diff --git a/src/Makefile.am b/src/Makefile.am
index 45b7703..27700af 100644
--- a/src/Makefile.am
+++ b/src/Makefile.am
@@ -1 +1,5 @@
+if DISABLE_TOOLS
+SUBDIRS=hunspell
+else
 SUBDIRS=hunspell parsers tools
+endif
