diff --git a/CMakeLists.txt b/CMakeLists.txt
index 5df20ca..23a8114 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -2,6 +2,8 @@
 cmake_minimum_required(VERSION 2.8.7)
 project(jsonnet C CXX)
 
+add_definitions(-D_CRT_SECURE_NO_WARNINGS)
+
 include(ExternalProject)
 include(GNUInstallDirs)
 
@@ -33,6 +35,7 @@ set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${GLOBAL_OUTPUT_PATH})
 set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${GLOBAL_OUTPUT_PATH})
 
 # Compiler flags.
+if (0)
 if (${CMAKE_CXX_COMPILER_ID} MATCHES "Clang" OR
         ${CMAKE_CXX_COMPILER_ID} STREQUAL "GNU")
     set(OPT "-O3")
@@ -42,7 +45,7 @@ else()
     # TODO: Windows support.
     message(FATAL_ERROR "Compiler ${CMAKE_CXX_COMPILER_ID} not supported")
 endif()
-
+endif()
 set(CMAKE_CXX_STANDARD 11)
 
 
diff --git a/cmd/CMakeLists.txt b/cmd/CMakeLists.txt
index c032f02..d80d2a0 100644
--- a/cmd/CMakeLists.txt
+++ b/cmd/CMakeLists.txt
@@ -13,5 +13,5 @@ if (BUILD_JSONNETFMT OR BUILD_TESTS)
     add_dependencies(jsonnetfmt libjsonnet_for_binaries)
     target_link_libraries(jsonnetfmt libjsonnet_for_binaries)
 
-	install(TARGETS jsonnetfmt DESTINATION "${CMAKE_INSTALL_BINDIR}")
+	install(TARGETS jsonnetfmt DESTINATION tools/jsonnet)
 endif()
diff --git a/core/CMakeLists.txt b/core/CMakeLists.txt
index e62a858..3b7afda 100644
--- a/core/CMakeLists.txt
+++ b/core/CMakeLists.txt
@@ -28,7 +28,7 @@ set(LIBJSONNET_SOURCE
     string_utils.cpp
     vm.cpp)
 
-add_library(libjsonnet SHARED ${LIBJSONNET_HEADERS} ${LIBJSONNET_SOURCE})
+add_library(libjsonnet ${LIBJSONNET_HEADERS} ${LIBJSONNET_SOURCE})
 add_dependencies(libjsonnet md5 stdlib)
 target_link_libraries(libjsonnet md5 nlohmann_json::nlohmann_json ryml)
 
@@ -46,7 +46,7 @@ set_target_properties(libjsonnet PROPERTIES OUTPUT_NAME jsonnet
 	PUBLIC_HEADER "${LIB_HEADER}")
 install(TARGETS libjsonnet
 	LIBRARY DESTINATION "${CMAKE_INSTALL_LIBDIR}"
-	ARCHIVE DESTINATION "${CMAKE_INSTALL_LIBDIR}"
+	ARCHIVE DESTINATION "${CMAKE_INSTALL_BINDIR}"
 	PUBLIC_HEADER DESTINATION "${CMAKE_INSTALL_INCLUDEDIR}")
 target_include_directories(libjsonnet INTERFACE
   $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/../include>)
diff --git a/stdlib/CMakeLists.txt b/stdlib/CMakeLists.txt
index a481d9f..2f58d2f 100644
--- a/stdlib/CMakeLists.txt
+++ b/stdlib/CMakeLists.txt
@@ -2,6 +2,7 @@
 
 add_executable(to_c_array to_c_array.cpp)
 
+if(0)
 # Custom command that will only build stdlib when it changes.
 add_custom_command(
 	OUTPUT ${PROJECT_SOURCE_DIR}/core/std.jsonnet.h
@@ -13,3 +14,4 @@ add_custom_command(
 # Standard library build target that libjsonnet can depend on.
 add_custom_target(stdlib ALL
 	DEPENDS ${PROJECT_SOURCE_DIR}/core/std.jsonnet.h)
+endif()
\ No newline at end of file
