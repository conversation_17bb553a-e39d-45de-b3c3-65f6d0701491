diff --git a/core/CMakeLists.txt b/core/CMakeLists.txt
index e8a0efa..5dfd2e4 100644
--- a/core/CMakeLists.txt
+++ b/core/CMakeLists.txt
@@ -34,6 +34,7 @@ if (BUILD_SHARED_BINARIES)
 add_library(libjsonnet ${LIBJSONNET_HEADERS} ${LIBJSONNET_SOURCE})
 add_dependencies(libjsonnet stdlib)
 target_link_libraries(libjsonnet nlohmann_json::nlohmann_json ryml)
+target_compile_features(libjsonnet PRIVATE cxx_std_17)
 
 file(STRINGS ${CMAKE_CURRENT_SOURCE_DIR}/../include/libjsonnet.h JSONNET_VERSION_DEF
      REGEX "[#]define[ \t]+LIB_JSONNET_VERSION[ \t]+")
@@ -60,6 +61,7 @@ if (BUILD_STATIC_LIBS)
     add_library(libjsonnet_static STATIC ${LIBJSONNET_SOURCE})
     add_dependencies(libjsonnet_static stdlib)
     target_link_libraries(libjsonnet_static nlohmann_json::nlohmann_json ryml)
+    target_compile_features(libjsonnet_static PRIVATE cxx_std_17)
     set_target_properties(libjsonnet_static PROPERTIES OUTPUT_NAME jsonnet)
     install(TARGETS libjsonnet_static DESTINATION "${CMAKE_INSTALL_LIBDIR}")
     target_include_directories(libjsonnet_static INTERFACE
