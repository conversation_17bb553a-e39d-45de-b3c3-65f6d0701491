diff --git a/core/CMakeLists.txt b/core/CMakeLists.txt
index a152fa9..e8a0efa 100644
--- a/core/CMakeLists.txt
+++ b/core/CMakeLists.txt
@@ -15,7 +15,8 @@ set(LIBJSONNET_HEADERS
     static_error.h
     string_utils.h
     unicode.h
-    vm.h)
+    vm.h
+    ../third_party/md5/md5.h)
 
 set(LIBJSONNET_SOURCE
     desugarer.cpp
@@ -26,12 +27,13 @@ set(LIBJSONNET_SOURCE
     pass.cpp
     static_analysis.cpp
     string_utils.cpp
-    vm.cpp)
+    vm.cpp
+    ../third_party/md5/md5.cpp)
 
 if (BUILD_SHARED_BINARIES)
 add_library(libjsonnet ${LIBJSONNET_HEADERS} ${LIBJSONNET_SOURCE})
-add_dependencies(libjsonnet md5 stdlib)
-target_link_libraries(libjsonnet md5 nlohmann_json::nlohmann_json ryml)
+add_dependencies(libjsonnet stdlib)
+target_link_libraries(libjsonnet nlohmann_json::nlohmann_json ryml)
 
 file(STRINGS ${CMAKE_CURRENT_SOURCE_DIR}/../include/libjsonnet.h JSONNET_VERSION_DEF
      REGEX "[#]define[ \t]+LIB_JSONNET_VERSION[ \t]+")
@@ -56,8 +58,8 @@ endif()
 if (BUILD_STATIC_LIBS)
     # Static library for jsonnet command-line tool.
     add_library(libjsonnet_static STATIC ${LIBJSONNET_SOURCE})
-    add_dependencies(libjsonnet_static md5 stdlib)
-    target_link_libraries(libjsonnet_static md5 nlohmann_json::nlohmann_json ryml)
+    add_dependencies(libjsonnet_static stdlib)
+    target_link_libraries(libjsonnet_static nlohmann_json::nlohmann_json ryml)
     set_target_properties(libjsonnet_static PROPERTIES OUTPUT_NAME jsonnet)
     install(TARGETS libjsonnet_static DESTINATION "${CMAKE_INSTALL_LIBDIR}")
     target_include_directories(libjsonnet_static INTERFACE
