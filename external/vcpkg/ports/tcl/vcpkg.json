{"name": "tcl", "version-string": "core-9-0-a1", "port-version": 8, "description": "Tcl provides a powerful platform for creating integration applications that tie together diverse applications, protocols, devices, and frameworks. When paired with the Tk toolkit, Tcl provides the fastest and most powerful way to create GUI applications that run on PCs, Unix, and Mac OS X. Tcl can also be used for a variety of web-related tasks and for creating powerful command languages for applications.", "homepage": "https://github.com/tcltk/tcl", "supports": "!android & !(windows & arm) & !uwp", "dependencies": ["zlib"], "features": {"profile": {"description": "Adds profiling hooks.  Map file is assumed."}, "thrdalloc": {"description": "Use the thread allocator (shared global free pool)."}, "unchecked": {"description": "Allows a symbols build to not use the debug enabled runtime (msvcrt.dll not msvcrtd.dll or libcmt.lib not libcmtd.lib)."}, "utfmax": {"description": "Forces Tcl_UniChar to be a 32-bit quantity in stead of 16-bits"}}}