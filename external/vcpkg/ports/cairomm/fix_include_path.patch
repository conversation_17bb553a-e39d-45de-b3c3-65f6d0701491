diff --git a/meson.build b/meson.build
index b8c2191..8f280b4 100644
--- a/meson.build
+++ b/meson.build
@@ -288,7 +288,7 @@ mm_conf_data.set('CAIROMM_MICRO_VERSION', cairomm_micro_version)
 mm_conf_data.set('VERSION', meson.project_version()) # for MSVC_NMake/cairomm/cairomm.rc
 
 cairommconfig_h_meson = files('cairommconfig.h.meson')
-install_includeconfigdir = install_libdir / cairomm_pcname / 'include'
+install_includeconfigdir = install_includedir
 configure_file(
   input: cairommconfig_h_meson,
   output: 'cairommconfig.h',
@@ -380,7 +380,7 @@ summary = [
   'Directories:',
   '                  prefix: @0@'.format(install_prefix),
   '              includedir: @0@'.format(install_prefix / install_includedir),
-  '       includecairommdir: @0@'.format(install_prefix / install_includedir / cairomm_pcname),
+  '       includecairommdir: @0@'.format(install_prefix / install_includedir),
   '                  libdir: @0@'.format(install_prefix / install_libdir),
   '        includeconfigdir: @0@'.format(install_prefix / install_includeconfigdir),
   '            pkgconfigdir: @0@'.format(install_prefix / install_pkgconfigdir),
diff --git a/cairomm/meson.build b/cairomm/meson.build
index f940268..23b955e 100644
--- a/cairomm/meson.build
+++ b/cairomm/meson.build
@@ -64,7 +64,7 @@ cairomm_private_h = [
 # docs/reference/meson.build needs this.
 source_h_files = cairomm_public_h
 
-install_headers(cairomm_public_h, subdir: cairomm_pcname / 'cairomm')
+install_headers(cairomm_public_h, subdir: 'cairomm')
 
 # Make sure we are exporting the symbols from the DLL
 cairomm_cpp_args = ['-DCAIROMM_BUILD=1']
diff --git a/data/cairomm.pc.in b/data/cairomm.pc.in
index b6d19a2..59a03f0 100644
--- a/data/cairomm.pc.in
+++ b/data/cairomm.pc.in
@@ -16,4 +16,4 @@ Version: @PACKAGE_VERSION@
 URL: http://www.cairographics.org/cairomm/
 Requires: @CAIROMM_MODULES@
 Libs: -L${libdir} -lcairomm@MSVC_TOOLSET_VER@-@CAIROMM_API_VERSION@ @CAIROMM_EXTRA_LIBS@
-Cflags: -I${includedir}/@CAIROMM_MODULE_NAME@ -I${libdir}/@CAIROMM_MODULE_NAME@/include
+Cflags: -I${includedir}
