diff --git a/CMakeLists.txt b/CMakeLists.txt
index c4b291c..2475881 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -86,13 +86,13 @@ install (FILES
 		DESTINATION 
 			"${CMAKE_INSTALL_PREFIX}/lib/pkgconfig")
 
-enable_testing()
+#enable_testing()
 
 
 
 add_subdirectory (sigc++)
-add_subdirectory (examples)
-add_subdirectory (tests)
+#add_subdirectory (examples)
+#add_subdirectory (tests)
 
 
 set (PROJECT_CMAKE_NAME		"${PROJECT_NAME}-3")
diff --git a/sigc++/CMakeLists.txt b/sigc++/CMakeLists.txt
index dd2d339..8480a5e 100644
--- a/sigc++/CMakeLists.txt
+++ b/sigc++/CMakeLists.txt
@@ -24,7 +24,7 @@ set (SOURCE_FILES
 
 set (SIGCPP_LIB_NAME sigc-${SIGCXX_API_VERSION})
 
-add_library(${SIGCPP_LIB_NAME} SHARED ${SOURCE_FILES})
+add_library(${SIGCPP_LIB_NAME} ${SOURCE_FILES})
 
 set_property (TARGET ${SIGCPP_LIB_NAME} PROPERTY VERSION ${PACKAGE_VERSION})
 set_property(TARGET ${SIGCPP_LIB_NAME}  PROPERTY SOVERSION ${LIBSIGCPP_SOVERSION})
