diff --git a/CMake/vtkModuleWrapClientServer.cmake b/CMake/vtkModuleWrapClientServer.cmake
index cba0380cd9..9c8bbc5084 100644
--- a/CMake/vtkModuleWrapClientServer.cmake
+++ b/CMake/vtkModuleWrapClientServer.cmake
@@ -72,8 +72,8 @@ function (_vtk_module_wrap_client_server_sources module sources classes)
         "guarantee intended behavior.")
     endif ()
   endif ()
-  file(GENERATE OUTPUT "compile_definitions_${_vtk_client_server_target_name}" CONTENT "${_vtk_client_server_genex_compile_definitions_all}")
-  file(GENERATE OUTPUT "include_directories_${_vtk_client_server_target_name}" CONTENT "${_vtk_client_server_genex_include_directories_all}")
+  file(GENERATE OUTPUT "compile_definitions_${_vtk_client_server_library_name}" CONTENT "${_vtk_client_server_genex_compile_definitions_all}")
+  file(GENERATE OUTPUT "include_directories_${_vtk_client_server_library_name}" CONTENT "${_vtk_client_server_genex_include_directories_all}")
   file(GENERATE
     OUTPUT  "${_vtk_client_server_args_file}"
     CONTENT "$<$<BOOL:${_vtk_client_server_genex_compile_definitions_all}>:\n-D\'$<JOIN:${_vtk_client_server_genex_compile_definitions_all},\'\n-D\'>\'>\n
diff --git a/CMakeLists.txt b/CMakeLists.txt
index 1be00fb71a..c47cf20d55 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -68,7 +68,7 @@ if (PARAVIEW_USE_EXTERNAL_VTK)
       "External VTK not found but ParaView requires VTK to function")
   endif ()
   if (NOT PARAVIEW_BUILD_SHARED_LIBS)
-    message(FATAL_ERROR
+    message(WARNING
       "Static build with external VTK does not currently work.")
   endif()
   get_filename_component(vtk_cmake_dir "${VTK_CONFIG}" DIRECTORY)
diff --git a/Utilities/PythonInterpreterPath/vtkPVPythonInterpreterPath.cxx b/Utilities/PythonInterpreterPath/vtkPVPythonInterpreterPath.cxx
index 06e517964b..fbf9f5c2df 100644
--- a/Utilities/PythonInterpreterPath/vtkPVPythonInterpreterPath.cxx
+++ b/Utilities/PythonInterpreterPath/vtkPVPythonInterpreterPath.cxx
@@ -13,8 +13,9 @@ extern "C"
   void vtkPVInitializePythonModules();
   void VTKUTILITIESPYTHONINTERPRETERPATH_EXPORT vtkPVPythonInterpreterPath()
   {
-    std::string libraryPath = vtkGetLibraryPathForSymbol(vtkPVInitializePythonModules);
-    vtkPythonInterpreter::SetUserPythonPath(
-      libraryPath.c_str(), "paraview/__init__.py" /*landmark*/);
+    // Ignore this for now. Requires a slightly newer VTK version than PV 5.12 uses.
+    //std::string libraryPath = vtkGetLibraryPathForSymbol(vtkPVInitializePythonModules);
+    //vtkPythonInterpreter::SetUserPythonPath(
+    //  libraryPath.c_str(), "paraview/__init__.py" /*landmark*/);
   }
 }
diff --git a/VTKExtensions/Core/vtkCLIOptions.cxx b/VTKExtensions/Core/vtkCLIOptions.cxx
--- a/VTKExtensions/Core/vtkCLIOptions.cxx
+++ b/VTKExtensions/Core/vtkCLIOptions.cxx
@@ -52,9 +52,9 @@
 
 public:
   // overridden to add a new line before each group.
-  std::string make_expanded(const CLI::App* sub) const override
+  std::string make_expanded(const CLI::App* sub, CLI::AppFormatMode mode) const override
   {
-    return "\n" + Superclass::make_expanded(sub);
+    return "\n" + Superclass::make_expanded(sub, mode);
   }
 
   // overridden to ensure good word wrapping for description text.
