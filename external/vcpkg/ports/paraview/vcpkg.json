{"name": "paraview", "version": "5.12.1", "port-version": 4, "description": "VTK-based Data Analysis and Visualization Application", "homepage": "https://www.paraview.org/", "license": "BSD-3-<PERSON><PERSON>", "dependencies": ["boost-algorithm", "boost-core", "boost-format", "cgns", "protobuf", "qt5compat", "qtsvg", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}, {"name": "vtk", "default-features": false, "features": ["paraview"]}], "features": {"all-modules": {"description": "enables the build of all paraview modules", "dependencies": ["ffmpeg", "gdal", "laszip", "pdal"]}, "cuda": {"description": "enables cuda within paraview", "dependencies": ["cuda", {"name": "vtk", "default-features": false, "features": ["cuda"]}]}, "mpi": {"description": "enables cuda within paraview", "dependencies": [{"name": "hdf5", "default-features": false, "features": ["parallel"]}, {"name": "vtk", "default-features": false, "features": ["mpi"]}]}, "python": {"description": "enables the build of python wrappers", "dependencies": [{"name": "vtk", "default-features": false, "features": ["python"]}]}, "tools": {"description": "Build paraview tools"}, "vtkm": {"description": "enables vtkm for the build of paraview", "dependencies": [{"name": "vtk", "default-features": false, "features": ["vtkm"]}]}}}