diff --git a/CMakeLists.txt b/CMakeLists.txt
index da1d4d8..f314754 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -162,12 +162,6 @@ if (${CPP_INDIRECT_IS_NOT_SUBPROJECT})
             ${CMAKE_INSTALL_INCLUDEDIR}
     )
 
-    install(
-        FILES
-            "${CMAKE_CURRENT_SOURCE_DIR}/LICENSE.txt"
-        DESTINATION
-            "${CMAKE_INSTALL_DATAROOTDIR}/licenses/indirect_value"
-    )
 
     install(
         TARGETS indirect_value
@@ -179,6 +173,7 @@ if (${CPP_INDIRECT_IS_NOT_SUBPROJECT})
 
     install(
         EXPORT indirect_value-export-set
+        FILE indirect_value-target.cmake
         NAMESPACE indirect_value::
         DESTINATION "${CMAKE_INSTALL_LIBDIR}/cmake/indirect_value"
     )
