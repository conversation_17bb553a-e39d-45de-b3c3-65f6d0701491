{"$comment": "Automatically generated by scripts/boost/generate-ports.ps1", "name": "boost-stacktrace", "version": "1.87.0", "description": "Boost stacktrace module", "homepage": "https://www.boost.org/libs/stacktrace", "license": "BSL-1.0", "supports": "!uwp", "dependencies": [{"name": "boost-assert", "version>=": "1.87.0"}, {"name": "boost-cmake", "version>=": "1.87.0"}, {"name": "boost-config", "version>=": "1.87.0"}, {"name": "boost-container-hash", "version>=": "1.87.0"}, {"name": "boost-core", "version>=": "1.87.0"}, {"name": "boost-headers", "version>=": "1.87.0"}, {"name": "boost-predef", "version>=": "1.87.0"}, {"name": "boost-winapi", "version>=": "1.87.0"}], "default-features": [{"name": "backtrace", "platform": "!windows"}, {"name": "windbg", "platform": "windows"}], "features": {"backtrace": {"description": "Use boost_stacktrace_backtrace", "supports": "!windows", "dependencies": [{"name": "libback<PERSON>ce", "platform": "!windows"}]}, "windbg": {"description": "Use boost_stacktrace_windbg", "supports": "windows"}}}