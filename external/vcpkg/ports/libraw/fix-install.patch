diff --git a/CMakeLists.txt b/CMakeLists.txt
index e6a70d9..a5c70e7 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -629,7 +629,13 @@ if (LIBRAW_INSTALL)
            )
 
     # Install Shared binary files.
-    install(TARGETS raw raw_r
+    install(TARGETS raw
+            EXPORT ${PROJECT_NAME}Targets
+            RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
+            LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}/manual-link
+            ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR}/manual-link
+           )
+    install(TARGETS raw_r
             EXPORT ${PROJECT_NAME}Targets
             RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
             LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
@@ -640,6 +646,7 @@ if (LIBRAW_INSTALL)
     if(NOT BUILD_SHARED_LIBS AND "${CMAKE_CXX_SIMULATE_ID}" STREQUAL "MSVC")
         message("ClangCl does not support pdb generation with static libraries")
     elseif(MSVC)
+    elseif(0)
         install(FILES ${PROJECT_BINARY_DIR}/raw.pdb ${PROJECT_BINARY_DIR}/raw_r.pdb
                 DESTINATION ${CMAKE_INSTALL_LIBDIR}
                 CONFIGURATIONS Debug RelWithDebInfo
diff --git a/cmake/data/libraw.pc.cmake b/cmake/data/libraw.pc.cmake
index 54c5af4..d182680 100644
--- a/cmake/data/libraw.pc.cmake
+++ b/cmake/data/libraw.pc.cmake
@@ -1,6 +1,6 @@
 prefix=@CMAKE_INSTALL_PREFIX@
 exec_prefix=${prefix}
-libdir=${exec_prefix}/@CMAKE_INSTALL_LIBDIR@
+libdir=${exec_prefix}/@CMAKE_INSTALL_LIBDIR@/manual-link
 includedir=${prefix}/@CMAKE_INSTALL_INCLUDEDIR@
 
 Name: @PROJECT_NAME@
