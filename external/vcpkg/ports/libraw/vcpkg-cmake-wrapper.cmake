find_library(LibRaw_LIBRARY_RELEASE NAMES raw PATHS "${CMAKE_CURRENT_LIST_DIR}/../../lib/manual-link" NO_DEFAULT_PATH)
find_library(LibRaw_LIBRARY_DEBUG NAMES rawd raw PATHS "${CMAKE_CURRENT_LIST_DIR}/../../debug/lib/manual-link" NO_DEFAULT_PATH)
find_library(LibRaw_r_LIBRARY_RELEASE NAMES raw_r PATHS "${CMAKE_CURRENT_LIST_DIR}/../../lib" NO_DEFAULT_PATH)
find_library(LibRaw_r_LIBRARY_DEBUG NAMES raw_rd raw_r PATHS "${CMAKE_CURRENT_LIST_DIR}/../../debug/lib" NO_DEFAULT_PATH)

set(LIBRAW_PREV_MODULE_PATH "${CMAKE_MODULE_PATH}")
list(APPEND CMAKE_MODULE_PATH "${CMAKE_CURRENT_LIST_DIR}")
_find_package(${ARGS})
set(CMAKE_MODULE_PATH ${LIBRAW_PREV_MODULE_PATH})

if(NOT LibRaw_r_LIBRARIES STREQUAL "")
    if ("@ENABLE_OPENMP@")
        find_package(OpenMP REQUIRED)
        if (OpenMP_FOUND)
            list(APPEND LibRaw_LIBRARIES ${OpenMP_CXX_LIBRARIES})
            list(APPEND LibRaw_r_LIBRARIES ${OpenMP_CXX_LIBRARIES})
        endif()
    endif()

    if ("@VCPKG_LIBRARY_LINKAGE@" STREQUAL "static")
        find_package(Jasper REQUIRED)
        list(APPEND LibRaw_LIBRARIES ${JASPER_LIBRARIES})
        list(APPEND LibRaw_r_LIBRARIES ${JASPER_LIBRARIES})
        find_package(lcms2 CONFIG REQUIRED)
        list(APPEND LibRaw_LIBRARIES lcms2::lcms2)
        list(APPEND LibRaw_r_LIBRARIES lcms2::lcms2)
        find_package(ZLIB REQUIRED)
        list(APPEND LibRaw_LIBRARIES ${ZLIB_LIBRARIES})
        list(APPEND LibRaw_r_LIBRARIES ${ZLIB_LIBRARIES})
        if("@CMAKE_REQUIRE_FIND_PACKAGE_JPEG@")
            find_package(JPEG REQUIRED)
            list(APPEND LibRaw_LIBRARIES ${JPEG_LIBRARIES})
            list(APPEND LibRaw_r_LIBRARIES ${JPEG_LIBRARIES})
        endif()
        if("@MATH_LIBRARY@")
            list(APPEND LibRaw_LIBRARIES @MATH_LIBRARY@)
            list(APPEND LibRaw_r_LIBRARIES @MATH_LIBRARY@)
        endif()
    endif()
endif()
