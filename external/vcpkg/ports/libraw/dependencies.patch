diff --git a/CMakeLists.txt b/CMakeLists.txt
index d3faf24..8833187 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -130,7 +130,10 @@ set(LIBRAW_PC_REQUIRES_PRIVATE "")
 if(NOT WIN32 AND NOT EMSCRIPTEN)
     FIND_LIBRARY(MATH_LIBRARY m)
     if(MATH_LIBRARY)
+        set(MATH_LIBRARY m)
         string(APPEND LIBRAW_PC_LIBS_PRIVATE " -l${MATH_LIBRARY}")
+    else()
+        set(MATH_LIBRARY "")
     endif()
 endif()
 
@@ -142,7 +145,11 @@ set(LCMS_FOUND false)
 
 if(ENABLE_LCMS)
     message(STATUS "Check for LCMS2 availability...")
-    find_package(LCMS2)
+    find_package(lcms2 CONFIG REQUIRED)
+    set(LCMS2_FOUND 1)
+    set(LCMS2_VERSION 2.14)
+    set(LCMS2_INCLUDE_DIR "")
+    set(LCMS2_LIBRARIES lcms2::lcms2)
     if(LCMS2_FOUND AND (LCMS2_VERSION VERSION_EQUAL 2.1 OR LCMS2_VERSION VERSION_GREATER 2.1))
         message(STATUS "Found LCMS2 : ${LCMS2_LIBRARIES} ${LCMS2_INCLUDE_DIR}")
         include_directories(${LCMS2_INCLUDE_DIR})
@@ -188,7 +195,7 @@ MACRO_BOOL_TO_01(ZLIB_FOUND LIBRAW_USE_DNGDEFLATECODEC)
 # JPEG library check
 find_package(JPEG)
 if(JPEG_FOUND)
-    if (${JPEG_VERSION} LESS 80)
+    if (${JPEG_VERSION} LESS 80 OR NOT CMAKE_REQUIRE_FIND_PACKAGE_JPEG)
         set(JPEG8_FOUND FALSE)
     else()
         set(JPEG8_FOUND TRUE)
diff --git a/cmake/librawConfig.cmake.in b/cmake/librawConfig.cmake.in
index b1b58d8..dae06cc 100644
--- a/cmake/librawConfig.cmake.in
+++ b/cmake/librawConfig.cmake.in
@@ -13,7 +13,7 @@ endif()
 
 if(@LCMS_SUPPORT_CAN_BE_COMPILED@)
     if(@LCMS2_FOUND@)
-        find_dependency(LCMS2)
+        find_dependency(lcms2 CONFIG)
     elseif(@LCMS_FOUND@)
         find_dependency(LCMS)
     endif()
