{"name": "restc-cpp", "version-semver": "1.0.0", "port-version": 1, "description": "Modern C++ REST Client library", "homepage": "https://github.com/jgaa/restc-cpp", "license": "MIT", "dependencies": ["boost-asio", "boost-chrono", "boost-context", "boost-coroutine", "boost-date-time", "boost-filesystem", "boost-log", "boost-program-options", "boost-system", "boost-uuid", "<PERSON><PERSON><PERSON>", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "default-features": ["openssl", "zlib"], "features": {"openssl": {"description": "OpenSSL support.", "dependencies": ["openssl"]}, "threaded-ctx": {"description": "Allow asio contexts with multiple threads. Enables thread-safe internal access."}, "zlib": {"description": "Use zlib.", "dependencies": ["zlib"]}}}