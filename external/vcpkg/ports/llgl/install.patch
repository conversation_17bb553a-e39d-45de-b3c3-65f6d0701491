diff --git a/CMakeLists.txt b/CMakeLists.txt
index f4c8b497..ff922fd6 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -729,8 +729,12 @@ else()
     set(SUMMARY_LIBRARY_TYPE "Shared")
     add_library(LLGL SHARED ${FilesLLGL})
 endif()
+set(all_targets LLGL)
 
-target_include_directories(LLGL PUBLIC ${PROJECT_INCLUDE_DIR})
+include(GNUInstallDirs)
+target_include_directories(LLGL INTERFACE
+                           $<BUILD_INTERFACE:${PROJECT_INCLUDE_DIR}>
+                           $<INSTALL_INTERFACE:${CMAKE_INSTALL_INCLUDEDIR}>)
 
 if(LLGL_ANDROID_PLATFORM)
     target_link_libraries(LLGL android log)
@@ -763,6 +767,8 @@ if(LLGL_BUILD_RENDERER_NULL)
     target_link_libraries(LLGL_Null LLGL ${OPENGL_LIBRARIES})
     
     ADD_DEFINE(LLGL_BUILD_RENDERER_NULL)
+
+    list(APPEND all_targets LLGL_Null)
 endif()
 
 if(LLGL_BUILD_RENDERER_OPENGLES3)
@@ -788,6 +794,8 @@ if(LLGL_BUILD_RENDERER_OPENGLES3)
         
         ADD_DEFINE(LLGL_BUILD_RENDERER_OPENGLES3)
         ADD_PROJECT_DEFINE(LLGL_OpenGLES3 LLGL_OPENGLES3)
+
+        list(APPEND all_targets LLGL_OpenGLES3)
     else()
         message("Missing OpenGLES -> LLGL_OpenGLES3 renderer will be excluded from project")
     endif()
@@ -816,6 +824,8 @@ if(LLGL_BUILD_RENDERER_OPENGL)
         
         ADD_DEFINE(LLGL_BUILD_RENDERER_OPENGL)
         ADD_PROJECT_DEFINE(LLGL_OpenGL LLGL_OPENGL)
+
+        list(APPEND all_targets LLGL_OpenGL)
     else()
         message("Missing OpenGL -> LLGL_OpenGL renderer will be excluded from project")
     endif()
@@ -838,6 +848,8 @@ if(NOT APPLE AND LLGL_BUILD_RENDERER_VULKAN)
         target_link_libraries(LLGL_Vulkan LLGL ${Vulkan_LIBRARY})
         
         ADD_DEFINE(LLGL_BUILD_RENDERER_VULKAN)
+        
+        list(APPEND all_targets LLGL_Vulkan)
     else()
         message("Missing Vulkan -> LLGL_Vulkan renderer will be excluded from project")
     endif()
@@ -869,6 +881,8 @@ if(APPLE AND LLGL_BUILD_RENDERER_METAL)
         endif()
         
         ADD_DEFINE(LLGL_BUILD_RENDERER_METAL)
+        
+        list(APPEND all_targets LLGL_Metal)
     else()
         message("Missing Metal/MetalKit -> LLGL_Metal renderer will be excluded from project")
     endif()
@@ -888,6 +902,8 @@ if(WIN32)
         target_link_libraries(LLGL_Direct3D11 LLGL d3d11 dxgi D3DCompiler)
         
         ADD_DEFINE(LLGL_BUILD_RENDERER_DIRECT3D11)
+        
+        list(APPEND all_targets LLGL_Direct3D11)
     endif()
     
     if(LLGL_BUILD_RENDERER_DIRECT3D12)
@@ -904,6 +920,8 @@ if(WIN32)
         target_compile_definitions(LLGL_Direct3D12 PUBLIC -DLLGL_DX_ENABLE_D3D12)
         
         ADD_DEFINE(LLGL_BUILD_RENDERER_DIRECT3D12)
+        
+        list(APPEND all_targets LLGL_Direct3D12)
     endif()
 endif()
 
@@ -994,6 +1012,20 @@ if(WIN32 AND LLGL_BUILD_WRAPPER_CSHARP)
     add_subdirectory(Wrapper/CSharp)
 endif()
 
+# Install targets
+install(TARGETS ${all_targets} EXPORT LLGLTargets RUNTIME LIBRARY ARCHIVE)
+# Install headers
+install(DIRECTORY "${PROJECT_INCLUDE_DIR}/" DESTINATION "${CMAKE_INSTALL_INCLUDEDIR}")
+# Install CMake config files
+install(EXPORT LLGLTargets NAMESPACE LLGL:: DESTINATION "${CMAKE_INSTALL_LIBDIR}/cmake/LLGL")
+include(CMakePackageConfigHelpers)
+configure_package_config_file("${CMAKE_CURRENT_SOURCE_DIR}/cmake/Config.cmake.in"
+  "${CMAKE_CURRENT_BINARY_DIR}/LLGLConfig.cmake"
+  INSTALL_DESTINATION ${CMAKE_INSTALL_LIBDIR}/cmake/LLGL
+)
+install(FILES "${CMAKE_CURRENT_BINARY_DIR}/LLGLConfig.cmake"
+        DESTINATION "${CMAKE_INSTALL_LIBDIR}/cmake/LLGL"
+)
 # Summary Information
 message("~~~ Build Summary ~~~")
 
diff --git a/cmake/Config.cmake.in b/cmake/Config.cmake.in
new file mode 100644
index 00000000..aa67a9e4
--- /dev/null
+++ b/cmake/Config.cmake.in
@@ -0,0 +1,5 @@
+@PACKAGE_INIT@
+
+include("${CMAKE_CURRENT_LIST_DIR}/LLGLTargets.cmake")
+
+check_required_components(@PROJECT_NAME@)
