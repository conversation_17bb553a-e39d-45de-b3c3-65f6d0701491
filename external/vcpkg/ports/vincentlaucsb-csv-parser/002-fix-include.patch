diff --git a/include/internal/basic_csv_parser.hpp b/include/internal/basic_csv_parser.hpp
index d76b2d9..8dd0110 100644
--- a/include/internal/basic_csv_parser.hpp
+++ b/include/internal/basic_csv_parser.hpp
@@ -15,7 +15,7 @@
 #include <thread>
 #include <vector>
 
-#include "../external/mio.hpp"
+#include "mio/mmap.hpp"
 #include "col_names.hpp"
 #include "common.hpp"
 #include "csv_format.hpp"
diff --git a/include/internal/common.hpp b/include/internal/common.hpp
index c132bfb..dff4d03 100644
--- a/include/internal/common.hpp
+++ b/include/internal/common.hpp
@@ -28,12 +28,12 @@
 #pragma once
 #include <type_traits>
 
-#include "../external/string_view.hpp"
+#include "nonstd/string_view.hpp"
 
   // If there is another version of He<PERSON>, then the newer one 
   // takes precedence.
   // See: https://github.com/nemequ/hedley
-#include "../external/hedley.h"
+#include "hedley.h"
 
 namespace csv {
 #ifdef _MSC_VER
diff --git a/include/internal/csv_reader.hpp b/include/internal/csv_reader.hpp
index 1cdf6e0..3077395 100644
--- a/include/internal/csv_reader.hpp
+++ b/include/internal/csv_reader.hpp
@@ -15,7 +15,7 @@
 #include <string>
 #include <vector>
 
-#include "../external/mio.hpp"
+#include "mio/mmap.hpp"
 #include "basic_csv_parser.hpp"
 #include "common.hpp"
 #include "data_type.hpp"
