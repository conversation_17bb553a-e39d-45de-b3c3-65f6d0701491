diff --git a/CMakeLists.txt b/CMakeLists.txt
index 1b920b1..c56a142 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -1,6 +1,12 @@
 cmake_minimum_required(VERSION 3.9)
 project(csv)
 
+include(GNUInstallDirs)
+
+find_path(HEDLEY_INCLUDE_DIRS "hedley.h")
+find_package(mio CONFIG REQUIRED)
+find_package(string-view-lite CONFIG REQUIRED)
+
 if(CSV_CXX_STANDARD)
 	set(CMAKE_CXX_STANDARD ${CSV_CXX_STANDARD})
 else()
@@ -40,10 +46,7 @@ set(CSV_TEST_DIR ${CMAKE_CURRENT_LIST_DIR}/tests)
 
 include_directories(${CSV_INCLUDE_DIR})
 
-## Load developer specific CMake settings
-if (CMAKE_SOURCE_DIR STREQUAL CMAKE_CURRENT_SOURCE_DIR)
-    SET(CSV_DEVELOPER TRUE)
-endif()
+SET(CSV_DEVELOPER OFF)
 
 ## Main Library
 add_subdirectory(${CSV_SOURCE_DIR})
@@ -60,6 +63,23 @@ if (CSV_BUILD_PROGRAMS)
     add_subdirectory("programs")
 endif()
 
+install(TARGETS csv EXPORT unofficial-vincentlaucsb-csv-parser)
+
+install(
+    EXPORT unofficial-vincentlaucsb-csv-parser
+    FILE unofficial-vincentlaucsb-csv-parser-config.cmake
+    DESTINATION "${CMAKE_INSTALL_DATAROOTDIR}/unofficial-vincentlaucsb-csv-parser"
+    NAMESPACE unofficial::vincentlaucsb-csv-parser::
+)
+
+install(
+    DIRECTORY "${CMAKE_SOURCE_DIR}/include/"
+    DESTINATION "${CMAKE_INSTALL_INCLUDEDIR}/vincentlaucsb-csv-parser"
+    PATTERN "*.cpp" EXCLUDE
+    PATTERN "CMakeLists.txt" EXCLUDE
+    PATTERN "external" EXCLUDE
+)
+
 ## Developer settings
 if (CSV_DEVELOPER)
     # Allow for performance profiling
diff --git a/include/internal/CMakeLists.txt b/include/internal/CMakeLists.txt
index 4cbf58c..e9e65f8 100644
--- a/include/internal/CMakeLists.txt
+++ b/include/internal/CMakeLists.txt
@@ -23,6 +23,9 @@ target_sources(csv
 		"data_type.hpp"
 		)
 
-set_target_properties(csv PROPERTIES LINKER_LANGUAGE CXX)
-target_link_libraries(csv PRIVATE Threads::Threads)
-target_include_directories(csv INTERFACE ../)
+set_target_properties(csv PROPERTIES LINKER_LANGUAGE CXX OUTPUT_NAME "vincentlaucsb-csv-parser-csv")
+target_include_directories(csv
+	PUBLIC ${HEDLEY_INCLUDE_DIRS}
+	INTERFACE $<INSTALL_INTERFACE:${CMAKE_INSTALL_INCLUDEDIR}/vincentlaucsb-csv-parser>
+)
+target_link_libraries(csv PRIVATE Threads::Threads PUBLIC mio::mio mio::mio-headers nonstd::string-view-lite)
