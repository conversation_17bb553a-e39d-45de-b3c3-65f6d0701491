diff --git a/Makefile.in b/Makefile.in
index a935107..51c5e07 100644
--- a/Makefile.in
+++ b/Makefile.in
@@ -1572,13 +1572,13 @@ libast_la_SOURCES = \
 libast_la_LDFLAGS = -version-info @version_info@
 @EXTERNAL_CMINPACK_FALSE@@EXTERNAL_PAL_FALSE@libast_la_LIBADD = libast_pal.la libast_cminpack.la
 @EXTERNAL_CMINPACK_FALSE@@EXTERNAL_PAL_TRUE@libast_la_LIBADD = -lpal libast_cminpack.la
-@EXTERNAL_CMINPACK_TRUE@@EXTERNAL_PAL_FALSE@libast_la_LIBADD = libast_pal.la -lcminpack
+@EXTERNAL_CMINPACK_TRUE@@EXTERNAL_PAL_FALSE@libast_la_LIBADD = libast_pal.la $(LIBCMINPACK)
 
 # Ensure libast links against libraries containing functions used within
 # libast. If AST is configured --with-external-pal, then the internal
 # libast_pal library will be empty, and we link to an external PAL
 # library instead. Do the same for cminpack
-@EXTERNAL_CMINPACK_TRUE@@EXTERNAL_PAL_TRUE@libast_la_LIBADD = -lpal -lcminpack
+@EXTERNAL_CMINPACK_TRUE@@EXTERNAL_PAL_TRUE@libast_la_LIBADD = -lpal $(LIBCMINPACK)
 
 # AST_PAR is really part of GRP_F_INCLUDE_FILES, but it must not be
 # distributed, so list it separately.
diff --git a/configure b/configure
index d9db3ee..f3d3fc0 100755
--- a/configure
+++ b/configure
@@ -15763,7 +15763,7 @@ fi
 EXTERNAL_CMINPACK=$external_cminpack
 
 if test "$external_cminpack" = "1"; then
-   LIBCMINPACK="-lcminpack"
+   LIBCMINPACK="-lcminpack$CMINPACK_DEBUG_SUFFIX"
 
 
 $as_echo "#define EXTERNAL_CMINPACK 1" >>confdefs.h
diff --git a/src/polymap.c b/src/polymap.c
index 0b436cc..1aee268 100644
--- a/src/polymap.c
+++ b/src/polymap.c
@@ -165,7 +165,7 @@ f     - AST_POLYTRAN: Fit a PolyMap inverse or forward transformation
 #include "cmpmap.h"              /* Compound mappings */
 #include "polymap.h"             /* Interface definition for this class */
 #include "unitmap.h"             /* Unit mappings */
-#include "cminpack/cminpack.h"   /* Levenberg - Marquardt minimization */
+#include "cminpack-1/cminpack.h"   /* Levenberg - Marquardt minimization */
 #include "pal.h"                 /* SLALIB function definitions */
 
 /* Error code definitions. */
