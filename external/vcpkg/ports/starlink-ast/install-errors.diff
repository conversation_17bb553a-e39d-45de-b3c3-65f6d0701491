diff --git a/Makefile.in b/Makefile.in
index a935107..dbc590d 100644
--- a/Makefile.in
+++ b/Makefile.in
@@ -1772,7 +1772,7 @@ install-libLTLIBRARIES: $(lib_LTLIBRARIES)
 	  echo " $(MKDIR_P) '$(DESTDIR)$(libdir)'"; \
 	  $(MKDIR_P) "$(DESTDIR)$(libdir)" || exit 1; \
 	  echo " $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=install $(INSTALL) $(INSTALL_STRIP_FLAG) $$list2 '$(DESTDIR)$(libdir)'"; \
-	  $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=install $(INSTALL) $(INSTALL_STRIP_FLAG) $$list2 "$(DESTDIR)$(libdir)"; \
+	  $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=install $(INSTALL) $(INSTALL_STRIP_FLAG) $$list2 "$(DESTDIR)$(libdir)" || exit 1; \
 	    if $(MANIFEST); then \
 	      for p in $$list2; do \
 	         echo "MANIFEST:$(DESTDIR)$(libdir)/$$p"; \
