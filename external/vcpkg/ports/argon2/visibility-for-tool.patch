diff --git a/src/core.h b/src/core.h
index 78000ba..91c7bcf 100644
--- a/src/core.h
+++ b/src/core.h
@@ -135,7 +135,7 @@ void secure_wipe_memory(void *v, size_t n);
  * @param mem Pointer to the memory
  * @param s Memory size in bytes
  */
-void clear_internal_memory(void *v, size_t n);
+ARGON2_PUBLIC void clear_internal_memory(void *v, size_t n);
 
 /*
  * Computes absolute position of reference block in the lane following a skewed
