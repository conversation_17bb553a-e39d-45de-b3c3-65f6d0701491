diff --git a/include/X11/Intrinsic.h b/include/X11/Intrinsic.h
index cf8d3fe41..559697aa0 100644
--- a/include/X11/Intrinsic.h
+++ b/include/X11/Intrinsic.h
@@ -50,6 +50,10 @@ in this Software without prior written authorization from The Open Group.
 #ifndef _XtIntrinsic_h
 #define _XtIntrinsic_h
 
+#ifdef WIN32
+#define _WILLWINSOCK_
+#endif
+
 #include	<X11/Xlib.h>
 #include	<X11/Xutil.h>
 #include	<X11/Xresource.h>
diff --git a/include/X11/IntrinsicI.h b/include/X11/IntrinsicI.h
index a8b7f48cf..99c5349e7 100644
--- a/include/X11/IntrinsicI.h
+++ b/include/X11/IntrinsicI.h
@@ -48,11 +48,12 @@ SOFTWARE.
 #ifndef _XtintrinsicI_h
 #define _XtintrinsicI_h
 
-#include "Xtos.h"
-#include "IntrinsicP.h"
 #ifdef WIN32
 #define _WILLWINSOCK_
 #endif
+
+#include "Xtos.h"
+#include "IntrinsicP.h"
 #include <X11/Xos.h>
 
 #include "Object.h"
diff --git a/src/Convert.c b/src/Convert.c
index 9b2d465b3..5c89850f1 100644
--- a/src/Convert.c
+++ b/src/Convert.c
@@ -71,6 +71,9 @@ in this Software without prior written authorization from The Open Group.
 #ifdef HAVE_CONFIG_H
 #include <config.h>
 #endif
+#ifdef _MSC_VER
+#include <direct.h>
+#endif
 #include        "IntrinsicI.h"
 #include        "StringDefs.h"
 #include        "Intrinsic.h"
diff --git a/src/Core.c b/src/Core.c
index d728a9286..5ee0d2fe8 100644
--- a/src/Core.c
+++ b/src/Core.c
@@ -80,6 +80,9 @@ in this Software without prior written authorization from The Open Group.
 #include "RectObjP.h"
 #include "ThreadsI.h"
 #include "StringDefs.h"
+#ifndef None
+#define None 0L
+#endif
 
 /******************************************************************
  *
diff --git a/src/Error.c b/src/Error.c
index 4bf4713e5..a64784a6f 100644
--- a/src/Error.c
+++ b/src/Error.c
@@ -74,7 +74,9 @@ in this Software without prior written authorization from The Open Group.
 #include "IntrinsicI.h"
 #include <stdio.h>
 #include <stdlib.h>
-
+#ifndef None
+#define None 0L
+#endif
 /* The error handlers in the application context aren't used since we can't
    come up with a uniform way of using them.  If you can, define
    GLOBALERRORS to be FALSE (or 0). */
diff --git a/src/Event.c b/src/Event.c
index dadcedeb8..1eafc0143 100644
--- a/src/Event.c
+++ b/src/Event.c
@@ -74,7 +74,9 @@ in this Software without prior written authorization from The Open Group.
 #include "IntrinsicI.h"
 #include "Shell.h"
 #include "StringDefs.h"
-
+#ifndef None
+#define None 0L
+#endif
 typedef struct _XtEventRecExt {
     int type;
     XtPointer select_data[1];   /* actual dimension is [mask] */
diff --git a/src/GCManager.c b/src/GCManager.c
index 6031248e5..205f1cbe3 100644
--- a/src/GCManager.c
+++ b/src/GCManager.c
@@ -72,7 +72,9 @@ in this Software without prior written authorization from The Open Group.
 #include <config.h>
 #endif
 #include "IntrinsicI.h"
-
+#ifndef None
+#define None 0L
+#endif
 typedef struct _GCrec {
     unsigned char screen;       /* Screen for GC */
     unsigned char depth;        /* Depth for GC */
diff --git a/src/Geometry.c b/src/Geometry.c
index 3704cb63d..2f00cdcca 100644
--- a/src/Geometry.c
+++ b/src/Geometry.c
@@ -75,6 +75,10 @@ in this Software without prior written authorization from The Open Group.
 #include "ShellP.h"
 #include "ShellI.h"
 
+#ifndef None
+#define None 0L
+#endif
+
 static void
 ClearRectObjAreas(RectObj r, XWindowChanges *old)
 {
diff --git a/src/Keyboard.c b/src/Keyboard.c
index da6fa11aa..7d9271f45 100644
--- a/src/Keyboard.c
+++ b/src/Keyboard.c
@@ -80,6 +80,10 @@ in this Software without prior written authorization from The Open Group.
 #include "PassivGraI.h"
 #include "EventI.h"
 
+#ifndef None
+#define None 0L
+#endif
+
 #define _GetWindowedAncestor(w) (XtIsWidget(w) ? w : _XtWindowedAncestor(w))
 
 /* InActiveSubtree cache of the current focus source and its ancestors */
diff --git a/util/makestrs.c b/util/makestrs.c
index 4d5c0a6b7..b64e42eea 100644
--- a/util/makestrs.c
+++ b/util/makestrs.c
@@ -29,7 +29,9 @@ in this Software without prior written authorization from The Open Group.
 #include <stdio.h>
 #include <string.h>
 #include <stdlib.h>
+#ifdef HAVE_UNISTD_H
 #include <unistd.h>
+#endif
 
 typedef struct _TableEnt {
     struct _TableEnt *next;
diff --git a/src/Initialize.c b/src/Initialize.c
index 8ae54b47c..765479603 100644
--- a/src/Initialize.c
+++ b/src/Initialize.c
@@ -246,7 +246,7 @@ _XtInherit(void)
  * cygwin-xfree: http://www.cygwin.com/ml/cygwin-xfree/2003-10/msg00000.html
  */
 
-#ifdef __x86_64__
+#if defined(__x86_64__) || defined(_M_X64)
 asm(".section .trampoline, \"dwx\" \n\
  .globl _XtInherit        \n\
  _XtInherit:              \n\
diff --git a/src/Shell.c b/src/Shell.c
index 07713cc25..fcf10a5e2 100644
--- a/src/Shell.c
+++ b/src/Shell.c
@@ -87,7 +87,9 @@ in this Software without prior written authorization from The Open Group.
 #include <X11/ICE/ICElib.h>
 #include <stdio.h>
 #include <stdlib.h>
+#ifdef HAVE_UNISTD_H
 #include <unistd.h>
+#endif
 
 #ifdef EDITRES
 #include <X11/Xmu/Editres.h>
