diff --git a/Makefile.am b/Makefile.am
index f8d5a6b72..47f847bbd 100644
--- a/Makefile.am
+++ b/Makefile.am
@@ -19,7 +19,7 @@
 #  TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
 #  PERFORMANCE OF THIS SOFTWARE.
 
-SUBDIRS = util src include man specs test
+SUBDIRS = src include man specs test
 
 ACLOCAL_AMFLAGS = -I m4
 
diff --git a/src/Makefile.am b/src/Makefile.am
index a26e10d7a..e8a520a2f 100644
--- a/src/Makefile.am
+++ b/src/Makefile.am
@@ -104,5 +104,5 @@ $(BUILT_SOURCE): $(top_builddir)/util/makestrs$(EXEEXT)
 	$(AM_V_at)cp Shell.h $(top_builddir)/include/X11
 	$(AM_V_at)rm StringDefs.h Shell.h
 
-$(top_builddir)/util/makestrs$(EXEEXT) :
-	$(am__cd) $(@D) && $(MAKE) $(AM_MAKEFLAGS) $(@F)
+# $(top_builddir)/util/makestrs$(EXEEXT) :
+# 	$(am__cd) $(@D) && $(MAKE) $(AM_MAKEFLAGS) $(@F)
