diff --git a/src/Converters.c b/src/Converters.c
index 4c2762356..5376a34fb 100644
--- a/src/Converters.c
+++ b/src/Converters.c
@@ -82,6 +82,9 @@ in this Software without prior written authorization from The Open Group.
 #include        <X11/Xlocale.h>
 #include        <errno.h>       /* for StringToDirectoryString */
+#ifdef _MSC_VER
+#include <direct.h>
+#endif
 
 #define IsNewline(str) ((str) == '\n')
 #define IsWhitespace(str) ((str)== ' ' || (str) == '\t')
 
