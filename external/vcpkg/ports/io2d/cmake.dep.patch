diff --git a/P0267_RefImpl/P0267_RefImpl/cairo/CMakeLists.txt b/P0267_RefImpl/P0267_RefImpl/cairo/CMakeLists.txt
index 5ebeb6afa..702864667 100644
--- a/P0267_RefImpl/P0267_RefImpl/cairo/CMakeLists.txt
+++ b/P0267_RefImpl/P0267_RefImpl/cairo/CMakeLists.txt
@@ -2,8 +2,8 @@ cmake_minimum_required(VERSION 3.8)
 
 project(io2d CXX)
 
-find_package(Cairo REQUIRED)
-find_package(GraphicsMagick REQUIRED)
+find_package(Cairo REQUIRED)
+find_package(unofficial-GraphicsMagick REQUIRED)
 
 add_library(io2d_cairo
 	cairo_renderer-graphicsmagickinit.cpp
@@ -24,7 +24,7 @@ target_include_directories(io2d_cairo PUBLIC
 
 target_compile_features(io2d_cairo PUBLIC cxx_std_17)
 
-target_link_libraries(io2d_cairo PUBLIC io2d_core Cairo::Cairo GraphicsMagick::GraphicsMagick)
+target_link_libraries(io2d_cairo PUBLIC io2d_core Cairo::Cairo unofficial::graphicsmagick::graphicsmagick)
 
 install(
 	TARGETS io2d_cairo EXPORT io2d_targets
diff --git a/P0267_RefImpl/P0267_RefImpl/cairo/win32/CMakeLists.txt b/P0267_RefImpl/P0267_RefImpl/cairo/win32/CMakeLists.txt
index abb150113..75d8c654d 100644
--- a/P0267_RefImpl/P0267_RefImpl/cairo/win32/CMakeLists.txt
+++ b/P0267_RefImpl/P0267_RefImpl/cairo/win32/CMakeLists.txt
@@ -27,15 +27,24 @@ if(MSVC)
 	target_compile_definitions(io2d_cairo_win32 PUBLIC -DUNICODE -D_UNICODE -D_CRT_SECURE_NO_WARNINGS)
 
 	find_library(PIXMAN_LIB pixman-1)
-	find_library(FREETYPE_LIB freetype)
-	find_library(FONTCONFIG_LIB fontconfig)
-	find_library(BZ_LIB bz2)
-	find_library(JPEG_LIB jpeg)
-	find_library(TIFF_LIB tiff)
-	find_library(EXPAT_LIB expat)
-	find_library(LZMA_LIB lzma)
-	find_library(ICONV_LIB libiconv)
-	find_library(CHARSET_LIB libcharset)
+    find_package(FreeType REQUIRED)
+	set(FREETYPE_LIB ${FREETYPE_LIBRARIES}) # I dont use targets here since this means I have to correct the config.cmake too
+    find_package(Fontconfig REQUIRED)
+	set(FONTCONFIG_LIB ${Fontconfig_LIBRARIES})
+    find_package(BZip2 REQUIRED)
+	set(BZ_LIB ${BZIP2_LIBRARIES})
+    find_package(JPEG REQUIRED)
+	set(JPEG_LIB ${JPEG_LIBRARIES})
+    find_package(TIFF REQUIRED)
+	set(TIFF_LIB ${TIFF_LIBRARIES})
+    find_package(EXPAT REQUIRED)
+	set(EXPAT_LIB ${EXPAT_LIBRARIES})
+    find_package(LibLZMA REQUIRED)
+	set(LZMA_LIB ${LIBLZMA_LIBRARIES})
+    find_package(Iconv REQUIRED)
+    if(NOT Iconv_IS_BUILT_IN)
+        set(ICONV_LIB ${Iconv_LIBRARIES})
+	endif()
 
 	target_link_libraries(io2d_cairo_win32 PUBLIC ${PIXMAN_LIB} ${FREETYPE_LIB} ${FONTCONFIG_LIB} ${BZ_LIB} ${JPEG_LIB} ${TIFF_LIB} ${EXPAT_LIB} ${LZMA_LIB} ${ICONV_LIB} ${CHARSET_LIB})
 endif()
