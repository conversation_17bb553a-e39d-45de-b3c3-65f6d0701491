{"name": "parallelstl", "version": "20200330", "port-version": 3, "description": "Parallel STL is an implementation of the C++ standard library algorithms with support for execution policies, as specified in ISO/IEC 14882:2017 standard, commonly called C++17.", "homepage": "https://github.com/intel/parallelstl", "dependencies": ["tbb", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}]}