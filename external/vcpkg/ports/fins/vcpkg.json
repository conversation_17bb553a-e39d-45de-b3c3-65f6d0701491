{"$schema": "https://raw.githubusercontent.com/microsoft/vcpkg-tool/main/docs/vcpkg.schema.json", "name": "fins", "version-date": "2023-07-31", "maintainers": "<PERSON><PERSON><PERSON>", "description": "LibFINS is an MIT licensed library written in C implementing the FINS/TCP protocol used for communication between Omron PLCs. The library contains all the documented client FINS functions. This open source library is a spin-off of a larger project which also implements FINS/UDP client communication and a FINS server engine.", "homepage": "https://www.libfins.org", "documentation": "https://www.libfins.org/api-reference/", "license": "MIT", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}]}