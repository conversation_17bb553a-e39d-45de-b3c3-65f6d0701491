{"$comment": "Automatically generated by scripts/boost/generate-ports.ps1", "name": "boost-property-tree", "version": "1.87.0", "description": "Boost property_tree module", "homepage": "https://www.boost.org/libs/property_tree", "license": "BSL-1.0", "dependencies": [{"name": "boost-any", "version>=": "1.87.0"}, {"name": "boost-assert", "version>=": "1.87.0"}, {"name": "boost-bind", "version>=": "1.87.0"}, {"name": "boost-cmake", "version>=": "1.87.0"}, {"name": "boost-config", "version>=": "1.87.0"}, {"name": "boost-core", "version>=": "1.87.0"}, {"name": "boost-headers", "version>=": "1.87.0"}, {"name": "boost-iterator", "version>=": "1.87.0"}, {"name": "boost-mpl", "version>=": "1.87.0"}, {"name": "boost-multi-index", "version>=": "1.87.0"}, {"name": "boost-optional", "version>=": "1.87.0"}, {"name": "boost-range", "version>=": "1.87.0"}, {"name": "boost-serialization", "version>=": "1.87.0"}, {"name": "boost-static-assert", "version>=": "1.87.0"}, {"name": "boost-throw-exception", "version>=": "1.87.0"}, {"name": "boost-type-traits", "version>=": "1.87.0"}]}