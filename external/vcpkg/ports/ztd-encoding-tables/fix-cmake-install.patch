diff --git a/CMakeLists.txt b/CMakeLists.txt
index 7612c6c..8d2233d 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -50,6 +50,8 @@ project(ztd.encoding_tables
 	HOMEPAGE_URL "https://ztdencoding_tables.rtfd.io/"
 	LANGUAGES CXX C)
 
+include(CMakePackageConfigHelpers)
+include(GNUInstallDirs)
 if(ZTD_ENCODING_TABLES_READTHEDOCS)
 	# ReadTheDocs seems unable to handle the include at the project level: something must be going wrong?
 	include(CheckCXXCompilerFlag)
@@ -127,10 +129,7 @@ if(ZTD_ENCODING_TABLES_IS_TOP_LEVEL_PROJECT)
 endif()
 
 # # Main Dependency
-FetchContent_Declare(ztd.idk
-	GIT_REPOSITORY https://github.com/soasis/idk.git
-	GIT_TAG main)
-FetchContent_MakeAvailable(ztd.idk)
+find_package(ztd.idk CONFIG REQUIRED)
 
 # # Main library declarations
 # ztd.encoding_tables
@@ -179,7 +178,9 @@ if(ZTD_ENCODING_TABLES_IS_TOP_LEVEL_PROJECT)
 	)
 endif()
 
-install(TARGETS ztd.encoding_tables)
+install(TARGETS ztd.encoding_tables
+        EXPORT ztd.encoding_tables-targets
+        DESTINATION lib)
 install(DIRECTORY include/
 	DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}
 )
@@ -199,9 +200,15 @@ export(TARGETS ztd.encoding_tables
 	"${CMAKE_CURRENT_BINARY_DIR}/cmake/ztd.encoding_tables/ztd.encoding_tables-targets.cmake"
 )
 
-install(
-	DIRECTORY "${CMAKE_CURRENT_BINARY_DIR}/cmake"
-	TYPE DATA
+install(FILES
+  ${CMAKE_CURRENT_BINARY_DIR}/cmake/ztd.encoding_tables/ztd.encoding_tables-config.cmake
+  ${CMAKE_CURRENT_BINARY_DIR}/cmake/ztd.encoding_tables/ztd.encoding_tables-config-version.cmake
+  DESTINATION share/ztd.encoding_tables
+  )
+
+install(EXPORT ztd.encoding_tables-targets
+  FILE ztd.encoding_tables-targets.cmake
+  DESTINATION share/ztd.encoding_tables
 )
 
 if(ZTD_ENCODING_TABLES_GENERATE_SINGLE)
diff --git a/cmake/ztd.encoding_tables-config.cmake.in b/cmake/ztd.encoding_tables-config.cmake.in
index 2b8db83..579f865 100644
--- a/cmake/ztd.encoding_tables-config.cmake.in
+++ b/cmake/ztd.encoding_tables-config.cmake.in
@@ -1,6 +1,7 @@
 @PACKAGE_INIT@
 
-find_package(ztd.encoding_tables CONFIG REQUIRED)
+include(CMakeFindDependencyMacro)
+find_dependency(ztd.idk CONFIG)
 include(${CMAKE_CURRENT_LIST_DIR}/ztd.encoding_tables-targets.cmake)
 
 if (TARGET ztd.encoding_tables)
