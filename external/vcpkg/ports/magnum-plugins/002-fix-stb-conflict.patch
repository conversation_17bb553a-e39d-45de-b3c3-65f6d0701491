diff --git a/src/MagnumPlugins/StbImageImporter/StbImageImporter.cpp b/src/MagnumPlugins/StbImageImporter/StbImageImporter.cpp
index c24a968..1a38162 100644
--- a/src/MagnumPlugins/StbImageImporter/StbImageImporter.cpp
+++ b/src/MagnumPlugins/StbImageImporter/StbImageImporter.cpp
@@ -45,7 +45,7 @@
 #endif
 
 /* Not defining malloc/free, because there's no equivalent for realloc in C++ */
-#include "stb_image.h"
+#include "external/stb/stb_image.h"
 
 namespace Magnum { namespace Trade {
 
