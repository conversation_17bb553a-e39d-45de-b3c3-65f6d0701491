{"name": "magnum-plugins", "version-string": "2020.06", "port-version": 13, "description": "Plugins for magnum, C++11/C++14 graphics middleware for games and data visualization", "homepage": "https://magnum.graphics/", "license": null, "dependencies": [{"name": "magnum", "default-features": false}, {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "default-features": ["ddsimporter", "icoimporter", "miniexrimageconverter", "opengeximporter", "stanfordimporter", "stanfordsceneconverter", "stbimageconverter", "stbimageimporter", "stlimporter"], "features": {"assimpimporter": {"description": "AssimpImporter plugin", "dependencies": ["assimp", {"name": "magnum", "default-features": false, "features": ["anyimageimporter"]}, {"name": "magnum", "default-features": false, "features": ["trade"]}]}, "basisimageconverter": {"description": "BasisImageConverter plugin", "dependencies": [{"name": "magnum", "default-features": false, "features": ["trade"]}, "zstd"]}, "basisimporter": {"description": "BasisImporter plugin", "dependencies": [{"name": "magnum", "default-features": false, "features": ["trade"]}, "zstd"]}, "cgltfimporter": {"description": "CgltfImporter plugin", "dependencies": [{"name": "magnum", "default-features": false, "features": ["anyimageimporter"]}, {"name": "magnum", "default-features": false, "features": ["trade"]}]}, "ddsimporter": {"description": "DdsImporter plugin", "dependencies": [{"name": "magnum", "default-features": false, "features": ["trade"]}]}, "devilimageimporter": {"description": "DevIlImageImporter plugin", "dependencies": ["devil", {"name": "magnum", "default-features": false, "features": ["trade"]}]}, "drflacaudioimporter": {"description": "DrFlacAudioImporter plugin", "dependencies": [{"name": "magnum", "default-features": false, "features": ["audio"]}]}, "drmp3audioimporter": {"description": "DrMp3AudioImporter plugin", "dependencies": [{"name": "magnum", "default-features": false, "features": ["audio"]}]}, "drwavaudioimporter": {"description": "DrWavAudioImporter plugin", "dependencies": [{"name": "magnum", "default-features": false, "features": ["audio"]}]}, "freetypefont": {"description": "FreeTypeFont plugin", "dependencies": ["freetype", {"name": "magnum", "default-features": false, "features": ["text"]}]}, "glslangshaderconverter": {"description": "GlslangShaderConverter plugin", "dependencies": ["glslang", {"name": "magnum", "default-features": false, "features": ["shadertools"]}]}, "harfbuzzfont": {"description": "HarfBuzzFont plugin", "dependencies": ["harfbuzz", {"name": "magnum-plugins", "default-features": false, "features": ["freetypefont"]}]}, "icoimporter": {"description": "IcoImporter plugin", "dependencies": [{"name": "magnum", "default-features": false, "features": ["trade"]}]}, "jpegimageconverter": {"description": "JpegImageConverter plugin", "dependencies": ["libjpeg-turbo", {"name": "magnum", "default-features": false, "features": ["trade"]}]}, "jpegimporter": {"description": "JpegImporter plugin", "dependencies": ["libjpeg-turbo", {"name": "magnum", "default-features": false, "features": ["trade"]}]}, "ktximageconverter": {"description": "KtxImageConverter plugin", "dependencies": [{"name": "magnum", "default-features": false, "features": ["trade"]}]}, "ktximporter": {"description": "KtxImporter plugin", "dependencies": [{"name": "magnum", "default-features": false, "features": ["trade"]}]}, "meshoptimizersceneconverter": {"description": "MeshOptimizerSceneConverter plugin", "dependencies": [{"name": "magnum", "default-features": false, "features": ["trade"]}, "meshoptimizer"]}, "miniexrimageconverter": {"description": "MiniExrImageConverter plugin", "dependencies": [{"name": "magnum", "default-features": false, "features": ["trade"]}]}, "openddl": {"description": "OpenDdl library"}, "openexrimageconverter": {"description": "OpenExrImageConverter plugin", "dependencies": [{"name": "magnum", "default-features": false, "features": ["trade"]}, "openexr"]}, "openexrimporter": {"description": "OpenExrImporter plugin", "dependencies": [{"name": "magnum", "default-features": false, "features": ["trade"]}, "openexr"]}, "opengeximporter": {"description": "OpenGexImporter plugin", "dependencies": [{"name": "magnum", "default-features": false, "features": ["anyimageimporter"]}, {"name": "magnum", "default-features": false, "features": ["trade"]}, {"name": "magnum-plugins", "default-features": false, "features": ["openddl"]}]}, "pngimageconverter": {"description": "PngImageConverter plugin", "dependencies": ["libpng", {"name": "magnum", "default-features": false, "features": ["trade"]}]}, "pngimporter": {"description": "PngImporter plugin", "dependencies": ["libpng", {"name": "magnum", "default-features": false, "features": ["trade"]}]}, "spirvtoolsshaderconverter": {"description": "SpirvToolsShaderConverter plugin", "dependencies": [{"name": "magnum", "default-features": false, "features": ["shadertools"]}, "spirv-tools"]}, "stanfordimporter": {"description": "StanfordImporter plugin", "dependencies": [{"name": "magnum", "default-features": false, "features": ["trade"]}]}, "stanfordsceneconverter": {"description": "StanfordSceneConverter plugin", "dependencies": [{"name": "magnum", "default-features": false, "features": ["trade"]}]}, "stbdxtimageconverter": {"description": "StbDxtImageConverter plugin", "dependencies": [{"name": "magnum", "default-features": false, "features": ["trade"]}]}, "stbimageconverter": {"description": "StbImageConverter plugin", "dependencies": [{"name": "magnum", "default-features": false, "features": ["trade"]}]}, "stbimageimporter": {"description": "StbImageImporter plugin", "dependencies": [{"name": "magnum", "default-features": false, "features": ["trade"]}]}, "stbtruetypefont": {"description": "StbTrueTypeFont plugin", "dependencies": [{"name": "magnum", "default-features": false, "features": ["text"]}]}, "stbvorbisaudioimporter": {"description": "StbVorbisAudioImporter plugin", "dependencies": [{"name": "magnum", "default-features": false, "features": ["audio"]}]}, "stlimporter": {"description": "StlImporter plugin", "dependencies": [{"name": "magnum", "default-features": false, "features": ["trade"]}]}, "tinygltfimporter": {"description": "TinyGltfImporter plugin", "dependencies": [{"name": "magnum", "default-features": false, "features": ["anyimageimporter"]}, {"name": "magnum", "default-features": false, "features": ["trade"]}]}}}