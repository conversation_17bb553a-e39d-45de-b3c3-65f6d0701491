{"name": "ktx", "version-semver": "4.3.2", "port-version": 1, "description": ["The Khronos KTX library and tools.", "Functions for writing and reading KTX files, and instantiating OpenGL®, OpenGL ES™️ and Vulkan® textures from them."], "homepage": "https://github.com/KhronosGroup/KTX-Software", "license": null, "supports": "arm64 | x64 | !windows", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}, "zstd"], "features": {"tools": {"description": "Build tools", "supports": "!android & !uwp", "dependencies": ["cxxopts", "fmt"]}, "vulkan": {"description": "Build Vulkan support", "supports": "!emscripten"}}}