diff --git a/CMakeLists.txt b/CMakeLists.txt
index 9a56491..d7ca937 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -345,7 +345,6 @@ set(KTX_MAIN_SRC
     lib/basisu/transcoder/basisu_transcoder.cpp
     lib/basisu/transcoder/basisu_transcoder.h
     lib/basisu/transcoder/basisu.h
-    lib/basisu/zstd/zstd.c
     lib/checkheader.c
     lib/dfdutils/createdfd.c
     lib/dfdutils/colourspaces.c
@@ -532,7 +531,6 @@ macro(common_libktx_settings target enable_write library_type)
         $<INSTALL_INTERFACE:lib/basisu/transcoder>
 
         $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/lib/basisu/zstd>
-        $<INSTALL_INTERFACE:lib/basisu/zstd>
 
         $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/utils>
         $<INSTALL_INTERFACE:utils>
@@ -627,6 +625,11 @@ macro(common_libktx_settings target enable_write library_type)
         target_compile_definitions(${target} PUBLIC KTX_FEATURE_KTX2)
     endif()
 
+    # Use vcpkg zstd
+    find_package(zstd CONFIG REQUIRED)
+    set(ZSTD_LIBRARIES "$<IF:$<TARGET_EXISTS:zstd::libzstd_shared>,zstd::libzstd_shared,zstd::libzstd_static>")
+    target_link_libraries(${target} PRIVATE ${ZSTD_LIBRARIES})
+
     if(WIN32)
         if(MINGW)
             # Check if the Threads package is provided; if using Mingw it MIGHT be
diff --git a/cmake/KtxConfig.cmake b/cmake/KtxConfig.cmake
index 6386ba2..537bf4f 100644
--- a/cmake/KtxConfig.cmake
+++ b/cmake/KtxConfig.cmake
@@ -1,7 +1,8 @@
 # Copyright 2015-2020 The Khronos Group Inc.
 # SPDX-License-Identifier: Apache-2.0
 
-# include(CMakeFindDependencyMacro)
-# find_dependency()
+include(CMakeFindDependencyMacro)
+find_dependency(Threads)
+find_dependency(zstd CONFIG)
 
 include("${CMAKE_CURRENT_LIST_DIR}/KtxTargets.cmake")
diff --git a/lib/basisu/CMakeLists.txt b/lib/basisu/CMakeLists.txt
index 492233a..152ceb5 100644
--- a/lib/basisu/CMakeLists.txt
+++ b/lib/basisu/CMakeLists.txt
@@ -145,9 +145,6 @@ set(BASISU_SRC_LIST ${COMMON_SRC_LIST}
 	transcoder/basisu_transcoder.cpp
 	)
 
-if (ZSTD)
-	set(BASISU_SRC_LIST ${BASISU_SRC_LIST} zstd/zstd.c)
-endif()
 
 if (APPLE)
    set(BIN_DIRECTORY "bin_osx")
@@ -165,6 +162,10 @@ else()
 	target_compile_definitions(basisu PRIVATE BASISD_SUPPORT_KTX2_ZSTD=0)
 endif()
 
+if(ZSTD_LIBRARIES)
+    target_link_libraries(basisu ${ZSTD_LIBRARIES})
+endif()
+
 if (NOT MSVC)
 	# For Non-Windows builds, let cmake try and find the system OpenCL headers/libs for us.
 	if (OPENCL_FOUND)
diff --git a/lib/basisu/webgl/encoder/CMakeLists.txt b/lib/basisu/webgl/encoder/CMakeLists.txt
index 588d91b..a337b13 100644
--- a/lib/basisu/webgl/encoder/CMakeLists.txt
+++ b/lib/basisu/webgl/encoder/CMakeLists.txt
@@ -34,9 +34,6 @@ if (EMSCRIPTEN)
   )
 
   if (KTX2_ZSTANDARD)
-  	set(SRC_LIST ${SRC_LIST}
-		../../zstd/zstd.c
-	)
 	set(ZSTD_DEFINITION BASISD_SUPPORT_KTX2_ZSTD=1)
   else()
   	set(ZSTD_DEFINITION BASISD_SUPPORT_KTX2_ZSTD=0)
@@ -55,6 +52,10 @@ if (EMSCRIPTEN)
   target_compile_options(basis_encoder.js PRIVATE -fno-strict-aliasing -O3)
   
   target_include_directories(basis_encoder.js PRIVATE ../../transcoder)
+  
+  if(ZSTD_LIBRARIES)
+      target_link_libraries(basis_encoder.js ${ZSTD_LIBRARIES})
+  endif()
 
   set_target_properties(basis_encoder.js PROPERTIES
       OUTPUT_NAME "basis_encoder"
diff --git a/lib/basisu/webgl/transcoder/CMakeLists.txt b/lib/basisu/webgl/transcoder/CMakeLists.txt
index 372653d..5ebc3cf 100644
--- a/lib/basisu/webgl/transcoder/CMakeLists.txt
+++ b/lib/basisu/webgl/transcoder/CMakeLists.txt
@@ -28,9 +28,6 @@ if (EMSCRIPTEN)
   endif()
   
   if (KTX2_ZSTANDARD)
- 	set(SRC_LIST ${SRC_LIST}
-		../../zstd/zstddeclib.c
-	)
 	set(ZSTD_DEFINITION BASISD_SUPPORT_KTX2_ZSTD=1)
   else()
   	set(ZSTD_DEFINITION BASISD_SUPPORT_KTX2_ZSTD=0)
@@ -44,6 +41,10 @@ if (EMSCRIPTEN)
   target_compile_definitions(basis_transcoder.js PRIVATE NDEBUG BASISD_SUPPORT_UASTC=1 BASISD_SUPPORT_BC7=1 BASISD_SUPPORT_ATC=0 BASISD_SUPPORT_ASTC_HIGHER_OPAQUE_QUALITY=0 BASISD_SUPPORT_PVRTC2=0 BASISD_SUPPORT_FXT1=0 BASISD_SUPPORT_ETC2_EAC_RG11=0 BASISU_SUPPORT_ENCODING=0 ${KTX2_DEFINITION} ${ZSTD_DEFINITION} )
   target_compile_options(basis_transcoder.js PRIVATE -O3 -fno-strict-aliasing)
   target_include_directories(basis_transcoder.js PRIVATE ../../transcoder)
+  
+  if(ZSTD_LIBRARIES)
+      target_link_libraries(basis_transcoder.js ${ZSTD_LIBRARIES})
+  endif()
 
   set_target_properties(basis_transcoder.js PROPERTIES
       OUTPUT_NAME "basis_transcoder"
