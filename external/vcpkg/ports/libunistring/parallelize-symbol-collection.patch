diff --git a/lib/Makefile.am b/lib/Makefile.am
index 382d2bc..d9f0144 100644
--- a/lib/Makefile.am
+++ b/lib/Makefile.am
@@ -274,6 +274,25 @@ endif
 # But don't hide symbols that start with "libintl_locale", for the reason
 # stated in localename-table.h.
 all check install: config.h
+%_exported:
+	@d=`echo "$@" | sed -e 's,/[^/]*$$,,'`; \
+	test "$$d" = "$@" || mkdir -p "$$d" ; \
+	true >"$@" || exit 1 ; \
+	f=`echo "$@" | sed -e 's,_exported$$,,'`; \
+	case $$f in \
+	  *.res.lo ) ;; \
+	  *.c | *.$(OBJEXT) | *.lo ) \
+	    sf=`echo "$$f" | sed -e 's,\\.[^.]*$$,,'`.c; \
+	    test -f $$sf || sf=$(srcdir)/$$sf; \
+	    of=`echo "$$f" | sed -e 's,^.*/,,' -e 's,\\.[^.]*$$,,'`.$(OBJEXT); \
+	    echo "$(COMPILE) -c $$sf && sh ./exported.sh $$of 1>>$@" ; \
+	    $(COMPILE) -c $$sf || exit 1; \
+	    sh ./exported.sh $$of 1>>"$@"; \
+	    rm -f $$of `echo "$$of" | sed -e 's,\\.$(OBJEXT)$$,.lo,'`; \
+	    ;; \
+	esac
+libunistring_la_EXPORTED = $(libunistring_la_SOURCES:%=%_exported) $(libunistring_la_LIBADD:%=%_exported)
+exported: $(libunistring_la_EXPORTED)
 config.h: $(BUILT_SOURCES) $(srcdir)/libunistring.sym
 	{ echo '/* DO NOT EDIT! GENERATED AUTOMATICALLY! */'; \
 	  : "Avoid double inclusion, to avoid a warning about redefinitions."; \
@@ -285,10 +304,14 @@ config.h: $(BUILT_SOURCES) $(srcdir)/libunistring.sym
 	  echo '#endif /* UNISTRING_CONFIG_H */'; \
 	} > config.h && \
 	if test -n "$(NAMESPACING)" && test -n "$(HAVE_GLOBAL_SYMBOL_PIPE)"; then \
+	  echo "Collecting symbols to be renamed"; \
+	  $(MAKE) exported && \
 	  { \
 	    { \
-	      for f in $(libunistring_la_SOURCES) $(libunistring_la_LIBADD); do \
+	      for f in $(libunistring_la_EXPORTED); do \
 	        case $$f in \
+	          *_exported ) cat $$f 1>&5; ;; \
+	          * ) ;; \
 	          *.res.lo ) ;; \
 	          *.c | *.$(OBJEXT) | *.lo ) \
 	            sf=`echo "$$f" | sed -e 's,\\.[^.]*$$,,'`.c; \
