{"name": "mesa", "version": "24.0.7", "port-version": 3, "description": "Mesa - The 3D Graphics Library", "homepage": "https://www.mesa3d.org/", "license": "MIT AND BSL-1.0 AND SGI-B-2.0", "supports": "!(windows & arm) & !staticcrt & !uwp", "dependencies": [{"name": "vcpkg-get-python-packages", "host": true}, {"name": "vcpkg-tool-meson", "host": true}, "zlib", "zstd"], "default-features": ["gles1", "gles2", {"name": "llvm", "platform": "x64"}], "features": {"egl": {"description": "Build support for EGL platform", "dependencies": ["egl-registry"]}, "gles1": {"description": "Build support for OpenGL ES 1.x", "dependencies": ["opengl"]}, "gles2": {"description": "Build support for OpenGL ES 2.x and 3.x", "dependencies": ["opengl"]}, "llvm": {"description": "Build with llvmpipe", "dependencies": [{"name": "llvm", "default-features": false, "features": ["enable-rtti", "libclc"]}, {"name": "llvm", "default-features": false, "features": ["target-arm"], "platform": "arm32"}, {"name": "llvm", "default-features": false, "features": ["target-aarch64"], "platform": "arm64"}, {"name": "llvm", "default-features": false, "features": ["target-x86"], "platform": "x64 | x86"}]}, "offscreen": {"description": "Build with support for offscreen rendering (OSMesa)", "dependencies": ["opengl"]}}}