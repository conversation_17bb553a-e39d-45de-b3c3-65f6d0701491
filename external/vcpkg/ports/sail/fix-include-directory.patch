diff --git a/CMakeLists.txt b/CMakeLists.txt
index ac81279..cfc2f3b 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -299,7 +299,7 @@ endif()
 # Common configuration file
 #
 configure_file("${PROJECT_SOURCE_DIR}/src/config.h.in" "${PROJECT_BINARY_DIR}/include/sail-common/config.h" @ONLY)
-install(FILES "${PROJECT_BINARY_DIR}/include/sail-common/config.h" DESTINATION include/sail/sail-common)
+install(FILES "${PROJECT_BINARY_DIR}/include/sail-common/config.h" DESTINATION include/sail-common)
 
 # Print configuration statistics
 #
diff --git a/src/bindings/sail-c++/CMakeLists.txt b/src/bindings/sail-c++/CMakeLists.txt
index 4b69ad4..f4bac29 100644
--- a/src/bindings/sail-c++/CMakeLists.txt
+++ b/src/bindings/sail-c++/CMakeLists.txt
@@ -131,7 +131,7 @@ install(TARGETS sail-c++
         ARCHIVE DESTINATION "${CMAKE_INSTALL_LIBDIR}"
         LIBRARY DESTINATION "${CMAKE_INSTALL_LIBDIR}"
         RUNTIME DESTINATION "${CMAKE_INSTALL_BINDIR}"
-        PUBLIC_HEADER DESTINATION "${CMAKE_INSTALL_INCLUDEDIR}/sail/sail-c++")
+        PUBLIC_HEADER DESTINATION "${CMAKE_INSTALL_INCLUDEDIR}/sail-c++")
 
 # Install development packages
 #
diff --git a/src/sail-common/CMakeLists.txt b/src/sail-common/CMakeLists.txt
index 06ce246..c8576e5 100644
--- a/src/sail-common/CMakeLists.txt
+++ b/src/sail-common/CMakeLists.txt
@@ -114,7 +114,7 @@ endif()
 
 target_include_directories(sail-common
                             PUBLIC $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/..>
-                                   $<INSTALL_INTERFACE:include/sail>)
+                                   $<INSTALL_INTERFACE:include>)
 
 # pkg-config integration
 #
@@ -129,7 +129,7 @@ install(TARGETS sail-common
         ARCHIVE DESTINATION "${CMAKE_INSTALL_LIBDIR}"
         LIBRARY DESTINATION "${CMAKE_INSTALL_LIBDIR}"
         RUNTIME DESTINATION "${CMAKE_INSTALL_BINDIR}"
-        PUBLIC_HEADER DESTINATION "${CMAKE_INSTALL_INCLUDEDIR}/sail/sail-common")
+        PUBLIC_HEADER DESTINATION "${CMAKE_INSTALL_INCLUDEDIR}/sail-common")
 
 # Install development packages
 #
diff --git a/src/sail-manip/CMakeLists.txt b/src/sail-manip/CMakeLists.txt
index 5740764..47b81bb 100644
--- a/src/sail-manip/CMakeLists.txt
+++ b/src/sail-manip/CMakeLists.txt
@@ -59,7 +59,7 @@ install(TARGETS sail-manip
         ARCHIVE DESTINATION "${CMAKE_INSTALL_LIBDIR}"
         LIBRARY DESTINATION "${CMAKE_INSTALL_LIBDIR}"
         RUNTIME DESTINATION "${CMAKE_INSTALL_BINDIR}"
-        PUBLIC_HEADER DESTINATION "${CMAKE_INSTALL_INCLUDEDIR}/sail/sail-manip")
+        PUBLIC_HEADER DESTINATION "${CMAKE_INSTALL_INCLUDEDIR}/sail-manip")
 
 # Install development packages
 #
diff --git a/src/sail/CMakeLists.txt b/src/sail/CMakeLists.txt
index 85590af..2303f63 100644
--- a/src/sail/CMakeLists.txt
+++ b/src/sail/CMakeLists.txt
@@ -118,11 +118,11 @@ install(TARGETS sail
         ARCHIVE DESTINATION "${CMAKE_INSTALL_LIBDIR}"
         LIBRARY DESTINATION "${CMAKE_INSTALL_LIBDIR}"
         RUNTIME DESTINATION "${CMAKE_INSTALL_BINDIR}"
-        PUBLIC_HEADER DESTINATION "${CMAKE_INSTALL_INCLUDEDIR}/sail/sail")
+        PUBLIC_HEADER DESTINATION "${CMAKE_INSTALL_INCLUDEDIR}/sail")
 
 # Install layouts for debugging codecs
 #
-install(DIRECTORY "${CMAKE_CURRENT_SOURCE_DIR}/layout/" DESTINATION "${CMAKE_INSTALL_INCLUDEDIR}/sail/sail/layout")
+install(DIRECTORY "${CMAKE_CURRENT_SOURCE_DIR}/layout/" DESTINATION "${CMAKE_INSTALL_INCLUDEDIR}/sail/layout")
 
 # Install development packages
 #
