{"name": "sail", "version-semver": "0.9.8", "description": "The missing small and fast image decoding library for humans (not for machines)", "homepage": "https://github.com/HappySeaFox/sail", "license": "MIT", "supports": "!uwp", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "default-features": ["highest-priority-codecs"], "features": {"all": {"description": "Enable all codecs", "dependencies": [{"name": "sail", "features": ["high-priority-codecs", "highest-priority-codecs", "low-priority-codecs", "lowest-priority-codecs", "medium-priority-codecs"]}]}, "avif": {"description": "Enable AVIF codec", "dependencies": ["liba<PERSON>f"]}, "bmp": {"description": "Enable BMP codec"}, "gif": {"description": "Enable GIF codec", "dependencies": ["gif<PERSON>b"]}, "high-priority-codecs": {"description": "Enable high priority codecs such as BMP", "dependencies": [{"name": "sail", "features": ["bmp", "svg"]}]}, "highest-priority-codecs": {"description": "Enable highest priority codecs such as JPEG or PNG", "dependencies": [{"name": "sail", "features": ["gif", "jpeg", "png", "tiff"]}]}, "ico": {"description": "Enable ICO codec"}, "jpeg": {"description": "Enable JPEG codec", "dependencies": ["libjpeg-turbo"]}, "jpeg2000": {"description": "Enable JPEG2000 codec", "dependencies": ["jasper"]}, "jpegxl": {"description": "Enable JPEG XL codec", "dependencies": ["libjxl"]}, "low-priority-codecs": {"description": "Enable low priority codecs such as TGA", "dependencies": [{"name": "sail", "features": ["ico", "pcx", "psd", "qoi", "tga"]}]}, "lowest-priority-codecs": {"description": "Enable lowest priority codecs such as XBM", "dependencies": [{"name": "sail", "features": ["wal", "xbm"]}]}, "medium-priority-codecs": {"description": "Enable medium priority codecs such as AVIF", "dependencies": [{"name": "sail", "features": ["avif", "jpeg2000", "jpegxl", "webp"]}]}, "openmp": {"description": "Enable OpenMP support"}, "pcx": {"description": "Enable PCX codec"}, "png": {"description": "Enable PNG codec", "dependencies": ["libpng"]}, "psd": {"description": "Enable PSD codec"}, "qoi": {"description": "Enable QOI codec"}, "svg": {"description": "Enable SVG codec", "dependencies": ["nanosvg"]}, "tga": {"description": "Enable TGA codec"}, "tiff": {"description": "Enable TIFF codec", "dependencies": [{"name": "tiff", "default-features": false}]}, "wal": {"description": "Enable WAL codec"}, "webp": {"description": "Enable WEBP codec", "dependencies": ["libwebp"]}, "xbm": {"description": "Enable XBM codec"}}}