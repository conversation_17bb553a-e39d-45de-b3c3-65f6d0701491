diff --git a/CMakeLists.txt b/CMakeLists.txt
index 65c4776..5d4086a 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -60,12 +60,12 @@ ENDIF()
 
 IF(CMAKE_BUILD_TYPE STREQUAL "Debug")
     SET(MY_CMAKE_FLAGS "${MY_CMAKE_FLAGS} -O0 -g3")
-    IF(CMAKE_C_COMPILER MATCHES "clang" AND
-                        NOT "$ENV{TRAVIS}" MATCHES "^true$" AND
-                        NOT "$ENV{EXTRA_CFLAGS}" MATCHES "-fsanitize")
-        SET(MY_CMAKE_FLAGS "${MY_CMAKE_FLAGS} -fsanitize=address")
-        SET(LIBS ${LIBS} -fsanitize=address)
-    ENDIF()
+    # IF(CMAKE_C_COMPILER MATCHES "clang" AND
+    #                     NOT "$ENV{TRAVIS}" MATCHES "^true$" AND
+    #                     NOT "$ENV{EXTRA_CFLAGS}" MATCHES "-fsanitize")
+    #     SET(MY_CMAKE_FLAGS "${MY_CMAKE_FLAGS} -fsanitize=address")
+    #     SET(LIBS ${LIBS} -fsanitize=address)
+    # ENDIF()
     # Uncomment to enable cleartext protocol mode (no crypto):
     #SET (MY_CMAKE_FLAGS "${MY_CMAKE_FLAGS} -DLSQUIC_ENABLE_HANDSHAKE_DISABLE=1")
 ELSE()
