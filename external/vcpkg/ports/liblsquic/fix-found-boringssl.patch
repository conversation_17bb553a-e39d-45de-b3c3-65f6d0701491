diff --git a/CMakeLists.txt b/CMakeLists.txt
index 5d4086a..e085a83 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -120,10 +120,12 @@ IF(CMAKE_BUILD_TYPE STREQUAL "Debug")
     SET(MY_CMAKE_FLAGS "${MY_CMAKE_FLAGS} -Od")
     #SET (MY_CMAKE_FLAGS "${MY_CMAKE_FLAGS} -DFIU_ENABLE=1")
     #SET(LIBS ${LIBS} fiu)
+    SET(LIB_NAME ssld cryptod)
 ELSE()
     SET(MY_CMAKE_FLAGS "${MY_CMAKE_FLAGS} -Ox")
     # Comment out the following line to compile out debug messages:
     #SET(MY_CMAKE_FLAGS "${MY_CMAKE_FLAGS} -DLSQUIC_LOWEST_LOG_LEVEL=LSQ_LOG_INFO")
+    SET(LIB_NAME ssl crypto)
 ENDIF()
 
 ENDIF() #MSVC
@@ -191,7 +193,7 @@ IF (NOT DEFINED BORINGSSL_LIB AND DEFINED BORINGSSL_DIR)
 ELSE()
 
 
-    FOREACH(LIB_NAME ssl crypto)
+    FOREACH(LIB ${LIB_NAME})
         # If BORINGSSL_LIB is defined, try find each lib. Otherwise, user should define BORINGSSL_LIB_ssl,
         # BORINGSSL_LIB_crypto and so on explicitly. For example, including boringssl and lsquic both via
         # add_subdirectory:
@@ -201,20 +203,20 @@ ELSE()
         #   add_subdirectory(third_party/lsquic)
         IF (DEFINED BORINGSSL_LIB)
             IF (CMAKE_SYSTEM_NAME STREQUAL Windows)
-                FIND_LIBRARY(BORINGSSL_LIB_${LIB_NAME}
-                    NAMES ${LIB_NAME}
+                FIND_LIBRARY(BORINGSSL_LIB_${LIB}
+                    NAMES ${LIB}
                     PATHS ${BORINGSSL_LIB}
                     PATH_SUFFIXES Debug Release MinSizeRel RelWithDebInfo
                     NO_DEFAULT_PATH)
             ELSE()
-                FIND_LIBRARY(BORINGSSL_LIB_${LIB_NAME}
-                    NAMES lib${LIB_NAME}${LIB_SUFFIX}
+                FIND_LIBRARY(BORINGSSL_LIB_${LIB}
+                    NAMES lib${LI}${LIB_SUFFIX}
                     PATHS ${BORINGSSL_LIB}
-                    PATH_SUFFIXES ${LIB_NAME}
+                    PATH_SUFFIXES ${LIB}
                     NO_DEFAULT_PATH)
             ENDIF()
         ENDIF()
-        IF(BORINGSSL_LIB_${LIB_NAME})
+        IF(BORINGSSL_LIB_${LIB})
             MESSAGE(STATUS "Found ${LIB_NAME} library: ${BORINGSSL_LIB_${LIB_NAME}}")
         ELSE()
             MESSAGE(FATAL_ERROR "BORINGSSL_LIB_${LIB_NAME} library not found")
