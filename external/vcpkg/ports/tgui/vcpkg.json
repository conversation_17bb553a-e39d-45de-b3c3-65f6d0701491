{"name": "tgui", "version": "1.8.0", "port-version": 2, "description": "TGUI is an easy to use, cross-platform, C++ GUI for SFML.", "homepage": "https://tgui.eu", "license": "<PERSON><PERSON><PERSON>", "dependencies": ["stb", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "default-features": ["sfml"], "features": {"raylib": {"description": "Build the RAYLIB backend", "dependencies": ["<PERSON><PERSON><PERSON>"]}, "sdl2": {"description": "Build the SDL backend", "dependencies": [{"name": "opengl", "platform": "!android & !ios"}, "sdl2", "sdl2-ttf"]}, "sdl3": {"description": "Build the SDL3 backend", "dependencies": [{"name": "opengl", "platform": "!android & !ios"}, "sdl3", "sdl3-ttf"]}, "sfml": {"description": "Build the SFML backend", "dependencies": [{"name": "sfml", "default-features": false, "features": ["graphics"]}]}, "tool": {"description": "Build GUI builder"}}}