diff --git a/CMake/Dependencies.cmake b/CMake/Dependencies.cmake
index 21590f4..4b46a29 100644
--- a/CMake/Dependencies.cmake
+++ b/CMake/Dependencies.cmake
@@ -210,11 +210,14 @@ endif()
 #######################################################################
 
 # Find FreeImage
-find_package(FreeImage)
+find_package(FreeImage NAMES freeimage)
+set(FreeImage_LIBRARIES freeimage::FreeImage)
+set(FreeImage_LIBRARY_DBG "-lFreeImaged")
+set(FreeImage_LIBRARY_REL "-lFreeImage")
 macro_log_feature(FreeImage_FOUND "freeimage" "Support for commonly used graphics image formats" "http://freeimage.sourceforge.net")
 
 # Find FreeType
-find_package(Freetype)
+find_package(FREETYPE NAMES freetype)
 macro_log_feature(FREETYPE_FOUND "freetype" "Portable font engine" "http://www.freetype.org")
 
 # Find X11
@@ -281,13 +284,17 @@ endif ()
 macro_log_feature(ENV{VULKAN_SDK} "Vulkan SDK" "Vulkan RenderSystem, glslang Plugin. Alternatively use system packages" "https://vulkan.lunarg.com/")
 
 # OpenEXR
-find_package(OpenEXR)
+find_package(IMath CONFIG)
+find_package(OPENEXR NAMES OpenEXR)
+set(OPENEXR_LIBRARIES OpenEXR::OpenEXR Imath::Imath)
 macro_log_feature(OPENEXR_FOUND "OpenEXR" "Load High dynamic range images" "http://www.openexr.com/")
 
 # Python
 set(Python_ADDITIONAL_VERSIONS 3.4) # allows using python3 on Ubuntu 14.04
-find_package(PythonInterp)
-find_package(PythonLibs)
+find_package(Python3 COMPONENTS Development Interpreter) # Interpreter is needed for Python3_FOUND
+set(PYTHONLIBS_FOUND "${Python3_FOUND}")
+set(PYTHON_INCLUDE_PATH "${Python3_INCLUDE_DIRS}")
+set(PYTHON_LIBRARIES "${Python3_LIBRARIES}")
 macro_log_feature(PYTHONLIBS_FOUND "Python" "Language bindings to use OGRE from Python" "http://www.python.org/")
 
 # SWIG
@@ -295,7 +302,7 @@ find_package(SWIG 3.0.8 QUIET)
 macro_log_feature(SWIG_FOUND "SWIG" "Language bindings (Python, Java, C#) for OGRE" "http://www.swig.org/")
 
 # pugixml
-find_package(pugixml QUIET)
+find_package(pugixml CONFIG REQUIRED)
 macro_log_feature(pugixml_FOUND "pugixml" "Needed for XMLConverter and DotScene Plugin" "https://pugixml.org/")
 
 # Find zlib
@@ -303,7 +310,7 @@ find_package(ZLIB)
 macro_log_feature(ZLIB_FOUND "zlib" "Simple data compression library" "http://www.zlib.net")
 
 # Assimp
-find_package(assimp QUIET)
+find_package(assimp CONFIG)
 macro_log_feature(assimp_FOUND "Assimp" "Needed for the AssimpLoader Plugin" "https://www.assimp.org/")
 
 # Bullet
@@ -311,6 +318,8 @@ find_package(Bullet QUIET)
 macro_log_feature(BULLET_FOUND "Bullet" "Bullet physics" "https://pybullet.org")
 
 if(assimp_FOUND)
+  add_library(fix::assimp ALIAS assimp::assimp)
+elseif(0)
   # workaround horribly broken assimp cmake, fixed with assimp 5.1
   add_library(fix::assimp INTERFACE IMPORTED)
   set_target_properties(fix::assimp PROPERTIES
@@ -329,7 +338,7 @@ endif()
 # Find sdl2
 if(NOT ANDROID AND NOT EMSCRIPTEN)
   # find script does not work in cross compilation environment
-  find_package(SDL2 QUIET)
+  find_package(SDL2 CONFIG REQUIRED)
   macro_log_feature(SDL2_FOUND "SDL2" "Simple DirectMedia Library needed for input handling in samples" "https://www.libsdl.org/")
   if(SDL2_FOUND AND NOT TARGET SDL2::SDL2)
     add_library(SDL2::SDL2 INTERFACE IMPORTED)
diff --git a/CMake/Templates/OGREConfig.cmake.in b/CMake/Templates/OGREConfig.cmake.in
index 2047f66..a5c7cd0 100644
--- a/CMake/Templates/OGREConfig.cmake.in
+++ b/CMake/Templates/OGREConfig.cmake.in
@@ -35,6 +35,25 @@ set(OGRE_LIBRARIES)
 cmake_policy(PUSH)
 cmake_policy(SET CMP0012 NEW)
 
+include(CMakeFindDependencyMacro)
+find_dependency(pugixml CONFIG)
+find_dependency(SDL2 CONFIG)
+find_dependency(ZLIB)
+find_dependency(freetype CONFIG)
+if (@OGRE_BUILD_COMPONENT_OVERLAY_IMGUI@)
+    find_dependency(imgui CONFIG)
+endif()
+if(@OGRE_BUILD_PLUGIN_ASSIMP@)
+    find_dependency(assimp CONFIG)
+endif()
+if(@OGRE_BUILD_PLUGIN_FREEIMAGE@)
+    find_dependency(freeimage CONFIG)
+endif()
+if(@OGRE_BUILD_PLUGIN_EXRCODEC@)
+    find_dependency(Imath CONFIG)
+    find_dependency(OpenEXR CONFIG)
+endif()
+
 if(@OGRE_THREAD_PROVIDER@ EQUAL 1)
     list(APPEND OGRE_INCLUDE_DIRS @Boost_INCLUDE_DIRS@)
 endif()
diff --git a/Components/Bites/CMakeLists.txt b/Components/Bites/CMakeLists.txt
index 9e990b8..16c0ae4 100644
--- a/Components/Bites/CMakeLists.txt
+++ b/Components/Bites/CMakeLists.txt
@@ -185,6 +185,12 @@ elseif(NOT EMSCRIPTEN)
   message(WARNING "SDL2 not found - no input handling and reduced window creation capabilites")
 endif()
 
+if(OGRE_BUILD_COMPONENT_OVERLAY_IMGUI)
+  find_package(imgui CONFIG REQUIRED)
+  find_path(IMGUI_DIR NAMES imgui.h)
+  target_link_libraries(OgreBites PRIVATE imgui::imgui)
+endif()
+
 generate_export_header(OgreBites 
     EXPORT_MACRO_NAME _OgreBitesExport
     EXPORT_FILE_NAME ${PROJECT_BINARY_DIR}/include/OgreBitesPrerequisites.h)
diff --git a/Components/Bites/src/OgreImGuiInputListener.cpp b/Components/Bites/src/OgreImGuiInputListener.cpp
index 3cb2379..5629bb5 100644
--- a/Components/Bites/src/OgreImGuiInputListener.cpp
+++ b/Components/Bites/src/OgreImGuiInputListener.cpp
@@ -116,7 +116,7 @@ static bool keyEvent(const KeyboardEvent& arg)
     if (key == ImGuiKey_None)
         return io.WantCaptureKeyboard;
 
-    io.AddKeyEvent(ImGuiKey(key), arg.type == OgreBites::KEYDOWN);
+    io.AddKeyEvent(static_cast<ImGuiKey>(key), arg.type == OgreBites::KEYDOWN);
     return io.WantCaptureKeyboard;
 }
 
diff --git a/Components/Overlay/CMakeLists.txt b/Components/Overlay/CMakeLists.txt
index 41bd634..87f1406 100644
--- a/Components/Overlay/CMakeLists.txt
+++ b/Components/Overlay/CMakeLists.txt
@@ -19,6 +19,8 @@ list(APPEND HEADER_FILES
 file(GLOB SOURCE_FILES "${CMAKE_CURRENT_SOURCE_DIR}/src/*.cpp")
 
 if(OGRE_BUILD_COMPONENT_OVERLAY_IMGUI)
+  find_package(imgui CONFIG REQUIRED)
+elseif(0)
   set(IMGUI_DIR "${PROJECT_BINARY_DIR}/imgui-1.91.2" CACHE PATH "")
   if(NOT EXISTS ${IMGUI_DIR})
     message(STATUS "Downloading imgui")
@@ -63,6 +65,8 @@ elseif(UNIX)
 endif()
 
 if(OGRE_BUILD_COMPONENT_OVERLAY_IMGUI)
+  target_link_libraries(OgreOverlay PUBLIC OgreMain PRIVATE imgui::imgui)
+elseif(0)
   target_include_directories(OgreOverlay PUBLIC
     PUBLIC "$<BUILD_INTERFACE:${IMGUI_DIR}>"
     PRIVATE "$<BUILD_INTERFACE:${IMGUI_DIR}/misc/freetype>")
diff --git a/PlugIns/EXRCodec/src/OgreEXRCodec.cpp b/PlugIns/EXRCodec/src/OgreEXRCodec.cpp
index efd4b32..1e3ea23 100644
--- a/PlugIns/EXRCodec/src/OgreEXRCodec.cpp
+++ b/PlugIns/EXRCodec/src/OgreEXRCodec.cpp
@@ -36,6 +36,9 @@ THE SOFTWARE.
 #include "O_IStream.h"
 
 #include <cmath>
+#include <ImathBox.h>
+#include <ImfFrameBuffer.h>
+#include <ImfHeader.h>
 #include <ImfOutputFile.h>
 #include <ImfInputFile.h>
 #include <ImfChannelList.h>
diff --git a/PlugIns/STBICodec/CMakeLists.txt b/PlugIns/STBICodec/CMakeLists.txt
index 10283f5..e7edfd3 100644
--- a/PlugIns/STBICodec/CMakeLists.txt
+++ b/PlugIns/STBICodec/CMakeLists.txt
@@ -19,8 +19,10 @@ endif()
 add_library(Codec_STBI ${OGRE_LIB_TYPE} ${HEADER_FILES} ${SOURCES})
 target_link_libraries(Codec_STBI PUBLIC OgreMain)
 
+find_path(STB_INCLUDE_DIRS "stb_image.h")
 target_include_directories(Codec_STBI PUBLIC 
   "$<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>"
+  "$<BUILD_INTERFACE:${STB_INCLUDE_DIRS}>"
   $<INSTALL_INTERFACE:include/OGRE/Plugins/STBICodec>)
 
 if(CMAKE_COMPILER_IS_GNUCXX)
diff --git a/PlugIns/STBICodec/src/OgreSTBICodec.cpp b/PlugIns/STBICodec/src/OgreSTBICodec.cpp
index c5a4d4b..df648d7 100644
--- a/PlugIns/STBICodec/src/OgreSTBICodec.cpp
+++ b/PlugIns/STBICodec/src/OgreSTBICodec.cpp
@@ -40,7 +40,7 @@ THE SOFTWARE.
 #define STBI_NO_STDIO
 #define STB_IMAGE_IMPLEMENTATION
 #define STB_IMAGE_STATIC
-#include "stbi/stb_image.h"
+#include "stb_image.h"
 
 #ifdef HAVE_ZLIB
 #include <zlib.h>
@@ -63,7 +63,7 @@ static Ogre::uchar* custom_zlib_compress(Ogre::uchar* data, int data_len, int* o
 
 #define STB_IMAGE_WRITE_IMPLEMENTATION
 #define STBI_WRITE_NO_STDIO
-#include "stbi/stb_image_write.h"
+#include "stb_image_write.h"
 
 namespace Ogre {
 
@@ -74,7 +74,7 @@ namespace Ogre {
         stbi_convert_iphone_png_to_rgb(1);
         stbi_set_unpremultiply_on_load(1);
 
-        LogManager::getSingleton().logMessage("stb_image - v2.30 - public domain image loader");
+        LogManager::getSingleton().logMessage("stb_image - public domain image loader");
         
         // Register codecs
         String exts = "jpeg,jpg,png,bmp,psd,tga,gif,pic,ppm,pgm,hdr";
