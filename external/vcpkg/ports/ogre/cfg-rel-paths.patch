diff --git a/CMake/InstallResources.cmake b/CMake/InstallResources.cmake
index 09789ce..3d918e4 100644
--- a/CMake/InstallResources.cmake
+++ b/CMake/InstallResources.cmake
@@ -61,7 +61,6 @@ elseif (APPLE)
   endif()
   # these are resolved relative to the app bundle
   set(OGRE_SAMPLES_DIR_REL "Contents/Plugins/")
-  set(OGRE_CFG_INSTALL_PATH "bin")
 elseif (UNIX)
   set(OGRE_TEST_MEDIA_DIR_REL "${CMAKE_INSTALL_PREFIX}/Tests/Media")
   set(OGRE_SAMPLES_DIR_REL "${CMAKE_INSTALL_PREFIX}/${OGRE_LIB_DIRECTORY}/OGRE/Samples")
@@ -155,6 +154,10 @@ endif()
 
 set(OGRE_SAMPLE_RESOURCES "")
 
+set(OGRE_PLUGIN_DIR_REL "${CMAKE_INSTALL_PREFIX}/${OGRE_PLUGINS_PATH}")
+cmake_path(RELATIVE_PATH OGRE_PLUGIN_DIR_REL BASE_DIRECTORY "${CMAKE_INSTALL_PREFIX}/${OGRE_CFG_INSTALL_PATH}")
+set(OGRE_MEDIA_DIR_REL "${CMAKE_INSTALL_PREFIX}/${OGRE_MEDIA_PATH}")
+cmake_path(RELATIVE_PATH OGRE_MEDIA_DIR_REL BASE_DIRECTORY "${CMAKE_INSTALL_PREFIX}/${OGRE_CFG_INSTALL_PATH}")
 set(OGRE_CORE_MEDIA_DIR "${OGRE_MEDIA_DIR_REL}")
 
 # CREATE CONFIG FILES - INSTALL VERSIONS
 
