diff --git a/CorrelationVector/CMakeLists.txt b/CorrelationVector/CMakeLists.txt
index 2b32f8b..2c3a0ec 100644
--- a/CorrelationVector/CMakeLists.txt
+++ b/CorrelationVector/CMakeLists.txt
@@ -9,7 +9,7 @@ include (CVOptions)
 include (CVHelpers)
 
 add_global_definitions ()
-set_global_compile_flags ()
+#set_global_compile_flags ()
 
 set(CORRELATION_VECTOR_VERSION_MAJOR 1)
 set(CORRELATION_VECTOR_VERSION_MINOR 0)
diff --git a/CorrelationVector/cmake/correlation_vector-config.in.cmake b/CorrelationVector/cmake/correlation_vector-config.in.cmake
index 6b389d5..9c4fb5a 100644
--- a/CorrelationVector/cmake/correlation_vector-config.in.cmake
+++ b/CorrelationVector/cmake/correlation_vector-config.in.cmake
@@ -1 +1,7 @@
+# Optional dependency for Linux
+if(UNIX)
+    include(CMakeFindDependencyMacro)
+    find_dependency(unofficial-libuuid)
+endif()
+
 include("${CMAKE_CURRENT_LIST_DIR}/correlation_vector-targets.cmake")
\ No newline at end of file
diff --git a/CorrelationVector/src/CMakeLists.txt b/CorrelationVector/src/CMakeLists.txt
index 00baa66..08f3fc9 100644
--- a/CorrelationVector/src/CMakeLists.txt
+++ b/CorrelationVector/src/CMakeLists.txt
@@ -15,16 +15,9 @@ else()
     if (WIN32)
         target_compile_definitions(${TARGETNAME} PUBLIC GUID_WINDOWS)
     elseif (UNIX)
-        # apt-get install pkg-config uuid-dev
-        find_package(PkgConfig REQUIRED)
-        # TODO: move to FindUUID module
-        pkg_check_modules(UUID uuid)
-        if (UUID_FOUND)
-            message("Found and using uuid.")
-            target_include_directories(${TARGETNAME} PUBLIC ${UUID_INCLUDE_DIRS})
-            target_link_libraries(${TARGETNAME} PRIVATE ${UUID_LIBRARIES})
-            target_compile_definitions(${TARGETNAME} PUBLIC GUID_LIBUUID)
-        endif()
+        find_package(unofficial-libuuid REQUIRED)
+        target_compile_definitions(${TARGETNAME} PUBLIC GUID_LIBUUID)
+        target_link_libraries(${TARGETNAME} PRIVATE unofficial::UUID::uuid)
     endif()
 endif()
 
