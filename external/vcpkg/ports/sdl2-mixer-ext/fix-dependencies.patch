diff --git a/SDL2_mixer_extConfig.cmake.in b/SDL2_mixer_extConfig.cmake.in
index 997d4e828150d10fa4113b3341220ca185fc29a5..2d781528151075385a2e837df3863900b8d36573 100644
--- a/SDL2_mixer_extConfig.cmake.in
+++ b/SDL2_mixer_extConfig.cmake.in
@@ -6,10 +6,58 @@ set_package_properties(SDL2_mixer_ext PROPERTIES
 
 @PACKAGE_INIT@
 
+set(SDL_MIXER_X_USE_OGG_VORBIS              @USE_OGG_VORBIS@)
+set(SDL_MIXER_X_USE_OPUS                    @USE_OPUS@)
+set(SDL_MIXER_X_USE_USE_FLAC                @USE_FLAC@)
+set(SDL_MIXER_X_USE_USE_WAVPACK             @USE_WAVPACK@)
+set(SDL_MIXER_X_USE_MP3_MPG123              @USE_MP3_MPG123@)
+set(SDL_MIXER_X_USE_XMP                     @USE_XMP@)
+set(SDL_MIXER_X_USE_MIDI_ADLMIDI            @USE_MIDI_ADLMIDI@)
+set(SDL_MIXER_X_USE_MIDI_OPNMIDI            @USE_MIDI_OPNMIDI@)
+set(SDL_MIXER_X_USE_MIDI_FLUIDSYNTH         @USE_MIDI_FLUIDSYNTH@)
+
 if(EXISTS "${CMAKE_CURRENT_LIST_DIR}/SDL2_mixer_ext-shared-targets.cmake")
     include("${CMAKE_CURRENT_LIST_DIR}/SDL2_mixer_ext-shared-targets.cmake")
 endif()
 
 if(EXISTS "${CMAKE_CURRENT_LIST_DIR}/SDL2_mixer_ext-static-targets.cmake")
+    include(CMakeFindDependencyMacro)
+
+    if(SDL_MIXER_X_USE_OGG_VORBIS AND NOT TARGET Vorbis::vorbisfile)
+        find_dependency(Vorbis CONFIG)
+    endif()
+
+    if(SDL_MIXER_X_USE_OPUS AND NOT TARGET OpusFile::opusfile)
+        find_dependency(OpusFile CONFIG)
+    endif()
+
+    if(SDL_MIXER_X_USE_USE_FLAC AND NOT TARGET FLAC::FLAC)
+        find_dependency(FLAC CONFIG)
+    endif()
+
+    if(SDL_MIXER_X_USE_USE_WAVPACK AND NOT TARGET WavPack::WavPack)
+        find_dependency(WavPack CONFIG)
+    endif()
+
+    if(SDL_MIXER_X_USE_MP3_MPG123 AND NOT TARGET MPG123::libmpg123)
+        find_dependency(MPG123 CONFIG)
+    endif()
+
+    if(SDL_MIXER_X_USE_XMP AND NOT TARGET libxmp::xmp_static)
+        find_dependency(libxmp CONFIG)
+    endif()
+
+    if(SDL_MIXER_X_USE_MIDI_ADLMIDI AND NOT TARGET libADLMIDI::ADLMIDI_static)
+        find_dependency(libADLMIDI CONFIG)
+    endif()
+
+    if(SDL_MIXER_X_USE_MIDI_OPNMIDI AND NOT TARGET libOPNMIDI::OPNMIDI_static)
+        find_dependency(libOPNMIDI CONFIG)
+    endif()
+
+    if(SDL_MIXER_X_USE_MIDI_FLUIDSYNTH AND NOT TARGET FluidSynth::libfluidsynth)
+        find_dependency(FluidSynth CONFIG)
+    endif()
+
     include("${CMAKE_CURRENT_LIST_DIR}/SDL2_mixer_ext-static-targets.cmake")
 endif()
diff --git a/src/codecs/music_ffmpeg.cmake b/src/codecs/music_ffmpeg.cmake
index 285d54df037a1bb126f3c680102ca08d27e83d6e..d2305f39b7c5bfeba6b9fc566764320354833765 100644
--- a/src/codecs/music_ffmpeg.cmake
+++ b/src/codecs/music_ffmpeg.cmake
@@ -3,7 +3,7 @@ if(USE_FFMPEG AND MIXERX_LGPL)
     option(USE_FFMPEG_DYNAMIC "Use dynamical loading of FFMPEG" ON)
 
     if(USE_SYSTEM_AUDIO_LIBRARIES)
-        find_package(FFMPEG QUIET)
+        find_package(FFMPEG REQUIRED)
         message("FFMPEG: [${FFMPEG_avcodec_FOUND}] ${FFMPEG_INCLUDE_DIRS} ${FFMPEG_swresample_LIBRARY} ${FFMPEG_avformat_LIBRARY} ${FFMPEG_avcodec_LIBRARY} ${FFMPEG_avutil_LIBRARY}")
 
         if(USE_FFMPEG_DYNAMIC)
@@ -18,10 +18,7 @@ if(USE_FFMPEG AND MIXERX_LGPL)
         endif()
 
         set(FFMPEG_LINK_LIBRARIES
-            ${FFMPEG_swresample_LIBRARY}
-            ${FFMPEG_avformat_LIBRARY}
-            ${FFMPEG_avcodec_LIBRARY}
-            ${FFMPEG_avutil_LIBRARY}
+            ${FFMPEG_LIBRARIES}
         )
     else()
         message(WARNING "FFMPEG libraries are not a part of AudioCodecs yet. Using any available from the system.")
@@ -43,7 +40,7 @@ if(USE_FFMPEG AND MIXERX_LGPL)
         set(FFMPEG_swresample_FOUND 1)
     endif()
 
-    if(FFMPEG_avcodec_FOUND AND FFMPEG_avformat_FOUND AND FFMPEG_avutil_FOUND AND FFMPEG_swresample_FOUND)
+    if(1)
         set(FFMPEG_FOUND 1)
     endif()
 
diff --git a/src/codecs/music_flac.cmake b/src/codecs/music_flac.cmake
index 1a39a4984c6bb36c8a09978540ebdd3b8031d7ef..6726655de7ef622fbb872660cfc0a3d49b00a058 100644
--- a/src/codecs/music_flac.cmake
+++ b/src/codecs/music_flac.cmake
@@ -3,7 +3,10 @@ if(USE_FLAC)
     option(USE_FLAC_DYNAMIC "Use dynamical loading of FLAC" OFF)
 
     if(USE_SYSTEM_AUDIO_LIBRARIES)
-        find_package(FLAC QUIET)
+        find_package(FLAC CONFIG REQUIRED)
+        get_target_property(FLAC_INCLUDE_DIRS FLAC::FLAC INTERFACE_INCLUDE_DIRECTORIES)
+        set(FLAC_LIBRARIES FLAC::FLAC)
+        set(FLAC_FOUND 1)
         message("FLAC: [${FLAC_FOUND}] ${FLAC_INCLUDE_DIRS} ${FLAC_LIBRARIES}")
         if(USE_FLAC_DYNAMIC)
             list(APPEND SDL_MIXER_DEFINITIONS -DFLAC_DYNAMIC=\"${FLAC_DYNAMIC_LIBRARY}\")
diff --git a/src/codecs/music_fluidsynth.cmake b/src/codecs/music_fluidsynth.cmake
index 8dd54447317cba97928f320ce2bd5cc8364a8967..ad7a302173e06302d8a5313302445151f9c14c70 100644
--- a/src/codecs/music_fluidsynth.cmake
+++ b/src/codecs/music_fluidsynth.cmake
@@ -6,7 +6,10 @@ if(USE_MIDI_FLUIDSYNTH AND NOT USE_MIDI_FLUIDLITE AND MIXERX_LGPL)
         message(WARNING "AudioCodecs doesn't ship FluidSynth, it will be recognized from a system!!!")
     endif()
 
-    find_package(FluidSynth QUIET)
+    find_package(FluidSynth CONFIG REQUIRED)
+    get_target_property(FluidSynth_INCLUDE_DIRS FluidSynth::libfluidsynth INTERFACE_INCLUDE_DIRECTORIES)
+    set(FluidSynth_LIBRARIES FluidSynth::libfluidsynth)
+    set(FluidSynth_FOUND 1)
     message("FluidSynth: [${FluidSynth_FOUND}] ${FluidSynth_INCLUDE_DIRS} ${FluidSynth_LIBRARIES}")
     if(USE_MIDI_FLUIDSYNTH_DYNAMIC)
         list(APPEND SDL_MIXER_DEFINITIONS -DFLUIDSYNTH_DYNAMIC=\"${FluidSynth_DYNAMIC_LIBRARY}\")
diff --git a/src/codecs/music_gme.cmake b/src/codecs/music_gme.cmake
index e8a455391489080a88685e4501250f37138b38d6..124d3f3e63eff2e125cbce8139a2958a78088b15 100644
--- a/src/codecs/music_gme.cmake
+++ b/src/codecs/music_gme.cmake
@@ -3,7 +3,14 @@ if(USE_GME AND MIXERX_LGPL)
     option(USE_GME_DYNAMIC "Use dynamical loading of Game Music Emulators library" OFF)
 
     if(USE_SYSTEM_AUDIO_LIBRARIES)
-        find_package(GME QUIET)
+        find_path(GME_INCLUDE_DIRS "gme.h" PATH_SUFFIXES gme)
+        find_library(GME_LIBRARY_RELEASE NAMES gme PATHS "${_VCPKG_INSTALLED_DIR}/${VCPKG_TARGET_TRIPLET}/lib" NO_DEFAULT_PATH)
+        find_library(GME_LIBRARY_DEBUG NAMES gme PATHS "${_VCPKG_INSTALLED_DIR}/${VCPKG_TARGET_TRIPLET}/debug/lib" NO_DEFAULT_PATH)
+        include(SelectLibraryConfigurations)
+        select_library_configurations(GME)
+        find_package(ZLIB REQUIRED)
+        list(APPEND GME_LIBRARIES ${ZLIB_LIBRARIES})
+        set(GME_FOUND 1)
         message("GME: [${GME_FOUND}] ${GME_INCLUDE_DIRS} ${GME_LIBRARIES}")
         if(USE_GME_DYNAMIC)
             list(APPEND SDL_MIXER_DEFINITIONS -DGME_DYNAMIC=\"${GME_DYNAMIC_LIBRARY}\")
diff --git a/src/codecs/music_midi_adl.cmake b/src/codecs/music_midi_adl.cmake
index c8f7bbdbfdb62e1fd0a20e5006c4a4d1b4ab8452..0be6f3b9e761263857dba8c1931e84cf22640535 100644
--- a/src/codecs/music_midi_adl.cmake
+++ b/src/codecs/music_midi_adl.cmake
@@ -3,7 +3,15 @@ if(USE_MIDI_ADLMIDI AND MIXERX_GPL)
     option(USE_MIDI_ADLMIDI_DYNAMIC "Use dynamical loading of libADLMIDI library" OFF)
 
     if(USE_SYSTEM_AUDIO_LIBRARIES)
-        find_package(ADLMIDI QUIET)
+        find_package(libADLMIDI CONFIG REQUIRED)
+        if(SDL_MIXER_X_STATIC)
+            set(_adlmidi libADLMIDI::ADLMIDI_static)
+        else()
+            set(_adlmidi libADLMIDI::ADLMIDI_shared)
+        endif()
+        get_target_property(ADLMIDI_INCLUDE_DIRS ${_adlmidi} INTERFACE_INCLUDE_DIRECTORIES)
+        set(ADLMIDI_LIBRARIES ${_adlmidi})
+        set(ADLMIDI_FOUND 1)
         message("ADLMIDI: [${ADLMIDI_FOUND}] ${ADLMIDI_INCLUDE_DIRS} ${ADLMIDI_LIBRARIES}")
 
         if(USE_MIDI_ADLMIDI_DYNAMIC)
diff --git a/src/codecs/music_midi_opn.cmake b/src/codecs/music_midi_opn.cmake
index 62b9dda0c2771ef25a7bc91949cdce0c55ea5250..26bb304d720f7d93eb3d8b24fd81dbc33df513cc 100644
--- a/src/codecs/music_midi_opn.cmake
+++ b/src/codecs/music_midi_opn.cmake
@@ -3,7 +3,15 @@ if(USE_MIDI_OPNMIDI AND MIXERX_GPL)
     option(USE_MIDI_OPNMIDI_DYNAMIC "Use dynamical loading of libOPNMIDI library" OFF)
 
     if(USE_SYSTEM_AUDIO_LIBRARIES)
-        find_package(OPNMIDI QUIET)
+        find_package(libOPNMIDI CONFIG REQUIRED)
+        if(SDL_MIXER_X_STATIC)
+            set(_opnmidi libOPNMIDI::OPNMIDI_static)
+        else()
+            set(_opnmidi libOPNMIDI::OPNMIDI_shared)
+        endif()
+        get_target_property(OPNMIDI_INCLUDE_DIRS ${_opnmidi} INTERFACE_INCLUDE_DIRECTORIES)
+        set(OPNMIDI_LIBRARIES ${_opnmidi})
+        set(OPNMIDI_FOUND 1)
         message("OPNMIDI: [${OPNMIDI_FOUND}] ${OPNMIDI_INCLUDE_DIRS} ${OPNMIDI_LIBRARIES}")
 
         if(USE_MIDI_OPNMIDI_DYNAMIC)
diff --git a/src/codecs/music_modplug.cmake b/src/codecs/music_modplug.cmake
index 555bcd91574d1c65761121648b331715a5fe8dad..ad065be36cd0176aa7f08631a6df48e857f76e42 100644
--- a/src/codecs/music_modplug.cmake
+++ b/src/codecs/music_modplug.cmake
@@ -4,7 +4,12 @@ if(USE_MODPLUG)
     option(USE_MODPLUG_STATIC "Use linking with a static ModPlug" ON)
 
     if(USE_SYSTEM_AUDIO_LIBRARIES)
-        find_package(ModPlug QUIET)
+        find_path(ModPlug_INCLUDE_DIRS "modplug.h" PATH_SUFFIXES libmodplug)
+        find_library(ModPlug_LIBRARY_RELEASE NAMES modplug PATHS "${_VCPKG_INSTALLED_DIR}/${VCPKG_TARGET_TRIPLET}/lib" NO_DEFAULT_PATH)
+        find_library(ModPlug_LIBRARY_DEBUG NAMES modplug PATHS "${_VCPKG_INSTALLED_DIR}/${VCPKG_TARGET_TRIPLET}/debug/lib" NO_DEFAULT_PATH)
+        include(SelectLibraryConfigurations)
+        select_library_configurations(ModPlug)
+        set(ModPlug_FOUND 1)
         message("ModPlug: [${ModPlug_FOUND}] ${ModPlug_INCLUDE_DIRS} ${ModPlug_LIBRARIES}")
         if(USE_MODPLUG_DYNAMIC)
             list(APPEND SDL_MIXER_DEFINITIONS -DMODPLUG_DYNAMIC=\"${ModPlug_DYNAMIC_LIBRARY}\")
diff --git a/src/codecs/music_mpg123.cmake b/src/codecs/music_mpg123.cmake
index c615896cd95e2854ced2d4188ea8e0d18870ad91..602a7b136ec135d55839cf7ab103df56357a8a4c 100644
--- a/src/codecs/music_mpg123.cmake
+++ b/src/codecs/music_mpg123.cmake
@@ -4,7 +4,10 @@ if(USE_MP3_MPG123 AND MIXERX_LGPL)
     option(USE_MP3_MPG123_DYNAMIC "Use dynamical loading of MPG123" OFF)
 
     if(USE_SYSTEM_AUDIO_LIBRARIES)
-        find_package(Mpg123 QUIET)
+        find_package(MPG123 CONFIG REQUIRED)
+        get_target_property(MPG123_INCLUDE_DIR MPG123::libmpg123 INTERFACE_INCLUDE_DIRECTORIES)
+        set(MPG123_LIBRARIES MPG123::libmpg123)
+        set(MPG123_FOUND 1)
         message("MPG123 found in ${MPG123_INCLUDE_DIR} folder")
         if(USE_MP3_MPG123_DYNAMIC)
             list(APPEND SDL_MIXER_DEFINITIONS -DMPG123_DYNAMIC=\"${MPG123_DYNAMIC_LIBRARY}\")
diff --git a/src/codecs/music_ogg.cmake b/src/codecs/music_ogg.cmake
index e7930f9c7e1a583f77306f6838933b991382872f..33a8a59e72e30e3cabd4e834895a23ae7b101324 100644
--- a/src/codecs/music_ogg.cmake
+++ b/src/codecs/music_ogg.cmake
@@ -1,4 +1,4 @@
-if(LIBOGG_NEEDED)
+if(0)
     if(USE_SYSTEM_AUDIO_LIBRARIES)
         find_package(OGG REQUIRED)
     else()
diff --git a/src/codecs/music_ogg_vorbis.cmake b/src/codecs/music_ogg_vorbis.cmake
index 0de2bec5d9327af7b38f09993dd884be786f3a5b..42b94340956fb8b28a03485ddc68282e3cf57ee9 100644
--- a/src/codecs/music_ogg_vorbis.cmake
+++ b/src/codecs/music_ogg_vorbis.cmake
@@ -10,7 +10,10 @@ if(USE_OGG_VORBIS)
                 find_package(Tremor QUIET)
                 message("Tremor: [${Tremor_FOUND}] ${Tremor_INCLUDE_DIRS} ${Tremor_LIBRARIES}")
             else()
-                find_package(Vorbis QUIET)
+                find_package(Vorbis CONFIG REQUIRED)
+                get_target_property(Vorbis_INCLUDE_DIRS Vorbis::vorbisfile INTERFACE_INCLUDE_DIRECTORIES)
+                set(Vorbis_LIBRARIES Vorbis::vorbisfile)
+                set(Vorbis_FOUND 1)
                 message("Vorbis: [${Vorbis_FOUND}] ${Vorbis_INCLUDE_DIRS} ${Vorbis_LIBRARIES}")
             endif()
 
diff --git a/src/codecs/music_opus.cmake b/src/codecs/music_opus.cmake
index 61fb32e7ec1bcb4e21cb77607399f1f36317c481..44e0b5da00146492a2b34a65ab0c16846b1aeb84 100644
--- a/src/codecs/music_opus.cmake
+++ b/src/codecs/music_opus.cmake
@@ -3,7 +3,10 @@ if(USE_OPUS)
     option(USE_OPUS_DYNAMIC "Use dynamical loading of Opus" OFF)
 
     if(USE_SYSTEM_AUDIO_LIBRARIES)
-        find_package(Opus QUIET)
+        find_package(OpusFile CONFIG REQUIRED)
+        get_target_property(Opus_INCLUDE_DIRS OpusFile::opusfile INTERFACE_INCLUDE_DIRECTORIES)
+        set(Opus_LIBRARIES OpusFile::opusfile)
+        set(Opus_FOUND 1)
         message("Opus: [${Opus_FOUND}] ${Opus_INCLUDE_DIRS} ${Opus_LIBRARIES} ${LIBOPUS_LIB}")
         if(USE_OPUS_DYNAMIC)
             list(APPEND SDL_MIXER_DEFINITIONS -DOPUS_DYNAMIC=\"${OpusFile_DYNAMIC_LIBRARY}\")
diff --git a/src/codecs/music_wavpack.cmake b/src/codecs/music_wavpack.cmake
index f93acdb4315e0f44311efdbc254a3fcd69964151..95d0372e2b8d0503864edf41f01977cf28a480f9 100644
--- a/src/codecs/music_wavpack.cmake
+++ b/src/codecs/music_wavpack.cmake
@@ -4,7 +4,10 @@ if(USE_WAVPACK)
     option(USE_WAVPACK_DSD "Enable WavPack DSD music support" OFF)
 
     if(USE_SYSTEM_AUDIO_LIBRARIES)
-        find_package(WavPack QUIET)
+        find_package(WavPack CONFIG REQUIRED)
+        get_target_property(WavPack_INCLUDE_DIRS WavPack::WavPack INTERFACE_INCLUDE_DIRECTORIES)
+        set(WavPack_LIBRARIES WavPack::WavPack)
+        set(WavPack_FOUND 1)
         message("WavPack: [${WavPack_FOUND}] ${WavPack_INCLUDE_DIRS} ${WavPack_LIBRARIES}")
         if(USE_WAVPACK_DYNAMIC)
             list(APPEND SDL_MIXER_DEFINITIONS -DWAVPACK_DYNAMIC=\"${WavPack_DYNAMIC_LIBRARY}\")
diff --git a/src/codecs/music_xmp.cmake b/src/codecs/music_xmp.cmake
index 52f2048b0b8cb9c72a27f16777fb7d9112be1c2d..2eab53c039e875733f18fc2f20b2e9f178371425 100644
--- a/src/codecs/music_xmp.cmake
+++ b/src/codecs/music_xmp.cmake
@@ -25,7 +25,15 @@ if(USE_XMP)
             set(XMP_LIBRARIES ${XMPLITE_LIBRARIES})
             set(XMP_FOUND ${XMPLITE_FOUND})
         else()
-            find_package(XMP)
+            find_package(libxmp CONFIG REQUIRED)
+            if(SDL_MIXER_X_STATIC)
+                set(_xmp libxmp::xmp_static)
+            else()
+                set(_xmp libxmp::xmp_shared)
+            endif()
+            get_target_property(XMP_INCLUDE_DIRS ${_xmp} INTERFACE_INCLUDE_DIRECTORIES)
+            set(XMP_LIBRARIES ${_xmp})
+            set(XMP_FOUND 1)
             message("XMP: [${XMP_FOUND}] ${XMP_INCLUDE_DIRS} ${XMP_LIBRARIES}")
             if(USE_XMP_DYNAMIC)
                 list(APPEND SDL_MIXER_DEFINITIONS -DXMP_DYNAMIC=\"${XMP_DYNAMIC_LIBRARY}\")
