{"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "0.10.0", "description": "A high performance JSON library written in ANSI C", "homepage": "https://github.com/ibireme/yyjson", "license": "MIT", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "default-features": ["fast-fp-conv", "non-standard", "reader", "writer"], "features": {"fast-fp-conv": {"description": "Build with custom floating-point number conversion"}, "non-standard": {"description": "Build with support for non-standard JSON"}, "reader": {"description": "Build with JSON reader"}, "writer": {"description": "Build with JSON writer", "dependencies": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "default-features": false, "features": ["reader"]}]}}}