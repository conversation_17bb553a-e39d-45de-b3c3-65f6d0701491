SET(SRC_LIBLUACPP ${SRC_LIBLUA})
ADD_LIBRARY ( lua-cpp ${SRC_LIBLUACPP} ${HDR_LIBLUACPP} )
SET_TARGET_PROPERTIES(lua-cpp PROPERTIES OUTPUT_NAME "lua-c++")
SET_SOURCE_FILES_PROPERTIES(${SRC_LIBLUACPP} PROPERTIES LANGUAGE CXX)
TARGET_INCLUDE_DIRECTORIES(lua-cpp PRIVATE $<BUILD_INTERFACE:${CMAKE_SOURCE_DIR}/src> PUBLIC $<INSTALL_INTERFACE:include>)
IF (BUILD_SHARED_LIBS AND WIN32)
    TARGET_COMPILE_DEFINITIONS (lua-cpp PUBLIC -DLUA_BUILD_AS_DLL )
ENDIF ()
IF (UNIX)
    IF (APPLE)
        TARGET_COMPILE_DEFINITIONS (lua-cpp PUBLIC -DLUA_USE_DLOPEN)
    ELSE ()
        FIND_LIBRARY (LIB_DLOPEN NAMES dl)
        IF (LIB_DLOPEN)
            TARGET_COMPILE_DEFINITIONS (lua-cpp PUBLIC -DLUA_USE_DLOPEN)
            TARGET_LINK_LIBRARIES (lua-cpp ${CMAKE_DL_LIBS})
        ENDIF ()
    ENDIF ()
ENDIF ()

INSTALL ( TARGETS lua-cpp
    EXPORT unofficial-lua-cpp-config
    RUNTIME DESTINATION bin
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
)

INSTALL(EXPORT unofficial-lua-cpp-config DESTINATION share/unofficial-lua-cpp)
