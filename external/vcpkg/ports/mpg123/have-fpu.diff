diff --git a/ports/cmake/src/CMakeLists.txt b/ports/cmake/src/CMakeLists.txt
index 32bc03c..acd420f 100644
--- a/ports/cmake/src/CMakeLists.txt
+++ b/ports/cmake/src/CMakeLists.txt
@@ -188,6 +188,8 @@ check_cpu_arch_arm64(ARCH_IS_ARM64)
 
 if(WIN32 OR (ARCH_IS_ARM64 AND APPLE))
     set(HAVE_FPU 1)
+elseif(CMAKE_CROSSCOMPILING OR DEFINED HAVE_FPU)
+    set(HAVE_FPU 1 CACHE STRING "Override in triplet if needed")
 else()
     cmake_host_system_information(RESULT HAVE_FPU QUERY HAS_FPU)
 endif()
