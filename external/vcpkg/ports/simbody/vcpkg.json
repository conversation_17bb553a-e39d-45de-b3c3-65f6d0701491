{"name": "simbody", "version-date": "2023-01-10", "port-version": 1, "description": "High-performance C++ multibody dynamics/physics library for simulating articulated biomechanical and mechanical systems like vehicles, robots, and the human skeleton.", "homepage": "https://simtk.org/home/<USER>", "license": "Apache-2.0", "supports": "!uwp", "dependencies": ["blas", "lapack", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}]}