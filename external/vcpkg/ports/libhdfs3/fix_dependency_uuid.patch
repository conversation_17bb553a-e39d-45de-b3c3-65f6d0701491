diff --git a/CMakeLists.txt b/CMakeLists.txt
index 1a8b109..6ea24f8 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -21,7 +21,9 @@ FIND_PACKAGE(KERBEROS REQUIRED)
 FIND_PACKAGE(GSasl REQUIRED)
 
 IF(OS_LINUX)
-    FIND_PACKAGE(LibUUID REQUIRED)
+    find_package(unofficial-libuuid CONFIG REQUIRED)
+    set(LIBUUID_LIBRARIES unofficial::UUID::uuid) 
+    add_library(uuid ALIAS unofficial::UUID::uuid)
 ENDIF(OS_LINUX)
 
 ADD_SUBDIRECTORY(mock)
diff --git a/hdfs_benchmark/CMakeLists.txt b/hdfs_benchmark/CMakeLists.txt
index dcb4c0a..f4274b6 100644
--- a/hdfs_benchmark/CMakeLists.txt
+++ b/hdfs_benchmark/CMakeLists.txt
@@ -10,4 +10,4 @@ set(SOURCE_FILES main.cpp)
 add_executable(hdfs_benchmark ${SOURCE_FILES})
 add_dependencies(hdfs_benchmark libhdfs3-shared)

-target_link_libraries(hdfs_benchmark uuid pthread libhdfs3-shared)
+target_link_libraries(hdfs_benchmark pthread libhdfs3-shared)
