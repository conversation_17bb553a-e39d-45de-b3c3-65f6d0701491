{"name": "lib<PERSON>er", "version": "3.3.15", "port-version": 2, "description": "Minimal C library to access, configure and get data from neuromorphic sensors and processors.", "homepage": "https://gitlab.com/inivation/dv/libcaer", "license": "BSD-2-Clause AND Apache-2.0", "supports": "!(arm & windows & !mingw)", "dependencies": ["libusb", {"name": "pkgconf", "host": true}, {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"opencv": {"description": "Enable support for frame enhancements using OpenCV", "dependencies": [{"name": "opencv4", "default-features": false, "features": ["fs", "intrinsics", "thread"]}]}}}