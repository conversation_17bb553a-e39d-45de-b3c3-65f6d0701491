diff --git a/CMakeLists.txt b/CMakeLists.txt
index 9446bc4..a8a3288 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -3,16 +3,7 @@ cmake_minimum_required(VERSION 2.6)
 project( libmfx )
 
 # FIXME Adds support for using system/other install of intel media sdk
-find_path ( INTELMEDIASDK_PATH mfx/mfxvideo.h
-  HINTS "${CMAKE_SOURCE_DIR}"
-)
-
-if (INTELMEDIASDK_PATH_NOTFOUND)
-  message( FATAL_ERROR "Intel MEDIA SDK include not found" )
-else (INTELMEDIASDK_PATH_NOTFOUND)
-  message(STATUS "Intel Media SDK is here: ${INTELMEDIASDK_PATH}")
-endif (INTELMEDIASDK_PATH_NOTFOUND)
-
+set(INTELMEDIASDK_PATH "${CMAKE_CURRENT_LIST_DIR}")
 
 set(SOURCES
   src/main.cpp
diff --git a/libmfx.pc.cmake b/libmfx.pc.cmake
index fabb541..5d248fe 100644
--- a/libmfx.pc.cmake
+++ b/libmfx.pc.cmake
@@ -6,9 +6,9 @@ Requires.private:
 Name: libmfx
 Description: Intel Media SDK Dispatched static library
-Version: 2013
+Version: 1.35
 Requires:
 Requires.private:
 Conflicts:
-Libs: -L${libdir} -lsupc++ ${libdir}/libmfx.lib
+Libs: -L${libdir} -llibmfx
 Libs.private:
-Cflags: -I${includedir} -I@INTELMEDIASDK_PATH@
+Cflags: -I${includedir}
