Subject: [PATCH] fix for vcpkg
fix missing mfx_driver_store_loader related symbols
---
Index: CMakeLists.txt
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/CMakeLists.txt b/CMakeLists.txt
--- a/CMakeLists.txt	(revision 7e4d221c36c630c1250b23a5dfa15657bc04c10c)
+++ b/CMakeLists.txt	(revision 5ebef171699530ca01594a5cef10a68811f4d105)
@@ -40,6 +39,7 @@
     src/mfx_load_plugin.cpp
     src/mfx_plugin_hive.cpp
     src/mfx_win_reg_key.cpp
+    src/mfx_driver_store_loader.cpp
   )
 endif (CMAKE_SYSTEM_NAME MATCHES "Windows")
 
@@ -56,6 +56,12 @@
 configure_file (${CMAKE_SOURCE_DIR}/libmfx.pc.cmake ${CMAKE_BINARY_DIR}/libmfx.pc @ONLY)
 
 add_library( mfx STATIC ${SOURCES} )
+
+if (CMAKE_SYSTEM_NAME MATCHES "Windows")
+  set_target_properties(mfx
+          PROPERTIES PREFIX lib)
+endif (CMAKE_SYSTEM_NAME MATCHES "Windows")
+
 install (DIRECTORY ${CMAKE_SOURCE_DIR}/mfx DESTINATION ${CMAKE_INSTALL_PREFIX}/include FILES_MATCHING PATTERN "*.h")
 install (FILES ${CMAKE_BINARY_DIR}/libmfx.pc DESTINATION ${CMAKE_INSTALL_PREFIX}/lib/pkgconfig)
 install (TARGETS mfx ARCHIVE DESTINATION ${CMAKE_INSTALL_PREFIX}/lib)
Index: libmfx.pc.cmake
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/libmfx.pc.cmake b/libmfx.pc.cmake
--- a/libmfx.pc.cmake	(revision 7e4d221c36c630c1250b23a5dfa15657bc04c10c)
+++ b/libmfx.pc.cmake	(revision 388559e9e8234eb0989e1598a9beea4035a04132)
@@ -9,6 +9,6 @@
 Requires:
 Requires.private:
 Conflicts:
-Libs: -L${libdir} -lsupc++ ${libdir}/libmfx.a
+Libs: -L${libdir} -lsupc++ ${libdir}/libmfx.lib
 Libs.private:
 Cflags: -I${includedir} -I@INTELMEDIASDK_PATH@
Index: src/mfx_driver_store_loader.cpp
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/mfx_driver_store_loader.cpp b/src/mfx_driver_store_loader.cpp
--- a/src/mfx_driver_store_loader.cpp	(revision 388559e9e8234eb0989e1598a9beea4035a04132)
+++ b/src/mfx_driver_store_loader.cpp	(revision 5ebef171699530ca01594a5cef10a68811f4d105)
@@ -24,6 +24,9 @@
 #include "mfx_dispatcher_log.h"
 #include "mfx_load_dll.h"
 
+#pragma comment(lib, "Ole32.lib")
+#pragma comment(lib, "Advapi32.lib")
+
 namespace MFX
 {
 
