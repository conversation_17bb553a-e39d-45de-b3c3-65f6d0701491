{"name": "libmysql", "version": "8.0.40", "description": "A MySQL client library for C development", "homepage": "https://github.com/mysql/mysql-server", "license": "GPL-2.0-or-later", "supports": "!android & !mingw & !uwp & !xbox", "dependencies": ["boost-algorithm", "boost-functional", "boost-geometry", "boost-graph", "boost-optional", {"name": "libmysql", "host": true}, "lz4", {"name": "ncurses", "platform": "!windows | mingw"}, "openssl", "<PERSON><PERSON><PERSON>", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}, "zlib", "zstd"]}