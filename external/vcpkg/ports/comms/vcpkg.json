{"name": "comms", "version": "5.2.7", "description": "COMMS is the C++(11) headers only, platform independent library, which makes the implementation of a communication protocol to be an easy and relatively quick process.", "homepage": "https://commschamp.github.io/", "documentation": "https://github.com/commschamp/comms", "license": "MPL-2.0", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}]}