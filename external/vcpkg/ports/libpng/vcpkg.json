{"name": "libpng", "version": "1.6.46", "description": "libpng is a library implementing an interface for reading and writing PNG (Portable Network Graphics) format files", "homepage": "https://github.com/pnggroup/libpng", "license": "libpng-2.0", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}, {"name": "vcpkg-cmake-get-vars", "host": true, "platform": "arm & android"}, "zlib"], "features": {"apng": {"description": "This is backward compatible with the regular libpng, both in library usage and format"}, "tools": {"description": "Build the libpng tools", "supports": "!android & !ios"}}}