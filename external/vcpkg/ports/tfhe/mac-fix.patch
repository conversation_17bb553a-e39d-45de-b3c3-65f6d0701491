diff --git a/src/libtfhe/fft_processors/nayuki/fft_processor_nayuki.cpp b/src/libtfhe/fft_processors/nayuki/fft_processor_nayuki.cpp
index 26de3f0..422c7f6 100644
--- a/src/libtfhe/fft_processors/nayuki/fft_processor_nayuki.cpp
+++ b/src/libtfhe/fft_processors/nayuki/fft_processor_nayuki.cpp
@@ -12,7 +12,7 @@ FFT_Processor_nayuki::FFT_Processor_nayuki(const int N): _2N(2*N),N(N),Ns2(N/2)
     tables_reverse = fft_init_reverse(_2N);
     omegaxminus1 = (cplx*) malloc(sizeof(cplx) * _2N);
     for (int x=0; x<_2N; x++) {
-	omegaxminus1[x]=cos(x*M_PI/N)-1. + sin(x*M_PI/N) * 1i; 
+	omegaxminus1[x]=cplx(cos(x*M_PI/N)-1., sin(x*M_PI/N)); // instead of cos(x*M_PI/N)-1. + sin(x*M_PI/N) * 1i
 	//exp(i.x.pi/N)-1
     }
 }
