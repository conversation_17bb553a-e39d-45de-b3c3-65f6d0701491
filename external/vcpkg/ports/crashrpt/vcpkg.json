{"name": "crashrpt", "version": "1.4.3", "port-version": 3, "description": "A crash reporting system for Windows applications", "homepage": "http://crashrpt.sourceforge.net/", "license": "BSD-3-<PERSON><PERSON>", "supports": "!osx & !linux & !uwp & (x86 | x64)", "dependencies": ["db<PERSON><PERSON>p", "libjpeg-turbo", "libogg", "libpng", "libtheora", "tinyxml", {"name": "vcpkg-cmake", "host": true}, "wtl", "zlib"], "features": {"demos": {"description": "Demo applications for CrashRptProbe"}, "probe": {"description": "The CrashRptProbe library"}, "tests": {"description": "Test application for crashrpt", "dependencies": [{"name": "crashrpt", "default-features": false, "features": ["probe"]}]}}}