diff --git a/processing/crashrptprobe/CMakeLists.txt b/processing/crashrptprobe/CMakeLists.txt
index c513c6d..8ac306a 100644
--- a/processing/crashrptprobe/CMakeLists.txt
+++ b/processing/crashrptprobe/CMakeLists.txt
@@ -13,7 +13,7 @@ list(REMOVE_ITEM srcs_using_precomp  ./CrashRptProbe.rc ./CrashRptProbe.def ./st
 add_msvc_precompiled_header(stdafx.h ./stdafx.cpp srcs_using_precomp)
 
 # Define _UNICODE (use wide-char encoding)
-add_definitions(-D_UNICODE)
+add_definitions(-D_UNICODE -DUNICODE)
 
 fix_default_compiler_settings_()
 
diff --git a/reporting/crashrpt/CMakeLists.txt b/reporting/crashrpt/CMakeLists.txt
index c7e4fd6..da9cef1 100644
--- a/reporting/crashrpt/CMakeLists.txt
+++ b/reporting/crashrpt/CMakeLists.txt
@@ -12,7 +12,7 @@ list(REMOVE_ITEM srcs_using_precomp ./CrashRpt.rc ./StdAfx.cpp ./CrashRpt.def)
 add_msvc_precompiled_header(stdafx.h ./StdAfx.cpp srcs_using_precomp)
 
 # Define _UNICODE (use wide-char encoding)
-add_definitions(-D_UNICODE -D_CRT_SECURE_NO_DEPRECATE)
+add_definitions(-D_UNICODE -DUNICODE -D_CRT_SECURE_NO_DEPRECATE)
 
 # Modify default compiler settings
 fix_default_compiler_settings_()
diff --git a/reporting/crashsender/CMakeLists.txt b/reporting/crashsender/CMakeLists.txt
index aa4ab8a..f081254 100644
--- a/reporting/crashsender/CMakeLists.txt
+++ b/reporting/crashsender/CMakeLists.txt
@@ -15,7 +15,7 @@ list(APPEND source_files
 	${CMAKE_SOURCE_DIR}/reporting/CrashRpt/SharedMem.cpp)
 	
 # Define _UNICODE (use wide-char encoding)
-add_definitions(-D_UNICODE )
+add_definitions(-D_UNICODE -DUNICODE)
 
 fix_default_compiler_settings_()
 
