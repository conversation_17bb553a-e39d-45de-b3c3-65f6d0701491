diff --git a/src/dictionary.c b/src/dictionary.c
index cb7ccd4..7985ab3 100644
--- a/src/dictionary.c
+++ b/src/dictionary.c
@@ -18,7 +18,6 @@
 #include <stdio.h>
 #include <stdlib.h>
 #include <string.h>
-#include <unistd.h>
 
 /** Maximum value size for integers and doubles. */
 #define MAXVALSZ    1024
diff --git a/src/dictionary.h b/src/dictionary.h
index d04b6ce..929ae17 100644
--- a/src/dictionary.h
+++ b/src/dictionary.h
@@ -21,7 +21,12 @@
 #include <stdio.h>
 #include <stdlib.h>
 #include <string.h>
+#ifdef _MSC_VER
+#include <BaseTsd.h>
+typedef SSIZE_T ssize_t;
+#else
 #include <unistd.h>
+#endif
 
 #ifdef __cplusplus
 extern "C" {
