{"name": "brotli", "version": "1.1.0", "port-version": 1, "description": "a generic-purpose lossless compression algorithm that compresses data using a combination of a modern variant of the LZ77 algorithm, <PERSON><PERSON><PERSON> coding and 2nd order context modeling.", "homepage": "https://github.com/google/brotli", "license": "MIT", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}]}