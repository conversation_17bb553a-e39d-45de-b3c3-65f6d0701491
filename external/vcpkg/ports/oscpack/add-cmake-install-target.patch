From 3a7ee65ecf43b97cc4a8c7cea5493de0d2b992fa Mon Sep 17 00:00:00 2001
From: =?UTF-8?q?Daniel=20Sch=C3=<PERSON>rmann?= <<EMAIL>>
Date: Wed, 15 Jan 2025 07:58:23 +0100
Subject: [PATCH] Add cmake install target

---
 CMakeLists.txt | 28 ++++++++++++++++++++++++++++
 1 file changed, 28 insertions(+)

diff --git a/CMakeLists.txt b/CMakeLists.txt
index e8bef70..4f200b2 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -42,6 +42,34 @@ osc/OscOutboundPacketStream.cpp
 
 )
 
+set(IP_HEADERS 
+  ip/IpEndpointName.h
+  ip/NetworkingUtils.h
+  ip/PacketListener.h
+  ip/TimerListener.h
+  ip/UdpSocket.h
+)
+
+
+set(OSC_HEADERS 
+  osc/MessageMappingOscPacketListener.h
+  osc/OscException.h
+  osc/OscHostEndianness.h
+  osc/OscOutboundPacketStream.h
+  osc/OscPacketListener.h
+  osc/OscPrintReceivedElements.h
+  osc/OscReceivedElements.h
+  osc/OscTypes.h
+)
+
+INSTALL(TARGETS oscpack
+  RUNTIME DESTINATION bin
+  LIBRARY DESTINATION lib
+  ARCHIVE DESTINATION lib
+)
+
+INSTALL(FILES ${IP_HEADERS} DESTINATION include/ip)
+INSTALL(FILES ${OSC_HEADERS} DESTINATION include/osc)
 
 ADD_EXECUTABLE(OscUnitTests tests/OscUnitTests.cpp)
 TARGET_LINK_LIBRARIES(OscUnitTests oscpack ${LIBS})
-- 
2.34.1

