diff --git a/src/libgit2/CMakeLists.txt b/src/libgit2/CMakeLists.txt
index a7d3c7c..2a7f111 100644
--- a/src/libgit2/CMakeLists.txt
+++ b/src/libgit2/CMakeLists.txt
@@ -60,7 +60,7 @@ target_link_libraries(libgit2package ${LIBGIT2_SYSTEM_LIBS})
 target_include_directories(libgit2package SYSTEM PRIVATE ${LIBGIT2_INCLUDES})
 target_include_directories(libgit2package INTERFACE $<INSTALL_INTERFACE:./include/git2>)
 
-set_target_properties(libgit2package PROPERTIES C_STANDARD 90)
+set_target_properties(libgit2package PROPERTIES C_STANDARD 99)
 set_target_properties(libgit2package PROPERTIES C_EXTENSIONS OFF)
 set_target_properties(libgit2package PROPERTIES LIBRARY_OUTPUT_DIRECTORY ${PROJECT_BINARY_DIR})
 set_target_properties(libgit2package PROPERTIES RUNTIME_OUTPUT_DIRECTORY ${PROJECT_BINARY_DIR})
