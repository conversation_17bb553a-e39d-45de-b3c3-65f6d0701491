From e2c9c0a1cee07c6cacedd682d3506b447c2c561b Mon Sep 17 00:00:00 2001
From: <PERSON><PERSON> <<EMAIL>>
Date: Mon, 1 May 2023 23:11:19 +0200
Subject: [PATCH 2/2] Remove min/max macros

Signed-off-by: <PERSON><PERSON> <<EMAIL>>
---
 src/CMakeLists.txt | 3 +++
 1 file changed, 3 insertions(+)

diff --git a/src/CMakeLists.txt b/src/CMakeLists.txt
index 5b8649b..5c818ee 100644
--- a/src/CMakeLists.txt
+++ b/src/CMakeLists.txt
@@ -36,6 +36,9 @@ set(NAMESPACE           "${PROJECT_NAME}::")
 set(TARGET_EXPORT_NAME  ${PROJECT_NAME}Targets)
 
 add_library(${TARGET_NAME} ${CPPKAFKA_LIBRARY_TYPE} ${SOURCES})
+if (MSVC)
+    target_compile_definitions(${TARGET_NAME} PRIVATE NOMINMAX)
+endif ()
 target_include_directories(${TARGET_NAME} PUBLIC $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/../include/cppkafka>)
 set_target_properties(${TARGET_NAME} PROPERTIES
         ARCHIVE_OUTPUT_DIRECTORY "${CMAKE_INSTALL_LIBDIR}"
-- 
2.37.1 (Apple Git-137.1)

