{"name": "cppkafka", "version": "0.4.0", "description": "cppkafka allows C++ applications to consume and produce messages using the Apache Kafka protocol. The library is built on top of librdkafka, and provides a high level API that uses modern C++ features to make it easier to write code while keeping the wrapper's performance overhead to a minimum.", "homepage": "https://github.com/mfontanini/cppkafka", "license": "BSD-2-<PERSON><PERSON>", "dependencies": ["boost-program-options", "librdkafka", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}]}