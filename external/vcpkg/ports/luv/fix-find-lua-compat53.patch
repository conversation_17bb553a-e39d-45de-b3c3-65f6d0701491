diff --git a/CMakeLists.txt b/CMakeLists.txt
index e63a431..1f144c8 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -231,13 +231,7 @@ if(APPLE)
   # )
 endif()
 
-if(NOT LUA_COMPAT53_DIR)
-  set(LUA_COMPAT53_DIR deps/lua-compat-5.3)
-endif()
-if(DEFINED ENV{LUA_COMPAT53_DIR})
-  set(LUA_COMPAT53_DIR $ENV{LUA_COMPAT53_DIR})
-endif()
-include_directories(${LUA_COMPAT53_DIR}/c-api)
+find_path(LUA_COMPAT53_INCLUDE_DIRS "compat-5.3.h")
 
 if(WIN32)
   add_definitions(-DLUA_BUILD_AS_DLL -DLUA_LIB)
@@ -263,6 +257,7 @@ foreach(TARGET_NAME ${ACTIVE_TARGETS})
   else()
     target_link_libraries(${TARGET_NAME} ${LIBUV_LIBRARIES})
   endif()
+	target_include_directories(${TARGET_NAME} PRIVATE ${LUA_COMPAT53_INCLUDE_DIRS})
 endforeach()
 
 if (NOT LUA)

