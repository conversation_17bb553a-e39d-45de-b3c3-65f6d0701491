diff --git a/CMakeLists.txt b/CMakeLists.txt
index e63a431..841db78 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -125,7 +125,7 @@ else (LUA)
       # We only link the libs on Windows, so find_package fully succeeding
       # is only required on Windows
       if (WIN32 OR CYGWIN)
-        find_package(LuaJIT REQUIRED)
+        find_package(LUAJIT REQUIRED)
         link_directories(${LUAJIT_LIBRARIES})
       else()
         find_package(LuaJIT)
diff --git a/cmake/Modules/FindLuaJIT.cmake b/cmake/Modules/FindLuaJIT.cmake
index 0d0786e..7121e06 100644
--- a/cmake/Modules/FindLuaJIT.cmake
+++ b/cmake/Modules/FindLuaJIT.cmake
@@ -23,8 +23,12 @@
 #  LUA_VERSION_MINOR  - the minor version of Lua
 #  LUA_VERSION_PATCH  - the patch version of Lua
 
-FIND_PATH(LUAJIT_INCLUDE_DIR NAMES lua.h PATH_SUFFIXES luajit-2.0 luajit-2.1)
-FIND_LIBRARY(LUAJIT_LIBRARIES NAMES luajit-5.1 luajit)
+FIND_PATH(LUAJIT_INCLUDE_DIR NAMES luajit.h PATH_SUFFIXES luajit-2.0 luajit-2.1 luajit)
+set(LUAJIT_LIB_NAMES luajit-5.1 luajit)
+if(MSVC)
+  list(APPEND LUAJIT_LIB_NAMES lua51)
+endif()
+FIND_LIBRARY(LUAJIT_LIBRARIES NAMES ${LUAJIT_LIB_NAMES})
 
 if (LUAJIT_INCLUDE_DIR AND EXISTS "${LUAJIT_INCLUDE_DIR}/lua.h")
     # At least 5.[012] have different ways to express the version

