{"$comment": "Use scripts/update_suitesparse.py to update all SuiteSparse ports", "name": "suitesparse-colamd", "version-semver": "3.3.4", "description": "COLAMD: Routines for column approximate minimum degree ordering algorithm in SuiteSparse", "homepage": "https://people.engr.tamu.edu/davis/suitesparse.html", "license": "BSD-3-<PERSON><PERSON>", "dependencies": ["suitesparse-config", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}]}