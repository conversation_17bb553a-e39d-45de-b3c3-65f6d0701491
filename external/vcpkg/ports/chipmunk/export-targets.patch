diff --git a/src/CMakeLists.txt b/src/CMakeLists.txt
index 34882d1..d47c303 100644
--- a/src/CMakeLists.txt
+++ b/src/CMakeLists.txt
@@ -31,7 +31,9 @@ if(BUILD_SHARED)
 	  # need to explicitly link to the math library because the CMake/Android toolchains may not do it automatically
 	  target_link_libraries(chipmunk m)
   endif(ANDROID OR UNIX)
-  install(TARGETS chipmunk RUNTIME DESTINATION ${BIN_INSTALL_DIR}
+  target_include_directories(chipmunk INTERFACE $<INSTALL_INTERFACE:include>)
+  install(TARGETS chipmunk EXPORT unofficial-chipmunk-config
+                           RUNTIME DESTINATION ${BIN_INSTALL_DIR}
                            LIBRARY DESTINATION ${LIB_INSTALL_DIR}
                            ARCHIVE DESTINATION ${LIB_INSTALL_DIR})
 endif(BUILD_SHARED)
@@ -46,9 +48,12 @@ if(BUILD_STATIC)
     set_target_properties(chipmunk_static PROPERTIES LINKER_LANGUAGE CXX)
   endif(MSVC)
   # Sets chipmunk_static to output "libchipmunk.a" not "libchipmunk_static.a"
-  set_target_properties(chipmunk_static PROPERTIES OUTPUT_NAME chipmunk)
+  set_target_properties(chipmunk_static PROPERTIES OUTPUT_NAME chipmunk EXPORT_NAME chipmunk)
+  target_include_directories(chipmunk_static INTERFACE $<INSTALL_INTERFACE:include>)
   if(INSTALL_STATIC)
-    install(TARGETS chipmunk_static ARCHIVE DESTINATION ${LIB_INSTALL_DIR})
+    install(TARGETS chipmunk_static EXPORT unofficial-chipmunk-config
+                                    LIBRARY DESTINATION ${LIB_INSTALL_DIR}
+                                    ARCHIVE DESTINATION ${LIB_INSTALL_DIR})
   endif(INSTALL_STATIC)
 endif(BUILD_STATIC)
 
@@ -57,3 +62,9 @@ if(BUILD_SHARED OR INSTALL_STATIC)
   install(FILES ${chipmunk_public_header} DESTINATION include/chipmunk)
   install(FILES ${chipmunk_constraint_header} DESTINATION include/chipmunk/constraints)
 endif(BUILD_SHARED OR INSTALL_STATIC)
+
+install(EXPORT unofficial-chipmunk-config
+        FILE unofficial-chipmunk-config.cmake
+        NAMESPACE unofficial::chipmunk::
+        DESTINATION share/unofficial-chipmunk
+)
