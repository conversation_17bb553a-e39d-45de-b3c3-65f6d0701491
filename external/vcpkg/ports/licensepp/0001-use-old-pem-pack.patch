From d1b446c7a69344cb53be6692cd76186960a1428d Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Wed, 23 Sep 2020 09:40:32 -0700
Subject: [PATCH 1/2] use old pem pack

In this commit: https://github.com/noloader/cryptopp-pem/commit/0cfa60820ec1d5e8ac4d77a0a8786ee43e9a2400
the parameter orders have been changed.
But we cannot update pem-pack to this version or newer because it
wont compile with the current version of cryptopp in vcpkg.
---
 src/external/Ripe.cc | 2 +-
 1 file changed, 1 insertion(+), 1 deletion(-)

diff --git a/src/external/Ripe.cc b/src/external/Ripe.cc
index 6ff9561..a1d1eec 100644
--- a/src/external/Ripe.cc
+++ b/src/external/Ripe.cc
@@ -227,7 +227,7 @@ Ripe::KeyPair Ripe::generateRSAKeyPair(unsigned int length, const std::string& s
         if (secret.empty()) {
             PEM_Save(snk, privateKey);
         } else {
-            PEM_Save(snk, privateKey, rng, PRIVATE_RSA_ALGORITHM, secret.data(), secret.size());
+            PEM_Save(snk, rng, privateKey, PRIVATE_RSA_ALGORITHM, secret.data(), secret.size());
         }
         snk.MessageEnd();
     }
-- 
2.24.3 (Apple Git-128)

