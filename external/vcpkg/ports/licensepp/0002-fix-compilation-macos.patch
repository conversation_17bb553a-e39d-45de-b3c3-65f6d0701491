From b0e09221f5f782e6dc2dedf197eba8064095edcd Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Thu, 6 Apr 2023 09:36:17 +0200
Subject: [PATCH] Fix compilation on macOS

---
 src/json-object.h | 6 ------
 1 file changed, 6 deletions(-)

diff --git a/src/json-object.h b/src/json-object.h
index 712e9a0..9764514 100644
--- a/src/json-object.h
+++ b/src/json-object.h
@@ -80,12 +80,6 @@ class JsonObject
         return get<bool>(key, defaultValue);
     }
 
-    inline const char* getCString(const std::string& key,
-                                  const char* defaultValue = "") const
-    {
-        return getString(key, defaultValue).c_str();
-    }
-
     inline bool hasKey(const std::string& key) const
     {
          return m_root.count(key) > 0;
