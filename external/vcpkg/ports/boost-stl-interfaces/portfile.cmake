# Automatically generated by scripts/boost/generate-ports.ps1

vcpkg_from_github(
    OUT_SOURCE_PATH SOURCE_PATH
    REPO boostorg/stl_interfaces
    REF boost-${VERSION}
    SHA512 000f90b5c312d4450177d1448e0c50cfdb5b3d2753cb1c146cc20763b0d325e0572d31d6a578c354f673fedef803b8a6f1c4b8b74273876078431efb5ee3f564
    HEAD_REF master
)

set(FEATURE_OPTIONS "")
boost_configure_and_install(
    SOURCE_PATH "${SOURCE_PATH}"
    OPTIONS ${FEATURE_OPTIONS}
)
