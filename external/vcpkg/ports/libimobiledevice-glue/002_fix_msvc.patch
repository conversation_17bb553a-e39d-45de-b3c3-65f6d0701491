diff --git a/src/socket.c b/src/socket.c
index 4b8b857..4cf8a92 100644
--- a/src/socket.c
+++ b/src/socket.c
@@ -26,10 +26,12 @@
 #include <stddef.h>
 #include <stdlib.h>
 #include <string.h>
-#include <unistd.h>
 #include <errno.h>
-#include <sys/time.h>
 #include <sys/stat.h>
+#ifndef _MSC_VER
+#include <unistd.h>
+#include <sys/time.h>
+#endif
 #ifdef WIN32
 #include <winsock2.h>
 #include <ws2tcpip.h>
diff --git a/src/termcolors.c b/src/termcolors.c
index 5c436f2..9a84ff2 100644
--- a/src/termcolors.c
+++ b/src/termcolors.c
@@ -28,7 +28,9 @@
 
 #include <stdio.h>
 #include <stdlib.h>
+#ifndef _MSC_VER
 #include <unistd.h>
+#endif
 #include <string.h>
 #include <stdarg.h>
 
diff --git a/src/utils.c b/src/utils.c
index ad8e9c4..b9803ac 100644
--- a/src/utils.c
+++ b/src/utils.c
@@ -30,7 +30,9 @@
 #include <stdlib.h>
 #include <string.h>
 #include <time.h>
+#ifndef _MSC_VER
 #include <sys/time.h>
+#endif
 #include <inttypes.h>
 #include <ctype.h>
 #include <errno.h>
