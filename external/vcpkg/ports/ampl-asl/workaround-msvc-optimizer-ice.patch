diff --git a/src/solvers/avltree.c b/src/solvers/avltree.c
index 7a9adab..a75da90 100644
--- a/src/solvers/avltree.c
+++ b/src/solvers/avltree.c
@@ -54,6 +54,10 @@ AVL_Tree {
 	void (*Free)(void*);
 	};
 
+#if defined(_MSC_VER) && _MSC_VER < 1917
+#pragma optimize("", off)
+#endif
+
  AVL_Tree*
 AVL_Tree_alloc2(void *v, AVL_Elcomp cmp, void *(*Malloc)(size_t), void (*Free)(void*))
 {
diff --git a/src/solvers/sphes.c b/src/solvers/sphes.c
index a50065f..6e6eedc 100644
--- a/src/solvers/sphes.c
+++ b/src/solvers/sphes.c
@@ -461,6 +461,10 @@ compar(const void *a, const void *b)
 #undef del_mblk
 #define del_mblk(b,c) Del_mblk_ASL(a,b,(Char*)(c))
 
+#if defined(_MSC_VER) && _MSC_VER < 1917
+#pragma optimize("", off)
+#endif
+
  static void
 new_Hesoprod(ASL_pfgh *asl, ograd *L, ograd *R, real coef)
 {
diff --git a/src/solvers2/avltree.c b/src/solvers2/avltree.c
index 7a9adab..a75da90 100644
--- a/src/solvers2/avltree.c
+++ b/src/solvers2/avltree.c
@@ -54,6 +54,10 @@ AVL_Tree {
 	void (*Free)(void*);
 	};
 
+#if defined(_MSC_VER) && _MSC_VER < 1917
+#pragma optimize("", off)
+#endif
+
  AVL_Tree*
 AVL_Tree_alloc2(void *v, AVL_Elcomp cmp, void *(*Malloc)(size_t), void (*Free)(void*))
 {
diff --git a/src/solvers2/sphes.c b/src/solvers2/sphes.c
index dd2edff..ebdd3af 100644
--- a/src/solvers2/sphes.c
+++ b/src/solvers2/sphes.c
@@ -855,6 +855,10 @@ compar(const void *a, const void *b)
 #undef del_mblk
 #define del_mblk(c) Del_mblk_ASL(a,(Char*)(c))
 
+#if defined(_MSC_VER) && _MSC_VER < 1917
+#pragma optimize("", off)
+#endif
+
  static void
 new_Hesoprod(EvalWorkspace *ew, int nov, int *ov, real *oc, int nR, int *Rov, real *Roc, real coef)
 {
