diff --git a/CMakeLists.txt b/CMakeLists.txt
index 6934fca..c2f855d 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -68,7 +68,7 @@ set(GEOGRAM_INSTALL_PKGCONFIG_DIR ${CMAKE_INSTALL_LIBDIR}/pkgconfig CACHE PATH "
 set(GEOGRAM_INSTALL_CMAKE_DIR ${CMAKE_INSTALL_LIBDIR}/cmake/modules CACHE PATH "CMake file install destination")
 
 
-set(VORPALINE_INCLUDE_SUBPATH geogram${VORPALINE_VERSION_MAJOR})
+set(VORPALINE_INCLUDE_SUBPATH ./)
 
 # Determine the current Build-OS (Build-platform without the compiler info)
 string(REGEX REPLACE "-[^-]+$" "" VORPALINE_OS ${VORPALINE_PLATFORM})
@@ -130,6 +130,7 @@ endif()
 add_subdirectory(src/lib/third_party)
 
 if(GEOGRAM_WITH_GRAPHICS)
+find_package(GLFW3 REQUIRED)
 add_subdirectory(src/lib/geogram_gfx)
 endif()
 
@@ -160,7 +161,44 @@ file(REMOVE ${CMAKE_BINARY_DIR}/doc/LICENSE.txt)
 
 # FindGeogram.cmake
 
-install(FILES cmake/FindGeogram.cmake DESTINATION ${GEOGRAM_INSTALL_CMAKE_DIR} COMPONENT devkit)
+# This installation is not suitable for vcpkg
+# install(FILES cmake/FindGeogram.cmake DESTINATION lib/cmake/modules COMPONENT devkit)
+
+set(config_install_dir "share/geogram")
+set(include_install_dir "include")
+set(generated_dir "${CMAKE_CURRENT_BINARY_DIR}/generated")
+
+# Configuration
+set(version_config "${generated_dir}/${PROJECT_NAME}ConfigVersion.cmake")
+set(project_config "${generated_dir}/${PROJECT_NAME}Config.cmake")
+set(targets_export_name "${PROJECT_NAME}Targets")
+set(namespace "${PROJECT_NAME}::")
+
+# Include module with fuction 'write_basic_package_version_file'
+include(CMakePackageConfigHelpers)
+
+write_basic_package_version_file(
+    "${version_config}" COMPATIBILITY SameMajorVersion VERSION ${VORPALINE_VERSION}
+)
+
+configure_package_config_file(
+    "cmake/Config.cmake.in"
+    "${project_config}"
+    INSTALL_DESTINATION "${config_install_dir}"
+)
+ 
+# Config
+install(
+    FILES "${project_config}" "${version_config}"
+    DESTINATION "${config_install_dir}"
+)
+
+# Config
+install(
+    EXPORT "${targets_export_name}"
+    NAMESPACE "${namespace}"
+    DESTINATION "${config_install_dir}"
+)
 
 # Configure CPack
 
diff --git a/src/lib/geogram/CMakeLists.txt b/src/lib/geogram/CMakeLists.txt
index d50b3d2..41ec3e4 100644
--- a/src/lib/geogram/CMakeLists.txt
+++ b/src/lib/geogram/CMakeLists.txt
@@ -69,7 +69,7 @@ endif()
 # path for targets that depend on geogram.
 # See: https://cmake.org/cmake/help/v3.3/command/target_include_directories.html
 # https://stackoverflow.com/questions/26243169/cmake-target-include-directories-meaning-of-scope
-target_include_directories(geogram PUBLIC ${PROJECT_SOURCE_DIR}/src/lib)
+target_include_directories(geogram PUBLIC $<BUILD_INTERFACE:${PROJECT_SOURCE_DIR}/src/lib>)
 
 if(ANDROID)
 target_include_directories(geogram PRIVATE
@@ -78,6 +78,9 @@ ${ANDROID_NDK}/sources/android/native_app_glue
 message(STATUS "building for Android")
 endif()
 
+if(WIN32 AND VORPALINE_BUILD_DYNAMIC)
+    target_compile_definitions(geogram PUBLIC GEO_DYNAMIC_LIBS)
+endif()
 
 set_target_properties(geogram PROPERTIES
 VERSION ${VORPALINE_VERSION}
@@ -101,7 +104,14 @@ target_link_libraries(geogram ${GEOGRAMPLUS_LIBS})
 endif()
 
 # Install the library
-install_devkit_targets(geogram)
+install(
+    TARGETS geogram
+    EXPORT "${PROJECT_NAME}Targets"
+    LIBRARY DESTINATION "lib"
+    ARCHIVE DESTINATION "lib"
+    RUNTIME DESTINATION "bin"
+    INCLUDES DESTINATION "include"
+)
 
 # Install include files for the standard devkit
 install(
diff --git a/src/lib/geogram_gfx/CMakeLists.txt b/src/lib/geogram_gfx/CMakeLists.txt
index 391e607..13b6bcb 100644
--- a/src/lib/geogram_gfx/CMakeLists.txt
+++ b/src/lib/geogram_gfx/CMakeLists.txt
@@ -52,14 +52,21 @@ target_link_libraries(geogram_gfx ${OPENGL_LIBRARIES})
 endif()
 
 # Install the library
-install_devkit_targets(geogram_gfx)
+install(
+    TARGETS geogram_gfx
+    EXPORT "${PROJECT_NAME}Targets"
+    LIBRARY DESTINATION "lib"
+    ARCHIVE DESTINATION "lib"
+    RUNTIME DESTINATION "bin"
+    INCLUDES DESTINATION "include"
+)
 
 # Install include files for the standard devkit
 install(
 DIRECTORY .
 DESTINATION include/${VORPALINE_INCLUDE_SUBPATH}/geogram_gfx
 COMPONENT devkit
-FILES_MATCHING PATTERN *.h
+FILES_MATCHING PATTERN *.h PATTERN *.xpm # xpm is the colrbar file
 )
 
 # Install include files for the full devkit
@@ -67,7 +74,7 @@ install(
 DIRECTORY .
 DESTINATION include/${VORPALINE_INCLUDE_SUBPATH}/geogram_gfx
 COMPONENT devkit-full
-FILES_MATCHING PATTERN *.h
+FILES_MATCHING PATTERN *.h PATTERN *.xpm # xpm is the colrbar file
 )
 
 install(
diff --git a/src/lib/third_party/numerics/CMakeLists.txt b/src/lib/third_party/numerics/CMakeLists.txt
index 5faf2fa..33c9bdd 100644
--- a/src/lib/third_party/numerics/CMakeLists.txt
+++ b/src/lib/third_party/numerics/CMakeLists.txt
@@ -14,10 +14,11 @@ endif()
 
 include_directories(${GEOGRAM_SOURCE_DIR}/src/lib/third_party/numerics/INCLUDE)
 
+set(LAPACK_lapack_WORKS TRUE)
+find_package(BLAS REQUIRED)
+find_package(LAPACK REQUIRED)
 aux_source_directories(SOURCES "Source Files" .)
 aux_source_directories(SOURCES "Source Files\\LIBF2C" LIBF2C)
-aux_source_directories(SOURCES "Source Files\\CBLAS" CBLAS)
-aux_source_directories(SOURCES "Source Files\\CLAPACK" CLAPACK)
 aux_source_directories(SOURCES "Source Files\\SUPERLU" SUPERLU)
 aux_source_directories(SOURCES "Source Files\\ARPACK" ARPACK)
 aux_source_directories(SOURCES "Source Files\\ARPACK_UTIL" ARPACK_UTIL)
@@ -28,6 +29,7 @@ list(REMOVE_ITEM SOURCES "LIBF2C/system_.c")
 
 add_library(geogram_num_3rdparty ${SOURCES})
 
+target_link_libraries(geogram_num_3rdparty ${BLAS_LIBRARIES} ${LAPACK_LIBRARIES})
 if(UNIX)
     target_link_libraries(geogram_num_3rdparty m)
 endif()
diff --git a/src/lib/third_party/numerics/LIBF2C/libf2c_getarg_.c b/src/lib/third_party/numerics/LIBF2C/libf2c_getarg_.c
index 2b69a1e..a4b4c55 100644
--- a/src/lib/third_party/numerics/LIBF2C/libf2c_getarg_.c
+++ b/src/lib/third_party/numerics/LIBF2C/libf2c_getarg_.c
@@ -17,20 +17,21 @@ VOID getarg_(n, s, ls) ftnint *n; char *s; ftnlen ls;
 void getarg_(ftnint *n, char *s, ftnlen ls)
 #endif
 {
-	extern int xargc;
-	extern char **xargv;
-	Const char *t;
-	int i;
+	// extern int xargc;
+	// extern char **xargv;
+	// Const char *t;
+	// int i;
 	
-	if(*n>=0 && *n<xargc)
-		t = xargv[*n];
-	else
-		t = "";
-	for(i = 0; i<ls && *t!='\0' ; ++i)
-		*s++ = *t++;
-	for( ; i<ls ; ++i)
-		*s++ = ' ';
-	}
+	// if(*n>=0 && *n<xargc)
+		// t = xargv[*n];
+	// else
+		// t = "";
+	// for(i = 0; i<ls && *t!='\0' ; ++i)
+		// *s++ = *t++;
+	// for( ; i<ls ; ++i)
+		// *s++ = ' ';
+	// }
+}
 #ifdef __cplusplus
 }
 #endif
diff --git a/src/lib/third_party/numerics/LIBF2C/libf2c_iargc_.c b/src/lib/third_party/numerics/LIBF2C/libf2c_iargc_.c
index 2f29da0..48dfd9f 100644
--- a/src/lib/third_party/numerics/LIBF2C/libf2c_iargc_.c
+++ b/src/lib/third_party/numerics/LIBF2C/libf2c_iargc_.c
@@ -9,8 +9,9 @@ ftnint iargc_()
 ftnint iargc_(void)
 #endif
 {
-extern int xargc;
-return ( xargc - 1 );
+// extern int xargc;
+// return ( xargc - 1 );
+    return -1;
 }
 #ifdef __cplusplus
 }
