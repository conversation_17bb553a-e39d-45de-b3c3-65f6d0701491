diff --git a/build/cmake/GenerateConfigModule.cmake b/build/cmake/GenerateConfigModule.cmake
index 9533c82..d074a1b 100644
--- a/build/cmake/GenerateConfigModule.cmake
+++ b/build/cmake/GenerateConfigModule.cmake
@@ -19,8 +19,8 @@
 
 include(CMakePackageConfigHelpers)
 set(PACKAGE_INCLUDE_INSTALL_DIR "${includedir}/thrift")
-set(PACKAGE_CMAKE_INSTALL_DIR "${cmakedir}/thrift")
-set(PACKAGE_BIN_INSTALL_DIR "${exec_prefix}")
+set(PACKAGE_CMAKE_INSTALL_DIR "${prefix}/share/thrift")
+set(PACKAGE_BIN_INSTALL_DIR "${prefix}/tools/thrift")
 
 # In CYGWIN enviroment below commands does not work properly
 if (NOT CYGWIN)
