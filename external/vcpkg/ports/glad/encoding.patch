diff --git a/glad/__main__.py b/glad/__main__.py
index e7c0544a5..1e10dcc60 100644
--- a/glad/__main__.py	
+++ b/glad/__main__.py
@@ -32,7 +32,7 @@ def main():
         if reproducible:
             logger.info('reproducible build, using packaged specification: \'%s.xml\'', value)
             try:
-                return spec_cls.from_file(glad.files.open_local(value + '.xml'))
+                return spec_cls.from_file(glad.files.open_local(value + '.xml', encoding='utf-8-sig'))
             except IOError:
                 raise ValueError('unable to open reproducible copy of {}.xml, '
                                  'try dropping --reproducible'.format(value))
