{"name": "glad", "version": "0.1.36", "description": "Multi-Language Vulkan/GL/GLES/EGL/GLX/WGL Loader-Generator based on the official specs.", "homepage": "https://github.com/Dav1dde/glad", "documentation": "https://github.com/Dav1dde/glad/wiki", "license": "MIT", "dependencies": ["egl-registry", "opengl-registry", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "default-features": ["loader"], "features": {"egl": {"description": "Use `egl` spec instead of `gl`."}, "extensions": {"description": "Enables all extensions."}, "gl-api-10": {"description": "Imports extensions from OpenGL API specification version 1.0."}, "gl-api-11": {"description": "Imports extensions from OpenGL API specification version 1.1.", "dependencies": [{"name": "glad", "default-features": false, "features": ["gl-api-10"]}]}, "gl-api-12": {"description": "Imports extensions from OpenGL API specification version 1.2.", "dependencies": [{"name": "glad", "default-features": false, "features": ["gl-api-11"]}]}, "gl-api-13": {"description": "Imports extensions from OpenGL API specification version 1.3.", "dependencies": [{"name": "glad", "default-features": false, "features": ["gl-api-12"]}]}, "gl-api-14": {"description": "Imports extensions from OpenGL API specification version 1.4.", "dependencies": [{"name": "glad", "default-features": false, "features": ["gl-api-13"]}]}, "gl-api-15": {"description": "Imports extensions from OpenGL API specification version 1.5.", "dependencies": [{"name": "glad", "default-features": false, "features": ["gl-api-14"]}]}, "gl-api-20": {"description": "Imports extensions from OpenGL API specification version 2.0.", "dependencies": [{"name": "glad", "default-features": false, "features": ["gl-api-15"]}]}, "gl-api-21": {"description": "Imports extensions from OpenGL API specification version 2.1.", "dependencies": [{"name": "glad", "default-features": false, "features": ["gl-api-20"]}]}, "gl-api-30": {"description": "Imports extensions from OpenGL API specification version 3.0.", "dependencies": [{"name": "glad", "default-features": false, "features": ["gl-api-21"]}]}, "gl-api-31": {"description": "Imports extensions from OpenGL API specification version 3.1.", "dependencies": [{"name": "glad", "default-features": false, "features": ["gl-api-30"]}]}, "gl-api-32": {"description": "Imports extensions from OpenGL API specification version 3.2.", "dependencies": [{"name": "glad", "default-features": false, "features": ["gl-api-31"]}]}, "gl-api-33": {"description": "Imports extensions from OpenGL API specification version 3.3.", "dependencies": [{"name": "glad", "default-features": false, "features": ["gl-api-32"]}]}, "gl-api-40": {"description": "Imports extensions from OpenGL API specification version 4.0.", "dependencies": [{"name": "glad", "default-features": false, "features": ["gl-api-33"]}]}, "gl-api-41": {"description": "Imports extensions from OpenGL API specification version 4.1.", "dependencies": [{"name": "glad", "default-features": false, "features": ["gl-api-40"]}]}, "gl-api-42": {"description": "Imports extensions from OpenGL API specification version 4.2.", "dependencies": [{"name": "glad", "default-features": false, "features": ["gl-api-41"]}]}, "gl-api-43": {"description": "Imports extensions from OpenGL API specification version 4.3.", "dependencies": [{"name": "glad", "default-features": false, "features": ["gl-api-42"]}]}, "gl-api-44": {"description": "Imports extensions from OpenGL API specification version 4.4.", "dependencies": [{"name": "glad", "default-features": false, "features": ["gl-api-43"]}]}, "gl-api-45": {"description": "Imports extensions from OpenGL API specification version 4.5.", "dependencies": [{"name": "glad", "default-features": false, "features": ["gl-api-44"]}]}, "gl-api-46": {"description": "Imports extensions from OpenGL API specification version 4.6.", "dependencies": [{"name": "glad", "default-features": false, "features": ["gl-api-45"]}]}, "gl-api-latest": {"description": "Imports extensions from latest OpenGL API specification version.", "dependencies": [{"name": "glad", "default-features": false, "features": ["gl-api-46"]}]}, "gles1-api-10": {"description": "Imports extensions from OpenGL ES 1 specification version 1.0."}, "gles1-api-latest": {"description": "Imports extensions from latest OpenGL ES 1 specification.", "dependencies": [{"name": "glad", "default-features": false, "features": ["gles1-api-10"]}]}, "gles2-api-20": {"description": "Imports extensions from OpenGL ES 2 specification version 2.0."}, "gles2-api-30": {"description": "Imports extensions from OpenGL ES 2 specification version 3.0.", "dependencies": [{"name": "glad", "default-features": false, "features": ["gles2-api-20"]}]}, "gles2-api-31": {"description": "Imports extensions from OpenGL ES 2 specification version 3.1.", "dependencies": [{"name": "glad", "default-features": false, "features": ["gles2-api-30"]}]}, "gles2-api-32": {"description": "Imports extensions from OpenGL ES 2 specification version 3.2.", "dependencies": [{"name": "glad", "default-features": false, "features": ["gles2-api-31"]}]}, "gles2-api-latest": {"description": "Imports extensions from latest OpenGL ES 2 specification.", "dependencies": [{"name": "glad", "default-features": false, "features": ["gles2-api-32"]}]}, "glsc2-api-20": {"description": "Imports extensions from OpenGL SC API specification version 2.0."}, "glsc2-api-latest": {"description": "Imports extensions from latest OpenGL SC API specification.", "dependencies": [{"name": "glad", "default-features": false, "features": ["glsc2-api-20"]}]}, "glx": {"description": "Use `glx` spec instead of `gl`. Only available with the X Window System.", "supports": "!(windows | uwp)"}, "loader": {"description": "Generate loader logic."}, "wgl": {"description": "Use `wgl` spec instead of `gl`. Only available for Windows and UWP platforms.", "supports": "!(linux | osx)"}}}