﻿diff --git a/CMakeLists.txt b/CMakeLists.txt
index c4031a6..6a106e9 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -23,16 +23,6 @@ project(GLAD VERSION 0.1.34 LANGUAGES C)
 
 set(GLAD_DIR "${CMAKE_CURRENT_SOURCE_DIR}")
 
-# Find the python interpreter, set the PYTHON_EXECUTABLE variable
-if (CMAKE_VERSION VERSION_LESS 3.12)
-    # this logic is deprecated in CMake after 3.12
-    find_package(PythonInterp REQUIRED)
-else()
-    # the new hotness.  This will preferentially find Python3 instead of Python2
-    find_package(Python)
-    set(PYTHON_EXECUTABLE ${Python_EXECUTABLE})
-endif()
-
 # Options
 set(GLAD_OUT_DIR "${CMAKE_CURRENT_BINARY_DIR}" CACHE STRING "Output directory")
 set(GLAD_PROFILE "compatibility" CACHE STRING "OpenGL profile")
