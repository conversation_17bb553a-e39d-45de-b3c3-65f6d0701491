{"$comment": "Automatically generated by scripts/boost/generate-ports.ps1", "name": "boost-parameter", "version": "1.87.0", "description": "Boost parameter module", "homepage": "https://www.boost.org/libs/parameter", "license": "BSL-1.0", "dependencies": [{"name": "boost-cmake", "version>=": "1.87.0"}, {"name": "boost-config", "version>=": "1.87.0"}, {"name": "boost-core", "version>=": "1.87.0"}, {"name": "boost-function", "version>=": "1.87.0"}, {"name": "boost-fusion", "version>=": "1.87.0"}, {"name": "boost-headers", "version>=": "1.87.0"}, {"name": "boost-mp11", "version>=": "1.87.0"}, {"name": "boost-mpl", "version>=": "1.87.0"}, {"name": "boost-optional", "version>=": "1.87.0"}, {"name": "boost-preprocessor", "version>=": "1.87.0"}, {"name": "boost-type-traits", "version>=": "1.87.0"}, {"name": "boost-utility", "version>=": "1.87.0"}]}