diff --git a/imgui_extra_math.h b/imgui_extra_math.h
index 5c46cc4..18f6dad 100644
--- a/imgui_extra_math.h
+++ b/imgui_extra_math.h
@@ -30,8 +30,10 @@ struct ImLine
 
 
 //------------------------------------------------------------------------------
+# if IMGUI_VERSION_NUM < 19002
 inline bool operator==(const ImVec2& lhs, const ImVec2& rhs);
 inline bool operator!=(const ImVec2& lhs, const ImVec2& rhs);
+#endif
 inline ImVec2 operator*(const float lhs, const ImVec2& rhs);
 # if IMGUI_VERSION_NUM < 18955
 inline ImVec2 operator-(const ImVec2& lhs);
diff --git a/imgui_extra_math.inl b/imgui_extra_math.inl
index 8b1b071..acc1254 100644
--- a/imgui_extra_math.inl
+++ b/imgui_extra_math.inl
@@ -19,6 +19,7 @@
 
 
 //------------------------------------------------------------------------------
+# if IMGUI_VERSION_NUM < 19002
 inline bool operator==(const ImVec2& lhs, const ImVec2& rhs)
 {
     return lhs.x == rhs.x && lhs.y == rhs.y;
@@ -28,6 +29,7 @@ inline bool operator!=(const ImVec2& lhs, const ImVec2& rhs)
 {
     return lhs.x != rhs.x || lhs.y != rhs.y;
 }
+#endif
 
 inline ImVec2 operator*(const float lhs, const ImVec2& rhs)
 {
