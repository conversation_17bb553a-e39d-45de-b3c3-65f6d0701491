diff --git a/imgui_node_editor.cpp b/imgui_node_editor.cpp
index 1d2bb06..f70d099 100644
--- a/imgui_node_editor.cpp
+++ b/imgui_node_editor.cpp
@@ -60,6 +60,7 @@ namespace Detail {
 
 DECLARE_KEY_TESTER(ImGuiKey_F);
 DECLARE_KEY_TESTER(ImGuiKey_D);
+DECLARE_KEY_TESTER(ImGuiKey_Delete);
 
 static inline int GetKeyIndexForF()
 {
@@ -70,6 +71,11 @@ static inline int GetKeyIndexForD()
 {
     return KeyTester_ImGuiKey_D::Get<ImGuiKey_>(nullptr);
 }
+
+static inline int GetKeyIndexForDelete()
+{
+    return KeyTester_ImGuiKey_Delete::Get<ImGuiKey_>(nullptr);
+}
 # else
 static inline ImGuiKey GetKeyIndexForF()
 {
@@ -80,6 +86,11 @@ static inline ImGuiKey GetKeyIndexForD()
 {
     return ImGuiKey_D;
 }
+
+static inline ImGuiKey GetKeyIndexForDelete()
+{
+    return ImGuiKey_Delete;
+}
 # endif
 
 } // namespace Detail
@@ -4391,6 +4402,7 @@ ed::EditorAction::AcceptResult ed::ShortcutAction::Accept(const Control& control
     Action candidateAction = None;
 
     auto& io = ImGui::GetIO();
+# if !defined(IMGUI_VERSION_NUM) || (IMGUI_VERSION_NUM < 18822)
     if (io.KeyCtrl && !io.KeyShift && !io.KeyAlt && ImGui::IsKeyPressed(ImGui::GetKeyIndex(ImGuiKey_X)))
         candidateAction = Cut;
     if (io.KeyCtrl && !io.KeyShift && !io.KeyAlt && ImGui::IsKeyPressed(ImGui::GetKeyIndex(ImGuiKey_C)))
@@ -4401,6 +4413,18 @@ ed::EditorAction::AcceptResult ed::ShortcutAction::Accept(const Control& control
         candidateAction = Duplicate;
     if (!io.KeyCtrl && !io.KeyShift && !io.KeyAlt && ImGui::IsKeyPressed(ImGui::GetKeyIndex(ImGuiKey_Space)))
         candidateAction = CreateNode;
+# else
+    if (io.KeyCtrl && !io.KeyShift && !io.KeyAlt && ImGui::IsKeyPressed(ImGuiKey_X))
+        candidateAction = Cut;
+    if (io.KeyCtrl && !io.KeyShift && !io.KeyAlt && ImGui::IsKeyPressed(ImGuiKey_C))
+        candidateAction = Copy;
+    if (io.KeyCtrl && !io.KeyShift && !io.KeyAlt && ImGui::IsKeyPressed(ImGuiKey_V))
+        candidateAction = Paste;
+    if (io.KeyCtrl && !io.KeyShift && !io.KeyAlt && ImGui::IsKeyPressed(GetKeyIndexForD()))
+        candidateAction = Duplicate;
+    if (!io.KeyCtrl && !io.KeyShift && !io.KeyAlt && ImGui::IsKeyPressed(ImGuiKey_Space))
+        candidateAction = CreateNode;
+# endif
 
     if (candidateAction != None)
     {
@@ -4953,7 +4977,7 @@ ed::EditorAction::AcceptResult ed::DeleteItemsAction::Accept(const Control& cont
         return False;
 
     auto& io = ImGui::GetIO();
-    if (Editor->CanAcceptUserInput() && ImGui::IsKeyPressed(ImGui::GetKeyIndex(ImGuiKey_Delete)) && Editor->AreShortcutsEnabled())
+    if (Editor->CanAcceptUserInput() && ImGui::IsKeyPressed(GetKeyIndexForDelete()) && Editor->AreShortcutsEnabled())
     {
         auto& selection = Editor->GetSelectedObjects();
         if (!selection.empty())
