diff --git a/yasm.props b/yasm.props
index 06efe7a..10f6309 100644
--- a/yasm.props
+++ b/yasm.props
@@ -6,15 +6,15 @@
     <YASMAfterTargets>CustomBuild</YASMAfterTargets>
   </PropertyGroup>
   <PropertyGroup>
-    <YasmPath Condition= "'$(YASMPATH)' == ''">$(VCInstallDir)</YasmPath>
+    <YasmPath Condition= "'$(YasmPath)' == ''">$(VCInstallDir)</YasmPath>
   </PropertyGroup>
   <ItemDefinitionGroup>
     <YASM>
       <Preprocessor>0</Preprocessor>
       <Parser>0</Parser>
       <ObjectFileName>$(IntDir)%(FileName).obj</ObjectFileName>
-      <CommandLineTemplate Condition="'$(Platform)' == 'Win32'">"$(YasmPath)"yasm.exe -Xvc -f win32 [AllOptions] [AdditionalOptions] "%(FullPath)"</CommandLineTemplate>
-      <CommandLineTemplate Condition="'$(Platform)' == 'x64'">"$(YasmPath)"yasm.exe -Xvc -f win64 [AllOptions] [AdditionalOptions] "%(FullPath)"</CommandLineTemplate>
+      <CommandLineTemplate Condition="'$(Platform)' == 'Win32'">"$(YasmPath)" -Xvc -f win32 [AllOptions] [AdditionalOptions] "%(FullPath)"</CommandLineTemplate>
+      <CommandLineTemplate Condition="'$(Platform)' == 'x64'">"$(YasmPath)" -Xvc -f win64 [AllOptions] [AdditionalOptions] "%(FullPath)"</CommandLineTemplate>
       <CommandLineTemplate Condition="'$(Platform)' != 'Win32' and '$(Platform)' != 'x64'">echo YASM not supported on this platform
 exit 1</CommandLineTemplate>
       <ExecutionDescription>%(Identity)</ExecutionDescription>
