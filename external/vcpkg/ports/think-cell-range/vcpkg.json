{"name": "think-cell-range", "version": "2023.1", "port-version": 1, "description": "think-cell's range library", "homepage": "https://github.com/think-cell/think-cell-library", "license": "BSL-1.0", "dependencies": ["boost-filesystem", "boost-fusion", "boost-integer", "boost-intrusive", "boost-lexical-cast", "boost-mpl", "boost-multi-index", "boost-predef", "boost-preprocessor", "boost-range", "boost-spirit"]}