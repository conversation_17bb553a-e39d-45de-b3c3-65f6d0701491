{"name": "qt", "version": "6.8.3", "description": "A cross-platform application and UI framework.", "homepage": "https://www.qt.io/", "license": null, "dependencies": [{"name": "qt3d", "default-features": false, "features": ["animation", "assimp", "extras", "input", "logic", "qml", "render", "rhi"]}, {"name": "qt5compat", "default-features": false, "features": ["big-codecs", "codecs", "qml", "textcodec"]}, {"name": "qtapplicationmanager", "default-features": false, "features": ["systemd-watchdog"], "platform": "linux"}, {"name": "qtapplicationmanager", "features": ["installer", "package-server"], "platform": "android | ios | linux | osx | qnx | (windows & !uwp & (arm64 | x64))"}, {"name": "qtbase", "default-features": false}, {"name": "qtcharts", "default-features": false, "features": ["designer", "qml"]}, "qtcoap", "qtconnectivity", "qtdatavis3d", "qtdeclarative", {"name": "qtdeviceutilities", "default-features": false, "platform": "linux"}, "qtdoc", "qtgraphs", "qtgrpc", {"name": "qtimageformats", "default-features": false, "features": ["jasper", "tiff", "webp"]}, "qtinterfaceframework", "qtlanguageserver", {"name": "qtlottie", "default-features": false, "features": ["qml"]}, "qtmqtt", {"name": "qtmultimedia", "default-features": false, "features": ["ffmpeg", "qml", "widgets"]}, {"name": "qtmultimedia", "default-features": false, "features": [{"name": "gstreamer", "platform": "linux"}], "platform": "!windows"}, "qtnetworkauth", {"name": "qtopcua", "features": ["qml"]}, {"name": "qtpositioning", "default-features": false, "features": ["qml"]}, {"name": "qtquick3d", "default-features": false}, "qtquicktimeline", {"name": "qtremoteobjects", "default-features": false, "features": ["qml"]}, {"name": "qtscxml", "default-features": false, "features": ["qml"]}, {"name": "qtsensors", "default-features": false, "features": ["qml"]}, "qtserialbus", "qtserialport", "qtshadertools", {"name": "qtspeech", "default-features": false, "platform": "!(windows & x86)"}, "qtsvg", {"name": "qttools", "default-features": false, "features": ["assistant", "designer", "qml"]}, "qttranslations", "qtvirtualkeyboard", {"name": "qtwebchannel", "default-features": false, "features": ["qml"]}, {"name": "qtwebengine", "default-features": false, "features": ["geolocation", "spellchecker", "webchannel"], "platform": "!static & !(windows & arm)"}, {"name": "qtwebsockets", "default-features": false, "features": ["qml"]}, {"name": "qtwebview", "default-features": false}, {"name": "qtwebview", "default-features": false, "features": ["webengine"], "platform": "!static & !(windows & arm)"}], "default-features": ["default-features"], "features": {"default-features": {"description": "Platform-dependent default features", "dependencies": [{"name": "qtactiveqt", "features": ["qml"], "platform": "windows"}, {"name": "qtquick3d", "features": ["openxr"], "platform": "!uwp"}, {"name": "qtwayland", "features": ["qml"], "platform": "linux"}]}}}