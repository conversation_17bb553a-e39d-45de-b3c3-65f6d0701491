{"name": "directxtk12", "version-date": "2025-03-20", "description": "A collection of helper classes for writing DirectX 12 code in C++.", "homepage": "https://github.com/Microsoft/DirectXTK12", "documentation": "https://github.com/microsoft/DirectXTK12/wiki", "license": "MIT", "supports": "windows & !arm32", "dependencies": [{"name": "directx-dxc", "host": true, "platform": "windows & !xbox"}, {"name": "directx-headers", "platform": "windows & !xbox"}, "directxmath", {"name": "ms-gdkx", "platform": "xbox"}, {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "default-features": ["xaudio2-9"], "features": {"gameinput": {"description": "Build using GameInput API for input processing", "supports": "windows & x64 & !uwp", "dependencies": ["gameinput"]}, "spectre": {"description": "Build Spectre-mitigated library"}, "tools": {"description": "MakeSpriteFont and xwbtool command-line tools", "supports": "windows & !uwp & !xbox & (x64 | arm64 | arm64ec)"}, "xaudio2-9": {"description": "Build with XAudio 2.9 support for Windows 10/11"}, "xaudio2redist": {"description": "Build with XAudio2Redist", "dependencies": ["xaudio2redist"]}}}