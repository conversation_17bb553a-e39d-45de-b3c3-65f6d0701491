diff --git a/cmake/FindCURSES.cmake b/cmake/FindCURSES.cmake
index a0755c697..f60735a91 100644
--- a/cmake/FindCURSES.cmake
+++ b/cmake/FindCURSES.cmake
@@ -107,7 +107,7 @@ else()
       set(CURSES_INCLUDE "<curses.h>")
 
       find_library(CURSES_LIBRARY
-        NAMES curses
+        NAMES curses pdcurses
         PATHS ${PC_PDCurses_LIBRARY_DIRS}
       )
 
diff --git a/cmake/FindPthreadW32.cmake b/cmake/FindPthreadW32.cmake
index 4fded1eb7..0d44af310 100644
--- a/cmake/FindPthreadW32.cmake
+++ b/cmake/FindPthreadW32.cmake
@@ -54,7 +54,7 @@ find_path(PThreadW32_INCLUDE_DIR
   PATH_SUFFIXES pthreadw32
 )
 find_library(PThreadW32_LIBRARY
-  NAMES pthreadVC2
+  NAMES pthreadVC2 pthreadVC3
   PATHS ${PC_PThreadW32_LIBRARY_DIRS}
 )
 
