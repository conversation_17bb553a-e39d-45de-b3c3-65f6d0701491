diff --git a/freshclam/CMakeLists.txt b/freshclam/CMakeLists.txt
index 545d5d4ba..59cbbc4ce 100644
--- a/freshclam/CMakeLists.txt
+++ b/freshclam/CMakeLists.txt
@@ -28,18 +28,8 @@ target_link_libraries(freshclam-bin
         ClamAV::libfreshclam
         ClamAV::libclamav
         ClamAV::shared )
-if(WIN32)
-    install(TARGETS freshclam-bin DESTINATION ${CMAKE_INSTALL_PREFIX})
-else()
-    install(TARGETS freshclam-bin DESTINATION ${CMAKE_INSTALL_BINDIR})
-endif()
 
-# Install an empty database directory
-if(WIN32)
-    INSTALL(CODE "FILE(MAKE_DIRECTORY \${ENV}\${CMAKE_INSTALL_PREFIX}/database)")
-else()
-    INSTALL(CODE "FILE(MAKE_DIRECTORY \${ENV}\${CMAKE_INSTALL_PREFIX}/share/clamav)")
-endif()
+install(TARGETS freshclam-bin)
 
 # now we rename resman-bin executable to resman using target properties
 set_target_properties( freshclam-bin
diff --git a/libclamav/CMakeLists.txt b/libclamav/CMakeLists.txt
index bb14510db..136ea308a 100644
--- a/libclamav/CMakeLists.txt
+++ b/libclamav/CMakeLists.txt
@@ -564,11 +564,7 @@ if(ENABLE_SHARED_LIB)
     else()
         target_link_libraries( clamav PUBLIC Iconv::Iconv )
     endif()
-    if(WIN32)
-        install(TARGETS clamav DESTINATION ${CMAKE_INSTALL_PREFIX})
-    else()
-        install(TARGETS clamav DESTINATION ${CMAKE_INSTALL_LIBDIR})
-    endif()
+    install(TARGETS clamav)
 
     if(LLVM_FOUND)
         target_link_directories( clamav PRIVATE ${LLVM_LIBRARY_DIRS} )
@@ -609,11 +605,7 @@ if(ENABLE_STATIC_LIB)
         COMPILE_FLAGS       "${WARNCFLAGS}"
         VERSION ${LIBCLAMAV_VERSION} SOVERSION ${LIBCLAMAV_SOVERSION})
     target_compile_definitions(clamav_static PUBLIC clamav_staticLIB)
-    if(WIN32)
-        install(TARGETS clamav_static DESTINATION ${CMAKE_INSTALL_PREFIX})
-    else()
-        install(TARGETS clamav_static DESTINATION ${CMAKE_INSTALL_LIBDIR})
-    endif()
+    install(TARGETS clamav_static)
 
     add_library( ClamAV::libclamav_static ALIAS clamav_static )
 endif()
diff --git a/libclammspack/CMakeLists.txt b/libclammspack/CMakeLists.txt
index 1b5edbd4d..bba781d76 100644
--- a/libclammspack/CMakeLists.txt
+++ b/libclammspack/CMakeLists.txt
@@ -73,11 +73,7 @@ endif()
 target_sources( mspack
     PUBLIC
         "${CMAKE_CURRENT_SOURCE_DIR}/mspack/mspack.h" )
-if(WIN32)
-    install(TARGETS mspack DESTINATION ${CMAKE_INSTALL_PREFIX})
-else()
-    install(TARGETS mspack DESTINATION ${CMAKE_INSTALL_LIBDIR})
-endif()
+install(TARGETS mspack)
 
 # Public (forwarded) dependencies.
 target_link_libraries( mspack
diff --git a/libclamunrar/CMakeLists.txt b/libclamunrar/CMakeLists.txt
index 03dbc3387..267690b1c 100644
--- a/libclamunrar/CMakeLists.txt
+++ b/libclamunrar/CMakeLists.txt
@@ -90,10 +90,6 @@ target_link_libraries( clamunrar
     PUBLIC
         unrar_obj )
 
-if(WIN32)
-    install(TARGETS clamunrar DESTINATION ${CMAKE_INSTALL_PREFIX})
-else()
-    install(TARGETS clamunrar DESTINATION ${CMAKE_INSTALL_LIBDIR})
-endif()
+install(TARGETS clamunrar)
 
 add_library( ClamAV::libclamunrar ALIAS clamunrar )
diff --git a/libclamunrar_iface/CMakeLists.txt b/libclamunrar_iface/CMakeLists.txt
index 42f81544d..61f21da33 100644
--- a/libclamunrar_iface/CMakeLists.txt
+++ b/libclamunrar_iface/CMakeLists.txt
@@ -55,11 +55,7 @@ if(ENABLE_UNRAR)
         PUBLIC
             ClamAV::libclamunrar_iface_iface)
 
-    if(WIN32)
-        install(TARGETS clamunrar_iface DESTINATION ${CMAKE_INSTALL_PREFIX})
-    else()
-        install(TARGETS clamunrar_iface DESTINATION ${CMAKE_INSTALL_LIBDIR})
-    endif()
+    install(TARGETS clamunrar_iface)
 
     add_library( ClamAV::libclamunrar_iface ALIAS clamunrar_iface )
-endif()
\ No newline at end of file
+endif()
diff --git a/libfreshclam/CMakeLists.txt b/libfreshclam/CMakeLists.txt
index 6fb711818..27321ceb4 100644
--- a/libfreshclam/CMakeLists.txt
+++ b/libfreshclam/CMakeLists.txt
@@ -65,11 +65,7 @@ if(ENABLE_SHARED_LIB)
     set_target_properties(freshclam PROPERTIES
         COMPILE_FLAGS "${WARNCFLAGS}"
         VERSION ${LIBFRESHCLAM_VERSION} SOVERSION ${LIBFRESHCLAM_SOVERSION})
-    if(WIN32)
-        install(TARGETS freshclam DESTINATION ${CMAKE_INSTALL_PREFIX})
-    else()
-        install(TARGETS freshclam DESTINATION ${CMAKE_INSTALL_LIBDIR})
-    endif()
+    install(TARGETS freshclam)
 
     add_library( ClamAV::libfreshclam ALIAS freshclam )
 endif()
@@ -88,11 +84,7 @@ if(ENABLE_STATIC_LIB)
         COMPILE_FLAGS       "${WARNCFLAGS}"
         VERSION ${LIBFRESHCLAM_VERSION} SOVERSION ${LIBFRESHCLAM_SOVERSION})
     target_compile_definitions(freshclam_static PUBLIC freshclam_staticLIB)
-    if(WIN32)
-        install(TARGETS freshclam_static DESTINATION ${CMAKE_INSTALL_PREFIX})
-    else()
-        install(TARGETS freshclam_static DESTINATION ${CMAKE_INSTALL_LIBDIR})
-    endif()
+    install(TARGETS freshclam_static)
 
     add_library( ClamAV::libfreshclam_static ALIAS freshclam_static )
 endif()
