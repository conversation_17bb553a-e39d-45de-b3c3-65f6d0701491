diff --git a/CMakeLists.txt b/CMakeLists.txt
index 83a921a..34bf0b4 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -249,12 +249,8 @@ if(PQP_FOUND)
     set(OMPLAPP_LIBRARIES ${OMPLAPP_LIBRARIES} ${PQP_LIBRARY})
 endif()
 
-add_subdirectory(ompl/doc)
-add_subdirectory(ompl/src)
-add_subdirectory(ompl/py-bindings)
-add_subdirectory(ompl/tests)
-add_subdirectory(ompl/demos)
-add_subdirectory(ompl/scripts)
+find_package(ompl CONFIG REQUIRED)
+add_custom_target(clean_bindings)
 add_subdirectory(gui)
 add_subdirectory(doc)
 add_subdirectory(src)
@@ -287,6 +283,8 @@ if(OMPL_BUILD_PYBINDINGS)
     endif()
 endif()
 
+return()
+
 if(OPENGL_FOUND AND NOT MSVC)
     target_link_flags(ompl ompl_app_base ompl_app)
     set(PKG_NAME "ompl")
