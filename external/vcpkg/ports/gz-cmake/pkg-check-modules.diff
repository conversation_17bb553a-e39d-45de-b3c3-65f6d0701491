diff --git a/cmake/GzPkgConfig.cmake b/cmake/GzPkgConfig.cmake
index 5fcdb76..9ff169c 100644
--- a/cmake/GzPkgConfig.cmake
+++ b/cmake/GzPkgConfig.cmake
@@ -105,6 +105,7 @@ macro(gz_pkg_check_modules_quiet package signature)
     unset(${package}_LIBRARIES)
 
     pkg_check_modules(${package}
+                      IMPORTED_TARGET
                       ${gz_pkg_check_modules_quiet_arg}
                       ${gz_pkg_check_modules_no_cmake_environment_path_arg}
                       ${signature})
@@ -155,15 +156,13 @@ macro(gz_pkg_check_modules_quiet package signature)
       #
       # TODO: How would we deal with multiple modules that are in different
       # directories? How does cmake-3.6+ handle that situation?
-      _gz_pkgconfig_find_libraries(
-        ${package}_LIBRARIES
-        ${package}
-        "${${package}_LIBRARIES}"
-        "${${package}_LIBRARY_DIRS}")
-
-      include(GzImportTarget)
-      gz_import_target(${package} ${_gz_pkg_check_modules_interface_option}
-        TARGET_NAME ${gz_pkg_check_modules_TARGET_NAME})
+
+      if(NOT TARGET ${gz_pkg_check_modules_TARGET_NAME})
+        add_library(${gz_pkg_check_modules_TARGET_NAME} INTERFACE IMPORTED)
+        set_target_properties(${gz_pkg_check_modules_TARGET_NAME} PROPERTIES
+          INTERFACE_LINK_LIBRARIES PkgConfig::${package}
+        )
+      endif()
 
     endif()
 
