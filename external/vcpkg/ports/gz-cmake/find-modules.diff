diff --git a/cmake/FindFreeImage.cmake b/cmake/FindFreeImage.cmake
index 8a5836e..d7e41d2 100644
--- a/cmake/FindFreeImage.cmake
+++ b/cmake/FindFreeImage.cmake
@@ -23,7 +23,23 @@ set(minor_version ${FreeImage_FIND_VERSION_MINOR})
 # Set the full version number
 set(full_version ${major_version}.${minor_version})
 
-if (NOT WIN32)
+if(1)
+  find_package(freeimage CONFIG) # not using NAMES to avoid version check
+  if(freeimage_FOUND)
+    set(FreeImage_FOUND 1)
+    set(FreeImage_LIBRARIES freeimage::FreeImage)
+    include(GzImportTarget)
+    gz_import_target(FreeImage INTERFACE)
+    # sorry, no transitive link libs for pkgconfig
+    include(GzPkgConfig)
+    find_library(FreeImage_LIBRARY NAMES FreeImage FreeImaged NAMES_PER_DIR)
+    if(FreeImage_LIBRARY MATCHES "FreeImaged[^/]*\$")
+      gz_pkg_config_library_entry(FreeImage "FreeImaged")
+    else()
+      gz_pkg_config_library_entry(FreeImage "FreeImage")
+    endif()
+  endif()
+elseif(0)
   include(GzPkgConfig)
   gz_pkg_config_library_entry(FreeImage freeimage)
 
diff --git a/cmake/FindGTS.cmake b/cmake/FindGTS.cmake
index b400483..48cf9f7 100644
--- a/cmake/FindGTS.cmake
+++ b/cmake/FindGTS.cmake
@@ -16,7 +16,7 @@
 ########################################
 # Find GNU Triangulation Surface Library
 
-if (NOT WIN32)
+if(1)
   # Configuration using pkg-config modules
   include(GzPkgConfig)
   gz_pkg_check_modules(GTS gts)
diff --git a/cmake/FindGzBullet.cmake b/cmake/FindGzBullet.cmake
index 70f7a76..e39a3fd 100644
--- a/cmake/FindGzBullet.cmake
+++ b/cmake/FindGzBullet.cmake
@@ -31,7 +31,7 @@ endif()
 # Bullet. Force MODULE mode to use the FindBullet.cmake file distributed with
 # CMake. Otherwise, we may end up using the BulletConfig.cmake file distributed
 # with Bullet, which uses relative paths and may break transitive dependencies.
-find_package(Bullet MODULE ${gz_quiet_arg})
+find_package(BULLET NAMES Bullet ${gz_quiet_arg})
 
 set(GzBullet_FOUND false)
 # create Bullet target
diff --git a/cmake/FindGzOGRE2.cmake b/cmake/FindGzOGRE2.cmake
index 2decd84..dd2c2c2 100644
--- a/cmake/FindGzOGRE2.cmake
+++ b/cmake/FindGzOGRE2.cmake
@@ -146,7 +146,7 @@ endmacro()
 find_package(PkgConfig QUIET)
 if (PkgConfig_FOUND)
   set(PKG_CONFIG_PATH_ORIGINAL $ENV{PKG_CONFIG_PATH})
-  foreach (GZ_OGRE2_PROJECT_NAME "OGRE2" "OGRE-Next")
+  foreach (GZ_OGRE2_PROJECT_NAME "OGRE-Next")
     message(STATUS "Looking for OGRE using the name: ${GZ_OGRE2_PROJECT_NAME}")
     if (GZ_OGRE2_PROJECT_NAME STREQUAL "OGRE2")
       set(OGRE2_INSTALL_PATH "OGRE-2.${GzOGRE2_FIND_VERSION_MINOR}")
@@ -173,6 +173,7 @@ if (PkgConfig_FOUND)
       set(GZ_PKG_NAME ${OGRE2_INSTALL_PATH})
       set(OGRE2_FOUND ${${GZ_OGRE2_PROJECT_NAME}_FOUND})  # sync possible OGRE-Next to OGRE2
       fix_pkgconfig_prefix_jammy_bug("${${GZ_OGRE2_PROJECT_NAME}_LIBRARY_DIRS}" OGRE2_LIBRARY_DIRS)
+      set(OGRE2_LIBRARY_DIRS ${${GZ_OGRE2_PROJECT_NAME}_LIBRARY_DIRS})
       set(OGRE2_LIBRARIES ${${GZ_OGRE2_PROJECT_NAME}_LIBRARIES})  # sync possible Ogre-Next ot OGRE2
     else()
       # look for OGRE2 installed from source
@@ -264,10 +265,10 @@ if (PkgConfig_FOUND)
       get_filename_component(dir_name "${dir}" NAME)
       if ("${dir_name}" STREQUAL ${GZ_PKG_NAME})
         set(dir_include "${dir}/RenderSystems/GL3Plus")
-      else()
-        set(dir_include "${dir}")
+        if(EXISTS "${dir_include}")
+          list(APPEND OGRE2_INCLUDE_DIRS "${dir_include}")
+        endif()
       endif()
-      list(APPEND OGRE2_INCLUDE_DIRS ${dir_include})
     endforeach()
 
     file(READ ${OGRE2_INCLUDE}/OgrePrerequisites.h OGRE_TEMP_VERSION_CONTENT)
@@ -309,6 +310,9 @@ if (PkgConfig_FOUND)
           "${OGRE2LIBNAME}${component}_d"
           "${OGRE2LIBNAME}${component}.${OGRE2_VERSION}"
           "${OGRE2LIBNAME}${component}"
+          "${OGRE2LIBNAME}${component}Static"
+          "${OGRE2LIBNAME}${component}Static_d"
+        NAMES_PER_DIR
         HINTS ${OGRE2_LIBRARY_DIRS})
       if (NOT "${OGRE2-${component}}" STREQUAL "OGRE2-${component}-NOTFOUND")
         message(STATUS "  + component ${component}: found")
@@ -400,17 +404,22 @@ else() #PkgConfig_FOUND
   set(OGRE2_VERSION_MINOR "")
   set(OGRE2_RESOURCE_PATH "")
 
-  set(OGRE2_SEARCH_VER "OGRE-${GzOGRE2_FIND_VERSION_MAJOR}.${GzOGRE2_FIND_VERSION_MINOR}")
+  set(OGRE2_SEARCH_VER "OGRE-Next")
   set(OGRE2_PATHS "")
   set(OGRE2_INC_PATHS "")
   foreach(_rootPath ${VCPKG_CMAKE_FIND_ROOT_PATH})
-      list(APPEND OGRE2_PATHS "${_rootPath}/lib/${OGRE2_SEARCH_VER}/")
-      list(APPEND OGRE2_PATHS "${_rootPath}/lib/${OGRE2_SEARCH_VER}/manual-link/")
+      list(APPEND OGRE2_PATHS "${_rootPath}/lib")
+      list(APPEND OGRE2_PATHS "${_rootPath}/lib/manual-link")
       list(APPEND OGRE2_INC_PATHS "${_rootPath}/include/${OGRE2_SEARCH_VER}")
   endforeach()
 
   find_library(OGRE2_LIBRARY
-    NAMES "OgreMain"
+    NAMES
+     "OgreNextMain"
+     "OgreNextMainStatic"
+     "OgreNextMain_d"
+     "OgreNextMainStatic_d"
+    NAMES_PER_DIR
     HINTS ${OGRE2_PATHS}
     NO_DEFAULT_PATH)
 
@@ -450,8 +459,11 @@ else() #PkgConfig_FOUND
 
       find_library(${PREFIX}_LIBRARY
           NAMES
-              "Ogre${COMPONENT}"
-              "Ogre${COMPONENT}_d"
+              "OgreNext${COMPONENT}"
+              "OgreNext${COMPONENT}_d"
+              "OgreNext${COMPONENT}Static"
+              "OgreNext${COMPONENT}Static_d"
+          NAMES_PER_DIR
           HINTS
               ${OGRE2_LIBRARY_DIRS}
           NO_DEFAULT_PATH)
@@ -506,6 +518,7 @@ else() #PkgConfig_FOUND
   ogre_find_component(Overlay OgreOverlaySystem.h "Overlay")
   ogre_find_component(HlmsPbs OgreHlmsPbs.h Hlms/Pbs/)
   ogre_find_component(HlmsUnlit OgreHlmsUnlit.h Hlms/Unlit)
+  ogre_find_component(PlanarReflections OgrePlanarReflections.h PlanarReflections)
 
   ogre_find_plugin(Plugin_ParticleFX OgreParticleFXPrerequisites.h PlugIns/ParticleFX/include)
   ogre_find_plugin(RenderSystem_GL3Plus OgreGL3PlusRenderSystem.h RenderSystems/GL3Plus/include)
@@ -521,7 +534,7 @@ else() #PkgConfig_FOUND
       if(${HLMS_POS} GREATER -1)
         foreach (dir ${OGRE2_INCLUDE_DIRS})
           get_filename_component(dir_name "${dir}" NAME)
-          if ("${dir_name}" STREQUAL "OGRE-${OGRE2_VERSION_MAJOR}.${OGRE2_VERSION_MINOR}")
+          if ("${dir_name}" STREQUAL "OGRE-Next")
             set(dir_include "${dir}/Hlms/Common")
             if (EXISTS ${dir_include})
               list(APPEND component_INCLUDE_DIRS ${dir_include})
