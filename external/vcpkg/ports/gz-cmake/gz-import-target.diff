diff --git a/cmake/GzImportTarget.cmake b/cmake/GzImportTarget.cmake
index 934df6d..aba4129 100644
--- a/cmake/GzImportTarget.cmake
+++ b/cmake/GzImportTarget.cmake
@@ -83,6 +83,15 @@ macro(gz_import_target package)
       set(gz_import_target_CFLAGS_VAR ${package}_CFLAGS)
     endif()
 
+    # In vcpkg, some <Pkg>_LIBRARIES carry targets. Example: BULLET_LIBRARIES
+    # Targets must not be used for IMPORTED_LOCATION or IMPORTED_IMPLIB.
+    if(NOT gz_import_target_INTERFACE)
+      list(GET "${gz_import_target_LIB_VAR}" 0 gz_import_target_first_item)
+      if(TARGET "${gz_import_target_first_item}")
+        set(gz_import_target_INTERFACE TRUE)
+      endif()
+    endif()
+
     #------------------------------------
     # Link against this "imported" target by saying
     # target_link_libraries(mytarget package::package), instead of linking
