diff --git a/cmake/GzConfigureBuild.cmake b/cmake/GzConfigureBuild.cmake
index 3122c15..f64d695 100644
--- a/cmake/GzConfigureBuild.cmake
+++ b/cmake/GzConfigureBuild.cmake
@@ -265,6 +265,13 @@ macro(gz_configure_build)
           gz_string_append(skip_msg "by user request")
         elseif(${component}_MISSING_DEPS)
           gz_string_append(skip_msg "because the following packages are missing: ${${component}_MISSING_DEPS}")
+          if(NOT DEFINED GZ_LOCK_DEPENDENCIES OR "${GZ_LOCK_DEPENDENCIES}")
+            message(SEND_ERROR "Vcpkg ports must lock dependencies."
+              "  To enable ${component}, provide missing dependencies."
+              "  To disable ${component}, pass -DSKIP_${component}=true."
+              "  To disable this check, pass -DGZ_LOCK_DEPENDENCIES=OFF."
+            )
+          endif()
         endif()
 
         message(STATUS "${skip_msg}")
