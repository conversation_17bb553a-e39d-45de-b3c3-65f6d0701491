diff --git a/source/App/vvencFFapp/CMakeLists.txt b/source/App/vvencFFapp/CMakeLists.txt
index 452301c..947da3d 100644
--- a/source/App/vvencFFapp/CMakeLists.txt
+++ b/source/App/vvencFFapp/CMakeLists.txt
@@ -29,9 +29,9 @@ set_target_properties( ${EXE_NAME} PROPERTIES DEBUG_POSTFIX          "${CMAKE_DE
 set_target_properties( ${EXE_NAME} PROPERTIES RELWITHDEBINFO_POSTFIX "${CMAKE_RELWITHDEBINFO_POSTFIX}" )
 set_target_properties( ${EXE_NAME} PROPERTIES MINSIZEREL_POSTFIX     "${CMAKE_MINSIZEREL_POSTFIX}" )
 
-target_compile_options( ${EXE_NAME} PRIVATE $<$<OR:$<CXX_COMPILER_ID:Clang>,$<CXX_COMPILER_ID:AppleClang>>:-Wall -Werror>
-                                            $<$<CXX_COMPILER_ID:GNU>:-Wall -Werror -fdiagnostics-show-option>
-                                            $<$<CXX_COMPILER_ID:MSVC>:/W4 /WX /wd4100 /wd4244 /wd4251 /wd4459 /wd4996>)
+target_compile_options( ${EXE_NAME} PRIVATE $<$<OR:$<CXX_COMPILER_ID:Clang>,$<CXX_COMPILER_ID:AppleClang>>:-Wall>
+                                            $<$<CXX_COMPILER_ID:GNU>:-Wall -fdiagnostics-show-option>
+                                            $<$<CXX_COMPILER_ID:MSVC>:/wd4100 /wd4244 /wd4251 /wd4459 /wd4996>)
 
 target_include_directories( ${EXE_NAME} PRIVATE ../../Lib )
 target_link_libraries( ${EXE_NAME} Threads::Threads vvenc nlohmann_json::nlohmann_json )
diff --git a/source/App/vvencapp/CMakeLists.txt b/source/App/vvencapp/CMakeLists.txt
index 98e5bb4..f5ae8f1 100644
--- a/source/App/vvencapp/CMakeLists.txt
+++ b/source/App/vvencapp/CMakeLists.txt
@@ -26,9 +26,9 @@ set_target_properties( ${EXE_NAME} PROPERTIES DEBUG_POSTFIX          "${CMAKE_DE
 set_target_properties( ${EXE_NAME} PROPERTIES RELWITHDEBINFO_POSTFIX "${CMAKE_RELWITHDEBINFO_POSTFIX}" )
 set_target_properties( ${EXE_NAME} PROPERTIES MINSIZEREL_POSTFIX     "${CMAKE_MINSIZEREL_POSTFIX}" )
 
-target_compile_options( ${EXE_NAME} PRIVATE $<$<OR:$<CXX_COMPILER_ID:Clang>,$<CXX_COMPILER_ID:AppleClang>>:-Wall -Werror>
-                                            $<$<CXX_COMPILER_ID:GNU>:-Wall -Werror -fdiagnostics-show-option>
-                                            $<$<CXX_COMPILER_ID:MSVC>:/W4 /WX /wd4244 /wd4251 /wd4996>)
+target_compile_options( ${EXE_NAME} PRIVATE $<$<OR:$<CXX_COMPILER_ID:Clang>,$<CXX_COMPILER_ID:AppleClang>>:-Wall>
+                                            $<$<CXX_COMPILER_ID:GNU>:-Wall -fdiagnostics-show-option>
+                                            $<$<CXX_COMPILER_ID:MSVC>:/wd4244 /wd4251 /wd4996>)
 
 target_include_directories( ${EXE_NAME} PRIVATE ../../Lib )
 target_link_libraries( ${EXE_NAME} Threads::Threads vvenc nlohmann_json::nlohmann_json )
diff --git a/source/Lib/vvenc/CMakeLists.txt b/source/Lib/vvenc/CMakeLists.txt
index 827cf41..93bcfc0 100644
--- a/source/Lib/vvenc/CMakeLists.txt
+++ b/source/Lib/vvenc/CMakeLists.txt
@@ -74,9 +74,9 @@ find_path(SIMDE_INCLUDE_DIRS "simde/arm/neon.h")
 include_directories(SYSTEM ${SIMDE_INCLUDE_DIRS})
 
 # set common warning flags
-add_compile_options( "$<$<OR:$<CXX_COMPILER_ID:Clang>,$<CXX_COMPILER_ID:AppleClang>>:-Wall;-Werror;-Wno-deprecated-register;-Wno-unused-const-variable;-Wno-unknown-attributes>" )
-add_compile_options( "$<$<CXX_COMPILER_ID:GNU>:-Wall;-Werror;-Wno-unused-function;;-Wno-unused-variable;;-Wno-sign-compare;;-fdiagnostics-show-option;-Wno-ignored-attributes>" )
-add_compile_options( "$<$<CXX_COMPILER_ID:MSVC>:/W4;/WX;/wd4100;/wd4127;/wd4244;/wd4245;/wd4251;/wd4310;/wd4389;/wd4456;/wd4457;/wd4458;/wd4459;/wd4505;/wd4701;/wd4702;/wd4703;/wd4996>" )
+add_compile_options( "$<$<OR:$<CXX_COMPILER_ID:Clang>,$<CXX_COMPILER_ID:AppleClang>>:-Wall;-Wno-deprecated-register;-Wno-unused-const-variable;-Wno-unknown-attributes>" )
+add_compile_options( "$<$<CXX_COMPILER_ID:GNU>:-Wall;-Wno-unused-function;;-Wno-unused-variable;;-Wno-sign-compare;;-fdiagnostics-show-option;-Wno-ignored-attributes>" )
+add_compile_options( "$<$<CXX_COMPILER_ID:MSVC>:/wd4100;/wd4127;/wd4244;/wd4245;/wd4251;/wd4310;/wd4389;/wd4456;/wd4457;/wd4458;/wd4459;/wd4505;/wd4701;/wd4702;/wd4703;/wd4996>" )
 
 # don't export all symbols from shared libraries by default (gcc: -fvisibility=hidden), only those marked as VVENC_DECL
 #  behavior similar to __declspec(dllexport) on windows
