{"name": "vvenc", "version": "1.7.0", "description": "VVenC is a fast and efficient H.266/VVC encoder implementation.", "homepage": "https://github.com/fraunhoferhhi/vvenc", "license": "BSD-3-<PERSON><PERSON>-<PERSON>", "supports": "!x86 & !arm", "dependencies": ["<PERSON><PERSON><PERSON>-<PERSON><PERSON>", "simde", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"tools": {"description": "Build user tools: vvencFFapp and vvencapp"}}}