diff --git a/Data/SQLite/cmake/PocoDataSQLiteConfig.cmake b/Data/SQLite/cmake/PocoDataSQLiteConfig.cmake
index 5478bab..c5d6d6d 100644
--- a/Data/SQLite/cmake/PocoDataSQLiteConfig.cmake
+++ b/Data/SQLite/cmake/PocoDataSQLiteConfig.cmake
@@ -5,7 +5,7 @@ if(@POCO_UNBUNDLED@)
 	if(CMAKE_VERSION VERSION_LESS "3.14")
 		list(APPEND CMAKE_MODULE_PATH "${CMAKE_CURRENT_LIST_DIR}/V313")
 	endif()
-	find_dependency(SQLite3 REQUIRED)
+	find_dependency(unofficial-sqlite3 REQUIRED)
 endif()
 
 include("${CMAKE_CURRENT_LIST_DIR}/PocoDataSQLiteTargets.cmake")
