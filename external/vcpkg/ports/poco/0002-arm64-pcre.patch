diff --git a/Foundation/src/EventLogChannel.cpp b/Foundation/src/EventLogChannel.cpp
index f9a9fb1..b032f9d 100644
--- a/Foundation/src/EventLogChannel.cpp
+++ b/Foundation/src/EventLogChannel.cpp
@@ -11,6 +11,7 @@
 // SPDX-License-Identifier:	BSL-1.0
 //
 
+#pragma comment(lib,"advapi32.lib")
 
 #include "Poco/EventLogChannel.h"
 #include "Poco/Message.h"
diff --git a/Foundation/src/utils.h b/Foundation/src/utils.h
index c9b6c00..710c199 100644
--- a/Foundation/src/utils.h
+++ b/Foundation/src/utils.h
@@ -129,7 +129,7 @@ int main(int argc, char** argv) {
 //
 // If it prints "correct" then the architecture should be here, in the "correct" section.
 #if defined(_M_X64) || defined(__x86_64__) || \
-    defined(__ARMEL__) || defined(__avr32__) || defined(_M_ARM) || defined(_M_ARM64) || \
+    defined(__ARMEL__) || defined(_M_ARM) || defined(__arm__) || defined(__arm64__) || defined(_M_ARM64) || \
     defined(__hppa__) || defined(__ia64__) || \
     defined(__mips__) || \
     defined(__loongarch__) || \
diff --git a/Net/src/Net.cpp b/Net/src/Net.cpp
index 1a75379..b4081fd 100644
--- a/Net/src/Net.cpp
+++ b/Net/src/Net.cpp
@@ -11,6 +11,7 @@
 // SPDX-License-Identifier:	BSL-1.0
 //
 
+#pragma comment(lib,"advapi32.lib")
 
 #include "Poco/Net/Net.h"
 
diff --git a/Util/src/WinRegistryKey.cpp b/Util/src/WinRegistryKey.cpp
index 7fd1a28..a07c5d2 100644
--- a/Util/src/WinRegistryKey.cpp
+++ b/Util/src/WinRegistryKey.cpp
@@ -11,6 +11,7 @@
 // SPDX-License-Identifier:	BSL-1.0
 //
 
+#pragma comment(lib,"advapi32.lib")
 
 #include "Poco/Util/WinRegistryKey.h"
 #include "Poco/Exception.h"
