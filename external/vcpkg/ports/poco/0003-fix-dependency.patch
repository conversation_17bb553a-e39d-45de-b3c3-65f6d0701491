diff --git a/CMakeLists.txt b/CMakeLists.txt
index 861c27c..d2701ce 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -80,8 +80,6 @@ option(ENABLE_NETSSL_WIN "Enable NetSSL Windows" OFF)
 
 if(<PERSON>NABLE_CRYPTO OR ENABLE_NETSSL OR ENABLE_JWT)
 	find_package(OpenSSL REQUIRED)
-else()
-	find_package(OpenSSL)
 endif()
 
 if(OPENSSL_FOUND)
@@ -111,24 +109,19 @@ else()
 	option(ENABLE_APACHECONNECTOR "Enable ApacheConnector" OFF)
 endif()
 
-if(ENABLE_DATA_MYSQL)
-	find_package(MySQL REQUIRED)
-else()
-	find_package(MySQL)
-endif()
+option(ENABLE_DATA "Enable Data" OFF)
+option(ENABLE_DATA_MYSQL "Enable Data MySQL or MariaDB" OFF)
 
-if(MYSQL_FOUND)
-	option(ENABLE_DATA "Enable Data" ON)
-	option(ENABLE_DATA_MYSQL "Enable Data MySQL or MariaDB" ON)
-else()
-	option(ENABLE_DATA "Enable Data" OFF)
-	option(ENABLE_DATA_MYSQL "Enable Data MySQL or MariaDB" OFF)
+if(ENABLE_DATA_MYSQL)
+    find_package(libmysql)
+    if (NOT libmysql_FOUND)
+        find_package(unofficial-libmariadb CONFIG REQUIRED)
+        set(MYSQL_LIBRARIES unofficial::libmariadb)
+    endif()
 endif()
 
 if(ENABLE_DATA_POSTGRESQL)
 	find_package(PostgreSQL REQUIRED)
-else()
-	find_package(PostgreSQL)
 endif()
 
 if(PostgreSQL_FOUND)
@@ -266,6 +259,9 @@ include(DefinePlatformSpecific)
 # Collect the built libraries and include dirs, the will be used to create the PocoConfig.cmake file
 set(Poco_COMPONENTS "")
 
+# Do not declare the link library in the code!
+add_definitions(-DPOCO_NO_AUTOMATIC_LIBS)
+
 if(ENABLE_TESTS)
 	add_subdirectory(CppUnit)
 	set(ENABLE_XML ON CACHE BOOL "Enable XML" FORCE)
@@ -398,8 +394,11 @@ if(EXISTS ${PROJECT_SOURCE_DIR}/Prometheus AND ENABLE_PROMETHEUS)
 	list(APPEND Poco_COMPONENTS "Prometheus")
 endif()
 
-if(EXISTS ${PROJECT_SOURCE_DIR}/PDF AND ENABLE_PDF)
-	add_subdirectory(PDF)
+if(ENABLE_PDF)
+	include(SelectLibraryConfigurations)
+	find_library(PocoPDF_LIBRARY_RELEASE NAMES libhpdf)
+	find_library(PocoPDF_LIBRARY_DEBUG NAMES libhpdfd)
+	select_library_configurations(PocoPDF)
 	list(APPEND Poco_COMPONENTS "PDF")
 endif()
 
diff --git a/Data/CMakeLists.txt b/Data/CMakeLists.txt
index 95e5019..d7bd537 100644
--- a/Data/CMakeLists.txt
+++ b/Data/CMakeLists.txt
@@ -73,7 +73,7 @@ else(ENABLE_DATA_SQLITE)
 	message(STATUS "SQLite Support Disabled")
 endif()
 
-if(MYSQL_FOUND AND ENABLE_DATA_MYSQL)
+if((unofficial-libmysql_FOUND OR unofficial-libmariadb_FOUND) AND ENABLE_DATA_MYSQL)
 	message(STATUS "MySQL Support Enabled")
 	add_subdirectory(MySQL)
 else()
diff --git a/Data/MySQL/CMakeLists.txt b/Data/MySQL/CMakeLists.txt
index 0ea8701..3c8a426 100644
--- a/Data/MySQL/CMakeLists.txt
+++ b/Data/MySQL/CMakeLists.txt
@@ -21,7 +21,7 @@ set_target_properties(DataMySQL
 	DEFINE_SYMBOL MySQL_EXPORTS
 )
 
-target_link_libraries(DataMySQL PUBLIC Poco::Data MySQL::client)
+target_link_libraries(DataMySQL PUBLIC Poco::Data ${MYSQL_LIBRARIES})
 target_include_directories(DataMySQL
 	PUBLIC
 		$<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
diff --git a/Data/MySQL/include/Poco/Data/MySQL/MySQL.h b/Data/MySQL/include/Poco/Data/MySQL/MySQL.h
index 2386590..81b2c8e 100644
--- a/Data/MySQL/include/Poco/Data/MySQL/MySQL.h
+++ b/Data/MySQL/include/Poco/Data/MySQL/MySQL.h
@@ -57,8 +57,6 @@
 	#endif
 	#if defined(LIBMARIADB)
 		#pragma comment(lib, "libmariadb")
-	#else
-		#pragma comment(lib, "libmysql")
 	#endif
 #endif
 
diff --git a/Data/SQLite/CMakeLists.txt b/Data/SQLite/CMakeLists.txt
index 7141112..0c73beb 100644
--- a/Data/SQLite/CMakeLists.txt
+++ b/Data/SQLite/CMakeLists.txt
@@ -7,7 +7,7 @@ file(GLOB_RECURSE HDRS_G "include/*.h")
 POCO_HEADERS_AUTO(SQLITE_SRCS ${HDRS_G})
 
 if(POCO_UNBUNDLED)
-	find_package(SQLite3 REQUIRED)
+	find_package(unofficial-sqlite3 CONFIG REQUIRED)
 else()
 	# sqlite3
 	POCO_SOURCES(SQLITE_SRCS sqlite3
@@ -39,7 +39,7 @@ target_include_directories(DataSQLite
 )
 
 if(POCO_UNBUNDLED)
-		target_link_libraries(DataSQLite PUBLIC SQLite::SQLite3)
+	target_link_libraries(DataSQLite PUBLIC unofficial::sqlite3::sqlite3)
 	target_compile_definitions(DataSQLite PUBLIC
 		POCO_UNBUNDLED
 		SQLITE_THREADSAFE=1
diff --git a/XML/CMakeLists.txt b/XML/CMakeLists.txt
index cf66250..89e6c8f 100644
--- a/XML/CMakeLists.txt
+++ b/XML/CMakeLists.txt
@@ -20,7 +20,7 @@ endif()
 # If POCO_UNBUNDLED is enabled we try to find the required packages
 # The configuration will fail if the packages are not found
 if(POCO_UNBUNDLED)
-	find_package(EXPAT REQUIRED)
+	find_package(expat CONFIG REQUIRED)
 else()
 	POCO_SOURCES(SRCS expat
 		src/xmlparse.cpp
@@ -50,7 +50,7 @@ target_include_directories(XML
 )
 
 if(POCO_UNBUNDLED)
-	target_link_libraries(XML PUBLIC EXPAT::EXPAT)
+	target_link_libraries(XML PUBLIC expat::expat)
 	target_compile_definitions(XML PUBLIC POCO_UNBUNDLED)
 else()
 	if(WIN32)
diff --git a/XML/cmake/PocoXMLConfig.cmake b/XML/cmake/PocoXMLConfig.cmake
index ef58207..4ed94ec 100644
--- a/XML/cmake/PocoXMLConfig.cmake
+++ b/XML/cmake/PocoXMLConfig.cmake
@@ -4,7 +4,7 @@ if(@POCO_UNBUNDLED@)
 	if(CMAKE_VERSION VERSION_LESS "3.10")
 		list(APPEND CMAKE_MODULE_PATH "${CMAKE_CURRENT_LIST_DIR}/V39")
 	endif()
-	find_dependency(EXPAT REQUIRED)
+	find_dependency(expat CONFIG REQUIRED)
 endif()
 
 include("${CMAKE_CURRENT_LIST_DIR}/PocoXMLTargets.cmake")
diff --git a/cmake/PocoConfig.cmake.in b/cmake/PocoConfig.cmake.in
index 173eacd..90f68fc 100644
--- a/cmake/PocoConfig.cmake.in
+++ b/cmake/PocoConfig.cmake.in
@@ -8,6 +8,12 @@ if (NOT Poco_FIND_COMPONENTS)
     return()
 endif()
 
+include(CMakeFindDependencyMacro)
+find_dependency(ZLIB REQUIRED)
+if(Poco_FIND_REQUIRED_XML)
+    find_dependency(expat CONFIG REQUIRED)
+endif()
+
 set(_Poco_FIND_PARTS_REQUIRED)
 if (Poco_FIND_REQUIRED)
     set(_Poco_FIND_PARTS_REQUIRED REQUIRED)
@@ -23,7 +29,7 @@ set(_Poco_NOTFOUND_MESSAGE)
 
 # Let components find each other, but don't overwrite CMAKE_PREFIX_PATH
 set(_Poco_CMAKE_PREFIX_PATH_old ${CMAKE_PREFIX_PATH})
-set(CMAKE_PREFIX_PATH ${_Poco_install_prefix})
+set(CMAKE_PREFIX_PATH ${_Poco_install_prefix} ${CMAKE_PREFIX_PATH})
 
 foreach(module ${Poco_FIND_COMPONENTS})
     find_package(Poco${module}
diff --git a/Foundation/cmake/PocoFoundationConfig.cmake b/Foundation/cmake/PocoFoundationConfig.cmake
index 82c5788..739adef 100644
--- a/Foundation/cmake/PocoFoundationConfig.cmake
+++ b/Foundation/cmake/PocoFoundationConfig.cmake
@@ -3,7 +3,7 @@ if(@POCO_UNBUNDLED@)
 	list(APPEND CMAKE_MODULE_PATH "${CMAKE_CURRENT_LIST_DIR}")
 	find_dependency(ZLIB REQUIRED)
 	find_dependency(PCRE2 REQUIRED)
-	find_dependency(Utf8Proc REQUIRED)
+	find_dependency(unofficial-utf8proc REQUIRED)
 endif()
 
 include("${CMAKE_CURRENT_LIST_DIR}/PocoFoundationTargets.cmake")
