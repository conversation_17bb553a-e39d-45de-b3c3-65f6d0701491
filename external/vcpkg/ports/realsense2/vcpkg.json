{"name": "realsense2", "version": "2.56.2", "port-version": 2, "description": "Intel® RealSense™ SDK 2.0 is a cross-platform library for Intel® RealSense™ depth cameras (D400 series and the SR300).", "homepage": "https://github.com/IntelRealSense/librealsense", "license": "Apache-2.0", "supports": "!uwp & !(windows & arm)", "dependencies": [{"name": "libusb", "platform": "!windows"}, "<PERSON><PERSON><PERSON>-<PERSON><PERSON>", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"openni2": {"description": "Build Intel® RealSense™ OpenNI2 driver", "dependencies": ["openni2"]}, "tools": {"description": "Build Intel® RealSense™ tools"}}}