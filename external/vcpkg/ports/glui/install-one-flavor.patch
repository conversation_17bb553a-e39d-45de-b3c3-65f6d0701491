diff --git a/CMakeLists.txt b/CMakeLists.txt
index c0f7d32..8446f3c 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -59,6 +59,22 @@ target_include_directories(glui_obj
 # create static *and* shared libraries without double compilation
 set_target_properties( glui_obj PROPERTIES POSITION_INDEPENDENT_CODE 1)
 
+if (MSVC)
+    target_compile_options(glui_obj
+        PRIVATE
+            /D_CRT_SECURE_NO_WARNINGS
+            /wd4244
+            /wd4305
+    )
+endif()
+
+if(BUILD_SHARED_LIBS)
+    if(WIN32)
+        target_compile_definitions(glui_obj
+            PRIVATE GLUI_BUILDING_LIB
+            PUBLIC GLUIDLL
+        )
+    endif()
 add_library(glui SHARED $<TARGET_OBJECTS:glui_obj>)
 target_include_directories(glui
       <PERSON>
@@ -71,7 +87,7 @@ set_target_properties(glui PROPERTIES
   DEBUG_POSTFIX "d"
   VERSION ${PROJECT_VERSION}
   SOVERSION ${PROJECT_VERSION})
-
+else()
 add_library(glui_static STATIC $<TARGET_OBJECTS:glui_obj>)
 target_include_directories(glui_static
       PUBLIC
@@ -81,7 +97,9 @@ target_include_directories(glui_static
       ${GLUT_INCLUDE_DIR})
 target_link_libraries(glui_static PUBLIC ${GLUT_LIBRARIES} ${OPENGL_LIBRARIES})
 set_target_properties(glui_static PROPERTIES DEBUG_POSTFIX "d")
+endif()
 
+if(GLUI_BUILD_EXAMPLES)
 add_executable(ppm2array tools/ppm.cpp tools/ppm2array.cpp)
 target_link_libraries(ppm2array)
 
@@ -97,7 +115,7 @@ add_executable(example5 example/example5.cpp)
 target_link_libraries(example5 glui_static ${GLUT_LIBRARIES} ${OPENGL_LIBRARIES})
 add_executable(example6 example/example6.cpp)
 target_link_libraries(example6 glui_static ${GLUT_LIBRARIES} ${OPENGL_LIBRARIES})
-
+endif()
 
 
 ####
@@ -130,8 +148,13 @@ configure_package_config_file(
 )
 
 # Targets:
+if(BUILD_SHARED_LIBS)
+    set(glui_target glui)
+else()
+    set(glui_target glui_static)
+endif()
 install(
-    TARGETS glui_static glui
+    TARGETS ${glui_target}
     EXPORT "${targets_export_name}"
     LIBRARY DESTINATION "lib"
     ARCHIVE DESTINATION "lib"
