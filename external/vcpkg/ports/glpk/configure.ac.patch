--- configure.ac.original	2022-12-15 13:46:24.222223600 -0500
+++ configure.ac	2022-12-28 09:30:59.301984700 -0500
@@ -114,6 +114,12 @@
       LIBODBC="libodbc.dylib"
       LIBMYSQL="libmysqlclient.dylib"
       ;;
+   cygwin* | mingw*)
+      LIBIODBC="odbc32.dll"
+      LIBODBC="odbc32.dll"
+      LIBMYSQL="libmysql.dll"
+      AC_DEFINE([__WOE__], [1], [N/A])
+      ;;
    *)
       LIBIODBC="libiodbc.so"
       LIBODBC="libodbc.so"
@@ -145,7 +151,6 @@
       AC_MSG_ERROR([--enable-mysql requires --enable-dl])
    fi
    AC_MSG_RESULT([yes])
-   CPPFLAGS="-I/usr/include/mysql $CPPFLAGS"
    AC_DEFINE_UNQUOTED([MYSQL_DLNAME], ["$LIBMYSQL"], [N/A])
 else
    AC_MSG_RESULT([no])
