# Compute the installation prefix relative to this file.
get_filename_component(_IMPORT_PREFIX "${CMAKE_CURRENT_LIST_FILE}" PATH)
get_filename_component(_IMPORT_PREFIX "${_IMPORT_PREFIX}" PATH)
get_filename_component(_IMPORT_PREFIX "${_IMPORT_PREFIX}" PATH)
if(_IMPORT_PREFIX STREQUAL "/")
  set(_IMPORT_PREFIX "")
endif()

include(CMakeFindDependencyMacro)
find_dependency(ZLIB)

if(NOT TARGET crashpad::crashpad)
  add_library(crashpad::crashpad INTERFACE IMPORTED)
  target_include_directories(crashpad::crashpad INTERFACE "${_IMPORT_PREFIX}/include/crashpad")

  set(_libs vcpkg_crashpad_client vcpkg_crashpad_client_common vcpkg_crashpad_util vcpkg_crashpad_base)
  if(APPLE)
    list(APPEND _libs mig_output)
  endif()
  include(SelectLibraryConfigurations)
  foreach(_lib IN LISTS _libs)
    find_library(CRASHPAD_${_lib}_LIBRARY_RELEASE NAMES ${_lib} PATHS "${_VCPKG_INSTALLED_DIR}/${VCPKG_TARGET_TRIPLET}/lib" NO_DEFAULT_PATH)
    find_library(CRASHPAD_${_lib}_LIBRARY_DEBUG NAMES ${_lib} PATHS "${_VCPKG_INSTALLED_DIR}/${VCPKG_TARGET_TRIPLET}/debug/lib" NO_DEFAULT_PATH)
    select_library_configurations(CRASHPAD_${_lib})
    target_link_libraries(crashpad::crashpad INTERFACE ${CRASHPAD_${_lib}_LIBRARIES})
  endforeach()

  if(WIN32)
  	target_compile_definitions(crashpad::crashpad INTERFACE NOMINMAX)
  elseif(APPLE)
    foreach(_lib IN ITEMS ApplicationServices CoreFoundation Foundation IOKit Security bsm)
      find_library(CRASHPAD_${_lib}_LIBRARY NAMES ${_lib})
      target_link_libraries(crashpad::crashpad INTERFACE ${CRASHPAD_${_lib}_LIBRARY})
    endforeach()
  elseif(ANDROID)
    target_link_libraries(crashpad::crashpad INTERFACE log)
  endif()

  target_link_libraries(crashpad::crashpad INTERFACE ZLIB::ZLIB)

  if(NOT TARGET crashpad)
    add_library(crashpad ALIAS crashpad::crashpad)
  endif()

  unset(_lib)
  unset(_libs)
endif()
