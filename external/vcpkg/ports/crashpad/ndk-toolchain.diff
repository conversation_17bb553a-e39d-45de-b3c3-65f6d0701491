diff --git a/build/config/BUILD.gn b/build/config/BUILD.gn
index 0802be8..dd77285 100644
--- a/build/config/BUILD.gn
+++ b/build/config/BUILD.gn
@@ -425,7 +425,7 @@ toolchain("gcc_like_toolchain") {
                               ])
 
     if (target_cpu == "arm") {
-      tool_prefix = "arm-linux-androideabi"
+      # unused
     } else if (target_cpu == "arm64") {
       tool_prefix = "aarch64-linux-android"
     } else if (target_cpu == "x86") {
@@ -446,7 +446,7 @@ toolchain("gcc_like_toolchain") {
     cxx = ndk_bin_dir + clang_prefix + "-clang++"
     asm = cxx
     ld = cxx
-    ar = ndk_bin_dir + tool_prefix + "-ar"
+    ar = ndk_bin_dir + "llvm-ar"
   } else {
     cc = "clang"
     cxx = "clang++"
