{"name": "crashpad", "version-date": "2024-04-11", "port-version": 7, "description": ["Crashpad is a crash-reporting system.", "Crashpad is a library for capturing, storing and transmitting postmortem crash reports from a client to an upstream collection server. Crashpad aims to make it possible for clients to capture process state at the time of crash with the best possible fidelity and coverage, with the minimum of fuss."], "homepage": "https://chromium.googlesource.com/crashpad/crashpad/+/master/README.md", "license": "Apache-2.0", "supports": "android | linux | osx | (windows & !uwp)", "dependencies": [{"name": "curl", "default-features": false, "platform": "linux"}, {"name": "vcpkg-cmake-get-vars", "host": true}, "vcpkg-get-python-packages", {"name": "vcpkg-gn", "host": true}, {"name": "vcpkg-tool-gn", "host": true}, "zlib"]}