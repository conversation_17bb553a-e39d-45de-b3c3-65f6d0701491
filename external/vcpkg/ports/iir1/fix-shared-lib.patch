diff --git a/CMakeLists.txt b/CMakeLists.txt
index 4dd67df..3840ff8 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -84,6 +84,7 @@ set_target_properties(iir PROPERTIES
   PUBLIC_HEADER Iir.h
   PRIVATE_HEADER "${LIBINCLUDE}")
 
+if(BUILD_SHARED_LIBS)
 install(TARGETS iir EXPORT iir-targets
   LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
   ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR}
@@ -91,6 +92,10 @@ install(TARGETS iir EXPORT iir-targets
   INCLUDES DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}
   RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
   PRIVATE_HEADER DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}/iir)
+  set(IIR1_INSTALL_STATIC OFF)
+else()
+  set_target_properties(iir PROPERTIES EXCLUDE_FROM_ALL 1)
+endif()
 
 include(Join<PERSON>aths)
 
