{"$comment": "Use scripts/update_suitesparse.py to update all SuiteSparse ports", "name": "suitesparse-mongoose", "version-semver": "3.3.4", "description": "Mongoose: Graph partitioning library in SuiteSparse", "homepage": "https://people.engr.tamu.edu/davis/suitesparse.html", "license": "GPL-3.0-only", "dependencies": ["suitesparse-config", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"tools": {"description": "Build suitesparse_mongoose CLI tool"}}}