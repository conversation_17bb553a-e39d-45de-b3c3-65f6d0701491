libjpeg-turbo is compatible with built-in implementation-agnostic CMake targets:

    find_package(JPEG REQUIRED)
    target_link_libraries(main PRIVATE JPEG::JPEG)

libjpeg-turbo provides CMake targets for the TurboJPEG C API:

    find_package(libjpeg-turbo CONFIG REQUIRED)
    target_link_libraries(main PRIVATE $<IF:$<TARGET_EXISTS:libjpeg-turbo::turbojpeg>,libjpeg-turbo::turbojpeg,libjpeg-turbo::turbojpeg-static>)
