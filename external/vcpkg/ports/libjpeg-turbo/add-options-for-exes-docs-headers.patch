diff --git a/CMakeLists.txt b/CMakeLists.txt
index ff9c9c27..d3fbad30 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -224,6 +224,12 @@ option(ENABLE_SHARED "Build shared libraries" TRUE)
 boolean_number(ENABLE_SHARED)
 option(ENABLE_STATIC "Build static libraries" TRUE)
 boolean_number(ENABLE_STATIC)
+option(ENABLE_EXECUTABLES "Build executables" TRUE)
+boolean_number(ENABLE_EXECUTABLES)
+option(INSTALL_DOCS "Install doc files" TRUE)
+boolean_number(INSTALL_DOCS)
+option(INSTALL_HEADERS "Install header files" TRUE)
+boolean_number(INSTALL_HEADERS)
 option(REQUIRE_SIMD
   "Generate a fatal error if SIMD extensions are not available for this platform (default is to fall back to a non-SIMD build)"
   FALSE)
@@ -734,6 +740,7 @@ if(WITH_TURBOJPEG)
         LINK_FLAGS "${TJMAPFLAG}${TJMAPFILE}")
     endif()
 
+    if(ENABLE_EXECUTABLES)
     add_executable(tjunittest src/tjunittest.c src/tjutil.c src/md5/md5.c
       src/md5/md5hl.c)
     target_link_libraries(tjunittest turbojpeg)
@@ -752,9 +759,11 @@ if(WITH_TURBOJPEG)
 
     add_executable(tjtran src/tjtran.c)
     target_link_libraries(tjtran turbojpeg)
-
+    endif()
+    if(INSTALL_DOCS)
     add_custom_target(tjdoc COMMAND doxygen -s ../doc/doxygen.config
       WORKING_DIRECTORY ${CMAKE_SOURCE_DIR}/src)
+    endif()
   endif()
 
   if(ENABLE_STATIC)
@@ -776,6 +785,7 @@ if(WITH_TURBOJPEG)
       set_target_properties(turbojpeg-static PROPERTIES OUTPUT_NAME turbojpeg)
     endif()
 
+    if(ENABLE_EXECUTABLES)
     add_executable(tjunittest-static src/tjunittest.c src/tjutil.c
       src/md5/md5.c src/md5/md5hl.c)
     target_link_libraries(tjunittest-static turbojpeg-static)
@@ -785,6 +795,7 @@ if(WITH_TURBOJPEG)
     if(UNIX)
       target_link_libraries(tjbench-static m)
     endif()
+    endif()
   endif()
 endif()
 
@@ -803,12 +814,14 @@ if(ENABLE_STATIC)
   add_library(cjpeg16-static OBJECT src/rdppm.c)
   set_property(TARGET cjpeg16-static PROPERTY COMPILE_FLAGS
     "-DBITS_IN_JSAMPLE=16 -DGIF_SUPPORTED -DPPM_SUPPORTED")
+  if(ENABLE_EXECUTABLES)
   add_executable(cjpeg-static src/cjpeg.c src/cdjpeg.c src/rdbmp.c src/rdgif.c
     src/rdppm.c src/rdswitch.c src/rdtarga.c $<TARGET_OBJECTS:cjpeg12-static>
     $<TARGET_OBJECTS:cjpeg16-static>)
   set_property(TARGET cjpeg-static PROPERTY COMPILE_FLAGS
     ${CDJPEG_COMPILE_FLAGS})
   target_link_libraries(cjpeg-static jpeg-static)
+  endif()
 
   # Compile a separate version of these source files with 12-bit and 16-bit
   # data precision.
@@ -818,6 +831,7 @@ if(ENABLE_STATIC)
   add_library(djpeg16-static OBJECT src/wrppm.c)
   set_property(TARGET djpeg16-static PROPERTY COMPILE_FLAGS
     "-DBITS_IN_JSAMPLE=16 -DPPM_SUPPORTED")
+  if(ENABLE_EXECUTABLES)
   add_executable(djpeg-static src/djpeg.c src/cdjpeg.c src/rdcolmap.c
     src/rdswitch.c src/wrbmp.c src/wrgif.c src/wrppm.c src/wrtarga.c
     $<TARGET_OBJECTS:djpeg12-static> $<TARGET_OBJECTS:djpeg16-static>)
@@ -832,11 +846,14 @@ if(ENABLE_STATIC)
 
   add_executable(example-static src/example.c)
   target_link_libraries(example-static jpeg-static)
+  endif()
 endif()
 
+if(ENABLE_EXECUTABLES)
 add_executable(rdjpgcom src/rdjpgcom.c)
 
 add_executable(wrjpgcom src/wrjpgcom.c)
+endif()
 
 
 ###############################################################################
@@ -1971,9 +1988,11 @@ if(WITH_TURBOJPEG)
       INCLUDES DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}
       ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR} COMPONENT lib
       LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR} COMPONENT lib
-      RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR} COMPONENT bin)
+      RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR} COMPONENT bin)      
+    if(ENABLE_EXECUTABLES)
     install(TARGETS tjbench
       RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR} COMPONENT bin)
+    endif()
     if(NOT CMAKE_VERSION VERSION_LESS "3.1" AND MSVC_LIKE AND
       CMAKE_C_LINKER_SUPPORTS_PDB)
       install(FILES "$<TARGET_PDB_FILE:turbojpeg>"
@@ -1984,7 +2003,7 @@ if(WITH_TURBOJPEG)
     install(TARGETS turbojpeg-static EXPORT ${CMAKE_PROJECT_NAME}Targets
       INCLUDES DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}
       ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR} COMPONENT lib)
-    if(NOT ENABLE_SHARED)
+    if(NOT ENABLE_SHARED AND ENABLE_EXECUTABLES)
       if(GENERATOR_IS_MULTI_CONFIG)
         set(DIR "${CMAKE_CURRENT_BINARY_DIR}/\${CMAKE_INSTALL_CONFIG_NAME}")
       else()
@@ -1994,15 +2013,17 @@ if(WITH_TURBOJPEG)
         DESTINATION ${CMAKE_INSTALL_BINDIR} COMPONENT bin RENAME tjbench${EXE})
     endif()
   endif()
+  if(INSTALL_HEADERS)
   install(FILES ${CMAKE_CURRENT_SOURCE_DIR}/src/turbojpeg.h
     DESTINATION ${CMAKE_INSTALL_INCLUDEDIR} COMPONENT include)
+  endif()
 endif()
 
 if(ENABLE_STATIC)
   install(TARGETS jpeg-static EXPORT ${CMAKE_PROJECT_NAME}Targets
     INCLUDES DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}
     ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR} COMPONENT lib)
-  if(NOT ENABLE_SHARED)
+  if(NOT ENABLE_SHARED AND ENABLE_EXECUTABLES)
     if(GENERATOR_IS_MULTI_CONFIG)
       set(DIR "${CMAKE_CURRENT_BINARY_DIR}/\${CMAKE_INSTALL_CONFIG_NAME}")
     else()
@@ -2017,9 +2038,12 @@ if(ENABLE_STATIC)
   endif()
 endif()
 
+if(ENABLE_EXECUTABLES)
 install(TARGETS rdjpgcom wrjpgcom
   RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR} COMPONENT bin)
+endif()
 
+if(INSTALL_DOCS)
 install(FILES ${CMAKE_CURRENT_SOURCE_DIR}/README.ijg
   ${CMAKE_CURRENT_SOURCE_DIR}/README.md
   ${CMAKE_CURRENT_SOURCE_DIR}/src/example.c
@@ -2038,8 +2062,9 @@ if(WITH_JAVA)
     ${CMAKE_CURRENT_SOURCE_DIR}/java/TJTran.java
     DESTINATION ${CMAKE_INSTALL_DOCDIR} COMPONENT doc)
 endif()
+endif()
 
-if(UNIX OR MINGW)
+if((UNIX OR MINGW) AND INSTALL_DOCS)
   install(FILES ${CMAKE_CURRENT_SOURCE_DIR}/doc/cjpeg.1
     ${CMAKE_CURRENT_SOURCE_DIR}/doc/djpeg.1
     ${CMAKE_CURRENT_SOURCE_DIR}/doc/jpegtran.1
@@ -2063,12 +2088,13 @@ install(EXPORT ${CMAKE_PROJECT_NAME}Targets
   DESTINATION ${CMAKE_INSTALL_LIBDIR}/cmake/${CMAKE_PROJECT_NAME}
   COMPONENT lib)
 
+if(INSTALL_HEADERS)
 install(FILES ${CMAKE_CURRENT_BINARY_DIR}/jconfig.h
   ${CMAKE_CURRENT_SOURCE_DIR}/src/jerror.h
   ${CMAKE_CURRENT_SOURCE_DIR}/src/jmorecfg.h
   ${CMAKE_CURRENT_SOURCE_DIR}/src/jpeglib.h
   DESTINATION ${CMAKE_INSTALL_INCLUDEDIR} COMPONENT include)
-
+endif()
 include(cmakescripts/BuildPackages.cmake)
 
 configure_file("${CMAKE_CURRENT_SOURCE_DIR}/cmakescripts/cmake_uninstall.cmake.in"

diff --git a/sharedlib/CMakeLists.txt b/sharedlib/CMakeLists.txt
index eaed9e95..74d53696 100644
--- a/sharedlib/CMakeLists.txt
+++ b/sharedlib/CMakeLists.txt
@@ -94,12 +94,13 @@ set_property(TARGET cjpeg12 PROPERTY COMPILE_FLAGS
 add_library(cjpeg16 OBJECT ../src/rdppm.c)
 set_property(TARGET cjpeg16 PROPERTY COMPILE_FLAGS
   "-DBITS_IN_JSAMPLE=16 -DGIF_SUPPORTED -DPPM_SUPPORTED")
+if(ENABLE_EXECUTABLES)
 add_executable(cjpeg ../src/cjpeg.c ../src/cdjpeg.c ../src/rdbmp.c
   ../src/rdgif.c ../src/rdppm.c ../src/rdswitch.c ../src/rdtarga.c
   $<TARGET_OBJECTS:cjpeg12> $<TARGET_OBJECTS:cjpeg16>)
 set_property(TARGET cjpeg PROPERTY COMPILE_FLAGS ${CDJPEG_COMPILE_FLAGS})
 target_link_libraries(cjpeg jpeg)
-
+endif()
 # Compile a separate version of these source files with 12-bit and 16-bit data
 # precision.
 add_library(djpeg12 OBJECT ../src/rdcolmap.c ../src/wrgif.c ../src/wrppm.c)
@@ -108,6 +109,7 @@ set_property(TARGET djpeg12 PROPERTY COMPILE_FLAGS
 add_library(djpeg16 OBJECT ../src/wrppm.c)
 set_property(TARGET djpeg16 PROPERTY COMPILE_FLAGS
   "-DBITS_IN_JSAMPLE=16 -DPPM_SUPPORTED")
+if(ENABLE_EXECUTABLES)
 add_executable(djpeg ../src/djpeg.c ../src/cdjpeg.c ../src/rdcolmap.c
   ../src/rdswitch.c ../src/wrbmp.c ../src/wrgif.c ../src/wrppm.c
   ../src/wrtarga.c $<TARGET_OBJECTS:djpeg12> $<TARGET_OBJECTS:djpeg16>)
@@ -124,14 +126,16 @@ target_link_libraries(example jpeg)
 
 add_executable(jcstest ../src/jcstest.c)
 target_link_libraries(jcstest jpeg)
-
+endif()
 install(TARGETS jpeg EXPORT ${CMAKE_PROJECT_NAME}Targets
   INCLUDES DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}
   ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR} COMPONENT lib
   LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR} COMPONENT lib
   RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR} COMPONENT bin)
+if(ENABLE_EXECUTABLES)
 install(TARGETS cjpeg djpeg jpegtran
   RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR} COMPONENT bin)
+endif()
 if(NOT CMAKE_VERSION VERSION_LESS "3.1" AND MSVC_LIKE AND
   CMAKE_C_LINKER_SUPPORTS_PDB)
   install(FILES "$<TARGET_PDB_FILE:jpeg>"
