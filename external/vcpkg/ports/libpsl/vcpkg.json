{"name": "libpsl", "version": "0.21.5", "port-version": 1, "description": "C library for the Public Suffix List", "homepage": "https://rockdaboot.github.io/libpsl/", "license": "MIT", "supports": "!uwp", "dependencies": [{"name": "vcpkg-tool-meson", "host": true}], "default-features": [{"name": "libicu", "platform": "windows"}, {"name": "libidn2", "platform": "!windows"}], "features": {"libicu": {"description": "choose libi<PERSON> as runtime", "supports": "windows", "dependencies": ["icu"]}, "libidn2": {"description": "choose libidn2 as runtime", "supports": "!windows", "dependencies": ["libidn2"]}}}