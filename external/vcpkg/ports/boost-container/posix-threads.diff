diff --git a/CMakeLists.txt b/CMakeLists.txt
index 01e97c5..faf17d4 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -15,6 +15,16 @@ add_library(boost_container
   src/synchronized_pool_resource.cpp
   src/unsynchronized_pool_resource.cpp
 )
+if(NOT WIN32)
+  set(THREADS_PREFER_PTHREAD_FLAG 1)
+  find_package(Threads REQUIRED)
+  target_link_libraries(boost_container PUBLIC Threads::Threads)
+  if(EMSCRIPTEN)
+    # <PERSON><PERSON> config needs `-pthread` to see `_POSIX_THREADS`,
+    # but FindTheads.cmake finishes with `CMAKE_HAVE_LIBC_PTHREAD`.
+    target_compile_options(boost_container PUBLIC -pthread)
+  endif()
+endif()
 
 add_library(Boost::container ALIAS boost_container)
 
