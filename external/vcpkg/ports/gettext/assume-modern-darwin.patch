diff --git a/gettext-runtime/configure b/gettext-runtime/configure
index a984774..f436a19 100755
--- a/gettext-runtime/configure
+++ b/gettext-runtime/configure
@@ -35448,7 +35448,7 @@ fi
       haiku*) use_elf_origin_trick=yes ;;
       # On Mac OS X 10.4 or newer, use Mac OS X tools. See
       # <https://wincent.com/wiki/@executable_path,_@load_path_and_@rpath>.
-      darwin | darwin[1-7].*) ;;
+      darwin[1-7].*) ;;
       darwin*) use_macos_tools=yes ;;
     esac
     if test $is_noop = yes; then
diff --git a/gettext-tools/configure b/gettext-tools/configure
index ee64b69..2dde1f5 100755
--- a/gettext-tools/configure
+++ b/gettext-tools/configure
@@ -48606,7 +48606,7 @@ fi
       haiku*) use_elf_origin_trick=yes ;;
       # On Mac OS X 10.4 or newer, use Mac OS X tools. See
       # <https://wincent.com/wiki/@executable_path,_@load_path_and_@rpath>.
-      darwin | darwin[1-7].*) ;;
+      darwin[1-7].*) ;;
       darwin*) use_macos_tools=yes ;;
     esac
     if test $is_noop = yes; then
