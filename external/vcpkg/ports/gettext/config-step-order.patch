diff --git a/gettext-runtime/configure b/gettext-runtime/configure
index 2a376c6..a984774 100755
--- a/gettext-runtime/configure
+++ b/gettext-runtime/configure
@@ -22346,6 +22346,12 @@ printf "%s\n" "$acl_cv_libdirstems" >&6; }
 
 
 
+### Configuration step reordering
+### Similar to AM_GNU_GETTEXT(external,...), cf. gettext-runtime/m4/gettext.m4
+### Pull (include_next and) iconv lookup before actual GNU gettext lookup.
+for configuration_step in gettext-independent gettext-main ; do
+case "$configuration_step" in
+gettext-main)
     { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for CFPreferencesCopyAppValue" >&5
 printf %s "checking for CFPreferencesCopyAppValue... " >&6; }
 if test ${gt_cv_func_CFPreferencesCopyAppValue+y}
@@ -23288,6 +23294,9 @@ printf "%s\n" "#define HAVE_DCGETTEXT 1" >>confdefs.h
 
 
 
+### Configuration step reordering
+;;
+gettext-independent)
                         # Check whether --enable-cross-guesses was given.
 if test ${enable_cross_guesses+y}
 then :
@@ -30464,6 +30473,10 @@ printf "%s\n" "$gl_cv_next_iconv_h" >&6; }
        gl_next_as_first_directive=$gl_cv_next_iconv_h
      fi
      NEXT_AS_FIRST_DIRECTIVE_ICONV_H=$gl_next_as_first_directive
+### Configuration step reordering
+;;
+esac
+done
 
 
 
diff --git a/gettext-tools/configure b/gettext-tools/configure
index 5ce6cf1..ee64b69 100755
--- a/gettext-tools/configure
+++ b/gettext-tools/configure
@@ -25735,6 +25735,12 @@ printf "%s\n" "$acl_cv_libdirstems" >&6; }
 
 
 
+### Configuration step reordering
+### Similar to AM_GNU_GETTEXT(external,...), cf. gettext-runtime/m4/gettext.m4
+### Pull (include_next and) iconv lookup before actual GNU gettext lookup.
+for configuration_step in gettext-independent gettext-main; do
+case "$configuration_step" in
+gettext-main)
     { printf "%s\n" "$as_me:${as_lineno-$LINENO}: checking for CFPreferencesCopyAppValue" >&5
 printf %s "checking for CFPreferencesCopyAppValue... " >&6; }
 if test ${gt_cv_func_CFPreferencesCopyAppValue+y}
@@ -27588,6 +27594,9 @@ fi
 
 
 
+### Configuration step reordering
+;;
+gettext-independent)
                         # Check whether --enable-cross-guesses was given.
 if test ${enable_cross_guesses+y}
 then :
@@ -38458,6 +38467,10 @@ printf "%s\n" "$gl_cv_next_iconv_h" >&6; }
        gl_next_as_first_directive=$gl_cv_next_iconv_h
      fi
      NEXT_AS_FIRST_DIRECTIVE_ICONV_H=$gl_next_as_first_directive
+### Configuration step reordering
+;;
+esac
+done
 
 
 
