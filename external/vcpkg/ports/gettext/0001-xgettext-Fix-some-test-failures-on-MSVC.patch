From e5cf655767413f38e8a308f6e0440d7d283ef841 Mon Sep 17 00:00:00 2001
From: =?UTF-8?q?Daniel=20Sch=C3=<PERSON>rmann?= <<EMAIL>>
Date: Wed, 12 Mar 2025 00:55:16 +0100
Subject: [PATCH] xgettext: Fix some test failures on MSVC.

* gettext-tools/src/locating-rule.c: Include <dirent.h> always.
(HAVE_DIR): Remove macro.
(locating_rule_list_add_from_directory): Don't test HAVE_DIR.
* gettext-tools/src/msginit.c: Include <dirent.h> always.
(HAVE_DIR): Remove macro.
(find_pot): Don't test HAVE_DIR.
---
 gettext-tools/src/locating-rule.c | 12 +-----------
 gettext-tools/src/msginit.c       | 12 +-----------
 2 <USER> <GROUP>, 2 insertions(+), 22 deletions(-)

diff --git a/gettext-tools/src/locating-rule.c b/gettext-tools/src/locating-rule.c
index c6f4b1b..d38bb15 100644
--- a/gettext-tools/src/locating-rule.c
+++ b/gettext-tools/src/locating-rule.c
@@ -27,15 +27,7 @@
 #include "concat-filename.h"
 #include "c-strcase.h"
 
-#if HAVE_DIRENT_H
-# include <dirent.h>
-#endif
-
-#if HAVE_DIRENT_H
-# define HAVE_DIR 1
-#else
-# define HAVE_DIR 0
-#endif
+#include <dirent.h>
 
 #include "dir-list.h"
 #include <errno.h>
@@ -411,7 +403,6 @@ bool
 locating_rule_list_add_from_directory (struct locating_rule_list_ty *rules,
                                        const char *directory)
 {
-#if HAVE_DIR
   DIR *dirp;
 
   dirp = opendir (directory);
@@ -445,7 +436,6 @@ locating_rule_list_add_from_directory (struct locating_rule_list_ty *rules,
   if (closedir (dirp))
     return false;
 
-#endif
   return true;
 }
 
diff --git a/gettext-tools/src/msginit.c b/gettext-tools/src/msginit.c
index 9ff75e7..736c671 100644
--- a/gettext-tools/src/msginit.c
+++ b/gettext-tools/src/msginit.c
@@ -39,15 +39,7 @@
 
 #include <unistd.h>
 
-#if HAVE_DIRENT_H
-# include <dirent.h>
-#endif
-
-#if HAVE_DIRENT_H
-# define HAVE_DIR 1
-#else
-# define HAVE_DIR 0
-#endif
+#include <dirent.h>
 
 #include <textstyle.h>
 
@@ -491,7 +483,6 @@ or by email to <%s>.\n"),
 static const char *
 find_pot ()
 {
-#if HAVE_DIR
   DIR *dirp;
   char *found = NULL;
 
@@ -534,7 +525,6 @@ Please specify the input .pot file through the --input option.\n")));
       if (found != NULL)
         return found;
     }
-#endif
 
   multiline_error (xstrdup (""),
                    xstrdup (_("\
-- 
2.34.1

