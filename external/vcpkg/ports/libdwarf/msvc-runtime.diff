diff --git a/CMakeLists.txt b/CMakeLists.txt
index 28076ee..f9795dd 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -19,6 +19,8 @@ endmacro()
 set(LIBDWARF_CRT "MD" CACHE STRING "Either MT or MD, specifies whether to use the static or dynamic MSVCRT.")
 
 if (CMAKE_CXX_COMPILER_ID STREQUAL "MSVC")
+  set(LIBDWARF_CRT ignore)
+elseif(0)
   # Use CMAKE_MSVC_RUNTIME in versions 3.15 and up
   if (CMAKE_VERSION VERSION_GREATER_EQUAL "3.15")
     cmake_policy(SET CMP0091 NEW)
