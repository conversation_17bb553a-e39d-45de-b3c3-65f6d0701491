diff --git a/src/lib/libdwarf/CMakeLists.txt b/src/lib/libdwarf/CMakeLists.txt
index 0278755..19f1673 100644
--- a/src/lib/libdwarf/CMakeLists.txt
+++ b/src/lib/libdwarf/CMakeLists.txt
@@ -103,7 +103,7 @@ msvc_posix(dwarf)
 target_compile_definitions(dwarf PUBLIC ${DEFS})
 target_include_directories(dwarf PUBLIC
     $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}>
-    $<INSTALL_INTERFACE:${CMAKE_INSTALL_INCLUDEDIR}>
+    $<INSTALL_INTERFACE:${CMAKE_INSTALL_INCLUDEDIR}/libdwarf>
   )
 if(ZLIB_FOUND AND zstd_FOUND)
   target_link_libraries(dwarf PRIVATE  ZLIB::ZLIB ${ZSTD_LIB} )
@@ -114,7 +114,7 @@ install(TARGETS dwarf
         RUNTIME DESTINATION "${CMAKE_INSTALL_BINDIR}"
         LIBRARY DESTINATION "${CMAKE_INSTALL_LIBDIR}"
         ARCHIVE DESTINATION "${CMAKE_INSTALL_LIBDIR}"
-        PUBLIC_HEADER DESTINATION "${CMAKE_INSTALL_INCLUDEDIR}"
+        PUBLIC_HEADER DESTINATION "${CMAKE_INSTALL_INCLUDEDIR}/libdwarf"
         )
 
 configure_file(libdwarf.pc.in libdwarf.pc @ONLY)
@@ -130,7 +130,7 @@ install(FILES "${CMAKE_CURRENT_BINARY_DIR}/libdwarfConfig.cmake" "${CMAKE_CURREN
 install(TARGETS dwarf EXPORT libdwarfTargets
         ARCHIVE DESTINATION "${CMAKE_INSTALL_LIBDIR}"
         LIBRARY DESTINATION "${CMAKE_INSTALL_LIBDIR}"
-        PUBLIC_HEADER DESTINATION "${CMAKE_INSTALL_INCLUDEDIR}")
+        PUBLIC_HEADER DESTINATION "${CMAKE_INSTALL_INCLUDEDIR}/libdwarf")
 install(EXPORT libdwarfTargets
         FILE libdwarf-targets.cmake
         NAMESPACE libdwarf::
diff --git a/src/lib/libdwarf/libdwarf.pc.in b/src/lib/libdwarf/libdwarf.pc.in
index 3bb9178..4a094d1 100644
--- a/src/lib/libdwarf/libdwarf.pc.in
+++ b/src/lib/libdwarf/libdwarf.pc.in
@@ -2,7 +2,7 @@
 prefix=@CMAKE_INSTALL_PREFIX@
 exec_prefix="${prefix}"
 libdir="${prefix}/lib"
-includedir="${prefix}/include"
+includedir="${prefix}/include/libdwarf"
 
 Name: libdwarf
 Description: DWARF debug symbols library
