diff --git a/src/lib/libdwarf/libdwarf_private.h b/src/lib/libdwarf/libdwarf_private.h
index b37ae994..7fa89256 100644
--- a/src/lib/libdwarf/libdwarf_private.h
+++ b/src/lib/libdwarf/libdwarf_private.h
@@ -26,11 +26,7 @@
 #ifdef _MSC_VER /* Macro to select VS compiler */
 #include <windows.h>
 typedef SSIZE_T ssize_t;
-#ifdef _WIN64
-typedef long long off_t;
-#else
 typedef long off_t;
-#endif
 #endif /* _MSC_VER */
 
 #ifndef TRUE
