diff --git a/Build/CMake/Modules/GLTFPlatform.cmake b/Build/CMake/Modules/GLTFPlatform.cmake
index 3d940f5..322f029 100644
--- a/Build/CMake/Modules/GLTFPlatform.cmake
+++ b/Build/CMake/Modules/GLTFPlatform.cmake
@@ -55,14 +55,14 @@ endfunction(GetGLTFPlatform)
 function(CreateGLTFInstallTargets target platform)
 
     install(TARGETS ${target}
-        ARCHIVE DESTINATION ${CMAKE_SOURCE_DIR}/Built/Out/${platform}/$<CONFIG>/${PROJECT_NAME}
-        LIBRARY DESTINATION ${CMAKE_SOURCE_DIR}/Built/Out/${platform}/$<CONFIG>/${PROJECT_NAME}
-        RUNTIME DESTINATION ${CMAKE_SOURCE_DIR}/Built/Out/${platform}/$<CONFIG>/${PROJECT_NAME}
-        BUN<PERSON>LE DESTINATION ${CMAKE_SOURCE_DIR}/Built/Out/${platform}/$<CONFIG>/${PROJECT_NAME}
+        ARCHIVE DESTINATION ${CMAKE_INSTALL_PREFIX}/lib
+        LIBRARY DESTINATION ${CMAKE_INSTALL_PREFIX}/lib
+        RUNTIME DESTINATION ${CMAKE_INSTALL_PREFIX}/bin
+        BUNDLE DESTINATION  ${CMAKE_INSTALL_PREFIX}/bin
     )
 
     if (MSVC)
-        install(FILES ${CMAKE_CURRENT_BINARY_DIR}/$<CONFIG>/${PROJECT_NAME}.pdb DESTINATION ${CMAKE_SOURCE_DIR}/Built/Out/${platform}/$<CONFIG>/${PROJECT_NAME})
+        install(FILES ${CMAKE_CURRENT_BINARY_DIR}/$<CONFIG>/${PROJECT_NAME}.pdb DESTINATION ${CMAKE_INSTALL_PREFIX}/lib)
     endif()
 
 endfunction(CreateGLTFInstallTargets)
diff --git a/GLTFSDK.Test/CMakeLists.txt b/GLTFSDK.Test/CMakeLists.txt
index 5c8eca7..a0752a3 100644
--- a/GLTFSDK.Test/CMakeLists.txt
+++ b/GLTFSDK.Test/CMakeLists.txt
@@ -46,4 +46,4 @@ add_custom_command(TARGET GLTFSDK.Test
 AddGLTFIOSAppProperties(GLTFSDK.Test)
 CreateGLTFInstallTargets(GLTFSDK.Test ${Platform})
 
-install(FILES ${PROJECT_BINARY_DIR}/$<CONFIG>/Resources/ DESTINATION ${CMAKE_SOURCE_DIR}/Built/Out/${Platform}/$<CONFIG>/${PROJECT_NAME}/Resources)
+install(FILES ${PROJECT_BINARY_DIR}/$<CONFIG>/Resources/ DESTINATION ${CMAKE_INSTALL_PREFIX}/bin)
diff --git a/GLTFSDK/CMakeLists.txt b/GLTFSDK/CMakeLists.txt
index 3c53c74..1e8d223 100644
--- a/GLTFSDK/CMakeLists.txt
+++ b/GLTFSDK/CMakeLists.txt
@@ -46,4 +46,5 @@ target_include_directories(GLTFSDK
     PRIVATE "${CMAKE_BINARY_DIR}/GeneratedFiles"
 )
 
+install(DIRECTORY ${CMAKE_CURRENT_LIST_DIR}/Inc/GLTFSDK DESTINATION ${CMAKE_INSTALL_PREFIX}/include)
 CreateGLTFInstallTargets(GLTFSDK ${Platform})
