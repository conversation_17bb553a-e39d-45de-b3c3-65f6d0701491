diff --git a/CMakeLists.txt b/CMakeLists.txt
index 2c5b0f3..b8178fa 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -219,6 +219,10 @@ elseif(MSVC)
 	set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -MP -W4 -WX ${MSVC_DISABLED_WARNINGS_STR}")
 	set(CMAKE_C_FLAGS_DEBUG "${CMAKE_C_FLAGS_DEBUG} /Od /Z7 /guard:cf /sdl /RTCcsu")
 	set(CMAKE_C_FLAGS_RELEASE "${CMAKE_C_FLAGS_RELEASE} /Zi /guard:cf /sdl")
+	add_definitions("-D_CRT_SECURE_NO_WARNINGS" "-D_CRT_NONSTDC_NO_DEPRECATE")
+	string(REPLACE " -WX" "" CMAKE_C_FLAGS "${CMAKE_C_FLAGS}")
+	string(REPLACE " /sdl" "" CMAKE_C_FLAGS_DEBUG "${CMAKE_C_FLAGS_DEBUG}")
+	string(REPLACE " /sdl" "" CMAKE_C_FLAGS_RELEASE "${CMAKE_C_FLAGS_RELEASE}")
 	if(USE_WINHELLO)
 		add_definitions(-DUSE_WINHELLO)
 	endif()
@@ -303,7 +307,6 @@ elseif(1)
 
 	add_compile_options(-Wall)
 	add_compile_options(-Wextra)
-	add_compile_options(-Werror)
 	add_compile_options(-Wshadow)
 	add_compile_options(-Wcast-qual)
 	add_compile_options(-Wwrite-strings)
@@ -311,7 +314,6 @@ elseif(1)
 	add_compile_options(-Wbad-function-cast)
 	add_compile_options(-Wimplicit-fallthrough)
 	add_compile_options(-pedantic)
-	add_compile_options(-pedantic-errors)
 
 	set(EXTRA_CFLAGS "-Wconversion -Wsign-conversion")
 
