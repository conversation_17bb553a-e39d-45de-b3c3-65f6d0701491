{"name": "libfido2", "version": "1.15.0", "port-version": 1, "description": "Provides library functionality to communicate with a FIDO device over USB, and to verify attestation and assertion signatures.", "homepage": "https://developers.yubico.com/libfido2/", "license": "BSD-2-<PERSON><PERSON>", "supports": "!android & !uwp", "dependencies": ["libc<PERSON>", "openssl", {"name": "vcpkg-cmake", "host": true}, "zlib"]}