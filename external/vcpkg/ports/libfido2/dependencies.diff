diff --git a/CMakeLists.txt b/CMakeLists.txt
index c41de28..2c5b0f3 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -168,7 +168,13 @@ if(UNIX)
 endif()
 
 
-if(MSVC)
+find_package(libcbor CONFIG REQUIRED)
+set(CBOR_LIBRARIES libcbor::libcbor)
+find_package(ZLIB REQUIRED)
+set(ZLIB_LIBRARIES ZLIB::ZLIB)
+find_package(OpenSSL REQUIRED)
+set(CRYPTO_LIBRARIES OpenSSL::Crypto)
+if(0)
 	if((NOT CBOR_INCLUDE_DIRS) OR (NOT CBOR_LIBRARY_DIRS) OR
 	   (NOT CRYPTO_INCLUDE_DIRS) OR (NOT CRYPTO_LIBRARY_DIRS) OR
 	   (NOT ZLIB_INCLUDE_DIRS) OR (NOT ZLIB_LIBRARY_DIRS))
@@ -191,6 +197,7 @@ if(MSVC)
 		set(CRYPTO_LIBRARIES crypto)
 	endif()
 
+elseif(MSVC)
 	set(MSVC_DISABLED_WARNINGS_LIST
 		"C4152" # nonstandard extension used: function/data pointer
 			# conversion in expression;
@@ -216,7 +223,7 @@ if(MSVC)
 		add_definitions(-DUSE_WINHELLO)
 	endif()
 	set(NFC_LINUX OFF)
-else()
+elseif(0)
 	include(FindPkgConfig)
 	pkg_search_module(CBOR libcbor)
 	pkg_search_module(CRYPTO libcrypto)
@@ -239,6 +246,7 @@ else()
 		set(CRYPTO_LIBRARIES "crypto")
 	endif()
 
+elseif(1)
 	if(CMAKE_SYSTEM_NAME STREQUAL "Linux")
 		pkg_search_module(UDEV libudev REQUIRED)
 		set(UDEV_NAME "udev")
diff --git a/src/libfido2.pc.in b/src/libfido2.pc.in
index 03d0606..1a299ae 100644
--- a/src/libfido2.pc.in
+++ b/src/libfido2.pc.in
@@ -7,6 +7,6 @@ Name: @PROJECT_NAME@
 Description: A FIDO2 library
 URL: https://github.com/yubico/libfido2
 Version: @FIDO_VERSION@
-Requires: libcrypto
+Requires.private: libcrypto libcbor zlib
 Libs: -L${libdir} -lfido2
 Cflags: -I${includedir}
