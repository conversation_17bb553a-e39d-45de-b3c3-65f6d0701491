diff --git a/libcpuid/cpuid_main.c b/libcpuid/cpuid_main.c
index ed3db36..b082caa 100644
--- a/libcpuid/cpuid_main.c
+++ b/libcpuid/cpuid_main.c
@@ -1452,7 +1452,7 @@ int cpuid_get_raw_data_core(struct cpu_raw_data_t* data, logical_cpu_t logical_c
 	}
 # endif /* PLATFORM_AARCH64 */
 #else
-# warning This CPU architecture is not supported by libcpuid
+#pragma message("Warning: This CPU architecture is not supported by libcpuid")
 	UNUSED(data);
 #endif
 
