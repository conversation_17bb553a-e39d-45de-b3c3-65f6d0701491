diff --git a/C++/API/CMakeLists.txt b/C++/API/CMakeLists.txt
index 8537305..db3d29b 100644
--- a/C++/API/CMakeLists.txt
+++ b/C++/API/CMakeLists.txt
@@ -37,7 +37,10 @@ if(APPLE)
     find_library(IOKIT IOKit)
     target_link_libraries(treehopper pthread ${CORE_FOUNDATION} ${IOKIT})
 elseif(UNIX)
-    target_link_libraries(treehopper usb-1.0 pthread)
+    find_package(PkgConfig REQUIRED)
+    pkg_check_modules(libusb REQUIRED IMPORTED_TARGET libusb-1.0)
+    find_package(Threads REQUIRED)
+    target_link_libraries(treehopper PRIVATE PkgConfig::libusb ${CMAKE_THREAD_LIBS_INIT})
 elseif(WIN32)
     target_link_libraries(treehopper winusb setupapi)
 endif(APPLE)
diff --git a/C++/API/inc/ConnectionService.h b/C++/API/inc/ConnectionService.h
index 66d338e..12a0192 100644
--- a/C++/API/inc/ConnectionService.h
+++ b/C++/API/inc/ConnectionService.h
@@ -8,7 +8,7 @@
 #include <CoreFoundation/CFRunLoop.h>
 #endif
 #ifdef __linux__
-#include "libusb-1.0/libusb.h"
+#include "libusb.h"
 #endif
 #include <vector>
 
diff --git a/C++/API/inc/LibUsbConnection.h b/C++/API/inc/LibUsbConnection.h
index b6d5198..e9cd29e 100644
--- a/C++/API/inc/LibUsbConnection.h
+++ b/C++/API/inc/LibUsbConnection.h
@@ -6,7 +6,7 @@
 
 #include "Treehopper.h"
 
-#include <libusb-1.0/libusb.h>
+#include <libusb.h>
 #include "UsbConnection.h"
 
 namespace Treehopper {
