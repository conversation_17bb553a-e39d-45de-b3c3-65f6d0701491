{"name": "gtk3", "version": "3.24.43", "description": "Portable library for creating graphical user interfaces.", "homepage": "https://www.gtk.org/", "license": null, "dependencies": [{"name": "at-spi2-atk", "platform": "linux"}, "atk", {"name": "cairo", "default-features": false, "features": ["gobject"]}, {"name": "cairo", "default-features": false, "features": ["x11"], "platform": "linux"}, "gdk-pixbuf", {"name": "gdk-pixbuf", "host": true}, "gettext", {"name": "gettext", "host": true, "default-features": false, "features": ["tools"]}, "glib", {"name": "glib", "host": true}, "libepoxy", "pango", {"name": "vcpkg-tool-meson", "host": true}], "features": {"introspection": {"description": "build with introspection", "supports": "!windows | !static", "dependencies": [{"name": "atk", "default-features": false, "features": ["introspection"]}, {"name": "gdk-pixbuf", "host": true, "default-features": false, "features": ["introspection"]}, {"name": "gdk-pixbuf", "default-features": false, "features": ["introspection"]}, "gobject-introspection", {"name": "pango", "default-features": false, "features": ["introspection"]}]}}}