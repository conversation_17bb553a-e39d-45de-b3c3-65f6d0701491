diff --git a/CMakeLists.txt b/CMakeLists.txt
index 20098bf..38c3c95 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -190,3 +190,5 @@ message(STATUS "src processed")
 add_subdirectory(sample)
 message(STATUS "sample processed")
 message(STATUS "DONE!")
+
+install(TARGETS msix)
\ No newline at end of file
diff --git a/src/msix/CMakeLists.txt b/src/msix/CMakeLists.txt
index 296eda4..cf87993 100644
--- a/src/msix/CMakeLists.txt
+++ b/src/msix/CMakeLists.txt
@@ -87,9 +87,9 @@ endif()
 include(msix_resources) # Handles all the certificates and schemas we are going to use.
 
 set(LIB_PUBLIC_HEADERS
-    ../inc/AppxPackaging.hpp
-    ../inc/MSIXWindows.hpp
-    ../inc/MsixErrors.hpp
+    ${CMAKE_CURRENT_SOURCE_DIR}/../inc/AppxPackaging.hpp
+    ${CMAKE_CURRENT_SOURCE_DIR}/../inc/MSIXWindows.hpp
+    ${CMAKE_CURRENT_SOURCE_DIR}/../inc/MsixErrors.hpp
 )
 
 set(MsixSrc) # list with all the files we are going to use
@@ -260,7 +260,7 @@ if(WIN32)
             "/DELAYLOAD:api-ms-win-core-winrt-l1-1-0.dll")
     string(REPLACE ";" " " DELAYFLAGS "${DELAYFLAGS}")
     set_property(TARGET ${PROJECT_NAME} APPEND_STRING PROPERTY LINK_FLAGS "${DELAYFLAGS} /LTCG")
-    set_property(TARGET ${PROJECT_NAME} APPEND_STRING PROPERTY LINK_FLAGS " /DEF:windowsexports.def")
+    set_property(TARGET ${PROJECT_NAME} APPEND_STRING PROPERTY LINK_FLAGS " /DEF:${CMAKE_CURRENT_BINARY_DIR}/windowsexports.def")
     if(USE_STATIC_MSVC)
         if(CMAKE_BUILD_TYPE MATCHES Debug)
             set_property(TARGET ${PROJECT_NAME} APPEND_STRING PROPERTY LINK_FLAGS " /NODEFAULTLIB:MSVCRTD")
