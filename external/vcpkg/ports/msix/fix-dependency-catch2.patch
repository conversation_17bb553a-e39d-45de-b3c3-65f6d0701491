diff --git a/src/test/msixtest/CMakeLists.txt b/src/test/msixtest/CMakeLists.txt
index e991231..e5c43ed 100644
--- a/src/test/msixtest/CMakeLists.txt
+++ b/src/test/msixtest/CMakeLists.txt
@@ -5,6 +5,8 @@
 cmake_minimum_required(VERSION 3.8.0 FATAL_ERROR)
 project (msixtest)
 
+find_package(Catch2 CONFIG REQUIRED)
+
 if(WIN32)
     set(DESCRIPTION "msixtest manifest")
     configure_file(${MSIX_PROJECT_ROOT}/manifest.cmakein ${MSIX_TEST_OUTPUT_DIRECTORY}/${PROJECT_NAME}.exe.manifest CRLF)
@@ -91,7 +93,7 @@ set_target_properties(${PROJECT_NAME} PROPERTIES
 )
 
 add_dependencies(${PROJECT_NAME} msix)
-target_link_libraries(${PROJECT_NAME} msix)
+target_link_libraries(${PROJECT_NAME} msix Catch2::Catch2)
 
 # For windows copy the library
 if(WIN32)
