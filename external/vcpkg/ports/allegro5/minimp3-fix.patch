diff --git a/addons/acodec/CMakeLists.txt b/addons/acodec/CMakeLists.txt
index 6906a99..7896989 100644
--- a/addons/acodec/CMakeLists.txt
+++ b/addons/acodec/CMakeLists.txt
@@ -394,9 +394,10 @@ acodec_summary(" - Opus" SUPPORT_OPUS)
 # MP3
 #
 if(WANT_MP3)
-    find_package(MiniMP3)
+    find_path(MINIMP3_INCLUDE_DIRS "minimp3/minimp3.h")
+    set(MINIMP3_FOUND true)
     if(MINIMP3_FOUND)
-        include_directories(SYSTEM ${MINIMP3_INCLUDE_DIRS})
+        include_directories(SYSTEM ${MINIMP3_INCLUDE_DIRS}/minimp3)
         set(ALLEGRO_CFG_ACODEC_MP3 1)
         list(APPEND ACODEC_SOURCES mp3.c)
     endif(MINIMP3_FOUND)
