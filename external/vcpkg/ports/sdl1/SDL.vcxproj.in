<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|ARM64">
      <Configuration>Debug</Configuration>
      <Platform>ARM64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|ARM64">
      <Configuration>Release</Configuration>
      <Platform>ARM64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{81CE8DAF-EBB2-4761-8E45-B71ABCCA8C68}</ProjectGuid>
    <RootNamespace>SDL</RootNamespace>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>@LIB_TYPE@</ConfigurationType>
    <PlatformToolset>v141</PlatformToolset>
    <UseOfMfc>false</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>@LIB_TYPE@</ConfigurationType>
    <PlatformToolset>v141</PlatformToolset>
    <UseOfMfc>false</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>@LIB_TYPE@</ConfigurationType>
    <PlatformToolset>v141</PlatformToolset>
    <UseOfMfc>false</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|ARM64'" Label="Configuration">
    <ConfigurationType>@LIB_TYPE@</ConfigurationType>
    <PlatformToolset>v141</PlatformToolset>
    <UseOfMfc>false</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>@LIB_TYPE@</ConfigurationType>
    <PlatformToolset>v141</PlatformToolset>
    <UseOfMfc>false</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|ARM64'" Label="Configuration">
    <ConfigurationType>@LIB_TYPE@</ConfigurationType>
    <PlatformToolset>v141</PlatformToolset>
    <UseOfMfc>false</UseOfMfc>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.CPP.UpgradeFromVC70.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.CPP.UpgradeFromVC70.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.CPP.UpgradeFromVC70.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|ARM64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.CPP.UpgradeFromVC70.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.CPP.UpgradeFromVC70.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|ARM64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.CPP.UpgradeFromVC70.props" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>15.0.27924.0</_ProjectFileVersion>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <OutDir>$(SolutionDir)$(Configuration)\</OutDir>
    <IntDir>$(Configuration)\</IntDir>
    <LinkIncremental>true</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <OutDir>$(SolutionDir)$(Platform)\$(Configuration)\</OutDir>
    <IntDir>$(Platform)\$(Configuration)\</IntDir>
    <LinkIncremental>true</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|ARM64'">
    <OutDir>$(SolutionDir)$(Platform)\$(Configuration)\</OutDir>
    <IntDir>$(Platform)\$(Configuration)\</IntDir>
    <LinkIncremental>true</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <OutDir>$(SolutionDir)$(Configuration)\</OutDir>
    <IntDir>$(Configuration)\</IntDir>
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <OutDir>$(SolutionDir)$(Platform)\$(Configuration)\</OutDir>
    <IntDir>$(Platform)\$(Configuration)\</IntDir>
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|ARM64'">
    <OutDir>$(SolutionDir)$(Platform)\$(Configuration)\</OutDir>
    <IntDir>$(Platform)\$(Configuration)\</IntDir>
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Midl>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TargetEnvironment>Win32</TargetEnvironment>
      <TypeLibraryName>.\Debug/SDL.tlb</TypeLibraryName>
    </Midl>
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>$(ProjectDir)\..\..\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_CRT_SECURE_NO_DEPRECATE;_DEBUG;_WINDOWS;_WIN32_WINNT=0x0400;WINDOWS_IGNORE_PACKING_MISMATCH;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>@CRT_TYPE_DBG@</RuntimeLibrary>
      <BufferSecurityCheck>false</BufferSecurityCheck>
      <PrecompiledHeader />
      <PrecompiledHeaderOutputFile>.\Debug/SDL.pch</PrecompiledHeaderOutputFile>
      <WarningLevel>Level3</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <DebugInformationFormat>OldStyle</DebugInformationFormat>
      <CompileAs>Default</CompileAs>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0409</Culture>
    </ResourceCompile>
    <Link>
      <AdditionalOptions>/MACHINE:I386 %(AdditionalOptions)</AdditionalOptions>
      <AdditionalDependencies>winmm.lib;dxguid.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <OutputFile>.\Debug/SDL.dll</OutputFile>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <IgnoreAllDefaultLibraries>false</IgnoreAllDefaultLibraries>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <ProgramDatabaseFile>.\Debug/SDL.pdb</ProgramDatabaseFile>
      <SubSystem>Windows</SubSystem>
      <CLRUnmanagedCodeCheck>false</CLRUnmanagedCodeCheck>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Midl>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TargetEnvironment>X64</TargetEnvironment>
      <TypeLibraryName>.\Debug/SDL.tlb</TypeLibraryName>
    </Midl>
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>$(ProjectDir)\..\..\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_CRT_SECURE_NO_DEPRECATE;_DEBUG;_WINDOWS;_WIN32_WINNT=0x0400;WINDOWS_IGNORE_PACKING_MISMATCH;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>@CRT_TYPE_DBG@</RuntimeLibrary>
      <BufferSecurityCheck>false</BufferSecurityCheck>
      <PrecompiledHeader />
      <PrecompiledHeaderOutputFile>.\Debug/SDL.pch</PrecompiledHeaderOutputFile>
      <WarningLevel>Level3</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <DebugInformationFormat>OldStyle</DebugInformationFormat>
      <CompileAs>Default</CompileAs>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0409</Culture>
    </ResourceCompile>
    <Link>
      <AdditionalDependencies>winmm.lib;dxguid.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <OutputFile>.\Debug/SDL.dll</OutputFile>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <IgnoreAllDefaultLibraries>false</IgnoreAllDefaultLibraries>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <ProgramDatabaseFile>.\Debug/SDL.pdb</ProgramDatabaseFile>
      <SubSystem>Windows</SubSystem>
      <TargetMachine>MachineX64</TargetMachine>
      <CLRUnmanagedCodeCheck>false</CLRUnmanagedCodeCheck>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|ARM64'">
    <Midl>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TypeLibraryName>.\Debug/SDL.tlb</TypeLibraryName>
    </Midl>
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>$(ProjectDir)\..\..\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_CRT_SECURE_NO_DEPRECATE;_DEBUG;_WINDOWS;_WIN32_WINNT=0x0400;WINDOWS_IGNORE_PACKING_MISMATCH;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>@CRT_TYPE_DBG@</RuntimeLibrary>
      <BufferSecurityCheck>false</BufferSecurityCheck>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <PrecompiledHeaderOutputFile>.\Debug/SDL.pch</PrecompiledHeaderOutputFile>
      <WarningLevel>Level3</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <DebugInformationFormat>OldStyle</DebugInformationFormat>
      <CompileAs>Default</CompileAs>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0409</Culture>
    </ResourceCompile>
    <Link>
      <AdditionalDependencies>winmm.lib;dxguid.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <OutputFile>.\Debug/SDL.dll</OutputFile>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <IgnoreAllDefaultLibraries>false</IgnoreAllDefaultLibraries>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <ProgramDatabaseFile>.\Debug/SDL.pdb</ProgramDatabaseFile>
      <SubSystem>Windows</SubSystem>
      <CLRUnmanagedCodeCheck>false</CLRUnmanagedCodeCheck>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Midl>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TargetEnvironment>Win32</TargetEnvironment>
      <TypeLibraryName>.\Release/SDL.tlb</TypeLibraryName>
    </Midl>
    <ClCompile>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <IntrinsicFunctions>false</IntrinsicFunctions>
      <AdditionalIncludeDirectories>$(ProjectDir)\..\..\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_CRT_SECURE_NO_DEPRECATE;NDEBUG;_WINDOWS;_WIN32_WINNT=0x0400;WINDOWS_IGNORE_PACKING_MISMATCH;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <StringPooling>true</StringPooling>
      <RuntimeLibrary>@CRT_TYPE_REL@</RuntimeLibrary>
      <BufferSecurityCheck>false</BufferSecurityCheck>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <PrecompiledHeader />
      <PrecompiledHeaderOutputFile>.\Release/SDL.pch</PrecompiledHeaderOutputFile>
      <WarningLevel>Level3</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <CompileAs>Default</CompileAs>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0409</Culture>
    </ResourceCompile>
    <Link>
      <AdditionalOptions>/MACHINE:I386 %(AdditionalOptions)</AdditionalOptions>
      <AdditionalDependencies>winmm.lib;dxguid.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <OutputFile>.\Release/SDL.dll</OutputFile>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <IgnoreAllDefaultLibraries>false</IgnoreAllDefaultLibraries>
      <ProgramDatabaseFile>.\Release/SDL.pdb</ProgramDatabaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Midl>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TargetEnvironment>X64</TargetEnvironment>
      <TypeLibraryName>.\Release/SDL.tlb</TypeLibraryName>
    </Midl>
    <ClCompile>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <IntrinsicFunctions>false</IntrinsicFunctions>
      <AdditionalIncludeDirectories>$(ProjectDir)\..\..\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_CRT_SECURE_NO_DEPRECATE;NDEBUG;_WINDOWS;_WIN32_WINNT=0x0400;WINDOWS_IGNORE_PACKING_MISMATCH;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <StringPooling>true</StringPooling>
      <RuntimeLibrary>@CRT_TYPE_REL@</RuntimeLibrary>
      <BufferSecurityCheck>false</BufferSecurityCheck>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <PrecompiledHeader />
      <PrecompiledHeaderOutputFile>.\Release/SDL.pch</PrecompiledHeaderOutputFile>
      <WarningLevel>Level3</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <CompileAs>Default</CompileAs>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0409</Culture>
    </ResourceCompile>
    <Link>
      <AdditionalDependencies>winmm.lib;dxguid.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <OutputFile>.\Release/SDL.dll</OutputFile>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <IgnoreAllDefaultLibraries>false</IgnoreAllDefaultLibraries>
      <ProgramDatabaseFile>.\Release/SDL.pdb</ProgramDatabaseFile>
      <SubSystem>Windows</SubSystem>
      <TargetMachine>MachineX64</TargetMachine>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|ARM64'">
    <Midl>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TypeLibraryName>.\Release/SDL.tlb</TypeLibraryName>
    </Midl>
    <ClCompile>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <IntrinsicFunctions>false</IntrinsicFunctions>
      <AdditionalIncludeDirectories>$(ProjectDir)\..\..\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_CRT_SECURE_NO_DEPRECATE;NDEBUG;_WINDOWS;_WIN32_WINNT=0x0400;WINDOWS_IGNORE_PACKING_MISMATCH;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <StringPooling>true</StringPooling>
      <RuntimeLibrary>@CRT_TYPE_REL@</RuntimeLibrary>
      <BufferSecurityCheck>false</BufferSecurityCheck>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <PrecompiledHeaderOutputFile>.\Release/SDL.pch</PrecompiledHeaderOutputFile>
      <WarningLevel>Level3</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <CompileAs>Default</CompileAs>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0409</Culture>
    </ResourceCompile>
    <Link>
      <AdditionalDependencies>winmm.lib;dxguid.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <OutputFile>.\Release/SDL.dll</OutputFile>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <IgnoreAllDefaultLibraries>false</IgnoreAllDefaultLibraries>
      <ProgramDatabaseFile>.\Release/SDL.pdb</ProgramDatabaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="..\..\src\audio\disk\SDL_diskaudio.c" />
    <ClCompile Include="..\..\src\audio\dummy\SDL_dummyaudio.c" />
    <ClCompile Include="..\..\src\audio\SDL_audio.c" />
    <ClCompile Include="..\..\src\audio\SDL_audiocvt.c" />
    <ClCompile Include="..\..\src\audio\SDL_mixer.c" />
    <ClCompile Include="..\..\src\audio\SDL_mixer_MMX_VC.c" />
    <ClCompile Include="..\..\src\audio\SDL_wave.c" />
    <ClCompile Include="..\..\src\audio\windib\SDL_dibaudio.c" />
    <ClCompile Include="..\..\src\audio\windx5\SDL_dx5audio.c" />
    <ClCompile Include="..\..\src\cdrom\SDL_cdrom.c" />
    <ClCompile Include="..\..\src\cdrom\win32\SDL_syscdrom.c" />
    <ClCompile Include="..\..\src\cpuinfo\SDL_cpuinfo.c" />
    <ClCompile Include="..\..\src\events\SDL_active.c" />
    <ClCompile Include="..\..\src\events\SDL_events.c" />
    <ClCompile Include="..\..\src\events\SDL_expose.c" />
    <ClCompile Include="..\..\src\events\SDL_keyboard.c" />
    <ClCompile Include="..\..\src\events\SDL_mouse.c" />
    <ClCompile Include="..\..\src\events\SDL_quit.c" />
    <ClCompile Include="..\..\src\events\SDL_resize.c" />
    <ClCompile Include="..\..\src\file\SDL_rwops.c" />
    <ClCompile Include="..\..\src\joystick\SDL_joystick.c" />
    <ClCompile Include="..\..\src\joystick\win32\SDL_mmjoystick.c" />
    <ClCompile Include="..\..\src\loadso\win32\SDL_sysloadso.c" />
    <ClCompile Include="..\..\src\SDL.c" />
    <ClCompile Include="..\..\src\SDL_error.c" />
    <ClCompile Include="..\..\src\SDL_fatal.c" />
    <ClCompile Include="..\..\src\stdlib\SDL_getenv.c" />
    <ClCompile Include="..\..\src\stdlib\SDL_iconv.c" />
    <ClCompile Include="..\..\src\stdlib\SDL_malloc.c" />
    <ClCompile Include="..\..\src\stdlib\SDL_qsort.c" />
    <ClCompile Include="..\..\src\stdlib\SDL_stdlib.c" />
    <ClCompile Include="..\..\src\stdlib\SDL_string.c" />
    <ClCompile Include="..\..\src\thread\generic\SDL_syscond.c" />
    <ClCompile Include="..\..\src\thread\SDL_thread.c" />
    <ClCompile Include="..\..\src\thread\win32\SDL_sysmutex.c" />
    <ClCompile Include="..\..\src\thread\win32\SDL_syssem.c" />
    <ClCompile Include="..\..\src\thread\win32\SDL_systhread.c" />
    <ClCompile Include="..\..\src\timer\SDL_timer.c" />
    <ClCompile Include="..\..\src\timer\win32\SDL_systimer.c" />
    <ClCompile Include="..\..\src\video\dummy\SDL_nullevents.c" />
    <ClCompile Include="..\..\src\video\dummy\SDL_nullmouse.c" />
    <ClCompile Include="..\..\src\video\dummy\SDL_nullvideo.c" />
    <ClCompile Include="..\..\src\video\SDL_blit.c" />
    <ClCompile Include="..\..\src\video\SDL_blit_0.c" />
    <ClCompile Include="..\..\src\video\SDL_blit_1.c" />
    <ClCompile Include="..\..\src\video\SDL_blit_A.c" />
    <ClCompile Include="..\..\src\video\SDL_blit_N.c" />
    <ClCompile Include="..\..\src\video\SDL_bmp.c" />
    <ClCompile Include="..\..\src\video\SDL_cursor.c" />
    <ClCompile Include="..\..\src\video\SDL_gamma.c" />
    <ClCompile Include="..\..\src\video\SDL_pixels.c" />
    <ClCompile Include="..\..\src\video\SDL_RLEaccel.c" />
    <ClCompile Include="..\..\src\video\SDL_stretch.c" />
    <ClCompile Include="..\..\src\video\SDL_surface.c" />
    <ClCompile Include="..\..\src\video\SDL_video.c" />
    <ClCompile Include="..\..\src\video\SDL_yuv.c" />
    <ClCompile Include="..\..\src\video\SDL_yuv_sw.c" />
    <ClCompile Include="..\..\src\video\wincommon\SDL_sysevents.c" />
    <ClCompile Include="..\..\src\video\wincommon\SDL_sysmouse.c" />
    <ClCompile Include="..\..\src\video\wincommon\SDL_syswm.c" />
    <ClCompile Include="..\..\src\video\wincommon\SDL_wingl.c" />
    <ClCompile Include="..\..\src\video\windib\SDL_dibevents.c" />
    <ClCompile Include="..\..\src\video\windib\SDL_dibvideo.c" />
    <ClCompile Include="..\..\src\video\Windx5\SDL_dx5events.c" />
    <ClCompile Include="..\..\src\video\Windx5\SDL_dx5video.c" />
    <ClCompile Include="..\..\src\video\windx5\SDL_dx5yuv.c" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\..\src\audio\disk\SDL_diskaudio.h" />
    <ClInclude Include="..\..\src\audio\dummy\SDL_dummyaudio.h" />
    <ClInclude Include="..\..\src\audio\SDL_audiomem.h" />
    <ClInclude Include="..\..\src\audio\SDL_audio_c.h" />
    <ClInclude Include="..\..\src\audio\SDL_sysaudio.h" />
    <ClInclude Include="..\..\src\audio\SDL_wave.h" />
    <ClInclude Include="..\..\src\audio\windib\SDL_dibaudio.h" />
    <ClInclude Include="..\..\src\audio\windx5\SDL_dx5audio.h" />
    <ClInclude Include="..\..\src\cdrom\SDL_syscdrom.h" />
    <ClInclude Include="..\..\src\events\SDL_events_c.h" />
    <ClInclude Include="..\..\src\events\SDL_sysevents.h" />
    <ClInclude Include="..\..\src\joystick\SDL_joystick_c.h" />
    <ClInclude Include="..\..\src\joystick\SDL_sysjoystick.h" />
    <ClInclude Include="..\..\src\SDL_error_c.h" />
    <ClInclude Include="..\..\src\SDL_fatal.h" />
    <ClInclude Include="..\..\src\thread\SDL_systhread.h" />
    <ClInclude Include="..\..\src\thread\SDL_thread_c.h" />
    <ClInclude Include="..\..\src\thread\win32\SDL_systhread_c.h" />
    <ClInclude Include="..\..\src\timer\SDL_systimer.h" />
    <ClInclude Include="..\..\src\timer\SDL_timer_c.h" />
    <ClInclude Include="..\..\src\video\dummy\SDL_nullevents_c.h" />
    <ClInclude Include="..\..\src\video\dummy\SDL_nullmouse_c.h" />
    <ClInclude Include="..\..\src\video\dummy\SDL_nullvideo.h" />
    <ClInclude Include="..\..\src\video\SDL_blit.h" />
    <ClInclude Include="..\..\src\video\SDL_blit_A.h" />
    <ClInclude Include="..\..\src\video\SDL_cursor_c.h" />
    <ClInclude Include="..\..\src\video\SDL_leaks.h" />
    <ClInclude Include="..\..\src\video\SDL_pixels_c.h" />
    <ClInclude Include="..\..\src\video\SDL_RLEaccel_c.h" />
    <ClInclude Include="..\..\src\video\SDL_stretch_c.h" />
    <ClInclude Include="..\..\src\video\SDL_sysvideo.h" />
    <ClInclude Include="..\..\src\video\SDL_yuvfuncs.h" />
    <ClInclude Include="..\..\src\video\SDL_yuv_sw_c.h" />
    <ClInclude Include="..\..\src\video\wincommon\SDL_lowvideo.h" />
    <ClInclude Include="..\..\src\video\wincommon\SDL_sysmouse_c.h" />
    <ClInclude Include="..\..\src\video\wincommon\SDL_syswm_c.h" />
    <ClInclude Include="..\..\src\video\wincommon\SDL_wingl_c.h" />
    <ClInclude Include="..\..\src\video\wincommon\Wmmsg.h" />
    <ClInclude Include="..\..\src\video\windib\SDL_dibevents_c.h" />
    <ClInclude Include="..\..\src\video\windib\SDL_dibvideo.h" />
    <ClInclude Include="..\..\src\video\windib\SDL_vkeys.h" />
    <ClInclude Include="..\..\src\video\Windx5\SDL_dx5events_c.h" />
    <ClInclude Include="..\..\src\video\Windx5\SDL_dx5video.h" />
    <ClInclude Include="..\..\src\video\windx5\SDL_dx5yuv_c.h" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>