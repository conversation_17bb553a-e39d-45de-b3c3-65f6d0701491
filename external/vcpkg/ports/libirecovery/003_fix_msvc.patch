diff --git a/src/libirecovery.c b/src/libirecovery.c
index 45ff6f2..d9ff1e6 100644
--- a/src/libirecovery.c
+++ b/src/libirecovery.c
@@ -29,7 +29,9 @@
 #include <string.h>
 #include <inttypes.h>
 #include <ctype.h>
+#ifndef _MSC_VER
 #include <unistd.h>
+#endif
 #include <sys/stat.h>
 
 #include <libimobiledevice-glue/collection.h>
@@ -59,6 +61,10 @@
 
 #include "libirecovery.h"
 
+#ifdef _MSC_VER
+#define strcasecmp _stricmp
+#endif
+
 struct irecv_client_private {
 	int debug;
 	int usb_config;
