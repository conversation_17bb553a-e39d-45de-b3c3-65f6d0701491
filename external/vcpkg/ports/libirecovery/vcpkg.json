{"name": "libirecovery", "version-date": "2023-05-13", "port-version": 2, "description": "Library and utility to talk to iBoot/iBSS via USB on Mac OS X, Windows, and Linux", "homepage": "https://libimobiledevice.org/", "license": "LGPL-2.1-or-later", "supports": "!uwp", "dependencies": ["libimobiledevice-glue", {"name": "libusb", "platform": "!windows & !osx"}, {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"tools": {"description": "build command line tool", "supports": "!android & !ios & !xbox", "dependencies": ["getopt", "readline"]}}}