diff --git a/ArcusConfig.cmake.in b/ArcusConfig.cmake.in
index 3208a69..fb4a968 100644
--- a/ArcusConfig.cmake.in
+++ b/ArcusConfig.cmake.in
@@ -4,7 +4,7 @@
 # However, if ProtobufConfig is used instead, there is a CMake option that controls
 # this, which defaults to OFF. We need to force this option to ON instead.
 set(protobuf_MODULE_COMPATIBLE ON CACHE "" INTERNAL FORCE)
-find_package(Protobuf 3.0.0 REQUIRED)
+find_package(Protobuf REQUIRED)
 
 get_filename_component(SELF_DIR "${CMAKE_CURRENT_LIST_FILE}" PATH)
 include(${SELF_DIR}/Arcus-targets.cmake)
diff --git a/CMakeLists.txt b/CMakeLists.txt
index 1d736ba..66d6c13 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -17,7 +17,7 @@ endif()
 # However, if ProtobufConfig is used instead, there is a CMake option that controls
 # this, which defaults to OFF. We need to force this option to ON instead.
 set(protobuf_MODULE_COMPATIBLE ON CACHE INTERNAL "" FORCE)
-find_package(Protobuf 3.0.0 REQUIRED)
+find_package(Protobuf REQUIRED)
 
 set(CMAKE_POSITION_INDEPENDENT_CODE ON) #Required if a patch to libArcus needs to be made via templates.
 
@@ -103,9 +103,8 @@ endif()
 target_include_directories(Arcus PUBLIC
     $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/src>
     $<INSTALL_INTERFACE:${CMAKE_INSTALL_INCLUDEDIR}>
-    ${PROTOBUF_INCLUDE_DIR}
 )
-target_link_libraries(Arcus PUBLIC ${PROTOBUF_LIBRARIES})
+target_link_libraries(Arcus PUBLIC protobuf::libprotobuf)
 
 if(WIN32)
     add_definitions(-D_WIN32_WINNT=0x0600) # Declare we require Vista or higher, this allows us to use IPv6 functions.
