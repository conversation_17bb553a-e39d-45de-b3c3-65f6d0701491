{"name": "breakpad", "version-date": "2024-02-16", "description": "a set of client and server components which implement a crash-reporting system.", "homepage": "https://github.com/google/breakpad", "license": "BSD-3-<PERSON><PERSON>", "supports": "!uwp & (!windows | !arm) & (!windows | !arm64)", "dependencies": [{"name": "atl", "platform": "windows"}, "libdisasm", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}, {"name": "zlib", "platform": "linux"}], "features": {"tools": {"description": "Build breakpad tools", "supports": "linux"}}}