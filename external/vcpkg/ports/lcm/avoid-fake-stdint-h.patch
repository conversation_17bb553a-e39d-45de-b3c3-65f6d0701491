diff --git a/CMakeLists.txt b/CMakeLists.txt
index 90833e3..2b7cff8 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -17,7 +17,6 @@ include(lcm-cmake/version.cmake)
 set(CMAKE_INCLUDE_CURRENT_DIR ON)
 if (WIN32)
   add_definitions(-DWIN32 -D_CRT_SECURE_NO_WARNINGS)
-  include_directories(${lcm_SOURCE_DIR}/WinSpecific/include)
   include_directories(${lcm_SOURCE_DIR}/WinSpecific/getopt)
   include_directories(${lcm_SOURCE_DIR})
   add_subdirectory(WinSpecific)
diff --git a/lcm-python/setup.py b/lcm-python/setup.py
index e015d20..2528d9f 100644
--- a/lcm-python/setup.py
+++ b/lcm-python/setup.py
@@ -47,7 +47,6 @@ if os.name == 'nt':
         sys.exit('GLIB_PATH environment variable not set.')
 
     include_dirs += [ \
-            os.path.join("..", "WinSpecific\include"),
             os.path.join("..", "WinSpecific"),
             os.path.join(glibPath, "include", "glib-2.0"),
             os.path.join(glibPath, "lib", "glib-2.0", "include") ]
