diff --git a/lcm/CMakeLists.txt b/lcm/CMakeLists.txt
index a706a85..639ec12 100644
--- a/lcm/CMakeLists.txt
+++ b/lcm/CMakeLists.txt
@@ -76,8 +76,12 @@ endif()
 target_include_directories(lcm-coretypes INTERFACE
   $<BUILD_INTERFACE:${lcm_SOURCE_DIR}>
 )
-
-install(TARGETS lcm-coretypes lcm-static lcm
+if(BUILD_SHARED_LIBS)
+  set(INSTALL_TARGETS lcm)
+else()
+  set(INSTALL_TARGETS lcm-static)
+endif()
+install(TARGETS lcm-coretypes ${INSTALL_TARGETS}
   EXPORT lcmTargets
   RUNTIME DESTINATION bin
   LIBRARY DESTINATION lib${LIB_SUFFIX}
