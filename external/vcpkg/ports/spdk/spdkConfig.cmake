
FUNCTION(SET_LIBRARY_TARGET NAMESPACE LIB_NAME DEBUG_LIB_FILE_NAME RELEASE_LIB_FILE_NAME INCLUDE_DIR)
    ADD_LIBRARY(${NAMESPACE}::${LIB_NAME} STATIC IMPORTED)
    SET_TARGET_PROPERTIES(${NAMESPACE}::${LIB_NAME} PROPERTIES
                          IMPORTED_CONFIGURATIONS "RELEASE;DEBUG"
                          IMPORTED_LOCATION_RELEASE "${RELEASE_LIB_FILE_NAME}"
                          IMPORTED_LOCATION_DEBUG "${DEBUG_LIB_FILE_NAME}"
                          INTERFACE_INCLUDE_DIRECTORIES "${INCLUDE_DIR}"
                          )
    SET(${NAMESPACE}_${LIB_NAME}_FOUND 1)
ENDFUNCTION()

GET_FILENAME_COMPONENT(SPDK_ROOT "${CMAKE_CURRENT_LIST_FILE}" PATH)
GET_FILENAME_COMPONENT(SPDK_ROOT "${SPDK_ROOT}" PATH)
GET_FILENAME_COMPONENT(SPDK_ROOT "${SPDK_ROOT}" PATH)

IF (CMAKE_BUILD_TYPE STREQUAL Debug)
    LINK_DIRECTORIES(${SPDK_ROOT}/debug/lib/)
ELSE ()
    LINK_DIRECTORIES(${SPDK_ROOT}/lib/)
ENDIF ()

FILE(GLOB SPDK_LIBS ${SPDK_ROOT}/lib/libspdk*.*)
FOREACH (LIB_FILE_NAME ${SPDK_LIBS})
    GET_FILENAME_COMPONENT(LIB_NAME ${LIB_FILE_NAME} NAME_WE)
    GET_FILENAME_COMPONENT(FULL_LIB_NAME ${LIB_FILE_NAME} NAME)
    STRING(REPLACE "lib" "" LIB_NAME "${LIB_NAME}")
    SET_LIBRARY_TARGET("SPDK" "${LIB_NAME}" "${SPDK_ROOT}/debug/lib/${FULL_LIB_NAME}" "${SPDK_ROOT}/lib/${FULL_LIB_NAME}" "${SPDK_ROOT}/include/spdk")
ENDFOREACH ()
