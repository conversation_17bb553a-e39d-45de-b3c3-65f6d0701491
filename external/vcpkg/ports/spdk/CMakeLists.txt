CMAKE_MINIMUM_REQUIRED(VERSION 3.9)
PROJECT(spdk C)

LIST(APPEND CMAKE_MODULE_PATH "${CMAKE_SOURCE_DIR}")
LIST(APPEND CMAKE_PREFIX_PATH "${CMAKE_SOURCE_DIR}")

FIND_PACKAGE(Numa REQUIRED)
FIND_PACKAGE(uuid REQUIRED)
FIND_PACKAGE(ibverbs REQUIRED)
FIND_PACKAGE(RDMA REQUIRED)
FIND_PACKAGE(OpenSSL REQUIRED)
FIND_PACKAGE(Python COMPONENTS Interpreter REQUIRED)

FIND_PROGRAM(MAKE make)

IF (SOURCE_PATH)
    SET(CMAKE_SOURCE_DIR ${SOURCE_PATH})
ENDIF ()

SET(SPDK_CONFIG --disable-tests --with-rdma --with-dpdk=${CMAKE_SOURCE_DIR}/../../../spdk-dpdk/src/spdk-18.11-411c51fb97/build)
# No option to define path for isa-l (--with-isal=${CMAKE_SOURCE_DIR}/../../../spdk-isal/src/spdk-d34ebb51cd)
IF (CMAKE_BUILD_TYPE STREQUAL Debug)
    LIST(APPEND ${SPDK_CONFIG} --enable-debug)
ENDIF ()

SET(DPDK_CONFIG "-fPIC")
IF (CMAKE_BUILD_TYPE STREQUAL Debug)
    STRING(APPEND ${DPDK_CONFIG} "-O0 -g")
ENDIF ()

ADD_CUSTOM_TARGET(spdk-configure
                  COMMAND ./configure ${SPDK_CONFIG}
                  WORKING_DIRECTORY ${CMAKE_SOURCE_DIR}/
                  VERBATIM
                  )

ADD_CUSTOM_TARGET(spdk ALL
                  COMMAND ${MAKE} -j DPDK_CFLAGS=${DPDK_CONFIG}
                  WORKING_DIRECTORY ${CMAKE_SOURCE_DIR}/
                  )

ADD_DEPENDENCIES(spdk spdk-configure)

INSTALL(DIRECTORY ${CMAKE_SOURCE_DIR}/build/lib
        DESTINATION ${CMAKE_SOURCE_DIR}/${CMAKE_BUILD_TYPE}/)
