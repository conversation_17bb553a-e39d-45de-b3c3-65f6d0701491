The package spdk is compatible with built-in CMake targets:

Add following to build examples/nvme/perf/perf.c

    FIND_PACKAGE(spdk CONFIG REQUIRED)
    FIND_PACKAGE(spdk-dpdk CONFIG REQUIRED)

    ADD_EXECUTABLE(SPDKTest perf.c)

    TARGET_LINK_DIRECTORIES(SPDKTest PRIVATE ${VCPKG_INSTALLED_DIR}/${VCPKG_TARGET_TRIPLET}/lib/spdk)
    TARGET_LINK_LIBRARIES(SPDKTest PRIVATE
                          SPDK::spdk_app_rpc
                          SPDK::spdk_bdev
                          SPDK::spdk_bdev_null
                          SPDK::spdk_bdev_rpc
                          SPDK::spdk_conf
                          SPDK::spdk_copy
                          SPDK::spdk_thread
                          SPDK::spdk_event
                          SPDK::spdk_event_bdev
                          SPDK::spdk_event_copy
                          SPDK::spdk_event_net
                          SPDK::spdk_net
                          SPDK::spdk_nvmf
                          SPDK::spdk_nvme
                          SPDK::spdk_sock_posix
                          SPDK::spdk_sock
                          SPDK::spdk_rpc
                          SPDK::spdk_jsonrpc
                          SPDK::spdk_json
                          SPDK::spdk_trace
                          SPDK::spdk_util
                          SPDK::spdk_log
                          SPDK::spdk_env_dpdk
                          SPDK::dpdk
                          numa
                          rdmacm
                          uuid
                          ibverbs
                          pthread
                          dl
                          )

Configuration script can be found at: ${CURRENT_PACKAGES_DIR}/share/${PORT}/scripts
