{"name": "spout2", "version-string": "2.007.010", "description": "Spout is a video frame sharing system for Microsoft Windows, which allows applications to share OpenGL textures in a similar way to Syphon for the Mac.", "homepage": "https://github.com/leadedge/Spout2", "supports": "windows & !uwp & !arm64", "dependencies": ["opengl", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"dx": {"description": "A sub-set of the Spout SDK for applications using DirectX rather than OpenGL."}}}