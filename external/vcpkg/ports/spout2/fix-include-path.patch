Subject: [PATCH] fixup for vcpkg
---
Index: SPOUTSDK/SpoutDirectX/SpoutDX/CMakeLists.txt
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/SPOUTSDK/SpoutDirectX/SpoutDX/CMakeLists.txt b/SPOUTSDK/SpoutDirectX/SpoutDX/CMakeLists.txt
--- a/SPOUTSDK/SpoutDirectX/SpoutDX/CMakeLists.txt	(revision 62362774c96547d63b502d7efd5cfbf138eb7570)
+++ b/SPOUTSDK/SpoutDirectX/SpoutDX/CMakeLists.txt	(revision e16402c39ed2389692876d7bbd1c7d4a771a5b86)
@@ -67,6 +67,10 @@
     SPOUT_BUILD_DLL
     SPOUTLIBRARY_EXPORTS
 )
+
+
+target_include_directories(SpoutDX_static PRIVATE ../../)
+target_include_directories(SpoutDX PRIVATE ../../)
 #/-------------------------------------- . -----------------------------------\#
 
 
Index: SPOUTSDK/SpoutDirectX/SpoutDX/SpoutDX.h
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/SPOUTSDK/SpoutDirectX/SpoutDX/SpoutDX.h b/SPOUTSDK/SpoutDirectX/SpoutDX/SpoutDX.h
--- a/SPOUTSDK/SpoutDirectX/SpoutDX/SpoutDX.h	(revision 62362774c96547d63b502d7efd5cfbf138eb7570)
+++ b/SPOUTSDK/SpoutDirectX/SpoutDX/SpoutDX.h	(revision e16402c39ed2389692876d7bbd1c7d4a771a5b86)
@@ -33,12 +33,12 @@
 #define __spoutDX__
 
 // Change the path as required
-#include "..\..\SpoutGL\SpoutCommon.h" // for dll build
-#include "..\..\SpoutGL\SpoutSenderNames.h" // for sender creation and update
-#include "..\..\SpoutGL\SpoutDirectX.h" // for creating DX11 textures
-#include "..\..\SpoutGL\SpoutFrameCount.h" // for mutex lock and new frame signal
-#include "..\..\SpoutGL\SpoutCopy.h" // for pixel copy
-#include "..\..\SpoutGL\SpoutUtils.h" // Registry utiities
+#include "SpoutGL\SpoutCommon.h" // for dll build
+#include "SpoutGL\SpoutSenderNames.h" // for sender creation and update
+#include "SpoutGL\SpoutDirectX.h" // for creating DX11 textures
+#include "SpoutGL\SpoutFrameCount.h" // for mutex lock and new frame signal
+#include "SpoutGL\SpoutCopy.h" // for pixel copy
+#include "SpoutGL\SpoutUtils.h" // Registry utiities
 
 #include <direct.h> // for _getcwd
 #include <TlHelp32.h> // for PROCESSENTRY32
