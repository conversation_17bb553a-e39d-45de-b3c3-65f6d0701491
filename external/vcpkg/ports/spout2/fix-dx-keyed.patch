Subject: [PATCH] fix dx keyed
---
Index: SPOUTSDK/SpoutDirectX/SpoutDX/SpoutDX.cpp
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/SPOUTSDK/SpoutDirectX/SpoutDX/SpoutDX.cpp b/SPOUTSDK/SpoutDirectX/SpoutDX/SpoutDX.cpp
--- a/SPOUTSDK/SpoutDirectX/SpoutDX/SpoutDX.cpp	(revision e16402c39ed2389692876d7bbd1c7d4a771a5b86)
+++ b/SPOUTSDK/SpoutDirectX/SpoutDX/SpoutDX.cpp	(revision f3ba250699b87c8004a3430d6b00f3f537af0c0d)
@@ -193,6 +193,7 @@
 	m_bSwapRB = false;
 	m_bAdapt = false; // Receiver switch to the sender's graphics adapter
 	m_bMemoryShare = GetMemoryShareMode(); // 2.006 memoryshare mode
+    m_bKeyed = false;
 
 	ZeroMemory(&m_SenderInfo, sizeof(SharedTextureInfo));
 	ZeroMemory(&m_ShExecInfo, sizeof(m_ShExecInfo));
