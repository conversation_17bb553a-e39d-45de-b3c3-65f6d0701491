diff --git a/wpilibNewCommands/wpilibNewCommands-config.cmake.in b/wpilibNewCommands/wpilibNewCommands-config.cmake.in
index 75aa6ad7d..8a8d8d8ec 100644
--- a/wpilibNewCommands/wpilibNewCommands-config.cmake.in
+++ b/wpilibNewCommands/wpilibNewCommands-config.cmake.in
@@ -1,5 +1,4 @@
 include(CMakeFindDependencyMacro)
- @FILENAME_DEP_REPLACE@
  @WPIUTIL_DEP_REPLACE@
  @NTCORE_DEP_REPLACE@
  @CSCORE_DEP_REPLACE@
@@ -8,4 +7,5 @@ include(CMakeFindDependencyMacro)
  @WPILIBC_DEP_REPLACE@
  @WPIMATH_DEP_REPLACE@
 
+ @FILENAME_DEP_REPLACE@
  include(${SELF_DIR}/wpilibNewCommands.cmake)
diff --git a/wpimath/wpimath-config.cmake.in b/wpimath/wpimath-config.cmake.in
index 4769e4317..9100d7943 100644
--- a/wpimath/wpimath-config.cmake.in
+++ b/wpimath/wpimath-config.cmake.in
@@ -2,5 +2,9 @@ include(CMakeFindDependencyMacro)
 @FILENAME_DEP_REPLACE@
 @WPIUTIL_DEP_REPLACE@
 
+if(@USE_SYSTEM_EIGEN@)
+    find_dependency(Eigen3)
+endif()
+
 @FILENAME_DEP_REPLACE@
 include(${SELF_DIR}/wpimath.cmake)
diff --git a/wpiutil/wpiutil-config.cmake.in b/wpiutil/wpiutil-config.cmake.in
index fde839e2f..3f696c8a0 100644
--- a/wpiutil/wpiutil-config.cmake.in
+++ b/wpiutil/wpiutil-config.cmake.in
@@ -4,5 +4,9 @@ set(THREADS_PREFER_PTHREAD_FLAG ON)
 find_dependency(Threads)
 @FMTLIB_SYSTEM_REPLACE@
 
+if(@USE_SYSTEM_FMTLIB@)
+    find_dependency(fmt)
+endif()
+
 @FILENAME_DEP_REPLACE@
 include(${SELF_DIR}/wpiutil.cmake)
