{"name": "wpilib", "version-date": "2023-08-24", "port-version": 2, "description": "WPILib is the software library package for the FIRST Robotics Competition. The core install includes wpiutil, a common utilies library, and ntcore, the base NetworkTables library.", "homepage": "https://github.com/wpilibsuite/allwpilib", "license": null, "dependencies": ["eigen3", "fmt", "libuv", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}, {"name": "vcpkg-get-python-packages", "host": true}], "features": {"allwpilib": {"description": "Enables the simulation HAL, and the high level wpilibc library.", "dependencies": ["opencv", {"name": "wpilib", "features": ["cameraserver"]}]}, "cameraserver": {"description": "Enables the CameraServer and CSCore libraries for manipulating USB Cameras and HTTP Camera Streams", "dependencies": ["opencv"]}}}