diff --git a/cmake/modules/CompileWarnings.cmake b/cmake/modules/CompileWarnings.cmake
index 93b35b8ab..df7ff7365 100644
--- a/cmake/modules/CompileWarnings.cmake
+++ b/cmake/modules/CompileWarnings.cmake
@@ -1,8 +1,8 @@
 macro(wpilib_target_warnings target)
     if(NOT MSVC)
-        target_compile_options(${target} PRIVATE -Wall -pedantic -Wextra -Werror -Wno-unused-parameter ${WPILIB_TARGET_WARNINGS})
+        target_compile_options(${target} PRIVATE -Wall -pedantic -Wextra -Wno-unused-parameter ${WPILIB_TARGET_WARNINGS})
     else()
-        target_compile_options(${target} PRIVATE /wd4146 /wd4244 /wd4251 /wd4267 /WX /D_CRT_SECURE_NO_WARNINGS ${WPILIB_TARGET_WARNINGS})
+        target_compile_options(${target} PRIVATE /wd4146 /wd4244 /wd4251 /wd4267 /D_CRT_SECURE_NO_WARNINGS ${WPILIB_TARGET_WARNINGS})
     endif()
 
     # Suppress C++-specific OpenCV warning; C compiler rejects it with an error
