{"name": "kdsingleapplication", "version": "1.1.0", "port-version": 1, "description": "KDSingleApplication is a helper class for single-instance policy applications.", "homepage": "https://github.com/KDAB/KDSingleApplication", "license": "MIT", "dependencies": [{"name": "qtbase", "default-features": false, "features": ["network", "widgets"]}, {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}]}