diff --git a/configure.ac b/configure.ac
index 803abe77b..7d694e401 100644
--- a/configure.ac
+++ b/configure.ac
@@ -11,6 +11,7 @@ AM_MAINTAINER_MODE
 XCB_UTIL_COMMON([1.4], [1.6])
 
 PKG_CHECK_MODULES(XCB_RENDER, xcb-render)
+PKG_CHECK_MODULES(PTHREAD_STUBS, pthread-stubs)
 
 AC_CONFIG_FILES([Makefile
 	renderutil/Makefile
diff --git a/renderutil/Makefile.am b/renderutil/Makefile.am
index 3a5179884..d403aa34a 100644
--- a/renderutil/Makefile.am
+++ b/renderutil/Makefile.am
@@ -4,9 +4,9 @@ lib_LTLIBRARIES = libxcb-render-util.la
 xcbinclude_HEADERS = xcb_renderutil.h
 
 AM_CFLAGS = $(BASE_CFLAGS)
-AM_CPPFLAGS = $(XCB_CFLAGS) $(XCB_RENDER_CFLAGS)
+AM_CPPFLAGS = $(XCB_CFLAGS) $(XCB_RENDER_CFLAGS) ${PTHREAD_STUBS_CFLAGS}
 libxcb_render_util_la_SOURCES = cache.c util.c glyph.c
-libxcb_render_util_la_LIBADD = $(XCB_LIBS) $(XCB_RENDER_LIBS)
+libxcb_render_util_la_LIBADD = $(XCB_LIBS) $(XCB_RENDER_LIBS) ${PTHREAD_STUBS_LIBS}
 libxcb_render_util_la_LDFLAGS = -no-undefined
 
 pkgconfig_DATA = xcb-renderutil.pc
