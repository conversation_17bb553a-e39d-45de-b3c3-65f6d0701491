The package halide provides CMake targets:

    set(CMAKE_CXX_STANDARD 17)
    set(CMAKE_CXX_STANDARD_REQUIRED YES)
    set(CMAKE_CXX_EXTENSIONS NO)

    find_package(Halide REQUIRED)

    # JIT mode:
    add_executable(my_halide_app main.cpp)
    target_link_libraries(my_halide_app PRIVATE Halide::Halide)

    # AOT mode:
    add_executable(my_generators my_generators.cpp)
    target_link_libraries(my_generators PRIVATE Halide::Generator)
    add_halide_library(my_first_generator FROM my_generators)
    add_halide_library(my_second_generator FROM my_generators
        PARAMS parallel=false scale=3.0 rotation=ccw output.type=uint16)
    add_halide_library(my_second_generator_2 FROM my_generators
        GENERATOR my_second_generator
        PARAMS scale=9.0 rotation=ccw output.type=float32)
    add_halide_library(my_second_generator_3 FROM my_generators
        GENERATOR my_second_generator
        PARAMS parallel=false output.type=float64)

For more information see:
    https://github.com/halide/Halide/blob/@HALIDE_VERSION_TAG@/README_cmake.md
