{"name": "halide", "version": "18.0.0", "port-version": 1, "description": "Halide is a programming language designed to make it easier to write high-performance image and array processing code on modern machines.", "homepage": "https://github.com/halide/Halide", "license": "MIT", "supports": "!uwp", "dependencies": [{"name": "halide", "default-features": false, "features": ["target-arm"], "platform": "arm32"}, {"name": "halide", "default-features": false, "features": ["target-aarch64"], "platform": "arm64"}, {"name": "halide", "default-features": false, "features": ["target-x86"], "platform": "x86 | x64"}, {"name": "halide", "features": ["target-all"], "platform": "!x86 & !x64 & !arm"}, {"name": "llvm", "default-features": false, "features": ["clang", "enable-rtti", "tools"]}, {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"target-aarch64": {"description": "Include AArch64 target", "dependencies": [{"name": "llvm", "default-features": false, "features": ["target-aarch64"]}]}, "target-all": {"description": "Include all targets", "dependencies": [{"name": "halide", "default-features": false, "features": ["target-aarch64", "target-amdgpu", "target-arm", "target-d3d12compute", "target-hexagon", "target-metal", "target-nvptx", "target-opencl", "target-opengl-compute", "target-powerpc", "target-riscv", "target-webassembly", "target-x86"]}]}, "target-amdgpu": {"description": "Include AMDGPU target", "dependencies": [{"name": "llvm", "default-features": false, "features": ["target-amdgpu"]}]}, "target-arm": {"description": "Include ARM target", "dependencies": [{"name": "llvm", "default-features": false, "features": ["target-arm"]}]}, "target-d3d12compute": {"description": "Include Direct3D 12 Compute target"}, "target-hexagon": {"description": "Include Hexagon target", "dependencies": [{"name": "llvm", "default-features": false, "features": ["target-hexagon"]}]}, "target-metal": {"description": "Include Metal target"}, "target-nvptx": {"description": "Include NVPTX target", "dependencies": [{"name": "llvm", "default-features": false, "features": ["target-nvptx"]}]}, "target-opencl": {"description": "Include OpenCL-C target"}, "target-opengl-compute": {"description": "Include OpenGL Compute target"}, "target-powerpc": {"description": "Include PowerPC target", "dependencies": [{"name": "llvm", "default-features": false, "features": ["target-powerpc"]}]}, "target-riscv": {"description": "Include RISCV target", "dependencies": [{"name": "llvm", "default-features": false, "features": ["target-riscv"]}]}, "target-webassembly": {"description": "Include WebAssembly target", "dependencies": [{"name": "llvm", "default-features": false, "features": ["target-webassembly"]}]}, "target-x86": {"description": "Include X86 target", "dependencies": [{"name": "llvm", "default-features": false, "features": ["target-x86"]}]}}}