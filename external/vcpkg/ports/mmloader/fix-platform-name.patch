diff --git a/CMakeLists.txt b/CMakeLists.txt
index b672037..f9a6a15 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -14,6 +14,13 @@ if ((NOT DEFINED CMAKE_VS_PLATFORM_NAME) OR (CMAKE_VS_PLATFORM_NAME STREQUAL "")
     message(STATUS "CMAKE_VS_PLATFORM_NAME is empty, use default: Win32")
     set(CMAKE_VS_PLATFORM_NAME Win32)
 endif()
+
+if(VCPKG_TARGET_ARCHITECTURE MATCHES "x86")
+    set(CMAKE_VS_PLATFORM_NAME "Win32")
+else()
+    set(CMAKE_VS_PLATFORM_NAME "${VCPKG_TARGET_ARCHITECTURE}")
+endif()
+
 message(STATUS "CMAKE_VS_PLATFORM_NAME=" ${CMAKE_VS_PLATFORM_NAME})
 string(COMPARE EQUAL "${CMAKE_VS_PLATFORM_NAME}" Win32 BUILD_ARC_X8632)
 string(COMPARE EQUAL "${CMAKE_VS_PLATFORM_NAME}" x64 BUILD_ARC_X8664)
