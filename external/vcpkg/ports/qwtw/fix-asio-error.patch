diff --git a/qwtw/sfigure.cpp b/qwtw/sfigure.cpp
index f3e1642..3a77148 100644
--- a/qwtw/sfigure.cpp
+++ b/qwtw/sfigure.cpp
@@ -57,9 +57,9 @@ struct BroadcastMessage {
 #pragma pack()
 class BCUdpClient {
 public:
-	BCUdpClient() : resolver(io_service), q(udp::v4(), "127.0.0.1", "49561"), socket(io_service) {
+	BCUdpClient() : resolver(io_context), socket(io_context) {
 
-		destination = boost::asio::ip::udp::endpoint(boost::asio::ip::address::from_string("127.0.0.1"), 49561);
+		destination = boost::asio::ip::udp::endpoint(boost::asio::ip::make_address("127.0.0.1"), 49561);
 
 		//receiver_endpoint = *resolver.resolve(q);
 		socket.open(udp::v4());
@@ -76,9 +76,8 @@ public:
 	}
 
 private: 
-	boost::asio::io_service io_service;
+	boost::asio::io_context io_context;
 	udp::resolver resolver;
-	udp::resolver::query q;
 	boost::asio::ip::udp::endpoint destination;
 	udp::endpoint receiver_endpoint;
 	udp::socket socket;
@@ -93,14 +92,14 @@ private:
 	std::mutex mu;
 	std::thread st;
 	volatile bool pleaseStop;
-	boost::asio::io_service io_service;
+	boost::asio::io_context io_context;
 	udp::socket socket_;
 	udp::endpoint remote_endpoint_;
 	unsigned char	rb[256];
 	std::function<void(double[3])> onPointF;
 
 public:
-	BCUdpServer() : socket_(io_service, udp::endpoint(udp::v4(), 49562)) {
+	BCUdpServer() : socket_(io_context, udp::endpoint(udp::v4(), 49562)) {
 		created = false;
 		createdMarker = false;
 		somethingWasChanged = false;
@@ -123,11 +122,11 @@ public:
 			return;
 		}
 		// wait for the task to finish??
-		boost::asio::io_service io_service1;
-		udp::socket s1(io_service1);
+		boost::asio::io_context io_context1;
+		udp::socket s1(io_context1);
 		s1.open(udp::v4());
 		unsigned char b[5];
-		boost::asio::ip::udp::endpoint destination = boost::asio::ip::udp::endpoint(boost::asio::ip::address::from_string("127.0.0.1"), 49562);
+		boost::asio::ip::udp::endpoint destination = boost::asio::ip::udp::endpoint(boost::asio::ip::make_address("127.0.0.1"), 49562);
 		try {
 			s1.send_to(boost::asio::buffer(b, 5), destination);
 			s1.send_to(boost::asio::buffer(b, 5), destination);
@@ -146,7 +145,7 @@ public:
 		try {
 			start_receive();
 			xm_printf("BCUdpServer tcpThread started\n");
-			io_service.run();
+			io_context.run();
 		} catch (std::exception& e) {
 			std::cerr << e.what() << std::endl;
 			xm_printf("TRACE: RDFramerDebugGuiUpdateCallback  exception: %s  \n", e.what());
