{"name": "qwtw", "version": "3.1.0", "port-version": 5, "description": "qwt-based 2D plotting library", "homepage": "https://github.com/ig-or/qwtw", "supports": "windows & x64 & !static", "dependencies": ["boost-asio", "boost-chrono", "boost-circular-buffer", "boost-date-time", "boost-filesystem", "boost-regex", "boost-system", "boost-thread", "marble", "qt5-base", "qt5-<PERSON>auth", "qt5-svg", "qwt", {"name": "vcpkg-cmake", "host": true}]}