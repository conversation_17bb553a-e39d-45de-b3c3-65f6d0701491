diff --git a/CMakeLists.txt b/CMakeLists.txt
index c7de889..2a58f19 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -743,6 +743,9 @@ ELSE()
     FIND_PACKAGE(Inventor)
     FIND_PACKAGE(Jasper)
     FIND_PACKAGE(OpenEXR)
+    set(OPENEXR_FOUND "${OpenEXR_FOUND}")
+    set(OPENEXR_LIBRARIES OpenEXR::OpenEXR)
+    set(OPENEXR_LIBRARIES_VARS OPENEXR_LIBRARIES)
     FIND_PACKAGE(OpenCascade)
     FIND_PACKAGE(COLLADA)
     FIND_PACKAGE(FBX)
diff --git a/src/osgPlugins/exr/ReaderWriterEXR.cpp b/src/osgPlugins/exr/ReaderWriterEXR.cpp
index 7132e97..bea1483 100644
--- a/src/osgPlugins/exr/ReaderWriterEXR.cpp
+++ b/src/osgPlugins/exr/ReaderWriterEXR.cpp
@@ -41,11 +41,11 @@ public:
       {
         return _inStream->read(c,n).good();
       };
-      virtual Int64    tellg ()
+      virtual uint64_t    tellg ()
       {
           return _inStream->tellg();
       };
-      virtual void    seekg (Int64 pos)
+      virtual void    seekg (uint64_t pos)
       {
         _inStream->seekg(pos);
       };
@@ -69,11 +69,11 @@ public:
       {
         _outStream->write(c,n);
       };
-      virtual Int64    tellp ()
+      virtual uint64_t    tellp ()
       {
         return _outStream->tellp();
       };
-      virtual void seekp (Int64 pos)
+      virtual void seekp (uint64_t pos)
       {
         _outStream->seekp(pos);
       };
