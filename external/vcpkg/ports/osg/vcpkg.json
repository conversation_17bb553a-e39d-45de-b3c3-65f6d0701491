{"name": "osg", "version": "3.6.5", "port-version": 26, "description": "The OpenSceneGraph is an open source high performance 3D graphics toolkit.", "homepage": "https://www.openscenegraph.com/", "license": null, "supports": "!uwp", "dependencies": [{"name": "expat", "platform": "windows"}, "opengl-registry", {"name": "openimageio", "default-features": false, "platform": "osx"}, {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}, "zlib"], "default-features": [{"name": "fontconfig", "platform": "!android & !ios & !uwp"}, "freetype", {"name": "nvtt", "platform": "!x86 & !(windows & arm64) & !uwp"}, {"name": "openexr", "platform": "!uwp"}, "plugins"], "features": {"collada": {"description": "Support for Collada (.dae) files", "dependencies": ["collada-dom"]}, "docs": {"description": "Build OpenSceneGraph reference documentation using doxygen (use: make doc_openscenegraph doc_openthreads"}, "examples": {"description": "Enable to build OSG Examples", "dependencies": [{"name": "libiconv", "platform": "windows"}, {"name": "osg", "default-features": false, "features": ["freetype", "plugins"]}, "sdl2"]}, "fontconfig": {"description": "Enable Fontconfig support for osgText", "dependencies": ["fontconfig", {"name": "osg", "default-features": false, "features": ["freetype"]}]}, "freetype": {"description": "Enable Freetype support", "dependencies": [{"name": "freetype", "default-features": false}]}, "nvtt": {"description": "Build texture processing tools plugin", "dependencies": ["nvtt"]}, "openexr": {"description": "Build the exr plugin", "dependencies": ["openexr"]}, "packages": {"description": "Set to ON to generate CPack configuration files and packaging targets"}, "plugins": {"description": "Build most OSG plugins", "dependencies": [{"name": "curl", "default-features": false}, {"name": "gdal", "default-features": false}, {"name": "gif<PERSON>b", "platform": "windows"}, {"name": "jasper", "default-features": false}, "libgta", {"name": "libiconv", "platform": "windows"}, "libjpeg-turbo", "libpng", {"name": "libxml2", "platform": "windows"}, {"name": "tiff", "default-features": false}]}, "sdl1": {"description": "Build SDL 1 plugin, and enable SDL 1 app features", "dependencies": ["sdl1"]}, "tools": {"description": "Enable to build OSG Applications (e.g. osgviewer)", "dependencies": [{"name": "curl", "default-features": false}, {"name": "libiconv", "platform": "windows"}, "libjpeg-turbo", "libpng", {"name": "osg", "default-features": false, "features": ["freetype", "plugins"]}]}}}