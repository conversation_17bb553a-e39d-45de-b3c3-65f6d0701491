From bee432e701c541f9a86bd051b795c3e056e36646 Mon Sep 17 00:00:00 2001
From: An<PERSON><PERSON> <<EMAIL>>
Date: Sun, 16 Aug 2020 14:36:31 -0700
Subject: [PATCH] osg

---
 src/osgPlugins/zip/unzip.cpp | 4 ++--
 1 file changed, 2 insertions(+), 2 deletions(-)

diff --git a/src/osgPlugins/zip/unzip.cpp b/src/osgPlugins/zip/unzip.cpp
index 0bec28f0c..74e7b2413 100644
--- a/src/osgPlugins/zip/unzip.cpp
+++ b/src/osgPlugins/zip/unzip.cpp
@@ -1742,8 +1742,8 @@ int inflate_blocks_free(inflate_blocks_statef *s, z_streamp z)
 
 
 
-extern const char inflate_copyright[] =
-   " inflate 1.1.3 Copyright 1995-1998 <PERSON> ";
+//extern const char inflate_copyright[] =
+//   " inflate 1.1.3 Copyright 1995-1998 <PERSON> ";
 // If you use the zlib library in a product, an acknowledgment is welcome
 // in the documentation of your product. If for some reason you cannot
 // include such an acknowledgment, I would appreciate that you keep this
-- 
2.26.2

From 24d4036036884b350bd6c519b64626faa2c7e660 Mon Sep 17 00:00:00 2001
From: Ankur Verma <<EMAIL>>
Date: Thu, 20 Aug 2020 23:16:12 -0700
Subject: [PATCH] Symbol clashes

---
 src/osgPlugins/zip/unzip.cpp | 2 +-
 1 file changed, 1 insertion(+), 1 deletion(-)

diff --git a/src/osgPlugins/zip/unzip.cpp b/src/osgPlugins/zip/unzip.cpp
index 74e7b2413..f5169c6f4 100644
--- a/src/osgPlugins/zip/unzip.cpp
+++ b/src/osgPlugins/zip/unzip.cpp
@@ -213,7 +213,7 @@ DWORD GetFilePosU(HANDLE hfout)
   return ftell(hfout);
 }
 
-bool FileExists(const TCHAR *fn)
+static bool FileExists(const TCHAR *fn)
 { struct stat st;
   int res=stat(fn,&st);
   return (res==0);
-- 
2.26.2

