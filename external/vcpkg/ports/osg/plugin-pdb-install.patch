diff --git a/CMakeModules/OsgMacroUtils.cmake b/CMakeModules/OsgMacroUtils.cmake
index a12fcf1..ce55be0 100644
--- a/CMakeModules/OsgMacroUtils.cmake
+++ b/CMakeModules/OsgMacroUtils.cmake
@@ -351,7 +351,7 @@ MACRO(SETUP_PLUGIN PLUGIN_NAME)
             LIBRARY DESTINATION bin/${OSG_PLUGINS} COMPONENT ${PACKAGE_COMPONENT})
         IF(MSVC AND DYNAMIC_OPENSCENEGRAPH)
             INSTALL(FILES ${OUTPUT_BINDIR}/${OSG_PLUGINS}/${TARGET_TARGETNAME}${CMAKE_RELWITHDEBINFO_POSTFIX}.pdb DESTINATION bin/${OSG_PLUGINS} COMPONENT ${PACKAGE_COMPONENT} CONFIGURATIONS RelWithDebInfo)
-            INSTALL(FILES ${OUTPUT_BINDIR}/${OSG_PLUGINS}/${TARGET_TARGETNAME}${CMAKE_DEBUG_POSTFIX}.pdb DESTINATION bin/${OSG_PLUGINS} COMPONENT ${PACKAGE_COMPONENT} CONFIGURATIONS Debug)
+            INSTALL(FILES $<TARGET_PDB_FILE:${TARGET_TARGETNAME}> DESTINATION bin/${OSG_PLUGINS} COMPONENT ${PACKAGE_COMPONENT} CONFIGURATIONS Debug)
         ENDIF(MSVC AND DYNAMIC_OPENSCENEGRAPH)
     ELSE(WIN32)
         INSTALL(TARGETS ${TARGET_TARGETNAME}
