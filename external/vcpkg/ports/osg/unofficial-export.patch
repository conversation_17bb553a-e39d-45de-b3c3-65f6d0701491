diff --git a/CMakeLists.txt b/CMakeLists.txt
index c975d78..cc53332 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -1374,3 +1374,6 @@ ADD_CUSTOM_TARGET(uninstall
 #
 include(FeatureSummary)
 feature_summary(WHAT ALL)
+
+INSTALL(EXPORT osg-targets NAMESPACE unofficial::osg:: DESTINATION share/unofficial-osg)
+INSTALL(EXPORT osg-plugins NAMESPACE unofficial::osg:: DESTINATION share/unofficial-osg)
diff --git a/CMakeModules/ModuleInstall.cmake b/CMakeModules/ModuleInstall.cmake
index f6714c5..d58f6fd 100644
--- a/CMakeModules/ModuleInstall.cmake
+++ b/CMakeModules/ModuleInstall.cmake
@@ -27,6 +27,7 @@ ENDIF()
 
 INSTALL(
     TARGETS ${LIB_NAME}
+    EXPORT osg-targets
     RUNTIME DESTINATION ${INSTALL_BINDIR} COMPONENT libopenscenegraph
     LIBRARY DESTINATION ${INSTALL_LIBDIR} COMPONENT libopenscenegraph
     ARCHIVE DESTINATION ${INSTALL_ARCHIVEDIR} COMPONENT libopenscenegraph-dev    
diff --git a/CMakeModules/OsgMacroUtils.cmake b/CMakeModules/OsgMacroUtils.cmake
index dcc1fe37d..76429b85a 100644
--- a/CMakeModules/OsgMacroUtils.cmake
+++ b/CMakeModules/OsgMacroUtils.cmake
@@ -345,18 +350,20 @@ MACRO(SETUP_PLUGIN PLUGIN_NAME)
 #the installation path are differentiated for win32 that install in bib versus other architecture that install in lib${LIB_POSTFIX}/${OSG_PLUGINS}
     IF(WIN32)
         INSTALL(TARGETS ${TARGET_TARGETNAME}
+            EXPORT osg-plugins
             RUNTIME DESTINATION bin COMPONENT ${PACKAGE_COMPONENT}
             ARCHIVE DESTINATION lib/${OSG_PLUGINS} COMPONENT libopenscenegraph-dev
-            LIBRARY DESTINATION bin/${OSG_PLUGINS} COMPONENT ${PACKAGE_COMPONENT})
+            LIBRARY DESTINATION plugins/${OSG_PLUGINS} COMPONENT ${PACKAGE_COMPONENT})
         IF(MSVC AND DYNAMIC_OPENSCENEGRAPH)
-            INSTALL(FILES ${OUTPUT_BINDIR}/${OSG_PLUGINS}/${TARGET_TARGETNAME}${CMAKE_RELWITHDEBINFO_POSTFIX}.pdb DESTINATION bin/${OSG_PLUGINS} COMPONENT ${PACKAGE_COMPONENT} CONFIGURATIONS RelWithDebInfo)
-            INSTALL(FILES $<TARGET_PDB_FILE:${TARGET_TARGETNAME}> DESTINATION bin/${OSG_PLUGINS} COMPONENT ${PACKAGE_COMPONENT} CONFIGURATIONS Debug)
+            INSTALL(FILES ${OUTPUT_BINDIR}/${OSG_PLUGINS}/${TARGET_TARGETNAME}${CMAKE_RELWITHDEBINFO_POSTFIX}.pdb DESTINATION plugins/${OSG_PLUGINS} COMPONENT ${PACKAGE_COMPONENT} CONFIGURATIONS RelWithDebInfo)
+            INSTALL(FILES $<TARGET_PDB_FILE:${TARGET_TARGETNAME}> DESTINATION plugins/${OSG_PLUGINS} COMPONENT ${PACKAGE_COMPONENT} CONFIGURATIONS Debug)
         ENDIF(MSVC AND DYNAMIC_OPENSCENEGRAPH)
     ELSE(WIN32)
         INSTALL(TARGETS ${TARGET_TARGETNAME}
+            EXPORT osg-plugins
             RUNTIME DESTINATION bin COMPONENT ${PACKAGE_COMPONENT}
             ARCHIVE DESTINATION lib${LIB_POSTFIX}/${OSG_PLUGINS} COMPONENT libopenscenegraph-dev
-            LIBRARY DESTINATION lib${LIB_POSTFIX}/${OSG_PLUGINS} COMPONENT ${PACKAGE_COMPONENT})
+            LIBRARY DESTINATION plugins${LIB_POSTFIX}/${OSG_PLUGINS} COMPONENT ${PACKAGE_COMPONENT})
     ENDIF(WIN32)
 ENDMACRO(SETUP_PLUGIN)
 
diff --git a/src/OpenThreads/pthreads/CMakeLists.txt b/src/OpenThreads/pthreads/CMakeLists.txt
index e18969c..5a8ad62 100644
--- a/src/OpenThreads/pthreads/CMakeLists.txt
+++ b/src/OpenThreads/pthreads/CMakeLists.txt
@@ -157,6 +157,7 @@ ELSE()
 
     INSTALL(
         TARGETS OpenThreads
+        EXPORT osg-targets
         ARCHIVE DESTINATION lib${LIB_POSTFIX} COMPONENT libopenthreads-dev
         LIBRARY DESTINATION lib${LIB_POSTFIX} COMPONENT libopenthreads
         RUNTIME DESTINATION bin COMPONENT libopenthreads
diff --git a/src/OpenThreads/win32/CMakeLists.txt b/src/OpenThreads/win32/CMakeLists.txt
index 0e816f7..0c083fd 100644
--- a/src/OpenThreads/win32/CMakeLists.txt
+++ b/src/OpenThreads/win32/CMakeLists.txt
@@ -45,6 +45,7 @@ ENDIF()
 
 INSTALL(
     TARGETS OpenThreads
+    EXPORT osg-targets
     ARCHIVE DESTINATION lib COMPONENT libopenthreads-dev
     LIBRARY DESTINATION lib COMPONENT libopenthreads
     RUNTIME DESTINATION bin COMPONENT libopenthreads
