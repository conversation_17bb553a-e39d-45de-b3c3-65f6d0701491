diff --git a/CMakeModules/OsgMacroUtils.cmake b/CMakeModules/OsgMacroUtils.cmake
index ce55be0..9b7107c 100644
--- a/CMakeModules/OsgMacroUtils.cmake
+++ b/CMakeModules/OsgMacroUtils.cmake
@@ -37,6 +37,16 @@ SET(VALID_BUILDER_VERSION OFF)
 
 MACRO(LINK_WITH_VARIABLES TRGTNAME)
     FOREACH(varname ${ARGN})
+        string(REPLACE "_LIBRARY" "_LINK_LIBRARIES" lwv_link_libraries "${varname}")
+        if(DEFINED ${lwv_link_libraries})
+            TARGET_LINK_LIBRARIES(${TRGTNAME} ${${lwv_link_libraries}})
+            continue()
+        endif()
+        string(REPLACE "_LIBRARY" "_LIBRARIES" lwv_libraries "${varname}")
+        if(DEFINED ${lwv_libraries})
+            TARGET_LINK_LIBRARIES(${TRGTNAME} ${${lwv_libraries}})
+            continue()
+        endif()
         IF(${varname}_DEBUG)
             IF(${varname}_RELEASE)
                 TARGET_LINK_LIBRARIES(${TRGTNAME} optimized "${${varname}_RELEASE}" debug "${${varname}_DEBUG}")
