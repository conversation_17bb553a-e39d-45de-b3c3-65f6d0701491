cmake_policy(PUSH)
cmake_policy(SET CMP0012 NEW)
cmake_policy(SET CMP0054 NEW)
cmake_policy(SET CMP0057 NEW)
include("${CMAKE_CURRENT_LIST_DIR}/osg-targets.cmake")
include(CMakeFindDependencyMacro)
if("@OSG_TEXT_USE_FONTCONFIG@")
    find_dependency(Fontconfig) # CMake 3.14
endif()
if("plugins" IN_LIST ${CMAKE_FIND_PACKAGE_NAME}_FIND_COMPONENTS)
    include("${CMAKE_CURRENT_LIST_DIR}/osg-plugins.cmake")
    if(TARGET unofficial::osg::osgdb_curl)
        find_dependency(CURL)
    endif()
    if(TARGET unofficial::osg::osgdb_exr)
        find_dependency(OpenEXR CONFIG)
    endif()
    if(TARGET unofficial::osg::osgdb_gdal OR TARGET unofficial::osg::osgdb_ogr)
        find_dependency(GDAL)
    endif()
endif()
set(${CMAKE_FIND_PACKAGE_NAME}_VERSION "@OSG_VER@")
cmake_policy(POP)
