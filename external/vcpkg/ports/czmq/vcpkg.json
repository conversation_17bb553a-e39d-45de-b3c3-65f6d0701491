{"name": "czmq", "version-semver": "4.2.1", "port-version": 4, "description": "High-level C binding for ZeroMQ", "homepage": "https://github.com/zeromq/czmq", "license": "MPL-2.0", "supports": "!xbox", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}, "zeromq"], "features": {"curl": {"description": "Build with libcurl", "dependencies": ["curl"]}, "draft": {"description": "Build and install draft APIs"}, "httpd": {"description": "Build with HTTP server support (libmicrohttpd)", "dependencies": ["libmicrohttpd"]}, "lz4": {"description": "Build with lz4", "dependencies": ["lz4"]}, "tool": {"description": "Build and install czmq tools (zmakecert)"}, "uuid": {"description": "Build with libuuid", "dependencies": [{"name": "libuuid", "platform": "!windows & !uwp & !osx"}]}}}