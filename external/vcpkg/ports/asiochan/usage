asiochan is header-only and can be used from CMake via:

    find_path(ASIOCHAN_INCLUDE_DIRS "asiochan/asiochan.hpp")
    target_include_directories(main PRIVATE ${ASIOCHAN_INCLUDE_DIRS})

By default asiochan depend on boost/asio. If you need use standalone asio instead, please define micro ASIOCHAN_USE_STANDALONE_ASIO. 
For example:

    find_path(ASIOCHAN_INCLUDE_DIRS "asiochan/asiochan.hpp")
    target_compile_definitions(main PRIVATE ASIOCHAN_USE_STANDALONE_ASIO)
    target_include_directories(main PRIVATE ${ASIOCHAN_INCLUDE_DIRS})

