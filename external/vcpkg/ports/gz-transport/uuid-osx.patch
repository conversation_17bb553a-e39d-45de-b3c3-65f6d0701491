diff --git a/CMakeLists.txt b/CMakeLists.txt
index 8c69aa0e..428bd0ac 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -111,6 +111,8 @@ gz_find_package(CPPZMQ REQUIRED PRIVATE
 if (MSVC)
   set(skip_pybind11_default_value ON)
   message (STATUS "UUID: Using Windows RPC UuidCreate function\n")
+elseif (APPLE)
+  message (STATUS "Use system uuid header")
 else()
   gz_find_package(UUID REQUIRED)
 endif()
diff --git a/src/CMakeLists.txt b/src/CMakeLists.txt
index 13101c50..340e0e82 100644
--- a/src/CMakeLists.txt
+++ b/src/CMakeLists.txt
@@ -21,7 +21,7 @@ target_include_directories(${PROJECT_LIBRARY_TARGET_NAME}
     $<TARGET_PROPERTY:CPPZMQ::CPPZMQ,INTERFACE_INCLUDE_DIRECTORIES>)
 
 # Windows system library provides UUID
-if (NOT MSVC)
+if (NOT MSVC AND NOT APPLE)
   target_link_libraries(${PROJECT_LIBRARY_TARGET_NAME}
     PUBLIC
       UUID::UUID
