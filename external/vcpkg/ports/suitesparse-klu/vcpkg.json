{"$comment": "Use scripts/update_suitesparse.py to update all SuiteSparse ports", "name": "suitesparse-klu", "version-semver": "2.3.5", "description": "KLU: <PERSON><PERSON>ines for solving sparse linear systems of equations in SuiteSparse", "homepage": "https://people.engr.tamu.edu/davis/suitesparse.html", "license": "LGPL-2.1-or-later", "dependencies": ["suitesparse-amd", "suitesparse-btf", "suitesparse-colamd", "suitesparse-config", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"cholmod": {"description": "Build example user-ordering function using CHOLMOD", "dependencies": ["suitesparse-cholmod"]}}}