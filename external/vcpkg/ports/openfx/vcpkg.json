{"$schema": "https://raw.githubusercontent.com/microsoft/vcpkg-tool/main/docs/vcpkg.schema.json", "name": "openfx", "version": "1.4", "maintainers": "<PERSON><PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON> <<PERSON><PERSON><EMAIL>>", "summary": "OpenFX - An open-source plugin API for visual effects", "homepage": "https://github.com/AcademySoftwareFoundation/openfx", "license": "BSD-3-<PERSON><PERSON>", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}]}