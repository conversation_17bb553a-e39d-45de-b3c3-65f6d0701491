{"name": "yalantinglibs", "version": "0.4.0", "description": "A Collection of C++20 libraries, include struct_pack, struct_json, struct_xml, struct_yaml, struct_pb, easylog, coro_rpc, coro_http and async_simple", "homepage": "https://github.com/alibaba/yalantinglibs", "license": "Apache-2.0", "supports": "!android", "dependencies": [{"name": "asio", "version>=": "1.24.0"}, {"name": "async-simple", "version>=": "1.3"}, {"name": "frozen", "version>=": "1.2.0"}, {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}]}