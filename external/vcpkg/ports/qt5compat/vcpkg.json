{"name": "qt5compat", "version": "6.8.3", "description": "The Qt 5 Core Compat module contains the Qt 5 Core APIs that were removed in Qt 6. The module facilitates the transition to Qt 6.", "homepage": "https://www.qt.io/", "license": null, "dependencies": [{"name": "qtbase", "default-features": false}], "default-features": ["big-codecs", "codecs", "qml", "textcodec"], "features": {"big-codecs": {"description": "Supports big codecs, e.g. CJK.", "dependencies": [{"name": "qt5compat", "default-features": false, "features": ["textcodec"]}]}, "codecs": {"description": "Supports non-unicode text conversions.", "dependencies": [{"name": "qt5compat", "default-features": false, "features": ["textcodec"]}]}, "iconv": {"description": "Provides internationalization on Unix.", "dependencies": ["libiconv", {"name": "qt5compat", "default-features": false, "features": ["textcodec"]}]}, "qml": {"description": "Build QML imports", "dependencies": [{"name": "qtbase", "default-features": false, "features": ["gui"]}, {"name": "qtdeclarative", "default-features": false}]}, "textcodec": {"description": "Supports conversions between text encodings."}}}