diff --git a/src/CMakeLists.txt b/src/CMakeLists.txt
index e618325..21d977f 100644
--- a/src/CMakeLists.txt
+++ b/src/CMakeLists.txt
@@ -107,7 +107,7 @@ if(NOT VULKAN_INCOMPATIBLE)
 endif()
 
 find_package(Threads REQUIRED)
-find_package(JsonCpp)
+find_package(JSONCPP NAMES jsoncpp REQUIRED)
 
 ### All options defined here
 option(BUILD_LOADER "Build loader" ON)
diff --git a/src/loader/OpenXRConfig.cmake.in b/src/loader/OpenXRConfig.cmake.in
index 81b12e7..4c24771 100644
--- a/src/loader/OpenXRConfig.cmake.in
+++ b/src/loader/OpenXRConfig.cmake.in
@@ -6,6 +6,7 @@
 
 include(CMakeFindDependencyMacro)
 find_dependency(Threads)
+find_dependency(jsoncpp CONFIG)
 
 include("${CMAKE_CURRENT_LIST_DIR}/OpenXRTargets.cmake")
 
