{"name": "openxr-loader", "version": "1.1.45", "description": "A royalty-free, open standard that provides high-performance access to Augmented Reality (AR) and Virtual Reality (VR)—collectively known as XR—platforms and devices", "homepage": "https://github.com/KhronosGroup/OpenXR-SDK", "license": "Apache-2.0", "supports": "!uwp", "dependencies": [{"name": "egl", "platform": "android"}, "jsoncpp", {"name": "opengl", "platform": "!android & !ios & !osx & !(arm & windows) & !uwp"}, {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"vulkan": {"description": "Vulkan functionality for OpenXR", "supports": "!(arm & windows) & !uwp", "dependencies": ["vulkan"]}}}