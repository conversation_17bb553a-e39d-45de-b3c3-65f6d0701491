diff --git a/CMakeLists.txt b/CMakeLists.txt
index 8a4bd5b..7dc50f1 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -9,6 +9,7 @@ endif(COMMAND cmake_policy)
 
 OPTION( HAVE_GETOPT_H "Have the getopt library" 0 )
 IF( HAVE_GETOPT_H )
+	find_package(unofficial-getopt-win32 REQUIRED)
 	ADD_DEFINITIONS( -DHAVE_GETOPT_H )
 ENDIF( HAVE_GETOPT_H )
 
diff --git a/src/CMakeLists.txt b/src/CMakeLists.txt
index 03e112c..8c58082 100644
--- a/src/CMakeLists.txt
+++ b/src/CMakeLists.txt
@@ -1,7 +1,12 @@
 include(GNUInstallDirs)
 
 IF( WIN32 )
+	if(HAVE_GETOPT_H)
+	ADD_LIBRARY( argtable2 arg_dbl.c arg_end.c arg_file.c arg_int.c arg_lit.c arg_rem.c arg_str.c argtable2.c argtable2.h)
+	target_link_libraries(argtable2 PRIVATE unofficial::getopt-win32::getopt)
+	else()
 	ADD_LIBRARY( argtable2 arg_dbl.c arg_end.c arg_file.c arg_int.c arg_lit.c arg_rem.c arg_str.c argtable2.c argtable2.h getopt.c getopt.h getopt1.c )
+	endif()
 ELSE( WIN32 )
 	ADD_LIBRARY( argtable2 arg_date.c arg_dbl.c arg_end.c arg_file.c arg_int.c arg_lit.c arg_rem.c arg_rex.c arg_str.c argtable2.c argtable2.h getopt.c getopt.h getopt1.c )
 ENDIF( WIN32 )
diff --git a/src/argtable2.c b/src/argtable2.c
index c4d210c..1bc0a3e 100644
--- a/src/argtable2.c
+++ b/src/argtable2.c
@@ -46,7 +46,7 @@ USA.
 #endif
 
 #include "argtable2.h"
-#include "./getopt.h"
+//#include "./getopt.h"
 
 static
 void arg_register_error(struct arg_end *end, void *parent, int error, const char *argval)
