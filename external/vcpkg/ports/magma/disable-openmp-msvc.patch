diff --git a/CMakeLists.txt b/CMakeLists.txt
index 828911d05f..a4f5b35ef3 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -103,6 +103,10 @@ endif()
 
 # ----------------------------------------
 # locate OpenMP
+if(MSVC AND CMAKE_CXX_COMPILER_ID STREQUAL "MSVC")
+  set(CMAKE_DISBLE_FIND_PACKAGE_OpenMP ON)
+  # MSVC OpenMP is not enough to compile
+endif()
 find_package( OpenMP )
 if (OPENMP_FOUND)
     message( STATUS "Found OpenMP" )
