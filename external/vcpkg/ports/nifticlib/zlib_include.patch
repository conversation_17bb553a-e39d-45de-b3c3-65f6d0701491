diff --git a/CMakeLists.txt b/CMakeLists.txt
index 54150d739..c3ca8fe50 100644
--- a/CMakeLists.txt	
+++ b/CMakeLists.txt
@@ -77,6 +77,7 @@ set_if_not_defined(ZNZ_COMPILE_DEF "")
 if(NOT NIFTI_ZLIB_LIBRARIES) # If using a custom zlib library, skip the find package
   ###  USE AS STAND ALONE PACKAGE
   find_package(ZLIB REQUIRED)
+  include_directories(${ZLIB_INCLUDE_DIRS})
   set(NIFTI_ZLIB_LIBRARIES ${ZLIB_LIBRARIES})
 endif()
 #message(STATUS "---------------------ZLIB -${NIFTI_ZLIB_LIBRARIES}--")
