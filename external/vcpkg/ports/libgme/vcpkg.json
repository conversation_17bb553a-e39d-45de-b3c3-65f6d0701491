{"name": "libgme", "version": "0.6.3", "description": "Video game music file emulation/playback library", "homepage": "https://bitbucket.org/mpyne/game-music-emu/wiki/Home", "license": "LGPL-2.1-or-later OR GPL-2.0-or-later", "dependencies": [{"name": "vcpkg-cmake", "host": true}], "default-features": ["ay", "gbs", "gym", "hes", "kss", "nsf", "nsfe", "sap", "spc", "vgm"], "features": {"ay": {"description": "Enable Spectrum ZX music emulation"}, "gbs": {"description": "Enable Game Boy music emulation"}, "gym": {"description": "Enable Sega MegaDrive/Genesis music emulation"}, "hes": {"description": "Enable PC Engine/TurboGrafx-16 music emulation"}, "kss": {"description": "Enable MSX or other Z80 systems music emulation"}, "nsf": {"description": "Enable NES NSF music emulation"}, "nsfe": {"description": "Enable NES NSFE and NSF music emulation"}, "sap": {"description": "Enable Atari SAP music emulation"}, "spc": {"description": "Enable SNES SPC music emulation"}, "spc-isolated-echo-buffer": {"description": "Enable isolated echo buffer on SPC emulator to allow correct playing of \"dodgy\" SPC files made for various ROM hacks ran on ZSNES"}, "vgm": {"description": "Enable Sega VGM/VGZ music emulation", "dependencies": ["zlib"]}}}