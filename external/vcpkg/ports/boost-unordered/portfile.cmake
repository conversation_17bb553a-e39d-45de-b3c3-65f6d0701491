# Automatically generated by scripts/boost/generate-ports.ps1

vcpkg_download_distfile(ARM32_PATCH
    URLS https://github.com/boostorg/unordered/commit/07f6463c1c302c5b1d28aa253e0b768e71c90235.patch?full_index=1
    FILENAME boost-unordered-arm32-07f6463c1c302c5b1d28aa253e0b768e71c90235.patch
    SHA512 99f631e57b0c7d8d08f32d994c34dbca2588b409c10ad35500c36dc5374dba91888ee5ba3ca7ed24fa75539e9d44ffc123892d859a736f63ca0ae5fffee2c178
)
vcpkg_from_github(
    OUT_SOURCE_PATH SOURCE_PATH
    REPO boostorg/unordered
    REF boost-${VERSION}
    SHA512 c28cc7c7bbb9fa04634f9fa4d371e31df084a64177e9d6b0395e82084f513aac3ec01cdb3b0fa7019902c963dce6cfac65da5e66d444932c005b0430e69471cf
    HEAD_REF master
    PATCHES
        ${ARM32_PATCH}
)

set(FEATURE_OPTIONS "")
boost_configure_and_install(
    SOURCE_PATH "${SOURCE_PATH}"
    OPTIONS ${FEATURE_OPTIONS}
)
