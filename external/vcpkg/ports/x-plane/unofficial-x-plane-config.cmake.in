@PACKAGE_INIT@

get_filename_component(ROOT "${CMAKE_CURRENT_LIST_DIR}" PATH)
get_filename_component(ROOT "${ROOT}" PATH)

if(NOT TARGET unofficial::x-plane::xplm)
  if(WIN32 OR APPLE)
    add_library(unofficial::x-plane::xplm SHARED IMPORTED)
    if(WIN32)
      set_target_properties(
        unofficial::x-plane::xplm
        PROPERTIES IMPORTED_IMPLIB "${ROOT}/lib/x-plane/XPLM_64.lib")
      set_target_properties(
        unofficial::x-plane::xplm PROPERTIES INTERFACE_COMPILE_DEFINITIONS
                                                "-DIBM=1;-DAPL=0;-DLIN=0")
    else()
      set_target_properties(
        unofficial::x-plane::xplm
        PROPERTIES IMPORTED_LOCATION "${ROOT}/lib/x-plane/XPLM.framework/XPLM")
      set_target_properties(
        unofficial::x-plane::xplm PROPERTIES INTERFACE_COMPILE_DEFINITIONS
                                                "-DIBM=0;-DAPL=1;-DLIN=0")
    endif()
  else()
    add_library(unofficial::x-plane::xplm INTERFACE IMPORTED)
    set_target_properties(
      unofficial::x-plane::xplm PROPERTIES INTERFACE_COMPILE_DEFINITIONS
                                              "-DIBM=0;-DAPL=0;-DLIN=1")
  endif()
  set_target_properties(
    unofficial::x-plane::xplm PROPERTIES INTERFACE_INCLUDE_DIRECTORIES
                                            "${ROOT}/include")
endif()

if(NOT TARGET unofficial::x-plane::xpwidgets)
  if(WIN32 OR APPLE)
    add_library(unofficial::x-plane::xpwidgets SHARED IMPORTED)
    if(WIN32)
      set_target_properties(
        unofficial::x-plane::xpwidgets
        PROPERTIES IMPORTED_IMPLIB "${ROOT}/lib/x-plane/XPWidgets_64.lib")
    else()
      set_target_properties(
        unofficial::x-plane::xpwidgets
        PROPERTIES IMPORTED_LOCATION
                   "${ROOT}/lib/x-plane/XPWidgets.framework/XPWidgets")
    endif()
  else()
    add_library(unofficial::x-plane::xpwidgets INTERFACE IMPORTED)
  endif()
  set_target_properties(
    unofficial::x-plane::xpwidgets PROPERTIES INTERFACE_LINK_LIBRARIES
                                                 "unofficial::x-plane::xplm")
  set_target_properties(
    unofficial::x-plane::xpwidgets
    PROPERTIES INTERFACE_INCLUDE_DIRECTORIES
               "${ROOT}/include")
endif()

include("${CMAKE_CURRENT_LIST_DIR}/unofficial-x-plane-targets.cmake")

check_required_components(unofficial-x-plane)
