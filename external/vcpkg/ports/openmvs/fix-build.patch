diff --git a/build/Templates/OpenMVSConfig.cmake.in b/build/Templates/OpenMVSConfig.cmake.in
index 9747b3e..ed2347e 100644
--- a/build/Templates/OpenMVSConfig.cmake.in
+++ b/build/Templates/OpenMVSConfig.cmake.in
@@ -15,9 +15,64 @@ set(OpenMVS_INCLUDE_DIRS "@INSTALL_INCLUDE_DIR_IN@")
 
 set(OpenMVS_DEFINITIONS "@OpenMVS_DEFINITIONS@")
 
+list(APPEND CMAKE_MODULE_PATH "${OpenMVS_CMAKE_DIR}")
+
+if (MSVC)
+    set(CMAKE_C_FLAGS_RELEASE "${CMAKE_C_FLAGS_RELEASE} /GL")
+    set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} /GL")
+    set(CMAKE_EXE_LINKER_FLAGS_RELEASE "${CMAKE_EXE_LINKER_FLAGS_RELEASE} /LTCG")
+    set(CMAKE_MODULE_LINKER_FLAGS_RELEASE "${CMAKE_MODULE_LINKER_FLAGS_RELEASE} /LTCG")
+endif()
+
+set(CMAKE_CXX_STANDARD 14)
+set(CMAKE_CXX_STANDARD_REQUIRED ON)
+
+include(CMakeFindDependencyMacro)
+
+if(@OpenMVS_USE_OPENMP@)
+    find_dependency(OpenMP)
+    add_definitions(-D_USE_OPENMP)
+endif()
+
+if(@OpenMVS_USE_OPENGL@)
+    find_dependency(OpenGL)
+    add_definitions(${OpenGL_DEFINITIONS} -D_USE_OPENGL)
+endif()
+
+if(@OpenMVS_USE_CUDA@)
+    find_dependency(CUDA)
+    add_definitions(-D_USE_CUDA)
+    include_directories(${CUDA_INCLUDE_DIRS})
+endif()
+
+if(@OpenMVS_USE_BREAKPAD@)
+    find_dependency(BREAKPAD)
+    add_definitions(${BREAKPAD_DEFINITIONS} -D_USE_BREAKPAD)
+endif()
+
+find_dependency(Boost COMPONENTS iostreams program_options system serialization)
+add_definitions(${Boost_DEFINITIONS} -D_USE_BOOST)
+include_directories(${Boost_INCLUDE_DIRS})
+find_dependency(Eigen3)
+add_definitions(${EIGEN3_DEFINITIONS} -D_USE_EIGEN)
+include_directories(${EIGEN3_INCLUDE_DIRS})
+find_dependency(OpenCV)
+add_definitions(${OpenCV_DEFINITIONS})
+find_dependency(CGAL)
+add_definitions(${CGAL_DEFINITIONS})
+
+if(@OpenMVS_USE_CERES@)
+    find_dependency(Ceres)
+    add_definitions(${CERES_DEFINITIONS})
+endif()
+
+add_definitions(@OpenMVS_DEFINITIONS@)
+
+# Our library dependencies (contains definitions for IMPORTED targets)
+if(NOT TARGET MVS AND NOT OpenMVS_BINARY_DIR)
+    include("${OpenMVS_CMAKE_DIR}/OpenMVSTargets.cmake")
+endif()
+
 # These are IMPORTED targets created by OpenMVSTargets.cmake
 set(OpenMVS_LIBRARIES MVS)
-set(OpenMVS_BINARIES InterfaceCOLMAP DensifyPointCloud ReconstructMesh RefineMesh TextureMesh)
-
-include("${CMAKE_CURRENT_LIST_DIR}/OpenMVSTargets.cmake")
-check_required_components("OpenMVS")
+set(OpenMVS_BINARIES InterfaceCOLMAP InterfaceMetashape InterfaceMVSNet DensifyPointCloud ReconstructMesh RefineMesh TextureMesh TransformScene Viewer)
diff --git a/build/Utils.cmake b/build/Utils.cmake
index 0cb2d8e1..62e1661f 100644
--- a/build/Utils.cmake
+++ b/build/Utils.cmake
@@ -173,32 +173,6 @@ macro(ComposePackageLibSuffix)
 	set(PACKAGE_LIB_SUFFIX "")
 	set(PACKAGE_LIB_SUFFIX_DBG "")
 	set(PACKAGE_LIB_SUFFIX_REL "")
-	if(MSVC)
-		if("${MSVC_VERSION}" STRGREATER "1929")
-			set(PACKAGE_LIB_SUFFIX "/vc17")
-		elseif("${MSVC_VERSION}" STRGREATER "1916")
-			set(PACKAGE_LIB_SUFFIX "/vc16")
-		elseif("${MSVC_VERSION}" STRGREATER "1900")
-			set(PACKAGE_LIB_SUFFIX "/vc15")
-		elseif("${MSVC_VERSION}" STREQUAL "1900")
-			set(PACKAGE_LIB_SUFFIX "/vc14")
-		elseif("${MSVC_VERSION}" STREQUAL "1800")
-			set(PACKAGE_LIB_SUFFIX "/vc12")
-		elseif("${MSVC_VERSION}" STREQUAL "1700")
-			set(PACKAGE_LIB_SUFFIX "/vc11")
-		elseif("${MSVC_VERSION}" STREQUAL "1600")
-			set(PACKAGE_LIB_SUFFIX "/vc10")
-		elseif("${MSVC_VERSION}" STREQUAL "1500")
-			set(PACKAGE_LIB_SUFFIX "/vc9")
-		endif()
-		if("${SYSTEM_BITNESS}" STREQUAL "64")
-			set(PACKAGE_LIB_SUFFIX "${PACKAGE_LIB_SUFFIX}/x64")
-		else()
-			set(PACKAGE_LIB_SUFFIX "${PACKAGE_LIB_SUFFIX}/x86")
-		endif()
-		set(PACKAGE_LIB_SUFFIX_DBG "${PACKAGE_LIB_SUFFIX}/Debug")
-		set(PACKAGE_LIB_SUFFIX_REL "${PACKAGE_LIB_SUFFIX}/Release")
-	endif()
 endmacro()
 
 
@@ -805,12 +779,8 @@ macro(ConfigLibrary)
 	# Offer the user the choice of overriding the installation directories
 	set(INSTALL_LIB_DIR "lib" CACHE PATH "Installation directory for libraries")
 	set(INSTALL_BIN_DIR "bin" CACHE PATH "Installation directory for executables")
-	set(INSTALL_INCLUDE_DIR "include" CACHE PATH "Installation directory for header files")
-	if(WIN32 AND NOT CYGWIN)
-		set(DEF_INSTALL_CMAKE_DIR "CMake")
-	else()
-		set(DEF_INSTALL_CMAKE_DIR "lib/cmake")
-	endif()
+	set(INSTALL_INCLUDE_DIR "include/openmvs" CACHE PATH "Installation directory for header files")
+	set(DEF_INSTALL_CMAKE_DIR "share/openmvs")
 	set(INSTALL_CMAKE_DIR ${DEF_INSTALL_CMAKE_DIR} CACHE PATH "Installation directory for CMake files")
 	# Make relative paths absolute (needed later on)
 	foreach(p LIB BIN INCLUDE CMAKE)
@@ -821,7 +791,7 @@ macro(ConfigLibrary)
 		else()
 			set(${varp} "${CMAKE_INSTALL_PREFIX}/${${var}}")
 		endif()
-		set(${var} "${${varp}}/${PROJECT_NAME}")
+		set(${var} "${${varp}}")
 	endforeach()
 endmacro()
 

diff --git a/libs/Common/Strings.h b/libs/Common/MVSStrings.h
similarity index 100%
rename from libs/Common/Strings.h
rename to libs/Common/MVSStrings.h
diff --git a/libs/Common/Types.h b/libs/Common/Types.h
index d5dcb27e..04fbfe2f 100644
--- a/libs/Common/Types.h
+++ b/libs/Common/Types.h
@@ -385,7 +385,7 @@ typedef TAliasCast<double,int32_t> CastD2I;
 
 // I N C L U D E S /////////////////////////////////////////////////
 
-#include "Strings.h"
+#include "MVSStrings.h"
 #include "AutoPtr.h"
 #include "List.h"
 #include "Thread.h"

diff --git a/apps/InterfaceMetashape/InterfaceMetashape.cpp b/apps/InterfaceMetashape/InterfaceMetashape.cpp
index 4886973a..c2ab8f98 100644
--- a/apps/InterfaceMetashape/InterfaceMetashape.cpp
+++ b/apps/InterfaceMetashape/InterfaceMetashape.cpp
@@ -627,7 +627,7 @@ bool ParseSceneXML(Scene& scene, PlatformDistCoeffs& pltDistCoeffs, size_t& nCam
 		}
 		const size_t nLen(pStream->getSize());
 		String str; str.resize(nLen);
-		pStream->read(&str[0], nLen);
+		pStream->read(str.data(), nLen);
 		doc.Parse(str.c_str(), nLen);
 	}
 	if (doc.ErrorID() != tinyxml2::XML_SUCCESS) {
