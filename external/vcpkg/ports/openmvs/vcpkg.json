{"name": "openmvs", "version": "2.1.0", "port-version": 6, "description": "OpenMVS: open Multi-View Stereo reconstruction library", "homepage": "https://cdcseacave.github.io/openMVS", "license": "AGPL-3.0-only", "supports": "!xbox", "dependencies": ["boost-iostreams", "boost-program-options", "boost-serialization", "boost-system", "boost-throw-exception", {"name": "cgal", "default-features": false}, "eigen3", "glew", "glfw3", "libpng", "opencv", {"name": "openmvg", "features": ["software"], "platform": "!(windows & static)"}, {"name": "tiff", "default-features": false}, "vcglib", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}, "zlib"], "default-features": ["nonfree"], "features": {"ceres": {"description": "CERES support for openmvs", "dependencies": ["ceres"]}, "cuda": {"description": "CUDA support for openmvs", "dependencies": ["cuda"]}, "nonfree": {"description": "nonfree support for openmvs"}, "openmp": {"description": "OpenMP support for openmvs"}}}