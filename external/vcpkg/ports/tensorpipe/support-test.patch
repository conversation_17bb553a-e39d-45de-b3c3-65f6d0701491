diff --git a/tensorpipe/test/CMakeLists.txt b/tensorpipe/test/CMakeLists.txt
index 53e8643..09baaa0 100644
--- a/tensorpipe/test/CMakeLists.txt
+++ b/tensorpipe/test/CMakeLists.txt
@@ -93,25 +93,30 @@ if(TP_USE_CUDA)
       channel/cuda_ipc/cuda_ipc_test.cc
       )
   endif()
-
-  list(APPEND TP_TEST_SRCS
-    channel/cuda_gdr/cuda_gdr_test.cc
-    )
-
+  if(TP_ENABLE_CUDA_GDR)
+    list(APPEND TP_TEST_SRCS
+      channel/cuda_gdr/cuda_gdr_test.cc
+      )
+  endif()
   cuda_add_library(tensorpipe_cuda_kernel channel/kernel.cu)
   list(APPEND TP_TEST_LINK_LIBRARIES tensorpipe_cuda_kernel)
 
   list(APPEND TP_TEST_LINK_LIBRARIES tensorpipe_cuda)
 endif()
 
-add_subdirectory(${PROJECT_SOURCE_DIR}/third_party/googletest
-  ${PROJECT_BINARY_DIR}/third_party/googletest EXCLUDE_FROM_ALL)
+find_package(GTest CONFIG REQUIRED)
+
+if (TARGET libuv::uv)
+    set(LIBUV_LIBRARY libuv::uv)
+else()
+    set(LIBUV_LIBRARY libuv::uv_a)
+endif()
 
 list(APPEND TP_TEST_LINK_LIBRARIES
   tensorpipe
-  uv::uv
-  gmock
-  gtest_main)
+  ${LIBUV_LIBRARY}
+  GTest::gmock
+  GTest::gtest_main)
 
 add_executable(tensorpipe_test ${TP_TEST_SRCS})
 
