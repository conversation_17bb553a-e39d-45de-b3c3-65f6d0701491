{"name": "tensorpipe", "version-date": "2022-03-16", "port-version": 5, "description": "A tensor-aware point-to-point communication primitive for machine learning", "homepage": "https://github.com/pytorch/tensorpipe", "license": "BSD-3-<PERSON><PERSON>", "supports": "linux | osx", "dependencies": ["libnop", "libuv", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"cuda": {"description": "Enable support for CUDA tensors, CUDA IPC channel", "dependencies": ["cuda"]}, "pybind11": {"description": "Build Python bindings", "dependencies": ["pybind11"]}, "test": {"description": "Build with Google.Test", "dependencies": ["gtest"]}}}