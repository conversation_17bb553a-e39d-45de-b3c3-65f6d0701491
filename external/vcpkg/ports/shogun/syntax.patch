diff --git a/src/shogun/io/ShogunErrc.h b/src/shogun/io/ShogunErrc.h
index 6cb5619c5..98b08b494 100644
--- a/src/shogun/io/ShogunErrc.h
+++ b/src/shogun/io/ShogunErrc.h
@@ -8,6 +8,7 @@
 #define SHOGUN_ERRORS_H__
 
 #include <system_error>
+#include <cstdint>
 
 #include <shogun/lib/common.h>
 
diff --git a/src/shogun/mathematics/linalg/LinalgNamespace.h b/src/shogun/mathematics/linalg/LinalgNamespace.h
index 40888e00c..c4293e378 100644
--- a/src/shogun/mathematics/linalg/LinalgNamespace.h
+++ b/src/shogun/mathematics/linalg/LinalgNamespace.h
@@ -1825,7 +1825,6 @@ namespace shogun
 				auto max_it = std::max_element(a_copy.begin(), a_copy.begin() + n);
 				result = (a_copy[n] + *max_it) / 2;
 			}
-		}
 
 			return result;
 		}
