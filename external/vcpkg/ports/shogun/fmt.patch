diff --git a/src/shogun/classifier/mkl/MKL.cpp b/src/shogun/classifier/mkl/MKL.cpp
index 2622df919..1a36aa513 100644
--- a/src/shogun/classifier/mkl/MKL.cpp
+++ b/src/shogun/classifier/mkl/MKL.cpp
@@ -369,7 +369,7 @@ bool MKL::train_machine(std::shared_ptr<Features> data)
 	io::info("mkl_epsilon = %1.1e", mkl_epsilon);
 	io::info("C_mkl = %1.1e", C_mkl);
 	io::info("mkl_norm = %1.3e", mkl_norm);
-	io::info("solver = {}", get_solver_type());
+	io::info("solver = {}", (int)get_solver_type());
 	io::info("ent_lambda = {}", ent_lambda);
 	io::info("mkl_block_norm = {}", mkl_block_norm);
 
diff --git a/src/shogun/classifier/mkl/MKLMulticlass.cpp b/src/shogun/classifier/mkl/MKLMulticlass.cpp
index b50876f14..dc0f80e8a 100644
--- a/src/shogun/classifier/mkl/MKLMulticlass.cpp
+++ b/src/shogun/classifier/mkl/MKLMulticlass.cpp
@@ -104,7 +104,7 @@ void MKLMulticlass::initlpsolver()
 	{
       error("MKLMulticlass::initlpsolver(): given kernel is not of type"
             " K_COMBINED {} required by Multiclass Mkl \n",
-            m_kernel->get_kernel_type());
+            (int)m_kernel->get_kernel_type());
 	}
 
    int numker=std::dynamic_pointer_cast<CombinedKernel>(m_kernel)->get_num_subkernels();
diff --git a/src/shogun/evaluation/ROCEvaluation.cpp b/src/shogun/evaluation/ROCEvaluation.cpp
index 482f06528..58595c844 100644
--- a/src/shogun/evaluation/ROCEvaluation.cpp
+++ b/src/shogun/evaluation/ROCEvaluation.cpp
@@ -30,11 +30,11 @@ float64_t ROCEvaluation::evaluate(std::shared_ptr<Labels> predicted, std::shared
 	require(
 	    predicted->get_label_type() == LT_BINARY,
 	    "Given predicted labels ({}) must be binary ({}).",
-	    predicted->get_label_type(), LT_BINARY);
+	    (int)predicted->get_label_type(), (int)LT_BINARY);
 	require(
 	    ground_truth->get_label_type() == LT_BINARY,
 	    "Given ground truth labels ({}) must be binary ({}).",
-	    ground_truth->get_label_type(), LT_BINARY);
+	    (int)ground_truth->get_label_type(), (int)LT_BINARY);
 
 	return evaluate_roc(binary_labels(predicted),binary_labels(ground_truth));
 }
diff --git a/src/shogun/io/SGIO.h b/src/shogun/io/SGIO.h
index 336e35b03..0ba7b2c0b 100644
--- a/src/shogun/io/SGIO.h
+++ b/src/shogun/io/SGIO.h
@@ -307,7 +307,7 @@ namespace shogun
 			if (should_log(prio))
 			{
 				fmt::memory_buffer msg;
-				fmt::format_to(msg, format, std::forward<Args>(args)...);
+				fmt::format_to(std::back_inserter(msg), format, std::forward<Args>(args)...);
 				message_(prio, loc, fmt::string_view(msg.data(), msg.size()));
 			}
 		}
@@ -357,7 +357,7 @@ namespace shogun
 		    "ExceptionType must be nothrow copy constructible");
 
 		fmt::memory_buffer msg;
-		fmt::format_to(msg, format, std::forward<Args>(args)...);
+		fmt::format_to(std::back_inserter(msg), format, std::forward<Args>(args)...);
 		msg.push_back('\0');
 		env()->io()->message(io::MSG_ERROR, loc, msg.data());
 		throw ExceptionType(msg.data());
diff --git a/src/shogun/io/serialization/JsonDeserializer.cpp b/src/shogun/io/serialization/JsonDeserializer.cpp
index 24fd30edd..7667e34d3 100644
--- a/src/shogun/io/serialization/JsonDeserializer.cpp
+++ b/src/shogun/io/serialization/JsonDeserializer.cpp
@@ -49,7 +49,7 @@ public:
 	void on(std::vector<bool>::reference* v) override
 	{
 		*v = next_element<bool>(&ValueType::GetBool);
-		SG_DEBUG("read bool with value {}", *v);
+		SG_DEBUG("read bool with value {}", (bool)*v);
 	}
 	void on(char* v) override
 	{
diff --git a/src/shogun/io/serialization/JsonSerializer.cpp b/src/shogun/io/serialization/JsonSerializer.cpp
index 22c99ec5c..530a14525 100644
--- a/src/shogun/io/serialization/JsonSerializer.cpp
+++ b/src/shogun/io/serialization/JsonSerializer.cpp
@@ -62,7 +62,7 @@ public:
 	}
 	void on(std::vector<bool>::reference* v) override
 	{
-		SG_DEBUG("writing bool with value {}", *v);
+		SG_DEBUG("writing bool with value {}", (bool)*v);
 		m_json_writer.Bool(*v);
 		close_container();
 	}
diff --git a/src/shogun/kernel/CustomKernel.cpp b/src/shogun/kernel/CustomKernel.cpp
index 06a7e7abd..78be0ef6f 100644
--- a/src/shogun/kernel/CustomKernel.cpp
+++ b/src/shogun/kernel/CustomKernel.cpp
@@ -112,10 +112,10 @@ bool CustomKernel::init(std::shared_ptr<Features> l, std::shared_ptr<Features> r
 	/* Make sure l and r have the same type of CFeatures */
 	require(l->get_feature_class()==r->get_feature_class(),
 			"Different FeatureClass: l is {}, r is {}",
-			l->get_feature_class(),r->get_feature_class());
+			(int)l->get_feature_class(),(int)r->get_feature_class());
 	require(l->get_feature_type()==r->get_feature_type(),
 			"Different FeatureType: l is {}, r is {}",
-			l->get_feature_type(),r->get_feature_type());
+			(int)l->get_feature_type(),(int)r->get_feature_type());
 
 	/* If l and r are the type of IndexFeatures,
 	 * the init function adds a subset to kernel matrix.
diff --git a/src/shogun/preprocessor/DensePreprocessor.cpp b/src/shogun/preprocessor/DensePreprocessor.cpp
index 56de8a9e6..05cd9a32e 100644
--- a/src/shogun/preprocessor/DensePreprocessor.cpp
+++ b/src/shogun/preprocessor/DensePreprocessor.cpp
@@ -90,7 +90,7 @@ std::shared_ptr<Features> DensePreprocessor<ST>::transform(std::shared_ptr<Featu
 {
 	require(features->get_feature_class()==C_DENSE, "Provided features ({}) "
 			"has to be of C_DENSE ({}) class!",
-			features->get_feature_class(), C_DENSE);
+			(int)features->get_feature_class(), (int)C_DENSE);
 
 	auto matrix = features->as<DenseFeatures<ST>>()->get_feature_matrix();
 	if (!inplace)
@@ -107,7 +107,7 @@ DensePreprocessor<ST>::inverse_transform(std::shared_ptr<Features> features, boo
 		features->get_feature_class() == C_DENSE,
 		"Provided features ({}) "
 		"has to be of C_DENSE ({}) class!",
-		features->get_feature_class(), C_DENSE);
+		(int)features->get_feature_class(), (int)C_DENSE);
 
 	auto matrix = features->as<DenseFeatures<ST>>()->get_feature_matrix();
 	if (!inplace)
diff --git a/src/shogun/preprocessor/KernelPCA.cpp b/src/shogun/preprocessor/KernelPCA.cpp
index 6709f67cd..0707f3db9 100644
--- a/src/shogun/preprocessor/KernelPCA.cpp
+++ b/src/shogun/preprocessor/KernelPCA.cpp
@@ -125,7 +125,7 @@ std::shared_ptr<Features> KernelPCA::transform(std::shared_ptr<Features> feature
 		return apply_to_string_features(features);
 	}
 
-	error("Feature type {} not supported", features->get_feature_type());
+	error("Feature type {} not supported", (int)features->get_feature_type());
 	return NULL;
 }
 
diff --git a/src/shogun/preprocessor/StringPreprocessor.cpp b/src/shogun/preprocessor/StringPreprocessor.cpp
index c0c342b82..87629b5cb 100644
--- a/src/shogun/preprocessor/StringPreprocessor.cpp
+++ b/src/shogun/preprocessor/StringPreprocessor.cpp
@@ -95,7 +95,7 @@ namespace shogun
 		    features->get_feature_class() == C_STRING,
 		    "Provided features ({}) "
 		    "has to be of C_STRING ({}) class!",
-		    features->get_feature_class(), C_STRING);
+		    (int)features->get_feature_class(), (int)C_STRING);
 
 
 
diff --git a/src/shogun/structure/BeliefPropagation.cpp b/src/shogun/structure/BeliefPropagation.cpp
index e54228917..96b90c458 100644
--- a/src/shogun/structure/BeliefPropagation.cpp
+++ b/src/shogun/structure/BeliefPropagation.cpp
@@ -248,7 +248,7 @@ void TreeMaxProduct::bottom_up_pass()
 	for (uint32_t mi = 0; mi < m_msg_order.size(); ++mi)
 	{
 		SG_DEBUG("mi = {}, mtype: {} {} -> {}", mi,
-			m_msg_order[mi]->mtype, m_msg_order[mi]->child, m_msg_order[mi]->parent);
+			(int)m_msg_order[mi]->mtype, m_msg_order[mi]->child, m_msg_order[mi]->parent);
 
 		if (m_msg_order[mi]->mtype == VAR_TO_FAC) // var -> factor
 		{
@@ -392,7 +392,7 @@ void TreeMaxProduct::top_down_pass()
 	for (int32_t mi = (int32_t)(m_msg_order.size()-1); mi >= 0; --mi)
 	{
 		SG_DEBUG("mi = {}, mtype: {} {} <- {}", mi,
-			m_msg_order[mi]->mtype, m_msg_order[mi]->child, m_msg_order[mi]->parent);
+			(int)m_msg_order[mi]->mtype, m_msg_order[mi]->child, m_msg_order[mi]->parent);
 
 		if (m_msg_order[mi]->mtype == FAC_TO_VAR) // factor <- var
 		{
diff --git a/src/shogun/structure/CCSOSVM.cpp b/src/shogun/structure/CCSOSVM.cpp
index 8dd920ad8..8267a402c 100644
--- a/src/shogun/structure/CCSOSVM.cpp
+++ b/src/shogun/structure/CCSOSVM.cpp
@@ -321,7 +321,7 @@ bool CCSOSVM::train_machine(std::shared_ptr<Features> data)
 					proximal_rhs[i] = (1+rho)*delta[i] - rho*gammaG0[i];
 					break;
 				default:
-					error("Invalid QPType: {}", m_qp_type);
+					error("Invalid QPType: {}", (int)m_qp_type);
 			}
 		}
 
@@ -351,7 +351,7 @@ bool CCSOSVM::train_machine(std::shared_ptr<Features> data)
 				*/
 				break;
 			default:
-				error("Invalid QPType: {}", m_qp_type);
+				error("Invalid QPType: {}", (int)m_qp_type);
 		}
 
 		/* DEBUG */
@@ -697,12 +697,12 @@ void CCSOSVM::init()
 
 	/* check return code */
 	if (r != MSK_RES_OK)
-		error("Error while creating mosek env: {}", r);
+		error("Error while creating mosek env: {}", (int)r);
 
 	/* initialize the environment */
 	r = MSK_initenv(m_msk_env);
 	if (r != MSK_RES_OK)
-		error("Error while initializing mosek env: {}", r);
+		error("Error while initializing mosek env: {}", (int)r);
 #endif
 
 	SG_ADD(&m_C, "m_C", "C");
