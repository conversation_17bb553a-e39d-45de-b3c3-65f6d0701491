{"name": "shogun", "version-date": "2023-12-19", "port-version": 1, "description": "Unified and efficient Machine Learning", "homepage": "https://github.com/shogun-toolbox/shogun", "dependencies": ["blas", "bzip2", "curl", "eigen3", {"name": "hdf5", "default-features": false, "platform": "!windows"}, "lapack", "liblzma", "libxml2", "lzo", "nlopt", "protobuf", "<PERSON><PERSON><PERSON>", "rxcpp", "snappy", "spdlog", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}, {"name": "vcpkg-pkgconfig-get-modules", "host": true}, "zlib"]}