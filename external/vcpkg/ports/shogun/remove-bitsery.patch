diff --git a/src/interfaces/swig/SGBase.i b/src/interfaces/swig/SGBase.i
index 543a8ff00..4b4df8604 100644
--- a/src/interfaces/swig/SGBase.i
+++ b/src/interfaces/swig/SGBase.i
@@ -504,10 +504,7 @@ namespace shogun
         PyObject* __getstate__()
         {
             std::shared_ptr<io::Serializer> serializer = nullptr;
-            if (pickle_ascii)
                 serializer = std::make_shared<io::JsonSerializer>();
-            else
-                serializer = std::make_shared<io::BitserySerializer>();
             auto byte_stream = std::make_shared<io::ByteArrayOutputStream>();
             serializer->attach(byte_stream);
             serializer->write(std::shared_ptr<SGObject>($self));
@@ -537,10 +534,7 @@ namespace shogun
             PyString_AsStringAndSize(py_str, &str, &len);
 #endif
             std::shared_ptr<io::Deserializer> deser = nullptr;
-            if (pickle_ascii)
                 deser = std::make_shared<io::JsonDeserializer>();
-            else
-                deser = std::make_shared<io::BitseryDeserializer>();
 
             auto byte_input_stream = std::make_shared<io::ByteArrayInputStream>(str, len);
             deser->attach(byte_input_stream);
diff --git a/src/shogun/CMakeLists.txt b/src/shogun/CMakeLists.txt
index 20c4b4fa6..fa4be96f6 100644
--- a/src/shogun/CMakeLists.txt
+++ b/src/shogun/CMakeLists.txt
@@ -12,6 +12,7 @@ include(CheckCXXSourceCompiles)
 include(CMakePackageConfigHelpers)
 
 FILE(GLOB_RECURSE LIBSHOGUN_SRC *.${EXT_SRC_CPP} *.${EXT_SRC_C})
+list(FILTER LIBSHOGUN_SRC EXCLUDE REGEX ".*(BitserySerializer\.cpp|BitseryDeserializer\.cpp)$")
 FILE(GLOB_RECURSE LIBSHOGUN_HEADERS *.${EXT_SRC_HEADER})
 FILE(GLOB_RECURSE LIBSHOGUN_SRC_TMP *.${EXT_CPP_TMP})
 
diff --git a/src/shogun/base/class_list.cpp.py b/src/shogun/base/class_list.cpp.py
index 854d5dd1e..92c4379c1 100644
--- a/src/shogun/base/class_list.cpp.py
+++ b/src/shogun/base/class_list.cpp.py
@@ -69,7 +69,7 @@ class_blacklist = ["SGVector", "SGMatrix", "SGSparseVector", "SGSparseMatrix",
         "NumericalVGLikelihood", "SingleFITCInference", "VariationalGaussianLikelihood",
         "RationalApproximation", "FirstOrderStochasticMinimizer", "IndependenceTest",
         "TwoDistributionTest", "TwoSampleTest", "RealDistance", "BinaryClassEvaluation",
-        "MomentumCorrection", "OneDistributionTest", "DependenceMaximization"]
+        "MomentumCorrection", "OneDistributionTest", "DependenceMaximization", "BitserySerializer", "BitseryDeserializer"]
 
 SHOGUN_TEMPLATE_CLASS = "SHOGUN_TEMPLATE_CLASS"
 SHOGUN_BASIC_CLASS = "SHOGUN_BASIC_CLASS"
