diff --git a/CMakeLists.txt b/CMakeLists.txt
index 8c59277..3fe6f5d 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -11,11 +11,7 @@ if (POLICY CMP0058)
   cmake_policy(SET CMP0058 NEW)
 endif()
 
-if (NOT IS_DIRECTORY "${CMAKE_CURRENT_SOURCE_DIR}/ext/glfw/src")
-  message(FATAL_ERROR "The NanoGUI dependency repositories (GLFW, etc.) are missing! "
-    "You probably did not clone the project with --recursive. It is possible to recover "
-    "by calling \"git submodule update --init --recursive\"")
-endif()
+
 
 if (WIN32)
   set(NANOGUI_USE_GLAD_DEFAULT ON)
@@ -78,13 +74,11 @@ if (CMAKE_CXX_COMPILER_ID MATCHES "Clang")
   set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -Wno-deprecated-declarations")
 endif()
 
-add_subdirectory("${CMAKE_CURRENT_SOURCE_DIR}/ext/glfw" "ext_build/glfw")
 # Two targets have now been defined: `glfw_objects`, which will be merged into
 # NanoGUI at the end, and `glfw`.  The `glfw` target is the library itself
 # (e.g., libglfw.so), but can be skipped as we do not need to link against it
 # (because we merge `glfw_objects` into NanoGUI).  Skipping is required for
 # XCode, but preferable for all build systems (reduces build artifacts).
-set_target_properties(glfw PROPERTIES EXCLUDE_FROM_ALL 1 EXCLUDE_FROM_DEFAULT_BUILD 1)
 
 # Python support: add NANOGUI_PYTHON flag to all targets
 if (NANOGUI_BUILD_PYTHON)
@@ -310,7 +304,6 @@ add_definitions(${NANOGUI_EXTRA_DEFS})
 # Compile main NanoGUI library
 add_library(nanogui-obj OBJECT
   # Merge NanoVG into the NanoGUI library
-  ext/nanovg/src/nanovg.c
   # Merge GLAD into the NanoGUI library (only if needed)
   ${LIBNANOGUI_EXTRA_SOURCE}
   # Fonts etc.
@@ -352,6 +345,11 @@ add_library(nanogui-obj OBJECT
   src/serializer.cpp
 )
 
+find_path(EIGEN_INCLUDE_DIR Eigen/Core)
+target_include_directories(nanogui-obj PRIVATE ${EIGEN_INCLUDE_DIR})
+find_path(STB_INCLUDE_DIR stb_image.h)
+target_include_directories(nanogui-obj PRIVATE ${STB_INCLUDE_DIR})
+
 # XCode has a serious bug where the XCode project produces an invalid target
 # that will not get linked if it consists only of objects from object libraries,
 # it will not generate any products (executables, libraries). The only work
@@ -363,15 +361,18 @@ if (CMAKE_GENERATOR STREQUAL Xcode)
   add_library(nanogui ${NANOGUI_LIBRARY_TYPE}
     ${XCODE_DUMMY}
     $<TARGET_OBJECTS:nanogui-obj>
-    $<TARGET_OBJECTS:glfw_objects>
   )
 else()
   add_library(nanogui ${NANOGUI_LIBRARY_TYPE}
     $<TARGET_OBJECTS:nanogui-obj>
-    $<TARGET_OBJECTS:glfw_objects>
   )
 endif()
 
+find_package(nanovg CONFIG REQUIRED)
+find_package(Eigen3 CONFIG REQUIRED)
+find_package(glfw3 CONFIG REQUIRED)
+target_link_libraries(nanogui glfw nanovg::nanovg Eigen3::Eigen)
+
 if (NANOGUI_BUILD_SHARED)
   set_property(TARGET nanogui-obj PROPERTY POSITION_INDEPENDENT_CODE ON)
 endif()
