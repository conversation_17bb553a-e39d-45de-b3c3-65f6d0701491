diff --git a/CMakeLists.txt b/CMakeLists.txt
index 3fe6f5d..22dc16f 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -87,7 +87,7 @@ endif()
 
 # Shared library mode: add dllimport/dllexport flags to all symbols
 if (NANOGUI_BUILD_SHARED)
-  list(APPEND NANOGUI_EXTRA_DEFS -DNANOGUI_SHARED -DNVG_SHARED -DGLAD_GLAPI_EXPORT)
+  list(APPEND NANOGUI_EXTRA_DEFS -DNANOGUI_SHARED -DNVG_SHARED)
 endif()
 
 if (MSVC)
@@ -196,21 +196,6 @@ if (CMAKE_CXX_COMPILER_ID MATCHES "Clang")
   endif()
 endif()
 
-if (NANOGUI_USE_GLAD)
-  # Build and include GLAD on Windows
-  list(APPEND LIBNANOGUI_EXTRA_SOURCE
-     "${CMAKE_CURRENT_SOURCE_DIR}/ext/glad/src/glad.c"
-	 "${CMAKE_CURRENT_SOURCE_DIR}/ext/glad/include/glad/glad.h"
-	 "${CMAKE_CURRENT_SOURCE_DIR}/ext/glad/include/KHR/khrplatform.h")
-  if (MSVC)
-    set_source_files_properties("${CMAKE_CURRENT_SOURCE_DIR}/ext/glad/src/glad.c"
-      PROPERTIES COMPILE_FLAGS "/wd4055 ")
-  endif()
-  include_directories(ext/glad/include)
-  list(APPEND NANOGUI_EXTRA_DEFS -DNANOGUI_GLAD)
-  list(APPEND NANOGUI_EXTRA_INCS "${CMAKE_CURRENT_SOURCE_DIR}/ext/glad/include")
-endif()
-
 list(APPEND NANOGUI_EXTRA_INCS
   "${CMAKE_CURRENT_SOURCE_DIR}/ext/glfw/include"
   "${CMAKE_CURRENT_SOURCE_DIR}/ext/nanovg/src"
@@ -299,8 +284,6 @@ if (APPLE)
   add_compile_options(-fobjc-arc)
 endif()
 
-add_definitions(${NANOGUI_EXTRA_DEFS})
-
 # Compile main NanoGUI library
 add_library(nanogui-obj OBJECT
   # Merge NanoVG into the NanoGUI library
@@ -373,6 +356,14 @@ find_package(Eigen3 CONFIG REQUIRED)
 find_package(glfw3 CONFIG REQUIRED)
 target_link_libraries(nanogui glfw nanovg::nanovg Eigen3::Eigen)
 
+if (NANOGUI_USE_GLAD)
+  find_package(glad CONFIG REQUIRED)
+  target_link_libraries(nanogui glad::glad)
+  list(APPEND NANOGUI_EXTRA_DEFS -DNANOGUI_GLAD)
+endif()
+
+add_definitions(${NANOGUI_EXTRA_DEFS})
+
 if (NANOGUI_BUILD_SHARED)
   set_property(TARGET nanogui-obj PROPERTY POSITION_INDEPENDENT_CODE ON)
 endif()
@@ -380,11 +371,6 @@ endif()
 # Compile/link flags for NanoGUI
 set_property(TARGET nanogui-obj APPEND PROPERTY COMPILE_DEFINITIONS "NANOGUI_BUILD;NVG_BUILD")
 
-if (NANOGUI_USE_GLAD AND NANOGUI_BUILD_SHARED)
-  set_property(TARGET nanogui-obj APPEND PROPERTY COMPILE_DEFINITIONS
-    "GLAD_GLAPI_EXPORT;GLAD_GLAPI_EXPORT_BUILD")
-endif()
-
 if (NANOGUI_BUILD_SHARED)
   target_link_libraries(nanogui ${NANOGUI_EXTRA_LIBS})
 endif()
diff --git a/include/nanogui/opengl.h b/include/nanogui/opengl.h
index f5abcb2..1c20653 100644
--- a/include/nanogui/opengl.h
+++ b/include/nanogui/opengl.h
@@ -17,10 +17,6 @@
 
 #ifndef DOXYGEN_SHOULD_SKIP_THIS
 #if defined(NANOGUI_GLAD)
-    #if defined(NANOGUI_SHARED) && !defined(GLAD_GLAPI_EXPORT)
-        #define GLAD_GLAPI_EXPORT
-    #endif
-
     #include <glad/glad.h>
 #else
     #if defined(__APPLE__)
diff --git a/src/example3.cpp b/src/example3.cpp
index 3d2ecfa..72deaa8 100644
--- a/src/example3.cpp
+++ b/src/example3.cpp
@@ -14,10 +14,6 @@
 // GLFW
 //
 #if defined(NANOGUI_GLAD)
-    #if defined(NANOGUI_SHARED) && !defined(GLAD_GLAPI_EXPORT)
-        #define GLAD_GLAPI_EXPORT
-    #endif
-
     #include <glad/glad.h>
 #else
     #if defined(__APPLE__)
