diff --git a/Modules/IO/JPEG2000/src/itkJPEG2000ImageIO.cxx b/Modules/IO/JPEG2000/src/itkJPEG2000ImageIO.cxx
index 3592573..aefb8a2 100644
--- a/Modules/IO/JPEG2000/src/itkJPEG2000ImageIO.cxx
+++ b/Modules/IO/JPEG2000/src/itkJPEG2000ImageIO.cxx
@@ -26,9 +26,7 @@
 
 extern "C"
 {
-#include "openjpeg.h"
-#include "j2k.h"
-#include "jp2.h"
+#include <openjpeg.h>
 }
 
 
@@ -62,8 +60,7 @@ public:
   OPJ_UINT32 m_TileStartX;
   OPJ_UINT32 m_TileStartY;
 
-  OPJ_UINT32 m_NumberOfTilesInX;
-  OPJ_UINT32 m_NumberOfTilesInY;
+  OPJ_UINT32 m_NumberOfTiles;
 
   opj_dparameters_t m_DecompressionParameters; /* decompression parameters */
 };
@@ -86,8 +83,7 @@ JPEG2000ImageIO::JPEG2000ImageIO()
   this->m_Internal->m_TileStartX = 0;
   this->m_Internal->m_TileStartY = 0;
 
-  this->m_Internal->m_NumberOfTilesInX = 0;
-  this->m_Internal->m_NumberOfTilesInY = 0;
+  this->m_Internal->m_NumberOfTiles = 0;
 
   const char * extensions[] = { ".j2k", ".jp2", ".jpt" };
 
@@ -139,22 +135,16 @@ JPEG2000ImageIO::ReadImageInformation()
 {
   itkDebugMacro("ReadImageInformation()");
 
-  FILE * l_file = fopen(this->m_FileName.c_str(), "rb");
-
-  /* decompression parameters */
+  /* set decoding parameters to default values */
+  opj_set_default_decoder_parameters(&(this->m_Internal->m_DecompressionParameters));
 
-  if (!l_file)
+  opj_stream_t * cio = opj_stream_create_default_file_stream(this->m_FileName.c_str(), true);
+  if (!cio)
   {
     itkExceptionMacro("JPEG2000ImageIO failed to open file for reading: " << this->GetFileName() << std::endl
                                                                           << "Reason: "
                                                                           << itksys::SystemTools::GetLastSystemError());
   }
-
-  /* set decoding parameters to default values */
-  opj_set_default_decoder_parameters(&(this->m_Internal->m_DecompressionParameters));
-
-  opj_stream_t * cio = opj_stream_create_default_file_stream(l_file, true);
-
   this->m_Internal->m_Dinfo = nullptr; /* handle to a decompressor */
 
   opj_image_t * l_image = nullptr;
@@ -187,14 +177,14 @@ JPEG2000ImageIO::ReadImageInformation()
       /* JPEG-2000 codestream */
 
       /* get a decoder handle */
-      this->m_Internal->m_Dinfo = opj_create_decompress(CODEC_J2K);
+      this->m_Internal->m_Dinfo = opj_create_decompress(OPJ_CODEC_J2K);
       if (!this->m_Internal->m_Dinfo)
       {
         opj_stream_destroy(cio);
-        fclose(l_file);
+        // fclose(l_file);
         itkExceptionMacro("JPEG2000ImageIO failed to read file: "
                           << this->GetFileName() << std::endl
-                          << "Reason: opj_create_decompress(CODEC_J2K) returns nullptr");
+                          << "Reason: opj_create_decompress(OPJ_CODEC_J2K) returns nullptr");
       }
       break;
     }
@@ -202,14 +192,14 @@ JPEG2000ImageIO::ReadImageInformation()
     {
       /* JPEG 2000 compressed image data */
       /* get a decoder handle */
-      this->m_Internal->m_Dinfo = opj_create_decompress(CODEC_JP2);
+      this->m_Internal->m_Dinfo = opj_create_decompress(OPJ_CODEC_JP2);
       if (!this->m_Internal->m_Dinfo)
       {
         opj_stream_destroy(cio);
-        fclose(l_file);
+        // fclose(l_file);
         itkExceptionMacro("JPEG2000ImageIO failed to read file: "
                           << this->GetFileName() << std::endl
-                          << "Reason: opj_create_decompress(CODEC_JP2) returns nullptr");
+                          << "Reason: opj_create_decompress(OPJ_CODEC_JP2) returns nullptr");
       }
       break;
     }
@@ -217,20 +207,19 @@ JPEG2000ImageIO::ReadImageInformation()
     {
       /* JPEG 2000, JPIP */
       /* get a decoder handle */
-      this->m_Internal->m_Dinfo = opj_create_decompress(CODEC_JPT);
+      this->m_Internal->m_Dinfo = opj_create_decompress(OPJ_CODEC_JPT);
       if (!this->m_Internal->m_Dinfo)
       {
         opj_stream_destroy(cio);
-        fclose(l_file);
+        // fclose(l_file);
         itkExceptionMacro("JPEG2000ImageIO failed to read file: "
                           << this->GetFileName() << std::endl
-                          << "Reason: opj_create_decompress(CODEC_JPT) returns nullptr");
+                          << "Reason: opj_create_decompress(OPJ_CODEC_JPT) returns nullptr");
       }
       break;
     }
     default:
       opj_stream_destroy(cio);
-      fclose(l_file);
       itkExceptionMacro("JPEG2000ImageIO failed to read file: "
                         << this->GetFileName() << std::endl
                         << "Reason: "
@@ -247,27 +236,11 @@ JPEG2000ImageIO::ReadImageInformation()
                                                               << "Reason: opj_setup_decoder returns false");
   }
 
-  // Image parameters - first tile
-  OPJ_INT32 l_tile_x0;
-  OPJ_INT32 l_tile_y0;
 
-  // Image parameters - tile width, height and number of tiles
-  OPJ_UINT32 l_tile_width;
-  OPJ_UINT32 l_tile_height;
-  OPJ_UINT32 l_nb_tiles_x;
-  OPJ_UINT32 l_nb_tiles_y;
 
   itkDebugMacro("Trying to read header now...");
 
-  bResult = opj_read_header(this->m_Internal->m_Dinfo,
-                            &l_image,
-                            &l_tile_x0,
-                            &l_tile_y0,
-                            &l_tile_width,
-                            &l_tile_height,
-                            &l_nb_tiles_x,
-                            &l_nb_tiles_y,
-                            cio);
+  bResult = opj_read_header(cio, this->m_Internal->m_Dinfo, &l_image);
 
   if (!bResult)
   {
@@ -283,14 +256,22 @@ JPEG2000ImageIO::ReadImageInformation()
                                                               << "Reason: Error whle reading image header");
   }
 
+  // Image parameters - first tile
+  OPJ_INT32 l_tile_x0=l_image->x0;
+  OPJ_INT32 l_tile_y0=l_image->y0;
+
+  // Image parameters - tile width, height and number of tiles
+  OPJ_UINT32 l_tile_width=l_image->x1;
+  OPJ_UINT32 l_tile_height=l_image->y1;
+  OPJ_UINT32 l_nb_tiles=l_image->numcomps;
+
   this->m_Internal->m_TileStartX = l_tile_x0;
   this->m_Internal->m_TileStartY = l_tile_y0;
 
   this->m_Internal->m_TileWidth = l_tile_width;
   this->m_Internal->m_TileHeight = l_tile_height;
 
-  this->m_Internal->m_NumberOfTilesInX = l_nb_tiles_x;
-  this->m_Internal->m_NumberOfTilesInY = l_nb_tiles_y;
+  this->m_Internal->m_NumberOfTiles = l_nb_tiles;
 
 
   itkDebugMacro("Number of Components = " << l_image->numcomps);
@@ -319,7 +300,7 @@ JPEG2000ImageIO::ReadImageInformation()
       this->SetPixelType(IOPixelEnum::SCALAR);
       break;
     case 3:
-      if (l_image->color_space != CLRSPC_SRGB)
+      if (l_image->color_space != OPJ_CLRSPC_SRGB)
       {
         itkWarningMacro("file does not specify color space, assuming sRGB");
       }
@@ -335,8 +316,7 @@ JPEG2000ImageIO::ReadImageInformation()
   itkDebugMacro("Tile Start Y = " << this->m_Internal->m_TileStartY);
   itkDebugMacro("Tile Width = " << this->m_Internal->m_TileWidth);
   itkDebugMacro("Tile Height = " << this->m_Internal->m_TileHeight);
-  itkDebugMacro("Number of Tiles X = " << this->m_Internal->m_NumberOfTilesInX);
-  itkDebugMacro("Number of Tiles Y = " << this->m_Internal->m_NumberOfTilesInY);
+  itkDebugMacro("Number of Tiles = " << this->m_Internal->m_NumberOfTiles);
 
   itkDebugMacro("image->x1 = " << l_image->x1);
   itkDebugMacro("image->y1 = " << l_image->y1);
@@ -349,7 +329,6 @@ JPEG2000ImageIO::ReadImageInformation()
 
   /* close the byte stream */
   opj_stream_destroy(cio);
-  fclose(l_file);
 
   if (this->m_Internal->m_Dinfo)
   {
@@ -368,16 +347,7 @@ JPEG2000ImageIO::Read(void * buffer)
 {
   itkDebugMacro("JPEG2000ImageIO::Read() Begin");
 
-  FILE * l_file = fopen(this->m_FileName.c_str(), "rb");
-
-  if (!l_file)
-  {
-    itkExceptionMacro("JPEG2000ImageIO failed to open file for reading: " << this->GetFileName() << std::endl
-                                                                          << "Reason: "
-                                                                          << itksys::SystemTools::GetLastSystemError());
-  }
-
-  opj_stream_t * l_stream = opj_stream_create_default_file_stream(l_file, true);
+  opj_stream_t * l_stream = opj_stream_create_default_file_stream(this->m_FileName.c_str(), true);
   if (!l_stream)
   {
     itkExceptionMacro("JPEG2000ImageIO failed to read file: "
@@ -397,12 +367,13 @@ JPEG2000ImageIO::Read(void * buffer)
     {
       /* JPEG-2000 codestream */
       /* get a decoder handle */
-      this->m_Internal->m_Dinfo = opj_create_decompress(CODEC_J2K);
+      this->m_Internal->m_Dinfo = opj_create_decompress(OPJ_CODEC_J2K);
       if (!this->m_Internal->m_Dinfo)
       {
+        opj_stream_destroy(l_stream);
         itkExceptionMacro("JPEG2000ImageIO failed to read file: "
                           << this->GetFileName() << std::endl
-                          << "Reason: opj_create_decompress(CODEC_J2K) returns nullptr");
+                          << "Reason: opj_create_decompress(OPJ_CODEC_J2K) returns nullptr");
       }
       break;
     }
@@ -410,12 +381,13 @@ JPEG2000ImageIO::Read(void * buffer)
     {
       /* JPEG 2000 compressed image data */
       /* get a decoder handle */
-      this->m_Internal->m_Dinfo = opj_create_decompress(CODEC_JP2);
+      this->m_Internal->m_Dinfo = opj_create_decompress(OPJ_CODEC_JP2);
       if (!this->m_Internal->m_Dinfo)
       {
+        opj_stream_destroy(l_stream);
         itkExceptionMacro("JPEG2000ImageIO failed to read file: "
                           << this->GetFileName() << std::endl
-                          << "Reason: opj_create_decompress(CODEC_JP2) returns nullptr");
+                          << "Reason: opj_create_decompress(OPJ_CODEC_JP2) returns nullptr");
       }
       break;
     }
@@ -423,17 +395,18 @@ JPEG2000ImageIO::Read(void * buffer)
     {
       /* JPEG 2000, JPIP */
       /* get a decoder handle */
-      this->m_Internal->m_Dinfo = opj_create_decompress(CODEC_JPT);
+      this->m_Internal->m_Dinfo = opj_create_decompress(OPJ_CODEC_JPT);
       if (!this->m_Internal->m_Dinfo)
       {
+        opj_stream_destroy(l_stream);
         itkExceptionMacro("JPEG2000ImageIO failed to read file: "
                           << this->GetFileName() << std::endl
-                          << "Reason: opj_create_decompress(CODEC_JPT) returns nullptr");
+                          << "Reason: opj_create_decompress(OPJ_CODEC_JPT) returns nullptr");
       }
       break;
     }
     default:
-      fclose(l_file);
+      opj_stream_destroy(l_stream);
       itkExceptionMacro("JPEG2000ImageIO failed to read file: "
                         << this->GetFileName() << std::endl
                         << "Reason: "
@@ -448,29 +421,13 @@ JPEG2000ImageIO::Read(void * buffer)
                                                               << "Reason: opj_setup_decoder returns false");
   }
 
-  OPJ_INT32 l_tile_x0, l_tile_y0;
-
-  OPJ_UINT32 l_tile_width;
-  OPJ_UINT32 l_tile_height;
-  OPJ_UINT32 l_nb_tiles_x;
-  OPJ_UINT32 l_nb_tiles_y;
-
-  bool bResult = opj_read_header(this->m_Internal->m_Dinfo,
-                                 &l_image,
-                                 &l_tile_x0,
-                                 &l_tile_y0,
-                                 &l_tile_width,
-                                 &l_tile_height,
-                                 &l_nb_tiles_x,
-                                 &l_nb_tiles_y,
-                                 l_stream);
+  bool bResult = opj_read_header(l_stream, this->m_Internal->m_Dinfo,&l_image);
 
   if (!bResult)
   {
     opj_destroy_codec(this->m_Internal->m_Dinfo);
     this->m_Internal->m_Dinfo = nullptr;
     opj_stream_destroy(l_stream);
-    fclose(l_file);
     itkExceptionMacro("JPEG2000ImageIO failed to read file: " << this->GetFileName() << std::endl
                                                               << "Reason: opj_read_header returns false");
   }
@@ -499,7 +456,7 @@ JPEG2000ImageIO::Read(void * buffer)
   itkDebugMacro("p_end_x = " << p_end_x);
   itkDebugMacro("p_end_y = " << p_end_y);
 
-  bResult = opj_set_decode_area(this->m_Internal->m_Dinfo, p_start_x, p_start_y, p_end_x, p_end_y);
+  bResult = opj_set_decode_area(this->m_Internal->m_Dinfo, l_image, p_start_x, p_start_y, p_end_x, p_end_y);
 
   itkDebugMacro("opj_set_decode_area() after");
 
@@ -508,7 +465,6 @@ JPEG2000ImageIO::Read(void * buffer)
     opj_destroy_codec(this->m_Internal->m_Dinfo);
     this->m_Internal->m_Dinfo = nullptr;
     opj_stream_destroy(l_stream);
-    fclose(l_file);
     itkExceptionMacro("JPEG2000ImageIO failed to read file: " << this->GetFileName() << std::endl
                                                               << "Reason: opj_set_decode_area returns false");
   }
@@ -525,13 +481,13 @@ JPEG2000ImageIO::Read(void * buffer)
 
   OPJ_UINT32 l_max_data_size = 1000;
 
-  bool l_go_on = true;
+  int l_go_on = true; // should be OBJ_BOOL
 
   auto * l_data = (OPJ_BYTE *)malloc(1000);
 
   while (l_go_on)
   {
-    bool tileHeaderRead = opj_read_tile_header(this->m_Internal->m_Dinfo,
+    bool tileHeaderRead = opj_read_tile_header(this->m_Internal->m_Dinfo, l_stream,
                                                &l_tile_index,
                                                &l_data_size,
                                                &l_current_tile_x0,
@@ -539,14 +495,12 @@ JPEG2000ImageIO::Read(void * buffer)
                                                &l_current_tile_x1,
                                                &l_current_tile_y1,
                                                &l_nb_comps,
-                                               &l_go_on,
-                                               l_stream);
+                                               &l_go_on);
 
     if (!tileHeaderRead)
     {
       free(l_data);
       opj_stream_destroy(l_stream);
-      fclose(l_file);
       opj_destroy_codec(this->m_Internal->m_Dinfo);
       opj_image_destroy(l_image);
       itkExceptionMacro("JPEG2000ImageIO failed to read file: " << this->GetFileName() << std::endl
@@ -571,7 +525,6 @@ JPEG2000ImageIO::Read(void * buffer)
         if (!l_data)
         {
           opj_stream_destroy(l_stream);
-          fclose(l_file);
           opj_destroy_codec(this->m_Internal->m_Dinfo);
           opj_image_destroy(l_image);
           itkExceptionMacro("JPEG2000ImageIO failed to read file: " << this->GetFileName() << std::endl
@@ -590,7 +543,6 @@ JPEG2000ImageIO::Read(void * buffer)
       {
         free(l_data);
         opj_stream_destroy(l_stream);
-        fclose(l_file);
         opj_destroy_codec(this->m_Internal->m_Dinfo);
         opj_image_destroy(l_image);
         itkExceptionMacro("JPEG2000ImageIO failed to read file: " << this->GetFileName() << std::endl
@@ -654,7 +606,6 @@ JPEG2000ImageIO::Read(void * buffer)
   {
     free(l_data);
     opj_stream_destroy(l_stream);
-    fclose(l_file);
     opj_destroy_codec(this->m_Internal->m_Dinfo);
     opj_image_destroy(l_image);
     itkExceptionMacro("JPEG2000ImageIO failed to read file: " << this->GetFileName() << std::endl
@@ -666,14 +617,12 @@ JPEG2000ImageIO::Read(void * buffer)
     opj_destroy_codec(this->m_Internal->m_Dinfo);
     this->m_Internal->m_Dinfo = nullptr;
     opj_stream_destroy(l_stream);
-    fclose(l_file);
     itkExceptionMacro("JPEG2000ImageIO failed to read file: " << this->GetFileName() << std::endl
                                                               << "Reason: error whle reading image");
   }
 
   /* close the byte stream */
   opj_stream_destroy(l_stream);
-  fclose(l_file);
 
   if (this->m_Internal->m_Dinfo)
   {
@@ -842,13 +791,13 @@ JPEG2000ImageIO::Write(const void * buffer)
 
   parameters.numresolution = numberOfResolutions;
 
-  OPJ_COLOR_SPACE      color_space = CLRSPC_GRAY;
+  OPJ_COLOR_SPACE      color_space = OPJ_CLRSPC_GRAY;
   opj_image_cmptparm_t cmptparms[3];
 
   if (this->GetNumberOfComponents() == 3)
   {
 
-    color_space = (this->GetPixelType() == IOPixelEnum::RGB) ? CLRSPC_SRGB : CLRSPC_UNSPECIFIED;
+    color_space = (this->GetPixelType() == IOPixelEnum::RGB) ? OPJ_CLRSPC_SRGB : OPJ_CLRSPC_UNSPECIFIED;
 
     /* initialize image components */
     memset(&cmptparms[0], 0, 3 * sizeof(opj_image_cmptparm_t));
@@ -866,7 +815,7 @@ JPEG2000ImageIO::Write(const void * buffer)
 
   if (this->GetNumberOfComponents() == 1)
   {
-    color_space = CLRSPC_GRAY;
+    color_space = OPJ_CLRSPC_GRAY;
 
     /* initialize image components */
     memset(&cmptparms[0], 0, sizeof(opj_image_cmptparm_t));
@@ -942,32 +891,32 @@ JPEG2000ImageIO::Write(const void * buffer)
   opj_codec_t * cinfo = nullptr;
   if (extension == ".j2k")
   {
-    cinfo = opj_create_compress(CODEC_J2K);
+    cinfo = opj_create_compress(OPJ_CODEC_J2K);
     if (!cinfo)
     {
       itkExceptionMacro("JPEG2000ImageIO failed to write file: "
                         << this->GetFileName() << std::endl
-                        << "Reason: opj_create_compress(CODEC_J2K) returns nullptr");
+                        << "Reason: opj_create_compress(OPJ_CODEC_J2K) returns nullptr");
     }
   }
   else if (extension == ".jp2")
   {
-    cinfo = opj_create_compress(CODEC_JP2);
+    cinfo = opj_create_compress(OPJ_CODEC_JP2);
     if (!cinfo)
     {
       itkExceptionMacro("JPEG2000ImageIO failed to write file: "
                         << this->GetFileName() << std::endl
-                        << "Reason: opj_create_compress(CODEC_JP2) returns nullptr");
+                        << "Reason: opj_create_compress(OPJ_CODEC_JP2) returns nullptr");
     }
   }
   else if (extension == ".jpt")
   {
-    cinfo = opj_create_compress(CODEC_JPT);
+    cinfo = opj_create_compress(OPJ_CODEC_JPT);
     if (!cinfo)
     {
       itkExceptionMacro("JPEG2000ImageIO failed to write file: "
                         << this->GetFileName() << std::endl
-                        << "Reason: opj_create_compress(CODEC_JPT) returns nullptr");
+                        << "Reason: opj_create_compress(OPJ_CODEC_JPT) returns nullptr");
     }
   }
   else
@@ -991,17 +940,9 @@ JPEG2000ImageIO::Write(const void * buffer)
                                                                << "Reason: opj_setup_encoder returns false");
   }
 
-  FILE * l_file = fopen(parameters.outfile, "wb");
-  if (!l_file)
-  {
-    itkExceptionMacro("JPEG2000ImageIO failed to open file for writing: " << this->GetFileName() << std::endl
-                                                                          << "Reason: "
-                                                                          << itksys::SystemTools::GetLastSystemError());
-  }
-
   /* open a byte stream for writing */
   /* allocate memory for all tiles */
-  opj_stream_t * cio = opj_stream_create_default_file_stream(l_file, false);
+  opj_stream_t * cio = opj_stream_create_default_file_stream(parameters.outfile, false);
   if (!cio)
   {
     itkExceptionMacro("JPEG2000ImageIO failed to write file: "
@@ -1019,8 +960,7 @@ JPEG2000ImageIO::Write(const void * buffer)
   if (!bSuccess)
   {
     opj_stream_destroy(cio);
-    fclose(l_file);
-    itkExceptionMacro("JPEG2000ImageIO failed to write file: " << this->GetFileName() << std::endl
+     itkExceptionMacro("JPEG2000ImageIO failed to write file: " << this->GetFileName() << std::endl
                                                                << "Reason: opj_start_compress returns false");
   }
 
@@ -1028,7 +968,6 @@ JPEG2000ImageIO::Write(const void * buffer)
   if (!bSuccess)
   {
     opj_stream_destroy(cio);
-    fclose(l_file);
     itkExceptionMacro("JPEG2000ImageIO failed to write file: " << this->GetFileName() << std::endl
                                                                << "Reason: opj_encode returns false");
   }
@@ -1037,14 +976,12 @@ JPEG2000ImageIO::Write(const void * buffer)
   if (!bSuccess)
   {
     opj_stream_destroy(cio);
-    fclose(l_file);
     itkExceptionMacro("JPEG2000ImageIO failed to write file: " << this->GetFileName() << std::endl
                                                                << "Reason: opj_end_compress returns false");
   }
 
   /* close and free the byte stream */
   opj_stream_destroy(cio);
-  fclose(l_file);
 
   /* free remaining compression structures */
   opj_destroy_codec(cinfo);
diff --git a/Modules/ThirdParty/OpenJPEG/src/itk_openjpeg.h b/Modules/ThirdParty/OpenJPEG/src/itk_openjpeg.h
index 2ad0009..77a48c5 100644
--- a/Modules/ThirdParty/OpenJPEG/src/itk_openjpeg.h
+++ b/Modules/ThirdParty/OpenJPEG/src/itk_openjpeg.h
@@ -25,6 +25,6 @@
 #ifndef itk_openjpeg_h
 #define itk_openjpeg_h
 
-#include <itkopenjpeg/openjpeg.h>
+#include <openjpeg.h>
 
 #endif
