diff --git a/Wrapping/TypedefMacros.cmake b/Wrapping/TypedefMacros.cmake
index e6f8d0bf3..6c000dba8 100644
--- a/Wrapping/TypedefMacros.cmake	
+++ b/Wrapping/TypedefMacros.cmake
@@ -62,8 +62,7 @@ macro(itk_wrap_module library_name)
 
   # WRAPPER_LIBRARY_LINK_LIBRARIES. List of other libraries that should
   # be linked to the wrapper library.
-  set(WRAPPER_LIBRARY_LINK_LIBRARIES ${ITK_LIBRARIES} ${${itk-module}_LIBRARIES})
-
+  set(WRAPPER_LIBRARY_LINK_LIBRARIES ${ITK_LIBRARIES} ${${library_name}_LIBRARIES})
   # WRAPPER_SUBMODULE_ORDER. List of *.wrap submodules in the source dir
   # that should be included/wrapped before the rest in the given order.
   # Just the submodule group name is needed, not the full path or file name.
