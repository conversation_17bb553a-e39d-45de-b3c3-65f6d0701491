diff --git a/Modules/ThirdParty/OpenJPEG/src/openjpeg/opj_includes.h b/Modules/ThirdParty/OpenJPEG/src/openjpeg/opj_includes.h
index e75a220d4d..4b13e1726a 100644
--- a/Modules/ThirdParty/OpenJPEG/src/openjpeg/opj_includes.h
+++ b/Modules/ThirdParty/OpenJPEG/src/openjpeg/opj_includes.h
@@ -87,8 +87,8 @@ Most compilers implement their own version of this keyword ...
   #endif
 #endif

-/* MSVC and Borland C do not have lrintf */
-#if defined(_MSC_VER) || defined(__BORLANDC__)
+/* MSVC pre 16.8 and Borland C do not have lrintf */
+#if (defined(_MSC_VER) && _MSC_VER < 1928) || defined(__BORLANDC__)

 /* MSVC 64bits doesn't support _asm */
 #if !defined(_WIN64)
