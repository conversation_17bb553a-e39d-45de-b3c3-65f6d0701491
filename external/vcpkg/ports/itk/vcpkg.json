{"name": "itk", "version": "5.4.0", "port-version": 2, "description": "Insight Segmentation and Registration Toolkit (ITK) is used for image processing and analysis.", "homepage": "https://github.com/InsightSoftwareConsortium/ITK", "license": "Apache-2.0", "dependencies": ["double-conversion", "eigen3", "expat", "gdcm", {"name": "hdf5", "default-features": false, "features": ["cpp"]}, "libjpeg-turbo", "libpng", "minc", {"name": "openjpeg", "default-features": false}, {"name": "tiff", "default-features": false}, {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}, "vxl", "zlib"], "features": {"cuda": {"description": "Build CUDA module", "dependencies": ["cuda", {"name": "itk", "default-features": false, "features": ["opencl"]}]}, "cufftw": {"description": "Use CUDA FFTW", "dependencies": ["cuda"]}, "fftw": {"description": "Enable the FFTW backend", "dependencies": [{"name": "fftw3", "features": ["threads"]}]}, "opencl": {"description": "Use OpenCL", "dependencies": ["opencl"]}, "opencv": {"description": "Build ITKVideoBridgeOpenCV module.", "dependencies": ["opencv"]}, "python": {"description": "Python functionality for ITK", "dependencies": ["python3", {"name": "vcpkg-tool-castxml", "host": true}]}, "rtk": {"description": "Build RTK Module", "supports": "!x86", "license": null}, "tbb": {"description": "Build TBB Module", "dependencies": ["tbb"]}, "tools": {"description": "Build RTK with tools", "dependencies": [{"name": "itk", "default-features": false, "features": ["rtk"]}]}, "vtk": {"description": "Build ITKVtkGlue module.", "dependencies": [{"name": "vtk", "default-features": false}]}}}