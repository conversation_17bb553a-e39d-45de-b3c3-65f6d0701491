diff --git a/CMake/ITKModuleMacros.cmake b/CMake/ITKModuleMacros.cmake
index da9f3a902..60420bb0a 100644
--- a/CMake/ITKModuleMacros.cmake	
+++ b/CMake/ITKModuleMacros.cmake
@@ -162,9 +162,6 @@ macro(itk_module_impl)
     foreach(dep IN LISTS ITK_MODULE_${itk-module}_DEPENDS)
       list(APPEND ${itk-module}_LIBRARIES "${${dep}_LIBRARIES}")
     endforeach()
-    if(${itk-module}_LIBRARIES)
-      list(REMOVE_DUPLICATES ${itk-module}_LIBRARIES)
-    endif()
   endif()
 
   if(EXISTS ${${itk-module}_SOURCE_DIR}/include)
diff --git a/CMake/ITKModuleAPI.cmake b/CMake/ITKModuleAPI.cmake
index af64d96dc..809335e6d 100644
--- a/CMake/ITKModuleAPI.cmake	
+++ b/CMake/ITKModuleAPI.cmake
@@ -133,11 +133,10 @@ macro(itk_module_config ns)
   endforeach()
   unset(_${ns}_USED_MODULES)
 
   foreach(
     v
-    ${ns}_LIBRARIES
-    ${ns}_INCLUDE_DIRS
-    ${ns}_LIBRARY_DIRS
+    ${ns}_INCLUDE_DIRS
+    ${ns}_LIBRARY_DIRS
     ${ns}_RUNTIME_LIBRARY_DIRS
     ${ns}_FACTORY_NAMES
     ${ns}_FACTORY_LIST)
