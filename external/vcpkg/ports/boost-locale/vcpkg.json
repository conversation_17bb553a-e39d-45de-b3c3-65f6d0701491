{"$comment": "Automatically generated by scripts/boost/generate-ports.ps1", "name": "boost-locale", "version": "1.87.0", "description": "Boost locale module", "homepage": "https://www.boost.org/libs/locale", "license": "BSL-1.0", "supports": "!uwp", "dependencies": [{"name": "boost-assert", "version>=": "1.87.0"}, {"name": "boost-cmake", "version>=": "1.87.0"}, {"name": "boost-config", "version>=": "1.87.0"}, {"name": "boost-core", "version>=": "1.87.0"}, {"name": "boost-headers", "version>=": "1.87.0"}, {"name": "boost-iterator", "version>=": "1.87.0"}, {"name": "boost-predef", "version>=": "1.87.0"}, {"name": "boost-thread", "version>=": "1.87.0"}, {"name": "boost-utility", "version>=": "1.87.0"}, {"name": "libiconv", "platform": "!uwp & !windows & !mingw"}], "features": {"icu": {"description": "ICU backend for Boost.Locale", "dependencies": ["icu"]}}}