# Automatically generated by scripts/boost/generate-ports.ps1

vcpkg_from_github(
    OUT_SOURCE_PATH SOURCE_PATH
    REPO boostorg/locale
    REF boost-${VERSION}
    SHA512 8ced7d51661754c54c95e2fd1499657270c90c225d9024dd0ba136d68e4aee153eb5a82f41dce5d41b78dacc4c8c2a0ae0bf4fa08e4b70223629a49c5d6506f5
    HEAD_REF master
)

set(FEATURE_OPTIONS "")
include("${CMAKE_CURRENT_LIST_DIR}/features.cmake")
boost_configure_and_install(
    SOURCE_PATH "${SOURCE_PATH}"
    OPTIONS ${FEATURE_OPTIONS}
)
