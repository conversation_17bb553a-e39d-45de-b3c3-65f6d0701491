diff --git a/CMakeLists.txt b/CMakeLists.txt
index 3c6c863..fd08a48 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -1,6 +1,6 @@
 cmake_minimum_required(VERSION 3.1)
 
-if (WIN32)
+if (0)
     set(Boost_USE_STATIC_LIBS OFF)
     # The auto-linking feature has problems with USE_STATIC_LIBS off, so we use
     # BOOST_ALL_NO_LIB to turn it off.
@@ -100,6 +100,12 @@ find_package(Boost 1.41.0 REQUIRED COMPONENTS
   thread
   unit_test_framework
 )
+set(Boost_LIBRARIES
+  Boost::program_options
+  Boost::system
+  Boost::thread
+  Boost::unit_test_framework
+)
 
 # Define where include files live
 include_directories(${Boost_INCLUDE_DIRS})
