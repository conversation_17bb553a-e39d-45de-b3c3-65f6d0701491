diff --git a/CMakeLists.txt b/CMakeLists.txt
index fd08a48..7439ebf 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -128,7 +128,7 @@ install(EXPORT kenlmTargets
   DESTINATION share/kenlm/cmake
 )
 
-foreach(SUBDIR IN ITEMS util util/double-conversion util/stream lm lm/builder lm/common lm/filter lm/interpolate)
+foreach(SUBDIR IN ITEMS util                        util/stream lm lm/builder lm/common lm/filter lm/interpolate)
   file(GLOB HEADERS ${CMAKE_CURRENT_LIST_DIR}/${SUBDIR}/*.h ${CMAKE_CURRENT_LIST_DIR}/${SUBDIR}/*.hh)
   install(FILES ${HEADERS} DESTINATION include/kenlm/${SUBDIR} COMPONENT headers)
 endforeach(SUBDIR)
diff --git a/cmake/kenlmConfig.cmake.in b/cmake/kenlmConfig.cmake.in
index 0fbf0c6..592407d 100644
--- a/cmake/kenlmConfig.cmake.in
+++ b/cmake/kenlmConfig.cmake.in
@@ -4,6 +4,7 @@ include(CMakeFindDependencyMacro)
 
 find_dependency(Boost)
 find_dependency(Threads)
+find_dependency(double-conversion CONFIG)
 
 # Compression libs
 if (@ZLIB_FOUND@)
diff --git a/util/CMakeLists.txt b/util/CMakeLists.txt
index 7a96ef5..3318d73 100644
--- a/util/CMakeLists.txt
+++ b/util/CMakeLists.txt
@@ -32,10 +32,11 @@ if (WIN32)
 endif()
 
 # This directory has children that need to be processed
-add_subdirectory(double-conversion)
+find_package(double-conversion CONFIG REQUIRED)
 add_subdirectory(stream)
 
 add_library(kenlm_util ${KENLM_UTIL_DOUBLECONVERSION_SOURCE} ${KENLM_UTIL_STREAM_SOURCE} ${KENLM_UTIL_SOURCE})
+target_link_libraries(kenlm_util PRIVATE double-conversion::double-conversion)
 # Since headers are relative to `include/kenlm` at install time, not just `include`
 target_include_directories(kenlm_util PUBLIC $<INSTALL_INTERFACE:include/kenlm>)
 
