{"name": "libmem", "version": "5.0.4", "port-version": 1, "description": "Advanced Game Hacking Library for C, Modern C++, Rust and Python (Windows/Linux/FreeBSD) (Process/Memory Hacking) (Hooking/Detouring) (Cross Platform) (x86/x64/ARM/ARM64) (DLL/SO Injection) (Internal/External) (Assembler/Disassembler)", "homepage": "https://github.com/rdbo/libmem", "license": "AGPL-3.0-only", "supports": "(!xbox & windows) | ((linux | freebsd) & (x86 | x64))", "dependencies": ["capstone", "keystone", {"name": "llvm", "default-features": false}, {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}, {"name": "vcpkg-pkgconfig-get-modules", "host": true}]}