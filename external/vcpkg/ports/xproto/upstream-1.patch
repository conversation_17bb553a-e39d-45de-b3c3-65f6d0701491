diff --git a/include/X11/XF86keysym.h b/include/X11/XF86keysym.h
index c242e42f5fee2e28da11df2d33b005e9bb4b5441..59ba54d1a13fabdded6920ce946c1e20594f49ff 100644
--- a/include/X11/XF86keysym.h
+++ b/include/X11/XF86keysym.h
@@ -394,6 +394,7 @@
 #define XF86XK_VoiceCommand		_EVDEVK(0x246)		/* v3.16 KEY_VOICECOMMAND */
 #define XF86XK_Assistant		_EVDEVK(0x247)		/* v4.13 KEY_ASSISTANT */
 /* Use: XK_ISO_Next_Group		_EVDEVK(0x248)		   v5.2  KEY_KBD_LAYOUT_NEXT */
+#define XF86XK_EmojiPicker		_EVDEVK(0x249)		/* v5.13 KEY_EMOJI_PICKER */
 #define XF86XK_BrightnessMin		_EVDEVK(0x250)		/* v3.16 KEY_BRIGHTNESS_MIN */
 #define XF86XK_BrightnessMax		_EVDEVK(0x251)		/* v3.16 KEY_BRIGHTNESS_MAX */
 #define XF86XK_KbdInputAssistPrev	_EVDEVK(0x260)		/* v3.18 KEY_KBDINPUTASSIST_PREV */
