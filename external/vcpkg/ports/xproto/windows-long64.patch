diff --git a/include/X11/Xwinsock.h b/include/X11/Xwinsock.h
index 624047d..e8d5653 100644
--- a/include/X11/Xwinsock.h
+++ b/include/X11/Xwinsock.h
@@ -47,6 +47,10 @@ The Open Group.
 #define _NO_BOOL_TYPEDEF
 #define BOOL WINBOOL
 #define INT32 wINT32
+#ifdef __x86_64__
+#define INT64 wINT64
+#define LONG64 wLONG64
+#endif
 #undef Status
 #define Status wStatus
 #define ATOM wATOM
@@ -58,6 +62,8 @@ The Open Group.
 #undef BYTE
 #undef BOOL
 #undef INT32
+#undef INT64
+#undef LONG64
 #undef ATOM
 #undef FreeResource
 #undef CreateWindowA
diff --git a/include/X11/meson.build b/include/X11/meson.build
index 1c33c64..703ab19 100644
--- a/include/X11/meson.build
+++ b/include/X11/meson.build
@@ -1,7 +1,8 @@
 fd_set_maybe_headers = [ 'sys/param.h',
                          'sys/types.h',
                          'sys/time.h',
-                         'sys/select.h'
+                         'sys/select.h',
+                         'winsock2.h'
 ]
 fd_set_headers = ''
 foreach h : fd_set_maybe_headers
@@ -15,6 +16,8 @@ if cc.has_member('fd_set', 'fds_bits', prefix: fd_set_headers)
     fds_bits = 'fds_bits'
 elif cc.has_member('fd_set', '__fds_bits', prefix: fd_set_headers)
     fds_bits = '__fds_bits'
+elif cc.has_member('fd_set', 'fd_count', prefix: fd_set_headers)
+    fds_bits = 'fd_count'
 else
     error('Your fd_set is too weird.')
 endif
-- 