{"$comment": "Automatically generated by scripts/boost/generate-ports.ps1", "name": "boost-filesystem", "version": "1.87.0", "description": "Boost filesystem module", "homepage": "https://www.boost.org/libs/filesystem", "license": "BSL-1.0", "supports": "!uwp", "dependencies": [{"name": "boost-assert", "version>=": "1.87.0"}, {"name": "boost-atomic", "version>=": "1.87.0"}, {"name": "boost-cmake", "version>=": "1.87.0"}, {"name": "boost-config", "version>=": "1.87.0"}, {"name": "boost-container-hash", "version>=": "1.87.0"}, {"name": "boost-core", "version>=": "1.87.0"}, {"name": "boost-detail", "version>=": "1.87.0"}, {"name": "boost-headers", "version>=": "1.87.0"}, {"name": "boost-io", "version>=": "1.87.0"}, {"name": "boost-iterator", "version>=": "1.87.0"}, {"name": "boost-predef", "version>=": "1.87.0"}, {"name": "boost-scope", "version>=": "1.87.0"}, {"name": "boost-smart-ptr", "version>=": "1.87.0"}, {"name": "boost-system", "version>=": "1.87.0"}, {"name": "boost-type-traits", "version>=": "1.87.0"}, {"name": "boost-winapi", "version>=": "1.87.0"}]}