{"$comment": "Automatically generated by scripts/boost/generate-ports.ps1", "name": "boost-numeric-conversion", "version": "1.87.0", "description": "Boost numeric_conversion module", "homepage": "https://www.boost.org/libs/numeric/conversion", "license": "BSL-1.0", "dependencies": [{"name": "boost-cmake", "version>=": "1.87.0"}, {"name": "boost-config", "version>=": "1.87.0"}, {"name": "boost-conversion", "version>=": "1.87.0"}, {"name": "boost-core", "version>=": "1.87.0"}, {"name": "boost-headers", "version>=": "1.87.0"}, {"name": "boost-mpl", "version>=": "1.87.0"}, {"name": "boost-preprocessor", "version>=": "1.87.0"}, {"name": "boost-throw-exception", "version>=": "1.87.0"}, {"name": "boost-type-traits", "version>=": "1.87.0"}]}