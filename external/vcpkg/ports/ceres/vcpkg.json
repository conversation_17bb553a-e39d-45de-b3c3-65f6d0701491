{"name": "ceres", "version": "2.2.0", "port-version": 1, "description": "non-linear optimization package", "homepage": "https://github.com/ceres-solver/ceres-solver", "license": "Apache-2.0", "dependencies": ["eigen3", {"name": "glog", "platform": "!(arm & uwp)"}, {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"cuda": {"description": "Support for CUDA based dense solvers", "supports": "linux | (!osx & !uwp & !(arm64 & windows))", "dependencies": ["cuda"]}, "eigensparse": {"description": "Use of Eigen as a sparse linear algebra library in Ceres"}, "lapack": {"description": "Use Lapack in Ceres", "dependencies": ["lapack"]}, "schur": {"description": "Enable fixed-size Schur specializations in Ceres"}, "suitesparse": {"description": "SuiteSparse support for Ceres", "dependencies": [{"name": "ceres", "features": ["lapack"]}, {"name": "suitesparse-cholmod", "features": ["matrixops"]}, "suitesparse-spqr"]}, "tools": {"description": "Ceres tools", "dependencies": ["gflags"]}}}