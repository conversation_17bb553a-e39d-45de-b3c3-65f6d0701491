diff --git a/internal/ceres/fake_bundle_adjustment_jacobian.cc b/internal/ceres/fake_bundle_adjustment_jacobian.cc
index efe4d8d7..22f34059 100644
--- a/internal/ceres/fake_bundle_adjustment_jacobian.cc
+++ b/internal/ceres/fake_bundle_adjustment_jacobian.cc
@@ -96,25 +96,4 @@ std::unique_ptr<BlockSparseMatrix> CreateFakeBundleAdjustmentJacobian(
   return jacobian;
 }
 
-std::pair<
-    std::unique_ptr<PartitionedMatrixView<2, Eigen::Dynamic, Eigen::Dynamic>>,
-    std::unique_ptr<BlockSparseMatrix>>
-CreateFakeBundleAdjustmentPartitionedJacobian(int num_cameras,
-                                              int num_points,
-                                              int camera_size,
-                                              int landmark_size,
-                                              double visibility,
-                                              std::mt19937& rng) {
-  using PartitionedView =
-      PartitionedMatrixView<2, Eigen::Dynamic, Eigen::Dynamic>;
-  auto block_sparse_matrix = CreateFakeBundleAdjustmentJacobian(
-      num_cameras, num_points, camera_size, landmark_size, visibility, rng);
-  LinearSolver::Options options;
-  options.elimination_groups.push_back(num_points);
-  auto partitioned_view =
-      std::make_unique<PartitionedView>(options, *block_sparse_matrix);
-  return std::make_pair(std::move(partitioned_view),
-                        std::move(block_sparse_matrix));
-}
-
 }  // namespace ceres::internal
diff --git a/internal/ceres/fake_bundle_adjustment_jacobian.h b/internal/ceres/fake_bundle_adjustment_jacobian.h
index ced1b161..0448dbf3 100644
--- a/internal/ceres/fake_bundle_adjustment_jacobian.h
+++ b/internal/ceres/fake_bundle_adjustment_jacobian.h
@@ -47,32 +47,6 @@ std::unique_ptr<BlockSparseMatrix> CreateFakeBundleAdjustmentJacobian(
     double visibility,
     std::mt19937& prng);
 
-template <int kEBlockSize = 3, int kFBlockSize = 6>
-std::pair<std::unique_ptr<PartitionedMatrixView<2, kEBlockSize, kFBlockSize>>,
-          std::unique_ptr<BlockSparseMatrix>>
-CreateFakeBundleAdjustmentPartitionedJacobian(int num_cameras,
-                                              int num_points,
-                                              double visibility,
-                                              std::mt19937& rng) {
-  using PartitionedView = PartitionedMatrixView<2, kEBlockSize, kFBlockSize>;
-  auto block_sparse_matrix = CreateFakeBundleAdjustmentJacobian(
-      num_cameras, num_points, kFBlockSize, kEBlockSize, visibility, rng);
-  auto partitioned_view =
-      std::make_unique<PartitionedView>(*block_sparse_matrix, num_points);
-  return std::make_pair(std::move(partitioned_view),
-                        std::move(block_sparse_matrix));
-}
-
-std::pair<
-    std::unique_ptr<PartitionedMatrixView<2, Eigen::Dynamic, Eigen::Dynamic>>,
-    std::unique_ptr<BlockSparseMatrix>>
-CreateFakeBundleAdjustmentPartitionedJacobian(int num_cameras,
-                                              int num_points,
-                                              int camera_size,
-                                              int landmark_size,
-                                              double visibility,
-                                              std::mt19937& rng);
-
 }  // namespace ceres::internal
 
 #endif  // CERES_INTERNAL_FAKE_BUNDLE_ADJUSTMENT_JACOBIAN
