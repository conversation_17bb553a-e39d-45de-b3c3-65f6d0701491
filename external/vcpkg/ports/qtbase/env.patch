diff --git a/cmake/QtTestHelpers.cmake b/cmake/QtTestHelpers.cmake
index 279ab07dd..951fd3d36 100644
--- a/cmake/QtTestHelpers.cmake	
+++ b/cmake/QtTestHelpers.cmake
@@ -653,9 +653,9 @@ function(qt_internal_collect_command_environment out_path out_plugin_path)
     # The regular CMAKE_INSTALL_PREFIX can be different for example when building standalone tests.
     # Any given CMAKE_INSTALL_PREFIX takes priority over qt_relocatable_install_prefix for the
     # PATH environment variable.
-    set(install_prefixes "${CMAKE_INSTALL_PREFIX}")
+    set(install_prefixes "${CMAKE_INSTALL_PREFIX}$<$<CONFIG:DEBUG>:/debug>")
     if(QT_BUILD_INTERNALS_RELOCATABLE_INSTALL_PREFIX)
-        list(APPEND install_prefixes "${QT_BUILD_INTERNALS_RELOCATABLE_INSTALL_PREFIX}")
+        list(APPEND install_prefixes "${QT_BUILD_INTERNALS_RELOCATABLE_INSTALL_PREFIX}$<$<CONFIG:DEBUG>:/debug>")
     endif()
 
     file(TO_NATIVE_PATH "${CMAKE_CURRENT_BINARY_DIR}" test_env_path)
