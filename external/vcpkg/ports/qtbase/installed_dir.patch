diff --git a/cmake/qt.toolchain.cmake.in b/cmake/qt.toolchain.cmake.in
index 6e37ae02..e7e84468 100644
--- a/cmake/qt.toolchain.cmake.in
+++ b/cmake/qt.toolchain.cmake.in
@@ -65,6 +65,10 @@ get_filename_component(QT_TOOLCHAIN_RELOCATABLE_INSTALL_PREFIX
 # one level higher is what we're looking for.
 get_filename_component(QT_TOOLCHAIN_RELOCATABLE_CMAKE_DIR "${CMAKE_CURRENT_LIST_DIR}/.." ABSOLUTE)
 
+get_filename_component(vcpkg_installed_dir
+                       ${CMAKE_CURRENT_LIST_DIR}/../../../
+                       ABSOLUTE)
+
 # REROOT_PATH_ISSUE_MARKER
 # There's a subdirectory check in cmake's cmFindCommon::RerootPaths() function, that doesn't handle
 # the case of CMAKE_PREFIX_PATH == CMAKE_FIND_ROOT_PATH for a particular pair of entries.
