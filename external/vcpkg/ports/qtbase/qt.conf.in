[DevicePaths]
Prefix=${CURRENT_INSTALLED_DIR}
Headers=include/@QT6_DIRECTORY_PREFIX@
Libraries=@REL_PATH@lib
Plugins=@REL_PATH@@QT6_DIRECTORY_PREFIX@plugins
Qml2Imports=@REL_PATH@@QT6_DIRECTORY_PREFIX@qml
Documentation=doc/@QT6_DIRECTORY_PREFIX@
Binaries=@REL_PATH@bin
LibraryExecutables=tools/Qt6/bin
ArchData=share/Qt6
Data=share/Qt6
Translations=translations/@QT6_DIRECTORY_PREFIX@
Examples=share/examples/@QT6_DIRECTORY_PREFIX@
[Paths]
Prefix=${CURRENT_INSTALLED_DIR}
Headers=include/@QT6_DIRECTORY_PREFIX@
Libraries=@REL_PATH@lib
Plugins=@REL_PATH@@QT6_DIRECTORY_PREFIX@plugins
Qml2Imports=@REL_PATH@@QT6_DIRECTORY_PREFIX@qml
Documentation=doc/@QT6_DIRECTORY_PREFIX@
Binaries=@REL_PATH@bin
LibraryExecutables=tools/Qt6/bin
ArchData=share/Qt6
Data=share/Qt6
Translations=translations/@QT6_DIRECTORY_PREFIX@
Examples=share/examples/@QT6_DIRECTORY_PREFIX@
HostPrefix=${CURRENT_HOST_INSTALLED_DIR}
HostData=@REL_HOST_TO_DATA@share/Qt6
HostBinaries=@REL_PATH@bin
HostLibraries=@REL_PATH@lib
HostLibraryExecutables=tools/Qt6/bin
