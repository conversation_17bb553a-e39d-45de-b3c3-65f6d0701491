vcpkg_from_github(
    OUT_SOURCE_PATH SOURCE_PATH
    REPO NoAvailableAlias/nano-signal-slot
    REF 7e237d75e72501109d1276d7c0c97b33e9d7caf1
    SHA512 a998e59bfded36d9fe2c88d8f3c5229db4fa3c02062f218267f070f4809f63d0bad07300d3cf1f60141ab84bb1802402ac11de06159df6af460f0be4c47f8b9a
    HEAD_REF master
)

file(GLOB INCLUDES ${SOURCE_PATH}/*.hpp)
file(INSTALL ${INCLUDES} DESTINATION ${CURRENT_PACKAGES_DIR}/include)

file(INSTALL ${SOURCE_PATH}/LICENSE DESTINATION ${CURRENT_PACKAGES_DIR}/share/${PORT} RENAME copyright)