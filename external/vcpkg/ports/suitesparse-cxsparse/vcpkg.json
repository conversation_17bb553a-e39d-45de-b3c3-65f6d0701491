{"$comment": "Use scripts/update_suitesparse.py to update all SuiteSparse ports", "name": "suitesparse-cxsparse", "version-semver": "4.4.1", "description": "CXSparse: Software package for permuting a matrix into block upper triangular form in SuiteSparse", "homepage": "https://people.engr.tamu.edu/davis/suitesparse.html", "license": "LGPL-2.1-or-later", "dependencies": ["suitesparse-config", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}]}