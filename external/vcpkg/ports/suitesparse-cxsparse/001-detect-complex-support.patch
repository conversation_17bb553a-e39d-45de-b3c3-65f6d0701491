Android API level 22 and earlier don't provide any complex-valued math functions in complex.h,
which are required for complex data type support in CXSparse.

--- a/CXSparse/CMakeLists.txt
+++ b/CXSparse/CMakeLists.txt
@@ -45,7 +45,7 @@
 # MS Visual Studio does not support the complex type for ANSI C11.
 # FIXME: see GraphBLAS for how to use complex types in MS Visual Studio.
 
-if ( MSVC OR ("${CMAKE_C_SIMULATE_ID}" STREQUAL "MSVC") )
+if ( MSVC OR CMAKE_C_SIMULATE_ID STREQUAL "MSVC" OR (CMAKE_SYSTEM_NAME STREQUAL "Android" AND CMAKE_SYSTEM_VERSION VERSION_LESS 23) )
     option ( CXSPARSE_USE_COMPLEX "ON: complex data type enabled.  OFF (default): complex data type disabled." OFF )
 else ( )
     option ( CXSPARSE_USE_COMPLEX "ON (default): complex data type enabled.  OFF: complex data type disabled." ON )
