{"name": "zlmediakit", "version-date": "2024-09-29", "port-version": 1, "description": "A high-performance carrier-grade streaming media service framework based on C++11.", "homepage": "https://github.com/ZLMediaKit/ZLMediaKit", "license": "MIT", "supports": "!uwp", "dependencies": ["jsoncpp", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "default-features": ["mp4", "openssl", "webrtc"], "features": {"mp4": {"description": "Enable MP4"}, "openssl": {"description": "Enable OpenSSL", "dependencies": ["openssl"]}, "sctp": {"description": "Enable SCTP", "dependencies": ["usrsctp"]}, "webrtc": {"description": "Enable WebRTC", "dependencies": [{"name": "libsrtp", "features": ["openssl"]}]}}}