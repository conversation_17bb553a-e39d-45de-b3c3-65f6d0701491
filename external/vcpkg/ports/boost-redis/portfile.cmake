# Automatically generated by scripts/boost/generate-ports.ps1

vcpkg_from_github(
    OUT_SOURCE_PATH SOURCE_PATH
    REPO boostorg/redis
    REF boost-${VERSION}
    SHA512 84c36606241d6e6fe39b8fbc097b2dfe72f07ac46e30ff57243565b8803f0073b53b56f77e37fa2c4b44c7d0f866df3b16a1e5830fdf0b4a93496acce522cd9e
    HEAD_REF master
)

set(FEATURE_OPTIONS "")
boost_configure_and_install(
    SOURCE_PATH "${SOURCE_PATH}"
    OPTIONS ${FEATURE_OPTIONS}
)
