diff --git a/CMakeLists.txt b/CMakeLists.txt
index 4a21f65..a4cede0 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -15,11 +15,15 @@ set(CMAKE_CXX_EXTENSIONS OFF)
 # Find OpenMP.
 find_package(OpenMP)
 if (OPENMP_FOUND)
-    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} ${OpenMP_CXX_FLAGS} -fPIC -Wall -Wextra -Werror -pedantic")
+    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} ${OpenMP_CXX_FLAGS} -fPIC -pedantic")
 else()
-    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fPIC -Wall -Wextra -Werror -pedantic")
+    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fPIC -pedantic")
 endif()
 
+if(CMAKE_COMPILER_IS_GNUCXX)
+    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra -Werror")
+endif(CMAKE_COMPILER_IS_GNUCXX)
+
 if (COV)
     set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} --coverage")
 endif()
@@ -39,7 +43,7 @@ endif()
 add_subdirectory(src)
 
 # GASol test dir.
-add_subdirectory(unittest)
+# add_subdirectory(unittest)
 
 # GASol run dir.
 add_subdirectory(example)
