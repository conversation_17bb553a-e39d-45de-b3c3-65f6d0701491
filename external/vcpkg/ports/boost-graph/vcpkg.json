{"$comment": "Automatically generated by scripts/boost/generate-ports.ps1", "name": "boost-graph", "version": "1.87.0", "description": "Boost graph module", "homepage": "https://www.boost.org/libs/graph", "license": "BSL-1.0", "dependencies": [{"name": "boost-algorithm", "version>=": "1.87.0"}, {"name": "boost-any", "version>=": "1.87.0"}, {"name": "boost-array", "version>=": "1.87.0"}, {"name": "boost-assert", "version>=": "1.87.0"}, {"name": "boost-bimap", "version>=": "1.87.0"}, {"name": "boost-bind", "version>=": "1.87.0"}, {"name": "boost-cmake", "version>=": "1.87.0"}, {"name": "boost-concept-check", "version>=": "1.87.0"}, {"name": "boost-config", "version>=": "1.87.0"}, {"name": "boost-container-hash", "version>=": "1.87.0"}, {"name": "boost-conversion", "version>=": "1.87.0"}, {"name": "boost-core", "version>=": "1.87.0"}, {"name": "boost-detail", "version>=": "1.87.0"}, {"name": "boost-foreach", "version>=": "1.87.0"}, {"name": "boost-function", "version>=": "1.87.0"}, {"name": "boost-headers", "version>=": "1.87.0"}, {"name": "boost-integer", "version>=": "1.87.0"}, {"name": "boost-iterator", "version>=": "1.87.0"}, {"name": "boost-lexical-cast", "version>=": "1.87.0"}, {"name": "boost-math", "version>=": "1.87.0"}, {"name": "boost-move", "version>=": "1.87.0"}, {"name": "boost-mpl", "version>=": "1.87.0"}, {"name": "boost-multi-index", "version>=": "1.87.0"}, {"name": "boost-optional", "version>=": "1.87.0"}, {"name": "boost-parameter", "version>=": "1.87.0"}, {"name": "boost-preprocessor", "version>=": "1.87.0"}, {"name": "boost-property-map", "version>=": "1.87.0"}, {"name": "boost-property-tree", "version>=": "1.87.0"}, {"name": "boost-random", "version>=": "1.87.0"}, {"name": "boost-range", "version>=": "1.87.0"}, {"name": "boost-regex", "version>=": "1.87.0"}, {"name": "boost-serialization", "version>=": "1.87.0"}, {"name": "boost-smart-ptr", "version>=": "1.87.0"}, {"name": "boost-spirit", "version>=": "1.87.0"}, {"name": "boost-static-assert", "version>=": "1.87.0"}, {"name": "boost-throw-exception", "version>=": "1.87.0"}, {"name": "boost-tti", "version>=": "1.87.0"}, {"name": "boost-tuple", "version>=": "1.87.0"}, {"name": "boost-type-traits", "version>=": "1.87.0"}, {"name": "boost-typeof", "version>=": "1.87.0"}, {"name": "boost-unordered", "version>=": "1.87.0"}, {"name": "boost-utility", "version>=": "1.87.0"}, {"name": "boost-xpressive", "version>=": "1.87.0"}]}