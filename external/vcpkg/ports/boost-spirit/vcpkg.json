{"$comment": "Automatically generated by scripts/boost/generate-ports.ps1", "name": "boost-spirit", "version": "1.87.0", "description": "Boost spirit module", "homepage": "https://www.boost.org/libs/spirit", "license": "BSL-1.0", "dependencies": [{"name": "boost-array", "version>=": "1.87.0"}, {"name": "boost-assert", "version>=": "1.87.0"}, {"name": "boost-cmake", "version>=": "1.87.0"}, {"name": "boost-config", "version>=": "1.87.0"}, {"name": "boost-core", "version>=": "1.87.0"}, {"name": "boost-endian", "version>=": "1.87.0"}, {"name": "boost-function", "version>=": "1.87.0"}, {"name": "boost-function-types", "version>=": "1.87.0"}, {"name": "boost-fusion", "version>=": "1.87.0"}, {"name": "boost-headers", "version>=": "1.87.0"}, {"name": "boost-integer", "version>=": "1.87.0"}, {"name": "boost-io", "version>=": "1.87.0"}, {"name": "boost-iterator", "version>=": "1.87.0"}, {"name": "boost-move", "version>=": "1.87.0"}, {"name": "boost-mpl", "version>=": "1.87.0"}, {"name": "boost-optional", "version>=": "1.87.0"}, {"name": "boost-phoenix", "version>=": "1.87.0"}, {"name": "boost-pool", "version>=": "1.87.0"}, {"name": "boost-preprocessor", "version>=": "1.87.0"}, {"name": "boost-proto", "version>=": "1.87.0"}, {"name": "boost-range", "version>=": "1.87.0"}, {"name": "boost-regex", "version>=": "1.87.0"}, {"name": "boost-smart-ptr", "version>=": "1.87.0"}, {"name": "boost-static-assert", "version>=": "1.87.0"}, {"name": "boost-thread", "version>=": "1.87.0"}, {"name": "boost-throw-exception", "version>=": "1.87.0"}, {"name": "boost-type-traits", "version>=": "1.87.0"}, {"name": "boost-typeof", "version>=": "1.87.0"}, {"name": "boost-unordered", "version>=": "1.87.0"}, {"name": "boost-utility", "version>=": "1.87.0"}, {"name": "boost-variant", "version>=": "1.87.0"}]}