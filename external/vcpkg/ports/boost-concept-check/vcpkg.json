{"$comment": "Automatically generated by scripts/boost/generate-ports.ps1", "name": "boost-concept-check", "version": "1.87.0", "description": "Boost concept_check module", "homepage": "https://www.boost.org/libs/concept_check", "license": "BSL-1.0", "dependencies": [{"name": "boost-cmake", "version>=": "1.87.0"}, {"name": "boost-config", "version>=": "1.87.0"}, {"name": "boost-headers", "version>=": "1.87.0"}, {"name": "boost-preprocessor", "version>=": "1.87.0"}, {"name": "boost-static-assert", "version>=": "1.87.0"}, {"name": "boost-type-traits", "version>=": "1.87.0"}]}