function(copy_from_windows_sdk WINDOWS_SDK)
    if (WINDOWS_SDK MATCHES "10.")
        set(LIBGLFILEPATH "$ENV{WindowsSdkDir}Lib/${WINDOWS_SDK}/um/${TRIPLET_SYSTEM_ARCH}/OpenGL32.Lib")
        set(LIBGLUFILEPATH "$ENV{WindowsSdkDir}Lib/${WINDOWS_SDK}/um/${TRIPLET_SYSTEM_ARCH}/GlU32.Lib")
        set(HEADERSPATH "$ENV{WindowsSdkDir}Include/${WINDOWS_SDK}/um")
        set(COPYRIGHT "See https://developer.microsoft.com/windows/downloads/windows-10-sdk for the Windows 10 SDK license.")
    elseif(WINDOWS_SDK MATCHES "8.")
        set(LIBGLFILEPATH "$ENV{WindowsSdkDir}Lib/winv6.3/um/${TRIPLET_SYSTEM_ARCH}/OpenGL32.Lib")
        set(LIBGLUFILEPATH "$ENV{WindowsSdkDir}Lib/winv6.3/um/${TRIPLET_SYSTEM_ARCH}/GlU32.Lib")
        set(HEADERSPATH "$ENV{WindowsSdkDir}Include/um")
        set(COPYRIGHT "See https://developer.microsoft.com/windows/downloads/windows-8-1-sdk for the Windows 8.1 SDK license.")
    else()
        message(FATAL_ERROR "Portfile not yet configured for Windows SDK with version: ${WINDOWS_SDK}")
    endif()

    if (NOT EXISTS "${LIBGLFILEPATH}")
        file(TO_NATIVE_PATH "${LIBGLFILEPATH}" DISPLAY)
        message(FATAL_ERROR "Cannot find Windows ${WINDOWS_SDK} SDK. File does not exist: ${DISPLAY}")
    endif()

    if (NOT EXISTS "${LIBGLUFILEPATH}")
        file(TO_NATIVE_PATH "${LIBGLUFILEPATH}" DISPLAY)
        message(FATAL_ERROR "Cannot find Windows ${WINDOWS_SDK} SDK. File does not exist: ${DISPLAY}")
    endif()

    file(INSTALL "${HEADERSPATH}/GL/gl.h" DESTINATION "${CURRENT_PACKAGES_DIR}/include/GL")
    file(INSTALL "${HEADERSPATH}/GL/glu.h" DESTINATION "${CURRENT_PACKAGES_DIR}/include/GL")
    file(INSTALL "${LIBGLFILEPATH}" DESTINATION "${CURRENT_PACKAGES_DIR}/lib")
    file(INSTALL "${LIBGLUFILEPATH}" DESTINATION "${CURRENT_PACKAGES_DIR}/lib")
    if (NOT VCPKG_BUILD_TYPE)
        file(INSTALL "${LIBGLFILEPATH}" DESTINATION "${CURRENT_PACKAGES_DIR}/debug/lib")
        file(INSTALL "${LIBGLUFILEPATH}" DESTINATION "${CURRENT_PACKAGES_DIR}/debug/lib")
    endif()

    file(MAKE_DIRECTORY "${CURRENT_PACKAGES_DIR}/share/${PORT}")
    file(WRITE "${CURRENT_PACKAGES_DIR}/share/${PORT}/copyright" "${COPYRIGHT}")
endfunction()

if(VCPKG_TARGET_IS_WINDOWS)
    if(VCPKG_TARGET_IS_MINGW)
        set(VCPKG_POLICY_EMPTY_PACKAGE enabled)
        set(WINDOWS_SDK_SEMVER "10.0.0")
        set(WINDOWS_GL_CFLAGS "")
    else()
        vcpkg_get_windows_sdk(WINDOWS_SDK)
        copy_from_windows_sdk("${WINDOWS_SDK}")
        string(REGEX MATCH "^([0-9]+)\\.([0-9]+)\\.([0-9]+)" WINDOWS_SDK_SEMVER "${WINDOWS_SDK}")
        set(WINDOWS_GL_CFLAGS "-I\${includedir}")
    endif()
    configure_file("${CMAKE_CURRENT_LIST_DIR}/opengl.pc.in" "${CURRENT_PACKAGES_DIR}/lib/pkgconfig/opengl.pc" @ONLY)
    configure_file("${CMAKE_CURRENT_LIST_DIR}/glu.pc.in" "${CURRENT_PACKAGES_DIR}/lib/pkgconfig/glu.pc" @ONLY)
    if(NOT VCPKG_BUILD_TYPE)
        file(COPY "${CURRENT_PACKAGES_DIR}/lib/pkgconfig/opengl.pc" DESTINATION "${CURRENT_PACKAGES_DIR}/debug/lib/pkgconfig")
        file(COPY "${CURRENT_PACKAGES_DIR}/lib/pkgconfig/glu.pc" DESTINATION "${CURRENT_PACKAGES_DIR}/debug/lib/pkgconfig")
    endif()
    vcpkg_fixup_pkgconfig()
else()
    set(VCPKG_POLICY_EMPTY_PACKAGE enabled)
endif()

file(INSTALL "${CMAKE_CURRENT_LIST_DIR}/usage" DESTINATION "${CURRENT_PACKAGES_DIR}/share/${PORT}")
