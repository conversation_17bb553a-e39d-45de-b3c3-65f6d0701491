diff --git a/CMakeLists.txt b/CMakeLists.txt
index 6c1aafc..97854a6 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -58,11 +58,10 @@ else ()
 endif ()
 
 check_include_file_cxx("workflow/Workflow.h" WORKFLOW_INSTALLED)
-if (NOT WORKFLOW_INSTALLED)
+if (0)
 	if (NOT EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/workflow/workflow-config.cmake.in")
 		message( FATAL_ERROR "\nWorkflow" ${THIRD_PARTY_FATAL_MESSAGE} )
 	endif ()
-else ()
 	if (EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/workflow/workflow-config.cmake.in")
 		message("Workflow third_party FOUND. Use for source code dependencies.")
 		set(WORKFLOW_INSTALLED 0)
diff --git a/src/CMakeLists.txt b/src/CMakeLists.txt
index 3b47ee6..15c8009 100644
--- a/src/CMakeLists.txt
+++ b/src/CMakeLists.txt
@@ -16,7 +16,7 @@ set(protobuf_MODULE_COMPATIBLE ON CACHE BOOL "")
 if (WIN32)
 	find_package(Protobuf REQUIRED CONFIG)
 else ()
-	find_package(Protobuf 3.5.0 REQUIRED)
+	find_package(Protobuf CONFIG REQUIRED)
 endif ()
 
 if (WIN32)
@@ -59,8 +59,8 @@ if (WIN32)
 	set(CMAKE_C_FLAGS   "${CMAKE_C_FLAGS}   /MP /wd4200")
 	set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /MP /wd4200 /Zc:__cplusplus /std:c++14")
 else ()
-	set(CMAKE_C_FLAGS   "${CMAKE_C_FLAGS}   -Wall -fPIC -pipe -std=gnu90")
-	set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -fPIC -pipe -std=c++11 -fno-exceptions")
+	set(CMAKE_C_FLAGS   "${CMAKE_C_FLAGS}   -Wall -fPIC -pipe")
+	set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -fPIC -pipe -fno-exceptions")
 endif ()
 
 add_subdirectory(generator)
@@ -100,6 +100,7 @@ if (WIN32)
 	)
 
 	add_dependencies(${PROJECT_NAME} LINK_HEADERS)
+	target_link_libraries(${PROJECT_NAME} PUBLIC protobuf::libprotobuf)
 
 	target_compile_definitions(
 		${PROJECT_NAME} PRIVATE
@@ -118,7 +119,6 @@ if (WIN32)
 else ()
 	set(STATIC_LIB_NAME ${PROJECT_NAME}-static)
 	set(SHARED_LIB_NAME ${PROJECT_NAME}-shared)
-	get_filename_component(Protobuf_LIB_DIR ${Protobuf_LIBRARY} DIRECTORY)
 	link_directories(${OPENSSL_LINK_DIR} ${WORKFLOW_LIB_DIR} ${Protobuf_LIB_DIR})
 
 	add_library(
@@ -143,8 +143,10 @@ else ()
 		$<TARGET_OBJECTS:http>
 	)
 
+	target_link_libraries(${SHARED_LIB_NAME} PUBLIC protobuf::libprotobuf)
+	target_link_libraries(${STATIC_LIB_NAME} PUBLIC protobuf::libprotobuf)
 	if (APPLE)
-		target_link_libraries(${SHARED_LIB_NAME}
+		target_link_libraries(${SHARED_LIB_NAME} PUBLIC
 							  OpenSSL::SSL
 							  OpenSSL::Crypto
 							  pthread
diff --git a/src/compress/CMakeLists.txt b/src/compress/CMakeLists.txt
index 818b505..c0d1dde 100644
--- a/src/compress/CMakeLists.txt
+++ b/src/compress/CMakeLists.txt
@@ -51,3 +51,4 @@ else ()
 	)
 endif()
 
+target_link_libraries(${PROJECT_NAME} PUBLIC protobuf::libprotobuf)
diff --git a/src/generator/CMakeLists.txt b/src/generator/CMakeLists.txt
index c2d72d7..674c5d0 100644
--- a/src/generator/CMakeLists.txt
+++ b/src/generator/CMakeLists.txt
@@ -8,6 +8,7 @@ set(SRC
 )
 
 add_executable(${PROJECT_NAME} ${SRC})
+target_link_libraries(${PROJECT_NAME} PUBLIC protobuf::libprotobuf)
 if (WIN32)
 	target_compile_definitions(
 		${PROJECT_NAME} PRIVATE
diff --git a/src/http/CMakeLists.txt b/src/http/CMakeLists.txt
index 64c0377..1cf1dfc 100644
--- a/src/http/CMakeLists.txt
+++ b/src/http/CMakeLists.txt
@@ -11,6 +11,7 @@ set(SRC
 )
 
 add_library(${PROJECT_NAME} OBJECT ${SRC})
+target_link_libraries(${PROJECT_NAME} PUBLIC protobuf::libprotobuf)
 if (WIN32)
 	target_compile_definitions(
 		${PROJECT_NAME} PRIVATE
diff --git a/src/message/CMakeLists.txt b/src/message/CMakeLists.txt
index 952607f..f947540 100644
--- a/src/message/CMakeLists.txt
+++ b/src/message/CMakeLists.txt
@@ -15,6 +15,7 @@ set(SRC
 )
 
 add_library(${PROJECT_NAME} OBJECT ${SRC})
+target_link_libraries(${PROJECT_NAME} PUBLIC protobuf::libprotobuf)
 if (WIN32)
 	target_compile_definitions(
 		${PROJECT_NAME} PRIVATE
diff --git a/src/module/CMakeLists.txt b/src/module/CMakeLists.txt
index f0041a4..a9d9a65 100644
--- a/src/module/CMakeLists.txt
+++ b/src/module/CMakeLists.txt
@@ -22,6 +22,7 @@ set(SRC
 )
 
 add_library(${PROJECT_NAME} OBJECT ${SRC})
+target_link_libraries(${PROJECT_NAME} PUBLIC protobuf::libprotobuf)
 
 if (WIN32)
 	target_compile_definitions(
diff --git a/src/thrift/CMakeLists.txt b/src/thrift/CMakeLists.txt
index 1d1d61c..dc66723 100644
--- a/src/thrift/CMakeLists.txt
+++ b/src/thrift/CMakeLists.txt
@@ -7,4 +7,4 @@ set(SRC
 )
 
 add_library(${PROJECT_NAME} OBJECT ${SRC})
-
+target_link_libraries(${PROJECT_NAME} PUBLIC protobuf::libprotobuf)
diff --git a/srpc-config.cmake.in b/srpc-config.cmake.in
index 54b4323..1cdb97f 100644
--- a/srpc-config.cmake.in
+++ b/srpc-config.cmake.in
@@ -3,7 +3,6 @@
 set(SRPC_VERSION "@srpc_VERSION@")
 set_and_check(SRPC_INCLUDE_DIR "@PACKAGE_CONFIG_INC_DIR@")
 set_and_check(SRPC_LIB_DIR "@PACKAGE_CONFIG_LIB_DIR@")
-set_and_check(SRPC_BIN_DIR "@PACKAGE_CONFIG_BIN_DIR@")
 
 if (EXISTS "${CMAKE_CURRENT_LIST_DIR}/srpc-targets.cmake")
     include ("${CMAKE_CURRENT_LIST_DIR}/srpc-targets.cmake")
