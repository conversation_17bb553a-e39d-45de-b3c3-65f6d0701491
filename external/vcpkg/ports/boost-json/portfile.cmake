# Automatically generated by scripts/boost/generate-ports.ps1

vcpkg_download_distfile(ARM32_PATCH
    URLS https://github.com/boostorg/json/commit/57d42f07158b8ea4a713378c90d22b28ca4787f8.patch?full_index=1
    FILENAME boost-json-arm32-57d42f07158b8ea4a713378c90d22b28ca4787f8.patch
    SHA512 d74a43dfc08fb34ba270a4d7de00a4307728567fbb2949f40f649c0e1c63b690d48aacfbc888e4b8075ff55262048700ab6ef669aa4277d6e9bebbbbd5e9473f
)
vcpkg_from_github(
    OUT_SOURCE_PATH SOURCE_PATH
    REPO boostorg/json
    REF boost-${VERSION}
    SHA512 9eea8a9191afab5ca0842232f579a90c9ff9dc994a66669e7fc7b4171fe0bf3104cbcf1be7a11df3da9f404842314327673fcb693cefae56f3f734975f90d71b
    HEAD_REF master
    PATCHES
        ${ARM32_PATCH}
)

set(FEATURE_OPTIONS "")
boost_configure_and_install(
    SOURCE_PATH "${SOURCE_PATH}"
    OPTIONS ${FEATURE_OPTIONS}
)
