{"$comment": "Automatically generated by scripts/boost/generate-ports.ps1", "name": "boost-json", "version": "1.87.0", "port-version": 1, "description": "Boost json module", "homepage": "https://www.boost.org/libs/json", "license": "BSL-1.0", "dependencies": [{"name": "boost-align", "version>=": "1.87.0"}, {"name": "boost-assert", "version>=": "1.87.0"}, {"name": "boost-cmake", "version>=": "1.87.0"}, {"name": "boost-config", "version>=": "1.87.0"}, {"name": "boost-container", "version>=": "1.87.0"}, {"name": "boost-container-hash", "version>=": "1.87.0"}, {"name": "boost-core", "version>=": "1.87.0"}, {"name": "boost-describe", "version>=": "1.87.0"}, {"name": "boost-endian", "version>=": "1.87.0"}, {"name": "boost-headers", "version>=": "1.87.0"}, {"name": "boost-mp11", "version>=": "1.87.0"}, {"name": "boost-static-assert", "version>=": "1.87.0"}, {"name": "boost-system", "version>=": "1.87.0"}, {"name": "boost-throw-exception", "version>=": "1.87.0"}]}