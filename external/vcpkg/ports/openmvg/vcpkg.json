{"name": "openmvg", "version": "2.1", "port-version": 1, "description": "open Multiple View Geometry library. Basis for 3D computer vision and Structure from Motion.", "license": "MPL-2.0-no-copyleft-exception", "supports": "(x86 | x64 | arm64) & !xbox", "dependencies": ["cereal", {"name": "ceres", "features": ["suitesparse"]}, "coin-or-clp", "coin-or-osi", "coinutils", "eigen3", "flann", "libjpeg-turbo", "liblemon", "libpng", {"name": "tiff", "default-features": false}, {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}, "vlfeat", "zlib"], "features": {"opencv": {"description": "opencv support for openmvg", "dependencies": [{"name": "opencv", "features": ["contrib"]}]}, "openmp": {"description": "openmp support for openmvg"}, "software": {"description": "build openMVG tools", "dependencies": ["qt5-base", "qt5-svg"]}}}