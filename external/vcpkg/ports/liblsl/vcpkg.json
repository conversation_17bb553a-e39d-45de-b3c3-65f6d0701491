{"name": "liblsl", "version": "1.16.2", "description": "C++ lsl library for multi-modal time-synched data transmission over the local network", "homepage": "https://github.com/sccn/liblsl", "license": "MIT", "supports": "!uwp", "dependencies": ["asio", "boost-atomic", "boost-bind", "boost-chrono", "boost-config", "boost-endian", "boost-functional", "boost-integer", "boost-lexical-cast", "boost-math", "boost-serialization", "boost-smart-ptr", "boost-thread", "boost-uuid", "pugixml", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}]}