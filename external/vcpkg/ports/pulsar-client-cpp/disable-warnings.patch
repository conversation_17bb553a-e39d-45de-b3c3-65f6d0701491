diff --git a/CMakeLists.txt b/CMakeLists.txt
index b004653..4b7abd9 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -88,7 +88,6 @@ elseif (CMAKE_CXX_COMPILER_ID STREQUAL "Intel")
     # ?? Don't have this to test with
 else() # GCC or Clang are mostly compatible:
     # Turn on warnings and enable warnings-as-errors:
-    add_compile_options(-Wall -Wformat-security -Wvla -Werror) 
     # Turn off certain warnings that are too much pain for too little gain:
     add_compile_options(-Wno-sign-compare -Wno-deprecated-declarations -Wno-error=cpp)
     if (CMAKE_SYSTEM_PROCESSOR STREQUAL "x86_64" OR APPLE)
