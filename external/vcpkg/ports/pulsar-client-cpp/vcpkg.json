{"name": "pulsar-client-cpp", "version": "3.7.0", "description": "The Apache Pulsar C++ library", "homepage": "https://github.com/apache/pulsar-client-cpp", "license": "Apache-2.0", "supports": "!(arm & windows) & !android", "dependencies": ["asio", "boost-accumulators", "boost-algorithm", "boost-any", "boost-circular-buffer", "boost-date-time", "boost-format", "boost-predef", "boost-property-tree", "boost-random", "boost-serialization", "boost-xpressive", {"name": "curl", "default-features": false, "features": ["openssl"]}, {"name": "dlfcn-win32", "platform": "windows"}, "openssl", {"name": "protobuf", "version>=": "3.21.12"}, "snappy", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}, "zlib", "zstd"]}