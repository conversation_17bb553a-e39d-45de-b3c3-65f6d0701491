diff --git a/bus/CMakeLists.txt b/bus/CMakeLists.txt
index e464f60..fc991f4 100644
--- a/bus/CMakeLists.txt
+++ b/bus/CMakeLists.txt
@@ -113,7 +113,6 @@ if(NOT WIN32)
     install(FILES ${CMAKE_CURRENT_BINARY_DIR}/legacy-config/system.conf DESTINATION ${CMAKE_INSTALL_FULL_SYSCONFDIR}/dbus-1)
     install(DIRECTORY DESTINATION ${CMAKE_INSTALL_DATADIR}/dbus-1/system.d)
     install(DIRECTORY DESTINATION ${CMAKE_INSTALL_DATADIR}/dbus-1/system-services)
-    install(DIRECTORY DESTINATION ${CMAKE_INSTALL_FULL_LOCALSTATEDIR}/run/dbus)
 endif()
 
 if(DBUS_SERVICE)
