diff --git a/CMakeLists.txt b/CMakeLists.txt
index caef738..b878f42 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -724,11 +724,11 @@ add_custom_target(help-options
 #
 if(DBUS_ENABLE_PKGCONFIG)
     set(PLATFORM_LIBS pthread ${LIBRT})
-    if(PKG_CONFIG_FOUND)
-        # convert lists of link libraries into -lstdc++ -lm etc..
-        foreach(LIB ${CMAKE_C_IMPLICIT_LINK_LIBRARIES} ${PLATFORM_LIBS})
-            set(LIBDBUS_LIBS "${LIBDBUS_LIBS} -l${LIB}")
-        endforeach()
+    if(1)
+        set(LIBDBUS_LIBS "${CMAKE_THREAD_LIBS_INIT}")
+        if(LIBRT)
+            string(APPEND LIBDBUS_LIBS " -lrt")
+        endif()
         set(original_prefix "${CMAKE_INSTALL_PREFIX}")
         if(DBUS_RELOCATABLE)
             set(pkgconfig_prefix "\${pcfiledir}/../..")
