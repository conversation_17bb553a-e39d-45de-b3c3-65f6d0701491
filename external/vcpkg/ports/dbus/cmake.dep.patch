diff --git a/tools/CMakeLists.txt b/tools/CMakeLists.txt
index 8cde1ffe0..d4d09f223 100644
--- a/tools/CMakeLists.txt	
+++ b/tools/CMakeLists.txt
@@ -91,7 +91,9 @@ endif()
 add_executable(dbus-launch ${dbus_launch_SOURCES})
 target_link_libraries(dbus-launch ${DBUS_LIBRARIES})
 if(DBUS_BUILD_X11)
-    target_link_libraries(dbus-launch ${X11_LIBRARIES} )
+    find_package(Threads REQUIRED)
+    target_link_libraries(dbus-launch ${X11_LIBRARIES} ${X11_xcb_LIB} ${X11_Xau_LIB} ${X11_Xdmcp_LIB} Threads::Threads)
+    target_include_directories(dbus-launch PRIVATE ${X11_INCLUDE_DIR})
 endif()
 install(TARGETS dbus-launch ${INSTALL_TARGETS_DEFAULT_ARGS})
 
