{"name": "dbus", "version": "1.16.2", "port-version": 1, "description": "D-Bus specification and reference implementation, including libdbus and dbus-daemon", "homepage": "https://gitlab.freedesktop.org/dbus/dbus", "license": "AFL-2.1 OR GPL-2.0-or-later", "supports": "!uwp & !staticcrt & !android & !ios", "dependencies": ["expat", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "default-features": [{"name": "systemd", "platform": "linux"}], "features": {"systemd": {"description": "Build with systemd at_console support", "supports": "linux", "dependencies": [{"name": "libsystemd", "platform": "linux"}]}, "x11": {"description": "Build with X11 autolaunch support", "supports": "!windows", "dependencies": ["libx11"]}}}