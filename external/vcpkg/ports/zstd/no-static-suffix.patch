diff --git a/build/cmake/lib/CMakeLists.txt b/build/cmake/lib/CMakeLists.txt
index 8234060..765f003 100644
--- a/build/cmake/lib/CMakeLists.txt
+++ b/build/cmake/lib/CMakeLists.txt
@@ -118,7 +118,7 @@ endif ()
 
 # With MSVC static library needs to be renamed to avoid conflict with import library
 if (MSVC OR (WIN32 AND CMAKE_CXX_COMPILER_ID STREQUAL "Clang" AND NOT MINGW))
-    set(STATIC_LIBRARY_BASE_NAME zstd_static)
+    set(STATIC_LIBRARY_BASE_NAME zstd)
 else ()
     set(STATIC_LIBRARY_BASE_NAME zstd)
 endif ()
