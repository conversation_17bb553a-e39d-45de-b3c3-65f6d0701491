diff --git a/build/cmake/CMakeModules/AddZstdCompilationFlags.cmake b/build/cmake/CMakeModules/AddZstdCompilationFlags.cmake
index 5f381c6..f1e9b32 100644
--- a/build/cmake/CMakeModules/AddZstdCompilationFlags.cmake
+++ b/build/cmake/CMakeModules/AddZstdCompilationFlags.cmake
@@ -79,10 +79,22 @@ macro(ADD_ZSTD_COMPILATION_FLAGS)
         endif ()
         # Add noexecstack flags
         # LDFLAGS
-        EnableCompilerFlag("-Wl,-z,noexecstack" false false true)
-        # CFLAGS & CXXFLAGS
-        EnableCompilerFlag("-Qunused-arguments" true true false)
-        EnableCompilerFlag("-Wa,--noexecstack" true true false)
+        if(MSVC)
+            # UNICODE SUPPORT
+            EnableCompilerFlag("/D_UNICODE" true true false)
+            EnableCompilerFlag("/DUNICODE" true true false)
+            # Enable asserts in Debug mode
+            if (CMAKE_BUILD_TYPE MATCHES "Debug")
+                EnableCompilerFlag("/DDEBUGLEVEL=1" true true false)
+            endif ()
+        else()
+        if(NOT EMSCRIPTEN)
+            EnableCompilerFlag("-z noexecstack" false false true)
+        endif()
+            # CFLAGS & CXXFLAGS
+            EnableCompilerFlag("-Qunused-arguments" true true false)
+            EnableCompilerFlag("-Wa,--noexecstack" true true false)
+        endif()
         # NOTE: Using 3 nested ifs because the variables are sometimes
         # empty if the condition is false, and sometimes equal to false.
         # This implicitly converts them to truthy values. There may be
