diff --git a/u2f-server/core.c b/u2f-server/core.c
index 294c94b..746932b 100644
--- a/u2f-server/core.c
+++ b/u2f-server/core.c
@@ -211,7 +211,7 @@ u2fs_rc u2fs_set_keyHandle(u2fs_ctx_t * ctx, const char *keyHandle)
     ctx->keyHandle = NULL;
   }
 
-  ctx->keyHandle = strndup(keyHandle, strlen(keyHandle));
+  ctx->keyHandle = strdup(keyHandle);
 
   if (ctx->keyHandle == NULL)
     return U2FS_MEMORY_ERROR;
@@ -726,7 +726,7 @@ static u2fs_rc decode_clientData(const char *clientData, char **output)
     fprintf(stderr, "clientData: %s\n", data);
   }
 
-  *output = strndup(data, strlen(data));
+  *output = strdup(data);
 
   free(data);
   data = NULL;
@@ -870,7 +870,7 @@ u2fs_rc u2fs_registration_verify(u2fs_ctx_t * ctx, const char *response,
     goto failure;
 
   u2fs_EC_KEY_t *key_ptr;
-  (*output)->keyHandle = strndup(buf, strlen(buf));
+  (*output)->keyHandle = strdup(buf);
 
   rc = decode_user_key(user_public_key, &key_ptr);
   if (rc != U2FS_OK)
