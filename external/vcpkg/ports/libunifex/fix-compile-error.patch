diff --git a/cmake/unifex_env.cmake b/cmake/unifex_env.cmake
index b26aed1..051a7af 100644
--- a/cmake/unifex_env.cmake
+++ b/cmake/unifex_env.cmake
@@ -21,8 +21,8 @@ endif()
 
 if (UNIFEX_CXX_COMPILER_MSVC)
     # warning level 3 and all warnings as errors
-    add_compile_options(/W3 /WX)
+    add_compile_options(/W3 /WX /Zc:externConstexpr /EHsc)
 else()
     # lots of warnings and all warnings as errors
-    add_compile_options(-Wall -Wextra -pedantic -Werror)
+    add_compile_options(-Wall -Wextra -pedantic)
 endif()
