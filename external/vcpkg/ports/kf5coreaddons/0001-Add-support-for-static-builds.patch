diff --git a/KF5CoreAddonsConfig.cmake.in b/KF5CoreAddonsConfig.cmake.in
index 711b8d41..2523da18 100644
--- a/KF5CoreAddonsConfig.cmake.in
+++ b/KF5CoreAddonsConfig.cmake.in
@@ -3,6 +3,24 @@
 include(CMakeFindDependencyMacro)
 find_dependency(Qt@QT_MAJOR_VERSION@Core @REQUIRED_QT_VERSION@)
 
+if(NOT @BUILD_SHARED_LIBS@)
+    if(NOT WIN32)
+        find_dependency(Threads)
+    endif()
+
+    if(@Inotify_FOUND@)
+        find_package(Inotify)
+    endif()
+
+    if(@Procstat_FOUND@)
+        find_dependency(Procstat)
+    endif()
+
+    if(@HAVE_QTDBUS@)
+        find_dependency(Qt@QT_MAJOR_VERSION@DBus @REQUIRED_QT_VERSION@)
+    endif()
+endif()
+
 @PACKAGE_SETUP_AUTOMOC_VARIABLES@
 
 if(CMAKE_CROSSCOMPILING AND KF5_HOST_TOOLING)
@@ -23,6 +41,7 @@ else()
         set_target_properties(KF5::desktoptojson PROPERTIES IMPORTED_LOCATION ${DESKTOPTOJSON_EXECUTABLE})
     endif()
 endif()
+
 include("${CMAKE_CURRENT_LIST_DIR}/KF5CoreAddonsTargets.cmake")
 include("${CMAKE_CURRENT_LIST_DIR}/KF5CoreAddonsMacros.cmake")
 @PACKAGE_INCLUDE_QCHTARGETS@
