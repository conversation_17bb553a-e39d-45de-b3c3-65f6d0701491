{"name": "sdl3-ttf", "version": "3.1.0", "description": "A library for rendering TrueType fonts with SDL", "homepage": "https://www.libsdl.org/projects/SDL_ttf/", "license": "<PERSON><PERSON><PERSON>", "dependencies": [{"name": "freetype", "default-features": false}, {"name": "sdl3", "default-features": false}, {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"harfbuzz": {"description": "Enable HarfBuzz support", "dependencies": ["harfbuzz"]}, "svg": {"description": "Enable plutosvg for color emoji support", "dependencies": ["plutosvg"]}}}