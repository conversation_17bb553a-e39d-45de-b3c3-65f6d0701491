{"name": "libplist", "version-date": "2023-06-15", "port-version": 1, "description": "A library to handle Apple Property List format in binary or XML", "homepage": "https://libimobiledevice.org/", "license": "LGPL-2.1-or-later", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"tools": {"description": "build command line tool", "supports": "!android & !ios & !xbox"}}}