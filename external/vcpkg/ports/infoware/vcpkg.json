{"name": "infoware", "version-date": "2023-04-12", "description": "C++ Library for pulling system and hardware information, without hitting the command line.", "homepage": "https://github.com/ThePhD/infoware", "license": "CC0-1.0", "supports": "!android & !uwp & !(windows & arm)", "dependencies": [{"name": "infoware", "host": true}, {"name": "pciids", "platform": "native"}, {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"d3d": {"description": ["Use Direct3D for GPU detection.", "This choice has priority over opencl and opengl."], "supports": "windows"}, "opencl": {"description": ["Use OpenCL for GPU detection.", "This choice has priority over opengl."], "dependencies": ["opencl"]}, "opengl": {"description": "Use OpenGL for GPU detection.", "supports": "!osx & !ios", "dependencies": ["opengl"]}, "x11": {"description": "Use X11 for display detection.", "supports": "!windows"}}}