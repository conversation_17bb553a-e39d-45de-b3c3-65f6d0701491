{"name": "glfw3", "version": "3.4", "port-version": 1, "description": "GLFW is a free, Open Source, multi-platform library for OpenGL, OpenGL ES and Vulkan application development. It provides a simple, platform-independent API for creating windows, contexts and surfaces, reading input, handling events, etc.", "homepage": "https://github.com/glfw/glfw", "license": "<PERSON><PERSON><PERSON>", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"wayland": {"description": "Use Wayland display server", "supports": "linux", "dependencies": ["wayland"]}}}