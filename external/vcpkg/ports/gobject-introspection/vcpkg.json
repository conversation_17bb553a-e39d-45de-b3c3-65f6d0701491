{"name": "gobject-introspection", "version": "1.82.0", "description": "A middleware layer between C libraries (using GObject) and language bindings.", "homepage": "https://gi.readthedocs.io/en/latest/", "license": null, "supports": "!static & (native | (windows & x86))", "dependencies": [{"name": "cairo", "default-features": false, "features": ["gobject"]}, "glib", {"name": "glib", "host": true}, {"name": "gobject-introspection", "host": true, "platform": "!(windows & x86)"}, "libffi", "python3", {"name": "python3", "host": true, "platform": "!(windows & x86)"}, {"name": "vcpkg-get-python-packages", "host": true}, {"name": "vcpkg-tool-meson", "host": true}]}