--- a/gir/meson.build	2021-09-17 23:02:42.502052000 +0700
+++ b/gir/meson.build	2022-01-05 02:31:25.960457800 +0700
@@ -42,7 +42,7 @@
 install_data(installed_gir_files, install_dir: girdir)
 
 scanner_command = [
-  find_program('g-ir-scanner', native: true),
+  find_program('g-ir-scanner'),
   '--output=@OUTPUT@',
   '--no-libtool',
   '--quiet',
@@ -495,6 +495,7 @@
 )
 
 typelibs = []
+gircompiler = find_program('g-ir-compiler')
 if get_option('gi_cross_binary_wrapper') != ''
   gircompiler_command = [get_option('gi_cross_binary_wrapper'), gircompiler.full_path(), ]
 else
@@ -510,7 +511,7 @@
   typelibs += custom_target(
     input: gir.get('file'),
     output: '@BASENAME@.typelib',
-    depends: [gobject_gir, gircompiler, ],
+    depends: [gobject_gir, ],
     command: gircompiler_command,
     install: true,
     install_dir: typelibdir,
--- a/tools/meson.build	2021-09-17 23:02:42.601052000 +0700
+++ b/tools/meson.build	2022-01-05 02:36:06.103763400 +0700
@@ -64,7 +64,9 @@
   install: true,
   c_args: custom_c_args,
 )
-meson.override_find_program('g-ir-compiler', gircompiler)
+if not get_option('gi_cross_use_prebuilt_gi')
+  meson.override_find_program('g-ir-compiler', gircompiler)
+endif
 
 girgenerate = executable('g-ir-generate', 'generate.c',
   dependencies: [
@@ -74,14 +76,18 @@
   install: true,
   c_args: custom_c_args,
 )
-meson.override_find_program('g-ir-generate', girgenerate)
+if not get_option('gi_cross_use_prebuilt_gi')
+  meson.override_find_program('g-ir-generate', girgenerate)
+endif
 
 girinspect = executable('g-ir-inspect', 'g-ir-inspect.c',
   dependencies: [
     girepo_dep,
     gio_dep,
   ],
   install: true,
   c_args: custom_c_args,
 )
-meson.override_find_program('g-ir-inspect', girinspect)
+if not get_option('gi_cross_use_prebuilt_gi')
+  meson.override_find_program('g-ir-inspect', girinspect)
+endif
\ No newline at end of file
