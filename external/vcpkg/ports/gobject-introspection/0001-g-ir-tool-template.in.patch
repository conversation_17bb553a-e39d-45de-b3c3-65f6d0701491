--- a/tools/g-ir-tool-template.in	2021-09-17 23:02:42.601052000 +0700
+++ b/tools/g-ir-tool-template.in	2021-12-29 21:41:52.987141900 +0700
@@ -59,7 +59,7 @@
 builtins.__dict__['GIR_DIR'] = gir_dir
 
 # Again, relative paths first so that the installation prefix is relocatable
-pylibdir = os.path.abspath(os.path.join(filedir, '..', 'lib', 'gobject-introspection'))
+pylibdir = os.path.abspath(os.path.join(filedir, '..', '..', 'lib', 'gobject-introspection'))
 
 # EXT_SUFFIX for py3 SO for py2
 py_mod_suffix = sysconfig.get_config_var('EXT_SUFFIX') or sysconfig.get_config_var('SO')
@@ -91,6 +91,11 @@
 
 sys.path.insert(0, pylibdir)
 
+pkg_config_path = os.getenv('PKG_CONFIG_PATH', '')
+if pkg_config_path:
+    pkg_config_path = os.pathsep + pkg_config_path 
+os.environ['PKG_CONFIG_PATH'] = os.path.normpath(os.path.join(pylibdir, '..', 'pkgconfig')) + pkg_config_path
+
 from giscanner.utils import dll_dirs
 dll_dirs = dll_dirs()
 dll_dirs.add_dll_dirs(['gio-2.0'])
