diff --git a/tools/g-ir-tool-template.in b/tools/g-ir-tool-template.in
index 75c35a7..499ed01 100644
--- a/tools/g-ir-tool-template.in
+++ b/tools/g-ir-tool-template.in
@@ -46,7 +46,7 @@ if debug:
 filedir = os.path.dirname(__file__)
 
 # Try using relative paths first so that the installation prefix is relocatable
-datadir = os.path.abspath(os.path.join(filedir, '..', 'share'))
+datadir = os.path.abspath(os.path.join(filedir, '..', '..', 'share'))
 # Fallback to hard-coded paths if the relocatable paths are wrong
 if not os.path.isdir(os.path.join(datadir, 'gir-1.0')):
     datadir = "@datarootdir@"
@@ -53,10 +53,7 @@ builtins.__dict__['DATADIR'] = datadir
 
 builtins.__dict__['DATADIR'] = datadir
 
-gir_dir = os.path.abspath(os.path.join(filedir, '..', '@gir_dir_prefix@', 'gir-1.0'))
-# Fallback to hard-coded paths if the relocatable paths are wrong
-if not os.path.isdir(gir_dir):
-    gir_dir = "@GIR_DIR@"
+gir_dir = datadir
 
 builtins.__dict__['GIR_DIR'] = gir_dir
 
