diff --git a/CMakeLists.txt b/CMakeLists.txt
index 663044e..9121cb3 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -15,8 +15,31 @@ if (CMAKE_CXX_COMPILER_ID MATCHES "GNU")
     add_compile_options(-foptimize-sibling-calls)
 endif()
 
+include(GNUInstallDirs)
+
 add_library(ucoro INTERFACE)
-target_include_directories(ucoro INTERFACE include)
+target_include_directories(ucoro INTERFACE $<BUILD_INTERFACE:${PROJECT_SOURCE_DIR}/include>
+                                           $<INSTALL_INTERFACE:${CMAKE_INSTALL_INCLUDEDIR}>)
+
+option(UCORO_BUILD_TESTING "Build the tests" ON)
+if (UCORO_BUILD_TESTING)
+    enable_testing()
+    add_subdirectory(tests)
+endif()
 
-enable_testing()
-add_subdirectory(tests)
+install(
+    TARGETS ucoro
+    EXPORT ucoroTargets
+    INCLUDES DESTINATION "${CMAKE_INSTALL_INCLUDEDIR}"
+)
+install(
+    DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/include/ucoro
+    DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}
+)
+# generate config.cmake
+install(
+    EXPORT ucoroTargets
+    FILE ucoro-config.cmake
+    NAMESPACE ucoro::
+    DESTINATION "share/ucoro"
+)
