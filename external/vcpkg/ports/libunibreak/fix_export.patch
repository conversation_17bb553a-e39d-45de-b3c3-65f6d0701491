diff --git a/src/eastasianwidthdef.h b/src/eastasianwidthdef.h
index 67a41aa..c081a02 100644
--- a/src/eastasianwidthdef.h
+++ b/src/eastasianwidthdef.h
@@ -26,6 +26,9 @@
 
 #include "unibreakdef.h"
 
+#ifdef __cplusplus
+extern "C" {
+#endif
 /**
  * East Asian Width (ea) class.  This is defined in Unicode Standard
  * Annex 11.
@@ -52,5 +55,8 @@ struct EastAsianWidthProperties
 };
 
 enum EastAsianWidthClass ub_get_char_eaw_class(utf32_t ch);
+#ifdef __cplusplus
+}
+#endif
 
 #endif /* EASTASIANWIDTHDEF_H */
