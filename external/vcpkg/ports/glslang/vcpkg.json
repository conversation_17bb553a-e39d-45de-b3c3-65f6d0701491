{"name": "glslang", "version": "15.1.0", "description": "Khronos-reference front end for GLSL/ESSL, partial front end for HLSL, and a SPIR-V generator.", "homepage": "https://github.com/KhronosGroup/glslang", "license": "Apache-2.0 AND BSD-3-<PERSON>e AND MIT AND GPL-3.0-or-later", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"opt": {"description": "Build with spirv-opt capability", "dependencies": ["spirv-tools"]}, "rtti": {"description": "Build with dynamic typeinfo"}, "tools": {"description": "Build the glslangValidator and spirv-remap binaries", "supports": "!ios"}}}