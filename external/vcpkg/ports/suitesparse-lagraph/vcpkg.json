{"$comment": "Use scripts/update_suitesparse.py to update all SuiteSparse ports", "name": "suitesparse-lagraph", "version-semver": "1.1.4", "port-version": 1, "description": "LAGraph: Library plus test harness for collecting algorithms that use GraphBLAS", "homepage": "https://lagraph.readthedocs.io/en/latest/", "license": "BSD-2-<PERSON><PERSON>", "dependencies": ["suitesparse-graphblas", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"openmp": {"description": "Enable OpenMP support", "dependencies": [{"name": "suitesparse-graphblas", "features": ["openmp"]}]}}}