diff --git a/CMakeLists.txt b/CMakeLists.txt
index 0063385..f1522bc 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -10,6 +10,15 @@ project(onnx_optimizer C CXX)
 
 set(CMAKE_CXX_STANDARD 17)
 
+# Add MSVC RunTime Flag
+function(add_msvc_runtime_flag lib)
+  if(${ONNX_USE_MSVC_STATIC_RUNTIME})
+    target_compile_options(${lib} PRIVATE $<$<NOT:$<CONFIG:Debug>>:/MT> $<$<CONFIG:Debug>:/MTd>)    
+  else()
+    target_compile_options(${lib} PRIVATE $<$<NOT:$<CONFIG:Debug>>:/MD> $<$<CONFIG:Debug>:/MDd>)
+  endif()
+endfunction()
+
 set(CMAKE_POSITION_INDEPENDENT_CODE ON)
 
 include(cmake/utils.cmake)
@@ -23,12 +32,10 @@ option(ONNX_OPT_USE_SYSTEM_PROTOBUF "" OFF)
 if(NOT ONNX_OPT_USE_SYSTEM_PROTOBUF)
   option(protobuf_BUILD_TESTS "" OFF)
   option(protobuf_MSVC_STATIC_RUNTIME "" ${ONNX_USE_MSVC_STATIC_RUNTIME})
-  add_subdirectory_if_no_target(${PROJECT_SOURCE_DIR}/third_party/protobuf/cmake libprotobuf)
 endif()
 
-
-set(ONNX_ROOT ${PROJECT_SOURCE_DIR}/third_party/onnx)
-add_subdirectory_if_no_target(${ONNX_ROOT} ${ONNX_TARGET_NAME})
+find_package(protobuf CONFIG REQUIRED)
+find_package(ONNX CONFIG REQUIRED)
 
 file(READ "${PROJECT_SOURCE_DIR}/VERSION_NUMBER" ONNX_OPTIMIZER_VERSION)
 string(STRIP "${ONNX_OPTIMIZER_VERSION}" ONNX_OPTIMIZER_VERSION)
@@ -41,7 +48,7 @@ file(GLOB onnx_opt_srcs "onnxoptimizer/*.cc"
 list(REMOVE_ITEM onnx_opt_srcs "${PROJECT_SOURCE_DIR}/onnxoptimizer/cpp2py_export.cc")
 
 onnxopt_add_library(onnx_optimizer ${onnx_opt_srcs})
-target_link_libraries(onnx_optimizer PUBLIC ${ONNX_TARGET_NAME})
+target_link_libraries(onnx_optimizer PUBLIC ONNX::onnx ONNX::onnx_proto)
 target_include_directories(onnx_optimizer PUBLIC
     $<BUILD_INTERFACE:${PROJECT_SOURCE_DIR}>
     $<INSTALL_INTERFACE:include>
diff --git a/cmake/utils.cmake b/cmake/utils.cmake
index 6cca9f3..9d79f1a 100644
--- a/cmake/utils.cmake
+++ b/cmake/utils.cmake
@@ -1,5 +1,3 @@
-include(${PROJECT_SOURCE_DIR}/third_party/onnx/cmake/Utils.cmake)
-
 # Poor man's FetchContent
 function(add_subdirectory_if_no_target dir target)
   if (NOT TARGET ${target})
diff --git a/cmake/ONNXOptimizerConfig.cmake.in b/cmake/ONNXOptimizerConfig.cmake.in
index 72dcc88..c5639c8 100644
--- a/cmake/ONNXOptimizerConfig.cmake.in
+++ b/cmake/ONNXOptimizerConfig.cmake.in
@@ -3,6 +3,9 @@
 #   ONNX_OPTIMIZER_INCLUDE_DIRS     - include directories for onnx optimizer
 # as well as ONNX Optimizer targets for other cmake libraries to use.
 
+include(CMakeFindDependencyMacro)
+find_dependency(protobuf CONFIG)
+find_dependency(ONNX CONFIG)
 # library version information
 set(ONNX_OPTIMIZER_VERSION "@ONNX_OPTIMIZER_VERSION@")
 
