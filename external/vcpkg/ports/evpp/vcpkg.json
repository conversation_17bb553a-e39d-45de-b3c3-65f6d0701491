{"name": "evpp", "version": "0.7.0", "port-version": 8, "description": "A modern C++ network library based on libevent for developing high performance network services in TCP/UDP/HTTP protocols.", "homepage": "https://github.com/Qihoo360/evpp", "license": "BSD-3-<PERSON><PERSON>", "dependencies": [{"name": "boost-lockfree", "platform": "!windows"}, {"name": "<PERSON><PERSON><PERSON>", "platform": "!windows"}, "glog", {"name": "libevent", "features": ["openssl"]}, "<PERSON><PERSON><PERSON>", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}]}