diff --git a/evpp/server_status.h b/evpp/server_status.h
index 3a83725..f28f833 100644
--- a/evpp/server_status.h
+++ b/evpp/server_status.h
@@ -25,7 +25,7 @@ public:
     };
 
     std::string StatusToString() const {
-        H_CASE_STRING_BIGIN(status_);
+        H_CASE_STRING_BIGIN(+status_);
         H_CASE_STRING(kNull);
         H_CASE_STRING(kInitialized);
         H_CASE_STRING(kRunning);
diff --git a/evpp/sockets.cc b/evpp/sockets.cc
index 333c05c..c547d88 100644
--- a/evpp/sockets.cc
+++ b/evpp/sockets.cc
@@ -22,6 +22,14 @@ std::string strerror(int e) {
     }
 
     return empty_string;
+#elif defined(__APPLE__)
+    char buf[2048] = {};
+    strerror_r(e, buf, sizeof(buf) - 1);
+    const char* s = buf;
+    if (s) {
+        return std::string(s);
+    }
+    return std::string();
 #else
     char buf[2048] = {};
     const char* s = strerror_r(e, buf, sizeof(buf) - 1);
diff --git a/evpp/tcp_conn.cc b/evpp/tcp_conn.cc
index b7678d0..527b2b9 100644
--- a/evpp/tcp_conn.cc
+++ b/evpp/tcp_conn.cc
@@ -309,7 +309,7 @@ void TCPConn::SetTCPNoDelay(bool on) {
 }
 
 std::string TCPConn::StatusToString() const {
-    H_CASE_STRING_BIGIN(status_);
+    H_CASE_STRING_BIGIN(+status_);
     H_CASE_STRING(kDisconnected);
     H_CASE_STRING(kConnecting);
     H_CASE_STRING(kConnected);
