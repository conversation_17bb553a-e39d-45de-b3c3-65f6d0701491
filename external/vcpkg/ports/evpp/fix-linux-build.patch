diff --git a/CMakeLists.txt b/CMakeLists.txt
index 7872fc8..2a06614 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -47,7 +47,11 @@ string (REPLACE ";" " " CMAKE_CXX_FLAGS "${CXX_FLAGS}")
 if (UNIX)
     SET(CMAKE_CXX_FLAGS_DEBUG   "-O0 -g -ggdb -D_DEBUG -DGOOGLE_STRIP_LOG=0")
     SET(CMAKE_CXX_FLAGS_RELEASE "-O3 -g -ggdb -DNDEBUG -DGOOGLE_STRIP_LOG=1")
-    SET(DEPENDENT_LIBRARIES event glog pthread)
+    find_package(glog CONFIG REQUIRED)
+    find_path(GLOG_INCLUDE_DIRS logging.h PATH_SUFFIXES glog)
+    find_package(Libevent CONFIG REQUIRED)
+    list(APPEND DEPENDENT_INCLUDE_DIRS "${GLOG_INCLUDE_DIRS}/../")
+    SET(DEPENDENT_LIBRARIES libevent::core libevent::extra libevent::openssl glog::glog pthread)
 else (UNIX)
     SET(DEPENDENT_LIBRARIES event glog)
 endif (UNIX)
diff --git a/apps/CMakeLists.txt b/apps/CMakeLists.txt
index 469ed1c..8219424 100644
--- a/apps/CMakeLists.txt
+++ b/apps/CMakeLists.txt
@@ -2,7 +2,7 @@
 include_directories(${PROJECT_SOURCE_DIR}/apps ${PROJECT_SOURCE_DIR}/3rdparty)
 
 if (UNIX)
-set(LIBRARIES evpp_concurrentqueue event glog pthread)
+set(LIBRARIES evpp_concurrentqueue libevent::core libevent::extra libevent::openssl glog::glog pthread)
 link_directories("/home/<USER>/safe/lib" ${PROJECT_BUILD_DIR}/lib)
 else(UNIX)
 set(LIBRARIES evpp_static event glog)
diff --git a/benchmark/http/libevent/CMakeLists.txt b/benchmark/http/libevent/CMakeLists.txt
index 1148616..0356b71 100644
--- a/benchmark/http/libevent/CMakeLists.txt
+++ b/benchmark/http/libevent/CMakeLists.txt
@@ -1,3 +1,3 @@
 
 add_executable(benchmark_http_libevent libevent_http_bench.c)
-target_link_libraries(benchmark_http_libevent event)
+target_link_libraries(benchmark_http_libevent libevent::core libevent::extra libevent::openssl)
diff --git a/benchmark/ioevent/libevent/CMakeLists.txt b/benchmark/ioevent/libevent/CMakeLists.txt
index dfa51a3..de4849d 100644
--- a/benchmark/ioevent/libevent/CMakeLists.txt
+++ b/benchmark/ioevent/libevent/CMakeLists.txt
@@ -1,3 +1,3 @@
 
 add_executable(benchmark_ioevent_libevent libevent_ioevent_bench.c)
-target_link_libraries(benchmark_ioevent_libevent event)
+target_link_libraries(benchmark_ioevent_libevent libevent::core libevent::extra libevent::openssl)
diff --git a/benchmark/throughput/libevent/CMakeLists.txt b/benchmark/throughput/libevent/CMakeLists.txt
index 21dab84..aefe698 100644
--- a/benchmark/throughput/libevent/CMakeLists.txt
+++ b/benchmark/throughput/libevent/CMakeLists.txt
@@ -1,5 +1,5 @@
 add_executable(benchmark_tcp_libevent_client client.c)
-target_link_libraries(benchmark_tcp_libevent_client event)
+target_link_libraries(benchmark_tcp_libevent_client libevent::core libevent::extra libevent::openssl)
 
 add_executable(benchmark_tcp_libevent_server server.c)
-target_link_libraries(benchmark_tcp_libevent_server event)
+target_link_libraries(benchmark_tcp_libevent_server libevent::core libevent::extra libevent::openssl)
diff --git a/examples/recipes/self_control_timer/basic_01/CMakeLists.txt b/examples/recipes/self_control_timer/basic_01/CMakeLists.txt
index 7834db4..82294c5 100644
--- a/examples/recipes/self_control_timer/basic_01/CMakeLists.txt
+++ b/examples/recipes/self_control_timer/basic_01/CMakeLists.txt
@@ -1,7 +1,7 @@
 file(GLOB SRCS *.cc *.h)
 
 add_executable(example_recipes_self_control_timer_basic_01 ${SRCS})
-target_link_libraries(example_recipes_self_control_timer_basic_01 event)
+target_link_libraries(example_recipes_self_control_timer_basic_01 libevent::core libevent::extra libevent::openssl)
 
 
 
diff --git a/examples/recipes/self_control_timer/basic_02/CMakeLists.txt b/examples/recipes/self_control_timer/basic_02/CMakeLists.txt
index a8be419..0329ccc 100644
--- a/examples/recipes/self_control_timer/basic_02/CMakeLists.txt
+++ b/examples/recipes/self_control_timer/basic_02/CMakeLists.txt
@@ -1,7 +1,7 @@
 file(GLOB SRCS *.cc *.h)
 
 add_executable(example_recipes_self_control_timer_basic_02 ${SRCS})
-target_link_libraries(example_recipes_self_control_timer_basic_02 event)
+target_link_libraries(example_recipes_self_control_timer_basic_02 libevent::core libevent::extra libevent::openssl)
 
 
 
diff --git a/examples/recipes/self_control_timer/cancel_03/CMakeLists.txt b/examples/recipes/self_control_timer/cancel_03/CMakeLists.txt
index 1cae9bb..218b771 100644
--- a/examples/recipes/self_control_timer/cancel_03/CMakeLists.txt
+++ b/examples/recipes/self_control_timer/cancel_03/CMakeLists.txt
@@ -1,7 +1,7 @@
 file(GLOB SRCS *.cc *.h)
 
 add_executable(example_recipes_self_control_timer_cancel_03 ${SRCS})
-target_link_libraries(example_recipes_self_control_timer_cancel_03 event)
+target_link_libraries(example_recipes_self_control_timer_cancel_03 libevent::core libevent::extra libevent::openssl)
 
 
 
diff --git a/examples/recipes/self_control_timer/periodic_04/CMakeLists.txt b/examples/recipes/self_control_timer/periodic_04/CMakeLists.txt
index 749c584..e7c658e 100644
--- a/examples/recipes/self_control_timer/periodic_04/CMakeLists.txt
+++ b/examples/recipes/self_control_timer/periodic_04/CMakeLists.txt
@@ -1,7 +1,7 @@
 file(GLOB SRCS *.cc *.h)
 
 add_executable(example_recipes_self_control_timer_periodic_04 ${SRCS})
-target_link_libraries(example_recipes_self_control_timer_periodic_04 event)
+target_link_libraries(example_recipes_self_control_timer_periodic_04 libevent::core libevent::extra libevent::openssl)
 
 
 
