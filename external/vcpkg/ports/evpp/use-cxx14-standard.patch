diff --git a/CMakeLists.txt b/CMakeLists.txt
index 772da36..7ff62b8 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -24,7 +24,7 @@ if (UNIX)
          -fms-extensions
          -Wfloat-equal
          -Wextra
-         -std=c++11
+         -std=c++14
     )
     include_directories (${PROJECT_SOURCE_DIR}
                          ${PROJECT_SOURCE_DIR}/3rdparty)
diff --git a/evpp/CMakeLists.txt b/evpp/CMakeLists.txt
index 1c17f04..9f07f97 100644
--- a/evpp/CMakeLists.txt
+++ b/evpp/CMakeLists.txt
@@ -15,12 +15,10 @@ include_directories(${PROJECT_SOURCE_DIR})
 
 add_library(evpp_static STATIC ${evpp_SRCS})
 target_link_libraries(evpp_static ${DEPENDENT_LIBRARIES})
-target_compile_features(evpp_static PRIVATE cxx_std_11)
 target_include_directories(evpp_static INTERFACE $<INSTALL_INTERFACE:include>)
 
 add_library(evpp_lite_static STATIC ${evpp_lite_SRCS})
 target_link_libraries(evpp_lite_static ${DEPENDENT_LIBRARIES})
-target_compile_features(evpp_lite_static PRIVATE cxx_std_11)
 target_include_directories(evpp_lite_static INTERFACE $<INSTALL_INTERFACE:include>)
 
 if (UNIX)
