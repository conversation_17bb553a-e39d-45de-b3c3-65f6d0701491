diff --git a/CMakeLists.txt b/CMakeLists.txt
index 4aead6c369f129f50d79d77e6ac8f58afba91fa5..83f3f5026861dd904060293be8754c9e265047ab 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -113,7 +113,7 @@ endif()
 
 set(FLUIDLITE_VENDORED FALSE)
 if (ENABLE_SF3 AND NOT STB_VORBIS)
-    find_package(Vorbis QUIET)
+    find_package(Vorbis CONFIG REQUIRED)
     if (NOT TARGET Vorbis::vorbisfile)
         message(WARNING "Using vendored libogg/libvorbis")
 
diff --git a/fluidlite-config.cmake.in b/fluidlite-config.cmake.in
index 8c40ff0eb077fc0fd530f052500214d75e96899d..46940e4cd218d245014f40f1097f7193cc8952b3 100644
--- a/fluidlite-config.cmake.in
+++ b/fluidlite-config.cmake.in
@@ -15,7 +15,7 @@ endif()
 if(EXISTS "${CMAKE_CURRENT_LIST_DIR}/@<EMAIL>")
    include(CMakeFindDependencyMacro)
    if("${FLUIDLITE_SF3_SUPPORT}" STREQUAL "SF3_XIPH_VORBIS" AND NOT FLUIDLITE_VENDORED AND NOT TARGET Vorbis::vorbisfile)
-      find_dependency(Vorbis)
+      find_dependency(Vorbis CONFIG)
    endif()
 
    include("${CMAKE_CURRENT_LIST_DIR}/@<EMAIL>")
