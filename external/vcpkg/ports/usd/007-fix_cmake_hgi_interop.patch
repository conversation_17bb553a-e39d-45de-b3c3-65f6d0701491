diff --git a/pxr/imaging/hgiInterop/CMakeLists.txt b/pxr/imaging/hgiInterop/CMakeLists.txt
index 64ebfcb9e..0087d89aa 100644
--- a/pxr/imaging/hgiInterop/CMakeLists.txt
+++ b/pxr/imaging/hgiInterop/CMakeLists.txt
@@ -15,6 +15,7 @@ set(optionalPrivateHeaders "")
 list(APPEND optionalLibraries garch)
 
 if (PXR_ENABLE_GL_SUPPORT)
+    list(APPEND optionalLibraries hgiGL)
     list(APPEND optionalCppFiles opengl.cpp)
     list(APPEND optionalPrivateHeaders opengl.h)
 endif()
@@ -32,6 +33,10 @@ if (PXR_ENABLE_METAL_SUPPORT)
     list(APPEND optionalPrivateHeaders metal.h)
 endif()
 
+if (NOT (PXR_ENABLE_GL_SUPPORT OR PXR_ENABLE_VULKAN_SUPPORT OR PXR_ENABLE_METAL_SUPPORT))
+    message(FATAL_ERROR "No valid GPU backend set for hgiInterop")
+endif()
+
 pxr_library(hgiInterop
     LIBRARIES
         gf
