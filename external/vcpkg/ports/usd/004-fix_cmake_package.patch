diff --git a/pxr/CMakeLists.txt b/pxr/CMakeLists.txt
index 4c7301b..94d2f2a 100644
--- a/pxr/CMakeLists.txt
+++ b/pxr/CMakeLists.txt
@@ -17,13 +17,31 @@ endif()
 
 pxr_core_epilogue()
 
-export(PACKAGE pxr)
+include(GNUInstallDirs)
+include(CMakePackageConfigHelpers)
 
-configure_file(pxrConfig.cmake.in
-  "${PROJECT_BINARY_DIR}/pxrConfig.cmake" @ONLY)
-install(FILES
+configure_file(
+  "pxrConfig.cmake.in"
   "${PROJECT_BINARY_DIR}/pxrConfig.cmake"
-  DESTINATION "${CMAKE_INSTALL_PREFIX}"
+  @ONLY
 )
 
-install(EXPORT pxrTargets DESTINATION "cmake")
+write_basic_package_version_file("${PROJECT_BINARY_DIR}/pxrConfigVersion.cmake"
+  VERSION "${PXR_MAJOR_VERSION}.${PXR_MINOR_VERSION}.${PXR_PATCH_VERSION}"
+  COMPATIBILITY AnyNewerVersion
+)
+
+install(
+    FILES
+      "${PROJECT_BINARY_DIR}/pxrConfig.cmake"
+      "${PROJECT_BINARY_DIR}/pxrConfigVersion.cmake"
+
+    DESTINATION
+      "${CMAKE_INSTALL_DATADIR}/pxr"
+)
+
+install(
+    EXPORT      pxrTargets
+    # NAMESPACE   "pxr::"
+    DESTINATION "${CMAKE_INSTALL_DATADIR}/pxr"
+)
