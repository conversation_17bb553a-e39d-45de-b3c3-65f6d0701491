diff --git a/pxr/base/work/dispatcher.cpp b/pxr/base/work/dispatcher.cpp
index 8cc8dbb..74fba2d 100644
--- a/pxr/base/work/dispatcher.cpp
+++ b/pxr/base/work/dispatcher.cpp
@@ -31,11 +31,18 @@ WorkDispatcher::WorkDispatcher()
 }
 
 #if TBB_INTERFACE_VERSION_MAJOR >= 12
+#if TBB_VERSION_MAJOR >= 2022
+inline tbb::detail::d1::wait_context_vertex& 
+WorkDispatcher::_TaskGroup::_GetInternalWaitContext() {
+    return m_wait_vertex;
+}
+#else
 inline tbb::detail::d1::wait_context& 
 WorkDispatcher::_TaskGroup::_GetInternalWaitContext() {
     return m_wait_ctx;
 }
 #endif
+#endif
 
 WorkDispatcher::~WorkDispatcher() noexcept
 {
@@ -54,7 +61,11 @@ WorkDispatcher::Wait()
     // The native task_group::wait() has a comment saying its call to the
     // context reset method is not thread safe. So we do our own
     // synchronization to ensure it is called once.
+#if TBB_VERSION_MAJOR >= 2022
+    tbb::detail::d1::wait(_taskGroup._GetInternalWaitContext().get_context(), _context);
+#else
     tbb::detail::d1::wait(_taskGroup._GetInternalWaitContext(), _context);
+#endif
 #else
     _rootTask->wait_for_all();
 #endif
diff --git a/pxr/base/work/dispatcher.h b/pxr/base/work/dispatcher.h
index b35bcd8..7d24165 100644
--- a/pxr/base/work/dispatcher.h
+++ b/pxr/base/work/dispatcher.h
@@ -199,7 +199,11 @@ private:
     class _TaskGroup : public tbb::task_group {
     public:
         _TaskGroup(tbb::task_group_context& ctx) : tbb::task_group(ctx) {}
+#if TBB_VERSION_MAJOR >= 2022
+         inline tbb::detail::d1::wait_context_vertex& _GetInternalWaitContext();
+ #else
          inline tbb::detail::d1::wait_context& _GetInternalWaitContext();
+ #endif
     };
 
     _TaskGroup _taskGroup;
