diff --git a/cmake/defaults/Packages.cmake b/cmake/defaults/Packages.cmake
index fe60570b3..ccaabe765 100644
--- a/cmake/defaults/Packages.cmake
+++ b/cmake/defaults/Packages.cmake
@@ -137,7 +137,8 @@ endif()
 
 
 # --TBB
-find_package(TBB REQUIRED COMPONENTS tbb)
+find_package(TBB CONFIG REQUIRED)
+set(TBB_tbb_LIBRARY TBB::tbb)
 add_definitions(${TBB_DEFINITIONS})
 
 # --math
diff --git a/pxr/pxrConfig.cmake.in b/pxr/pxrConfig.cmake.in
index afe3dc448..041cd8cba 100644
--- a/pxr/pxrConfig.cmake.in
+++ b/pxr/pxrConfig.cmake.in
@@ -18,6 +18,8 @@ set(PXR_VERSION "@PXR_VERSION@")
 
 include(CMakeFindDependencyMacro)
 
+find_dependency(TBB CONFIG)
+
 # If Python support was enabled for this USD build, find the import
 # targets by invoking the appropriate FindPython module. Use the same
 # LIBRARY and INCLUDE_DIR settings from the original build if they
