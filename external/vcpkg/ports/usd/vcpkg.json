{"name": "usd", "version": "25.2", "description": "Universal Scene Description (USD) is an efficient, scalable system for authoring, reading, and streaming time-sampled scene description for interchange between graphics applications.", "homepage": "https://github.com/PixarAnimationStudios/OpenUSD", "license": null, "supports": "!x86 & !(arm & windows) & !android", "dependencies": ["opensubdiv", "tbb", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}, "zlib"], "features": {"materialx": {"description": "Enable MaterialX support", "dependencies": [{"name": "materialx", "features": ["glsl-generator", "render"]}]}, "metal": {"description": "Enable Metal based components", "supports": "osx"}, "openimageio": {"description": "Build OpenImageIO plugin", "dependencies": ["openimageio"]}, "vulkan": {"description": "Enable Vulkan based components", "dependencies": ["opengl", {"name": "opensubdiv", "default-features": false, "features": ["opengl"]}, "shaderc", "spirv-reflect", "vulkan", "vulkan-memory-allocator"]}}}