diff --git a/pxr/usd/pcp/primIndex.h b/pxr/usd/pcp/primIndex.h
index 6b31c7da4..285d13fa7 100644
--- a/pxr/usd/pcp/primIndex.h
+++ b/pxr/usd/pcp/primIndex.h
@@ -69,7 +69,7 @@ public:
     PcpPrimIndex(const PcpPrimIndex& rhs);
 
     /// Move-construction
-    PcpPrimIndex(PcpPrimIndex &&rhs) noexcept = default;
+    PcpPrimIndex(PcpPrimIndex &&rhs) = default;
 
     /// Assignment.
     PcpPrimIndex &operator=(const PcpPrimIndex &rhs) {
@@ -78,7 +78,7 @@ public:
     }
 
     // Move-assignment.
-    PcpPrimIndex &operator=(PcpPrimIndex &&rhs) noexcept = default;
+    PcpPrimIndex &operator=(PcpPrimIndex &&rhs) = default;
 
     /// Swap the contents of this prim index with \p index.
     PCP_API
