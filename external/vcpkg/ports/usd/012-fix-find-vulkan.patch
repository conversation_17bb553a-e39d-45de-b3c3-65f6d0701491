diff --git a/cmake/defaults/Packages.cmake b/cmake/defaults/Packages.cmake
index 9494278..473bb62 100644
--- a/cmake/defaults/Packages.cmake
+++ b/cmake/defaults/Packages.cmake
@@ -218,13 +218,6 @@ if (PXR_BUILD_IMAGING)
     endif()
     if (PXR_ENABLE_VULKAN_SUPPORT)
         message(STATUS "Enabling experimental feature Vulkan support")
-        if (EXISTS $ENV{VULKAN_SDK})
-            # Prioritize the VULKAN_SDK includes and packages before any system
-            # installed headers. This is to prevent linking against older SDKs
-            # that may be installed by the OS.
-            # XXX This is fixed in cmake 3.18+
-            include_directories(BEFORE SYSTEM $ENV{VULKAN_SDK} $ENV{VULKAN_SDK}/include $ENV{VULKAN_SDK}/lib $ENV{VULKAN_SDK}/source)
-            set(ENV{PATH} "$ENV{VULKAN_SDK}:$ENV{VULKAN_SDK}/include:$ENV{VULKAN_SDK}/lib:$ENV{VULKAN_SDK}/source:$ENV{PATH}")
             find_package(Vulkan REQUIRED)
             list(APPEND VULKAN_LIBS Vulkan::Vulkan)

@@ -249,9 +242,6 @@ if (PXR_BUILD_IMAGING)
             endif()

             add_definitions(-DPXR_VULKAN_SUPPORT_ENABLED)
-        else()
-            message(FATAL_ERROR "VULKAN_SDK not valid")
-        endif()
         find_package(unofficial-shaderc CONFIG REQUIRED)
         find_package(unofficial-spirv-reflect CONFIG REQUIRED)
         find_package(VulkanMemoryAllocator CONFIG REQUIRED)
