{"name": "kfr", "version-semver": "6.0.3", "description": "Fast, modern C++ DSP framework.", "homepage": "https://www.kfr.dev/", "license": null, "supports": "!(arm64 & windows) & !xbox", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"capi": {"description": "Enable C API build.", "dependencies": [{"name": "kfr", "default-features": false, "features": ["dft"]}]}, "dft": {"description": "Enable DFT and related algorithms."}}}