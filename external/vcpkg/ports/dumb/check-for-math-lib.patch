diff --git a/CMakeLists.txt b/CMakeLists.txt
index a793bba..6fd094c 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -223,7 +223,10 @@ IF(CMAKE_SYSTEM_NAME STREQUAL "Darwin")
     set_target_properties(dumb PROPERTIES INSTALL_NAME_DIR ${CMAKE_INSTALL_FULL_LIBDIR})
 ENDIF(CMAKE_SYSTEM_NAME STREQUAL "Darwin")
 
+find_library(HAS_MATH_LIB NAMES "m")
+if(HAS_MATH_LIB)
 target_link_libraries(dumb m)
+endif()
 
 install(FILES ${PKG_CONFIG_FILE} DESTINATION ${CMAKE_INSTALL_LIBDIR}/pkgconfig)
 install(FILES ${INSTALL_HEADERS} DESTINATION ${CMAKE_INSTALL_INCLUDEDIR})

