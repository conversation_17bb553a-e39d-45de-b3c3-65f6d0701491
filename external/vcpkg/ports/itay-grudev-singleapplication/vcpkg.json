{"name": "itay-grudev-singleapplication", "version": "3.5.2", "description": "Replacement of QtSingleApplication for Qt 5 and Qt 6 with support for inter-instance communication", "homepage": "https://github.com/itay-grudev/SingleApplication", "license": "MIT", "dependencies": [{"name": "qtbase", "default-features": false, "features": ["gui", "network", "widgets"]}, {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}]}