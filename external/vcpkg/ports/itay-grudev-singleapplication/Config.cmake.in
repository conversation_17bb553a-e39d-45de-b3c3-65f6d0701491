if(NOT TARGET itay-grudev-singleapplication)
    add_library(itay-grudev-singleapplication UNKNOWN IMPORTED)

    set_target_properties(itay-grudev-singleapplication PROPERTIES
        INTERFACE_INCLUDE_DIRECTORIES "${_VCPKG_INSTALLED_DIR}/${VCPKG_TARGET_TRIPLET}/include"
    )

    find_library(SingleApplication_LIBRARY_RELEASE NAMES SingleApplication PATHS "${_VCPKG_INSTALLED_DIR}/${VCPKG_TARGET_TRIPLET}/lib" NO_DEFAULT_PATH)
    if(EXISTS "${SingleApplication_LIBRARY_RELEASE}")
        set_property(TARGET itay-grudev-singleapplication APPEND PROPERTY IMPORTED_CONFIGURATIONS "Release")
        set_target_properties(itay-grudev-singleapplication PROPERTIES IMPORTED_LOCATION_RELEASE "${SingleApplication_LIBRARY_RELEASE}")
    endif()

    find_library(SingleApplication_LIBRARY_DEBUG NAMES SingleApplication PATHS "${_VCPKG_INSTALLED_DIR}/${VCPKG_TARGET_TRIPLET}/debug/lib" NO_DEFAULT_PATH)
    if(EXISTS "${SingleApplication_LIBRARY_DEBUG}")
        set_property(TARGET itay-grudev-singleapplication APPEND PROPERTY IMPORTED_CONFIGURATIONS "Debug")
        set_target_properties(itay-grudev-singleapplication PROPERTIES IMPORTED_LOCATION_DEBUG "${SingleApplication_LIBRARY_DEBUG}")
    endif()

    set_target_properties(itay-grudev-singleapplication PROPERTIES
        INTERFACE_COMPILE_DEFINITIONS QAPPLICATION_CLASS=@QAPPLICATION_CLASS@
    )
endif()