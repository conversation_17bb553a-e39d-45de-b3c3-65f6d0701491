diff --git a/lib/stream.c b/lib/stream.c
index 53b1b72..bb47c3b 100644
--- a/lib/stream.c
+++ b/lib/stream.c
@@ -310,8 +310,7 @@ int _ctmStreamWritePackedInts(_CTMcontext * self, CTMint * aData,
                          outProps,
                          &outPropsSize,
                          self->mCompressionLevel, // Level (0-9)
-                         0, -1, -1, -1, -1, -1,   // Default values (set by level)
-                         lzmaAlgo                 // Algorithm (0 = fast, 1 = normal)
+                         0, -1, -1, -1, -1, -1   // Default values (set by level)
                         );
 
   // Free temporary array
@@ -477,8 +476,7 @@ int _ctmStreamWritePackedFloats(_CTMcontext * self, CTMfloat * aData,
                          outProps,
                          &outPropsSize,
                          self->mCompressionLevel, // Level (0-9)
-                         0, -1, -1, -1, -1, -1,   // Default values (set by level)
-                         lzmaAlgo                 // Algorithm (0 = fast, 1 = normal)
+                         0, -1, -1, -1, -1, -1   // Default values (set by level)
                         );
 
   // Free temporary array
