{"name": "libinterpolate", "version": "2.7.1", "description": "Header-only C++ interpolation library.", "homepage": "https://github.com/CD3/libInterpolate", "license": "MIT", "dependencies": ["boost-geometry", "boost-headers", "boost-lexical-cast", "boost-mpl", "boost-program-options", "boost-range", "boost-tokenizer", "boost-type-erasure", "boost-type-traits", "eigen3", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}]}