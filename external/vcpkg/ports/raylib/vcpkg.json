{"name": "<PERSON><PERSON><PERSON>", "version": "5.5", "description": "A simple and easy-to-use library to enjoy videogames programming", "homepage": "https://github.com/raysan5/raylib", "license": "<PERSON><PERSON><PERSON>", "supports": "!arm32 & !uwp", "dependencies": ["cgltf", "dirent", "drlibs", {"name": "glfw3", "platform": "!(android | emscripten)"}, "miniaudio", "mmx", "nanosvg", "qoi", "stb", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "default-features": ["use-audio"], "features": {"use-audio": {"description": "Build raylib with audio module"}}}