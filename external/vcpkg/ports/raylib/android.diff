diff --git a/cmake/GlfwImport.cmake b/cmake/GlfwImport.cmake
index d0c23ca..92cd5c3 100644
--- a/cmake/GlfwImport.cmake
+++ b/cmake/GlfwImport.cmake
@@ -30,6 +30,8 @@ if(NOT glfw3_FOUND AND NOT USE_EXTERNAL_GLFW STREQUAL "ON" AND "${PLATFORM}" MAT
     include_directories(BEFORE SYSTEM external/glfw/include)
 elseif("${PLATFORM}" STREQUAL "DRM")
     MESSAGE(STATUS "No GLFW required on PLATFORM_DRM")
+elseif("${PLATFORM}" STREQUAL "Android")
+    list(REMOVE_ITEM LIBS_PRIVATE glfw)
 else()
     MESSAGE(STATUS "Using external GLFW")
     set(GLFW_PKG_DEPS glfw3)
