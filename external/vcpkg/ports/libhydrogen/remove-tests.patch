--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -139,12 +139,13 @@
 # Tests
 
 set(tests_executable "${PROJECT_NAME}-tests")
 set(tests_run_target "${PROJECT_NAME}-run-tests")
 set(tests_run_file "${PROJECT_BINARY_DIR}/${tests_run_target}.done")
 
+if(0)
 enable_testing()
 add_executable("${tests_executable}" ${test_files})
 target_compile_options("${tests_executable}" PRIVATE ${compile_options})
 target_link_libraries("${tests_executable}" "${PROJECT_NAME}")
 add_test(NAME "${tests_executable}" COMMAND "${tests_executable}")
 
@@ -166,12 +167,13 @@
                      COMMAND "${CMAKE_COMMAND}"
                      ARGS -E touch "${tests_run_file}"
                      WORKING_DIRECTORY "${PROJECT_BINARY_DIR}"
                      VERBATIM)
   add_custom_target("${tests_run_target}" ALL DEPENDS "${tests_run_file}" VERBATIM)
 endif()
+endif()
 
 # Generate Arduino package
 
 set(arduino_package_file "${PROJECT_BINARY_DIR}/hydrogen-crypto.zip")
 
 # Use the relative versions of the file path lists or else the full paths will end up in the
