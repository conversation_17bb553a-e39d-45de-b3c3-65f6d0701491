{"name": "idyntree", "version": "12.4.0", "description": "Multibody Dynamics Library designed for Free Floating Robots.", "homepage": "https://github.com/robotology/idyntree", "license": "BSD-3-<PERSON><PERSON>", "supports": "!xbox", "dependencies": ["eigen3", "libxml2", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "default-features": ["assimp"], "features": {"assimp": {"description": "Add support for loading meshes", "dependencies": ["assimp"]}, "irrlicht": {"description": "Add support for irrlicht-based visualizer", "supports": "!windows", "dependencies": ["glfw3", "irr<PERSON>t"]}}}