{"name": "lib<PERSON>h", "version": "1.5.0", "port-version": 6, "description": "The libMesh library provides a framework for the numerical simulation of partial differential equations using arbitrary unstructured discretizations on serial and parallel platforms. A major goal of the library is to provide support for adaptive mesh refinement (AMR) computations in parallel while allowing a research scientist to focus on the physics they are modeling.", "homepage": "https://github.com/libMesh/libmesh", "license": "LGPL-2.1-only", "supports": "linux"}