diff --git a/zookeeper-client/zookeeper-client-c/CMakeLists.txt b/zookeeper-client/zookeeper-client-c/CMakeLists.txt
index 24a5a1b..40fa67e 100644
--- a/zookeeper-client/zookeeper-client-c/CMakeLists.txt
+++ b/zookeeper-client/zookeeper-client-c/CMakeLists.txt
@@ -147,13 +147,15 @@ endforeach()
 include(CheckStructHasMember)
 check_struct_has_member("struct sockaddr_in6" sin6_addr "netinet/in.h" ZOO_IPV6_ENABLED)
 
+include(GNUInstallDirs)
+
 # configure
 configure_file(cmake_config.h.in ${CMAKE_CURRENT_BINARY_DIR}/include/config.h)
 
 # hashtable library
 set(hashtable_sources src/hashtable/hashtable_itr.c src/hashtable/hashtable.c)
 add_library(hashtable STATIC ${hashtable_sources})
-target_include_directories(hashtable PUBLIC include)
+target_include_directories(hashtable PUBLIC $<INSTALL_INTERFACE:${CMAKE_INSTALL_INCLUDEDIR}> $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>)
 target_link_libraries(hashtable PUBLIC $<$<OR:$<PLATFORM_ID:Linux>,$<PLATFORM_ID:FreeBSD>>:m>)
 
 # zookeeper library
@@ -176,11 +178,16 @@ if(WIN32)
 endif()
 
 add_library(zookeeper STATIC ${zookeeper_sources})
-target_include_directories(zookeeper PUBLIC include ${CMAKE_CURRENT_BINARY_DIR}/include generated)
+target_include_directories(zookeeper PUBLIC
+  $<INSTALL_INTERFACE:${CMAKE_INSTALL_INCLUDEDIR}>
+  $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
+  $<BUILD_INTERFACE:${CMAKE_CURRENT_BINARY_DIR}/include>
+  $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/generated>)
+
 target_link_libraries(zookeeper PUBLIC
-  hashtable
   $<$<PLATFORM_ID:Linux>:rt> # clock_gettime
-  $<$<PLATFORM_ID:Windows>:ws2_32>) # Winsock 2.0
+  $<$<PLATFORM_ID:Windows>:ws2_32> # Winsock 2.0
+  PRIVATE hashtable)
 
 if(WANT_SYNCAPI AND NOT WIN32)
   find_package(Threads REQUIRED)
@@ -189,7 +196,7 @@ endif()
 
 # cli executable
 add_executable(cli src/cli.c)
-target_link_libraries(cli zookeeper)
+target_link_libraries(cli PRIVATE zookeeper)
 
 # load_gen executable
 if(WANT_SYNCAPI AND NOT WIN32)
@@ -247,3 +254,23 @@ if(WANT_CPPUNIT)
     "ZKROOT=${CMAKE_CURRENT_SOURCE_DIR}/../.."
     "CLASSPATH=$CLASSPATH:$CLOVER_HOME/lib/clover*.jar")
 endif()
+
+
+target_compile_definitions(zookeeper PRIVATE _CRT_SECURE_NO_WARNINGS _WINSOCK_DEPRECATED_NO_WARNINGS _CRT_NONSTDC_NO_DEPRECATE)
+target_compile_definitions(cli PRIVATE _CRT_SECURE_NO_WARNINGS)
+
+file(GLOB ZOOKEEPER_HEADERS include/*.h)
+
+install(FILES ${ZOOKEEPER_HEADERS} generated/zookeeper.jute.h DESTINATION include/zookeeper)
+
+install(TARGETS zookeeper hashtable
+    EXPORT zookeeperConfig
+    RUNTIME DESTINATION bin
+    ARCHIVE DESTINATION lib
+    LIBRARY DESTINATION lib
+)
+install(EXPORT zookeeperConfig
+  FILE zookeeperConfig.cmake
+  NAMESPACE zookeeper::
+  DESTINATION "${CMAKE_INSTALL_PREFIX}/share/zookeeper"
+)
