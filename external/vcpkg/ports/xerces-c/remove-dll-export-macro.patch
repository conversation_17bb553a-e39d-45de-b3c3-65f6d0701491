diff --git a/src/xercesc/util/XercesDefs.hpp b/src/xercesc/util/XercesDefs.hpp
index 8071260..cd6bd68 100644
--- a/src/xercesc/util/XercesDefs.hpp
+++ b/src/xercesc/util/XercesDefs.hpp
@@ -133,7 +133,7 @@ typedef XMLUInt32           UCS4Ch;
 // The DLL_EXPORT flag should be defined on the command line during the build of a DLL
 // configure conspires to make this happen.
 
-#if defined(DLL_EXPORT)
+#if defined(XERCES_DLL_EXPORT)
   #if defined(XERCES_BUILDING_LIBRARY)
     #define XMLUTIL_EXPORT XERCES_PLATFORM_EXPORT
     #define XMLPARSER_EXPORT XERCES_PLATFORM_EXPORT
diff --git a/src/xercesc/util/Xerces_autoconf_config.hpp.cmake.in b/src/xercesc/util/Xerces_autoconf_config.hpp.cmake.in
index e849e08..69fe3bf 100644
--- a/src/xercesc/util/Xerces_autoconf_config.hpp.cmake.in
+++ b/src/xercesc/util/Xerces_autoconf_config.hpp.cmake.in
@@ -85,9 +85,6 @@
 #define XERCES_PLATFORM_EXPORT @XERCES_PLATFORM_EXPORT@
 #define XERCES_PLATFORM_IMPORT @XERCES_PLATFORM_IMPORT@
 #define XERCES_TEMPLATE_EXTERN @XERCES_TEMPLATE_EXTERN@
-#ifdef XERCES_DLL_EXPORT
-#  define DLL_EXPORT
-#endif
 
 // ---------------------------------------------------------------------------
 //  Include standard headers, if available, that we may rely on below.
