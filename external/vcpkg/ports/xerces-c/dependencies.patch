diff --git a/cmake/XercesNetAccessorSelection.cmake b/cmake/XercesNetAccessorSelection.cmake
index 7a63f1f..22ea65b 100644
--- a/cmake/XercesNetAccessorSelection.cmake
+++ b/cmake/XercesNetAccessorSelection.cmake
@@ -23,7 +23,7 @@ option(network "Network support" ON)
 
 if(network)
   find_library(SOCKET_LIBRARY socket)
-  find_library(NSL_LIBRARY nsl)
+  set(NSL_LIBRARY "" CACHE INTERNAL "Not used")
 
   # netaccessors in order of preference
 
diff --git a/cmake/XercesTranscoderSelection.cmake b/cmake/XercesTranscoderSelection.cmake
index 4ff5b16..48e274c 100644
--- a/cmake/XercesTranscoderSelection.cmake
+++ b/cmake/XercesTranscoderSelection.cmake
@@ -25,6 +25,7 @@
 
 if(ICU_FOUND)
   list(APPEND transcoders icu)
+  set(TRANSCODER_REQUIRES "icu-uc")
 endif()
 
 # MacOS
@@ -41,6 +42,20 @@ endif()
 
 # GNU iconv
 
+if(NOT transcoder AND NOT transcoders AND NOT WIN32)
+  find_package(Iconv)
+  set(FIND_DEPENDENCY_ICONV "${Iconv_FOUND}")
+  if(Iconv_FOUND AND NOT Iconv_IS_BUILT_IN)
+    list(APPEND libxerces_c_DEPS Iconv::Iconv)
+    set(TRANSCODER_LIBS "-liconv -lcharset")
+    list(APPEND CMAKE_REQUIRED_LIBRARIES Iconv::Iconv)
+    include(CheckSymbolExists)
+    check_symbol_exists(iconv_open "iconv.h" HAVE_ICONV_OPEN)
+    check_symbol_exists(iconv_close "iconv.h" HAVE_ICONV_CLOSE)
+    check_symbol_exists(iconv "iconv.h" HAVE_ICONV)
+  endif()
+endif()
+
 check_include_file_cxx(iconv.h HAVE_ICONV_H)
 check_include_file_cxx(wchar.h HAVE_WCHAR_H)
 check_include_file_cxx(string.h HAVE_STRING_H)
diff --git a/src/XercesCConfig.cmake.in b/src/XercesCConfig.cmake.in
index aa8fc2e..754c425 100644
--- a/src/XercesCConfig.cmake.in
+++ b/src/XercesCConfig.cmake.in
@@ -1,5 +1,14 @@
 @PACKAGE_INIT@
 
+include(CMakeFindDependencyMacro)
+find_dependency(Threads)
+if("@CMAKE_REQUIRE_FIND_PACKAGE_ICU@")
+    find_dependency(ICU COMPONENTS uc data)
+endif()
+if("@FIND_DEPENDENCY_ICONV@")
+    find_dependency(Iconv)
+endif()
+
 include(${CMAKE_CURRENT_LIST_DIR}/XercesCConfigInternal.cmake)
 
 add_library(XercesC::XercesC INTERFACE IMPORTED)
diff --git a/xerces-c.pc.in b/xerces-c.pc.in
index 16b18c7..077296b 100644
--- a/xerces-c.pc.in
+++ b/xerces-c.pc.in
@@ -7,5 +7,6 @@ Name: Xerces-C++
 Description: Validating XML parser library for C++
 Version: @VERSION@
 Libs: -L${libdir} -lxerces-c
-Libs.private: @CURL_LIBS@
+Libs.private: @CURL_LIBS@ @TRANSCODER_LIBS@
+Requires.private: @TRANSCODER_REQUIRES@
 Cflags: -I${includedir}
