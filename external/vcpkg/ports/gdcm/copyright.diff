diff --git a/Utilities/CMakeLists.txt b/Utilities/CMakeLists.txt
index 4cdc999..5971705 100644
--- a/Utilities/CMakeLists.txt
+++ b/Utilities/CMakeLists.txt
@@ -12,8 +12,8 @@ if(NOT GDCM_USE_SYSTEM_LJPEG)
 endif()
 
 # Do expat
-APPEND_COPYRIGHT(${CMAKE_CURRENT_SOURCE_DIR}/gdcmexpat/COPYING)
 if(NOT GDCM_USE_SYSTEM_EXPAT)
+APPEND_COPYRIGHT(${CMAKE_CURRENT_SOURCE_DIR}/gdcmexpat/COPYING)
   set(EXPAT_NAMESPACE "GDCMEXPAT")
   set(EXPAT_INSTALL_NO_LIBRARIES ${GDCM_INSTALL_NO_LIBRARIES})
   set(EXPAT_INSTALL_BIN_DIR      ${GDCM_INSTALL_BIN_DIR})
@@ -22,8 +22,8 @@ if(NOT GDCM_USE_SYSTEM_EXPAT)
 endif()
 
 # Do openjpeg (jpeg2000 implementation)
-APPEND_COPYRIGHT(${CMAKE_CURRENT_SOURCE_DIR}/gdcmopenjpeg/LICENSE)
 if(NOT GDCM_USE_SYSTEM_OPENJPEG)
+APPEND_COPYRIGHT(${CMAKE_CURRENT_SOURCE_DIR}/gdcmopenjpeg/LICENSE)
   set(OPENJPEG_NAMESPACE "GDCMOPENJPEG")
   set(OPENJPEG_INSTALL_NO_LIBRARIES ${GDCM_INSTALL_NO_LIBRARIES})
   set(OPENJPEG_INSTALL_BIN_DIR      ${GDCM_INSTALL_BIN_DIR})
@@ -34,9 +34,9 @@ if(NOT GDCM_USE_SYSTEM_OPENJPEG)
 endif()
 
 # Do jpegls (JPEG-LS aka near lossless implementation)
-APPEND_COPYRIGHT(${CMAKE_CURRENT_SOURCE_DIR}/gdcmcharls/License.txt)
 if(GDCM_USE_JPEGLS)
 if(NOT GDCM_USE_SYSTEM_CHARLS)
+APPEND_COPYRIGHT(${CMAKE_CURRENT_SOURCE_DIR}/gdcmcharls/License.txt)
   set(CHARLS_NAMESPACE "GDCMCHARLS")
   set(CHARLS_INSTALL_NO_LIBRARIES ${GDCM_INSTALL_NO_LIBRARIES})
   set(CHARLS_INSTALL_BIN_DIR      ${GDCM_INSTALL_BIN_DIR})
@@ -47,9 +47,9 @@ endif()
 endif()
 
 # Do md5
-APPEND_COPYRIGHT(${CMAKE_CURRENT_SOURCE_DIR}/gdcmmd5/COPYING)
 if(GDCM_BUILD_TESTING)
 if(NOT GDCM_USE_SYSTEM_MD5)
+APPEND_COPYRIGHT(${CMAKE_CURRENT_SOURCE_DIR}/gdcmmd5/COPYING)
   set(MD5_NAMESPACE "GDCMMD5")
   set(MD5_INSTALL_NO_LIBRARIES ${GDCM_INSTALL_NO_LIBRARIES})
   set(MD5_INSTALL_BIN_DIR      ${GDCM_INSTALL_BIN_DIR})
@@ -59,8 +59,8 @@ endif()
 endif()
 
 # Do zlib
-APPEND_COPYRIGHT(${CMAKE_CURRENT_SOURCE_DIR}/gdcmzlib/COPYING)
 if(NOT GDCM_USE_SYSTEM_ZLIB)
+APPEND_COPYRIGHT(${CMAKE_CURRENT_SOURCE_DIR}/gdcmzlib/COPYING)
   set(ZLIB_NAMESPACE "GDCMZLIB")
   set(ZLIB_INSTALL_NO_LIBRARIES ${GDCM_INSTALL_NO_LIBRARIES})
   set(ZLIB_INSTALL_BIN_DIR      ${GDCM_INSTALL_BIN_DIR})
@@ -70,8 +70,8 @@ endif()
 
 # Do getopt
 if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/getopt)
-  APPEND_COPYRIGHT(${CMAKE_CURRENT_SOURCE_DIR}/getopt/COPYING)
   if(WIN32 AND NOT CYGWIN AND NOT MINGW)
+  APPEND_COPYRIGHT(${CMAKE_CURRENT_SOURCE_DIR}/getopt/COPYING)
     set(GETOPT_NAMESPACE "GDCMGETOPT")
     set(GETOPT_INSTALL_NO_LIBRARIES ${GDCM_INSTALL_NO_LIBRARIES})
     set(GETOPT_INSTALL_BIN_DIR      ${GDCM_INSTALL_BIN_DIR})
@@ -81,9 +81,9 @@ if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/getopt)
 endif()
 
 # you could be running mingw32 on linux in which case you do NOT want the gdcmuuid lib
-APPEND_COPYRIGHT(${CMAKE_CURRENT_SOURCE_DIR}/gdcmuuid/COPYING)
 if(NOT WIN32 AND NOT MINGW)
   if(NOT GDCM_USE_SYSTEM_UUID)
+APPEND_COPYRIGHT(${CMAKE_CURRENT_SOURCE_DIR}/gdcmuuid/COPYING)
     set(UUID_NAMESPACE "GDCMUUID")
     set(UUID_INSTALL_NO_LIBRARIES ${GDCM_INSTALL_NO_LIBRARIES})
     set(UUID_INSTALL_BIN_DIR      ${GDCM_INSTALL_BIN_DIR})
@@ -92,8 +92,8 @@ if(NOT WIN32 AND NOT MINGW)
   endif()
 endif()
 
-APPEND_COPYRIGHT(${CMAKE_CURRENT_SOURCE_DIR}/pvrg/COPYING)
 if(GDCM_USE_PVRG)
+APPEND_COPYRIGHT(${CMAKE_CURRENT_SOURCE_DIR}/pvrg/COPYING)
   if(NOT GDCM_USE_SYSTEM_PVRG)
     add_subdirectory(pvrg)
   endif()
