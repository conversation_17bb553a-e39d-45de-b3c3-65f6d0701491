diff --git a/CMake/ExportConfiguration/GDCMConfig.cmake.in b/CMake/ExportConfiguration/GDCMConfig.cmake.in
index 169cb82..c77cf1f 100644
--- a/CMake/ExportConfiguration/GDCMConfig.cmake.in
+++ b/CMake/ExportConfiguration/GDCMConfig.cmake.in
@@ -1,3 +1,7 @@
+include(CMakeFindDependencyMacro)
+find_dependency(expat CONFIG)
+find_dependency(OpenJPEG CONFIG)
+find_dependency(ZLIB)
 #-----------------------------------------------------------------------------
 #
 # GDCMConfig.cmake - CMake configuration file for external projects.
diff --git a/CMakeLists.txt b/CMakeLists.txt
index 38c65d1..b0cb77b 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -360,8 +360,8 @@ else()
 endif()
 
 if(GDCM_USE_SYSTEM_OPENJPEG)
-  find_package(OpenJPEG 2.0.0 REQUIRED)
-  set(GDCM_OPENJPEG_LIBRARIES ${OPENJPEG_LIBRARIES})
+  find_package(OpenJPEG CONFIG REQUIRED)
+  set(GDCM_OPENJPEG_LIBRARIES $<TARGET_NAME:openjp2>)
 else()
   set(GDCM_OPENJPEG_LIBRARIES gdcmopenjp2)
 endif()
@@ -401,7 +401,7 @@ if(GDCM_USE_SYSTEM_ZLIB)
   # If user say so, then this is a requirement !
   find_package(ZLIB REQUIRED)
   include_directories(${ZLIB_INCLUDE_DIR})
-  set(GDCM_ZLIB_LIBRARIES ${ZLIB_LIBRARIES})
+  set(GDCM_ZLIB_LIBRARIES ZLIB::ZLIB)
 else()
   set(GDCM_ZLIB_LIBRARIES "gdcmzlib")
 endif()
@@ -420,8 +420,8 @@ endif()
 
 if(GDCM_USE_SYSTEM_EXPAT)
   # If user say so, then this is a requirement !
-  find_package(EXPAT REQUIRED)
-  set(GDCM_EXPAT_LIBRARIES ${EXPAT_LIBRARIES})
+  find_package(expat CONFIG REQUIRED)
+  set(GDCM_EXPAT_LIBRARIES expat::expat)
 else()
   set(GDCM_EXPAT_LIBRARIES "gdcmexpat")
 endif()
