diff --git a/PC/pyconfig.h b/PC/pyconfig.h
index 64e7aec..aa36745 100644
--- a/PC/pyconfig.h
+++ b/PC/pyconfig.h
@@ -331,6 +331,7 @@ typedef int pid_t;
 
 /* For Windows the Python core is in a DLL by default.  Test
 Py_NO_ENABLE_SHARED to find out.  Also support MS_NO_COREDLL for b/w compat */
+#define Py_NO_ENABLE_SHARED
 #if !defined(MS_NO_COREDLL) && !defined(Py_NO_ENABLE_SHARED)
 #	define Py_ENABLE_SHARED 1 /* standard symbol for shared library */
 #	define MS_COREDLL	/* deprecated old symbol */
-- 

