diff --git a/setup.py b/setup.py
index f764223..d6a58e4 100644
--- a/setup.py
+++ b/setup.py
@@ -1506,6 +1506,14 @@ class PyBuildExt(build_ext):
             exts.append( Extension('bz2', ['bz2module.c'],
                                    libraries = ['bz2'],
                                    extra_link_args = bz2_extra_link_args) )
+        elif (self.compiler.find_library_file(lib_dirs, 'bz2d')):
+            if host_platform == "darwin":
+                bz2_extra_link_args = ('-Wl,-search_paths_first',)
+            else:
+                bz2_extra_link_args = ()
+            exts.append( Extension('bz2', ['bz2module.c'],
+                               libraries=['bz2d'],
+                               extra_link_args = bz2_extra_link_args) )
         else:
             missing.append('bz2')
 
