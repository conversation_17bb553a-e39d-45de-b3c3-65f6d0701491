diff --git a/include/ismrmrd/ismrmrd.h b/include/ismrmrd/ismrmrd.h
index 40614eb..e5e3e25 100644
--- a/include/ismrmrd/ismrmrd.h
+++ b/include/ismrmrd/ismrmrd.h
@@ -30,6 +30,9 @@ typedef unsigned __int32 uint32_t;
 typedef __int64 int64_t;
 typedef unsigned __int64 uint64_t;
 #endif
+#if _WIN32
+#include <stddef.h>     /* for size_t */
+#endif
 #else /* non MS C or C++ compiler */
 #include <stdint.h>
 #include <stddef.h>     /* for size_t */
