diff --git a/CMakeLists.txt b/CMakeLists.txt
index 2144fd3..68f9ca9 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -108,7 +108,11 @@ endif()
 if (USE_HDF5_DATASET_SUPPORT)
     if (VCPKG_TARGET_TRIPLET) #VCPKG HDF5 is packaged differently.
         find_package(HDF5 CONFIG COMPONENTS C shared REQUIRED)
-        set(HDF5_C_LIBRARIES hdf5::hdf5-shared)
+        if (BUILD_SHARED_LIBS)
+            set(HDF5_C_LIBRARIES hdf5::hdf5-shared)
+        else()
+            set(HDF5_C_LIBRARIES hdf5::hdf5-static)
+        endif()
     else ()
         find_package(HDF5 COMPONENTS C REQUIRED)
     endif ()
