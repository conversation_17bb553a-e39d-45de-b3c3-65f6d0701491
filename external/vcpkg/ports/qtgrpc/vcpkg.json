{"name": "qtgrpc", "version": "6.8.3", "description": "The Qt GRPC and Qt Protobuf modules together allow you to define data and messages in proto files, and then use the code generators, which generate client code allowing accessors for fields and gRPC services in the Qt framework.", "homepage": "https://www.qt.io/", "license": null, "dependencies": [{"name": "grpc", "default-features": false}, {"name": "protobuf", "default-features": false}, {"name": "qtbase", "default-features": false}, {"name": "qtgrpc", "host": true, "default-features": false}]}