diff --git a/tmxlite/CMakeLists.txt b/tmxlite/CMakeLists.txt
index a1b1882..8c0aad6 100644
--- a/tmxlite/CMakeLists.txt
+++ b/tmxlite/CMakeLists.txt
@@ -68,10 +68,9 @@ if(USE_EXTLIBS)
     SET(CMAKE_MODULE_PATH ${CMAKE_MODULE_PATH} "${CMAKE_SOURCE_DIR}/cmake/modules/")
     
     find_package(ZLIB REQUIRED)
-    find_package(PUGIXML REQUIRED)
-    find_package(Zstd REQUIRED)
+    find_package(pugixml CONFIG REQUIRED)
+    find_package(zstd CONFIG REQUIRED)
     
-    include_directories(${ZLIB_INCLUDE_DIRS} ${PUGIXML_INCLUDE_DIR} ${ZSTD_INCLUDE_DIR})
     
 else()
     #add miniz and pugixml from source
@@ -102,7 +101,10 @@ else()
 endif()
 
 if(USE_EXTLIBS)
-    target_link_libraries(${PROJECT_NAME} ${ZLIB_LIBRARIES} ${PUGIXML_LIBRARY} ${ZSTD_LIBRARY})
+    target_link_libraries(${PROJECT_NAME} PRIVATE
+      $<IF:$<TARGET_EXISTS:zstd::libzstd_shared>,zstd::libzstd_shared,zstd::libzstd_static>
+      pugixml::pugixml
+      ZLIB::ZLIB)
 else()
     if(USE_ZSTD)
     target_link_libraries(${PROJECT_NAME} ${ZSTD_LIBRARY})    
