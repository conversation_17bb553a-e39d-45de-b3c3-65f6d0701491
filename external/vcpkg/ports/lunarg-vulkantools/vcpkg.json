{"name": "lunarg-vulkantools", "version": "1.4.304.1", "description": "Tools to aid in Vulkan development", "homepage": "https://github.com/LunarG/VulkanTools", "license": null, "supports": "!osx & !staticcrt", "dependencies": ["jsoncpp", "qt5-base", "<PERSON><PERSON><PERSON><PERSON>", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}, {"name": "vcpkg-get-python-packages", "host": true}, "vulkan-loader", "vulkan-utility-libraries"]}