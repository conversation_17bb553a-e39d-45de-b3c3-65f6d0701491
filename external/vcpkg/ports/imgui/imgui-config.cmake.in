cmake_policy(SET CMP0012 NEW)

@PACKAGE_INIT@

include(CMakeFindDependencyMacro)

if (@IMGUI_BUILD_GLFW_BINDING@)
    if (NOT "@EMSCRIPTEN@")
        find_dependency(glfw3 CONFIG)
    endif()
endif()

if (@IMGUI_BUILD_GLUT_BINDING@)
    find_dependency(GLUT)
endif()

if (@IMGUI_BUILD_SDL3_BINDING@ OR @IMGUI_BUILD_SDL3_RENDERER_BINDING@ OR @IMGUI_BUILD_SDLGPU3_BINDING@)
    find_dependency(SDL3 CONFIG)
endif()

if (@IMGUI_BUILD_VULKAN_BINDING@)
    find_dependency(Vulkan)
endif()

if (@IMGUI_FREETYPE@)
    find_dependency(freetype CONFIG)
endif()

if (@IMGUI_FREETYPE_SVG@)
    find_dependency(plutosvg CONFIG)
endif()

if (@IMGUI_BUILD_ALLEGRO5_BINDING@)
    find_dependency(Allegro CONFIG)
endif()

if (@IMGUI_TEST_ENGINE@)
    find_dependency(Stb)
endif()

include("${CMAKE_CURRENT_LIST_DIR}/imgui-targets.cmake")
