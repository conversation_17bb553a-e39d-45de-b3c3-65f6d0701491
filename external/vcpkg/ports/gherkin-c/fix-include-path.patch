diff --git a/CMakeLists.txt b/CMakeLists.txt
index 74b9ffb..6af4645 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -60,7 +60,7 @@ LIST(APPEND GHERKIN_SRS
         )
 
 add_library(gherkin ${GH<PERSON>KIN_SRS})
-target_include_directories(gherkin PUBLIC "$<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include;${CMAKE_CURRENT_SOURCE_DIR}/src>")
+target_include_directories(gherkin PUBLIC "$<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include;${CMAKE_CURRENT_SOURCE_DIR}/src>" "$<INSTALL_INTERFACE:include/gherkin-c>")
 
 if (BUILD_GHERKIN_TESTS)
   LIST(APPEND GENERATE_TOKEN
@@ -96,7 +96,7 @@ if (BUILD_GHERKIN_TESTS)
   ENDFOREACH()
 endif()
 
-set(include_install_dir "include")
+set(include_install_dir "include/gherkin-c")
 set(lib_install_dir "lib/")
 set(bin_install_dir "bin/")
 set(config_install_dir "lib/cmake/")
