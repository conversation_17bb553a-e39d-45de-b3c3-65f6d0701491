diff --git a/CMakeLists.txt b/CMakeLists.txt
index e0cc288..e5a4648 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -195,7 +195,7 @@ elseif(CMAKE_CXX_COMPILER_ID STREQUAL "MSVC")
             set(MSVC2017_COMPATIBILITY ON)
             message(WARNING "MSVC 2017 detected, automatically enabling MSVC2017_COMPATIBILITY. Note that some features may not be available with this compiler.")
         endif()
-    elseif(CMAKE_CXX_COMPILER_VERSION VERSION_LESS "19.30")
+    elseif(CMAKE_CXX_COMPILER_VERSION GREATER "19.20")
         if(NOT MSVC2019_COMPATIBILITY)
             set(MSVC2019_COMPATIBILITY ON)
             message(WARNING "MSVC 2019 detected, automatically enabling MSVC2019_COMPATIBILITY. Note that some features may not be available with this compiler.")
