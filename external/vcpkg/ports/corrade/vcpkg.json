{"name": "corrade", "version-string": "2020.06", "port-version": 8, "description": "C++11/C++14 multiplatform utility library.", "homepage": "https://magnum.graphics/corrade/", "dependencies": [{"name": "corrade", "host": true, "default-features": false}, {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "default-features": ["interconnect", "pluginmanager", "testsuite", "utility"], "features": {"dynamic-pluginmanager": {"description": "PluginManager library with dynamic plugin support", "supports": "!ios & !uwp & !android", "dependencies": [{"name": "corrade", "default-features": false, "features": ["pluginmanager"]}]}, "interconnect": {"description": "Interconnect library", "dependencies": [{"name": "corrade", "default-features": false, "features": ["utility"]}]}, "pluginmanager": {"description": "PluginManager library", "dependencies": [{"name": "corrade", "default-features": false, "features": [{"name": "dynamic-pluginmanager", "platform": "!ios & !uwp & !android"}, "utility"]}]}, "testsuite": {"description": "TestSuite library", "dependencies": [{"name": "corrade", "default-features": false, "features": ["utility"]}]}, "utility": {"description": "Utility library"}}}