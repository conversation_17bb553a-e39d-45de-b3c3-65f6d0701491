{"$comment": ["NOTE: All changes made to this file will get overwritten by the next port release.", "Please contribute your changes to https://github.com/Azure/azure-sdk-for-cpp."], "name": "azure-storage-common-cpp", "version-semver": "12.10.0", "description": ["Microsoft Azure Common Storage SDK for C++", "This library provides common Azure Storage-related abstractions for Azure SDK."], "homepage": "https://github.com/Azure/azure-sdk-for-cpp/tree/main/sdk/storage/azure-storage-common", "license": "MIT", "dependencies": [{"name": "azure-core-cpp", "default-features": false, "version>=": "1.14.1"}, {"name": "libxml2", "default-features": false, "platform": "!windows"}, {"name": "openssl", "platform": "!windows"}, {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}]}