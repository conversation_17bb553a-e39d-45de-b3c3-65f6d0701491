diff --git a/thirdparty/CMakeLists.txt b/thirdparty/CMakeLists.txt
index b136fff..05ca64a 100644
--- a/thirdparty/CMakeLists.txt
+++ b/thirdparty/CMakeLists.txt
@@ -89,7 +89,7 @@ else(BUILD_THIRDPARTY)
     message(STATUS "Your system seems to have a TIFF lib available, we will use it")
     set(OPJ_HAVE_TIFF_H 1 PARENT_SCOPE)
     set(OPJ_HAVE_LIBTIFF 1 PARENT_SCOPE)
-    if(BUILD_STATIC_LIBS AND NOT BUILD_SHARED_LIBS)
+    if("${not_using_vcpkg}")
       # Probably incorrect as PC_TIFF_STATIC_LIBRARIES will lack the path to the libraries
       # and will only work if they are in system directories
       set(TIFF_LIBNAME ${PC_TIFF_STATIC_LIBRARIES} PARENT_SCOPE)
@@ -131,7 +131,7 @@ else(BUILD_THIRDPARTY)
     message(STATUS "Your system seems to have a LCMS2 lib available, we will use it")
     set(OPJ_HAVE_LCMS2_H 1 PARENT_SCOPE)
     set(OPJ_HAVE_LIBLCMS2 1 PARENT_SCOPE)
-    if(BUILD_STATIC_LIBS AND NOT BUILD_SHARED_LIBS)
+    if("${not_using_vcpkg}")
       # Probably incorrect as PC_LCMS2_STATIC_LIBRARIES will lack the path to the libraries
       # and will only work if they are in system directories
       set(LCMS_LIBNAME ${PC_LCMS2_STATIC_LIBRARIES} PARENT_SCOPE)
