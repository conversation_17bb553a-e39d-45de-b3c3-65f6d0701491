{"name": "openvpn3", "version": "3.10", "port-version": 1, "description": "a C++ class library that implements the functionality of an OpenVPN client, and is protocol-compatible with the OpenVPN 2.x branch.", "homepage": "https://openvpn.net", "license": "AGPL-3.0-only", "supports": "!uwp & !xbox", "dependencies": ["asio", "lz4", "mbedtls", {"name": "tap-windows6", "platform": "windows"}, {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}]}