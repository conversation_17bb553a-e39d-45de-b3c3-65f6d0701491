diff --git a/CMakeLists.txt b/CMakeLists.txt
index 6cf519d..d07321c 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -32,11 +32,13 @@ include(ovpn-doxygen)
 
 add_subdirectory(client)
 add_subdirectory(test/unittests)
+if(BUILD_TOOLS)
 add_subdirectory(test/ovpncli)
 
 add_subdirectory(openvpn/omi)
 add_subdirectory(openvpn/ovpnagent/win)
 add_subdirectory(openvpn/ovpnagent/mac)
+endif()
 
 if (ENABLE_DOXYGEN)
     # Exclude some project specific directories
diff --git a/client/CMakeLists.txt b/client/CMakeLists.txt
index e1ecf8f..5afe6ec 100644
--- a/client/CMakeLists.txt
+++ b/client/CMakeLists.txt
@@ -1,5 +1,29 @@
 include(findcoredeps)
 include(findswigdeps)
+add_library(ovpnclilib ovpncli.cpp)
+add_core_dependencies(ovpnclilib)
+target_compile_features(ovpnclilib PUBLIC cxx_std_17)
+target_include_directories(ovpnclilib PRIVATE "${CMAKE_CURRENT_SOURCE_DIR}" PUBLIC "$<INSTALL_INTERFACE:include>")
+set_target_properties(ovpnclilib PROPERTIES
+    OUTPUT_NAME ovpncli
+    EXPORT_NAME ovpncli
+)
+install(TARGETS ovpnclilib EXPORT unofficial-openvpn3-targets)
+install(EXPORT unofficial-openvpn3-targets
+    NAMESPACE unofficial::openvpn3::
+    DESTINATION share/unofficial-openvpn3
+)
+file(WRITE "${CMAKE_CURRENT_BINARY_DIR}/unofficial-openvpn3-config.cmake" "\
+include(CMakeFindDependencyMacro)
+find_dependency(asio CONFIG)
+find_dependency(lz4 CONFIG)
+if(UNIX)
+  set(THREADS_PREFER_PTHREAD_FLAG 1)
+  find_dependency(Threads)
+endif()
+include(\"\${CMAKE_CURRENT_LIST_DIR}/unofficial-openvpn3-targets.cmake\")
+")
+install(FILES "${CMAKE_CURRENT_BINARY_DIR}/unofficial-openvpn3-config.cmake" DESTINATION share/unofficial-openvpn3)
 
 if (BUILD_SWIG_LIB)
     set_property(SOURCE ovpncli.i PROPERTY CPLUSPLUS ON)
