diff --git a/cipher/Makefile.am b/cipher/Makefile.am
index ea9014c..8aeedb7 100644
--- a/cipher/Makefile.am
+++ b/cipher/Makefile.am
@@ -19,6 +19,13 @@
 
 # Process this file with automake to produce Makefile.in
 
+# Building host tools for native triplet, or not at all
+CPPFLAGS_FOR_BUILD = $(CPPFLAGS)
+CFLAGS_FOR_BUILD = $(CFLAGS)
+LDFLAGS_FOR_BUILD = $(LDFLAGS)
+# Using native tools, either this build or from host triplet
+HOST_TOOLS_PREFIX ?= .
+
 # Need to include ../src in addition to top_srcdir because gcrypt.h is
 # a built header.
 AM_CPPFLAGS = -I../src -I$(top_srcdir)/src -I../mpi -I$(top_srcdir)/mpi
@@ -160,8 +167,8 @@ EXTRA_libcipher_la_SOURCES = \
 	blake2s-amd64-avx.S blake2s-amd64-avx512.S
 
 gost28147.lo: gost-sb.h
-gost-sb.h: gost-s-box$(EXEEXT_FOR_BUILD)
-	./gost-s-box$(EXEEXT_FOR_BUILD) $@
+gost-sb.h: $(HOST_TOOLS_PREFIX)/gost-s-box$(EXEEXT_FOR_BUILD)
+	$(HOST_TOOLS_PREFIX)/gost-s-box$(EXEEXT_FOR_BUILD) $@
 
 gost-s-box$(EXEEXT_FOR_BUILD): gost-s-box.c
 	$(CC_FOR_BUILD) $(CFLAGS_FOR_BUILD) $(LDFLAGS_FOR_BUILD) \
