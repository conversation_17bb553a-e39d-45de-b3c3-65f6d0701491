{"name": "opencensus-cpp", "version-date": "2021-08-26", "port-version": 2, "description": "OpenCensus is a toolkit for collecting application performance and behavior data. It currently includes an API for tracing and stats.", "homepage": "https://github.com/census-instrumentation/opencensus-cpp", "license": "Apache-2.0", "supports": "!windows", "dependencies": ["abseil", "prometheus-cpp", {"name": "vcpkg-cmake", "host": true}], "features": {"test": {"description": "Build test", "dependencies": ["benchmark", "gtest"]}}}