diff --git a/CMakeLists.txt b/CMakeLists.txt
index ddbc459..06c4165 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -20,7 +20,7 @@ set(CMAKE_DEBUG_POSTFIX "-dbg")
 if(UNIX OR VXWORKS)
     include(GNUInstallDirs)
 
-    set(FOONATHAN_MEMORY_INC_INSTALL_DIR "${CMAKE_INSTALL_INCLUDEDIR}/foonathan_memory")
+    set(FOONATHAN_MEMORY_INC_INSTALL_DIR "${CMAKE_INSTALL_INCLUDEDIR}")
     set(FO<PERSON>ATHAN_MEMORY_RUNTIME_INSTALL_DIR "${CMAKE_INSTALL_BINDIR}")
     set(FOONATHAN_MEMORY_LIBRARY_INSTALL_DIR "${CMAKE_INSTALL_LIBDIR}")
     set(FOONATHAN_MEMORY_ARCHIVE_INSTALL_DIR "${CMAKE_INSTALL_LIBDIR}")
@@ -29,7 +29,7 @@ if(UNIX OR VXWORKS)
     set(FOONATHAN_MEMORY_CMAKE_CONFIG_INSTALL_DIR "${CMAKE_INSTALL_LIBDIR}/foonathan_memory/cmake")
     set(FOONATHAN_MEMORY_ADDITIONAL_FILES_INSTALL_DIR "${CMAKE_INSTALL_DATADIR}/foonathan_memory")
 elseif(WIN32)
-    set(FOONATHAN_MEMORY_INC_INSTALL_DIR "include/foonathan_memory")
+    set(FOONATHAN_MEMORY_INC_INSTALL_DIR "include/")
     set(FOONATHAN_MEMORY_RUNTIME_INSTALL_DIR   "bin")
     set(FOONATHAN_MEMORY_LIBRARY_INSTALL_DIR   "bin")
     set(FOONATHAN_MEMORY_ARCHIVE_INSTALL_DIR   "lib")
