{"name": "foonathan-memory", "version": "0.7.3", "port-version": 2, "description": "STL compatible C++ memory allocator library", "homepage": "https://foonathan.net/doc/memory/", "license": "<PERSON><PERSON><PERSON>", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "default-features": ["tool"], "features": {"tool": {"description": "Build foonathan memory tool"}}}