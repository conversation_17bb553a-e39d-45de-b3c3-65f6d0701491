{"name": "lief", "version-semver": "0.16.1", "description": "LIEF - Library to Instrument Executable Formats", "homepage": "https://lief.quarkslab.com", "license": "Apache-2.0", "dependencies": ["fmt", "frozen", "mbedtls", "spdlog", "tcb-span", "tl-expected", "utfcpp", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "default-features": ["art", "dex", "elf", "enable-json", "logging", "macho", "oat", "pe", "vdex"], "features": {"art": {"description": "Build LIEF with ART module"}, "c-api": {"description": "C API"}, "dex": {"description": "Build LIEF with DEX module"}, "elf": {"description": "Build LIEF with ELF module"}, "enable-json": {"description": "Enable JSON-related APIs", "dependencies": ["<PERSON><PERSON><PERSON>-<PERSON><PERSON>"]}, "extra-warnings": {"description": "Enable extra warning from the compiler"}, "logging": {"description": "Enable logging"}, "logging-debug": {"description": "Enable debug logging"}, "macho": {"description": "Build LIEF with MachO module"}, "oat": {"description": "Build LIEF with OAT module"}, "pe": {"description": "Build LIEF with PE module"}, "use-ccache": {"description": "Use ccache to speed up compilation"}, "vdex": {"description": "Build LIEF with VDEX module"}}}