{"name": "caf", "version": "1.0.2", "description": "an open source implementation of the actor model for C++ featuring lightweight & fast actor implementations, pattern matching for messages, network transparent messaging, and more.", "homepage": "https://github.com/actor-framework/actor-framework", "license": "BSD-3-<PERSON><PERSON>", "dependencies": ["openssl", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}]}