{"$comment": "Automatically generated by scripts/boost/generate-ports.ps1", "name": "boost-xpressive", "version": "1.87.0", "description": "Boost xpressive module", "homepage": "https://www.boost.org/libs/xpressive", "license": "BSL-1.0", "dependencies": [{"name": "boost-assert", "version>=": "1.87.0"}, {"name": "boost-cmake", "version>=": "1.87.0"}, {"name": "boost-config", "version>=": "1.87.0"}, {"name": "boost-conversion", "version>=": "1.87.0"}, {"name": "boost-core", "version>=": "1.87.0"}, {"name": "boost-exception", "version>=": "1.87.0"}, {"name": "boost-fusion", "version>=": "1.87.0"}, {"name": "boost-headers", "version>=": "1.87.0"}, {"name": "boost-integer", "version>=": "1.87.0"}, {"name": "boost-iterator", "version>=": "1.87.0"}, {"name": "boost-lexical-cast", "version>=": "1.87.0"}, {"name": "boost-mpl", "version>=": "1.87.0"}, {"name": "boost-numeric-conversion", "version>=": "1.87.0"}, {"name": "boost-optional", "version>=": "1.87.0"}, {"name": "boost-preprocessor", "version>=": "1.87.0"}, {"name": "boost-proto", "version>=": "1.87.0"}, {"name": "boost-range", "version>=": "1.87.0"}, {"name": "boost-smart-ptr", "version>=": "1.87.0"}, {"name": "boost-spirit", "version>=": "1.87.0"}, {"name": "boost-static-assert", "version>=": "1.87.0"}, {"name": "boost-throw-exception", "version>=": "1.87.0"}, {"name": "boost-type-traits", "version>=": "1.87.0"}, {"name": "boost-typeof", "version>=": "1.87.0"}, {"name": "boost-utility", "version>=": "1.87.0"}]}