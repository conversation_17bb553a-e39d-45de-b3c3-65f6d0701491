diff --git a/zbar/decoder.c b/zbar/decoder.c
index 6c41b7f..cbf0bb7 100644
--- a/zbar/decoder.c
+++ b/zbar/decoder.c
@@ -25,6 +25,7 @@
 #include <stdio.h>  /* snprintf */
 #include <stdlib.h> /* malloc, calloc, free */
 #include <string.h> /* memset, strlen */
+#include <stddef.h> /* offsetof */
 
 #include <zbar.h>
 
@@ -113,7 +114,7 @@ void zbar_decoder_destroy(zbar_decoder_t *dcode)
 
 void zbar_decoder_reset(zbar_decoder_t *dcode)
 {
-    memset(dcode, 0, (long)&dcode->buf_alloc - (long)dcode);
+    memset(dcode, 0, offsetof(zbar_decoder_t, buf_alloc));
 #if ENABLE_EAN == 1
     ean_reset(&dcode->ean);
 #endif
