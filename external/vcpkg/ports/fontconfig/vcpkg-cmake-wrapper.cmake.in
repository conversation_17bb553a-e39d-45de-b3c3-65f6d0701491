_find_package(${ARGS})
if(Fontconfig_FOUND) # theoretically this could be skipped. If the wrapper is installed it should be found!
    if(NOT TARGET Fontconfig::Fontconfig)
        # Simplify wrapper for case of vendored FindFontconfig.cmake
        add_library(Fontconfig::Fontconfig UNKNOWN IMPORTED)
    endif()
    include(SelectLibraryConfigurations)
    find_library(Fontconfig_LIBRARY_DEBUG NAMES fontconfig fontconfigd NAMES_PER_DIR PATH_SUFFIXES lib PATHS "${_INSTALLED_DIR}/debug" NO_DEFAULT_PATH)
    find_library(Fontconfig_LIBRARY_RELEASE NAMES fontconfig NAMES_PER_DIR PATH_SUFFIXES lib PATHS "${_VCPKG_INSTALLED_DIR}/${VCPKG_TARGET_TRIPLET}" NO_DEFAULT_PATH)
    select_library_configurations(Fontconfig)
    set_target_properties(Fontconfig::Fontconfig PROPERTIES
        IMPORTED_CONFIGURATIONS "Release"
        IMPORTED_LOCATION_RELEASE "${Fontconfig_LIBRARY_RELEASE}"
    )
    if(Fontconfig_LIBRARY_DEBUG)
        set_property(TARGET Fontconfig::Fontconfig APPEND PROPERTY IMPORTED_CONFIGURATIONS "Debug")
        set_target_properties(Fontconfig::Fontconfig PROPERTIES IMPORTED_LOCATION_DEBUG "${Fontconfig_LIBRARY_DEBUG}")
    endif()
    find_package(Freetype)
    if(Freetype_FOUND)
        list(APPEND Fontconfig_LIBRARIES "${FREETYPE_LIBRARIES}")
        if(TARGET Freetype::Freetype)
            set_property(TARGET Fontconfig::Fontconfig APPEND PROPERTY INTERFACE_LINK_LIBRARIES "\$<LINK_ONLY:Freetype::Freetype>")
        else()
            # TODO link FREETYPE_LIBRARIES transformed for $<CONFIG:...>.
        endif()
    endif()
    find_package(EXPAT)
    if(EXPAT_FOUND)
        list(APPEND Fontconfig_LIBRARIES "${EXPAT_LIBRARIES}")
        if(TARGET EXPAT::EXPAT)
            set_property(TARGET Fontconfig::Fontconfig APPEND PROPERTY INTERFACE_LINK_LIBRARIES "\$<LINK_ONLY:EXPAT::EXPAT>")
        else()
            # TODO link EXPAT_LIBRARIES transformed for $<CONFIG:...>.
        endif()
    endif()
    if("@VCPKG_TARGET_IS_LINUX@")
        find_library(UUID_LIBRARY_DEBUG NAMES uuid uuidd uuid_d  NAMES_PER_DIR PATH_SUFFIXES lib PATHS "${_INSTALLED_DIR}/debug" NO_DEFAULT_PATH)
        find_library(UUID_LIBRARY_RELEASE NAMES uuid NAMES_PER_DIR PATH_SUFFIXES lib PATHS "${_VCPKG_INSTALLED_DIR}/${VCPKG_TARGET_TRIPLET}" NO_DEFAULT_PATH)
        select_library_configurations(UUID)
        if(UUID_LIBRARIES)
            list(APPEND Fontconfig_LIBRARIES "${UUID_LIBRARIES}")
            if(UUID_LIBRARY_DEBUG)
                set_property(TARGET Fontconfig::Fontconfig APPEND PROPERTY INTERFACE_LINK_LIBRARIES "$<$<NOT:$<CONFIG:DEBUG>>:${UUID_LIBRARY_RELEASE}>")
                set_property(TARGET Fontconfig::Fontconfig APPEND PROPERTY INTERFACE_LINK_LIBRARIES "$<$<CONFIG:DEBUG>:${UUID_LIBRARY_DEBUG}>")
            else()
                set_property(TARGET Fontconfig::Fontconfig APPEND PROPERTY INTERFACE_LINK_LIBRARIES "${UUID_LIBRARY_RELEASE}")
            endif()
        endif()
    endif()
endif()
