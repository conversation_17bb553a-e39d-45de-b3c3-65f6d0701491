diff --git a/fc-cache/meson.build b/fc-cache/meson.build
index 5e40fac..3c3e46b 100644
--- a/fc-cache/meson.build
+++ b/fc-cache/meson.build
@@ -1,6 +1,7 @@
 fccache = executable('fc-cache', ['fc-cache.c', fcstdint_h, alias_headers, ft_alias_headers],
   include_directories: [incbase, incsrc],
   link_with: [libfontconfig],
+  dependencies: [getopt_dep],
   c_args: c_args,
   install: true,
 )
diff --git a/fc-cat/meson.build b/fc-cat/meson.build
index f26e4b8..476c0f9 100644
--- a/fc-cat/meson.build
+++ b/fc-cat/meson.build
@@ -1,6 +1,7 @@
 fccat = executable('fc-cat', ['fc-cat.c', fcstdint_h, alias_headers, ft_alias_headers],
   include_directories: [incbase, incsrc],
   link_with: [libfontconfig],
+  dependencies: [getopt_dep],
   c_args: c_args,
   install: true,
 )
diff --git a/fc-conflist/meson.build b/fc-conflist/meson.build
index f543cf9..f06640b 100644
--- a/fc-conflist/meson.build
+++ b/fc-conflist/meson.build
@@ -1,6 +1,7 @@
 fcconflist = executable('fc-conflist', ['fc-conflist.c', fcstdint_h, alias_headers, ft_alias_headers],
   include_directories: [incbase, incsrc],
   link_with: [libfontconfig],
+  dependencies: [getopt_dep],
   c_args: c_args,
   install: true,
 )
diff --git a/fc-list/meson.build b/fc-list/meson.build
index 2f679d5..4b0fb62 100644
--- a/fc-list/meson.build
+++ b/fc-list/meson.build
@@ -1,6 +1,7 @@
 fclist = executable('fc-list', ['fc-list.c', fcstdint_h, alias_headers, ft_alias_headers],
   include_directories: [incbase, incsrc],
   link_with: [libfontconfig],
+  dependencies: [getopt_dep],
   c_args: c_args,
   install: true,
 )
diff --git a/fc-match/meson.build b/fc-match/meson.build
index aca8bc8..cab4f09 100644
--- a/fc-match/meson.build
+++ b/fc-match/meson.build
@@ -1,6 +1,7 @@
 fcmatch = executable('fc-match', ['fc-match.c', fcstdint_h, alias_headers, ft_alias_headers],
   include_directories: [incbase, incsrc],
   link_with: [libfontconfig],
+  dependencies: [getopt_dep],
   c_args: c_args,
   install: true,
 )
diff --git a/fc-pattern/meson.build b/fc-pattern/meson.build
index 07de245..b957c67 100644
--- a/fc-pattern/meson.build
+++ b/fc-pattern/meson.build
@@ -1,6 +1,7 @@
 fcpattern = executable('fc-pattern', ['fc-pattern.c', fcstdint_h, alias_headers, ft_alias_headers],
   include_directories: [incbase, incsrc],
   link_with: [libfontconfig],
+  dependencies: [getopt_dep],
   c_args: c_args,
   install: true,
 )
diff --git a/fc-query/meson.build b/fc-query/meson.build
index d0f2dd4..940b021 100644
--- a/fc-query/meson.build
+++ b/fc-query/meson.build
@@ -1,7 +1,7 @@
 fcquery = executable('fc-query', ['fc-query.c', fcstdint_h, alias_headers, ft_alias_headers],
   include_directories: [incbase, incsrc],
   link_with: [libfontconfig],
-  dependencies: [freetype_dep],
+  dependencies: [freetype_dep, getopt_dep],
   c_args: c_args,
   install: true,
 )
diff --git a/fc-scan/meson.build b/fc-scan/meson.build
index 4de2134..c5b2b67 100644
--- a/fc-scan/meson.build
+++ b/fc-scan/meson.build
@@ -1,7 +1,7 @@
 fcscan = executable('fc-scan', ['fc-scan.c', fcstdint_h, alias_headers, ft_alias_headers],
   include_directories: [incbase, incsrc],
   link_with: [libfontconfig],
-  dependencies: [freetype_dep],
+  dependencies: [freetype_dep, getopt_dep],
   c_args: c_args,
   install: true,
 )
diff --git a/fc-validate/meson.build b/fc-validate/meson.build
index e2b956e..8902d59 100644
--- a/fc-validate/meson.build
+++ b/fc-validate/meson.build
@@ -1,7 +1,7 @@
 fcvalidate = executable('fc-validate', ['fc-validate.c', fcstdint_h, alias_headers, ft_alias_headers],
   include_directories: [incbase, incsrc],
   link_with: [libfontconfig],
-  dependencies: [freetype_dep],
+  dependencies: [freetype_dep, getopt_dep],
   c_args: c_args,
   install: true,
 )
diff --git a/meson.build b/meson.build
index f616600..6d82a16 100644
--- a/meson.build
+++ b/meson.build
@@ -202,6 +202,14 @@ if cc.links(files('meson-cc-tests/solaris-atomic-operations.c'), name: 'Solaris
   conf.set('HAVE_SOLARIS_ATOMIC_OPS', 1)
 endif
 
+if host_machine.system() == 'windows'
+  conf.set('HAVE_GETOPT', 1)
+  conf.set('HAVE_GETOPT_LONG', 1)
+  getopt_dep = cc.find_library('getopt', required: false)
+else
+  getopt_dep = dependency('', required: false)
+endif
+
 
 # Check iconv support
 iconv_dep = []
