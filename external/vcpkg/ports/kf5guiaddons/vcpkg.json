{"name": "kf5guiaddons", "version": "5.98.0", "description": "Addons to QtGui", "homepage": "https://api.kde.org/frameworks/kguiaddons/html/index.html", "dependencies": ["ecm", "qt5-tools", {"name": "qt5-winextras", "platform": "windows"}, {"name": "qt5-x11extras", "platform": "linux"}, {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "default-features": ["defaults"], "features": {"defaults": {"description": "Default features", "dependencies": [{"name": "kf5guiaddons", "features": ["wayland", "x11"], "platform": "linux"}]}, "wayland": {"description": "Linux-only. Build with support for KeySequenceEditor inhibiting shortcuts on Wayland", "dependencies": ["plasma-wayland-protocols", {"name": "qt5-wayland", "platform": "linux"}]}, "x11": {"description": "Linux-only. Build with support for KeySequenceEditor inhibiting shortcuts on X11", "dependencies": [{"name": "qt5-x11extras", "platform": "linux"}]}}}