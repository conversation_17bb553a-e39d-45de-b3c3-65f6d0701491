diff --git a/KF5GuiAddonsConfig.cmake.in b/KF5GuiAddonsConfig.cmake.in
index ae17f6b..34e41bd 100644
--- a/KF5GuiAddonsConfig.cmake.in
+++ b/KF5GuiAddonsConfig.cmake.in
@@ -10,6 +10,7 @@ if (NOT @BUILD_SHARED_LIBS@)
         endif()
     endif()
     if (@WITH_WAYLAND@)
+        set(QtWaylandScanner_EXECUTABLE "${PACKAGE_PREFIX}/tools/qt5-wayland/bin/qtwaylandscanner" CACHE STRING "QtWaylandScanner workaround")
         find_dependency(Wayland REQUIRED Client)
         find_dependency(Qt@QT_MAJOR_VERSION@WaylandClient @REQUIRED_QT_VERSION@)
         find_dependency(QtWaylandScanner)
