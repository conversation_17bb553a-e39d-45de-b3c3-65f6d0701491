diff --git a/CMakeLists.txt b/CMakeLists.txt
index db28fd9..1692f72 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -545,7 +545,11 @@ CONFIGURE_FILE(${CMAKE_CURRENT_SOURCE_DIR}/src/maconncpp.def.in
                ${CMAKE_CURRENT_SOURCE_DIR}/src/maconncpp.def)
 
 # Dynamic linking is default on non-Windows
-IF(MARIADB_LINK_DYNAMIC)
+IF(1)
+  FIND_PACKAGE(unofficial-libmariadb CONFIG REQUIRED)
+  SET(MARIADB_CLIENT_TARGET_NAME unofficial::libmariadb)
+  ADD_LIBRARY(mariadbclient ALIAS unofficial::libmariadb)
+ELSEIF(MARIADB_LINK_DYNAMIC)
   IF(USE_SYSTEM_INSTALLED_LIB)
     IF(MINGW)
       # I guess -l can be removed here. Also, for build with c/c as submodule this will have to me moved on top level out of this IF's 
@@ -601,6 +605,7 @@ ENDIF()
 
 
 ADD_LIBRARY(${LIBRARY_NAME}_obj OBJECT ${MACPP_SOURCES})
+TARGET_LINK_LIBRARIES(${LIBRARY_NAME}_obj PRIVATE unofficial::libmariadb)
 IF(UNIX)
   SET_TARGET_PROPERTIES(${LIBRARY_NAME}_obj PROPERTIES COMPILE_FLAGS "${CMAKE_SHARED_LIBRARY_C_FLAGS}")
 ENDIF()
