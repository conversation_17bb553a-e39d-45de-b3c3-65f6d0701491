diff --git a/dynamic_mp3.c b/dynamic_mp3.c
index 7c3209f0..ba80ee7a 100644
--- a/dynamic_mp3.c
+++ b/dynamic_mp3.c
@@ -63,7 +63,7 @@ int Mix_InitMP3(void)
 #else
 		FUNCTION_LOADER(mpg123_read, int (*)(mpg123_handle *mh, unsigned char *outmemory, size_t outmemsize, size_t *done ))
 #endif
-		FUNCTION_LOADER(mpg123_replace_reader_handle, int (*)( mpg123_handle *mh, ssize_t (*r_read) (void *, void *, size_t), off_t (*r_lseek)(void *, off_t, int), void (*cleanup)(void*) ))
+		FUNCTION_LOADER(mpg123_replace_reader_handle, int (*)( mpg123_handle *mh, mpg123_ssize_t (*r_read) (void *, void *, size_t), off_t (*r_lseek)(void *, off_t, int), void (*cleanup)(void*) ))
 		FUNCTION_LOADER(mpg123_seek, off_t (*)( mpg123_handle *mh, off_t sampleoff, int whence ))
 		FUNCTION_LOADER(mpg123_strerror, const char* (*)(mpg123_handle *mh))
 		if (mpg123.mpg123_init() != MPG123_OK) {
diff --git a/dynamic_mp3.h b/dynamic_mp3.h
index 2e4c48af..ad63bf76 100644
--- a/dynamic_mp3.h
+++ b/dynamic_mp3.h
@@ -43,7 +43,7 @@ typedef struct {
 #else
 	int (*mpg123_read)(mpg123_handle *mh, unsigned char *outmemory, size_t outmemsize, size_t *done );
 #endif
-	int (*mpg123_replace_reader_handle)( mpg123_handle *mh, ssize_t (*r_read) (void *, void *, size_t), off_t (*r_lseek)(void *, off_t, int), void (*cleanup)(void*) );
+	int (*mpg123_replace_reader_handle)( mpg123_handle *mh, mpg123_ssize_t (*r_read) (void *, void *, size_t), off_t (*r_lseek)(void *, off_t, int), void (*cleanup)(void*) );
 	off_t (*mpg123_seek)( mpg123_handle *mh, off_t sampleoff, int whence );
 	const char* (*mpg123_strerror)(mpg123_handle *mh);
 } mpg123_loader;
diff --git a/music_mpg.c b/music_mpg.c
index 531615a2..7d92c28b 100644
--- a/music_mpg.c
+++ b/music_mpg.c
@@ -67,8 +67,8 @@ mpg_err(mpg123_handle* mpg, int code)
 
 /* we're gonna override mpg123's I/O with these wrappers for RWops */
 static
-ssize_t rwops_read(void* p, void* dst, size_t n) {
-    return (ssize_t)MP3_RWread((struct mp3file_t *)p, dst, 1, n);
+mpg123_ssize_t rwops_read(void* p, void* dst, size_t n) {
+    return (mpg123_ssize_t)MP3_RWread((struct mp3file_t *)p, dst, 1, n);
 }
 
 static
