<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{F7E944B3-0815-40CD-B3E4-90B2A15B0E33}</ProjectGuid>
    <RootNamespace>SDL_mixer</RootNamespace>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>@LIB_TYPE@</ConfigurationType>
    <PlatformToolset>v141</PlatformToolset>
    <UseOfMfc>false</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>@LIB_TYPE@</ConfigurationType>
    <PlatformToolset>v141</PlatformToolset>
    <UseOfMfc>false</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>@LIB_TYPE@</ConfigurationType>
    <PlatformToolset>v141</PlatformToolset>
    <UseOfMfc>false</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>@LIB_TYPE@</ConfigurationType>
    <PlatformToolset>v141</PlatformToolset>
    <UseOfMfc>false</UseOfMfc>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.CPP.UpgradeFromVC60.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.CPP.UpgradeFromVC60.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.CPP.UpgradeFromVC60.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.CPP.UpgradeFromVC60.props" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>15.0.28307.799</_ProjectFileVersion>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <OutDir>.\Debug\</OutDir>
    <IntDir>.\Debug\</IntDir>
    <LinkIncremental>true</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <OutDir>$(Platform)\$(Configuration)\</OutDir>
    <IntDir>$(Platform)\$(Configuration)\</IntDir>
    <LinkIncremental>true</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <OutDir>.\Release\</OutDir>
    <IntDir>.\Release\</IntDir>
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <OutDir>$(Platform)\$(Configuration)\</OutDir>
    <IntDir>$(Platform)\$(Configuration)\</IntDir>
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Midl>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TargetEnvironment>Win32</TargetEnvironment>
      <TypeLibraryName>.\Debug/SDL_mixer.tlb</TypeLibraryName>
      <HeaderFileName />
    </Midl>
    <ClCompile>
      <AdditionalOptions>/D OGG_DYNAMIC=\"vorbisfile.dll\" %(AdditionalOptions)</AdditionalOptions>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>..\timidity;..\native_midi;@CURRENT_PACKAGES_DIR@/include/SDL;@CURRENT_INSTALLED_DIR@/include/SDL;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_DEBUG;WIN32;_WINDOWS;_CRT_SECURE_NO_WARNINGS;WAV_MUSIC;MOD_MUSIC;MOD_DYNAMIC="mikmod.dll";OGG_MUSIC;OGG_DYNAMIC="vorbisfile.dll";FLAC_MUSIC;FLAC_DYNAMIC="FLAC.dll";MP3_MUSIC;MPG123_DYNAMIC="mpg123.dll";MID_MUSIC;USE_TIMIDITY_MIDI;USE_NATIVE_MIDI;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MinimalRebuild>true</MinimalRebuild>
      <RuntimeLibrary>@CRT_TYPE_DBG@</RuntimeLibrary>
      <PrecompiledHeaderOutputFile>.\Debug/SDL_mixer.pch</PrecompiledHeaderOutputFile>
      <WarningLevel>Level3</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <DebugInformationFormat>OldStyle</DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0409</Culture>
    </ResourceCompile>
    <Link>
      <AdditionalDependencies>winmm.lib;SDL.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <ProgramDatabaseFile>.\Debug/SDL_mixer.pdb</ProgramDatabaseFile>
      <SubSystem>Windows</SubSystem>
      <TargetMachine>MachineX86</TargetMachine>
      <AdditionalLibraryDirectories>@CURRENT_INSTALLED_DIR@/lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
    </Link>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\Debug/SDL_mixer.bsc</OutputFile>
    </Bscmake>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Midl>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TargetEnvironment>X64</TargetEnvironment>
      <TypeLibraryName>.\Debug/SDL_mixer.tlb</TypeLibraryName>
      <HeaderFileName />
    </Midl>
    <ClCompile>
      <AdditionalOptions>/D OGG_DYNAMIC=\"vorbisfile.dll\" %(AdditionalOptions)</AdditionalOptions>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>..\timidity;..\native_midi;@CURRENT_PACKAGES_DIR@/include/SDL;@CURRENT_INSTALLED_DIR@/include/SDL;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_DEBUG;WIN32;_WINDOWS;_CRT_SECURE_NO_WARNINGS;WAV_MUSIC;MOD_MUSIC;MOD_DYNAMIC="mikmod.dll";OGG_MUSIC;OGG_DYNAMIC="vorbisfile.dll";FLAC_MUSIC;FLAC_DYNAMIC="FLAC.dll";MP3_MUSIC;MPG123_DYNAMIC="mpg123.dll";MID_MUSIC;USE_TIMIDITY_MIDI;USE_NATIVE_MIDI;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MinimalRebuild>true</MinimalRebuild>
      <RuntimeLibrary>@CRT_TYPE_DBG@</RuntimeLibrary>
      <PrecompiledHeaderOutputFile>.\Debug/SDL_mixer.pch</PrecompiledHeaderOutputFile>
      <WarningLevel>Level3</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <DebugInformationFormat>OldStyle</DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0409</Culture>
    </ResourceCompile>
    <Link>
      <AdditionalDependencies>winmm.lib;SDL.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <ProgramDatabaseFile>.\Debug/SDL_mixer.pdb</ProgramDatabaseFile>
      <SubSystem>Windows</SubSystem>
      <TargetMachine>MachineX64</TargetMachine>
      <AdditionalLibraryDirectories>@CURRENT_INSTALLED_DIR@/lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
    </Link>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\Debug/SDL_mixer.bsc</OutputFile>
    </Bscmake>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Midl>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TargetEnvironment>Win32</TargetEnvironment>
      <TypeLibraryName>.\Release/SDL_mixer.tlb</TypeLibraryName>
      <HeaderFileName />
    </Midl>
    <ClCompile>
      <AdditionalOptions>/D OGG_DYNAMIC=\"vorbisfile.dll\" %(AdditionalOptions)</AdditionalOptions>
      <Optimization>MaxSpeed</Optimization>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <AdditionalIncludeDirectories>..\timidity;..\native_midi;@CURRENT_PACKAGES_DIR@/include/SDL;@CURRENT_INSTALLED_DIR@/include/SDL;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>NDEBUG;WIN32;_WINDOWS;_CRT_SECURE_NO_WARNINGS;WAV_MUSIC;MOD_MUSIC;MOD_DYNAMIC="mikmod.dll";OGG_MUSIC;OGG_DYNAMIC="vorbisfile.dll";FLAC_MUSIC;FLAC_DYNAMIC="FLAC.dll";MP3_MUSIC;MPG123_DYNAMIC="mpg123.dll";MID_MUSIC;USE_TIMIDITY_MIDI;USE_NATIVE_MIDI;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <StringPooling>true</StringPooling>
      <RuntimeLibrary>@CRT_TYPE_REL@</RuntimeLibrary>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <PrecompiledHeaderOutputFile>.\Release/SDL_mixer.pch</PrecompiledHeaderOutputFile>
      <WarningLevel>Level3</WarningLevel>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0409</Culture>
    </ResourceCompile>
    <Link>
      <AdditionalDependencies>winmm.lib;SDL.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <ProgramDatabaseFile>.\Release/SDL_mixer.pdb</ProgramDatabaseFile>
      <SubSystem>Windows</SubSystem>
      <TargetMachine>MachineX86</TargetMachine>
      <AdditionalLibraryDirectories>@CURRENT_INSTALLED_DIR@/lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
    </Link>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\Release/SDL_mixer.bsc</OutputFile>
    </Bscmake>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Midl>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TargetEnvironment>X64</TargetEnvironment>
      <TypeLibraryName>.\Release/SDL_mixer.tlb</TypeLibraryName>
      <HeaderFileName />
    </Midl>
    <ClCompile>
      <AdditionalOptions>/D OGG_DYNAMIC=\"vorbisfile.dll\" %(AdditionalOptions)</AdditionalOptions>
      <Optimization>MaxSpeed</Optimization>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <AdditionalIncludeDirectories>..\timidity;..\native_midi;@CURRENT_PACKAGES_DIR@/include/SDL;@CURRENT_INSTALLED_DIR@/include/SDL;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>NDEBUG;WIN32;_WINDOWS;_CRT_SECURE_NO_WARNINGS;WAV_MUSIC;MOD_MUSIC;MOD_DYNAMIC="mikmod.dll";OGG_MUSIC;OGG_DYNAMIC="vorbisfile.dll";FLAC_MUSIC;FLAC_DYNAMIC="FLAC.dll";MP3_MUSIC;MPG123_DYNAMIC="mpg123.dll";MID_MUSIC;USE_TIMIDITY_MIDI;USE_NATIVE_MIDI;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <StringPooling>true</StringPooling>
      <RuntimeLibrary>@CRT_TYPE_REL@</RuntimeLibrary>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <PrecompiledHeaderOutputFile>.\Release/SDL_mixer.pch</PrecompiledHeaderOutputFile>
      <WarningLevel>Level3</WarningLevel>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0409</Culture>
    </ResourceCompile>
    <Link>
      <AdditionalDependencies>winmm.lib;SDL.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <ProgramDatabaseFile>.\Release/SDL_mixer.pdb</ProgramDatabaseFile>
      <SubSystem>Windows</SubSystem>
      <TargetMachine>MachineX64</TargetMachine>
      <AdditionalLibraryDirectories>@CURRENT_INSTALLED_DIR@/lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
    </Link>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\Release/SDL_mixer.bsc</OutputFile>
    </Bscmake>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="..\dynamic_flac.c" />
    <ClCompile Include="..\dynamic_fluidsynth.c" />
    <ClCompile Include="..\dynamic_mod.c" />
    <ClCompile Include="..\dynamic_mp3.c" />
    <ClCompile Include="..\dynamic_ogg.c" />
    <ClCompile Include="..\effects_internal.c" />
    <ClCompile Include="..\effect_position.c" />
    <ClCompile Include="..\effect_stereoreverse.c" />
    <ClCompile Include="..\load_aiff.c" />
    <ClCompile Include="..\load_flac.c" />
    <ClCompile Include="..\load_ogg.c" />
    <ClCompile Include="..\load_voc.c" />
    <ClCompile Include="..\mixer.c" />
    <ClCompile Include="..\mp3utils.c" />
    <ClCompile Include="..\music.c" />
    <ClCompile Include="..\music_cmd.c" />
    <ClCompile Include="..\music_flac.c" />
    <ClCompile Include="..\music_fluidsynth.c" />
    <ClCompile Include="..\music_mad.c" />
    <ClCompile Include="..\music_mod.c" />
    <ClCompile Include="..\music_mpg.c" />
    <ClCompile Include="..\music_ogg.c" />
    <ClCompile Include="..\wavestream.c" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\dynamic_flac.h" />
    <ClInclude Include="..\dynamic_fluidsynth.h" />
    <ClInclude Include="..\dynamic_mod.h" />
    <ClInclude Include="..\dynamic_mp3.h" />
    <ClInclude Include="..\dynamic_ogg.h" />
    <ClInclude Include="..\effects_internal.h" />
    <ClInclude Include="..\load_aiff.h" />
    <ClInclude Include="..\load_flac.h" />
    <ClInclude Include="..\load_ogg.h" />
    <ClInclude Include="..\load_voc.h" />
    <ClInclude Include="..\mp3utils.h" />
    <ClInclude Include="..\music_cmd.h" />
    <ClInclude Include="..\music_flac.h" />
    <ClInclude Include="..\music_fluidsynth.h" />
    <ClInclude Include="..\music_mad.h" />
    <ClInclude Include="..\music_mod.h" />
    <ClInclude Include="..\music_mpg.h" />
    <ClInclude Include="..\music_ogg.h" />
    <ClInclude Include="..\SDL_mixer.h" />
    <ClInclude Include="..\wavestream.h" />
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="Version.rc" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="native_midi\native_midi.vcxproj">
      <Project>{ebda67ca-4a23-4f22-bfbc-b8dbe0580d4f}</Project>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
    </ProjectReference>
    <ProjectReference Include="timidity\timidity.vcxproj">
      <Project>{b162b6f1-e876-4d5f-a1f6-e3a6dc2f4a2c}</Project>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>