{"$comment": "Use scripts/update_suitesparse.py to update all SuiteSparse ports", "name": "suitesparse-umfpack", "version-semver": "6.3.5", "description": "UMFPACK: <PERSON><PERSON><PERSON> solving sparse linear systems via LU factorization in SuiteSparse", "homepage": "https://people.engr.tamu.edu/davis/suitesparse.html", "license": "GPL-2.0-or-later", "dependencies": ["suitesparse-amd", "suitesparse-config", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "default-features": ["cholmod"], "features": {"cholmod": {"description": "Use CHOLMOD", "dependencies": ["suitesparse-cholmod"]}}}