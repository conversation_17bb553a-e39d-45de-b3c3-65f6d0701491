diff --git a/src/ci/MEDfileExist.c b/src/ci/MEDfileExist.c
index f4f1683..6c902ae 100644
--- a/src/ci/MEDfileExist.c
+++ b/src/ci/MEDfileExist.c
@@ -25,6 +25,7 @@
 #define	W_OK	2		/* Test for write permission.  */
 #define	X_OK	1		/* Test for execute permission.  */
 #define	F_OK	0		/* Test for existence.  */
+#include <io.h>
 #else
 #include <unistd.h>
 #endif
diff --git a/src/misc/_MEDaccess.c b/src/misc/_MEDaccess.c
index 481203d..4c18b2a 100644
--- a/src/misc/_MEDaccess.c
+++ b/src/misc/_MEDaccess.c
@@ -17,6 +17,7 @@
 
 #if defined(WIN32)
 #include <windows.h>
+#include <io.h>
 #else
 #include <unistd.h>
 #endif
diff --git a/tools/mdump/mdump2.c b/tools/mdump/mdump2.c
index 820a5f0..c711a8b 100644
--- a/tools/mdump/mdump2.c
+++ b/tools/mdump/mdump2.c
@@ -80,6 +80,7 @@ extern "C" {
 
 #ifdef PPRO_NT
 #define F_OK 0
+#include <io.h>
 #else
 #include <unistd.h>
 #endif
diff --git a/tools/mdump/mdump3.c b/tools/mdump/mdump3.c
index 228fb81..cb648b5 100644
--- a/tools/mdump/mdump3.c
+++ b/tools/mdump/mdump3.c
@@ -81,6 +81,7 @@ extern "C" {
 #ifdef PPRO_NT
 #define F_OK 0
 #define snprintf _snprintf
+#include <io.h>
 #else
 #include <unistd.h>
 #endif
diff --git a/tools/mdump/mdump4.c b/tools/mdump/mdump4.c
index 6f2fc3d..725cd14 100644
--- a/tools/mdump/mdump4.c
+++ b/tools/mdump/mdump4.c
@@ -81,6 +81,7 @@ extern "C" {
 #ifdef PPRO_NT
 #define F_OK 0
 #define snprintf _snprintf
+#include <io.h>
 #else
 #include <unistd.h>
 #endif
