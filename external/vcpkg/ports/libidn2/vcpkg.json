{"name": "libidn2", "version": "2.3.7", "port-version": 2, "description": "GNU Libidn is an implementation of the Stringprep, Punycode and IDNA 2003 specifications. Libidn's purpose is to encode and decode internationalized domain names.", "homepage": "https://www.gnu.org/software/libidn/", "license": null, "dependencies": ["libiconv", "libunistring", {"name": "vcpkg-make", "host": true}], "features": {"nls": {"description": "Enable native language support", "dependencies": [{"name": "gettext", "host": true, "features": ["tools"]}, "gettext-libintl"]}}}