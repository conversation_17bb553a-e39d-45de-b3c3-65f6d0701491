diff --git a/Xtrans.h b/Xtrans.h
index fbf385e01..e52e7cba2 100644
--- a/Xtrans.h
+++ b/Xtrans.h
@@ -50,6 +50,10 @@ from The Open Group.
 #ifndef _XTRANS_H_
 #define _XTRANS_H_
 
+#if defined(_WIN32) && !defined(WIN32)
+# define WIN32 _WIN32
+#endif
+
 #include <X11/Xfuncproto.h>
 #include <X11/Xos.h>
 #include <X11/Xmd.h>
diff --git a/Xtransint.h b/Xtransint.h
index 2156bd52f..735b54301 100644
--- a/Xtransint.h
+++ b/Xtransint.h
@@ -72,7 +72,7 @@ from The Open Group.
 #  define XTRANSDEBUG 1
 #endif
 
-#ifdef WIN32
+#if defined(WIN32) || defined(_WIN32)
 # define _WILLWINSOCK_
 #endif
 
diff --git a/Xtranssock.c b/Xtranssock.c
index c29390eaa..f2ef365cc 100644
--- a/Xtranssock.c
+++ b/Xtranssock.c
@@ -74,6 +74,10 @@ from the copyright holders.
 #include <X11/Xthreads.h>
 #endif
 
+#if defined(_WIN32) && !defined(WIN32)
+# define WIN32 _WIN32
+#endif
+
 #ifndef WIN32
 
 #if defined(TCPCONN) || defined(UNIXCONN)
diff --git a/Xtransutil.c b/Xtransutil.c
index f15be243c..cc67315e6 100644
--- a/Xtransutil.c
+++ b/Xtransutil.c
@@ -54,6 +54,10 @@ from The Open Group.
  * the internal implementation.
  */
 
+#if defined(_WIN32) && !defined(WIN32)
+# define WIN32 _WIN32
+#endif
+
 #ifdef XTHREADS
 #include <X11/Xthreads.h>
 #endif
