{"name": "qtimageformats", "version": "6.8.3", "description": "The Qt Image Formats add-on module provides optional support for other image file formats.", "homepage": "https://www.qt.io/", "license": null, "dependencies": [{"name": "qtbase", "default-features": false, "features": ["gui"]}], "default-features": ["jasper", "tiff", "webp"], "features": {"jasper": {"description": "Use jasper", "dependencies": ["jasper"]}, "tiff": {"description": "Use TIFF", "dependencies": [{"name": "tiff", "default-features": false}]}, "webp": {"description": "Use WebP", "dependencies": ["libwebp"]}}}