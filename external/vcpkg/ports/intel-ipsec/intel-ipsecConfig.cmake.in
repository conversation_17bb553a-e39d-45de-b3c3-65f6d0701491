if(TARGET IPSEC::ipsec)
    return()
endif()

get_filename_component(_IMPORT_PREFIX "${CMAKE_CURRENT_LIST_FILE}" PATH) # intel-ipsec
get_filename_component(_IMPORT_PREFIX "${_IMPORT_PREFIX}" PATH) # share
get_filename_component(_IMPORT_PREFIX "${_IMPORT_PREFIX}" PATH) # package root
add_library(IPSEC::ipsec @INTEL_IPSEC_STATIC_OR_SHARED@ IMPORTED)
set_target_properties(IPSEC::ipsec PROPERTIES INTERFACE_INCLUDE_DIRECTORIES "${_IMPORT_PREFIX}/include")
set(IPSEC_ipsec_FOUND 1)

get_filename_component(_DIR "${CMAKE_CURRENT_LIST_FILE}" PATH)
file(GLOB CONFIG_FILES "${_DIR}/intel-ipsec-targets-*.cmake")
foreach(f ${CONFIG_FILES})
    include(${f})
endforeach()
