{"name": "shader-slang", "version": "2025.5", "description": "Slang is a shading language that makes it easier to build and maintain large shader codebases in a modular and extensible fashion, while also maintaining the highest possible performance on modern GPUs and graphics APIs. Slang is based on years of collaboration between researchers at NVIDIA, Carnegie Mellon University, and Stanford.", "homepage": "https://github.com/shader-slang/slang", "license": null, "supports": "(arm64 | x64) & (linux | osx | windows) & !uwp"}