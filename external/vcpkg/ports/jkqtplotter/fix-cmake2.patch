
diff --git a/lib/jkqtmath/CMakeLists.txt b/lib/jkqtmath/CMakeLists.txt
index 716254ac8..835b4e8cb 100644
--- a/lib/jkqtmath/CMakeLists.txt
+++ b/lib/jkqtmath/CMakeLists.txt
@@ -110,7 +110,7 @@ endif()
 if(JKQtPlotter_BUILD_STATIC_LIBS)
     add_library(${lib_name} STATIC ${SOURCES} ${HEADERS})
     JKQtCommon_setDefaultLibOptions(${lib_name})
-    target_link_libraries(${libsh_name} PUBLIC JKQTCommonLib)
+    target_link_libraries(${lib_name} PUBLIC JKQTCommonLib)
     set_property(TARGET ${lib_name} PROPERTY OUTPUT_NAME  "${lib_name_decorated}")
     write_basic_package_version_file(${CMAKE_CURRENT_BINARY_DIR}/${lib_name}Version.cmake
                                      VERSION ${PROJECT_VERSION} 
