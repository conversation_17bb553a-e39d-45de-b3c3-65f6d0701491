{"name": "jaeger-client-cpp", "version-semver": "0.7.0", "port-version": 2, "description": "C++ OpenTracing binding for <PERSON><PERSON><PERSON> https://jaegertracing.io/", "homepage": "https://github.com/jaegertracing/jaeger-client-cpp", "supports": "!arm64", "dependencies": ["<PERSON><PERSON><PERSON>-<PERSON><PERSON>", "opentracing", "thrift", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}, "yaml-cpp"]}