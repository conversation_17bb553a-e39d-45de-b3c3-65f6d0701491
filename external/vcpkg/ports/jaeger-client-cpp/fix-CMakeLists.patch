diff --git a/CMakeLists.txt b/CMakeLists.txt
index 53e4598..4220910 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -60,7 +60,7 @@ if(CMAKE_CXX_COMPILER_ID STREQUAL "GNU" AND
 endif()
 
 hunter_add_package(thrift)
-find_package(thrift ${hunter_config} REQUIRED)
+find_package(Thrift CONFIG REQUIRED)
 if(HUNTER_ENABLED)
   list(APPEND LIBS thrift::thrift_static)
 else()
@@ -74,7 +74,7 @@ hunter_add_package(opentracing-cpp)
 # OpenTracingConfig.cmake file
 find_package(OpenTracing CONFIG REQUIRED)
 # Under Windows, link dynamically with OpenTracing
-if (WIN32)
+if (TARGET OpenTracing::opentracing)
   list(APPEND LIBS OpenTracing::opentracing)
   set(OPENTRACING_LIB OpenTracing::opentracing)
 else()
@@ -273,6 +273,9 @@ if(JAEGERTRACING_PLUGIN)
   file(WRITE ${CMAKE_CURRENT_BINARY_DIR}/export.map
     "{ global: OpenTracingMakeTracerFactory; local: *; };")
   add_library(jaegertracing_plugin MODULE ${SRC})
+  if (WIN32)
+    target_link_libraries(jaegertracing_plugin PUBLIC Iphlpapi Ws2_32)
+  endif()
   add_lib_deps(jaegertracing_plugin)
   target_link_libraries(jaegertracing_plugin PUBLIC
     -static-libgcc
@@ -418,7 +421,7 @@ include(GNUInstallDirs)
 #   * <prefix>/lib*/cmake/<PROJECT-NAME>
 #   * <prefix>/lib*/
 #   * <prefix>/include/
-set(config_install_dir "${CMAKE_INSTALL_LIBDIR}/cmake/${PROJECT_NAME}")
+set(config_install_dir "${CMAKE_INSTALL_DATAROOTDIR}/jaeger-client-cpp")
 
 set(generated_dir "${CMAKE_CURRENT_BINARY_DIR}/generated")
 
