diff --git a/DuiLib/CMakeLists.txt b/DuiLib/CMakeLists.txt
index 4961174..96b8fe4 100644
--- a/DuiLib/CMakeLists.txt
+++ b/DuiLib/CMakeLists.txt
@@ -14,6 +14,11 @@ include_directories(${CMAKE_CURRENT_SOURCE_DIR}/Core)
 include_directories(${CMAKE_CURRENT_SOURCE_DIR}/Layout)
 include_directories(${CMAKE_CURRENT_SOURCE_DIR}/Utils)
 
+if (MSVC)
+  SET(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} /source-charset:.936")
+  SET(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /source-charset:.936")
+endif()
+
 set(LIBRARY_OUTPUT_PATH ${PROJECT_BINARY_DIR}/lib)
 add_library(duilib SHARED ${Control_src} ${Core_src} ${Layout_src} ${Utils_src} ${Root_src})
 
diff --git a/DuiLib/Control/UIGifAnim.cpp b/DuiLib/Control/UIGifAnim.cpp
index 870c9da..61aa32f 100644
--- a/DuiLib/Control/UIGifAnim.cpp
+++ b/DuiLib/Control/UIGifAnim.cpp
@@ -319,7 +319,6 @@ namespace DuiLib
 
 		while (!pData)
 		{
-			//读不到图片, 则直接去读取bitmap.m_lpstr指向的路径
 			HANDLE hFile = ::CreateFile(pstrGifPath, GENERIC_READ, FILE_SHARE_READ, NULL, OPEN_EXISTING, \
 				FILE_ATTRIBUTE_NORMAL, NULL);
 			if( hFile == INVALID_HANDLE_VALUE ) break;
