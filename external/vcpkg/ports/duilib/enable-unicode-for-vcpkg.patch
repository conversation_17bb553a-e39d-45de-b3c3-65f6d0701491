diff --git a/DuiLib/CMakeLists.txt b/DuiLib/CMakeLists.txt
index 835d430..b178984 100644
--- a/DuiLib/CMakeLists.txt
+++ b/DuiLib/CMakeLists.txt
@@ -2,6 +2,9 @@
 #Author <PERSON>(monk<PERSON>@gmail.com)
 #Created: 2012/09/16
 
+#if use vcpkg to build, need to add extra unicode definitions
+add_definitions(-DUNICODE -D_UNICODE)
+
 aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR} Root_src)
 aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/Control Control_src)
 aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/Core Core_src)

