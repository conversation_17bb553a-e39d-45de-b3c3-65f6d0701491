diff --git a/DuiLib/CMakeLists.txt b/DuiLib/CMakeLists.txt
index 6a4da5a..4961174 100644
--- a/DuiLib/CMakeLists.txt
+++ b/DuiLib/CMakeLists.txt
@@ -18,5 +18,5 @@ set(LIBRARY_OUTPUT_PATH ${PROJECT_BINARY_DIR}/lib)
 add_library(duilib SHARED ${Control_src} ${Core_src} ${Layout_src} ${Utils_src} ${Root_src})
 
 add_definitions(-DUILIB_EXPORTS)
-target_link_libraries(duilib comctl32)
+target_link_libraries(duilib comctl32 gdi32 user32)
 set_target_properties(duilib PROPERTIES OUTPUT_NAME "duilib")
