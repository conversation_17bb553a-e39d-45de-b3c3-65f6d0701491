diff --git a/CMakeLists.txt b/CMakeLists.txt
index 4663a65..b91077a 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -95,19 +95,25 @@ message("## ${OATPP_THIS_MODULE_NAME} module. Resolving dependencies...\n")
 ##############################
 ## Find Libressl dependency
 
-include(FindPkgConfig)
-list(APPEND CMAKE_MODULE_PATH "${CMAKE_CURRENT_LIST_DIR}/cmake/module")
-
-find_package(LibreSSL 3.0.0 REQUIRED)
-
-message("LIBRESSL_INCLUDE_DIR=${LIBRESSL_INCLUDE_DIR}")
-message("LIBRESSL_TLS_LIBRARY=${LIBRESSL_TLS_LIBRARY}")
-message("LIBRESSL_SSL_LIBRARY=${LIBRESSL_SSL_LIBRARY}")
-message("LIBRESSL_CRYPTO_LIBRARY=${LIBRESSL_CRYPTO_LIBRARY}")
-message("LIBRESSL_LIBRARIES=${LIBRESSL_LIBRARIES}")
-message("LIBRESSL_VERSION=${LIBRESSL_VERSION}")
-
-message("\n############################################################################\n")
+find_file(LIBRESSL_INCLUDE_DIR tls.h)
+if (NOT LIBRESSL_INCLUDE_DIR)
+    find_file(_OPENSSL_SSL_INCLUDE_FILE openssl/ssl.h)
+    if (NOT _OPENSSL_SSL_INCLUDE_FILE)
+        message(FATAL_ERROR "LibreSSL and OpenSSL not found. LibreSSL must be installed.")
+    else()
+        message(FATAL_ERROR "OpenSSL installed instead of LibreSSL. oatpp-libressl requires LibreSSL.")
+    endif()
+endif()
+get_filename_component(LIBRESSL_INCLUDE_DIR ${LIBRESSL_INCLUDE_DIR} DIRECTORY)
+find_library(LIBRESSL_TLS_LIBRARY NAMES tls tls-21 tls-20 tls-19)
+find_library(LIBRESSL_SSL_LIBRARY NAMES ssl ssl-49 ssl-48 ssl-47)
+find_library(LIBRESSL_CRYPTO_LIBRARY NAMES crypto crypto-47 crypto-46 crypto-45)
+find_library(LIBRESSL_LIBRARIES "${LIBRESSL_CRYPTO_LIBRARY};${LIBRESSL_SSL_LIBRARY};${LIBRESSL_TLS_LIBRARY}")
+message(STATUS "LIBRESSL_INCLUDE_DIR=${LIBRESSL_INCLUDE_DIR}")
+message(STATUS "LIBRESSL_TLS_LIBRARY=${LIBRESSL_TLS_LIBRARY}")
+message(STATUS "LIBRESSL_SSL_LIBRARY=${LIBRESSL_SSL_LIBRARY}")
+message(STATUS "LIBRESSL_CRYPTO_LIBRARY=${LIBRESSL_CRYPTO_LIBRARY}")
+message(STATUS "LIBRESSL_LIBRARIES=${LIBRESSL_LIBRARIES}")
 
 ###################################################################################################
 ## define targets
diff --git a/src/CMakeLists.txt b/src/CMakeLists.txt
index ace3b13..489fbb1 100644
--- a/src/CMakeLists.txt
+++ b/src/CMakeLists.txt
@@ -30,10 +30,12 @@ target_include_directories(${OATPP_THIS_MODULE_NAME}
         PUBLIC $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}>
 )
 
+target_include_directories(${OATPP_THIS_MODULE_NAME}
+        SYSTEM PRIVATE "${LIBRESSL_INCLUDE_DIR}"
+)
+
 target_link_libraries(${OATPP_THIS_MODULE_NAME}
-        PUBLIC LibreSSL::TLS
-        PUBLIC LibreSSL::SSL
-        PUBLIC LibreSSL::Crypto
+        PUBLIC "${LIBRESSL_TLS_LIBRARY}" "${LIBRESSL_SSL_LIBRARY}" "${LIBRESSL_CRYPTO_LIBRARY}"
 )
 
 #######################################################################################################
