{"name": "libfreenect2", "version": "0.2.1", "port-version": 2, "description": "Open source drivers for the Kinect for Windows v2 device", "homepage": "https://github.com/OpenKinect/libfreenect2", "license": "GPL-2.0-only OR Apache-2.0", "supports": "!xbox", "dependencies": ["libjpeg-turbo", "libusb", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "default-features": ["opengl"], "features": {"opencl": {"description": "OpenCL support for libfreenect2", "dependencies": ["opencl"]}, "opengl": {"description": "OpenGL support for libfreenect2", "dependencies": ["glfw3", "opengl"]}, "openni2": {"description": "OpenNI2 support for libfreenect2", "dependencies": ["openni2"]}}}