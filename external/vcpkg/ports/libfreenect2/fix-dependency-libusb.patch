diff --git a/CMakeLists.txt b/CMakeLists.txt
index 3648fb5..43c3d28 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -91,7 +91,7 @@ SET(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${PROJECT_BINARY_DIR}/lib)
 
 # dependencies
 FIND_PACKAGE(PkgConfig)    # try find PKGConfig as it will be used if found
-FIND_PACKAGE(LibUSB REQUIRED)
+pkg_check_modules(libusb REQUIRED IMPORTED_TARGET libusb-1.0)
 
 # Add includes
 INCLUDE_DIRECTORIES(
@@ -99,7 +99,7 @@ INCLUDE_DIRECTORIES(
   "${MY_DIR}/include/internal"
   ${PROJECT_BINARY_DIR} # for generated headers
   ${LIBFREENECT2_THREADING_INCLUDE_DIR}
-  ${LibUSB_INCLUDE_DIRS}
+  ${libusb_INCLUDE_DIRS}
 )
 
 SET(RESOURCES_INC_FILE "${PROJECT_BINARY_DIR}/resources.inc.h")
@@ -157,7 +157,7 @@ SET(SOURCES
 )
 
 SET(LIBRARIES
-  ${LibUSB_LIBRARIES}
+  PkgConfig::libusb
   ${LIBFREENECT2_THREADING_LIBRARIES}
 )
 
