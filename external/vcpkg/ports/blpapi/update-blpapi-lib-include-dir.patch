diff --git a/cmake/blpapiTargets-release.cmake b/cmake/blpapiTargets-release.cmake
index 12bcaa6..af5c135 100644
--- a/cmake/blpapiTargets-release.cmake
+++ b/cmake/blpapiTargets-release.cmake
@@ -5,21 +5,18 @@
 # Import targets for configuration "Release".
 
 # Path to blpapi shared object in relation to blpapiConfig.cmake:
-# 1. On Windows
-#    a. For release ../lib/
-# 2. On other platforms
-#    a. For release ../<SystemName>/. For example for linux it is ../Linux/
+# 1. For release ../../lib/
 
 # Windows
 if(WIN32)
   set(_BLPAPI_SHARED_OBJ_NAME "${_BLPAPI_PREFIX}_${_ARCH}.dll")
   set(_BLPAPI_SHARED_IMP_OBJ_NAME "${_BLPAPI_PREFIX}_${_ARCH}.lib")
-  set(_BLPAPI_SHARED_OBJ "${_BLPAPI_CONFIG_CMAKE_DIR}/../lib/${_BLPAPI_SHARED_OBJ_NAME}")
-  set(_BLPAPI_IMP_OBJ "${_BLPAPI_CONFIG_CMAKE_DIR}/../lib/${_BLPAPI_SHARED_IMP_OBJ_NAME}")
+  set(_BLPAPI_SHARED_OBJ "${_BLPAPI_CONFIG_CMAKE_DIR}/../../bin/${_BLPAPI_SHARED_OBJ_NAME}")
+  set(_BLPAPI_IMP_OBJ "${_BLPAPI_CONFIG_CMAKE_DIR}/../../lib/${_BLPAPI_SHARED_IMP_OBJ_NAME}")
 else() # Other platforms
   set(_BLPAPI_SHARED_OBJ_NAME "lib${_BLPAPI_PREFIX}_${_ARCH}.so")
   set(_BLPAPI_SHARED_OBJ
-      "${_BLPAPI_CONFIG_CMAKE_DIR}/../${CMAKE_SYSTEM_NAME}/${_BLPAPI_SHARED_OBJ_NAME}")
+      "${_BLPAPI_CONFIG_CMAKE_DIR}/../../lib/${_BLPAPI_SHARED_OBJ_NAME}")
 endif()
 
 set_property(TARGET blpapi APPEND PROPERTY IMPORTED_LOCATION "${_BLPAPI_SHARED_OBJ}")
diff --git a/cmake/blpapiTargets.cmake b/cmake/blpapiTargets.cmake
index b4c4a73..ef4421c 100644
--- a/cmake/blpapiTargets.cmake
+++ b/cmake/blpapiTargets.cmake
@@ -47,9 +47,9 @@ unset(_expectedTargets)
 # Create imported shared object target blpapi.
 add_library(blpapi SHARED IMPORTED)
 
-# Include headers can be found at ../include/ from location
+# Include headers can be found at ../../include/ from location
 # of blpapiConfig.cmake file.
-set(_BLPAPI_LIB_INCLUDE_DIR "${_BLPAPI_CONFIG_CMAKE_DIR}/../include")
+set(_BLPAPI_LIB_INCLUDE_DIR "${_BLPAPI_CONFIG_CMAKE_DIR}/../../include")
 
 # Add path to the include directories.
 set_property(TARGET blpapi APPEND PROPERTY
