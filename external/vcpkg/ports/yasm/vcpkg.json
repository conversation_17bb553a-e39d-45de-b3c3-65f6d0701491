{"name": "yasm", "version": "1.3.0", "port-version": 6, "description": "Yasm is a complete rewrite of the NASM assembler under the new BSD License.", "homepage": "https://github.com/yasm/yasm", "license": "BSD-2-Clause OR BSD-3-Clause OR Artistic-1.0 OR GPL-2.0-only OR LGPL-2.0-only", "supports": "!uwp", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}, {"name": "yasm", "host": true, "default-features": false, "features": ["tools"]}], "features": {"tools": {"description": "Build yasm tools"}}}