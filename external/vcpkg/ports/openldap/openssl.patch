diff --git a/configure.ac b/configure.ac
index d231e0e..c994103 100644
--- a/configure.ac
+++ b/configure.ac
@@ -1239,6 +1239,8 @@ fi
 
 ol_link_tls=no
 if test $ol_with_tls = openssl || test $ol_with_tls = auto ; then
+	PKG_CHECK_MODULES(OPENSSL, openssl >= 1.1.1, [have_openssl=yes], [AC_MSG_FAILURE([OpenSSL is required])])
+	CFLAGS="${OPENSSL_CFLAGS} ${CFLAGS}"
 	AC_CHECK_HEADERS(openssl/ssl.h)
 
 	if test $ac_cv_header_openssl_ssl_h = yes ; then
@@ -1249,6 +1251,7 @@ if test $ol_with_tls = openssl || test $ol_with_tls = auto ; then
 [#endif]])],
 					, [AC_MSG_FAILURE([OpenSSL 1.1.1 or newer required])])
 
+		ac_cv_lib_ssl_SSL_CTX_set_ciphersuites=yes
 		AC_CHECK_LIB(ssl, SSL_CTX_set_ciphersuites,
 			[have_openssl=yes], [have_openssl=no],
 			[-lcrypto])
@@ -1261,7 +1264,8 @@ if test $ol_with_tls = openssl || test $ol_with_tls = auto ; then
 			AC_DEFINE(HAVE_OPENSSL, 1, 
 				[define if you have OpenSSL])
 
-			TLS_LIBS="-lssl -lcrypto"
+			TLS_LIBS="${OPENSSL_LIBS}"
+			AC_SUBST(TLS_MODULE, openssl, [TLS pkg-config module])
 		fi
 	fi
 fi
diff --git a/libraries/libldap/ldap.pc.in b/libraries/libldap/ldap.pc.in
index 3f7dd46..43b2e52 100644
--- a/libraries/libldap/ldap.pc.in
+++ b/libraries/libldap/ldap.pc.in
@@ -10,4 +10,5 @@ Version: @VERSION@
 Requires: lber
 Cflags: -I${includedir}
 Libs: -L${libdir} -lldap
-Libs.private: @LIBS@ @SASL_LIBS@ @TLS_LIBS@ @AUTH_LIBS@
+Libs.private: @LIBS@ @SASL_LIBS@ @AUTH_LIBS@
+Requires.private: @TLS_MODULE@
