{"$comment": "Automatically generated by scripts/boost/generate-ports.ps1", "name": "boost", "version": "1.87.0", "port-version": 1, "description": "Peer-reviewed portable C++ source libraries", "homepage": "https://boost.org", "license": "BSL-1.0", "dependencies": [{"name": "boost-accumulators", "version>=": "1.87.0"}, {"name": "boost-algorithm", "version>=": "1.87.0"}, {"name": "boost-align", "version>=": "1.87.0"}, {"name": "boost-any", "version>=": "1.87.0"}, {"name": "boost-array", "version>=": "1.87.0"}, {"name": "boost-asio", "version>=": "1.87.0"}, {"name": "boost-assert", "version>=": "1.87.0"}, {"name": "boost-assign", "version>=": "1.87.0"}, {"name": "boost-atomic", "version>=": "1.87.0"}, {"name": "boost-beast", "platform": "!emscripten", "version>=": "1.87.0"}, {"name": "boost-bimap", "version>=": "1.87.0"}, {"name": "boost-bind", "version>=": "1.87.0"}, {"name": "boost-callable-traits", "version>=": "1.87.0"}, {"name": "boost-charconv", "version>=": "1.87.0"}, {"name": "boost-chrono", "version>=": "1.87.0"}, {"name": "boost-circular-buffer", "version>=": "1.87.0"}, {"name": "boost-compat", "version>=": "1.87.0"}, {"name": "boost-compute", "version>=": "1.87.0"}, {"name": "boost-concept-check", "version>=": "1.87.0"}, {"name": "boost-config", "version>=": "1.87.0"}, {"name": "boost-container", "version>=": "1.87.0"}, {"name": "boost-container-hash", "version>=": "1.87.0"}, {"name": "boost-context", "platform": "!uwp & !emscripten", "version>=": "1.87.0"}, {"name": "boost-contract", "version>=": "1.87.0"}, {"name": "boost-conversion", "version>=": "1.87.0"}, {"name": "boost-convert", "version>=": "1.87.0"}, {"name": "boost-core", "version>=": "1.87.0"}, {"name": "boost-coroutine", "platform": "!(arm & windows) & !uwp & !emscripten", "version>=": "1.87.0"}, {"name": "boost-coroutine2", "platform": "!uwp & !emscripten", "version>=": "1.87.0"}, {"name": "boost-crc", "version>=": "1.87.0"}, {"name": "boost-date-time", "version>=": "1.87.0"}, {"name": "boost-describe", "version>=": "1.87.0"}, {"name": "boost-detail", "version>=": "1.87.0"}, {"name": "boost-dll", "platform": "!uwp", "version>=": "1.87.0"}, {"name": "boost-dynamic-bitset", "version>=": "1.87.0"}, {"name": "boost-endian", "version>=": "1.87.0"}, {"name": "boost-exception", "version>=": "1.87.0"}, {"name": "boost-fiber", "platform": "!uwp & !(arm & windows) & !emscripten", "version>=": "1.87.0"}, {"name": "boost-filesystem", "platform": "!uwp", "version>=": "1.87.0"}, {"name": "boost-flyweight", "version>=": "1.87.0"}, {"name": "boost-foreach", "version>=": "1.87.0"}, {"name": "boost-format", "version>=": "1.87.0"}, {"name": "boost-function", "version>=": "1.87.0"}, {"name": "boost-function-types", "version>=": "1.87.0"}, {"name": "boost-functional", "version>=": "1.87.0"}, {"name": "boost-fusion", "version>=": "1.87.0"}, {"name": "boost-geometry", "version>=": "1.87.0"}, {"name": "boost-gil", "version>=": "1.87.0"}, {"name": "boost-graph", "platform": "!uwp", "version>=": "1.87.0"}, {"name": "boost-hana", "version>=": "1.87.0"}, {"name": "boost-headers", "version>=": "1.87.0"}, {"name": "boost-heap", "version>=": "1.87.0"}, {"name": "boost-histogram", "version>=": "1.87.0"}, {"name": "boost-hof", "version>=": "1.87.0"}, {"name": "boost-icl", "version>=": "1.87.0"}, {"name": "boost-integer", "version>=": "1.87.0"}, {"name": "boost-interprocess", "version>=": "1.87.0"}, {"name": "boost-interval", "version>=": "1.87.0"}, {"name": "boost-intrusive", "version>=": "1.87.0"}, {"name": "boost-io", "version>=": "1.87.0"}, {"name": "boost-iostreams", "platform": "!uwp", "version>=": "1.87.0"}, {"name": "boost-iterator", "version>=": "1.87.0"}, {"name": "boost-json", "version>=": "1.87.0"}, {"name": "boost-lambda", "version>=": "1.87.0"}, {"name": "boost-lambda2", "version>=": "1.87.0"}, {"name": "boost-leaf", "version>=": "1.87.0"}, {"name": "boost-lexical-cast", "version>=": "1.87.0"}, {"name": "boost-local-function", "version>=": "1.87.0"}, {"name": "boost-locale", "platform": "!uwp", "version>=": "1.87.0"}, {"name": "boost-lockfree", "version>=": "1.87.0"}, {"name": "boost-log", "platform": "!uwp & !emscripten", "version>=": "1.87.0"}, {"name": "boost-logic", "version>=": "1.87.0"}, {"name": "boost-math", "version>=": "1.87.0"}, {"name": "boost-metaparse", "version>=": "1.87.0"}, {"name": "boost-move", "version>=": "1.87.0"}, {"name": "boost-mp11", "version>=": "1.87.0"}, {"name": "boost-mpl", "version>=": "1.87.0"}, {"name": "boost-msm", "version>=": "1.87.0"}, {"name": "boost-multi-array", "version>=": "1.87.0"}, {"name": "boost-multi-index", "version>=": "1.87.0"}, {"name": "boost-multiprecision", "version>=": "1.87.0"}, {"name": "boost-mysql", "version>=": "1.87.0"}, {"name": "boost-nowide", "version>=": "1.87.0"}, {"name": "boost-numeric-conversion", "version>=": "1.87.0"}, {"name": "boost-odeint", "version>=": "1.87.0"}, {"name": "boost-optional", "version>=": "1.87.0"}, {"name": "boost-outcome", "version>=": "1.87.0"}, {"name": "boost-parameter", "version>=": "1.87.0"}, {"name": "boost-parameter-python", "platform": "!uwp & !emscripten & !ios & !android", "version>=": "1.87.0"}, {"name": "boost-parser", "version>=": "1.87.0"}, {"name": "boost-pfr", "version>=": "1.87.0"}, {"name": "boost-phoenix", "version>=": "1.87.0"}, {"name": "boost-poly-collection", "version>=": "1.87.0"}, {"name": "boost-polygon", "version>=": "1.87.0"}, {"name": "boost-pool", "version>=": "1.87.0"}, {"name": "boost-predef", "version>=": "1.87.0"}, {"name": "boost-preprocessor", "version>=": "1.87.0"}, {"name": "boost-process", "platform": "!uwp & !emscripten & !android", "version>=": "1.87.0"}, {"name": "boost-program-options", "version>=": "1.87.0"}, {"name": "boost-property-map", "version>=": "1.87.0"}, {"name": "boost-property-tree", "version>=": "1.87.0"}, {"name": "boost-proto", "version>=": "1.87.0"}, {"name": "boost-ptr-container", "version>=": "1.87.0"}, {"name": "boost-python", "platform": "!uwp & !emscripten & !ios & !android", "version>=": "1.87.0"}, {"name": "boost-qvm", "version>=": "1.87.0"}, {"name": "boost-random", "platform": "!uwp", "version>=": "1.87.0"}, {"name": "boost-range", "version>=": "1.87.0"}, {"name": "boost-ratio", "version>=": "1.87.0"}, {"name": "boost-rational", "version>=": "1.87.0"}, {"name": "boost-redis", "version>=": "1.87.0"}, {"name": "boost-regex", "version>=": "1.87.0"}, {"name": "boost-safe-numerics", "version>=": "1.87.0"}, {"name": "boost-scope", "version>=": "1.87.0"}, {"name": "boost-scope-exit", "version>=": "1.87.0"}, {"name": "boost-serialization", "version>=": "1.87.0"}, {"name": "boost-signals2", "version>=": "1.87.0"}, {"name": "boost-smart-ptr", "version>=": "1.87.0"}, {"name": "boost-sort", "version>=": "1.87.0"}, {"name": "boost-spirit", "version>=": "1.87.0"}, {"name": "boost-stacktrace", "platform": "!uwp", "version>=": "1.87.0"}, {"name": "boost-statechart", "version>=": "1.87.0"}, {"name": "boost-static-assert", "version>=": "1.87.0"}, {"name": "boost-static-string", "version>=": "1.87.0"}, {"name": "boost-stl-interfaces", "version>=": "1.87.0"}, {"name": "boost-system", "version>=": "1.87.0"}, {"name": "boost-test", "platform": "!uwp", "version>=": "1.87.0"}, {"name": "boost-thread", "version>=": "1.87.0"}, {"name": "boost-throw-exception", "version>=": "1.87.0"}, {"name": "boost-timer", "version>=": "1.87.0"}, {"name": "boost-tokenizer", "version>=": "1.87.0"}, {"name": "boost-tti", "version>=": "1.87.0"}, {"name": "boost-tuple", "version>=": "1.87.0"}, {"name": "boost-type-erasure", "version>=": "1.87.0"}, {"name": "boost-type-index", "version>=": "1.87.0"}, {"name": "boost-type-traits", "version>=": "1.87.0"}, {"name": "boost-typeof", "version>=": "1.87.0"}, {"name": "boost-ublas", "version>=": "1.87.0"}, {"name": "boost-units", "version>=": "1.87.0"}, {"name": "boost-unordered", "version>=": "1.87.0"}, {"name": "boost-url", "version>=": "1.87.0"}, {"name": "boost-utility", "version>=": "1.87.0"}, {"name": "boost-uuid", "version>=": "1.87.0"}, {"name": "boost-variant", "version>=": "1.87.0"}, {"name": "boost-variant2", "version>=": "1.87.0"}, {"name": "boost-vmd", "version>=": "1.87.0"}, {"name": "boost-wave", "platform": "!uwp", "version>=": "1.87.0"}, {"name": "boost-winapi", "version>=": "1.87.0"}, {"name": "boost-xpressive", "version>=": "1.87.0"}, {"name": "boost-yap", "version>=": "1.87.0"}], "features": {"cobalt": {"description": "Build boost-cobalt", "dependencies": [{"name": "boost-cobalt", "platform": "!osx & !ios & !android & !uwp", "version>=": "1.87.0"}]}, "mpi": {"description": "Build with MPI support", "dependencies": [{"name": "boost-graph-parallel", "version>=": "1.87.0"}, {"name": "boost-mpi", "version>=": "1.87.0"}, {"name": "boost-property-map-parallel", "version>=": "1.87.0"}]}}}