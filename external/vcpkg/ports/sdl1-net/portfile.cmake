vcpkg_download_distfile(ARCHIVE
    URLS "https://www.libsdl.org/projects/SDL_net/release/SDL_net-1.2.8.tar.gz"
    FILENAME "SDL_net-1.2.8.tar.gz"
    SHA512 2766ca55343127c619958ab3a3ae3052a27a676839f10a158f7dfc071b8db38c2f1fc853e8add32b9fef94ab07eaa986f46a68e264e8087b57c990af30ea9a0b
)

vcpkg_extract_source_archive(
    SOURCE_PATH
    ARCHIVE "${ARCHIVE}"
)

file(COPY "${CMAKE_CURRENT_LIST_DIR}/CMakeLists.txt" DESTINATION "${SOURCE_PATH}")

vcpkg_cmake_configure(
    SOURCE_PATH "${SOURCE_PATH}"
    OPTIONS_DEBUG
        -DSDL_NET_SKIP_HEADERS=ON)

vcpkg_cmake_install()
vcpkg_copy_pdbs()

file(INSTALL "${SOURCE_PATH}/COPYING" DESTINATION "${CURRENT_PACKAGES_DIR}/share/${PORT}" RENAME copyright)
