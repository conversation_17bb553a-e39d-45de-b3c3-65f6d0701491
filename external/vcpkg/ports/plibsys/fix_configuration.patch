diff --git a/src/CMakeLists.txt b/src/CMakeLists.txt
index 12cad07..cda9f76 100644
--- a/src/CMakeLists.txt
+++ b/src/CMakeLists.txt
@@ -827,10 +827,10 @@ if (PLIBSYS_PLATFORM_DEFINES)
 endif()
 
 # Add targets
-add_library (plibsys SHARED ${PLIBSYS_SRCS} ${PLIBSYS_PLATFORM_SRCS} ${PLIBSYS_PUBLIC_HDRS} ${PLIBSYS_PRIVATE_HDRS})
-
 if (PLIBSYS_BUILD_STATIC)
         add_library (plibsysstatic STATIC ${PLIBSYS_SRCS} ${PLIBSYS_PLATFORM_SRCS} ${PLIBSYS_PUBLIC_HDRS} ${PLIBSYS_PRIVATE_HDRS})
+else()
+        add_library (plibsys SHARED ${PLIBSYS_SRCS} ${PLIBSYS_PLATFORM_SRCS} ${PLIBSYS_PUBLIC_HDRS} ${PLIBSYS_PRIVATE_HDRS})
 endif()
 
 # Prepare installation dirs
@@ -838,6 +838,10 @@ if (NOT CMAKE_INSTALL_LIBDIR)
         set (CMAKE_INSTALL_LIBDIR "lib")
 endif()
 
+if (NOT CMAKE_INSTALL_BINDIR)
+        set (CMAKE_INSTALL_BINDIR "bin")
+endif()
+
 if (NOT CMAKE_INSTALL_INCLUDEDIR)
         set (CMAKE_INSTALL_INCLUDEDIR "include")
 endif()
@@ -846,16 +850,16 @@ set (PLIBSYS_INCL_INSTALL_DIR ${CMAKE_INSTALL_INCLUDEDIR}/plibsys)
 
 # Add include directories
 if (COMMAND target_include_directories)
-        target_include_directories (plibsys PUBLIC
-                "$<BUILD_INTERFACE:${PLIBSYS_INCLUDE_DIRS}>"
-                "$<INSTALL_INTERFACE:${PLIBSYS_INCL_INSTALL_DIR}>"
-        )
-
         if (PLIBSYS_BUILD_STATIC)
                 target_include_directories (plibsysstatic PUBLIC
                         "$<BUILD_INTERFACE:${PLIBSYS_INCLUDE_DIRS}>"
                         "$<INSTALL_INTERFACE:${PLIBSYS_INCL_INSTALL_DIR}>"
                 )
+        else()
+                target_include_directories (plibsys PUBLIC
+                        "$<BUILD_INTERFACE:${PLIBSYS_INCLUDE_DIRS}>"
+                        "$<INSTALL_INTERFACE:${PLIBSYS_INCL_INSTALL_DIR}>"
+        )
         endif()
 else()
         include_directories (${PLIBSYS_INCLUDE_DIRS})
@@ -863,15 +867,16 @@ endif()
 
 # Add compile definitions
 if (COMMAND target_compile_definitions)
-        target_compile_definitions (plibsys PRIVATE ${PLIBSYS_COMPILE_DEFS})
-
         if (PLIBSYS_BUILD_STATIC)
                 target_compile_definitions (plibsysstatic PRIVATE ${PLIBSYS_COMPILE_DEFS})
+        else()
+                target_compile_definitions (plibsys PRIVATE ${PLIBSYS_COMPILE_DEFS})
         endif()
 else()
         add_definitions (${PLIBSYS_COMPILE_DEFS})
 endif()
 
+if(NOT PLIBSYS_BUILD_STATIC)
 set_target_properties (plibsys PROPERTIES RUNTIME_OUTPUT_DIRECTORY ${OUTPUT_DIR})
 set_target_properties (plibsys PROPERTIES LIBRARY_OUTPUT_DIRECTORY ${OUTPUT_DIR})
 set_target_properties (plibsys PROPERTIES ARCHIVE_OUTPUT_DIRECTORY ${OUTPUT_DIR})
@@ -884,6 +889,7 @@ endif()
 if (NOT PLIBSYS_TARGET_OS STREQUAL os2 AND NOT PLIBSYS_TARGET_OS STREQUAL amigaos)
         set_target_properties (plibsys PROPERTIES SOVERSION ${PLIBSYS_SOVERSION})
 endif()
+endif()
 
 if (PLIBSYS_BUILD_STATIC)
         set_target_properties (plibsysstatic PROPERTIES ARCHIVE_OUTPUT_DIRECTORY ${OUTPUT_DIR})
@@ -898,10 +904,10 @@ if (PLIBSYS_PLATFORM_CFLAGS)
                 set (PLIBSYS_PLATFORM_CFLAGS_STR "${PLIBSYS_PLATFORM_CFLAGS_STR} ${PLATFORM_CFLAG}")
         endforeach()
 
-        set_target_properties (plibsys PROPERTIES COMPILE_FLAGS "${PLIBSYS_PLATFORM_CFLAGS_STR}")
-
         if (PLIBSYS_BUILD_STATIC)
                 set_target_properties (plibsysstatic PROPERTIES COMPILE_FLAGS "${PLIBSYS_PLATFORM_CFLAGS_STR}")
+        else()
+                set_target_properties (plibsys PROPERTIES COMPILE_FLAGS "${PLIBSYS_PLATFORM_CFLAGS_STR}")
         endif()
 endif()
 
@@ -910,29 +916,29 @@ if (PLIBSYS_PLATFORM_LDFLAGS)
                 set (PLIBSYS_PLATFORM_LDFLAGS_STR "${PLIBSYS_PLATFORM_LDFLAGS_STR} ${PLATFORM_LDFLAG}")
         endforeach()
 
-        set_target_properties (plibsys PROPERTIES LINK_FLAGS "${PLIBSYS_PLATFORM_LDFLAGS_STR}")
-
         if (PLIBSYS_BUILD_STATIC)
                 set_target_properties (plibsysstatic PROPERTIES LINK_FLAGS "${PLIBSYS_PLATFORM_LDFLAGS_STR}")
+        else()
+                set_target_properties (plibsys PROPERTIES LINK_FLAGS "${PLIBSYS_PLATFORM_LDFLAGS_STR}")
         endif()
 endif()
 
-target_link_libraries (plibsys ${PLIBSYS_PLATFORM_LINK_LIBRARIES})
-
 if (PLIBSYS_BUILD_STATIC)
         target_link_libraries (plibsysstatic ${PLIBSYS_PLATFORM_LINK_LIBRARIES})
+else()
+        target_link_libraries (plibsys ${PLIBSYS_PLATFORM_LINK_LIBRARIES})
 endif()
 
 if (PLIBSYS_BUILD_STATIC)
-        set (PLIBSYS_INSTALL_TARGETS plibsys plibsysstatic)
+        set (PLIBSYS_INSTALL_TARGETS plibsysstatic)
 else()
         set (PLIBSYS_INSTALL_TARGETS plibsys)
 endif()
 
 if (PLIBSYS_NATIVE_WINDOWS)
         install (TARGETS ${PLIBSYS_INSTALL_TARGETS}
-                DESTINATION lib
-                RUNTIME DESTINATION lib
+                LIBRARY DESTINATION lib
+                RUNTIME DESTINATION bin
                 COMPONENT Core
         )
 
@@ -942,11 +948,6 @@ if (PLIBSYS_NATIVE_WINDOWS)
 
         set (CMAKE_INSTALL_SYSTEM_RUNTIME_LIBS_SKIP TRUE)
         include (InstallRequiredSystemLibraries)
-
-        install (PROGRAMS ${CMAKE_INSTALL_SYSTEM_RUNTIME_LIBS}
-                DESTINATION lib
-                COMPONENT Core
-        )
 endif()
 
 set (PLIBSYS_EXPORT_NAME plibsys-targets)
@@ -974,13 +975,9 @@ endif()
 
 install (TARGETS ${PLIBSYS_INSTALL_TARGETS}
         EXPORT ${PLIBSYS_EXPORT_NAME}
-        DESTINATION ${CMAKE_INSTALL_LIBDIR}
-        LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
-        COMPONENT Core
-)
-install (TARGETS ${PLIBSYS_INSTALL_TARGETS}
-        DESTINATION ${CMAKE_INSTALL_LIBDIR}
         ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR}
+        LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
+        RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
         COMPONENT Core
 )
 install (FILES
