diff --git a/makefile.vc b/makefile.vc
index 8edb536b9..33fd83e35 100644
--- a/makefile.vc
+++ b/makefile.vc
@@ -8,7 +8,7 @@ LIBOBJ	               =	src\readosm.obj src\osmxml.obj \
 							src\protobuf.obj src\osm_objects.obj
 READOSM_DLL	 	       =	readosm$(VERSION).dll
 
-CFLAGS	=	/nologo -I. -Iheaders -IC:\OSGeo4W\include $(OPTFLAGS)
+CFLAGS	=	/nologo -I. -Iheaders -I$(INSTALLED_ROOT)\include $(OPTFLAGS)
 
 default:	all
 
@@ -21,9 +21,9 @@ readosm.lib:	$(LIBOBJ)
 $(READOSM_DLL):	readosm_i.lib
 
 readosm_i.lib:	$(LIBOBJ)
-	link /dll /out:$(READOSM_DLL) \
+	link $(LINK_FLAGS) /dll /out:$(READOSM_DLL) \
 		/implib:readosm_i.lib $(LIBOBJ) \
-		C:\OSGeo4w\lib\libexpat.lib C:\OSGeo4w\lib\zlib.lib
+		$(LIBS_ALL)
 	if exist $(READOSM_DLL).manifest mt -manifest \
 		$(READOSM_DLL).manifest -outputresource:$(READOSM_DLL);2 
 		
@@ -35,7 +35,7 @@ clean:
 	del *.exp
 	del *.manifest
 	del *.lib
-	del *.obj
+	del src\*.obj
 	del *.pdb
 
 install: all

diff --git a/nmake.opt b/nmake.opt
index 5e45c0e..61c44f9 100644
--- a/nmake.opt
+++ b/nmake.opt
@@ -2,7 +2,7 @@
 INSTDIR=C:\OSGeo4W
 
 # Uncomment the first for an optimized build, or the second for debug.
-OPTFLAGS=	/nologo /Ox /fp:precise /W3 /MD /D_CRT_SECURE_NO_WARNINGS \
+OPTFLAGS=	/nologo /fp:precise /W3 $(CL_FLAGS) /D_CRT_SECURE_NO_WARNINGS \
 			/DDLL_EXPORT
 #OPTFLAGS=	/nologo /Zi /MD /Fdreadosm.pdb /DDLL_EXPORT

