vcpkg_from_github(
    OUT_SOURCE_PATH SOURCE_PATH
    REPO vurtun/mmx
    REF 71382a1d14dad58219e7f6634d2381fa30dab175
    SHA512 d29b6a7e276612f0cbea84a90a4af7b3947a6f434e171a180b24eb0b5b74d714011fcda9e2ebe3f39d01bdb64f648d31c68a4cf7ef5f104320fe8155212fe1e0
    HEAD_REF master
)

file(GLOB_RECURSE MMX_HEADERS "${SOURCE_PATH}/*.h")
file(INSTALL ${MMX_HEADERS} DESTINATION "${CURRENT_PACKAGES_DIR}/include/mmx")

file(INSTALL "${CMAKE_CURRENT_LIST_DIR}/copyright" DESTINATION "${CURRENT_PACKAGES_DIR}/share/${PORT}")
