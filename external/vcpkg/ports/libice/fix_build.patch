diff --git a/src/error.c b/src/error.c
index 055452e..0ce530a 100644
--- a/src/error.c
+++ b/src/error.c
@@ -32,7 +32,11 @@ Author: <PERSON>, X Consortium
 #include <X11/ICE/ICElib.h>
 #include "ICElibint.h"
 #include <stdio.h>
+#ifdef HAVE_UNISTD_H
 #include <unistd.h>
+#elif _MSC_VER
+#include <process.h>
+#endif
 #include <errno.h>
 
 void
diff --git a/src/iceauth.c b/src/iceauth.c
index 5a4d400..4549f0e 100644
--- a/src/iceauth.c
+++ b/src/iceauth.c
@@ -39,7 +39,12 @@ Author: <PERSON>, X Consortium
 #include <stdlib.h>	/* for arc4random_buf() */
 #endif
 
+#ifdef HAVE_UNISTD_H
 #include <unistd.h>
+#elif _MSC_VER
+#include <process.h>
+#define ITIMER_REAL
+#endif
 
 static int was_called_state;
 
