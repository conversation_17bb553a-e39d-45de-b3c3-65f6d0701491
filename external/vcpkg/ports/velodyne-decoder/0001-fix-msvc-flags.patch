Backport of https://github.com/valgur/velodyne_decoder/commit/22809df3a4d550c3746b17aaca1d6c20692730c4

--- CMakeLists.txt
+++ CMakeLists.txt
@@ -17,7 +17,11 @@
 set(CMAKE_WINDOWS_EXPORT_ALL_SYMBOLS ON)
 
 if(MSVC)
-  add_compile_options(/W4 /O2)
+  add_compile_options(
+      "$<$<CONFIG:RelWithDebInfo>:/O2>"
+      "$<$<CONFIG:Release>:/O2>"
+      /W4
+  )
 else()
   add_compile_options(
     "$<$<CONFIG:Debug>:-ggdb3;-Og>"
