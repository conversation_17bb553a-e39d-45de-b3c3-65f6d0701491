{"name": "qscintilla", "version": "2.14.1", "port-version": 1, "description": "QScintilla is a port to Qt of the Scintilla editing component. Features syntax highlighting, code-completion and much more (Barebone build without python bindings (missing dependeny PyQt) and without QtDesigner plugin)", "homepage": "https://www.riverbankcomputing.com/software/qscintilla", "license": "GPL-3.0-or-later", "supports": "!xbox", "dependencies": [{"name": "qtbase", "default-features": false}, {"name": "vcpkg-qmake", "host": true, "default-features": false}]}