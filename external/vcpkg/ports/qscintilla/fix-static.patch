diff --git a/src/qscintilla.pro b/src/qscintilla.pro
index 8d0acd2..2246442 100644
--- a/src/qscintilla.pro
+++ b/src/qscintilla.pro
@@ -37,13 +37,13 @@ CONFIG(debug, debug|release) {
     TARGET = qscintilla2_qt$${QT_MAJOR_VERSION}
 }
 
-macx:!CONFIG(staticlib) {
+macx:!CONFIG(static) {
     QMAKE_POST_LINK += install_name_tool -id @rpath/$(TARGET1) $(TARGET)
 }
 
 INCLUDEPATH += . ../scintilla/include ../scintilla/lexlib ../scintilla/src
 
-!CONFIG(staticlib) {
+!CONFIG(static) {
     DEFINES += QSCINTILLA_MAKE_DLL
 
     # Comment this in to build a dynamic library supporting multiple
@@ -86,7 +86,7 @@ qsci.files = ../qsci
 INSTALLS += qsci
 
 features.path = $$[QT_HOST_DATA]/mkspecs/features
-CONFIG(staticlib) {
+CONFIG(static) {
     features.files = $$PWD/features_staticlib/qscintilla2.prf
 } else {
     features.files = $$PWD/features/qscintilla2.prf
