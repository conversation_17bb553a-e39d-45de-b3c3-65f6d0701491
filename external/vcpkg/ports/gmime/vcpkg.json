{"name": "gmime", "version": "3.2.15", "port-version": 1, "description": "GMime is a C/C++ library which may be used for the creation and parsing of messages using the Multipurpose Internet Mail Extension (MIME).", "homepage": "https://github.com/jstedfast/gmime", "license": "LGPL-2.1-or-later", "supports": "!xbox", "dependencies": ["glib", {"name": "gmime", "host": true}, "libiconv", "libidn2", "zlib"], "features": {"crypto": {"description": "PGP and S/MIME support", "dependencies": [{"name": "gpgme", "default-features": false}]}}}