From 1d4a866282ace376c8e3ba05c21ce3bcc6643040 Mon Sep 17 00:00:00 2001
From: =?UTF-8?q?Jan=20M=C3=B6bius?= <<EMAIL>>
Date: Tue, 9 Jan 2024 12:59:45 +0100
Subject: [PATCH] Small patch to keep backward compatibility with c++11

---
 src/OpenMesh/Core/Utils/Property.hh | 2 +-
 1 file changed, 1 insertion(+), 1 deletion(-)

diff --git a/src/OpenMesh/Core/Utils/Property.hh b/src/OpenMesh/Core/Utils/Property.hh
index 485d3072..6ba66584 100644
--- a/src/OpenMesh/Core/Utils/Property.hh
+++ b/src/OpenMesh/Core/Utils/Property.hh
@@ -250,7 +250,7 @@ public: // inherited from BaseProperty
   virtual void reserve(size_t _n) override { data_.reserve(_n);    }
   virtual void resize(size_t _n) override  { data_.resize(_n);     }
   virtual void clear() override  { data_.clear(); vector_type().swap(data_);    }
-  virtual void push_back() override        { data_.emplace_back(); }
+  virtual void push_back() override        { data_.push_back(bool()); }
   virtual void swap(size_t _i0, size_t _i1) override
   { bool t(data_[_i0]); data_[_i0]=data_[_i1]; data_[_i1]=t; }
   virtual void copy(size_t _i0, size_t _i1) override
-- 
GitLab

