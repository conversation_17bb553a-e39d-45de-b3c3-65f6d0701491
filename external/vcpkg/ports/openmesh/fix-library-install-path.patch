diff --git a/cmake-library/VCI/VCICommon.cmake b/cmake-library/VCI/VCICommon.cmake
index 7a5269c..e3f87ff 100644
--- a/cmake-library/VCI/VCICommon.cmake
+++ b/cmake-library/VCI/VCICommon.cmake
@@ -242,7 +242,8 @@
     set (_and_static 0)
   endif ()
 
-  add_library (${_target} ${_type} ${ARGN} )
+  set(_and_static 0)
+  add_library (${_target} ${ARGN} )
 
   # set common target properties defined in common.cmake
   vci_set_target_props (${_target})
index 7a5269c..e3f87ff 100644
--- a/src/OpenMesh/Core/CMakeLists.txt
+++ b/src/OpenMesh/Core/CMakeLists.txt
@@ -156,9 +156,9 @@
                                           $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/../..>
                                           $<INSTALL_INTERFACE:include>)
 
-  target_include_directories(OpenMeshCoreStatic PUBLIC
-                                          $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/../..>
-                                          $<INSTALL_INTERFACE:include>)
+  #target_include_directories(OpenMeshCoreStatic PUBLIC
+  #                                        $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/../..>
+  #                                        $<INSTALL_INTERFACE:include>)
 
   set_target_properties (OpenMeshCore PROPERTIES VERSION ${PROJECT_VERSION_MAJOR}.${PROJECT_VERSION_MINOR}
                                                SOVERSION ${PROJECT_VERSION_MAJOR}.${PROJECT_VERSION_MINOR} )
@@ -180,7 +180,7 @@ endif()
 
 # if we build debug and release in the same dir, we want to install both!
 if ( ${CMAKE_PROJECT_NAME} MATCHES "OpenMesh")
-  if ( WIN32 )
+  if ( 0 )
     FILE(GLOB files_install_libs "${CMAKE_BINARY_DIR}/Build/lib/*.lib" )
     FILE(GLOB files_install_dlls "${CMAKE_BINARY_DIR}/Build/*.dll" )
     INSTALL(FILES ${files_install_libs} DESTINATION lib )
@@ -240,7 +240,8 @@ target_include_directories(OpenMeshCore PUBLIC
 endif ()
 
 install(TARGETS OpenMeshCore EXPORT OpenMeshConfig
-  ARCHIVE DESTINATION ${VCI_PROJECT_LIBDIR}
-  LIBRARY DESTINATION ${VCI_PROJECT_LIBDIR}
-  RUNTIME DESTINATION ${VCI_PROJECT_BINDIR})
+  ARCHIVE DESTINATION lib
+  LIBRARY DESTINATION lib
+  RUNTIME DESTINATION bin)
+  target_compile_features(OpenMeshCore PUBLIC cxx_std_11)
 
diff --git a/src/OpenMesh/Tools/CMakeLists.txt b/src/OpenMesh/Tools/CMakeLists.txt
index 0170e2b..e40dfa1 100644
--- a/src/OpenMesh/Tools/CMakeLists.txt
+++ b/src/OpenMesh/Tools/CMakeLists.txt
@@ -122,9 +122,9 @@
                                           $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/../..>
                                           $<INSTALL_INTERFACE:include>)
 
-  target_include_directories(OpenMeshToolsStatic PUBLIC
-                                          $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/../..>
-                                          $<INSTALL_INTERFACE:include>)
+  #target_include_directories(OpenMeshToolsStatic PUBLIC
+  #                                        $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/../..>
+  #                                        $<INSTALL_INTERFACE:include>)
 
   set_target_properties (OpenMeshTools PROPERTIES VERSION ${PROJECT_VERSION_MAJOR}.${PROJECT_VERSION_MINOR}
                                                 SOVERSION ${PROJECT_VERSION_MAJOR}.${PROJECT_VERSION_MINOR} )
@@ -132,7 +132,7 @@
 
 target_link_libraries (OpenMeshTools OpenMeshCore)
 
-IF( NOT WIN32 )
+IF( 0 )
   target_link_libraries (OpenMeshToolsStatic OpenMeshCoreStatic)
 ENDIF(NOT WIN32)
 
@@ -126,7 +126,8 @@ target_include_directories(OpenMeshTools PUBLIC
 endif ()
 
 install(TARGETS OpenMeshTools EXPORT OpenMeshConfig
-  ARCHIVE DESTINATION ${VCI_PROJECT_LIBDIR}
-  LIBRARY DESTINATION ${VCI_PROJECT_LIBDIR}
-  RUNTIME DESTINATION ${VCI_PROJECT_BINDIR})
+  ARCHIVE DESTINATION lib
+  LIBRARY DESTINATION lib
+  RUNTIME DESTINATION bin
+)
 
