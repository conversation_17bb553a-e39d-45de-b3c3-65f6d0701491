diff --git a/CMakeLists.txt b/CMakeLists.txt
index 7a5269c..e3f87ff 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -188,12 +188,15 @@
 
 # Generate openmesh.pc file
 
+if("${CMAKE_BUILD_TYPE}" STREQUAL "Debug")
+  set(_debug_postfix "${CMAKE_DEBUG_POSTFIX}")
+endif()
 set(DEST_DIR "${CMAKE_INSTALL_PREFIX}")
-set(PRIVATE_LIBS "-lOpenMeshCore -lOpenMeshTools")
+set(PRIVATE_LIBS "-lOpenMeshCore${_debug_postfix} -lOpenMeshTools${_debug_postfix}")
 
 configure_file("openmesh.pc.in" "${CMAKE_CURRENT_BINARY_DIR}/openmesh.pc" @ONLY)
 
-install(FILES ${CMAKE_CURRENT_BINARY_DIR}/openmesh.pc DESTINATION libdata/pkgconfig)
+install(FILES ${CMAKE_CURRENT_BINARY_DIR}/openmesh.pc DESTINATION lib/pkgconfig)
 
 # generate target file
 
