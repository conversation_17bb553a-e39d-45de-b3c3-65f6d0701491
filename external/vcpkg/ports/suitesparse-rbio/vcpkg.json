{"$comment": "Use scripts/update_suitesparse.py to update all SuiteSparse ports", "name": "suitesparse-rbio", "version-semver": "4.3.4", "description": "RBio: routines for reading/writing sparse matrices in Rutherford/Boeing format in SuiteSparse", "homepage": "https://people.engr.tamu.edu/davis/suitesparse.html", "license": "GPL-2.0-or-later", "dependencies": ["suitesparse-config", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}]}