{"name": "quantlib", "version": "1.37", "description": "The QuantLib C++ library", "homepage": "https://www.quantlib.org/", "license": "BSD-3-<PERSON><PERSON>", "supports": "!(windows & !static)", "dependencies": ["boost-accumulators", "boost-algorithm", "boost-any", "boost-assert", "boost-bimap", "boost-bind", "boost-config", "boost-core", "boost-date-time", "boost-dynamic-bitset", "boost-format", "boost-function", "boost-functional", "boost-iterator", "boost-math", "boost-multi-array", "boost-multiprecision", "boost-optional", "boost-preprocessor", "boost-smart-ptr", "boost-tuple", "boost-type-traits", "boost-ublas", "boost-unordered", "boost-utility", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}]}