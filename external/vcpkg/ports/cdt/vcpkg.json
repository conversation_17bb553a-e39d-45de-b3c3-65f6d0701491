{"name": "cdt", "version": "1.4.1", "description": "Constrained Delaunay Triangulation", "homepage": "https://github.com/artem-ogre/CDT.git", "license": "MPL-2.0", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"64-bit-index-type": {"description": "64bits are used to store vertex/triangle index types"}, "as-compiled-library": {"description": "Templates for float and double will be instantiated and compiled into a library"}}}