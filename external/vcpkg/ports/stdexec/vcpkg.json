{"name": "stdexec", "version-date": "2024-06-16", "port-version": 2, "description": "stdexec is an experimental reference implementation of the Senders model of asynchronous programming proposed by P2300 - std::execution for adoption into the C++ Standard.", "homepage": "https://github.com/NVIDIA/stdexec", "license": "Apache-2.0", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}]}