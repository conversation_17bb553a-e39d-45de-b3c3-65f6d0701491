diff --git a/CMakeLists.txt b/CMakeLists.txt
index 7c2874e..0c81985 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -35,7 +35,7 @@ if(NOT EXISTS ${CMAKE_BINARY_DIR}/execution.bs)
       ${CMAKE_BINARY_DIR}/execution.bs)
 endif()
 file(STRINGS "${CMAKE_BINARY_DIR}/execution.bs" STD_EXECUTION_BS_REVISION_LINE REGEX "Revision: [0-9]+")
-string(REGEX REPLACE "Revision: ([0-9]+)" "\\1" STD_EXECUTION_BS_REVISION ${STD_EXECUTION_BS_REVISION_LINE})
+string(REGEX REPLACE "Revision: ([0-9]+).*" "\\1" STD_EXECUTION_BS_REVISION ${STD_EXECUTION_BS_REVISION_LINE})
 
 # nvc++ isn't supported by (s)ccache yet, so unset these before the `project()`
 # call so <PERSON><PERSON>ake's CXX compiler detection doesn't throw attempting to use it
