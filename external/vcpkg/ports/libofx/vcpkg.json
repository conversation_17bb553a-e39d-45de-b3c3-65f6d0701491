{"name": "libofx", "version": "0.10.9", "port-version": 1, "description": "OFX banking protocol abstraction library", "homepage": "https://github.com/libofx/libofx", "license": "GPL-2.0-only", "supports": "!uwp & !xbox", "dependencies": ["libopensp", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "default-features": ["iconv"], "features": {"iconv": {"description": "Builds with ICONV support for encoding conversion", "dependencies": ["libiconv"]}, "ofx2qif": {"description": "Enables OFX file to QIF (Quicken Interchange Format) file converter"}, "ofxdump": {"description": "Enables ofxdump utility which prints, in human readable form, everything the library understands about a file"}}}