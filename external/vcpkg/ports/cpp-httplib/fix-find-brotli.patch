diff --git a/CMakeLists.txt b/CMakeLists.txt
index 0353b0c..5c0cd33 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -151,10 +151,10 @@ endif()
 # This is so we can use our custom FindBrotli.cmake
 list(APPEND CMAKE_MODULE_PATH "${CMAKE_CURRENT_SOURCE_DIR}/cmake")
 if(HTTPLIB_REQUIRE_BROTLI)
-	find_package(Brotli COMPONENTS encoder decoder common REQUIRED)
+	find_package(unofficial-brotli CONFIG REQUIRED)
 	set(HTTPLIB_IS_USING_BROTLI TRUE)
 elseif(HTTPLIB_USE_BROTLI_IF_AVAILABLE)
-	find_package(Brotli COMPONENTS encoder decoder common QUIET)
+	find_package(unofficial-brotli CONFIG QUIET)
 	set(HTTPLIB_IS_USING_BROTLI ${Brotli_FOUND})
 endif()
 
@@ -236,9 +236,9 @@ target_link_libraries(${PROJECT_NAME} ${_INTERFACE_OR_PUBLIC}
 		# Needed for API from MacOS Security framework
 		"$<$<AND:$<PLATFORM_ID:Darwin>,$<BOOL:${HTTPLIB_IS_USING_OPENSSL}>,$<BOOL:${HTTPLIB_USE_CERTS_FROM_MACOSX_KEYCHAIN}>>:-framework CoreFoundation -framework Security>"
 		# Can't put multiple targets in a single generator expression or it bugs out.
-		$<$<BOOL:${HTTPLIB_IS_USING_BROTLI}>:Brotli::common>
-		$<$<BOOL:${HTTPLIB_IS_USING_BROTLI}>:Brotli::encoder>
-		$<$<BOOL:${HTTPLIB_IS_USING_BROTLI}>:Brotli::decoder>
+		$<$<BOOL:${HTTPLIB_IS_USING_BROTLI}>:unofficial::brotli::brotlicommon>
+		$<$<BOOL:${HTTPLIB_IS_USING_BROTLI}>:unofficial::brotli::brotlienc>
+		$<$<BOOL:${HTTPLIB_IS_USING_BROTLI}>:unofficial::brotli::brotlidec>
 		$<$<BOOL:${HTTPLIB_IS_USING_ZLIB}>:ZLIB::ZLIB>
 		$<$<BOOL:${HTTPLIB_IS_USING_ZSTD}>:zstd::libzstd>
 		$<$<BOOL:${HTTPLIB_IS_USING_OPENSSL}>:OpenSSL::SSL>
@@ -296,9 +296,6 @@ if(HTTPLIB_INSTALL)
 	install(FILES
 		"${CMAKE_CURRENT_BINARY_DIR}/${PROJECT_NAME}Config.cmake"
 		"${CMAKE_CURRENT_BINARY_DIR}/${PROJECT_NAME}ConfigVersion.cmake"
-		# Install it so it can be used later by the httplibConfig.cmake file.
-		# Put it in the same dir as our config file instead of a global path so we don't potentially stomp on other packages.
-		"${CMAKE_CURRENT_SOURCE_DIR}/cmake/FindBrotli.cmake"
 		DESTINATION ${_TARGET_INSTALL_CMAKEDIR}
 	)
 
diff --git a/cmake/httplibConfig.cmake.in b/cmake/httplibConfig.cmake.in
index bf57364..1c6fe62 100644
--- a/cmake/httplibConfig.cmake.in
+++ b/cmake/httplibConfig.cmake.in
@@ -34,8 +34,8 @@ if(@HTTPLIB_IS_USING_BROTLI@)
 	# Note that the FindBrotli.cmake file is installed in the same dir as this file.
 	list(APPEND CMAKE_MODULE_PATH "${CMAKE_CURRENT_LIST_DIR}")
 	set(BROTLI_USE_STATIC_LIBS @BROTLI_USE_STATIC_LIBS@)
-	find_dependency(Brotli COMPONENTS common encoder decoder)
-	set(httplib_Brotli_FOUND ${Brotli_FOUND})
+	find_dependency(unofficial-brotli COMPONENTS common encoder decoder)
+	set(httplib_Brotli_FOUND ${unofficial-brotli_FOUND})
 endif()
 
 if(@HTTPLIB_IS_USING_ZSTD@)
