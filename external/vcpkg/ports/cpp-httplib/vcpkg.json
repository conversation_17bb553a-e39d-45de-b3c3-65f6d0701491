{"name": "cpp-httplib", "version": "0.20.0", "port-version": 1, "description": "A single file C++11 header-only HTTP/HTTPS server and client library", "homepage": "https://github.com/yhirose/cpp-httplib", "license": "MIT", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "default-features": ["brotli"], "features": {"brotli": {"description": "Enables brotli compression support using brotli", "dependencies": ["brotli"]}, "openssl": {"description": "Enables HTTPS support using OpenSSL", "dependencies": ["openssl"]}, "zlib": {"description": "Enables gzip compression support using zlib", "dependencies": ["zlib"]}, "zstd": {"description": "Enables zstd support", "dependencies": ["zstd"]}}}