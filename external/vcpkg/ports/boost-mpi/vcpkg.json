{"$comment": "Automatically generated by scripts/boost/generate-ports.ps1", "name": "boost-mpi", "version": "1.87.0", "description": "Boost mpi module", "homepage": "https://www.boost.org/libs/mpi", "license": "BSL-1.0", "supports": "!uwp", "dependencies": [{"name": "boost-assert", "version>=": "1.87.0"}, {"name": "boost-cmake", "version>=": "1.87.0"}, {"name": "boost-config", "version>=": "1.87.0"}, {"name": "boost-core", "version>=": "1.87.0"}, {"name": "boost-foreach", "version>=": "1.87.0"}, {"name": "boost-function", "version>=": "1.87.0"}, {"name": "boost-graph", "version>=": "1.87.0"}, {"name": "boost-headers", "version>=": "1.87.0"}, {"name": "boost-integer", "version>=": "1.87.0"}, {"name": "boost-iterator", "version>=": "1.87.0"}, {"name": "boost-lexical-cast", "version>=": "1.87.0"}, {"name": "boost-mpl", "version>=": "1.87.0"}, {"name": "boost-optional", "version>=": "1.87.0"}, {"name": "boost-serialization", "version>=": "1.87.0"}, {"name": "boost-smart-ptr", "version>=": "1.87.0"}, {"name": "boost-static-assert", "version>=": "1.87.0"}, {"name": "boost-throw-exception", "version>=": "1.87.0"}, {"name": "boost-type-traits", "version>=": "1.87.0"}, {"name": "boost-utility", "version>=": "1.87.0"}, "mpi"], "features": {"python3": {"description": "Build Python3 bindings", "supports": "!static", "dependencies": [{"name": "boost-python", "platform": "!uwp & !emscripten & !ios & !android", "version>=": "1.87.0"}, "python3"]}}}