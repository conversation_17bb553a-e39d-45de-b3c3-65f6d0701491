diff --git a/CMakeLists.txt b/CMakeLists.txt
index 4c0a9be..cbec22f 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -28,7 +28,7 @@ ENDIF (NOT CMAKE_BUILD_TYPE)
 
 SET(CMAKE_C_FLAGS "${CMAKE_C_FLAGS}")
 
-IF (WIN32)
+IF (WIN32 AND FALSE)
   SET(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} /W4")
   ADD_DEFINITIONS(-DWIN32)
   SET(linkFlags "/PDB:NONE /INCREMENTAL:NO /OPT:NOREF /OPT:NOICF")
@@ -46,7 +46,8 @@ IF (WIN32)
   SET(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} /wd4996 /wd4255 /wd4130 /wd4100 /wd4711")
   SET(CMAKE_C_FLAGS_DEBUG "/D DEBUG /Od /Z7")
   SET(CMAKE_C_FLAGS_RELEASE "/D NDEBUG /O2")
-ELSE (WIN32)
+endif()
+if (NOT WIN32)
   SET(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -Wall")
   IF(CMAKE_COMPILER_IS_GNUCC)
     INCLUDE(CheckCCompilerFlag)
diff --git a/src/CMakeLists.txt b/src/CMakeLists.txt
index 99cf9e9..454482a 100644
--- a/src/CMakeLists.txt
+++ b/src/CMakeLists.txt
@@ -78,7 +78,7 @@ INCLUDE_DIRECTORIES(${incDir}/..)
 # at build time you may specify the cmake variable LIB_SUFFIX to handle
 # 64-bit systems which use 'lib64'
 INSTALL(TARGETS yajl
-        RUNTIME DESTINATION lib${LIB_SUFFIX}
+        RUNTIME DESTINATION bin
         LIBRARY DESTINATION lib${LIB_SUFFIX}
         ARCHIVE DESTINATION lib${LIB_SUFFIX})
 INSTALL(TARGETS yajl_s ARCHIVE DESTINATION lib${LIB_SUFFIX})
