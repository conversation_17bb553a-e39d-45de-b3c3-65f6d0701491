{"name": "frozen", "version": "1.2.0", "description": "Header-only library that provides 0 cost initialization for immutable containers and various algorithms. Frozen provides:immutable (a.k.a. frozen), constexpr-compatible versions of std::set, std::unordered_set, std::map and std::unordered_map and 0-cost initialization version of std::search for frozen needles using Boyer<PERSON> or K<PERSON><PERSON>-<PERSON> algorithms.", "homepage": "https://github.com/serge-sans-paille/frozen", "license": "Apache-2.0", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}]}