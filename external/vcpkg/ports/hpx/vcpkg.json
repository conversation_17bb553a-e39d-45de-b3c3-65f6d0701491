{"name": "hpx", "version": "1.10.0", "port-version": 1, "description": ["The C++ Standards Library for Concurrency and Parallelism", "HPX is a C++ Standards Library for Concurrency and Parallelism. It implements all of the corresponding facilities as defined by the C++ Standard. Additionally, in HPX we implement functionalities proposed as part of the ongoing C++ standardization process. We also extend the C++ Standard APIs to the distributed case."], "homepage": "https://github.com/STEllAR-GROUP/hpx", "license": "BSL-1.0", "supports": "!(windows & x86)", "dependencies": ["asio", "boost-accumulators", "boost-config", "boost-context", "boost-dynamic-bitset", "boost-exception", "boost-filesystem", "boost-iostreams", "boost-lockfree", "boost-range", "boost-spirit", "boost-system", "boost-throw-exception", "boost-variant", "boost-winapi", {"name": "gperftools", "platform": "linux"}, "hwl<PERSON>", {"name": "pkgconf", "host": true}, {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "default-features": ["bzip2", "mpi", "snappy", "zlib"], "features": {"bzip2": {"description": "Build with bzip2 compression", "dependencies": ["bzip2"]}, "cuda": {"description": "Build with CUDA support", "dependencies": ["cuda"]}, "mpi": {"description": "Build with MPI parcelport", "dependencies": ["mpi"]}, "snappy": {"description": "Build with snappy compression", "dependencies": ["snappy"]}, "zlib": {"description": "Build with zlib compression", "dependencies": ["zlib"]}}}