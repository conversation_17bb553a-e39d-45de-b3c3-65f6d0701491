diff --git a/CMakeLists.txt b/CMakeLists.txt
index 65b58c4..a8f024c 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -184,7 +184,7 @@ set (PEDANTIC FALSE CACHE BOOL "Determines if we should compile with -Wall -Werr
 set (WITH_DESIGNER_PLUGIN TRUE CACHE BOOL "Build plugins for Qt Designer")
 add_feature_info("Qt Designer plugins" WITH_DESIGNER_PLUGIN "Marble widget support in Qt Designer. Toggle with WITH_DESIGNER_PLUGIN=YES/NO")
 
-set(EXEC_INSTALL_PREFIX  ${CMAKE_INSTALL_PREFIX} CACHE PATH  "Base directory for executables and libraries" FORCE)
+set(EXEC_INSTALL_PREFIX  ${CMAKE_INSTALL_PREFIX}/bin CACHE PATH  "Base directory for executables and libraries" FORCE)
 if (NOT QT_PLUGINS_DIR)
    set(QT_PLUGINS_DIR ${CMAKE_INSTALL_LIBDIR}/plugins)
 endif()
diff --git a/src/plugins/positionprovider/CMakeLists.txt b/src/plugins/positionprovider/CMakeLists.txt
index ec2ba1a..cc8e52e 100644
--- a/src/plugins/positionprovider/CMakeLists.txt
+++ b/src/plugins/positionprovider/CMakeLists.txt
@@ -12,12 +12,12 @@ find_package(Qt5 ${REQUIRED_QT_VERSION}
     OPTIONAL_COMPONENTS
         Positioning
 )
-marble_set_package_properties( Qt5Positioning PROPERTIES DESCRIPTION "a collection of APIs and frameworks" )
-marble_set_package_properties( Qt5Positioning PROPERTIES URL "https://www.qt.io/developers/" )
-marble_set_package_properties( Qt5Positioning PROPERTIES TYPE OPTIONAL PURPOSE "position information via Qt5Positioning" )
-if(Qt5Positioning_FOUND)
-   ADD_SUBDIRECTORY( qtpositioning )
-endif()
+#marble_set_package_properties( Qt5Positioning PROPERTIES DESCRIPTION "a collection of APIs and frameworks" )
+#marble_set_package_properties( Qt5Positioning PROPERTIES URL "https://www.qt.io/developers/" )
+#marble_set_package_properties( Qt5Positioning PROPERTIES TYPE OPTIONAL PURPOSE "position information via Qt5Positioning" )
+#if(Qt5Positioning_FOUND)
+#   ADD_SUBDIRECTORY( qtpositioning )
+#endif()
 
 
 macro_optional_find_package(libwlocate)
