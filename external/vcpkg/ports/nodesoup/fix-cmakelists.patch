diff --git a/CMakeLists.txt b/CMakeLists.txt
index be6aa9d..26e59f3 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -34,7 +34,7 @@ install(
   ARCHIVE DESTINATION lib
   INCLUDES
   DESTINATION include
-  PUBLIC_HEADER DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}/nodesoup)
+  PUBLIC_HEADER DESTINATION include)
 
 include(CMakePackageConfigHelpers)
 write_basic_package_version_file(
diff --git a/src/CMakeLists.txt b/src/CMakeLists.txt
index dbd43b9..44164c9 100644
--- a/src/CMakeLists.txt
+++ b/src/CMakeLists.txt
@@ -5,7 +5,9 @@ add_library(nodesoup::nodesoup ALIAS nodesoup)
 target_include_directories(
   nodesoup
   PUBLIC $<BUILD_INTERFACE:${nodesoup_SOURCE_DIR}/include>
-  PRIVATE $<BUILD_INTERFACE:${nodesoup_SOURCE_DIR}/src>)
+         $<INSTALL_INTERFACE:include>
+  PRIVATE $<BUILD_INTERFACE:${nodesoup_SOURCE_DIR}/src>
+)
 
 target_compile_features(nodesoup PUBLIC cxx_std_14)
 
