{"$comment": "Automatically generated by scripts/boost/generate-ports.ps1", "name": "boost-statechart", "version": "1.87.0", "description": "Boost statechart module", "homepage": "https://www.boost.org/libs/statechart", "license": "BSL-1.0", "dependencies": [{"name": "boost-assert", "version>=": "1.87.0"}, {"name": "boost-bind", "version>=": "1.87.0"}, {"name": "boost-cmake", "version>=": "1.87.0"}, {"name": "boost-config", "version>=": "1.87.0"}, {"name": "boost-conversion", "version>=": "1.87.0"}, {"name": "boost-core", "version>=": "1.87.0"}, {"name": "boost-detail", "version>=": "1.87.0"}, {"name": "boost-function", "version>=": "1.87.0"}, {"name": "boost-headers", "version>=": "1.87.0"}, {"name": "boost-mpl", "version>=": "1.87.0"}, {"name": "boost-smart-ptr", "version>=": "1.87.0"}, {"name": "boost-static-assert", "version>=": "1.87.0"}, {"name": "boost-thread", "version>=": "1.87.0"}, {"name": "boost-type-traits", "version>=": "1.87.0"}]}