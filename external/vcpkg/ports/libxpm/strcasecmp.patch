diff --git a/include/X11/xpm.h b/include/X11/xpm.h
index f108f1f6e..143b6657d 100644
--- a/include/X11/xpm.h	
+++ b/include/X11/xpm.h
@@ -69,6 +69,10 @@
 # include <malloc.h>
 # include "simx.h"		/* defines some X stuff using MSW types */
 #define NEED_STRCASECMP		/* at least for MSVC++ */
+#elif _MSC_VER
+#define strcasecmp _stricmp
+#  include <X11/Xlib.h>
+#  include <X11/Xutil.h>
 #else /* FOR_MSW */
 # ifdef AMIGA
 #  include "amigax.h"
