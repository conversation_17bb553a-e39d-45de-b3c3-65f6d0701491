diff --git a/configure.ac b/configure.ac
index 51bc0f2bc..e35dc6e17 100644
--- a/configure.ac	
+++ b/configure.ac
@@ -25,9 +25,9 @@ AC_CHECK_FUNC([fork],[], AC_DEFINE(NO_ZPIPE))
 
 # Obtain compiler/linker options for dependencies
 PKG_CHECK_MODULES(XPM, xproto x11)
-PKG_CHECK_MODULES(SXPM, [x11 xt xext xextproto xproto >= 7.0.17],
-                  [build_sxpm=true], [build_sxpm=false])
+# PKG_CHECK_MODULES(SXPM, [x11 xt xext xextproto xproto >= 7.0.17],
+#                  [build_sxpm=true], [build_sxpm=false])
 AM_CONDITIONAL(BUILD_SXPM, test x$build_sxpm = xtrue)
 
 # Internationalization & localization support
 AC_ARG_WITH([gettext], AC_HELP_STRING([--with-gettext],
