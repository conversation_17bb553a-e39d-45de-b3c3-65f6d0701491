{"name": "devil", "version": "1.8.0", "port-version": 13, "description": "A full featured cross-platform image library", "homepage": "https://github.com/DentonW/DevIL", "supports": "!uwp", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "default-features": ["jasper", "lcms", "libjpeg", "libpng", "openexr", "tiff"], "features": {"jasper": {"description": "Use JasPer for .jp2 (and some .icns) support", "dependencies": ["jasper"]}, "lcms": {"description": "Use Little CMS for color profiles", "dependencies": ["lcms"]}, "libjpeg": {"description": "Use Libjpeg for .jpg (and some .blp) support", "dependencies": ["libjpeg-turbo"]}, "libpng": {"description": "Use Libpng for .png (and some .ico)", "dependencies": ["libpng"]}, "openexr": {"description": " Use openexr", "dependencies": ["openexr"]}, "tiff": {"description": "Use Libtiff for .tif support", "dependencies": [{"name": "tiff", "default-features": false}]}}}