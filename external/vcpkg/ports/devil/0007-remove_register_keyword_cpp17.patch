diff --git a/DevIL/src-IL/src/il_manip.cpp b/DevIL/src-IL/src/il_manip.cpp
index 79acc99..229a692 100644
--- a/DevIL/src-IL/src/il_manip.cpp
+++ b/DevIL/src-IL/src/il_manip.cpp
@@ -37,9 +37,9 @@ ILushort ILAPIENTRY ilFloatToHalf(ILuint i) {
 	// of float and half (127 versus 15).
 	//
 
-	register int s =  (i >> 16) & 0x00008000;
-	register int e = ((i >> 23) & 0x000000ff) - (127 - 15);
-	register int m =   i        & 0x007fffff;
+	int s =  (i >> 16) & 0x00008000;
+	int e = ((i >> 23) & 0x000000ff) - (127 - 15);
+	int m =   i        & 0x007fffff;
 
 	//
 	// Now reassemble s, e and m into a half:
diff --git a/DevIL/src-ILU/src/ilu_scaling.cpp b/DevIL/src-ILU/src/ilu_scaling.cpp
index c2893a3..ef35c13 100644
--- a/DevIL/src-ILU/src/ilu_scaling.cpp
+++ b/DevIL/src-ILU/src/ilu_scaling.cpp
@@ -406,7 +406,7 @@ main(argc, argv)
 int argc;
 char *argv[];
 {
-	register int c;
+	int c;
 	int optind;
 	char *optarg;
 	int xsize = 0, ysize = 0;
