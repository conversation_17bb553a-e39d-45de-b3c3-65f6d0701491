diff --git a/DevIL/src-IL/src/il_jp2.cpp b/DevIL/src-IL/src/il_jp2.cpp
index 730afee..a419c14 100644
--- a/DevIL/src-IL/src/il_jp2.cpp
+++ b/DevIL/src-IL/src/il_jp2.cpp
@@ -313,20 +313,31 @@ ILboolean iLoadJp2Internal(jas_stream_t	*Stream, ILimage *Image)
 }
 
 
-
-static int iJp2_file_read(jas_stream_obj_t *obj, char *buf, int cnt)
+#ifdef __APPLE__
+static int iJp2_file_read(void *obj, char *buf, unsigned int cnt)
+#else
+static int iJp2_file_read(jas_stream_obj_t *obj, char *buf, unsigned int cnt)
+#endif
 {
 	obj;
-	return iread(buf, 1, cnt);
+	return iread((void*)buf, 1, cnt);
 }
 
-static int iJp2_file_write(jas_stream_obj_t *obj, char *buf, int cnt)
+#ifdef __APPLE__
+static int iJp2_file_write(void *obj, const char *buf, unsigned int cnt)
+#else
+static int iJp2_file_write(jas_stream_obj_t *obj, const char *buf, unsigned int cnt)
+#endif
 {
 	obj;
 	return iwrite(buf, 1, cnt);
 }
 
+#ifdef __APPLE__
 static long iJp2_file_seek(jas_stream_obj_t *obj, long offset, int origin)
+#else
+static long iJp2_file_seek(void *obj, long offset, int origin)
+#endif
 {
 	obj;
 
@@ -343,7 +354,11 @@ static long iJp2_file_seek(jas_stream_obj_t *obj, long offset, int origin)
 	return 0;  // Failed
 }
 
+#ifdef __APPLE__
+static int iJp2_file_close(void *obj)
+#else
 static int iJp2_file_close(jas_stream_obj_t *obj)
+#endif
 {
 	obj;
 	return 0;  // We choose when we want to close the file.
