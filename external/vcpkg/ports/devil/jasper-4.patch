diff --git a/DevIL/src-IL/src/il_jp2.cpp b/DevIL/src-IL/src/il_jp2.cpp
index a419c1484..7b7e3cb12 100644
--- a/DevIL/src-IL/src/il_jp2.cpp
+++ b/DevIL/src-IL/src/il_jp2.cpp
@@ -314,9 +314,9 @@ ILboolean iLoadJp2Internal(jas_stream_t	*Stream, ILimage *Image)
 
 
 #ifdef __APPLE__
-static int iJp2_file_read(void *obj, char *buf, unsigned int cnt)
+static ssize_t iJp2_file_read(void *obj, char *buf, size_t cnt)
 #else
-static int iJp2_file_read(jas_stream_obj_t *obj, char *buf, unsigned int cnt)
+static ssize_t iJp2_file_read(jas_stream_obj_t *obj, char *buf, size_t cnt)
 #endif
 {
 	obj;
@@ -324,9 +324,9 @@ static int iJp2_file_read(jas_stream_obj_t *obj, char *buf, unsigned int cnt)
 }
 
 #ifdef __APPLE__
-static int iJp2_file_write(void *obj, const char *buf, unsigned int cnt)
+static ssize_t iJp2_file_write(void *obj, const char *buf, size_t cnt)
 #else
-static int iJp2_file_write(jas_stream_obj_t *obj, const char *buf, unsigned int cnt)
+static ssize_t iJp2_file_write(jas_stream_obj_t *obj, const char *buf, size_t cnt)
 #endif
 {
 	obj;
