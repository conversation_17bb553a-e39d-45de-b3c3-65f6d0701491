diff --git a/DevIL/src-IL/CMakeLists.txt b/DevIL/src-IL/CMakeLists.txt
index 02e4f03..4accaa1 100644
--- a/DevIL/src-IL/CMakeLists.txt
+++ b/DevIL/src-IL/CMakeLists.txt
@@ -235,8 +235,12 @@ target_link_libraries(IL ${libs})
 
 # generate config.h
 configure_file(${CMAKE_CURRENT_SOURCE_DIR}/include/config.h.cmake.in ${CMAKE_CURRENT_SOURCE_DIR}/include/config.h)
-
-
+add_definitions(-DHAVE_CONFIG_H)
+if(WIN32)
+	if(BUILD_SHARED_LIBS)
+		add_definitions(-DOPENEXR_DLL)
+	endif()
+endif()
 
 # generate pkg-config file
 # TODO: add Requires.private or Libs.private
diff --git a/DevIL/src-IL/src/il_exr.cpp b/DevIL/src-IL/src/il_exr.cpp
index bbb3978..3916c6f 100644
--- a/DevIL/src-IL/src/il_exr.cpp
+++ b/DevIL/src-IL/src/il_exr.cpp
@@ -14,15 +14,10 @@
 #include "il_internal.h"
 #ifndef IL_NO_EXR
 
-#ifndef HAVE_CONFIG_H // We are probably on a Windows box .
-//#define OPENEXR_DLL
-#define HALF_EXPORTS
-#endif //HAVE_CONFIG_H
-
 #include "il_exr.h"
-#include <ImfRgba.h>
-#include <ImfArray.h>
-#include <ImfRgbaFile.h>
+#include <OpenEXR/ImfRgba.h>
+#include <OpenEXR/ImfArray.h>
+#include <OpenEXR/ImfRgbaFile.h>
 //#include <ImfTiledRgbaFile.h>
 //#include <ImfInputFile.h>
 //#include <ImfTiledInputFile.h>
diff --git a/DevIL/src-IL/include/il_exr.h b/DevIL/src-IL/include/il_exr.h
index 3d880c4..04d721a 100644
--- a/DevIL/src-IL/include/il_exr.h
+++ b/DevIL/src-IL/include/il_exr.h
@@ -15,7 +15,7 @@
 #define EXR_H
 
 #include "il_internal.h"
-#include <ImfIO.h>
+#include <OpenEXR/ImfIO.h>
 
 
 //using namespace Imf;  // Using this leads to errors with Microsoft's IStream.
