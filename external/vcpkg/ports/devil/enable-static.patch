diff --git a/DevIL/src-ILU/CMakeLists.txt b/DevIL/src-ILU/CMakeLists.txt
index 17a3afe..a46ab41 100644
--- a/DevIL/src-ILU/CMakeLists.txt
+++ b/DevIL/src-ILU/CMakeLists.txt
@@ -43,7 +43,7 @@ source_group("Header Files" FILES ${ILU_INC} )
 source_group("Resource Files" FILES ${ILU_RSRC} )
 
 # Remove SHARED to create a static library
-add_library(ILU SHARED ${ILU_SRCS} ${ILU_INC} ${ILU_RSRC})
+add_library(ILU ${ILU_SRCS} ${ILU_INC} ${ILU_RSRC})
 
 
 ## ILU requires IL
diff --git a/DevIL/src-ILUT/CMakeLists.txt b/DevIL/src-ILUT/CMakeLists.txt
index 21470cb..398d3c1 100644
--- a/DevIL/src-ILUT/CMakeLists.txt
+++ b/DevIL/src-ILUT/CMakeLists.txt
@@ -43,7 +43,7 @@ source_group("Header Files" FILES ${ILUT_INC} )
 source_group("Resource Files" FILES ${ILUT_RSRC} )
 
 # Remove SHARED to create a static library
-add_library(ILUT SHARED ${ILUT_SRCS} ${ILUT_INC} ${ILUT_RSRC})
+add_library(ILUT ${ILUT_SRCS} ${ILUT_INC} ${ILUT_RSRC})
 
 ## add link sub library info
 target_link_libraries(ILUT
