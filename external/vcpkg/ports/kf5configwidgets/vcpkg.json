{"name": "kf5configwidgets", "version": "5.98.0", "description": "Widgets for configuration dialogs", "homepage": "https://api.kde.org/frameworks/kconfigwidgets/html/index.html", "dependencies": ["ecm", {"name": "gettext", "host": true, "features": ["tools"]}, {"name": "kf5auth", "platform": "!(windows | android)"}, "kf5codecs", "kf5config", "kf5coreaddons", "kf5guiaddons", "kf5i18n", "kf5widgetsaddons", "qt5-base", "qt5-tools", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"designerplugin": {"description": "Enables a Qt Designer plugin"}}}