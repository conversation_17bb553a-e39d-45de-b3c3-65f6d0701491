# Automatically generated by scripts/boost/generate-ports.ps1

vcpkg_from_github(
    OUT_SOURCE_PATH SOURCE_PATH
    REPO boostorg/coroutine2
    REF boost-${VERSION}
    SHA512 c1576b0a5d6f6a83c8711d11d82dca71e4e8f687d255ade6c6680a104a46b826434beb67a45830d2c7694a6a732b877ac969b8096fb51280b6ced501609d6e77
    HEAD_REF master
)

set(FEATURE_OPTIONS "")
boost_configure_and_install(
    SOURCE_PATH "${SOURCE_PATH}"
    OPTIONS ${FEATURE_OPTIONS}
)
