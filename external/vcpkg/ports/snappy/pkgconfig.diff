diff --git a/CMakeLists.txt b/CMakeLists.txt
index c3062e2..05477e9 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -417,4 +417,18 @@ if(SNAPPY_INSTALL)
       "${PROJECT_BINARY_DIR}/cmake/${PROJECT_NAME}ConfigVersion.cmake"
     DESTINATION "${CMAKE_INSTALL_LIBDIR}/cmake/${PROJECT_NAME}"
   )
+
+  cmake_policy(SET CMP0057 NEW)
+  set(LIBS_PRIVATE "")
+  foreach(lib IN LISTS CMAKE_CXX_IMPLICIT_LINK_LIBRARIES)
+    if(lib IN_LIST CMAKE_C_IMPLICIT_LINK_LIBRARIES)
+      continue()
+    elseif(EXISTS "${lib}")
+      string(APPEND LIBS_PRIVATE " ${CMAKE_LINK_LIBRARY_FILE_FLAG}${lib}")
+    else()
+      string(APPEND LIBS_PRIVATE " ${CMAKE_LINK_LIBRARY_FLAG}${lib}")
+    endif()
+  endforeach()
+  configure_file(snappy.pc.in "${CMAKE_CURRENT_BINARY_DIR}/snappy.pc" @ONLY)
+  install(FILES "${CMAKE_CURRENT_BINARY_DIR}/snappy.pc" DESTINATION "${CMAKE_INSTALL_LIBDIR}/pkgconfig")
 endif(SNAPPY_INSTALL)
