{"name": "webui", "version": "2.4.2", "description": "Use any web browser or WebView as GUI, with your preferred language in the backend and modern web technologies in the frontend, all in a lightweight portable library.", "homepage": "https://github.com/webui-dev/webui", "license": "MIT", "supports": "!uwp & !(arm32 & android)", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"tls": {"description": "Enable TLS support", "dependencies": ["openssl"]}}}