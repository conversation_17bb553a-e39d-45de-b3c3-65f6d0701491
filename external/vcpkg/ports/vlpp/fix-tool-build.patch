diff --git a/Import/CMakeLists.txt b/Import/CMakeLists.txt
index c65ab65..2101e86 100644
--- a/Import/CMakeLists.txt
+++ b/Import/CMakeLists.txt
@@ -10,6 +10,7 @@ cmake_dependent_option(WOR<PERSON>FLOW_COMPILER "Use VlppWorkflowCompiler" ON "WORKFLOW
 cmake_dependent_option(GACUI_CORE "Use GacUI" ON "GLR_PARSER;WORKFLOW_LIBRARY" OFF)
 cmake_dependent_option(GACUI_REFLECTION "Use GacUIReflection" ON "GACUI_CORE;WORKFLOW_RUNTIME;REFLECTION" OFF)
 cmake_dependent_option(GACUI_COMPILER "Use GacUICompiler" ON "GACUI_REFLECTION;WORKFLOW_COMPILER" OFF)
+option(BUILD_TOOLS "Build tools" OFF)
 
 # core library
 set(CORE_HDRS
@@ -144,6 +145,11 @@ if (GACUI_CORE)
     list(APPEND EXPORT_TARGETS gacui_core)
 endif()
 
+if (BUILD_TOOLS)
+    add_subdirectory(gacgen/Tools/GacGen)
+    add_subdirectory(workflow/Tools/CppMerge)
+endif()
+
 # Install targets
 install(
   TARGETS ${EXPORT_TARGETS}
