
FUNCTION(SET_LIBRARY_TARGET NAMESPACE LIB_NAME DEBUG_LIB_FILE_NAME RELEASE_LIB_FILE_NAME INCLUDE_DIR)
    ADD_LIBRARY(${NAMESPACE}::${LIB_NAME} STATIC IMPORTED)
    SET_TARGET_PROPERTIES(${NAMESPACE}::${LIB_NAME} PROPERTIES
                          IMPORTED_CONFIGURATIONS "RELEASE;DEBUG"
                          IMPORTED_LOCATION_RELEASE "${RELEASE_LIB_FILE_NAME}"
                          IMPORTED_LOCATION_DEBUG "${DEBUG_LIB_FILE_NAME}"
                          INTERFACE_INCLUDE_DIRECTORIES "${INCLUDE_DIR}"
                          )
    SET(${NAMESPACE}_${LIB_NAME}_FOUND 1)
ENDFUNCTION()

GET_FILENAME_COMPONENT(DPDK_ROOT "${CMAKE_CURRENT_LIST_FILE}" PATH)
GET_FILENAME_COMPONENT(DPDK_ROOT "${DPDK_ROOT}" PATH)
GET_FILENAME_COMPONENT(DPDK_ROOT "${DPDK_ROOT}" PATH)

SET_LIBRARY_TARGET("SPDK" "dpdk" "${DPDK_ROOT}/debug/lib/spdk/libdpdk.a" "${DPDK_ROOT}/lib/spdk/libdpdk.a" "${DPDK_ROOT}/include/spdk-dpdk")

IF (CMAKE_BUILD_TYPE STREQUAL Debug)
    LINK_DIRECTORIES(${DPDK_ROOT}/debug/lib/spdk)
ELSE ()
    LINK_DIRECTORIES(${DPDK_ROOT}/lib/spdk)
ENDIF ()

FILE(GLOB DPDK_LIBS ${DPDK_ROOT}/lib/spdk/librte*.*)
FOREACH (LIB_FILE_NAME ${DPDK_LIBS})
    GET_FILENAME_COMPONENT(LIB_NAME ${LIB_FILE_NAME} NAME_WE)
    GET_FILENAME_COMPONENT(FULL_LIB_NAME ${LIB_FILE_NAME} NAME)
    STRING(REPLACE "lib" "" LIB_NAME "${LIB_NAME}")
    SET_LIBRARY_TARGET("SPDK" "${LIB_NAME}" "${DPDK_ROOT}/debug/lib/spdk/${FULL_LIB_NAME}" "${DPDK_ROOT}/lib/spdk/${FULL_LIB_NAME}" "${DPDK_ROOT}/include/spdk-dpdk")
ENDFOREACH ()
