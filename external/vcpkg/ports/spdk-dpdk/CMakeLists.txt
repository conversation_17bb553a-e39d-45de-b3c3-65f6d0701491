CMAKE_MINIMUM_REQUIRED(VERSION 3.9)
PROJECT(dpdk C)

LIST(APPEND CMAKE_MODULE_PATH "${CMAKE_SOURCE_DIR}")
LIST(APPEND CMAKE_PREFIX_PATH "${CMAKE_SOURCE_DIR}")

INCLUDE(ProcessorCount)
PROCESSORCOUNT(PROCS)

#ARCH can be: i686, x86_64, ppc_64, arm64
#MACHINE can be: native, power8, armv8a
#EXECENV can be: linuxapp, bsdapp
#TOOLCHAIN can be: gcc, icc

SET(ARCH x86_64)
SET(MACHINE native)
SET(EXECENV linuxapp)
SET(TOOLCHAIN gcc)

IF (CMAKE_SYSTEM_PROCESSOR MATCHES "^i.86$")
    SET(ARCH "i686")
ELSEIF (CMAKE_SYSTEM_PROCESSOR MATCHES "^arm")
    SET(MACHINE arm8a)
ELSEIF (CMAKE_SYSTEM_PROCESSOR MATCHES "^(powerpc|ppc)64")
    SET(MACHINE power8)
    SET(ARCH ppc_64)
ENDIF ()

IF (CMAKE_SYSTEM_NAME STREQUAL "FreeBSD")
    SET(EXECENV bsdapp)
ENDIF ()

FIND_PACKAGE(Numa REQUIRED)
FIND_PROGRAM(MAKE make)

SET(DPDK_CFLAGS "-fPIC")
IF (CMAKE_BUILD_TYPE STREQUAL Debug)
    STRING(APPEND ${DPDK_CFLAGS} "-O0 -g")
ENDIF ()

SET(DPDK_TARGET ${ARCH}-${MACHINE}-${EXECENV}-${TOOLCHAIN})
#SET(DPDK_CONFIG O=${CMAKE_BUILD_TYPE}/build T=${DPDK_TARGET})
SET(DPDK_CONFIG "T=${DPDK_TARGET}")

IF (SOURCE_PATH)
    SET(CMAKE_SOURCE_DIR ${SOURCE_PATH})
ENDIF ()

ADD_CUSTOM_TARGET(dpdk-configure
                  COMMAND ${MAKE} config ${DPDK_CONFIG}
                  WORKING_DIRECTORY ${CMAKE_SOURCE_DIR}/
                  VERBATIM
                  )

ADD_CUSTOM_TARGET(dpdk ALL
                  COMMAND ${MAKE} EXTRA_CFLAGS=\"${DPDK_CFLAGS}\" MAKEFLAGS=\"${DPDK_CONFIG} -j ${PROCS}\"
                  WORKING_DIRECTORY ${CMAKE_SOURCE_DIR}/
                  )

ADD_DEPENDENCIES(dpdk dpdk-configure)

INSTALL(DIRECTORY ${CMAKE_SOURCE_DIR}/build/lib
        DESTINATION ${CMAKE_SOURCE_DIR}/${CMAKE_BUILD_TYPE}/)
