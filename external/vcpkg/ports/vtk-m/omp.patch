diff --git a/CMake/VTKmDeviceAdapters.cmake b/CMake/VTKmDeviceAdapters.cmake
index e9ac039..c745604 100644
--- a/CMake/VTKmDeviceAdapters.cmake
+++ b/CMake/VTKmDeviceAdapters.cmake
@@ -59,20 +59,14 @@ if(VTKm_ENABLE_OPENMP AND NOT TARGET vtkm::openmp)
   find_package(OpenMP 4.0 REQUIRED COMPONENTS CXX QUIET)
 
   add_library(vtkm_openmp INTERFACE)
   set_target_properties(vtkm_openmp PROPERTIES EXPORT_NAME openmp)
+  target_link_libraries(vtkm_openmp INTERFACE OpenMP::OpenMP_CXX)
   if(OpenMP_CXX_FLAGS)
-    set_property(TARGET vtkm_openmp
-      APPEND PROPERTY INTERFACE_COMPILE_OPTIONS $<$<COMPILE_LANGUAGE:CXX>:${OpenMP_CXX_FLAGS}>)
-
     if(VTKm_ENABLE_CUDA)
       string(REPLACE ";" "," openmp_cuda_flags "-Xcompiler=${OpenMP_CXX_FLAGS}")
       set_property(TARGET vtkm_openmp
         APPEND PROPERTY INTERFACE_COMPILE_OPTIONS $<$<COMPILE_LANGUAGE:CUDA>:${openmp_cuda_flags}>)
     endif()
   endif()
-  if(OpenMP_CXX_LIBRARIES)
-    set_target_properties(vtkm_openmp PROPERTIES
-      INTERFACE_LINK_LIBRARIES "${OpenMP_CXX_LIBRARIES}")
-  endif()
   install(TARGETS vtkm_openmp EXPORT ${VTKm_EXPORT_NAME})
 endif()
