{"name": "vtk-m", "version": "2.1.0", "description": "VTK-m is a toolkit of scientific visualization algorithms for emerging processor architectures.", "homepage": "https://gitlab.kitware.com/vtk/vtk-m/", "license": null, "supports": "!x86 & !uwp & !(arm & windows)", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"cuda": {"description": "Use the NVIDIA CUDA device adapter.", "dependencies": ["cuda"]}, "double": {"description": "Use double precision in floating point calculations"}, "mpi": {"description": "Use the MPI controller.", "dependencies": ["mpi"]}, "omp": {"description": "Use the OpenMP device adapter."}, "tbb": {"description": "Use the Intel TBB device adapter.", "dependencies": ["tbb"]}}}