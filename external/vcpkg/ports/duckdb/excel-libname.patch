diff --git a/CMakeLists.txt b/CMakeLists.txt
index 29d069e..eb8d178 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -1,3 +1,4 @@
+
 cmake_minimum_required(VERSION 2.8.12...3.29)
 set(TARGET_NAME excel)
 set(EXTENSION_NAME ${TARGET_NAME}_extension)
@@ -6,7 +7,7 @@ set(LOADABLE_EXTENSION_NAME ${TARGET_NAME}_loadable_extension)
 project(ExcelExtension)
 
 # Dependencies from VCPKG
-find_package(EXPAT REQUIRED)
+find_package(expat REQUIRED)
 find_package(ZLIB REQUIRED)
 find_package(minizip-ng CONFIG REQUIRED)
 
@@ -23,9 +24,9 @@ set(PARAMETERS "-warnings")
 build_loadable_extension(${TARGET_NAME} ${PARAMETERS} ${EXTENSION_SOURCES}
                          ${NUMFORMAT_OBJECT_FILES})
 
-target_link_libraries(${EXTENSION_NAME} EXPAT::EXPAT MINIZIP::minizip-ng
+target_link_libraries(${EXTENSION_NAME} expat::expat MINIZIP::minizip-ng
                       ZLIB::ZLIB)
-target_link_libraries(${LOADABLE_EXTENSION_NAME} EXPAT::EXPAT
+target_link_libraries(${LOADABLE_EXTENSION_NAME} expat::expat
                       MINIZIP::minizip-ng ZLIB::ZLIB)
 
 install(
