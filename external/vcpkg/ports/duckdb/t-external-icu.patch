diff --git a/DuckDBConfig.cmake.in b/DuckDBConfig.cmake.in
index 2e5270e..7c5ce31 100644
--- a/DuckDBConfig.cmake.in
+++ b/DuckDBConfig.cmake.in
@@ -7,7 +7,7 @@
 include(CMakeFindDependencyMacro)
 find_dependency(Threads)
 if(NOT @WITH_INTERNAL_ICU@)
-    find_dependency(ICU COMPONENTS i18n uc)
+    find_dependency(ICU COMPONENTS i18n uc data)
 endif()
 
 set(EXTENSION_LIST "@BUILD_EXTENSIONS@")
diff --git a/extension/icu/CMakeLists.txt b/extension/icu/CMakeLists.txt
index b5585e4..e3ae9cf 100644
--- a/extension/icu/CMakeLists.txt
+++ b/extension/icu/CMakeLists.txt
@@ -30,12 +30,15 @@ set(ICU_EXTENSION_FILES
 build_static_extension(icu ${ICU_EXTENSION_FILES})
 link_threads(icu_extension)
 if(NOT WITH_INTERNAL_ICU)
-    find_package(ICU COMPONENTS i18n uc REQUIRED)
-    target_link_libraries(icu_extension ICU::i18n ICU::uc)
+    find_package(ICU COMPONENTS i18n uc data REQUIRED)
+    target_link_libraries(icu_extension ICU::i18n ICU::uc ICU::data)
 endif()
 disable_target_warnings(icu_extension)
 set(PARAMETERS "-no-warnings")
 build_loadable_extension(icu ${PARAMETERS} ${ICU_EXTENSION_FILES})
+if(NOT WITH_INTERNAL_ICU)
+  target_link_libraries(icu_loadable_extension ICU::i18n ICU::uc ICU::data)
+endif()
 install(
   TARGETS icu_extension
   EXPORT "${DUCKDB_EXPORT_SET}"
