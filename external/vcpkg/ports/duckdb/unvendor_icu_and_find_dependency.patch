diff --git a/DuckDBConfig.cmake.in b/DuckDBConfig.cmake.in
index ef61b6b..2e5270e 100644
--- a/DuckDBConfig.cmake.in
+++ b/DuckDBConfig.cmake.in
@@ -4,6 +4,12 @@
 #  DuckDB_INCLUDE_DIRS - include directories for DuckDB
 #  DuckDB_LIBRARIES    - libraries to link against
 
+include(CMakeFindDependencyMacro)
+find_dependency(Threads)
+if(NOT @WITH_INTERNAL_ICU@)
+    find_dependency(ICU COMPONENTS i18n uc)
+endif()
+
 # Compute paths
 get_filename_component(DuckDB_CMAKE_DIR "${CMAKE_CURRENT_LIST_FILE}" PATH)
 set(DuckDB_INCLUDE_DIRS "@CONF_INCLUDE_DIRS@")
diff --git a/extension/icu/CMakeLists.txt b/extension/icu/CMakeLists.txt
index 65cbe38..b5585e4 100644
--- a/extension/icu/CMakeLists.txt
+++ b/extension/icu/CMakeLists.txt
@@ -3,11 +3,14 @@ cmake_minimum_required(VERSION 2.8.12...3.29)
 project(ICUExtension)
 
 include_directories(include)
-include_directories(third_party/icu/common)
-include_directories(third_party/icu/i18n)
-
-add_subdirectory(third_party)
+option(WITH_INTERNAL_ICU "Use vendored copy of icu" TRUE)
+if(WITH_INTERNAL_ICU)
+    include_directories(third_party/icu/common)
+    include_directories(third_party/icu/i18n)
 
+    add_subdirectory(third_party)
+endif()
+    
 set(ICU_EXTENSION_FILES
     ${ICU_LIBRARY_FILES}
     icu_extension.cpp
@@ -26,6 +29,10 @@ set(ICU_EXTENSION_FILES
 
 build_static_extension(icu ${ICU_EXTENSION_FILES})
 link_threads(icu_extension)
+if(NOT WITH_INTERNAL_ICU)
+    find_package(ICU COMPONENTS i18n uc REQUIRED)
+    target_link_libraries(icu_extension ICU::i18n ICU::uc)
+endif()
 disable_target_warnings(icu_extension)
 set(PARAMETERS "-no-warnings")
 build_loadable_extension(icu ${PARAMETERS} ${ICU_EXTENSION_FILES})
diff --git a/extension/icu/icu-datefunc.cpp b/extension/icu/icu-datefunc.cpp
index 0c38827..c744b68 100644
--- a/extension/icu/icu-datefunc.cpp
+++ b/extension/icu/icu-datefunc.cpp
@@ -72,7 +72,7 @@ unique_ptr<FunctionData> ICUDateFunc::Bind(ClientContext &context, ScalarFunctio
 }
 
 bool ICUDateFunc::TrySetTimeZone(icu::Calendar *calendar, const string_t &tz_id) {
-	auto tz = icu_66::TimeZone::createTimeZone(icu::UnicodeString::fromUTF8(icu::StringPiece(tz_id.GetString())));
+	auto tz = icu::TimeZone::createTimeZone(icu::UnicodeString::fromUTF8(icu::StringPiece(tz_id.GetString())));
 	if (*tz == icu::TimeZone::getUnknown()) {
 		delete tz;
 		return false;
