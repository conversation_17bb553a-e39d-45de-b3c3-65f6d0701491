{"name": "libdatrie", "version": "0.2.13", "port-version": 1, "description": "Implementation of double-array structure for representing trie", "homepage": "https://linux.thai.net/pub/ThaiLinux/software/libthai", "license": "LGPL-2.1-or-later", "dependencies": [{"name": "vcpkg-cmake", "host": true}], "features": {"tool": {"description": "Build the trietool application", "supports": "!uwp", "dependencies": ["libiconv"]}}}