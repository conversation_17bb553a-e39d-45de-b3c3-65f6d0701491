diff --git a/CMakeLists.txt b/CMakeLists.txt
index 9395f76..3bd354a 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -134,8 +134,6 @@ install(EXPORT libbase_targets
 file(WRITE "${CMAKE_CURRENT_BINARY_DIR}/${CMAKE_PROJECT_NAME}-config.cmake"
 "include(CMakeFindDependencyMacro)\n"
 "find_dependency(glog CONFIG REQUIRED)\n"
-"find_dependency(GTest CONFIG QUIET)\n"
-"find_dependency(benchmark CONFIG QUIET)\n"
 "include(\"\${CMAKE_CURRENT_LIST_DIR}/${CMAKE_PROJECT_NAME}-targets.cmake\")\n"
 )
 
