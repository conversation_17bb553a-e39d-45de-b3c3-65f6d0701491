diff --git a/configure.ac b/configure.ac
index a956106..739ee88 100644
--- a/configure.ac
+++ b/configure.ac
@@ -110,7 +110,7 @@ else
     fi
 fi
 # Extract the linker and include flags
-GEOS_LDFLAGS=`$GEOSCONFIG --ldflags`
+GEOS_LDFLAGS=`$GEOSCONFIG --clibs`
 GEOS_CFLAGS=-I`$GEOSCONFIG --includes`
 AC_SUBST([GEOS_LDFLAGS])
 AC_SUBST([GEOS_CFLAGS])
@@ -122,9 +122,9 @@ CPPFLAGS="$CPPFLAGS_SAVE"
 # Ensure we can link against libgeos_c
 LIBS_SAVE="$LIBS"
 LIBS="$GEOS_LDFLAGS"
-AC_SEARCH_LIBS(GEOSContext_setErrorMessageHandler_r,geos_c,,AC_MSG_ERROR([could not find libgeos_c (or obsolete 'libgeos_c' < v.3.5.0 found) - you may need to specify the directory of a geos-config file using --with-geosconfig]))
+AC_SEARCH_LIBS(GEOSContext_setErrorMessageHandler_r,,,AC_MSG_ERROR([could not find libgeos_c (or obsolete 'libgeos_c' < v.3.5.0 found) - you may need to specify the directory of a geos-config file using --with-geosconfig]))
 LIBS="$LIBS_SAVE"
-LIBS="$LIBS $GEOS_LDFLAGS -lgeos_c"
+LIBS="$LIBS $GEOS_LDFLAGS"
 GEOS_MAJOR_VERSION=`$GEOSCONFIG --version | cut -d. -f1 | sed 's/[[^0-9]]//g'`
 GEOS_MINOR_VERSION=`$GEOSCONFIG --version | cut -d. -f2 | sed 's/[[^0-9]]//g'`
 RTGEOM_GEOS_VERSION="$GEOS_MAJOR_VERSION$GEOS_MINOR_VERSION"
