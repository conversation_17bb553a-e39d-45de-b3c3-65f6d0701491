diff -u -r a/src/python/CMakeLists.txt b/src/python/CMakeLists.txt
--- a/src/python/CMakeLists.txt
+++ b/src/python/CMakeLists.txt
@@ -3,6 +3,13 @@
 # https://github.com/AcademySoftwareFoundation/OpenImageIO
 
 
+if(USE_FFMPEG AND UNIX AND NOT BUILD_SHARED_LIBS AND VCPKG_CRT_LINKAGE STREQUAL "dynamic")
+    include(CheckLinkerFlag)
+    check_linker_flag(C "LINKER:-Bsymbolic" supports_bsymbolic)
+    if(supports_bsymbolic)
+        add_link_options("LINKER:-Bsymbolic")
+    endif()
+endif()
 
 file (GLOB python_srcs *.cpp)
 setup_python_module (TARGET    PyOpenImageIO
