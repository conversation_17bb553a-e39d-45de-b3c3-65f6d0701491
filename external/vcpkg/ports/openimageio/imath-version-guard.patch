diff --git a/src/cmake/externalpackages.cmake b/src/cmake/externalpackages.cmake
index 816ba08..7fe6a14 100644
--- a/src/cmake/externalpackages.cmake
+++ b/src/cmake/externalpackages.cmake
@@ -133,6 +133,11 @@ if (NOT OPENCOLORIO_INCLUDES)
     get_target_property(OPENCOLORIO_INCLUDES OpenColorIO::OpenColorIO INTERFACE_INCLUDE_DIRECTORIES)
 endif ()
 include_directories(BEFORE ${OPENCOLORIO_INCLUDES})
+if(USE_OPENCOLORIO AND TARGET Imath::Imath AND OIIO_USING_IMATH STREQUAL "2")
+    message(FATAL_ERROR
+        "OpenColorIO and OpenEXR use incompatible versions of Imath. "
+        "You cannot use openimageio[opencolorio] for this configuration.")
+endif()

 set(OPENCV_INCLUDES "")
 set(OPENCV_LIBRARIES opencv_core)
