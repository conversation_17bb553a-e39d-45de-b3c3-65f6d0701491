diff --git a/src/cmake/Config.cmake.in b/src/cmake/Config.cmake.in
index 2620994b3..c9cbe7290 100644
--- a/src/cmake/Config.cmake.in
+++ b/src/cmake/Config.cmake.in
@@ -10,6 +10,8 @@ include(CMakeFindDependencyMacro)
 if (NOT @OPENIMAGEIO_CONFIG_DO_NOT_FIND_IMATH@ AND NOT OPENIMAGEIO_CONFIG_DO_NOT_FIND_IMATH)
     find_dependency(Imath @Imath_VERSION@
                     HINTS @Imath_DIR@)
+    find_dependency(OpenEXR @OpenEXR_VERSION@
+                        HINTS @OpenEXR_DIR@)
 endif ()
 
 if (NOT @fmt_LOCAL_BUILD@ AND NOT @OIIO_INTERNALIZE_FMT@)
