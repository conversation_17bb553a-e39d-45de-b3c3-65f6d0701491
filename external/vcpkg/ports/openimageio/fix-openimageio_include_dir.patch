diff --git a/src/cmake/Config.cmake.in b/src/cmake/Config.cmake.in
index 2a67ee3..11a5d67 100644
--- a/src/cmake/Config.cmake.in
+++ b/src/cmake/Config.cmake.in
@@ -53,7 +53,7 @@ endif ()
 
 # Compute the installation prefix relative to this file. Note that cmake files are installed
 # to ${CMAKE_INSTALL_LIBDIR}/cmake/${PROJECT_NAME} (see OIIO_CONFIG_INSTALL_DIR)
-get_filename_component(_CURR_INSTALL_LIBDIR "${CMAKE_CURRENT_LIST_DIR}/../../" ABSOLUTE)
+get_filename_component(_CURR_INSTALL_LIBDIR "${CMAKE_CURRENT_LIST_DIR}/../" ABSOLUTE)
 get_filename_component(_ABS_CMAKE_INSTALL_LIBDIR "@CMAKE_INSTALL_FULL_LIBDIR@" ABSOLUTE)
 get_filename_component(_ABS_CMAKE_INSTALL_INCLUDEDIR "@CMAKE_INSTALL_FULL_INCLUDEDIR@" ABSOLUTE)
 file(RELATIVE_PATH _INCLUDEDIR_RELATIVE_TO_LIBDIR
