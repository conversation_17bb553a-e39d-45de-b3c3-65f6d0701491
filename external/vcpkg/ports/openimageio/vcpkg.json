{"name": "openimageio", "version": "*******", "port-version": 2, "description": "A library for reading and writing images, and a bunch of related classes, utilities, and application.", "homepage": "https://github.com/OpenImageIO/oiio", "license": "BSD-3-<PERSON><PERSON>", "dependencies": ["fmt", "libjpeg-turbo", "libpng", "opencolorio", "openexr", "robin-map", {"name": "tiff", "default-features": false}, {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}, "zlib"], "features": {"ffmpeg": {"description": "Enable ffmpeg support for openimageio", "dependencies": [{"name": "ffmpeg", "default-features": false, "features": ["avresample"]}]}, "freetype": {"description": "Enable freetype support for openimageio", "dependencies": ["freetype"]}, "gif": {"description": "Enable giflib support for openimageio", "dependencies": ["gif<PERSON>b"]}, "jpegxl": {"description": "Enable JPEG XL codec", "dependencies": ["libjxl"]}, "libheif": {"description": "Enable heif support for openimageio", "dependencies": ["lib<PERSON><PERSON>"]}, "libraw": {"description": "Enable RAW image files support", "dependencies": ["libraw"]}, "opencolorio": {"description": ["Enable opencolorio support for openimageio.", "This feature can only be used when openexr and opencolorio use the same version of Imath."], "dependencies": ["opencolorio"]}, "opencv": {"description": "Enable opencv support for openimageio", "dependencies": [{"name": "opencv", "default-features": false, "features": ["fs", "intrinsics", "thread"]}]}, "openjpeg": {"description": "Enable openjpeg support for openimageio", "dependencies": ["openjpeg"]}, "pybind11": {"description": "Enable Python bindings support for openimageio", "dependencies": ["pybind11", "python3"]}, "tools": {"description": "Build openimageio tools"}, "viewer": {"description": "Build openimageio viewer", "dependencies": ["opengl", {"name": "openimageio", "features": ["tools"]}, {"name": "qtbase", "default-features": false}]}, "webp": {"description": "Enable libwebp support for openimageio", "dependencies": ["libwebp"]}}}