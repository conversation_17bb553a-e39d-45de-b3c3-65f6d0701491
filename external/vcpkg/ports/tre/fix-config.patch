diff --git a/win32/config.h b/win32/config.h
index 93b8210..db7a9e1 100644
--- a/win32/config.h
+++ b/win32/config.h
@@ -20,7 +20,6 @@
 /* #undef HAVE_ALLOCA_H */
 
 /* Define to 1 if you have <malloc.h> and it should be used. */
-#define HAVE_MALLOC_H 1
 
 /* Define if the GNU dcgettext() function is already present or preinstalled.
    */
@@ -180,4 +179,3 @@
 /* Avoid silly warnings about "insecure" functions. */
 #define _CRT_SECURE_NO_DEPRECATE 1
 
-#define snprintf sprintf_s
