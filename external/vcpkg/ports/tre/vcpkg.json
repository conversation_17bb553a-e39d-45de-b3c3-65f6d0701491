{"name": "tre", "version": "0.8.0", "port-version": 6, "description": "TRE is a lightweight, robust, and efficient POSIX compliant regexp matching library with some exciting features such as approximate (fuzzy) matching.", "homepage": "https://github.com/laurikari/tre", "license": "BSD-2-<PERSON><PERSON>", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}]}