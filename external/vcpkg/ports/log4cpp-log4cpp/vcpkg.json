{"name": "log4cpp-log4cpp", "version": "1.1.4", "description": "A library of C++ classes for flexible logging to files (rolling), syslog, IDSA and other destinations. It is modeled after the Log for Java library (http://www.log4j.org), staying as close to their API as is reasonable.", "homepage": "https://sourceforge.net/projects/log4cpp/", "supports": "(linux | windows) & !uwp", "dependencies": [{"name": "vcpkg-cmake", "host": true}]}