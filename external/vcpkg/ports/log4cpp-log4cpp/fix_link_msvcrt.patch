diff --git a/CMakeLists.txt b/CMakeLists.txt
index 140910b..08cde3b 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -81,8 +81,12 @@ ADD_LIBRARY ( ${LOG4CPP_LIBRARY_NAME}
 )
 
 IF (WIN32)
-  TARGET_LINK_LIBRARIES (${LOG4CPP_LIBRARY_NAME} kernel32 user32 ws2_32 advapi32 )
-  SET_TARGET_PROPERTIES(${LOG4CPP_LIBRARY_NAME} PROPERTIES LINK_FLAGS /NODEFAULTLIB:msvcrt )
+  TARGET_LINK_LIBRARIES (${LOG4CPP_LIBRARY_NAME} kernel32 user32 ws2_32 advapi32)
+  IF ( CMAKE_BUILD_TYPE MATCHES "Debug" )
+    SET_TARGET_PROPERTIES(${LOG4CPP_LIBRARY_NAME} PROPERTIES LINK_FLAGS /NODEFAULTLIB:MSVCRT)
+  ELSE(CMAKE_BUILD_TYPE MATCHES "Debug" )
+    SET_TARGET_PROPERTIES(${LOG4CPP_LIBRARY_NAME} PROPERTIES LINK_FLAGS /NODEFAULTLIB:MSVCRTD)
+  ENDIF(CMAKE_BUILD_TYPE MATCHES "Debug" )
 ENDIF (WIN32)
 
 INSTALL (
@@ -92,9 +96,11 @@ INSTALL (
   PATTERN ".svn" EXCLUDE
   PATTERN "*.am" EXCLUDE
   PATTERN "*.in" EXCLUDE
+  PATTERN ".cvsignore" EXCLUDE
   )
 
 INSTALL (
   TARGETS ${LOG4CPP_LIBRARY_NAME}
   ARCHIVE DESTINATION lib
+  RUNTIME DESTINATION bin
   )
