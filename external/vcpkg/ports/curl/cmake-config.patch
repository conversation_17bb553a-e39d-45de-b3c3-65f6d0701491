diff --git a/CMake/curl-config.cmake.in b/CMake/curl-config.cmake.in
index a013adf..2f39949 100644
--- a/CMake/curl-config.cmake.in
+++ b/CMake/curl-config.cmake.in
@@ -46,5 +46,6 @@ check_required_components("@PROJECT_NAME@")
 
 # Alias for either shared or static library
 if(NOT TARGET @PROJECT_NAME@::libcurl)
-  add_library(@PROJECT_NAME@::libcurl ALIAS @PROJECT_NAME@::@LIB_SELECTED@)
+  add_library(@PROJECT_NAME@::libcurl INTERFACE IMPORTED)
+  set_target_properties(@PROJECT_NAME@::libcurl PROPERTIES INTERFACE_LINK_LIBRARIES @PROJECT_NAME@::@LIB_SELECTED@)
 endif()
