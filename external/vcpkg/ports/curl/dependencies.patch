diff --git a/CMakeLists.txt b/CMakeLists.txt
index 86add74..be7b193 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -345,7 +345,7 @@ set(LIBCURL_PC_REQUIRES_PRIVATE "")
 if(ENABLE_ARES)
   set(USE_ARES 1)
   find_package(Cares REQUIRED)
-  list(APPEND CURL_LIBS ${CARES_LIBRARIES})
+  list(APPEND CURL_LIBS ${CARES_LINK_LIBRARIES})
   list(APPEND CURL_LIBDIRS ${CARES_LIBRARY_DIRS})
   list(APPEND LIBCURL_PC_REQUIRES_PRIVATE ${CARES_PC_REQUIRES})
   link_directories(${CARES_LIBRARY_DIRS})
@@ -789,7 +789,7 @@ if(CURL_USE_MBEDTLS)
   find_package(MbedTLS REQUIRED)
   set(_ssl_enabled ON)
   set(USE_MBEDTLS ON)
-  list(APPEND CURL_LIBS ${MBEDTLS_LIBRARIES})
+  list(APPEND CURL_LIBS ${MBEDTLS_LINK_LIBRARIES})
   list(APPEND CURL_LIBDIRS ${MBEDTLS_LIBRARY_DIRS})
   list(APPEND LIBCURL_PC_REQUIRES_PRIVATE ${MBEDTLS_PC_REQUIRES})
   include_directories(SYSTEM ${MBEDTLS_INCLUDE_DIRS})
@@ -823,7 +823,7 @@ if(CURL_USE_WOLFSSL)
   find_package(WolfSSL REQUIRED)
   set(_ssl_enabled ON)
   set(USE_WOLFSSL ON)
-  list(APPEND CURL_LIBS ${WOLFSSL_LIBRARIES})
+  list(APPEND CURL_LIBS ${WOLFSSL_LINK_LIBRARIES})
   list(APPEND CURL_LIBDIRS ${WOLFSSL_LIBRARY_DIRS})
   list(APPEND LIBCURL_PC_REQUIRES_PRIVATE ${WOLFSSL_PC_REQUIRES})
   include_directories(SYSTEM ${WOLFSSL_INCLUDE_DIRS})
@@ -856,7 +856,7 @@ if(CURL_USE_GNUTLS)
   find_package(Nettle REQUIRED)
   set(_ssl_enabled ON)
   set(USE_GNUTLS ON)
-  list(APPEND CURL_LIBS ${GNUTLS_LIBRARIES} ${NETTLE_LIBRARIES})
+  list(APPEND CURL_LIBS ${GNUTLS_LINK_LIBRARIES} ${NETTLE_LINK_LIBRARIES})
   list(APPEND CURL_LIBDIRS ${GNUTLS_LIBRARY_DIRS} ${NETTLE_LIBRARY_DIRS})
   list(APPEND LIBCURL_PC_REQUIRES_PRIVATE "gnutls" ${NETTLE_PC_REQUIRES})
   include_directories(SYSTEM ${GNUTLS_INCLUDE_DIRS} ${NETTLE_INCLUDE_DIRS})
@@ -919,7 +919,7 @@ set(HAVE_BROTLI OFF)
 curl_dependency_option(CURL_BROTLI Brotli "brotli")
 if(BROTLI_FOUND)
   set(HAVE_BROTLI ON)
-  list(APPEND CURL_LIBS ${BROTLI_LIBRARIES})
+  list(APPEND CURL_LIBS ${BROTLI_LINK_LIBRARIES})
   list(APPEND CURL_LIBDIRS ${BROTLI_LIBRARY_DIRS})
   list(APPEND LIBCURL_PC_REQUIRES_PRIVATE ${BROTLI_PC_REQUIRES})
   include_directories(SYSTEM ${BROTLI_INCLUDE_DIRS})
@@ -934,7 +934,7 @@ curl_dependency_option(CURL_ZSTD Zstd "zstd")
 if(ZSTD_FOUND)
   if(NOT ZSTD_VERSION VERSION_LESS 1.0.0)
     set(HAVE_ZSTD ON)
-    list(APPEND CURL_LIBS ${ZSTD_LIBRARIES})
+    list(APPEND CURL_LIBS ${ZSTD_LINK_LIBRARIES})
     list(APPEND CURL_LIBDIRS ${ZSTD_LIBRARY_DIRS})
     list(APPEND LIBCURL_PC_REQUIRES_PRIVATE ${ZSTD_PC_REQUIRES})
     include_directories(SYSTEM ${ZSTD_INCLUDE_DIRS})
@@ -1052,7 +1052,7 @@ option(USE_NGHTTP2 "Use nghttp2 library" ON)
 if(USE_NGHTTP2)
   find_package(NGHTTP2)
   if(NGHTTP2_FOUND)
-    list(APPEND CURL_LIBS ${NGHTTP2_LIBRARIES})
+    list(APPEND CURL_LIBS ${NGHTTP2_LINK_LIBRARIES})
     list(APPEND CURL_LIBDIRS ${NGHTTP2_LIBRARY_DIRS})
     list(APPEND LIBCURL_PC_REQUIRES_PRIVATE ${NGHTTP2_PC_REQUIRES})
     include_directories(SYSTEM ${NGHTTP2_INCLUDE_DIRS})
@@ -1084,7 +1084,7 @@ if(USE_NGTCP2)
   else()
     message(FATAL_ERROR "ngtcp2 requires OpenSSL, wolfSSL or GnuTLS")
   endif()
-  list(APPEND CURL_LIBS ${NGTCP2_LIBRARIES})
+  list(APPEND CURL_LIBS ${NGTCP2_LINK_LIBRARIES})
   list(APPEND CURL_LIBDIRS ${NGTCP2_LIBRARY_DIRS})
   list(APPEND LIBCURL_PC_REQUIRES_PRIVATE ${NGTCP2_PC_REQUIRES})
   include_directories(SYSTEM ${NGTCP2_INCLUDE_DIRS})
@@ -1095,7 +1095,7 @@ if(USE_NGTCP2)
 
   find_package(NGHTTP3 REQUIRED)
   set(USE_NGHTTP3 ON)
-  list(APPEND CURL_LIBS ${NGHTTP3_LIBRARIES})
+  list(APPEND CURL_LIBS ${NGHTTP3_LINK_LIBRARIES})
   list(APPEND CURL_LIBDIRS ${NGHTTP3_LIBRARY_DIRS})
   list(APPEND LIBCURL_PC_REQUIRES_PRIVATE ${NGHTTP3_PC_REQUIRES})
   include_directories(SYSTEM ${NGHTTP3_INCLUDE_DIRS})
@@ -1196,7 +1196,7 @@ if(NOT CURL_DISABLE_LDAP)
     find_package(LDAP)
     if(LDAP_FOUND)
       set(HAVE_LBER_H 1)
-      set(CURL_LIBS ${LDAP_LIBRARIES} ${CURL_LIBS})
+      list(PREPEND CURL_LIBS ${LDAP_LINK_LIBRARIES})
       list(APPEND CURL_LIBDIRS ${LDAP_LIBRARY_DIRS})
       if(LDAP_PC_REQUIRES)
         set(LIBCURL_PC_REQUIRES_PRIVATE ${LDAP_PC_REQUIRES} ${LIBCURL_PC_REQUIRES_PRIVATE})
@@ -1273,7 +1273,7 @@ set(HAVE_LIBIDN2 OFF)
 if(USE_LIBIDN2 AND NOT USE_APPLE_IDN AND NOT USE_WIN32_IDN)
   find_package(Libidn2)
   if(LIBIDN2_FOUND)
-    set(CURL_LIBS ${LIBIDN2_LIBRARIES} ${CURL_LIBS})
+    list(PREPEND CURL_LIBS ${LIBIDN2_LINK_LIBRARIES})
     list(APPEND CURL_LIBDIRS ${LIBIDN2_LIBRARY_DIRS})
     set(LIBCURL_PC_REQUIRES_PRIVATE ${LIBIDN2_PC_REQUIRES} ${LIBCURL_PC_REQUIRES_PRIVATE})
     include_directories(SYSTEM ${LIBIDN2_INCLUDE_DIRS})
@@ -1293,7 +1293,7 @@ set(USE_LIBPSL OFF)
 
 if(CURL_USE_LIBPSL)
   find_package(Libpsl REQUIRED)
-  list(APPEND CURL_LIBS ${LIBPSL_LIBRARIES})
+  list(APPEND CURL_LIBS ${LIBPSL_LINK_LIBRARIES})
   list(APPEND CURL_LIBDIRS ${LIBPSL_LIBRARY_DIRS})
   list(APPEND LIBCURL_PC_REQUIRES_PRIVATE ${LIBPSL_PC_REQUIRES})
   include_directories(SYSTEM ${LIBPSL_INCLUDE_DIRS})
@@ -1312,7 +1312,7 @@ set(USE_LIBSSH2 OFF)
 if(CURL_USE_LIBSSH2)
   find_package(Libssh2)
   if(LIBSSH2_FOUND)
-    set(CURL_LIBS ${LIBSSH2_LIBRARIES} ${CURL_LIBS})  # keep it before TLS-crypto, compression
+    list(PREPEND CURL_LIBS ${LIBSSH2_LINK_LIBRARIES}) # keep it before TLS-crypto, compression
     list(APPEND CURL_LIBDIRS ${LIBSSH2_LIBRARY_DIRS})
     set(LIBCURL_PC_REQUIRES_PRIVATE ${LIBSSH2_PC_REQUIRES} ${LIBCURL_PC_REQUIRES_PRIVATE})
     include_directories(SYSTEM ${LIBSSH2_INCLUDE_DIRS})
@@ -1361,7 +1361,7 @@ option(CURL_USE_GSASL "Use libgsasl" OFF)
 mark_as_advanced(CURL_USE_GSASL)
 if(CURL_USE_GSASL)
   find_package(Libgsasl REQUIRED)
-  list(APPEND CURL_LIBS ${LIBGSASL_LIBRARIES})
+  list(APPEND CURL_LIBS ${LIBGSASL_LINK_LIBRARIES})
   list(APPEND CURL_LIBDIRS ${LIBGSASL_LIBRARY_DIRS})
   list(APPEND LIBCURL_PC_REQUIRES_PRIVATE ${LIBGSASL_PC_REQUIRES})
   include_directories(SYSTEM ${LIBGSASL_INCLUDE_DIRS})
@@ -1380,7 +1380,7 @@ if(CURL_USE_GSSAPI)
 
   set(HAVE_GSSAPI ${GSS_FOUND})
   if(GSS_FOUND)
-    list(APPEND CURL_LIBS ${GSS_LIBRARIES})
+    list(APPEND CURL_LIBS ${_GSS_LINK_LIBRARIES})
     list(APPEND CURL_LIBDIRS ${GSS_LIBRARY_DIRS})
     list(APPEND LIBCURL_PC_REQUIRES_PRIVATE ${GSS_PC_REQUIRES})
     include_directories(SYSTEM ${GSS_INCLUDE_DIRS})
@@ -1451,7 +1451,7 @@ endif()
 option(USE_LIBRTMP "Enable librtmp from rtmpdump" OFF)
 if(USE_LIBRTMP)
   find_package(Librtmp REQUIRED)
-  list(APPEND CURL_LIBS ${LIBRTMP_LIBRARIES})
+  list(APPEND CURL_LIBS ${LIBRTMP_LINK_LIBRARIES})
   list(APPEND CURL_LIBDIRS ${LIBRTMP_LIBRARY_DIRS})
   list(APPEND LIBCURL_PC_REQUIRES_PRIVATE ${LIBRTMP_PC_REQUIRES})
   include_directories(SYSTEM ${LIBRTMP_INCLUDE_DIRS})
