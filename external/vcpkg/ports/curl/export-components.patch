diff --git a/CMakeLists.txt b/CMakeLists.txt
index 30c0154..d55cef4 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -2437,7 +2437,17 @@ if(NOT CURL_DISABLE_INSTALL)
       DESTINATION ${_install_cmake_dir})
   endif()
 
+  set(components_file "${CMAKE_CURRENT_BINARY_DIR}/CURLConfigComponents.cmake")
+  file(CONFIGURE OUTPUT "${components_file}" CONTENT [[
+  foreach(z_vcpkg_curl_component IN ITEMS @SUPPORT_FEATURES@ @SUPPORT_PROTOCOLS@)
+    if(z_vcpkg_curl_component MATCHES "^[-_a-zA-Z0-9]*$")
+      set(CURL_${z_vcpkg_curl_component}_FOUND TRUE)
+    endif()
+  endforeach()
+  ]] @ONLY)
+
   install(FILES ${_version_config} ${_project_config}
+                ${components_file}
     DESTINATION ${_install_cmake_dir})
 
   if(NOT TARGET curl_uninstall)

