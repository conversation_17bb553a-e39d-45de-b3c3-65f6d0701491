{"name": "colmap", "version": "3.11.1", "port-version": 3, "description": "COLMAP is a general-purpose Structure-from-Motion (SfM) and Multi-View Stereo (MVS) pipeline with a graphical and command-line interface. It offers a wide range of features for reconstruction of ordered and unordered image collections. The software is licensed under the new BSD license.", "homepage": "https://colmap.github.io/", "license": "BSD-3-<PERSON><PERSON>", "supports": "(linux | (windows & !static) | osx) & (x86 | x64 | arm64)", "dependencies": ["boost-algorithm", "boost-graph", "boost-heap", "boost-program-options", "boost-property-map", "boost-property-tree", {"name": "ceres", "features": ["lapack", "suitesparse"]}, "eigen3", "flann", "freeimage", "gflags", "glog", {"name": "jasper", "default-features": false}, "metis", "poselib", "sqlite3", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "default-features": ["gui"], "features": {"cgal": {"description": "Build with CGAL.", "dependencies": ["cgal"]}, "cuda": {"description": "Build with CUDA.", "dependencies": ["cuda", "glew"]}, "cuda-redist": {"description": "Redistributable CUDA support for common supported compute architectures.", "dependencies": ["cuda"]}, "gui": {"description": "Build the GUI.", "dependencies": ["glew", "qt5-base"]}, "tests": {"description": "Build all tests.", "dependencies": ["gtest"]}}}