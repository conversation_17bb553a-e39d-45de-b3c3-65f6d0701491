diff --git a/src/thirdparty/SiftGPU/GlobalUtil.cpp b/src/thirdparty/SiftGPU/GlobalUtil.cpp
index e98ef6b..39435db 100644
--- a/src/thirdparty/SiftGPU/GlobalUtil.cpp
+++ b/src/thirdparty/SiftGPU/GlobalUtil.cpp
@@ -137,7 +137,7 @@ void GlobalUtil::CheckErrorsGL(const char* location)
 	const char *errstr;
 	while (errnum = glGetError())
 	{
-		errstr = (const char *)(gluErrorString(errnum));
+		errstr = nullptr; // just print errnum
 		if(errstr) {
 			std::cerr << errstr;
 		}
