{"name": "ompl", "version": "1.6.0", "port-version": 4, "description": "The Open Motion Planning Library, consists of many state-of-the-art sampling-based motion planning algorithms", "homepage": "https://ompl.kavrakilab.org/", "supports": "!(static & staticcrt) | (windows & !mingw)", "dependencies": ["boost-algorithm", "boost-assert", "boost-concept-check", "boost-config", "boost-container-hash", "boost-dynamic-bitset", "boost-filesystem", "boost-foreach", "boost-graph", "boost-math", "boost-odeint", "boost-program-options", "boost-property-map", "boost-range", "boost-serialization", "boost-smart-ptr", "boost-system", "boost-thread", "boost-type-traits", "eigen3", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}]}