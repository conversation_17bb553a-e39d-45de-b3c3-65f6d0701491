diff --git a/omplConfig.cmake.in b/omplConfig.cmake.in
index 2d88251..c987fdc 100644
--- a/omplConfig.cmake.in
+++ b/omplConfig.cmake.in
@@ -18,8 +18,16 @@ set(OMPL_MAJOR_VERSION @PROJECT_VERSION_MAJOR@)
 set(OMPL_MINOR_VERSION @PROJECT_VERSION_MINOR@)
 set(OMPL_PATCH_VERSION @PROJECT_VERSION_PATCH@)
 
+include(CMakeFindDependencyMacro)
+find_dependency(Boost COMPONENTS @ALL_BOOST_COMPONENTS@)
+find_dependency(Eigen3)
+include("${CMAKE_CURRENT_LIST_DIR}/omplapp-dependencies.cmake" OPTIONAL)
+
+include("${CMAKE_CURRENT_LIST_DIR}/ompl-targets.cmake")
+include("${CMAKE_CURRENT_LIST_DIR}/omplapp-targets.cmake" OPTIONAL)
+
 set_and_check(OMPL_INCLUDE_DIR "@PACKAGE_INCLUDE_INSTALL_DIR@")
-set(OMPL_INCLUDE_DIRS "${OMPL_INCLUDE_DIR};@Boost_INCLUDE_DIR@;@EIGEN3_INCLUDE_DIR@")
+set(OMPL_INCLUDE_DIRS "${OMPL_INCLUDE_DIR};${Boost_INCLUDE_DIRS};${EIGEN3_INCLUDE_DIRS}")
 foreach(_dir @FLANN_INCLUDE_DIRS@;@ODE_INCLUDE_DIRS@;@SPOT_INCLUDE_DIRS@;@TRIANGLE_INCLUDE_DIR@;@FCL_INCLUDE_DIRS@;@PQP_INCLUDE_DIR@;@ASSIMP_INCLUDE_DIRS@;@OPENGL_INCLUDE_DIR@)
     if(_dir)
         list(APPEND OMPL_INCLUDE_DIRS "${_dir}")
@@ -29,7 +37,7 @@ list(REMOVE_DUPLICATES OMPL_INCLUDE_DIRS)
 set(OMPL_INCLUDE_DIRS "${OMPL_INCLUDE_DIRS}" CACHE STRING "Include path for OMPL and its dependencies")
 
 set_and_check(OMPL_LIBRARY_DIR @PACKAGE_LIB_INSTALL_DIR@)
-set(OMPL_LIBRARY_DIRS "${OMPL_LIBRARY_DIR};@Boost_LIBRARY_DIRS@")
+set(OMPL_LIBRARY_DIRS "${OMPL_LIBRARY_DIR};${Boost_LIBRARY_DIRS}")
 foreach(_dir @FLANN_LIBRARY_DIRS@;@ODE_LIBRARY_DIRS@;@SPOT_LIBRARY_DIRS@;@FCL_LIBRARY_DIRS@;@ASSIMP_LIBRARY_DIRS@)
     if(_dir)
         list(APPEND OMPL_LIBRARY_DIRS "${_dir}")
@@ -40,7 +48,7 @@ set(OMPL_LIBRARY_DIRS "${OMPL_LIBRARY_DIRS}" CACHE STRING "Library path for OMPL
 
 find_library(OMPL_LIBRARIES NAMES ompl.${OMPL_VERSION} ompl
     PATHS ${OMPL_LIBRARY_DIR} NO_DEFAULT_PATH)
-foreach(_lib @Boost_SERIALIZATION_LIBRARY@;@Boost_FILESYSTEM_LIBRARY@;@Boost_SYSTEM_LIBRARY@;@ODE_LIBRARIES@;@SPOT_LIBRARIES@)
+foreach(_lib ${Boost_SERIALIZATION_LIBRARY};${Boost_FILESYSTEM_LIBRARY};${Boost_SYSTEM_LIBRARY};@ODE_LIBRARIES@;@SPOT_LIBRARIES@)
     if(_lib)
         list(APPEND OMPL_LIBRARIES "${_lib}")
     endif()
diff --git a/src/ompl/CMakeLists.txt b/src/ompl/CMakeLists.txt
index df38912..5166568 100644
--- a/src/ompl/CMakeLists.txt
+++ b/src/ompl/CMakeLists.txt
@@ -60,6 +60,9 @@ target_link_libraries(ompl
     ${Boost_FILESYSTEM_LIBRARY}
     ${Boost_SYSTEM_LIBRARY}
     ${CMAKE_THREAD_LIBS_INIT})
+set(ALL_BOOST_COMPONENTS "serialization filesystem system" CACHE INTERNAL
+    "Components for Boost link libs as used above this line"
+)
 
 if (OMPL_EXTENSION_ODE)
     if (NOT CMAKE_VERSION VERSION_LESS 3.13)
