{"name": "sdl2-image", "version": "2.8.8", "port-version": 1, "description": "SDL_image is an image file loading library. It loads images as SDL surfaces and textures, and supports the following formats: BMP, GIF, JPEG, LBM, PCX, PNG, PNM, TGA, TIFF, WEBP, XCF, XPM, XV", "homepage": "https://github.com/libsdl-org/SDL_image", "license": "<PERSON><PERSON><PERSON>", "dependencies": ["libpng", {"name": "sdl2", "default-features": false}, {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"avif": {"description": "Support for AVIF image format", "dependencies": ["liba<PERSON>f"]}, "libjpeg-turbo": {"description": "Support for JPEG image format", "dependencies": ["libjpeg-turbo"]}, "libwebp": {"description": "Support for WEBP image format.", "dependencies": ["libwebp"]}, "tiff": {"description": "Support for TIFF image format", "dependencies": [{"name": "tiff", "default-features": false}]}}}