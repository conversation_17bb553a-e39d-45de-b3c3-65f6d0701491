{"name": "dv-processing", "version": "1.7.9", "port-version": 3, "description": "Generic algorithms for event cameras. (C++20 required.)", "homepage": "https://gitlab.com/inivation/dv/dv-processing", "license": "Apache-2.0", "dependencies": ["boost-algorithm", {"name": "boost-asio", "features": ["ssl"]}, "boost-callable-traits", "boost-circular-buffer", "boost-core", "boost-endian", "boost-geometry", "boost-lockfree", "boost-nowide", "boost-property-tree", "boost-stacktrace", "cli11", "eigen3", "fmt", "lib<PERSON>er", "lz4", {"name": "opencv4", "default-features": false, "features": ["fs", "intrinsics", "thread"]}, "openssl", {"name": "vcpkg-cmake", "host": true}, "zstd"], "features": {"tools": {"description": "Build CLI utilities"}}}