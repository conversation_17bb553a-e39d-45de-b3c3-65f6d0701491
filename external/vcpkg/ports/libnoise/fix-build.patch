diff --git a/noiseutils/CMakeLists.txt b/noiseutils/CMakeLists.txt
index 07747de..68db2a2 100644
--- a/noiseutils/CMakeLists.txt
+++ b/noiseutils/CMakeLists.txt
@@ -19,11 +19,14 @@ if(BUILD_SHARED_LIBS)
 	
 	set_target_properties(${TARGET_NAME} PROPERTIES VERSION ${LIBNOISE_VERSION})
 	target_link_libraries(${TARGET_NAME} noise)
-	target_include_directories(${TARGET_NAME} PRIVATE ${PROJECT_SOURCE_DIR}/src)
+	target_include_directories(${TARGET_NAME} PRIVATE ${PROJECT_SOURCE_DIR}/src PUBLIC $<BUILD_INTERFACE:${CMAKE_CURRENT_LIST_DIR}> $<INSTALL_INTERFACE:include>)
 	
 	# install dynamic libraries (.dll or .so) into /bin
-	install(TARGETS ${TARGET_NAME} DESTINATION "${CMAKE_INSTALL_PREFIX}/bin")
-endif()
+	install(TARGETS ${TARGET_NAME} EXPORT unofficial-noiseutilsTargets
+        RUNTIME DESTINATION "${CMAKE_INSTALL_PREFIX}/bin"
+        LIBRARY DESTINATION "${CMAKE_INSTALL_PREFIX}/lib"
+        ARCHIVE DESTINATION "${CMAKE_INSTALL_PREFIX}/lib")
+else()
 
 #----------------------------------------
 # build static lib (it's good practice to include a lib file for the dll)
@@ -31,11 +34,22 @@ set(TARGET_NAME "${LIB_NAME}-static")
 add_library(${TARGET_NAME} STATIC ${libSrcs})
 set_target_properties(${TARGET_NAME} PROPERTIES VERSION ${LIBNOISE_VERSION})
 target_link_libraries(${TARGET_NAME} noise-static) 
-target_include_directories(${TARGET_NAME} PRIVATE ${PROJECT_SOURCE_DIR}/src) 
+target_include_directories(${TARGET_NAME} PRIVATE ${PROJECT_SOURCE_DIR}/src PUBLIC $<BUILD_INTERFACE:${CMAKE_CURRENT_LIST_DIR}> $<INSTALL_INTERFACE:include>) 
 # install static libraries (.lib) into /lib
-install(TARGETS ${TARGET_NAME} DESTINATION "${CMAKE_INSTALL_PREFIX}/lib")
+install(TARGETS ${TARGET_NAME} EXPORT unofficial-noiseutilsTargets DESTINATION "${CMAKE_INSTALL_PREFIX}/lib")
 #----------------------------------------
-
+endif()
 # install include files into /include
 install( FILES "${PROJECT_SOURCE_DIR}/noiseutils/noiseutils.h" 
-		DESTINATION "${CMAKE_INSTALL_PREFIX}/include/noise" )
\ No newline at end of file
+		DESTINATION "${CMAKE_INSTALL_PREFIX}/include/noise" )
+
+install(EXPORT unofficial-noiseutilsTargets
+	NAMESPACE unofficial::noiseutils::
+	DESTINATION share/unofficial-noiseutils
+)
+
+file(WRITE "${CMAKE_CURRENT_BINARY_DIR}/unofficial-noiseutils-config.cmake.in"
+[[include("${CMAKE_CURRENT_LIST_DIR}/unofficial-noiseutilsTargets.cmake")]])
+configure_file("${CMAKE_CURRENT_BINARY_DIR}/unofficial-noiseutils-config.cmake.in" "${CMAKE_CURRENT_BINARY_DIR}/unofficial-noiseutils-config.cmake" @ONLY)
+install(FILES ${CMAKE_CURRENT_BINARY_DIR}/unofficial-noiseutils-config.cmake DESTINATION share/unofficial-noiseutils)
+		
\ No newline at end of file
diff --git a/src/CMakeLists.txt b/src/CMakeLists.txt
index 2757f30..47dcc51 100644
--- a/src/CMakeLists.txt
+++ b/src/CMakeLists.txt
@@ -60,10 +60,13 @@ if(BUILD_SHARED_LIBS)
 		add_library(${TARGET_NAME} SHARED ${libSrcs})
     endif()
 	set_target_properties(${TARGET_NAME} PROPERTIES VERSION ${LIBNOISE_VERSION})
-	target_include_directories(${TARGET_NAME} PRIVATE ${PROJECT_SOURCE_DIR}/src)
+	target_include_directories(${TARGET_NAME} PRIVATE ${PROJECT_SOURCE_DIR}/src PUBLIC $<BUILD_INTERFACE:${CMAKE_CURRENT_LIST_DIR}/noise> $<INSTALL_INTERFACE:include>)
 	target_compile_definitions(${TARGET_NAME} PRIVATE NOISE_BUILD_DLL)
-	install(TARGETS ${TARGET_NAME} DESTINATION "${CMAKE_INSTALL_PREFIX}/bin")
-endif()
+	install(TARGETS ${TARGET_NAME} EXPORT unofficial-noiseTargets
+        RUNTIME DESTINATION "${CMAKE_INSTALL_PREFIX}/bin"
+        LIBRARY DESTINATION "${CMAKE_INSTALL_PREFIX}/lib"
+        ARCHIVE DESTINATION "${CMAKE_INSTALL_PREFIX}/lib")
+else()
 
 #----------------------------------------
 # build static lib (it's good practice to include a lib file for the dll)
@@ -71,12 +74,22 @@ set(TARGET_NAME "${LIB_NAME}-static")
 message(STATUS "build ${TARGET_NAME}")
 add_library(${TARGET_NAME} STATIC ${libSrcs})
 set_target_properties(${TARGET_NAME} PROPERTIES VERSION ${LIBNOISE_VERSION})
-target_include_directories(${TARGET_NAME} PRIVATE ${PROJECT_SOURCE_DIR}/src)
+target_include_directories(${TARGET_NAME} PRIVATE ${PROJECT_SOURCE_DIR}/src PUBLIC $<BUILD_INTERFACE:${CMAKE_CURRENT_LIST_DIR}/noise> $<INSTALL_INTERFACE:include>)
 target_compile_definitions(${TARGET_NAME} PUBLIC NOISE_STATIC)
 # install static libraries (.lib) into /lib
-install(TARGETS ${TARGET_NAME} DESTINATION "${CMAKE_INSTALL_PREFIX}/lib")
+install(TARGETS ${TARGET_NAME} EXPORT unofficial-noiseTargets DESTINATION "${CMAKE_INSTALL_PREFIX}/lib")
 #----------------------------------------
-
+endif()
 # install include files into /include
 install( DIRECTORY   "${PROJECT_SOURCE_DIR}/src/noise" 
-		 DESTINATION "${CMAKE_INSTALL_PREFIX}/include" )
\ No newline at end of file
+		 DESTINATION "${CMAKE_INSTALL_PREFIX}/include" )
+
+install(EXPORT unofficial-noiseTargets
+	NAMESPACE unofficial::noise::
+	DESTINATION share/unofficial-noise
+)
+
+file(WRITE "${CMAKE_CURRENT_BINARY_DIR}/unofficial-noise-config.cmake.in"
+[[include("${CMAKE_CURRENT_LIST_DIR}/unofficial-noiseTargets.cmake")]])
+configure_file("${CMAKE_CURRENT_BINARY_DIR}/unofficial-noise-config.cmake.in" "${CMAKE_CURRENT_BINARY_DIR}/unofficial-noise-config.cmake" @ONLY)
+install(FILES ${CMAKE_CURRENT_BINARY_DIR}/unofficial-noise-config.cmake DESTINATION share/unofficial-noise)
