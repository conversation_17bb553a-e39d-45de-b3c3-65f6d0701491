{"name": "libnoise", "version": "1.0.0", "port-version": 3, "description": "A general-purpose library that generates three-dimensional coherent noise. Useful for terrain generation and procedural texture generation. Uses a broad number of techniques (Perlin noise, ridged multifractal, etc.) and combinations of those techniques.", "homepage": "https://github.com/RobertHue/libnoise", "license": "LGPL-2.1", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}]}