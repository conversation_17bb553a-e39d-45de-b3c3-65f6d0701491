{"$comment": "Use scripts/update_suitesparse.py to update all SuiteSparse ports", "name": "suitesparse-amd", "version-semver": "3.3.3", "description": "AMD: Routines for permuting sparse matrices prior to factorization in SuiteSparse", "homepage": "https://people.engr.tamu.edu/davis/suitesparse.html", "license": "BSD-3-<PERSON><PERSON>", "dependencies": ["suitesparse-config", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}]}