diff --git a/src/las2txt.cpp b/src/las2txt.cpp
index 7c8a841..196ec65 100644
--- a/src/las2txt.cpp
+++ b/src/las2txt.cpp
@@ -570,7 +570,7 @@ enum extended_flags{HSV = -1, HSL = -2, HSV255 = -3, HSL255 = -4};
 static void parse_extended_flags(char *parse_string)
 {
   const char *extended_flags[] = {"(HSV)", "(HSL)", "(hsv)", "(hsl)"};
-  const char replacement_codes[] = {HSV255, HSL255, HSV, HSL};
+  const signed char replacement_codes[] = {HSV255, HSL255, HSV, HSL};
   I32 nflags = (I32)(sizeof(extended_flags) / sizeof(char*));
 
   for (I32 i = 0; i < nflags; i++)
