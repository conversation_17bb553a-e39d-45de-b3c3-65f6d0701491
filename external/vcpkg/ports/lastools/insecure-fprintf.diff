diff --git a/LASzip/src/lasmessage.cpp b/LASzip/src/lasmessage.cpp
index c39d13a..e7d70cd 100644
--- a/LASzip/src/lasmessage.cpp
+++ b/LASzip/src/lasmessage.cpp
@@ -139,8 +139,8 @@ void las_default_message_handler(LAS_MESSAGE_TYPE type, const char* msg, void* u
 	if (!prefix.empty())
 	{
 		format_message(message, (unsigned)prefix.size());
-		fprintf(stderr, prefix.c_str());
-		fprintf(stderr, message.c_str());
+		fputs(prefix.c_str(), stderr);
+		fputs(message.c_str(), stderr);
 	}
 	else
 	{
diff --git a/src/geoprojectionconverter.cpp b/src/geoprojectionconverter.cpp
index 1e3298d..6a80699 100644
--- a/src/geoprojectionconverter.cpp
+++ b/src/geoprojectionconverter.cpp
@@ -4366,7 +4366,7 @@ bool GeoProjectionConverter::set_VerticalCSTypeGeoKey(short value, char* descrip
             // this is where the name ends
             line[run] = '\0';
           }
-          if (description) sprintf(description, name);
+          if (description) sprintf(description, "%s", name);
           run++;
           // skip two commas
           while (line[run] != ',') run++;
