diff --git a/LASlib/src/CMakeLists.txt b/LASlib/src/CMakeLists.txt
index 51dca48..97a25c4 100644
--- a/LASlib/src/CMakeLists.txt
+++ b/LASlib/src/CMakeLists.txt
@@ -104,15 +104,13 @@ endif()
 
 install(FILES ${LAS_INCLUDES} DESTINATION include/LASlib)
 
-if (MSVC)
+if (0)
 	foreach( OUTPUTCONFIG ${CMAKE_CONFIGURATION_TYPES} )
 		install(DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/../lib/${OUTPUTCONFIG} DESTINATION lib/LASlib)
 	endforeach( OUTPUTCONFIG CMAKE_CONFIGURATION_TYPES )
 else()
 	install(TARGETS LASlib EXPORT laslib-targets
-		ARCHIVE DESTINATION lib/LASlib
-		LIBRARY DESTINATION lib/LASlib
-		RUNTIME DESTINATION lib/LASlib)
+	)
 	install(EXPORT laslib-targets DESTINATION lib/cmake/LASlib)
 	install(FILES ${CMAKE_SOURCE_DIR}/LASlib/src/laslib-config.cmake DESTINATION lib/cmake/LASlib)
-endif(MSVC)
+endif(0)
