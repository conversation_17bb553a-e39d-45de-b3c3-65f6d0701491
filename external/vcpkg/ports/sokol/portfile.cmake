# header-only library

vcpkg_from_github(
    OUT_SOURCE_PATH SOURCE_PATH
    REPO floooh/sokol
    REF d98c8b92c25070f13d0491f5fade1d9d2ca885ad
    SHA512 bb49dc3da366e70c7b6b16ebd490f9b2c88c496b278f1b9651321b3aab0977db92707d3955c2cb2654d8032647429ac90aa585de36644380112d988eac877cd0
    HEAD_REF master
)

file(GLOB SOKOL_INCLUDE_FILES "${SOURCE_PATH}/*.h")
file(COPY ${SOKOL_INCLUDE_FILES} DESTINATION "${CURRENT_PACKAGES_DIR}/include")

file(GLOB SOKOL_UTIL_INCLUDE_FILES "${SOURCE_PATH}/util/*.h")
file(COPY ${SOKOL_UTIL_INCLUDE_FILES} DESTINATION "${CURRENT_PACKAGES_DIR}/include/util")

vcpkg_install_copyright(FILE_LIST "${SOURCE_PATH}/LICENSE")
