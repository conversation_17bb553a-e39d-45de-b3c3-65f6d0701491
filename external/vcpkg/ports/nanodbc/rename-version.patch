--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -20,8 +20,8 @@
 ########################################
 ## nanodbc version
 ########################################
-file(STRINGS VERSION NANODBC_VERSION REGEX "[0-9]+\\.[0-9]+\\.[0-9]+")
+file(STRINGS "VERSION.txt" NANODBC_VERSION REGEX "[0-9]+\\.[0-9]+\\.[0-9]+")
 string(REGEX REPLACE "^([0-9]+)\\.[0-9]+\\.[0-9]+" "\\1" NANODBC_VERSION_MAJOR "${NANODBC_VERSION}")
 string(REGEX REPLACE "^[0-9]+\\.([0-9])+\\.[0-9]+" "\\1" NANODBC_VERSION_MINOR "${NANODBC_VERSION}")
 string(REGEX REPLACE "^[0-9]+\\.[0-9]+\\.([0-9]+)" "\\1" NANODBC_VERSION_PATCH "${NANODBC_VERSION}")
 message(STATUS "nanodbc version: ${NANODBC_VERSION}")

