--- "a/CMakeLists.txt"
+++ "b/CMakeLists.txt"
@@ -110,6 +110,13 @@ message(STATUS "nanodbc feature: Enable SQL_NO_DATA bug workaround - ${NANODBC_E
 ## find unixODBC or iODBC config binary
 ########################################
 if(UNIX)
+  # Try to find unixodbc package first
+  find_package(unixodbc)
+  if(unixodbc_FOUND)
+    message(STATUS "nanodbc build: unixODBC package found")
+    set(ODBCLIB UNIX::odbc)
+    set(ODBC_CONFIG true)
+  else()
   # Try to find unixODBC first via odbc_config program.
   find_program(ODBC_CONFIG odbc_config
     PATHS $ENV{ODBC_PATH}/bin /usr/bin /usr/local/bin)
@@ -158,6 +165,7 @@ if(UNIX)
       endif()
     endif()
   endif()
+  endif()
 
   if(NOT ODBC_CONFIG)
     message(FATAL_ERROR "can not find a suitable odbc driver manager")
