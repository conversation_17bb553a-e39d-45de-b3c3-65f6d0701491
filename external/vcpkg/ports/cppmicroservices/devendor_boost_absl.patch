diff --git a/CMakeLists.txt b/CMakeLists.txt
index 87cfb37..d7fbd20 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -693,7 +693,7 @@ endif()
 # Compile libraries here if you do not want -Werror or /WX on
 #-----------------------------------------------------------------------------
 set(BUILD_SHARED_LIBS OFF CACHE BOOL "Build shared libraries" FORCE)
-add_subdirectory(third_party/boost/nowide)
+find_package(Boost COMPONENTS nowide CONFIG REQUIRED)
 set(BUILD_SHARED_LIBS ${_us_build_shared} CACHE BOOL "Build shared libraries" FORCE)
 #-----------------------------------------------------------------------------
 
diff --git a/cmake/usBundleConfig.cmake.in b/cmake/usBundleConfig.cmake.in
index c9cf743..f0ab1ca 100644
--- a/cmake/usBundleConfig.cmake.in
+++ b/cmake/usBundleConfig.cmake.in
@@ -3,6 +3,10 @@
 set(US_@PROJECT_NAME@_LIBRARIES @PROJECT_TARGET@)
 set(US_@PROJECT_NAME@_RUNTIME_LIBRARY_DIRS "@PACKAGE_CONFIG_RUNTIME_LIBRARY_DIR@")
 
+include(CMakeFindDependencyMacro)
+find_dependency(Boost COMPONENTS asio nowide CONFIG)
+find_dependency(absl CONFIG)
+
 if(NOT TARGET @PROJECT_TARGET@)
   include("${CMAKE_CURRENT_LIST_DIR}/us@<EMAIL>")
 endif()
diff --git a/compendium/CMakeLists.txt b/compendium/CMakeLists.txt
index a571331..d26ac7e 100644
--- a/compendium/CMakeLists.txt
+++ b/compendium/CMakeLists.txt
@@ -1,3 +1,4 @@
+find_package(Boost COMPONENTS asio CONFIG REQUIRED)
 if(US_BUILD_TESTING)
   add_subdirectory(test_bundles)
 endif()
diff --git a/compendium/ConfigurationAdmin/src/CMAsyncWorkService.cpp b/compendium/ConfigurationAdmin/src/CMAsyncWorkService.cpp
index 50f02e6..fdfa45d 100644
--- a/compendium/ConfigurationAdmin/src/CMAsyncWorkService.cpp
+++ b/compendium/ConfigurationAdmin/src/CMAsyncWorkService.cpp
@@ -22,10 +22,10 @@
 
 #include "CMAsyncWorkService.hpp"
 
-#include "boost/asio/async_result.hpp"
-#include "boost/asio/packaged_task.hpp"
-#include "boost/asio/post.hpp"
-#include "boost/asio/thread_pool.hpp"
+#include <boost/asio/async_result.hpp>
+#include <boost/asio/packaged_task.hpp>
+#include <boost/asio/post.hpp>
+#include <boost/asio/thread_pool.hpp>
 
 namespace cppmicroservices
 {
diff --git a/compendium/ConfigurationAdmin/src/CMakeLists.txt b/compendium/ConfigurationAdmin/src/CMakeLists.txt
index a079c20..2657610 100644
--- a/compendium/ConfigurationAdmin/src/CMakeLists.txt
+++ b/compendium/ConfigurationAdmin/src/CMakeLists.txt
@@ -28,7 +28,7 @@ set(_private_headers
   )
 
 add_library(ConfigurationAdminObjs OBJECT ${_srcs} ${_private_headers})
-
+target_link_libraries(ConfigurationAdminObjs PULBIC Boost::asio)
 if(CMAKE_SYSTEM_PROCESSOR STREQUAL "x86_64")
   get_property(_compile_flags TARGET ConfigurationAdminObjs PROPERTY COMPILE_FLAGS)
   set_property(TARGET ConfigurationAdminObjs PROPERTY COMPILE_FLAGS "${_compile_flags} -fPIC")
diff --git a/compendium/DeclarativeServices/CMakeLists.txt b/compendium/DeclarativeServices/CMakeLists.txt
index 2920189..810b00b 100755
--- a/compendium/DeclarativeServices/CMakeLists.txt
+++ b/compendium/DeclarativeServices/CMakeLists.txt
@@ -37,14 +37,14 @@ endif()
 add_compile_definitions(BOOST_DATE_TIME_NO_LIB)
 add_compile_definitions(BOOST_REGEX_NO_LIB)
 
-
+find_package(Boost COMPONENTS asio CONFIG REQUIRED)
 usMacroCreateBundle(DeclarativeServices
   VERSION "1.5.13"
   DEPENDS Framework
   TARGET DeclarativeServices
   SYMBOLIC_NAME declarative_services
   EMBED_RESOURCE_METHOD LINK
-  LINK_LIBRARIES ${_link_libraries} usServiceComponent usAsyncWorkService
+  LINK_LIBRARIES ${_link_libraries} usServiceComponent usAsyncWorkService Boost::asio
   PRIVATE_HEADERS ${_ds_private_headers}
   SOURCES $<TARGET_OBJECTS:DeclarativeServicesObjs> src/SCRActivator.cpp
   BINARY_RESOURCES manifest.json
diff --git a/compendium/DeclarativeServices/src/CMakeLists.txt b/compendium/DeclarativeServices/src/CMakeLists.txt
index 726d402..4e075d5 100644
--- a/compendium/DeclarativeServices/src/CMakeLists.txt
+++ b/compendium/DeclarativeServices/src/CMakeLists.txt
@@ -80,9 +80,9 @@ set(_private_headers
   metadata/ServiceMetadata.hpp
   metadata/Util.hpp
   )
-  
+find_package(Boost COMPONENTS asio CONFIG REQUIRED)
 add_library(DeclarativeServicesObjs OBJECT ${_srcs} ${_private_headers})
-
+target_link_libraries(DeclarativeServicesObjs PUBLIC Boost::asio)
 if(CMAKE_SYSTEM_PROCESSOR STREQUAL "x86_64")
   get_property(_compile_flags TARGET DeclarativeServicesObjs PROPERTY COMPILE_FLAGS)
   set_property(TARGET DeclarativeServicesObjs PROPERTY COMPILE_FLAGS "${_compile_flags} -fPIC")
diff --git a/compendium/DeclarativeServices/src/SCRAsyncWorkService.cpp b/compendium/DeclarativeServices/src/SCRAsyncWorkService.cpp
index ffc93e9..1b9af7b 100644
--- a/compendium/DeclarativeServices/src/SCRAsyncWorkService.cpp
+++ b/compendium/DeclarativeServices/src/SCRAsyncWorkService.cpp
@@ -22,10 +22,10 @@
 
 #include "SCRAsyncWorkService.hpp"
 
-#include "boost/asio/async_result.hpp"
-#include "boost/asio/packaged_task.hpp"
-#include "boost/asio/post.hpp"
-#include "boost/asio/thread_pool.hpp"
+#include <boost/asio/async_result.hpp>
+#include <boost/asio/packaged_task.hpp>
+#include <boost/asio/post.hpp>
+#include <boost/asio/thread_pool.hpp>
 
 namespace cppmicroservices
 {
diff --git a/compendium/DeclarativeServices/src/manager/ComponentConfigurationImpl.cpp b/compendium/DeclarativeServices/src/manager/ComponentConfigurationImpl.cpp
index c4483a9..84d4217 100644
--- a/compendium/DeclarativeServices/src/manager/ComponentConfigurationImpl.cpp
+++ b/compendium/DeclarativeServices/src/manager/ComponentConfigurationImpl.cpp
@@ -30,7 +30,7 @@
 #include "ReferenceManager.hpp"
 #include "ReferenceManagerImpl.hpp"
 #include "RegistrationManager.hpp"
-#include "boost/asio/post.hpp"
+#include <boost/asio/post.hpp>
 #include "cppmicroservices/servicecomponent/ComponentConstants.hpp"
 #include "states/CCUnsatisfiedReferenceState.hpp"
 #include "states/ComponentConfigurationState.hpp"
diff --git a/tools/rc/CMakeLists.txt b/tools/rc/CMakeLists.txt
index c4a36f8..8844292 100755
--- a/tools/rc/CMakeLists.txt
+++ b/tools/rc/CMakeLists.txt
@@ -20,8 +20,8 @@ if(WIN32)
     target_link_libraries(${US_RCC_EXECUTABLE_TARGET} Shlwapi)
 endif()
 
-target_link_libraries(${US_RCC_EXECUTABLE_TARGET} nowide::nowide)
-target_include_directories(${US_RCC_EXECUTABLE_TARGET} PRIVATE ${CppMicroServices_SOURCE_DIR}/third_party/boost/nowide/include)
+find_package(Boost COMPONENTS nowide CONFIG REQUIRED)
+target_link_libraries(${US_RCC_EXECUTABLE_TARGET} Boost::nowide)
 
 set_property(TARGET ${US_RCC_EXECUTABLE_TARGET} APPEND PROPERTY
              COMPILE_DEFINITIONS "MINIZ_NO_ARCHIVE_READING_API;MINIZ_NO_ZLIB_COMPATIBLE_NAMES")
diff --git a/tools/rc/ResourceCompiler.cpp b/tools/rc/ResourceCompiler.cpp
index a5b81d3..4e92db4 100755
--- a/tools/rc/ResourceCompiler.cpp
+++ b/tools/rc/ResourceCompiler.cpp
@@ -37,9 +37,12 @@
 #include <utility>
 #include <vector>
 
-#include <nowide/args.hpp>
-#include <nowide/fstream.hpp>
+#include <boost/nowide/args.hpp>
+#include <boost/nowide/fstream.hpp>
 
+namespace nowide {
+ using namespace boost::nowide;
+}
 #include "optionparser.h"
 #include "json/json.h"
 
