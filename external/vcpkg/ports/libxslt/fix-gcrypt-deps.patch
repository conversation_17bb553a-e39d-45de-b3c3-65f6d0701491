diff --git a/CMakeLists.txt b/CMakeLists.txt
index fb352475..4113fbff 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -319,7 +319,9 @@ target_include_directories(
 
 if(LIBXSLT_WITH_CRYPTO AND NOT WIN32)
 	target_link_libraries(LibExslt PRIVATE Gcrypt::Gcrypt)
-	set(LIBGCRYPT_LIBS "-lgcrypt")
+	# For libexslt.pc
+	set(LIBGCRYPT_LIBS "")
+	string(APPEND EXSLT_PRIVATE_REQUIRES " libgcrypt")
 endif()
 
 if(UNIX)
diff --git a/FindGcrypt.cmake b/FindGcrypt.cmake
index 781113d5..6f680beb 100644
--- a/FindGcrypt.cmake
+++ b/FindGcrypt.cmake
@@ -1,3 +1,20 @@
+cmake_policy(PUSH)
+cmake_policy(SET CMP0012 NEW)
+cmake_policy(SET CMP0057 NEW)
+find_package(PkgConfig)
+if("REQUIRED" IN_LIST ${CMAKE_FIND_PACKAGE_NAME}_FIND_REQUIRED)
+	pkg_check_modules(libxslt_gcrypt REQUIRED IMPORTED_TARGET libgcrypt)
+else()
+	pkg_check_modules(libxslt_gcrypt IMPORTED_TARGET libgcrypt)
+endif()
+set(Gcrypt_FOUND "${libxslt_gcrypt_FOUND}") # CMake standard, needed for find_dependency
+set(GCRYPT_FOUND "${libxslt_gcrypt_FOUND}") # libxslt usage
+if(libxslt_gcrypt_FOUND AND NOT TARGET Gcrypt::Gcrypt)
+	add_library(Gcrypt::Gcrypt INTERFACE IMPORTED)
+	set_target_properties(Gcrypt::Gcrypt PROPERTIES INTERFACE_LINK_LIBRARIES PkgConfig::libxslt_gcrypt)
+endif()
+cmake_policy(POP)
+if(0)
 include(FindPackageHandleStandardArgs)
 include(SelectLibraryConfigurations)
 
@@ -38,3 +55,4 @@ if(GCRYPT_FOUND AND NOT TARGET Gcrypt::Gcrypt)
 		INTERFACE_INCLUDE_DIRECTORIES "${GCRYPT_INCLUDE_DIRS}"
 	)
 endif()
+endif()
