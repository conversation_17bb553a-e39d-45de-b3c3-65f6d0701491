{"name": "libxslt", "version": "1.1.42", "port-version": 2, "description": "Libxslt is a XSLT library implemented in C for XSLT 1.0 and most of EXSLT", "homepage": "https://github.com/GNOME/libxslt", "license": null, "supports": "!uwp", "dependencies": [{"name": "libxml2", "default-features": false}, {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "default-features": ["profiler", "thread"], "features": {"crypto": {"description": "Build with crypto support", "supports": "!windows", "dependencies": ["libgcrypt"]}, "plugins": {"description": "(deprecated)", "supports": "!static"}, "profiler": {"description": "Build with profiling support"}, "python": {"description": "Builds with python support", "supports": "!windows", "dependencies": [{"name": "libxslt", "default-features": false, "features": ["profiler"]}]}, "thread": {"description": "Enable multi-threading support"}, "tools": {"description": "Build the utilities"}}}