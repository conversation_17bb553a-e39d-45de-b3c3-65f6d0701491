diff --git a/CMakeLists.txt b/CMakeLists.txt
index fb352475..75bd44a5 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -63,6 +63,11 @@ if(LIBXSLT_WITH_PYTHON)
 	check_symbol_exists(F_GETFL fcntl.h HAVE_F_GETFL)
 	if(WIN32 OR (HAVE_UNISTD_H AND HAVE_F_GETFL))
 		find_package(Python COMPONENTS Interpreter Development REQUIRED)
+	elseif(1)
+		find_package(Python3 COMPONENTS Interpreter Development REQUIRED)
+		add_library(Python::Python ALIAS Python3::Python)
+		set(Python_EXECUTABLE ${Python3_EXECUTABLE})
+		set(Python_SITEARCH ${Python3_SITEARCH})
 	else()
 		find_package(Python2 COMPONENTS Interpreter Development REQUIRED)
 		add_library(Python::Python ALIAS Python2::Python)
