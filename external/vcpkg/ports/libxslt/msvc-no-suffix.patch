diff --git a/CMakeLists.txt b/CMakeLists.txt
index fb352475..a4bb094e 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -247,7 +247,7 @@ set_target_properties(
 	SOVERSION ${LIBXSLT_MAJOR_VERSION}
 )
 
-if(MSVC)
+if(0) # Never add suffixes which are not added by the autotools build or the nmake makefiles
 	if(BUILD_SHARED_LIBS)
 		set_target_properties(
 			LibXslt
@@ -340,7 +340,7 @@ set_target_properties(
 	SOVERSION ${LIBEXSLT_MAJOR_VERSION}
 )
 
-if(MSVC)
+if(0) # same reason as above
 	if(BUILD_SHARED_LIBS)
 		set_target_properties(
 			LibExslt
