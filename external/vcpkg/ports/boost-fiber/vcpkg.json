{"$comment": "Automatically generated by scripts/boost/generate-ports.ps1", "name": "boost-fiber", "version": "1.87.0", "description": "Boost fiber module", "homepage": "https://www.boost.org/libs/fiber", "license": "BSL-1.0", "supports": "!uwp & !(arm & windows) & !emscripten", "dependencies": [{"name": "boost-algorithm", "version>=": "1.87.0"}, {"name": "boost-assert", "version>=": "1.87.0"}, {"name": "boost-cmake", "version>=": "1.87.0"}, {"name": "boost-config", "version>=": "1.87.0"}, {"name": "boost-context", "platform": "!uwp & !emscripten", "version>=": "1.87.0"}, {"name": "boost-core", "version>=": "1.87.0"}, {"name": "boost-filesystem", "platform": "!uwp", "version>=": "1.87.0"}, {"name": "boost-format", "version>=": "1.87.0"}, {"name": "boost-headers", "version>=": "1.87.0"}, {"name": "boost-intrusive", "version>=": "1.87.0"}, {"name": "boost-predef", "version>=": "1.87.0"}, {"name": "boost-smart-ptr", "version>=": "1.87.0"}], "features": {"numa": {"description": "Enable NUMA support"}}}