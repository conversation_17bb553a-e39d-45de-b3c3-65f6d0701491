--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -26,6 +26,7 @@
 cmake_minimum_required(VERSION 2.8.7)
 set(CMAKE_MACOSX_RPATH 1)
 include(${CMAKE_ROOT}/Modules/ExternalProject.cmake)
+include(${CMAKE_ROOT}/Modules/GenerateExportHeader.cmake)
 
 
 ###
@@ -153,6 +154,8 @@ IF (SELECT_TIMEOUT)
   set_target_properties(${PROJECT} PROPERTIES COMPILE_DEFINITIONS "__TACOPIE_TIMEOUT=${SELECT_TIMEOUT}")
 ENDIF(SELECT_TIMEOUT)
 
+generate_export_header(${PROJECT} EXPORT_FILE_NAME ${CMAKE_BINARY_DIR}/tacopie/utils/${PROJECT}_export.hpp)
+target_include_directories(${PROJECT} PUBLIC ${CMAKE_BINARY_DIR})
 
 ###
 # install
@@ -164,6 +167,7 @@ install(DIRECTORY DESTINATION ${CMAKE_BINARY_DIR}/bin/)
 install (DIRECTORY ${CMAKE_BINARY_DIR}/lib/ DESTINATION lib USE_SOURCE_PERMISSIONS)
 install (DIRECTORY ${CMAKE_BINARY_DIR}/bin/ DESTINATION bin USE_SOURCE_PERMISSIONS)
 install (DIRECTORY ${TACOPIE_INCLUDES}/ DESTINATION include USE_SOURCE_PERMISSIONS)
+install (FILES ${CMAKE_BINARY_DIR}/tacopie/utils/${PROJECT}_export.hpp DESTINATION include/tacopie/utils)
 
 
 ###
