diff --git a/kubernetes/CMakeLists.txt b/kubernetes/CMakeLists.txt
--- a/kubernetes/CMakeLists.txt	(revision 80aff0a1c71c2cb8a1ab4b73e0592f0f88c59376)
+++ b/kubernetes/CMakeLists.txt	(date 1642600807646)
@@ -1255,7 +1255,7 @@
     install(TARGETS ${pkgName} DESTINATION lib)
 else()
     include(GNUInstallDirs)
-    install(TARGETS ${pkgName} DESTINATION lib EXPORT ${pkgName}Targets)
+    install(TARGETS ${pkgName} EXPORT ${pkgName}Targets)
 
     foreach(HDR_FILE ${HDRS})
         get_filename_component(HDR_DIRECTORY ${HDR_FILE} DIRECTORY)
