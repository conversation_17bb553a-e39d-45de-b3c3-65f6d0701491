diff --git a/CMakeLists.txt b/CMakeLists.txt
index 8a400b8..6bc5d6c 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -261,7 +261,8 @@ endif ()
 option(CGNS_ENABLE_HDF5 "Enable or disable HDF5 interface" "ON")
 if (CGNS_ENABLE_HDF5)
 
-  if (CGNS_BUILD_SHARED)
+  find_package(hdf5 CONFIG REQUIRED)
+  if (TARGET hdf5::hdf5-shared)
     set (CG_HDF5_LINK_TYPE "shared")
     set (CG_HDF5_LINK_TYPE_UPPER "SHARED")
   else ()
diff --git a/src/CMakeLists.txt b/src/CMakeLists.txt
index f21eecb..fc3ad34 100644
--- a/src/CMakeLists.txt
+++ b/src/CMakeLists.txt
@@ -597,7 +597,7 @@ add_library(cgns_static STATIC ${cgns_FILES} $<$<BOOL:${CGNS_ENABLE_FORTRAN}>:$<
 add_library(CGNS::cgns-static ALIAS cgns_static)
 # Needed to work around a CMake > 3.8 bug on Windows with MSVS and Intel Fortran
 set_property(TARGET cgns_static PROPERTY LINKER_LANGUAGE C)
-target_link_libraries(cgns_static PRIVATE $<$<BOOL:${CGNS_ENABLE_HDF5}>:hdf5-${CG_HDF5_LINK_TYPE}>)
+target_link_libraries(cgns_static PRIVATE $<$<BOOL:${CGNS_ENABLE_HDF5}>:${HDF5_LIBRARY}>)
 
 # Build a shared version of the library
 if(CGNS_BUILD_SHARED)
@@ -614,7 +614,7 @@ if(CGNS_BUILD_SHARED)
     target_compile_definitions(cgns_shared INTERFACE -DUSE_DLL)
   endif ()
   if (CGNS_ENABLE_HDF5 AND HDF5_LIBRARY)
-    target_link_libraries(cgns_shared PUBLIC hdf5-${CG_HDF5_LINK_TYPE} $<$<NOT:$<PLATFORM_ID:Windows>>:${CMAKE_DL_LIBS}>)
+    target_link_libraries(cgns_shared PUBLIC ${HDF5_LIBRARY} $<$<NOT:$<PLATFORM_ID:Windows>>:${CMAKE_DL_LIBS}>)
     if(HDF5_NEED_ZLIB AND ZLIB_LIBRARY)
       target_link_libraries(cgns_shared PUBLIC ${ZLIB_LIBRARY})
     endif()
diff --git a/src/cgns-config.cmake.in b/src/cgns-config.cmake.in
index 12d9b17..23f2d99 100644
--- a/src/cgns-config.cmake.in
+++ b/src/cgns-config.cmake.in
@@ -1,5 +1,10 @@
 @PACKAGE_INIT@
 
+if("@CGNS_ENABLE_HDF5@")
+    include(CMakeFindDependencyMacro)
+    find_dependency(hdf5 CONFIG)
+endif()
+
 include("${CMAKE_CURRENT_LIST_DIR}/cgns-targets.cmake")
 
 check_required_components(cgns)
