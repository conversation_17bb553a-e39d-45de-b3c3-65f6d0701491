{"name": "cgns", "version-semver": "4.5.0", "description": "The CFD General Notation System (CGNS) provides a standard for recording and recovering computer data associated with the numerical solution of fluid dynamics equations.", "homepage": "http://cgns.org/", "license": "<PERSON><PERSON><PERSON>", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "default-features": ["hdf5", {"name": "lfs", "platform": "!osx"}], "features": {"fortran": {"description": "Enable fortran support (not yet implemented)"}, "hdf5": {"description": "Enable hdf5 support", "dependencies": [{"name": "hdf5", "default-features": false}]}, "legacy": {"description": "Enable legacy support"}, "lfs": {"description": "Enable LFS support"}, "mpi": {"description": "Enable MPI support", "dependencies": [{"name": "cgns", "default-features": false, "features": ["hdf5"]}, {"name": "hdf5", "default-features": false, "features": ["parallel"]}]}, "tests": {"description": "Build tests"}}}