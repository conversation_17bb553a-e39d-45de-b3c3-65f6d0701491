diff --git a/cmake/headers.cmake b/cmake/headers.cmake
index 3924a2a..f1e32d9 100644
--- a/cmake/headers.cmake
+++ b/cmake/headers.cmake
@@ -15,8 +15,6 @@ set(quickcpplib_HEADERS
   "include/quickcpplib/boost/test/unit_test.hpp"
   "include/quickcpplib/byte.hpp"
   "include/quickcpplib/byte/include/nonstd/byte.hpp"
-  "include/quickcpplib/byte/test/byte-main.t.hpp"
-  "include/quickcpplib/byte/test/lest/lest_cpp03.hpp"
   "include/quickcpplib/config.hpp"
   "include/quickcpplib/console_colours.hpp"
   "include/quickcpplib/cpp_feature.h"
@@ -40,11 +38,7 @@ set(quickcpplib_HEADERS
   "include/quickcpplib/ringbuffer_log.hpp"
   "include/quickcpplib/scope.hpp"
   "include/quickcpplib/signal_guard.hpp"
-  "include/quickcpplib/span-lite/example/nonstd/span.tweak.hpp"
   "include/quickcpplib/span-lite/include/nonstd/span.hpp"
-  "include/quickcpplib/span-lite/test/lest/lest_cpp03.hpp"
-  "include/quickcpplib/span-lite/test/nonstd/span.tweak.hpp"
-  "include/quickcpplib/span-lite/test/span-main.t.hpp"
   "include/quickcpplib/span.hpp"
   "include/quickcpplib/spinlock.hpp"
   "include/quickcpplib/spinlock.natvis"
