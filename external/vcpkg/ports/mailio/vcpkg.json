{"name": "mailio", "version": "0.24.0", "maintainers": "<PERSON><PERSON> <<EMAIL>>", "description": "mailio is a cross platform C++ library for MIME format and SMTP, POP3 and IMAP protocols. It is based on the standard C++ 17 and Boost library.", "homepage": "https://github.com/karastojko/mailio", "license": "BSD-2-<PERSON><PERSON>", "supports": "!uwp", "dependencies": ["boost-asio", "boost-date-time", "boost-regex", "boost-system", "openssl", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}]}