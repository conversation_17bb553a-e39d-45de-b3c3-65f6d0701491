{"$comment": "Keep the platform expressions in sync with the wrappers installed by the portfiles!", "name": "blas", "version-date": "2023-04-14", "port-version": 3, "description": "Metapackage for packages which provide BLAS", "license": null, "dependencies": [{"name": "lapack-reference", "features": ["cblas"], "platform": "!osx & !ios & !uwp & !(windows & arm) & windows & static & !mingw"}, {"name": "openblas", "platform": "!osx & !ios & (uwp | (windows & arm) | !windows | !static | mingw)"}, {"name": "vcpkg-cmake", "host": true}]}