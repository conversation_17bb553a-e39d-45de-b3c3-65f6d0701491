diff --git a/CMakeLists.txt b/CMakeLists.txt
index 08330d9..a58d669 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -5,10 +5,16 @@ project(rabit VERSION 0.0.0)
 option(RABIT_BUILD_TESTS "Build rabit tests" OFF)
 option(RABIT_BUILD_MPI "Build MPI" OFF)
 
+find_package(dmlc CONFIG REQUIRED)
+
 add_library(rabit src/allreduce_base.cc src/allreduce_robust.cc src/engine.cc src/c_api.cc)
 add_library(rabit_base src/allreduce_base.cc src/engine_base.cc src/c_api.cc)
 add_library(rabit_empty src/engine_empty.cc src/c_api.cc)
 
+target_link_libraries(rabit dmlc::dmlc)
+target_link_libraries(rabit_base dmlc::dmlc)
+target_link_libraries(rabit_empty dmlc::dmlc)
+
 set(rabit_libs rabit rabit_base rabit_empty)
 if(RABIT_BUILD_MPI)
   find_package(MPI REQUIRED)
@@ -22,7 +28,6 @@ if(RABIT_BUILD_TESTS)
   add_library(rabit_mock STATIC src/allreduce_base.cc src/allreduce_robust.cc src/engine_mock.cc src/c_api.cc)
   list(APPEND rabit_libs rabit_mock) # add to list to apply build settings, then remove
 endif()
-
 foreach(lib ${rabit_libs})
   #include "./internal/utils.h"  
   target_include_directories(${lib} PUBLIC
@@ -42,7 +47,8 @@ if(RABIT_BUILD_TESTS)
   if(RABIT_BUILD_MPI)
     add_executable(speed_test_mpi test/speed_test.cc)
     target_link_libraries(speed_test_mpi rabit_mpi)
-    install(TARGETS speed_test_mpi DESTINATION bin)    
+    install(TARGETS speed_test_mpi DESTINATION bin) 
+    message("IN MPI ")    
   endif()
 endif()
 
diff --git a/include/rabit/serializable.h b/include/rabit/serializable.h
index 4a3c2a1..0fd4154 100644
--- a/include/rabit/serializable.h
+++ b/include/rabit/serializable.h
@@ -9,7 +9,7 @@
 #include <vector>
 #include <string>
 #include "./internal/utils.h"
-#include "../dmlc/io.h"
+#include <dmlc/io.h>
 
 namespace rabit {
 /*!
