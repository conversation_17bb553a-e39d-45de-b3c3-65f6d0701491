diff --git a/CMakeLists.txt b/CMakeLists.txt
index 1e13def..eda8d68 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -228,8 +228,9 @@ ENDIF(DOXYGEN_FOUND)
 # Configuration file
 CONFIGURE_FILE(
   "${PROJECT_SOURCE_DIR}/include/tins/config.h.in"
-  "${PROJECT_SOURCE_DIR}/include/tins/config.h"
+  "${PROJECT_BINARY_DIR}/include/tins/config.h"
 )
+INCLUDE_DIRECTORIES("${CMAKE_CURRENT_BINARY_DIR}/include")
 
 IF (NOT CMAKE_INSTALL_LIBDIR)
     SET(CMAKE_INSTALL_LIBDIR lib)
diff --git a/src/CMakeLists.txt b/src/CMakeLists.txt
index e07772e..94bc8bf 100644
--- a/src/CMakeLists.txt
+++ b/src/CMakeLists.txt
@@ -82,7 +82,7 @@ set(HEADERS
     ${LIBTINS_INCLUDE_DIR}/tins/handshake_capturer.h
     ${LIBTINS_INCLUDE_DIR}/tins/stp.h
     ${LIBTINS_INCLUDE_DIR}/tins/pppoe.h
-    ${LIBTINS_INCLUDE_DIR}/tins/config.h
+    ${PROJECT_BINARY_DIR}/include/tins/config.h
     ${LIBTINS_INCLUDE_DIR}/tins/constants.h
     ${LIBTINS_INCLUDE_DIR}/tins/crypto.h
     ${LIBTINS_INCLUDE_DIR}/tins/cxxstd.h
@@ -229,6 +229,7 @@ MACRO(INSTALL_HEADERS_WITH_DIRECTORY HEADER_LIST)
     FOREACH(HEADER ${HEADERS})
         # Extract directory name and remove leading '../'
         get_filename_component(DIR ${HEADER} PATH)
+        string(REPLACE "${PROJECT_BINARY_DIR}/" "" DIR ${DIR})
         STRING(REGEX REPLACE "^\\.\\.\\/" "" DIR ${DIR})
         INSTALL(FILES ${HEADER} DESTINATION ${DIR})
     ENDFOREACH(HEADER)
