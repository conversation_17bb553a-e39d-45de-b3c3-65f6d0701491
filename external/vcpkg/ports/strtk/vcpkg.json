{"name": "strtk", "version-date": "2020-09-14", "port-version": 4, "description": "robust, optimized and portable string processing algorithms for the C++ language", "homepage": "https://github.com/ArashPartow/strtk", "license": null, "features": {"boost": {"description": "Request boost libraries", "dependencies": ["boost-algorithm", "boost-lexical-cast", "boost-random", "boost-regex", "boost-spirit", "boost-tokenizer"]}}}