{"name": "mozjpeg", "version": "4.1.5", "port-version": 1, "description": "MozJPEG reduces file sizes of JPEG images while retaining quality and compatibility with the vast majority of the world's deployed decoders. It's compatible with libjpeg API and ABI, and can be used as a drop-in replacement for libjpeg.", "homepage": "https://github.com/mozilla/mozjpeg", "license": "IJG AND BSD-3-Clause AND Zlib", "dependencies": ["libpng", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}]}