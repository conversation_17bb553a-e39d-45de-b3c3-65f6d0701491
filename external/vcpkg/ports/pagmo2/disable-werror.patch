diff --git a/cmake_modules/yacma/YACMACompilerLinkerSettings.cmake b/cmake_modules/yacma/YACMACompilerLinkerSettings.cmake
index 7d7aa1b..81c8bf6 100644
--- a/cmake_modules/yacma/YACMACompilerLinkerSettings.cmake
+++ b/cmake_modules/yacma/YACMACompilerLinkerSettings.cmake
@@ -95,7 +95,6 @@ if(NOT _YACMACompilerLinkerSettingsRun)
         # NOTE: enable unconditionally, as it seems like the CMake
         # machinery for detecting this fails. Perhaps the source code
         # used for checking the flag emits warnings?
-        list(APPEND _YACMA_CXX_FLAGS_DEBUG "-Werror")
         # New warnings in clang 8.
         # NOTE: a few issues with macros here, let's disable for now.
         # _YACMA_CHECK_ENABLE_DEBUG_CXX_FLAG(-Wextra-semi-stmt)
@@ -180,7 +179,6 @@ if(NOT _YACMACompilerLinkerSettingsRun)
         # Enable higher warning level than usual.
         _YACMA_CHECK_ENABLE_DEBUG_CXX_FLAG(/W4)
         # Treat warnings as errors.
-        _YACMA_CHECK_ENABLE_DEBUG_CXX_FLAG(/WX)
     endif()
 
     # Set the cache variables.
