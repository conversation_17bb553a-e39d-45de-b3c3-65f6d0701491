diff --git a/src/common/mathutil.h b/src/common/mathutil.h
index 1d73bbf..c5b9cc8 100644
--- a/src/common/mathutil.h
+++ b/src/common/mathutil.h
@@ -1073,7 +1073,7 @@ inline int BitCount(uint64_t bits)
 #    endif  // defined(_M_IX86) || defined(_M_X64)
 #endif      // defined(_MSC_VER) && !defined(__clang__)
 
-#if defined(ANGLE_PLATFORM_POSIX) || defined(__clang__)
+#if defined(ANGLE_PLATFORM_POSIX) || defined(__clang__) || defined(__MINGW32__)
 inline int BitCount(uint32_t bits)
 {
     return __builtin_popcount(bits);
@@ -1083,7 +1083,7 @@ inline int BitCount(uint64_t bits)
 {
     return __builtin_popcountll(bits);
 }
-#endif  // defined(ANGLE_PLATFORM_POSIX) || defined(__clang__)
+#endif  // defined(ANGLE_PLATFORM_POSIX) || defined(__clang__) || defined(__MINGW32__)
 
 inline int BitCount(uint8_t bits)
 {
