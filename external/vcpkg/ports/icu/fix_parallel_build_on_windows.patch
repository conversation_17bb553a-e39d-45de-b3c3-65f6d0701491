diff --urN a/source/data/Makefile.in b/source/data/Makefile.in
--- a/source/data/Makefile.in
+++ b/source/data/Makefile.in
@@ -236,11 +236,12 @@
 ## Include the main build rules for data files
 include $(top_builddir)/$(subdir)/rules.mk
 
+PKGDATA_LIST = $(TMP_DIR)/icudata.lst
 
 ifeq ($(ENABLE_SO_VERSION_DATA),1)
 ifeq ($(PKGDATA_MODE),dll)
 SO_VERSION_DATA = $(OUTTMPDIR)/icudata.res
-$(SO_VERSION_DATA) : $(MISCSRCDIR)/icudata.rc | $(TMP_DIR)/dirs.timestamp
+$(SO_VERSION_DATA) : $(MISCSRCDIR)/icudata.rc $(PKGDATA_LIST)
 ifeq ($(MSYS_RC_MODE),1)
 	rc.exe -i$(srcdir)/../common -i$(top_builddir)/common -fo$@ $(CPPFLAGS) $<
 else
@@ -249,7 +250,6 @@
 endif
 endif
 
-PKGDATA_LIST = $(TMP_DIR)/icudata.lst
 
 
 #####################################################
