diff --git a/source/config/mh-msys-msvc b/source/config/mh-msys-msvc
index 6f50798ebf..2f1a2d6e69 100644
--- a/source/config/mh-msys-msvc
+++ b/source/config/mh-msys-msvc
@@ -90,10 +90,10 @@ LIBSICU = $(STATIC_PREFIX)$(ICUPREFIX)
 A = lib
 
 # Cygwin's ar can't handle Win64 right now. So we use Microsoft's tool instead.
-AR = LIB.EXE#M#
-ARFLAGS := -nologo $(ARFLAGS:r=)#M#
+AR := $(AR)#M#
+ARFLAGS := $(ARFLAGS)#M#
 RANLIB = ls -s#M#
 AR_OUTOPT = -OUT:#M#
 
 ## An import library is needed for z-OS, MSVC and Cygwin
 IMPORT_LIB_EXT = .lib
