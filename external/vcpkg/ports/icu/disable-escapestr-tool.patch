diff --git a/source/tools/Makefile.in b/source/tools/Makefile.in
index e0896f1..5ead980 100644
--- a/source/tools/Makefile.in
+++ b/source/tools/Makefile.in
@@ -19,9 +19,9 @@ SUBDIRS = toolutil ctestfw makeconv genrb genbrk \
 gencnval gensprep icuinfo genccode gencmn icupkg pkgdata \
 gentest gennorm2 gencfu gendict icuexportdata
 
-ifneq (@platform_make_fragment_name@,mh-cygwin-msvc)
-SUBDIRS += escapesrc
-endif
+#ifneq (@platform_make_fragment_name@,mh-cygwin-msvc)
+#SUBDIRS += escapesrc
+#endif
 
 ## List of phony targets
 .PHONY : all all-local all-recursive install install-local	\
