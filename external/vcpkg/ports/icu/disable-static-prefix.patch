diff --git a/source/icudefs.mk.in b/source/icudefs.mk.in
index 24bd97a..33169de 100644
--- a/source/icudefs.mk.in
+++ b/source/icudefs.mk.in
@@ -213,13 +213,13 @@ LIBICU = $(LIBPREFIX)$(ICUPREFIX)
 
 ## If we can't use the shared libraries, use the static libraries
 ifneq ($(ENABLE_SHARED),YES)
-STATIC_PREFIX_WHEN_USED = s
+STATIC_PREFIX_WHEN_USED =
 else
 STATIC_PREFIX_WHEN_USED = 
 endif
 
 # Static library prefix and file extension
-STATIC_PREFIX = s
+STATIC_PREFIX =
 LIBSICU = $(LIBPREFIX)$(STATIC_PREFIX)$(ICUPREFIX)
 A = a
 SOBJ = $(SO)
