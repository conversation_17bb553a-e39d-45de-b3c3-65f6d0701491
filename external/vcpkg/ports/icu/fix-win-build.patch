diff --git a/source/configure.ac b/source/configure.ac
index 425fdc7..97210f8 100644
--- a/source/configure.ac
+++ b/source/configure.ac
@@ -114,6 +114,15 @@ AC_ARG_ENABLE([icu-config],
         esac], [enable_icu_config=true])
 AC_SUBST(INSTALL_ICU_CONFIG, [$enable_icu_config])
 
+AC_ARG_ENABLE([icu-build-win],
+    AS_HELP_STRING([--enable-icu-build-win], [install icu-build-win]),
+        [case "${enableval}" in
+            yes) enable_icu_build_win=true ;;
+            no) enable_icu_build_win=false ;;
+            *) AC_MSG_ERROR([bad value '${enableval}' for --enable-icu-build-win]) ;;
+        esac], [enable_icu_build_win=true])
+AC_SUBST(INSTALL_ICU_BUILD_WIN, [$enable_icu_build_win])
+
 # Check whether to build debug libraries
 AC_MSG_CHECKING([whether to build debug libraries])
 enabled=no
@@ -263,12 +272,14 @@ ICU_CHECK_MH_FRAG
 # Checks for libraries and other host specific stuff
 # On HP/UX, don't link to -lm from a shared lib because it isn't
 #  PIC (at least on 10.2)
+if test "$enable_icu_build_win" = no; then
 case "${host}" in
 	*-*-hpux*) 	AC_CHECK_LIB(m, floor, LIB_M="-lm") ;;
 
 	*) 		AC_CHECK_LIB(m, floor)
 			LIB_M="" ;;
 esac
+fi
 AC_SUBST(LIB_M)
 
 # Check whether to build shared libraries
