{"name": "irr<PERSON>t", "version": "1.8.5", "description": "The Irrlicht Engine is an open source realtime 3D engine written in C++. It is cross-platform, using D3D, OpenGL and its own software renderers.", "homepage": "http://irrlicht.sourceforge.net", "supports": "!(arm | uwp)", "dependencies": ["bzip2", "libjpeg-turbo", "libpng", "vcpkg-cmake", "vcpkg-cmake-config", "zlib"], "features": {"fast-fpu": {"description": "Enable fast maths (at the expense of precision)"}, "tools": {"description": "Build the Tools FileToHeader, FontTool, GUIEditor and MeshConverter"}, "unicode": {"description": "Support unicode path on windows"}}}