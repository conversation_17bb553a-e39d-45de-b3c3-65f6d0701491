diff --git a/CMakeLists.txt b/CMakeLists.txt
index 7db55ee4f..c41d98f70 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -52,7 +52,7 @@ configure_file(
 
 add_library(verdict ${verdict_SOURCES})
 target_include_directories(verdict PUBLIC
-  $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}> $<BUILD_INTERFACE:${CMAKE_CURRENT_BINARY_DIR}>)
+  $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}> $<BUILD_INTERFACE:${CMAKE_CURRENT_BINARY_DIR}> $<INSTALL_INTERFACE:include>)
 
 
 # Setting the VERSION and SOVERSION of a library will include
