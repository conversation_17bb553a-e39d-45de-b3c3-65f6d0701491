{"name": "libmultisense", "version": "6.1.0", "description": "A C++ library for interfacing with the MultiSense S family of sensors from Carnegie Robotics.", "homepage": "https://github.com/carnegierobotics/LibMultiSense", "supports": "linux | (windows & !static)", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"utilities": {"description": "Build MultiSense utility applications."}}}