Index: CMakeLists.txt
===================================================================
--- CMakeLists.txt	(revision 1767)
+++ CMakeLists.txt	(working copy)
@@ -436,6 +436,12 @@
                        @ONLY)
 ENDIF(PCRE_BUILD_PCRECPP)
 
+# Make sure to not link debug libs
+# against release libs and vice versa
+IF(WIN32)
+  SET(CMAKE_DEBUG_POSTFIX "d")
+ENDIF(WIN32)
+
 # Generate pkg-config files
 SET(PACKAGE_VERSION "${PCRE_MAJOR}.${PCRE_MINOR}")
 SET(prefix "${CMAKE_INSTALL_PREFIX}")
@@ -442,6 +448,9 @@
 SET(exec_prefix "\${prefix}")
 SET(libdir "\${exec_prefix}/${CMAKE_INSTALL_LIBDIR}")
 SET(includedir "\${prefix}/include")
+IF(WIN32 AND (CMAKE_BUILD_TYPE MATCHES Debug))
+  SET(LIB_POSTFIX ${CMAKE_DEBUG_POSTFIX})
+ENDIF()
 IF(NOT BUILD_SHARED_LIBS)
         SET(PCRE_STATIC_CFLAG "-DPCRE_STATIC")
 ENDIF(NOT BUILD_SHARED_LIBS)
@@ -659,11 +668,6 @@
 ENDIF(MSVC)
 
 SET(CMAKE_INCLUDE_CURRENT_DIR 1)
-# needed to make sure to not link debug libs
-# against release libs and vice versa
-IF(WIN32)
-  SET(CMAKE_DEBUG_POSTFIX "d")
-ENDIF(WIN32)
 
 SET(targets)
 
Index: configure.ac
===================================================================
--- configure.ac	(revision 1767)
+++ configure.ac	(working copy)
@@ -1044,6 +1044,9 @@
 AM_CONDITIONAL([WITH_GCOV],[test "x$enable_coverage" = "xyes"])
 
 # Produce these files, in addition to config.h.
+# LIB_POSTFIX is used by CMakeLists.txt for Windows debug builds.
+# Pass empty LIB_POSTFIX to *.pc files and pcre-config here.
+AC_SUBST(LIB_POSTFIX)
 AC_CONFIG_FILES(
 	Makefile
 	libpcre.pc
Index: libpcre.pc.in
===================================================================
--- libpcre.pc.in	(revision 1767)
+++ libpcre.pc.in	(working copy)
@@ -8,6 +8,6 @@
 Name: libpcre
 Description: PCRE - Perl compatible regular expressions C library with 8 bit character support
 Version: @PACKAGE_VERSION@
-Libs: -L${libdir} -lpcre
+Libs: -L${libdir} -lpcre@LIB_POSTFIX@
 Libs.private: @PTHREAD_CFLAGS@ @PTHREAD_LIBS@
 Cflags: -I${includedir} @PCRE_STATIC_CFLAG@
Index: libpcre16.pc.in
===================================================================
--- libpcre16.pc.in	(revision 1767)
+++ libpcre16.pc.in	(working copy)
@@ -8,6 +8,6 @@
 Name: libpcre16
 Description: PCRE - Perl compatible regular expressions C library with 16 bit character support
 Version: @PACKAGE_VERSION@
-Libs: -L${libdir} -lpcre16
+Libs: -L${libdir} -lpcre16@LIB_POSTFIX@
 Libs.private: @PTHREAD_CFLAGS@ @PTHREAD_LIBS@
 Cflags: -I${includedir} @PCRE_STATIC_CFLAG@
Index: libpcre32.pc.in
===================================================================
--- libpcre32.pc.in	(revision 1767)
+++ libpcre32.pc.in	(working copy)
@@ -8,6 +8,6 @@
 Name: libpcre32
 Description: PCRE - Perl compatible regular expressions C library with 32 bit character support
 Version: @PACKAGE_VERSION@
-Libs: -L${libdir} -lpcre32
+Libs: -L${libdir} -lpcre32@LIB_POSTFIX@
 Libs.private: @PTHREAD_CFLAGS@ @PTHREAD_LIBS@
 Cflags: -I${includedir} @PCRE_STATIC_CFLAG@
Index: libpcrecpp.pc.in
===================================================================
--- libpcrecpp.pc.in	(revision 1767)
+++ libpcrecpp.pc.in	(working copy)
@@ -8,5 +8,5 @@
 Name: libpcrecpp
 Description: PCRECPP - C++ wrapper for PCRE
 Version: @PACKAGE_VERSION@
-Libs: -L${libdir} -lpcre -lpcrecpp
+Libs: -L${libdir} -lpcre@LIB_POSTFIX@ -lpcrecpp@LIB_POSTFIX@
 Cflags: -I${includedir} @PCRE_STATIC_CFLAG@
Index: libpcreposix.pc.in
===================================================================
--- libpcreposix.pc.in	(revision 1767)
+++ libpcreposix.pc.in	(working copy)
@@ -8,6 +8,6 @@
 Name: libpcreposix
 Description: PCREPosix - Posix compatible interface to libpcre
 Version: @PACKAGE_VERSION@
-Libs: -L${libdir} -lpcreposix
+Libs: -L${libdir} -lpcreposix@LIB_POSTFIX@
 Cflags: -I${includedir} @PCRE_STATIC_CFLAG@
 Requires.private: libpcre
Index: pcre-config.in
===================================================================
--- pcre-config.in	(revision 1767)
+++ pcre-config.in	(working copy)
@@ -91,7 +91,7 @@
       ;;
     --libs-posix)
       if test @enable_pcre8@ = yes ; then
-        echo $libS$libR -lpcreposix -lpcre
+        echo $libS$libR -lpcreposix@LIB_POSTFIX@ -lpcre@LIB_POSTFIX@
       else
         echo "${usage}" 1>&2
       fi
@@ -98,7 +98,7 @@
       ;;
     --libs)
       if test @enable_pcre8@ = yes ; then
-        echo $libS$libR -lpcre
+        echo $libS$libR -lpcre@LIB_POSTFIX@
       else
         echo "${usage}" 1>&2
       fi
@@ -105,7 +105,7 @@
       ;;
     --libs16)
       if test @enable_pcre16@ = yes ; then
-        echo $libS$libR -lpcre16
+        echo $libS$libR -lpcre16@LIB_POSTFIX@
       else
         echo "${usage}" 1>&2
       fi
@@ -112,7 +112,7 @@
       ;;
     --libs32)
       if test @enable_pcre32@ = yes ; then
-        echo $libS$libR -lpcre32
+        echo $libS$libR -lpcre32@LIB_POSTFIX@
       else
         echo "${usage}" 1>&2
       fi
@@ -119,7 +119,7 @@
       ;;
     --libs-cpp)
       if test @enable_cpp@ = yes ; then
-        echo $libS$libR -lpcrecpp -lpcre
+        echo $libS$libR -lpcrecpp@LIB_POSTFIX@ -lpcre@LIB_POSTFIX@
       else
         echo "${usage}" 1>&2
       fi
