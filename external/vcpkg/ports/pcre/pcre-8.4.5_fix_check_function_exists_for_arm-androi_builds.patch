diff --git a/CMakeLists.txt b/CMakeLists.txt
index 2c3a309..cdd480f 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -94,7 +94,7 @@ FIND_PACKAGE( Editline )
 
 INCLUDE(CheckIncludeFile)
 INCLUDE(CheckIncludeFileCXX)
-INCLUDE(CheckFunctionExists)
+INCLUDE(CheckSymbolExists)
 INCLUDE(CheckTypeSize)
 INCLUDE(GNUInstallDirs) # for CMAKE_INSTALL_LIBDIR
 
@@ -109,12 +109,12 @@ CHECK_INCLUDE_FILE(windows.h    HAVE_WINDOWS_H)
 CHECK_INCLUDE_FILE_CXX(type_traits.h            HAVE_TYPE_TRAITS_H)
 CHECK_INCLUDE_FILE_CXX(bits/type_traits.h       HAVE_BITS_TYPE_TRAITS_H)
 
-CHECK_FUNCTION_EXISTS(bcopy     HAVE_BCOPY)
-CHECK_FUNCTION_EXISTS(memmove   HAVE_MEMMOVE)
-CHECK_FUNCTION_EXISTS(strerror  HAVE_STRERROR)
-CHECK_FUNCTION_EXISTS(strtoll   HAVE_STRTOLL)
-CHECK_FUNCTION_EXISTS(strtoq    HAVE_STRTOQ)
-CHECK_FUNCTION_EXISTS(_strtoi64 HAVE__STRTOI64)
+CHECK_SYMBOL_EXISTS(bcopy     strings.h     HAVE_BCOPY)
+CHECK_SYMBOL_EXISTS(memmove   string.h      HAVE_MEMMOVE)
+CHECK_SYMBOL_EXISTS(strerror  string.h      HAVE_STRERROR)
+CHECK_SYMBOL_EXISTS(strtoll   stdlib.h      HAVE_STRTOLL)
+CHECK_SYMBOL_EXISTS(strtoq    stdlib.h      HAVE_STRTOQ)
+CHECK_SYMBOL_EXISTS(_strtoi64 stdlib.h      HAVE__STRTOI64)
 
 CHECK_TYPE_SIZE("long long"             LONG_LONG)
 CHECK_TYPE_SIZE("unsigned long long"    UNSIGNED_LONG_LONG)
