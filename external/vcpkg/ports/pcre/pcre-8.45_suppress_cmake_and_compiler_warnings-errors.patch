--- a/CMakeLists.txt	2021-06-14 10:33:38.000000000 +0200
+++ b/CMakeLists.txt	2021-06-18 17:59:59.155148900 +0200
@@ -77,7 +77,6 @@
 # CMP0026 to avoid warnings for the use of LOCATION in GET_TARGET_PROPERTY.
 
 CMAKE_MINIMUM_REQUIRED(VERSION 2.8.5)
-CMAKE_POLICY(SET CMP0026 OLD)
 
 # For FindReadline.cmake. This was changed to allow setting CMAKE_MODULE_PATH
 # on the command line.
@@ -199,6 +198,7 @@
 ENDIF(MINGW)
 
 IF(MSVC)
+  add_definitions(/wd4703 /wd4146 /wd4308)
   OPTION(PCRE_STATIC_RUNTIME
 	"ON=Compile against the static runtime (/MT)."
 	OFF)
