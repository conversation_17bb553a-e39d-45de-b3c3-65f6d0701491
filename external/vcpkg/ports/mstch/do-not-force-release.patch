diff --git a/CMakeLists.txt b/CMakeLists.txt
index 8d8e0c7..4304ad7 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -5,17 +5,17 @@ option(WITH_UNIT_TESTS "enable building unit test executable" OFF)
 option(WITH_BENCHMARK "enable building benchmark executable" OFF)
 
 set(CMAKE_INCLUDE_CURRENT_DIR ON)
-set(CMAKE_INCLUDE_CURRENT_DIR_IN_INTERFACE ON)
-set(CMAKE_BUILD_TYPE Release)
 
 set(mstch_VERSION 1.0.1)
 
 if(NOT MSVC)
-  set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++11 -Wall -Wextra -O3")
+  set(CMAKE_CXX_STANDARD 11)
 endif()
 
 add_subdirectory(src)
 
+target_include_directories(mstch PUBLIC $<INSTALL_INTERFACE:include>)
+
 if(WITH_UNIT_TESTS)
   enable_testing()
   add_subdirectory(vendor/headerize)
