string(REGEX MATCH "^[0-9]+" VERSION_MAJOR ${VERSION})
string(REGEX MATCH "^gz-([a-z-]+)" MATCHED_VALUE ${PORT})
set(PACKAGE_NAME ${CMAKE_MATCH_1})

ignition_modular_library(NAME ${PACKAGE_NAME}
    REF ${PORT}${VERSION_MAJOR}_${VERSION}
    VERSION ${VERSION}
    SHA512 abac6e68ccb9b036dab4a49e5c9c64045045311595ac5d7a96222f8368d8fe4b007fe274abe45b877df19dce12bb08350e6600a28f040bb09acc3f15a5851bd2
    PATCHES
)
