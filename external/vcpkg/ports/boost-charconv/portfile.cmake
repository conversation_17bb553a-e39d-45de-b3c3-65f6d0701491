# Automatically generated by scripts/boost/generate-ports.ps1

vcpkg_from_github(
    OUT_SOURCE_PATH SOURCE_PATH
    REPO boostorg/charconv
    REF boost-${VERSION}
    SHA512 43c7e36a026de018af54e9150b68aa71d91bf687569225684220f32ae12cf6edb8aa8c236e47a0e8b30e5ab7eefa1847b57ecaa5a7c1c03843746260de7d03e4
    HEAD_REF master
)

set(FEATURE_OPTIONS "")
boost_configure_and_install(
    SOURCE_PATH "${SOURCE_PATH}"
    OPTIONS ${FEATURE_OPTIONS}
)
