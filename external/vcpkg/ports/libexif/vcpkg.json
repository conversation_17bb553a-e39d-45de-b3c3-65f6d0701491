{"name": "libexif", "version": "0.6.25", "description": "a library for parsing, editing, and saving EXIF data", "homepage": "https://libexif.github.io/", "license": "LGPL-2.1-or-later", "supports": "!uwp", "dependencies": [{"$comment": "Needs gettext m4 files and autopoint during autoreconf. No runtime dependency.", "name": "gettext", "host": true}, {"name": "vcpkg-cmake", "host": true}], "features": {"nls": {"description": "Enable native language support.", "dependencies": [{"name": "gettext", "host": true, "features": ["tools"]}, "gettext-libintl"]}}}