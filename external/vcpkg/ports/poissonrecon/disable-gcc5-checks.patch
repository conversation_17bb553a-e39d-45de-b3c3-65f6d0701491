diff --git a/Src/FEMTree.WeightedSamples.inl b/Src/FEMTree.WeightedSamples.inl
index 7b0d6b4..bb3ad0f 100644
--- a/Src/FEMTree.WeightedSamples.inl
+++ b/Src/FEMTree.WeightedSamples.inl
@@ -362,7 +362,7 @@ Real FEMTree< Dim , Real >::_splatPointData( Allocator< FEMTreeNode > *nodeAlloc
 		//		pow( width , -dim ): So that each sample is splatted with a unit volume
 		V _v = v * weight / Real( pow( width , dim ) ) * dx;
 //		V _v = v / Length(v) * dx;
-#if defined( __GNUC__ ) && __GNUC__ < 5
+#if defined( __GNUC__ ) && __GNUC__ < 5 && 0
 #ifdef SHOW_WARNINGS
 #warning "you've got me gcc version<5"
 #endif // SHOW_WARNINGS
diff --git a/Src/FEMTree.h b/Src/FEMTree.h
index 8f00045..2faa0f4 100644
--- a/Src/FEMTree.h
+++ b/Src/FEMTree.h
@@ -231,6 +231,7 @@ template< class Data , typename Pack > struct _SparseOrDenseNodeData{};
 template< class Data , unsigned int ... FEMSigs >
 struct _SparseOrDenseNodeData< Data , UIntPack< FEMSigs ... > >
 {
+	virtual ~_SparseOrDenseNodeData() {}
 	static const unsigned int Dim = sizeof ... ( FEMSigs );
 	typedef UIntPack< FEMSigs ... > FEMSignatures;
 	typedef Data data_type;
diff --git a/Src/FEMTree.inl b/Src/FEMTree.inl
index 814f1ae..405e996 100644
--- a/Src/FEMTree.inl
+++ b/Src/FEMTree.inl
@@ -425,7 +425,7 @@ SparseNodeData< OutData , UIntPack< DataSigs ... > > FEMTree< Dim , Real >::setI
 				AddAtomic( weightSum , sample.weight );
 				out *= sample.weight;
 				Allocator< FEMTreeNode > *nodeAllocator = nodeAllocators.size() ? nodeAllocators[ thread ] : NULL;
-#if defined( __GNUC__ ) && __GNUC__ < 5
+#if defined( __GNUC__ ) && __GNUC__ < 5 && 0
 #ifdef SHOW_WARNINGS
 #warning "you've got me gcc version<5"
 #endif // SHOW_WARNINGS
@@ -436,7 +436,7 @@ SparseNodeData< OutData , UIntPack< DataSigs ... > > FEMTree< Dim , Real >::setI
 				else
 				{
 					Real width = (Real)( 1.0 / ( 1<<maxDepth ) );
-#if defined( __GNUC__ ) && __GNUC__ < 5
+#if defined( __GNUC__ ) && __GNUC__ < 5 && 0
 #ifdef SHOW_WARNINGS
 #warning "you've got me gcc version<5"
 #endif // SHOW_WARNINGS
