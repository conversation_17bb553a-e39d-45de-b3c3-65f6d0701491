--- a/Src/JPEG.h
+++ b/Src/JPEG.h
@@ -6,14 +6,10 @@
 
 #ifdef _WIN32
 #include <windows.h>
-#include "JPEG/jpeglib.h"
-#include "JPEG/jerror.h"
-#include "JPEG/jmorecfg.h"
-#else // !_WIN32
+#endif // _WIN32
 #include <jpeglib.h>
 #include <jerror.h>
 #include <jmorecfg.h>
-#endif // _WIN32
 
 struct my_error_mgr
 {
--- a/Src/PNG.h
+++ b/Src/PNG.h
@@ -1,7 +1,7 @@
 #ifndef PNG_INCLUDED
 #define PNG_INCLUDED
 
-#include "PNG/png.h"
+#include <png.h>
 
 struct PNGReader : public ImageReader
 {
--- a/Src/PNG.inl
+++ b/Src/PNG.inl
@@ -1,10 +1,6 @@
 #include <stdio.h>
 #include <vector>
-#ifdef _WIN32
-#include "PNG/png.h"
-#else // !_WIN32
 #include <png.h>
-#endif // _WIN32
 
 inline PNGReader::PNGReader( const char* fileName , unsigned int& width , unsigned int& height , unsigned int& channels )
 {
