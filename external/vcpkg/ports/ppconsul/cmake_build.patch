diff --git a/CMakeLists.txt b/CMakeLists.txt
index 92e4698..aa643aa 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -52,6 +52,8 @@ endif()
 
 enable_testing()
 
+find_package(json11 REQUIRED)
+
 if (${USE_CPPNETLIB})
     find_package(Boost ${BOOST_MIN_VERSION} REQUIRED COMPONENTS thread filesystem system date_time chrono regex)
 
@@ -75,6 +77,8 @@ else ()
 	   set (CMAKE_LIBRARY_PATH ${CMAKE_LIBRARY_PATH} "${CURL_ROOT}/lib")
     endif ()
     find_package(CURL REQUIRED)
+    find_package(OpenSSL REQUIRED)
+    find_package(ZLIB REQUIRED)
 endif ()
 
 set(LIBB64_DIR "${PROJECT_SOURCE_DIR}/ext/b64")
@@ -87,9 +91,7 @@ if (WIN32 AND NOT BUILD_STATIC_LIB)
     message(FATAL_ERROR "Building Ppconsul as dynamic library on Windows is not supported, see https://github.com/oliora/ppconsul/issues/25")
 endif()
 
-add_subdirectory(ext/json11)
 add_subdirectory(src)
-add_subdirectory(tests)
 
 install(
     DIRECTORY "${HEADERS_DIR}"
diff --git a/src/CMakeLists.txt b/src/CMakeLists.txt
index d747100..966c7e2 100644
--- a/src/CMakeLists.txt
+++ b/src/CMakeLists.txt
@@ -78,7 +78,7 @@ target_include_directories(${PROJECT_NAME}
 
 target_link_libraries(${PROJECT_NAME}
     PRIVATE
-        json11
+        ${JSON11_LIBRARIES}
         ${Boost_LIBRARIES}
 )
 
@@ -87,7 +87,10 @@ if (${USE_CPPNETLIB})
     target_link_libraries(${PROJECT_NAME} PRIVATE ${CPPNETLIB_LIBRARIES})
 else ()
     target_include_directories(${PROJECT_NAME} PRIVATE ${CURL_INCLUDE_DIR})
-    target_link_libraries(${PROJECT_NAME} PRIVATE ${CURL_LIBRARIES})
+    target_link_libraries(${PROJECT_NAME} PRIVATE ${CURL_LIBRARIES} ${OPENSSL_LIBRARIES} ZLIB::ZLIB)
+    if (${CMAKE_SYSTEM_NAME} MATCHES "Darwin")
+        target_link_libraries(${PROJECT_NAME} PRIVATE "-framework CoreFoundation" "-framework Security")
+    endif ()
 endif ()
 
 source_group(${PROJECT_NAME} FILES ${SOURCES})
diff --git a/src/s11n.h b/src/s11n.h
index c317834..3cb03c4 100644
--- a/src/s11n.h
+++ b/src/s11n.h
@@ -6,7 +6,7 @@
 
 #include "ppconsul/config.h"
 #include "ppconsul/error.h"
-#include <json11/json11.hpp>
+#include <json11.hpp>
 #include <vector>
 #include <chrono>
 #include <set>
