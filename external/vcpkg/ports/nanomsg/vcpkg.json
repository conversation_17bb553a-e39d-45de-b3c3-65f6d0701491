{"name": "nanomsg", "version-semver": "1.2.1", "port-version": 2, "description": ["A simple high-performance implementation of several \"scalability protocols\".", "These scalability protocols are light-weight messaging protocols which can be used to solve a number of very common messaging patterns, such as request/reply, publish/subscribe, surveyor/respondent, and so forth. These protocols can run over a variety of transports such as TCP, UNIX sockets, and even WebSocket."], "homepage": "https://nanomsg.org/", "license": "MIT", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"tool": {"description": "nanomsg tool (nanocat)"}}}