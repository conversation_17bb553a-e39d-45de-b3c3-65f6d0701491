{"$comment": "Use scripts/update_suitesparse.py to update all SuiteSparse ports", "name": "suitesparse-paru", "version-semver": "1.0.0", "description": "ParU: Routines for solving sparse linear system via parallel multifrontal LU factorization algorithms in SuiteSparse", "homepage": "https://people.engr.tamu.edu/davis/suitesparse.html", "license": "GPL-3.0-or-later", "dependencies": ["suitesparse-cholmod", "suitesparse-config", "suitesparse-umfpack", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"openmp": {"description": "Enable OpenMP support", "supports": "!windows", "dependencies": [{"name": "suitesparse-config", "features": ["openmp"]}]}}}