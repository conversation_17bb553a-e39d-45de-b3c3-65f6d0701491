diff --git a/src/ideviceinstaller.c b/src/ideviceinstaller.c
index c50bacf..e4a8c3d 100644
--- a/src/ideviceinstaller.c
+++ b/src/ideviceinstaller.c
@@ -32,7 +32,9 @@
 #include <getopt.h>
 #include <errno.h>
 #include <time.h>
+#ifndef _MSC_VER
 #include <libgen.h>
+#endif
 #include <inttypes.h>
 #include <limits.h>
 #include <sys/stat.h>
@@ -87,6 +89,32 @@ static int asprintf(char **PTR, const char *TEMPLATE, ...)
 }
 #endif
 
+#ifdef _MSC_VER
+#ifndef ISSLASH
+#define ISSLASH(C) ((C) == '/' || (C) == '\\')
+#endif
+char *basename(char const *name) {
+    char const *base = name;
+    char const *p;
+    for (p = base; *p; p++) {
+        if (ISSLASH(*p)) {
+            do p++;
+            while (ISSLASH(*p));
+
+            if (!*p) {
+                if (ISSLASH(*base))
+                    base = p - 1;
+                break;
+            }
+
+            base = p;
+        }
+    }
+
+    return (char *) base;
+}
+#endif
+
 #define ITUNES_METADATA_PLIST_FILENAME "iTunesMetadata.plist"
 
 const char PKG_PATH[] = "PublicStaging";
