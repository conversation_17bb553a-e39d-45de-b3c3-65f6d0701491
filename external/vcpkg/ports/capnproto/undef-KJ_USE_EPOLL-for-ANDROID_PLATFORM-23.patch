From 306220e57881064da99f89ad4192b3e19c7953a9 Mon Sep 17 00:00:00 2001
From: <PERSON><PERSON> <<EMAIL>>
Date: Thu, 31 Aug 2023 21:20:09 +0200
Subject: [PATCH] Undef KJ_USE_EPOLL for ANDROID_PLATFORM < 23

---
 c++/src/kj/async-unix.h | 5 ++++-
 1 file changed, 4 insertions(+), 1 deletion(-)

diff --git a/c++/src/kj/async-unix.h b/c++/src/kj/async-unix.h
index 665305ea..c5f195cf 100644
--- a/c++/src/kj/async-unix.h
+++ b/c++/src/kj/async-unix.h
@@ -34,7 +34,10 @@
 KJ_BEGIN_HEADER
 
 #if !defined(KJ_USE_EPOLL) && !defined(KJ_USE_KQUEUE)
-#if __linux__
+
+// Android NDK less than API version 23 doesn't have sigtimedwait
+// sigtimedwait is used in async-unix.c++ if KJ_USE_EPOLL is defined
+#if __linux__ && !(__ANDROID__ && __ANDROID_API__ < 23)
 // Default to epoll on Linux.
 #define KJ_USE_EPOLL 1
 #elif __APPLE__ || __FreeBSD__ || __OpenBSD__ || __NetBSD__ || __DragonFly__
-- 
2.41.0.windows.3

