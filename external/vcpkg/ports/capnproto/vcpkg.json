{"name": "capnproto", "version": "1.1.0", "description": "Data interchange format and capability-based RPC system", "homepage": "https://capnproto.org/", "license": "MIT", "supports": "!windows | (!uwp & !arm32)", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}, "zlib"], "features": {"openssl": {"description": "Build libkj-tls by linking against OpenSSL.", "dependencies": ["openssl"]}}}