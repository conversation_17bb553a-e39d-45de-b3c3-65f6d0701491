{"name": "opencc", "version": "1.1.9", "description": "A project for conversions between Traditional Chinese, Simplified Chinese and Japanese Kanji (Shinjitai)", "homepage": "https://github.com/BYVoid/OpenCC", "license": "Apache-2.0", "supports": "!(arm | uwp)", "dependencies": ["darts-clone", "marisa-trie", {"name": "pkgconf", "host": true}, "<PERSON><PERSON><PERSON>", "tclap", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"tools": {"description": "Build OpenCC command-line tools"}}}