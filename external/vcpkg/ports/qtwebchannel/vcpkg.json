{"name": "qtwebchannel", "version": "6.8.3", "description": "Qt WebChannel enables peer-to-peer communication between a server (QML/C++ application) and a client (HTML/JavaScript or QML application).", "homepage": "https://www.qt.io/", "license": null, "dependencies": [{"name": "qtbase", "default-features": false}], "features": {"qml": {"description": "Build QML imports", "dependencies": [{"name": "qtdeclarative", "default-features": false}]}}}