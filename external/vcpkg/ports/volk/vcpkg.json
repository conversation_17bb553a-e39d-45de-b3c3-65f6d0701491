{"name": "volk", "version": "1.4.304.1", "description": ["Meta loader for Vulkan API.", "Note that the static library target volk::volk is built without platform-specific defines.", "Use the header-only target volk::volk_headers if you require platform-specific extensions."], "homepage": "https://github.com/zeux/volk", "license": "MIT", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}, "vulkan-headers"]}