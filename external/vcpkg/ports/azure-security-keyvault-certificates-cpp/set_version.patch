diff --git a/sdk/keyvault/azure-security-keyvault-certificates/CMakeLists.txt b/sdk/keyvault/azure-security-keyvault-certificates/CMakeLists.txt
index 0806e09b0..0c08f1576 100644
--- a/sdk/keyvault/azure-security-keyvault-certificates/CMakeLists.txt
+++ b/sdk/keyvault/azure-security-keyvault-certificates/CMakeLists.txt
@@ -100,6 +100,7 @@ target_compile_definitions(azure-security-keyvault-certificates PRIVATE _azure_B
 create_code_coverage(keyvault azure-security-keyvault-certificates azure-security-keyvault-certificates-test "tests?/*;samples?/*")
 
 get_az_version("${CMAKE_CURRENT_SOURCE_DIR}/src/private/package_version.hpp")
+set_target_properties(azure-security-keyvault-certificates PROPERTIES VERSION ${AZ_LIBRARY_VERSION})
 generate_documentation(azure-security-keyvault-certificates ${AZ_LIBRARY_VERSION})
 
 if(BUILD_TESTING)
