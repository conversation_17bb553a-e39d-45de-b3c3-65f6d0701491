diff --git a/usrsctplib/CMakeLists.txt b/usrsctplib/CMakeLists.txt
index aa99cf2..c60b393 100644
--- a/usrsctplib/CMakeLists.txt
+++ b/usrsctplib/CMakeLists.txt
@@ -174,7 +174,8 @@ list(APPEND usrsctp_sources
 
 add_library(usrsctp ${usrsctp_sources} ${usrsctp_headers})
 
-target_include_directories(usrsctp PUBLIC ${CMAKE_CURRENT_SOURCE_DIR})
+target_include_directories(usrsctp PUBLIC $<INSTALL_INTERFACE:include>
+        $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}>)
 
 if (WIN32)
 	message(STATUS "link library: ws2_32")
@@ -188,8 +189,12 @@ set_target_properties(usrsctp PROPERTIES SOVERSION ${SOVERSION_SHORT} VERSION ${
 # INSTALL LIBRARY AND HEADER
 #################################################
 
-install(TARGETS usrsctp DESTINATION ${CMAKE_INSTALL_LIBDIR})
+install(TARGETS usrsctp EXPORT unofficial-usrsctp-config 
+        ARCHIVE DESTINATION lib
+        LIBRARY DESTINATION lib
+        RUNTIME DESTINATION bin)
 install(FILES usrsctp.h DESTINATION ${CMAKE_INSTALL_INCLUDEDIR})
+install(EXPORT unofficial-usrsctp-config NAMESPACE unofficial::usrsctp:: DESTINATION share/unofficial-usrsctp)
 
 #################################################
 # GENERATE AND INSTALL PKG-CONFIG FILE
