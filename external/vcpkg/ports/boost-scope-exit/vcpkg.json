{"$comment": "Automatically generated by scripts/boost/generate-ports.ps1", "name": "boost-scope-exit", "version": "1.87.0", "description": "Boost scope_exit module", "homepage": "https://www.boost.org/libs/scope_exit", "license": "BSL-1.0", "dependencies": [{"name": "boost-cmake", "version>=": "1.87.0"}, {"name": "boost-config", "version>=": "1.87.0"}, {"name": "boost-function", "version>=": "1.87.0"}, {"name": "boost-headers", "version>=": "1.87.0"}, {"name": "boost-preprocessor", "version>=": "1.87.0"}, {"name": "boost-type-traits", "version>=": "1.87.0"}, {"name": "boost-typeof", "version>=": "1.87.0"}]}