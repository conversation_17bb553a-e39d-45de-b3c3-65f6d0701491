# Automatically generated by scripts/boost/generate-ports.ps1

vcpkg_from_github(
    OUT_SOURCE_PATH SOURCE_PATH
    REPO boostorg/scope_exit
    REF boost-${VERSION}
    SHA512 0a7a505068c78924c1dcd6fd546c3821dc37c27846f7da8b461bc6843dfd388f818260e4a66008bf38a0e41adb008a3a0d0bc990d01db0a0e1fdcee22402793f
    HEAD_REF master
)

set(FEATURE_OPTIONS "")
boost_configure_and_install(
    SOURCE_PATH "${SOURCE_PATH}"
    OPTIONS ${FEATURE_OPTIONS}
)
