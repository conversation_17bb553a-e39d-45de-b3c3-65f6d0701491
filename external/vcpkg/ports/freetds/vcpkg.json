{"name": "freetds", "version": "1.3.10", "port-version": 2, "description": "Implementation of the Tabular Data Stream protocol", "homepage": "https://www.freetds.org", "license": "GPL-2.0-only", "supports": "windows & !uwp & !xbox", "dependencies": [{"name": "gperf", "host": true}, {"name": "libmysql", "platform": "windows"}, {"name": "unixodbc", "platform": "!windows"}, {"name": "vcpkg-cmake", "host": true}], "default-features": ["openssl"], "features": {"openssl": {"description": "OpenSSL support", "dependencies": ["openssl"]}, "tools": {"description": "Build tools"}}}