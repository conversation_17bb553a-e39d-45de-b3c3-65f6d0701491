diff --git a/CMakeLists.txt b/CMakeLists.txt
index 7ac6cc58..1a6a4f0e 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -64,9 +64,9 @@ math(EXPR BUILD_NUMBER "${BUILD_NUMBER}")
 
 set(FREETDS_TOPDIR ${CMAKE_CURRENT_LIST_DIR})
 
-enable_testing()
-set(CMAKE_CTEST_COMMAND ctest)
-add_custom_target(check COMMAND ${CMAKE_CTEST_COMMAND})
+#enable_testing()
+#set(CMAKE_CTEST_COMMAND ctest)
+#add_custom_target(check COMMAND ${CMAKE_CTEST_COMMAND})
 
 # TODO depends on configure
 add_definitions(-D_REENTRANT -D_THREAD_SAFE)
@@ -459,9 +459,11 @@ add_subdirectory(src/tds)
 add_subdirectory(src/ctlib)
 add_subdirectory(src/dblib)
 add_subdirectory(src/odbc)
-add_subdirectory(src/apps)
+if(WITH_TOOLS)
+	add_subdirectory(src/apps)
+	add_subdirectory(src/pool)
+endif()
 add_subdirectory(src/server)
-add_subdirectory(src/pool)
 
 configure_file(${CMAKE_BINARY_DIR}/include/config.h.in ${CMAKE_BINARY_DIR}/include/config.h)
 configure_file(${CMAKE_SOURCE_DIR}/include/tds_sysdep_public.h.in ${CMAKE_BINARY_DIR}/include/tds_sysdep_public.h)
diff --git a/src/ctlib/CMakeLists.txt b/src/ctlib/CMakeLists.txt
index e83b0b1a..017d571a 100644
--- a/src/ctlib/CMakeLists.txt
+++ b/src/ctlib/CMakeLists.txt
@@ -1,4 +1,4 @@
-add_subdirectory(unittests)
+#add_subdirectory(unittests)
 
 set(static_lib_name ct)
 if(WIN32)
diff --git a/src/dblib/CMakeLists.txt b/src/dblib/CMakeLists.txt
index 5f1766f4..c70e87a3 100644
--- a/src/dblib/CMakeLists.txt
+++ b/src/dblib/CMakeLists.txt
@@ -1,4 +1,4 @@
-add_subdirectory(unittests)
+#add_subdirectory(unittests)
 
 if(WIN32)
 	set(win_SRCS winmain.c dblib.def dbopen.c)
diff --git a/src/odbc/CMakeLists.txt b/src/odbc/CMakeLists.txt
index ec8eaadd..6f9f32c9 100644
--- a/src/odbc/CMakeLists.txt
+++ b/src/odbc/CMakeLists.txt
@@ -1,4 +1,4 @@
-add_subdirectory(unittests)
+#add_subdirectory(unittests)
 
 # TODO checks
 add_definitions(-DUNIXODBC -DHAVE_SQLGETPRIVATEPROFILESTRING)
diff --git a/src/replacements/CMakeLists.txt b/src/replacements/CMakeLists.txt
index 48d7e536..f7b490f8 100644
--- a/src/replacements/CMakeLists.txt
+++ b/src/replacements/CMakeLists.txt
@@ -41,7 +41,7 @@ if (NOT WIN32)
 	set_target_properties(replacements PROPERTIES POSITION_INDEPENDENT_CODE ON)
 endif()
 
-add_subdirectory(unittests)
+#add_subdirectory(unittests)
 
 
 INSTALL(TARGETS replacements
diff --git a/src/tds/CMakeLists.txt b/src/tds/CMakeLists.txt
index 134a30cb..5426571b 100644
--- a/src/tds/CMakeLists.txt
+++ b/src/tds/CMakeLists.txt
@@ -1,4 +1,4 @@
-add_subdirectory(unittests)
+#add_subdirectory(unittests)
 
 if(PERL_FOUND AND NOT EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/tds_willconvert.h")
 	if (NOT GPERF)
diff --git a/src/utils/CMakeLists.txt b/src/utils/CMakeLists.txt
index 2a1935c9..a46483e0 100644
--- a/src/utils/CMakeLists.txt
+++ b/src/utils/CMakeLists.txt
@@ -1,4 +1,4 @@
-add_subdirectory(unittests)
+#add_subdirectory(unittests)
 
 if(WIN32)
 	set(add_SRCS md5.c)
