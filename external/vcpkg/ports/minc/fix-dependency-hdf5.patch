diff --git a/CMakeLists.txt b/CMakeLists.txt
index a6830b4..da3d635 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -65,7 +65,12 @@ IF(NOT LIBMINC_EXTERNALLY_CONFIGURED)
   # external packages
   FIND_PACKAGE(ZLIB REQUIRED)
   SET(HDF5_NO_FIND_PACKAGE_CONFIG_FILE ON)
-  FIND_PACKAGE(HDF5 REQUIRED COMPONENTS C )
+  FIND_PACKAGE(hdf5 CONFIG REQUIRED)
+  if (TARGET hdf5::hdf5-shared)
+      SET(HDF5_LIBRARIES hdf5::hdf5-shared)
+  elseif (TARGET hdf5::hdf5-static)
+      SET(HDF5_LIBRARIES hdf5::hdf5-static)
+  endif()
   
   IF (LIBMINC_USE_SYSTEM_NIFTI)
     FIND_PACKAGE(NIFTI)
@@ -515,12 +520,8 @@ IF(SUPERBUILD_STAGING_PREFIX)
   STRING(REPLACE "${SUPERBUILD_STAGING_PREFIX}/" "" LIBMINC_LIBRARIES_CONFIG    "${LIBMINC_LIBRARIES_CONFIG}")
   STRING(REPLACE "${SUPERBUILD_STAGING_PREFIX}/" "" EZMINC_LIBRARIES            "${EZMINC_LIBRARIES}")
   STRING(REPLACE "${SUPERBUILD_STAGING_PREFIX}/" "" NETCDF_INCLUDE_DIR          "${NETCDF_INCLUDE_DIR}")
-  STRING(REPLACE "${SUPERBUILD_STAGING_PREFIX}/" "" HDF5_INCLUDE_DIR            "${HDF5_INCLUDE_DIR}")
-  STRING(REPLACE "${SUPERBUILD_STAGING_PREFIX}/" "" HDF5_INCLUDE_DIRS           "${HDF5_INCLUDE_DIRS}")
   STRING(REPLACE "${SUPERBUILD_STAGING_PREFIX}/" "" NIFTI_INCLUDE_DIR           "${NIFTI_INCLUDE_DIR}")
   STRING(REPLACE "${SUPERBUILD_STAGING_PREFIX}/" "" NETCDF_LIBRARY              "${NETCDF_LIBRARY}")
-  STRING(REPLACE "${SUPERBUILD_STAGING_PREFIX}/" "" HDF5_LIBRARY                "${HDF5_LIBRARY}")
-  STRING(REPLACE "${SUPERBUILD_STAGING_PREFIX}/" "" HDF5_LIBRARIES              "${HDF5_LIBRARIES}")
   STRING(REPLACE "${SUPERBUILD_STAGING_PREFIX}/" "" NIFTI_LIBRARY               "${NIFTI_LIBRARY}")
   STRING(REPLACE "${SUPERBUILD_STAGING_PREFIX}/" "" ZNZ_LIBRARY                 "${ZNZ_LIBRARY}")
   STRING(REPLACE "${SUPERBUILD_STAGING_PREFIX}/" "" ZLIB_LIBRARY                "${ZLIB_LIBRARY}")
