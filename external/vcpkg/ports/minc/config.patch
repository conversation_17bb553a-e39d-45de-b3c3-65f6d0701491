diff --git a/LIBMINCConfig.cmake.in b/LIBMINCConfig.cmake.in
index 7790597b2..c041b685a 100644
--- a/LIBMINCConfig.cmake.in	
+++ b/LIBMINCConfig.cmake.in
@@ -1,3 +1,4 @@
+set(_IMPORT_PREFIX "${CMAKE_CURRENT_LIST_DIR}/../../")
 # LIBMINC CMake configuration file
 
 get_filename_component(LIBMINC_CMAKE_DIR "${CMAKE_CURRENT_LIST_FILE}" PATH )
@@ -37,3 +38,4 @@ set( LIBMINC_FOUND 1 )
 #  set( LIBMINC_TARGETS_IMPORTED 1 )
 #  include( "${LIBMINC_CMAKE_DIR}/@LIBMINC_EXPORTED_TARGETS@.cmake" )
 #endif()  
+unset(_IMPORT_PREFIX)
\ No newline at end of file
