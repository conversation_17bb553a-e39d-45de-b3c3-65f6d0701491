diff --git a/CMakeLists.txt b/CMakeLists.txt
index 5fd2ce0..202bcfe 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -27,10 +27,6 @@ option(SPIRVCROSS_WITHOUT_INSTALL "Compile with spirv-cross without install"
        OFF)
 option(USE_CREATE_COMPILER_FUNCTION "Whether LLGI::CreateCompiler is used." ON)
 
-if(LINUX)
-  set(BUILD_VULKAN TRUE)
-endif()
-
 option(USE_MSVC_RUNTIME_LIBRARY_DLL "compile as multithreaded DLL" ON)
 
 include(cmake/ClangFormat.cmake)
@@ -234,8 +230,13 @@ if(BUILD_VULKAN_COMPILER OR BUILD_TOOL)
       spirv-cross-util)
 
   else()
-    list(APPEND LLGI_THIRDPARTY_INCLUDES ${GLSLANG_INCLUDE_DIR}
-         ${SPIRVCROSS_INCLUDE_DIR})
+    # from vcpkg
+    find_package(glslang CONFIG REQUIRED)
+    find_package(spirv_cross_core CONFIG REQUIRED)
+    find_package(spirv_cross_glsl CONFIG REQUIRED)
+    find_package(spirv_cross_hlsl CONFIG REQUIRED)
+    find_package(spirv_cross_msl CONFIG REQUIRED)
+    find_package(spirv_cross_cpp CONFIG REQUIRED)
 
   endif()
 
diff --git a/tools/ShaderTranspilerCore/CMakeLists.txt b/tools/ShaderTranspilerCore/CMakeLists.txt
index bf34437..09e428d 100644
--- a/tools/ShaderTranspilerCore/CMakeLists.txt
+++ b/tools/ShaderTranspilerCore/CMakeLists.txt
@@ -24,6 +24,10 @@ else()
 
 endif()
 
+target_link_libraries(ShaderTranspilerCore PUBLIC
+  glslang::glslang glslang::glslang-default-resource-limits glslang::SPIRV glslang::SPVRemapper spirv-cross-cpp spirv-cross-glsl spirv-cross-msl spirv-cross-hlsl
+)
+
 if(USE_THIRDPARTY_DIRECTORY)
   add_dependencies(ShaderTranspilerCore EP_glslang EP_SPIRV-Cross)
 endif()
