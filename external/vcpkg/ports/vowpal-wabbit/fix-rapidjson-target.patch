diff --git a/ext_libs/ext_libs.cmake b/ext_libs/ext_libs.cmake
index e03de8690..f8acddd7e 100644
--- a/ext_libs/ext_libs.cmake
+++ b/ext_libs/ext_libs.cmake
@@ -37,8 +37,6 @@ endif()
 if(RAPIDJSON_SYS_DEP)
   # Since EXACT is not specified, any version compatible with 1.1.0 is accepted (>= 1.1.0)
   find_package(RapidJSON 1.1.0 CONFIG REQUIRED)
-  add_library(RapidJSON INTERFACE)
-  target_include_directories(RapidJSON INTERFACE ${RapidJSON_INCLUDE_DIRS} ${RAPIDJSON_INCLUDE_DIRS})
 else()
   add_library(RapidJSON INTERFACE)
   target_include_directories(RapidJSON SYSTEM INTERFACE "${CMAKE_CURRENT_LIST_DIR}/rapidjson/include")
