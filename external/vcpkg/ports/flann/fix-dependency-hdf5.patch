diff --git a/CMakeLists.txt b/CMakeLists.txt
index 9f1dbcb74..5587c896e 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -77,11 +77,19 @@ if (NOT PYTHON_EXECUTABLE)
 endif()
 endif()
 
-find_hdf5()
+if (WITH_HDF5)
+find_package(HDF5 CONFIG REQUIRED)
 if (NOT HDF5_FOUND)
-	message(WARNING "hdf5 library not found, some tests will not be run")
+	message(ERROR "hdf5 library not found")
 else()
-    include_directories(${HDF5_INCLUDE_DIR})
+    if (TARGET hdf5::hdf5-shared)
+        link_libraries(hdf5::hdf5-shared)
+    elseif (TARGET hdf5::hdf5-static)
+        link_libraries(hdf5::hdf5-static)
+    endif()
+    set(PKG_EXTERNAL_DEPS "${PKG_EXTERNAL_DEPS} hdf5")
+    set(CMAKE_EXTERNAL_DEPS "find_dependency(HDF5)")
+endif()
 endif()
 
 if (USE_MPI OR HDF5_IS_PARALLEL)

diff --git a/cmake/flann_utils.cmake b/cmake/flann_utils.cmake
index 8aef8e0..747414f 100644
--- a/cmake/flann_utils.cmake
+++ b/cmake/flann_utils.cmake
@@ -21,21 +21,8 @@ endmacro(DISSECT_VERSION)
 
 # workaround a FindHDF5 bug
 macro(find_hdf5)
-    find_package(HDF5)
-
-    set( HDF5_IS_PARALLEL FALSE )
-    foreach( _dir ${HDF5_INCLUDE_DIRS} )
-        if( EXISTS "${_dir}/H5pubconf.h" )
-            file( STRINGS "${_dir}/H5pubconf.h" 
-                HDF5_HAVE_PARALLEL_DEFINE
-                REGEX "HAVE_PARALLEL 1" )
-            if( HDF5_HAVE_PARALLEL_DEFINE )
-                set( HDF5_IS_PARALLEL TRUE )
-            endif()
-        endif()
-    endforeach()
-    set( HDF5_IS_PARALLEL ${HDF5_IS_PARALLEL} CACHE BOOL
-        "HDF5 library compiled with parallel IO support" )
+    find_package(hdf5 CONFIG)
+    set(HDF5_IS_PARALLEL ${HDF5_ENABLE_PARALLEL})
     mark_as_advanced( HDF5_IS_PARALLEL )
 endmacro(find_hdf5)
 
