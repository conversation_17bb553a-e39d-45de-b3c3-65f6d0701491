{"name": "flann", "version-date": "2022-10-28", "description": "Fast Library for Approximate Nearest Neighbors", "homepage": "https://github.com/mariusmuja/flann", "license": "BSD-3-<PERSON><PERSON>", "dependencies": ["lz4", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"cuda": {"description": "CUDA support for FLANN", "dependencies": ["cuda"]}, "hdf5": {"description": "Build with hdf5", "dependencies": ["hdf5"]}}}