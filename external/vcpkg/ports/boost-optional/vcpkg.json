{"$comment": "Automatically generated by scripts/boost/generate-ports.ps1", "name": "boost-optional", "version": "1.87.0", "port-version": 1, "description": "Boost optional module", "homepage": "https://www.boost.org/libs/optional", "license": "BSL-1.0", "dependencies": [{"name": "boost-assert", "version>=": "1.87.0"}, {"name": "boost-cmake", "version>=": "1.87.0"}, {"name": "boost-config", "version>=": "1.87.0"}, {"name": "boost-core", "version>=": "1.87.0"}, {"name": "boost-headers", "version>=": "1.87.0"}, {"name": "boost-throw-exception", "version>=": "1.87.0"}, {"name": "boost-type-traits", "version>=": "1.87.0"}]}