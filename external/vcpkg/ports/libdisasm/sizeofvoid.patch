--- a/libdisasm/x86_disasm.c
+++ b/libdisasm/x86_disasm.c
@@ -35,7 +35,7 @@ unsigned int x86_disasm( unsigned char *buf, unsigned int buf_len,
 
         if ( offset >= buf_len ) {
                 /* another caller screwup ;) */
-                x86_report_error(report_disasm_bounds, (void*)(long)buf_rva+offset);
+                x86_report_error(report_disasm_bounds, (void*)(long)(buf_rva+offset));
                 return 0;
         }
 
@@ -53,13 +53,13 @@ unsigned int x86_disasm( unsigned char *buf, unsigned int buf_len,
 
         /* check and see if we had an invalid instruction */
         if (! size ) {
-                x86_report_error(report_invalid_insn, (void*)(long)buf_rva+offset );
+                x86_report_error(report_invalid_insn, (void*)(long)(buf_rva+offset));
                 return 0;
         }
 
         /* check if we overran the end of the buffer */
         if ( size > len ) {
-                x86_report_error( report_insn_bounds, (void*)(long)buf_rva + offset );
+                x86_report_error( report_insn_bounds, (void*)(long)(buf_rva + offset));
 		MAKE_INVALID( insn, bytes );
 		return 0;
 	}
