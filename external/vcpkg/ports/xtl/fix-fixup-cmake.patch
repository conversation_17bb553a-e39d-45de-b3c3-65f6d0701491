diff --git a/CMakeLists.txt b/CMakeLists.txt
index 796dc46..d2be91f 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -30,7 +30,7 @@ message(STATUS "xtl v${${PROJECT_NAME}_VERSION}")
 # ============
 
 if(NOT TARGET nlohmann_json)
-    find_package(nlohmann_json QUIET)
+    find_package(nlohmann_json QUIET REQUIRED)
 endif()
 
 # Build
@@ -80,6 +80,7 @@ set_target_properties(xtl
     PUBLIC_HEADER "${XTL_HEADERS}")
 
 target_compile_features(xtl INTERFACE cxx_std_17)
+target_link_libraries(xtl INTERFACE nlohmann_json::nlohmann_json)
 
 option(BUILD_TESTS "xtl test suite" OFF)
 option(DOWNLOAD_GTEST "build gtest from downloaded sources" OFF)
@@ -104,7 +105,7 @@ install(TARGETS xtl
 export(EXPORT ${PROJECT_NAME}-targets
        FILE "${CMAKE_CURRENT_BINARY_DIR}/${PROJECT_NAME}Targets.cmake")
 
-set(XTL_CMAKECONFIG_INSTALL_DIR "${CMAKE_INSTALL_DATAROOTDIR}/cmake/${PROJECT_NAME}" CACHE
+set(XTL_CMAKECONFIG_INSTALL_DIR "${CMAKE_INSTALL_LIBDIR}/cmake/${PROJECT_NAME}" CACHE
     STRING "install path for xtlConfig.cmake")
 
 configure_package_config_file(${PROJECT_NAME}Config.cmake.in
@@ -133,4 +134,4 @@ configure_file(${PROJECT_NAME}.pc.in
                "${CMAKE_CURRENT_BINARY_DIR}/${PROJECT_NAME}.pc"
                 @ONLY)
 install(FILES "${CMAKE_CURRENT_BINARY_DIR}/${PROJECT_NAME}.pc"
-        DESTINATION "${CMAKE_INSTALL_DATAROOTDIR}/pkgconfig/")
+        DESTINATION "${CMAKE_INSTALL_LIBDIR}/pkgconfig/")
diff --git a/xtlConfig.cmake.in b/xtlConfig.cmake.in
index 936eef1..ef392c6 100644
--- a/xtlConfig.cmake.in
+++ b/xtlConfig.cmake.in
@@ -13,6 +13,8 @@
 #   xtl_INCLUDE_DIRS - the directory containing xtl headers
 #   xtl_LIBRARY - empty
 
+include(CMakeFindDependencyMacro)
+find_dependency(nlohmann_json)
 @PACKAGE_INIT@
 
 if(NOT TARGET @PROJECT_NAME@)
