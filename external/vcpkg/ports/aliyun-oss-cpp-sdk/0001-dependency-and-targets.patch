diff --git a/CMakeLists.txt b/CMakeLists.txt
index ea0d8d6..2a853a0 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -78,8 +78,8 @@ if (${TARGET_OS} STREQUAL "WINDOWS")
 	set(CLIENT_INCLUDE_DIRS 
 		${CMAKE_SOURCE_DIR}/third_party/include) 
 else()
-	include(FindCURL)
-	include(FindOpenSSL)
+	find_package(CURL CONFIG REQUIRED)
+	find_package(OpenSSL REQUIRED)
 
 	if(NOT CURL_FOUND)
 		message(FATAL_ERROR "Could not find curl")
diff --git a/sdk/CMakeLists.txt b/sdk/CMakeLists.txt
index 28d04c2..d5d715d 100644
--- a/sdk/CMakeLists.txt
+++ b/sdk/CMakeLists.txt
@@ -146,6 +146,13 @@ set_target_properties(${PROJECT_NAME}${STATIC_LIB_SUFFIX}
     OUTPUT_NAME ${TARGET_OUTPUT_NAME_PREFIX}${PROJECT_NAME}${STATIC_LIB_SUFFIX}
     )
 
+target_include_directories(${PROJECT_NAME}${STATIC_LIB_SUFFIX}
+    PUBLIC $<INSTALL_INTERFACE:include>
+)
+
+target_link_libraries(${PROJECT_NAME}${STATIC_LIB_SUFFIX} ${CRYPTO_LIBS})
+target_link_libraries(${PROJECT_NAME}${STATIC_LIB_SUFFIX} ${CLIENT_LIBS})
+
 target_include_directories(${PROJECT_NAME}${STATIC_LIB_SUFFIX}
     PRIVATE include
     PRIVATE include/alibabacloud/oss    
@@ -173,6 +180,9 @@ if (BUILD_SHARED_LIBS)
         OUTPUT_NAME ${TARGET_OUTPUT_NAME_PREFIX}${PROJECT_NAME}
         )
     
+    target_include_directories(${PROJECT_NAME}
+        PUBLIC $<INSTALL_INTERFACE:include>
+    )
     target_include_directories(${PROJECT_NAME}
         PRIVATE include
         PRIVATE include/alibabacloud/oss    
@@ -210,16 +220,18 @@ install(FILES ${sdk_encryption_header}
 install(FILES ${sdk_public_header}
     DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}/alibabacloud/oss)
 
-install(TARGETS  ${PROJECT_NAME}${STATIC_LIB_SUFFIX}
+install(TARGETS  ${PROJECT_NAME}${STATIC_LIB_SUFFIX} EXPORT unofficial-aliyun-oss-cpp-sdk-targets
     ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR}
     LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
     RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
     )
 
 if (BUILD_SHARED_LIBS)
-install(TARGETS  ${PROJECT_NAME}
+install(TARGETS  ${PROJECT_NAME} EXPORT unofficial-aliyun-oss-cpp-sdk-targets
     ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR}
     LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
     RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
     )
-endif()
\ No newline at end of file
+endif()
+
+include(0002-unofficial-export.cmake)
