diff --git a/CMakeLists.txt b/CMakeLists.txt
index 6eb607f0..5b0f60f6 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -553,8 +553,7 @@ if(${use_http})
             set(CURL_FIND_REQUIRED 1)
             find_package_handle_standard_args(CURL DEFAULT_MSG CURL_LIBRARIES)

-            include_directories(${CURL_INCLUDE_DIRS})
-            set(aziotsharedutil_target_libs ${aziotsharedutil_target_libs} ${CURL_LIBRARIES})
+            set(aziotsharedutil_target_libs ${aziotsharedutil_target_libs} CURL::libcurl)
         endif(NOT use_builtin_httpapi)
     endif()
 endif(${use_http})
@@ -593,7 +592,7 @@ if(${use_bearssl})
 endif()

 if(${use_openssl})
-    set(aziotsharedutil_target_libs ${aziotsharedutil_target_libs} ${OPENSSL_LIBRARIES})
+    set(aziotsharedutil_target_libs ${aziotsharedutil_target_libs} OpenSSL::SSL)
     if (WIN32)
         set(aziotsharedutil_target_libs ${aziotsharedutil_target_libs} crypt32 ws2_32 secur32)
     endif()
@@ -743,7 +742,7 @@ write_basic_package_version_file(

 configure_file("configs/${PROJECT_NAME}Config.cmake"
     "${CMAKE_CURRENT_BINARY_DIR}/${PROJECT_NAME}/${PROJECT_NAME}Config.cmake"
-    COPYONLY
+    @ONLY
 )

 install(EXPORT aziotsharedutilTargets
@@ -755,7 +754,7 @@ install(EXPORT aziotsharedutilTargets

 install(
 FILES
-    "configs/${PROJECT_NAME}Config.cmake"
+    "${CMAKE_CURRENT_BINARY_DIR}/${PROJECT_NAME}/${PROJECT_NAME}Config.cmake"
     "configs/${PROJECT_NAME}Functions.cmake"
     "configs/azure_iot_build_rules.cmake"
     "${CMAKE_CURRENT_BINARY_DIR}/${PROJECT_NAME}/${PROJECT_NAME}ConfigVersion.cmake"
diff --git a/configs/azure_c_shared_utilityConfig.cmake b/configs/azure_c_shared_utilityConfig.cmake
index 2d7c733e..0e1e4d41 100644
--- a/configs/azure_c_shared_utilityConfig.cmake
+++ b/configs/azure_c_shared_utilityConfig.cmake
@@ -1,11 +1,12 @@
 #Copyright (c) Microsoft. All rights reserved.
 #Licensed under the MIT license. See LICENSE file in the project root for full license information.

-if(UNIX)
-    if(${use_http})
-        include(CMakeFindDependencyMacro)
-        find_dependency(CURL)
-    endif()
+include(CMakeFindDependencyMacro)
+if("@use_openssl@")
+    find_dependency(OpenSSL)
+endif()
+if(UNIX AND NOT "@use_builtin_httpapi@")
+    find_dependency(CURL)
 endif()

 include("${CMAKE_CURRENT_LIST_DIR}/azure_c_shared_utilityTargets.cmake")
