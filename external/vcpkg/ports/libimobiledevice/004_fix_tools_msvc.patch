diff --git a/tools/idevicebackup.c b/tools/idevicebackup.c
index 5694c12..77fd81f 100644
--- a/tools/idevicebackup.c
+++ b/tools/idevicebackup.c
@@ -50,7 +50,9 @@
 #else
 #error No supported crypto library enabled
 #endif
+#ifndef _MSC_VER
 #include <unistd.h>
+#endif
 #include <ctype.h>
 #include <time.h>
 
@@ -68,6 +70,18 @@
 #define LOCK_ATTEMPTS 50
 #define LOCK_WAIT 200000
 
+#ifdef _MSC_VER
+static void usleep(__int64 usec) {
+    HANDLE timer;
+    LARGE_INTEGER ft;
+    ft.QuadPart = -(10*usec);
+    timer = CreateWaitableTimer(NULL, TRUE, NULL);
+    SetWaitableTimer(timer, &ft, 0, NULL, NULL, 0);
+    WaitForSingleObject(timer, INFINITE);
+    CloseHandle(timer);
+}
+#endif
+
 #ifdef WIN32
 #include <windows.h>
 #define sleep(x) Sleep(x*1000)
diff --git a/tools/idevicebackup2.c b/tools/idevicebackup2.c
index c73b269..33157c9 100644
--- a/tools/idevicebackup2.c
+++ b/tools/idevicebackup2.c
@@ -31,9 +31,13 @@
 #include <errno.h>
 #include <stdlib.h>
 #include <signal.h>
+#ifndef _MSC_VER
 #include <unistd.h>
+#endif
 #include <dirent.h>
+#ifndef _MSC_VER
 #include <libgen.h>
+#endif
 #include <ctype.h>
 #include <time.h>
 #include <getopt.h>
@@ -54,6 +58,62 @@
 #define LOCK_ATTEMPTS 50
 #define LOCK_WAIT 200000
 
+#ifdef _MSC_VER
+static void usleep(__int64 usec) {
+    HANDLE timer;
+    LARGE_INTEGER ft;
+    ft.QuadPart = -(10*usec);
+    timer = CreateWaitableTimer(NULL, TRUE, NULL);
+    SetWaitableTimer(timer, &ft, 0, NULL, NULL, 0);
+    WaitForSingleObject(timer, INFINITE);
+    CloseHandle(timer);
+}
+#ifndef ISSLASH
+#define ISSLASH(C) ((C) == '/' || (C) == '\\')
+#endif
+#ifndef FILESYSTEM_PREFIX_LEN
+#define FILESYSTEM_PREFIX_LEN(Filename) 0
+#endif
+static char *basename(char const *name) {
+    char const *base = name + FILESYSTEM_PREFIX_LEN(name);
+    char const *p;
+
+    for (p = base; *p; p++) {
+        if (ISSLASH(*p)) {
+            do p++;
+            while (ISSLASH(*p));
+
+            if (!*p) {
+                if (ISSLASH(*base))
+                    base = p - 1;
+                break;
+            }
+            base = p;
+        }
+    }
+
+    return (char *) base;
+}
+static size_t dirlen(char const *path) {
+    size_t prefix_length = FILESYSTEM_PREFIX_LEN(path);
+    size_t length;
+    for (length = basename(path) - path; prefix_length < length; length--)
+        if (!ISSLASH(path[length - 1]))
+            return length;
+    return prefix_length + ISSLASH(path[prefix_length]);
+}
+static char *dirname(char const *path) {
+    size_t length = dirlen(path);
+    int append_dot = (length == FILESYSTEM_PREFIX_LEN(path));
+    char *newpath = (char *) malloc(length + append_dot + 1);
+    memcpy(newpath, path, length);
+    if (append_dot)
+        newpath[length++] = '.';
+    newpath[length] = 0;
+    return newpath;
+}
+#endif
+
 #ifdef WIN32
 #include <windows.h>
 #include <conio.h>
diff --git a/tools/idevicebtlogger.c b/tools/idevicebtlogger.c
index 8de6b22..8fbf40d 100644
--- a/tools/idevicebtlogger.c
+++ b/tools/idevicebtlogger.c
@@ -31,7 +31,9 @@
 #include <errno.h>
 #include <signal.h>
 #include <stdlib.h>
+#ifndef _MSC_VER
 #include <unistd.h>
+#endif
 #include <getopt.h>
 #include <assert.h>
 #include <fcntl.h>
diff --git a/tools/idevicecrashreport.c b/tools/idevicecrashreport.c
index 09bd537..9f53b30 100644
--- a/tools/idevicecrashreport.c
+++ b/tools/idevicecrashreport.c
@@ -29,7 +29,14 @@
 #include <stdio.h>
 #include <stdlib.h>
 #include <string.h>
+#ifndef _MSC_VER
 #include <unistd.h>
+#else
+#define S_IFIFO _S_IFIFO
+#define S_IFBLK 0x3000
+#define S_ISREG(m) (((m) & S_IFMT) == S_IFREG)
+#define S_ISDIR(m) (((m) & S_IFMT) == S_IFDIR)
+#endif
 #include <getopt.h>
 #ifndef WIN32
 #include <signal.h>
diff --git a/tools/idevicedebug.c b/tools/idevicedebug.c
index 36c594e..9c11781 100644
--- a/tools/idevicedebug.c
+++ b/tools/idevicedebug.c
@@ -30,8 +30,12 @@
 #include <stdlib.h>
 #include <string.h>
 #include <time.h>
+#ifndef _MSC_VER
 #include <unistd.h>
+#endif
+#ifndef _MSC_VER
 #include <libgen.h>
+#endif
 #include <getopt.h>
 
 #ifdef WIN32
diff --git a/tools/idevicedevmodectl.c b/tools/idevicedevmodectl.c
index ba9b935..8e28e4b 100644
--- a/tools/idevicedevmodectl.c
+++ b/tools/idevicedevmodectl.c
@@ -30,7 +30,9 @@
 #include <string.h>
 #include <getopt.h>
 #include <sys/stat.h>
+#ifndef _MSC_VER
 #include <unistd.h>
+#endif
 #include <errno.h>
 #ifndef WIN32
 #include <signal.h>
diff --git a/tools/ideviceimagemounter.c b/tools/ideviceimagemounter.c
index f551b6c..26bb80c 100644
--- a/tools/ideviceimagemounter.c
+++ b/tools/ideviceimagemounter.c
@@ -32,9 +32,13 @@
 #include <string.h>
 #include <getopt.h>
 #include <errno.h>
+#ifndef _MSC_VER
 #include <libgen.h>
+#endif
 #include <time.h>
+#ifndef _MSC_VER
 #include <sys/time.h>
+#endif
 #include <inttypes.h>
 #ifndef WIN32
 #include <signal.h>
diff --git a/tools/idevicename.c b/tools/idevicename.c
index 69b76f6..cb8c9a6 100644
--- a/tools/idevicename.c
+++ b/tools/idevicename.c
@@ -27,7 +27,9 @@
 
 #include <stdio.h>
 #include <string.h>
+#ifndef _MSC_VER
 #include <unistd.h>
+#endif
 #include <stdlib.h>
 #include <getopt.h>
 #ifndef WIN32
diff --git a/tools/idevicepair.c b/tools/idevicepair.c
index 94d3f04..129c409 100644
--- a/tools/idevicepair.c
+++ b/tools/idevicepair.c
@@ -31,7 +31,9 @@
 #include <stdlib.h>
 #include <getopt.h>
 #include <ctype.h>
+#ifndef _MSC_VER
 #include <unistd.h>
+#endif
 #ifdef WIN32
 #include <windows.h>
 #include <conio.h>
diff --git a/tools/ideviceprovision.c b/tools/ideviceprovision.c
index 4080a28..f64e2c4 100644
--- a/tools/ideviceprovision.c
+++ b/tools/ideviceprovision.c
@@ -42,6 +42,14 @@
 #include <arpa/inet.h>
 #endif
 
+#ifdef _MSC_VER
+#include <Winsock2.h>
+#ifndef _S_ISTYPE
+#define _S_ISTYPE(mode, mask)  (((mode) & _S_IFMT) == (mask))
+#define S_ISDIR(mode) _S_ISTYPE((mode), _S_IFDIR)
+#endif
+#endif
+
 #include <libimobiledevice/libimobiledevice.h>
 #include <libimobiledevice/lockdown.h>
 #include <libimobiledevice/misagent.h>
diff --git a/tools/idevicescreenshot.c b/tools/idevicescreenshot.c
index 0e694c7..6f4b1c5 100644
--- a/tools/idevicescreenshot.c
+++ b/tools/idevicescreenshot.c
@@ -31,7 +31,11 @@
 #include <getopt.h>
 #include <errno.h>
 #include <time.h>
+#ifndef _MSC_VER
 #include <unistd.h>
+#else
+#define F_OK 0
+#endif
 #ifndef WIN32
 #include <signal.h>
 #endif
diff --git a/tools/idevicesyslog.c b/tools/idevicesyslog.c
index 5600be2..69b7b03 100644
--- a/tools/idevicesyslog.c
+++ b/tools/idevicesyslog.c
@@ -31,7 +31,9 @@
 #include <errno.h>
 #include <signal.h>
 #include <stdlib.h>
+#ifndef _MSC_VER
 #include <unistd.h>
+#endif
 #include <getopt.h>
 
 #ifdef WIN32
