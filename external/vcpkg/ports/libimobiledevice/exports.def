EXPORTS

    ; src/mobilebackup2.c
    mobilebackup2_client_new
    mobilebackup2_client_start_service
    mobilebackup2_client_free
    mobilebackup2_send_message
    mobilebackup2_receive_message
    mobilebackup2_send_raw
    mobilebackup2_receive_raw
    mobilebackup2_version_exchange
    mobilebackup2_send_request
    mobilebackup2_send_status_response

    ; src/misagent.c
    misagent_client_new
    misagent_client_start_service
    misagent_client_free
    misagent_install
    misagent_copy
    misagent_copy_all
    misagent_remove
    misagent_get_status_code

    ; src/mobileactivation.c
    mobileactivation_client_new
    mobileactivation_client_start_service
    mobileactivation_client_free
    mobileactivation_get_activation_state
    mobileactivation_create_activation_session_info
    mobileactivation_create_activation_info
    mobileactivation_create_activation_info_with_session
    mobileactivation_activate
    mobileactivation_activate_with_session
    mobileactivation_deactivate

    ; src/heartbeat.c
    heartbeat_client_new
    heartbeat_client_start_service
    heartbeat_client_free
    heartbeat_send
    heartbeat_receive
    heartbeat_receive_with_timeout

    ; src/lockdown-cu.c
    lockdownd_cu_pairing_create
    lockdownd_cu_send_request_and_get_reply
    lockdownd_get_value_cu
    lockdownd_pair_cu

    ; src/preboard.c
    preboard_client_new
    preboard_client_start_service
    preboard_client_free
    preboard_send
    preboard_receive_with_timeout
    preboard_receive
    preboard_create_stashbag
    preboard_commit_stashbag

    ; src/screenshotr.c
    screenshotr_client_new
    screenshotr_client_start_service
    screenshotr_client_free
    screenshotr_take_screenshot

    ; src/property_list_service.c
    property_list_service_client_new
    property_list_service_client_free
    property_list_service_send_xml_plist
    property_list_service_send_binary_plist
    property_list_service_receive_plist_with_timeout
    property_list_service_receive_plist
    property_list_service_enable_ssl
    property_list_service_disable_ssl
    property_list_service_get_service_client

    ; src/file_relay.c
    file_relay_client_new
    file_relay_client_start_service
    file_relay_client_free
    file_relay_request_sources_timeout
    file_relay_request_sources

    ; src/reverse_proxy.c
    reverse_proxy_client_start_proxy
    reverse_proxy_client_create_with_service
    reverse_proxy_client_create_with_port
    reverse_proxy_client_free
    reverse_proxy_get_type
    reverse_proxy_client_set_status_callback
    reverse_proxy_client_set_log_callback
    reverse_proxy_client_set_data_callback

    ; src/service.c
    service_client_new
    service_client_factory_start_service
    service_client_free
    service_send
    service_receive_with_timeout
    service_receive
    service_enable_ssl
    service_disable_ssl
    service_disable_bypass_ssl
    service_get_connection

    ; src/installation_proxy.c
    instproxy_client_new
    instproxy_client_start_service
    instproxy_client_free
    instproxy_browse_with_callback
    instproxy_browse
    instproxy_lookup
    instproxy_install
    instproxy_upgrade
    instproxy_uninstall
    instproxy_lookup_archives
    instproxy_archive
    instproxy_restore
    instproxy_remove_archive
    instproxy_check_capabilities_match
    instproxy_status_get_error
    instproxy_status_get_name
    instproxy_status_get_percent_complete
    instproxy_status_get_current_list
    instproxy_command_get_name
    instproxy_client_options_new
    instproxy_client_options_add
    instproxy_client_options_set_return_attributes
    instproxy_client_options_free
    instproxy_client_get_path_for_bundle_identifier

    ; src/companion_proxy.c
    companion_proxy_client_new
    companion_proxy_client_start_service
    companion_proxy_client_free
    companion_proxy_send
    companion_proxy_receive
    companion_proxy_get_device_registry
    companion_proxy_start_listening_for_devices
    companion_proxy_stop_listening_for_devices
    companion_proxy_get_value_from_registry
    companion_proxy_start_forwarding_service_port
    companion_proxy_stop_forwarding_service_port

    ; src/restore.c
    restored_client_free
    restored_client_set_label
    restored_receive
    restored_send
    restored_query_type
    restored_query_value
    restored_get_value
    restored_client_new
    restored_goodbye
    restored_start_restore
    restored_reboot

    ; src/notification_proxy.c
    np_client_new
    np_client_start_service
    np_client_free
    np_post_notification
    np_observe_notification
    np_observe_notifications
    np_set_notify_callback

    ; src/house_arrest.c
    house_arrest_client_new
    house_arrest_client_start_service
    house_arrest_client_free
    house_arrest_send_request
    house_arrest_send_command
    house_arrest_get_result
    afc_client_new_from_house_arrest_client

    ; src/mobilesync.c
    mobilesync_client_new
    mobilesync_client_start_service
    mobilesync_client_free
    mobilesync_receive
    mobilesync_send
    mobilesync_start
    mobilesync_finish
    mobilesync_get_all_records_from_device
    mobilesync_get_changes_from_device
    mobilesync_receive_changes
    mobilesync_clear_all_records_on_device
    mobilesync_acknowledge_changes_from_device
    mobilesync_ready_to_send_changes_from_computer
    mobilesync_send_changes
    mobilesync_remap_identifiers
    mobilesync_cancel
    mobilesync_anchors_new
    mobilesync_anchors_free
    mobilesync_actions_new
    mobilesync_actions_add
    mobilesync_actions_free

    ; src/idevice.c
    idevice_events_subscribe
    idevice_events_unsubscribe
    idevice_event_subscribe
    idevice_event_unsubscribe
    idevice_get_device_list_extended
    idevice_device_list_extended_free
    idevice_get_device_list
    idevice_device_list_free
    idevice_set_debug_level
    idevice_new_with_options
    idevice_new
    idevice_free
    idevice_connect
    idevice_disconnect
    idevice_connection_send
    idevice_connection_receive_timeout
    idevice_connection_receive
    idevice_connection_get_fd
    idevice_get_handle
    idevice_get_udid
    idevice_connection_enable_ssl
    idevice_connection_disable_ssl
    idevice_connection_disable_bypass_ssl

    ; src/mobilebackup.c
    mobilebackup_client_new
    mobilebackup_client_start_service
    mobilebackup_client_free
    mobilebackup_receive
    mobilebackup_send
    mobilebackup_request_backup
    mobilebackup_send_backup_file_received
    mobilebackup_request_restore
    mobilebackup_receive_restore_file_received
    mobilebackup_receive_restore_application_received
    mobilebackup_send_restore_complete
    mobilebackup_send_error

    ; src/diagnostics_relay.c
    diagnostics_relay_client_new
    diagnostics_relay_client_start_service
    diagnostics_relay_client_free
    diagnostics_relay_goodbye
    diagnostics_relay_sleep
    diagnostics_relay_restart
    diagnostics_relay_shutdown
    diagnostics_relay_request_diagnostics
    diagnostics_relay_query_mobilegestalt
    diagnostics_relay_query_ioregistry_entry
    diagnostics_relay_query_ioregistry_plane

    ; src/webinspector.c
    webinspector_client_new
    webinspector_client_start_service
    webinspector_client_free
    webinspector_send
    webinspector_receive
    webinspector_receive_with_timeout

    ; src/afc.c
    afc_client_new_with_service_client
    afc_client_new
    afc_client_start_service
    afc_client_free
    afc_read_directory
    afc_get_device_info
    afc_get_device_info_key
    afc_remove_path
    afc_rename_path
    afc_make_directory
    afc_get_file_info
    afc_file_open
    afc_file_read
    afc_file_write
    afc_file_close
    afc_file_lock
    afc_file_seek
    afc_file_tell
    afc_file_truncate
    afc_truncate
    afc_make_link
    afc_set_file_time
    afc_remove_path_and_contents
    afc_dictionary_free

    ; src/debugserver.c
    debugserver_client_new
    debugserver_client_start_service
    debugserver_client_free
    debugserver_client_send
    debugserver_client_receive_with_timeout
    debugserver_client_receive
    debugserver_command_new
    debugserver_command_free
    debugserver_encode_string
    debugserver_decode_string
    debugserver_client_set_ack_mode
    debugserver_client_set_receive_params
    debugserver_client_receive_response
    debugserver_client_send_command
    debugserver_client_set_environment_hex_encoded
    debugserver_client_set_argv

    ; src/sbservices.c
    sbservices_client_new
    sbservices_client_start_service
    sbservices_client_free
    sbservices_get_icon_state
    sbservices_set_icon_state
    sbservices_get_icon_pngdata
    sbservices_get_interface_orientation
    sbservices_get_home_screen_wallpaper_pngdata

    ; src/bt_packet_logger.c
    bt_packet_logger_client_new
    bt_packet_logger_client_start_service
    bt_packet_logger_client_free
    bt_packet_logger_receive_with_timeout
    bt_packet_logger_start_capture
    bt_packet_logger_stop_capture

    ; src/mobile_image_mounter.c
    mobile_image_mounter_new
    mobile_image_mounter_start_service
    mobile_image_mounter_free
    mobile_image_mounter_lookup_image
    mobile_image_mounter_upload_image
    mobile_image_mounter_mount_image
    mobile_image_mounter_hangup

    ; src/lockdown.c
    lockdownd_stop_session
    lockdownd_client_free
    lockdownd_client_set_label
    lockdownd_receive
    lockdownd_send
    lockdownd_query_type
    lockdownd_get_value
    lockdownd_set_value
    lockdownd_remove_value
    lockdownd_get_device_udid
    lockdownd_get_device_name
    lockdownd_client_new
    lockdownd_client_new_with_handshake
    lockdownd_pair
    lockdownd_pair_with_options
    lockdownd_validate_pair
    lockdownd_unpair
    lockdownd_enter_recovery
    lockdownd_goodbye
    lockdownd_start_session
    lockdownd_start_service
    lockdownd_start_service_with_escrow_bag
    lockdownd_activate
    lockdownd_deactivate
    lockdownd_get_sync_data_classes
    lockdownd_data_classes_free
    lockdownd_service_descriptor_free
    lockdownd_strerror

    ; src/syslog_relay.c
    syslog_relay_client_new
    syslog_relay_client_start_service
    syslog_relay_client_free
    syslog_relay_receive
    syslog_relay_receive_with_timeout
    syslog_relay_start_capture
    syslog_relay_start_capture_raw
    syslog_relay_stop_capture
