diff --git a/KF5DBusAddonsConfig.cmake.in b/KF5DBusAddonsConfig.cmake.in
index 8d8cea5..7770236 100644
--- a/KF5DBusAddonsConfig.cmake.in
+++ b/KF5DBusAddonsConfig.cmake.in
@@ -6,9 +6,9 @@ find_dependency(Qt@QT_MAJOR_VERSION@DBus @REQUIRED_QT_VERSION@)
 if(NOT @BUILD_SHARED_LIBS@)
     # Qt5::DBus target will have been defined by the find_dependency() call above
     # at the time where this file is used
-    if(TARGET Qt5::DBus)
+    if(@Qt5X11Extras_FOUND@)
         find_dependency(Qt5X11Extras @REQUIRED_QT_VERSION@)
-    else()
+    elseif(@Qt6Gui_FOUND@)
         find_dependency(Qt6Gui @REQUIRED_QT_VERSION@) # qtx11extras_p.h
     endif()
 endif()
