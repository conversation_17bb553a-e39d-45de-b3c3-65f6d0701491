From ed3b6e4bca1fe5211e3d7ca06bbbf9b161c8bc19 Mon Sep 17 00:00:00 2001
From: <PERSON><PERSON> <<EMAIL>>
Date: Sat, 2 Nov 2019 14:50:53 -0700
Subject: [PATCH] Add naive MSVC support to sources

---
 libmpeg2/convert/rgb.c | 2 +-
 libmpeg2/cpu_accel.c   | 4 ++--
 libmpeg2/cpu_state.c   | 4 ++--
 libmpeg2/idct.c        | 2 +-
 libmpeg2/motion_comp.c | 2 +-
 libvo/video_out_dx.c   | 6 +++---
 vc++/config.h          | 2 ++
 7 files changed, 12 insertions(+), 10 deletions(-)

diff --git a/libmpeg2/convert/rgb.c b/libmpeg2/convert/rgb.c
index 8863b0b..db6f4e3 100644
--- a/libmpeg2/convert/rgb.c
+++ b/libmpeg2/convert/rgb.c
@@ -499,7 +499,7 @@ static int rgb_internal (mpeg2convert_rgb_order_t order, unsigned int bpp,
     int convert420 = 0;
     int rgb_stride_min = ((bpp + 7) >> 3) * seq->width;
 
-#ifdef ARCH_X86
+#if !defined(_MSC_VER) && defined(ARCH_X86)
     if (!copy && (accel & MPEG2_ACCEL_X86_MMXEXT)) {
 	convert420 = 0;
 	copy = mpeg2convert_rgb_mmxext (order, bpp, seq);
diff --git a/libmpeg2/cpu_accel.c b/libmpeg2/cpu_accel.c
index 9b24610..a922df1 100644
--- a/libmpeg2/cpu_accel.c
+++ b/libmpeg2/cpu_accel.c
@@ -29,7 +29,7 @@
 #include "attributes.h"
 #include "mpeg2_internal.h"
 
-#if defined(ARCH_X86) || defined(ARCH_X86_64)
+#if !defined(_MSC_VER) && (defined(ARCH_X86) || defined(ARCH_X86_64))
 static inline uint32_t arch_accel (uint32_t accel)
 {
     if (accel & (MPEG2_ACCEL_X86_3DNOW | MPEG2_ACCEL_X86_MMXEXT))
@@ -253,7 +253,7 @@ static inline uint32_t arch_accel (uint32_t accel)
 
 uint32_t mpeg2_detect_accel (uint32_t accel)
 {
-#if defined (ARCH_X86) || defined (ARCH_X86_64) || defined (ARCH_PPC) || defined (ARCH_ALPHA) || defined (ARCH_SPARC)
+#if !defined(_MSC_VER) && (defined (ARCH_X86) || defined (ARCH_X86_64) || defined (ARCH_PPC) || defined (ARCH_ALPHA) || defined (ARCH_SPARC))
     accel = arch_accel (accel);
 #endif
     return accel;
diff --git a/libmpeg2/cpu_state.c b/libmpeg2/cpu_state.c
index 2f2f64a..f4966c1 100644
--- a/libmpeg2/cpu_state.c
+++ b/libmpeg2/cpu_state.c
@@ -36,7 +36,7 @@
 void (* mpeg2_cpu_state_save) (cpu_state_t * state) = NULL;
 void (* mpeg2_cpu_state_restore) (cpu_state_t * state) = NULL;
 
-#if defined(ARCH_X86) || defined(ARCH_X86_64)
+#if !defined(_MSC_VER) && (defined(ARCH_X86) || defined(ARCH_X86_64))
 static void state_restore_mmx (cpu_state_t * state)
 {
     emms ();
@@ -115,7 +115,7 @@ static void state_restore_altivec (cpu_state_t * state)
 
 void mpeg2_cpu_state_init (uint32_t accel)
 {
-#if defined(ARCH_X86) || defined(ARCH_X86_64)
+#if !defined(_MSC_VER) && (defined(ARCH_X86) || defined(ARCH_X86_64))
     if (accel & MPEG2_ACCEL_X86_MMX) {
 	mpeg2_cpu_state_restore = state_restore_mmx;
     }
diff --git a/libmpeg2/idct.c b/libmpeg2/idct.c
index 81c57e0..a057bf7 100644
--- a/libmpeg2/idct.c
+++ b/libmpeg2/idct.c
@@ -235,7 +235,7 @@ static void mpeg2_idct_add_c (const int last, int16_t * block,
 
 void mpeg2_idct_init (uint32_t accel)
 {
-#ifdef ARCH_X86
+#if !defined(_MSC_VER) && defined(ARCH_X86)
     if (accel & MPEG2_ACCEL_X86_SSE2) {
 	mpeg2_idct_copy = mpeg2_idct_copy_sse2;
 	mpeg2_idct_add = mpeg2_idct_add_sse2;
diff --git a/libmpeg2/motion_comp.c b/libmpeg2/motion_comp.c
index 7aed113..b00a32d 100644
--- a/libmpeg2/motion_comp.c
+++ b/libmpeg2/motion_comp.c
@@ -33,7 +33,7 @@ mpeg2_mc_t mpeg2_mc;
 
 void mpeg2_mc_init (uint32_t accel)
 {
-#ifdef ARCH_X86
+#if !defined(_MSC_VER) && defined(ARCH_X86)
     if (accel & MPEG2_ACCEL_X86_MMXEXT)
 	mpeg2_mc = mpeg2_mc_mmxext;
     else if (accel & MPEG2_ACCEL_X86_3DNOW)
diff --git a/libvo/video_out_dx.c b/libvo/video_out_dx.c
index 36de68a..0797cdc 100644
--- a/libvo/video_out_dx.c
+++ b/libvo/video_out_dx.c
@@ -82,7 +82,7 @@ static void update_overlay (dx_instance_t * instance)
 				       dwFlags, &ddofx);
 }
 
-static long FAR PASCAL event_procedure (HWND hwnd, UINT message,
+static LRESULT FAR PASCAL event_procedure (HWND hwnd, UINT message,
 					WPARAM wParam, LPARAM lParam)
 {
     RECT rect_window;
@@ -92,7 +92,7 @@ static long FAR PASCAL event_procedure (HWND hwnd, UINT message,
     switch (message) {
 
     case WM_WINDOWPOSCHANGED:
-	instance = (dx_instance_t *) GetWindowLong (hwnd, GWL_USERDATA);
+	instance = (dx_instance_t *) GetWindowLongPtr (hwnd, GWLP_USERDATA);
 
 	/* update the window position and size */
 	point_window.x = 0;
@@ -173,7 +173,7 @@ static int create_window (dx_instance_t * instance)
     /* store a directx_instance pointer into the window local storage
      * (for later use in event_handler).
      * We need to use SetWindowLongPtr when it is available in mingw */
-    SetWindowLong (instance->window, GWL_USERDATA, (LONG) instance);
+    SetWindowLongPtr (instance->window, GWLP_USERDATA, (LONG_PTR) instance);
 
     ShowWindow (instance->window, SW_SHOW);
 
diff --git a/vc++/config.h b/vc++/config.h
index 93719f0..a03cce6 100644
--- a/vc++/config.h
+++ b/vc++/config.h
@@ -16,7 +16,9 @@
 /* #undef ARCH_SPARC */
 
 /* x86 architecture */
+#if defined(_M_AMD64) || defined(_M_IX86)
 #define ARCH_X86
+#endif
 
 /* maximum supported data alignment */
 /* #undef ATTRIBUTE_ALIGNED_MAX */
-- 
2.25.0

