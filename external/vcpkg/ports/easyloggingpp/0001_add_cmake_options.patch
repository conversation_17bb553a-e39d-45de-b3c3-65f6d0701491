diff --git a/CMakeLists.txt b/CMakeLists.txt
index 8604a54..e08df91 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -25,6 +25,10 @@ option(test "Build all tests" OFF)
 option(build_static_lib "Build easyloggingpp as a static library" OFF)
 option(lib_utc_datetime "Build library with UTC date/time logging" OFF)
 
+option(no_default_logfile "Do not write to default log file \"myeasylog.log\" (define ELPP_NO_DEFAULT_LOG_FILE)" OFF)
+option(thread_safe "Build easyloggingpp thread safe (define ELPP_THREAD_SAFE)" OFF)
+option(use_std_threads "Use standard library thread synchronization (define ELPP_FORCE_USE_STD_THREAD)" OFF)
+
 set(ELPP_MAJOR_VERSION "9")
 set(ELPP_MINOR_VERSION "96")
 set(ELPP_PATCH_VERSION "7")
@@ -57,6 +61,18 @@ if (build_static_lib)
                 add_definitions(-DELPP_UTC_DATETIME)
         endif()
 
+        if (no_default_logfile)
+                add_definitions(-DELPP_NO_DEFAULT_LOG_FILE)
+        endif()
+
+        if (thread_safe)
+                add_definitions(-DELPP_THREAD_SAFE)
+        endif()
+
+        if (use_std_threads)
+                add_definitions(-DELPP_FORCE_USE_STD_THREAD)
+        endif()
+
         require_cpp14()
         add_library(easyloggingpp STATIC src/easylogging++.cc)
         set_property(TARGET easyloggingpp PROPERTY POSITION_INDEPENDENT_CODE ON)
