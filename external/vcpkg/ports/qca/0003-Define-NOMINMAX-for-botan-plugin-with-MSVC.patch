From f32f5ae8b8b49653bfff87f2f882862bcaa8c3f1 Mon Sep 17 00:00:00 2001
From: =?UTF-8?q?=D9=85=D9=87=D8=AF=D9=8A=20=D8=B4=D9=8A=D9=86=D9=88=D9=86?=
 =?UTF-8?q?=20=28Mehdi=20Chinoune=29?= <<EMAIL>>
Date: Mon, 20 Mar 2023 16:21:18 +0100
Subject: [PATCH] Define NOMINMAX to fix building qca-botan plugin with MSVC

---
 plugins/qca-botan/CMakeLists.txt | 4 ++++
 1 file changed, 4 insertions(+)

diff --git a/plugins/qca-botan/CMakeLists.txt b/plugins/qca-botan/CMakeLists.txt
index 11c0d20..9b8b978 100644
--- a/plugins/qca-botan/CMakeLists.txt
+++ b/plugins/qca-botan/CMakeLists.txt
@@ -11,6 +11,10 @@ if(BOTAN_FOUND)
   set(QCA_BOTAN_SOURCES qca-botan.cpp)
   add_library(qca-botan ${PLUGIN_TYPE} ${QCA_BOTAN_SOURCES})
 
+  if(MSVC)
+    target_compile_definitions(qca-botan PRIVATE NOMINMAX)
+  endif()
+
   if(APPLE AND ${PLUGIN_TYPE} STREQUAL "MODULE")
     set_property(TARGET qca-botan  PROPERTY SUFFIX ".dylib")
   endif()
-- 
2.40.0.windows.1

