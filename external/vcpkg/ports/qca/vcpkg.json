{"name": "qca", "version": "2.3.7", "port-version": 3, "description": "Qt Cryptographic Architecture (QCA).", "homepage": "https://userbase.kde.org/QCA", "dependencies": [{"name": "qt5compat", "default-features": false}, {"name": "qtbase", "default-features": false}, {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "default-features": ["botan"], "features": {"botan": {"description": "Build with botan", "dependencies": ["botan"]}, "ossl": {"description": "Build with openssl", "dependencies": ["openssl"]}}}