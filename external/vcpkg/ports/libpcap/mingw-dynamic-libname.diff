diff --git a/CMakeLists.txt b/CMakeLists.txt
index 88179b0..51a2732 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -3222,10 +3222,6 @@ if(WIN32)
         # For compatibility, build the shared library without the "lib" prefix on
         # MinGW as well.
         #
-        set_target_properties(${LIBRARY_NAME} PROPERTIES
-            PREFIX ""
-            OUTPUT_NAME "${LIBRARY_NAME}"
-        )
         set_target_properties(${LIBRARY_NAME}_static PROPERTIES
             OUTPUT_NAME "${LIBRARY_NAME}"
         )
