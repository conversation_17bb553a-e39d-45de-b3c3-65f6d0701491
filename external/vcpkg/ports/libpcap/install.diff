diff --git a/CMakeLists.txt b/CMakeLists.txt
index 9012ef4..88179b0 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -798,6 +798,7 @@ if(WIN32)
     cmake_pop_check_state()
     if(LIBWS2_32_HAS_GETADDRINFO)
         set(PCAP_LINK_LIBRARIES ws2_32 ${PCAP_LINK_LIBRARIES})
+        set(LIBS "-lws2_32 ${LIBS}")
     else(LIBWS2_32_HAS_GETADDRINFO)
         message(FATAL_ERROR "getaddrinfo is required, but wasn't found")
     endif(LIBWS2_32_HAS_GETADDRINFO)
@@ -3405,7 +3406,8 @@ set(MANMISC_EXPAND
 )
 
 if(BUILD_SHARED_LIBS)
-    set(LIBRARIES_TO_INSTALL "${LIBRARY_NAME}" "${LIBRARY_NAME_STATIC}")
+    set(LIBRARIES_TO_INSTALL "${LIBRARY_NAME}")
+    set_target_properties(${LIBRARY_NAME_STATIC} PROPERTIES EXCLUDE_FROM_ALL 1)
 else(BUILD_SHARED_LIBS)
     set(LIBRARIES_TO_INSTALL "${LIBRARY_NAME_STATIC}")
 endif(BUILD_SHARED_LIBS)
@@ -3416,7 +3418,7 @@ if(WIN32 OR CYGWIN OR MSYS)
     # the target is Windows; would there ever be a case where
     # CYGWIN or MSYS are set but WIN32 *isn't* set?
     #
-    if(MSVC AND CMAKE_SIZEOF_VOID_P EQUAL 8)
+    if(0)
         #
         # Install 64-bit code built with MSVC in the x64 subdirectories,
         # as that's where it expects it to be.
@@ -3463,7 +3465,10 @@ install(FILES ${CMAKE_CURRENT_SOURCE_DIR}/pcap-namedb.h DESTINATION ${CMAKE_INST
 
 # On UN*X, and on Windows when not using MSVC, generate libpcap.pc and
 # pcap-config and process man pages and arrange that they be installed.
-if(NOT MSVC)
+if(1)
+    if(MSVC AND NOT BUILD_SHARED_LIBS)
+        string(APPEND PACKAGE_NAME "_static")
+    endif()
     set(prefix ${CMAKE_INSTALL_PREFIX})
     set(exec_prefix "\${prefix}")
     set(includedir "\${prefix}/include")
@@ -3533,8 +3538,8 @@ if(NOT MSVC)
     endif()
     configure_file(${CMAKE_CURRENT_SOURCE_DIR}/pcap-config.in ${CMAKE_CURRENT_BINARY_DIR}/pcap-config @ONLY)
     configure_file(${CMAKE_CURRENT_SOURCE_DIR}/libpcap.pc.in ${CMAKE_CURRENT_BINARY_DIR}/libpcap.pc @ONLY)
-    install(PROGRAMS ${CMAKE_CURRENT_BINARY_DIR}/pcap-config DESTINATION bin)
     install(FILES ${CMAKE_CURRENT_BINARY_DIR}/libpcap.pc DESTINATION lib/pkgconfig)
+elseif(0)
 
     #
     # Man pages.
