diff --git a/src/library/CMakeLists.txt b/src/library/CMakeLists.txt
index f2d5a88..8f84133 100644
--- a/src/library/CMakeLists.txt
+++ b/src/library/CMakeLists.txt
@@ -910,11 +910,6 @@ endif( )
 
 include( InstallRequiredSystemLibraries )
 
-# Install necessary runtime files for debug builds
-install(    PROGRAMS ${CMAKE_INSTALL_SYSTEM_RUNTIME_LIBS}
-            CONFIGURATIONS Debug
-            DESTINATION ${CLBLAS_RUNTIME_DESTINATION} )
-
 # Install all *.pdb files for debug builds
 install(    DIRECTORY ${PROJECT_BINARY_DIR}/staging/
             DESTINATION ${CLBLAS_RUNTIME_DESTINATION}
