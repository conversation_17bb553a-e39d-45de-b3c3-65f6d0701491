diff --git a/CMakeLists.txt b/CMakeLists.txt
index 746afa7..803b82e 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -34,11 +34,6 @@ cmake_policy(VERSION 3.21)
 # # Project kickstart
 # Includes a bunch of basic flags and utilities shared across projects
 # See more at the github repository link below
-include(FetchContent)
-FetchContent_Declare(ztd.cmake
-	GIT_REPOSITORY https://github.com/soasis/cmake.git
-	GIT_TAG main)
-FetchContent_MakeAvailable(ztd.cmake)
 set(CMAKE_PROJECT_INCLUDE ${ZTD_CMAKE_PROJECT_PRELUDE})
 
 # # Project declaration
@@ -50,6 +45,8 @@ project(ztd.idk
 	HOMEPAGE_URL "https://ztdidk.rtfd.io/"
 	LANGUAGES CXX C)
 
+include(GNUInstallDirs)
+include(CMakePackageConfigHelpers)
 if(ZTD_IDK_READTHEDOCS)
 	# ReadTheDocs seems unable to handle the include at the project level: something must be going wrong?
 	include(CheckCXXCompilerFlag)
@@ -89,9 +86,6 @@ if(ZTD_IDK_IS_TOP_LEVEL_PROJECT)
 	set(CMAKE_LIBRARY_OUTPUT_DIRECTORY "${CMAKE_CURRENT_BINARY_DIR}/bin")
 	set(CMAKE_RUNTIME_OUTPUT_DIRECTORY "${CMAKE_CURRENT_BINARY_DIR}/bin")
 
-	if(NOT DEFINED CMAKE_CXX_STANDARD)
-		set(CMAKE_CXX_STANDARD 17)
-	endif()
 
 	if(NOT DEFINED CMAKE_C_STANDARD)
 		set(CMAKE_C_STANDARD 11)
@@ -133,6 +127,7 @@ endif()
 # ztd.version
 add_library(ztd.version INTERFACE)
 add_library(ztd::version ALIAS ztd.version)
+target_compile_features(ztd.version INTERFACE cxx_std_20)
 target_include_directories(ztd.version
 	INTERFACE
 	$<BUILD_INTERFACE:${PROJECT_SOURCE_DIR}/include>
@@ -143,20 +138,32 @@ install(DIRECTORY include/
 	${CMAKE_INSTALL_INCLUDEDIR}
 )
 
+install(TARGETS ztd.version
+        EXPORT ztd.version-targets
+        DESTINATION lib)
 configure_package_config_file(
 	cmake/ztd.version-config.cmake.in
 	"${CMAKE_CURRENT_BINARY_DIR}/cmake/ztd.version/ztd.version-config.cmake"
-	INSTALL_DESTINATION lib/cmake/ztd.version
+	INSTALL_DESTINATION share/ztd.version
 	NO_CHECK_REQUIRED_COMPONENTS_MACRO
 )
 write_basic_package_version_file(
 	"${CMAKE_CURRENT_BINARY_DIR}/cmake/ztd.version/ztd.version-config-version.cmake"
 	COMPATIBILITY SameMajorVersion
 )
+install(FILES
+  ${CMAKE_CURRENT_BINARY_DIR}/cmake/ztd.version/ztd.version-config.cmake
+  ${CMAKE_CURRENT_BINARY_DIR}/cmake/ztd.version/ztd.version-config-version.cmake
+  DESTINATION share/ztd.version
+  )
 export(TARGETS ztd.version
 	FILE
 	"${CMAKE_CURRENT_BINARY_DIR}/cmake/ztd.version/ztd.version-targets.cmake"
 )
+install(EXPORT ztd.version-targets
+  FILE ztd.version-targets.cmake
+  DESTINATION share/ztd.version
+)
 
 # ztd.tag_invoke
 file(GLOB_RECURSE ztd.tag_invoke.includes
@@ -166,6 +173,7 @@ file(GLOB_RECURSE ztd.tag_invoke.includes
 )
 
 add_library(ztd.tag_invoke INTERFACE)
+target_compile_features(ztd.tag_invoke INTERFACE cxx_std_20)
 add_library(ztd::tag_invoke ALIAS ztd.tag_invoke)
 target_include_directories(ztd.tag_invoke
 	INTERFACE
@@ -179,10 +187,14 @@ install(DIRECTORY include/
 	DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}
 )
 
+install(TARGETS ztd.tag_invoke
+        EXPORT ztd.tag_invoke-targets
+        DESTINATION lib)
+
 configure_package_config_file(
 	cmake/ztd.tag_invoke-config.cmake.in
 	"${CMAKE_CURRENT_BINARY_DIR}/cmake/ztd.tag_invoke/ztd.tag_invoke-config.cmake"
-	INSTALL_DESTINATION lib/cmake/ztd.tag_invoke
+	INSTALL_DESTINATION share/ztd.tag_invoke
 	NO_CHECK_REQUIRED_COMPONENTS_MACRO
 )
 write_basic_package_version_file(
@@ -193,6 +205,16 @@ export(TARGETS ztd.tag_invoke
 	FILE
 	"${CMAKE_CURRENT_BINARY_DIR}/cmake/ztd.tag_invoke/ztd.tag_invoke-targets.cmake"
 )
+install(FILES
+  ${CMAKE_CURRENT_BINARY_DIR}/cmake/ztd.tag_invoke/ztd.tag_invoke-config.cmake
+  ${CMAKE_CURRENT_BINARY_DIR}/cmake/ztd.tag_invoke/ztd.tag_invoke-config-version.cmake
+  DESTINATION share/ztd.tag_invoke
+)
+install(EXPORT ztd.tag_invoke-targets
+  FILE ztd.tag_invoke-targets.cmake
+  DESTINATION share/ztd.tag_invoke
+)
+
 
 # ztd.idk
 file(GLOB_RECURSE ztd.idk.includes
@@ -241,7 +263,9 @@ if(ZTD_IDK_IS_TOP_LEVEL_PROJECT)
 	)
 endif()
 
-install(TARGETS ztd.idk)
+install(TARGETS ztd.idk
+        EXPORT ztd.idk-targets
+        DESTINATION lib)
 install(DIRECTORY include/
 	DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}
 )
@@ -249,7 +273,7 @@ install(DIRECTORY include/
 configure_package_config_file(
 	cmake/ztd.idk-config.cmake.in
 	"${CMAKE_CURRENT_BINARY_DIR}/cmake/ztd.idk/ztd.idk-config.cmake"
-	INSTALL_DESTINATION lib/cmake/ztd.idk
+	INSTALL_DESTINATION share/ztd.idk
 	NO_CHECK_REQUIRED_COMPONENTS_MACRO
 )
 write_basic_package_version_file(
@@ -261,11 +285,17 @@ export(TARGETS ztd.idk
 	"${CMAKE_CURRENT_BINARY_DIR}/cmake/ztd.idk/ztd.idk-targets.cmake"
 )
 
-install(
-	DIRECTORY "${CMAKE_CURRENT_BINARY_DIR}/cmake"
-	TYPE DATA
+install(FILES
+  ${CMAKE_CURRENT_BINARY_DIR}/cmake/ztd.idk/ztd.idk-config.cmake
+  ${CMAKE_CURRENT_BINARY_DIR}/cmake/ztd.idk/ztd.idk-config-version.cmake
+  DESTINATION share/ztd.idk/
+  )
+install(EXPORT ztd.idk-targets
+  FILE ztd.idk-targets.cmake
+  DESTINATION share/ztd.idk/
 )
 
+
 if(ZTD_IDK_GENERATE_SINGLE)
 	add_subdirectory(single)
 endif()
diff --git a/cmake/ztd.idk-config.cmake.in b/cmake/ztd.idk-config.cmake.in
index 4ceb0ec..8377221 100644
--- a/cmake/ztd.idk-config.cmake.in
+++ b/cmake/ztd.idk-config.cmake.in
@@ -1,7 +1,8 @@
 @PACKAGE_INIT@
 
-find_package(ztd.version CONFIG REQUIRED)
-find_package(ztd.tag_invoke CONFIG REQUIRED)
+include(CMakeFindDependencyMacro)
+find_dependency(ztd.version CONFIG )
+find_dependency(ztd.tag_invoke CONFIG)
 include(${CMAKE_CURRENT_LIST_DIR}/ztd.idk-targets.cmake)
 
 if (TARGET ztd.idk)
diff --git a/cmake/ztd.tag_invoke-config.cmake.in b/cmake/ztd.tag_invoke-config.cmake.in
index 504c60b..ee2f5cd 100644
--- a/cmake/ztd.tag_invoke-config.cmake.in
+++ b/cmake/ztd.tag_invoke-config.cmake.in
@@ -1,6 +1,6 @@
 @PACKAGE_INIT@
-
-find_package(ztd.version CONFIG REQUIRED)
+include(CMakeFindDependencyMacro)
+find_dependency(ztd.version CONFIG )
 include(${CMAKE_CURRENT_LIST_DIR}/ztd.tag_invoke-targets.cmake)
 
 if (TARGET ztd.tag_invoke)
