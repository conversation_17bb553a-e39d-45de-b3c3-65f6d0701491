--- a/PQP_v1.3/src/PQP_Compile.h
+++ b/PQP_v1.3/src/PQP_Compile.h
@@ -44,10 +44,10 @@
 // prevents compiler warnings when PQP_REAL is float
 
 #include <math.h>
-inline float sqrt(float x) { return (float)sqrt((double)x); }
-inline float cos(float x) { return (float)cos((double)x); }
-inline float sin(float x) { return (float)sin((double)x); }
-inline float fabs(float x) { return (float)fabs((double)x); }
+//inline float sqrt(float x) { return (float)sqrt((double)x); }
+//inline float cos(float x) { return (float)cos((double)x); }
+//inline float sin(float x) { return (float)sin((double)x); }
+//inline float fabs(float x) { return (float)fabs((double)x); }
 
 //-------------------------------------------------------------------------
 //
