{"name": "libzippp", "version": "7.1-1.10.1", "description": "Simple basic C++ wrapper around the libzip library. It is meant to be a portable and easy-to-use library for ZIP handling", "homepage": "https://github.com/ctabin/libzippp", "license": "BSD-3-<PERSON><PERSON>", "dependencies": [{"name": "libzip", "default-features": false, "features": ["bzip2"]}, {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}, "zlib"], "features": {"encryption": {"description": "Support encryption"}}}