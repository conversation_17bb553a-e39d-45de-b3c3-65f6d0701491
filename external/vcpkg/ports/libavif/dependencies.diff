diff --git a/CMakeLists.txt b/CMakeLists.txt
index a28e6c3..aaeb5d2 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -248,6 +248,7 @@ if(AVIF_LIBYUV_ENABLED)
     target_compile_definitions(avif_obj PRIVATE -DAVIF_LIBYUV_ENABLED=1)
     avif_target_link_library(yuv::yuv)
     set(AVIF_PKG_CONFIG_EXTRA_LIBS_PRIVATE "${AVIF_PKG_CONFIG_EXTRA_LIBS_PRIVATE} -lyuv")
+    set(AVIF_PKG_CONFIG_EXTRA_REQUIRES_PRIVATE "${AVIF_PKG_CONFIG_EXTRA_REQUIRES_PRIVATE} libjpeg")
     set(AVIF_LIB_USE_CXX ON)
 endif(AVIF_LIBYUV_ENABLED)
 
@@ -849,13 +850,38 @@ if(NOT SKIP_INSTALL_LIBRARIES AND NOT SKIP_INSTALL_ALL)
 
     # Enable CMake configs in VCPKG mode
     if(BUILD_SHARED_LIBS OR VCPKG_TARGET_TRIPLET)
-        install(EXPORT ${PROJECT_NAME}-config DESTINATION ${CMAKE_INSTALL_LIBDIR}/cmake/${PROJECT_NAME})
+        install(EXPORT ${PROJECT_NAME}-config DESTINATION ${CMAKE_INSTALL_LIBDIR}/cmake/${PROJECT_NAME} FILE ${PROJECT_NAME}-targets.cmake)
+        if(NOT BUILD_SHARED_LIBS)
+            target_include_directories(avif_static PUBLIC $<INSTALL_INTERFACE:include>)
+            set(avif_static_link_libs yuv)
+            set(avif_find_dependencies "find_dependency(libyuv CONFIG)\n")
+            if(AVIF_CODEC_AOM_ENABLED)
+                find_package(aom CONFIG REQUIRED)
+                list(APPEND avif_static_link_libs unofficial::aom)
+                string(APPEND avif_find_dependencies "find_dependency(aom CONFIG)\n")
+            endif()
+            if(AVIF_CODEC_DAV1D_ENABLED)
+                list(APPEND avif_static_link_libs ${DAV1D_LIBRARIES} ${CMAKE_DL_LIBS})
+            endif()
+            if(UNIX OR MINGW)
+                list(APPEND avif_static_link_libs Threads::Threads m)
+                string(APPEND avif_find_dependencies "set(THREADS_PREFER_PTHREAD_FLAG ON)\nfind_dependency(Threads)\n")
+            endif()
+            target_link_libraries(avif_static INTERFACE ${avif_static_link_libs})
+        endif()
+        file(WRITE "${CMAKE_CURRENT_BINARY_DIR}/${PROJECT_NAME}-config.cmake"
+"# Generated by vcpkg
+include(CMakeFindDependencyMacro)
+${avif_find_dependencies}
+include(\"\${CMAKE_CURRENT_LIST_DIR}/${PROJECT_NAME}-targets.cmake\")
+")
 
         include(CMakePackageConfigHelpers)
         write_basic_package_version_file(
             ${PROJECT_NAME}-config-version.cmake VERSION ${PROJECT_VERSION} COMPATIBILITY SameMajorVersion
         )
         install(FILES ${CMAKE_CURRENT_BINARY_DIR}/${PROJECT_NAME}-config-version.cmake
+                      ${CMAKE_CURRENT_BINARY_DIR}/${PROJECT_NAME}-config.cmake
                 DESTINATION ${CMAKE_INSTALL_LIBDIR}/cmake/${PROJECT_NAME}
         )
     endif()
