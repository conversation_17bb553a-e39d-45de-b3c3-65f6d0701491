{"name": "ctp", "version-string": "6.6.1_P1_20210406_se", "port-version": 4, "description": "The Comprehensive Transaction Platform (CTP) is a future brokerage management system developed specially for futures companies. CTP API client library allows users to connect to CTP gateway for algorithmic trading.", "homepage": "http://www.sfit.com.cn/index.htm", "supports": "!osx & !uwp & !arm & !(static & staticcrt)", "features": {"datacollect": {"description": "Data collect support of CTP"}}}