diff --git a/CMakeLists.txt b/CMakeLists.txt
index a560278..3184d2c 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -239,6 +239,11 @@ if(MSVC AND MSVC_VERSION GREATER 1899)
 	SET(CMAKE_CXX_FLAGS "/EHsc -D_CRT_STDIO_ISO_WIDE_SPECIFIERS ${CMAKE_CXX_FLAGS}")
 	SET(CMAKE_C_FLAGS "-D_CRT_STDIO_ISO_WIDE_SPECIFIERS ${CMAKE_C_FLAGS}")
 endif(MSVC AND MSVC_VERSION GREATER 1899)
+# _CRT_STDIO_ISO_WIDE_SPECIFIERS was never officially supported
+# and breaks link mathgl widget libs against their GUI libs:
+# error LNK2038: mismatch detected for '_CRT_STDIO_ISO_WIDE_SPECIFIERS': value '0' doesn't match value '1' in fltk.cpp.obj
+string(REPLACE "-D_CRT_STDIO_ISO_WIDE_SPECIFIERS" "-DVCPKG_MSVC_WPRINTF" CMAKE_C_FLAGS "${CMAKE_C_FLAGS}")
+string(REPLACE "-D_CRT_STDIO_ISO_WIDE_SPECIFIERS" "-DVCPKG_MSVC_WPRINTF" CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS}")
 
 include(CheckCXXSourceCompiles)
 
diff --git a/src/export_2d.cpp b/src/export_2d.cpp
index bd07404..65f5fdf 100644
--- a/src/export_2d.cpp
+++ b/src/export_2d.cpp
@@ -17,6 +17,13 @@
  *   Free Software Foundation, Inc.,                                       *
  *   59 Temple Place - Suite 330, Boston, MA  02111-1307, USA.             *
  ***************************************************************************/
+// wprintf format specifier for char*.
+// Inject by replacing  (wprintf.*?[^"])" PERCENT_S "  with  $1" PERCENT_S "
+#ifdef VCPKG_MSVC_WPRINTF
+#define PERCENT_S "%S"
+#else
+#define PERCENT_S "%s"
+#endif
 #include "mgl2/canvas.h"
 #include "mgl2/canvas_cf.h"
 #include "mgl2/font.h"
@@ -661,7 +668,7 @@ void MGL_EXPORT mgl_write_tex(HMGL gr, const char *fname,const char *descr)
 	FILE *fp = fopen(fname,"w");
 	if(!fp)		{	gr->SetWarn(mglWarnOpen,fname);	return;	}
 	const std::string loc = setlocale(LC_NUMERIC, "C");	fwide(fp,1);
-	fwprintf(fp, L"%% Created by MathGL library\n%% Title: %s\n\n",descr?descr:fname);
+	fwprintf(fp, L"%% Created by MathGL library\n%% Title: " PERCENT_S "\n\n",descr?descr:fname);
 	// provide marks
 	fwprintf(fp, L"\\providecommand{\\mglp}[4]{\\draw[#3] (#1-#4, #2) -- (#1+#4,#2) (#1,#2-#4) -- (#1,#2+#4);}\n");
 	fwprintf(fp, L"\\providecommand{\\mglx}[4]{\\draw[#3] (#1-#4, #2-#4) -- (#1+#4,#2+#4) (#1+#4,#2-#4) -- (#1-#4,#2+#4);}\n");
@@ -707,47 +714,47 @@ void MGL_EXPORT mgl_write_tex(HMGL gr, const char *fname,const char *descr)
 			switch(q.n4)	// NOTE: no thickness for marks in TeX
 			{
 				case 'P':
-					fwprintf(fp, L"\\mglp{%.4g}{%.4g}{%s}{%.4g} \\mgls{%.4g}{%.4g}{%s}{%.4g}\n", x,y,cname,s,x,y,cname,s);	break;
+					fwprintf(fp, L"\\mglp{%.4g}{%.4g}{" PERCENT_S "}{%.4g} \\mgls{%.4g}{%.4g}{" PERCENT_S "}{%.4g}\n", x,y,cname,s,x,y,cname,s);	break;
 				case 'X':
-					fwprintf(fp, L"\\mglx{%.4g}{%.4g}{%s}{%.4g} \\mgls{%.4g}{%.4g}{%s}{%.4g}\n", x,y,cname,s,x,y,cname,s);	break;
+					fwprintf(fp, L"\\mglx{%.4g}{%.4g}{" PERCENT_S "}{%.4g} \\mgls{%.4g}{%.4g}{" PERCENT_S "}{%.4g}\n", x,y,cname,s,x,y,cname,s);	break;
 				case 'C':
-					fwprintf(fp, L"\\mglc{%.4g}{%.4g}{%s}{%.4g} \\mglo{%.4g}{%.4g}{%s}{%.4g}\n", x,y,cname,s,x,y,cname,s);	break;
-				case '+':	fwprintf(fp, L"\\mglp{%.4g}{%.4g}{%s}{%.4g}\n", x,y,cname,s);	break;
-				case 'x':	fwprintf(fp, L"\\mglx{%.4g}{%.4g}{%s}{%.4g}\n", x,y,cname,s);	break;
-				case 's':	fwprintf(fp, L"\\mgls{%.4g}{%.4g}{%s}{%.4g}\n", x,y,cname,s);	break;
-				case 'S':	fwprintf(fp, L"\\mglS{%.4g}{%.4g}{%s}{%.4g}\n", x,y,cname,s);	break;
-				case 'd':	fwprintf(fp, L"\\mgld{%.4g}{%.4g}{%s}{%.4g}\n", x,y,cname,s);	break;
-				case 'D':	fwprintf(fp, L"\\mglD{%.4g}{%.4g}{%s}{%.4g}\n", x,y,cname,s);	break;
-				case '^':	fwprintf(fp, L"\\mglt{%.4g}{%.4g}{%s}{%.4g}\n", x,y,cname,s);	break;
-				case 'T':	fwprintf(fp, L"\\mglT{%.4g}{%.4g}{%s}{%.4g}\n", x,y,cname,s);	break;
-				case 'v':	fwprintf(fp, L"\\mglv{%.4g}{%.4g}{%s}{%.4g}\n", x,y,cname,s);	break;
-				case 'V':	fwprintf(fp, L"\\mglV{%.4g}{%.4g}{%s}{%.4g}\n", x,y,cname,s);	break;
-				case '<':	fwprintf(fp, L"\\mgll{%.4g}{%.4g}{%s}{%.4g}\n", x,y,cname,s);	break;
-				case 'L':	fwprintf(fp, L"\\mglL{%.4g}{%.4g}{%s}{%.4g}\n", x,y,cname,s);	break;
-				case '>':	fwprintf(fp, L"\\mglr{%.4g}{%.4g}{%s}{%.4g}\n", x,y,cname,s);	break;
-				case 'R':	fwprintf(fp, L"\\mglR{%.4g}{%.4g}{%s}{%.4g}\n", x,y,cname,s);	break;
-				case 'Y':	fwprintf(fp, L"\\mglY{%.4g}{%.4g}{%s}{%.4g}\n", x,y,cname,s);	break;
-				case 'o':	fwprintf(fp, L"\\mglo{%.4g}{%.4g}{%s}{%.4g}\n", x,y,cname,s);	break;
-				case 'O':	fwprintf(fp, L"\\mglO{%.4g}{%.4g}{%s}{%.4g}\n", x,y,cname,s);	break;
-				case '*':	fwprintf(fp, L"\\mgla{%.4g}{%.4g}{%s}{%.4g}\n", x,y,cname,s);	break;
-				default:	fwprintf(fp, L"\\mglc{%.4g}{%.4g}{%s}\n", x,y,cname);	break;
+					fwprintf(fp, L"\\mglc{%.4g}{%.4g}{" PERCENT_S "}{%.4g} \\mglo{%.4g}{%.4g}{" PERCENT_S "}{%.4g}\n", x,y,cname,s,x,y,cname,s);	break;
+				case '+':	fwprintf(fp, L"\\mglp{%.4g}{%.4g}{" PERCENT_S "}{%.4g}\n", x,y,cname,s);	break;
+				case 'x':	fwprintf(fp, L"\\mglx{%.4g}{%.4g}{" PERCENT_S "}{%.4g}\n", x,y,cname,s);	break;
+				case 's':	fwprintf(fp, L"\\mgls{%.4g}{%.4g}{" PERCENT_S "}{%.4g}\n", x,y,cname,s);	break;
+				case 'S':	fwprintf(fp, L"\\mglS{%.4g}{%.4g}{" PERCENT_S "}{%.4g}\n", x,y,cname,s);	break;
+				case 'd':	fwprintf(fp, L"\\mgld{%.4g}{%.4g}{" PERCENT_S "}{%.4g}\n", x,y,cname,s);	break;
+				case 'D':	fwprintf(fp, L"\\mglD{%.4g}{%.4g}{" PERCENT_S "}{%.4g}\n", x,y,cname,s);	break;
+				case '^':	fwprintf(fp, L"\\mglt{%.4g}{%.4g}{" PERCENT_S "}{%.4g}\n", x,y,cname,s);	break;
+				case 'T':	fwprintf(fp, L"\\mglT{%.4g}{%.4g}{" PERCENT_S "}{%.4g}\n", x,y,cname,s);	break;
+				case 'v':	fwprintf(fp, L"\\mglv{%.4g}{%.4g}{" PERCENT_S "}{%.4g}\n", x,y,cname,s);	break;
+				case 'V':	fwprintf(fp, L"\\mglV{%.4g}{%.4g}{" PERCENT_S "}{%.4g}\n", x,y,cname,s);	break;
+				case '<':	fwprintf(fp, L"\\mgll{%.4g}{%.4g}{" PERCENT_S "}{%.4g}\n", x,y,cname,s);	break;
+				case 'L':	fwprintf(fp, L"\\mglL{%.4g}{%.4g}{" PERCENT_S "}{%.4g}\n", x,y,cname,s);	break;
+				case '>':	fwprintf(fp, L"\\mglr{%.4g}{%.4g}{" PERCENT_S "}{%.4g}\n", x,y,cname,s);	break;
+				case 'R':	fwprintf(fp, L"\\mglR{%.4g}{%.4g}{" PERCENT_S "}{%.4g}\n", x,y,cname,s);	break;
+				case 'Y':	fwprintf(fp, L"\\mglY{%.4g}{%.4g}{" PERCENT_S "}{%.4g}\n", x,y,cname,s);	break;
+				case 'o':	fwprintf(fp, L"\\mglo{%.4g}{%.4g}{" PERCENT_S "}{%.4g}\n", x,y,cname,s);	break;
+				case 'O':	fwprintf(fp, L"\\mglO{%.4g}{%.4g}{" PERCENT_S "}{%.4g}\n", x,y,cname,s);	break;
+				case '*':	fwprintf(fp, L"\\mgla{%.4g}{%.4g}{" PERCENT_S "}{%.4g}\n", x,y,cname,s);	break;
+				default:	fwprintf(fp, L"\\mglc{%.4g}{%.4g}{" PERCENT_S "}\n", x,y,cname);	break;
 			}
 		}
 		else if(q.type==2 && cp.r[3])
 		{
 			const mglPnt &p2=gr->GetPnt(q.n2), &p3=gr->GetPnt(q.n3);
 			if(cp.r[3]<255)
-				fwprintf(fp, L"\\fill[%s, fill opacity=%.4g] (%.4g,%.4g) -- (%.4g,%.4g) -- (%.4g,%.4g) -- cycle;\n", cname,cp.r[3]/255., x,y, p2.x/100,p2.y/100, p3.x/100,p3.y/100);
+				fwprintf(fp, L"\\fill[" PERCENT_S ", fill opacity=%.4g] (%.4g,%.4g) -- (%.4g,%.4g) -- (%.4g,%.4g) -- cycle;\n", cname,cp.r[3]/255., x,y, p2.x/100,p2.y/100, p3.x/100,p3.y/100);
 			else
-				fwprintf(fp, L"\\fill[%s, fill] (%.4g,%.4g) -- (%.4g,%.4g) -- (%.4g,%.4g) -- cycle;\n", cname, x,y, p2.x/100,p2.y/100, p3.x/100,p3.y/100);
+				fwprintf(fp, L"\\fill[" PERCENT_S ", fill] (%.4g,%.4g) -- (%.4g,%.4g) -- (%.4g,%.4g) -- cycle;\n", cname, x,y, p2.x/100,p2.y/100, p3.x/100,p3.y/100);
 		}
 		else if(q.type==3 && cp.r[3])
 		{
 			const mglPnt &p2=gr->GetPnt(q.n2), &p3=gr->GetPnt(q.n3), &p4=gr->GetPnt(q.n4);
 			if(cp.r[3]<255)
-				fwprintf(fp, L"\\fill[%s, fill opacity=%.4g] (%.4g,%.4g) -- (%.4g,%.4g) -- (%.4g,%.4g) -- (%.4g,%.4g) -- cycle;\n", cname,cp.r[3]/255., x,y, p2.x/100,p2.y/100, p4.x/100,p4.y/100, p3.x/100,p3.y/100);
+				fwprintf(fp, L"\\fill[" PERCENT_S ", fill opacity=%.4g] (%.4g,%.4g) -- (%.4g,%.4g) -- (%.4g,%.4g) -- (%.4g,%.4g) -- cycle;\n", cname,cp.r[3]/255., x,y, p2.x/100,p2.y/100, p4.x/100,p4.y/100, p3.x/100,p3.y/100);
 			else
-				fwprintf(fp, L"\\fill[%s, fill] (%.4g,%.4g) -- (%.4g,%.4g) -- (%.4g,%.4g) -- (%.4g,%.4g) -- cycle;\n", cname, x,y, p2.x/100,p2.y/100, p4.x/100,p4.y/100, p3.x/100,p3.y/100);
+				fwprintf(fp, L"\\fill[" PERCENT_S ", fill] (%.4g,%.4g) -- (%.4g,%.4g) -- (%.4g,%.4g) -- (%.4g,%.4g) -- cycle;\n", cname, x,y, p2.x/100,p2.y/100, p4.x/100,p4.y/100, p3.x/100,p3.y/100);
 
 		}
 		else if(q.type==1)	// lines
@@ -755,8 +762,8 @@ void MGL_EXPORT mgl_write_tex(HMGL gr, const char *fname,const char *descr)
 			//const char *dash[]={"", "8 8","4 4","1 3","7 4 1 4","3 2 1 2"};
 			const char *w[]={"semithick","thick","very thick","ultra thick"};
 			int iw=int(q.w-0.5);	if(iw>3)	iw=3;
-			if(iw<0)	fwprintf(fp,L"\\draw[%s] ",cname);
-			else		fwprintf(fp,L"\\draw[%s,%s] ",cname,w[iw]);
+			if(iw<0)	fwprintf(fp,L"\\draw[" PERCENT_S "] ",cname);
+			else		fwprintf(fp,L"\\draw[" PERCENT_S "," PERCENT_S "] ",cname,w[iw]);
 			// TODO: add line dashing
 			wp = q.w>1  ? q.w:1;	st = q.n3;
 			std::vector<long> ids = put_line(gr,i,wp,cp.c,st);
@@ -780,9 +787,9 @@ void MGL_EXPORT mgl_write_tex(HMGL gr, const char *fname,const char *descr)
 //			if(f&MGL_FONT_ITAL)	ss.append(",font=\\itshape");
 //			if(f&MGL_FONT_BOLD)	ss.append(",font=\\bfshape");
 			if(t.text.find('\\')!=std::string::npos || t.text.find('{')!=std::string::npos || t.text.find('_')!=std::string::npos || t.text.find('^')!=std::string::npos)
-				fwprintf(fp,L"\\draw[%s] (%.4g,%.4g) node[rotate=%.2g]{$%ls$};\n", ss.c_str(),x-dx,y-dy, -q.p, t.text.c_str());
+				fwprintf(fp,L"\\draw[" PERCENT_S "] (%.4g,%.4g) node[rotate=%.2g]{$%ls$};\n", ss.c_str(),x-dx,y-dy, -q.p, t.text.c_str());
 			else
-				fwprintf(fp,L"\\draw[%s] (%.4g,%.4g) node[rotate=%.2g]{%ls};\n", ss.c_str(),x-dx,y-dy, -q.p, t.text.c_str());
+				fwprintf(fp,L"\\draw[" PERCENT_S "] (%.4g,%.4g) node[rotate=%.2g]{%ls};\n", ss.c_str(),x-dx,y-dy, -q.p, t.text.c_str());
 		}
 	}
 	fwprintf(fp, L"\\end{tikzpicture}\n");
