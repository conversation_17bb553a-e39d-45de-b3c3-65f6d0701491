diff --git a/examples/CMakeLists.txt b/examples/CMakeLists.txt
index cd389bd..56ad515 100644
--- a/examples/CMakeLists.txt
+++ b/examples/CMakeLists.txt
@@ -26,7 +26,7 @@ endif(MGL_HAVE_GLUT)
 
 if(MGL_HAVE_WX)
 	include(${wxWidgets_USE_FILE})
-	add_executable(mgl_wx_example wnd_samples.cpp wx_example.cpp)
+	add_executable(mgl_wx_example WIN32 wnd_samples.cpp wx_example.cpp)
 	target_link_libraries(mgl_wx_example mgl-wx${link_type})
 endif(MGL_HAVE_WX)
 
diff --git a/examples/full_test.cpp b/examples/full_test.cpp
index 50fb6ae..6f3ef26 100644
--- a/examples/full_test.cpp
+++ b/examples/full_test.cpp
@@ -21,6 +21,14 @@
 #include <locale.h>
 #include <time.h>
 #include <getopt.h>
+#if defined(_UNICODE) && defined(getopt_long_only)
+#undef getopt_long_only
+#undef option
+#undef optarg
+#define getopt_long_only getopt_long_only_a
+#define option option_a
+#define optarg optarg_a
+#endif
 #ifdef WIN32
 #include <io.h>
 #include <direct.h>
