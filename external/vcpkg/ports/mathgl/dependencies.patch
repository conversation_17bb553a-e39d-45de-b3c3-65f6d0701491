diff --git a/CMakeLists.txt b/CMakeLists.txt
index 466e9bc..d99c1e4 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -441,9 +441,10 @@ else(enable-pthr-widget OR enable-pthread)
 endif(enable-pthr-widget OR enable-pthread)
 
 if(enable-arma)
-	find_package(Armadillo)
+	find_package(Armadillo CONFIG REQUIRED)
 	if(ARMADILLO_FOUND)
 		set(MGL_HAVE_ARMA 1)
+		list(APPEND MGL_DEP_LIBS armadillo)
 	else(ARMADILLO_FOUND)
 		set(MGL_HAVE_ARMA 0)
 	endif(ARMADILLO_FOUND)
@@ -548,7 +549,7 @@ endif(enable-hdf5)
 
 if(enable-jpeg)
 	set(MGL_HAVE_JPEG 1)
-	include(FindJPEG)
+	find_package(JPEG)
 	if(NOT JPEG_FOUND)
 		message(SEND_ERROR "Couldn't find JPEG library.")
 	endif(NOT JPEG_FOUND)
@@ -561,7 +562,7 @@ endif(enable-jpeg)
 
 if(enable-zlib)
 	set(MGL_HAVE_ZLIB 1)
-	include(FindZLIB)
+	find_package(ZLIB)
 	if(NOT ZLIB_FOUND)
 		message(SEND_ERROR "Couldn't find ZLib library.")
 	endif(NOT ZLIB_FOUND)
@@ -573,7 +574,7 @@ endif(enable-zlib)
 
 if(enable-freetype)
 	set(MGL_HAVE_FREETYPE 1)
-	include(FindFreetype)
+	find_package(Freetype)
 	if(NOT FREETYPE_FOUND)
 		message(SEND_ERROR "Couldn't find FreeType library.")
 	endif(NOT FREETYPE_FOUND)
@@ -588,7 +589,7 @@ if(enable-png)
 	if(NOT MGL_HAVE_ZLIB)
 		message(SEND_ERROR "You have to enable ZLib if you plan to use PNG export.")
 	endif(NOT MGL_HAVE_ZLIB)
-	include(FindPNG)
+	find_package(PNG)
 	if(NOT PNG_FOUND)
 		message(SEND_ERROR "Couldn't find PNG library.")
 	endif(NOT PNG_FOUND)
@@ -620,7 +621,7 @@ endif(enable-pdf)
 
 if(enable-gif)
 	set(MGL_HAVE_GIF 1)
-	include(FindGIF)
+	find_package(GIF)
 	if(NOT GIF_FOUND)
 		message(SEND_ERROR "Couldn't find GIF library.")
 	endif(NOT GIF_FOUND)
@@ -633,7 +634,7 @@ endif(enable-gif)
 if(enable-opengl)
 	set(MGL_HAVE_OPENGL 1)
 	set(OpenGL_GL_PREFERENCE LEGACY)
-	include(FindOpenGL)
+	find_package(OpenGL)
 	if(NOT OPENGL_FOUND)
 		message(SEND_ERROR "Couldn't find OpenGL libraries.")
 	endif(NOT OPENGL_FOUND)
@@ -648,7 +649,7 @@ if(enable-glut)
 	if(NOT MGL_HAVE_OPENGL)
 		message(SEND_ERROR "You have to enable OpenGL if you plan to use GLUT.")
 	endif(NOT MGL_HAVE_OPENGL)
-	include(FindGLUT)
+	find_package(GLUT REQUIRED)
 	if(NOT GLUT_FOUND)
 		message(SEND_ERROR "Couldn't find GLUT library.")
 	endif(NOT GLUT_FOUND)
@@ -658,14 +659,19 @@ endif(enable-glut)
 
 if(enable-fltk)
 	set(MGL_HAVE_FLTK 1)
-	FIND_PACKAGE(FLTK)
+	find_package(FLTK CONFIG)
+	set(FLTK_LIBRARIES fltk)
 	if(NOT FLTK_FOUND)
 		message(SEND_ERROR "Couldn't find FLTK library.")
 	else(NOT FLTK_FOUND)
-		include_directories(${FLTK_INCLUDE_DIR})
+		set(FLTK_LIBRARIES fltk)
+		include(CMakePushCheckState)
+		cmake_push_check_state()
+		list(APPEND CMAKE_REQUIRED_LIBRARIES ${FLTK_LIBRARIES})
 		CHECK_CXX_SOURCE_COMPILES(
 		"#include <FL/Fl_Copy_Surface.H>
 		int main(){return 0;}" MGL_HAVE_FL_COPY)
+		cmake_pop_check_state()
 	endif(NOT FLTK_FOUND)
 	if(NOT MGL_HAVE_FL_COPY)
 		set(MGL_HAVE_FL_COPY 0)
diff --git a/scripts/MathGLConfig.cmake.in b/scripts/MathGLConfig.cmake.in
index 78bad89..0c2e5ed 100644
--- a/scripts/MathGLConfig.cmake.in
+++ b/scripts/MathGLConfig.cmake.in
@@ -25,6 +25,12 @@ endif()
 if (MathGL2_HAVE_OPENMP STRGREATER "")
   find_dependency(OpenMP)
 endif()
+if("@enable-arma@")
+  find_dependency(Armadillo CONFIG)
+endif()
+if(MathGL2_HAVE_FLTK)
+  find_dependency(FLTK CONFIG)
+endif()
 
 # Compute paths
 get_filename_component(MathGL2_CMAKE_DIR "${CMAKE_CURRENT_LIST_FILE}" PATH)
