diff --git a/CMakeLists.txt b/CMakeLists.txt
index 2a73d0f..fda5ba1 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -51,6 +51,7 @@ endif()
 set(MathGL_INSTALL_CMAKE_DIR ${DEF_INSTALL_CMAKE_DIR} CACHE PATH "Installation directory for CMake files")
 # Make relative paths absolute (needed later on)
 foreach(p LIB BIN INCLUDE CMAKE)
+	break() # Absolute paths break config fixup for tools
 	set(var MathGL_INSTALL_${p}_DIR)
 	if(NOT IS_ABSOLUTE "${${var}}")
 		set(${var} "${CMAKE_INSTALL_PREFIX}/${${var}}")
diff --git a/scripts/MathGLConfig.cmake.in b/scripts/MathGLConfig.cmake.in
index f219126..2a9f58b 100644
--- a/scripts/MathGLConfig.cmake.in
+++ b/scripts/MathGLConfig.cmake.in
@@ -1,15 +1,19 @@
 # - Config file for the MathGL package
 # It defines the following variables
  
-set(MathGL2_INCLUDE_DIRS "@MathGL_INSTALL_INCLUDE_DIR@")
-set(MathGL2_LIBRARIES_DIRS "@MathGL_INSTALL_LIB_DIR@")
+set(MathGL2_INCLUDE_DIRS "@CMAKE_INSTALL_PREFIX@/@MathGL_INSTALL_INCLUDE_DIR@")
+if(CMAKE_BUILD_TYPE MATCHES "[Dd][Ee][Bb][Uu][Gg]")
+  set(MathGL2_LIBRARIES_DIRS "@CMAKE_INSTALL_PREFIX@/debug/@MathGL_INSTALL_LIB_DIR@")
+else()
+  set(MathGL2_LIBRARIES_DIRS "@CMAKE_INSTALL_PREFIX@/@MathGL_INSTALL_LIB_DIR@")
+endif()
 set(MathGL2_HAVE_QT5 "@enable-qt5@")
 set(MathGL2_HAVE_QT4 "@enable-qt4@")
 set(MathGL2_HAVE_WX "@enable-wx@")
 set(MathGL2_HAVE_FLTK "@enable-fltk@")
 set(MathGL2_HAVE_GLUT "@enable-glut@")
 set(MathGL2_HAVE_PTHREAD "@CMAKE_USE_PTHREADS_INIT@")
-set(MathGL2_HAVE_OPENMP "@OPENMP_FOUND@")
+set(MathGL2_HAVE_OPENMP "@enable-openmp@")
 
 include(CMakeFindDependencyMacro)
 # Adding dependency for Threads imported target
