diff --git a/CMakeLists.txt b/CMakeLists.txt
index 8a1ce4b..71d07bf 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -842,8 +842,9 @@ if(enable-examples)
 add_subdirectory( examples )
 endif()
 if(NOT WIN32)
-	add_subdirectory( fonts )
+	set(INSTALL_FONTS 1)
 endif(NOT WIN32)
+add_subdirectory( fonts )
 add_subdirectory( mgllab )
 endif(NOT enable-lgpl)
 
diff --git a/fonts/CMakeLists.txt b/fonts/CMakeLists.txt
index f529c18..aba1bc3 100644
--- a/fonts/CMakeLists.txt
+++ b/fonts/CMakeLists.txt
@@ -1,8 +1,20 @@
+if(MAKE_BIN_EXECUTABLE)
+	add_executable(make_bin IMPORTED)
+	set_target_properties(make_bin PROPERTIES IMPORTED_LOCATION "${MAKE_BIN_EXECUTABLE}")
+else()
+	add_executable(make_bin make_bin.cpp)
+	target_link_libraries(make_bin mgl${link_type} ${getopt_lib-static})
+	install(TARGETS make_bin RUNTIME DESTINATION bin)
+endif()
+if(NOT INSTALL_FONTS)
+	return()
+elseif(0)
 include_directories( ${MathGL2_SOURCE_DIR}/include ${MathGL2_BINARY_DIR}/include)
 set(hdrF ../include/mgl2/font.h ../include/mgl2/define.h ${MathGL2_BINARY_DIR}/include/mgl2/config.h)
 add_executable(make_bin make_bin.cpp ../src/font.cpp ${hdrF})
 #target_link_libraries(make_bin mgl-static ${getopt_lib-static})
 target_link_libraries(make_bin ${MGL_DEP_LIBS})
+endif()
 
 set(MGL_FONTS STIX adventor  bonum  cursor  heroscn  heros  pagella  schola  termes)
 set(MGL_FONTS_BIN )
diff --git a/fonts/make_bin.cpp b/fonts/make_bin.cpp
index 73d0adb..e0158be 100644
--- a/fonts/make_bin.cpp
+++ b/fonts/make_bin.cpp
@@ -20,6 +20,7 @@
 #include <getopt.h>
 #include "mgl2/font.h"
 //-----------------------------------------------------------------------------
+#if 0 // vcpkg links to mgl lib
 extern mglFont *mglDefFont;
 void mgl_init()	// TODO try to add ld option: "-init mgl_init"
 {
@@ -30,6 +31,7 @@ void mgl_init()	// TODO try to add ld option: "-init mgl_init"
 		mglDefFont = new mglFont(MGL_DEF_FONT_NAME);
 	}
 }
+#endif
 int main(int argc, char *argv[])
 {
 	mglFont fnt;
