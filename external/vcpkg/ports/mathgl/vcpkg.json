{"name": "mathgl", "version": "8.0.1", "port-version": 7, "description": "MathGL is a free library of fast C++ routines for the plotting of the data varied in one or more dimensions", "license": "GPL-3.0-only", "supports": "!uwp & !xbox & !android", "dependencies": ["getopt", {"$comment": "Non-windows targets need host `make_bin`.", "name": "mathgl", "host": true, "default-features": false}, {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "default-features": ["jpeg", "opengl", "png", "zlib"], "features": {"arma": {"description": "Armadillo support", "dependencies": ["armadillo"]}, "examples": {"description": "Build example programs (not installed)"}, "fltk": {"description": "fltk module", "dependencies": [{"name": "fltk", "default-features": false}]}, "gif": {"description": "gif module", "dependencies": ["gif<PERSON>b"]}, "glut": {"description": "glut module", "dependencies": [{"name": "freeglut", "platform": "!osx"}, {"name": "mathgl", "default-features": false, "features": ["opengl"]}]}, "gsl": {"description": "gsl module", "dependencies": ["gsl"]}, "hdf5": {"description": "hdf5 module", "dependencies": [{"name": "hdf5", "default-features": false}]}, "jpeg": {"description": "jpeg module", "dependencies": ["libjpeg-turbo"]}, "opengl": {"description": "opengl module", "dependencies": ["opengl"]}, "png": {"description": "png module", "dependencies": ["libpng", {"name": "mathgl", "default-features": false, "features": ["zlib"]}]}, "qt5": {"description": "qt5 module", "dependencies": [{"name": "qt5-base", "default-features": false}]}, "wx": {"description": "wx module", "dependencies": [{"name": "wxwidgets", "default-features": false}]}, "zlib": {"description": "zlib module", "dependencies": ["zlib"]}}}