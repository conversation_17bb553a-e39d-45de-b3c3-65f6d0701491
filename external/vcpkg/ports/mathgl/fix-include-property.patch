diff --git a/src/CMakeLists.txt b/src/CMakeLists.txt
index d44125c..1ad17a1 100644
--- a/src/CMakeLists.txt
+++ b/src/CMakeLists.txt
@@ -53,6 +53,9 @@ generate_export_header(mgl EXPORT_FILE_NAME ../include/mgl2/dllexport.h)
 target_link_libraries(mgl ${MGL_DEP_LIBS})
 target_link_libraries(mgl-static ${MGL_DEP_LIBS})
 
+target_include_directories(mgl PUBLIC $<INSTALL_INTERFACE:include>)
+target_include_directories(mgl-static PUBLIC $<INSTALL_INTERFACE:include>)
+
 mgl_po_src(${mgl_src} ${mgl_hdr})
 
 if(MGL_HAVE_MPI)
