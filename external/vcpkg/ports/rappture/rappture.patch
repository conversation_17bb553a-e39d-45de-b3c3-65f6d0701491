diff --git a/src/core/RpBuffer.cc b/src/core/RpBuffer.cc
index 6d439fe..ec45405 100644
--- a/src/core/RpBuffer.cc
+++ b/src/core/RpBuffer.cc
@@ -16,7 +16,6 @@
 #include <stdio.h>
 #include <sys/types.h>
 #include <sys/stat.h>
-#include <unistd.h>
 #include <fstream>
 #include <zlib.h>
 #include "b64/encode.h"
@@ -24,6 +23,11 @@
 #include "RpBuffer.h"
 #include "RpEncode.h"
 
+#if defined(_MSC_VER)
+#include <BaseTsd.h>
+typedef SSIZE_T ssize_t;
+#endif
+
 namespace Rappture {
 
 /**
@@ -109,6 +113,9 @@ Buffer::operator+=(const Buffer& b)
 Buffer::~Buffer()
 {}
 
+#ifndef _WIN32
+#   define _fileno fileno
+#endif
 
 bool
 Buffer::load (Outcome &status, const char *path)
@@ -123,7 +130,7 @@ Buffer::load (Outcome &status, const char *path)
     }
 
     struct stat stat;
-    if (fstat(fileno(f), &stat) < 0) {
+    if (fstat(_fileno(f), &stat) < 0) {
         status.addError("can't stat \"%s\": %s", path, strerror(errno));
         return false;
     }
diff --git a/src/core/RpDXWriter.cc b/src/core/RpDXWriter.cc
index e4748b3..8990c0f 100644
--- a/src/core/RpDXWriter.cc
+++ b/src/core/RpDXWriter.cc
@@ -18,6 +18,12 @@
 #include <cfloat>
 #include <RpDXWriter.h>
 #include <assert.h>
+
+#if defined(_MSC_VER)
+#include <BaseTsd.h>
+typedef SSIZE_T ssize_t;
+#endif
+
 using namespace Rappture;
 
 DXWriter::DXWriter() :
diff --git a/src/core/RpLibrary.cc b/src/core/RpLibrary.cc
index 2b9a540..4da787b 100644
--- a/src/core/RpLibrary.cc
+++ b/src/core/RpLibrary.cc
@@ -11,7 +11,6 @@
  * ======================================================================
  */
 
-#include "config.h"
 #include "scew/scew.h"
 #include "scew_extras.h"
 #include "RpLibrary.h"
diff --git a/src/core/scew/scew.h b/src/core/scew/scew.h
index 9741ed5..e335c2d 100644
--- a/src/core/scew/scew.h
+++ b/src/core/scew/scew.h
@@ -61,12 +61,6 @@
 #  define SCEW_LIB_D
 # endif /* _DEBUG */
 
-# if defined(SCEW_LIB_U) || defined(SCEW_LIB_S) || defined(SCEW_LIB_D)
-# pragma comment( lib, "scew_" SCEW_LIB_U SCEW_LIB_S SCEW_LIB_D ".lib" )
-# else
-# pragma comment( lib, "scew.lib" )
-# endif
-
 #endif /* _WIN32 */
 
 #endif /* SCEW_H_ALEIX0211250134 */
