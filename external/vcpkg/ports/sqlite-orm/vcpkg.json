{"name": "sqlite-orm", "version": "1.9.1", "description": "SQLite ORM light header only library for modern C++", "homepage": "https://github.com/fnc12/sqlite_orm", "license": "AGPL-3.0-or-later OR MIT", "dependencies": ["sqlite3", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"example": {"description": "Build examples"}, "test": {"description": "Build sqlite_orm unit tests", "dependencies": ["catch2"]}}}