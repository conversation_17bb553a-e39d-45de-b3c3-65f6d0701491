{"name": "brpc", "version": "1.11.0", "port-version": 1, "description": "Industrial-grade RPC framework used throughout Baidu, with 1,000,000+ instances and thousands kinds of services, called \"baidu-rpc\" inside Baidu.", "homepage": "https://github.com/apache/brpc", "license": "Apache-2.0", "supports": "!windows & !(arm & android)", "dependencies": ["gflags", "glog", {"name": "leveldb", "default-features": false}, "openssl", {"name": "protobuf", "features": ["zlib"]}, "thrift", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}]}