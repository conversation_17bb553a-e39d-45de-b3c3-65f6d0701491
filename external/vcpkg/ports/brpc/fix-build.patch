diff --git a/CMakeLists.txt b/CMakeLists.txt
index b14fe178..9e9a776a 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -68,7 +68,8 @@ endif()
 
 if(WITH_THRIFT)
     set(THRIFT_CPP_FLAG "-DENABLE_THRIFT_FRAMED_PROTOCOL")
-    find_library(THRIFT_LIB NAMES thrift)
+    find_package(Thrift CONFIG REQUIRED)
+    set(THRIFT_LIB "thrift::thrift")
     if (NOT THRIFT_LIB)
         message(FATAL_ERROR "Fail to find Thrift")
     endif()
@@ -154,6 +155,8 @@ if(CMAKE_CXX_COMPILER_ID STREQUAL "GNU")
 endif()
 
 find_package(Protobuf REQUIRED)
+get_target_property(PROTOBUF_INCLUDE_DIR protobuf::libprotobuf INTERFACE_INCLUDE_DIRECTORIES)
+set(PROTOBUF_LIBRARIES protobuf::libprotobuf)
 if(Protobuf_VERSION GREATER 4.21)
     # required by absl
     set(CMAKE_CXX_STANDARD 17)
@@ -199,9 +202,11 @@ else()
     use_cxx11()
 endif()
 find_package(Threads REQUIRED)
+find_package(ZLIB REQUIRED)
 
+find_package(leveldb CONFIG REQUIRED)
 find_path(LEVELDB_INCLUDE_PATH NAMES leveldb/db.h)
-find_library(LEVELDB_LIB NAMES leveldb)
+set(LEVELDB_LIB leveldb::leveldb)
 if ((NOT LEVELDB_INCLUDE_PATH) OR (NOT LEVELDB_LIB))
     message(FATAL_ERROR "Fail to find leveldb")
 endif()
@@ -216,8 +221,9 @@ if(WITH_SNAPPY)
 endif()
 
 if(WITH_GLOG)
-    find_path(GLOG_INCLUDE_PATH NAMES glog/logging.h)
-    find_library(GLOG_LIB NAMES glog)
+    find_package(glog CONFIG REQUIRED)
+    get_target_property(GLOG_INCLUDE_PATH glog::glog INTERFACE_INCLUDE_DIRECTORIES)
+    set(GLOG_LIB glog::glog)
     if((NOT GLOG_INCLUDE_PATH) OR (NOT GLOG_LIB))
         message(FATAL_ERROR "Fail to find glog")
     endif()
@@ -244,7 +250,7 @@ if(WITH_RDMA)
     endif()
 endif()
 
-find_library(PROTOC_LIB NAMES protoc)
+set(PROTOC_LIB "protobuf::libprotoc")
 if(NOT PROTOC_LIB)
     message(FATAL_ERROR "Fail to find protoc lib")
 endif()
@@ -259,7 +265,7 @@ else()
         )
     endif()
 
-    find_package(OpenSSL)
+    find_package(OpenSSL REQUIRED)
     include_directories(${OPENSSL_INCLUDE_DIR})
 endif()
 
@@ -275,9 +281,10 @@ set(DYNAMIC_LIB
     ${LEVELDB_LIB}
     ${PROTOC_LIB}
     ${CMAKE_THREAD_LIBS_INIT}
+    ${OPENSSL_SSL_LIBRARY}
     ${THRIFT_LIB}
     dl
-    z)
+    ZLIB::ZLIB)
 
 if(WITH_BORINGSSL)
     list(APPEND DYNAMIC_LIB ${BORINGSSL_SSL_LIBRARY})
@@ -520,7 +527,7 @@ compile_proto(PROTO_HDRS PROTO_SRCS ${PROJECT_BINARY_DIR}
                                     ${PROJECT_SOURCE_DIR}/src
                                     "${PROTO_FILES}")
 add_library(PROTO_LIB OBJECT ${PROTO_SRCS} ${PROTO_HDRS})
-
+target_link_libraries(PROTO_LIB PUBLIC ${DYNAMIC_LIB})
 set(SOURCES
     ${BVAR_SOURCES}
     ${BTHREAD_SOURCES}
diff --git a/cmake/FindGFLAGS.cmake b/cmake/FindGFLAGS.cmake
index dfad5fd8..8423d55a 100644
--- a/cmake/FindGFLAGS.cmake
+++ b/cmake/FindGFLAGS.cmake
@@ -24,7 +24,9 @@ if (GFLAGS_STATIC)
     set(CMAKE_FIND_LIBRARY_SUFFIXES .a ${CMAKE_FIND_LIBRARY_SUFFIXES})
   endif (WIN32)
 endif (GFLAGS_STATIC)
-find_library(GFLAGS_LIBRARY NAMES gflags libgflags)
+find_package(gflags CONFIG REQUIRED)
+set(GFLAGS_LIBRARY gflags::gflags)
+get_target_property(GFLAGS_INCLUDE_PATH gflags::gflags INTERFACE_INCLUDE_DIRECTORIES)
 if(GFLAGS_INCLUDE_PATH AND GFLAGS_LIBRARY)
   set(GFLAGS_FOUND TRUE)
 endif(GFLAGS_INCLUDE_PATH AND GFLAGS_LIBRARY)
diff --git a/src/CMakeLists.txt b/src/CMakeLists.txt
index 1b4b2332..638ec070 100644
--- a/src/CMakeLists.txt
+++ b/src/CMakeLists.txt
@@ -24,13 +24,16 @@ include_directories(${CMAKE_CURRENT_BINARY_DIR})
 include_directories(${PROJECT_SOURCE_DIR}/src)
 
 add_library(BUTIL_LIB OBJECT ${BUTIL_SOURCES})
+target_link_libraries(BUTIL_LIB PUBLIC ${DYNAMIC_LIB})
 add_library(SOURCES_LIB OBJECT ${SOURCES})
+target_link_libraries(SOURCES_LIB PUBLIC ${DYNAMIC_LIB})
 add_dependencies(SOURCES_LIB PROTO_LIB)
 
 # shared library needs POSITION_INDEPENDENT_CODE
 set_property(TARGET ${SOURCES_LIB} PROPERTY POSITION_INDEPENDENT_CODE 1)
 set_property(TARGET ${BUTIL_LIB} PROPERTY POSITION_INDEPENDENT_CODE 1)
 
+if(NOT BUILD_SHARED_LIBS)
 add_library(brpc-static STATIC $<TARGET_OBJECTS:BUTIL_LIB>
                                $<TARGET_OBJECTS:SOURCES_LIB>
                                $<TARGET_OBJECTS:PROTO_LIB>)
@@ -60,12 +63,19 @@ endfunction()
 
 
 if(WITH_THRIFT)
-   target_link_libraries(brpc-static ${THRIFT_LIB})
-   check_thrift_version(brpc-static)
+   target_link_libraries(brpc-static PUBLIC ${THRIFT_LIB})
 endif()
 
+target_link_libraries(brpc-static PUBLIC ${DYNAMIC_LIB})
+if(BRPC_WITH_GLOG)
+    target_link_libraries(brpc-static PUBLIC ${GLOG_LIB})
+endif()
+target_include_directories(brpc-static PUBLIC $<INSTALL_INTERFACE:include>)
+
 SET_TARGET_PROPERTIES(brpc-static PROPERTIES OUTPUT_NAME brpc CLEAN_DIRECT_OUTPUT 1)
+endif()
 
+if(0)
 # for protoc-gen-mcpack
 set(EXECUTABLE_OUTPUT_PATH ${PROJECT_BINARY_DIR}/output/bin)
     
@@ -74,36 +84,63 @@ set(protoc_gen_mcpack_SOURCES
  )
     
 add_executable(protoc-gen-mcpack ${protoc_gen_mcpack_SOURCES})
+endif()
 
 if(BUILD_SHARED_LIBS)
     add_library(brpc-shared SHARED $<TARGET_OBJECTS:BUTIL_LIB> 
                                    $<TARGET_OBJECTS:SOURCES_LIB>
                                    $<TARGET_OBJECTS:PROTO_LIB>)
-    target_link_libraries(brpc-shared ${DYNAMIC_LIB})
+    target_link_libraries(brpc-shared PUBLIC ${DYNAMIC_LIB})
+    target_include_directories(brpc-shared PUBLIC $<INSTALL_INTERFACE:include>)
     if(WITH_GLOG)
-        target_link_libraries(brpc-shared ${GLOG_LIB})
+        target_link_libraries(brpc-shared PUBLIC ${GLOG_LIB})
     endif()
     if(WITH_THRIFT)
-        target_link_libraries(brpc-shared ${THRIFT_LIB})
-        check_thrift_version(brpc-shared)
+        target_link_libraries(brpc-shared PUBLIC ${THRIFT_LIB})
     endif()
     SET_TARGET_PROPERTIES(brpc-shared PROPERTIES OUTPUT_NAME brpc CLEAN_DIRECT_OUTPUT 1)
-
+    if(0)
     target_link_libraries(protoc-gen-mcpack brpc-shared ${DYNAMIC_LIB} pthread)
+    endif()
 
-    install(TARGETS brpc-shared
+    install(TARGETS brpc-shared EXPORT unofficial-brpcTargets
             RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
             LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
             ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR}
             )
 else()
+if(0)
     target_link_libraries(protoc-gen-mcpack brpc-static ${DYNAMIC_LIB} pthread)
 endif()
+endif()
 
 
-
-install(TARGETS brpc-static
+if(NOT BUILD_SHARED_LIBS)
+install(TARGETS brpc-static EXPORT unofficial-brpcTargets
         RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
         LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
         ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR}
         )
+endif()
+
+file(WRITE "${CMAKE_CURRENT_BINARY_DIR}/unofficial-brpc-config.cmake"
+[[include(CMakeFindDependencyMacro)
+find_dependency(OpenSSL)
+find_dependency(Threads)
+find_dependency(ZLIB)
+find_dependency(gflags CONFIG)
+find_dependency(protobuf CONFIG)
+find_dependency(leveldb CONFIG)
+find_dependency(Thrift CONFIG)
+find_dependency(glog CONFIG)
+file(GLOB TARGET_FILES "${CMAKE_CURRENT_LIST_DIR}/unofficial-brpcTargets.cmake")
+foreach (TARGET_FILE ${TARGET_FILES})
+    include("${TARGET_FILE}")
+endforeach()
+]])
+install(FILES "${CMAKE_CURRENT_BINARY_DIR}/unofficial-brpc-config.cmake" DESTINATION share/unofficial-brpc)
+
+install(EXPORT unofficial-brpcTargets
+    NAMESPACE unofficial::brpc::
+    DESTINATION share/unofficial-brpc
+)
