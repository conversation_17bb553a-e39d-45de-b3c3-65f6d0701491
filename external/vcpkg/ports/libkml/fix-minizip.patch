diff --git a/CMakeLists.txt b/CMakeLists.txt
index 9728ead..028f50a 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -110,9 +110,9 @@ else()
   list(APPEND MINIZIP_DEPENDS ZLIB)
 endif()
 
-find_package(MiniZip)
+find_package(MINIZIP NAMES unofficial-minizip REQUIRED)
 if(MINIZIP_FOUND)
-  include_directories(${MINIZIP_INCLUDE_DIR})
+  set(MINIZIP_LIBRARY unofficial::minizip::minizip)
 else()
   include(External_minizip)
   list(APPEND KMLBASE_DEPENDS MINIZIP)
diff --git a/cmake/LibKMLConfig.cmake.in b/cmake/LibKMLConfig.cmake.in
index 3e295f4..97826df 100644
--- a/cmake/LibKMLConfig.cmake.in
+++ b/cmake/LibKMLConfig.cmake.in
@@ -1,3 +1,6 @@
+include(CMakeFindDependencyMacro)
+find_dependency(unofficial-minizip CONFIG)
+
 # Compute paths
 get_filename_component(LIBKML_CMAKE_DIR "${CMAKE_CURRENT_LIST_FILE}" PATH)
 
diff --git a/src/kml/base/contrib/minizip/iomem_simple.c b/src/kml/base/contrib/minizip/iomem_simple.c
index be89226..6be61d5 100644
--- a/src/kml/base/contrib/minizip/iomem_simple.c
+++ b/src/kml/base/contrib/minizip/iomem_simple.c
@@ -219,7 +219,7 @@ int ZCALLBACK mem_error (opaque, stream)
     return 0;
 }
 
-ZEXTERN void* ZEXPORT mem_simple_create_file(zlib_filefunc_def* api, void* buffer, size_t buf_len)
+extern void* mem_simple_create_file(zlib_filefunc_def* api, void* buffer, size_t buf_len)
 {
     MEMFILE* handle = malloc(sizeof(*handle));
     api->zopen_file  = NULL;
diff --git a/src/kml/base/contrib/minizip/iomem_simple.h b/src/kml/base/contrib/minizip/iomem_simple.h
index ec11396..515e24e 100644
--- a/src/kml/base/contrib/minizip/iomem_simple.h
+++ b/src/kml/base/contrib/minizip/iomem_simple.h
@@ -70,7 +70,7 @@ void fill_fopen_filefunc OF((zlib_filefunc_def* pzlib_filefunc_def));
  * This declaration is from the proposed iomem_simple package found at
  * http://code.trak.dk. See iomem_simple.c
  */
-extern void* ZEXPORT mem_simple_create_file(zlib_filefunc_def* pzlib_filefunc_def, void* buffer, size_t buflen);
+extern void* mem_simple_create_file(zlib_filefunc_def* pzlib_filefunc_def, void* buffer, size_t buflen);
 
 #ifdef __cplusplus
 }
diff --git a/src/kml/base/contrib/minizip/unzip.c b/src/kml/base/contrib/minizip/unzip.c
index a062863..fb69189 100644
--- a/src/kml/base/contrib/minizip/unzip.c
+++ b/src/kml/base/contrib/minizip/unzip.c
@@ -43,6 +43,14 @@ woven in by Terry Thorsen 1/2003.
 #include "unzip.h"
 #include "iomem_simple.h"
 #undef NOUNCRYPT
+#ifdef ZEXPORT
+#  undef ZEXPORT
+#endif
+#define ZEXPORT
+#ifdef ZEXTERN
+#  undef ZEXTERN
+#endif
+#define ZEXTERN extern
 
 #ifdef STDC
 #  include <stddef.h>
