diff --git a/src/lcdd.c b/src/lcdd.c
index 473e6c5..cafc1a5 100644
--- a/src/lcdd.c
+++ b/src/lcdd.c
@@ -44,7 +44,6 @@ int main(int argc, char *argv[])
   dd_ErrorType err;
 
   dd_set_global_constants();  /* First, this must be called. */
-  dd_log=dd_TRUE; /* Output log */
 
   if (argc > 2)  
     dd_DDFile2File(argv[1],argv[2],&err);
diff --git a/src/scdd.c b/src/scdd.c
index e9e0c59..9e6ed32 100644
--- a/src/scdd.c
+++ b/src/scdd.c
@@ -66,7 +66,6 @@ int main(int argc, char *argv[])
   FILE *reading=NULL, *writing;
 
   dd_set_global_constants();  /* First, this must be called. */
-  dd_log=dd_TRUE;  /* output log */
 
   if (argc>1) strcpy(inputfile,argv[1]);
   if (argc<=1 || !SetInputFile(&reading,argv[1])){

