diff --git a/cmake/urdfdom_headers-config.cmake.in b/cmake/urdfdom_headers-config.cmake.in
index b53e466..b35805a 100644
--- a/cmake/urdfdom_headers-config.cmake.in
+++ b/cmake/urdfdom_headers-config.cmake.in
@@ -3,8 +3,8 @@ if (@PACKAGE_NAME@_CONFIG_INCLUDED)
 endif()
 set(@PACKAGE_NAME@_CONFIG_INCLUDED TRUE)
 
-set(@PACKAGE_NAME@_INCLUDE_DIRS "${@PROJECT_NAME@_DIR}/@RELATIVE_PATH_CMAKE_DIR_TO_PREFIX@/@CMAKE_INSTALL_INCLUDEDIR@")
+set(@PACKAGE_NAME@_INCLUDE_DIRS "${CMAKE_CURRENT_LIST_DIR}/../../include")
 
-include("${@PACKAGE_NAME@_DIR}/@<EMAIL>")
+include("${CMAKE_CURRENT_LIST_DIR}/@<EMAIL>")
 
 list(APPEND @PACKAGE_NAME@_TARGETS @PACKAGE_NAME@::@PACKAGE_NAME@)
