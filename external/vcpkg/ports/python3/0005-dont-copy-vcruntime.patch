From 8086c67fa7ada1888a7808cbdc6fe74b62abe5b1 Mon Sep 17 00:00:00 2001
From: <PERSON> <<PERSON><PERSON>oh<PERSON>@gmail.com>
Date: Wed, 9 Sep 2020 16:12:49 -0400
Subject: [PATCH 4/6] dont copy vcruntime

VCRUNTIME140.dll should not be redistributed, ever.
---
 PCbuild/pythoncore.vcxproj | 2 +-
 1 file changed, 1 insertion(+), 1 deletion(-)

diff --git a/PCbuild/pythoncore.vcxproj b/PCbuild/pythoncore.vcxproj
index 5f30a35eb3..3cf21ba39c 100644
--- a/PCbuild/pythoncore.vcxproj
+++ b/PCbuild/pythoncore.vcxproj
@@ -565,7 +565,7 @@
   <Target Name="_CopyVCRuntime" AfterTargets="Build" Inputs="@(VCRuntimeDLL)" Outputs="$(OutDir)%(Filename)%(Extension)" DependsOnTargets="FindVCRuntime">
     <!-- bpo-38597: When we switch to another VCRuntime DLL, include vcruntime140.dll as well -->
     <Warning Text="A copy of vcruntime140.dll is also required" Condition="!$(VCToolsRedistVersion.StartsWith(`14.`))" />
-    <Copy SourceFiles="%(VCRuntimeDLL.FullPath)" DestinationFolder="$(OutDir)" />
+    <Copy Condition="false" SourceFiles="%(VCRuntimeDLL.FullPath)" DestinationFolder="$(OutDir)" />
   </Target>
   <Target Name="_CleanVCRuntime" AfterTargets="Clean">
     <Delete Files="@(VCRuntimeDLL->'$(OutDir)%(Filename)%(Extension)')" />
-- 
2.28.0.windows.1

