From 14d91e4f4a9377f47cc4cc33faeeb7c82f64b176 Mon Sep 17 00:00:00 2001
From: <PERSON> <<PERSON><PERSON><PERSON><PERSON>@gmail.com>
Date: Thu, 28 May 2020 17:25:21 -0400
Subject: [PATCH 1/7] static library

builds the pythoncore as a static library instead of a DLL
---
 PC/pyconfig.h              | 9 +++++++++
 PCbuild/pythoncore.vcxproj | 4 ++--
 2 files changed, 11 insertions(+), 2 deletions(-)

diff --git a/PC/pyconfig.h b/PC/pyconfig.h
index d7d3cf081e..34269f0b75 100644
--- a/PC/pyconfig.h
+++ b/PC/pyconfig.h
@@ -251,6 +251,9 @@ typedef int pid_t;
 
 /* For Windows the Python core is in a DLL by default.  Test
 Py_NO_ENABLE_SHARED to find out.  Also support MS_NO_COREDLL for b/w compat */
+#ifndef Py_NO_ENABLE_SHARED
+#define Py_NO_ENABLE_SHARED
+#endif
 #if !defined(MS_NO_COREDLL) && !defined(Py_NO_ENABLE_SHARED)
 #       define Py_ENABLE_SHARED 1 /* standard symbol for shared library */
 #       define MS_COREDLL       /* deprecated old symbol */
@@ -276,6 +277,15 @@ Py_NO_ENABLE_SHARED to find out.  Also support MS_NO_COREDLL for b/w compat */
 #                       endif /* _DEBUG */
 #               endif /* _MSC_VER */
 #       endif /* Py_BUILD_CORE */
+#else
+        /* So MSVC users need not specify the .lib file in their own config */
+#       pragma comment(lib, "version.lib")
+#       pragma comment(lib, "shlwapi.lib")
+#       pragma comment(lib, "ws2_32.lib")
+#       pragma comment(lib, "bcrypt.lib")
+#       if Py_WINVER > 0x0601
+#           pragma comment(lib, "pathcch.lib")
+#       endif /* Py_WINVER */
 #endif /* MS_COREDLL */
 
 #ifdef MS_WIN64
diff --git a/PCbuild/pcbuild.proj b/PCbuild/pcbuild.proj
index 70c336a9d3..ba797e8afd 100644
--- a/PCbuild/pcbuild.proj
+++ b/PCbuild/pcbuild.proj
@@ -45,7 +45,7 @@
       <BuildInParallel>false</BuildInParallel>
     </Projects>
     <!-- python3.dll -->
-    <Projects Include="python3dll.vcxproj" />
+    <Projects Include="python3dll.vcxproj" Condition="false" />
     <!-- py[w].exe -->
     <Projects Include="pylauncher.vcxproj;pywlauncher.vcxproj" Condition="false" />
     <!-- pyshellext.dll -->
diff --git a/PCbuild/pythoncore.vcxproj b/PCbuild/pythoncore.vcxproj
index 2625d0293d..2f8bdaa931 100644
--- a/PCbuild/pythoncore.vcxproj
+++ b/PCbuild/pythoncore.vcxproj
@@ -73,7 +73,7 @@
   <Import Project="python.props" />
   <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
   <PropertyGroup Label="Configuration">
-    <ConfigurationType>DynamicLibrary</ConfigurationType>
+    <ConfigurationType>StaticLibrary</ConfigurationType>
     <UseOfMfc>false</UseOfMfc>
   </PropertyGroup>
   <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
@@ -102,7 +102,7 @@
       <AdditionalOptions>/Zm200  %(AdditionalOptions)</AdditionalOptions>
       <AdditionalIncludeDirectories>$(PySourcePath)Modules\_hacl\include;$(PySourcePath)Modules\_hacl\internal;$(PySourcePath)Python;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
       <AdditionalIncludeDirectories Condition="$(IncludeExternals)">$(zlibDir);%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
-      <PreprocessorDefinitions>_USRDLL;Py_BUILD_CORE;Py_BUILD_CORE_BUILTIN;Py_ENABLE_SHARED;MS_DLL_ID="$(SysWinVer)";%(PreprocessorDefinitions)</PreprocessorDefinitions>
+      <PreprocessorDefinitions>_USRDLL;Py_BUILD_CORE;Py_BUILD_CORE_BUILTIN;MS_DLL_ID="$(SysWinVer)";%(PreprocessorDefinitions)</PreprocessorDefinitions>
       <PreprocessorDefinitions Condition="$(IncludeExternals)">_Py_HAVE_ZLIB;%(PreprocessorDefinitions)</PreprocessorDefinitions>
     </ClCompile>
     <Link>
diff --git a/PCbuild/pythoncore.vcxproj b/PCbuild/pythoncore.vcxproj
index 2625d0293d..2f8bdaa931 100644
--- a/PCbuild/_freeze_module.vcxproj
+++ b/PCbuild/_freeze_module.vcxproj
@@ -88,7 +88,7 @@
   <PropertyGroup Label="UserMacros" />
   <ItemDefinitionGroup>
     <ClCompile>
-      <PreprocessorDefinitions>Py_NO_ENABLE_SHARED;Py_BUILD_CORE;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
+      <PreprocessorDefinitions>Py_NO_ENABLE_SHARED;Py_BUILD_CORE;_CONSOLE;MS_DLL_ID="$(SysWinVer)";%(PreprocessorDefinitions)</PreprocessorDefinitions>
       <Optimization>Disabled</Optimization>
       <WholeProgramOptimization>false</WholeProgramOptimization>
     </ClCompile>
diff --git a/Python/sysmodule.c b/Python/sysmodule.c
index ac49f7867a..f3583345ff 100644
--- a/Python/sysmodule.c
+++ b/Python/sysmodule.c
@@ -2804,6 +2804,9 @@ _PySys_InitCore(PyThreadState *tstate, PyObject *sysdict
 #ifdef MS_COREDLL
     SET_SYS("dllhandle", PyLong_FromVoidPtr(PyWin_DLLhModule));
     SET_SYS_FROM_STRING("winver", PyWin_DLLVersionString);
+#elif defined(MS_WINDOWS)
+    SET_SYS("dllhandle", PyLong_FromVoidPtr(NULL));
+    SET_SYS_FROM_STRING("winver", MS_DLL_ID);
 #endif
 #ifdef ABIFLAGS
     SET_SYS_FROM_STRING("abiflags", ABIFLAGS);
-- 
2.28.0.windows.1

