{"name": "python3", "version": "3.12.9", "description": "The Python programming language", "homepage": "https://github.com/python/cpython", "license": "Python-2.0", "supports": "!uwp & !mingw", "dependencies": [{"name": "gettext", "platform": "osx"}, {"name": "libiconv", "platform": "osx"}, {"name": "libuuid", "platform": "!osx & !windows"}, {"name": "python3", "host": true, "default-features": false}, {"name": "python3", "features": ["extensions"], "platform": "!windows"}, {"name": "vcpkg-get-python", "host": true}, {"name": "vcpkg-msbuild", "host": true, "platform": "windows"}, "zlib"], "default-features": [{"name": "extensions", "platform": "!(staticcrt & windows)"}], "features": {"extensions": {"description": "Allow the build and usage of python extensions. On windows this requires python to be a dynamic library!", "supports": "!(staticcrt & windows)", "dependencies": [{"name": "bzip2", "default-features": false}, {"name": "expat", "default-features": false}, {"name": "libffi", "default-features": false}, {"name": "liblzma", "default-features": false}, {"name": "openssl", "default-features": false}, {"name": "sqlite3", "default-features": false}]}, "readline": {"description": "Build with readline. Requires system readline to be installed", "supports": "!windows"}}}