<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemDefinitionGroup>
    <Link>
      <AdditionalDependencies>Crypt32.lib;ws2_32.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalDependencies Condition="'$(Configuration)|$(IncludeExternals)'=='Release|true'">
        ${CRYPTO_RELEASE};${SSL_RELEASE};%(AdditionalDependencies)
      </AdditionalDependencies>
      <AdditionalDependencies Condition="'$(Configuration)|$(IncludeExternals)'=='Debug|true'">
        ${CRYPTO_DEBUG};${SSL_DEBUG};%(AdditionalDependencies)
      </AdditionalDependencies>
    </Link>
    <Lib>
      <AdditionalDependencies>Crypt32.lib;ws2_32.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalDependencies Condition="'$(Configuration)|$(IncludeExternals)'=='Release|true'">
        ${CRYPTO_RELEASE};${SSL_RELEASE};%(AdditionalDependencies)
      </AdditionalDependencies>
      <AdditionalDependencies Condition="'$(Configuration)|$(IncludeExternals)'=='Debug|true'">
        ${CRYPTO_DEBUG};${SSL_DEBUG};%(AdditionalDependencies)
      </AdditionalDependencies>
    </Lib>
  </ItemDefinitionGroup>
</Project>
