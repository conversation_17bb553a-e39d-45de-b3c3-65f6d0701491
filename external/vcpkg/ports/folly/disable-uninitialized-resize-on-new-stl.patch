diff --git a/folly/memory/UninitializedMemoryHacks.h b/folly/memory/UninitializedMemoryHacks.h
index daf5eb735..1ac44d6b2 100644
--- a/folly/memory/UninitializedMemoryHacks.h
+++ b/folly/memory/UninitializedMemoryHacks.h
@@ -101,6 +101,9 @@ template <
         typename std::enable_if<std::is_trivially_destructible<T>::value>::type>
 inline void resizeWithoutInitialization(
     std::basic_string<T>& s, std::size_t n) {
+#if defined(_MSVC_STL_UPDATE) && _MSVC_STL_UPDATE >= 202206L
+    s.resize(n);
+#else
   if (n <= s.size()) {
     s.resize(n);
   } else {
@@ -111,6 +114,7 @@ inline void resizeWithoutInitialization(
     }
     detail::unsafeStringSetLargerSize(s, n);
   }
+#endif // defined(_MSVC_STL_UPDATE) && _MSVC_STL_UPDATE >= 202206L
 }
 
 /**
@@ -278,8 +282,11 @@ struct MakeUnsafeStringSetLargerSize {
 } // namespace folly
 
 #if defined(FOLLY_DECLARE_STRING_RESIZE_WITHOUT_INIT)
+#if defined(_MSVC_STL_UPDATE) && _MSVC_STL_UPDATE >= 202206L
+#else
 FOLLY_DECLARE_STRING_RESIZE_WITHOUT_INIT(char)
 FOLLY_DECLARE_STRING_RESIZE_WITHOUT_INIT(wchar_t)
+#endif // defined(_MSVC_STL_UPDATE) && _MSVC_STL_UPDATE >= 202206L
 #endif
 
 namespace folly {
