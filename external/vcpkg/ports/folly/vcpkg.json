{"name": "folly", "version-string": "2025.03.31.00", "port-version": 1, "description": "An open-source C++ library developed and used at Facebook. The library is UNSTABLE on Windows", "homepage": "https://github.com/facebook/folly", "license": "Apache-2.0", "supports": "(windows & x64 & !uwp & !mingw) | (!windows & (x64 | arm64))", "dependencies": ["boost-chrono", "boost-context", "boost-conversion", "boost-crc", "boost-date-time", "boost-filesystem", "boost-multi-index", "boost-program-options", "boost-regex", "boost-smart-ptr", "boost-system", "boost-thread", "boost-variant", "double-conversion", "fast-float", "fmt", "gflags", "glog", "libevent", "liblzma", {"name": "libunwind", "platform": "linux"}, "openssl", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}, "zlib"], "features": {"bzip2": {"description": "Support bzip2 for compression", "dependencies": ["bzip2"]}, "libaio": {"description": "Support compile with libaio", "supports": "linux", "dependencies": ["libaio"]}, "libsodium": {"description": "Support libsodium for cryto", "dependencies": ["libsodium"]}, "liburing": {"description": "Support compile with liburing", "supports": "linux", "dependencies": ["liburing"]}, "lz4": {"description": "Support lz4 for compression", "dependencies": ["lz4"]}, "lzma": {"description": "Support LZMA for compression", "dependencies": ["liblzma"]}, "snappy": {"description": "Support Snappy for compression", "dependencies": [{"name": "snappy", "features": ["rtti"]}]}, "zstd": {"description": "Support zstd for compression", "dependencies": ["zstd"]}}}