{"name": "fmilib", "version": "2.4.1", "port-version": 2, "description": "FMI library is intended as a foundation for applications interfacing FMUs (Functional Mockup Units) that follow FMI Standard. This version of the library supports FMI 1.0 and FMI2.0.", "homepage": "https://www.fmi-standard.org/", "license": "BSD-3-<PERSON><PERSON>", "supports": "!uwp", "dependencies": ["expat", "minizip", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}]}