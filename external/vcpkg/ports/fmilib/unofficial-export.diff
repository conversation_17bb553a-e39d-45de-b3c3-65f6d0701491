diff --git a/CMakeLists.txt b/CMakeLists.txt
index 583e15b..0319e3a 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -300,10 +300,17 @@ endif()
 
 file(COPY "${FMILIBRARYHOME}/Config.cmake/fmilib.h" DESTINATION "${FMILibrary_BINARY_DIR}")
 
+target_include_directories(${FMILIB_TARGETS} INTERFACE $<INSTALL_INTERFACE:include>)
 install(TARGETS ${FMILIB_TARGETS}
+	EXPORT unofficial-fmilib-targets
 	ARCHIVE DESTINATION lib
 	LIBRARY DESTINATION lib
-	RUNTIME DESTINATION lib
+	RUNTIME DESTINATION bin
+)
+install(EXPORT unofficial-fmilib-targets
+        FILE unofficial-fmilib-targets.cmake
+        NAMESPACE unofficial::fmilib::
+        DESTINATION share/unofficial-fmilib
 )
 install(FILES 
 			"${FMILIBRARYHOME}/FMILIB_Readme.txt"
