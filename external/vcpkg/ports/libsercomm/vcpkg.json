{"name": "libsercomm", "version": "1.3.2", "port-version": 1, "description": "Multiplatform serial communications library", "homepage": "https://github.com/ingeniamc/sercomm", "license": "MIT", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "default-features": ["dev<PERSON>", "<PERSON><PERSON><PERSON>"], "features": {"devmon": {"description": "When enabled, device listing and monitoring will be supported"}, "errdesc": {"description": "When enabled, error details description can be obtained"}}}