{"name": "jsoncons", "version": "1.3.0", "description": "A C++, header-only library for constructing JSON and JSON-like text and binary data formats, with JSO<PERSON> Pointer, JSO<PERSON> Patch, JSON Schema, JSONPath, JMESPath, CSV, MessagePack, CBOR, BSON, UBJSON", "homepage": "https://github.com/danielaparker/jsoncons", "license": "BSL-1.0", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}]}