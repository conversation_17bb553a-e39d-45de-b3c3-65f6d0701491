diff --git a/include/clipp.h b/include/clipp.h
index d7b101e..a1bec2d 100644
--- a/include/clipp.h
+++ b/include/clipp.h
@@ -155,16 +155,27 @@ namespace traits {
  * @brief function (class) signature type trait
  *
  *****************************************************************************/
+#if defined(__cpp_lib_is_invocable)
 template<class Fn, class Ret, class... Args>
 constexpr auto
 check_is_callable(int) -> decltype(
     std::declval<Fn>()(std::declval<Args>()...),
     std::integral_constant<bool,
-        std::is_same<Ret,typename std::result_of<Fn(Args...)>::type>::value>{} );
+        std::is_same<Ret,typename std::invoke_result<Fn,Args...>::type>::value>{} );
 
-template<class,class,class...>
+template<class Fn, class Ret>
 constexpr auto
-check_is_callable(long) -> std::false_type;
+check_is_callable_without_arg(int) -> decltype(
+    std::declval<Fn>()(),
+    std::integral_constant<bool,
+        std::is_same<Ret,typename std::invoke_result<Fn>::type>::value>{} );
+#else
+template<class Fn, class Ret, class... Args>
+constexpr auto
+check_is_callable(int) -> decltype(
+    std::declval<Fn>()(std::declval<Args>()...),
+    std::integral_constant<bool,
+        std::is_same<Ret,typename std::result_of<Fn(Args...)>::type>::value>{} );
 
 template<class Fn, class Ret>
 constexpr auto
@@ -172,6 +183,11 @@ check_is_callable_without_arg(int) -> decltype(
     std::declval<Fn>()(),
     std::integral_constant<bool,
         std::is_same<Ret,typename std::result_of<Fn()>::type>::value>{} );
+#endif
+
+template<class,class,class...>
+constexpr auto
+check_is_callable(long) -> std::false_type;
 
 template<class,class>
 constexpr auto
