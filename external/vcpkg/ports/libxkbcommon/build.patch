diff --git a/tools/how-to-type.c b/tools/how-to-type.c
index 72aea1b..783373a 100644
--- a/tools/how-to-type.c
+++ b/tools/how-to-type.c
@@ -26,7 +26,9 @@
 #include <getopt.h>
 #include <stdbool.h>
 #include <stdlib.h>
+#ifdef HAVE_UNISTD_H
 #include <unistd.h>
+#endif
 #include <errno.h>
 
 #include "xkbcommon/xkbcommon.h"
diff --git a/tools/tools-common.c b/tools/tools-common.c
index 8eb3f4b..8b22307 100644
--- a/tools/tools-common.c
+++ b/tools/tools-common.c
@@ -42,6 +42,7 @@
 #ifdef _WIN32
 #include <io.h>
 #include <windows.h>
+#include <process.h>
 #else
 #include <unistd.h>
 #include <termios.h>
