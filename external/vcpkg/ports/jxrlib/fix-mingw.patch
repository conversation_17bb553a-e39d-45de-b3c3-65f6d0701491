diff --git a/common/include/wmspecstrings_adt.h b/common/include/wmspecstrings_adt.h
index ca7f25f..d5eb028 100644
--- a/common/include/wmspecstrings_adt.h
+++ b/common/include/wmspecstrings_adt.h
@@ -36,6 +36,10 @@
         __type_has_adt_prop(compname,valid_schars) \
         __type_has_adt_prop(compname,correct_len) \
         __nullterminated
+#ifdef __MINGW32__
+#undef __$compname_props
+#define __$compname_props
+#endif
 #if defined(UNICODE) || defined(_UNICODE)
 #define  __$TCHAR unsigned short
 #else
diff --git a/image/sys/strcodec.h b/image/sys/strcodec.h
index 695a454..9fad5b6 100644
--- a/image/sys/strcodec.h
+++ b/image/sys/strcodec.h
@@ -59,7 +59,7 @@
 //#ifdef WIN32
 #if defined(WIN32) && !defined(UNDER_CE)   // WIN32 seems to be defined always in VS2005 for ARM platform
 #define PLATFORM_X86
-#include "..\x86\x86.h"
+#include "../x86/x86.h"
 #endif
 
 #ifndef UNREFERENCED_PARAMETER
diff --git a/jxrgluelib/JXRMeta.h b/jxrgluelib/JXRMeta.h
index b7b5880..7c9d653 100644
--- a/jxrgluelib/JXRMeta.h
+++ b/jxrgluelib/JXRMeta.h
@@ -111,6 +111,18 @@
 #define __out_win   __out
 #endif
 
+#ifndef __in
+#define __in
+#endif
+#ifndef __out
+#define __out
+#endif
+#ifndef __in_ecount
+#define __in_ecount(x)
+#endif
+#ifndef __out_ecount
+#define __out_ecount(x)
+#endif
 
 //================================================================
 
