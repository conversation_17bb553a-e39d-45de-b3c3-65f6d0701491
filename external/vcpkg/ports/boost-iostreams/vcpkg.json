{"$comment": "Automatically generated by scripts/boost/generate-ports.ps1", "name": "boost-iostreams", "version": "1.87.0", "description": "Boost iostreams module", "homepage": "https://www.boost.org/libs/iostreams", "license": "BSL-1.0", "supports": "!uwp", "dependencies": [{"name": "boost-assert", "version>=": "1.87.0"}, {"name": "boost-cmake", "version>=": "1.87.0"}, {"name": "boost-config", "version>=": "1.87.0"}, {"name": "boost-core", "version>=": "1.87.0"}, {"name": "boost-detail", "version>=": "1.87.0"}, {"name": "boost-function", "version>=": "1.87.0"}, {"name": "boost-headers", "version>=": "1.87.0"}, {"name": "boost-integer", "version>=": "1.87.0"}, {"name": "boost-iterator", "version>=": "1.87.0"}, {"name": "boost-mpl", "version>=": "1.87.0"}, {"name": "boost-numeric-conversion", "version>=": "1.87.0"}, {"name": "boost-preprocessor", "version>=": "1.87.0"}, {"name": "boost-random", "platform": "!uwp", "version>=": "1.87.0"}, {"name": "boost-range", "version>=": "1.87.0"}, {"name": "boost-regex", "version>=": "1.87.0"}, {"name": "boost-smart-ptr", "version>=": "1.87.0"}, {"name": "boost-static-assert", "version>=": "1.87.0"}, {"name": "boost-throw-exception", "version>=": "1.87.0"}, {"name": "boost-type-traits", "version>=": "1.87.0"}, {"name": "boost-utility", "version>=": "1.87.0"}], "default-features": ["bzip2", "lzma", "zlib", "zstd"], "features": {"bzip2": {"description": "Support bzip2 filters", "dependencies": ["bzip2"]}, "lzma": {"description": "Support LZMA/xz filters", "dependencies": ["liblzma"]}, "zlib": {"description": "Support zlib filters", "dependencies": ["zlib"]}, "zstd": {"description": "Support zstd filters", "dependencies": ["zstd"]}}}