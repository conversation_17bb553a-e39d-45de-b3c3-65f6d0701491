diff --git a/Makefile.in b/Makefile.in
index 2f05c29..16a150c 100644
--- a/Makefile.in
+++ b/Makefile.in
@@ -26,7 +26,7 @@ include config.make
 PRE_CPPFLAGS = -I.
 EXTRA_CFLAGS = $(CCPIC)
 
-LIBTARGETS = @IF_STATIC@ libnettle.a @IF_HOGWEED@ libhogweed.a
+LIBTARGETS = @IF_STATIC@ $(LIBNETTLE_FILE) @IF_HOGWEED@ $(LIBHOGWEED_FILE)
 SHLIBTARGETS = @IF_SHARED@ $(LIBNETTLE_FORLINK) @IF_HOGWEED@ $(LIBHOGWEED_FORLINK)
 
 getopt_SOURCES = getopt.c getopt1.c
@@ -276,13 +276,13 @@ nettle_OBJS = $(nettle_SOURCES:.c=.$(OBJEXT)) \
 hogweed_OBJS = $(hogweed_SOURCES:.c=.$(OBJEXT)) \
 	       $(OPT_HOGWEED_OBJS) @IF_MINI_GMP@ mini-gmp.$(OBJEXT)
 
-libnettle.a: $(nettle_OBJS)
+$(LIBNETTLE_FILE): $(nettle_OBJS)
 	-rm -f $@
 	$(AR) $(ARFLAGS) $@ $(nettle_OBJS)
 	$(RANLIB) $@
 	echo nettle > libnettle.stamp
 
-libhogweed.a: $(hogweed_OBJS)
+$(LIBHOGWEED_FILE): $(hogweed_OBJS)
 	-rm -f $@
 	$(AR) $(ARFLAGS) $@ $(hogweed_OBJS)
 	$(RANLIB) $@
diff --git a/configure.ac b/configure.ac
index c020afd..0fb201b 100644
--- a/configure.ac
+++ b/configure.ac
@@ -786,8 +786,8 @@ case "$host_os" in
     # linking to the DLL, is installed into the lib dir.
     case "$host_os" in
       mingw32*)
-        LIBNETTLE_FORLINK='libnettle-$(LIBNETTLE_MAJOR).dll'
-        LIBHOGWEED_FORLINK='libhogweed-$(LIBHOGWEED_MAJOR).dll'
+        LIBNETTLE_FORLINK='nettle-$(LIBNETTLE_MAJOR).dll'
+        LIBHOGWEED_FORLINK='hogweed-$(LIBHOGWEED_MAJOR).dll'
         ;;
       cygwin*)
         LIBNETTLE_FORLINK='cygnettle-$(LIBNETTLE_MAJOR).dll'
@@ -808,16 +808,16 @@ case "$host_os" in
         W64_ABI=yes
     fi
     LIBNETTLE_SONAME=''
-    LIBNETTLE_FILE='libnettle.dll.a'
+    LIBNETTLE_FILE='nettle.lib'
     LIBNETTLE_FILE_SRC='$(LIBNETTLE_FILE)'
-    LIBNETTLE_LINK='$(CC) $(CFLAGS) $(LDFLAGS) -shared -Wl,--out-implib=$(LIBNETTLE_FILE) -Wl,--export-all-symbols -Wl,--enable-auto-import -Wl,--whole-archive'
-    LIBNETTLE_LIBS='-Wl,--no-whole-archive $(LIBS)'
+    LIBNETTLE_LINK='$(CC) $(CPPFLAGS) -Wl,$(LDFLAGS) -Wl,-DLL -Wl,-IMPLIB:$(LIBNETTLE_FILE) -Wl,-DEF:nettle.def'
+    LIBNETTLE_LIBS='$(LIBS)'
 
     LIBHOGWEED_SONAME=''
-    LIBHOGWEED_FILE='libhogweed.dll.a'
+    LIBHOGWEED_FILE='hogweed.lib'
     LIBHOGWEED_FILE_SRC='$(LIBHOGWEED_FILE)'
-    LIBHOGWEED_LINK='$(CC) $(CFLAGS) $(LDFLAGS) -shared -Wl,--out-implib=$(LIBHOGWEED_FILE) -Wl,--export-all-symbols -Wl,--enable-auto-import -Wl,--whole-archive'
-    LIBHOGWEED_LIBS='-Wl,--no-whole-archive $(LIBS) libnettle.dll.a'
+    LIBHOGWEED_LINK='$(CC) $(CPPFLAGS) -Wl,$(LDFLAGS) -Wl,-DLL -Wl,-IMPLIB:$(LIBHOGWEED_FILE) -Wl,-DEF:hogweed.def'
+    LIBHOGWEED_LIBS='$(LIBS) $(LIBNETTLE_FILE)'
     IF_DLL=''
     ;;
   darwin*)
