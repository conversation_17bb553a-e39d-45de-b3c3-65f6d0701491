diff --git a/Makefile.in b/Makefile.in
index 2bf7f1e8..3e0ba565 100644
--- a/Makefile.in
+++ b/Makefile.in
@@ -19,7 +19,7 @@ OPT_NETTLE_SOURCES = @OPT_NETTLE_SOURCES@
 
 FAT_TEST_LIST = @FAT_TEST_LIST@
 
-SUBDIRS = tools testsuite examples
+SUBDIRS = @IF_TOOLS@ tools
 
 include config.make
 
diff --git a/configure.ac b/configure.ac
index 4f27e663..a72b732b 100644
--- a/configure.ac
+++ b/configure.ac
@@ -24,6 +24,14 @@ AC_SUBST([MINOR_VERSION])
 
 AC_CANONICAL_HOST
 
+AC_ARG_ENABLE(tools,[])
+if test "x$enable_tools" = xyes ; then
+  IF_TOOLS=''
+else
+  IF_TOOLS='#'
+fi
+AC_SUBST([IF_TOOLS])
+
 # Command line options
 AC_ARG_WITH(include-path,
   AS_HELP_STRING([--with-include-path], [A colon-separated list of directories to search for include files]),,
