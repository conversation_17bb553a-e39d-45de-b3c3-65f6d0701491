diff --git a/Makefile.in b/Makefile.in
index 2bf7f1e8..4b80c8df 100644
--- a/Makefile.in
+++ b/Makefile.in
@@ -1,3 +1,6 @@
+# Using native tools, either this build or from host triplet
+HOST_TOOLS_PREFIX ?= .
+
 # Nettle Makefile
 
 @SET_MAKE@
@@ -347,11 +350,11 @@ des_headers = rotors.h keymap.h
 # Generate DES headers.
 $(des_headers): desdata.stamp
 	f="$(srcdir)/`basename $@`"; \
-	  ./desdata$(EXEEXT_FOR_BUILD) $(@F) > $${f}T; \
+	  $(HOST_TOOLS_PREFIX)/desdata$(EXEEXT_FOR_BUILD) $(@F) > $${f}T; \
 	  test -s $${f}T && mv -f $${f}T $$f
 
-desdata.stamp: desdata.c
-	$(MAKE) desdata$(EXEEXT_FOR_BUILD)
+./desdata$(EXEEXT_FOR_BUILD): desdata.c
+desdata.stamp: $(HOST_TOOLS_PREFIX)/desdata$(EXEEXT_FOR_BUILD)
 	echo stamp > desdata.stamp
 
 des.$(OBJEXT): des.c des.h $(des_headers)
@@ -364,7 +367,7 @@ des.$(OBJEXT): des.c des.h $(des_headers)
 # k = 11, c =  6, S = 192, T =  44 ( 33 A + 11 D)  9 KB
 # k = 16, c =  6, S = 128, T =  48 ( 32 A + 16 D)  6 KB
 ecc-secp192r1.h: eccdata.stamp
-	./eccdata$(EXEEXT_FOR_BUILD) secp192r1 8 6 $(NUMB_BITS) > $@T && mv $@T $@
+	$(HOST_TOOLS_PREFIX)/eccdata$(EXEEXT_FOR_BUILD) secp192r1 8 6 $(NUMB_BITS) > $@T && mv $@T $@
 
 # Some reasonable choices for 224:
 # k = 16, c =  7, S = 256, T =  48 ( 32 A + 16 D) ~16 KB
@@ -372,7 +375,7 @@ ecc-secp192r1.h: eccdata.stamp
 # k = 13, c =  6, S = 192, T =  52 ( 39 A + 13 D) ~12 KB
 # k =  9, c =  5, S = 160, T =  54 ( 45 A +  9 D) ~10 KB
 ecc-secp224r1.h: eccdata.stamp
-	./eccdata$(EXEEXT_FOR_BUILD) secp224r1 16 7 $(NUMB_BITS) > $@T && mv $@T $@
+	$(HOST_TOOLS_PREFIX)/eccdata$(EXEEXT_FOR_BUILD) secp224r1 16 7 $(NUMB_BITS) > $@T && mv $@T $@
 
 # Some reasonable choices for 256:
 # k =  9, c =  6, S = 320, T =  54 ( 45 A +  9 D) 20 KB
@@ -380,7 +383,7 @@ ecc-secp224r1.h: eccdata.stamp
 # k = 19, c =  7, S = 256, T =  57 ( 38 A + 19 D) 16 KB
 # k = 15, c =  6, S = 192, T =  60 ( 45 A + 15 D) 12 KB
 ecc-secp256r1.h: eccdata.stamp
-	./eccdata$(EXEEXT_FOR_BUILD) secp256r1 11 6 $(NUMB_BITS) > $@T && mv $@T $@
+	$(HOST_TOOLS_PREFIX)/eccdata$(EXEEXT_FOR_BUILD) secp256r1 11 6 $(NUMB_BITS) > $@T && mv $@T $@
 
 # Some reasonable choices for 384:
 # k = 16, c =  6, S = 256, T =  80 ( 64 A + 16 D) 24 KB
@@ -391,7 +394,7 @@ ecc-secp256r1.h: eccdata.stamp
 # k = 16, c =  5, S = 160, T =  96 ( 80 A + 16 D) 15 KB
 # k = 32, c =  6, S = 128, T =  96 ( 64 A + 32 D) 12 KB
 ecc-secp384r1.h: eccdata.stamp
-	./eccdata$(EXEEXT_FOR_BUILD) secp384r1 32 6 $(NUMB_BITS) > $@T && mv $@T $@
+	$(HOST_TOOLS_PREFIX)/eccdata$(EXEEXT_FOR_BUILD) secp384r1 32 6 $(NUMB_BITS) > $@T && mv $@T $@
 
 # Some reasonable choices for 521:
 # k = 29, c =  6, S = 192, T = 116 ( 87 A + 29 D) ~27 KB
@@ -399,14 +402,14 @@ ecc-secp384r1.h: eccdata.stamp
 # k = 44, c =  6, S = 128, T = 132 ( 88 A + 44 D) ~18 KB
 # k = 35, c =  5, S =  96, T = 140 (105 A + 35 D) ~14 KB
 ecc-secp521r1.h: eccdata.stamp
-	./eccdata$(EXEEXT_FOR_BUILD) secp521r1 44 6 $(NUMB_BITS) > $@T && mv $@T $@
+	$(HOST_TOOLS_PREFIX)/eccdata$(EXEEXT_FOR_BUILD) secp521r1 44 6 $(NUMB_BITS) > $@T && mv $@T $@
 
 # Parameter choices mostly the same as for ecc-secp256r1.h.
 ecc-curve25519.h: eccdata.stamp
-	./eccdata$(EXEEXT_FOR_BUILD) curve25519 11 6 $(NUMB_BITS) > $@T && mv $@T $@
+	$(HOST_TOOLS_PREFIX)/eccdata$(EXEEXT_FOR_BUILD) curve25519 11 6 $(NUMB_BITS) > $@T && mv $@T $@
 
 ecc-curve448.h: eccdata.stamp
-	./eccdata$(EXEEXT_FOR_BUILD) curve448 38 6 $(NUMB_BITS) > $@T && mv $@T $@
+	$(HOST_TOOLS_PREFIX)/eccdata$(EXEEXT_FOR_BUILD) curve448 38 6 $(NUMB_BITS) > $@T && mv $@T $@
 
 # Some reasonable choices for 256:
 # k =  9, c =  6, S = 320, T =  54 ( 45 A +  9 D) 20 KB
@@ -414,7 +417,7 @@ ecc-curve448.h: eccdata.stamp
 # k = 19, c =  7, S = 256, T =  57 ( 38 A + 19 D) 16 KB
 # k = 15, c =  6, S = 192, T =  60 ( 45 A + 15 D) 12 KB
 ecc-gost-gc256b.h: eccdata.stamp
-	./eccdata$(EXEEXT_FOR_BUILD) gost_gc256b 11 6 $(NUMB_BITS) > $@T && mv $@T $@
+	$(HOST_TOOLS_PREFIX)/eccdata$(EXEEXT_FOR_BUILD) gost_gc256b 11 6 $(NUMB_BITS) > $@T && mv $@T $@
 
 # Some reasonable choices for 512:
 # k = 22, c =  6, S = 256, T = 110 ( 88 A + 22 D) 32 KB
@@ -423,10 +426,10 @@ ecc-gost-gc256b.h: eccdata.stamp
 # k = 43, c =  6, S = 128, T = 129 ( 86 A + 43 D) 16 KB
 # k = 35, c =  5, S =  96, T = 140 (105 A + 35 D) 12 KB
 ecc-gost-gc512a.h: eccdata.stamp
-	./eccdata$(EXEEXT_FOR_BUILD) gost_gc512a 43 6 $(NUMB_BITS) > $@T && mv $@T $@
+	$(HOST_TOOLS_PREFIX)/eccdata$(EXEEXT_FOR_BUILD) gost_gc512a 43 6 $(NUMB_BITS) > $@T && mv $@T $@
 
-eccdata.stamp: eccdata.c
-	$(MAKE) eccdata$(EXEEXT_FOR_BUILD)
+./eccdata$(EXEEXT_FOR_BUILD): eccdata.c
+eccdata.stamp: $(HOST_TOOLS_PREFIX)/eccdata$(EXEEXT_FOR_BUILD)
 	echo stamp > eccdata.stamp
 
 ecc-curve25519.$(OBJEXT): ecc-curve25519.h
diff --git a/aclocal.m4 b/aclocal.m4
index 629db8a7..0cf32544 100644
--- a/aclocal.m4
+++ b/aclocal.m4
@@ -345,6 +345,7 @@ if AC_TRY_EVAL(gmp_compile); then
   if (./a.out || ./b.out || ./a.exe || ./a_out.exe || ./conftest) >&AS_MESSAGE_LOG_FD 2>&1; then
     cc_for_build_works=yes
   fi
+  cc_for_build_works=yes # forced
 fi
 rm -f conftest* a.out b.out a.exe a_out.exe
 AC_MSG_RESULT($cc_for_build_works)
