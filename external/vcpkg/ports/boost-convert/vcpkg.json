{"$comment": "Automatically generated by scripts/boost/generate-ports.ps1", "name": "boost-convert", "version": "1.87.0", "description": "Boost convert module", "homepage": "https://www.boost.org/libs/convert", "license": "BSL-1.0", "dependencies": [{"name": "boost-cmake", "version>=": "1.87.0"}, {"name": "boost-config", "version>=": "1.87.0"}, {"name": "boost-core", "version>=": "1.87.0"}, {"name": "boost-function-types", "version>=": "1.87.0"}, {"name": "boost-headers", "version>=": "1.87.0"}, {"name": "boost-lexical-cast", "version>=": "1.87.0"}, {"name": "boost-math", "version>=": "1.87.0"}, {"name": "boost-mpl", "version>=": "1.87.0"}, {"name": "boost-optional", "version>=": "1.87.0"}, {"name": "boost-parameter", "version>=": "1.87.0"}, {"name": "boost-range", "version>=": "1.87.0"}, {"name": "boost-spirit", "version>=": "1.87.0"}, {"name": "boost-type-traits", "version>=": "1.87.0"}]}