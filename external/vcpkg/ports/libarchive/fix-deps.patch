diff --git a/CMakeLists.txt b/CMakeLists.txt
index dc61b28..7dcdefd 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -446,6 +446,8 @@ IF(DEFINED __GNUWIN32PATH AND EXISTS "${__GNUWIN32PATH}")
 ENDIF(DEFINED __GNUWIN32PATH AND EXISTS "${__GNUWIN32PATH}")
 
 SET(ADDITIONAL_LIBS "")
+SET(LIBARCHIVE_LIBS_PRIVATE "") # additional libs for which the pc module is unknown
+SET(LIBSREQUIRED "") # pc modules for additonal libs
 #
 # Find ZLIB
 #
@@ -462,6 +464,7 @@ IF(ZLIB_FOUND)
   SET(HAVE_ZLIB_H 1)
   INCLUDE_DIRECTORIES(${ZLIB_INCLUDE_DIR})
   LIST(APPEND ADDITIONAL_LIBS ${ZLIB_LIBRARIES})
+  STRING(APPEND LIBSREQUIRED " zlib")
   IF(WIN32 AND NOT CYGWIN)
     SET(ZLIB_WINAPI OFF) # skip following test, it crashes with weird message box
     IF(ZLIB_WINAPI)
@@ -494,6 +497,7 @@ IF(BZIP2_FOUND)
   SET(HAVE_BZLIB_H 1)
   INCLUDE_DIRECTORIES(${BZIP2_INCLUDE_DIR})
   LIST(APPEND ADDITIONAL_LIBS ${BZIP2_LIBRARIES})
+  STRING(APPEND LIBSREQUIRED " bzip2")
   # Test if a macro is needed for the library.
   TRY_MACRO_FOR_LIBRARY(
     "${BZIP2_INCLUDE_DIR}" "${BZIP2_LIBRARIES}"
@@ -522,6 +526,8 @@ ENDIF()
 IF(LIBLZMA_FOUND)
   SET(HAVE_LIBLZMA 1)
   SET(HAVE_LZMA_H 1)
+  LIST(APPEND ADDITIONAL_LIBS LibLZMA::LibLZMA)
+  STRING(APPEND LIBSREQUIRED " liblzma")
   CMAKE_PUSH_CHECK_STATE()
   SET(CMAKE_REQUIRED_INCLUDES ${LIBLZMA_INCLUDE_DIR})
   SET(CMAKE_REQUIRED_LIBRARIES ${LIBLZMA_LIBRARIES})
@@ -569,6 +575,7 @@ IF(LZO2_FOUND)
   SET(HAVE_LZO_LZO1X_H 1)
   INCLUDE_DIRECTORIES(${LZO2_INCLUDE_DIR})
   LIST(APPEND ADDITIONAL_LIBS ${LZO2_LIBRARY})
+  STRING(APPEND LIBSREQUIRED " lzo2")
   #
   # TODO: test for static library.
   #
@@ -597,6 +604,7 @@ IF(LIBB2_FOUND)
   SET(ARCHIVE_BLAKE2 FALSE)
   LIST(APPEND ADDITIONAL_LIBS ${LIBB2_LIBRARY})
   INCLUDE_DIRECTORIES(${LIBB2_INCLUDE_DIR})
+  LIST(APPEND LIBARCHIVE_LIBS_PRIVATE ${LIBB2_LIBRARY})
   CMAKE_PUSH_CHECK_STATE()
   SET(CMAKE_REQUIRED_LIBRARIES ${LIBB2_LIBRARY})
   SET(CMAKE_REQUIRED_INCLUDES ${LIBB2_INCLUDE_DIR})
@@ -609,6 +617,8 @@ ENDIF(LIBB2_FOUND)
 # Find LZ4
 #
 IF(ENABLE_LZ4)
+  FIND_PACKAGE(LZ4 NAMES lz4 CONFIG REQUIRED)
+elseif(0)
   IF (LZ4_INCLUDE_DIR)
     # Already in cache, be silent
     SET(LZ4_FIND_QUIETLY TRUE)
@@ -624,6 +634,10 @@ ENDIF(ENABLE_LZ4)
 IF(LZ4_FOUND)
   SET(HAVE_LIBLZ4 1)
   SET(HAVE_LZ4_H 1)
+  SET(HAVE_LZ4HC_H 1)
+  LIST(APPEND ADDITIONAL_LIBS lz4::lz4)
+  STRING(APPEND LIBSREQUIRED " liblz4")
+elseif(0)
   CMAKE_PUSH_CHECK_STATE()	# Save the state of the variables
   SET(CMAKE_REQUIRED_INCLUDES ${LZ4_INCLUDE_DIR})
   CHECK_INCLUDE_FILES("lz4hc.h" HAVE_LZ4HC_H)
@@ -640,6 +654,8 @@ MARK_AS_ADVANCED(CLEAR LZ4_LIBRARY)
 # Find Zstd
 #
 IF(ENABLE_ZSTD)
+  FIND_PACKAGE(ZSTD NAMES zstd CONFIG REQUIRED)
+elseif(0)
   IF (ZSTD_INCLUDE_DIR)
     # Already in cache, be silent
     SET(ZSTD_FIND_QUIETLY TRUE)
@@ -659,6 +675,11 @@ ELSE(ENABLE_ZSTD)
 ENDIF(ENABLE_ZSTD)
 IF(ZSTD_FOUND)
   SET(HAVE_ZSTD_H 1)
+  SET(HAVE_LIBZSTD 1)
+  SET(HAVE_ZSTD_compressStream 1)
+  LIST(APPEND ADDITIONAL_LIBS zstd::libzstd)
+  STRING(APPEND LIBSREQUIRED " libzstd")
+elseif(0)
   INCLUDE_DIRECTORIES(${ZSTD_INCLUDE_DIR})
   LIST(APPEND ADDITIONAL_LIBS ${ZSTD_LIBRARY})
   CMAKE_PUSH_CHECK_STATE()
@@ -768,6 +789,7 @@ IF(ENABLE_CNG)
   LA_CHECK_INCLUDE_FILE("bcrypt.h" HAVE_BCRYPT_H)
   IF(HAVE_BCRYPT_H)
     LIST(APPEND ADDITIONAL_LIBS "bcrypt")
+    LIST(APPEND LIBARCHIVE_LIBS_PRIVATE "bcrypt")
   ENDIF(HAVE_BCRYPT_H)
 ELSE(ENABLE_CNG)
   UNSET(HAVE_BCRYPT_H CACHE)
@@ -799,6 +821,7 @@ IF(ENABLE_MBEDTLS)
   IF(MBEDTLS_FOUND)
     SET(HAVE_LIBMBEDCRYPTO 1)
     LIST(APPEND ADDITIONAL_LIBS ${MBEDCRYPTO_LIBRARY})
+    LIST(APPEND LIBARCHIVE_LIBS_PRIVATE ${MBEDCRYPTO_LIBRARY})
     INCLUDE_DIRECTORIES(${MBEDTLS_INCLUDE_DIRS})
 
     LIST(APPEND CMAKE_REQUIRED_INCLUDES ${MBEDTLS_INCLUDE_DIRS})
@@ -819,6 +842,7 @@ IF(ENABLE_NETTLE)
   IF(NETTLE_FOUND)
     SET(HAVE_LIBNETTLE 1)
     LIST(APPEND ADDITIONAL_LIBS ${NETTLE_LIBRARIES})
+    STRING(APPEND LIBSREQUIRED " nettle")
     INCLUDE_DIRECTORIES(${NETTLE_INCLUDE_DIR})
 
     LIST(APPEND CMAKE_REQUIRED_INCLUDES ${NETTLE_INCLUDE_DIR})
@@ -842,8 +866,8 @@ IF(ENABLE_OPENSSL AND NOT CMAKE_SYSTEM_NAME MATCHES "Darwin")
   FIND_PACKAGE(OpenSSL)
   IF(OPENSSL_FOUND)
     SET(HAVE_LIBCRYPTO 1)
-    INCLUDE_DIRECTORIES(${OPENSSL_INCLUDE_DIR})
-    LIST(APPEND ADDITIONAL_LIBS ${OPENSSL_CRYPTO_LIBRARY})
+    LIST(APPEND ADDITIONAL_LIBS OpenSSL::Crypto)
+    STRING(APPEND LIBSREQUIRED " libcrypto")
     SET(CMAKE_REQUIRED_LIBRARIES ${OPENSSL_CRYPTO_LIBRARY})
     SET(CMAKE_REQUIRED_INCLUDES ${OPENSSL_INCLUDE_DIR})
     LA_CHECK_INCLUDE_FILE("openssl/evp.h" HAVE_OPENSSL_EVP_H)
@@ -861,6 +885,7 @@ IF(NOT OPENSSL_FOUND)
     SET(CMAKE_REQUIRED_LIBRARIES "md")
     FIND_LIBRARY(LIBMD_LIBRARY NAMES md)
     LIST(APPEND ADDITIONAL_LIBS ${LIBMD_LIBRARY})
+    LIST(APPEND LIBARCHIVE_LIBS_PRIVATE ${LIBMD_LIBRARY})
     CMAKE_POP_CHECK_STATE()	# Restore the state of the variables
   ENDIF(LIBMD_FOUND)
 ENDIF(NOT OPENSSL_FOUND)
@@ -974,7 +999,8 @@ main(int argc, char **argv)
         IF ("${IMPLEMENTATION}" MATCHES "^OPENSSL$" AND OPENSSL_FOUND)
           INCLUDE_DIRECTORIES(${OPENSSL_INCLUDE_DIR})
 	  LIST(APPEND ADDITIONAL_LIBS ${OPENSSL_LIBRARIES})
-	   LIST(REMOVE_DUPLICATES ADDITIONAL_LIBS)
+	   #LIST(REMOVE_DUPLICATES ADDITIONAL_LIBS)
+          STRING(APPEND LIBSREQUIRED " libssl")
         ENDIF ("${IMPLEMENTATION}" MATCHES "^OPENSSL$" AND OPENSSL_FOUND)
       ENDIF (ARCHIVE_CRYPTO_${ALGORITHM}_${IMPLEMENTATION})
       ENDIF(NOT ARCHIVE_CRYPTO_${ALGORITHM})
@@ -1148,6 +1174,7 @@ IF(ENABLE_ICONV)
       CHECK_ICONV("libiconv" "")
       IF (HAVE_ICONV)
         LIST(APPEND ADDITIONAL_LIBS ${LIBICONV_PATH})
+        LIST(APPEND LIBARCHIVE_LIBS_PRIVATE ${LIBICONV_PATH})
       ENDIF(HAVE_ICONV)
     ENDIF(NOT HAVE_ICONV AND LIBICONV_PATH)
   ENDIF(ICONV_INCLUDE_DIR)
@@ -1181,6 +1208,7 @@ IF(ENABLE_ICONV)
       ENDIF(WIN32 AND NOT CYGWIN)
       IF(HAVE_LOCALE_CHARSET)
         LIST(APPEND ADDITIONAL_LIBS ${LIBCHARSET_PATH})
+        LIST(APPEND LIBARCHIVE_LIBS_PRIVATE ${LIBCHARSET_PATH})
       ENDIF(HAVE_LOCALE_CHARSET)
     ENDIF(LIBCHARSET_PATH)
   ENDIF(LIBICONV_PATH)
@@ -1214,6 +1242,7 @@ IF(LIBXML2_FOUND)
   CMAKE_PUSH_CHECK_STATE()	# Save the state of the variables
   INCLUDE_DIRECTORIES(${LIBXML2_INCLUDE_DIR})
   LIST(APPEND ADDITIONAL_LIBS ${LIBXML2_LIBRARIES})
+  STRING(APPEND LIBSREQUIRED " libxml-2.0")
   SET(HAVE_LIBXML2 1)
   # libxml2's include files use iconv.h
   SET(CMAKE_REQUIRED_INCLUDES ${ICONV_INCLUDE_DIR} ${LIBXML2_INCLUDE_DIR})
@@ -1243,6 +1272,7 @@ ELSE(LIBXML2_FOUND)
     CMAKE_PUSH_CHECK_STATE()	# Save the state of the variables
     INCLUDE_DIRECTORIES(${EXPAT_INCLUDE_DIR})
     LIST(APPEND ADDITIONAL_LIBS ${EXPAT_LIBRARIES})
+    STRING(APPEND LIBSREQUIRED " expat")
     SET(HAVE_LIBEXPAT 1)
     LA_CHECK_INCLUDE_FILE("expat.h" HAVE_EXPAT_H)
     CMAKE_POP_CHECK_STATE()	# Restore the state of the variables
@@ -1273,6 +1303,7 @@ IF(POSIX_REGEX_LIB MATCHES "^(AUTO|LIBC|LIBREGEX)$")
         CHECK_FUNCTION_EXISTS_GLIBC(regcomp HAVE_REGCOMP_LIBREGEX)
         IF(HAVE_REGCOMP_LIBREGEX)
           LIST(APPEND ADDITIONAL_LIBS ${REGEX_LIBRARY})
+          LIST(APPEND LIBARCHIVE_LIBS_PRIVATE ${REGEX_LIBRARY})
           #
           # If regex.h is not found, retry looking for regex.h at
           # REGEX_INCLUDE_DIR
@@ -1322,6 +1353,7 @@ IF(NOT FOUND_POSIX_REGEX_LIB AND POSIX_REGEX_LIB MATCHES "^(AUTO|LIBPCREPOSIX)$"
   IF(PCREPOSIX_FOUND)
     INCLUDE_DIRECTORIES(${PCRE_INCLUDE_DIR})
     LIST(APPEND ADDITIONAL_LIBS ${PCREPOSIX_LIBRARIES})
+    LIST(APPEND LIBARCHIVE_LIBS_PRIVATE  ${PCREPOSIX_LIBRARIES})
     # Test if a macro is needed for the library.
     TRY_MACRO_FOR_LIBRARY(
       "${PCRE_INCLUDE_DIR}" "${PCREPOSIX_LIBRARIES}"
@@ -1333,6 +1365,7 @@ IF(NOT FOUND_POSIX_REGEX_LIB AND POSIX_REGEX_LIB MATCHES "^(AUTO|LIBPCREPOSIX)$"
 	ELSEIF(NOT WITHOUT_PCRE_STATIC AND NOT PCRE_STATIC AND PCRE_FOUND)
 	  # Determine if pcre static libraries are to be used.
       LIST(APPEND ADDITIONAL_LIBS ${PCRE_LIBRARIES})
+      LIST(APPEND LIBARCHIVE_LIBS_PRIVATE ${PCRE_LIBRARIES})
       SET(TMP_LIBRARIES ${PCREPOSIX_LIBRARIES} ${PCRE_LIBRARIES})
       MESSAGE(STATUS "trying again with -lpcre included")
       TRY_MACRO_FOR_LIBRARY(
@@ -1348,6 +1381,7 @@ IF(NOT FOUND_POSIX_REGEX_LIB AND POSIX_REGEX_LIB MATCHES "^(AUTO|LIBPCREPOSIX)$"
         # ___chkstk_ms.
         MESSAGE(STATUS "Visual Studio build detected, trying again with -lgcc included")
         LIST(APPEND ADDITIONAL_LIBS ${LIBGCC_LIBRARIES})
+        LIST(APPEND LIBARCHIVE_LIBS_PRIVATE ${LIBGCC_LIBRARIES})
         SET(TMP_LIBRARIES ${PCREPOSIX_LIBRARIES} ${PCRE_LIBRARIES} ${LIBGCC_LIBRARIES})
           TRY_MACRO_FOR_LIBRARY(
             "${PCRE_INCLUDE_DIR}" "${TMP_LIBRARIES}"
@@ -1919,6 +1953,7 @@ IF(ENABLE_ACL)
     SET(CMAKE_REQUIRED_LIBRARIES "acl")
     FIND_LIBRARY(ACL_LIBRARY NAMES acl)
     LIST(APPEND ADDITIONAL_LIBS ${ACL_LIBRARY})
+    LIST(APPEND LIBARCHIVE_LIBS_PRIVATE ${ACL_LIBRARY})
   ENDIF(HAVE_LIBACL)
 
   CHECK_TYPE_EXISTS(acl_t "sys/types.h;sys/acl.h" HAVE_ACL_T)
@@ -2058,6 +2093,7 @@ int main(void) { return ACL_SYNCHRONIZE; }" HAVE_DECL_ACL_SYNCHRONIZE)
     SET(CMAKE_REQUIRED_LIBRARIES "richacl")
     FIND_LIBRARY(RICHACL_LIBRARY NAMES richacl)
     LIST(APPEND ADDITIONAL_LIBS ${RICHACL_LIBRARY})
+    LIST(APPEND LIBARCHIVE_LIBS_PRIVATE ${RICHACL_LIBRARY})
   ENDIF(HAVE_LIBRICHACL)
 
   CHECK_STRUCT_HAS_MEMBER("struct richace" e_type "sys/richacl.h"
diff --git a/build/cmake/CreatePkgConfigFile.cmake b/build/cmake/CreatePkgConfigFile.cmake
index bc5a43f..422b83b 100644
--- a/build/cmake/CreatePkgConfigFile.cmake
+++ b/build/cmake/CreatePkgConfigFile.cmake
@@ -8,7 +8,7 @@ SET(libdir \${exec_prefix}/lib)
 SET(includedir \${prefix}/include)
 # Now, this is not particularly pretty, nor is it terribly accurate...
 # Loop over all our additional libs
-FOREACH(mylib ${ADDITIONAL_LIBS})
+FOREACH(mylib ${LIBARCHIVE_LIBS_PRIVATE})
 	# Extract the filename from the absolute path
 	GET_FILENAME_COMPONENT(mylib_name ${mylib} NAME_WE)
 	# Strip the lib prefix
@@ -16,10 +16,6 @@ FOREACH(mylib ${ADDITIONAL_LIBS})
 	# Append it to our LIBS string
 	SET(LIBS "${LIBS} -l${mylib_name}")
 ENDFOREACH()
-# libxml2 is easier, since it's already using pkg-config
-FOREACH(mylib ${PC_LIBXML_STATIC_LDFLAGS})
-	SET(LIBS "${LIBS} ${mylib}")
-ENDFOREACH()
 # FIXME: The order of the libraries doesn't take dependencies into account,
 #	 thus there's a good chance it'll make some binutils versions unhappy...
 #	 This only affects Libs.private (looked up for static builds) though.
