{"name": "libarchive", "version": "3.7.8", "port-version": 1, "description": "Library for reading and writing streaming archives", "homepage": "https://www.libarchive.org", "license": null, "supports": "!uwp", "dependencies": [{"name": "vcpkg-cmake", "host": true}, "zlib"], "default-features": ["bzip2", "crypto", "libxml2", "lz4", "lzma", "zstd"], "features": {"bzip2": {"description": "BZip2 support", "dependencies": ["bzip2"]}, "crypto": {"description": "Support for cryptographic features", "dependencies": [{"name": "mbedtls", "platform": "osx"}, {"name": "openssl", "platform": "!osx"}]}, "libxml2": {"description": "Libxml2 support", "dependencies": ["libxml2"]}, "lz4": {"description": "Lz4 support", "dependencies": ["lz4"]}, "lzma": {"description": "Lzma support", "dependencies": ["liblzma"]}, "lzo": {"description": "Lzo support", "dependencies": ["lzo"]}, "zstd": {"description": "Zstd support", "dependencies": ["zstd"]}}}