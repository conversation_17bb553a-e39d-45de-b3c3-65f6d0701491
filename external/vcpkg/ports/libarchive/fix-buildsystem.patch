diff --git a/CMakeLists.txt b/CMakeLists.txt
index b2634da6..0d846877 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -148,10 +148,6 @@ IF (CMAKE_C_COMPILER_ID MATCHES "^XL$")
   SET(CMAKE_C_FLAGS_DEBUG "${CMAKE_C_FLAGS_DEBUG} -qinfo=pro:use")
 ENDIF(CMAKE_C_COMPILER_ID MATCHES "^XL$")
 IF (MSVC)
-  if (ENABLE_WERROR)
-    # /WX option is the same as gcc's -Werror option.
-    SET(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} /WX")
-  endif ()
   #################################################################
   # Set compile flags for debug build.
   # This is added into CMAKE_C_FLAGS when CMAKE_BUILD_TYPE is "Debug"
@@ -382,8 +378,6 @@ IF(DEFINED __GNUWIN32PATH AND EXISTS "${__GNUWIN32PATH}")
   # Maybe DLL path is "C:/Program Files/GnuWin32/bin".
   # The zlib and the bzip2 Setup program have installed programs and DLLs into
   # "C:/Program Files/GnuWin32" by default.
-  # This is convenience setting for Windows.
-  SET(CMAKE_PREFIX_PATH ${__GNUWIN32PATH} $(CMAKE_PREFIX_PATH))
   #
   # If you didn't use Setup program or installed into nonstandard path,
   # cmake cannot find out your zlib or bzip2 libraries and include files,
@@ -420,14 +414,7 @@ IF(ZLIB_FOUND)
   INCLUDE_DIRECTORIES(${ZLIB_INCLUDE_DIR})
   LIST(APPEND ADDITIONAL_LIBS ${ZLIB_LIBRARIES})
   IF(WIN32 AND NOT CYGWIN)
-    #
-    # Test if ZLIB_WINAPI macro is needed to use.
-    #
-    TRY_MACRO_FOR_LIBRARY(
-      "${ZLIB_INCLUDE_DIR}" "${ZLIB_LIBRARIES}"
-      RUNS
-      "#include <zlib.h>\nint main() {uLong f = zlibCompileFlags(); return (f&(1U<<10))?0:-1; }"
-      ZLIB_WINAPI)
+    SET(ZLIB_WINAPI OFF) # skip following test, it crashes with weird message box
     IF(ZLIB_WINAPI)
       ADD_DEFINITIONS(-DZLIB_WINAPI)
     ELSE(ZLIB_WINAPI)
@@ -1356,7 +1341,7 @@ CHECK_FUNCTION_EXISTS_GLIBC(localtime_r HAVE_LOCALTIME_R)
 CHECK_FUNCTION_EXISTS_GLIBC(lstat HAVE_LSTAT)
 CHECK_FUNCTION_EXISTS_GLIBC(lutimes HAVE_LUTIMES)
 CHECK_FUNCTION_EXISTS_GLIBC(mbrtowc HAVE_MBRTOWC)
-CHECK_FUNCTION_EXISTS_GLIBC(memmove HAVE_MEMMOVE)
+set(HAVE_MEMMOVE 1)
 CHECK_FUNCTION_EXISTS_GLIBC(mkdir HAVE_MKDIR)
 CHECK_FUNCTION_EXISTS_GLIBC(mkfifo HAVE_MKFIFO)
 CHECK_FUNCTION_EXISTS_GLIBC(mknod HAVE_MKNOD)
@@ -1388,10 +1373,10 @@ CHECK_FUNCTION_EXISTS_GLIBC(utime HAVE_UTIME)
 CHECK_FUNCTION_EXISTS_GLIBC(utimes HAVE_UTIMES)
 CHECK_FUNCTION_EXISTS_GLIBC(utimensat HAVE_UTIMENSAT)
 CHECK_FUNCTION_EXISTS_GLIBC(vfork HAVE_VFORK)
-CHECK_FUNCTION_EXISTS_GLIBC(wcrtomb HAVE_WCRTOMB)
-CHECK_FUNCTION_EXISTS_GLIBC(wcscmp HAVE_WCSCMP)
-CHECK_FUNCTION_EXISTS_GLIBC(wcscpy HAVE_WCSCPY)
-CHECK_FUNCTION_EXISTS_GLIBC(wcslen HAVE_WCSLEN)
-CHECK_FUNCTION_EXISTS_GLIBC(wctomb HAVE_WCTOMB)
+set(HAVE_WCRTOMB 1)
+set(HAVE_WCSCMP 1)
+set(HAVE_WCSCPY 1)
+set(HAVE_WCSLEN 1)
+set(HAVE_WCTOMB 1)
 CHECK_FUNCTION_EXISTS_GLIBC(_fseeki64 HAVE__FSEEKI64)
 CHECK_FUNCTION_EXISTS_GLIBC(_get_timezone HAVE__GET_TIMEZONE)
@@ -1405,10 +1390,10 @@ CHECK_FUNCTION_EXISTS(cygwin_conv_path HAVE_CYGWIN_CONV_PATH)
 CHECK_FUNCTION_EXISTS(fseeko HAVE_FSEEKO)
 CHECK_FUNCTION_EXISTS(strerror_r HAVE_STRERROR_R)
 CHECK_FUNCTION_EXISTS(strftime HAVE_STRFTIME)
-CHECK_FUNCTION_EXISTS(vprintf HAVE_VPRINTF)
-CHECK_FUNCTION_EXISTS(wmemcmp HAVE_WMEMCMP)
-CHECK_FUNCTION_EXISTS(wmemcpy HAVE_WMEMCPY)
-CHECK_FUNCTION_EXISTS(wmemmove HAVE_WMEMMOVE)
+set(HAVE_VPRINTF 1)
+set(HAVE_WMEMCMP 1)
+set(HAVE_WMEMCPY 1)
+set(HAVE_WMEMMOVE 1)
 
 CMAKE_POP_CHECK_STATE()	# Restore the state of the variables
 
@@ -2018,7 +2007,7 @@ INCLUDE(CreatePkgConfigFile)
 #
 # Register installation of PDF documents.
 #
-IF(WIN32 AND NOT CYGWIN)
+IF(0)
   #
   # On Windows platform, It's better that we install PDF documents
   # on one's computer.
diff --git a/libarchive/CMakeLists.txt b/libarchive/CMakeLists.txt
index f7fdfb68a..98d287e8c 100644
--- a/libarchive/CMakeLists.txt
+++ b/libarchive/CMakeLists.txt
@@ -267,11 +267,12 @@ IF(ENABLE_INSTALL)
             RUNTIME DESTINATION bin
             LIBRARY DESTINATION lib
             ARCHIVE DESTINATION lib)
+  ELSE()
+    INSTALL(TARGETS archive_static
+            RUNTIME DESTINATION bin
+            LIBRARY DESTINATION lib
+            ARCHIVE DESTINATION lib)
   ENDIF(BUILD_SHARED_LIBS)
-  INSTALL(TARGETS archive_static
-          RUNTIME DESTINATION bin
-          LIBRARY DESTINATION lib
-          ARCHIVE DESTINATION lib)
   INSTALL_MAN(${libarchive_MANS})
   INSTALL(FILES ${include_HEADERS} DESTINATION include)
 ENDIF()
