diff --git a/CMakeLists.txt b/CMakeLists.txt
index 07520043..7eec4059 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -1022,6 +1022,9 @@ if(WIN32)
     add_definitions(
             -D_CRT_SECURE_NO_WARNINGS
             -D_CRT_NONSTDC_NO_DEPRECATE)
+    if(CMAKE_SYSTEM_NAME STREQUAL "WindowsStore")
+        add_definitions(-DWINRT)
+    endif()
 
     include_directories(./WIN32-Code)
 endif()
diff --git a/evdns.c b/evdns.c
index 05e515c0..912f1115 100644
--- a/evdns.c
+++ b/evdns.c
@@ -4501,8 +4501,12 @@ evdns_get_default_hosts_filename(void)
 	char *path_out;
 	size_t len_out;
 
+#ifndef WINRT
 	if (! SHGetSpecialFolderPathA(NULL, path, CSIDL_SYSTEM, 0))
 		return NULL;
+#else
+	return NULL;
+#endif
 	len_out = strlen(path)+strlen(hostfile)+1;
 	path_out = mm_malloc(len_out);
 	evutil_snprintf(path_out, len_out, "%s%s", path, hostfile);
diff --git a/evutil.c b/evutil.c
index d46c997b..bad21ede 100644
--- a/evutil.c
+++ b/evutil.c
@@ -2240,6 +2240,9 @@ evutil_inet_pton_scope(int af, const char *src, void *dst, unsigned *indexp)
 	if (cp == NULL)
 		return evutil_inet_pton(af, src, dst);
 
+#ifdef WINRT
+	return -1;
+#else
 	if_index = if_nametoindex(cp + 1);
 	if (if_index == 0) {
 		/* Could be numeric */
@@ -2256,6 +2259,7 @@ evutil_inet_pton_scope(int af, const char *src, void *dst, unsigned *indexp)
 	r = evutil_inet_pton(af, tmp_src, dst);
 	free(tmp_src);
 	return r;
+#endif
 }
 
 int
