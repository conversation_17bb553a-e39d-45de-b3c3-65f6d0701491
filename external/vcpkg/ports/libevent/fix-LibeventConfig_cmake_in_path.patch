diff --git a/cmake/LibeventConfig.cmake.in b/cmake/LibeventConfig.cmake.in
index a12fc23c..fe108b8d 100644
--- a/cmake/LibeventConfig.cmake.in
+++ b/cmake/LibeventConfig.cmake.in
@@ -58,7 +58,7 @@ endif()
 
 # Get the path of the current file.
 get_filename_component(LIBEVENT_CMAKE_DIR "${CMAKE_CURRENT_LIST_FILE}" PATH)
-get_filename_component(_INSTALL_PREFIX "${LIBEVENT_CMAKE_DIR}/../../.." ABSOLUTE)
+get_filename_component(_INSTALL_PREFIX "${LIBEVENT_CMAKE_DIR}/../.." ABSOLUTE)
 
 macro(message_if_needed _flag _msg)
     if (NOT ${CMAKE_FIND_PACKAGE_NAME}_FIND_QUIETLY)
@@ -131,12 +131,10 @@ if(CONFIG_FOR_INSTALL_TREE)
         unset(_event_lib_rel CACHE)
         find_library(_event_lib_dbg
                     NAMES "event_${_comp}d"
-                    PATHS "${_INSTALL_PREFIX}/lib"
-                    NO_DEFAULT_PATH)
+                    PATHS "${_INSTALL_PREFIX}/lib")
         find_library(_event_lib_rel
                     NAMES "event_${_comp}"
-                    PATHS "${_INSTALL_PREFIX}/lib"
-                    NO_DEFAULT_PATH)
+                    PATHS "${_INSTALL_PREFIX}/lib")
         if(_event_lib_rel OR _event_lib_dbg)
             list(APPEND LIBEVENT_LIBRARIES "libevent::${_comp}")
             set_case_insensitive_found(${_comp})
