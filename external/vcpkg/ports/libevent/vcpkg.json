{"name": "libevent", "version": "2.1.12+20230128", "port-version": 1, "description": "An event notification library", "homepage": "https://github.com/libevent/libevent", "license": "BSD-3-<PERSON><PERSON>", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "default-features": ["thread"], "features": {"openssl": {"description": "Support for openssl", "dependencies": [{"name": "libevent", "features": ["thread"]}, "openssl"]}, "thread": {"description": "Support for thread"}}}