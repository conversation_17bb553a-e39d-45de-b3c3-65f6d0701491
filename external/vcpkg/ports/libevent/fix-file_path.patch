diff --git a/cmake/AddEventLibrary.cmake b/cmake/AddEventLibrary.cmake
index 04f5837..95d9808 100644
--- a/cmake/AddEventLibrary.cmake
+++ b/cmake/AddEventLibrary.cmake
@@ -42,7 +42,7 @@ macro(export_install_target TYPE LIB_NAME OUTER_INCLUDES)
         install(TARGETS "${LIB_NAME}_${TYPE}"
             LIBRARY DESTINATION "lib" COMPONENT lib
             ARCHIVE DESTINATION "lib" COMPONENT lib
-            RUNTIME DESTINATION "lib" COMPONENT lib
+            RUNTIME DESTINATION "bin" COMPONENT bin
             COMPONENT dev
         )
     else()
@@ -69,7 +69,7 @@ macro(export_install_target TYPE LIB_NAME OUTER_INCLUDES)
             EXPORT LibeventTargets-${TYPE}
             LIBRARY DESTINATION "lib" COMPONENT lib
             ARCHIVE DESTINATION "lib" COMPONENT lib
-            RUNTIME DESTINATION "lib" COMPONENT lib
+            RUNTIME DESTINATION "bin" COMPONENT bin
             COMPONENT dev
         )
     endif()
