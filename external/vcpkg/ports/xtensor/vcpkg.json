{"name": "xtensor", "version": "0.26.0", "description": "C++ tensors with broadcasting and lazy computing", "homepage": "https://github.com/xtensor-stack/xtensor", "license": "BSD-3-<PERSON><PERSON>", "dependencies": ["<PERSON><PERSON><PERSON>-<PERSON><PERSON>", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}, "xtl"], "features": {"tbb": {"description": "xtensor with tbb support", "dependencies": ["tbb"]}, "xsimd": {"description": "xtensor with xsimd support", "dependencies": ["xsimd"]}}}