diff --git a/CMakeLists.txt b/CMakeLists.txt
index 5c93655..f02dc48 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -69,7 +69,12 @@ if(XTENSOR_USE_XSIMD)
             message(STATUS "Found xsimd v${xsimd_VERSION}")
         endif()
     else()
-        find_package(xsimd ${xsimd_REQUIRED_VERSION} REQUIRED)
+        find_package(xsimd CONFIG REQUIRED)
+        if(${xsimd_VERSION} GREATER_EQUAL ${xsimd_REQUIRED_VERSION})
+            set(xsimd_REQUIRED_VERSION ${xsimd_VERSION})
+        else()
+            message(ERROR "Mismatch xsimd versions. Found '${xsimd_VERSION}' but requires: '${xsimd_REQUIRED_VERSION}'")
+        endif()
         message(STATUS "Found xsimd: ${xsimd_INCLUDE_DIRS}/xsimd")
     endif()
 endif()
diff --git a/xtensorConfig.cmake.in b/xtensorConfig.cmake.in
index ec72abb..7825612 100644
--- a/xtensorConfig.cmake.in
+++ b/xtensorConfig.cmake.in
@@ -25,7 +25,7 @@ if(NOT TARGET @PROJECT_NAME@)
 endif()
 
 if(XTENSOR_USE_XSIMD)
-    find_dependency(xsimd @xsimd_REQUIRED_VERSION@)
+    find_dependency(xsimd @xsimd_REQUIRED_VERSION@ CONFIG)
     target_link_libraries(@PROJECT_NAME@ INTERFACE xsimd)
     target_compile_definitions(@PROJECT_NAME@ INTERFACE XTENSOR_USE_XSIMD)
 endif()
