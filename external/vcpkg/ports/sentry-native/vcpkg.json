{"name": "sentry-native", "version": "0.8.3", "description": "Sentry SDK for C, C++ and native applications.", "homepage": "https://sentry.io/", "license": "MIT", "supports": "osx | linux | (!arm & !uwp)", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "default-features": ["backend", "transport", {"name": "wer", "platform": "windows & !static"}], "features": {"backend": {"description": ["Enables the platform-specific backend.", "This feature does nothing for some platforms.", "Cf. https://github.com/getsentry/sentry-native#compile-time-options"], "dependencies": [{"$comment": "pkgcong is used by the breakpad backend.", "name": "pkgconf", "host": true, "platform": "!android & !ios"}, {"$comment": "zlib is used by the crashpad backend.", "name": "zlib", "platform": "!android & !ios"}]}, "compression": {"description": ["Enables gzip compression support for the transport.", "Cf.https://github.com/getsentry/sentry-native#compile-time-options"], "dependencies": ["zlib"]}, "transport": {"description": ["Enables the platform-specific network transport.", "This feature does nothing for some platforms.", "Cf. https://github.com/getsentry/sentry-native#compile-time-options"], "dependencies": [{"name": "curl", "default-features": false, "platform": "!windows"}]}, "wer": {"description": ["Enables the Windows Error Reporting backend.", "Cf.https://github.com/getsentry/sentry-native#compile-time-options"], "supports": "windows & !static", "dependencies": [{"name": "sentry-native", "features": ["backend"]}]}}}