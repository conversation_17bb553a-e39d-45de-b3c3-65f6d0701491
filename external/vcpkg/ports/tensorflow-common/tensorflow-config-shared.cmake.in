if(TARGET tensorflow@TF_LIB_SUFFIX@::tensorflow@TF_LIB_SUFFIX@ OR TARGET tensorflow@TF_LIB_SUFFIX@::tensorflow_framework)
	if(NOT (TARGET tensorflow@TF_LIB_SUFFIX@::tensorflow@TF_LIB_SUFFIX@ AND TARGET tensorflow@TF_LIB_SUFFIX@::tensorflow_framework))
		message(FATAL_ERROR "Some (but not all) targets in this config.cmake were already defined.")
	endif()

	return()
endif()

add_library(tensorflow@TF_LIB_SUFFIX@::tensorflow@TF_LIB_SUFFIX@ SHARED IMPORTED GLOBAL)
add_library(tensorflow@TF_LIB_SUFFIX@::tensorflow_framework SHARED IMPORTED GLOBAL)

# Compute the installation prefix from this tensorflow@<EMAIL> file location.
# CMAKE_CURRENT_LIST_DIR = [vcpkg_installed_dir]/[target_triplet]/share/tensorflow@TF_PORT_SUFFIX@
get_filename_component(TENSORFLOW_INSTALL_PREFIX "${CMAKE_CURRENT_LIST_DIR}" DIRECTORY)
# TENSORFLOW_INSTALL_PREFIX = [vcpkg_installed_dir]/[target_triplet]/share
get_filename_component(TENSORFLOW_INSTALL_PREFIX "${TENSORFLOW_INSTALL_PREFIX}" DIRECTORY)
# TENSORFLOW_INSTALL_PREFIX = [vcpkg_installed_dir]/[target_triplet]

target_include_directories(tensorflow@TF_LIB_SUFFIX@::tensorflow@TF_LIB_SUFFIX@
	INTERFACE
		@TF_INCLUDE_DIRS@
)
target_include_directories(tensorflow@TF_LIB_SUFFIX@::tensorflow_framework
	INTERFACE
		@TF_INCLUDE_DIRS@
)

if(@TENSORFLOW_HAS_RELEASE@)
	set_property(TARGET tensorflow@TF_LIB_SUFFIX@::tensorflow@TF_LIB_SUFFIX@
		PROPERTY IMPORTED_LOCATION
			"${TENSORFLOW_INSTALL_PREFIX}/lib/@TF_LIB_NAME@"
	)
	set_property(TARGET tensorflow@TF_LIB_SUFFIX@::tensorflow_framework
		PROPERTY IMPORTED_LOCATION
			"${TENSORFLOW_INSTALL_PREFIX}/lib/@TF_FRAMEWORK_NAME@"
	)

	set_property(TARGET tensorflow@TF_LIB_SUFFIX@::tensorflow@TF_LIB_SUFFIX@
		APPEND PROPERTY IMPORTED_CONFIGURATIONS RELEASE
	)
	set_property(TARGET tensorflow@TF_LIB_SUFFIX@::tensorflow_framework
		APPEND PROPERTY IMPORTED_CONFIGURATIONS RELEASE
	)

	set_property(TARGET tensorflow@TF_LIB_SUFFIX@::tensorflow@TF_LIB_SUFFIX@
		PROPERTY IMPORTED_LOCATION_RELEASE
			"${TENSORFLOW_INSTALL_PREFIX}/lib/@TF_LIB_NAME@"
	)
	set_property(TARGET tensorflow@TF_LIB_SUFFIX@::tensorflow_framework
		PROPERTY IMPORTED_LOCATION_RELEASE
			"${TENSORFLOW_INSTALL_PREFIX}/lib/@TF_FRAMEWORK_NAME@"
	)
endif()

if(@TENSORFLOW_HAS_DEBUG)
	if(NOT @TENSORFLOW_HAS_RELEASE)
		set_property(TARGET tensorflow@TF_LIB_SUFFIX@::tensorflow@TF_LIB_SUFFIX@
			PROPERTY IMPORTED_LOCATION
				"${TENSORFLOW_INSTALL_PREFIX}/debug/lib/@TF_LIB_NAME@"
		)
		set_property(TARGET tensorflow@TF_LIB_SUFFIX@::tensorflow_framework
			PROPERTY IMPORTED_LOCATION
				"${TENSORFLOW_INSTALL_PREFIX}/debug/lib/@TF_FRAMEWORK_NAME@"
		)
	endif()

	set_property(TARGET tensorflow@TF_LIB_SUFFIX@::tensorflow@TF_LIB_SUFFIX@
		APPEND PROPERTY IMPORTED_CONFIGURATIONS DEBUG
	)
	set_property(TARGET tensorflow@TF_LIB_SUFFIX@::tensorflow_framework
		APPEND PROPERTY IMPORTED_CONFIGURATIONS DEBUG
	)

	set_property(TARGET tensorflow@TF_LIB_SUFFIX@::tensorflow@TF_LIB_SUFFIX@
		PROPERTY IMPORTED_LOCATION_DEBUG
			"${TENSORFLOW_INSTALL_PREFIX}/debug/lib/@TF_LIB_NAME@"
	)
	set_property(TARGET tensorflow@TF_LIB_SUFFIX@::tensorflow_framework
		PROPERTY IMPORTED_LOCATION_DEBUG
			"${TENSORFLOW_INSTALL_PREFIX}/debug/lib/@TF_FRAMEWORK_NAME@"
	)
endif()

set(tensorflow@TF_LIB_SUFFIX@_FOUND TRUE)
set(tensorflow_framework_FOUND TRUE)
