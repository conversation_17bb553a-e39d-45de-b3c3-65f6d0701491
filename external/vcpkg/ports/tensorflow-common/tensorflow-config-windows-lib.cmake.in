if(TARGET tensorflow@TF_LIB_SUFFIX@::tensorflow@TF_LIB_SUFFIX@)
	return()
endif()

set(tensorflow@TF_LIB_SUFFIX@_FOUND TRUE)

# Compute the installation prefix from this tensorflow@<EMAIL> file location.
# CMAKE_CURRENT_LIST_DIR = [vcpkg_installed_dir]/[target_triplet]/share/tensorflow@TF_PORT_SUFFIX@
get_filename_component(TENSORFLOW_INSTALL_PREFIX "${CMAKE_CURRENT_LIST_DIR}" DIRECTORY)
# TENSORFLOW_INSTALL_PREFIX = [vcpkg_installed_dir]/[target_triplet]/share
get_filename_component(TENSORFLOW_INSTALL_PREFIX "${TENSORFLOW_INSTALL_PREFIX}" DIRECTORY)
# TENSORFLOW_INSTALL_PREFIX = [vcpkg_installed_dir]/[target_triplet]

add_library(tensorflow@TF_LIB_SUFFIX@::tensorflow@TF_LIB_SUFFIX@ INTERFACE IMPORTED GLOBAL)
target_include_directories(tensorflow@TF_LIB_SUFFIX@::tensorflow@TF_LIB_SUFFIX@
	INTERFACE
		@TF_INCLUDE_DIRS@
)
