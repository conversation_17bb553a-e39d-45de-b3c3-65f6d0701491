diff --git a/Project/CMake/cmake/modules/FindTinyXML.cmake b/Project/CMake/cmake/modules/FindTinyXML.cmake
index af97107..4667b65 100644
--- a/Project/CMake/cmake/modules/FindTinyXML.cmake
+++ b/Project/CMake/cmake/modules/FindTinyXML.cmake
@@ -23,7 +23,7 @@ endif(PKG_CONFIG_FOUND)
 
 set(TinyXML_INCLUDE_DIRS ${PC_TINYXML_INCLUDE_DIRS} CACHE PATH "TinyXML include directory" FORCE)
 set(TinyXML_LIBRARY_DIRS ${PC_TINYXML_LIBRARY_DIRS} CACHE PATH "TinyXML library directory" FORCE)
-set(TinyXML_LIBRARIES ${PC_TINYXML_LIBRARIES} CACHE STRING "TinyXML libraries" FORCE)
+set(TinyXML_LIBRARIES ${PC_TINYXML_LINK_LIBRARIES} CACHE STRING "TinyXML libraries" FORCE)
 include(FindPackageHandleStandardArgs)
 
 find_package_handle_standard_args(TinyXML
diff --git a/Project/CMake/libmediainfo.pc.in b/Project/CMake/libmediainfo.pc.in
index 31e53fe..0829d71 100644
--- a/Project/CMake/libmediainfo.pc.in
+++ b/Project/CMake/libmediainfo.pc.in
@@ -8,6 +8,6 @@ Name: libmediainfo
 Version: @MediaInfoLib_VERSION@
 Description: MediaInfoLib
 Requires: libzen
-Requires.private:@CURL_PC@
-Libs: -L${libdir} -lmediainfo -lz
+Requires.private: libcurl tinyxml2 zlib
+Libs: -L${libdir} -lmediainfo
 Cflags: -I${includedir}
