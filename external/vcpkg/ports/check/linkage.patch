diff --git a/src/CMakeLists.txt b/src/CMakeLists.txt
index 4a02dbe..3e147e4 100644
--- a/src/CMakeLists.txt
+++ b/src/CMakeLists.txt
@@ -188,7 +188,14 @@ target_include_directories(checkShared
 )
 
 if(NOT THIS_IS_SUBPROJECT)
-  install(TARGETS check checkShared
+  if(BUILD_SHARED_LIBS)
+    set(lib checkShared)
+    set_target_properties(check PROPERTIES EXCLUDE_FROM_ALL 1)
+  else()
+    set(lib check)
+    set_target_properties(checkShared PROPERTIES EXCLUDE_FROM_ALL 1)
+  endif()
+  install(TARGETS ${lib}
     EXPORT check-targets
     ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR}
     LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}/manual-link
