diff --git a/doc/example/src/CMakeLists.txt b/doc/example/src/CMakeLists.txt
index b5e211e..2a92a1a 100644
--- a/doc/example/src/CMakeLists.txt
+++ b/doc/example/src/CMakeLists.txt
@@ -18,7 +18,7 @@ target_link_libraries(main money)
 
 install(TARGETS money
   RUNTIME DESTINATION bin
-  LIBRARY DESTINATION lib
-  ARCHIVE DESTINATION lib)
+  LIBRARY DESTINATION lib/manual-link
+  ARCHIVE DESTINATION lib/manual-link)
 
 install(FILES ${CMAKE_CURRENT_SOURCE_DIR}/money.h DESTINATION include)
diff --git a/lib/CMakeLists.txt b/lib/CMakeLists.txt
index 38cbc53..d8a4272 100644
--- a/lib/CMakeLists.txt
+++ b/lib/CMakeLists.txt
@@ -75,5 +75,11 @@ set(HEADERS libcompat.h)
 
 add_library(compat STATIC ${SOURCES} ${HEADERS})
 
+install(TARGETS compat
+    RUNTIME DESTINATION bin
+    LIBRARY DESTINATION lib/manual-link
+    ARCHIVE DESTINATION lib/manual-link
+)
+
 # vim: shiftwidth=2:softtabstop=2:tabstop=2:expandtab:autoindent
 
diff --git a/src/CMakeLists.txt b/src/CMakeLists.txt
index 4a02dbe..3bf2fa6 100644
--- a/src/CMakeLists.txt
+++ b/src/CMakeLists.txt
@@ -191,7 +191,7 @@ if(NOT THIS_IS_SUBPROJECT)
   install(TARGETS check checkShared
     EXPORT check-targets
     ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR}
-    LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
+    LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}/manual-link
     RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
     PUBLIC_HEADER DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}
   )
