diff --git a/cmake/WebPConfig.cmake.in b/cmake/WebPConfig.cmake.in
index a0d721f..8726c09 100644
--- a/cmake/WebPConfig.cmake.in
+++ b/cmake/WebPConfig.cmake.in
@@ -13,7 +13,14 @@ include("${CMAKE_CURRENT_LIST_DIR}/@<EMAIL>")
 set_and_check(WebP_INCLUDE_DIR "@PACKAGE_CMAKE_INSTALL_INCLUDEDIR@")
 set(WebP_INCLUDE_DIRS ${WebP_INCLUDE_DIR})
 set(WEBP_INCLUDE_DIRS ${WebP_INCLUDE_DIR})
-set(WebP_LIBRARIES "@INSTALLED_LIBRARIES@")
+set(WebP_LIBRARIES "")
+include(SelectLibraryConfigurations)
+foreach(_vcpkg_libwebp_lib IN ITEMS @INSTALLED_LIBRARIES@ sharpyuv)
+    find_library(_vcpkg_${_vcpkg_libwebp_lib}_LIBRARY_RELEASE NAMES ${_vcpkg_libwebp_lib} lib${_vcpkg_libwebp_lib} NAMES_PER_DIR PATHS "${VCPKG_IMPORT_PREFIX}/lib" NO_DEFAULT_PATH)
+    find_library(_vcpkg_${_vcpkg_libwebp_lib}_LIBRARY_DEBUG NAMES ${_vcpkg_libwebp_lib}d lib${_vcpkg_libwebp_lib}d ${_vcpkg_libwebp_lib} lib${_vcpkg_libwebp_lib} NAMES_PER_DIR PATHS "${VCPKG_IMPORT_PREFIX}/debug/lib" NO_DEFAULT_PATH)
+    select_library_configurations(_vcpkg_${_vcpkg_libwebp_lib})
+    list(APPEND WebP_LIBRARIES ${_vcpkg_${_vcpkg_libwebp_lib}_LIBRARIES})
+endforeach()
 set(WEBP_LIBRARIES "${WebP_LIBRARIES}")
 
 check_required_components(WebP)
