diff --git a/CMakeLists.txt b/CMakeLists.txt
index b785a8e..f214a32 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -665,6 +665,10 @@ if(WEBP_BUILD_EXTRAS)
                         ${CMAKE_CURRENT_BINARY_DIR}/src ${SDL2_INCLUDE_DIRS})
     set(WEBP_HAVE_SDL 1)
     target_compile_definitions(vwebp_sdl PUBLIC WEBP_HAVE_SDL)
+    target_compile_definitions(vwebp_sdl PRIVATE WEBP_HAVE_JUST_SDL_H)
+    if(WIN32)
+        target_link_libraries(vwebp_sdl dxguid winmm)
+    endif()
 
     set(CMAKE_REQUIRED_INCLUDES "${SDL2_INCLUDE_DIRS}")
     check_c_source_compiles(
@@ -699,8 +703,9 @@ if(WEBP_BUILD_WEBP_JS)
   if(NOT WEBP_ENABLE_SIMD)
     # JavaScript version
     add_executable(webp_js ${CMAKE_CURRENT_SOURCE_DIR}/extras/webp_to_sdl.c)
-    target_link_libraries(webp_js webpdecoder SDL2)
+    target_link_libraries(webp_js webpdecoder ${SDL_LIBRARY})
     target_include_directories(webp_js PRIVATE ${CMAKE_CURRENT_BINARY_DIR})
+    target_include_directories(webp_js PRIVATE ${SDL_INCLUDE_DIR})
     set(WEBP_HAVE_SDL 1)
     set_target_properties(
       webp_js
@@ -715,12 +720,15 @@ if(WEBP_BUILD_WEBP_JS)
          -sALLOW_MEMORY_GROWTH")
     set_target_properties(webp_js PROPERTIES OUTPUT_NAME webp)
     target_compile_definitions(webp_js PUBLIC EMSCRIPTEN WEBP_HAVE_SDL)
+    target_compile_definitions(webp_js PUBLIC EMSCRIPTEN WEBP_HAVE_JUST_SDL_H)
   endif()
 
   # WASM version
   add_executable(webp_wasm ${CMAKE_CURRENT_SOURCE_DIR}/extras/webp_to_sdl.c)
-  target_link_libraries(webp_wasm webpdecoder SDL2)
+  target_link_libraries(webp_wasm webpdecoder ${SDL_LIBRARY})
   target_include_directories(webp_wasm PRIVATE ${CMAKE_CURRENT_BINARY_DIR})
+  target_include_directories(webp_wasm PRIVATE ${SDL_INCLUDE_DIR})
+  target_compile_definitions(webp_wasm PUBLIC EMSCRIPTEN WEBP_HAVE_JUST_SDL_H)
   set_target_properties(
     webp_wasm
     PROPERTIES
