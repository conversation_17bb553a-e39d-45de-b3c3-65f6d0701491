diff --git a/CMakeLists.txt b/CMakeLists.txt
index 81f0f30d1..db08c345c 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -272,15 +272,6 @@ if (USE_BOOST)
       set (Boost_VERSION "${Boost_MAJOR_VERSION}.${Boost_MINOR_VERSION}.${Boost_SUBMINOR_VERSION}")
     endif ()
     set (OPENTURNS_HAVE_BOOST TRUE)
-    if (USE_MPFR)
-      find_package (MPFR)
-      if (MPFR_FOUND)
-        set (OPENTURNS_HAVE_MPFR TRUE)
-        list (APPEND OPENTURNS_PRIVATE_INCLUDE_DIRS ${MPFR_INCLUDE_DIRS})
-        list (APPEND OPENTURNS_PRIVATE_LIBRARIES ${MPFR_LIBRARIES})
-        list (APPEND OPENTURNS_ENABLED_FEATURES "mpfr")
-      endif ()
-    endif ()
     if (USE_MPC AND NOT Boost_VERSION VERSION_LESS 1.68)
       find_package (MPC)
       if (MPC_FOUND)
@@ -290,8 +281,19 @@ if (USE_BOOST)
         list (APPEND OPENTURNS_ENABLED_FEATURES "mpc")
       endif ()
     endif ()
-    list (APPEND OPENTURNS_PRIVATE_LIBRARIES Boost::boost)
-    list (APPEND OPENTURNS_ENABLED_FEATURES "boost")
+    if (USE_MPFR)
+      find_package (MPFR)
+      if (MPFR_FOUND)
+        set (OPENTURNS_HAVE_MPFR TRUE)
+        list (APPEND OPENTURNS_PRIVATE_INCLUDE_DIRS ${MPFR_INCLUDE_DIRS})
+        list (APPEND OPENTURNS_PRIVATE_LIBRARIES ${MPFR_LIBRARIES})
+        list (APPEND OPENTURNS_ENABLED_FEATURES "mpfr")
+      endif ()
+    endif ()
+    if((USE_MPC OR USE_MPFR) AND NOT BUILD_SHARED_LIBS)
+        find_library(GMP_LIBRARY NAMES gmp)
+        list (APPEND OPENTURNS_PRIVATE_LIBRARIES ${GMP_LIBRARY})
+    endif()
   endif ()
 endif ()
 
