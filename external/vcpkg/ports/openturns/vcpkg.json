{"name": "openturns", "version": "1.24", "description": "OpenTURNS is a scientific C++ and Python library featuring an internal data model and algorithms dedicated to the treatment of uncertainties.", "homepage": "http://www.openturns.org", "license": null, "dependencies": ["blas", "boost-geometry", "boost-math", "boost-multiprecision", "boost-random", "ceres", "cminpack", "dlib", "exprtk", {"name": "hdf5", "default-features": false, "features": ["cpp"]}, "lapack", "libxml2", "mpc", "mpfr", "mup<PERSON><PERSON>", "nanoflann", "nlopt", "pagmo2", "pthread", "python3", {"name": "python3", "host": true}, "spectra", "tbb", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}]}