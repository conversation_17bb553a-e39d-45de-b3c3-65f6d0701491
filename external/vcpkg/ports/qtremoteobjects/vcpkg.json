{"name": "qtremoteobjects", "version": "6.8.3", "description": "Qt Remote Objects (QtRO) is an Inter-Process Communication (IPC) module developed for Qt. This module extends Qt's existing functionalities to enable information exchange between processes or computers, easily.", "homepage": "https://www.qt.io/", "license": null, "dependencies": [{"name": "qtbase", "default-features": false, "features": ["network"]}, {"name": "qtdeclarative", "default-features": false}, {"name": "qtremoteobjects", "host": true, "default-features": false}], "features": {"qml": {"description": "Build QML imports", "dependencies": [{"name": "qtbase", "default-features": false, "features": ["gui"]}, {"name": "qtdeclarative", "default-features": false}]}}}