 mlir/cmake/modules/MLIRDetectPythonEnv.cmake | 2 +-
 1 file changed, 1 insertion(+), 1 deletion(-)

diff --git a/mlir/cmake/modules/MLIRDetectPythonEnv.cmake b/mlir/cmake/modules/MLIRDetectPythonEnv.cmake
index 0a486c1bbb54..c07c55b1e17a 100644
--- a/mlir/cmake/modules/MLIRDetectPythonEnv.cmake
+++ b/mlir/cmake/modules/MLIRDetectPythonEnv.cmake
@@ -19,7 +19,7 @@ macro(mlir_configure_python_dev_packages)
   set(_python_development_component Development.Module)
 
   find_package(Python3 ${LLVM_MINIMUM_PYTHON_VERSION}
-    COMPONENTS Interpreter ${_python_development_component} NumPy REQUIRED)
+    COMPONENTS Interpreter ${_python_development_component} REQUIRED)
   unset(_python_development_component)
   message(STATUS "Found python include dirs: ${Python3_INCLUDE_DIRS}")
   message(STATUS "Found python libraries: ${Python3_LIBRARIES}")
