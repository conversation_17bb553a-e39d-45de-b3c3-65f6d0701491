 llvm/tools/llvm-config/llvm-config.cpp | 2 +-
 1 file changed, 1 insertion(+), 1 deletion(-)

diff --git a/llvm/tools/llvm-config/llvm-config.cpp b/llvm/tools/llvm-config/llvm-config.cpp
index d5b76b1bb6c1..9fedcb2ab75f 100644
--- a/llvm/tools/llvm-config/llvm-config.cpp
+++ b/llvm/tools/llvm-config/llvm-config.cpp
@@ -304,7 +304,7 @@ int main(int argc, char **argv) {
   // bin dir).
   sys::fs::make_absolute(CurrentPath);
   CurrentExecPrefix =
-      sys::path::parent_path(sys::path::parent_path(CurrentPath)).str();
+      sys::path::parent_path(sys::path::parent_path(sys::path::parent_path(CurrentPath))).str();
 
   // Check to see if we are inside a development tree by comparing to possible
   // locations (prefix style or CMake style).
