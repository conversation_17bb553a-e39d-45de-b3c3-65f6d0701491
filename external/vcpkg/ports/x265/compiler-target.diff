diff --git a/source/CMakeLists.txt b/source/CMakeLists.txt
index ab5ddfe..e99c4d8 100755
--- a/source/CMakeLists.txt
+++ b/source/CMakeLists.txt
@@ -570,6 +570,9 @@ if((MSVC_IDE OR XCODE OR GCC) AND ENABLE_ASSEMBLY)
         set(SUFFIX o)
     endif()
 
+    if(CMAKE_CXX_COMPILER_TARGET)
+        list(PREPEND ARM_ARGS "--target=${CMAKE_CXX_COMPILER_TARGET}")
+    endif()
     if(ARM OR CROSS_COMPILE_ARM)
     # compile ARM arch asm files here
         enable_language(ASM)
