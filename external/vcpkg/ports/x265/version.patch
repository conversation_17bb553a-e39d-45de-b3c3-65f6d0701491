diff --git a/source/cmake/Version.cmake b/source/cmake/Version.cmake
index 3bbf42f..e77f639 100644
--- a/source/cmake/Version.cmake
+++ b/source/cmake/Version.cmake
@@ -28,6 +28,11 @@
 set(X265_VERSION "unknown")
 set(X265_LATEST_TAG "0.0")
 set(X265_TAG_DISTANCE "0")
+if(VERSION)
+    set(X265_VERSION "${VERSION}-vcpkg")
+    set(X265_LATEST_TAG "${VERSION}")
+    return()
+endif()
 
 #Find version control software to be used for live and extracted repositories from compressed tarballs
 if(CMAKE_VERSION VERSION_LESS "2.8.10")
