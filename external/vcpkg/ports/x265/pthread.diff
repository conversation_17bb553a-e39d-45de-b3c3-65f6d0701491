diff --git a/source/CMakeLists.txt b/source/CMakeLists.txt
index a407271..f147d37 100755
--- a/source/CMakeLists.txt
+++ b/source/CMakeLists.txt
@@ -85,6 +85,9 @@ else()
 endif()
 
 if(UNIX)
+    set(THREADS_PREFER_PTHREAD_FLAG 1)
+    find_package(Threads)
+    add_library(pthread ALIAS Threads::Threads)
     list(APPEND PLATFORM_LIBS pthread)
     find_library(LIBRT rt)
     if(LIBRT)
@@ -705,6 +708,9 @@ if(X265_LATEST_TAG)
         list(REMOVE_ITEM PLIBLIST "-lc" "-lpthread" "-lmingwex" "-lmingwthrd"
             "-lmingw32" "-lmoldname" "-lmsvcrt" "-ladvapi32" "-lshell32"
             "-luser32" "-lkernel32")
+        if(UNIX)
+            list(APPEND PLIBLIST ${CMAKE_THREAD_LIBS_INIT})
+        endif()
         string(REPLACE ";" " " PRIVATE_LIBS "${PLIBLIST}")
     else()
         set(PRIVATE_LIBS "")
