diff --git a/source/CMakeLists.txt b/source/CMakeLists.txt
index 6183a6d..eaa5c6f 100644
--- a/source/CMakeLists.txt
+++ b/source/CMakeLists.txt
@@ -842,7 +842,7 @@ if(SVTHEVC_FOUND)
 endif()
 
 install(FILES x265.h "${PROJECT_BINARY_DIR}/x265_config.h" DESTINATION include)
-if((WIN32 AND ENABLE_CLI) OR (WIN32 AND ENABLE_SHARED))
+if(0)
     if(MSVC_IDE)
         if(ENABLE_CLI)
             install(FILES "${PROJECT_BINARY_DIR}/Debug/x265.pdb" DESTINATION ${BIN_INSTALL_DIR} CONFIGURATIONS Debug)
