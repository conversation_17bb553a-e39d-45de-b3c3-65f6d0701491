diff --git a/CMakeLists.txt b/CMakeLists.txt
index fb0fb46..772f56c 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -240,7 +240,6 @@ install(FILES ${CMAKE_CURRENT_BINARY_DIR}/superlu.pc
         DESTINATION ${CMAKE_INSTALL_LIBDIR}/pkgconfig)
 
 configure_file(${SuperLU_SOURCE_DIR}/SRC/superlu_config.h.in ${SuperLU_BINARY_DIR}/SRC/superlu_config.h)
-configure_file(${SuperLU_SOURCE_DIR}/SRC/superlu_config.h.in ${SuperLU_SOURCE_DIR}/SRC/superlu_config.h)
 
 # Following is to configure a header file for FORTRAN code
 configure_file(${SuperLU_SOURCE_DIR}/SRC/superlu_config.h.in ${SuperLU_BINARY_DIR}/FORTRAN/superlu_config.h)
