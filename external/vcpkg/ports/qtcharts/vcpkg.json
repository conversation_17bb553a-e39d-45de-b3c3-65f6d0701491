{"name": "qtcharts", "version": "6.8.3", "description": "The Qt Charts module provides a set of easy-to-use chart components. It uses the Qt Graphics View Framework to integrate charts with modern user interfaces.", "homepage": "https://www.qt.io/", "license": null, "dependencies": [{"name": "qtbase", "default-features": false, "features": ["gui", "widgets"]}], "features": {"designer": {"description": "Build Designer plugin", "dependencies": [{"name": "qttools", "default-features": false}]}, "qml": {"description": "Build QML imports", "dependencies": [{"name": "qtdeclarative", "default-features": false}]}}}