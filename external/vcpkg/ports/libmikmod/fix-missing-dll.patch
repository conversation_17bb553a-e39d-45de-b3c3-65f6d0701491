diff --git a/CMakeLists.txt b/CMakeLists.txt
index 53a174e..b4d31d3 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -856,11 +856,19 @@ INSTALL(
     ARCHIVE DESTINATION "lib${LIB_SUFFIX}"
 )
 
-INSTALL(
-    FILES "${CMAKE_BINARY_DIR}/libmikmod-config"
-    PERMISSIONS GROUP_EXECUTE GROUP_READ OWNER_EXECUTE OWNER_READ OWNER_WRITE WORLD_EXECUTE WORLD_READ
-    DESTINATION "bin"
-)
+IF(WIN32)
+    SET(LIB_SUFFIX_USED ".dll")
+ELSEIF(UNIX)
+    SET(LIB_SUFFIX_USED ".so")
+ELSEIF(APPLE)
+    SET(LIB_SUFFIX_USED ".dylib")
+ELSE()
+    SET(LIB_SUFFIX_USED)
+ENDIF()
+
+IF(NOT ENABLE_STATIC)
+    INSTALL(FILES "${CMAKE_BINARY_DIR}/mikmod${LIB_SUFFIX_USED}" DESTINATION "bin")
+ENDIF()
 
 INSTALL(
     FILES
