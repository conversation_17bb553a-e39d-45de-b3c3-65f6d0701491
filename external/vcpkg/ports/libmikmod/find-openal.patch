From 505d9ebf6ad31d4f28ca0fbab6cf299a88d08036 Mon Sep 17 00:00:00 2001
From: dpogue <<EMAIL>>
Date: Sun, 24 Nov 2024 22:37:46 -0800
Subject: [PATCH] Use find_package for OpenAL

---
 CMakeLists.txt | 2 +-
 1 file changed, 1 insertion(+), 1 deletion(-)

diff --git a/CMakeLists.txt b/CMakeLists.txt
index 53a174e..0be2166 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -799,7 +799,7 @@ ELSE()
 ENDIF(ENABLE_SDL)
 
 IF (ENABLE_OPENAL)
-  include(FindOpenAL)
+  find_package(OpenAL CONFIG)
   IF (OPENAL_FOUND)
     SET (DRV_OPENAL 1)
     CHECK_MULTI_INCLUDE_FILES("AL/al.h" "AL/alc.h")
-- 
2.45.2.windows.1

