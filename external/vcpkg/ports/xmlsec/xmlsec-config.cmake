file(READ "${CMAKE_CURRENT_LIST_DIR}/usage" usage)
message(WARNING "find_package(xmlsec) is deprecated.\n${usage}")
include(CMakeFindDependencyMacro)
find_dependency(unofficial-xmlsec CONFIG REQUIRED)
if(NOT TARGET xmlsec1)
    add_library(xmlsec1 ALIAS unofficial::xmlsec::xmlsec1)
endif()
if(NOT TARGET xmlsec1-openssl)
    add_library(xmlsec1-openssl ALIAS unofficial::xmlsec::xmlsec1-openssl)
endif()
