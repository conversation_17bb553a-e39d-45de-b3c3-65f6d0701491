xmlsec can be imported via CMake FindPkgConfig module:

    find_package(PkgConfig)
    # For dynamic loading of xmlsec crypto library
    pkg_check_modules(XMLSEC1 REQUIRED IMPORTED_TARGET xmlsec1)
    target_link_libraries(main PRIVATE PkgConfig::XMLSEC1)
    # For selecting the openssl crypto engine at link time
    pkg_check_modules(XMLSEC1_OPENSSL REQUIRED IMPORTED_TARGET xmlsec1-openssl)
    target_link_libraries(main PRIVATE PkgConfig::XMLSEC1_OPENSSL)

vcpkg provides proprietary CMake targets:

    find_package(unofficial-xmlsec CONFIG REQUIRED)
    # For dynamic loading of xmlsec crypto library
    target_link_libraries(main PRIVATE unofficial::xmlsec::xmlsec1)
    # For selecting the openssl crypto engine at link time
    target_link_libraries(main PRIVATE unofficial::xmlsec::xmlsec1-openssl)
