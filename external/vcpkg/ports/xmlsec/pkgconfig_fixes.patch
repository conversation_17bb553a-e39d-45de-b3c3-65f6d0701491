diff --git a/xmlsec-openssl.pc.in b/xmlsec-openssl.pc.in
index af3ae29..40635cf 100644
--- a/xmlsec-openssl.pc.in
+++ b/xmlsec-openssl.pc.in
@@ -8,5 +8,4 @@ Version: @VERSION@
 Description: XML Security Library implements XML Signature and XML Encryption standards
 Requires: libxml-2.0 >= @LIBXML_MIN_VERSION@ @LIBXSLT_PC_FILE_COND@
 Cflags: @XMLSEC_OPENSSL_CFLAGS@
-Cflags.private: -DXMLSEC_STATIC
 Libs: @XMLSEC_OPENSSL_LIBS@
diff --git a/xmlsec.pc.in b/xmlsec.pc.in
index 2d5a3ad..0f72d68 100644
--- a/xmlsec.pc.in
+++ b/xmlsec.pc.in
@@ -7,5 +7,5 @@ Name: xmlsec1
 Version: @VERSION@
 Description: XML Security Library implements XML Signature and XML Encryption standards
 Requires: libxml-2.0 >= @LIBXML_MIN_VERSION@ @LIBXSLT_PC_FILE_COND@
-Cflags: -DXMLSEC_CRYPTO_DYNAMIC_LOADING=1 @XMLSEC_CORE_CFLAGS@
+Cflags: @XMLSEC_CORE_CFLAGS@
 Libs: -L${libdir} @XMLSEC_CORE_LIBS@
