diff --git a/CMakeLists.txt b/CMakeLists.txt
index c91ec97..11d475b 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -692,7 +692,7 @@ target_link_libraries(hello_world ${CPPCMS_LIB})
 if(NOT DISABLE_TCPCACHE)
 	add_executable(cppcms_scale src/cache_server_main.cpp)
 	target_link_libraries(cppcms_scale ${CPPCMS_LIB})
-	if(WIN32 OR CYGWIN)
+	if(NOT DISABLE_SHARED)
 		set_target_properties(cppcms_scale PROPERTIES COMPILE_DEFINITIONS DLL_EXPORT)
 	endif()
 endif()
