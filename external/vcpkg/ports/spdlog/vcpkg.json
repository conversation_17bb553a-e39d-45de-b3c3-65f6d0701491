{"name": "spdlog", "version-semver": "1.15.2", "description": "Very fast, header-only/compiled, C++ logging library.", "homepage": "https://github.com/gabime/spdlog", "license": "MIT", "dependencies": ["fmt", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"benchmark": {"description": "Use google benchmark", "dependencies": ["benchmark"]}, "wchar": {"description": "Build with wchar_t (Windows only)", "supports": "windows"}}}