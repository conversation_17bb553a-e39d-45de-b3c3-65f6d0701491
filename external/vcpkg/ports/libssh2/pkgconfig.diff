diff --git a/CMakeLists.txt b/CMakeLists.txt
index 6b51ac6..d8d5701 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -310,6 +310,8 @@ if(CRYPTO_<PERSON>CKEND STREQUAL "OpenSSL" OR NOT CRYPTO_BACKEND)
     list(APPEND LIBSSH2_LIBS OpenSSL::Crypto)
     list(APPEND LIBSSH2_PC_REQUIRES_PRIVATE "libcrypto")
 
+  endif()
+  if(0) # covered by OpenSSL wrapper
     if(WIN32)
       # Statically linking to OpenSSL requires crypt32 for some Windows APIs.
       # This should really be handled by FindOpenSSL.cmake.
diff --git a/cmake/libssh2-config.cmake.in b/cmake/libssh2-config.cmake.in
index edc86d7..905e4a2 100644
--- a/cmake/libssh2-config.cmake.in
+++ b/cmake/libssh2-config.cmake.in
@@ -14,7 +14,7 @@ elseif("@CRYPTO_BACKEND@" STREQUAL "mbedTLS")
   find_dependency(MbedTLS)
 endif()
 
-if(@ZLIB_FOUND@)
+if(@ENABLE_ZLIB_COMPRESSION@)
   find_dependency(ZLIB)
 endif()
 
diff --git a/src/CMakeLists.txt b/src/CMakeLists.txt
index 512c64e..c50ce6a 100644
--- a/src/CMakeLists.txt
+++ b/src/CMakeLists.txt
@@ -63,7 +63,8 @@ if(ENABLE_ZLIB_COMPRESSION)
   find_package(ZLIB REQUIRED)
 
   list(APPEND libssh2_INCLUDE_DIRS ${ZLIB_INCLUDE_DIRS})
-  list(APPEND LIBSSH2_LIBS ${ZLIB_LIBRARIES})
+  # Avoid multi-config string. Same pattern as in curl.
+  list(APPEND LIBSSH2_LIBS ZLIB::ZLIB)
   list(APPEND LIBSSH2_PC_REQUIRES_PRIVATE "zlib")
   if(ZLIB_FOUND)
     list(APPEND _libssh2_definitions "LIBSSH2_HAVE_ZLIB")
@@ -229,6 +230,7 @@ foreach(_libdir IN LISTS CMAKE_SYSTEM_PREFIX_PATH)
 endforeach()
 
 foreach(_libdir IN LISTS LIBSSH2_LIBDIRS)
+  cmake_path(SET _libdir NORMALIZE "${_libdir}")
   list(FIND _sys_libdirs "${_libdir}" _libdir_index)
   if(_libdir_index LESS 0)
     list(APPEND _ldflags "-L${_libdir}")
@@ -240,7 +242,13 @@ if(NOT MINGW AND NOT UNIX)
   set(_implicit_libs ${CMAKE_C_IMPLICIT_LINK_LIBRARIES})
 endif()
 
-foreach(_lib IN LISTS _implicit_libs LIBSSH2_LIBS)
+find_package(PkgConfig)
+pkg_check_modules(LIBSSH2_PC_REQUIRED REQUIRED ${LIBSSH2_PC_REQUIRES_PRIVATE})
+# Libs which are resolved by pkg-config via Requires (LIBSSH2_PC_REQUIRES_PRIVATE)
+# don't need to be written to `libssh2.pc` (LIBSSH2_PC_LIBS_PRIVATE)
+list(REMOVE_ITEM LIBSSH2_LIBS ZLIB::ZLIB OpenSSL::Crypto)
+
+foreach(_lib IN LISTS LIBSSH2_LIBS)
   if(TARGET "${_lib}")
     set(_libname "${_lib}")
     get_target_property(_imported "${_libname}" IMPORTED)
@@ -255,6 +263,9 @@ foreach(_lib IN LISTS _implicit_libs LIBSSH2_LIBS)
       continue()
     endif()
   endif()
+  if(_lib IN_LIST LIBSSH2_PC_REQUIRED_LINK_LIBRARIES)
+    continue()
+  endif()
   if(_lib MATCHES "^-")
     list(APPEND _ldflags "${_lib}")
   elseif(_lib MATCHES ".*/.*")
@@ -263,12 +274,19 @@ foreach(_lib IN LISTS _implicit_libs LIBSSH2_LIBS)
     get_filename_component(_libdir ${_lib} DIRECTORY)
     get_filename_component(_libname ${_lib} NAME_WE)
     if(_libname MATCHES "^lib")
+      cmake_path(SET _libdir NORMALIZE "${_libdir}")
       list(FIND _sys_libdirs "${_libdir}" _libdir_index)
       if(_libdir_index LESS 0)
         list(APPEND _ldflags "-L${_libdir}")
       endif()
       string(REGEX REPLACE "^lib" "" _libname "${_libname}")
       list(APPEND LIBSSH2_PC_LIBS_PRIVATE "-l${_libname}")
+      if(TARGET "${LIB_STATIC}" AND _libdir IN_LIST CMAKE_C_IMPLICIT_LINK_DIRECTORIES)
+        # Avoid absolute path to system lib in exported CMake config
+        get_target_property(static_link_libs "${LIB_STATIC}" INTERFACE_LINK_LIBRARIES)
+        string(REPLACE "${_lib}" "${_libname}" static_link_libs "${static_link_libs}")
+        set_target_properties("${LIB_STATIC}" PROPERTIES INTERFACE_LINK_LIBRARIES "${static_link_libs}")
+      endif()
     else()
       list(APPEND LIBSSH2_PC_LIBS_PRIVATE "${_lib}")
     endif()
@@ -296,8 +314,7 @@ if(BUILD_SHARED_LIBS)
   set(LIBSSH2_PC_REQUIRES "")
   set(LIBSSH2_PC_LIBS "")
 else()
-  set(LIBSSH2_PC_REQUIRES "${LIBSSH2_PC_REQUIRES_PRIVATE}")
-  set(LIBSSH2_PC_LIBS "${LIBSSH2_PC_LIBS_PRIVATE}")
+  # (processing by vcpkg_fixup_pkgconfig)
 endif()
 
 set(prefix       "${CMAKE_INSTALL_PREFIX}")
