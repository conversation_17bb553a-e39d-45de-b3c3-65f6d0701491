{"name": "cppgraphqlgen", "version": "4.5.7", "port-version": 2, "description": "C++ GraphQL schema service generator", "homepage": "https://github.com/microsoft/cppgraphqlgen", "license": "MIT", "dependencies": ["pegtl", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "default-features": ["<PERSON><PERSON><PERSON>"], "features": {"clientgen": {"description": "Build the clientgen CLI tool.", "dependencies": ["boost-program-options"]}, "rapidjson": {"description": "Build the graphqljson library with RapidJSON.", "dependencies": ["<PERSON><PERSON><PERSON>"]}, "schemagen": {"description": "Build the schemagen CLI tool.", "dependencies": ["boost-program-options"]}}}