diff --git a/src/cpp/CMakeLists.txt b/src/cpp/CMakeLists.txt
index e0be0a7..9eb07a6 100644
--- a/src/cpp/CMakeLists.txt
+++ b/src/cpp/CMakeLists.txt
@@ -661,7 +661,7 @@ if(MSVC OR MSVC_IDE)
     endif()
 
     # install symbols if any
-    if(PDB_FILE)
+    if(PDB_FILE AND BUILD_SHARED_LIBS)
         install(FILES ${PDB_FILE}
             DESTINATION ${LIB_INSTALL_DIR}${MSVCARCH_DIR_EXTENSION}
             COMPONENT symbols
