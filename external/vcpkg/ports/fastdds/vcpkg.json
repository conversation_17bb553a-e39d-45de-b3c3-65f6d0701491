{"name": "fastdds", "version": "3.1.2", "port-version": 1, "description": "eprosima Fast DDS (formerly Fast RTPS) is a C++ implementation of the DDS (Data Distribution Service) standard of the OMG (Object Management Group). eProsima Fast DDS implements the RTPS (Real Time Publish Subscribe) protocol, which provides publisher-subscriber communications over unreliable transports such as UDP, as defined and maintained by the Object Management Group (OMG) consortium.", "homepage": "https://www.eprosima.com/", "license": "Apache-2.0", "supports": "!uwp", "dependencies": ["asio", "fastcdr", "foonathan-memory", "openssl", "tinyxml2", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}]}