{"name": "libgnutls", "version": "*******", "description": "A secure communications library implementing the SSL, TLS and DTLS protocols.", "homepage": "https://www.gnutls.org/", "license": null, "supports": "!windows | mingw", "dependencies": ["gmp", "libidn2", "libtasn1", "libunistring", "nettle", "zlib"], "features": {"nls": {"description": "Enable native language support.", "dependencies": [{"name": "gettext", "host": true, "features": ["tools"]}, "gettext-libintl"]}, "openssl": {"description": "Enable the OpenSSL compatibility library."}}}