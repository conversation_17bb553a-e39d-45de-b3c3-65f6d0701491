diff --git a/configure.ac b/configure.ac
index d6f283a..77e5a56 100644
--- a/configure.ac
+++ b/configure.ac
@@ -40,6 +40,8 @@ AC_PROG_CC
 gl_EARLY
 ggl_EARLY
 unistring_EARLY
+# Override AM_PROG_AS default
+test "${CCASFLAGS+set}" = set || CCASFLAGS="$CPPFLAGS $CFLAGS"
 AM_PROG_AS
 AM_PROG_AR
 AC_PROG_CXX
@@ -243,7 +245,8 @@ if test "$hw_accel" = aarch64; then
   AARCH64_CCASFLAGS="-Wa,-march=all"
   AC_MSG_CHECKING([whether the compiler supports -Wa,-march=all])
   : > conftest.s
-  if "$CCAS" "$AARCH64_CCASFLAGS" -c conftest.s >/dev/null 2>&1; then
+  # As in Makefiles
+  if "$CCAS" "$AARCH64_CCASFLAGS" $CCASFLAGS -c conftest.s >/dev/null 2>&1; then
     AC_MSG_RESULT(yes)
   else
     AC_MSG_RESULT(no)
