{"name": "cpprestsdk", "version": "2.10.19", "port-version": 3, "description": ["C++11 JSON, REST, and OAuth library", "The C++ REST SDK is a Microsoft project for cloud-based client-server communication in native code using a modern asynchronous C++ API design. This project aims to help C++ developers connect to and interact with services."], "homepage": "https://github.com/Microsoft/cpprestsdk", "license": "MIT", "dependencies": [{"name": "boost-asio", "platform": "!uwp & !windows"}, {"name": "boost-chrono", "platform": "!uwp & !windows"}, {"name": "boost-date-time", "platform": "!uwp & !windows"}, {"name": "boost-filesystem", "platform": "!uwp & !windows"}, {"name": "boost-random", "platform": "!uwp & !windows"}, {"name": "boost-regex", "platform": "!uwp & !windows"}, {"name": "boost-system", "platform": "!uwp & !windows"}, {"name": "boost-thread", "platform": "!uwp & !windows"}, {"name": "openssl", "platform": "!uwp & !windows"}, {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "default-features": [{"name": "brotli", "platform": "windows"}, "compression"], "features": {"brotli": {"description": "Brotli compression support", "dependencies": ["brotli", {"name": "cpprestsdk", "default-features": false, "features": ["compression"]}]}, "compression": {"description": "HTTP Compression support", "dependencies": ["zlib"]}}}