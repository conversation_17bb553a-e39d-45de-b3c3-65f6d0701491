diff --git a/Release/include/cpprest/details/cpprest_compat.h b/Release/include/cpprest/details/cpprest_compat.h
index bf107479..00581371 100644
--- a/Release/include/cpprest/details/cpprest_compat.h
+++ b/Release/include/cpprest/details/cpprest_compat.h
@@ -29,7 +29,6 @@
 #else // ^^^ _WIN32 ^^^ // vvv !_WIN32 vvv
 
 #define __declspec(x) __attribute__((x))
-#define dllimport
 #define novtable /* no novtable equivalent */
 #define __assume(x)                                                                                                    \
     do                                                                                                                 \
@@ -74,9 +73,17 @@
 #define _ASYNCRTIMP_TYPEINFO
 #else // ^^^ _NO_ASYNCRTIMP ^^^ // vvv !_NO_ASYNCRTIMP vvv
 #ifdef _ASYNCRT_EXPORT
+#ifdef _WIN32
 #define _ASYNCRTIMP __declspec(dllexport)
+#else
+#define _ASYNCRTIMP __attribute__((visibility("default")))
+#endif
 #else // ^^^ _ASYNCRT_EXPORT ^^^ // vvv !_ASYNCRT_EXPORT vvv
+#ifdef _WIN32
 #define _ASYNCRTIMP __declspec(dllimport)
+#else
+#define _ASYNCRTIMP
+#endif
 #endif // _ASYNCRT_EXPORT
 
 #if defined(_WIN32)
diff --git a/Release/include/pplx/pplx.h b/Release/include/pplx/pplx.h
index d9ba9c61..8d36252c 100644
--- a/Release/include/pplx/pplx.h
+++ b/Release/include/pplx/pplx.h
@@ -30,9 +30,17 @@
 #define _PPLXIMP
 #else
 #ifdef _PPLX_EXPORT
+#ifdef _WIN32
 #define _PPLXIMP __declspec(dllexport)
 #else
+#define _PPLXIMP __attribute__((visibility("default")))
+#endif
+#else
+#ifdef _WIN32
 #define _PPLXIMP __declspec(dllimport)
+#else
+#define _PPLXIMP
+#endif
 #endif
 #endif
 
