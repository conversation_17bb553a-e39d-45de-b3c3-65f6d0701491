diff --git a/Release/src/CMakeLists.txt b/Release/src/CMakeLists.txt
index 098d33f..7f1f650 100644
--- a/Release/src/CMakeLists.txt
+++ b/Release/src/CMakeLists.txt
@@ -218,15 +218,15 @@ if (WIN32 AND NOT WINDOWS_STORE AND NOT WINDOWS_PHONE)
     crypt32.lib
   )
 elseif(WINDOWS_STORE)
-  if(NOT CMAKE_GENERATOR MATCHES "Visual Studio .*")
-    target_compile_definitions(cpprest PRIVATE -DWINAPI_FAMILY=WINAPI_FAMILY_PC_APP)
+  
+    target_compile_definitions(cpprest PUBLIC -DWINAPI_FAMILY=WINAPI_FAMILY_PC_APP)
     get_target_property(LINK_FLAGS cpprest LINK_FLAGS)
     if(NOT LINK_FLAGS)
         set(LINK_FLAGS "")
     endif()
     set(LINK_FLAGS "${LINK_FLAGS} /APPCONTAINER")
     set_target_properties(cpprest PROPERTIES LINK_FLAGS "${LINK_FLAGS}")
-  endif()
+  
 endif()
 
 set_target_properties(cpprest PROPERTIES OUTPUT_NAME "cpprest${CPPREST_ABI_TAG}")
