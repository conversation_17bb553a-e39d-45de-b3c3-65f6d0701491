From 83b292f263b92082e981a82f5777d927a61772ee Mon Sep 17 00:00:00 2001
From: <PERSON><PERSON><PERSON> <<EMAIL>>
Date: Fri, 13 Mar 2020 02:50:33 -0700
Subject: [PATCH] Include <ostream> in config.h

---
 include/ponder/config.hpp | 1 +
 1 file changed, 1 insertion(+)

diff --git a/include/ponder/config.hpp b/include/ponder/config.hpp
index cd035c19..814403cd 100644
--- a/include/ponder/config.hpp
+++ b/include/ponder/config.hpp
@@ -60,6 +60,7 @@
 #if defined(_MSC_VER)
     #pragma warning(disable: 4275) // non dll-interface class 'X' used as base for dll-interface class 'Y'
     #pragma warning(disable: 4251) // class 'X' needs to have dll-interface to be used by clients of class 'Y'
+    #include <ostream> //In future MSVC, <string> doesn't transitively <ostream>, ponder will  compile failed with error C2027 and C2065, so add <ostream> for fixing these issues.
 #endif
 
 #if defined(__GNUC__) && __GNUC__ <= 4 && __GNUC_MINOR__ < 9
