diff --git a/CMakeLists.txt b/CMakeLists.txt
index 7e86706..f2bdc36 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -57,7 +57,9 @@ ELSEIF(WITH_MINIZIP_NG)
   SET(ANYZIP_LIBRARIES minizip${MINIZIP_NG_SUFFIX})
   SET(ANYZIP_DEF USE_MINIZIP;USE_MINIZIP_NG)
 ELSE()
-  FIND_PACKAGE(Minizip REQUIRED)
+  FIND_PACKAGE(Minizip NAMES unofficial-minizip REQUIRED)
+  SET(MINIZIP_INCLUDE_DIRS "${VCPKG_INSTALLED_DIR}/${VCPKG_TARGET_TRIPLET}/include")
+  SET(MINIZIP_LIBRARIES unofficial::minizip::minizip)
   SET(ANYZIP_INCLUDE_DIRS ${MINIZIP_INCLUDE_DIRS})
   SET(ANYZIP_LIBRARIES ${MINIZIP_LIBRARIES})
   SET(ANYZIP_DEF USE_MINIZIP)
@@ -68,7 +70,8 @@ IF(EXPAT_DIR)
   FIND_PATH(EXPAT_INCLUDE_DIR NAMES expat.h NO_DEFAULT_PATH PATHS ${EXPAT_DIR}/include ${EXPAT_DIR})
   FIND_LIBRARY(EXPAT_LIBRARIES NAMES expat libexpat NO_DEFAULT_PATH PATHS ${EXPAT_DIR}/lib ${EXPAT_DIR})
 ELSE()
-  FIND_PACKAGE(EXPAT REQUIRED)
+  FIND_PACKAGE(EXPAT NAMES expat REQUIRED)
+  SET(EXPAT_LIBRARIES expat::expat)
 ENDIF()
 #   dependancy: expatw (if wide library was requested)
 IF(WITH_WIDE)
@@ -225,13 +228,13 @@ FILE(WRITE "${CMAKE_CURRENT_BINARY_DIR}/xlsxio-config.cmake.in"
 IF (@WITH_LIBZIP@)
   FIND_DEPENDENCY(LibZip)
 ELSE()
-  FIND_DEPENDENCY(minizip CONFIG)
+  FIND_DEPENDENCY(unofficial-minizip CONFIG)
 ENDIF()
 IF (@EXPAT_DIR@)
   FIND_PATH(EXPAT_INCLUDE_DIR NAMES expat.h NO_DEFAULT_PATH PATHS ${EXPAT_DIR}/include ${EXPAT_DIR})
   FIND_LIBRARY(EXPAT_LIBRARIES NAMES expat libexpat NO_DEFAULT_PATH PATHS ${EXPAT_DIR}/lib ${EXPAT_DIR})
 ELSE()
-  FIND_DEPENDENCY(EXPAT)
+  FIND_DEPENDENCY(expat CONFIG)
 ENDIF()
 
 IF(@WITH_WIDE@)
