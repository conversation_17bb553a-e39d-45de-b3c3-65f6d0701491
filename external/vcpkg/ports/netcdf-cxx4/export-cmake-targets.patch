diff --git a/CMakeLists.txt b/CMakeLists.txt
index f06fcc0..a19ab28 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -622,9 +622,23 @@ write_basic_package_version_file(
   VERSION ${NCXX_VERSION}
   COMPATIBILITY SameMajorVersion
   )
+  
+install(
+  EXPORT netcdf-cxx4Targets
+  FILE netcdf-cxx4Targets.cmake
+  NAMESPACE netCDF::
+  DESTINATION ${CMAKE_INSTALL_LIBDIR}/cmake/netCDFCxx
+  )
 
+configure_package_config_file(
+  "${CMAKE_CURRENT_SOURCE_DIR}/netCDFCxxConfig.cmake.in"
+  "${CMAKE_CURRENT_BINARY_DIR}/netCDFCxxConfig.cmake"
+  INSTALL_DESTINATION "${CMAKE_INSTALL_LIBDIR}/cmake/netCDFCxx"
+  )
+  
 install(
   FILES
+  "${CMAKE_CURRENT_BINARY_DIR}/netCDFCxxConfig.cmake"
   "${CMAKE_CURRENT_BINARY_DIR}/netCDF/netCDFCxxConfigVersion.cmake"
   DESTINATION ${CMAKE_INSTALL_LIBDIR}/cmake/netCDFCxx
   COMPONENT headers
diff --git a/cxx4/CMakeLists.txt b/cxx4/CMakeLists.txt
index b433786..5ef4bed 100644
--- a/cxx4/CMakeLists.txt
+++ b/cxx4/CMakeLists.txt
@@ -32,7 +32,7 @@ SET(ALL_TLL_LIBS ${ALL_TLL_LIBS} ${HDF5_C_LIBRARY_hdf5})
 SET(ALL_TLL_LIBS ${ALL_TLL_LIBS} ${EXTRA_DEPS} PARENT_SCOPE)
 
 ADD_LIBRARY(netcdf-cxx4 ${CXX_SOURCES})
-TARGET_INCLUDE_DIRECTORIES(netcdf-cxx4 PUBLIC "${CMAKE_CURRENT_SOURCE_DIR}")
+TARGET_INCLUDE_DIRECTORIES(netcdf-cxx4 PUBLIC $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}> $<INSTALL_INTERFACE:include>)
 TARGET_LINK_LIBRARIES(netcdf-cxx4 ${ALL_TLL_LIBS})
 SET_TARGET_PROPERTIES(netcdf-cxx4 PROPERTIES
   VERSION ${NCXX_LIB_VERSION}
@@ -49,6 +49,7 @@ INSTALL(
   )
 INSTALL(
   TARGETS netcdf-cxx4
+  EXPORT netcdf-cxx4Targets
   LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
   ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR}
   RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
