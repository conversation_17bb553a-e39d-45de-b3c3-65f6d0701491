diff --git a/src/CMakeLists.txt b/src/CMakeLists.txt
index ac3ff73..2fb9a59 100644
--- a/src/CMakeLists.txt
+++ b/src/CMakeLists.txt
@@ -23,15 +23,12 @@ SET(floatfann_LIB_SRCS
         floatfann.c
         )
 
-ADD_LIBRARY(floatfann SHARED ${floatfann_LIB_SRCS})
-ADD_LIBRARY(floatfann_static STATIC ${floatfann_LIB_SRCS})
+ADD_LIBRARY(floatfann ${floatfann_LIB_SRCS})
 
 SET_TARGET_PROPERTIES(floatfann PROPERTIES VERSION ${FANN_VERSION_STRING} SOVERSION ${FANN_VERSION_MAJOR})
-SET_TARGET_PROPERTIES(floatfann_static PROPERTIES VERSION ${FANN_VERSION_STRING} SOVERSION ${FANN_VERSION_MAJOR})
 if (UNIX)
-  SET_TARGET_PROPERTIES(floatfann_static PROPERTIES OUTPUT_NAME floatfann)
 endif(UNIX)
-INSTALL(TARGETS floatfann floatfann_static LIBRARY DESTINATION ${LIB_INSTALL_DIR}
+INSTALL(TARGETS floatfann LIBRARY DESTINATION ${LIB_INSTALL_DIR}
         ARCHIVE DESTINATION ${LIB_INSTALL_DIR}
         RUNTIME DESTINATION ${BIN_INSTALL_DIR} )
 
@@ -42,15 +39,12 @@ SET(doublefann_LIB_SRCS
         doublefann.c
         )
 
-ADD_LIBRARY(doublefann SHARED ${doublefann_LIB_SRCS})
-ADD_LIBRARY(doublefann_static STATIC ${doublefann_LIB_SRCS})
+ADD_LIBRARY(doublefann ${doublefann_LIB_SRCS})
 
 SET_TARGET_PROPERTIES(doublefann PROPERTIES VERSION ${FANN_VERSION_STRING} SOVERSION ${FANN_VERSION_MAJOR})
-SET_TARGET_PROPERTIES(doublefann_static PROPERTIES VERSION ${FANN_VERSION_STRING} SOVERSION ${FANN_VERSION_MAJOR})
 if (UNIX)
-  SET_TARGET_PROPERTIES(doublefann_static PROPERTIES OUTPUT_NAME doublefann)
 endif(UNIX)
-INSTALL(TARGETS doublefann doublefann_static LIBRARY DESTINATION ${LIB_INSTALL_DIR}
+INSTALL(TARGETS doublefann LIBRARY DESTINATION ${LIB_INSTALL_DIR}
         ARCHIVE DESTINATION ${LIB_INSTALL_DIR}
         RUNTIME DESTINATION ${BIN_INSTALL_DIR} )
 
@@ -61,20 +55,16 @@ SET(fixedfann_LIB_SRCS
         fixedfann.c
         )
 
-ADD_LIBRARY(fixedfann SHARED ${fixedfann_LIB_SRCS})
-ADD_LIBRARY(fixedfann_static STATIC ${fixedfann_LIB_SRCS})
+ADD_LIBRARY(fixedfann ${fixedfann_LIB_SRCS})
 
 if(NOT MSVC)
 TARGET_LINK_LIBRARIES(fixedfann m)
-TARGET_LINK_LIBRARIES(fixedfann_static m)
 endif(NOT MSVC)
 
 SET_TARGET_PROPERTIES(fixedfann PROPERTIES VERSION ${FANN_VERSION_STRING} SOVERSION ${FANN_VERSION_MAJOR})
-SET_TARGET_PROPERTIES(fixedfann_static PROPERTIES VERSION ${FANN_VERSION_STRING} SOVERSION ${FANN_VERSION_MAJOR})
 if (UNIX)
-  SET_TARGET_PROPERTIES(fixedfann_static PROPERTIES OUTPUT_NAME fixedfann)
 endif(UNIX)
-INSTALL(TARGETS fixedfann fixedfann_static LIBRARY DESTINATION ${LIB_INSTALL_DIR}
+INSTALL(TARGETS fixedfann LIBRARY DESTINATION ${LIB_INSTALL_DIR}
         ARCHIVE DESTINATION ${LIB_INSTALL_DIR}
         RUNTIME DESTINATION ${BIN_INSTALL_DIR} )
 
@@ -85,19 +75,15 @@ SET(fann_LIB_SRCS
         floatfann.c
         )
 
-ADD_LIBRARY(fann SHARED ${fann_LIB_SRCS})
-ADD_LIBRARY(fann_static STATIC ${fann_LIB_SRCS})
+ADD_LIBRARY(fann ${fann_LIB_SRCS})
 
 if(NOT MSVC)
 TARGET_LINK_LIBRARIES(fann m)
-TARGET_LINK_LIBRARIES(fann_static m)
 endif(NOT MSVC)
 
 SET_TARGET_PROPERTIES(fann PROPERTIES VERSION ${FANN_VERSION_STRING} SOVERSION ${FANN_VERSION_MAJOR})
-SET_TARGET_PROPERTIES(fann_static PROPERTIES VERSION ${FANN_VERSION_STRING} SOVERSION ${FANN_VERSION_MAJOR})
 if (UNIX)
-  SET_TARGET_PROPERTIES(fann_static PROPERTIES OUTPUT_NAME fann)
 endif(UNIX)
-INSTALL(TARGETS fann fann_static LIBRARY DESTINATION ${LIB_INSTALL_DIR}
+INSTALL(TARGETS fann LIBRARY DESTINATION ${LIB_INSTALL_DIR}
         ARCHIVE DESTINATION ${LIB_INSTALL_DIR}
         RUNTIME DESTINATION ${BIN_INSTALL_DIR} )
