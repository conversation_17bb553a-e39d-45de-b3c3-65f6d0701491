diff --git a/CMakeLists.txt b/CMakeLists.txt
index 12ea06c..6c33c25 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -156,10 +156,5 @@ install (FILES
 
 ################# compile tests ################
 
-ADD_SUBDIRECTORY( lib/googletest )
-
-if(COMPILER_SUPPORTS_CXX11)
-  ADD_SUBDIRECTORY( tests )
-endif()
 
 ENDIF()
diff --git a/cmake/Modules/DefineInstallationPaths.cmake b/cmake/Modules/DefineInstallationPaths.cmake
index 2969533..e70c4b7 100644
--- a/cmake/Modules/DefineInstallationPaths.cmake
+++ b/cmake/Modules/DefineInstallationPaths.cmake
@@ -127,7 +127,7 @@ if (UNIX OR MINGW OR WIN32)
   set (PKGCONFIG_INSTALL_DIR "${LIB_INSTALL_DIR}/pkgconfig" CACHE PATH "pkgconfig dir" FORCE)
 endif ()
 
-if (MSCV)
+if (0)
 	# Same same
 	SET(BIN_INSTALL_DIR .)
 	SET(SBIN_INSTALL_DIR .)
@@ -137,5 +137,5 @@ if (MSCV)
 	SET(ICON_INSTALL_DIR .)
 	SET(SOUND_INSTALL_DIR .)
 	SET(LOCALE_INSTALL_DIR lang)
-endif (MSCV)
+endif ()
 
