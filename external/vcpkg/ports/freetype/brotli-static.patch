diff --git a/builds/cmake/FindBrotliDec.cmake b/builds/cmake/FindBrotliDec.cmake
index 46356b1fd..ed4cc2409 100644
--- a/builds/cmake/FindBrotliDec.cmake
+++ b/builds/cmake/FindBrotliDec.cmake
@@ -35,10 +35,15 @@ find_path(BROTLIDEC_INCLUDE_DIRS
   PATH_SUFFIXES brotli)
 
 find_library(BROTLIDEC_LIBRARIES
-  NAMES brotlidec
+  NAMES brotlidec brotlidec-static NAMES_PER_DIR
   HINTS ${PC_BROTLIDEC_LIBDIR}
         ${PC_BROTLIDEC_LIBRARY_DIRS})
 
+  find_library(BROTLICOMMON_LIBRARIES
+    NAMES brotlicommon-static brotlicommon NAMES_PER_DIR
+    HINTS ${PC_BROTLIDEC_LIBDIR}
+          ${PC_BROTLIDEC_LIBRARY_DIRS})
+  set(BROTLIDEC_LIBRARIES "${BROTLIDEC_LIBRARIES};${BROTLICOMMON_LIBRARIES}")
 
 include(FindPackageHandleStandardArgs)
 find_package_handle_standard_args(
