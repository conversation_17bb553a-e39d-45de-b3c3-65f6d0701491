diff --git a/CMakeLists.txt b/CMakeLists.txt
index cb1b9a0f2..edca5d579 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -508,7 +508,6 @@ set(PKG_CONFIG_REQUIRED_PRIVATE "")
 set(PKGCONFIG_LIBS_PRIVATE "")
 
 if (ZLIB_FOUND)
-   target_link_libraries(freetype PRIVATE ${ZLIB_LIBRARIES})
+   target_link_libraries(freetype PRIVATE ZLIB::ZLIB)
-   target_include_directories(freetype PRIVATE ${ZLIB_INCLUDE_DIRS})
   list(APPEND PKGCONFIG_REQUIRES_PRIVATE "zlib")
 endif ()
@@ -596,12 +596,25 @@ if (NOT SKIP_INSTALL_LIBRARIES AND NOT SKIP_INSTALL_ALL)
   install(
     EXPORT freetype-targets
       DESTINATION ${CMAKE_INSTALL_LIBDIR}/cmake/freetype
-      FILE freetype-config.cmake
       COMPONENT headers)
   install(
     FILES ${PROJECT_BINARY_DIR}/freetype-config-version.cmake
     DESTINATION ${CMAKE_INSTALL_LIBDIR}/cmake/freetype
     COMPONENT headers)
+
+  if(ZLIB_FOUND AND BUILD_SHARED_LIBS)
+    file(WRITE "${CMAKE_CURRENT_BINARY_DIR}/freetype-config.cmake"
+[[include(CMakeFindDependencyMacro)
+find_dependency(ZLIB)
+include("${CMAKE_CURRENT_LIST_DIR}/freetype-targets.cmake")
+]])
+  else()
+    file(WRITE "${CMAKE_CURRENT_BINARY_DIR}/freetype-config.cmake"
+[[include("${CMAKE_CURRENT_LIST_DIR}/freetype-targets.cmake")
+]])
+  endif()
+
+  install(FILES ${CMAKE_CURRENT_BINARY_DIR}/freetype-config.cmake DESTINATION ${CMAKE_INSTALL_LIBDIR}/cmake/freetype)
 endif ()
 
 
