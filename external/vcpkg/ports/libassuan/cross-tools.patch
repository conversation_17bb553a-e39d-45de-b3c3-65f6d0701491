diff --git a/src/Makefile.am b/src/Makefile.am
index 6b9a46d..5156865 100644
--- a/src/Makefile.am
+++ b/src/Makefile.am
@@ -19,6 +19,13 @@
 # SPDX-License-Identifier: LGPL-2.1+
 ## Process this file with automake to produce Makefile.in
 
+# Building host tools for native triplet, or not at all
+CPPFLAGS_FOR_BUILD = $(CPPFLAGS)
+CFLAGS_FOR_BUILD = $(CFLAGS)
+LDFLAGS_FOR_BUILD = $(LDFLAGS)
+# Using native tools, either this build or from host triplet
+HOST_TOOLS_PREFIX ?= .
+
 pkgconfigdir = $(libdir)/pkgconfig
 pkgconfig_DATA = libassuan.pc
 
@@ -152,6 +159,6 @@ mkheader: mkheader.c Makefile
 	$(CC_FOR_BUILD) $(CFLAGS_FOR_BUILD) $(CPPFLAGS_FOR_BUILD) \
           $(LDFLAGS_FOR_BUILD) -I. -I$(srcdir) -o $@ $(srcdir)/mkheader.c
 
-assuan.h: assuan.h.in mkheader$(EXEEXT_FOR_BUILD) $(parts_of_assuan_h)
-	./mkheader$(EXEEXT_FOR_BUILD) $(host_os) $(srcdir)/assuan.h.in \
+assuan.h: assuan.h.in $(HOST_TOOLS_PREFIX)/mkheader$(EXEEXT_FOR_BUILD) $(parts_of_assuan_h)
+	$(HOST_TOOLS_PREFIX)/mkheader$(EXEEXT_FOR_BUILD) $(host_os) $(srcdir)/assuan.h.in \
                    $(PACKAGE_VERSION) $(VERSION_NUMBER) >$@
