{"name": "wampcc", "version-date": "2024-07-10", "description": "Wampcc is C++ library that implements the Web Application Messaging Protocol (WAMP) protocol.", "license": "MIT", "supports": "!(windows & arm64)", "dependencies": ["<PERSON><PERSON><PERSON>", "libuv", "openssl", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"examples": {"description": "Build example apps"}, "utils": {"description": "Build utility apps", "supports": "!windows"}}}