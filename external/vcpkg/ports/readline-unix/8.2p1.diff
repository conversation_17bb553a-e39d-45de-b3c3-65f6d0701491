diff --git a/nls.c b/nls.c
index 5c6a13b..8c027d6 100644
--- a/nls.c
+++ b/nls.c
@@ -141,6 +141,10 @@ _rl_init_locale (void)
   if (lspec == 0)
     lspec = "";
   ret = setlocale (LC_CTYPE, lspec);	/* ok, since it does not change locale */
+  if (ret == 0 || *ret == 0)
+    ret = setlocale (LC_CTYPE, (char *)NULL);
+  if (ret == 0 || *ret == 0)
+    ret = RL_DEFAULT_LOCALE;
 #else
   ret = (lspec == 0 || *lspec == 0) ? RL_DEFAULT_LOCALE : lspec;
 #endif
diff --git a/patchlevel b/patchlevel
index d8c9df7..fdf4740 100644
--- a/patchlevel
+++ b/patchlevel
@@ -1,3 +1,3 @@
 # Do not edit -- exists only for use by patch
 
-0
+1
