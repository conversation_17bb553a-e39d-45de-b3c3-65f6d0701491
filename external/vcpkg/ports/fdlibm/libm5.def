EXPORTS
	__ieee754_acos @ 1 ; 
	__ieee754_acosh @ 2 ; 
	__ieee754_asin @ 3 ; 
	__ieee754_atan2 @ 4 ; 
	__ieee754_atanh @ 5 ; 
	__ieee754_cosh @ 6 ; 
	__ieee754_exp @ 7 ; 
	__ieee754_fmod @ 8 ; 
	__ieee754_gamma @ 9 ; 
	__ieee754_gamma_r @ 10 ; 
	__ieee754_hypot @ 11 ; 
	__ieee754_j0 @ 12 ; 
	__ieee754_j1 @ 13 ; 
	__ieee754_jn @ 14 ; 
	__ieee754_lgamma @ 15 ; 
	__ieee754_lgamma_r @ 16 ; 
	__ieee754_log @ 17 ; 
	__ieee754_log10 @ 18 ; 
	__ieee754_pow @ 19 ; 
	__ieee754_rem_pio2 @ 20 ; 
	__ieee754_remainder @ 21 ; 
	__ieee754_scalb @ 22 ; 
	__ieee754_sinh @ 23 ; 
	__ieee754_sqrt @ 24 ; 
	__ieee754_y0 @ 25 ; 
	__ieee754_y1 @ 26 ; 
	__ieee754_yn @ 27 ; 
	__kernel_cos @ 28 ; 
	__kernel_rem_pio2 @ 29 ; 
	__kernel_sin @ 30 ; 
	__kernel_standard @ 31 ; 
	__kernel_tan @ 32 ; 
	_fdlib_version @ 33 DATA ; 
	acos @ 34 ; 
	acosh @ 35 ; 
	asin @ 36 ; 
	asinh @ 37 ; 
	atan @ 38 ; 
	atan2 @ 39 ; 
	atanh @ 40 ; 
	cbrt @ 41 ; 
	ceil @ 42 ; 
	copysign @ 43 ; 
	cos @ 44 ; 
	cosh @ 45 ; 
	erf @ 46 ; 
	erfc @ 47 ; 
	exp @ 48 ; 
	expm1 @ 49 ; 
	fabs @ 50 ; 
	ieee_finite @ 51 ; 
	floor @ 52 ; 
	fmod @ 53 ; 
	frexp @ 54 ; 
	ieee_gamma @ 55 ; 
	ieee_gamma_r @ 56 ; 
	hypot @ 57 ; 
	ilogb @ 58 ; 
	ieee_isnan @ 59 ; 
	j0 @ 60 ; 
	j1 @ 61 ; 
	jn @ 62 ; 
	ldexp @ 63 ; 
	ieee_lgamma @ 64 ; 
	ieee_lgamma_r @ 65 ; 
	log @ 66 ; 
	log10 @ 67 ; 
	log1p @ 68 ; 
	logb @ 69 ; 
	ieee_matherr @ 70 ; 
	modf @ 71 ; 
	nextafter @ 72 ; 
	pow @ 73 ; 
	remainder @ 74 ; 
	rint @ 75 ; 
	ieee_scalb @ 76 ; 
	scalbn @ 77 ; 
	signgam @ 78 DATA ; 
	ieee_significand @ 79 ; 
	sin @ 80 ; 
	sinh @ 81 ; 
	sqrt @ 82 ; 
	tan @ 83 ; 
	tanh @ 84 ; 
	y0 @ 85 ; 
	y1 @ 86 ; 
	yn @ 87 ; 
