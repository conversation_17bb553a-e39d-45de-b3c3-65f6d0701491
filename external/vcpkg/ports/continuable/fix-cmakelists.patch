Index: 4.2.0-0b164f3722.clean/CMakeLists.txt
===================================================================
--- 4.2.0-0b164f3722.clean.orig/CMakeLists.txt
+++ 4.2.0-0b164f3722.clean/CMakeLists.txt
@@ -82,7 +82,7 @@ if(NOT TARGET Threads::Threads)
   find_package(Threads REQUIRED)
 endif()
 
-if(CTI_CONTINUABLE_IS_TOP_LEVEL_PROJECT)
+if(false)
   include(cmake/CMakeLists.txt)
   add_subdirectory(dep)
 else()
@@ -168,8 +168,8 @@ if(CTI_CONTINUABLE_WITH_INSTALL)
   # Create an install target: Headers and license files
   install(DIRECTORY "${PROJECT_SOURCE_DIR}/include/continuable"
           DESTINATION "include")
-  install(FILES "LICENSE.txt" DESTINATION .)
-  install(FILES "Readme.md" DESTINATION .)
+  install(FILES "LICENSE.txt" DESTINATION share/${PROJECT_NAME} RENAME copyright)
+  install(FILES "Readme.md" DESTINATION share/${PROJECT_NAME})
 
   # Config.cmake
   write_basic_package_version_file(
