vcpkg_from_github(
    OUT_SOURCE_PATH SOURCE_PATH
    REPO Naios/continuable
    REF "${VERSION}"
    SHA512 069b1fe37d5dd3495639942af40b9d5fe90408ee0f92f61e85c8aab3e1132300eb75df216a84595cb05c35e0fcb1b789fda4dae3fd2b263ac70910f5374536cc
    HEAD_REF master
    PATCHES
        fix-cmakelists.patch
)

vcpkg_cmake_configure(
    SOURCE_PATH "${SOURCE_PATH}"
    OPTIONS
        -DCTI_CONTINUABLE_WITH_INSTALL=ON
        -DCTI_CONTINUABLE_WITH_TESTS=OFF
        -DCTI_CONTINUABLE_WITH_EXAMPLES=OFF
        -DCTI_CONTINUABLE_WITH_BENCHMARKS=OFF
        -DCTI_CONTINUABLE_WITH_NO_EXCEPTIONS=OFF
        -DCTI_CONTINUABLE_WITH_UNHANDLED_EXCEPTIONS=ON
        -DCTI_CONTINUABLE_WITH_EXPERIMENTAL_COROUTINE=ON
        -DCTI_CONTINUABLE_WITH_CPP_LATEST=ON # requires cxx_std_17
)
vcpkg_cmake_install()
vcpkg_copy_pdbs()
vcpkg_cmake_config_fixup(CONFIG_PATH lib/cmake/${PORT})

file(REMOVE_RECURSE "${CURRENT_PACKAGES_DIR}/lib" 
                    "${CURRENT_PACKAGES_DIR}/debug"
)
