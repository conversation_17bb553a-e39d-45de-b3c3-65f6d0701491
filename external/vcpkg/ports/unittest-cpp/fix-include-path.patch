diff --git a/cmake/UnitTest++Config.cmake b/cmake/UnitTest++Config.cmake
index afe165c..cc43a2b 100644
--- a/cmake/UnitTest++Config.cmake
+++ b/cmake/UnitTest++Config.cmake
@@ -1,2 +1,4 @@
 include("${CMAKE_CURRENT_LIST_DIR}/UnitTest++Targets.cmake")
-get_filename_component(UTPP_INCLUDE_DIRS "${CMAKE_CURRENT_LIST_DIR}/../../../include/" ABSOLUTE)
+get_filename_component(UTPP_INCLUDE_DIRS "${CMAKE_CURRENT_LIST_DIR}/../../include/" ABSOLUTE)
+
+get_filename_component(UnitTest++_INCLUDE_DIRS "${CMAKE_CURRENT_LIST_DIR}/../../include/" ABSOLUTE)
\ No newline at end of file
