diff --git a/src/sbml/validator/test/tps/dirent.c b/src/sbml/validator/test/tps/dirent.c
index beafc5e..48a60ca 100644
--- a/src/sbml/validator/test/tps/dirent.c
+++ b/src/sbml/validator/test/tps/dirent.c
@@ -1,3 +1,4 @@
+#if 0
 /* /////////////////////////////////////////////////////////////////////////////
  * File:    dirent.c
  *
@@ -239,3 +240,4 @@ struct dirent *readdir(DIR *dir)
 }
 
 /* ////////////////////////////////////////////////////////////////////////// */
+#endif
diff --git a/src/sbml/validator/test/tps/dirent.h b/src/sbml/validator/test/tps/dirent.h
index 4703aa2..8e4c29c 100644
--- a/src/sbml/validator/test/tps/dirent.h
+++ b/src/sbml/validator/test/tps/dirent.h
@@ -46,6 +46,9 @@
 
 #ifndef SYNSOFT_UNIXEM_INCL_H_DIRENT
 #define SYNSOFT_UNIXEM_INCL_H_DIRENT
+#include <dirent.h>
+#endif
+#if 0
 
 #ifndef _SYNSOFT_DOCUMENTATION_SKIP_SECTION
 # define SYNSOFT_UNIXEM_VER_H_DIRENT_MAJOR      2
