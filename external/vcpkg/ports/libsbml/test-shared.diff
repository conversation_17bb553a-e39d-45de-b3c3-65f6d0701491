diff --git a/src/sbml/test/CMakeLists.txt b/src/sbml/test/CMakeLists.txt
index 7a408d7..462d58a 100644
--- a/src/sbml/test/CMakeLists.txt
+++ b/src/sbml/test/CMakeLists.txt
@@ -49,6 +49,9 @@ if (EXTRA_INCLUDE_DIRS)
  include_directories(${EXTRA_INCLUDE_DIRS})
 endif(EXTRA_INCLUDE_DIRS)
 
+if(NOT TARGET ${LIBSBML_LIBRARY}-static)
+	add_library(${LIBSBML_LIBRARY}-static ALIAS ${LIBSBML_LIBRARY})
+endif()
 
 add_executable(test_sbml_sbml ${TEST_FILES})
 target_link_libraries(test_sbml_sbml ${LIBSBML_LIBRARY}-static CHECK::CHECK)
