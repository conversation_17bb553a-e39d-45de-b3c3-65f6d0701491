diff --git a/CMakeLists.txt b/CMakeLists.txt
index b2e97c6..5f5418d 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -435,8 +435,9 @@ set(LIBSBML_XML_LIBRARY_LIBS)
 if(WITH_EXPAT)
   
     if(NOT TARGET EXPAT::EXPAT)
-    find_package(EXPAT REQUIRED)
     endif()
+    find_package(EXPAT NAMES expat REQUIRED)
+    set(EXPAT_LIBRARY expat::expat)
 
     add_definitions( -DUSE_EXPAT )
     list(APPEND SWIG_EXTRA_ARGS -DUSE_EXPAT)
@@ -457,7 +458,8 @@ endif(WITH_EXPAT)
 set(USE_LIBXML OFF)
 if(WITH_LIBXML)
 
-  find_package(LIBXML REQUIRED)
+  find_package(LIBXML NAMES LibXml2 REQUIRED)
+  set(LIBXML_LIBRARY LibXml2::LibXml2)
   
   add_definitions( -DUSE_LIBXML )
   list(APPEND SWIG_EXTRA_ARGS -DUSE_LIBXML)
@@ -509,7 +511,7 @@ endif()
 option(WITH_BZIP2    "Enable the use of bzip2 compression."  ${BZIP_INITIAL_VALUE})
 set(USE_BZ2 OFF)
 if(WITH_BZIP2)
-  find_package(BZ2 REQUIRED)
+  find_package(BZip2 REQUIRED)
   set(USE_BZ2 ON)
   add_definitions( -DUSE_BZ2 )
   list(APPEND SWIG_EXTRA_ARGS -DUSE_BZ2)
@@ -535,6 +537,9 @@ Separate multiple directories using semicolons." )
 # Locate zlib
 #
 
+if(WITH_ZLIB)
+    find_package(ZLIB REQUIRED)
+endif()
 set(ZLIB_INITIAL_VALUE)
 if (NOT LIBZ_LIBRARY)
 find_library(LIBZ_LIBRARY
@@ -628,7 +633,6 @@ valid. It should contain the file zlib.h, but it does not.")
 endif(WITH_ZLIB)
 
 # install find scripts only for used dependencies
-install(FILES ${LIBSBML_FIND_MODULES} DESTINATION share/cmake/Modules)
 
 ###############################################################################
 #
@@ -685,7 +689,12 @@ if(WITH_CHECK)
 
   enable_testing()
 
-  find_package(CHECK REQUIRED)
+  find_package(CHECK NAMES check REQUIRED)
+  if(BUILD_SHARED_LIBS)
+    add_library(CHECK::CHECK ALIAS Check::checkShared)
+  else()
+    add_library(CHECK::CHECK ALIAS Check::check)
+  endif()
 
   if (UNIX)
       # setup valgrind
@@ -1018,18 +1027,29 @@ install(FILES ${DOCUMENTATION_FILES} DESTINATION ${MISC_PREFIX})
 # Write libsbml.pc
 #
 
-set (PRIVATE_LIBS "-lstdc++ -lm")
+set(FAKE_CXX_LINKAGE "")
+foreach(lib IN LISTS CMAKE_CXX_IMPLICIT_LINK_LIBRARIES)
+    if(lib IN_LIST CMAKE_C_IMPLICIT_LINK_LIBRARIES)
+        continue()
+    elseif(EXISTS "${lib}")
+        string(APPEND FAKE_CXX_LINKAGE " ${CMAKE_LINK_LIBRARY_FILE_FLAG}${lib}")
+    else()
+        string(APPEND FAKE_CXX_LINKAGE " ${CMAKE_LINK_LIBRARY_FLAG}${lib}")
+    endif()
+endforeach()
+set (PRIVATE_LIBS "${FAKE_CXX_LINKAGE}")
+set (PRIVATE_REQUIRES "")
 if (WITH_ZLIB)
-set (PRIVATE_LIBS "${LIBZ_LIBRARY} ${PRIVATE_LIBS}")
+    string(APPEND PRIVATE_REQUIRES " zlib")
 endif()
 if (WITH_BZIP2)
-set (PRIVATE_LIBS "${LIBBZ_LIBRARY} ${PRIVATE_LIBS}")
+    string(APPEND PRIVATE_REQUIRES " bzip2")
 endif()
 if (WITH_LIBXML)
-set (PRIVATE_LIBS "${LIBXML_LIBRARY} ${PRIVATE_LIBS}")
+    string(APPEND PRIVATE_REQUIRES " libxml-2.0")
 endif()
 if (WITH_EXPAT)
-set (PRIVATE_LIBS "${LIBEXPAT_LIBRARY} ${PRIVATE_LIBS}")
+    string(APPEND PRIVATE_REQUIRES " expat")
 endif()
 if (WITH_XERCES)
 set (PRIVATE_LIBS "${XERCES_LIBRARY} ${PRIVATE_LIBS}")
@@ -1051,9 +1071,7 @@ if (WITH_XERCES)
 file(APPEND "${CMAKE_CURRENT_BINARY_DIR}/libsbml.pc" "Conflicts: xerces-c = 2.6.0\n")
 endif()
 
-if (WITH_LIBXML)
-file(APPEND "${CMAKE_CURRENT_BINARY_DIR}/libsbml.pc" "Requires.private: libxml-2.0 >= 2.6.22\n")
-endif()
+file(APPEND "${CMAKE_CURRENT_BINARY_DIR}/libsbml.pc" "Requires.private: ${PRIVATE_REQUIRES}\n")
 
 if (UNIX OR MINGW)
 install(FILES "${CMAKE_CURRENT_BINARY_DIR}/libsbml.pc"
diff --git a/src/CMakeLists.txt b/src/CMakeLists.txt
index 79301a6..0957d29 100644
--- a/src/CMakeLists.txt
+++ b/src/CMakeLists.txt
@@ -311,7 +311,7 @@ if(WITH_BZIP2)
         sbml/compress/bzfstream.h
         sbml/compress/bzfstream.cpp
         )
-  set(LIBSBML_LIBS ${LIBSBML_LIBS} BZ2::BZ2)
+  set(LIBSBML_LIBS ${LIBSBML_LIBS} BZip2::BZip2)
 
 endif()
 
@@ -405,7 +405,7 @@ if(WITH_EXPAT)
         sbml/xml/ExpatHandler.h
         sbml/xml/ExpatParser.h
     )
-    set(LIBSBML_LIBS ${LIBSBML_LIBS} EXPAT::EXPAT)
+    set(LIBSBML_LIBS ${LIBSBML_LIBS} expat::expat)
 
 endif(WITH_EXPAT)
 
@@ -423,7 +423,7 @@ if(WITH_LIBXML)
         sbml/xml/LibXMLParser.h
         sbml/xml/LibXMLTranscode.h
     )
-    set(LIBSBML_LIBS ${LIBSBML_LIBS} LIBXML::LIBXML)
+    set(LIBSBML_LIBS ${LIBSBML_LIBS} LibXml2::LibXml2)
 
 endif(WITH_LIBXML)
 
