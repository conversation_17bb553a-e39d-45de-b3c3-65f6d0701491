
get_filename_component(_kinectsdk2_root "${CMAKE_CURRENT_LIST_FILE}" PATH)
get_filename_component(_kinectsdk2_root "${_kinectsdk2_root}" PATH)
get_filename_component(_kinectsdk2_root "${_kinectsdk2_root}" PATH)

set(_kinectsdk2_rel_lib "${_kinectsdk2_root}/lib/Kinect20.lib")
set(_kinectsdk2_dbg_lib "${_kinectsdk2_root}/debug/lib/Kinect20.lib")
if (EXISTS "${_kinectsdk2_rel_lib}" OR EXISTS "${_kinectsdk2_dbg_lib}")

    add_library(unofficial::kinectsdk2::kinectsdk2 INTERFACE IMPORTED)
    set_target_properties(unofficial::kinectsdk2::kinectsdk2 PROPERTIES INTERFACE_INCLUDE_DIRECTORIES "${_kinectsdk2_root}/include")

    if (EXISTS "${_kinectsdk2_rel_lib}")
        set_target_properties(unofficial::kinectsdk2::kinectsdk2 
            PROPERTIES IMPORTED_LOCATION_RELEASE "${_kinectsdk2_rel_lib}")
        set_property(TARGET unofficial::kinectsdk2::kinectsdk2 APPEND PROPERTY IMPORTED_CONFIGURATIONS RELEASE)
    endif()
    if (EXISTS "${_kinectsdk2_dbg_lib}")
        set_target_properties(unofficial::kinectsdk2::kinectsdk2 
            PROPERTIES IMPORTED_LOCATION_DEBUG "${_kinectsdk2_dbg_lib}")
        set_property(TARGET unofficial::kinectsdk2::kinectsdk2 APPEND PROPERTY IMPORTED_CONFIGURATIONS DEBUG)
    endif()

else()

    set(kinectsdk2_FOUND FALSE)

endif()

unset(_kinectsdk2_rel_lib)
unset(_kinectsdk2_dbg_lib)

unset(_kinectsdk2_root)
