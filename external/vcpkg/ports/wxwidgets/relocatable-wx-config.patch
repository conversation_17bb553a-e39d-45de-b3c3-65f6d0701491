diff --git a/wx-config.in b/wx-config.in
index 4df8571..a90db3d 100644
--- a/wx-config.in
+++ b/wx-config.in
@@ -398,8 +398,23 @@ is_cross()  { [ "x@cross_compiling@" = "xyes" ]; }
 
 
 # Determine the base directories we require.
-prefix=${input_option_prefix-${this_prefix:-@prefix@}}
-exec_prefix=${input_option_exec_prefix-${input_option_prefix-${this_exec_prefix:-@exec_prefix@}}}
+vcpkg_prefix=$(CDPATH= cd -- "$(dirname -- "$0")" && pwd -P)
+case "$vcpkg_prefix" in
+    */lib/wx/config)
+        vcpkg_prefix=${vcpkg_prefix%/*/*/*}
+        ;;
+    */tools/wxwidgets/debug)
+        vcpkg_prefix=${vcpkg_prefix%/*/*/*}/debug
+        ;;
+    */tools/wxwidgets)
+        vcpkg_prefix=${vcpkg_prefix%/*/*}
+        ;;
+esac
+if [ -n "@MINGW@" -a -n "@CMAKE_HOST_WIN32@" ]; then
+    vcpkg_prefix=$(cygpath -m "$vcpkg_prefix")
+fi
+prefix=${input_option_prefix-${this_prefix:-$vcpkg_prefix}}
+exec_prefix=${input_option_exec_prefix-${input_option_prefix-${this_exec_prefix:-$prefix}}}
 wxconfdir="@libdir@/wx/config"
 
 installed_configs=`cd "$wxconfdir" 2> /dev/null && ls | grep -v "^inplace-"`
@@ -936,6 +951,9 @@ prefix=${this_prefix-$prefix}
 exec_prefix=${this_exec_prefix-$exec_prefix}
 
 includedir="@includedir@"
+if [ "@CMAKE_BUILD_TYPE@" = "Debug" ] ; then
+    includedir="${includedir%/debug/include}/include"
+fi
 libdir="@libdir@"
 bindir="@bindir@"
 
