diff --git a/build/cmake/modules/FindGTK3.cmake b/build/cmake/modules/FindGTK3.cmake
index d2939a1..daf33fe 100644
--- a/build/cmake/modules/FindGTK3.cmake
+++ b/build/cmake/modules/FindGTK3.cmake
@@ -47,6 +47,12 @@ include(CheckSymbolExists)
 set(CMAKE_REQUIRED_INCLUDES ${GTK3_INCLUDE_DIRS})
 check_symbol_exists(GDK_WINDOWING_WAYLAND "gdk/gdk.h" wxHAVE_GDK_WAYLAND)
 check_symbol_exists(GDK_WINDOWING_X11 "gdk/gdk.h" wxHAVE_GDK_X11)
+# With Lerc support in TIFF, Gtk3 may carry C++ compiler libs which break FindWxWidgets.cmake.
+# WxWidgets is C++, so we can remove them here using the inverse pattern.
+set(cxx_libs "${CMAKE_CXX_IMPLICIT_LINK_LIBRARIES}")
+list(REMOVE_ITEM cxx_libs ${CMAKE_C_IMPLICIT_LINK_LIBRARIES})
+list(REMOVE_ITEM GTK3_LINK_LIBRARIES ${cxx_libs})
+set(GTK3_LIBRARIES "${GTK3_LINK_LIBRARIES}" CACHE INTERNAL "")
 include(FindPackageHandleStandardArgs)
 FIND_PACKAGE_HANDLE_STANDARD_ARGS(GTK3 DEFAULT_MSG GTK3_INCLUDE_DIRS GTK3_LIBRARIES VERSION_OK)
 
