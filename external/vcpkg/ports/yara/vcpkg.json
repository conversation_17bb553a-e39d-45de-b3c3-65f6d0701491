{"name": "yara", "version": "4.5.2", "port-version": 1, "description": "The pattern matching swiss knife", "homepage": "https://github.com/VirusTotal/yara", "license": "BSD-3-<PERSON><PERSON>", "supports": "!uwp", "dependencies": ["openssl", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"cuckoo": {"description": "The Cuckoo module enables you to create YARA rules based on behavioral information generated by Cuckoo sandbox.", "dependencies": ["<PERSON><PERSON><PERSON>"]}, "dotnet": {"description": "The dotnet module allows you to create more fine-grained rules for .NET files by using attributes and features of the .NET file format."}}}