diff --git a/app/app_pb/CMakeLists.txt b/app/app_pb/CMakeLists.txt
index edd036188..1aae43a81 100644
--- a/app/app_pb/CMakeLists.txt
+++ b/app/app_pb/CMakeLists.txt
@@ -68,7 +68,7 @@ target_compile_options(${PROJECT_NAME}
 
 set_property(TARGET ${PROJECT_NAME} PROPERTY POSITION_INDEPENDENT_CODE ON)
 
-target_link_libraries(${PROJECT_NAME} protobuf::libprotobuf)
+target_link_libraries(${PROJECT_NAME} PUBLIC protobuf::libprotobuf)
 target_compile_features(${PROJECT_NAME} PUBLIC cxx_std_14) 
 
 ecal_install_library(${PROJECT_NAME})
diff --git a/ecal/core_pb/CMakeLists.txt b/ecal/core_pb/CMakeLists.txt
index e8f0704c7..502a92c11 100644
--- a/ecal/core_pb/CMakeLists.txt
+++ b/ecal/core_pb/CMakeLists.txt
@@ -63,7 +63,7 @@ target_compile_options(${PROJECT_NAME}
 
 set_property(TARGET ${PROJECT_NAME} PROPERTY POSITION_INDEPENDENT_CODE ON)
 
-target_link_libraries(${PROJECT_NAME} protobuf::libprotobuf)
+target_link_libraries(${PROJECT_NAME} PUBLIC protobuf::libprotobuf)
 target_compile_features(${PROJECT_NAME} PUBLIC cxx_std_14)
 
 ecal_install_library(${PROJECT_NAME})
