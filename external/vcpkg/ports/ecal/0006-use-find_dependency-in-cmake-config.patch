diff --git a/cmake/eCALConfig.cmake.in b/cmake/eCALConfig.cmake.in
index 704da4de8..e8cfb765c 100644
--- a/cmake/eCALConfig.cmake.in
+++ b/cmake/eCALConfig.cmake.in
@@ -25,12 +25,21 @@ set(eCAL_VERSION_MAJOR  @eCAL_VERSION_MAJOR@)
 set(eCAL_VERSION_MINOR  @eCAL_VERSION_MINOR@)
 set(eCAL_VERSION_PATCH  @eCAL_VERSION_PATCH@)
 set(eCAL_VERSION_STRING @eCAL_VERSION_STRING@)
+set(eCAL_IS_SHARED @BUILD_SHARED_LIBS@)
 
 # eCAL is provided only with Release and Debug Version, thus map the other configs to Release build.
 set(CMAKE_MAP_IMPORTED_CONFIG_MINSIZEREL Release "")
 set(CMAKE_MAP_IMPORTED_CONFIG_RELWITHDEBINFO Release "")
 
-find_package(Protobuf REQUIRED)
+include(CMakeFindDependencyMacro)
+find_dependency(Protobuf CONFIG)
+
+# Ensure transitive dependencies are present for static builds
+if(NOT eCAL_IS_SHARED)
+  find_dependency(asio)
+  find_dependency(tcp_pubsub)
+  find_dependency(HDF5)
+endif()
 
 include("@PACKAGE_eCAL_install_cmake_dir@/helper_functions/ecal_add_functions.cmake")
 include("@PACKAGE_eCAL_install_cmake_dir@/helper_functions/ecal_helper_functions.cmake")
@@ -44,6 +53,6 @@ include("@PACKAGE_eCAL_install_cmake_dir@/eCALTargets.cmake")
 #  list(APPEND CMAKE_PREFIX_PATH "${PACKAGE_PREFIX_DIR}/../../../../cmake")
 #endif()
 
-find_package(CMakeFunctions REQUIRED)
+find_dependency(CMakeFunctions CONFIG)
 
-find_package(Threads REQUIRED)
+find_dependency(Threads)
