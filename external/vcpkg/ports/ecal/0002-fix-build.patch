diff --git a/CMakeLists.txt b/CMakeLists.txt
index e82e353e0..19d8f2a93 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -26,7 +26,8 @@ endif (POLICY CMP0077)
 
 list(APPEND CMAKE_MODULE_PATH
   ${CMAKE_CURRENT_SOURCE_DIR}/cmake
-  ${CMAKE_CURRENT_SOURCE_DIR}/cmake/Modules)
+  # Removed project specific Module overrides
+)
   
 set(eCAL_DIR ${CMAKE_CURRENT_SOURCE_DIR}/cmake/Modules)
 
@@ -221,6 +222,8 @@ set(possible_subprojects
 set(ECAL_THIRDPARTY_BUILD_CMAKEFUNCTIONS ${ECAL_THIRDPARTY_BUILD_CMAKE_FUNCTIONS})
 
 # if a package does need to be build, include the cmake file with build instructions
+# Disable all submodules, except CMakeFunctions which isn't a submodule and required
+set(possible_subprojects "CMakeFunctions")
 foreach (dep IN LISTS possible_subprojects)
   string(TOUPPER ${dep} dep_upper)
   string(TOLOWER ${dep} dep_lower)
@@ -336,7 +339,9 @@ add_subdirectory(ecal/core)
 # custom libs
 # --------------------------------------------------------
 add_subdirectory(lib/ThreadingUtils)
-add_subdirectory(lib/CustomTclap)
+# Removed due to only being used by the apps, which are disabled, and for
+# publicly linking tclap::tclap which doesn't exist outside of eCAL
+# add_subdirectory(lib/CustomTclap)
 add_subdirectory(lib/ecal_utils)
 
 if(HAS_QT)
@@ -559,7 +564,9 @@ endif()
 # --------------------------------------------------------
 # create package
 # --------------------------------------------------------
+if(CPACK_PACK_WITH_INNOSETUP)
 include(cpack/cpack_variables.cmake)
+endif()
 
 message(STATUS "Build Options:")
 message(STATUS "--------------------------------------------------------------------------------")
diff --git a/contrib/ecalhdf5/CMakeLists.txt b/contrib/ecalhdf5/CMakeLists.txt
index d5285101c..3b7eb705a 100644
--- a/contrib/ecalhdf5/CMakeLists.txt
+++ b/contrib/ecalhdf5/CMakeLists.txt
@@ -55,7 +55,12 @@ set(ecalhdf5_header_base
     include/ecalhdf5/eh5_types.h
 )
 
+if (WIN32)
+# This library, ecal::hdf5 does not export any symbols on Windows. Must be static
+ecal_add_static_library(${PROJECT_NAME} ${ecalhdf5_src} ${ecalhdf5_header_base})
+else()
 ecal_add_library(${PROJECT_NAME} ${ecalhdf5_src} ${ecalhdf5_header_base})
+endif()
 add_library(eCAL::${PROJECT_NAME} ALIAS ${PROJECT_NAME})
 
 target_include_directories(${PROJECT_NAME}
diff --git a/contrib/ecalproto/CMakeLists.txt b/contrib/ecalproto/CMakeLists.txt
index 04f1a1b9a..58df32705 100644
--- a/contrib/ecalproto/CMakeLists.txt
+++ b/contrib/ecalproto/CMakeLists.txt
@@ -37,7 +37,12 @@ set(ecal_protobuf_header
     include/ecal/protobuf/ecal_proto_visitor.h
 )
 
+if (WIN32)
+# This library, ecal::proto does not export any symbols on Windows. Must be static
+ecal_add_static_library(${PROJECT_NAME} ${ecal_protobuf_src} ${ecal_protobuf_header})
+else()
 ecal_add_library(${PROJECT_NAME} ${ecal_protobuf_src} ${ecal_protobuf_header})
+endif()
 add_library(eCAL::${PROJECT_NAME} ALIAS ${PROJECT_NAME})
 target_include_directories(${PROJECT_NAME} PUBLIC 
   $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
diff --git a/ecal/core/CMakeLists.txt b/ecal/core/CMakeLists.txt
index f18dd6291..8655d134a 100644
--- a/ecal/core/CMakeLists.txt
+++ b/ecal/core/CMakeLists.txt
@@ -20,8 +20,6 @@ project(core VERSION ${eCAL_VERSION_STRING})
 
 find_package(Threads      REQUIRED)
 find_package(asio         REQUIRED)
-find_package(tclap        REQUIRED)
-find_package(simpleini    REQUIRED)
 find_package(tcp_pubsub   REQUIRED)
 if (ECAL_NPCAP_SUPPORT)
   find_package(udpcap REQUIRED)
@@ -549,8 +547,6 @@ target_link_libraries(${PROJECT_NAME}
     $<$<BOOL:${WIN32}>:wsock32>
     $<$<BOOL:${QNXNTO}>:socket>
     asio::asio
-    tclap::tclap
-    simpleini::simpleini
     eCAL::core_pb
     Threads::Threads
     eCAL::ecal-utils
@@ -558,6 +554,14 @@ target_link_libraries(${PROJECT_NAME}
     ecal_service
 )
 
+# tclap is header only and only used for implementation
+find_path(TCLAP_INCLUDE_DIRS "tclap/Arg.h")
+target_include_directories(${PROJECT_NAME}  PRIVATE ${TCLAP_INCLUDE_DIRS})
+
+# simpleini is header only and only used for implementation
+find_path(SIMPLEINI_INCLUDE_DIRS "ConvertUTF.c")
+target_include_directories(${PROJECT_NAME} PRIVATE ${SIMPLEINI_INCLUDE_DIRS})
+
 set_property(TARGET ${PROJECT_NAME}   PROPERTY FOLDER ecal/core)
 set_property(TARGET ${PROJECT_NAME}_c PROPERTY FOLDER ecal/core)
 
diff --git a/ecal/service/ecal_service/CMakeLists.txt b/ecal/service/ecal_service/CMakeLists.txt
index 0ac70a214..6a565c726 100644
--- a/ecal/service/ecal_service/CMakeLists.txt
+++ b/ecal/service/ecal_service/CMakeLists.txt
@@ -99,6 +99,8 @@ set_target_properties(${PROJECT_NAME} PROPERTIES
     FOLDER ecal/service
 )
 
+ecal_install_library(${PROJECT_NAME})
+
 ##################################
 
 source_group(TREE "${CMAKE_CURRENT_SOURCE_DIR}" FILES 
diff --git a/thirdparty/cmakefunctions/cmake_functions/CMakeLists.txt b/thirdparty/cmakefunctions/cmake_functions/CMakeLists.txt
index 8d13b791c..6ed0d1a14 100644
--- a/thirdparty/cmakefunctions/cmake_functions/CMakeLists.txt
+++ b/thirdparty/cmakefunctions/cmake_functions/CMakeLists.txt
@@ -40,4 +40,6 @@ foreach (f ${file_list})
   install( FILES ${f} DESTINATION "${cmake_functions_install_cmake_dir}/${dir}" )
 endforeach()
 
+if(CPACK_PACK_WITH_INNOSETUP)
 include(cmake/cpack_variables.cmake)
+endif()
diff --git a/thirdparty/cmakefunctions/cmake_functions/cmake_functions.cmake b/thirdparty/cmakefunctions/cmake_functions/cmake_functions.cmake
index a053b06e6..127eb14e6 100644
--- a/thirdparty/cmakefunctions/cmake_functions/cmake_functions.cmake
+++ b/thirdparty/cmakefunctions/cmake_functions/cmake_functions.cmake
@@ -5,22 +5,12 @@ set (file_list_include
   target_definitions/targets_protobuf.cmake
 )
 
-if(WIN32)
-  list(APPEND file_list_include
-    qt/qt_windeployqt.cmake
-  )
-endif()
 
 set(file_list_no_include
   protoc_functions/protoc_generate_cpp.cmake
   protoc_functions/protoc_generate_python.cmake
 )
 
-if(WIN32)
-  list(APPEND file_list_no_include
-    qt/qt_windeployqt_threadsafe_cmake.bat.in
-  )
-endif()
 
 # Set list of all files to be installed by CMake Script.
 set(file_list
