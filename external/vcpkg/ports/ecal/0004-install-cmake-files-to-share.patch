diff --git a/CMakeLists.txt b/CMakeLists.txt
index 19d8f2a93..dca8948be 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -277,7 +277,7 @@ set(eCAL_install_app_dir           ${CMAKE_INSTALL_BINDIR})
 set(eCAL_install_archive_dir       ${CMAKE_INSTALL_LIBDIR})
 set(eCAL_install_archive_dyn_dir   ${CMAKE_INSTALL_LIBDIR})
 set(eCAL_install_bin_dir           ${CMAKE_INSTALL_BINDIR})
-set(eCAL_install_cmake_dir         ${CMAKE_INSTALL_LIBDIR}/cmake/eCAL)
+set(eCAL_install_cmake_dir         share/eCAL)
 set(eCAL_install_config_dir        ${CMAKE_INSTALL_SYSCONFDIR}/ecal)
 set(eCAL_install_doc_dir           ${CMAKE_INSTALL_DOCDIR})
 set(eCAL_install_include_dir       ${CMAKE_INSTALL_INCLUDEDIR})
diff --git a/thirdparty/cmakefunctions/cmake_functions/CMakeLists.txt b/thirdparty/cmakefunctions/cmake_functions/CMakeLists.txt
index 6ed0d1a14..9a833a1f9 100644
--- a/thirdparty/cmakefunctions/cmake_functions/CMakeLists.txt
+++ b/thirdparty/cmakefunctions/cmake_functions/CMakeLists.txt
@@ -4,12 +4,8 @@ include(cmake_functions.cmake)
 
 project(CMakeFunctions VERSION 0.4.1)
 
-if (MSVC)
-# Variable definitions
-set(cmake_functions_install_cmake_dir   cmake)
-else (MSVC)
-set(cmake_functions_install_cmake_dir   lib/cmake/${PROJECT_NAME}-${PROJECT_VERSION})
-endif (MSVC)
+set(cmake_functions_install_cmake_dir "share/${PROJECT_NAME}")
+
 set(cmake_functions_config              ${CMAKE_CURRENT_BINARY_DIR}/cmake/${PROJECT_NAME}Config.cmake)
 set(cmake_functions_config_version      ${CMAKE_CURRENT_BINARY_DIR}/cmake/${PROJECT_NAME}ConfigVersion.cmake)
 
