{"name": "lib<PERSON><PERSON><PERSON><PERSON>", "version": "1.5.1", "port-version": 2, "description": "libOPNMIDI is a free Software MIDI synthesizer library with OPN2 (YM2612) and OPNA (YM2608) emulation", "homepage": "https://github.com/Wohlstand/libOPNMIDI", "license": "LGPL-2.1-or-later OR GPL-2.0-or-later OR GPL-3.0-or-later OR MIT", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "default-features": ["gens-emulator", "mame-ym2608-emulator", "mame-ym2612-emulator", "midi-sequencer", "mus", "np2-emulator", "nuked-emulator", "xmi"], "features": {"gens-emulator": {"description": "Build with GENS 2.10 emulator (innacurate, fastest)"}, "gx-emulator": {"description": "Build with Genesis Plus GX emulator (experimental)"}, "mame-ym2608-emulator": {"description": "Build with MAME YM2608 emulator (well-accurate and fast)"}, "mame-ym2612-emulator": {"description": "Build with MAME YM2612 emulator (well-accurate and fast)"}, "midi-sequencer": {"description": "Build with embedded MIDI sequencer"}, "mus": {"description": "Support for DMX MUS files"}, "np2-emulator": {"description": "Build with Neko Project 2 YM2608 emulator (semi-accurate and fast)"}, "nuked-emulator": {"description": "Build with Nuked OPN2 emulator (very accurate, needs more CPU power)"}, "pmdwin-emulator": {"description": "Build with PMDWin emulator (experimental)"}, "xmi": {"description": "Support for AIL XMI files"}}}