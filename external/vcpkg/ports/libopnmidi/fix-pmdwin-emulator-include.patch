diff --git a/src/chips/pmdwin/opna.c b/src/chips/pmdwin/opna.c
index 3dbf164..78b88a5 100644
--- a/src/chips/pmdwin/opna.c
+++ b/src/chips/pmdwin/opna.c
@@ -27,7 +27,6 @@ SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE. */
 #include <stdint.h>
 #include <stdarg.h>
 #include <math.h>
-#include <unistd.h>
 #include <assert.h>
 #include "op.h"
 #include "psg.h"
diff --git a/src/chips/pmdwin/psg.c b/src/chips/pmdwin/psg.c
index 44a7cf0..a1b1c43 100644
--- a/src/chips/pmdwin/psg.c
+++ b/src/chips/pmdwin/psg.c
@@ -39,7 +39,6 @@ SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE. */
 #include <stdlib.h>
 #include <string.h>
 #include <math.h>
-#include <unistd.h>
 #include "op.h"
 #include "psg.h"
 
@@ -340,4 +339,3 @@ void PSGMix(PSG *psg, int32_t *dest, uint32_t nsamples)
         }
     }
 }
-
