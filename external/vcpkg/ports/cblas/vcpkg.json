{"$comment": "Keep the platform expressions in sync with the wrappers installed by the portfiles!", "name": "cblas", "version-date": "2024-03-19", "description": "Metapackage for packages which provide CBLAS", "license": null, "dependencies": [{"name": "lapack-reference", "features": ["cblas"], "platform": "!osx & !ios & !uwp & !(windows & arm) & windows & static"}, {"name": "openblas", "platform": "!osx & !ios & (uwp | (windows & arm) | !windows | !static)"}, {"name": "vcpkg-cmake", "host": true}]}