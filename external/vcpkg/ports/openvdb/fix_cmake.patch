diff --git a/CMakeLists.txt b/CMakeLists.txt
index 34df130..70351ef 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -311,12 +311,9 @@ endif()
 
 if(OPENVDB_INSTALL_CMAKE_MODULES)
   set(OPENVDB_CMAKE_MODULES
-    cmake/FindBlosc.cmake
     cmake/FindJemalloc.cmake
     cmake/FindLog4cplus.cmake
-    cmake/FindOpenEXR.cmake
     cmake/FindOpenVDB.cmake
-    cmake/FindTBB.cmake
     cmake/OpenVDBGLFW3Setup.cmake
     cmake/OpenVDBHoudiniSetup.cmake
     cmake/OpenVDBMayaSetup.cmake
diff --git a/cmake/FindOpenVDB.cmake b/cmake/FindOpenVDB.cmake
index 33b0838..c1e9d00 100644
--- a/cmake/FindOpenVDB.cmake
+++ b/cmake/FindOpenVDB.cmake
@@ -488,7 +488,7 @@ endif()
 
 # Add standard dependencies
 
-find_package(TBB REQUIRED COMPONENTS tbb)
+find_package(TBB CONFIG REQUIRED)
 
 # Add deps for pyopenvdb
 
@@ -613,7 +613,7 @@ elseif(NOT OPENVDB_USE_STATIC_LIBS)
 endif()
 
 if(OpenVDB_USES_BLOSC)
-  find_package(Blosc REQUIRED)
+  find_package(blosc CONFIG REQUIRED)
 endif()
 
 if(OpenVDB_USES_ZLIB)
@@ -670,7 +670,7 @@ set(_OPENVDB_HIDDEN_DEPENDENCIES)
 
 if(NOT OPENVDB_USE_STATIC_LIBS)
   if(OpenVDB_USES_BLOSC)
-    list(APPEND _OPENVDB_HIDDEN_DEPENDENCIES Blosc::blosc)
+    list(APPEND _OPENVDB_HIDDEN_DEPENDENCIES blosc)
   endif()
   if(OpenVDB_USES_ZLIB)
     list(APPEND _OPENVDB_HIDDEN_DEPENDENCIES ZLIB::ZLIB)
diff --git a/nanovdb/nanovdb/CMakeLists.txt b/nanovdb/nanovdb/CMakeLists.txt
index 4e0284e..ca3449b 100644
--- a/nanovdb/nanovdb/CMakeLists.txt
+++ b/nanovdb/nanovdb/CMakeLists.txt
@@ -122,15 +122,15 @@ if(NANOVDB_USE_OPENVDB)
 endif()
 
 if(NANOVDB_USE_TBB AND NOT TARGET TBB::tbb)
-  find_package(TBB ${MINIMUM_TBB_VERSION} REQUIRED)
+  find_package(TBB CONFIG REQUIRED)
 endif()
 
 if(NANOVDB_USE_BLOSC AND NOT TARGET Blosc::blosc)
-  find_package(Blosc ${MINIMUM_BLOSC_VERSION} REQUIRED)
+  find_package(blosc CONFIG REQUIRED)
 endif()
 
 if(NANOVDB_USE_ZLIB AND NOT TARGET ZLIB::ZLIB)
-  find_package(ZLIB ${MINIMUM_ZLIB_VERSION} REQUIRED)
+  find_package(ZLIB REQUIRED)
 endif()
 
 if(NANOVDB_USE_MAGICAVOXEL)
@@ -290,7 +290,7 @@ if(NANOVDB_USE_TBB)
 endif()
 
 if(NANOVDB_USE_BLOSC)
-  target_link_libraries(nanovdb INTERFACE Blosc::blosc)
+  target_link_libraries(nanovdb INTERFACE $<IF:$<TARGET_EXISTS:blosc_shared>,blosc_shared,blosc_static>)
   target_compile_definitions(nanovdb INTERFACE -DNANOVDB_USE_BLOSC)
 endif()
 
diff --git a/openvdb/openvdb/CMakeLists.txt b/openvdb/openvdb/CMakeLists.txt
index 3db0e4e..9475709 100644
--- a/openvdb/openvdb/CMakeLists.txt
+++ b/openvdb/openvdb/CMakeLists.txt
@@ -121,7 +121,7 @@ if(OPENVDB_USE_DELAYED_LOADING)
   endif()
 endif()
 
-find_package(TBB ${MINIMUM_TBB_VERSION} REQUIRED COMPONENTS tbb)
+find_package(TBB CONFIG REQUIRED)
 if(OPENVDB_FUTURE_DEPRECATION AND FUTURE_MINIMUM_TBB_VERSION)
   if(${TBB_VERSION} VERSION_LESS FUTURE_MINIMUM_TBB_VERSION)
     message(DEPRECATION "Support for TBB versions < ${FUTURE_MINIMUM_TBB_VERSION} "
@@ -150,6 +150,7 @@ endif()
 
 if(USE_BLOSC)
   # Find Blosc libraries
+  if(0)
   find_package(Blosc ${MINIMUM_BLOSC_VERSION} REQUIRED)
   if(Blosc_VERSION)
     if(OPENVDB_FUTURE_DEPRECATION AND FUTURE_MINIMUM_BLOSC_VERSION)
@@ -170,6 +171,8 @@ if(USE_BLOSC)
         "strongly recommend using the new future minimum version '${FUTURE_MINIMUM_BLOSC_VERSION}'")
     endif()
   endif()
+  endif()
+  find_package(blosc CONFIG REQUIRED)
 else()
   message(WARNING "Blosc support is disabled. It is strongly recommended to "
     "enable blosc for optimal builds of OpenVDB and to support compatible "
@@ -226,7 +229,11 @@ endif()
 # /usr/local
 
 if(USE_BLOSC)
-  list(APPEND OPENVDB_CORE_DEPENDENT_LIBS Blosc::blosc)
+  if(BUILD_SHARED_LIBS)
+    list(APPEND OPENVDB_CORE_DEPENDENT_LIBS blosc_shared)
+  else()
+    list(APPEND OPENVDB_CORE_DEPENDENT_LIBS blosc_static)
+  endif()
 endif()
 
 if(USE_BLOSC OR USE_ZLIB)
diff --git a/openvdb_cmd/CMakeLists.txt b/openvdb_cmd/CMakeLists.txt
index edbb1a4..463d3ed 100644
--- a/openvdb_cmd/CMakeLists.txt
+++ b/openvdb_cmd/CMakeLists.txt
@@ -53,7 +53,7 @@ if(CONCURRENT_MALLOC STREQUAL "Jemalloc")
   find_package(Jemalloc REQUIRED)
   list(APPEND OPENVDB_BINARIES_DEPENDENT_LIBS Jemalloc::jemalloc)
 elseif(CONCURRENT_MALLOC STREQUAL "Tbbmalloc")
-  find_package(TBB ${MINIMUM_TBB_VERSION} REQUIRED COMPONENTS tbbmalloc)
+  find_package(TBB CONFIG REQUIRED)
   list(APPEND OPENVDB_BINARIES_DEPENDENT_LIBS TBB::tbbmalloc)
 endif()
 
