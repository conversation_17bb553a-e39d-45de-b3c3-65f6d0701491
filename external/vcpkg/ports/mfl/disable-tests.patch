diff --git a/CMakeLists.txt b/CMakeLists.txt
index c411d11..9336e1d 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -18,17 +18,6 @@ include(cmake/StaticAnalyzers.cmake)
 
 
 add_subdirectory(src)
-add_subdirectory(tests/fonts_for_tests)
-add_subdirectory(tests/unit_tests)
-
-# Building approval tests causes ICE on msvc
-if(CMAKE_CXX_COMPILER_ID STREQUAL "GNU" OR CMAKE_CXX_COMPILER_ID STREQUAL "Clang")
-    add_subdirectory(tests/approval_tests)
-endif()
-
-if(CMAKE_CXX_COMPILER_ID STREQUAL "Clang")
-    add_subdirectory(tests/fuzz_tests)
-endif()
 
 include(GNUInstallDirs)
 
