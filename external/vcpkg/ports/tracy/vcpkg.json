{"name": "tracy", "version": "0.11.1", "port-version": 2, "description": "A real time, nanosecond resolution, remote telemetry, hybrid frame and sampling profiler for games and other applications.", "homepage": "https://github.com/wolfpld/tracy", "license": "BSD-3-<PERSON><PERSON>", "supports": "!(windows & (arm | uwp))", "dependencies": [{"name": "pthreads", "platform": "!windows"}, {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "default-features": ["crash-handler"], "features": {"cli-tools": {"description": "Build Tracy command-line tools: `capture`, `csvexport`, `import-chrome`, `import-fuchsia` and `update`", "supports": "!(windows & x86)", "dependencies": [{"name": "capstone", "features": ["arm", "arm64", "x86"]}, {"name": "dbus", "default-features": false, "platform": "!windows"}, "freetype", "glfw3", {"name": "tbb", "platform": "!windows"}]}, "crash-handler": {"description": "Enable crash handler"}, "fibers": {"description": "Enable fibers support"}, "gui-tools": {"description": "Build Tracy GUI tool: `profiler` (aka <PERSON><PERSON>` executable)", "supports": "!(windows & x86)", "dependencies": [{"name": "capstone", "features": ["arm", "arm64", "x86"]}, {"name": "dbus", "default-features": false, "platform": "!windows"}, "freetype", "glfw3", {"name": "tbb", "platform": "!windows"}]}, "on-demand": {"description": "Enable on-demand profiling"}, "verbose": {"description": "Enables verbose logging"}}}