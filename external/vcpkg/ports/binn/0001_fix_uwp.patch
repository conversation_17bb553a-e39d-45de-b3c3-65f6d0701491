diff --git a/src/binn.c b/src/binn.c
index ef32f35..d12d473 100644
--- a/src/binn.c
+++ b/src/binn.c
@@ -142,8 +142,14 @@ BINN_PRIVATE void copy_be64(u64 *pdest, u64 *psource) {
 /***************************************************************************/
 
 #ifndef WIN32
 #define stricmp strcasecmp
 #define strnicmp strncasecmp
+#define sprintf_s(b, n, ...) sprintf(b, __VA_ARGS__)
+#define strcpy_s(b, n, s) strcpy(b, s)
+#else
+#define stricmp _stricmp
+#define strnicmp _strnicmp
+#define strdup _strdup
 #endif
 
 BINN_PRIVATE BOOL IsValidBinnHeader(void *pbuf, int *ptype, int *pcount, int *psize, int *pheadersize);
@@ -1582,6 +1588,7 @@ BINN_PRIVATE BOOL binn_read_pair(int expected_type, void *ptr, int pos, int *pid
   base = p;
   plimit = p + size - 1;
   p += header_size;
+  key = 0;
 
   for (i = 0; i < count; i++) {
     switch (type) {
@@ -3333,7 +3340,7 @@ char * APIENTRY binn_get_str(binn *value) {
 
   if (type_family(value->type) == BINN_FAMILY_INT) {
     if (copy_int_value(value->ptr, &vint, value->type, BINN_INT64) == FALSE) return NULL;
-    sprintf(buf, "%" INT64_FORMAT, vint);
+    sprintf_s(buf, sizeof buf, "%" INT64_FORMAT, vint);
     goto loc_convert_value;
   }
 
@@ -3341,14 +3348,14 @@ char * APIENTRY binn_get_str(binn *value) {
   case BINN_FLOAT:
     value->vdouble = value->vfloat;
   case BINN_DOUBLE:
-    sprintf(buf, "%g", value->vdouble);
+    sprintf_s(buf, sizeof buf, "%g", value->vdouble);
     goto loc_convert_value;
   case BINN_STRING:
     return (char*) value->ptr;
   case BINN_BOOL:
     if (value->vbool)
-      strcpy(buf, "true");
+      strcpy_s(buf, sizeof buf, "true");
     else
-      strcpy(buf, "false");
+      strcpy_s(buf, sizeof buf, "false");
     goto loc_convert_value;
   }
