diff --git a/c/CMakeLists.txt b/c/CMakeLists.txt
index be69103..e92a7fc 100644
--- a/c/CMakeLists.txt
+++ b/c/CMakeLists.txt
@@ -29,9 +29,18 @@ set(CMAKE_CXX_STANDARD_REQUIRED ON)
 
 include(CTest)
 
-add_subdirectory(vendor/fmt EXCLUDE_FROM_ALL)
-set_target_properties(fmt PROPERTIES POSITION_INDEPENDENT_CODE ON)
-add_subdirectory(vendor/nanoarrow)
+
+if(ADBC_WITH_VENDORED_FMT)
+  add_subdirectory(vendor/fmt EXCLUDE_FROM_ALL)
+  set_target_properties(fmt PROPERTIES POSITION_INDEPENDENT_CODE ON)
+else()
+  find_package(fmt REQUIRED)
+endif()
+if(ADBC_WITH_VENDORED_NANOARROW)
+  add_subdirectory(vendor/nanoarrow)
+else()
+  find_package(nanoarrow REQUIRED)
+endif()
 add_subdirectory(driver/common)
 add_subdirectory(driver/framework)
 
diff --git a/c/cmake_modules/DefineOptions.cmake b/c/cmake_modules/DefineOptions.cmake
index 13e6757..6e990a7 100644
--- a/c/cmake_modules/DefineOptions.cmake
+++ b/c/cmake_modules/DefineOptions.cmake
@@ -133,6 +133,9 @@ if("${CMAKE_SOURCE_DIR}" STREQUAL "${CMAKE_CURRENT_SOURCE_DIR}")
 
   define_option(ADBC_GGDB_DEBUG "Pass -ggdb flag to debug builds" ON)
 
+  define_option(ADBC_WITH_VENDORED_FMT "Use vendored copy of fmt" ON)
+  define_option(ADBC_WITH_VENDORED_NANOARROW "Use vendored copy of nanoarrow" ON)
+
   #----------------------------------------------------------------------
   set_option_category("Test and benchmark")
 
diff --git a/c/driver/common/CMakeLists.txt b/c/driver/common/CMakeLists.txt
index 751eda3..5739e4e 100644
--- a/c/driver/common/CMakeLists.txt
+++ b/c/driver/common/CMakeLists.txt
@@ -18,8 +18,12 @@
 add_library(adbc_driver_common STATIC utils.c)
 adbc_configure_target(adbc_driver_common)
 set_target_properties(adbc_driver_common PROPERTIES POSITION_INDEPENDENT_CODE ON)
-target_include_directories(adbc_driver_common PRIVATE "${REPOSITORY_ROOT}/c/include"
-                                                      "${REPOSITORY_ROOT}/c/vendor")
+target_include_directories(adbc_driver_common PRIVATE "${REPOSITORY_ROOT}/c/include")
+if(ADBC_WITH_VENDORED_NANOARROW)
+  target_include_directories(adbc_driver_common PRIVATE "${REPOSITORY_ROOT}/c/vendor")
+else()
+  target_link_libraries(adbc_driver_common PRIVATE nanoarrow::nanoarrow)
+endif()
 
 if(ADBC_BUILD_TESTS)
   add_test_case(driver_common_test
diff --git a/c/driver/postgresql/CMakeLists.txt b/c/driver/postgresql/CMakeLists.txt
index a720696..32af4ac 100644
--- a/c/driver/postgresql/CMakeLists.txt
+++ b/c/driver/postgresql/CMakeLists.txt
@@ -46,13 +46,11 @@ add_arrow_lib(adbc_driver_postgresql
               SHARED_LINK_LIBS
               adbc_driver_common
               adbc_driver_framework
-              nanoarrow
               ${LIBPQ_LINK_LIBRARIES}
               STATIC_LINK_LIBS
               ${LIBPQ_LINK_LIBRARIES}
               adbc_driver_common
               adbc_driver_framework
-              nanoarrow
               ${LIBPQ_STATIC_LIBRARIES})
 
 foreach(LIB_TARGET ${ADBC_LIBRARIES})
diff --git a/c/driver/sqlite/CMakeLists.txt b/c/driver/sqlite/CMakeLists.txt
index d0c45b7..bb0772b 100644
--- a/c/driver/sqlite/CMakeLists.txt
+++ b/c/driver/sqlite/CMakeLists.txt
@@ -52,12 +52,10 @@ add_arrow_lib(adbc_driver_sqlite
               ${SQLite3_LINK_LIBRARIES}
               adbc_driver_common
               adbc_driver_framework
-              nanoarrow
               STATIC_LINK_LIBS
               ${SQLite3_LINK_LIBRARIES}
               adbc_driver_common
               adbc_driver_framework
-              nanoarrow
               ${LIBPQ_STATIC_LIBRARIES})
 
 foreach(LIB_TARGET ${ADBC_LIBRARIES})
