# User <PERSON>
# Date 1545049386 7200
#      Mon Dec 17 10:23:06 2018 -0200
Export tinyXML.

diff --git a/tinyxml.h b/tinyxml.h
--- a/tinyxml.h
+++ b/tinyxml.h
@@ -38,6 +38,8 @@
 #include <string.h>
 #include <assert.h>
 
+#include "tinyxml_export.h"
+
 // Help out windows:
 #if defined( _DEBUG ) && !defined( DEBUG )
 #define DEBUG
@@ -414,7 +416,7 @@
 
 	};
   static Entity entity[ NUM_ENTITY ];
-  static bool condenseWhiteSpace;
+  static TINYXML_EXPORT bool condenseWhiteSpace;
 };
 
 
