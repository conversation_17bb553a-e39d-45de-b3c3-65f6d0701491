vcpkg_from_github(
    OUT_SOURCE_PATH SOURCE_PATH
    REPO Ipotrick/Daxa
    REF ${VERSION}
    SHA512 5843d95ced3ec154d46e31ad5bf822a8470c785415819d235d5b499c3de629c30f5e4afe12b68ef3505ef7287afc68a70f06e255379136fae2e63976310ca3e0
    HEAD_REF master
)

vcpkg_check_features(OUT_FEATURE_OPTIONS FEATURE_OPTIONS
    FEATURES
    utils-imgui WITH_UTILS_IMGUI
    utils-mem WITH_UTILS_MEM
    utils-pipeline-manager-glslang WITH_UTILS_PIPELINE_MANAGER_GLSLANG
    utils-pipeline-manager-slang WITH_UTILS_PIPELINE_MANAGER_SLANG
    utils-pipeline-manager-spirv-validation WITH_UTILS_PIPELINE_MANAGER_SPIRV_VALIDATION
    utils-task-graph WITH_UTILS_TASK_GRAPH
)
set(DAXA_DEFINES "-DDAXA_INSTALL=true")

if(WITH_UTILS_IMGUI)
    list(APPEND DAXA_DEFINES "-DDAXA_ENABLE_UTILS_IMGUI=true")
endif()
if(WITH_UTILS_MEM)
    list(APPEND DAXA_DEFINES "-DDAXA_ENABLE_UTILS_MEM=true")
endif()
if(WITH_UTILS_PIPELINE_MANAGER_GLSLANG)
    list(APPEND DAXA_DEFINES "-DDAXA_ENABLE_UTILS_PIPELINE_MANAGER_GLSLANG=true")
endif()
if(WITH_UTILS_PIPELINE_MANAGER_SLANG)
    list(APPEND DAXA_DEFINES "-DDAXA_ENABLE_UTILS_PIPELINE_MANAGER_SLANG=true")
endif()
if(WITH_UTILS_PIPELINE_MANAGER_SPIRV_VALIDATION)
    list(APPEND DAXA_DEFINES "-DDAXA_ENABLE_UTILS_PIPELINE_MANAGER_SPIRV_VALIDATION=true")
endif()
if(WITH_UTILS_TASK_GRAPH)
    list(APPEND DAXA_DEFINES "-DDAXA_ENABLE_UTILS_TASK_GRAPH=true")
endif()

vcpkg_cmake_configure(
    SOURCE_PATH "${SOURCE_PATH}"
    OPTIONS
        ${DAXA_DEFINES}
        -DCMAKE_REQUIRE_FIND_PACKAGE_X11=ON
        -DCMAKE_REQUIRE_FIND_PACKAGE_WAYLAND=ON
    MAYBE_UNUSED_VARIABLES
        CMAKE_REQUIRE_FIND_PACKAGE_X11
        CMAKE_REQUIRE_FIND_PACKAGE_WAYLAND
)

vcpkg_cmake_install()
vcpkg_cmake_config_fixup()
file(REMOVE_RECURSE "${CURRENT_PACKAGES_DIR}/debug/include")
vcpkg_install_copyright(FILE_LIST "${SOURCE_PATH}/LICENSE")
