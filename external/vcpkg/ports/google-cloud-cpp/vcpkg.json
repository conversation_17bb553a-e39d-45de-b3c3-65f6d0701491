{"name": "google-cloud-cpp", "version": "2.36.0", "description": "C++ Client Libraries for Google Cloud Platform APIs.", "homepage": "https://github.com/googleapis/google-cloud-cpp", "license": "Apache-2.0", "supports": "!uwp", "dependencies": ["abseil", {"name": "openssl", "platform": "!windows"}, {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "default-features": ["big<PERSON>y", "bigtable", "iam", "pubsub", "spanner", "storage"], "features": {"accessapproval": {"description": "Access Approval API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "accesscontextmanager": {"description": "Access Context Manager API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "advisorynotifications": {"description": "Advisory Notifications API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "aiplatform": {"description": "Vertex AI API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "alloydb": {"description": "Alloy DB API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "apigateway": {"description": "API Gateway API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "apigeeconnect": {"description": "Apigee Connect API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "apikeys": {"description": "API Keys API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "appengine": {"description": "App Engine Admin API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "apphub": {"description": "App Hub API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "artifactregistry": {"description": "Artifact Registry API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "asset": {"description": "Cloud Asset API C++ Client Library", "supports": "!windows", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["accesscontextmanager", "grpc-common", "osconfig"]}]}, "assuredworkloads": {"description": "Assured Workloads API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "automl": {"description": "Cloud AutoML API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "backupdr": {"description": "Backup and DR Service API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "baremetalsolution": {"description": "Bare Metal Solution API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "batch": {"description": "Batch API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "beyondcorp": {"description": "BeyondCorp API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "bigquery": {"description": "The Google Cloud BigQuery C++ client library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "bigquerycontrol": {"description": "Cloud BigQuery Control API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common", "rest-common"]}]}, "bigtable": {"description": "The Google Cloud Bigtable C++ client library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "billing": {"description": "Cloud Billing Budget API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "binaryauthorization": {"description": "Binary Authorization API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grafeas", "grpc-common"]}]}, "certificatemanager": {"description": "Certificate Manager API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "channel": {"description": "Cloud Channel API C++ Client Library", "supports": "!windows", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "cloudbuild": {"description": "Cloud Build API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "cloudcontrolspartner": {"description": "Cloud Controls Partner API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "cloudquotas": {"description": "Cloud Quotas API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "commerce": {"description": "Cloud Commerce C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "composer": {"description": "Cloud Composer C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "compute": {"description": "Compute Engine C++ Client Library", "supports": "!windows", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common", "rest-common"]}]}, "confidentialcomputing": {"description": "Confidential Computing API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "config": {"description": "Infrastructure Manager API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "connectors": {"description": "Connectors API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "contactcenterinsights": {"description": "Contact Center AI Insights API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "container": {"description": "Kubernetes Engine API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "containeranalysis": {"description": "Container Analysis API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grafeas", "grpc-common"]}]}, "contentwarehouse": {"description": "Document AI Warehouse API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "datacatalog": {"description": "Google Cloud Data Catalog API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "datafusion": {"description": "Cloud Data Fusion API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "datamigration": {"description": "Database Migration API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "dataplex": {"description": "Cloud Dataplex API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "dataproc": {"description": "Cloud Dataproc API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "datastore": {"description": "Cloud Datastore API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "datastream": {"description": "Datastream API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "deploy": {"description": "Google Cloud Deploy API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "developerconnect": {"description": "Developer Connect API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "dialogflow-cx": {"description": "Cloud Dialogflow CX API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "dialogflow-es": {"description": "Cloud Dialogflow ES API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "discoveryengine": {"description": "Discovery Engine API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "dlp": {"description": "Cloud Data Loss Prevention (DLP) API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "documentai": {"description": "Cloud Document AI API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "domains": {"description": "Cloud Domains API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "edgecontainer": {"description": "Distributed Cloud Edge Container API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "edgenetwork": {"description": "Distributed Cloud Edge Network API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "essentialcontacts": {"description": "Essential Contacts API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "eventarc": {"description": "Eventarc API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "filestore": {"description": "Cloud Filestore API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "functions": {"description": "Cloud Functions API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "gkebackup": {"description": "Backup for GKE API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "gkeconnect": {"description": "GKE Connect Gateway API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common", "rest-common"]}]}, "gkehub": {"description": "GKE Hub C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "gkemulticloud": {"description": "Anthos Multi-Cloud C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "grafeas": {"description": "Protocol buffers implementing the 'Grafeas API' (metadata about software artifacts)", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "grpc-common": {"description": "Dependencies used by all gRPC-based libraries", "dependencies": ["grpc", {"name": "grpc", "host": true}, "protobuf", {"name": "protobuf", "host": true}]}, "iam": {"description": "The Google Cloud IAM C++ client library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "iap": {"description": "Cloud Identity-Aware Proxy API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "ids": {"description": "Cloud IDS API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "kms": {"description": "Cloud Key Management Service (KMS) API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "language": {"description": "Cloud Natural Language API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "logging": {"description": "Google Cloud Logging C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "managedidentities": {"description": "Managed Service for Microsoft Active Directory API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "managedkafka": {"description": "Manage Apache Kafka clusters and resources.", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "memcache": {"description": "Cloud Memorystore for Memcached API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "metastore": {"description": "Dataproc Metastore API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "migrationcenter": {"description": "Migration Center API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "monitoring": {"description": "Cloud Monitoring API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "netapp": {"description": "NetApp API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "networkconnectivity": {"description": "Network Connectivity API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "networkmanagement": {"description": "Network Management API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "networksecurity": {"description": "Secure Web Proxy API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "networkservices": {"description": "Network Services API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "notebooks": {"description": "Notebooks API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "oauth2": {"description": "OAuth2 Access Token Generation Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["rest-common"]}]}, "opentelemetry": {"description": "OpenTelemetry C++ GCP Exporter Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["monitoring", "rest-common", "trace"]}, "opentelemetry-cpp"]}, "optimization": {"description": "Cloud Optimization API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "orgpolicy": {"description": "Organization Policy API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "osconfig": {"description": "Cloud OS Config API C++ Client library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "oslogin": {"description": "Cloud OS Login API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "parallelstore": {"description": "Cloud Parallel Store API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "parametermanager": {"description": "Cloud Parameter Manager API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "policysimulator": {"description": "Policy Simulator API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "policytroubleshooter": {"description": "Policy Troubleshooter API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "privateca": {"description": "Certificate Authority API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "privilegedaccessmanager": {"description": "Privileged Access Manager API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "profiler": {"description": "Cloud Profiler API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "publicca": {"description": "Public Certificate Authority API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "pubsub": {"description": "The Google Cloud Bigtable C++ client library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "rapidmigrationassessment": {"description": "Rapid Migration Assessment C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "recaptchaenterprise": {"description": "reCAPTCHA Enterprise API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "recommender": {"description": "Recommender C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "redis": {"description": "Google Cloud Memorystore for Redis API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "resourcemanager": {"description": "Cloud Resource Manager API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "resourcesettings": {"description": "Resource Settings API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "rest-common": {"description": "Dependencies used by all REST-based libraries", "dependencies": [{"name": "curl", "features": ["ssl"]}, "<PERSON><PERSON><PERSON>-<PERSON><PERSON>"]}, "retail": {"description": "Retail API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "rpc": {"description": "RPC API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "run": {"description": "Cloud Run Admin API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "scheduler": {"description": "Cloud Scheduler API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "secretmanager": {"description": "The Google Cloud Secret Manager C++ client library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "securesourcemanager": {"description": "Secure Source Manager API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "securitycenter": {"description": "Security Command Center API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "securitycentermanagement": {"description": "Security Center Management API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "servicecontrol": {"description": "Service Control API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "servicedirectory": {"description": "Service Directory API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "servicehealth": {"description": "Personalized Service Health API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "servicemanagement": {"description": "Service Management API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "serviceusage": {"description": "Service Usage API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "shell": {"description": "Cloud Shell API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "spanner": {"description": "The Google Cloud Spanner C++ client library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "speech": {"description": "The Google Cloud Speech-to-Text C++ client library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "sql": {"description": "The Google Cloud SQL Admin C++ client library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common", "rest-common"]}]}, "storage": {"description": "The Google Cloud Storage C++ client library", "dependencies": ["crc32c", {"name": "google-cloud-cpp", "default-features": false, "features": ["rest-common"]}]}, "storage-grpc": {"description": "The GCS+gRPC plugin", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common", "opentelemetry", "storage"]}]}, "storagecontrol": {"description": "Cloud Storage Control API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "storageinsights": {"description": "Storage Insights API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "storagetransfer": {"description": "Storage Transfer API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "support": {"description": "Cloud Support API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "talent": {"description": "Cloud Talent Solution API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "tasks": {"description": "The Google Cloud Tasks C++ client library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "telcoautomation": {"description": "Cloud Telco Automation API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "texttospeech": {"description": "Cloud Text-to-Speech API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "timeseriesinsights": {"description": "Timeseries Insights API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "tpu": {"description": "Cloud TPU API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "trace": {"description": "Stackdriver Trace API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "translate": {"description": "Cloud Translation API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "video": {"description": "Video Services C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "videointelligence": {"description": "Cloud Video Intelligence API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "vision": {"description": "Cloud Vision API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "vmmigration": {"description": "VM Migration API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "vmwareengine": {"description": "VMware Engine API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "vpcaccess": {"description": "Serverless VPC Access API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "webrisk": {"description": "Web Risk API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "websecurityscanner": {"description": "Web Security Scanner API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "workflows": {"description": "Workflow Executions API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}, "workstations": {"description": "Workstations API C++ Client Library", "dependencies": [{"name": "google-cloud-cpp", "default-features": false, "features": ["grpc-common"]}]}}}