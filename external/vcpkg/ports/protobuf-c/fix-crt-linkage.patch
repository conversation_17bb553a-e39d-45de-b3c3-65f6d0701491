diff --git a/build-cmake/CMakeLists.txt b/build-cmake/CMakeLists.txt
index 98b51eb..0243b80 100644
--- a/build-cmake/CMakeLists.txt
+++ b/build-cmake/CMakeLists.txt
@@ -74,11 +74,6 @@ if(MSVC)
   set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} /wd4267 /wd4244")
   set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /wd4267 /wd4244")
 
-  # Allow matching protobuf runtime dependency
-  if(NOT BUILD_SHARED_LIBS)
-    set(CMAKE_MSVC_RUNTIME_LIBRARY "MultiThreaded$<$<CONFIG:Debug>:Debug>")
-  endif(NOT BUILD_SHARED_LIBS)
-
 endif()
 
 get_filename_component(MAIN_DIR ${CMAKE_CURRENT_SOURCE_DIR} PATH)
@@ -103,7 +98,7 @@ if(BUILD_PROTOC)
   include_directories(${CMAKE_CURRENT_BINARY_DIR}) # for generated files
 endif()
 
-if(MSVC AND NOT BUILD_SHARED_LIBS)
+if (0)
   # In case we are building static libraries, link also the runtime library
   # statically so that MSVCR*.DLL is not required at runtime.
   # https://msdn.microsoft.com/en-us/library/2kzt1wy3.aspx This is achieved by
