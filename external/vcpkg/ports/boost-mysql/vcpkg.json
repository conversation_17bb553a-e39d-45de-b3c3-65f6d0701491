{"$comment": "Automatically generated by scripts/boost/generate-ports.ps1", "name": "boost-mysql", "version": "1.87.0", "port-version": 1, "description": "Boost mysql module", "homepage": "https://www.boost.org/libs/mysql", "license": "BSL-1.0", "dependencies": [{"name": "boost-asio", "version>=": "1.87.0"}, {"name": "boost-assert", "version>=": "1.87.0"}, {"name": "boost-charconv", "version>=": "1.87.0"}, {"name": "boost-cmake", "version>=": "1.87.0"}, {"name": "boost-compat", "version>=": "1.87.0"}, {"name": "boost-config", "version>=": "1.87.0"}, {"name": "boost-core", "version>=": "1.87.0"}, {"name": "boost-describe", "version>=": "1.87.0"}, {"name": "boost-endian", "version>=": "1.87.0"}, {"name": "boost-headers", "version>=": "1.87.0"}, {"name": "boost-intrusive", "version>=": "1.87.0"}, {"name": "boost-mp11", "version>=": "1.87.0"}, {"name": "boost-optional", "version>=": "1.87.0"}, {"name": "boost-system", "version>=": "1.87.0"}, {"name": "boost-throw-exception", "version>=": "1.87.0"}, {"name": "boost-variant2", "version>=": "1.87.0"}, "openssl"]}