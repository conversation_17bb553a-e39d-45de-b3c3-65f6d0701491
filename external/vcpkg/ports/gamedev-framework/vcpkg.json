{"name": "gamedev-framework", "version-semver": "1.2.0", "maintainers": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "description": "gamedev-framework is a framework to build 2D games in C++17", "homepage": "https://gamedevframework.github.io/", "license": "<PERSON><PERSON><PERSON>", "supports": "!arm & !android", "dependencies": ["boost-algorithm", "boost-container", "boost-filesystem", "boost-heap", "freetype", "pugixml", {"name": "sdl2", "default-features": false, "features": ["x11"], "platform": "linux"}, {"name": "sdl2", "platform": "!linux"}, "stb", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}, "zlib"]}