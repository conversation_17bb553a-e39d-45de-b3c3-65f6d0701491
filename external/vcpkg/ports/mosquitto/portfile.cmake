vcpkg_from_github(
    OUT_SOURCE_PATH SOURCE_PATH
    REPO eclipse/mosquitto
    HEAD_REF master
    REF "v${VERSION}"
    SHA512 92994ec34cebc56dd9aba1a5c3e082117157b42dc5a4c418a9e57a741ad0f2d909226e432082d21b4c9836d2f13fc37d39a1a77f0122a349d1ba6d50974e5190
    PATCHES
        0003-add-find_package-libwebsockets.patch
        0004-support-static-build.patch
        0005-websocket-shared-lib-name.patch
)

string(COMPARE EQUAL "${VCPKG_LIBRARY_LINKAGE}" "static" STATIC_LINKAGE)

vcpkg_cmake_configure(
    SOURCE_PATH "${SOURCE_PATH}"
    OPTIONS
        -DWITH_STATIC_LIBRARIES=${STATIC_LINKAGE}
        -DWITH_SRV=OFF
        -DWITH_WEBSOCKETS=ON
        -DSTATIC_WEBSOCKETS=${STATIC_LINKAGE}
        -DWITH_TLS=ON
        -DWITH_TLS_PSK=ON
        -DWITH_THREADING=ON
        -DDOCUMENTATION=OFF
        -DWITH_PLUGINS=OFF
        -DWITH_CJSON=OFF
        -DWITH_CLIENTS=OFF
        -DWITH_APPS=OFF
        -DWITH_BROKER=OFF
        -DWITH_BUNDLED_DEPS=OFF
    MAYBE_UNUSED_VARIABLES
        WITH_WEBSOCKETS
        STATIC_WEBSOCKETS
)

vcpkg_cmake_install()
vcpkg_copy_pdbs()

vcpkg_fixup_pkgconfig()

file(REMOVE_RECURSE "${CURRENT_PACKAGES_DIR}/debug/include")
file(REMOVE_RECURSE "${CURRENT_PACKAGES_DIR}/debug/share")

if(VCPKG_LIBRARY_LINKAGE STREQUAL "static")
    file(REMOVE_RECURSE "${CURRENT_PACKAGES_DIR}/bin" "${CURRENT_PACKAGES_DIR}/debug/bin")

    file(REMOVE "${CURRENT_PACKAGES_DIR}/lib/${VCPKG_TARGET_SHARED_LIBRARY_PREFIX}mosquitto${VCPKG_TARGET_SHARED_LIBRARY_SUFFIX}")
    file(REMOVE "${CURRENT_PACKAGES_DIR}/lib/${VCPKG_TARGET_SHARED_LIBRARY_PREFIX}mosquittopp${VCPKG_TARGET_SHARED_LIBRARY_SUFFIX}")
    file(REMOVE "${CURRENT_PACKAGES_DIR}/debug/lib/${VCPKG_TARGET_SHARED_LIBRARY_PREFIX}mosquitto${VCPKG_TARGET_SHARED_LIBRARY_SUFFIX}")
    file(REMOVE "${CURRENT_PACKAGES_DIR}/debug/lib/${VCPKG_TARGET_SHARED_LIBRARY_PREFIX}mosquittopp${VCPKG_TARGET_SHARED_LIBRARY_SUFFIX}")

    file(GLOB VERSIONED_LIBRARIES LIST_DIRECTORIES FALSE
        "${CURRENT_PACKAGES_DIR}/lib/${VCPKG_TARGET_SHARED_LIBRARY_PREFIX}mosquitto${VCPKG_TARGET_SHARED_LIBRARY_SUFFIX}.*"
        "${CURRENT_PACKAGES_DIR}/lib/${VCPKG_TARGET_SHARED_LIBRARY_PREFIX}mosquittopp${VCPKG_TARGET_SHARED_LIBRARY_SUFFIX}.*"
        "${CURRENT_PACKAGES_DIR}/debug/lib/${VCPKG_TARGET_SHARED_LIBRARY_PREFIX}mosquitto${VCPKG_TARGET_SHARED_LIBRARY_SUFFIX}.*"
        "${CURRENT_PACKAGES_DIR}/debug/lib/${VCPKG_TARGET_SHARED_LIBRARY_PREFIX}mosquittopp${VCPKG_TARGET_SHARED_LIBRARY_SUFFIX}.*"
    )

    if(NOT "x${VERSIONED_LIBRARIES}x" STREQUAL "xx")
        file(REMOVE ${VERSIONED_LIBRARIES})
    endif()

    if(NOT "x${VCPKG_TARGET_IMPORT_LIBRARY_PREFIX}${VCPKG_TARGET_IMPORT_LIBRARY_SUFFIX}x" STREQUAL "xx")
        file(REMOVE "${CURRENT_PACKAGES_DIR}/lib/${VCPKG_TARGET_IMPORT_LIBRARY_PREFIX}mosquitto${VCPKG_TARGET_IMPORT_LIBRARY_SUFFIX}")
        file(REMOVE "${CURRENT_PACKAGES_DIR}/lib/${VCPKG_TARGET_IMPORT_LIBRARY_PREFIX}mosquittopp${VCPKG_TARGET_IMPORT_LIBRARY_SUFFIX}")
        file(REMOVE "${CURRENT_PACKAGES_DIR}/debug/lib/${VCPKG_TARGET_IMPORT_LIBRARY_PREFIX}mosquitto${VCPKG_TARGET_IMPORT_LIBRARY_SUFFIX}")
        file(REMOVE "${CURRENT_PACKAGES_DIR}/debug/lib/${VCPKG_TARGET_IMPORT_LIBRARY_PREFIX}mosquittopp${VCPKG_TARGET_IMPORT_LIBRARY_SUFFIX}")
    endif()
endif()

configure_file(${CMAKE_CURRENT_LIST_DIR}/vcpkg-cmake-wrapper.cmake ${CURRENT_PACKAGES_DIR}/share/${PORT}/vcpkg-cmake-wrapper.cmake @ONLY)

file(INSTALL "${CMAKE_CURRENT_LIST_DIR}/usage" DESTINATION "${CURRENT_PACKAGES_DIR}/share/${PORT}")
vcpkg_install_copyright(FILE_LIST "${SOURCE_PATH}/LICENSE.txt")
