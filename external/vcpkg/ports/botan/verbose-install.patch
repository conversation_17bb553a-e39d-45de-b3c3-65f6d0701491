diff --git a/src/build-data/makefile.in b/src/build-data/makefile.in
index 1d50a31..64789a5 100644
--- a/src/build-data/makefile.in
+++ b/src/build-data/makefile.in
@@ -61,7 +61,7 @@ distclean:
 	"$(PYTHON_EXE)" "$(SCRIPTS_DIR)/cleanup.py" --build-dir="%{build_dir}" --distclean
 
 install: %{install_targets}
-	"$(PYTHON_EXE)" "$(SCRIPTS_DIR)/install.py" --build-dir="%{build_dir}"
+	"$(PYTHON_EXE)" "$(SCRIPTS_DIR)/install.py" --build-dir="%{build_dir}" --verbose
 
 check: tests
 	"$(PYTHON_EXE)" "$(SCRIPTS_DIR)/check.py" --build-dir="%{build_dir}"
