diff --git a/src/build-data/makefile.in b/src/build-data/makefile.in
index 5102933..0fa5920 100644
--- a/src/build-data/makefile.in
+++ b/src/build-data/makefile.in
@@ -20,7 +20,7 @@ LDFLAGS        = %{ldflags}
 
 EXE_LINK_CMD   = %{exe_link_cmd}
 
-LIB_LINKS_TO        = %{external_link_cmd} %{link_to}
+LIB_LINKS_TO        = %{external_link_cmd} $(ZLIB_LIBS) %{link_to}
 BUILD_DIR_LINK_PATH = %{build_dir_link_path}
 EXE_LINKS_TO        = %{link_to_botan} $(LIB_LINKS_TO) %{extra_libs}
 
diff --git a/src/lib/compression/zlib/info.txt b/src/lib/compression/zlib/info.txt
index 1102bc5..34047fa 100644
--- a/src/lib/compression/zlib/info.txt
+++ b/src/lib/compression/zlib/info.txt
@@ -5,6 +5,4 @@ ZLIB -> 20160412
 load_on vendor
 
 <libs>
-all!windows -> z
-windows -> zlib
 </libs>
