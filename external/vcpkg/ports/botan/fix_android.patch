diff --git a/src/lib/utils/prefetch.cpp b/src/lib/utils/prefetch.cpp
index 40786b3..e0d5239 100644
--- a/src/lib/utils/prefetch.cpp
+++ b/src/lib/utils/prefetch.cpp
@@ -12,7 +12,7 @@
 namespace Botan {
 
 uint64_t prefetch_array_raw(size_t bytes, const void* arrayv) noexcept {
-#if defined(__cpp_lib_hardware_interference_size)
+#if defined(__cpp_lib_hardware_interference_size) && (!defined(BOTAN_TARGET_OS_IS_ANDROID) || defined(NDK_IS_NEWER_THAN_25))
    const size_t cache_line_size = std::hardware_destructive_interference_size;
 #else
    // We arbitrarily use a 64 byte cache line, which is by far the most
