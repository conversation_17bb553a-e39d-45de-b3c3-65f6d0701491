#!/usr/bin/env bash

set -e

declare -a OUT_OPTIONS

INTERPRETER=python3

LAST_SEEN=
COPY_OPTIONS=no
COPY_PREFIX=yes
BUILD_TYPE=RELEASE
for OPTION; do
    case "${OPTION},${COPY_OPTIONS}" in
    */configure.py,no)
        OUT_OPTIONS+=("${OPTION}")
        INTERPRETER="${LAST_SEEN}"
        COPY_OPTIONS=yes
        ;;
    --prefix=*,yes)
        if [ "${COPY_PREFIX}" = "yes" ] ; then
            OUT_OPTIONS+=("${OPTION}")
            COPY_PREFIX=no
        fi
        ;;
    --*=*,yes)
        OUT_OPTIONS+=("${OPTION}")
        ;;
    *=*)
        ;;
    *,yes)
        OUT_OPTIONS+=("${OPTION}")
        if [ "${OPTION}" = "--debug-mode" ] ; then
            BUILD_TYPE=DEBUG
        fi
        ;;
    esac
    LAST_SEEN="${OPTION}"
done

set -x
export CXXFLAGS="$CPPFLAGS $CXXFLAGS"
"${INTERPRETER}" "${OUT_OPTIONS[@]}"

sed -e "1i\\
ZLIB_LIBS = \$(ZLIB_LIBS_${BUILD_TYPE})" -i -- Makefile
