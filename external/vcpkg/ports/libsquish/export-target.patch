diff --git a/CMakeLists.txt b/CMakeLists.txt
index a3ecdde..6aa9e64 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -75,6 +75,8 @@ INCLUDE_DIRECTORIES(${CMAKE_CURRENT_SOURCE_DIR})
 
 ADD_LIBRARY(squish ${SQUISH_SRCS} ${SQUISH_HDRS})
 
+TARGET_INCLUDE_DIRECTORIES(squish PUBLIC $<INSTALL_INTERFACE:include>)
+
 INCLUDE(GenerateExportHeader)
 GENERATE_EXPORT_HEADER(squish
     EXPORT_FILE_NAME ${CMAKE_CURRENT_SOURCE_DIR}/squish_export.h
@@ -120,8 +122,14 @@ INCLUDE(GNUInstallDirs)
 
 INSTALL(
     TARGETS squish
+    EXPORT unofficial-libsquishConfig
     LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
     ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR}
     RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
     PUBLIC_HEADER DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}
     )
+
+INSTALL(EXPORT unofficial-libsquishConfig
+    NAMESPACE unofficial::libsquish::
+    DESTINATION share/unofficial-libsquish
+)
