{"$comment": "Automatically generated by scripts/boost/generate-ports.ps1", "name": "boost-odeint", "version": "1.87.0", "description": "Boost odeint module", "homepage": "https://www.boost.org/libs/numeric/odeint", "license": "BSL-1.0", "dependencies": [{"name": "boost-assert", "version>=": "1.87.0"}, {"name": "boost-cmake", "version>=": "1.87.0"}, {"name": "boost-compute", "version>=": "1.87.0"}, {"name": "boost-config", "version>=": "1.87.0"}, {"name": "boost-core", "version>=": "1.87.0"}, {"name": "boost-fusion", "version>=": "1.87.0"}, {"name": "boost-headers", "version>=": "1.87.0"}, {"name": "boost-iterator", "version>=": "1.87.0"}, {"name": "boost-math", "version>=": "1.87.0"}, {"name": "boost-mpl", "version>=": "1.87.0"}, {"name": "boost-multi-array", "version>=": "1.87.0"}, {"name": "boost-preprocessor", "version>=": "1.87.0"}, {"name": "boost-range", "version>=": "1.87.0"}, {"name": "boost-static-assert", "version>=": "1.87.0"}, {"name": "boost-throw-exception", "version>=": "1.87.0"}, {"name": "boost-type-traits", "version>=": "1.87.0"}, {"name": "boost-ublas", "version>=": "1.87.0"}, {"name": "boost-units", "version>=": "1.87.0"}, {"name": "boost-utility", "version>=": "1.87.0"}], "features": {"mpi": {"description": "Support parallelization with MPI", "dependencies": [{"name": "boost-mpi", "version>=": "1.87.0"}]}}}