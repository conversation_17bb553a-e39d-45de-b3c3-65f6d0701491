diff --git a/configure.ac b/configure.ac
index ca5b7b2..b059402 100644
--- a/configure.ac
+++ b/configure.ac
@@ -870,6 +870,7 @@ case $enable_backend_chert$enable_backend_glass in
   dnl
   dnl Similarly for uuid support.
 
+  if false; then
   dnl Check for zlib.h.
   AC_CHECK_HEADERS([zlib.h], [], [
     AC_MSG_ERROR([zlib.h not found - required for chert and glass (you may need to install the zlib1g-dev or zlib-devel package)])
@@ -879,6 +880,10 @@ case $enable_backend_chert$enable_backend_glass in
   AC_SEARCH_LIBS([zlibVersion], [z zlib zdll], [], [
     AC_MSG_ERROR([zlibVersion() not found in -lz, -lzlib, or -lzdll - required for chert and glass (you may need to install the zlib1g-dev or zlib-devel package)])
     ])
+  fi
+  PKG_CHECK_MODULES([ZLIB], [zlib], [],[AC_MSG_ERROR([zlib library not found])])
+  CFLAGS="$LIBS $ZLIB_CFLAGS"
+  LIBS="$ZLIB_LIBS $LIBS"
 
   dnl Find a way to generate UUIDs.
 
