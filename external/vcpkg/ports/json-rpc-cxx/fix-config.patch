diff --git a/CMakeLists.txt b/CMakeLists.txt
index a362ea4..9c23acf 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -10,8 +10,16 @@ option(CODE_COVERAGE "Enable coverage reporting" OFF)
 include(GNUInstallDirs)

 add_library(json-rpc-cxx INTERFACE)
-target_include_directories(json-rpc-cxx INTERFACE include)
+target_include_directories(json-rpc-cxx INTERFACE
+        $<BUILD_INTERFACE:${CMAKE_CURRENT_LIST_DIR}>
+        $<INSTALL_INTERFACE:include>
+        )
 install(DIRECTORY include/ DESTINATION ${CMAKE_INSTALL_INCLUDEDIR})
+install (TARGETS json-rpc-cxx EXPORT unofficial-json-rpc-cxx-config)
+install (EXPORT unofficial-json-rpc-cxx-config
+        NAMESPACE unofficial::json-rpc-cxx::
+        DESTINATION share/unofficial-json-rpc-cxx
+        )

 add_library(coverage_config INTERFACE)

