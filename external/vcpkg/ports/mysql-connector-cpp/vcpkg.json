{"name": "mysql-connector-cpp", "version": "9.1.0", "port-version": 4, "description": "This is a release of MySQL Connector/C++, the C++ interface for communicating with MySQL servers.", "homepage": "https://github.com/mysql/mysql-connector-cpp", "license": null, "supports": "!android & !uwp", "dependencies": ["lz4", {"name": "mysql-connector-cpp", "host": true, "platform": "!windows | mingw"}, "openssl", "protobuf", {"name": "protobuf", "host": true}, "<PERSON><PERSON><PERSON>", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}, "zlib", "zstd"], "features": {"jdbc": {"description": "Legacy C++ API based on the JDBC4 specification.", "supports": "static", "dependencies": ["libmysql"]}}}