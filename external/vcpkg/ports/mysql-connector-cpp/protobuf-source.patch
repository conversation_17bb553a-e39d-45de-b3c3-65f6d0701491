diff --git a/cdk/protocol/mysqlx/protocol.cc b/cdk/protocol/mysqlx/protocol.cc
index d4589c27..c61deca4 100644
--- a/cdk/protocol/mysqlx/protocol.cc
+++ b/cdk/protocol/mysqlx/protocol.cc
@@ -117,18 +117,15 @@ namespace mysqlx {
   Protobuf log handler initialization.
 */
 
-static void log_handler(LogLevel level, const char* filename, int line, const std::string& message);
 
 #ifdef _WIN32
 BOOL CALLBACK log_handler_init(PINIT_ONCE, PVOID, PVOID*)
 {
-  SetLogHandler(&log_handler);
   return TRUE;
 }
 #else
 static void log_handler_init()
 {
-  SetLogHandler(log_handler);
 }
 #endif
 
@@ -289,37 +286,6 @@ Message* mk_message(Protocol_side side, msg_type_t msg_type)
   situation occurs in Protobuf (such as parsing error etc).
 */
 
-static void log_handler(
-  LogLevel level, const char* /*filename*/, int /*line*/,
-  const std::string& message
-)
-{
-  switch(level)
-  {
-    case LOGLEVEL_FATAL:
-    case LOGLEVEL_ERROR:
-      /*
-        With this code the error description is:
-
-          MMM: Protobuf error (cdk:NNN)
-
-        where MMM is the message and NNN is the protbuf error code.
-
-        TODO: Change description to:
-
-          Protobuf error: MMM (cdk:NNN)
-      */
-      throw_error(cdkerrc::protobuf_error, message);
-
-    case LOGLEVEL_WARNING:
-    case LOGLEVEL_INFO:
-    default:
-    {
-      // just ignore for now
-      // TODO: this could be used for logging in the future
-    }
-  }
-}
 
 /*
   Implementation of protobuf's ZeroCopyOutputStream which stores
