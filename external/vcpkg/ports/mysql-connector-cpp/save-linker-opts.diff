diff --git a/cmake/libutils.cmake b/cmake/libutils.cmake
index 848b7cf..159145f 100644
--- a/cmake/libutils.cmake
+++ b/cmake/libutils.cmake
@@ -236,8 +236,9 @@ function(merge_libraries TARGET)
     #
 
     add_dependencies(${TARGET}-deps save_linker_opts)
+    set(WITH_SAVE_LINKER_OPTS "${LIBUTILS_BIN_DIR}/save_linker_opts" CACHE FILEPATH "")
     set_target_properties(${TARGET}-deps PROPERTIES
-      RULE_LAUNCH_LINK "${LIBUTILS_BIN_DIR}/save_linker_opts ${log_file}.STATIC "
+      RULE_LAUNCH_LINK "${WITH_SAVE_LINKER_OPTS} ${log_file}.STATIC "
     )
 
     # Arrange for ${TARGET}-deps to be built before ${TARGET}
@@ -255,7 +256,7 @@ function(merge_libraries TARGET)
     #
 
     set_target_properties(${TARGET} PROPERTIES
-      RULE_LAUNCH_LINK "${LIBUTILS_BIN_DIR}/save_linker_opts ${log_file}.SHARED "
+      RULE_LAUNCH_LINK "${WITH_SAVE_LINKER_OPTS} ${log_file}.SHARED "
     )
 
   else(NOT MSVC)
