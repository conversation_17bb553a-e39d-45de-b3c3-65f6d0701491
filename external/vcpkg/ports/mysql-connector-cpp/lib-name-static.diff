diff --git a/install_layout.cmake b/install_layout.cmake
index 4a8a511..ee091da 100644
--- a/install_layout.cmake
+++ b/install_layout.cmake
@@ -219,7 +219,7 @@ set(LIB_NAME_BASE "mysqlcppconnx")
 set(LIB_NAME_STATIC "${LIB_NAME_BASE}-static")
 
 if(WIN32 AND STATIC_MSVCRT)
-  set(LIB_NAME_STATIC "${LIB_NAME}-mt")
+  set(LIB_NAME_STATIC "${LIB_NAME_STATIC}-mt")
 endif()
 
 if(BUILD_STATIC)
diff --git a/jdbc/install_layout.cmake b/jdbc/install_layout.cmake
index e9e15a5..a4f7dc0 100644
--- a/jdbc/install_layout.cmake
+++ b/jdbc/install_layout.cmake
@@ -91,7 +91,7 @@ set(LIB_NAME_BASE "mysqlcppconn")
 set(LIB_NAME_STATIC "${LIB_NAME_BASE}-static")
 
 if(WIN32 AND STATIC_MSVCRT)
-  set(LIB_NAME_STATIC "${LIB_NAME}-mt")
+  set(LIB_NAME_STATIC "${LIB_NAME_STATIC}-mt")
 endif()
 
 if(BUILD_STATIC)
