diff --git a/jdbc/extra/otel/CMakeLists.txt b/jdbc/extra/otel/CMakeLists.txt
index bbed9bd..d03e2ef 100644
--- a/jdbc/extra/otel/CMakeLists.txt
+++ b/jdbc/extra/otel/CMakeLists.txt
@@ -9,7 +9,9 @@ if(NOT (WIN32 OR APPLE OR CMAKE_SYSTEM_NAME MATCHES "SunOS"))
   message(STATUS "Adding OTel support")
 
   set(TELEMETRY ON CACHE INTERNAL "Whether connector is built with OTel support")
+endif()
 
+if(TELEMETRY)
   target_include_directories(otel_api INTERFACE
     "${PROJECT_SOURCE_DIR}/extra/otel/${OPENTELEMETRY_CPP_TAG}/api/include"
   )
