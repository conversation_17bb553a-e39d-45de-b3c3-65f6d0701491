diff --git a/CMakeLists.txt b/CMakeLists.txt
index 341ed2d..686b859 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -356,9 +356,11 @@ if(WITH_JDBC)
   # we use a copy of them placed inside the build tree.
 
   target_include_directories(connector-jdbc
-    PUBLIC "${PROJECT_BINARY_DIR}/include/jdbc"
-    PUBLIC "${PROJECT_BINARY_DIR}/include/jdbc/cppconn"
-    PUBLIC "${PROJECT_SOURCE_DIR}/include"
+    PUBLIC
+      "$<BUILD_INTERFACE:${PROJECT_BINARY_DIR}/include/jdbc>"
+      "$<BUILD_INTERFACE:${PROJECT_BINARY_DIR}/include/jdbc/cppconn>"
+      "$<BUILD_INTERFACE:${PROJECT_SOURCE_DIR}/include>"
+      "$<INSTALL_INTERFACE:include>"
   )
 
 endif()
@@ -417,7 +419,8 @@ add_version_info(connector
 )
 
 target_include_directories(connector PUBLIC
-  "${PROJECT_SOURCE_DIR}/include"
+  $<BUILD_INTERFACE:${PROJECT_SOURCE_DIR}/include>
+  $<INSTALL_INTERFACE:include>
   # Note: This is needed when using connector directly from the build tree to
   # find headers generated by the build process.
   $<BUILD_INTERFACE:${PROJECT_BINARY_DIR}/include/mysqlx>
@@ -537,23 +540,18 @@ set_target_properties(connector PROPERTIES
 
 
 install(TARGETS connector
-  CONFIGURATIONS Release RelWithDebInfo
+  EXPORT unofficial-mysql-connector-cpp-targets
   ARCHIVE DESTINATION "${INSTALL_LIB_DIR_STATIC}" COMPONENT XDevAPIDev
-  RUNTIME DESTINATION "${INSTALL_LIB_DIR}" COMPONENT XDevAPIDll
+  RUNTIME DESTINATION "bin"                COMPONENT XDevAPIDll
   LIBRARY DESTINATION "${INSTALL_LIB_DIR}" COMPONENT XDevAPIDll
 )
 
-install(TARGETS connector
-  CONFIGURATIONS Debug
-  ARCHIVE DESTINATION "${INSTALL_LIB_DIR_STATIC_DEBUG}" COMPONENT XDevAPIDev
-  RUNTIME DESTINATION "${INSTALL_LIB_DIR_DEBUG}" COMPONENT XDevAPIDll
-  LIBRARY DESTINATION "${INSTALL_LIB_DIR_DEBUG}" COMPONENT XDevAPIDll
-)
 
 if(MSVC AND NOT BUILD_STATIC)
 
   install(FILES $<TARGET_PDB_FILE:connector>
     CONFIGURATIONS RelWithDebInfo
+                   Release
     DESTINATION "${INSTALL_LIB_DIR}"
     COMPONENT Debuginfo
   )
@@ -667,3 +665,15 @@ endif()
 
 
 show_config_options()
+
+include(CMakePackageConfigHelpers)
+configure_package_config_file(
+  "${CMAKE_SOURCE_DIR}/mysql-connector-cpp-config.cmake.in"
+  "${CMAKE_CURRENT_BINARY_DIR}/unofficial-mysql-connector-cpp-config.cmake"
+  INSTALL_DESTINATION "share/unofficial-mysql-connector-cpp"
+)
+install(FILES "${CMAKE_CURRENT_BINARY_DIR}/unofficial-mysql-connector-cpp-config.cmake" DESTINATION "share/unofficial-mysql-connector-cpp")
+install(EXPORT unofficial-mysql-connector-cpp-targets
+    NAMESPACE unofficial::mysql-connector-cpp::
+    DESTINATION share/unofficial-mysql-connector-cpp
+)
diff --git a/cmake/libutils.cmake b/cmake/libutils.cmake
index 159145f..4d4f7fe 100644
--- a/cmake/libutils.cmake
+++ b/cmake/libutils.cmake
@@ -164,7 +164,11 @@ function(merge_libraries TARGET)
   set_property(SOURCE "${LIBUTILS_SCRIPT_DIR}/empty.cc" PROPERTY LANGUAGE CXX)
 
   add_library(${TARGET} ${TYPE} "${LIBUTILS_SCRIPT_DIR}/empty.cc")
+  if(TYPE STREQUAL "SHARED")
   target_link_libraries(${TARGET} PRIVATE ${ARGN})
+  else() # merged into whole archive
+    add_dependencies(${TARGET} ${ARGN})
+  endif()
 
   #
   # Arrange for marge_archives.cmake script to be executed in a POST_BUILD
diff --git a/jdbc/CMakeLists.txt b/jdbc/CMakeLists.txt
index 60e36e4..7117213 100644
--- a/jdbc/CMakeLists.txt
+++ b/jdbc/CMakeLists.txt
@@ -341,24 +341,19 @@ endif()
 
 
 install(TARGETS connector-jdbc
-  CONFIGURATIONS Release RelWithDebInfo
+  EXPORT unofficial-mysql-connector-cpp-targets
   ARCHIVE DESTINATION "${INSTALL_LIB_DIR_STATIC}" COMPONENT JDBCDev
-  RUNTIME DESTINATION "${INSTALL_LIB_DIR}" COMPONENT JDBCDll
+  RUNTIME DESTINATION "bin"                COMPONENT JDBCDll
   LIBRARY DESTINATION "${INSTALL_LIB_DIR}" COMPONENT JDBCDll
 )
 
-install(TARGETS connector-jdbc
-  CONFIGURATIONS Debug
-  ARCHIVE DESTINATION "${INSTALL_LIB_DIR_STATIC_DEBUG}" COMPONENT JDBCDev
-  RUNTIME DESTINATION "${INSTALL_LIB_DIR_DEBUG}" COMPONENT JDBCDll
-  LIBRARY DESTINATION "${INSTALL_LIB_DIR_DEBUG}" COMPONENT JDBCDll
-)
 
 
 if(MSVC AND NOT BUILD_STATIC)
 
   install(FILES $<TARGET_PDB_FILE:connector-jdbc>
     CONFIGURATIONS RelWithDebInfo
+                   Release
     DESTINATION "${INSTALL_LIB_DIR}"
     COMPONENT Debuginfo
   )
