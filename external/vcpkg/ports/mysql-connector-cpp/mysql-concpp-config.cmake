# Partial reimplementation of official interface
include(CMakeFindDependencyMacro)
find_dependency(unofficial-mysql-connector-cpp)
set(suffix "")
if("@BUILD_STATIC@")
    set(suffix "-static")
endif()
if(NOT TARGET mysql::concpp-xdevapi${suffix})
    add_library(mysql::concpp${suffix} ALIAS unofficial::mysql-connector-cpp::connector)
    add_library(mysql::concpp-xdevapi${suffix} ALIAS unofficial::mysql-connector-cpp::connector)
    if(TARGET unofficial::mysql-connector-cpp::connector-jdbc)
        add_library(mysql::concpp-jdbc${suffix} ALIAS unofficial::mysql-connector-cpp::connector-jdbc)
    endif()
endif()
set(MYSQL_CONCPP_FOUND 1)
set(MYSQL_CONCPP_VERSION "@VERSION@")
set(mysql-concpp_VERSION "@VERSION@")
