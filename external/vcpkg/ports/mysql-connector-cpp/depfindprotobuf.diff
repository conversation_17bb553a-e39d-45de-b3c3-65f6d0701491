diff --git a/cdk/cmake/DepFindProtobuf.cmake b/cdk/cmake/DepFindProtobuf.cmake
index 1fc785e..2ba7e99 100644
--- a/cdk/cmake/DepFindProtobuf.cmake
+++ b/cdk/cmake/DepFindProtobuf.cmake
@@ -44,9 +44,9 @@
 #
 #
 
-if(TARGET ext::protobuf)
+if(COMMAND mysqlx_protobuf_generate_cpp)
   return()
-endif()
+elseif(0)
 
 message(STATUS "Setting up Protobuf.")
 
@@ -66,6 +66,7 @@ add_ext_targets(protobuf
 )
 
 
+endif()
 # Standard PROTOBUF_GENERATE_CPP modified to our usage
 function(mysqlx_protobuf_generate_cpp SRCS HDRS)
   IF(NOT ARGN)
