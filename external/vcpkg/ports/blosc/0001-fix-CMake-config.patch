diff --git a/CMakeLists.txt b/CMakeLists.txt
index 54d4817..07b551a 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -134,7 +134,7 @@ include(GNUInstallDirs)
 
 if(NOT DEACTIVATE_LZ4)
     if(PREFER_EXTERNAL_LZ4)
-        find_package(LZ4)
+        find_package(LZ4 NAMES lz4 CONFIG REQUIRED)
     else()
         message(STATUS "Using LZ4 internal sources.")
     endif()
@@ -144,7 +144,7 @@ if(NOT DEACTIVATE_LZ4)
 endif()
 
 if(NOT DEACTIVATE_SNAPPY)
-    find_package(Snappy)
+    find_package(Snappy CONFIG REQUIRED)
     if(SNAPPY_FOUND)
         message(STATUS "Activating support for SNAPPY.")
         set(HAVE_SNAPPY TRUE)
@@ -157,7 +157,7 @@ if(NOT DEACTIVATE_ZLIB)
     # import the ZLIB_ROOT environment variable to help finding the zlib library
     if(PREFER_EXTERNAL_ZLIB)
         set(ZLIB_ROOT $ENV{ZLIB_ROOT})
-        find_package(ZLIB)
+        find_package(ZLIB REQUIRED)
         if(NOT ZLIB_FOUND )
             message(STATUS "No zlib found.  Using internal sources.")
         endif()
@@ -171,7 +171,7 @@ endif()
 
 if(NOT DEACTIVATE_ZSTD)
     if(PREFER_EXTERNAL_ZSTD)
-        find_package(Zstd)
+        find_package(zstd CONFIG REQUIRED)
     else()
         message(STATUS "Using ZSTD internal sources.")
     endif()
diff --git a/blosc/CMakeLists.txt b/blosc/CMakeLists.txt
index 70f80a2..83d90d8 100644
--- a/blosc/CMakeLists.txt
+++ b/blosc/CMakeLists.txt
@@ -88,7 +88,7 @@ endif(WIN32)
 
 if(NOT DEACTIVATE_LZ4)
     if(LZ4_FOUND)
-        set(LIBS ${LIBS} ${LZ4_LIBRARY})
+        set(LIBS ${LIBS} lz4::lz4)
     else(LZ4_FOUND)
         file(GLOB LZ4_FILES ${LZ4_LOCAL_DIR}/*.c)
         set(SOURCES ${SOURCES} ${LZ4_FILES})
@@ -98,7 +98,7 @@ endif(NOT DEACTIVATE_LZ4)
 
 if(NOT DEACTIVATE_SNAPPY)
     if(SNAPPY_FOUND)
-        set(LIBS ${LIBS} ${SNAPPY_LIBRARY})
+        set(LIBS ${LIBS} Snappy::snappy)
     else(SNAPPY_FOUND)
         file(GLOB SNAPPY_FILES ${SNAPPY_LOCAL_DIR}/*.cc)
         set(SOURCES ${SOURCES} ${SNAPPY_FILES})
@@ -108,7 +108,7 @@ endif(NOT DEACTIVATE_SNAPPY)
 
 if(NOT DEACTIVATE_ZLIB)
     if(ZLIB_FOUND)
-        set(LIBS ${LIBS} ${ZLIB_LIBRARY})
+        set(LIBS ${LIBS} ZLIB::ZLIB)
     else(ZLIB_FOUND)
         file(GLOB ZLIB_FILES ${ZLIB_LOCAL_DIR}/*.c)
         set(SOURCES ${SOURCES} ${ZLIB_FILES})
@@ -136,6 +136,7 @@ if (NOT DEACTIVATE_ZSTD)
         set(ZSTD_FILES ${ZSTD_COMMON_FILES} ${ZSTD_COMPRESS_FILES}
             ${ZSTD_DECOMPRESS_FILES} ${ZSTD_DICT_FILES})
         set(SOURCES ${SOURCES} ${ZSTD_FILES})
+        set(LIBS ${LIBS} $<IF:$<TARGET_EXISTS:zstd::libzstd_shared>,zstd::libzstd_shared,zstd::libzstd_static>)
         source_group("Zstd" FILES ${ZSTD_FILES})
     endif (ZSTD_FOUND)
 endif (NOT DEACTIVATE_ZSTD)
@@ -207,7 +208,7 @@ endif()
 
 if (BUILD_SHARED)
     target_link_libraries(blosc_shared ${LIBS})
-    target_include_directories(blosc_shared PUBLIC ${BLOSC_INCLUDE_DIRS})
+    target_include_directories(blosc_shared PUBLIC $<INSTALL_INTERFACE:include> $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}>)
 endif()
 
 if (BUILD_TESTS)
@@ -222,7 +223,7 @@ if(BUILD_STATIC)
         set_target_properties(blosc_static PROPERTIES PREFIX lib)
     endif()
     target_link_libraries(blosc_static ${LIBS})
-    target_include_directories(blosc_static PUBLIC ${BLOSC_INCLUDE_DIRS})
+    target_include_directories(blosc_static PUBLIC $<INSTALL_INTERFACE:include> $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}>)
 endif(BUILD_STATIC)
 
 # install
@@ -230,12 +231,14 @@ if(BLOSC_INSTALL)
     install(FILES blosc.h blosc-export.h
             DESTINATION ${CMAKE_INSTALL_INCLUDEDIR} COMPONENT DEV)
     if(BUILD_SHARED)
-        install(TARGETS blosc_shared
+        install(TARGETS blosc_shared EXPORT blosc-config
                 LIBRARY COMPONENT LIB
-                ARCHIVE COMPONENT DEV
-                RUNTIME COMPONENT LIB)
+                ARCHIVE COMPONENT LIB
+                RUNTIME COMPONENT BIN)
     endif(BUILD_SHARED)
     if(BUILD_STATIC)
-        install(TARGETS blosc_static COMPONENT DEV)
+        install(TARGETS blosc_static EXPORT blosc-config LIBRARY COMPONENT LIB)
     endif(BUILD_STATIC)
 endif(BLOSC_INSTALL)
+
+install(EXPORT blosc-config DESTINATION share/blosc)
\ No newline at end of file
