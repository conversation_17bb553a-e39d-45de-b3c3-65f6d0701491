diff --git a/CMakeLists.txt b/CMakeLists.txt
index c6a31790..fff0e5c7 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -1,4 +1,4 @@
-cmake_minimum_required (VERSION 3.0)
+cmake_minimum_required (VERSION 3.5)
 
 cmake_policy(SET CMP0054 NEW)
 cmake_policy(SET CMP0048 NEW)
@@ -145,13 +145,13 @@ endif()
 SOURCE_GROUP("Source Files\\Autogenerated" FILES ${ACT_GENERATED_SOURCE})
 
 
-file(GLOB
-    LIBS_INCLUDE
-    LIST_DIRECTORIES true
-    ${CMAKE_CURRENT_SOURCE_DIR}/Libraries/*/Include
-)
-list(FILTER LIBS_INCLUDE EXCLUDE REGEX "zlib|libzip|libressl")
-target_include_directories(${PROJECT_NAME} PRIVATE ${LIBS_INCLUDE})
+#file(GLOB
+#    LIBS_INCLUDE
+#    LIST_DIRECTORIES true
+#    ${CMAKE_CURRENT_SOURCE_DIR}/Libraries/*/Include
+#)
+#list(FILTER LIBS_INCLUDE EXCLUDE REGEX "zlib|libzip|libressl")
+#target_include_directories(${PROJECT_NAME} PRIVATE ${LIBS_INCLUDE})
 
 # allow FASTFLOAT_ALLOWS_LEADING_PLUS
 add_definitions(-DFASTFLOAT_ALLOWS_LEADING_PLUS=1)
@@ -199,22 +199,39 @@ if (USE_INCLUDED_LIBZIP)
     endif()
 
 else()
-    find_package(PkgConfig REQUIRED)
-    pkg_check_modules(LIBZIP REQUIRED libzip)
-    target_link_libraries(${PROJECT_NAME} ${LIBZIP_LIBRARIES})
+#    find_package(PkgConfig REQUIRED)
+#    pkg_check_modules(LIBZIP REQUIRED libzip)
+#    target_link_libraries(${PROJECT_NAME} ${LIBZIP_LIBRARIES})
+    find_package(LIBZIP REQUIRED)
+    target_link_libraries(${PROJECT_NAME} PRIVATE libzip::zip)
 endif()
 
 
 if (USE_INCLUDED_ZLIB)
     target_include_directories(${PROJECT_NAME} PRIVATE ${CMAKE_CURRENT_SOURCE_DIR}/Libraries/zlib/Include)
 else()
-    find_package(PkgConfig REQUIRED)
-    pkg_check_modules(ZLIB REQUIRED zlib)
-    target_link_libraries(${PROJECT_NAME} ${ZLIB_LIBRARIES})
+#    find_package(PkgConfig REQUIRED)
+#    pkg_check_modules(ZLIB REQUIRED zlib)
+#    target_link_libraries(${PROJECT_NAME} ${ZLIB_LIBRARIES})
+    find_package(ZLIB REQUIRED)
+    target_link_libraries(${PROJECT_NAME} PRIVATE ZLIB::ZLIB)
 endif()
 
 
-target_include_directories(${PROJECT_NAME} PRIVATE ${CMAKE_CURRENT_SOURCE_DIR}/submodules/fast_float/include)
+#devendor base64
+# Include the directory for header files
+find_path(CPP_BASE64_INCLUDE_DIRS "cpp-base64/base64.cpp")
+include_directories("${CPP_BASE64_INCLUDE_DIRS}/cpp-base64")
+set(BASE64_SRC
+    ${CPP_BASE64_INCLUDE_DIRS}/cpp-base64/base64.h
+    ${CPP_BASE64_INCLUDE_DIRS}/cpp-base64/base64.cpp)
+message("BASE64_SRC" ${BASE64_SRC})
+# Append BASE64_SRC to the target
+target_sources(${PROJECT_NAME} PRIVATE ${BASE64_SRC})
+
+#devendor FastFloat
+find_package(FastFloat CONFIG REQUIRED)
+target_link_libraries(${PROJECT_NAME} PRIVATE FastFloat::fast_float)
 
 
 set_target_properties(${PROJECT_NAME} PROPERTIES PREFIX "" IMPORT_PREFIX "" )
diff --git a/Include/Common/NMR_StringUtils.h b/Include/Common/NMR_StringUtils.h
index 4cafe3a7..b32bc76a 100644
--- a/Include/Common/NMR_StringUtils.h
+++ b/Include/Common/NMR_StringUtils.h
@@ -37,7 +37,7 @@ and Exception-safe
 #include "Common/NMR_Types.h"
 #include "Common/NMR_Local.h"
 
-#include <fast_float.h>
+#include <fast_float/fast_float.h>
 
 #include <string>
 #include <string.h>
diff --git a/Source/CMakeLists.txt b/Source/CMakeLists.txt
index 27973c84..bc93c89d 100644
--- a/Source/CMakeLists.txt
+++ b/Source/CMakeLists.txt
@@ -12,7 +12,7 @@ if (USE_INCLUDED_ZLIB)
   file(GLOB ZLIB_FILES RELATIVE ${CMAKE_CURRENT_SOURCE_DIR} "Libraries/zlib/Source/*.c")
 endif()
 
-file (GLOB CPPBASE64_FILES RELATIVE ${CMAKE_CURRENT_SOURCE_DIR} "Libraries/cpp-base64/Source/*.cpp")
+#file (GLOB CPPBASE64_FILES RELATIVE ${CMAKE_CURRENT_SOURCE_DIR} "Libraries/cpp-base64/Source/*.cpp")
 
 # sources
 set(SRCS_PLATFORM
diff --git a/cmake/lib3mfConfig.cmake b/cmake/lib3mfConfig.cmake
index f1305b8f..b7755c3e 100644
--- a/cmake/lib3mfConfig.cmake
+++ b/cmake/lib3mfConfig.cmake
@@ -1,4 +1,7 @@
 # lib3mfConfig.cmake
+include(CMakeFindDependencyMacro)
+find_dependency(libzip)
+find_dependency(ZLIB)
 
 if(VCPKG_TOOLCHAIN)
     message("Lib3MF - VCPKG Tool Chain")
