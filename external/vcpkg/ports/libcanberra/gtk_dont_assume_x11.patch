From c0620e432650e81062c1967cc669829dbd29b310 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Fri, 9 Nov 2012 16:16:40 +0000
Subject: gtk: Don't assume all GdkDisplays are GdkX11Displays:
 broadway/wayland

---
 src/canberra-gtk-module.c | 15 +++++++++++++++
 src/canberra-gtk.c        |  5 +++++
 2 files changed, 20 insertions(+)

diff --git a/src/canberra-gtk-module.c b/src/canberra-gtk-module.c
index 67791f0..c1532ab 100644
--- a/src/canberra-gtk-module.c
+++ b/src/canberra-gtk-module.c
@@ -307,6 +307,11 @@ static gint window_get_desktop(GdkDisplay *d, GdkWindow *w) {
         guchar *data = NULL;
         gint ret = -1;
 
+#ifdef GDK_IS_X11_DISPLAY
+        if (!GDK_IS_X11_DISPLAY(d))
+                return 0;
+#endif
+
         if (XGetWindowProperty(GDK_DISPLAY_XDISPLAY(d), GDK_WINDOW_XID(w),
                                gdk_x11_get_xatom_by_name_for_display(d, "_NET_WM_DESKTOP"),
                                0, G_MAXLONG, False, XA_CARDINAL, &type_return,
@@ -335,6 +340,11 @@ static gint display_get_desktop(GdkDisplay *d) {
         guchar *data = NULL;
         gint ret = -1;
 
+#ifdef GDK_IS_X11_DISPLAY
+        if (!GDK_IS_X11_DISPLAY(d))
+                return 0;
+#endif
+
         if (XGetWindowProperty(GDK_DISPLAY_XDISPLAY(d), DefaultRootWindow(GDK_DISPLAY_XDISPLAY(d)),
                                gdk_x11_get_xatom_by_name_for_display(d, "_NET_CURRENT_DESKTOP"),
                                0, G_MAXLONG, False, XA_CARDINAL, &type_return,
@@ -365,6 +375,11 @@ static gboolean window_is_xembed(GdkDisplay *d, GdkWindow *w) {
         gboolean ret = FALSE;
         Atom xembed;
 
+#ifdef GDK_IS_X11_DISPLAY
+        if (!GDK_IS_X11_DISPLAY(d))
+                return FALSE;
+#endif
+
         /* Gnome Panel applets are XEMBED windows. We need to make sure we
          * ignore them */
 
diff --git a/src/canberra-gtk.c b/src/canberra-gtk.c
index 34446f5..08cb668 100644
--- a/src/canberra-gtk.c
+++ b/src/canberra-gtk.c
@@ -185,6 +185,11 @@ static gint window_get_desktop(GdkDisplay *d, GdkWindow *w) {
         guchar *data = NULL;
         gint ret = -1;
 
+#ifdef GDK_IS_X11_DISPLAY
+        if (!GDK_IS_X11_DISPLAY(d))
+                return 0;
+#endif
+
         if (XGetWindowProperty(GDK_DISPLAY_XDISPLAY(d), GDK_WINDOW_XID(w),
                                gdk_x11_get_xatom_by_name_for_display(d, "_NET_WM_DESKTOP"),
                                0, G_MAXLONG, False, XA_CARDINAL, &type_return,
-- 
cgit v0.12

