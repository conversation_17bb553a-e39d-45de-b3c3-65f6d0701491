Description: Fix FTBFS with binutils-gold
Author: <PERSON> <<EMAIL>>
Bug-Debian: http://bugs.debian.org/cgi-bin/bugreport.cgi?bug=555081

--- a/src/Makefile.am
+++ b/src/Makefile.am
@@ -341,6 +341,7 @@ libcanberra_gtk3_la_CFLAGS = \
 	$(GTK3_CFLAGS)
 libcanberra_gtk3_la_LIBADD = \
 	$(GTK3_LIBS) \
+	-lX11 \
 	libcanberra.la
 libcanberra_gtk3_la_LDFLAGS = \
 	-export-dynamic -version-info $(LIBCANBERRA_GTK_VERSION_INFO)
@@ -351,6 +352,8 @@ libcanberra_gtk3_module_la_CFLAGS = \
 	$(GTK3_CFLAGS)
 libcanberra_gtk3_module_la_LIBADD = \
 	$(GTK3_LIBS) \
+	-lX11 \
+	-lgmodule-2.0 \
 	libcanberra.la \
 	libcanberra-gtk3.la
 libcanberra_gtk3_module_la_LDFLAGS = \
@@ -379,6 +382,7 @@ libcanberra_gtk_la_CFLAGS = \
 	$(GTK_CFLAGS)
 libcanberra_gtk_la_LIBADD = \
 	$(GTK_LIBS) \
+	-lX11 \
 	libcanberra.la
 libcanberra_gtk_la_LDFLAGS = \
 	-export-dynamic -version-info $(LIBCANBERRA_GTK_VERSION_INFO)
@@ -389,6 +393,8 @@ libcanberra_gtk_module_la_CFLAGS = \
 	$(GTK_CFLAGS)
 libcanberra_gtk_module_la_LIBADD = \
 	$(GTK_LIBS) \
+	-lX11 \
+	-lgmodule-2.0 \
 	libcanberra.la \
 	libcanberra-gtk.la
 libcanberra_gtk_module_la_LDFLAGS = \
