diff --git a/CMakeLists.txt b/CMakeLists.txt
index 95df99eae0..7e969cfbb8 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -704,7 +704,7 @@ IF(MSVC)
     TARGET_COMPILE_DEFINITIONS(operator-utils PRIVATE "restrict=")
     TARGET_COMPILE_DEFINITIONS(XNNPACK PRIVATE "restrict=")
   ENDIF()
-
+  if(0)
   TARGET_COMPILE_OPTIONS(microkernels-all PRIVATE "$<$<NOT:$<CONFIG:Debug>>:/O2>")
   TARGET_COMPILE_OPTIONS(microkernels-prod PRIVATE "$<$<NOT:$<CONFIG:Debug>>:/O2>")
   TARGET_COMPILE_OPTIONS(hardware-config PRIVATE "$<$<NOT:$<CONFIG:Debug>>:/O2>")
@@ -723,6 +723,7 @@ IF(MSVC)
     TARGET_COMPILE_OPTIONS(operators PRIVATE "$<$<NOT:$<CONFIG:Debug>>:/O1>")
     TARGET_COMPILE_OPTIONS(XNNPACK PRIVATE "$<$<NOT:$<CONFIG:Debug>>:/O1>")
   ENDIF()
+  endif()
 ELSE()
   TARGET_COMPILE_OPTIONS(microkernels-all PRIVATE "$<$<NOT:$<CONFIG:Debug>>:-O2>")
   TARGET_COMPILE_OPTIONS(microkernels-prod PRIVATE "$<$<NOT:$<CONFIG:Debug>>:-O2>")
@@ -797,8 +798,8 @@ IF(NOT TARGET clog)
     # We build static version of clog but a dynamic library may indirectly depend on it
     SET_PROPERTY(TARGET clog PROPERTY POSITION_INDEPENDENT_CODE ON)
   ELSE()
-    ADD_LIBRARY(clog STATIC IMPORTED)
-    FIND_LIBRARY(CLOG_LIBRARY clog)
+    ADD_LIBRARY(clog UNKNOWN IMPORTED)
+    FIND_LIBRARY(CLOG_LIBRARY NAMES clog REQUIRED)
     IF(NOT CLOG_LIBRARY)
       MESSAGE(FATAL_ERROR "Cannot find clog")
     ENDIF()
@@ -817,8 +818,8 @@ IF(NOT TARGET cpuinfo)
       "${CPUINFO_SOURCE_DIR}"
       "${CMAKE_BINARY_DIR}/cpuinfo")
   ELSE()
-    ADD_LIBRARY(cpuinfo SHARED IMPORTED)
-    FIND_LIBRARY(CPUINFO_LIBRARY cpuinfo)
+    ADD_LIBRARY(cpuinfo UNKNOWN IMPORTED)
+    FIND_LIBRARY(CPUINFO_LIBRARY NAMES cpuinfo REQUIRED)
     IF(NOT CPUINFO_LIBRARY)
       MESSAGE(FATAL_ERROR "Cannot find cpuinfo")
     ENDIF()
@@ -841,13 +842,15 @@ IF(NOT TARGET pthreadpool)
       "${PTHREADPOOL_SOURCE_DIR}"
       "${CMAKE_BINARY_DIR}/pthreadpool")
   ELSE()
-    ADD_LIBRARY(pthreadpool SHARED IMPORTED)
-    FIND_LIBRARY(PTHREADPOOL_LIBRARY pthreadpool)
+    ADD_LIBRARY(pthreadpool UNKNOWN IMPORTED)
+    FIND_LIBRARY(PTHREADPOOL_LIBRARY NAMES pthreadpool REQUIRED)
+    find_path(PTHREADPOOL_INCLUDE NAMES pthreadpool.h REQUIRED PATH_SUFFIXES include)
+    target_include_directories(pthreadpool INTERFACE "${PTHREADPOOL_INCLUDE}")
     IF(NOT PTHREADPOOL_LIBRARY)
       MESSAGE(FATAL_ERROR "Cannot find pthreadpool")
     ENDIF()
     SET_PROPERTY(TARGET pthreadpool PROPERTY IMPORTED_LOCATION "${PTHREADPOOL_LIBRARY}")
-    SET_PROPERTY(TARGET pthreadpool PROPERTY IMPORTED_IMPLIB "${PTHREADPOOL_LIBRARY}")
+    #SET_PROPERTY(TARGET pthreadpool PROPERTY IMPORTED_IMPLIB "${PTHREADPOOL_LIBRARY}")
   ENDIF()
 ENDIF()
 TARGET_LINK_LIBRARIES(microkernels-all PRIVATE pthreadpool)
@@ -881,12 +884,12 @@ IF(NOT TARGET fxdiv)
       "${FXDIV_SOURCE_DIR}"
       "${CMAKE_BINARY_DIR}/FXdiv")
   ELSE()
-    FIND_FILE(FXDIV_HDR fxdiv.h PATH_SUFFIXES include)
+    FIND_PATH(FXDIV_HDR fxdiv.h PATH_SUFFIXES include)
     IF(NOT FXDIV_HDR)
       MESSAGE(FATAL_ERROR "Cannot find fxdiv")
     ENDIF()
-    ADD_LIBRARY(fxdiv STATIC "${FXDIV_HDR}")
-    SET_PROPERTY(TARGET fxdiv PROPERTY LINKER_LANGUAGE C)
+    ADD_LIBRARY(fxdiv INTERFACE IMPORTED)
+    target_include_directories(fxdiv INTERFACE "${FXDIV_HDR}")
   ENDIF()
 ENDIF()
 TARGET_LINK_LIBRARIES(microkernels-all PRIVATE fxdiv)
@@ -905,12 +908,12 @@ IF(NOT TARGET fp16)
       "${FP16_SOURCE_DIR}"
       "${CMAKE_BINARY_DIR}/FP16")
   ELSE()
-    FIND_FILE(FP16_HDR fp16.h PATH_SUFFIXES include)
+    FIND_PATH(FP16_HDR fp16.h PATH_SUFFIXES include REQUIRED)
     IF(NOT FP16_HDR)
       MESSAGE(FATAL_ERROR "Cannot find fp16")
     ENDIF()
-    ADD_LIBRARY(fp16 STATIC "${FP16_HDR}")
-    SET_PROPERTY(TARGET fp16 PROPERTY LINKER_LANGUAGE C)
+    ADD_LIBRARY(fp16 INTERFACE IMPORTED)
+    target_include_directories(fp16 INTERFACE "${FP16_HDR}")
   ENDIF()
 ENDIF()
 TARGET_LINK_LIBRARIES(microkernels-all PRIVATE fp16)
