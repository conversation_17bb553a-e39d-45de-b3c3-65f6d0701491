{"$comment": "Automatically generated by scripts/boost/generate-ports.ps1", "name": "boost-geometry", "version": "1.87.0", "description": "Boost geometry module", "homepage": "https://www.boost.org/libs/geometry", "license": "BSL-1.0", "dependencies": [{"name": "boost-algorithm", "version>=": "1.87.0"}, {"name": "boost-any", "version>=": "1.87.0"}, {"name": "boost-array", "version>=": "1.87.0"}, {"name": "boost-assert", "version>=": "1.87.0"}, {"name": "boost-cmake", "version>=": "1.87.0"}, {"name": "boost-concept-check", "version>=": "1.87.0"}, {"name": "boost-config", "version>=": "1.87.0"}, {"name": "boost-container", "version>=": "1.87.0"}, {"name": "boost-core", "version>=": "1.87.0"}, {"name": "boost-crc", "version>=": "1.87.0"}, {"name": "boost-function-types", "version>=": "1.87.0"}, {"name": "boost-fusion", "version>=": "1.87.0"}, {"name": "boost-headers", "version>=": "1.87.0"}, {"name": "boost-integer", "version>=": "1.87.0"}, {"name": "boost-iterator", "version>=": "1.87.0"}, {"name": "boost-lexical-cast", "version>=": "1.87.0"}, {"name": "boost-math", "version>=": "1.87.0"}, {"name": "boost-mpl", "version>=": "1.87.0"}, {"name": "boost-multiprecision", "version>=": "1.87.0"}, {"name": "boost-numeric-conversion", "version>=": "1.87.0"}, {"name": "boost-polygon", "version>=": "1.87.0"}, {"name": "boost-program-options", "version>=": "1.87.0"}, {"name": "boost-qvm", "version>=": "1.87.0"}, {"name": "boost-range", "version>=": "1.87.0"}, {"name": "boost-rational", "version>=": "1.87.0"}, {"name": "boost-serialization", "version>=": "1.87.0"}, {"name": "boost-static-assert", "version>=": "1.87.0"}, {"name": "boost-thread", "version>=": "1.87.0"}, {"name": "boost-throw-exception", "version>=": "1.87.0"}, {"name": "boost-tokenizer", "version>=": "1.87.0"}, {"name": "boost-tuple", "version>=": "1.87.0"}, {"name": "boost-type-traits", "version>=": "1.87.0"}, {"name": "boost-variant", "version>=": "1.87.0"}, {"name": "boost-variant2", "version>=": "1.87.0"}]}