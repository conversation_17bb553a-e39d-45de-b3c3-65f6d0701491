diff --git a/CMakeLists.txt b/CMakeLists.txt
index b501f08..022864f 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -855,11 +847,6 @@ if (ENABLE_PACKAGE_CONFIG)
 			${CMAKE_CURRENT_BINARY_DIR}/SndFileConfigVersion.cmake
 		DESTINATION	${CMAKE_INSTALL_PACKAGEDIR}
 		)
-
-		if (NOT BUILD_SHARED_LIBS AND FIND_MODULES_INSTALL_LIST)
-			file(COPY ${FIND_MODULES_INSTALL_LIST} DESTINATION ${CMAKE_CURRENT_BINARY_DIR})
-			install(FILES ${FIND_MODULES_INSTALL_LIST} DESTINATION ${CMAKE_INSTALL_PACKAGEDIR})
-		endif ()
 else ()
 
 	install (TARGETS sndfile ${sdnfile_PROGRAMS}
