diff --git a/CMakeLists.txt b/CMakeLists.txt
index 43bc5cd..7d626fb 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -137,7 +137,7 @@ if (VK_BOOTSTRAP_INSTALL)
                 message(FATAL_ERROR "Unable to locate required dependency Vulkan::Headers!")
             endif()
         endif()
-        include(@PACKAGE_VK_BOOTSTRAP_EXPORT_TARGETS@)
+        include("${CMAKE_CURRENT_LIST_DIR}/vk-bootstrap-targets.cmake")
     ]=])
 
     configure_package_config_file(
