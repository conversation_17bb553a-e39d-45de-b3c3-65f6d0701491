{"name": "qtwebview", "version": "6.8.3", "description": "Qt WebView provides a way to display web content in a QML application without necessarily including a full web browser stack by using native APIs where it makes sense.", "homepage": "https://www.qt.io/", "license": null, "dependencies": [{"name": "qtbase", "default-features": false, "features": ["gui"]}, {"name": "qtdeclarative", "default-features": false}], "features": {"webengine": {"description": "Build with Webengine", "supports": "!static", "dependencies": [{"name": "qtwebengine", "default-features": false}]}}}