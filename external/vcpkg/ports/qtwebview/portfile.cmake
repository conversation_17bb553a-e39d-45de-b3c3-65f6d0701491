set(SCRIPT_PATH "${CURRENT_INSTALLED_DIR}/share/qtbase")
include("${SCRIPT_PATH}/qt_install_submodule.cmake")

set(${PORT}_PATCHES)


vcpkg_check_features(OUT_FEATURE_OPTIONS FEATURE_OPTIONS
FEATURES
    "webengine"     CMAKE_REQUIRE_FIND_PACKAGE_WebEngineCore
INVERTED_FEATURES
    "webengine"     CMAKE_DISABLE_FIND_PACKAGE_WebEngineCore
)

qt_install_submodule(PATCHES    ${${PORT}_PATCHES}
                     CONFIGURE_OPTIONS ${FEATURE_OPTIONS}
                     CONFIGURE_OPTIONS_MAYBE_UNUSED
                        CMAKE_REQUIRE_FIND_PACKAGE_WebEngineCore
                    )
