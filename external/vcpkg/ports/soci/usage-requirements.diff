diff --git a/cmake/SociBackend.cmake b/cmake/SociBackend.cmake
index 5d4ef0d..279cb75 100644
--- a/cmake/SociBackend.cmake
+++ b/cmake/SociBackend.cmake
@@ -159,8 +159,13 @@ macro(soci_backend NAME)
           ${THIS_BACKEND_HEADERS})
 
         target_link_libraries(${THIS_BACKEND_TARGET}
+          PUBLIC
           ${SOCI_CORE_TARGET}
           ${THIS_BACKEND_DEPENDS_LIBRARIES})
+        target_include_directories(${THIS_BACKEND_TARGET}
+          PUBLIC
+          ${THIS_BACKEND_DEPENDS_INCLUDE_DIRS}
+        )
 
         if(WIN32)
           set_target_properties(${THIS_BACKEND_TARGET}
@@ -197,8 +202,14 @@ macro(soci_backend NAME)
 
         # Still need to link the libraries for tests to work
         target_link_libraries (${THIS_BACKEND_TARGET_STATIC}
+          PUBLIC
+          ${SOCI_CORE_TARGET}_static
           ${THIS_BACKEND_DEPENDS_LIBRARIES}
         )
+        target_include_directories(${THIS_BACKEND_TARGET_STATIC}
+          PUBLIC
+          ${THIS_BACKEND_DEPENDS_INCLUDE_DIRS}
+        )
 
         set_target_properties(${THIS_BACKEND_TARGET_STATIC}
           PROPERTIES
