{"name": "soci", "version": "4.0.3", "port-version": 3, "description": "SOCI - The C++ Database Access Library", "homepage": "https://soci.sourceforge.net/", "license": "BSL-1.0", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"boost": {"description": "Integration with Boost", "dependencies": ["boost-date-time", "boost-fusion", "boost-optional", "boost-preprocessor", "boost-tuple"]}, "empty": {"description": "Build the backend skeleton for new backends development"}, "mysql": {"description": "Build mysql backend", "dependencies": ["libmysql"]}, "odbc": {"description": "Build odbc backend", "supports": "!uwp", "dependencies": [{"name": "unixodbc", "platform": "!windows"}]}, "postgresql": {"description": "Build postgresql backend", "dependencies": [{"name": "libpq", "default-features": false}]}, "sqlite3": {"description": "Build sqlite3 backend", "dependencies": [{"name": "sqlite3", "default-features": false}]}}}