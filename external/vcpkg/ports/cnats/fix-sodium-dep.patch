diff --git a/CMakeLists.txt b/CMakeLists.txt
index 305198f..0b559b8 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -110,6 +110,11 @@ if(NATS_BUILD_STREAMING)
 endif(NATS_BUILD_STREAMING)
 
 if(NATS_BUILD_USE_SODIUM)
+  find_package(libsodium NAMES unofficial-sodium CONFIG REQUIRED)
+  set(NATS_SODIUM_INCLUDE_DIRS "")
+  set(NATS_SODIUM_LIBRARIES unofficial-sodium::sodium)
+  add_definitions(-DNATS_USE_LIBSODIUM)
+elseif(0)
   IF(DEFINED ENV{NATS_SODIUM_DIR})
     SET(NATS_SODIUM_DIR "$ENV{NATS_SODIUM_DIR}")
   ENDIF()
diff --git a/src/CMakeLists.txt b/src/CMakeLists.txt
index 971ddf0..65fee5a 100644
--- a/src/CMakeLists.txt
+++ b/src/CMakeLists.txt
@@ -65,7 +65,7 @@ if(NATS_BUILD_LIB_SHARED)
   install(TARGETS nats EXPORT cnats-targets DESTINATION ${NATS_LIBDIR})
   install(EXPORT cnats-targets
         NAMESPACE cnats::
-        FILE cnats-config.cmake
+        FILE cnats-targets.cmake
         DESTINATION ${NATS_LIBDIR}/cmake/cnats)
   install(FILES "${PROJECT_BINARY_DIR}/cnats-config-version.cmake"
         DESTINATION ${NATS_LIBDIR}/cmake/cnats)
@@ -79,12 +79,16 @@ if(NATS_BUILD_LIB_STATIC)
   install(TARGETS nats_static EXPORT cnats-targets ARCHIVE DESTINATION ${NATS_LIBDIR})
   install(EXPORT cnats-targets
         NAMESPACE cnats::
-        FILE cnats-config.cmake
+        FILE cnats-targets.cmake
         DESTINATION ${NATS_LIBDIR}/cmake/cnats)
   install(FILES "${PROJECT_BINARY_DIR}/cnats-config-version.cmake"
         DESTINATION ${NATS_LIBDIR}/cmake/cnats)
 endif(NATS_BUILD_LIB_STATIC)
 
+include(CMakePackageConfigHelpers)
+configure_package_config_file(${CMAKE_CURRENT_SOURCE_DIR}/Config.cmake.in "${CMAKE_CURRENT_BINARY_DIR}/cnats-config.cmake" INSTALL_DESTINATION ${NATS_LIBDIR}/cmake/cnats)
+install(FILES ${CMAKE_CURRENT_BINARY_DIR}/cnats-config.cmake DESTINATION ${NATS_LIBDIR}/cmake/cnats)
+
 install(FILES deprnats.h DESTINATION ${NATS_INCLUDE_DIR} RENAME nats.h)
 install(FILES nats.h status.h version.h DESTINATION ${NATS_INCLUDE_DIR}/nats)
 install(FILES adapters/libevent.h adapters/libuv.h DESTINATION ${NATS_INCLUDE_DIR}/nats/adapters)
diff --git a/src/Config.cmake.in b/src/Config.cmake.in
new file mode 100644
index 0000000..aa1d6bf
--- /dev/null
+++ b/src/Config.cmake.in
@@ -0,0 +1,8 @@
+@PACKAGE_INIT@
+
+include(CMakeFindDependencyMacro)
+if("@NATS_BUILD_USE_SODIUM@")
+  find_dependency(unofficial-sodium)
+endif()
+
+include ( "${CMAKE_CURRENT_LIST_DIR}/cnats-targets.cmake" )
