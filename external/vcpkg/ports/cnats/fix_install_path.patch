diff --git a/src/CMakeLists.txt b/src/CMakeLists.txt
index 65fee5a..3731b86 100644
--- a/src/CMakeLists.txt
+++ b/src/CMakeLists.txt
@@ -62,7 +62,10 @@ if(NATS_BUILD_LIB_SHARED)
   target_include_directories(nats PUBLIC
         $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}>
         $<INSTALL_INTERFACE:include>)
-  install(TARGETS nats EXPORT cnats-targets DESTINATION ${NATS_LIBDIR})
+  install(TARGETS nats EXPORT cnats-targets
+          ARCHIVE DESTINATION lib
+          LIBRARY DESTINATION lib
+          RUNTIME DESTINATION bin)
   install(EXPORT cnats-targets
         NAMESPACE cnats::
         FILE cnats-targets.cmake
@@ -76,7 +79,10 @@ if(NATS_BUILD_LIB_STATIC)
   target_include_directories(nats_static PUBLIC
         $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}>
         $<INSTALL_INTERFACE:include>)
-  install(TARGETS nats_static EXPORT cnats-targets ARCHIVE DESTINATION ${NATS_LIBDIR})
+  install(TARGETS nats_static EXPORT cnats-targets
+          ARCHIVE DESTINATION lib
+          LIBRARY DESTINATION lib
+          RUNTIME DESTINATION bin)
   install(EXPORT cnats-targets
         NAMESPACE cnats::
         FILE cnats-targets.cmake
