{"name": "mpi", "version": "1", "port-version": 5, "description": "Message Passing Interface (MPI) is a standardized and portable message-passing standard designed by a group of researchers from academia and industry to function on a wide variety of parallel computing architectures. The standard defines the syntax and semantics of a core of library routines useful to a wide range of users writing portable message-passing programs in C, C++, and Fortran. There are several well-tested and efficient implementations of MPI, many of which are open-source or in the public domain.", "license": null, "supports": "!uwp", "dependencies": [{"name": "msmpi", "platform": "windows"}, {"name": "openmpi", "platform": "!windows"}]}