{"name": "json-schema-validator", "version": "2.3.0", "port-version": 2, "description": "C++ library for validating JSON documents based on a JSON Schema. This validator is based on the nlohmann-json library.", "homepage": "https://github.com/pboettch/json-schema-validator", "license": "MIT", "dependencies": [{"name": "<PERSON><PERSON><PERSON>-<PERSON><PERSON>", "default-features": false}, {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}]}