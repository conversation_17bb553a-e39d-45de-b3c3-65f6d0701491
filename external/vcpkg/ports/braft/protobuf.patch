diff --git a/CMakeLists.txt b/CMakeLists.txt
index 28c1026..65af3ae 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -22,7 +22,7 @@ endif()
 set(CMAKE_MODULE_PATH ${PROJECT_SOURCE_DIR}/cmake)
 
 include(FindThreads)
-include(FindProtobuf)
+find_package(Protobuf CONFIG REQUIRED)
 
 if(CMAKE_CXX_COMPILER_ID STREQUAL "GNU")
     # require at least gcc 4.8
@@ -69,7 +69,8 @@ if ((NOT BRPC_INCLUDE_PATH) OR (NOT BRPC_LIB))
     message(FATAL_ERROR "Fail to find brpc")
 endif()
 
-if (NOT PROTOBUF_PROTOC_EXECUTABLE)
+set(PROTOBUF_LIBRARY protobuf::libprotobuf)
+if (0)
     get_filename_component(PROTO_LIB_DIR ${PROTOBUF_LIBRARY} DIRECTORY)
     set (PROTOBUF_PROTOC_EXECUTABLE "${PROTO_LIB_DIR}/../bin/protoc")
 endif()
