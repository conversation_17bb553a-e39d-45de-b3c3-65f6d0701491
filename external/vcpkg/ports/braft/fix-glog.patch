diff --git a/CMakeLists.txt b/CMakeLists.txt
index 090a02e..fbe0ac3 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -55,12 +55,8 @@ if ((NOT GFLAGS_INCLUDE_PATH) OR (NOT GFLAGS_LIB))
 endif()
 
 if(BRPC_WITH_GLOG)
-    find_path(GLOG_INCLUDE_PATH NAMES glog/logging.h)
-    find_library(GLOG_LIB NAMES glog)
-    if((NOT GLOG_INCLUDE_PATH) OR (NOT GLOG_LIB))
-        message(FATAL_ERROR "Fail to find glog")
-    endif()
-    include_directories(${GLOG_INCLUDE_PATH})
+    find_package(glog CONFIG REQUIRED)
+    set(GLOG_LIB glog::glog)
 endif()
 
 if(LEVELDB_WITH_SNAPPY)
diff --git a/src/CMakeLists.txt b/src/CMakeLists.txt
index 70d73df..a3d3046 100644
--- a/src/CMakeLists.txt
+++ b/src/CMakeLists.txt
@@ -11,6 +11,7 @@ include_directories(${CMAKE_SOURCE_DIR}/src)
 
 add_library(OBJ_LIB OBJECT ${SOURCES})
 
+target_link_libraries(OBJ_LIB PUBLIC ${DYNAMIC_LIB})
 set_property(TARGET ${OBJ_LIB} PROPERTY POSITION_INDEPENDENT_CODE 1)
 if (BUILD_SHARED_LIBS)
 add_library(braft-shared SHARED $<TARGET_OBJECTS:OBJ_LIB>)
@@ -50,6 +51,7 @@ file(WRITE "${CMAKE_CURRENT_BINARY_DIR}/unofficial-braft-config.cmake"
 [[include(CMakeFindDependencyMacro)
 find_dependency(ZLIB)
 find_dependency(gflags CONFIG)
+find_dependency(glog CONFIG)
 find_dependency(Protobuf CONFIG)
 file(GLOB TARGET_FILES "${CMAKE_CURRENT_LIST_DIR}/unofficial-braftTargets.cmake")
 foreach (TARGET_FILE ${TARGET_FILES})
