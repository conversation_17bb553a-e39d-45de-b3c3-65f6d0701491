{"name": "cyclonedds", "version-semver": "0.10.5", "description": "Eclipse Cyclone DDS is a very performant and robust open-source implementation of the OMG DDS specification", "homepage": "https://cyclonedds.io", "license": "EPL-2.0 OR BSD-3-Clause", "supports": "!uwp & !(arm64 & windows)", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "default-features": ["deadline-missed", "ipv6", "lifespan", "security", "source-specific-multicast", "topic-discovery", "type-discovery"], "features": {"ddsperf": {"description": "Build ddsperf tool", "dependencies": [{"name": "cyclonedds", "host": true, "default-features": false, "features": ["idlc"]}]}, "deadline-missed": {"description": "Enable Deadline Missed QoS suppor"}, "idlc": {"description": "Build IDL preprocessor"}, "ipv6": {"description": "Enable ipv6 support"}, "lifespan": {"description": "Enable Lifespan QoS support"}, "security": {"description": "Enable OMG DDS Security support"}, "shm": {"description": "Enable shared memory support", "supports": "!windows", "dependencies": ["iceoryx"]}, "source-specific-multicast": {"description": "Enable support for source-specific multicast"}, "ssl": {"description": "Enable OpenSSL support", "dependencies": ["openssl"]}, "topic-discovery": {"description": "Enable Topic Discovery support", "dependencies": [{"name": "cyclonedds", "default-features": false, "features": ["type-discovery"]}]}, "type-discovery": {"description": "Enable Type Discovery support"}}}