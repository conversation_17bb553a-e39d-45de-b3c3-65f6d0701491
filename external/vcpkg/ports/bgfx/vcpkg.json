{"name": "bgfx", "version": "1.129.8866-491", "maintainers": "<PERSON> <bwrsand<PERSON>@users.noreply.github.com>", "description": "Cross-platform, graphics API agnostic, Bring Your Own Engine/Framework style rendering library.", "homepage": "https://bkaradzic.github.io/bgfx/overview.html", "documentation": "https://bkaradzic.github.io/bgfx", "license": "BSD-2-Clause AND CC0-1.0", "dependencies": ["libsquish", "miniz", "tinyexr", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "default-features": [{"name": "multithreaded", "platform": "!emscripten"}], "features": {"multithreaded": {"description": "Encode and render on different threads", "supports": "!emscripten"}, "tools": {"$comment": "Use '\"host\": true' in dependencies of vcpkg.json in manifest mode.", "description": "Shader, Texture and Geometry compilers for bgfx.", "supports": "native"}}}