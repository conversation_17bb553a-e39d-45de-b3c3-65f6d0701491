vcpkg_from_github(
    OUT_SOURCE_PATH SOURCE_PATH
    REPO microsoft/krabsetw
    REF 550f5edfdb6464567d7a618702316183d31746e5
    SHA512 0e02e04b79d4f856962c135023b87057898cb9a05f8056e0dc2bca7c4bb6227a660097f5251134086d1334f2a8e1541b4e963667ccd4f5c5ed6d60aa32c2c968
    HEAD_REF master
)

file(INSTALL ${SOURCE_PATH}/krabs/krabs/ DESTINATION ${CURRENT_PACKAGES_DIR}/include/krabs)
file(INSTALL ${SOURCE_PATH}/krabs/krabs.hpp DESTINATION ${CURRENT_PACKAGES_DIR}/include)
file(INSTALL ${SOURCE_PATH}/LICENSE DESTINATION ${CURRENT_PACKAGES_DIR}/share/${PORT} RENAME copyright)
