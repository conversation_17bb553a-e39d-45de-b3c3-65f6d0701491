vcpkg_from_github(
    OUT_SOURCE_PATH SOURCE_PATH
    REPO fungos/cr
    REF 66b76e24611c3b87566e5365e8c422387bc85916
    SHA512 002162461ecf131d717f7567cf13ee1345b7b359bb17c0801f4246c472c1b180f4f8937e9a3f4c186d95c41c20ffcc5ba5e953f197f344fb1000f51d8c4ee473
    HEAD_REF master
)

file(
    COPY ${SOURCE_PATH}/cr.h
    DESTINATION ${CURRENT_PACKAGES_DIR}/include
)

# Handle copyright
file(INSTALL ${SOURCE_PATH}/LICENSE DESTINATION ${CURRENT_PACKAGES_DIR}/share/${PORT} RENAME copyright)
