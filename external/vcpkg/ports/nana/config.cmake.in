@PACKAGE_INIT@
include(CMakeFindDependencyMacro)

if(UNIX)
  find_dependency(Threads)
  find_dependency(Freetype)
  find_dependency(X11)
  find_dependency(Fontconfig)
endif()

option(NANA_ENABLE_PNG "Enable PNG support" @NANA_ENABLE_PNG@)
option(NANA_ENABLE_JPEG "Enable JPEG support" @NANA_ENABLE_JPEG@)

if(NANA_ENABLE_PNG)
  find_dependency(PNG)
endif()

if(NANA_ENABLE_JPEG)
  find_dependency(JPEG)
endif()

include("${CMAKE_CURRENT_LIST_DIR}/unofficial-nana-targets.cmake")

if(UNIX)
  target_link_libraries(unofficial::nana::nana INTERFACE Fontconfig::Fontconfig ${X11_LIBRARIES} ${X11_Xft_LIB})
endif()

if(NANA_ENABLE_JPEG)
  target_link_libraries(unofficial::nana::nana INTERFACE ${JPEG_LIBRARIES})
endif()
