{"name": "nana", "version": "1.7.4", "port-version": 5, "description": "Cross-platform library for GUI programming in modern C++ style.", "homepage": "https://github.com/cnjinhao/nana", "supports": "!uwp", "dependencies": [{"name": "fontconfig", "platform": "!uwp & !windows & !mingw"}, {"name": "freetype", "platform": "!uwp & !windows & !mingw"}, "libjpeg-turbo", "libpng", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}]}