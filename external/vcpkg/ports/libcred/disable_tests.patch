diff --git a/meson.build b/meson.build
index 4de2473..d49a42a 100644
--- a/meson.build
+++ b/meson.build
@@ -56,8 +56,8 @@ endif
 
 install_headers('include/libcred.hpp')
 
-executable('ex1', ['example/ex1.cpp'], link_with: credhelperlib, include_directories: ['include'])
-executable('ex2', ['example/ex2.cpp'], link_with: credhelperlib, include_directories: ['include'])
+#executable('ex1', ['example/ex1.cpp'], link_with: credhelperlib, include_directories: ['include'])
+#executable('ex2', ['example/ex2.cpp'], link_with: credhelperlib, include_directories: ['include'])
 
-testexe = executable('testexe', ['test/test.cpp'], link_with: credhelperlib, include_directories: ['include'])
-test('test1', testexe)
+#testexe = executable('testexe', ['test/test.cpp'], link_with: credhelperlib, include_directories: ['include'])
+#test('test1', testexe)
