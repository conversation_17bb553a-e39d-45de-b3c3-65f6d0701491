{"name": "libtracepoint", "version": "1.4.0", "description": "C/C++ interface for generating Linux Tracepoints", "homepage": "https://github.com/microsoft/LinuxTracepoints/", "license": "MIT", "supports": "linux | windows", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"tools": {"description": "Build user tools: tracepoint-register", "supports": "linux"}}}