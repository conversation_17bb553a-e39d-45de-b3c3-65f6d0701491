{"name": "qlementine", "version": "1.2.2", "description": "Modern QStyle for desktop Qt6 applications.", "homepage": "https://github.com/oclero/qlementine/", "documentation": "https://oclero.github.io/qlementine/", "license": "MIT", "dependencies": [{"name": "qtbase", "default-features": false, "features": ["widgets"]}, "qtsvg", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}]}