{"name": "directxtex", "version-date": "2025-03-24", "description": "DirectXTex texture processing library", "homepage": "https://github.com/Microsoft/DirectXTex", "documentation": "https://github.com/microsoft/DirectXTex/wiki", "license": "MIT", "supports": "(windows & !arm32) | linux", "dependencies": [{"name": "directx-headers", "platform": "mingw | linux"}, "directxmath", {"name": "ms-gdkx", "platform": "xbox"}, {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "default-features": [{"name": "dx11", "platform": "windows & !xbox"}], "features": {"dx11": {"description": "Build with DirectX11 support", "supports": "windows & !xbox"}, "dx12": {"description": "Build with DirectX12 support for Windows 10/Windows 11", "dependencies": [{"name": "directx-headers", "platform": "windows & !xbox"}]}, "jpeg": {"description": "Add the JPEG auxiliary functions", "supports": "linux", "dependencies": ["libjpeg-turbo"]}, "openexr": {"description": "Enable OpenEXR support", "dependencies": ["openexr"]}, "png": {"description": "Add the PNG auxiliary functions", "supports": "linux", "dependencies": ["libpng"]}, "spectre": {"description": "Build Spectre-mitigated library"}, "tools": {"description": "texture command-line tools", "supports": "windows & !uwp & !xbox"}, "xbox": {"description": "Adds Xbox specific extensions to the library", "supports": "windows & x64 & !uwp & !xbox", "dependencies": ["ms-gdkx"]}}}