{"name": "drogon", "version-semver": "1.9.10", "description": "A C++14/17 based HTTP web application framework running on Linux/macOS/Unix/Windows", "homepage": "https://github.com/an-tao/drogon", "documentation": "https://drogon.docsforge.com/master/overview/", "license": "MIT", "supports": "!uwp", "dependencies": ["brotli", "jsoncpp", {"name": "libuuid", "platform": "!windows & !osx"}, "trantor", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}, "zlib"], "features": {"ctl": {"description": "Build drogon_ctl tool.", "dependencies": [{"name": "drogon", "host": true, "default-features": false, "features": ["ctl"]}]}, "mysql": {"description": "Support reading and writing from/to MySQL databases.", "dependencies": [{"name": "drogon", "features": ["orm"]}, {"name": "libmariadb", "features": ["iconv"], "platform": "osx"}, {"name": "libmariadb", "platform": "!osx"}]}, "orm": {"description": "Build with object-relational mapping support."}, "postgres": {"description": "Support reading and writing from/to Postgres databases.", "dependencies": [{"name": "drogon", "features": ["orm"]}, "libpq"]}, "redis": {"description": "Support reading and writing from/to Redis databases.", "dependencies": [{"name": "drogon", "features": ["orm"]}, "<PERSON><PERSON>"]}, "sqlite3": {"description": "Support reading and writing from/to SQLite databases.", "dependencies": [{"name": "drogon", "features": ["orm"]}, "sqlite3"]}, "yaml": {"description": "Support YAML Drogon configuration files", "dependencies": ["yaml-cpp"]}}}