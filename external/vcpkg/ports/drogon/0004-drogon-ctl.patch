diff --git a/cmake/DrogonUtilities.cmake b/cmake/DrogonUtilities.cmake
index 7cb9799..5462572 100644
--- a/cmake/DrogonUtilities.cmake
+++ b/cmake/DrogonUtilities.cmake
@@ -3,6 +3,7 @@
 # [TRUE to use_path_as_namespace] [prefixed namespace])
 # ##############################################################################
 function(drogon_create_views arg)
+  find_program(DROGON_CTL_COMMAND drogon_ctl REQUIRED)
   if(ARGC LESS 3)
     message(STATUS "arguments error when calling drogon_create_views")
     return()
@@ -39,7 +40,7 @@ function(drogon_create_views arg)
         set(ns "")
       endif()
       add_custom_command(OUTPUT ${ARGV2}/${outputFile}.h ${ARGV2}/${outputFile}.cc
-                         COMMAND drogon_ctl
+                         COMMAND ${DROGON_CTL_COMMAND}
                                  ARGS
                                  create
                                  view
@@ -55,7 +56,7 @@ function(drogon_create_views arg)
     else()
       get_filename_component(classname ${cspFile} NAME_WE)
       add_custom_command(OUTPUT ${ARGV2}/${classname}.h ${ARGV2}/${classname}.cc
-                         COMMAND drogon_ctl
+                         COMMAND ${DROGON_CTL_COMMAND}
                                  ARGS
                                  create
                                  view
