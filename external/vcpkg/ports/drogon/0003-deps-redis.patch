diff --git a/CMakeLists.txt b/CMakeLists.txt
index d7218a6..ccd08cb 100755
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -467,10 +467,10 @@ if (BUILD_SQLITE)
 endif (BUILD_SQLITE)
 
 if (BUILD_REDIS)
-    find_package(Hiredis)
+    find_package(Hiredis NAMES hiredis CONFIG REQUIRED)
     if (Hiredis_FOUND)
         add_definitions(-DUSE_REDIS)
-        target_link_libraries(${PROJECT_NAME} PRIVATE Hiredis_lib)
+        target_link_libraries(${PROJECT_NAME} PRIVATE hiredis::hiredis)
         set(DROGON_SOURCES
             ${DROGON_SOURCES}
             nosql_lib/redis/src/RedisClientImpl.cc
diff --git a/cmake/templates/DrogonConfig.cmake.in b/cmake/templates/DrogonConfig.cmake.in
index 48eb393..9a9742e 100644
--- a/cmake/templates/DrogonConfig.cmake.in
+++ b/cmake/templates/DrogonConfig.cmake.in
@@ -38,7 +38,7 @@ if(@COZ-PROFILER_FOUND@)
 find_dependency(coz-profiler)
 endif()
 if(@Hiredis_FOUND@)
-find_dependency(Hiredis)
+find_dependency(hiredis CONFIG)
 endif()
 if(@yaml-cpp_FOUND@)
 find_dependency(yaml-cpp)
