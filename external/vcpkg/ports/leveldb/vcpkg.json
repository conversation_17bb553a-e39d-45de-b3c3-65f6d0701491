{"name": "leveldb", "version": "1.23", "description": "LevelDB is a fast key-value storage library written at Google that provides an ordered mapping from string keys to string values.", "homepage": "https://github.com/google/leveldb", "license": "BSD-3-<PERSON><PERSON>", "supports": "!uwp", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"crc32c": {"description": "Build with crc32c", "dependencies": ["crc32c"]}, "snappy": {"description": "Build with snappy", "dependencies": ["snappy"]}}}