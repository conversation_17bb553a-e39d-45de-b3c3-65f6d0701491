diff --git a/CMakeLists.txt b/CMakeLists.txt
index f8285b8..39536fc 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -38,8 +38,14 @@ include(CheckIncludeFile)
 check_include_file("unistd.h" HAVE_UNISTD_H)
 
 include(CheckLibraryExists)
-check_library_exists(crc32c crc32c_value "" HAVE_CRC32C)
-check_library_exists(snappy snappy_compress "" HAVE_SNAPPY)
+if (WITH_CRC32C)
+    find_package(Crc32c CONFIG REQUIRED)
+    set(HAVE_CRC32C 1)
+endif()
+if (WITH_SNAPPY)
+    find_package(Snappy CONFIG REQUIRED)
+    set(HAVE_SNAPPY 1)
+endif()
 check_library_exists(tcmalloc malloc "" HAVE_TCMALLOC)
 
 include(CheckCXXSymbolExists)
@@ -268,10 +274,10 @@ if(HAVE_CLANG_THREAD_SAFETY)
 endif(HAVE_CLANG_THREAD_SAFETY)
 
 if(HAVE_CRC32C)
-  target_link_libraries(leveldb crc32c)
+  target_link_libraries(leveldb Crc32c::crc32c)
 endif(HAVE_CRC32C)
 if(HAVE_SNAPPY)
-  target_link_libraries(leveldb snappy)
+  target_link_libraries(leveldb Snappy::snappy)
 endif(HAVE_SNAPPY)
 if(HAVE_TCMALLOC)
   target_link_libraries(leveldb tcmalloc)
