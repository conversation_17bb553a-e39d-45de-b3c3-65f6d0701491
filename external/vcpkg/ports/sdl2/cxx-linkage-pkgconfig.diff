diff --git a/CMakeLists.txt b/CMakeLists.txt
index 2a91824..a8e9de4 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -3162,6 +3162,19 @@ set(SDL_STATIC_LIBS ${SDL_LIBS} ${EXTRA_LDFLAGS} ${_EXTRA_LIBS})
 list(REMOVE_DUPLICATES SDL_STATIC_LIBS)
 listtostr(SDL_STATIC_LIBS _SDL_STATIC_LIBS)
 set(SDL_STATIC_LIBS ${_SDL_STATIC_LIBS})
+if("${SOURCE_FILES};" MATCHES "[.]cpp;")
+  set(FAKE_CXX_LINKAGE "")
+  foreach(lib IN LISTS CMAKE_CXX_IMPLICIT_LINK_LIBRARIES)
+      if(lib IN_LIST CMAKE_C_IMPLICIT_LINK_LIBRARIES)
+          continue()
+      elseif(EXISTS "${lib}")
+          string(APPEND FAKE_CXX_LINKAGE " ${CMAKE_LINK_LIBRARY_FILE_FLAG}${lib}")
+      else()
+          string(APPEND FAKE_CXX_LINKAGE " ${CMAKE_LINK_LIBRARY_FLAG}${lib}")
+      endif()
+  endforeach()
+  string(APPEND SDL_STATIC_LIBS "${FAKE_CXX_LINKAGE}")
+endif()
 listtostr(SDL_LIBS _SDL_LIBS)
 set(SDL_LIBS ${_SDL_LIBS})
 listtostr(SDL_CFLAGS _SDL_CFLAGS "")
