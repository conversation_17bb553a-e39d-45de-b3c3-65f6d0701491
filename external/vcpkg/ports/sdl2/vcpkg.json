{"name": "sdl2", "version": "2.32.4", "description": "Simple DirectMedia Layer is a cross-platform development library designed to provide low level access to audio, keyboard, mouse, joystick, and graphics hardware via OpenGL and Direct3D.", "homepage": "https://www.libsdl.org/download-2.0.php", "license": "<PERSON><PERSON><PERSON>", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "default-features": [{"name": "dbus", "platform": "linux"}, {"name": "ibus", "platform": "linux"}, {"name": "wayland", "platform": "linux"}, {"name": "x11", "platform": "linux"}], "features": {"alsa": {"description": "Support for alsa audio", "dependencies": ["alsa"]}, "dbus": {"description": "Build with D-Bus support", "dependencies": [{"name": "dbus", "default-features": false, "platform": "linux"}]}, "ibus": {"description": "Build with ibus IME support", "supports": "linux"}, "samplerate": {"description": "Use libsamplerate for audio rate conversion", "dependencies": ["libsamplerate"]}, "vulkan": {"description": "Vulkan functionality for SDL"}, "wayland": {"description": "Build with Wayland support", "supports": "linux"}, "x11": {"description": "Build with X11 support", "supports": "!windows"}}}