diff --git a/CMakeLists.txt b/CMakeLists.txt
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -22,8 +22,10 @@
 #-----------------------------------------------------------------------------
 include(GNUInstallDirs)
 include(CheckIPOSupported)
 include(CMakePackageConfigHelpers)
+include(CMakePushCheckState)
+include(CheckSymbolExists)
 # default is "No tests"
 option(BUILD_TESTING "" OFF)
 include(CTest)
 include(sources.cmake)
@@ -118,8 +120,19 @@
 target_link_options(${PROJECT_NAME} BEFORE PRIVATE
     ${LTM_LD_FLAGS}
 )
 
+if(MSVC)
+    cmake_push_check_state()
+    set(CMAKE_REQUIRED_LIBRARIES bcrypt)
+    check_symbol_exists(BCryptGenRandom "Windows.h;bcrypt.h" BCRYPT_AVAILABLE)
+    cmake_pop_check_state()
+    if(BCRYPT_AVAILABLE)
+        target_compile_definitions(${PROJECT_NAME} PRIVATE LTM_WIN32_BCRYPT)
+        target_link_libraries(${PROJECT_NAME} PRIVATE bcrypt)
+    endif()
+endif()
+
 set(PUBLIC_HEADERS tommath.h)
 set(C89 False CACHE BOOL "(Usually maintained automatically) Enable when the library is in c89 mode to package the correct header files on install")
 if(C89)
     list(APPEND PUBLIC_HEADERS tommath_c89.h)
diff --git a/bn_s_mp_rand_platform.c b/bn_s_mp_rand_platform.c
--- a/bn_s_mp_rand_platform.c
+++ b/bn_s_mp_rand_platform.c
@@ -28,8 +28,19 @@
 #endif
 
 #define WIN32_LEAN_AND_MEAN
 #include <windows.h>
+
+#ifdef LTM_WIN32_BCRYPT
+#include <bcrypt.h>
+#pragma comment(lib, "bcrypt")
+
+static mp_err s_read_wincsp(void *p, size_t n)
+{
+   return BCRYPT_SUCCESS(BCryptGenRandom(NULL, (PUCHAR)p, (ULONG)n,
+                                         BCRYPT_USE_SYSTEM_PREFERRED_RNG)) ? MP_OKAY : MP_ERR;
+}
+#else
 #include <wincrypt.h>
 
 static mp_err s_read_wincsp(void *p, size_t n)
 {
@@ -45,8 +56,9 @@
       hProv = h;
    }
    return CryptGenRandom(hProv, (DWORD)n, (BYTE *)p) == TRUE ? MP_OKAY : MP_ERR;
 }
+#endif
 #endif /* WIN32 */
 
 #if !defined(BN_S_READ_WINCSP_C) && defined(__linux__) && defined(__GLIBC_PREREQ)
 #if __GLIBC_PREREQ(2, 25)
