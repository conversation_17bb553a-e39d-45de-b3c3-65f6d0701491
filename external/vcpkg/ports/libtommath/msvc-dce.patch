diff --git a/bn_s_mp_rand_platform.c b/bn_s_mp_rand_platform.c
--- a/bn_s_mp_rand_platform.c
+++ b/bn_s_mp_rand_platform.c
@@ -136,13 +136,17 @@
 
 mp_err s_mp_rand_platform(void *p, size_t n)
 {
    mp_err err = MP_ERR;
+   #ifndef _MSC_VER
    if ((err != MP_OKAY) && MP_HAS(S_READ_ARC4RANDOM)) err = s_read_arc4random(p, n);
+   #endif
    if ((err != MP_OKAY) && MP_HAS(S_READ_WINCSP))     err = s_read_wincsp(p, n);
+   #ifndef _MSC_VER
    if ((err != MP_OKAY) && MP_HAS(S_READ_GETRANDOM))  err = s_read_getrandom(p, n);
    if ((err != MP_OKAY) && MP_HAS(S_READ_URANDOM))    err = s_read_urandom(p, n);
    if ((err != MP_OKAY) && MP_HAS(S_READ_LTM_RNG))    err = s_read_ltm_rng(p, n);
+   #endif
    return err;
 }
 
 #endif
