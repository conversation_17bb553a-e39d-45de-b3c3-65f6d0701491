{"name": "chronoengine", "version": "8.0.0", "description": "Multi-physics Simulation Engine.", "homepage": "http://projectchrono.org/", "license": "BSD-3-<PERSON><PERSON>", "supports": "(windows & x64 & !static & !uwp) | linux | osx", "dependencies": ["eigen3", "tbb", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"irrlicht": {"description": "Default visualization system for interactive 3D viewing of Chrono simulations.", "dependencies": ["irr<PERSON>t"]}, "vehicle": {"description": "Enables template-based ground vehicle modeling and simulation within Chrono."}}}