diff --git a/src/CMakeLists.txt b/src/CMakeLists.txt
index d75ce1055..a21acbd83 100644
--- a/src/CMakeLists.txt
+++ b/src/CMakeLists.txt
@@ -96,7 +96,7 @@ endif()
 
 message(STATUS "Searching for Threads...")
 set(THREADS_PREFER_PTHREAD_FLAG TRUE)
-find_package(Threads)
+find_package(Threads REQUIRED)
 
 message(STATUS "  Thread library:      ${CMAKE_THREAD_LIBS_INIT}")
 message(STATUS "  Using Win32 threads? ${CMAKE_USE_WIN32_THREADS_INIT}")
@@ -112,8 +112,9 @@ set(CH_C_FLAGS "${CH_C_FLAGS} ${CMAKE_THREAD_LIBS_INIT}")
 # main ChronoEngine library, regardless on whether or not <PERSON><PERSON> is found.
 
 message(STATUS "Searching for OpenMP...")
-find_package(OpenMP)
-
+if(0)
+find_package(OpenMP REQUIRED)
+endif()
 # Determine OpenMP version. Prepare substitution variables that can be used in
 # generating configuration header files.
 
@@ -177,7 +178,7 @@ if(USE_SIMD)
 	
    # Figure out SIMD support
    message(STATUS "Testing SIMD capabilities...")
-   find_package(SIMD)
+   find_package(SIMD REQUIRED)
    
    # Set substitution variables for configuration file
    if(SIMD_SSE)
@@ -250,7 +251,7 @@ endif()
 #-----------------------------------------------------------------------------
 
 message(STATUS "Searching for Eigen3...")
-find_package(Eigen3 3.3.0)
+find_package(Eigen3 3.3.0 REQUIRED)
 if(EIGEN3_FOUND)
   message(STATUS "  Eigen3 version: ${EIGEN3_VERSION}")
   message(STATUS "  Eigen3 include directory: ${EIGEN3_INCLUDE_DIR}")
@@ -293,7 +294,9 @@ endif()
 #-----------------------------------------------------------------------------
 
 message(STATUS "Searching for MPI...")
-find_package(MPI)
+if(0)
+find_package(MPI REQUIRED)
+endif()
 if(MPI_FOUND)
   message(STATUS "  MPI compiler:      ${MPI_CXX_COMPILER}")
   message(STATUS "  MPI compile flags: ${MPI_CXX_COMPILE_FLAGS}")
@@ -312,9 +315,9 @@ endif()
 #-----------------------------------------------------------------------------
 
 message(STATUS "Searching for CUDA...")
-
-find_package(CUDA QUIET)
-
+if(0)
+find_package(CUDA REQUIRED)
+endif()
 if(CUDA_FOUND)
   set(CUDA_BINARY_DIR "${CUDA_TOOLKIT_ROOT_DIR}/bin")
 
@@ -373,7 +376,7 @@ endif()
 #-----------------------------------------------------------------------------
 
 message(STATUS "Searching for Thrust...")
-
+if(0)
 if (${CMAKE_SYSTEM_NAME} MATCHES "FreeBSD")
   find_path(THRUST_INCLUDE_DIR NAMES thrust/version.h PATHS "/usr/local" "/usr/local/include")
   if (THRUST_INCLUDE_DIR)
@@ -383,9 +386,9 @@ if (${CMAKE_SYSTEM_NAME} MATCHES "FreeBSD")
     set(${THRUST_FOUND} FALSE)
   endif()
 else()
-  find_package(Thrust)
+  find_package(Thrust REQUIRED)
+endif()
 endif()
-
 if(THRUST_FOUND)
   message(STATUS "  Thrust version:     ${THRUST_VERSION}")
   message(STATUS "  Thrust include dir: ${THRUST_INCLUDE_DIR}")
@@ -500,14 +503,14 @@ if(ENABLE_HDF5)
     #    HDF5_INCLUDE_DIRS
     #    HDF5_C_LIBRARIES
     #    HDF5_CXX_LIBRARIES
-    find_package(HDF5 COMPONENTS CXX)
+    find_package(HDF5 COMPONENTS CXX REQUIRED)
 
     if(NOT HDF5_FOUND)
         # Look for a package configuration file
         # LIB_TYPE can be one of STATIC or SHARED.
         set(LIB_TYPE SHARED) # STATIC or SHARED
         string(TOLOWER ${LIB_TYPE} SEARCH_TYPE)
-        find_package(HDF5 NAMES hdf5 COMPONENTS CXX ${SEARCH_TYPE})
+        find_package(HDF5 NAMES hdf5 COMPONENTS CXX ${SEARCH_TYPE} REQUIRED)
 
         if (HDF5_FOUND)
             set(HDF5_INCLUDE_DIRS ${HDF5_INCLUDE_DIR})
diff --git a/src/chrono/CMakeLists.txt b/src/chrono/CMakeLists.txt
index 88f970fca..917452c4e 100644
--- a/src/chrono/CMakeLists.txt
+++ b/src/chrono/CMakeLists.txt
@@ -1405,9 +1405,9 @@ endif()
 # Add the ChronoEngine library to the project
 add_library(ChronoEngine SHARED ${ChronoEngine_FILES})
 
-target_link_libraries(ChronoEngine ${OPENMP_LIBRARIES} ${CH_SOCKET_LIB})
+target_link_libraries(ChronoEngine ${OPENMP_LIBRARIES} ${CH_SOCKET_LIB} TBB::tbb TBB::tbbmalloc)
 if (UNIX)
-  target_link_libraries(ChronoEngine pthread)
+  target_link_libraries(ChronoEngine pthread TBB::tbb TBB::tbbmalloc)
 endif()
 
 # Set some custom properties of this target
diff --git a/src/chrono_irrlicht/CMakeLists.txt b/src/chrono_irrlicht/CMakeLists.txt
index acffe0cb6..bc0e938bc 100644
--- a/src/chrono_irrlicht/CMakeLists.txt
+++ b/src/chrono_irrlicht/CMakeLists.txt
@@ -54,7 +54,7 @@ SOURCE_GROUP("" FILES
 
 set(CH_IRRLICHT_CXX_FLAGS "")
 set(CH_IRRLICHT_C_FLAGS "")
-
+if(0)
 IF(${CMAKE_SYSTEM_NAME} MATCHES "Windows")
   SET(IRRLICHT_ROOT "C:/irrlicht-1.8.1"  CACHE PATH   "Path to Irrlicht SDK installation.")
   IF("${CH_COMPILER}" STREQUAL "COMPILER_MSVC")
@@ -84,7 +84,8 @@ else()
   FIND_LIBRARY(IRRLICHT_LIBRARY NAMES Irrlicht PATHS ${IRRLICHT_ROOT}/../../lib)
   set(IRRLICHT_LIBRARY "${IRRLICHT_LIBRARY}")
 ENDIF()
-
+endif()
+find_package(irrlicht REQUIRED)
 # If using MSVC, disable warning 4275 (non-DLL-interface class used as base for DLL-interface class)
 if(MSVC)
   add_compile_options(/wd4275)
@@ -93,7 +94,7 @@ if(MSVC)
 endif()
 
 # Add path to IRRLICHT headers
-
+if(0)
 IF(EXISTS "${IRRLICHT_ROOT}/include/irrlicht")
   SET(CH_IRRLICHTINC "${IRRLICHT_ROOT}/include/irrlicht")
 ELSEIF(EXISTS "${IRRLICHT_ROOT}/include")
@@ -103,7 +104,7 @@ ELSE()
 ENDIF()
 
 INCLUDE_DIRECTORIES( ${CH_IRRLICHTINC} )
-
+endif(0)
 
 # Let some variables be visible also from outside this directory
 
@@ -119,7 +120,7 @@ set(COMPILER_FLAGS "${CH_CXX_FLAGS} ${CH_IRRLICHT_CXX_FLAGS}")
 set(LINKER_FLAGS "${CH_LINKERFLAG_SHARED}")
 set(LIBRARIES 
     ChronoEngine
-    ${IRRLICHT_LIBRARY}
+    Irrlicht
     )
 
 if (ENABLE_MODULE_POSTPROCESS)
@@ -168,7 +169,7 @@ endif()
 # appropriate directory (depending on the build type); however, we use
 # copy_if_different.
 
-IF(${CMAKE_SYSTEM_NAME} MATCHES "Windows")
+IF(0)
 
   IF(DEFINED ENV{CONDA_BUILD})
     SET(CH_IRRLICHT_DLL "$ENV{PREFIX}/Library/bin/Irrlicht.dll")
