diff --git a/cmake/FindBLAS.cmake b/cmake/FindBLAS.cmake
index 6a8f0f9e1..c44f92eb4 100644
--- a/cmake/FindBLAS.cmake
+++ b/cmake/FindBLAS.cmake
@@ -487,7 +487,7 @@ if (<PERSON>LA_VENDOR MATCHES "Intel" OR BLA_VENDOR STREQUAL "All")
  endif ()
  if (CMAKE_C_COMPILER_LOADED OR CMAKE_CXX_COMPILER_LOADED)
   if(BLAS_FIND_QUIETLY OR NOT BLAS_FIND_REQUIRED)
-    find_package(Threads)
+    find_package(Threads REQUIRED)
   else()
     find_package(Threads REQUIRED)
   endif()
diff --git a/cmake/FindEigen3.cmake b/cmake/FindEigen3.cmake
index 0d2b3cedd..02c2b50d2 100644
--- a/cmake/FindEigen3.cmake
+++ b/cmake/FindEigen3.cmake
@@ -1,7 +1,7 @@
 # - Try to find Eigen3 lib
 #
 # This module supports requiring a minimum version, e.g. you can do
-#   find_package(Eigen3 3.1.2)
+#   find_package(Eigen3 3.1.2 REQUIRED)
 # to require version 3.1.2 or newer of Eigen3.
 #
 # Once done this will define
@@ -70,7 +70,7 @@ else (EIGEN3_INCLUDE_DIR)
   # search first if an Eigen3Config.cmake is available in the system,
   # if successful this would set EIGEN3_INCLUDE_DIR and the rest of
   # the script will work as usual
-  find_package(Eigen3 ${Eigen3_FIND_VERSION} NO_MODULE QUIET)
+  find_package(Eigen3 ${Eigen3_FIND_VERSION} NO_MODULE REQUIRED)
 
   if(NOT EIGEN3_INCLUDE_DIR)
     find_path(EIGEN3_INCLUDE_DIR NAMES signature_of_eigen3_matrix_library
diff --git a/cmake/FindMKL.cmake b/cmake/FindMKL.cmake
index 6f8a2740f..acf43af4d 100644
--- a/cmake/FindMKL.cmake
+++ b/cmake/FindMKL.cmake
@@ -7,7 +7,7 @@
 #
 # Usage example:
 #   set(MKL_USE_STATIC_LIBS ON)
-#   find_package(MKL)
+#   find_package(MKL REQUIRED)
 #   if (MKL_FOUND)
 #      include_directories(${MKL_INCLUDE_DIRS})
 #      link_directories(${MKL_LIBRARY_DIRS})
diff --git a/cmake/FindMyGUI.cmake b/cmake/FindMyGUI.cmake
index b5563c801..2fc991246 100644
--- a/cmake/FindMyGUI.cmake
+++ b/cmake/FindMyGUI.cmake
@@ -81,7 +81,7 @@ IF (WIN32) #Windows
     ENDIF (OGRESOURCE)
 ELSE (WIN32) #Unix
     CMAKE_MINIMUM_REQUIRED(VERSION 2.4.7 FATAL_ERROR)
-    FIND_PACKAGE(PkgConfig)
+    find_package(PkgConfig REQUIRED)
     IF(MYGUI_STATIC)
         # don't use pkgconfig on OS X, find freetype & append it's libs to resulting MYGUI_LIBRARIES
         IF (NOT APPLE)
@@ -101,7 +101,7 @@ ELSE (WIN32) #Unix
             ENDIF (MYGUI_INCLUDE_DIRS)
         ELSE (NOT APPLE)
             SET(CMAKE_PREFIX_PATH ${CMAKE_PREFIX_PATH} ${MYGUI_DEPENDENCIES_DIR} ${OGRE_DEPENDENCIES_DIR})
-            FIND_PACKAGE(freetype)
+            find_package(freetype REQUIRED)
             FIND_PATH(MYGUI_INCLUDE_DIRS MyGUI.h PATHS /usr/local/include /usr/include PATH_SUFFIXES MyGUI MYGUI)
             FIND_LIBRARY(MYGUI_LIBRARIES MyGUIEngineStatic PATHS /usr/lib /usr/local/lib)
             SET(MYGUI_PLATFORM_LIBRARIES "MyGUI.OgrePlatform")
diff --git a/cmake/FindOgre.cmake b/cmake/FindOgre.cmake
index 9e2081a0c..950ba0185 100644
--- a/cmake/FindOgre.cmake
+++ b/cmake/FindOgre.cmake
@@ -262,17 +262,17 @@ endif ()
 # look for required Ogre dependencies in case of static build and/or threading
 if (OGRE_STATIC)
   set(OGRE_DEPS_FOUND TRUE)
-  find_package(Cg QUIET)
-  find_package(DirectX QUIET)
-  find_package(FreeImage QUIET)
-  find_package(Freetype QUIET)
-  find_package(OpenGL QUIET)
-  find_package(OpenGLES QUIET)
-  find_package(OpenGLES2 QUIET)
-  find_package(ZLIB QUIET)
-  find_package(ZZip QUIET)
+  find_package(Cg REQUIRED)
+  find_package(DirectX REQUIRED)
+  find_package(FreeImage REQUIRED)
+  find_package(Freetype REQUIRED)
+  find_package(OpenGL REQUIRED)
+  find_package(OpenGLES REQUIRED)
+  find_package(OpenGLES2 REQUIRED)
+  find_package(ZLIB REQUIRED)
+  find_package(ZZip REQUIRED)
   if (UNIX AND NOT APPLE AND NOT ANDROID)
-    find_package(X11 QUIET)
+    find_package(X11 REQUIRED)
     find_library(XAW_LIBRARY NAMES Xaw Xaw7 PATHS ${DEP_LIB_SEARCH_DIR} ${X11_LIB_SEARCH_PATH})
     if (NOT XAW_LIBRARY OR NOT X11_Xt_FOUND)
       set(X11_FOUND FALSE)
@@ -310,7 +310,7 @@ endif()
       endif()
       
       set(OGRE_BOOST_COMPONENTS thread date_time)
-      find_package(Boost COMPONENTS ${OGRE_BOOST_COMPONENTS} QUIET)
+      find_package(Boost COMPONENTS ${OGRE_BOOST_COMPONENTS} REQUIRED)
       if(Boost_FOUND AND Boost_VERSION GREATER 104900)
         if(Boost_VERSION GREATER 105300)
             set(OGRE_BOOST_COMPONENTS thread date_time system atomic chrono)
@@ -319,7 +319,7 @@ endif()
         endif()
       endif()
 
-      find_package(Boost COMPONENTS ${OGRE_BOOST_COMPONENTS} QUIET)
+      find_package(Boost COMPONENTS ${OGRE_BOOST_COMPONENTS} REQUIRED)
       if (NOT Boost_THREAD_FOUND)
         set(OGRE_DEPS_FOUND FALSE)
       else ()
@@ -327,7 +327,7 @@ endif()
         set(OGRE_INCLUDE_DIRS ${OGRE_INCLUDE_DIRS} ${Boost_INCLUDE_DIRS})
       endif ()
     elseif (OGRE_CONFIG_THREAD_PROVIDER EQUAL 2)
-      find_package(POCO QUIET)
+      find_package(POCO REQUIRED)
       if (NOT POCO_FOUND)
         set(OGRE_DEPS_FOUND FALSE)
       else ()
@@ -335,7 +335,7 @@ endif()
         set(OGRE_INCLUDE_DIRS ${OGRE_INCLUDE_DIRS} ${POCO_INCLUDE_DIRS})
       endif ()
     elseif (OGRE_CONFIG_THREAD_PROVIDER EQUAL 3)
-      find_package(TBB QUIET)
+      find_package(TBB REQUIRED)
       if (NOT TBB_FOUND)
         set(OGRE_DEPS_FOUND FALSE)
       else ()
diff --git a/cmake/FindPkgMacros.cmake b/cmake/FindPkgMacros.cmake
index 53111e074..2f6881c61 100644
--- a/cmake/FindPkgMacros.cmake
+++ b/cmake/FindPkgMacros.cmake
@@ -70,7 +70,7 @@ endmacro(clear_if_changed)
 # Try to get some hints from pkg-config, if available
 macro(use_pkgconfig PREFIX PKGNAME)
   if(NOT ANDROID)
-    find_package(PkgConfig)
+    find_package(PkgConfig REQUIRED)
     if (PKG_CONFIG_FOUND)
       pkg_check_modules(${PREFIX} ${PKGNAME})
     endif ()
diff --git a/cmake/FindSDL2.cmake b/cmake/FindSDL2.cmake
index 4b8f7e915..e27895a2f 100644
--- a/cmake/FindSDL2.cmake
+++ b/cmake/FindSDL2.cmake
@@ -112,7 +112,7 @@ ENDIF(NOT SDL2_BUILDING_LIBRARY)
 # frameworks may already provide it.
 # But for non-OSX systems, I will use the CMake Threads package.
 IF(NOT APPLE)
-	FIND_PACKAGE(Threads)
+	find_package(Threads REQUIRED)
 ENDIF(NOT APPLE)
 
 # MinGW needs an additional library, mwindows
diff --git a/cmake/GetGitRevisionDescription.cmake b/cmake/GetGitRevisionDescription.cmake
index 319f307a9..68ec2dad7 100644
--- a/cmake/GetGitRevisionDescription.cmake
+++ b/cmake/GetGitRevisionDescription.cmake
@@ -82,7 +82,7 @@ endfunction()
 
 function(git_describe _var)
 	if(NOT GIT_FOUND)
-		find_package(Git QUIET)
+		find_package(Git REQUIRED)
 	endif()
 	get_git_head_revision(refspec hash)
 	if(NOT GIT_FOUND)
diff --git a/src/chrono_cascade/CMakeLists.txt b/src/chrono_cascade/CMakeLists.txt
index 3d38c8707..f9558baf8 100644
--- a/src/chrono_cascade/CMakeLists.txt
+++ b/src/chrono_cascade/CMakeLists.txt
@@ -50,7 +50,7 @@ source_group("" FILES
 #-----------------------------------------------------------------------------	
 # Find OpenCASCADE
 
-find_package(OpenCASCADE CONFIG)
+find_package(OpenCASCADE CONFIG REQUIRED)
 
 if (NOT OpenCASCADE_FOUND)
   message("Could not find OpenCASCADE or one of its required modules")
diff --git a/src/chrono_mumps/CMakeLists.txt b/src/chrono_mumps/CMakeLists.txt
index d8564600b..a2e1ea207 100644
--- a/src/chrono_mumps/CMakeLists.txt
+++ b/src/chrono_mumps/CMakeLists.txt
@@ -88,7 +88,7 @@ unset(BLAS_FOUND)
 unset(BLAS_LIBRARIES)
 message(STATUS "Searching for BLAS...")
 
-find_package(BLAS)
+find_package(BLAS REQUIRED)
 
 set(CMAKE_LIBRARY_PATH ${CMAKE_LIBRARY_PATH_BKP}) # restore original CMAKE_LIBRARY_PATH
 
diff --git a/src/chrono_mumps/building_Mumps/CMakeLists_5.0.2.txt b/src/chrono_mumps/building_Mumps/CMakeLists_5.0.2.txt
index e179a6841..5a6852e4b 100644
--- a/src/chrono_mumps/building_Mumps/CMakeLists_5.0.2.txt
+++ b/src/chrono_mumps/building_Mumps/CMakeLists_5.0.2.txt
@@ -78,7 +78,7 @@ endif()
 SET(CMAKE_LIBRARY_PATH ${CMAKE_LIBRARY_PATH} ${BLAS_LIBRARIES_USER} ${INTEL_LIBRARIES_DIRS} )
 
 
-find_package(BLAS)
+find_package(BLAS REQUIRED)
 SET(CMAKE_LIBRARY_PATH ${CMAKE_LIBRARY_PATH_BKP}) # restore original CMAKE_LIBRARY_PATH
 
 if (NOT BLAS_FOUND)
@@ -101,7 +101,7 @@ SET(GENERIC_COMPILER_FLAGS_C #aka OPTC
 			"-MD")
 			
 
-find_package(OpenMP)
+find_package(OpenMP REQUIRED)
 if (ENABLE_OPENMP AND OPENMP_FOUND)
 	SET(GENERIC_COMPILER_FLAGS_FORTRAN "${GENERIC_COMPILER_FLAGS_FORTRAN} ${OpenMP_Fortran_FLAGS}")
 	SET(GENERIC_COMPILER_FLAGS_C "${GENERIC_COMPILER_FLAGS_C} ${OpenMP_C_FLAGS}")
diff --git a/src/chrono_mumps/building_Mumps/CMakeLists_5.1.1.txt b/src/chrono_mumps/building_Mumps/CMakeLists_5.1.1.txt
index 1c3a3b3bb..115d3372e 100644
--- a/src/chrono_mumps/building_Mumps/CMakeLists_5.1.1.txt
+++ b/src/chrono_mumps/building_Mumps/CMakeLists_5.1.1.txt
@@ -78,7 +78,7 @@ endif()
 SET(CMAKE_LIBRARY_PATH ${CMAKE_LIBRARY_PATH} ${BLAS_LIBRARIES_USER} ${INTEL_LIBRARIES_DIRS} )
 
 # Looking for valid BLAS libraries
-find_package(BLAS)
+find_package(BLAS REQUIRED)
 SET(CMAKE_LIBRARY_PATH ${CMAKE_LIBRARY_PATH_BKP}) # restore original CMAKE_LIBRARY_PATH
 
 if (NOT BLAS_FOUND)
@@ -121,7 +121,7 @@ SET(GENERIC_COMPILER_FLAGS_C #aka OPTC
 			"-MD")
 			
 
-find_package(OpenMP)
+find_package(OpenMP REQUIRED)
 if (ENABLE_OPENMP AND OPENMP_FOUND)
 	SET(GENERIC_COMPILER_FLAGS_FORTRAN "${GENERIC_COMPILER_FLAGS_FORTRAN} ${OpenMP_Fortran_FLAGS}")
 	SET(GENERIC_COMPILER_FLAGS_C "${GENERIC_COMPILER_FLAGS_C} ${OpenMP_C_FLAGS}")
diff --git a/src/chrono_opengl/CMakeLists.txt b/src/chrono_opengl/CMakeLists.txt
index a7b60f13d..9b76134fa 100644
--- a/src/chrono_opengl/CMakeLists.txt
+++ b/src/chrono_opengl/CMakeLists.txt
@@ -41,10 +41,10 @@ endif()
 
 cmake_policy(SET CMP0072 NEW)
 
-find_package(OpenGL)
-find_package(GLM)
-find_package(GLEW)
-find_package(GLFW)
+find_package(OpenGL REQUIRED)
+find_package(GLM REQUIRED)
+find_package(GLEW REQUIRED)
+find_package(GLFW REQUIRED)
 
 # On windows, ask for the GLEW and GLFW DLLs so that we can copy. This is
 # optional.  If not specified, it is the user's responsibility to make them
diff --git a/src/chrono_pardisomkl/CMakeLists.txt b/src/chrono_pardisomkl/CMakeLists.txt
index 8c35c5d70..8313af46e 100644
--- a/src/chrono_pardisomkl/CMakeLists.txt
+++ b/src/chrono_pardisomkl/CMakeLists.txt
@@ -27,7 +27,7 @@ message(STATUS "Find MKL libraries")
 
 #set(MKL_USE_STATIC_LIBS ON)
 #set(MKL_FIND_DEBUG ON)
-find_package(MKL)
+find_package(MKL REQUIRED)
 
 message(STATUS "   MKL include dirs:   ${MKL_INCLUDE_DIRS}")
 message(STATUS "   MKL libraries:      ${MKL_LIBRARIES}")
diff --git a/src/chrono_sensor/CMakeLists.txt b/src/chrono_sensor/CMakeLists.txt
index 2b4102d5b..14c2f15e9 100644
--- a/src/chrono_sensor/CMakeLists.txt
+++ b/src/chrono_sensor/CMakeLists.txt
@@ -29,9 +29,9 @@ if(${CMAKE_SYSTEM_NAME} MATCHES "Windows")
   mark_as_advanced(CLEAR GLFW_DLL)
 endif()
 
-find_package(GLFW OPTIONAL_COMPONENTS)
-find_package(OpenGL OPTIONAL_COMPONENTS)
-find_package(GLEW OPTIONAL_COMPONENTS)
+find_package(GLFW REQUIRED)
+find_package(OpenGL REQUIRED)
+find_package(GLEW REQUIRED)
 
 message(STATUS "OpenGL libraries: ${OPENGL_LIBRARIES}")
 message(STATUS "GLEW libraries:   ${GLEW_LIBRARY}")
