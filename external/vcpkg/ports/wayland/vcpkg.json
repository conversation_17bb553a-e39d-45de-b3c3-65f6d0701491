{"name": "wayland", "version": "1.21.0", "port-version": 1, "description": "Core Wayland window system code and protocol", "homepage": "https://wayland.freedesktop.org", "license": "MIT", "supports": "!(windows | osx)", "features": {"force-build": {"description": ["Build wayland libraries instead of depending on system ones.", "Requires triplet variable X_VCPKG_FORCE_VCPKG_WAYLAND_LIBRARIES to be set."], "dependencies": ["expat", "libffi", "libxml2", {"name": "vcpkg-tool-meson", "host": true}]}}}