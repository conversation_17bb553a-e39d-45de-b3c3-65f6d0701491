{"name": "kaitai-struct-cpp-stl-runtime", "version": "0.10.1", "port-version": 1, "description": "Kaitai Struct is a declarative language used for describe various binary data structures, laid out in files or in memory. This library implements Kaitai Struct API for C++ using STL", "homepage": "http://kaitai.io/", "documentation": "https://doc.kaitai.io/lang_cpp_stl.html", "license": "MIT", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}, "zlib"], "default-features": ["iconv"], "features": {"iconv": {"description": "Set the way strings have to be encoded to ICONV", "dependencies": ["libiconv"]}}}