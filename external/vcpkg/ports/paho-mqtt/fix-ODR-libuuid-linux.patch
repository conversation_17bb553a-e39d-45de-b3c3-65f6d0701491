diff --git a/src/CMakeLists.txt b/src/CMakeLists.txt
index 16382c1..7e14ab3 100644
--- a/src/CMakeLists.txt
+++ b/src/CMakeLists.txt
@@ -98,6 +98,12 @@ endif()
 if(PAHO_BUILD_STATIC)
   add_library(common_obj_static OBJECT ${common_src})
   set_target_properties(common_obj_static PROPERTIES POSITION_INDEPENDENT_CODE ON)
+  IF(WIN32 OR APPLE)
+  ELSE()
+    find_package(unofficial-libuuid CONFIG REQUIRED)
+    target_link_libraries(common_obj_static PRIVATE unofficial::UUID::uuid)
+    target_compile_definitions(common_obj_static PUBLIC LIBUUID)
+  ENDIF()
   target_compile_definitions(common_obj_static PRIVATE PAHO_MQTT_STATIC=1)
 endif()
 
@@ -317,6 +323,12 @@ if(PAHO_WITH_SSL OR PAHO_WITH_LIBRESSL)
     target_include_directories(common_ssl_obj_static PUBLIC ${SSL_INCLUDE_DIR})
 
     set_property(TARGET common_ssl_obj_static PROPERTY POSITION_INDEPENDENT_CODE ON)
+    IF(WIN32 OR APPLE)
+    ELSE()
+        find_package(unofficial-libuuid CONFIG REQUIRED)
+        target_link_libraries(common_ssl_obj_static PRIVATE unofficial::UUID::uuid)
+        target_compile_definitions(common_ssl_obj_static PUBLIC LIBUUID)
+    ENDIF()
     target_compile_definitions(common_ssl_obj_static PRIVATE OPENSSL=1 PAHO_MQTT_STATIC=1)
     
     add_library(paho-mqtt3cs-static STATIC
