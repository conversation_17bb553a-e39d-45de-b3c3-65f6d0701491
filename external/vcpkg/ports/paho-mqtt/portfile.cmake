vcpkg_from_github(
  OUT_SOURCE_PATH SOURCE_PATH
  REPO eclipse/paho.mqtt.c
  REF "v${VERSION}"
  SHA512 5576ac3531a5c707f92a02cbfb9d60710b42acd99f57bcde311aa224780267a5152e8b92a6b077afab4780ee236d5e0c2a0b8986453439bce4323758b3d4385b
  HEAD_REF master
  PATCHES
    fix-unresolvedsymbol-arm.patch
    fix-ODR-libuuid-linux.patch
)

string(COMPARE EQUAL "${VCPKG_LIBRARY_LINKAGE}" "static" PAHO_BUILD_STATIC)
string(COMPARE EQUAL "${VCPKG_LIBRARY_LINKAGE}" "dynamic" PAHO_BUILD_DYNAMIC)

vcpkg_cmake_configure(
  SOURCE_PATH "${SOURCE_PATH}"
  OPTIONS
    -DPAHO_WITH_SSL=TRUE
    -DPAHO_HIGH_PERFORMANCE=TRUE
    -DPAHO_BUILD_SHARED=${PAHO_BUILD_DYNAMIC}
    -DPAHO_BUILD_STATIC=${PAHO_BUILD_STATIC}
    -DPAHO_ENABLE_TESTING=FALSE
)

vcpkg_cmake_install()
vcpkg_cmake_config_fixup(PACKAGE_NAME eclipse-paho-mqtt-c CONFIG_PATH lib/cmake/eclipse-paho-mqtt-c)
vcpkg_copy_pdbs()

if(VCPKG_LIBRARY_LINKAGE STREQUAL "dynamic")
    vcpkg_copy_tools(TOOL_NAMES MQTTVersion AUTO_CLEAN)
endif()

if(VCPKG_TARGET_IS_WINDOWS OR VCPKG_TARGET_IS_OSX)
    vcpkg_replace_string("${CURRENT_PACKAGES_DIR}/share/eclipse-paho-mqtt-c/eclipse-paho-mqtt-cConfig.cmake"
        [[# Generated by CMake]]
        [[# Generated by CMake
    include(CMakeFindDependencyMacro)
    find_dependency(OpenSSL)]]
    )
else()
    vcpkg_replace_string("${CURRENT_PACKAGES_DIR}/share/eclipse-paho-mqtt-c/eclipse-paho-mqtt-cConfig.cmake"
        [[# Generated by CMake]]
        [[# Generated by CMake
    include(CMakeFindDependencyMacro)
    find_dependency(OpenSSL)
    find_dependency(unofficial-libuuid CONFIG)]]
    )
endif()

file(REMOVE_RECURSE "${CURRENT_PACKAGES_DIR}/debug/include" "${CURRENT_PACKAGES_DIR}/debug/share")
file(REMOVE_RECURSE "${CURRENT_PACKAGES_DIR}/share/doc")

vcpkg_install_copyright(FILE_LIST "${SOURCE_PATH}/LICENSE")
