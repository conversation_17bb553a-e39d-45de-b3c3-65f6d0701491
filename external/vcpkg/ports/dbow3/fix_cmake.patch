--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -1,268 +1,25 @@
-# ----------------------------------------------------------------------------
-#   Basic Configuration
-# ----------------------------------------------------------------------------
-CMAKE_MINIMUM_REQUIRED(VERSION 2.8)
+cmake_minimum_required(VERSION 3.10)
 
-set(CMAKE_CXX_STANDARD 11)
+project(DBow3 VERSION 1.0.0)
 
-PROJECT(DBoW3)
-set(PROJECT_VERSION "0.0.1")
-string(REGEX MATCHALL "[0-9]" PROJECT_VERSION_PARTS "${PROJECT_VERSION}")
-list(GET PROJECT_VERSION_PARTS 0 PROJECT_VERSION_MAJOR)
-list(GET PROJECT_VERSION_PARTS 1 PROJECT_VERSION_MINOR)
-list(GET PROJECT_VERSION_PARTS 2 PROJECT_VERSION_PATCH)
-set(PROJECT_SOVERSION "${PROJECT_VERSION_MAJOR}.${PROJECT_VERSION_MINOR}")
+option(BUILD_EXAMPLES "Set to ON to build examples" OFF)
+option(BUILD_TESTS "Set to ON to build tests" OFF)
+option(BUILD_SHARED_LIBS "Set to OFF to build static libraries" ON)
 
-message("LIB_INSTALL_DIR: ${LIB_INSTALL_DIR}")
+# OpenCV
+option(USE_OPENCV_CONTRIB "Set on to use opencv-contrib" ON)
 
-#------------------------------------------------------
-# Build type
-#------------------------------------------------------
+# Optimization
+option(USE_SIMD "Set on to enabel avx optimization" ON)
 
-IF(NOT CMAKE_BUILD_TYPE )
-   SET( CMAKE_BUILD_TYPE "Release" )
-ENDIF()
 
-#------------------------------------------------------
-# Lib Names and Dirs
-#------------------------------------------------------
+add_subdirectory(Lib)
 
-if(WIN32)
-    # Postfix of DLLs:
-    SET(PROJECT_DLLVERSION "${PROJECT_VERSION_MAJOR}${PROJECT_VERSION_MINOR}${PROJECT_VERSION_PATCH}")
-    SET(RUNTIME_OUTPUT_PATH ${PROJECT_BINARY_DIR}/bin CACHE PATH "Directory for dlls and binaries")
-    SET(EXECUTABLE_OUTPUT_PATH ${PROJECT_BINARY_DIR}/bin CACHE PATH "Directory for binaries")
-    SET(LIBRARY_OUTPUT_PATH ${PROJECT_BINARY_DIR}/bin CACHE PATH "Directory for dlls")
-else()
-    # Postfix of so's:
-    set(PROJECT_DLLVERSION)
-    set(LIB_INSTALL_DIR lib CACHE STRING "Install location of libraries (e.g. lib32 or lib64 for multilib installations)")
-    SET(CMAKE_MODULE_PATH ${CMAKE_MODULE_PATH} ${CMAKE_INSTALL_PREFIX}/${LIB_INSTALL_DIR}/cmake/ /usr/${LIB_INSTALL_DIR}/cmake )
-endif()
+if(BUILD_EXAMPLES)
+    add_subdirectory(examples)
+endif(BUILD_EXAMPLES)
 
+if(BUILD_TESTS)
+    add_subdirectory(tests)
+endif(BUILD_TESTS)
 
-
-#
-OPTION(BUILD_UTILS	"Set to OFF to not build utils" ON)
-OPTION(USE_CONTRIB 	"Set to ON if contrib are installed" OFF)
-OPTION(BUILD_SHARED_LIBS 	"Set to OFF to build static libraries" ON)
-
-# ----------------------------------------------------------------------------
-#   Find Dependencies
-# ----------------------------------------------------------------------------
-find_package(OpenCV  REQUIRED)
-IF(USE_CONTRIB)
-add_definitions(-DUSE_CONTRIB)
-ENDIF()
-if(NOT OpenCV_VERSION VERSION_LESS "3.0")
-    ADD_DEFINITIONS(-DOPENCV_VERSION_3)
-    SET(OPENCV_VERSION_3 ON)
-ELSE()
-    SET(OPENCV_VERSION_3 OFF)
-ENDIF()
-
-include_directories(${OpenCV_INCLUDE_DIRS})
-
-SET(REQUIRED_LIBRARIES ${REQUIRED_LIBRARIES} ${OpenCV_LIBS})
-
-# ----------------------------------------------------------------------------
-#   PROJECT CONFIGURATION
-#   force some variables that could be defined in the command line to be written to cache
-# ----------------------------------------------------------------------------
-OPTION(INSTALL_DOC 	"Set to ON to build/install Documentation" OFF)
-IF (INSTALL_DOC)
-    FIND_PACKAGE(Doxygen REQUIRED)
-    MESSAGE( STATUS "INSTALL_DOC:         ${INSTALL_DOC} ")
-    INCLUDE("${PROJECT_SOURCE_DIR}/generateDoc.cmake")
-    GENERATE_DOCUMENTATION(${PROJECT_SOURCE_DIR}/dox.in)
-ENDIF()
- 
-# ----------------------------------------------------------------------------
-#   Uninstall target, for "make uninstall"
-# ----------------------------------------------------------------------------
-CONFIGURE_FILE( "${CMAKE_CURRENT_SOURCE_DIR}/cmake_uninstall.cmake.in" "${CMAKE_CURRENT_BINARY_DIR}/cmake_uninstall.cmake" IMMEDIATE @ONLY)
-ADD_CUSTOM_TARGET(uninstall "${CMAKE_COMMAND}" -P "${CMAKE_CURRENT_BINARY_DIR}/cmake_uninstall.cmake")
-
-# ----------------------------------------------------------------------------
-# create configuration file from .in file (If you use windows take care with paths)
-# ----------------------------------------------------------------------------
-
-CONFIGURE_FILE("${PROJECT_SOURCE_DIR}/config.cmake.in" "${PROJECT_BINARY_DIR}/Find${PROJECT_NAME}.cmake")
-CONFIGURE_FILE("${PROJECT_SOURCE_DIR}/config.cmake.in" "${PROJECT_BINARY_DIR}/${PROJECT_NAME}Config.cmake")
-INSTALL(FILES "${PROJECT_BINARY_DIR}/Find${PROJECT_NAME}.cmake" DESTINATION ${LIB_INSTALL_DIR}/cmake/ )
-INSTALL(FILES "${PROJECT_BINARY_DIR}/${PROJECT_NAME}Config.cmake" DESTINATION ${LIB_INSTALL_DIR}/cmake/${PROJECT_NAME} )
-
-
-
-
-# ----------------------------------------------------------------------------
-#   Program Optimization and debug (Extracted from OpenCV)
-# ----------------------------------------------------------------------------
-set(WARNINGS_ARE_ERRORS 		OFF CACHE BOOL "Treat warnings as errors")
-set(WHOLE_PROGRAM_OPTIMIZATION 	OFF CACHE BOOL "Flags for whole program optimization.")
-
-set(EXTRA_C_FLAGS "")
-set(EXTRA_C_FLAGS_RELEASE "")
-set(EXTRA_C_FLAGS_DEBUG "")
-set(EXTRA_EXE_LINKER_FLAGS "")
-set(EXTRA_EXE_LINKER_FLAGS_RELEASE "")
-set(EXTRA_EXE_LINKER_FLAGS_DEBUG "")
-
-IF(CMAKE_COMPILER_IS_GNUCXX OR MINGW)
-    set(ENABLE_PROFILING 		OFF CACHE BOOL "Enable profiling in the GCC compiler (Add flags: -g -pg)")
-    set(USE_OMIT_FRAME_POINTER 	ON CACHE BOOL "Enable -fomit-frame-pointer for GCC")
-    if(${CMAKE_SYSTEM_PROCESSOR} MATCHES arm*) # We can use only -O2 because the -O3 causes gcc crash
-        set(USE_O2 ON CACHE BOOL "Enable -O2 for GCC")
-        set(USE_FAST_MATH OFF CACHE BOOL "Enable -ffast-math for GCC")
-    endif()
-    if(${CMAKE_SYSTEM_PROCESSOR} MATCHES powerpc*)
-        set(USE_O3 ON CACHE BOOL "Enable -O3 for GCC")
-        set(USE_POWERPC ON CACHE BOOL "Enable PowerPC for GCC")
-    endif ()
-    if(${CMAKE_SYSTEM_PROCESSOR} MATCHES amd64* OR ${CMAKE_SYSTEM_PROCESSOR} MATCHES x86_64*)
-        set(USE_O3 ON CACHE BOOL "Enable -O3 for GCC")
-        set(USE_FAST_MATH OFF CACHE BOOL "Enable -ffast-math for GCC")
-        set(USE_MMX ON CACHE BOOL "Enable MMX for GCC")
-        set(USE_SSE ON CACHE BOOL "Enable SSE for GCC")
-        set(USE_SSE2 ON CACHE BOOL "Enable SSE2 for GCC")
-        set(USE_SSE3 ON CACHE BOOL "Enable SSE3 for GCC")
-    endif()
-    if(${CMAKE_SYSTEM_PROCESSOR} MATCHES i686* OR ${CMAKE_SYSTEM_PROCESSOR} MATCHES x86)
-        set(USE_O3 ON CACHE BOOL "Enable -O3 for GCC")
-        set(USE_FAST_MATH OFF CACHE BOOL "Enable -ffast-math for GCC")
-        set(USE_MMX ON CACHE BOOL "Enable MMX for GCC")
-        set(USE_SSE OFF CACHE BOOL "Enable SSE for GCC")
-        set(USE_SSE2 OFF CACHE BOOL "Enable SSE2 for GCC")
-        set(USE_SSE3 OFF CACHE BOOL "Enable SSE3 for GCC")
-    endif ()
-
-    set(EXTRA_C_FLAGS "${EXTRA_C_FLAGS} -Wall")
-
-    if(WARNINGS_ARE_ERRORS)
-        set(EXTRA_C_FLAGS "${EXTRA_C_FLAGS} -Werror")
-    endif()
-
-    # The -Wno-long-long is required in 64bit systems when including sytem headers.
-    if(${CMAKE_SYSTEM_PROCESSOR} MATCHES x86_64* OR ${CMAKE_SYSTEM_PROCESSOR} MATCHES amd64*)
-                set(EXTRA_C_FLAGS "${EXTRA_C_FLAGS} -Wno-long-long")
-    endif()
-
-    # Whole program optimization
-    if(WHOLE_PROGRAM_OPTIMIZATION)
-        set(EXTRA_C_FLAGS_RELEASE "${EXTRA_C_FLAGS_RELEASE} -fwhole-program --combine")
-    endif()
-
-    # Other optimizations
-    if(USE_OMIT_FRAME_POINTER)
-       set(EXTRA_C_FLAGS_RELEASE "${EXTRA_C_FLAGS_RELEASE} -fomit-frame-pointer")
-    endif()
-    if(USE_O2)
-       set(EXTRA_C_FLAGS_RELEASE "${EXTRA_C_FLAGS_RELEASE} -O2")
-    endif()
-    if(USE_O3)
-       set(EXTRA_C_FLAGS_RELEASE "${EXTRA_C_FLAGS_RELEASE} -O3")
-    endif()
-    if(USE_FAST_MATH)
-       set(EXTRA_C_FLAGS_RELEASE "${EXTRA_C_FLAGS_RELEASE} -ffast-math")
-    endif()
-    if(USE_POWERPC)
-       set(EXTRA_C_FLAGS_RELEASE "${EXTRA_C_FLAGS_RELEASE} -mcpu=G3 -mtune=G5")
-    endif()
-    if(USE_MMX)
-       set(EXTRA_C_FLAGS_RELEASE "${EXTRA_C_FLAGS_RELEASE} -mmmx")
-    endif()
-    if(USE_SSE)
-       set(EXTRA_C_FLAGS_RELEASE "${EXTRA_C_FLAGS_RELEASE} -msse")
-    endif()
-    if(USE_SSE2)
-       set(EXTRA_C_FLAGS_RELEASE "${EXTRA_C_FLAGS_RELEASE} -msse2")
-    endif()
-    if(USE_SSE3 AND NOT MINGW) # SSE3 should be disabled under MingW because it generates compiler errors
-       set(EXTRA_C_FLAGS_RELEASE "${EXTRA_C_FLAGS_RELEASE} -msse3")
-    endif()
-
-    if(ENABLE_PROFILING)
-        set(EXTRA_C_FLAGS_RELEASE "${EXTRA_C_FLAGS_RELEASE} -pg -g")
-    else()
-        if(NOT APPLE)
-            set(EXTRA_C_FLAGS "${EXTRA_C_FLAGS} -ffunction-sections")
-        endif()
-    endif()
-
-
-    set(EXTRA_C_FLAGS_RELEASE "${EXTRA_C_FLAGS_RELEASE} -DNDEBUG   ")
-    set(EXTRA_C_FLAGS_DEBUG "-g3 -O0 -DDEBUG -D_DEBUG -W -Wextra -Wno-return-type   ")
-
-    MESSAGE( STATUS "-------------------------------------------------------------------------------" )
-    message( STATUS "GNU COMPILER")
-    MESSAGE( STATUS "-------------------------------------------------------------------------------" )
-
-
-
-
-ELSE()  # MSVC
-
-
-ENDIF()#END OF COMPILER SPECIFIC OPTIONS
-SET(CMAKE_C_FLAGS_RELEASE "${CMAKE_C_FLAGS} ${EXTRA_C_FLAGS_RELEASE}")
-SET(CMAKE_C_FLAGS_DEBUG "${CMAKE_C_FLAGS}  ${EXTRA_C_FLAGS_DEBUG}")
-SET(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_C_FLAGS_RELEASE} -std=c++11")
-SET(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_C_FLAGS_DEBUG} -std=c++11")
-set(CMAKE_C_FLAGS_RELWITHDEBINFO "${CMAKE_C_FLAGS_RELEASE} ${CMAKE_C_FLAGS_DEBUG}")
-set(CMAKE_CXX_FLAGS_RELWITHDEBINFO "${CMAKE_CXX_FLAGS_RELEASE} ${CMAKE_CXX_FLAGS_DEBUG}")
-SET(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} ${EXTRA_EXE_LINKER_FLAGS}")
-SET(CMAKE_EXE_LINKER_FLAGS_RELEASE "${CMAKE_EXE_LINKER_FLAGS_RELEASE} ${EXTRA_EXE_LINKER_FLAGS_RELEASE}")
-SET(CMAKE_EXE_LINKER_FLAGS_DEBUG "${CMAKE_EXE_LINKER_FLAGS_DEBUG} ${EXTRA_EXE_LINKER_FLAGS_DEBUG}")
-
-
-
-#------------------------------------------------
-# DIRS
-#------------------------------------------------
-ADD_SUBDIRECTORY(src)
-IF (BUILD_UTILS)
-ADD_SUBDIRECTORY(utils)
-ENDIF()
-
-IF (BUILD_TESTS)
-ADD_SUBDIRECTORY(tests)
-ENDIF()
-
-
-# ----------------------------------------------------------------------------
-# display status message for important variables
-# ----------------------------------------------------------------------------
-message( STATUS )
-MESSAGE( STATUS "-------------------------------------------------------------------------------" )
-message( STATUS "General configuration for ${PROJECT_NAME} ${PROJECT_VERSION}")
-MESSAGE( STATUS "-------------------------------------------------------------------------------" )
-message("    Built as dynamic libs?:" ${BUILD_SHARED_LIBS})
-message("    Compiler:"                   "${CMAKE_COMPILER}"   "${CMAKE_CXX_COMPILER}")
-
-message( STATUS "Build Type: ${CMAKE_BUILD_TYPE}")
-message( STATUS "C++ flags (Release):       ${CMAKE_CXX_FLAGS} ${CMAKE_CXX_FLAGS_RELEASE}")
-message( STATUS "C++ flags (Debug):         ${CMAKE_CXX_FLAGS} ${CMAKE_CXX_FLAGS_DEBUG}")
-message( STATUS "C++ flags (Relase+Debug):         ${CMAKE_CXX_FLAGS_RELWITHDEBINFO}")
-
-message( STATUS "CMAKE_CXX_FLAGS:         ${CMAKE_CXX_FLAGS}")
-message( STATUS "CMAKE_BINARY_DIR:         ${CMAKE_BINARY_DIR}")
-
-MESSAGE( STATUS )
-MESSAGE( STATUS "CMAKE_SYSTEM_PROCESSOR = ${CMAKE_SYSTEM_PROCESSOR}" )
-MESSAGE( STATUS "CMAKE_INSTALL_PREFIX = ${CMAKE_INSTALL_PREFIX}" )
-MESSAGE( STATUS "CMAKE_BUILD_TYPE = ${CMAKE_BUILD_TYPE}" )
-MESSAGE( STATUS "CMAKE_MODULE_PATH = ${CMAKE_MODULE_PATH}" )
-MESSAGE( STATUS "BUILD_UTILS= ${BUILD_UTILS}" )
-MESSAGE( STATUS "BUILD_TESTS= ${BUILD_TESTS}" )
-MESSAGE( STATUS "OPENCV_DIR= ${OpenCV_DIR} VERSION=${OpenCV_VERSION}" )
-
-MESSAGE( STATUS "USE_CONTRIB= ${USE_CONTRIB}" )
-
-MESSAGE( STATUS )
-MESSAGE( STATUS "OpenCV_LIB_DIR=${OpenCV_LIB_DIR}")
-MESSAGE( STATUS "CMAKE_INSTALL_PREFIX=${CMAKE_BINARY_DIR}")
-
-MESSAGE( STATUS )
-MESSAGE( STATUS )
-MESSAGE( STATUS "Change a value with: cmake -D<Variable>=<Value>" )
-MESSAGE( STATUS )
--- /dev/null
+++ b/Lib/CMakeLists.txt
@@ -0,0 +1,92 @@
+cmake_minimum_required(VERSION 3.10)
+
+find_package(OpenCV REQUIRED)
+
+find_package(OpenMP  REQUIRED)
+
+include(${CMAKE_SOURCE_DIR}/cmake/instructionSet.cmake)
+
+if(BUILD_SHARED_LIBS)
+    add_library(DBow3 SHARED)
+
+    target_compile_definitions(DBow3 
+                               PUBLIC 
+                               DBOW_API
+    )
+    
+else()
+    add_library(DBow3 STATIC)
+endif()
+
+add_library(DBow3::DBow3 ALIAS DBow3)
+
+target_sources(DBow3 
+               PRIVATE 
+               src/BowVector.cpp
+               src/Database.cpp
+               src/DescManip.cpp
+               src/FeatureVector.cpp
+               src/QueryResults.cpp
+               src/ScoringObject.cpp
+               src/Vocabulary.cpp
+               src/quicklz.c
+)
+
+target_include_directories(DBow3
+                           PUBLIC
+                           $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
+                           $<INSTALL_INTERFACE:include>
+)
+
+target_link_libraries(DBow3
+                      PUBLIC
+                      OpenMP::OpenMP_CXX
+)
+
+if(USE_OPENCV_CONTRIB)
+target_link_libraries(DBow3
+                      PUBLIC 
+                      ${OpenCV_LIBS}
+)
+
+target_include_directories(DBow3
+                           PUBLIC
+                           ${OpenCV_INCLUDE_DIR}
+)
+endif()
+
+target_compile_definitions(DBow3 
+                           PUBLIC 
+                           NOMINMAX 
+                           _USE_MATH_DEFINES
+)
+
+target_compile_features(DBow3
+                        PRIVATE 
+                        cxx_std_14
+)
+
+
+install(TARGETS DBow3 EXPORT DBow3Targets
+        LIBRARY  DESTINATION lib
+        ARCHIVE  DESTINATION lib
+        RUNTIME  DESTINATION bin
+        INCLUDES DESTINATION include
+)
+
+install (DIRECTORY include/ DESTINATION include)
+
+install(EXPORT DBow3Targets
+        FILE DBow3Targets.cmake
+        NAMESPACE DBow3::
+        DESTINATION cmake/DBow3
+)
+
+include(CMakePackageConfigHelpers)
+
+write_basic_package_version_file(DBow3ConfigVersion.cmake
+	                             COMPATIBILITY SameMajorVersion
+)
+
+install(FILES DBow3Config.cmake ${CMAKE_CURRENT_BINARY_DIR}/DBow3ConfigVersion.cmake
+        DESTINATION cmake/DBow3)
\ No newline at end of file
--- /dev/null
+++ b/Lib/DBow3Config.cmake
@@ -0,0 +1,4 @@
+include(CMakeFindDependencyMacro)
+find_dependency(OpenCV)
+find_dependency(OpenMP)
+include("${CMAKE_CURRENT_LIST_DIR}/FbowTargets.cmake")
\ No newline at end of file
diff --git a/src/BowVector.h b/Lib/include/DBow3/BowVector.h
similarity index 99%
rename from src/BowVector.h
rename to Lib/include/DBow3/BowVector.h
index d8c17e0..cdbf517 100644
--- a/src/BowVector.h
+++ b/Lib/include/DBow3/BowVector.h
@@ -12,6 +12,7 @@
 
 #include <map>
 #include <vector>
+#include <ostream>
 #include "exports.h"
 #if _WIN32
 #include <cstdint>
diff --git a/src/DBoW3.h b/Lib/include/DBow3/DBoW3.h
similarity index 100%
rename from src/DBoW3.h
rename to Lib/include/DBow3/DBoW3.h
diff --git a/src/Database.h b/Lib/include/DBow3/Database.h
similarity index 100%
rename from src/Database.h
rename to Lib/include/DBow3/Database.h
diff --git a/src/DescManip.h b/Lib/include/DBow3/DescManip.h
similarity index 100%
rename from src/DescManip.h
rename to Lib/include/DBow3/DescManip.h
diff --git a/src/FeatureVector.h b/Lib/include/DBow3/FeatureVector.h
similarity index 100%
rename from src/FeatureVector.h
rename to Lib/include/DBow3/FeatureVector.h
diff --git a/src/QueryResults.h b/Lib/include/DBow3/QueryResults.h
similarity index 100%
rename from src/QueryResults.h
rename to Lib/include/DBow3/QueryResults.h
diff --git a/src/ScoringObject.h b/Lib/include/DBow3/ScoringObject.h
similarity index 100%
rename from src/ScoringObject.h
rename to Lib/include/DBow3/ScoringObject.h
diff --git a/src/Vocabulary.h b/Lib/include/DBow3/Vocabulary.h
similarity index 100%
rename from src/Vocabulary.h
rename to Lib/include/DBow3/Vocabulary.h
diff --git a/src/exports.h b/Lib/include/DBow3/exports.h
similarity index 78%
rename from src/exports.h
rename to Lib/include/DBow3/exports.h
index c324953..a31b317 100644
--- a/src/exports.h
+++ b/Lib/include/DBow3/exports.h
@@ -35,17 +35,15 @@ or implied, of Rafael Muñoz Salinas.
 #define _CRT_SECURE_NO_DEPRECATE /* to avoid multiple Visual Studio 2005 warnings */
 #endif
 
-#if (defined WIN32 || defined _WIN32 || defined WINCE)   && defined DBOW_DSO_EXPORTS
-    #define DBOW_API __declspec(dllexport)
-    #pragma warning ( disable : 4251 ) //disable warning to templates with dll linkage.
-    #pragma warning ( disable : 4290 ) //disable warning due to exception specifications.
-    #pragma warning ( disable : 4996 ) //disable warning regarding unsafe vsprintf.
-    #pragma warning ( disable : 4244 ) //disable warning convesions with lost of data.
-
+#ifdef _WIN32
+#    ifdef DBOW_API
+#        define DBOW_API __declspec(dllexport)
+#    else
+#        define DBOW_API __declspec(dllimport)
+#    endif
 #else
-    #define DBOW_API
+#    define DBOW_API
 #endif
 
-
 #define  DBOW_VERSION "3.0.0"
 #endif
diff --git a/src/quicklz.h b/Lib/include/DBow3/quicklz.h
similarity index 100%
rename from src/quicklz.h
rename to Lib/include/DBow3/quicklz.h
diff --git a/src/timers.h b/Lib/include/DBow3/timers.h
similarity index 100%
rename from src/timers.h
rename to Lib/include/DBow3/timers.h
diff --git a/src/BowVector.cpp b/Lib/src/BowVector.cpp
similarity index 99%
rename from src/BowVector.cpp
rename to Lib/src/BowVector.cpp
index d132213..f61d421 100644
--- a/src/BowVector.cpp
+++ b/Lib/src/BowVector.cpp
@@ -13,7 +13,7 @@
 #include <algorithm>
 #include <cmath>
 
-#include "BowVector.h"
+#include "DBow3/BowVector.h"
 
 namespace DBoW3 {
 
diff --git a/src/Database.cpp b/Lib/src/Database.cpp
similarity index 99%
rename from src/Database.cpp
rename to Lib/src/Database.cpp
index d8a1b81..25610a0 100644
--- a/src/Database.cpp
+++ b/Lib/src/Database.cpp
@@ -1,4 +1,4 @@
-#include "Database.h"
+#include "DBow3/Database.h"
 
 namespace DBoW3{
 
diff --git a/src/DescManip.cpp b/Lib/src/DescManip.cpp
similarity index 96%
rename from src/DescManip.cpp
rename to Lib/src/DescManip.cpp
index d6331af..430ce2b 100644
--- a/src/DescManip.cpp
+++ b/Lib/src/DescManip.cpp
@@ -1,273 +1,273 @@
-/**
- * File: DescManip.cpp
- * Date: June 2012
- * Author: Dorian Galvez-Lopez
- * Description: functions for ORB descriptors
- * License: see the LICENSE.txt file
- *
- */
- 
-#include <vector>
-#include <string>
-#include <sstream>
-#include <iostream>
-#include <stdint.h>
-#include <limits.h>
-
-#include "DescManip.h"
-
-using namespace std;
-
-namespace DBoW3 {
-
-// --------------------------------------------------------------------------
-
-void DescManip::meanValue(const std::vector<cv::Mat> &descriptors,
-                       cv::Mat &mean)
-{
-
-    if(descriptors.empty()) return;
-
-    if(descriptors.size() == 1)
-    {
-        mean = descriptors[0].clone();
-        return;
-    }
-    //binary descriptor
-    if (descriptors[0].type()==CV_8U ){
-        //determine number of bytes of the binary descriptor
-        int L= getDescSizeBytes( descriptors[0]);
-        vector<int> sum( L * 8, 0);
-
-        for(size_t i = 0; i < descriptors.size(); ++i)
-        {
-            const cv::Mat &d = descriptors[i];
-            const unsigned char *p = d.ptr<unsigned char>();
-
-            for(int j = 0; j < d.cols; ++j, ++p)
-            {
-                if(*p & (1 << 7)) ++sum[ j*8     ];
-                if(*p & (1 << 6)) ++sum[ j*8 + 1 ];
-                if(*p & (1 << 5)) ++sum[ j*8 + 2 ];
-                if(*p & (1 << 4)) ++sum[ j*8 + 3 ];
-                if(*p & (1 << 3)) ++sum[ j*8 + 4 ];
-                if(*p & (1 << 2)) ++sum[ j*8 + 5 ];
-                if(*p & (1 << 1)) ++sum[ j*8 + 6 ];
-                if(*p & (1))      ++sum[ j*8 + 7 ];
-            }
-        }
-
-        mean = cv::Mat::zeros(1, L, CV_8U);
-        unsigned char *p = mean.ptr<unsigned char>();
-
-        const int N2 = (int)descriptors.size() / 2 + descriptors.size() % 2;
-        for(size_t i = 0; i < sum.size(); ++i)
-        {
-            if(sum[i] >= N2)
-            {
-                // set bit
-                *p |= 1 << (7 - (i % 8));
-            }
-
-            if(i % 8 == 7) ++p;
-        }
-    }
-    //non binary descriptor
-    else{
-        assert(descriptors[0].type()==CV_32F );//ensure it is float
-
-        mean.create(1, descriptors[0].cols,descriptors[0].type());
-        mean.setTo(cv::Scalar::all(0));
-        float inv_s =1./double( descriptors.size());
-        for(size_t i=0;i<descriptors.size();i++)
-            mean +=  descriptors[i] * inv_s;
-
-    }
-
-}
-
-// --------------------------------------------------------------------------
-static  inline uint32_t distance_8uc1(const cv::Mat &a, const cv::Mat &b);
-
-double DescManip::distance(const cv::Mat &a,  const cv::Mat &b)
-{
-
-    //binary descriptor
-    if (a.type()==CV_8U){
-
-        // Bit count function got from:
-         // http://graphics.stanford.edu/~seander/bithacks.html#CountBitsSetKernighan
-         // This implementation assumes that a.cols (CV_8U) % sizeof(uint64_t) == 0
-
-         const uint64_t *pa, *pb;
-         pa = a.ptr<uint64_t>(); // a & b are actually CV_8U
-         pb = b.ptr<uint64_t>();
-
-         uint64_t v, ret = 0;
-         for(size_t i = 0; i < a.cols / sizeof(uint64_t); ++i, ++pa, ++pb)
-         {
-           v = *pa ^ *pb;
-           v = v - ((v >> 1) & (uint64_t)~(uint64_t)0/3);
-           v = (v & (uint64_t)~(uint64_t)0/15*3) + ((v >> 2) &
-             (uint64_t)~(uint64_t)0/15*3);
-           v = (v + (v >> 4)) & (uint64_t)~(uint64_t)0/255*15;
-           ret += (uint64_t)(v * ((uint64_t)~(uint64_t)0/255)) >>
-             (sizeof(uint64_t) - 1) * CHAR_BIT;
-         }
-
-         return ret;
-    }
-    else{
-        double sqd = 0.;
-        assert(a.type()==CV_32F);
-        assert(a.rows==1);
-        const float *a_ptr=a.ptr<float>(0);
-        const float *b_ptr=b.ptr<float>(0);
-        for(int i = 0; i < a.cols; i ++)
-            sqd += (a_ptr[i  ] - b_ptr[i  ])*(a_ptr[i  ] - b_ptr[i  ]);
-        return sqd;
-    }
-}
-
-
-
-
-// --------------------------------------------------------------------------
-  
-std::string DescManip::toString(const cv::Mat &a)
-{
-    stringstream ss;
-    //introduce a magic value to distinguish from DBOw2
-    ss<<"dbw3 ";
-    //save size and type
-
-
-    ss <<a.type()<<" "<<a.cols<<" ";
-
-    if (a.type()==CV_8U){
-        const unsigned char *p = a.ptr<unsigned char>();
-        for(int i = 0; i < a.cols; ++i, ++p)
-            ss << (int)*p << " ";
-    }else{
-
-        const float *p = a.ptr<float>();
-        for(int i = 0; i < a.cols; ++i, ++p)
-            ss <<  *p << " ";
-
-    }
-
-    return ss.str();
-}
-
-// --------------------------------------------------------------------------
-  
-void DescManip::fromString(cv::Mat &a, const std::string &s)
-{
-
-    //check if the dbow3 is present
-    string ss_aux;ss_aux.reserve(10);
-    for(size_t i=0;i<10 && i<s.size();i++)
-        ss_aux.push_back(s[i]);
-    if(ss_aux.find("dbw3")==std::string::npos){//is dbow2
-        //READ UNTIL END
-        stringstream ss(s);
-        int val;
-        vector<uchar> data;data.reserve(100);
-        while( ss>>val) data.push_back(val);
-        //copy to a
-        a.create(1,data.size(),CV_8UC1);
-        memcpy(a.ptr<char>(0),&data[0],data.size());
-    }
-    else {
-        char szSign[10];
-        int type,cols;
-        stringstream ss(s);
-        ss >> szSign >> type >> cols;
-        a.create(1,  cols, type);
-        if(type==CV_8UC1){
-            unsigned char *p = a.ptr<unsigned char>();
-            int n;
-            for(int i = 0; i <  a.cols; ++i, ++p)
-                if ( ss >> n) *p = (unsigned char)n;
-        }
-        else{
-            float *p = a.ptr<float>();
-            for(int i = 0; i <  a.cols; ++i, ++p)
-                if ( !(ss >> *p))cerr<<"Error reading. Unexpected EOF. DescManip::fromString"<<endl;
-        }
-
-    }
-
-}
-
-// --------------------------------------------------------------------------
-
-void DescManip::toMat32F(const std::vector<cv::Mat> &descriptors,
-                     cv::Mat &mat)
-{
-    if(descriptors.empty())
-    {
-        mat.release();
-        return;
-    }
-
-    if(descriptors[0].type()==CV_8UC1){
-
-        const size_t N = descriptors.size();
-        int L=getDescSizeBytes(descriptors[0]);
-        mat.create(N,  L*8, CV_32F);
-        float *p = mat.ptr<float>();
-
-        for(size_t i = 0; i < N; ++i)
-        {
-            const int C = descriptors[i].cols;
-            const unsigned char *desc = descriptors[i].ptr<unsigned char>();
-
-            for(int j = 0; j < C; ++j, p += 8)
-            {
-                p[0] = (desc[j] & (1 << 7) ? 1 : 0);
-                p[1] = (desc[j] & (1 << 6) ? 1 : 0);
-                p[2] = (desc[j] & (1 << 5) ? 1 : 0);
-                p[3] = (desc[j] & (1 << 4) ? 1 : 0);
-                p[4] = (desc[j] & (1 << 3) ? 1 : 0);
-                p[5] = (desc[j] & (1 << 2) ? 1 : 0);
-                p[6] = (desc[j] & (1 << 1) ? 1 : 0);
-                p[7] = desc[j] & (1);
-            }
-        }
-    }
-    else{
-        assert(descriptors[0].type()==CV_32F);
-        const int N = descriptors.size();
-        int L=descriptors[0].cols;
-        mat.create(N, L, CV_32F);
-        for(int i = 0; i < N; ++i)
-            memcpy(mat.ptr<float>(i),descriptors[i].ptr<float>(0),sizeof(float)*L);
-    }
-}
-
-void DescManip::toStream(const cv::Mat &m,std::ostream &str){
-    assert(m.rows==1 || m.isContinuous());
-    int type=m.type();
-    int cols=m.cols;
-    int rows=m.rows;
-    str.write((char*)&cols,sizeof(cols));
-    str.write((char*)&rows,sizeof(rows));
-    str.write((char*)&type,sizeof(type));
-    str.write((char*)m.ptr<char>(0),m.elemSize()*m.cols);
-}
-
-void DescManip::fromStream(cv::Mat &m,std::istream &str){
-    int type,cols,rows;
-    str.read((char*)&cols,sizeof(cols));
-    str.read((char*)&rows,sizeof(rows));
-    str.read((char*)&type,sizeof(type));
-    m.create(rows,cols,type);
-    str.read((char*)m.ptr<char>(0),m.elemSize()*m.cols);
-}
-
-
-// --------------------------------------------------------------------------
-
-} // namespace DBoW3
-
+/**
+ * File: DescManip.cpp
+ * Date: June 2012
+ * Author: Dorian Galvez-Lopez
+ * Description: functions for ORB descriptors
+ * License: see the LICENSE.txt file
+ *
+ */
+ 
+#include <vector>
+#include <string>
+#include <sstream>
+#include <iostream>
+#include <stdint.h>
+#include <limits.h>
+
+#include "DBow3/DescManip.h"
+
+using namespace std;
+
+namespace DBoW3 {
+
+// --------------------------------------------------------------------------
+
+void DescManip::meanValue(const std::vector<cv::Mat> &descriptors,
+                       cv::Mat &mean)
+{
+
+    if(descriptors.empty()) return;
+
+    if(descriptors.size() == 1)
+    {
+        mean = descriptors[0].clone();
+        return;
+    }
+    //binary descriptor
+    if (descriptors[0].type()==CV_8U ){
+        //determine number of bytes of the binary descriptor
+        int L= getDescSizeBytes( descriptors[0]);
+        vector<int> sum( L * 8, 0);
+
+        for(size_t i = 0; i < descriptors.size(); ++i)
+        {
+            const cv::Mat &d = descriptors[i];
+            const unsigned char *p = d.ptr<unsigned char>();
+
+            for(int j = 0; j < d.cols; ++j, ++p)
+            {
+                if(*p & (1 << 7)) ++sum[ j*8     ];
+                if(*p & (1 << 6)) ++sum[ j*8 + 1 ];
+                if(*p & (1 << 5)) ++sum[ j*8 + 2 ];
+                if(*p & (1 << 4)) ++sum[ j*8 + 3 ];
+                if(*p & (1 << 3)) ++sum[ j*8 + 4 ];
+                if(*p & (1 << 2)) ++sum[ j*8 + 5 ];
+                if(*p & (1 << 1)) ++sum[ j*8 + 6 ];
+                if(*p & (1))      ++sum[ j*8 + 7 ];
+            }
+        }
+
+        mean = cv::Mat::zeros(1, L, CV_8U);
+        unsigned char *p = mean.ptr<unsigned char>();
+
+        const int N2 = (int)descriptors.size() / 2 + descriptors.size() % 2;
+        for(size_t i = 0; i < sum.size(); ++i)
+        {
+            if(sum[i] >= N2)
+            {
+                // set bit
+                *p |= 1 << (7 - (i % 8));
+            }
+
+            if(i % 8 == 7) ++p;
+        }
+    }
+    //non binary descriptor
+    else{
+        assert(descriptors[0].type()==CV_32F );//ensure it is float
+
+        mean.create(1, descriptors[0].cols,descriptors[0].type());
+        mean.setTo(cv::Scalar::all(0));
+        float inv_s =1./double( descriptors.size());
+        for(size_t i=0;i<descriptors.size();i++)
+            mean +=  descriptors[i] * inv_s;
+
+    }
+
+}
+
+// --------------------------------------------------------------------------
+static  inline uint32_t distance_8uc1(const cv::Mat &a, const cv::Mat &b);
+
+double DescManip::distance(const cv::Mat &a,  const cv::Mat &b)
+{
+
+    //binary descriptor
+    if (a.type()==CV_8U){
+
+        // Bit count function got from:
+         // http://graphics.stanford.edu/~seander/bithacks.html#CountBitsSetKernighan
+         // This implementation assumes that a.cols (CV_8U) % sizeof(uint64_t) == 0
+
+         const uint64_t *pa, *pb;
+         pa = a.ptr<uint64_t>(); // a & b are actually CV_8U
+         pb = b.ptr<uint64_t>();
+
+         uint64_t v, ret = 0;
+         for(size_t i = 0; i < a.cols / sizeof(uint64_t); ++i, ++pa, ++pb)
+         {
+           v = *pa ^ *pb;
+           v = v - ((v >> 1) & (uint64_t)~(uint64_t)0/3);
+           v = (v & (uint64_t)~(uint64_t)0/15*3) + ((v >> 2) &
+             (uint64_t)~(uint64_t)0/15*3);
+           v = (v + (v >> 4)) & (uint64_t)~(uint64_t)0/255*15;
+           ret += (uint64_t)(v * ((uint64_t)~(uint64_t)0/255)) >>
+             (sizeof(uint64_t) - 1) * CHAR_BIT;
+         }
+
+         return ret;
+    }
+    else{
+        double sqd = 0.;
+        assert(a.type()==CV_32F);
+        assert(a.rows==1);
+        const float *a_ptr=a.ptr<float>(0);
+        const float *b_ptr=b.ptr<float>(0);
+        for(int i = 0; i < a.cols; i ++)
+            sqd += (a_ptr[i  ] - b_ptr[i  ])*(a_ptr[i  ] - b_ptr[i  ]);
+        return sqd;
+    }
+}
+
+
+
+
+// --------------------------------------------------------------------------
+  
+std::string DescManip::toString(const cv::Mat &a)
+{
+    stringstream ss;
+    //introduce a magic value to distinguish from DBOw2
+    ss<<"dbw3 ";
+    //save size and type
+
+
+    ss <<a.type()<<" "<<a.cols<<" ";
+
+    if (a.type()==CV_8U){
+        const unsigned char *p = a.ptr<unsigned char>();
+        for(int i = 0; i < a.cols; ++i, ++p)
+            ss << (int)*p << " ";
+    }else{
+
+        const float *p = a.ptr<float>();
+        for(int i = 0; i < a.cols; ++i, ++p)
+            ss <<  *p << " ";
+
+    }
+
+    return ss.str();
+}
+
+// --------------------------------------------------------------------------
+  
+void DescManip::fromString(cv::Mat &a, const std::string &s)
+{
+
+    //check if the dbow3 is present
+    string ss_aux;ss_aux.reserve(10);
+    for(size_t i=0;i<10 && i<s.size();i++)
+        ss_aux.push_back(s[i]);
+    if(ss_aux.find("dbw3")==std::string::npos){//is dbow2
+        //READ UNTIL END
+        stringstream ss(s);
+        int val;
+        vector<uchar> data;data.reserve(100);
+        while( ss>>val) data.push_back(val);
+        //copy to a
+        a.create(1,data.size(),CV_8UC1);
+        memcpy(a.ptr<char>(0),&data[0],data.size());
+    }
+    else {
+        char szSign[10];
+        int type,cols;
+        stringstream ss(s);
+        ss >> szSign >> type >> cols;
+        a.create(1,  cols, type);
+        if(type==CV_8UC1){
+            unsigned char *p = a.ptr<unsigned char>();
+            int n;
+            for(int i = 0; i <  a.cols; ++i, ++p)
+                if ( ss >> n) *p = (unsigned char)n;
+        }
+        else{
+            float *p = a.ptr<float>();
+            for(int i = 0; i <  a.cols; ++i, ++p)
+                if ( !(ss >> *p))cerr<<"Error reading. Unexpected EOF. DescManip::fromString"<<endl;
+        }
+
+    }
+
+}
+
+// --------------------------------------------------------------------------
+
+void DescManip::toMat32F(const std::vector<cv::Mat> &descriptors,
+                     cv::Mat &mat)
+{
+    if(descriptors.empty())
+    {
+        mat.release();
+        return;
+    }
+
+    if(descriptors[0].type()==CV_8UC1){
+
+        const size_t N = descriptors.size();
+        int L=getDescSizeBytes(descriptors[0]);
+        mat.create(N,  L*8, CV_32F);
+        float *p = mat.ptr<float>();
+
+        for(size_t i = 0; i < N; ++i)
+        {
+            const int C = descriptors[i].cols;
+            const unsigned char *desc = descriptors[i].ptr<unsigned char>();
+
+            for(int j = 0; j < C; ++j, p += 8)
+            {
+                p[0] = (desc[j] & (1 << 7) ? 1 : 0);
+                p[1] = (desc[j] & (1 << 6) ? 1 : 0);
+                p[2] = (desc[j] & (1 << 5) ? 1 : 0);
+                p[3] = (desc[j] & (1 << 4) ? 1 : 0);
+                p[4] = (desc[j] & (1 << 3) ? 1 : 0);
+                p[5] = (desc[j] & (1 << 2) ? 1 : 0);
+                p[6] = (desc[j] & (1 << 1) ? 1 : 0);
+                p[7] = desc[j] & (1);
+            }
+        }
+    }
+    else{
+        assert(descriptors[0].type()==CV_32F);
+        const int N = descriptors.size();
+        int L=descriptors[0].cols;
+        mat.create(N, L, CV_32F);
+        for(int i = 0; i < N; ++i)
+            memcpy(mat.ptr<float>(i),descriptors[i].ptr<float>(0),sizeof(float)*L);
+    }
+}
+
+void DescManip::toStream(const cv::Mat &m,std::ostream &str){
+    assert(m.rows==1 || m.isContinuous());
+    int type=m.type();
+    int cols=m.cols;
+    int rows=m.rows;
+    str.write((char*)&cols,sizeof(cols));
+    str.write((char*)&rows,sizeof(rows));
+    str.write((char*)&type,sizeof(type));
+    str.write((char*)m.ptr<char>(0),m.elemSize()*m.cols);
+}
+
+void DescManip::fromStream(cv::Mat &m,std::istream &str){
+    int type,cols,rows;
+    str.read((char*)&cols,sizeof(cols));
+    str.read((char*)&rows,sizeof(rows));
+    str.read((char*)&type,sizeof(type));
+    m.create(rows,cols,type);
+    str.read((char*)m.ptr<char>(0),m.elemSize()*m.cols);
+}
+
+
+// --------------------------------------------------------------------------
+
+} // namespace DBoW3
+
diff --git a/src/FeatureVector.cpp b/Lib/src/FeatureVector.cpp
similarity index 98%
rename from src/FeatureVector.cpp
rename to Lib/src/FeatureVector.cpp
index 880eab1..a9cd792 100644
--- a/src/FeatureVector.cpp
+++ b/Lib/src/FeatureVector.cpp
@@ -7,7 +7,7 @@
  *
  */
 
-#include "FeatureVector.h"
+#include "DBow3/FeatureVector.h"
 #include <map>
 #include <vector>
 #include <iostream>
diff --git a/src/QueryResults.cpp b/Lib/src/QueryResults.cpp
similarity index 97%
rename from src/QueryResults.cpp
rename to Lib/src/QueryResults.cpp
index 7062400..b852979 100644
--- a/src/QueryResults.cpp
+++ b/Lib/src/QueryResults.cpp
@@ -9,7 +9,7 @@
 
 #include <iostream>
 #include <fstream>
-#include "QueryResults.h"
+#include "DBow3/QueryResults.h"
 
 using namespace std;
 
diff --git a/src/ScoringObject.cpp b/Lib/src/ScoringObject.cpp
similarity index 99%
rename from src/ScoringObject.cpp
rename to Lib/src/ScoringObject.cpp
index 7cf0812..adde8e9 100644
--- a/src/ScoringObject.cpp
+++ b/Lib/src/ScoringObject.cpp
@@ -8,8 +8,8 @@
  */
 
 #include <cfloat>
-#include "Vocabulary.h"
-#include "BowVector.h"
+#include "DBow3/Vocabulary.h"
+#include "DBow3/BowVector.h"
 
 using namespace DBoW3;
 
diff --git a/src/Vocabulary.cpp b/Lib/src/Vocabulary.cpp
similarity index 99%
rename from src/Vocabulary.cpp
rename to Lib/src/Vocabulary.cpp
index 9f0eff3..6ccbbea 100644
--- a/src/Vocabulary.cpp
+++ b/Lib/src/Vocabulary.cpp
@@ -1,8 +1,8 @@
-#include "Vocabulary.h"
-#include "DescManip.h"
-#include "quicklz.h"
+#include "DBow3/Vocabulary.h"
+#include "DBow3/DescManip.h"
+#include "DBow3/quicklz.h"
 #include <sstream>
-#include "timers.h"
+#include "DBow3/timers.h"
 namespace DBoW3{
 // --------------------------------------------------------------------------
 
diff --git a/src/quicklz.c b/Lib/src/quicklz.c
similarity index 99%
rename from src/quicklz.c
rename to Lib/src/quicklz.c
index 3742129..bd3cb11 100644
--- a/src/quicklz.c
+++ b/Lib/src/quicklz.c
@@ -9,7 +9,7 @@
 
 // 1.5.0 final
 
-#include "quicklz.h"
+#include "DBow3/quicklz.h"
 
 #if QLZ_VERSION_MAJOR != 1 || QLZ_VERSION_MINOR != 5 || QLZ_VERSION_REVISION != 0
 	#error quicklz.c and quicklz.h have different versions
diff --git a/cmake/instructionSet.cmake b/cmake/instructionSet.cmake
new file mode 100644
index 0000000..d0febeb
--- /dev/null
+++ b/cmake/instructionSet.cmake
@@ -0,0 +1,13 @@
+add_library(
+  SSE4.1
+  INTERFACE
+)
+
+add_library(SIMD::SSE4.1 ALIAS SSE4.1)
+
+target_compile_options(
+  SSE4.1
+  INTERFACE
+  $<$<PLATFORM_ID:Linux>:-msse4.1;-march=native>
+  $<$<PLATFORM_ID:Windows>:/arch:AVX2>
+)
diff --git a/cmake_uninstall.cmake.in b/cmake_uninstall.cmake.in
deleted file mode 100644
index 81482da..0000000
--- a/cmake_uninstall.cmake.in
+++ /dev/null
@@ -1,28 +0,0 @@
-# -----------------------------------------------
-# File that provides "make uninstall" target
-#  We use the file 'install_manifest.txt'
-# -----------------------------------------------
-IF(NOT EXISTS "@CMAKE_CURRENT_BINARY_DIR@/install_manifest.txt")
-  MESSAGE(FATAL_ERROR "Cannot find install manifest: \"@CMAKE_CURRENT_BINARY_DIR@/install_manifest.txt\"")
-ENDIF(NOT EXISTS "@CMAKE_CURRENT_BINARY_DIR@/install_manifest.txt")
-
-FILE(READ "@CMAKE_CURRENT_BINARY_DIR@/install_manifest.txt" files)
-STRING(REGEX REPLACE "\n" ";" files "${files}")
-FOREACH(file ${files})
-  MESSAGE(STATUS "Uninstalling \"$ENV{DESTDIR}${file}\"")
-#  IF(EXISTS "$ENV{DESTDIR}${file}")
-#    EXEC_PROGRAM(
-#      "@CMAKE_COMMAND@" ARGS "-E remove \"$ENV{DESTDIR}${file}\""
-#      OUTPUT_VARIABLE rm_out
-#      RETURN_VALUE rm_retval
-#      )
-	EXECUTE_PROCESS(COMMAND rm $ENV{DESTDIR}${file})
-#    IF(NOT "${rm_retval}" STREQUAL 0)
-#      MESSAGE(FATAL_ERROR "Problem when removing \"$ENV{DESTDIR}${file}\"")
-#    ENDIF(NOT "${rm_retval}" STREQUAL 0)
-#  ELSE(EXISTS "$ENV{DESTDIR}${file}")
-#    MESSAGE(STATUS "File \"$ENV{DESTDIR}${file}\" does not exist.")
-#  ENDIF(EXISTS "$ENV{DESTDIR}${file}")
-ENDFOREACH(file)
-
-
diff --git a/config.cmake.in b/config.cmake.in
deleted file mode 100644
index 9d15feb..0000000
--- a/config.cmake.in
+++ /dev/null
@@ -1,36 +0,0 @@
-# ===================================================================================
-#  @PROJECT_NAME@ CMake configuration file
-#
-#             ** File generated automatically, do not modify **
-#
-#  Usage from an external project:
-#    In your CMakeLists.txt, add these lines:
-#
-#    FIND_PACKAGE(@PROJECT_NAME@ REQUIRED )
-#    TARGET_LINK_LIBRARIES(MY_TARGET_NAME ${@PROJECT_NAME@_LIBS})
-#
-#    This file will define the following variables:
-#      - @PROJECT_NAME@_LIBS          : The list of libraries to links against.
-#      - @PROJECT_NAME@_LIB_DIR       : The directory where lib files are. Calling LINK_DIRECTORIES
-#                                with this path is NOT needed.
-#      - @PROJECT_NAME@_VERSION       : The  version of this PROJECT_NAME build. Example: "1.2.0"
-#      - @PROJECT_NAME@_VERSION_MAJOR : Major version part of VERSION. Example: "1"
-#      - @PROJECT_NAME@_VERSION_MINOR : Minor version part of VERSION. Example: "2"
-#      - @PROJECT_NAME@_VERSION_PATCH : Patch version part of VERSION. Example: "0"
-#
-# ===================================================================================
-INCLUDE_DIRECTORIES("@CMAKE_INSTALL_PREFIX@/include")
-SET(@PROJECT_NAME@_INCLUDE_DIRS "@CMAKE_INSTALL_PREFIX@/include")
-
-LINK_DIRECTORIES("@CMAKE_INSTALL_PREFIX@/@LIB_INSTALL_DIR@")
-SET(@PROJECT_NAME@_LIB_DIR "@CMAKE_INSTALL_PREFIX@/@LIB_INSTALL_DIR@")
-
-SET(@PROJECT_NAME@_LIBS @REQUIRED_LIBRARIES@ @PROJECT_NAME@@PROJECT_DLLVERSION@)
-SET(@PROJECT_NAME@_LIBRARIES @REQUIRED_LIBRARIES@ @PROJECT_NAME@@PROJECT_DLLVERSION@)
-
-SET(@PROJECT_NAME@_FOUND YES)
-SET(@PROJECT_NAME@_FOUND "YES")
-SET(@PROJECT_NAME@_VERSION        @PROJECT_VERSION@)
-SET(@PROJECT_NAME@_VERSION_MAJOR  @PROJECT_VERSION_MAJOR@)
-SET(@PROJECT_NAME@_VERSION_MINOR  @PROJECT_VERSION_MINOR@)
-SET(@PROJECT_NAME@_VERSION_PATCH  @PROJECT_VERSION_PATCH@)
diff --git a/utils/CMakeLists.txt b/examples/CMakeLists.txt
similarity index 100%
rename from utils/CMakeLists.txt
rename to examples/CMakeLists.txt
diff --git a/utils/create_voc_step0.cpp b/examples/create_voc_step0.cpp
similarity index 97%
rename from utils/create_voc_step0.cpp
rename to examples/create_voc_step0.cpp
index 4fd78f6..c48a3c4 100644
--- a/utils/create_voc_step0.cpp
+++ b/examples/create_voc_step0.cpp
@@ -1,126 +1,126 @@
-
-//First step of creating a vocabulary is extracting features from a set of images. We save them to a file for next step
-#include <iostream>
-#include <vector>
-
-// DBoW3
-#include "DBoW3.h"
-
-// OpenCV
-#include <opencv2/core/core.hpp>
-#include <opencv2/highgui/highgui.hpp>
-#include <opencv2/features2d/features2d.hpp>
-#ifdef USE_CONTRIB
-#include <opencv2/xfeatures2d/nonfree.hpp>
-#include <opencv2/xfeatures2d.hpp>
-#endif
-#include "DescManip.h"
-
-using namespace DBoW3;
-using namespace std;
-
-
-//command line parser
-class CmdLineParser{int argc; char **argv; public: CmdLineParser(int _argc,char **_argv):argc(_argc),argv(_argv){}  bool operator[] ( string param ) {int idx=-1;  for ( int i=0; i<argc && idx==-1; i++ ) if ( string ( argv[i] ) ==param ) idx=i;    return ( idx!=-1 ) ;    } string operator()(string param,string defvalue="-1"){int idx=-1;    for ( int i=0; i<argc && idx==-1; i++ ) if ( string ( argv[i] ) ==param ) idx=i; if ( idx==-1 ) return defvalue;   else  return ( argv[  idx+1] ); }};
-
-
-// - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - 
-
-// extended surf gives 128-dimensional vectors
-const bool EXTENDED_SURF = false;
-// - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - 
-
-void wait()
-{
-    cout << endl << "Press enter to continue" << endl;
-    getchar();
-}
-
-
-vector<string> readImagePaths(int argc,char **argv,int start){
-    vector<string> paths;
-    for(int i=start;i<argc;i++)    paths.push_back(argv[i]);
-        return paths;
-}
-
-vector< cv::Mat  >  loadFeatures( std::vector<string> path_to_images,string descriptor="") throw (std::exception){
-    //select detector
-    cv::Ptr<cv::Feature2D> fdetector;
-    if (descriptor=="orb")        fdetector=cv::ORB::create();
-    else if (descriptor=="brisk") fdetector=cv::BRISK::create();
-#ifdef OPENCV_VERSION_3
-    else if (descriptor=="akaze") fdetector=cv::AKAZE::create();
-#endif
-#ifdef USE_CONTRIB
-    else if(descriptor=="surf" )  fdetector=cv::xfeatures2d::SURF::create(400, 4, 2, EXTENDED_SURF);
-#endif
-
-    else throw std::runtime_error("Invalid descriptor");
-    assert(!descriptor.empty());
-    vector<cv::Mat>    features;
-
-
-    cout << "Extracting   features..." << endl;
-    for(size_t i = 0; i < path_to_images.size(); ++i)
-    {
-        vector<cv::KeyPoint> keypoints;
-        cv::Mat descriptors;
-        cout<<"reading image: "<<path_to_images[i]<<endl;
-        cv::Mat image = cv::imread(path_to_images[i], 0);
-        if(image.empty())throw std::runtime_error("Could not open image"+path_to_images[i]);
-        cout<<"extracting features"<<endl;
-        fdetector->detectAndCompute(image, cv::Mat(), keypoints, descriptors);
-        features.push_back(descriptors);
-        cout<<"done detecting features"<<endl;
-    }
-    return features;
-}
-
-// ----------------------------------------------------------------------------
-void saveToFile(string filename,const vector<cv::Mat> &features){
-
-    //test it is not created
-    std::ifstream ifile(filename);
-    if (ifile.is_open()){cerr<<"ERROR::: Output File "<<filename<<" already exists!!!!!"<<endl;exit(0);}
-    std::ofstream ofile(filename);
-    if (!ofile.is_open()){cerr<<"could not open output file"<<endl;exit(0);}
-    uint32_t size=features.size();
-    ofile.write((char*)&size,sizeof(size));
-    for(auto &f:features){
-        if( !f.isContinuous()){
-            cerr<<"Matrices should be continuous"<<endl;exit(0);
-        }
-        uint32_t aux=f.cols; ofile.write( (char*)&aux,sizeof(aux));
-          aux=f.rows; ofile.write( (char*)&aux,sizeof(aux));
-          aux=f.type(); ofile.write( (char*)&aux,sizeof(aux));
-        ofile.write( (char*)f.ptr<uchar>(0),f.total()*f.elemSize());
-    }
-}
-
-// ----------------------------------------------------------------------------
-
-int main(int argc,char **argv)
-{
-
-    try{
-        CmdLineParser cml(argc,argv);
-        if (cml["-h"] || argc==1){
-            cerr<<"Usage:  descriptor_name output image0 image1 ... \n\t descriptors:brisk,surf,orb(default),akaze(only if using opencv 3)"<<endl;
-            return -1;
-        }
-
-        string descriptor=argv[1];
-        string output=argv[2];
-
-        auto images=readImagePaths(argc,argv,3);
-        vector< cv::Mat   >   features= loadFeatures(images,descriptor);
-
-      //save features to file
-    saveToFile(argv[2],features);
-
-    }catch(std::exception &ex){
-        cerr<<ex.what()<<endl;
-    }
-
-    return 0;
-}
+
+//First step of creating a vocabulary is extracting features from a set of images. We save them to a file for next step
+#include <iostream>
+#include <vector>
+
+// DBoW3
+#include "DBoW3.h"
+
+// OpenCV
+#include <opencv2/core/core.hpp>
+#include <opencv2/highgui/highgui.hpp>
+#include <opencv2/features2d/features2d.hpp>
+#ifdef USE_CONTRIB
+#include <opencv2/xfeatures2d/nonfree.hpp>
+#include <opencv2/xfeatures2d.hpp>
+#endif
+#include "DescManip.h"
+
+using namespace DBoW3;
+using namespace std;
+
+
+//command line parser
+class CmdLineParser{int argc; char **argv; public: CmdLineParser(int _argc,char **_argv):argc(_argc),argv(_argv){}  bool operator[] ( string param ) {int idx=-1;  for ( int i=0; i<argc && idx==-1; i++ ) if ( string ( argv[i] ) ==param ) idx=i;    return ( idx!=-1 ) ;    } string operator()(string param,string defvalue="-1"){int idx=-1;    for ( int i=0; i<argc && idx==-1; i++ ) if ( string ( argv[i] ) ==param ) idx=i; if ( idx==-1 ) return defvalue;   else  return ( argv[  idx+1] ); }};
+
+
+// - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - 
+
+// extended surf gives 128-dimensional vectors
+const bool EXTENDED_SURF = false;
+// - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - 
+
+void wait()
+{
+    cout << endl << "Press enter to continue" << endl;
+    getchar();
+}
+
+
+vector<string> readImagePaths(int argc,char **argv,int start){
+    vector<string> paths;
+    for(int i=start;i<argc;i++)    paths.push_back(argv[i]);
+        return paths;
+}
+
+vector< cv::Mat  >  loadFeatures( std::vector<string> path_to_images,string descriptor="") throw (std::exception){
+    //select detector
+    cv::Ptr<cv::Feature2D> fdetector;
+    if (descriptor=="orb")        fdetector=cv::ORB::create();
+    else if (descriptor=="brisk") fdetector=cv::BRISK::create();
+#ifdef OPENCV_VERSION_3
+    else if (descriptor=="akaze") fdetector=cv::AKAZE::create();
+#endif
+#ifdef USE_CONTRIB
+    else if(descriptor=="surf" )  fdetector=cv::xfeatures2d::SURF::create(400, 4, 2, EXTENDED_SURF);
+#endif
+
+    else throw std::runtime_error("Invalid descriptor");
+    assert(!descriptor.empty());
+    vector<cv::Mat>    features;
+
+
+    cout << "Extracting   features..." << endl;
+    for(size_t i = 0; i < path_to_images.size(); ++i)
+    {
+        vector<cv::KeyPoint> keypoints;
+        cv::Mat descriptors;
+        cout<<"reading image: "<<path_to_images[i]<<endl;
+        cv::Mat image = cv::imread(path_to_images[i], 0);
+        if(image.empty())throw std::runtime_error("Could not open image"+path_to_images[i]);
+        cout<<"extracting features"<<endl;
+        fdetector->detectAndCompute(image, cv::Mat(), keypoints, descriptors);
+        features.push_back(descriptors);
+        cout<<"done detecting features"<<endl;
+    }
+    return features;
+}
+
+// ----------------------------------------------------------------------------
+void saveToFile(string filename,const vector<cv::Mat> &features){
+
+    //test it is not created
+    std::ifstream ifile(filename);
+    if (ifile.is_open()){cerr<<"ERROR::: Output File "<<filename<<" already exists!!!!!"<<endl;exit(0);}
+    std::ofstream ofile(filename);
+    if (!ofile.is_open()){cerr<<"could not open output file"<<endl;exit(0);}
+    uint32_t size=features.size();
+    ofile.write((char*)&size,sizeof(size));
+    for(auto &f:features){
+        if( !f.isContinuous()){
+            cerr<<"Matrices should be continuous"<<endl;exit(0);
+        }
+        uint32_t aux=f.cols; ofile.write( (char*)&aux,sizeof(aux));
+          aux=f.rows; ofile.write( (char*)&aux,sizeof(aux));
+          aux=f.type(); ofile.write( (char*)&aux,sizeof(aux));
+        ofile.write( (char*)f.ptr<uchar>(0),f.total()*f.elemSize());
+    }
+}
+
+// ----------------------------------------------------------------------------
+
+int main(int argc,char **argv)
+{
+
+    try{
+        CmdLineParser cml(argc,argv);
+        if (cml["-h"] || argc==1){
+            cerr<<"Usage:  descriptor_name output image0 image1 ... \n\t descriptors:brisk,surf,orb(default),akaze(only if using opencv 3)"<<endl;
+            return -1;
+        }
+
+        string descriptor=argv[1];
+        string output=argv[2];
+
+        auto images=readImagePaths(argc,argv,3);
+        vector< cv::Mat   >   features= loadFeatures(images,descriptor);
+
+      //save features to file
+    saveToFile(argv[2],features);
+
+    }catch(std::exception &ex){
+        cerr<<ex.what()<<endl;
+    }
+
+    return 0;
+}
diff --git a/utils/create_voc_step1.cpp b/examples/create_voc_step1.cpp
similarity index 97%
rename from utils/create_voc_step1.cpp
rename to examples/create_voc_step1.cpp
index 8d9de7d..1f82c66 100644
--- a/utils/create_voc_step1.cpp
+++ b/examples/create_voc_step1.cpp
@@ -1,71 +1,71 @@
-//Second step,creates the vocabulary from the set of features. It can be slow
-#include <iostream>
-#include <vector>
-
-// DBoW3
-#include "DBoW3.h"
-
-// OpenCV
-#include <opencv2/core/core.hpp>
-using namespace DBoW3;
-using namespace std;
-
-//command line parser
-class CmdLineParser{int argc; char **argv; public: CmdLineParser(int _argc,char **_argv):argc(_argc),argv(_argv){}  bool operator[] ( string param ) {int idx=-1;  for ( int i=0; i<argc && idx==-1; i++ ) if ( string ( argv[i] ) ==param ) idx=i;    return ( idx!=-1 ) ;    } string operator()(string param,string defvalue="-1"){int idx=-1;    for ( int i=0; i<argc && idx==-1; i++ ) if ( string ( argv[i] ) ==param ) idx=i; if ( idx==-1 ) return defvalue;   else  return ( argv[  idx+1] ); }};
-
-// ----------------------------------------------------------------------------
-
-// ----------------------------------------------------------------------------
-vector<cv::Mat> readFeaturesFromFile(string filename){
-vector<cv::Mat> features;
-    //test it is not created
-    std::ifstream ifile(filename);
-    if (!ifile.is_open()){cerr<<"could not open input file"<<endl;exit(0);}
-    uint32_t size;
-    ifile.read((char*)&size,sizeof(size));
-    features.resize(size);
-    for(size_t i=0;i<size;i++){
-
-        uint32_t cols,rows,type;
-        ifile.read( (char*)&cols,sizeof(cols));
-        ifile.read( (char*)&rows,sizeof(rows));
-        ifile.read( (char*)&type,sizeof(type));
-        features[i].create(rows,cols,type);
-        ifile.read( (char*)features[i].ptr<uchar>(0),features[i].total()*features[i].elemSize());
-    }
-    return features;
-}
-
-// ----------------------------------------------------------------------------
-
-int main(int argc,char **argv)
-{
-
-    try{
-        CmdLineParser cml(argc,argv);
-        if (cml["-h"] || argc!=3){
-            cerr<<"Usage:  features output_voc.yml[.gz]"<<endl;
-            return -1;
-        }
-
-
-        auto features=readFeaturesFromFile(argv[1]);
-
-        const int k = 9;
-        const int L = 3;
-        const WeightingType weight = TF_IDF;
-        const ScoringType score = L1_NORM;
-        DBoW3::Vocabulary voc (k, L, weight, score);
-
-        cout << "Creating a small " << k << "^" << L << " vocabulary..." << endl;
-        voc.create(features);
-        cerr<<"Saving "<<argv[2]<<endl;
-        voc.save(argv[2]);
-
-
-    }catch(std::exception &ex){
-        cerr<<ex.what()<<endl;
-    }
-
-    return 0;
-}
+//Second step,creates the vocabulary from the set of features. It can be slow
+#include <iostream>
+#include <vector>
+
+// DBoW3
+#include "DBoW3.h"
+
+// OpenCV
+#include <opencv2/core/core.hpp>
+using namespace DBoW3;
+using namespace std;
+
+//command line parser
+class CmdLineParser{int argc; char **argv; public: CmdLineParser(int _argc,char **_argv):argc(_argc),argv(_argv){}  bool operator[] ( string param ) {int idx=-1;  for ( int i=0; i<argc && idx==-1; i++ ) if ( string ( argv[i] ) ==param ) idx=i;    return ( idx!=-1 ) ;    } string operator()(string param,string defvalue="-1"){int idx=-1;    for ( int i=0; i<argc && idx==-1; i++ ) if ( string ( argv[i] ) ==param ) idx=i; if ( idx==-1 ) return defvalue;   else  return ( argv[  idx+1] ); }};
+
+// ----------------------------------------------------------------------------
+
+// ----------------------------------------------------------------------------
+vector<cv::Mat> readFeaturesFromFile(string filename){
+vector<cv::Mat> features;
+    //test it is not created
+    std::ifstream ifile(filename);
+    if (!ifile.is_open()){cerr<<"could not open input file"<<endl;exit(0);}
+    uint32_t size;
+    ifile.read((char*)&size,sizeof(size));
+    features.resize(size);
+    for(size_t i=0;i<size;i++){
+
+        uint32_t cols,rows,type;
+        ifile.read( (char*)&cols,sizeof(cols));
+        ifile.read( (char*)&rows,sizeof(rows));
+        ifile.read( (char*)&type,sizeof(type));
+        features[i].create(rows,cols,type);
+        ifile.read( (char*)features[i].ptr<uchar>(0),features[i].total()*features[i].elemSize());
+    }
+    return features;
+}
+
+// ----------------------------------------------------------------------------
+
+int main(int argc,char **argv)
+{
+
+    try{
+        CmdLineParser cml(argc,argv);
+        if (cml["-h"] || argc!=3){
+            cerr<<"Usage:  features output_voc.yml[.gz]"<<endl;
+            return -1;
+        }
+
+
+        auto features=readFeaturesFromFile(argv[1]);
+
+        const int k = 9;
+        const int L = 3;
+        const WeightingType weight = TF_IDF;
+        const ScoringType score = L1_NORM;
+        DBoW3::Vocabulary voc (k, L, weight, score);
+
+        cout << "Creating a small " << k << "^" << L << " vocabulary..." << endl;
+        voc.create(features);
+        cerr<<"Saving "<<argv[2]<<endl;
+        voc.save(argv[2]);
+
+
+    }catch(std::exception &ex){
+        cerr<<ex.what()<<endl;
+    }
+
+    return 0;
+}
diff --git a/utils/demo_general.cpp b/examples/demo_general.cpp
similarity index 96%
rename from utils/demo_general.cpp
rename to examples/demo_general.cpp
index 63db40b..b4bc391 100644
--- a/utils/demo_general.cpp
+++ b/examples/demo_general.cpp
@@ -1,202 +1,202 @@
-/**
- * Date:  2016
- * Author: Rafael Muñoz Salinas
- * Description: demo application of DBoW3
- * License: see the LICENSE.txt file
- */
-
-#include <iostream>
-#include <vector>
-
-// DBoW3
-#include "DBoW3.h"
-
-// OpenCV
-#include <opencv2/core/core.hpp>
-#include <opencv2/highgui/highgui.hpp>
-#include <opencv2/features2d/features2d.hpp>
-#ifdef USE_CONTRIB
-#include <opencv2/xfeatures2d/nonfree.hpp>
-#include <opencv2/xfeatures2d.hpp>
-#endif
-#include "DescManip.h"
-
-using namespace DBoW3;
-using namespace std;
-
-
-//command line parser
-class CmdLineParser{int argc; char **argv; public: CmdLineParser(int _argc,char **_argv):argc(_argc),argv(_argv){}  bool operator[] ( string param ) {int idx=-1;  for ( int i=0; i<argc && idx==-1; i++ ) if ( string ( argv[i] ) ==param ) idx=i;    return ( idx!=-1 ) ;    } string operator()(string param,string defvalue="-1"){int idx=-1;    for ( int i=0; i<argc && idx==-1; i++ ) if ( string ( argv[i] ) ==param ) idx=i; if ( idx==-1 ) return defvalue;   else  return ( argv[  idx+1] ); }};
-
-
-// - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - 
-
-// extended surf gives 128-dimensional vectors
-const bool EXTENDED_SURF = false;
-// - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - 
-
-void wait()
-{
-    cout << endl << "Press enter to continue" << endl;
-    getchar();
-}
-
-
-vector<string> readImagePaths(int argc,char **argv,int start){
-    vector<string> paths;
-    for(int i=start;i<argc;i++)    paths.push_back(argv[i]);
-        return paths;
-}
-
-vector< cv::Mat  >  loadFeatures( std::vector<string> path_to_images,string descriptor="") throw (std::exception){
-    //select detector
-    cv::Ptr<cv::Feature2D> fdetector;
-    if (descriptor=="orb")        fdetector=cv::ORB::create();
-    else if (descriptor=="brisk") fdetector=cv::BRISK::create();
-#ifdef OPENCV_VERSION_3
-    else if (descriptor=="akaze") fdetector=cv::AKAZE::create();
-#endif
-#ifdef USE_CONTRIB
-    else if(descriptor=="surf" )  fdetector=cv::xfeatures2d::SURF::create(400, 4, 2, EXTENDED_SURF);
-#endif
-
-    else throw std::runtime_error("Invalid descriptor");
-    assert(!descriptor.empty());
-    vector<cv::Mat>    features;
-
-
-    cout << "Extracting   features..." << endl;
-    for(size_t i = 0; i < path_to_images.size(); ++i)
-    {
-        vector<cv::KeyPoint> keypoints;
-        cv::Mat descriptors;
-        cout<<"reading image: "<<path_to_images[i]<<endl;
-        cv::Mat image = cv::imread(path_to_images[i], 0);
-        if(image.empty())throw std::runtime_error("Could not open image"+path_to_images[i]);
-        cout<<"extracting features"<<endl;
-        fdetector->detectAndCompute(image, cv::Mat(), keypoints, descriptors);
-        features.push_back(descriptors);
-        cout<<"done detecting features"<<endl;
-    }
-    return features;
-}
-
-// ----------------------------------------------------------------------------
-
-void testVocCreation(const vector<cv::Mat> &features)
-{
-    // branching factor and depth levels
-    const int k = 9;
-    const int L = 3;
-    const WeightingType weight = TF_IDF;
-    const ScoringType score = L1_NORM;
-
-    DBoW3::Vocabulary voc(k, L, weight, score);
-
-    cout << "Creating a small " << k << "^" << L << " vocabulary..." << endl;
-    voc.create(features);
-    cout << "... done!" << endl;
-
-    cout << "Vocabulary information: " << endl
-         << voc << endl << endl;
-
-    // lets do something with this vocabulary
-    cout << "Matching images against themselves (0 low, 1 high): " << endl;
-    BowVector v1, v2;
-    for(size_t i = 0; i < features.size(); i++)
-    {
-        voc.transform(features[i], v1);
-        for(size_t j = 0; j < features.size(); j++)
-        {
-            voc.transform(features[j], v2);
-
-            double score = voc.score(v1, v2);
-            cout << "Image " << i << " vs Image " << j << ": " << score << endl;
-        }
-    }
-
-    // save the vocabulary to disk
-    cout << endl << "Saving vocabulary..." << endl;
-    voc.save("small_voc.yml.gz");
-    cout << "Done" << endl;
-}
-
-////// ----------------------------------------------------------------------------
-
-void testDatabase(const  vector<cv::Mat > &features)
-{
-    cout << "Creating a small database..." << endl;
-
-    // load the vocabulary from disk
-    Vocabulary voc("small_voc.yml.gz");
-
-    Database db(voc, false, 0); // false = do not use direct index
-    // (so ignore the last param)
-    // The direct index is useful if we want to retrieve the features that
-    // belong to some vocabulary node.
-    // db creates a copy of the vocabulary, we may get rid of "voc" now
-
-    // add images to the database
-    for(size_t i = 0; i < features.size(); i++)
-        db.add(features[i]);
-
-    cout << "... done!" << endl;
-
-    cout << "Database information: " << endl << db << endl;
-
-    // and query the database
-    cout << "Querying the database: " << endl;
-
-    QueryResults ret;
-    for(size_t i = 0; i < features.size(); i++)
-    {
-        db.query(features[i], ret, 4);
-
-        // ret[0] is always the same image in this case, because we added it to the
-        // database. ret[1] is the second best match.
-
-        cout << "Searching for Image " << i << ". " << ret << endl;
-    }
-
-    cout << endl;
-
-    // we can save the database. The created file includes the vocabulary
-    // and the entries added
-    cout << "Saving database..." << endl;
-    db.save("small_db.yml.gz");
-    cout << "... done!" << endl;
-
-    // once saved, we can load it again
-    cout << "Retrieving database once again..." << endl;
-    Database db2("small_db.yml.gz");
-    cout << "... done! This is: " << endl << db2 << endl;
-}
-
-
-// ----------------------------------------------------------------------------
-
-int main(int argc,char **argv)
-{
-
-    try{
-        CmdLineParser cml(argc,argv);
-        if (cml["-h"] || argc<=2){
-            cerr<<"Usage:  descriptor_name     image0 image1 ... \n\t descriptors:brisk,surf,orb ,akaze(only if using opencv 3)"<<endl;
-             return -1;
-        }
-
-        string descriptor=argv[1];
-
-        auto images=readImagePaths(argc,argv,2);
-        vector< cv::Mat   >   features= loadFeatures(images,descriptor);
-        testVocCreation(features);
-
-
-        testDatabase(features);
-
-    }catch(std::exception &ex){
-        cerr<<ex.what()<<endl;
-    }
-
-    return 0;
-}
+/**
+ * Date:  2016
+ * Author: Rafael Muñoz Salinas
+ * Description: demo application of DBoW3
+ * License: see the LICENSE.txt file
+ */
+
+#include <iostream>
+#include <vector>
+
+// DBoW3
+#include "DBoW3.h"
+
+// OpenCV
+#include <opencv2/core/core.hpp>
+#include <opencv2/highgui/highgui.hpp>
+#include <opencv2/features2d/features2d.hpp>
+#ifdef USE_CONTRIB
+#include <opencv2/xfeatures2d/nonfree.hpp>
+#include <opencv2/xfeatures2d.hpp>
+#endif
+#include "DescManip.h"
+
+using namespace DBoW3;
+using namespace std;
+
+
+//command line parser
+class CmdLineParser{int argc; char **argv; public: CmdLineParser(int _argc,char **_argv):argc(_argc),argv(_argv){}  bool operator[] ( string param ) {int idx=-1;  for ( int i=0; i<argc && idx==-1; i++ ) if ( string ( argv[i] ) ==param ) idx=i;    return ( idx!=-1 ) ;    } string operator()(string param,string defvalue="-1"){int idx=-1;    for ( int i=0; i<argc && idx==-1; i++ ) if ( string ( argv[i] ) ==param ) idx=i; if ( idx==-1 ) return defvalue;   else  return ( argv[  idx+1] ); }};
+
+
+// - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - 
+
+// extended surf gives 128-dimensional vectors
+const bool EXTENDED_SURF = false;
+// - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - 
+
+void wait()
+{
+    cout << endl << "Press enter to continue" << endl;
+    getchar();
+}
+
+
+vector<string> readImagePaths(int argc,char **argv,int start){
+    vector<string> paths;
+    for(int i=start;i<argc;i++)    paths.push_back(argv[i]);
+        return paths;
+}
+
+vector< cv::Mat  >  loadFeatures( std::vector<string> path_to_images,string descriptor="") throw (std::exception){
+    //select detector
+    cv::Ptr<cv::Feature2D> fdetector;
+    if (descriptor=="orb")        fdetector=cv::ORB::create();
+    else if (descriptor=="brisk") fdetector=cv::BRISK::create();
+#ifdef OPENCV_VERSION_3
+    else if (descriptor=="akaze") fdetector=cv::AKAZE::create();
+#endif
+#ifdef USE_CONTRIB
+    else if(descriptor=="surf" )  fdetector=cv::xfeatures2d::SURF::create(400, 4, 2, EXTENDED_SURF);
+#endif
+
+    else throw std::runtime_error("Invalid descriptor");
+    assert(!descriptor.empty());
+    vector<cv::Mat>    features;
+
+
+    cout << "Extracting   features..." << endl;
+    for(size_t i = 0; i < path_to_images.size(); ++i)
+    {
+        vector<cv::KeyPoint> keypoints;
+        cv::Mat descriptors;
+        cout<<"reading image: "<<path_to_images[i]<<endl;
+        cv::Mat image = cv::imread(path_to_images[i], 0);
+        if(image.empty())throw std::runtime_error("Could not open image"+path_to_images[i]);
+        cout<<"extracting features"<<endl;
+        fdetector->detectAndCompute(image, cv::Mat(), keypoints, descriptors);
+        features.push_back(descriptors);
+        cout<<"done detecting features"<<endl;
+    }
+    return features;
+}
+
+// ----------------------------------------------------------------------------
+
+void testVocCreation(const vector<cv::Mat> &features)
+{
+    // branching factor and depth levels
+    const int k = 9;
+    const int L = 3;
+    const WeightingType weight = TF_IDF;
+    const ScoringType score = L1_NORM;
+
+    DBoW3::Vocabulary voc(k, L, weight, score);
+
+    cout << "Creating a small " << k << "^" << L << " vocabulary..." << endl;
+    voc.create(features);
+    cout << "... done!" << endl;
+
+    cout << "Vocabulary information: " << endl
+         << voc << endl << endl;
+
+    // lets do something with this vocabulary
+    cout << "Matching images against themselves (0 low, 1 high): " << endl;
+    BowVector v1, v2;
+    for(size_t i = 0; i < features.size(); i++)
+    {
+        voc.transform(features[i], v1);
+        for(size_t j = 0; j < features.size(); j++)
+        {
+            voc.transform(features[j], v2);
+
+            double score = voc.score(v1, v2);
+            cout << "Image " << i << " vs Image " << j << ": " << score << endl;
+        }
+    }
+
+    // save the vocabulary to disk
+    cout << endl << "Saving vocabulary..." << endl;
+    voc.save("small_voc.yml.gz");
+    cout << "Done" << endl;
+}
+
+////// ----------------------------------------------------------------------------
+
+void testDatabase(const  vector<cv::Mat > &features)
+{
+    cout << "Creating a small database..." << endl;
+
+    // load the vocabulary from disk
+    Vocabulary voc("small_voc.yml.gz");
+
+    Database db(voc, false, 0); // false = do not use direct index
+    // (so ignore the last param)
+    // The direct index is useful if we want to retrieve the features that
+    // belong to some vocabulary node.
+    // db creates a copy of the vocabulary, we may get rid of "voc" now
+
+    // add images to the database
+    for(size_t i = 0; i < features.size(); i++)
+        db.add(features[i]);
+
+    cout << "... done!" << endl;
+
+    cout << "Database information: " << endl << db << endl;
+
+    // and query the database
+    cout << "Querying the database: " << endl;
+
+    QueryResults ret;
+    for(size_t i = 0; i < features.size(); i++)
+    {
+        db.query(features[i], ret, 4);
+
+        // ret[0] is always the same image in this case, because we added it to the
+        // database. ret[1] is the second best match.
+
+        cout << "Searching for Image " << i << ". " << ret << endl;
+    }
+
+    cout << endl;
+
+    // we can save the database. The created file includes the vocabulary
+    // and the entries added
+    cout << "Saving database..." << endl;
+    db.save("small_db.yml.gz");
+    cout << "... done!" << endl;
+
+    // once saved, we can load it again
+    cout << "Retrieving database once again..." << endl;
+    Database db2("small_db.yml.gz");
+    cout << "... done! This is: " << endl << db2 << endl;
+}
+
+
+// ----------------------------------------------------------------------------
+
+int main(int argc,char **argv)
+{
+
+    try{
+        CmdLineParser cml(argc,argv);
+        if (cml["-h"] || argc<=2){
+            cerr<<"Usage:  descriptor_name     image0 image1 ... \n\t descriptors:brisk,surf,orb ,akaze(only if using opencv 3)"<<endl;
+             return -1;
+        }
+
+        string descriptor=argv[1];
+
+        auto images=readImagePaths(argc,argv,2);
+        vector< cv::Mat   >   features= loadFeatures(images,descriptor);
+        testVocCreation(features);
+
+
+        testDatabase(features);
+
+    }catch(std::exception &ex){
+        cerr<<ex.what()<<endl;
+    }
+
+    return 0;
+}
diff --git a/utils/images/image0.png b/examples/images/image0.png
similarity index 100%
rename from utils/images/image0.png
rename to examples/images/image0.png
diff --git a/utils/images/image1.png b/examples/images/image1.png
similarity index 100%
rename from utils/images/image1.png
rename to examples/images/image1.png
diff --git a/utils/images/image2.png b/examples/images/image2.png
similarity index 100%
rename from utils/images/image2.png
rename to examples/images/image2.png
diff --git a/utils/images/image3.png b/examples/images/image3.png
similarity index 100%
rename from utils/images/image3.png
rename to examples/images/image3.png
diff --git a/src/CMakeLists.txt b/src/CMakeLists.txt
deleted file mode 100644
index a9c52b4..0000000
--- a/src/CMakeLists.txt
+++ /dev/null
@@ -1,35 +0,0 @@
-INCLUDE_DIRECTORIES(. ./utils/)
-
-FILE(GLOB hdrs_base "*.h" )
-FILE(GLOB srcs_base "*.c*")
-
-FILE(GLOB hdrs ${hdrs_base}  )
-FILE(GLOB srcs  ${srcs_base} )
-
-
-ADD_LIBRARY(${PROJECT_NAME} ${srcs} ${hdrs})
-INCLUDE_DIRECTORIES(${CMAKE_CURRENT_BINARY_DIR} ) 
-
-SET_TARGET_PROPERTIES(${PROJECT_NAME} PROPERTIES 				# create *nix style library versions + symbolic links
-        DEFINE_SYMBOL DBOW_DSO_EXPORTS
-	VERSION ${PROJECT_VERSION}
-	SOVERSION ${PROJECT_SOVERSION}
-	CLEAN_DIRECT_OUTPUT 1 					# allow creating static and shared libs without conflicts
-	OUTPUT_NAME "${PROJECT_NAME}${PROJECT_DLLVERSION}" 	# avoid conflicts between library and binary target names
-)
-
-TARGET_LINK_LIBRARIES(${PROJECT_NAME} ${REQUIRED_LIBRARIES} )
-
-INSTALL(TARGETS ${PROJECT_NAME}
-	RUNTIME DESTINATION bin COMPONENT main			# Install the dll file in bin directory
-	LIBRARY DESTINATION ${LIB_INSTALL_DIR} PERMISSIONS OWNER_READ OWNER_WRITE OWNER_EXECUTE GROUP_READ GROUP_EXECUTE WORLD_READ WORLD_EXECUTE COMPONENT main
-        ARCHIVE DESTINATION ${LIB_INSTALL_DIR} COMPONENT main)			# Install the dll.a file in lib directory
-
-
-
-INSTALL(FILES ${hdrs_base}
-        DESTINATION include/${PROJECT_NAME}
-        COMPONENT main)
-
-
-
diff --git a/orbvoc.dbow3 b/voc/orbvoc.dbow3
similarity index 100%
rename from orbvoc.dbow3
rename to voc/orbvoc.dbow3

From 9f9a9125423edd0b2cc7c84897d4387c5dcea44b Mon Sep 17 00:00:00 2001
From: Ramadan <<EMAIL>>
Date: Thu, 15 Aug 2019 14:00:28 +0200
Subject: [PATCH 2/2] Fix DBow3Config.cmake

---
 Lib/DBow3Config.cmake | 2 +-
 1 file changed, 1 insertion(+), 1 deletion(-)

diff --git a/Lib/DBow3Config.cmake b/Lib/DBow3Config.cmake
index 9720a14..da63d5e 100644
--- a/Lib/DBow3Config.cmake
+++ b/Lib/DBow3Config.cmake
@@ -1,4 +1,4 @@
 include(CMakeFindDependencyMacro)
 find_dependency(OpenCV)
 find_dependency(OpenMP)
-include("${CMAKE_CURRENT_LIST_DIR}/FbowTargets.cmake")
\ No newline at end of file
+include("${CMAKE_CURRENT_LIST_DIR}/DBow3Targets.cmake")
\ No newline at end of file
