diff --git a/lib/CMakeLists.txt b/lib/CMakeLists.txt
index 14d5e85..cb7232f 100644
--- a/lib/CMakeLists.txt
+++ b/lib/CMakeLists.txt
@@ -30,19 +30,27 @@ if (GAINPUT_BUILD_SHARED)
 endif (GAINPUT_BUILD_SHARED)
 
 if (GAINPUT_BUILD_STATIC)
-  message(STATUS "..Building shared libraries (-DGAINPUT_BUILD_STATIC=OFF to disable)")
+  message(STATUS "..Building static libraries (-DGAINPUT_BUILD_STATIC=OFF to disable)")
   add_library(gainputstatic STATIC ${sources} ${mmsources})
   set_target_properties(gainputstatic PROPERTIES DEBUG_POSTFIX -d FOLDER gainput)
   set(install_libs ${install_libs} gainputstatic)
 endif (GAINPUT_BUILD_STATIC)
 
 if(WIN32)
-	target_link_libraries(gainput ${XINPUT} ws2_32)
-	target_link_libraries(gainputstatic ${XINPUT} ws2_32)
+  if (GAINPUT_BUILD_SHARED)
+    target_link_libraries(gainput ${XINPUT} ws2_32)
+  endif(GAINPUT_BUILD_SHARED)
+  if(GAINPUT_BUILD_STATIC)
+    target_link_libraries(gainputstatic ${XINPUT} ws2_32)
+  endif(GAINPUT_BUILD_STATIC)
 	add_definitions(-DGAINPUT_LIB_DYNAMIC=1)
 elseif(ANDROID)
-	target_link_libraries(gainputstatic native_app_glue log android)
-	target_link_libraries(gainput native_app_glue log android)
+  if (GAINPUT_BUILD_SHARED)
+    target_link_libraries(gainput native_app_glue log android)
+  endif(GAINPUT_BUILD_SHARED)
+  if(GAINPUT_BUILD_STATIC)
+    target_link_libraries(gainputstatic native_app_glue log android)
+  endif(GAINPUT_BUILD_STATIC)
 elseif(APPLE)
 	find_library(FOUNDATION Foundation)
 	find_library(IOKIT IOKit)
@@ -65,6 +73,14 @@ if(NOT DEFINED CMAKE_INSTALL_LIBDIR)
 endif(NOT DEFINED CMAKE_INSTALL_LIBDIR)
 set(libdir ${CMAKE_INSTALL_PREFIX}/${CMAKE_INSTALL_LIBDIR})
 
+foreach(t ${install_libs})
+    target_include_directories(
+        ${t}
+        INTERFACE $<BUILD_INTERFACE:${PROJECT_SOURCE_DIR}/include>
+                  $<INSTALL_INTERFACE:include>
+    )
+endforeach()
+
 install(
     DIRECTORY "include/gainput"
     DESTINATION "include"
@@ -73,7 +89,15 @@ install(
 
 install(
     TARGETS ${install_libs}
+    EXPORT  unofficial-${PROJECT_NAME}-Targets
     LIBRARY DESTINATION "${libdir}"
     ARCHIVE DESTINATION "${libdir}"
     RUNTIME DESTINATION "bin"
 )
+
+install(
+    EXPORT unofficial-${PROJECT_NAME}-Targets
+    FILE unofficial-${PROJECT_NAME}Config.cmake
+    NAMESPACE unofficial::${PROJECT_NAME}::
+    DESTINATION share/unofficial-${PROJECT_NAME}
+)
\ No newline at end of file
