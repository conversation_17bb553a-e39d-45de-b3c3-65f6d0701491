diff --git a/CMakeLists.txt b/CMakeLists.txt
index b666eec..66c234d 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -63,6 +63,16 @@ endif()
 
 include(GNUInstallDirs)
 
+# install protobuf files
+install(FILES ${CMAKE_CURRENT_BINARY_DIR}/onnx/onnx-data.proto
+              ${CMAKE_CURRENT_BINARY_DIR}/onnx/onnx-data.proto3
+              ${CMAKE_CURRENT_BINARY_DIR}/onnx/onnx-ml.proto
+              ${CMAKE_CURRENT_BINARY_DIR}/onnx/onnx-ml.proto3
+              ${CMAKE_CURRENT_BINARY_DIR}/onnx/onnx-operators-ml.proto
+              ${CMAKE_CURRENT_BINARY_DIR}/onnx/onnx-operators-ml.proto3
+        DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}/onnx
+)
+
 set(ONNX_ROOT ${PROJECT_SOURCE_DIR})
 
 # Read ONNX version
@@ -104,7 +114,8 @@ endif()
 # find_package Python has replaced PythonInterp and PythonLibs since cmake 3.12
 # Use the following command in the future; now this is only compatible with the latest pybind11
 # find_package(Python ${PY_VERSION} COMPONENTS Interpreter Development REQUIRED)
-find_package(PythonInterp ${PY_VERSION} REQUIRED)
+find_package(Python3 ${PY_VERSION} COMPONENTS Interpreter REQUIRED)
+set(PYTHON_EXECUTABLE ${Python3_EXECUTABLE})
 if(BUILD_ONNX_PYTHON)
   find_package(PythonLibs ${PY_VERSION})
 endif()
