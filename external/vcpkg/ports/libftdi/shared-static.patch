diff --git a/src/CMakeLists.txt b/src/CMakeLists.txt
index 071ae90..f91f6f7 100644
--- a/src/CMakeLists.txt
+++ b/src/CMakeLists.txt
@@ -4,7 +4,7 @@ include_directories(BEFORE ${CMAKE_CURRENT_BINARY_DIR}
                      )
 
 # Targets
-set(c_sources     ftdi.c)
+set(c_sources     ftdi.c exports.def)
 set(c_headers     ftdi.h)
 
 add_library(ftdi SHARED ${c_sources})
@@ -14,7 +14,7 @@ set_target_properties(ftdi PROPERTIES VERSION ${VERSION_FIXUP}.${MINOR_VERSION}.
 
 # Static library
 add_library(ftdi-static STATIC ${c_sources})
-set_target_properties(ftdi-static PROPERTIES OUTPUT_NAME "ftdi")
+set_target_properties(ftdi-static PROPERTIES OUTPUT_NAME "ftdi-static")
 
 # Prevent clobbering each other during the build
 set_target_properties(ftdi PROPERTIES CLEAN_DIRECT_OUTPUT 1)
@@ -24,7 +24,7 @@ set_target_properties(ftdi-static PROPERTIES CLEAN_DIRECT_OUTPUT 1)
 target_link_libraries(ftdi ${LIBUSB_LIBRARIES})
 
 # Install
-if(${UNIX})
+if(UNIX AND 0)
 
    install( TARGETS ftdi
             LIBRARY DESTINATION lib${LIB_SUFFIX}
@@ -41,23 +41,29 @@ if(${UNIX})
             COMPONENT headers
             )
 
-endif(${UNIX})
+endif()
 
-if(${WIN32})
+if(WIN32 OR 1)
 
+   if(BUILD_SHARED_LIBS)
    install( TARGETS ftdi
-            DESTINATION bin
+            EXPORT ftdi
             COMPONENT sharedlibs
             )
 
+   target_include_directories(ftdi PUBLIC $<INSTALL_INTERFACE:include>)
+   else()
    install( TARGETS ftdi-static
-            DESTINATION bin
+            EXPORT ftdi
             COMPONENT staticlibs
             )
+   target_include_directories(ftdi-static PUBLIC $<INSTALL_INTERFACE:include>)
+   endif()
+   install(EXPORT ftdi FILE libftdi-config.cmake NAMESPACE libftdi:: DESTINATION share/libftdi)
 
    install( FILES ${c_headers}
             DESTINATION include
             COMPONENT headers
             )
 
-endif(${WIN32})
+endif()
