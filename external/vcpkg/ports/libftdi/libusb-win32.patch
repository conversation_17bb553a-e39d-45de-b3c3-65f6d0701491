diff --git a/FindUSB.cmake b/FindUSB.cmake
index 566cdd8..0710345 100644
--- a/FindUSB.cmake
+++ b/FindUSB.cmake
@@ -24,10 +24,10 @@ else (LIBUSB_INCLUDE_DIR AND LIBUSB_LIBRARIES)
     pkg_check_modules(PC_LIBUSB libusb)
   ENDIF(NOT WIN32)
 
-  FIND_PATH(LIBUSB_INCLUDE_DIR usb.h
+  FIND_PATH(LIBUSB_INCLUDE_DIR lusb0_usb.h
     PATHS ${PC_LIBUSB_INCLUDEDIR} ${PC_LIBUSB_INCLUDE_DIRS})
 
-  FIND_LIBRARY(LIBUSB_LIBRARIES NAMES usb 
+  FIND_LIBRARY(LIBUSB_LIBRARIES NAMES usb0 libusb0
     PATHS ${PC_LIBUSB_LIBDIR} ${PC_LIBUSB_LIBRARY_DIRS})
 
   include(FindPackageHandleStandardArgs)
diff --git a/src/ftdi.c b/src/ftdi.c
index 980622d..8f6ab67 100644
--- a/src/ftdi.c
+++ b/src/ftdi.c
@@ -28,7 +28,7 @@
 /** \addtogroup libftdi */
 /* @{ */
 
-#include <usb.h>
+#include <lusb0_usb.h>
 #include <string.h>
 #include <errno.h>
 #include <stdio.h>
diff --git a/src/ftdi.h b/src/ftdi.h
index 6bb7a3e..d4472c2 100644
--- a/src/ftdi.h
+++ b/src/ftdi.h
@@ -17,7 +17,7 @@
 #ifndef __libftdi_h__
 #define __libftdi_h__
 
-#include <usb.h>
+#include <lusb0_usb.h>
 
 #define FTDI_DEFAULT_EEPROM_SIZE 128
 
