diff --git a/CMakeLists.txt b/CMakeLists.txt
index bde25da..e1129db 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -2,19 +2,29 @@ cmake_minimum_required(VERSION 3.5)
 
 project(miniply LANGUAGES CXX)
 
+include(GNUInstallDirs)
+
 set(CMAKE_CXX_STANDARD 11)
 set(CMAKE_CXX_STANDARD_REQUIRED ON)
 
-include_directories(.)
+add_library(miniply miniply.cpp)
+
+target_include_directories(miniply
+        PUBLIC
+        $<INSTALL_INTERFACE:include>
+        $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}>)
+
+install(FILES ${CMAKE_SOURCE_DIR}/miniply.h
+        DESTINATION ${CMAKE_INSTALL_INCLUDEDIR})
+
+install(TARGETS miniply EXPORT miniply-config
+        RUNTIME DESTINATION bin
+        ARCHIVE DESTINATION lib
+        LIBRARY DESTINATION lib
+        INCLUDES DESTINATION include)
 
-add_executable(miniply-perf
-  miniply.cpp
-  miniply.h
-  extra/miniply-perf.cpp
-)
+install(EXPORT miniply-config
+	FILE unofficial-miniply-config.cmake
+	NAMESPACE unofficial::miniply::
+	DESTINATION share/unofficial-miniply)
 
-add_executable(miniply-info
-  miniply.cpp
-  miniply.h
-  extra/miniply-info.cpp
-)
