vcpkg_from_github(
	OUT_SOURCE_PATH SOURCE_PATH
	REPO niXman/yas
	REF 7.1.0
	SHA512 1101BBE0B11FF8FA3B40B1E3030E5E93125FEDC85A90532466C9E6E0708B1C4C38821C86FCAFE153717B66B7107FCB29D0E13E87E68BF2217948A7014FC3BAC0
	HEAD_REF master
)

file(INSTALL "${SOURCE_PATH}/include/yas" DESTINATION "${CURRENT_PACKAGES_DIR}/include")

file(INSTALL "${SOURCE_PATH}/include/yas/version.hpp" DESTINATION "${CURRENT_PACKAGES_DIR}/share/${PORT}" RENAME copyright)
