{"name": "libproxy", "version": "0.4.18", "port-version": 3, "description": "libproxy is a library that provides automatic proxy configuration management.", "homepage": "https://github.com/libproxy/libproxy", "license": "LGPL-2.1-only", "supports": "!(uwp | xbox | android)", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"bindings-csharp": {"description": "Install C# bindings"}, "bindings-perl": {"description": "Install PERL bindings"}, "bindings-python": {"description": "Install Python bindings"}, "bindings-ruby": {"description": "Install Ruby bindings"}, "bindings-vala": {"description": "Install Vala bindings"}, "tests": {"description": "Build libproxy tests"}, "tools": {"description": "build tools"}}}