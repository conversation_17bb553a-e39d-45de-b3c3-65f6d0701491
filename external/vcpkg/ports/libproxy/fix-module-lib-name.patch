diff --git a/libproxy/Findlibproxy.cmake.in b/libproxy/Findlibproxy.cmake.in
index ef44489..c0bd2ae 100644
--- a/libproxy/Findlibproxy.cmake.in
+++ b/libproxy/Findlibproxy.cmake.in
@@ -12,7 +12,7 @@
 
 # Find proxy.h and the corresponding library (libproxy.so)
 FIND_PATH(LIBPROXY_INCLUDE_DIR proxy.h )
-FIND_LIBRARY(LIBPROXY_LIBRARIES NAMES proxy )
+FIND_LIBRARY(LIBPROXY_LIBRARIES NAMES proxy libproxy)
 
 # Set library version
 SET(LIBPROXY_VERSION @PROJECT_VERSION@)
