diff --git a/CMakeLists.txt b/CMakeLists.txt
index 2b7b03d6..1228484f 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -2,6 +2,7 @@ cmake_minimum_required(VERSION 3.5)
 list(APPEND CMAKE_MODULE_PATH ${CMAKE_CURRENT_SOURCE_DIR}/cmake)
 project(boost_text LANGUAGES CXX)
 
+include(CMakePackageConfigHelpers)
 ################################################## 
 # C++ standard version selection
 ################################################## 
@@ -85,7 +86,7 @@ target_include_directories(text
   PUBLIC
   $<INSTALL_INTERFACE:include>
   $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>)
-target_include_directories(text PRIVATE ${Boost_INCLUDE_DIR})
+target_link_libraries(text PRIVATE Boost::boost)
 set_property(TARGET text PROPERTY CXX_STANDARD ${CXX_STD})
 target_compile_options(text PRIVATE -DBOOST_TEXT_SOURCE)
 if (link_flags)
@@ -106,13 +107,22 @@ install(EXPORT text_export
   NAMESPACE Boost::
   DESTINATION lib/cmake/text)
 
+
+configure_package_config_file("${CMAKE_CURRENT_SOURCE_DIR}/cmake/text-config.cmake.in"
+  "${CMAKE_CURRENT_BINARY_DIR}/cmake/text-config.cmake"
+  INSTALL_DESTINATION "lib/cmake/text"
+  NO_SET_AND_CHECK_MACRO
+  NO_CHECK_REQUIRED_COMPONENTS_MACRO
+  )
+
 install(FILES
-  ${CMAKE_CURRENT_SOURCE_DIR}/cmake/text-config.cmake
+  ${CMAKE_CURRENT_BINARY_DIR}/cmake/text-config.cmake
   DESTINATION "lib/cmake/text")
 
 install(DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/include/boost
   DESTINATION "include")
 
+if(0)
 # If Boost was not found, make text depend on boost_clone, so that we clone
 # Boost from Github repos.  However, we don't want a permanent text ->
 # boost_clone dependency, so make this CMakeLists.txt file dirty by touching
@@ -140,4 +150,5 @@ endif ()
 add_subdirectory(test)
 add_subdirectory(perf)
 add_subdirectory(example)
+endif()
 
diff --git a/cmake/dependencies.cmake b/cmake/dependencies.cmake
index 1e25c825..bcec1bc5 100644
--- a/cmake/dependencies.cmake
+++ b/cmake/dependencies.cmake
@@ -7,7 +7,8 @@
 # Boost
 ###############################################################################
 set(Boost_USE_STATIC_LIBS ON)
-find_package(Boost 1.71.0 COMPONENTS ${boost_components})
+find_package(Boost REQUIRED COMPONENTS ${boost_components})
+if(0)
 if (Boost_INCLUDE_DIR)
   add_library(boost INTERFACE)
   target_include_directories(boost INTERFACE ${Boost_INCLUDE_DIR})
@@ -108,3 +109,4 @@ if (MSVC)
 else()
     find_package(ICU 60 COMPONENTS i18n data uc)
 endif()
+endif()
diff --git a/cmake/text-config.cmake b/cmake/text-config.cmake.in
similarity index 70%
rename from cmake/text-config.cmake
rename to cmake/text-config.cmake.in
index b7071ff5..b9c8be91 100644
--- a/cmake/text-config.cmake
+++ b/cmake/text-config.cmake.in
@@ -1,2 +1,4 @@
+@PACKAGE_INIT@
 include(CMakeFindDependencyMacro)
+find_dependency(Boost)
 include("${CMAKE_CURRENT_LIST_DIR}/text-targets.cmake")
