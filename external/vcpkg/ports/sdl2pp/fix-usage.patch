diff --git a/FindSDL2PP.cmake.in b/FindSDL2PP.cmake.in
index 344d2b2..06d6278 100644
--- a/FindSDL2PP.cmake.in
+++ b/FindSDL2PP.cmake.in
@@ -6,6 +6,18 @@
 #  SDL2PP_INCLUDE_DIRS
 #  SDL2PP_LIBRARIES
 
+include(CMakeFindDependencyMacro)
+find_dependency(SDL2 CONFIG)
+IF(@SDL2PP_WITH_IMAGE@)
+  find_dependency(SDL2_image CONFIG)
+ENDIF()
+IF(@SDL2PP_WITH_TTF@)
+  find_dependency(SDL2_ttf CONFIG)
+ENDIF()
+IF(@SDL2PP_WITH_MIXER@)
+  find_dependency(SDL2_mixer CONFIG)
+ENDIF()
+
 IF(SDL2PP_INCLUDE_DIR AND SDL2PP_LIBRARY)
 	# in cache already
 	SET(SDL2PP_FIND_QUIETLY TRUE)
