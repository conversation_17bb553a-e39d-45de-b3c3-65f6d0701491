diff --git a/CMakeLists.txt b/CMakeLists.txt
index cdfd2a6..dc08748 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -23,10 +23,25 @@ ENDIF(CMAKE_SOURCE_DIR STREQUAL PROJECT_SOURCE_DIR)
 
 # depends
 FIND_PACKAGE(SDL2 REQUIRED)
-SET(SDL2_ALL_INCLUDE_DIRS ${SDL2_INCLUDE_DIR})
-SET(SDL2_ALL_LIBRARIES ${SDL2_LIBRARY})
+SET(SDL2_ALL_INCLUDE_DIRS "")
+IF(TARGET SDL2::SDL2)
+    SET(SDL2_ALL_LIBRARIES SDL2::SDL2)
+ELSE()
+    SET(SDL2_ALL_LIBRARIES SDL2::SDL2-static)
+ENDIF()
 SET(SDL2_ALL_PKGCONFIG_MODULES sdl2)
-SET(SDL2PP_EXTRA_LIBRARIES ${SDL2MAIN_LIBRARY})
+set(SDL2PP_EXTRA_LIBRARIES SDL2::SDL2main)
+get_target_property(SDL2_MAIN_LIBRARY_DEBUG SDL2::SDL2main IMPORTED_LOCATION_DEBUG)
+get_target_property(SDL2_MAIN_LIBRARY_RELEASE SDL2::SDL2main IMPORTED_LOCATION_RELEASE)
+get_target_property(SDL2_MAIN_LIBRARY_GENERAL SDL2::SDL2main IMPORTED_LOCATION)
+set(SDL2MAIN_LIBRARY "")
+if(CMAKE_BUILD_TYPE STREQUAL "Debug" AND SDL2_MAIN_LIBRARY_DEBUG)
+set(SDL2MAIN_LIBRARY "${SDL2_MAIN_LIBRARY_DEBUG}")
+elseif(SDL2_MAIN_LIBRARY_RELEASE)
+set(SDL2MAIN_LIBRARY "${SDL2_MAIN_LIBRARY_RELEASE}")
+elseif(SDL2_MAIN_LIBRARY_GENERAL)
+set(SDL2MAIN_LIBRARY "${SDL2_MAIN_LIBRARY_GENERAL}")
+endif()
 SET(SDL2PP_EXTRA_PKGCONFIG_LIBRARIES ${SDL2MAIN_LIBRARY})
 
 IF(MINGW)
@@ -36,18 +40,16 @@ IF(MINGW)
 ENDIF(MINGW)
 
 IF(SDL2PP_WITH_IMAGE)
  FIND_PACKAGE(SDL2_image REQUIRED)
-	SET(SDL2_ALL_INCLUDE_DIRS ${SDL2_ALL_INCLUDE_DIRS} ${SDL2_IMAGE_INCLUDE_DIR})
-	SET(SDL2_ALL_LIBRARIES ${SDL2_ALL_LIBRARIES} ${SDL2_IMAGE_LIBRARY})
+	SET(SDL2_ALL_LIBRARIES ${SDL2_ALL_LIBRARIES} $<IF:$<TARGET_EXISTS:SDL2_image::SDL2_image>,SDL2_image::SDL2_image,SDL2_image::SDL2_image-static>)
 	SET(SDL2_ALL_PKGCONFIG_MODULES "${SDL2_ALL_PKGCONFIG_MODULES} SDL2_image")
 ELSE(SDL2PP_WITH_IMAGE)
 	MESSAGE(STATUS "SDL2_image support disabled")
 ENDIF(SDL2PP_WITH_IMAGE)
 
 IF(SDL2PP_WITH_TTF)
 	FIND_PACKAGE(SDL2_ttf REQUIRED)
-	SET(SDL2_ALL_INCLUDE_DIRS ${SDL2_ALL_INCLUDE_DIRS} ${SDL2_TTF_INCLUDE_DIR})
-	SET(SDL2_ALL_LIBRARIES ${SDL2_ALL_LIBRARIES} ${SDL2_TTF_LIBRARY})
+	SET(SDL2_ALL_LIBRARIES ${SDL2_ALL_LIBRARIES} $<IF:$<TARGET_EXISTS:SDL2_ttf::SDL2_ttf>,SDL2_ttf::SDL2_ttf,SDL2_ttf::SDL2_ttf-static>)
 	SET(SDL2_ALL_PKGCONFIG_MODULES "${SDL2_ALL_PKGCONFIG_MODULES} SDL2_ttf")
 ELSE(SDL2PP_WITH_TTF)
 	MESSAGE(STATUS "SDL2_ttf support disabled")
@@ -56,7 +60,7 @@ ENDIF(SDL2PP_WITH_TTF)
 IF(SDL2PP_WITH_MIXER)
 	FIND_PACKAGE(SDL2_mixer REQUIRED)
 	SET(SDL2_ALL_INCLUDE_DIRS ${SDL2_ALL_INCLUDE_DIRS} ${SDL2_MIXER_INCLUDE_DIR})
-	SET(SDL2_ALL_LIBRARIES ${SDL2_ALL_LIBRARIES} ${SDL2_MIXER_LIBRARY})
+	SET(SDL2_ALL_LIBRARIES ${SDL2_ALL_LIBRARIES} $<IF:$<TARGET_EXISTS:SDL2_mixer::SDL2_mixer>,SDL2_mixer::SDL2_mixer,SDL2_mixer::SDL2_mixer-static>)
 	SET(SDL2_ALL_PKGCONFIG_MODULES "${SDL2_ALL_PKGCONFIG_MODULES} SDL2_mixer")
 ELSE(SDL2PP_WITH_MIXER)
 	MESSAGE(STATUS "SDL2_mixer support disabled")
@@ -200,10 +203,10 @@ IF(CMAKE_SOURCE_DIR STREQUAL PROJECT_SOURCE_DIR)
 	OPTION(SDL2PP_STATIC "Build static library instead of shared one" OFF)
 
 	# library
-	IF(SDL2PP_STATIC)
+	IF(0)
 		ADD_LIBRARY(SDL2pp STATIC ${LIBRARY_SOURCES} ${LIBRARY_HEADERS})
 	ELSE(SDL2PP_STATIC)
-		ADD_LIBRARY(SDL2pp SHARED ${LIBRARY_SOURCES} ${LIBRARY_HEADERS})
+		ADD_LIBRARY(SDL2pp ${LIBRARY_SOURCES} ${LIBRARY_HEADERS})
 		TARGET_LINK_LIBRARIES(SDL2pp ${SDL2_ALL_LIBRARIES})
 		SET_TARGET_PROPERTIES(SDL2pp PROPERTIES VERSION 8.3.0 SOVERSION 8)
 	ENDIF(SDL2PP_STATIC)
