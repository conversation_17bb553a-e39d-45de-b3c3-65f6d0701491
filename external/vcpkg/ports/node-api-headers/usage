node-api-headers provides CMake targets:

    find_package(unofficial-node-api-headers CONFIG REQUIRED)
    target_link_libraries(main PRIVATE unofficial::node-api-headers::node-api-headers)

Add this to your CMakeLists.txt to build a Node.js addon:
    add_library(addon SHARED main.cpp ...)
    set_target_properties(addon PROPERTIES OUTPUT_NAME "my-addon" PREFIX "" SUFFIX ".node")

    find_package(unofficial-node-api-headers CONFIG REQUIRED)
    target_link_libraries(addon PRIVATE unofficial::node-api-headers::node-api-headers)

You will be able to load your addon in JavaScript code:
    const addon = require("/path/to/addon/my-addon.node");

Make sure that your Node.js OS/architecture matches vcpkg triplet you use.

Also, make sure to register a module in your C/C++ code like official example does:
https://github.com/nodejs/node-addon-examples/blob/main/1_hello_world/napi/hello.c

This port doesn't have specific functionality for publishing npm packages.

See https://nodejs.org/api/n-api.html
