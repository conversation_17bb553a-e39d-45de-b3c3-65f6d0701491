{"name": "fbthrift", "version-string": "2025.03.31.00", "port-version": 1, "description": "Facebook's branch of Apache Thrift, including a new C++ server.", "homepage": "https://github.com/facebook/fbthrift", "license": "Apache-2.0", "supports": "x64 & static", "dependencies": ["boost-context", "boost-filesystem", "boost-program-options", "boost-regex", "boost-system", "boost-thread", "fizz", "fmt", "folly", "gflags", "glog", "mvfst", "openssl", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}, "wangle", "xxhash", "zlib", "zstd"]}