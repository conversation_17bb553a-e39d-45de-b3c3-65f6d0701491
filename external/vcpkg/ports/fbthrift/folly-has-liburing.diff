diff --git a/thrift/lib/cpp2/security/SSLUtil.cpp b/thrift/lib/cpp2/security/SSLUtil.cpp
index 52a15b6..515e5b5 100644
--- a/thrift/lib/cpp2/security/SSLUtil.cpp
+++ b/thrift/lib/cpp2/security/SSLUtil.cpp
@@ -133,7 +133,7 @@ folly::AsyncSocketTransport::UniquePtr moveToPlaintext(FizzSocket* fizzSock) {
 
   auto sock = fizzSock->template getUnderlyingTransport<folly::AsyncSocket>();
   folly::AsyncSocketTransport::UniquePtr plaintextTransport;
-#if defined(__linux__) && __has_include(<liburing.h>)
+#if defined(__linux__) && FOLLY_HAS_LIBURING
   if (!sock &&
       fizzSock->template getUnderlyingTransport<folly::AsyncIoUringSocket>()) {
     // `AsyncFdSocket` currently lacks uring support, so hardcode `AsyncSocket`
