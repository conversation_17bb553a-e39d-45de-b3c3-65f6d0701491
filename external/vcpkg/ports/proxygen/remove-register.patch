diff --git a/proxygen/lib/utils/gen_perfect_hash_table.sh b/proxygen/lib/utils/gen_perfect_hash_table.sh
index a870b2d67..2dec9c5a1 100755
--- a/proxygen/lib/utils/gen_perfect_hash_table.sh
+++ b/proxygen/lib/utils/gen_perfect_hash_table.sh
@@ -35,6 +35,7 @@ function generate_perfect_hash_table {
   LC_ALL=C sort -u ${1?} | awk "${6?}" - "${5?}" | \
   ${8:-gperf} -m5 -D --output-file="${7?}"
   perl -p -i -e "s/\/\*FALLTHROUGH\*\//[[fallthrough]];/g" "${7?}"
+  perl -p -i -e "s/register//g" "${7?}"
 
   # Here we delete one of the comment lines gperf adds to the top of the file.
   # i.e. /* Command-line: .../gperf -m5 --output-file=...  */
