diff --git a/proxygen/lib/services/WorkerThread.cpp b/proxygen/lib/services/WorkerThread.cpp
index 3c422db..c4a05fa 100644
--- a/proxygen/lib/services/WorkerThread.cpp
+++ b/proxygen/lib/services/WorkerThread.cpp
@@ -17,7 +17,7 @@
 #include <glog/logging.h>
 #include <signal.h>
 
-#if !FOLLY_MOBILE && __has_include(<liburing.h>)
+#if !FOLLY_MOBILE && FOLLY_HAS_LIBURING
 
 DEFINE_int32(pwt_io_uring_capacity, -1, "io_uring backend capacity");
 DEFINE_int32(pwt_io_uring_max_submit, 128, "io_uring backend max submit");
