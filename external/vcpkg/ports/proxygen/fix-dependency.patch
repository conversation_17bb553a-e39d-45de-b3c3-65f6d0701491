diff --git a/CMakeLists.txt b/CMakeLists.txt
index dde1bcd..f72165b 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -71,11 +71,11 @@ endif()
 #
 # IMPORTANT: If you change this, make the analogous update in:
 #   cmake/proxygen-config.cmake.in
-find_package(fmt REQUIRED)
-find_package(folly REQUIRED)
-find_package(wangle REQUIRED)
-find_package(mvfst REQUIRED)
-find_package(Zstd REQUIRED)
+find_package(fmt CONFIG REQUIRED)
+find_package(folly CONFIG REQUIRED)
+find_package(wangle CONFIG REQUIRED)
+find_package(mvfst CONFIG REQUIRED)
+find_package(zstd CONFIG REQUIRED)
 find_package(ZLIB REQUIRED)
 find_package(OpenSSL REQUIRED)
 find_package(Threads)
@@ -130,6 +130,7 @@ SET(GFLAG_DEPENDENCIES "")
 SET(PROXYGEN_EXTRA_LINK_LIBRARIES "")
 SET(PROXYGEN_EXTRA_INCLUDE_DIRECTORIES "")
 
+set(GFLAGS_USE_TARGET_NAMESPACE ON)
 find_package(gflags CONFIG QUIET)
 if (gflags_FOUND)
   message("module path: ${CMAKE_MODULE_PATH}")
diff --git a/cmake/proxygen-config.cmake.in b/cmake/proxygen-config.cmake.in
index 6849b0a..1d2cad2 100644
--- a/cmake/proxygen-config.cmake.in
+++ b/cmake/proxygen-config.cmake.in
@@ -17,17 +17,21 @@
 @PACKAGE_INIT@
 
 include(CMakeFindDependencyMacro)
-find_dependency(fmt)
-find_dependency(folly)
-find_dependency(wangle)
-find_dependency(mvfst)
-find_dependency(Fizz)
+find_dependency(fmt CONFIG)
+find_dependency(folly CONFIG)
+find_dependency(wangle CONFIG)
+find_dependency(mvfst CONFIG)
+find_dependency(fizz CONFIG)
+set(z_vcpkg_proxygen_gflags_backup ${GFLAGS_USE_TARGET_NAMESPACE})
+set(GFLAGS_USE_TARGET_NAMESPACE ON)
+find_dependency(gflags CONFIG)
+set(GFLAGS_USE_TARGET_NAMESPACE ${z_vcpkg_proxygen_gflags_backup})
 # For now, anything that depends on Proxygen has to copy its FindZstd.cmake
 # and issue a `find_package(Zstd)`.  Uncommenting this won't work because
 # this Zstd module exposes a library called `zstd`.  The right fix is
 # discussed on D24686032.
 #
-# find_dependency(Zstd)
+find_dependency(zstd CONFIG)
 find_dependency(ZLIB)
 find_dependency(OpenSSL)
 find_dependency(Threads)
diff --git a/proxygen/lib/CMakeLists.txt b/proxygen/lib/CMakeLists.txt
index 74a0284..71039e7 100644
--- a/proxygen/lib/CMakeLists.txt
+++ b/proxygen/lib/CMakeLists.txt
@@ -250,10 +250,10 @@ target_link_libraries(
     Folly::folly
     fizz::fizz
     wangle::wangle
-    zstd
+    zstd::libzstd
     Boost::boost
     Boost::iostreams
-    -lz
+    ZLIB::ZLIB
     ${HTTP3_DEPEND_LIBS}
 )
 
