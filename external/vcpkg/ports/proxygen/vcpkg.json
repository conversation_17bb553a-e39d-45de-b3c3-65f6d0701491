{"name": "proxygen", "version-string": "2025.03.31.00", "port-version": 1, "description": "It comprises the core C++ HTTP abstractions used at Facebook.", "homepage": "https://github.com/facebook/proxygen", "license": "BSD-3-<PERSON><PERSON>", "supports": "!windows", "dependencies": ["boost-context", "boost-date-time", "boost-filesystem", "boost-iostreams", "boost-program-options", "boost-regex", "boost-system", "boost-thread", "fizz", "folly", "gflags", {"name": "gperf", "host": true}, "mvfst", "openssl", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}, "wangle", "zlib", "zstd"]}