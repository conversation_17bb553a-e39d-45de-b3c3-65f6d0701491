{"name": "libyuv", "version": "1896", "port-version": 1, "description": "libyuv is an open source project that includes YUV scaling and conversion functionality", "homepage": "https://chromium.googlesource.com/libyuv/libyuv", "license": null, "dependencies": ["libjpeg-turbo", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}, {"name": "vcpkg-cmake-get-vars", "host": true}], "features": {"tools": {"description": "build command line tool", "supports": "!android & !ios & !xbox & !wasm32"}}}