diff --git a/cpp/src/phonenumbers/regexp_adapter_re2.cc b/cpp/src/phonenumbers/regexp_adapter_re2.cc
index 398f369b..3dd5d854 100644
--- a/cpp/src/phonenumbers/regexp_adapter_re2.cc
+++ b/cpp/src/phonenumbers/regexp_adapter_re2.cc
@@ -41,7 +41,7 @@ class RE2RegExpInput : public RegExpInput {
         utf8_input_(string_) {}
 
   virtual string ToString() const {
-    return utf8_input_.ToString();
+    return string(utf8_input_.data(), utf8_input_.size());
   }
 
   StringPiece* Data() {
