diff --git a/cpp/CMakeLists.txt b/cpp/CMakeLists.txt
index 10b6dce7..50f06eda 100644
--- a/cpp/CMakeLists.txt
+++ b/cpp/CMakeLists.txt
@@ -46,6 +46,7 @@ function (print_error DESCRIPTION FILE)
     "Can't find ${DESCRIPTION}: can't locate ${FILE}. Please read the README.")
 endfunction ()
 
+set(CMAKE_FIND_LIBRARY_PREFIXES "${CMAKE_FIND_LIBRARY_PREFIXES};lib")
 # Find a library. If it has not been found, stop CMake with a fatal error
 # message.
 function (find_required_library NAME HEADER LIBRARY DESCRIPTION)
