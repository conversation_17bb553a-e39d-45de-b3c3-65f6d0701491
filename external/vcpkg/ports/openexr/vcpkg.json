{"name": "openexr", "version": "3.3.2", "description": "OpenEXR is a high dynamic-range (HDR) image file format developed by Industrial Light & Magic for use in computer imaging applications", "homepage": "https://www.openexr.com/", "license": "BSD-3-<PERSON><PERSON>", "supports": "!uwp", "dependencies": ["imath", "libdeflate", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"tools": {"description": "Build tools"}}}