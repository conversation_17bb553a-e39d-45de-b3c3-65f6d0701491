diff --git a/backend/CMakeLists.txt b/backend/CMakeLists.txt
index 133abef..a95f620 100644
--- a/backend/CMakeLists.txt
+++ b/backend/CMakeLists.txt
@@ -46,12 +46,12 @@ function(zint_target_compile_definitions scope definition)
     endif()
 endfunction()
 
-function(zint_target_include_directories scope directory)
+function(zint_target_include_directories)
     if(ZINT_SHARED)
-        target_include_directories(zint ${scope} ${directory})
+        target_include_directories(zint ${ARGN})
     endif()
     if(ZINT_STATIC)
-        target_include_directories(zint-static ${scope} ${directory})
+        target_include_directories(zint-static ${ARGN})
     endif()
 endfunction()
 
