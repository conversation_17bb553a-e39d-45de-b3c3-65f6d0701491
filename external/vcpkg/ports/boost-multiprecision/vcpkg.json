{"$comment": "Automatically generated by scripts/boost/generate-ports.ps1", "name": "boost-multiprecision", "version": "1.87.0", "description": "Boost multiprecision module", "homepage": "https://www.boost.org/libs/multiprecision", "license": "BSL-1.0", "dependencies": [{"name": "boost-assert", "version>=": "1.87.0"}, {"name": "boost-cmake", "version>=": "1.87.0"}, {"name": "boost-config", "version>=": "1.87.0"}, {"name": "boost-core", "version>=": "1.87.0"}, {"name": "boost-headers", "version>=": "1.87.0"}, {"name": "boost-integer", "version>=": "1.87.0"}, {"name": "boost-lexical-cast", "version>=": "1.87.0"}, {"name": "boost-math", "version>=": "1.87.0"}, {"name": "boost-predef", "version>=": "1.87.0"}, {"name": "boost-random", "platform": "!uwp", "version>=": "1.87.0"}, {"name": "boost-throw-exception", "version>=": "1.87.0"}]}