commit 6023d12ca2df861b134b345151d2463a963e50c4
Author: <PERSON> <<EMAIL>>
Date:   Wed Aug 4 13:24:43 2021 +1000

    ESD-2166 Fix windows install dll directory (#1061)

diff --git a/c/src/CMakeLists.txt b/c/src/CMakeLists.txt
index b00fa523..1019683f 100644
--- a/c/src/CMakeLists.txt
+++ b/c/src/CMakeLists.txt
@@ -27,9 +27,13 @@ if (MINGW)
   endif()
 endif()
 
-install(TARGETS sbp
-        EXPORT sbp-export
-        DESTINATION ${CMAKE_INSTALL_FULL_LIBDIR})
+install(
+  TARGETS sbp
+  EXPORT sbp-export
+  RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
+  LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
+  ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR}
+)
 install(DIRECTORY "${PROJECT_SOURCE_DIR}/include/libsbp/" DESTINATION "${CMAKE_INSTALL_FULL_INCLUDEDIR}/libsbp")
 
 export(EXPORT sbp-export
