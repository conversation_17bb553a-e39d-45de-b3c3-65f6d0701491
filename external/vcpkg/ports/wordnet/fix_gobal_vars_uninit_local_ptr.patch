diff --git a/include/wn.h b/include/wn.h
index d3ffa08..8452377 100644
--- a/include/wn.h
+++ b/include/wn.h
@@ -151,14 +151,6 @@
 #define PREDICATIVE		PADJ
 #define IMMED_POSTNOMINAL	IPADJ
 
-extern char *wnrelease;		/* WordNet release/version number */
-
-extern char *lexfiles[];	/* names of lexicographer files */
-extern char *ptrtyp[];		/* pointer characters */
-extern char *partnames[];	/* POS strings */
-extern char partchars[];	/* single chars for each POS */
-extern char *adjclass[];	/* adjective class strings */
-extern char *frametext[];	/* text of verb frames */
 
 /* Data structures used by search code functions. */
 
@@ -236,6 +228,21 @@ typedef struct {
 
 typedef SearchResults *SearchResultsPtr;
 
+/* Make all the functions compatible with c++ files */
+#ifdef __cplusplus
+extern "C" {
+#endif
+
+extern char* wnrelease;		/* WordNet release/version number */
+
+extern char* lexfiles[];	/* names of lexicographer files */
+extern char* ptrtyp[];		/* pointer characters */
+extern char* partnames[];	/* POS strings */
+extern char partchars[];	/* single chars for each POS */
+extern char* adjclass[];	/* adjective class strings */
+extern char* frametext[];	/* text of verb frames */
+
+
 /* Global variables and flags */
 
 extern SearchResults wnresults;	/* structure containing results of search */
@@ -271,11 +278,6 @@ extern int default_display_message(char *);
 extern int (*display_message)(char *);
 
 
-/* Make all the functions compatible with c++ files */
-#ifdef __cplusplus
-extern "C" {
-#endif
-
 /* External library function prototypes */
 
 /*** Search and database functions (search.c) ***/
diff --git a/lib/search.c b/lib/search.c
index 1cdedc3..72c4bb5 100644
--- a/lib/search.c
+++ b/lib/search.c
@@ -1256,7 +1256,7 @@ static void add_relatives(int pos, IndexPtr idx, int rel1, int rel2)
 {
     int i;
     struct relgrp *rel, *last, *r;
-
+    last = NULL;
     /* If either of the new relatives are already in a relative group,
        then add the other to the existing group (transitivity).
        Otherwise create a new group and add these 2 senses to it. */
@@ -1281,8 +1281,9 @@ static void add_relatives(int pos, IndexPtr idx, int rel1, int rel2)
     rel->senses[rel1] = rel->senses[rel2] = 1;
     if (rellist == NULL)
 	rellist = rel;
-    else
-	last->next = rel;
+    else if (last) {
+        last->next = rel;
+    }
 }
 
 static struct relgrp *mkrellist(void)
