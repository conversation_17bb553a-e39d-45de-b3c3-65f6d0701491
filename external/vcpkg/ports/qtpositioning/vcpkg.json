{"name": "qtpositioning", "version": "6.8.3", "description": "The Qt Positioning API provides positioning information via QML and C++ interfaces.", "homepage": "https://www.qt.io/", "license": null, "dependencies": ["jhasse-poly2tri", {"name": "qtbase", "default-features": false}, {"name": "qtserialport", "default-features": false, "platform": "!ios"}], "features": {"qml": {"description": "Build QML imports", "dependencies": [{"name": "qtdeclarative", "default-features": false}]}}}