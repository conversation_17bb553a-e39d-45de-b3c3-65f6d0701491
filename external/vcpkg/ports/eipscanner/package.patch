diff --git a/cmake/EIPScannerConfig.cmake.in b/cmake/EIPScannerConfig.cmake.in
new file mode 100644
index 0000000..3e16bd1
--- /dev/null
+++ b/cmake/EIPScannerConfig.cmake.in
@@ -0,0 +1,6 @@
+@PACKAGE_INIT@
+
+include("${CMAKE_CURRENT_LIST_DIR}/EIPScannerTargets.cmake")
+
+check_required_components(EIPScanner)
+
diff --git a/CMakeLists.txt b/CMakeLists.txt
index 68a291e..d2562a8 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -13,6 +13,7 @@ option(TEST_ENABLED "Enable unit test" OFF)
 option(EXAMPLE_ENABLED "Build examples" OFF)
 
 add_subdirectory(src)
+
 if (EXAMPLE_ENABLED)
     add_subdirectory(examples)
 endif()
diff --git a/src/CMakeLists.txt b/src/CMakeLists.txt
index dc2bbb9..d8d3cfa 100644
--- a/src/CMakeLists.txt
+++ b/src/CMakeLists.txt
@@ -64,7 +64,18 @@ set_target_properties(
         VERSION ${EIPSCANNER_FULL_VERSION}
         SOVERSION ${EIPSCANNER_MAJOR_VERSION})
 
-install(TARGETS EIPScanner EIPScannerS
+if(BUILD_SHARED_LIBS)
+        set(install_target EIPScanner)
+        set_target_properties(EIPScanner PROPERTIES EXPORT_NAME eipscanner)
+        set_target_properties(EIPScannerS PROPERTIES EXCLUDE_FROM_ALL 1)
+else()
+        set(install_target EIPScannerS)
+        set_target_properties(EIPScannerS PROPERTIES EXPORT_NAME eipscanner)
+        set_target_properties(EIPScanner PROPERTIES EXCLUDE_FROM_ALL 1)
+endif()
+
+install(TARGETS ${install_target}
+        EXPORT ${install_target}
         LIBRARY
             DESTINATION lib
         ARCHIVE
@@ -73,3 +84,33 @@ install(TARGETS EIPScanner EIPScannerS
 install(DIRECTORY ${CMAKE_SOURCE_DIR}/src/
         DESTINATION include/EIPScanner
         FILES_MATCHING PATTERN "*.h*")
+
+target_include_directories(${install_target}
+    INTERFACE
+        $<INSTALL_INTERFACE:include/EIPScanner>
+)
+
+include(CMakePackageConfigHelpers)
+configure_package_config_file(
+        ../cmake/EIPScannerConfig.cmake.in
+        ${CMAKE_CURRENT_BINARY_DIR}/EIPScannerConfig.cmake
+        INSTALL_DESTINATION lib/cmake/eipscanner
+)
+
+write_basic_package_version_file(
+        ${CMAKE_CURRENT_BINARY_DIR}/EIPScannerConfigVersion.cmake
+        VERSION ${PROJECT_VERSION}
+        COMPATIBILITY SameMajorVersion
+)
+
+install(FILES
+        ${CMAKE_CURRENT_BINARY_DIR}/EIPScannerConfig.cmake
+        ${CMAKE_CURRENT_BINARY_DIR}/EIPScannerConfigVersion.cmake
+        DESTINATION lib/cmake/eipscanner
+)
+
+install(EXPORT ${install_target}
+        FILE EIPScannerTargets.cmake
+        NAMESPACE unofficial::eipscanner::
+        DESTINATION lib/cmake/eipscanner
+)
