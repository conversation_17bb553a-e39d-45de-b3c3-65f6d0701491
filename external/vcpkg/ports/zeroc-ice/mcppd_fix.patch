--- a/config/Make.project.rules	2022-01-17 10:28:23.000000000 
+++ b/config/Make.project.rules	2022-04-18 12:15:19.000000000 
@@ -1050,17 +1050,17 @@
 #
 # $(call make-lib,$1=libname)
 #
 define make-lib
 ifneq ($($1_home),)
 # Check for 3rd party libraries either in home/lib<platform specific subdir> or home/lib
-$1_installdir   = $$($1_home)/lib$$(call platform-var,installdir,$1,$$($$2_platform),$$($$2_config),$$2)
-$1_libdir       ?= $$(strip $$(if $$(wildcard $$($1_installdir)),$$($1_installdir),$$($1_home)/lib))
-$1_includedir   ?= $$($1_home)/include
+$1_installdir   =
+$1_libdir       ?=
+$1_includedir   ?=
 endif
-$1_ldflags      ?= -l$1
+$1_ldflags      ?= -l$$(if $$(and $$(filter mcpp,$1),$$(filter no,$$(OPTIMIZE))),$1d,$1)
 endef
 
 #
 # $(call repathfromdir,path)
 #
 # Returns reversed relative path for directory (e.g.: test/Ice/operations
