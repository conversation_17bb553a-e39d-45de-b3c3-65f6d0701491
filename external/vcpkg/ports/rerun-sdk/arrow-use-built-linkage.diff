diff --git a/CMakeLists.txt b/CMakeLists.txt
index 101ee6b8cb..591e47332b 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -109,16 +109,24 @@ target_link_libraries(rerun_sdk PRIVATE rerun_c)
 # Arrow dependency.
 # This makes the setup a lot easier on Windows where we otherwise need to put Arrow.dll either in path or copy it with the executable.
 # Additionally reduces risk of picking up system libraries on Mac / Linux.
-set(RERUN_ARROW_LINK_SHARED_DEFAULT OFF)
-option(RERUN_ARROW_LINK_SHARED "Link to the Arrow shared library." ${RERUN_ARROW_LINK_SHARED_DEFAULT})
 option(RERUN_DOWNLOAD_AND_BUILD_ARROW "If enabled, arrow will be added as an external project and built with the minimal set required by the Rerun C++ SDK" ON)
 
+if (NOT RERUN_DOWNLOAD_AND_BUILD_ARROW)
+    find_package(Arrow REQUIRED)
+endif()
+
+if (ARROW_BUILD_SHARED)
+    set(RERUN_ARROW_LINK_SHARED_DEFAULT ON)
+else()
+    set(RERUN_ARROW_LINK_SHARED_DEFAULT OFF)
+endif()
+
+option(RERUN_ARROW_LINK_SHARED "Link to the Arrow shared library." ${RERUN_ARROW_LINK_SHARED_DEFAULT})
+
 if(RERUN_DOWNLOAD_AND_BUILD_ARROW)
     include(download_and_build_arrow.cmake)
     download_and_build_arrow() # populates `rerun_arrow_target`
 else()
-    find_package(Arrow REQUIRED)
-
     if(RERUN_ARROW_LINK_SHARED)
         add_library(rerun_arrow_target ALIAS Arrow::arrow_shared)
     else()
