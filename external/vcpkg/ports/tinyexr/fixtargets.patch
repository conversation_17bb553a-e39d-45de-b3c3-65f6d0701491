diff --git a/CMakeLists.txt b/CMakeLists.txt
index b7f97b0..2afd0e0 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -7,6 +7,20 @@ set(SAMPLE_TARGET "test_tinyexr")
 
 project(${BUILD_TARGET} CXX)
 
+set(INSTALL_BIN_DIR      "bin"                      CACHE PATH "Path where exe and dll will be installed")
+set(INSTALL_LIB_DIR      "lib"                      CACHE PATH "Path where lib will be installed")
+set(INSTALL_INCLUDE_DIR  "include"                  CACHE PATH "Path where headers will be installed")
+set(INSTALL_CMAKE_DIR    "share/tinyexr"            CACHE PATH "Path where cmake configs will be installed")
+
+# Make relative paths absolute (needed later on)
+set(RELATIVE_INSTALL_INCLUDE_DIR ${INSTALL_INCLUDE_DIR})
+foreach(p LIB BIN INCLUDE CMAKE)
+  set(var INSTALL_${p}_DIR)
+  if(NOT IS_ABSOLUTE "${${var}}")
+    set(${var} "${CMAKE_INSTALL_PREFIX}/${${var}}")
+  endif()
+endforeach()
+
 # options
 option(TINYEXR_BUILD_SAMPLE "Build a sample" ON)
 option(TINYEXR_USE_MINIZ "Use miniz" ON)
@@ -28,11 +42,8 @@ set(TINYEXR_SOURCES
     )
 
 if(TINYEXR_USE_MINIZ)
-  enable_language(C)
-  add_library(miniz STATIC deps/miniz/miniz.c)
-  target_include_directories(miniz PUBLIC deps/miniz)
-  set_target_properties(miniz PROPERTIES FOLDER "deps")
-  list(APPEND TINYEXR_EXT_LIBRARIES miniz)
+  find_package(miniz CONFIG REQUIRED)
+  list(APPEND TINYEXR_EXT_LIBRARIES miniz::miniz)
 endif()
 
 add_library(${BUILD_TARGET} ${TINYEXR_SOURCES} ${TINYEXR_DEP_SOURCES})
@@ -43,7 +54,7 @@ target_link_libraries(${BUILD_TARGET} ${TINYEXR_EXT_LIBRARIES} ${CMAKE_DL_LIBS})
 
 # Increase warning level for clang.
 if (CMAKE_CXX_COMPILER_ID MATCHES "Clang" AND NOT MSVC)
-  set(CLANG_COMPILE_FLAGS "-Weverything -Werror -Wno-padded -Wno-c++98-compat-pedantic -Wno-documentation -Wno-unused-member-function")
+  set(CLANG_COMPILE_FLAGS "-Weverything -Wno-padded -Wno-c++98-compat-pedantic -Wno-documentation -Wno-unused-member-function")
   if (${CMAKE_CXX_COMPILER_VERSION} VERSION_GREATER 16)
     set(CLANG_COMPILE_FLAGS "${CLANG_COMPILE_FLAGS} -Wno-unsafe-buffer-usage")
   endif()
@@ -76,3 +87,32 @@ if (TINYEXR_BUILD_SAMPLE)
   endif(WIN32)
 
 endif (TINYEXR_BUILD_SAMPLE)
+
+target_include_directories(${BUILD_TARGET} PUBLIC $<INSTALL_INTERFACE:${RELATIVE_INSTALL_INCLUDE_DIR}> $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}>)
+set_target_properties(${BUILD_TARGET} PROPERTIES PUBLIC_HEADER "${PROJECT_SOURCE_DIR}/tinyexr.h")
+
+install(TARGETS ${BUILD_TARGET} EXPORT ${BUILD_TARGET}Targets
+  RUNTIME DESTINATION "${INSTALL_BIN_DIR}"
+  LIBRARY DESTINATION "${INSTALL_LIB_DIR}"
+  ARCHIVE DESTINATION "${INSTALL_LIB_DIR}"
+  PUBLIC_HEADER DESTINATION "${INSTALL_INCLUDE_DIR}"
+  COMPONENT dev
+)
+
+install(EXPORT ${BUILD_TARGET}Targets
+  FILE ${BUILD_TARGET}Targets.cmake
+  NAMESPACE unofficial::${BUILD_TARGET}::
+  DESTINATION "${INSTALL_CMAKE_DIR}"
+)
+
+include(CMakePackageConfigHelpers)
+configure_package_config_file(
+  ${CMAKE_CURRENT_SOURCE_DIR}/Config.cmake.in
+  ${CMAKE_CURRENT_BINARY_DIR}/${BUILD_TARGET}Config.cmake
+  INSTALL_DESTINATION "${INSTALL_CMAKE_DIR}"
+)
+
+install(
+  FILES ${CMAKE_CURRENT_BINARY_DIR}/${BUILD_TARGET}Config.cmake
+  DESTINATION "${INSTALL_CMAKE_DIR}"
+)
diff --git a/Config.cmake.in b/Config.cmake.in
new file mode 100644
index 0000000..2e33c1d
--- /dev/null
+++ b/Config.cmake.in
@@ -0,0 +1,6 @@
+@PACKAGE_INIT@
+
+include(CMakeFindDependencyMacro)
+find_dependency(miniz CONFIG REQUIRED)
+
+include("${CMAKE_CURRENT_LIST_DIR}/@<EMAIL>")
