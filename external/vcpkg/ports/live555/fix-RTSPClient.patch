diff --git a/liveMedia/RTSPClient.cpp b/liveMedia/RTSPClient.cpp
index 66e0c79..13255af 100644
--- a/liveMedia/RTSPClient.cpp
+++ b/liveMedia/RTSPClient.cpp
@@ -2029,7 +2029,7 @@ int RTSPClient::write(const char* data, unsigned count) {
       if (fOutputTLS->isNeeded) {
 	return fOutputTLS->write(data, count);
       } else {
-	return send(fOutputSocketNum, data, count, MSG_NOSIGNAL);
+	return send(fOutputSocketNum, (const char *)data, count, MSG_NOSIGNAL);
       }
 }
 
