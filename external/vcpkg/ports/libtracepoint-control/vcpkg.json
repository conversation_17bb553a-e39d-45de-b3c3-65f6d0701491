{"name": "libtracepoint-control", "version": "1.4.0", "description": "C++ classes for collecting Linux Tracepoints", "homepage": "https://github.com/microsoft/LinuxTracepoints/", "license": "MIT", "supports": "linux", "dependencies": [{"name": "libtracepoint-decode", "version>=": "1.4.0"}, {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"tools": {"description": "Build user tools: perf-collect", "supports": "linux"}}}