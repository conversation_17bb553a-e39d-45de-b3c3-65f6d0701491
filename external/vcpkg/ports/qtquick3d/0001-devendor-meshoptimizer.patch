diff --git a/src/utils/CMakeLists.txt b/src/utils/CMakeLists.txt
index 78ad8af..bee1ca6 100644
--- a/src/utils/CMakeLists.txt
+++ b/src/utils/CMakeLists.txt
@@ -7,6 +7,8 @@
 ## Quick3DUtils Module:
 #####################################################################
 
+qt_find_package(meshoptimizer PROVIDED_TARGETS meshoptimizer::meshoptimizer)
+
 qt_internal_add_module(Quick3DUtils
     SOURCES
         qqsbcollection.cpp qqsbcollection_p.h
@@ -26,49 +28,17 @@ qt_internal_add_module(Quick3DUtils
         qquick3dprofiler_p.h
         ../3rdparty/xatlas/xatlas.cpp ../3rdparty/xatlas/xatlas.h
         qssglightmapuvgenerator.cpp qssglightmapuvgenerator_p.h
-        ../3rdparty/meshoptimizer/src/allocator.cpp
-        ../3rdparty/meshoptimizer/src/clusterizer.cpp
-        ../3rdparty/meshoptimizer/src/indexcodec.cpp
-        ../3rdparty/meshoptimizer/src/indexgenerator.cpp
-        ../3rdparty/meshoptimizer/src/meshoptimizer.h
-        ../3rdparty/meshoptimizer/src/overdrawanalyzer.cpp
-        ../3rdparty/meshoptimizer/src/overdrawoptimizer.cpp
-        ../3rdparty/meshoptimizer/src/simplifier.cpp
-        ../3rdparty/meshoptimizer/src/spatialorder.cpp
-        ../3rdparty/meshoptimizer/src/stripifier.cpp
-        ../3rdparty/meshoptimizer/src/vcacheanalyzer.cpp
-        ../3rdparty/meshoptimizer/src/vcacheoptimizer.cpp
-        ../3rdparty/meshoptimizer/src/vertexcodec.cpp
-        ../3rdparty/meshoptimizer/src/vertexfilter.cpp
-        ../3rdparty/meshoptimizer/src/vfetchanalyzer.cpp
-        ../3rdparty/meshoptimizer/src/vfetchoptimizer.cpp
     NO_UNITY_BUILD_SOURCES
         ../3rdparty/xatlas/xatlas.cpp ../3rdparty/xatlas/xatlas.h
-        ../3rdparty/meshoptimizer/src/allocator.cpp
-        ../3rdparty/meshoptimizer/src/clusterizer.cpp
-        ../3rdparty/meshoptimizer/src/indexcodec.cpp
-        ../3rdparty/meshoptimizer/src/indexgenerator.cpp
-        ../3rdparty/meshoptimizer/src/meshoptimizer.h
-        ../3rdparty/meshoptimizer/src/overdrawanalyzer.cpp
-        ../3rdparty/meshoptimizer/src/overdrawoptimizer.cpp
-        ../3rdparty/meshoptimizer/src/simplifier.cpp
-        ../3rdparty/meshoptimizer/src/spatialorder.cpp
-        ../3rdparty/meshoptimizer/src/stripifier.cpp
-        ../3rdparty/meshoptimizer/src/vcacheanalyzer.cpp
-        ../3rdparty/meshoptimizer/src/vcacheoptimizer.cpp
-        ../3rdparty/meshoptimizer/src/vertexcodec.cpp
-        ../3rdparty/meshoptimizer/src/vertexfilter.cpp
-        ../3rdparty/meshoptimizer/src/vfetchanalyzer.cpp
-        ../3rdparty/meshoptimizer/src/vfetchoptimizer.cpp
     DEFINES
         QT_BUILD_QUICK3DUTILS_LIB
     INCLUDE_DIRECTORIES
         ../3rdparty/xatlas
-        ../3rdparty/meshoptimizer/src/
     LIBRARIES
         Qt::CorePrivate
         Qt::GuiPrivate
         Qt::QuickPrivate
+        meshoptimizer::meshoptimizer
     PUBLIC_LIBRARIES
         Qt::Core
         Qt::Gui
