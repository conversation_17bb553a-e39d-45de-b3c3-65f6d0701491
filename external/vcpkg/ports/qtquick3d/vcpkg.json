{"name": "qtquick3d", "version": "6.8.3", "description": "Qt Quick 3D provides a high-level API for creating 3D content and 3D user interfaces based on Qt Quick.", "homepage": "https://www.qt.io/", "license": null, "dependencies": ["meshoptimizer", {"name": "qtbase", "default-features": false, "features": ["gui"]}, "qtdeclarative", {"name": "qtquick3d", "host": true, "default-features": false}, "qtquicktimeline", "qtshadertools"], "features": {"assimp": {"description": "assimp", "dependencies": ["assimp"]}, "openxr": {"description": "OpenXR support", "dependencies": ["openxr-loader"]}}}