diff --git a/CMakeLists.txt b/CMakeLists.txt
index f77ff7f..1aeb5fc 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -1,7 +1,7 @@
 cmake_minimum_required(VERSION 3.17)
 project(spine)
 
-set(CMAKE_INSTALL_PREFIX "./")
+include(GNUInstallDirs)
 set(CMAKE_VERBOSE_MAKEFILE ON)
 set(SPINE_SFML FALSE CACHE BOOL FALSE)
 set(SPINE_SANITIZE FALSE CACHE BOOL FALSE)
@@ -20,12 +20,12 @@ else()
 	endif()
 endif()
 
-if((${SPINE_SFML}) OR (${CMAKE_CURRENT_BINARY_DIR} MATCHES "spine-sfml"))
-	add_subdirectory(spine-c)
+add_subdirectory(spine-c)
+add_subdirectory(spine-cpp)
+if(SPINE_SFML)
 	add_subdirectory(spine-sfml/c)
-	add_subdirectory(spine-cpp)
 	add_subdirectory(spine-sfml/cpp)
 endif()
 
 # add_subdirectory(spine-c/spine-c-unit-tests)
-add_subdirectory(spine-cpp/spine-cpp-unit-tests)
+# add_subdirectory(spine-cpp/spine-cpp-unit-tests)
diff --git a/spine-c/CMakeLists.txt b/spine-c/CMakeLists.txt
index 906f7b0..c5e1f18 100644
--- a/spine-c/CMakeLists.txt
+++ b/spine-c/CMakeLists.txt
@@ -3,6 +3,6 @@ file(GLOB INCLUDES "spine-c/include/**/*.h")
 file(GLOB SOURCES "spine-c/src/**/*.c" "spine-c/src/**/*.cpp")
 
 add_library(spine-c STATIC ${SOURCES} ${INCLUDES})
-target_include_directories(spine-c PUBLIC spine-c/include)
-install(TARGETS spine-c DESTINATION dist/lib)
-install(FILES ${INCLUDES} DESTINATION dist/include)
\ No newline at end of file
+target_include_directories(spine-c PUBLIC $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/spine-c/include>)
+install(TARGETS spine-c DESTINATION ${CMAKE_INSTALL_LIBDIR})
+install(FILES ${INCLUDES} DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}/spine)
\ No newline at end of file
diff --git a/spine-cpp/CMakeLists.txt b/spine-cpp/CMakeLists.txt
index e008c33..805aba8 100644
--- a/spine-cpp/CMakeLists.txt
+++ b/spine-cpp/CMakeLists.txt
@@ -12,6 +12,6 @@ file(GLOB INCLUDES "spine-cpp/include/**/*.h")
 file(GLOB SOURCES "spine-cpp/src/**/*.cpp")
 
 add_library(spine-cpp STATIC ${SOURCES} ${INCLUDES})
-target_include_directories(spine-cpp PUBLIC spine-cpp/include)
-install(TARGETS spine-cpp DESTINATION dist/lib)
-install(FILES ${INCLUDES} DESTINATION dist/include)
\ No newline at end of file
+target_include_directories(spine-cpp PUBLIC $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/spine-cpp/include>)
+install(TARGETS spine-cpp DESTINATION ${CMAKE_INSTALL_LIBDIR})
+install(FILES ${INCLUDES} DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}/spine)
\ No newline at end of file
diff --git a/spine-sfml/c/CMakeLists.txt b/spine-sfml/c/CMakeLists.txt
index 68dcc75..2ccfb2d 100644
--- a/spine-sfml/c/CMakeLists.txt
+++ b/spine-sfml/c/CMakeLists.txt
@@ -48,8 +48,9 @@ file(GLOB INCLUDES "src/**/*.h")
 file(GLOB SOURCES "src/**/*.cpp")
 add_library(spine-sfml STATIC ${SOURCES} ${INCLUDES})
 target_link_libraries(spine-sfml LINK_PUBLIC spine-c)
-install(TARGETS spine-sfml DESTINATION dist/lib)
-install(FILES ${INCLUDES} DESTINATION dist/include)
+install(TARGETS spine-sfml DESTINATION ${CMAKE_INSTALL_LIBDIR})
+install(FILES ${INCLUDES} DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}/spine)
+return()
 
 # Define spine-sfml example executable
 add_executable(spine-sfml-example example/main.cpp)
diff --git a/spine-sfml/cpp/CMakeLists.txt b/spine-sfml/cpp/CMakeLists.txt
index 903d0f7..bbcbb97 100644
--- a/spine-sfml/cpp/CMakeLists.txt
+++ b/spine-sfml/cpp/CMakeLists.txt
@@ -57,8 +57,9 @@ file(GLOB INCLUDES "src/**/*.h")
 file(GLOB SOURCES "src/**/*.cpp")
 add_library(spine-sfml-cpp STATIC ${SOURCES} ${INCLUDES})
 target_link_libraries(spine-sfml-cpp LINK_PUBLIC spine-cpp)
-install(TARGETS spine-sfml-cpp DESTINATION dist/lib)
-install(FILES ${INCLUDES} DESTINATION dist/include)
+install(TARGETS spine-sfml-cpp DESTINATION ${CMAKE_INSTALL_LIBDIR})
+install(FILES ${INCLUDES} DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}/spine)
+return()
 
 # Define spine-sfml example executable
 add_executable(spine-sfml-cpp-example example/main.cpp)
diff --git a/CMakeLists.txt b/CMakeLists.txt
index 1aeb5fc..0a9ffb7 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -11,8 +11,6 @@ if(MSVC)
 	set (CMAKE_C_FLAGS "${CMAKE_C_FLAGS}")
 	set (CMAKE_C_FLAGS "${CMAKE_CXX_FLAGS}")
 else()
-	set (CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -Wall -Wunused-value -Wno-c++11-long-long -Wno-variadic-macros -Werror -Wextra -pedantic -Wnonportable-include-path -Wshadow -std=c89")
-	set (CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wunused-value -Wno-c++11-long-long -Wno-variadic-macros -Werror -Wextra -Wnon-virtual-dtor -pedantic -Wnonportable-include-path -Wshadow -std=c++11 -fno-exceptions -fno-rtti")
 
 	if (${SPINE_SANITIZE})
 		set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -fsanitize=address -fsanitize=undefined")
