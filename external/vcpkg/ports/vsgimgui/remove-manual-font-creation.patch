diff --git a/include/vsgImGui/RenderImGui.h b/include/vsgImGui/RenderImGui.h
index e8186c6..8059d75 100644
--- a/include/vsgImGui/RenderImGui.h
+++ b/include/vsgImGui/RenderImGui.h
@@ -86,7 +86,6 @@ namespace vsgImGui
                    vsg::ref_ptr<vsg::RenderPass> renderPass,
                    uint32_t minImageCount, uint32_t imageCount,
                    VkExtent2D imageSize, bool useClearAttachments);
-        void _uploadFonts();
     };
 
 } // namespace vsgImGui
diff --git a/src/vsgImGui/RenderImGui.cpp b/src/vsgImGui/RenderImGui.cpp
index 306030b..d662531 100644
--- a/src/vsgImGui/RenderImGui.cpp
+++ b/src/vsgImGui/RenderImGui.cpp
@@ -60,7 +60,6 @@ namespace vsgImGui
 RenderImGui::RenderImGui(const vsg::ref_ptr<vsg::Window>& window, bool useClearAttachments)
 {
     _init(window, useClearAttachments);
-    _uploadFonts();
 }
 
 RenderImGui::RenderImGui(vsg::ref_ptr<vsg::Device> device, uint32_t queueFamily,
@@ -69,7 +68,6 @@ RenderImGui::RenderImGui(vsg::ref_ptr<vsg::Device> device, uint32_t queueFamily,
                          VkExtent2D imageSize, bool useClearAttachments)
 {
     _init(device, queueFamily, renderPass, minImageCount, imageCount, imageSize, useClearAttachments);
-    _uploadFonts();
 }
 
 RenderImGui::~RenderImGui()
@@ -184,11 +182,6 @@ void RenderImGui::_init(
     }
 }
 
-void RenderImGui::_uploadFonts()
-{
-    ImGui_ImplVulkan_CreateFontsTexture();
-}
-
 void RenderImGui::accept(vsg::RecordTraversal& rt) const
 {
     auto& commandBuffer = *(rt.getState()->_commandBuffer);
