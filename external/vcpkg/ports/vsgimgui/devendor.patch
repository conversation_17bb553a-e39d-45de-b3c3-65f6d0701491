diff --git a/CMakeLists.txt b/CMakeLists.txt
index a9e6265..96a2ecb 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -22,34 +22,13 @@ if (VULKAN_SDK)
     set(ENV{VULKAN_SDK} ${VULKAN_SDK})
 endif()
 
-find_package(vsg 1.0.5)
+find_package(vsg REQUIRED)
+find_package(imgui REQUIRED)
+find_package(implot REQUIRED)
 
 vsg_setup_dir_vars()
 vsg_setup_build_vars()
 
-if ( (NOT EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/src/imgui/imgui.h) OR
-     (NOT EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/src/implot/implot.h) )
-    find_package(Git QUIET)
-
-    execute_process(COMMAND ${GIT_EXECUTABLE} submodule update --init --recursive
-                    WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
-                    RESULT_VARIABLE GIT_SUBMOD_RESULT)
-
-    if(NOT GIT_SUBMOD_RESULT EQUAL "0")
-        message(FATAL_ERROR "git submodule update --init --recursive failed with ${GIT_SUBMOD_RESULT}, please checkout submodules")
-    endif()
-endif()
-
-vsg_copy_imgui_headers(
-    FILES
-        ${VSGIMGUI_SOURCE_DIR}/src/imgui/imgui.h
-        ${VSGIMGUI_SOURCE_DIR}/src/imgui/imconfig.h
-        ${VSGIMGUI_SOURCE_DIR}/src/imgui/imgui_internal.h
-        ${VSGIMGUI_SOURCE_DIR}/src/imgui/imstb_textedit.h
-        ${VSGIMGUI_SOURCE_DIR}/src/imgui//misc/cpp/imgui_stdlib.h
-        ${VSGIMGUI_SOURCE_DIR}/src/implot/implot.h
-        ${VSGIMGUI_SOURCE_DIR}/src/implot/implot_internal.h
-)
 
 vsg_add_target_clang_format(
     FILES
diff --git a/include/vsgImGui/RenderImGui.h b/include/vsgImGui/RenderImGui.h
index b4210d2..33198e4 100644
--- a/include/vsgImGui/RenderImGui.h
+++ b/include/vsgImGui/RenderImGui.h
@@ -31,7 +31,7 @@ CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 #include <vsg/vk/DescriptorPool.h>
 
 #include <vsgImGui/Export.h>
-#include <vsgImGui/imgui.h>
+#include <imgui.h>
 
 namespace vsgImGui
 {
diff --git a/src/CMakeLists.txt b/src/CMakeLists.txt
index 41d749d..919159e 100644
--- a/src/CMakeLists.txt
+++ b/src/CMakeLists.txt
@@ -6,40 +6,21 @@ set(EXTRA_INCLUDES)
 SET(HEADER_PATH ${VSGIMGUI_SOURCE_DIR}/include/vsgImGui)
 
 set(HEADERS
-    ${HEADER_PATH}/imgui.h
     ${HEADER_PATH}/SendEventsToImGui.h
     ${HEADER_PATH}/RenderImGui.h
     ${HEADER_PATH}/Texture.h
-    imgui/imconfig.h
-    imgui/imgui_internal.h
-    imgui/imstb_rectpack.h
-    imgui/imstb_textedit.h
-    imgui/imstb_truetype.h
-    imgui/misc/cpp/imgui_stdlib.h
-    implot/implot.h
-    implot/implot_internal.h
 )
 
 set(SOURCES
     vsgImGui/RenderImGui.cpp
     vsgImGui/SendEventsToImGui.cpp
     vsgImGui/Texture.cpp
-    imgui/imgui.cpp
-    imgui/imgui_draw.cpp
-    imgui/imgui_tables.cpp
-    imgui/imgui_widgets.cpp
-    imgui/backends/imgui_impl_vulkan.cpp
-    imgui/misc/cpp/imgui_stdlib.cpp
-    implot/implot.cpp
-    implot/implot_items.cpp
 )
 
 OPTION(SHOW_DEMO_WINDOW "Toggle the build of the ImGui::ShowDemoWindow(bool*) and ImPlot::ShadowDemoWindow(bool*)" ON)
 
 if (SHOW_DEMO_WINDOW)
     set(SOURCES ${SOURCES}
-        imgui/imgui_demo.cpp
-        implot/implot_demo.cpp
     )
 else()
     set(SOURCES ${SOURCES}
@@ -73,6 +54,8 @@ target_include_directories(vsgImGui PUBLIC
 target_link_libraries(vsgImGui
     PUBLIC
         vsg::vsg
+        imgui::imgui
+        implot::implot
     PRIVATE
         ${EXTRA_LIBRARIES}
 )
diff --git a/src/vsgImGui/RenderImGui.cpp b/src/vsgImGui/RenderImGui.cpp
index 5d7cf06..dac2227 100644
--- a/src/vsgImGui/RenderImGui.cpp
+++ b/src/vsgImGui/RenderImGui.cpp
@@ -22,9 +22,9 @@ CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 </editor-fold> */
 
 #include <vsgImGui/RenderImGui.h>
-#include <vsgImGui/implot.h>
+#include <implot.h>
 
-#include "../imgui/backends/imgui_impl_vulkan.h"
+#include <imgui_impl_vulkan.h>
 
 #include <vsg/io/Logger.h>
 #include <vsg/vk/State.h>
diff --git a/src/vsgImGui/SendEventsToImGui.cpp b/src/vsgImGui/SendEventsToImGui.cpp
index 4e83e4c..b1f4d83 100644
--- a/src/vsgImGui/SendEventsToImGui.cpp
+++ b/src/vsgImGui/SendEventsToImGui.cpp
@@ -22,7 +22,7 @@ CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 </editor-fold> */
 
 #include <vsgImGui/SendEventsToImGui.h>
-#include <vsgImGui/imgui.h>
+#include <imgui.h>
 
 #include <vsg/ui/KeyEvent.h>
 #include <vsg/ui/PointerEvent.h>
diff --git a/src/vsgImGuiConfig.cmake.in b/src/vsgImGuiConfig.cmake.in
index e2c14f2..5cf8bd9 100644
--- a/src/vsgImGuiConfig.cmake.in
+++ b/src/vsgImGuiConfig.cmake.in
@@ -2,5 +2,7 @@ include(CMakeFindDependencyMacro)
 
 find_dependency(Vulkan)
 find_dependency(vsg)
+find_dependency(imgui)
+find_dependency(implot)
 
 include("${CMAKE_CURRENT_LIST_DIR}/vsgImGuiTargets.cmake")
