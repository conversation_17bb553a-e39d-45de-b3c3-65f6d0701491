diff --git a/prj/cmake/CMakeLists.txt b/prj/cmake/CMakeLists.txt
index 1aa93f5..41e56fc 100644
--- a/prj/cmake/CMakeLists.txt
+++ b/prj/cmake/CMakeLists.txt
@@ -160,7 +160,7 @@ if(CMAKE_GENERATOR MATCHES "Visual Studio")
 else()
 	if((CMAKE_SYSTEM_PROCESSOR STREQUAL "i686") OR (CMAKE_SYSTEM_PROCESSOR STREQUAL "x86_64") OR (CMAKE_SYSTEM_PROCESSOR STREQUAL "AMD64"))
 		include(x86.cmake)
-	elseif((CMAKE_SYSTEM_PROCESSOR MATCHES "arm") OR (CMAKE_SYSTEM_PROCESSOR STREQUAL "aarch64"))
+	elseif((CMAKE_SYSTEM_PROCESSOR MATCHES "arm") OR (CMAKE_SYSTEM_PROCESSOR MATCHES "ARM") OR (CMAKE_SYSTEM_PROCESSOR STREQUAL "aarch64"))
 		include(arm.cmake)
 	else()
 	    message(FATAL_ERROR "Unknown value of CMAKE_SYSTEM_PROCESSOR!")
