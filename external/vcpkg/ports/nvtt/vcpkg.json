{"name": "nvtt", "version": "2.1.2", "port-version": 8, "description": "Texture processing tools with support for Direct3D 10 and 11 formats.", "homepage": "https://github.com/castano/nvidia-texture-tools", "license": "MIT", "supports": "!uwp & !arm", "dependencies": ["libsquish", {"name": "vcpkg-cmake", "host": true}], "features": {"cuda": {"description": "Enable CUDA support", "dependencies": ["cuda"]}}}