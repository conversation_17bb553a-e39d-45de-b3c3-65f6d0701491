diff --git a/src/nvtt/CMakeLists.txt b/src/nvtt/CMakeLists.txt
index 9688c9c..42942c0 100644
--- a/src/nvtt/CMakeLists.txt
+++ b/src/nvtt/CMakeLists.txt
@@ -47,6 +47,10 @@ ADD_DEFINITIONS(-DNVTT_EXPORTS)
 #ADD_DEFINITIONS(-DHAVE_RGETC)
 #ADD_DEFINITIONS(-DHAVE_ETCPACK)
 
+if(APPLE)
+    add_compile_options(-mbmi2 -mfma)
+endif()
+
 IF(NVTT_SHARED)	
     ADD_LIBRARY(nvtt SHARED ${NVTT_SRCS})
 ELSE(NVTT_SHARED)
