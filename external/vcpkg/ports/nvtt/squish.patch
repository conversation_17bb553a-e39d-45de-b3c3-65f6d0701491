diff --git a/src/nvtt/squish/CMakeLists.txt b/src/nvtt/squish/CMakeLists.txt
index 65a5dd3..d093ce5 100644
--- a/src/nvtt/squish/CMakeLists.txt
+++ b/src/nvtt/squish/CMakeLists.txt
@@ -21,3 +21,8 @@ SET(SQUISH_SRCS
 	simd_ve.h)
 
 ADD_LIBRARY(nvsquish STATIC ${SQUISH_SRCS})
+
+INSTALL(TARGETS nvsquish
+    RUNTIME DESTINATION bin
+    LIBRARY DESTINATION lib
+    ARCHIVE DESTINATION lib/static)
\ No newline at end of file
