diff --git a/src/nvmath/nvmath.h b/src/nvmath/nvmath.h
index 38532eb..e68d8f8 100644
--- a/src/nvmath/nvmath.h
+++ b/src/nvmath/nvmath.h
@@ -118,12 +118,12 @@ inline float asinf_assert(const float f)
 #endif
 
 #if NV_CC_MSVC
-NV_FORCEINLINE float log2f(float x)
+NV_FORCEINLINE float nv_log2f(float x)
 {
     nvCheck(x >= 0);
     return logf(x) / logf(2.0f);
 }
-NV_FORCEINLINE float exp2f(float x)
+NV_FORCEINLINE float nv_exp2f(float x)
 {
     return powf(2.0f, x);
 }
