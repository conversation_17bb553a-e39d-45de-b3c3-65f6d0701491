{"name": "libmicrohttpd", "version": "1.0.1", "port-version": 1, "description": "GNU libmicrohttpd is a small C library that is supposed to make it easy to run an HTTP server as part of another application", "homepage": "https://www.gnu.org/software/libmicrohttpd/", "license": "LGPL-2.1-or-later", "supports": "!((arm & windows) | uwp)", "dependencies": [{"name": "gettext", "platform": "!windows"}, {"name": "vcpkg-msbuild", "host": true, "platform": "windows & !mingw"}], "features": {"https": {"description": "Enable HTTPS protocol support", "supports": "!windows | mingw", "dependencies": ["libgnutls"]}}}