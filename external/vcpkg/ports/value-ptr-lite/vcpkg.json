{"name": "value-ptr-lite", "version-semver": "0.2.1", "port-version": 1, "description": "A C++ smart-pointer with value semantics for C++98, C++11 and later in a single-file header-only library.", "homepage": "https://github.com/martinmoene/value-ptr-lite", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"test": {"description": "Build and perform value_ptr-lite tests."}}}