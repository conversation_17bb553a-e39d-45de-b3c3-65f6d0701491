diff --git a/CMakeLists.txt b/CMakeLists.txt
index 303fcae..cdcf2c9 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -74,3 +74,12 @@ install(EXPORT FTGL-targets DESTINATION "${cmakedir}")
 install(
   FILES       "${CMAKE_CURRENT_BINARY_DIR}/CMakeFiles/FTGLConfig.cmake"
   DESTINATION "${cmakedir}")
+
+SET(PKGCONFIG_INSTALL_PREFIX "lib${LIB_SUFFIX}/pkgconfig/" CACHE STRING "Base directory for pkgconfig files")
+CONFIGURE_FILE(
+  ${CMAKE_CURRENT_SOURCE_DIR}/ftgl.pc.cmake 
+  ${CMAKE_CURRENT_BINARY_DIR}/ftgl.pc 
+  @ONLY)
+INSTALL(
+  FILES       ${CMAKE_CURRENT_BINARY_DIR}/ftgl.pc
+  DESTINATION ${PKGCONFIG_INSTALL_PREFIX})
diff --git a/ftgl.pc.cmake b/ftgl.pc.cmake
new file mode 100644
index 0000000..d242667
--- /dev/null
+++ b/ftgl.pc.cmake
@@ -0,0 +1,6 @@
+Name: ftgl
+Description: OpenGL frontend to Freetype 2
+Requires.private: freetype2
+Version: @VERSION_SERIES@.@VERSION_MAJOR@.@VERSION_MINOR@
+Libs: -L@CMAKE_INSTALL_PREFIX@/lib -lftgl
+Cflags: -I@CMAKE_INSTALL_PREFIX@/include
\ No newline at end of file
