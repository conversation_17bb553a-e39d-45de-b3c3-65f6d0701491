{"name": "getdns", "version": "1.7.3", "description": "GetDNS is a modern asynchronous DNS API", "homepage": "https://getdnsapi.net/", "license": "BSD-3-<PERSON><PERSON>", "supports": "!uwp", "dependencies": ["libidn2", "openssl", {"name": "vcpkg-cmake", "host": true}], "features": {"libevent": {"description": "libevent event loop integration", "dependencies": ["libevent"]}, "libuv": {"description": "libuv event loop integration", "dependencies": ["libuv"]}}}