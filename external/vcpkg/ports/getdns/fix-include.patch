diff --git a/src/gldns/parse.c b/src/gldns/parse.c
index 367fa80..79fa527 100644
--- a/src/gldns/parse.c
+++ b/src/gldns/parse.c
@@ -13,7 +13,7 @@
 #include "gldns/gbuffer.h"
 
 #include <limits.h>
-#include <strings.h>
+#include <stdlib.h>
 
 gldns_lookup_table gldns_directive_types[] = {
         { GLDNS_DIR_TTL, "$TTL" },
diff --git a/src/gldns/parseutil.c b/src/gldns/parseutil.c
index 293496a..bcd8e6f 100644
--- a/src/gldns/parseutil.c
+++ b/src/gldns/parseutil.c
@@ -14,8 +14,12 @@
 
 #include "config.h"
 #include "gldns/parseutil.h"
+#ifdef HAVE_SYS_TIME_H
 #include <sys/time.h>
+#endif
+#ifdef HAVE_TIME_H
 #include <time.h>
+#endif
 #include <ctype.h>
 
 gldns_lookup_table *
