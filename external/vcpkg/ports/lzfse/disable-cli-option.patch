diff --git a/CMakeLists.txt b/CMakeLists.txt
index 336a68d..96a2a74 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -67,18 +67,20 @@ add_library(lzfse
   src/lzvn_encode_base.c)
 lzfse_add_compiler_flags(lzfse -Wall -Wno-unknown-pragmas -Wno-unused-variable)
 
-add_executable(lzfse_cli
-  src/lzfse_main.c)
-target_link_libraries(lzfse_cli lzfse)
-set_target_properties(lzfse_cli PROPERTIES OUTPUT_NAME lzfse)
-lzfse_add_compiler_flags(lzfse_cli -Wall -Wno-unknown-pragmas -Wno-unused-variable)
-
-if(CMAKE_VERSION VERSION_LESS 3.1 OR CMAKE_C_COMPLIER_ID STREQUAL "Intel")
-  lzfse_add_compiler_flags(lzfse -std=c99)
-  lzfse_add_compiler_flags(lzfse_cli -std=c99)
-else()
-  set_property(TARGET lzfse PROPERTY C_STANDARD 99)
-  set_property(TARGET lzfse_cli PROPERTY C_STANDARD 99)
+if(NOT LZFSE_DISABLE_CLI)
+  add_executable(lzfse_cli
+    src/lzfse_main.c)
+  target_link_libraries(lzfse_cli lzfse)
+  set_target_properties(lzfse_cli PROPERTIES OUTPUT_NAME lzfse)
+  lzfse_add_compiler_flags(lzfse_cli -Wall -Wno-unknown-pragmas -Wno-unused-variable)
+
+  if(CMAKE_VERSION VERSION_LESS 3.1 OR CMAKE_C_COMPLIER_ID STREQUAL "Intel")
+    lzfse_add_compiler_flags(lzfse -std=c99)
+    lzfse_add_compiler_flags(lzfse_cli -std=c99)
+  else()
+    set_property(TARGET lzfse PROPERTY C_STANDARD 99)
+    set_property(TARGET lzfse_cli PROPERTY C_STANDARD 99)
+  endif()
 endif()
 
 set_target_properties(lzfse PROPERTIES
@@ -95,7 +97,12 @@ endif()
 if(NOT LZFSE_BUNDLE_MODE)
   include(GNUInstallDirs)
 
-  install(TARGETS lzfse lzfse_cli
+  if(NOT LZFSE_DISABLE_CLI)
+    install(TARGETS lzfse_cli
+      RUNTIME DESTINATION "${CMAKE_INSTALL_BINDIR}")
+  endif()
+
+  install(TARGETS lzfse
     RUNTIME DESTINATION "${CMAKE_INSTALL_BINDIR}"
     LIBRARY DESTINATION "${CMAKE_INSTALL_LIBDIR}"
     ARCHIVE DESTINATION "${CMAKE_INSTALL_LIBDIR}")
