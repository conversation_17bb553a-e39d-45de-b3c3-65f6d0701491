{"name": "kdstatemachineeditor", "version": "2.0.0", "description": "KDStateMachineEditor is a library for visualizing and editing state charts.", "homepage": "https://github.com/KDAB/KDStateMachineEditor", "license": "LGPL-2.1-only", "dependencies": ["graphviz", "qt5compat", {"name": "qtbase", "default-features": false, "features": ["testlib", "widgets"]}, "qtdeclarative", "qtremoteobjects", "qtscxml", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}]}