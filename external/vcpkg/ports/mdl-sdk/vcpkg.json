{"name": "mdl-sdk", "version": "2024.1", "description": "NVIDIA Material Definition Language SDK", "homepage": "https://github.com/NVIDIA/MDL-SDK", "license": "BSD-3-<PERSON><PERSON>", "supports": "!x86 & !(windows & (staticcrt | arm | uwp)) & !(osx & arm) & !android", "dependencies": ["boost-algorithm", "boost-core", "boost-dynamic-bitset", "boost-functional", "boost-intrusive", "boost-tokenizer", "boost-unordered", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"dds": {"description": "Build image plugin for DDS"}, "openimageio": {"description": "Build image plugin for various image formats using OpenImageIO", "dependencies": [{"name": "openimageio", "features": ["gif", "openjpeg", "webp"]}]}}}