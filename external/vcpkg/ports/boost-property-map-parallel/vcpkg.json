{"$comment": "Automatically generated by scripts/boost/generate-ports.ps1", "name": "boost-property-map-parallel", "version": "1.87.0", "description": "Boost property_map_parallel module", "homepage": "https://www.boost.org/libs/property_map_parallel", "license": "BSL-1.0", "dependencies": [{"name": "boost-assert", "version>=": "1.87.0"}, {"name": "boost-cmake", "version>=": "1.87.0"}, {"name": "boost-concept-check", "version>=": "1.87.0"}, {"name": "boost-config", "version>=": "1.87.0"}, {"name": "boost-function", "version>=": "1.87.0"}, {"name": "boost-headers", "version>=": "1.87.0"}, {"name": "boost-mpi", "version>=": "1.87.0"}, {"name": "boost-mpl", "version>=": "1.87.0"}, {"name": "boost-multi-index", "version>=": "1.87.0"}, {"name": "boost-optional", "version>=": "1.87.0"}, {"name": "boost-property-map", "version>=": "1.87.0"}, {"name": "boost-serialization", "version>=": "1.87.0"}, {"name": "boost-smart-ptr", "version>=": "1.87.0"}, {"name": "boost-static-assert", "version>=": "1.87.0"}, {"name": "boost-type-traits", "version>=": "1.87.0"}]}