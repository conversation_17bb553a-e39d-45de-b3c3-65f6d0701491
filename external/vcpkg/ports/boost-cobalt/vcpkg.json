{"$comment": "Automatically generated by scripts/boost/generate-ports.ps1", "name": "boost-cobalt", "version": "1.87.0", "port-version": 1, "description": "Boost cobalt module", "homepage": "https://www.boost.org/libs/cobalt", "license": "BSL-1.0", "supports": "!uwp", "dependencies": [{"name": "boost-asio", "version>=": "1.87.0"}, {"name": "boost-callable-traits", "version>=": "1.87.0"}, {"name": "boost-circular-buffer", "version>=": "1.87.0"}, {"name": "boost-cmake", "version>=": "1.87.0"}, {"name": "boost-config", "version>=": "1.87.0"}, {"name": "boost-container", "version>=": "1.87.0"}, {"name": "boost-context", "platform": "!uwp & !emscripten", "version>=": "1.87.0"}, {"name": "boost-core", "version>=": "1.87.0"}, {"name": "boost-headers", "version>=": "1.87.0"}, {"name": "boost-intrusive", "version>=": "1.87.0"}, {"name": "boost-leaf", "version>=": "1.87.0"}, {"name": "boost-mp11", "version>=": "1.87.0"}, {"name": "boost-preprocessor", "version>=": "1.87.0"}, {"name": "boost-smart-ptr", "version>=": "1.87.0"}, {"name": "boost-system", "version>=": "1.87.0"}, {"name": "boost-throw-exception", "version>=": "1.87.0"}, {"name": "boost-variant2", "version>=": "1.87.0"}]}