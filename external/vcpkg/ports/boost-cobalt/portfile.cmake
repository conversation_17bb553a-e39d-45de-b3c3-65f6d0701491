# Automatically generated by scripts/boost/generate-ports.ps1

vcpkg_from_github(
    OUT_SOURCE_PATH SOURCE_PATH
    REPO boostorg/cobalt
    REF boost-${VERSION}
    SHA512 af8161c15d9134ed46d38d0817403ca4f537892a68055048e1edc5d5f15626fe0d3d80b1ff76faec875f66146f3291a298ae289aea6280067477c4ca99cf9a02
    HEAD_REF master
    PATCHES
        fail-on-compiler-not-supported.patch
)

set(FEATURE_OPTIONS "")
boost_configure_and_install(
    SOURCE_PATH "${SOURCE_PATH}"
    OPTIONS ${FEATURE_OPTIONS}
)
