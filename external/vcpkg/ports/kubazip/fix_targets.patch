diff --git a/CMakeLists.txt b/CMakeLists.txt
index 804df5e..5498bb9 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -1,6 +1,6 @@
 cmake_minimum_required(VERSION 3.14)
 
-project(zip
+project(kubazip
   LANGUAGES C
   VERSION "0.3.0")
 set(CMAKE_MODULE_PATH "${PROJECT_SOURCE_DIR}/cmake" ${CMAKE_MODULE_PATH})
@@ -46,11 +46,11 @@ endif()
 
 target_include_directories(${PROJECT_NAME} PUBLIC
   $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/src>
-  $<INSTALL_INTERFACE:include>
+  $<INSTALL_INTERFACE:include/${PROJECT_NAME}>
 )
 
 # test
-if (NOT CMAKE_DISABLE_TESTING)
+if (0)
   enable_testing()
   add_subdirectory(test)
 endif()
@@ -74,7 +74,7 @@ endif (MSVC)
 
 ####
 set(CONFIG_INSTALL_DIR "lib/cmake/${PROJECT_NAME}")
-set(INCLUDE_INSTALL_DIR "include")
+set(INCLUDE_INSTALL_DIR "include/${PROJECT_NAME}")
 
 set(GENERATED_DIR "${CMAKE_CURRENT_BINARY_DIR}/generated")
 
