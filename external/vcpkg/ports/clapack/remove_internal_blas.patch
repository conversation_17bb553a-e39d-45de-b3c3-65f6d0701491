diff --git a/CMakeLists.txt b/CMakeLists.txt
index 320ccc6..414ac8e 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -1,7 +1,6 @@
 cmake_minimum_required(VERSION 2.6)
 project(CLAPACK C)
-enable_testing()
-include(CTest)
+find_package(BLAS REQUIRED)
 
 if(WIN32 AND NOT CYGWIN)
   set(SECOND_SRC  ${CLAPACK_SOURCE_DIR}/INSTALL/winsecond.c)
@@ -11,7 +10,6 @@ else()
   set(SECOND_SRC  ${CLAPACK_SOURCE_DIR}/INSTALL/second.c)
   set(DSECOND_SRC  ${CLAPACK_SOURCE_DIR}/INSTALL/dsecnd.c)
 endif()
-enable_testing()
 option(USE_BLAS_WRAP "pre-pend f2c_ to each function in blas" OFF)
 if(NOT USE_BLAS_WRAP)
 # _zrotg_ seems to be missing in the wrap header
@@ -19,16 +17,15 @@ if(NOT USE_BLAS_WRAP)
 endif()
 include_directories(${CLAPACK_SOURCE_DIR}/INCLUDE)
 add_subdirectory(F2CLIBS)
-add_subdirectory(BLAS)
 add_subdirectory(SRC)
-add_subdirectory(TESTING)
 set(CLAPACK_VERSION 3.2.1)
-set(CPACK_PACKAGE_VERSION_MAJOR 3)
-set(CPACK_PACKAGE_VERSION_MINOR 2)
-set(CPACK_PACKAGE_VERSION_PATCH 1)
-include(CPack)
-export(TARGETS f2c blas lapack FILE clapack-targets.cmake)
+export(TARGETS f2c lapack FILE ${CMAKE_INSTALL_PREFIX}/share/clapack/clapack-targets.cmake)
 configure_file(${CLAPACK_SOURCE_DIR}/clapack-config-version.cmake.in
-  ${CLAPACK_BINARY_DIR}/clapack-config-version.cmake @ONLY)
+	${CMAKE_INSTALL_PREFIX}/share/clapack/clapack-config-version.cmake @ONLY)
 configure_file(${CLAPACK_SOURCE_DIR}/clapack-config.cmake.in
-  ${CLAPACK_BINARY_DIR}/clapack-config.cmake @ONLY)
+	${CMAKE_INSTALL_PREFIX}/share/clapack/clapack-config.cmake @ONLY)
+install(FILES
+        ${CMAKE_CURRENT_LIST_DIR}/INCLUDE/clapack.h
+        ${CMAKE_CURRENT_LIST_DIR}/INCLUDE/f2c.h
+        DESTINATION include)
+
diff --git a/F2CLIBS/libf2c/CMakeLists.txt b/F2CLIBS/libf2c/CMakeLists.txt
index 43d7b3f..6fa3598 100644
--- a/F2CLIBS/libf2c/CMakeLists.txt
+++ b/F2CLIBS/libf2c/CMakeLists.txt
@@ -60,3 +60,11 @@ include_directories(${CLAPACK_SOURCE_DIR}/F2CLIBS/libf2c)
 include_directories(${CLAPACK_BINARY_DIR}/F2CLIBS/libf2c)
 add_library(f2c ${OFILES} ${CMAKE_CURRENT_BINARY_DIR}/arith.h)
 set_property(TARGET f2c PROPERTY PREFIX lib)
+if(UNIX)
+  target_link_libraries(f2c m)
+endif()
+install(TARGETS f2c
+        RUNTIME DESTINATION bin
+        LIBRARY DESTINATION lib
+        ARCHIVE DESTINATION lib)
+
diff --git a/SRC/CMakeLists.txt b/SRC/CMakeLists.txt
index ac4cce3..07dc8c7 100644
--- a/SRC/CMakeLists.txt
+++ b/SRC/CMakeLists.txt
@@ -376,5 +376,9 @@ if(BUILD_COMPLEX16)
   set(ALLOBJ  ${ZLASRC} ${ALLAUX} ${DZLAUX})
 endif()
 add_library(lapack ${ALLOBJ} ${ALLXOBJ})
-target_link_libraries(lapack blas)
+target_link_libraries(lapack ${BLAS_LIBRARIES} f2c)
+install(TARGETS lapack
+        RUNTIME DESTINATION bin
+        LIBRARY DESTINATION lib
+        ARCHIVE DESTINATION lib)
 
