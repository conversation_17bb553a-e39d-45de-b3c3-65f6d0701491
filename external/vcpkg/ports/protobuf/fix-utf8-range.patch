diff --git a/CMakeLists.txt b/CMakeLists.txt
index 15065d874..540c12253 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -302,6 +302,7 @@ endif (protobuf_BUILD_TESTS)
 include(${protobuf_SOURCE_DIR}/cmake/abseil-cpp.cmake)
 
 if (protobuf_BUILD_PROTOBUF_BINARIES)
+  find_package(utf8_range CONFIG REQUIRED)
   include(${protobuf_SOURCE_DIR}/cmake/utf8_range.cmake)
   include(${protobuf_SOURCE_DIR}/cmake/libprotobuf-lite.cmake)
   if (NOT DEFINED protobuf_LIB_PROTOBUF_LITE)
diff --git a/cmake/libprotobuf-lite.cmake b/cmake/libprotobuf-lite.cmake
index 9aa81fb88..699c92ddd 100644
--- a/cmake/libprotobuf-lite.cmake
+++ b/cmake/libprotobuf-lite.cmake
@@ -46,4 +46,4 @@ set_target_properties(libprotobuf-lite PROPERTIES
 )
 add_library(protobuf::libprotobuf-lite ALIAS libprotobuf-lite)
 
-target_link_libraries(libprotobuf-lite PRIVATE utf8_validity)
+target_link_libraries(libprotobuf-lite PRIVATE utf8_range::utf8_validity)
diff --git a/cmake/libprotobuf.cmake b/cmake/libprotobuf.cmake
index 11c09b1bc..33ebac7cc 100644
--- a/cmake/libprotobuf.cmake
+++ b/cmake/libprotobuf.cmake
@@ -48,4 +48,4 @@ set_target_properties(libprotobuf PROPERTIES
 )
 add_library(protobuf::libprotobuf ALIAS libprotobuf)
 
-target_link_libraries(libprotobuf PRIVATE utf8_validity)
+target_link_libraries(libprotobuf PRIVATE utf8_range::utf8_validity)
diff --git a/cmake/utf8_range.cmake b/cmake/utf8_range.cmake
index f411a8c5b..21bf8235b 100644
--- a/cmake/utf8_range.cmake
+++ b/cmake/utf8_range.cmake
@@ -1,4 +1,4 @@
-if (NOT TARGET utf8_range)
+if (0)
   set(utf8_range_ENABLE_TESTS OFF CACHE BOOL "Disable utf8_range tests")
 
   if (NOT EXISTS "${protobuf_SOURCE_DIR}/third_party/utf8_range/CMakeLists.txt")
@@ -12,4 +12,4 @@ if (NOT TARGET utf8_range)
   include_directories(${CMAKE_CURRENT_SOURCE_DIR}/third_party/utf8_range)
 endif ()
 
-set(_protobuf_FIND_UTF8_RANGE "if(NOT TARGET utf8_range)\n  find_package(utf8_range CONFIG)\nendif()")
+set(_protobuf_FIND_UTF8_RANGE "if(NOT TARGET utf8_range::utf8_range)\n  find_package(utf8_range CONFIG)\nendif()")
