{"name": "protobuf", "version": "5.29.3", "description": "Google's language-neutral, platform-neutral, extensible mechanism for serializing structured data.", "homepage": "https://github.com/protocolbuffers/protobuf", "license": "BSD-3-<PERSON><PERSON>", "dependencies": ["abseil", {"name": "protobuf", "host": true}, "utf8-range", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"zlib": {"description": "ZLib based features like Gzip streams", "dependencies": ["zlib"]}}}