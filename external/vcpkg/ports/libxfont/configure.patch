diff --git a/configure.ac b/configure.ac
index f507c285a..a821e7311 100644
--- a/configure.ac	
+++ b/configure.ac
@@ -122,14 +122,14 @@ AC_SUBST(FREETYPE_REQUIRES)
 AC_DEFINE(X_GZIP_FONT_COMPRESSION,1,[Support gzip for bitmap fonts])
 X_GZIP_FONT_COMPRESSION=1
 AC_SUBST(X_GZIP_FONT_COMPRESSION)
-AC_CHECK_LIB(z, gzopen, [Z_LIBS=-lz], AC_MSG_ERROR([*** zlib is required]))
+AC_SEARCH_LIBS(gzopen, [z zlib zlibd] , [Z_LIBS="$ac_cv_search_gzopen"], AC_MSG_ERROR([*** zlib is required]))
 
 AC_ARG_WITH(bzip2,
 	AS_HELP_STRING([--with-bzip2],
 	  [Use libbz2 to support bzip2 compressed bitmap fonts (default: no)]),
 	[], [with_bzip2=no])
 if test "x$with_bzip2" = xyes; then
-	AC_CHECK_LIB(bz2, BZ2_bzopen, [Z_LIBS="$Z_LIBS -lbz2"],
+	AC_SEARCH_LIBS(BZ2_bzopen, [bz2 bz2d], [Z_LIBS="$Z_LIBS $ac_cv_search_BZ2_bzopen"],
 		AC_MSG_ERROR([*** libbz2 is required for bzip2 support]))
 	AC_DEFINE(X_BZIP2_FONT_COMPRESSION,1,[Support bzip2 for bitmap fonts])
 fi
@@ -204,8 +204,13 @@ if test "x$XFONT_FC" = xyes; then
 fi
 
 
-AC_CHECK_LIB(m, hypot, [MATH_LIBS=-lm
-AC_SUBST(MATH_LIBS)], AC_MSG_ERROR([*** libm is required]))
+AC_SEARCH_LIBS([hypot], [m], [MATH_LIBS=-lm AC_SUBST(MATH_LIBS)], 
+[if test "x$ac_cv_search_hypot" != "xnone required"; then
+	AC_MSG_ERROR([*** libm is required])	
+else
+	AC_MSG_ERROR([*** libm is not required])
+fi]
+)
 
 PKG_CHECK_MODULES(XFONT, [xproto xtrans fontsproto >= 2.1.3 fontenc])
 
