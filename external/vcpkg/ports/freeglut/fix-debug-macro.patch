diff --git a/include/GL/freeglut_std.h b/include/GL/freeglut_std.h
index a658c7c..a5efb3b 100644
--- a/include/GL/freeglut_std.h
+++ b/include/GL/freeglut_std.h
@@ -70,7 +70,7 @@
 
         /* Link with Win32 static freeglut lib */
 #       if FREEGLUT_LIB_PRAGMAS
-#           ifdef NDEBUG
+#           if defined(NDEBUG) || !defined(_DEBUG)
 #              pragma comment (lib, "freeglut_static.lib")
 #           else
 #              pragma comment (lib, "freeglut_staticd.lib")
@@ -88,7 +88,7 @@
 
             /* Link with Win32 shared freeglut lib */
 #           if FREEGLUT_LIB_PRAGMAS
-#               ifdef NDEBUG
+#               if defined(NDEBUG) || !defined(_DEBUG)
 #                   pragma comment (lib, "freeglut.lib")
 #               else
 #                   pragma comment (lib, "freeglutd.lib")
diff --git a/src/blackberry/fg_main_blackberry.c b/src/blackberry/fg_main_blackberry.c
index a1b9cbb..a20c53d 100644
--- a/src/blackberry/fg_main_blackberry.c
+++ b/src/blackberry/fg_main_blackberry.c
@@ -31,7 +31,7 @@
 #include "fg_internal.h"
 #include "egl/fg_window_egl.h"
 
-#ifdef NDEBUG
+#if defined(NDEBUG) || !defined(_DEBUG)
 #define LOGI(...)
 #endif
 
