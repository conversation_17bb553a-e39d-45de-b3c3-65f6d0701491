diff --git a/CMakeLists.txt b/CMakeLists.txt
index 5568b63..bec3de5 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -620,6 +620,15 @@ ELSE()
     SET(PC_LIBS_PRIVATE "-lX11 -lXxf86vm -lXrandr -lGL -lm")
   ENDIF()
 ENDIF()
+if(NOT X11_Xrandr_FOUND)
+    string(REPLACE " -lXrandr" "" PC_LIBS_PRIVATE "${PC_LIBS_PRIVATE}")
+endif()
+if(NOT X11_xf86vmode_FOUND)
+    string(REPLACE " -lXxf86vm" "" PC_LIBS_PRIVATE "${PC_LIBS_PRIVATE}")
+endif()
+if(X11_Xinput_FOUND)
+    string(REPLACE "-lX11 " "-lX11 -lXi " PC_LIBS_PRIVATE "${PC_LIBS_PRIVATE}")
+endif()
 # Client applications need to define FreeGLUT GLES version to
 # bootstrap headers inclusion in freeglut_std.h:
 SET(PC_LIBNAME ${LIBNAME})
