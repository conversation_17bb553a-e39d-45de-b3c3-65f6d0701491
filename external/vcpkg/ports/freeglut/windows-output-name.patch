diff --git a/CMakeLists.txt b/CMakeLists.txt
index 99957a1..9a5fb2b 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -533,12 +533,12 @@ IF(WIN32)
     LIST(APPEND LIBS winmm gdi32)
     IF(FREEGLUT_BUILD_SHARED_LIBS)
         TARGET_COMPILE_DEFINITIONS(freeglut PRIVATE FREEGLUT_EXPORTS)
-        SET_TARGET_PROPERTIES(freeglut PROPERTIES OUTPUT_NAME ${LIBNAME})
+        SET_TARGET_PROPERTIES(freeglut PROPERTIES OUTPUT_NAME freeglut)
     ENDIF()
     IF(FREEGLUT_BUILD_STATIC_LIBS)
         TARGET_COMPILE_DEFINITIONS(freeglut_static PUBLIC FREEGLUT_STATIC)
         IF(FREEGLUT_REPLACE_GLUT)
-            SET_TARGET_PROPERTIES(freeglut_static PROPERTIES OUTPUT_NAME ${LIBNAME})
+            SET_TARGET_PROPERTIES(freeglut_static PROPERTIES OUTPUT_NAME freeglut)
         ENDIF()
         # need to set machine:x64 for linker, at least for VC10, and
         # doesn't hurt for older compilers:
diff --git a/include/GL/freeglut_std.h b/include/GL/freeglut_std.h
index e5da4ce..4eea6eb 100644
--- a/include/GL/freeglut_std.h
+++ b/include/GL/freeglut_std.h
@@ -71,9 +71,9 @@
         /* Link with Win32 static freeglut lib */
 #       if FREEGLUT_LIB_PRAGMAS
 #           if defined(NDEBUG) || !defined(_DEBUG)
-#              pragma comment (lib, "freeglut_static.lib")
+#              pragma comment (lib, "freeglut.lib")
 #           else
-#              pragma comment (lib, "freeglut_staticd.lib")
+#              pragma comment (lib, "freeglutd.lib")
 #           endif
 #       endif
 
