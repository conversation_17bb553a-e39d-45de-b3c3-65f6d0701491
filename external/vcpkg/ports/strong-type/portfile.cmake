vcpkg_from_github(OUT_SOURCE_PATH SOURCE_PATH
    REPO "rollbear/strong_type"
    REF "v${VERSION}"
    SHA512 "67F6F0AC34703206A5F45B492CB52ACAF87189D137B3CA5F9A0BB3708FE91FF4FBA61B6BFD9CDC8D2494D112F7964C8BE366F2ECECEF3B8B5B7CC1F318EFAFDD"
)

vcpkg_cmake_configure(SOURCE_PATH "${SOURCE_PATH}")
vcpkg_cmake_install()

vcpkg_cmake_config_fixup(PACKAGE_NAME "strong_type" CONFIG_PATH "lib/cmake/strong_type")
file(REMOVE_RECURSE "${CURRENT_PACKAGES_DIR}/debug" "${CURRENT_PACKAGES_DIR}/lib")
vcpkg_install_copyright(FILE_LIST "${SOURCE_PATH}/LICENSE")
