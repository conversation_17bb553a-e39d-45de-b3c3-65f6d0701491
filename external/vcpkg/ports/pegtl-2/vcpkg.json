{"name": "pegtl-2", "version": "2.8.3", "port-version": 3, "description": "The Parsing Expression Grammar Template Library (PEGTL) is a zero-dependency C++ header-only parser combinator library for creating parsers according to a Parsing Expression Grammar (PEG). This version maintains compatibility with C++11.", "homepage": "https://github.com/taocpp/PEGTL", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}]}