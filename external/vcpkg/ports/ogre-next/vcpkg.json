{"name": "ogre-next", "version": "2.3.3", "port-version": 3, "description": "Ogre Next - scene-oriented, flexible 3D engine written in C++", "homepage": "https://github.com/OGRECave/ogre-next", "license": "MIT", "supports": "!emscripten & !uwp & !xbox", "dependencies": ["freeimage", {"name": "freetype", "default-features": false}, "<PERSON><PERSON><PERSON>", "tinyxml", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}, "zlib", "zziplib"], "default-features": [{"name": "d3d11", "platform": "windows"}, {"name": "gl3plus", "platform": "!android & !ios & !osx & !windows"}, {"name": "metal", "platform": "ios | osx"}, {"name": "vulkan", "platform": "android"}], "features": {"d3d11": {"description": "Direct3D 11 render system", "supports": "windows"}, "gl3plus": {"description": "OpenGL 3+ render system", "supports": "!android & !(arm & windows)"}, "metal": {"description": "Metal render system", "supports": "osx | ios"}, "planar-reflections": {"description": "Component to use planar reflections, can be used by both HlmsPbs and HlmsUnlit"}, "vulkan": {"description": "Vulkan render system", "supports": "!osx", "dependencies": ["glslang", "vulkan"]}}}