diff --git a/CMake/CMakeLists.txt b/CMake/CMakeLists.txt
index cdb097a..967d33b 100644
--- a/CMake/CMakeLists.txt
+++ b/CMake/CMakeLists.txt
@@ -21,9 +21,7 @@ endif()
 set(OGRE_CMAKE_DIR "share/ogre-next")
 
 set(INST_FILES
-  Packages/FindRapidjson.cmake
   Packages/FindRemotery.cmake
-  Packages/FindSDL2.cmake
   Packages/FindOGRE.cmake
   Utils/FindPkgMacros.cmake
   Utils/MacroLogFeature.cmake
@@ -45,12 +43,8 @@ endif ()
 
 set(STATIC_INST_FILES
   Packages/FindDirectX.cmake
-  Packages/FindFreeImage.cmake
-  Packages/FindFreetype.cmake
   Packages/FindOpenGLES.cmake
   Packages/FindOpenGLES2.cmake
-  Packages/FindZLIB.cmake
-  Packages/FindZZip.cmake
   Packages/FindSoftimage.cmake
 )
 if (WIN32)
diff --git a/CMake/Dependencies.cmake b/CMake/Dependencies.cmake
index 74cb0f4..2cad26a 100644
--- a/CMake/Dependencies.cmake
+++ b/CMake/Dependencies.cmake
@@ -74,21 +74,24 @@ set(CMAKE_FRAMEWORK_PATH ${OGRE_DEP_SEARCH_PATH} ${CMAKE_FRAMEWORK_PATH})
 #######################################################################
 
 # Find zlib
-find_package(ZLIB)
+find_package(ZLIB REQUIRED)
 macro_log_feature(ZLIB_FOUND "zlib" "Simple data compression library" "http://www.zlib.net" FALSE "" "")
 
 if (ZLIB_FOUND)
   # Find zziplib
-  find_package(ZZip)
+  find_package(ZZip NAMES zziplib CONFIG REQUIRED)
+  set(ZZip_LIBRARIES zziplib::libzzip)
   macro_log_feature(ZZip_FOUND "zziplib" "Extract data from zip archives" "http://zziplib.sourceforge.net" FALSE "" "")
 endif ()
 
 # Find FreeImage
-find_package(FreeImage)
+find_package(FreeImage NAMES freeimage REQUIRED)
+set(FreeImage_LIBRARIES freeimage::FreeImage)
 macro_log_feature(FreeImage_FOUND "freeimage" "Support for commonly used graphics image formats" "http://freeimage.sourceforge.net" FALSE "" "")
 
 # Find FreeType
-find_package(Freetype)
+find_package(FREETYPE NAMES freetype REQUIRED)
+set(FREETYPE_LIBRARIES freetype)
 macro_log_feature(FREETYPE_FOUND "freetype" "Portable font engine" "http://www.freetype.org" FALSE "" "")
 
 find_package(Vulkan)
@@ -105,7 +108,7 @@ if (UNIX AND NOT APPLE AND NOT ANDROID AND NOT EMSCRIPTEN)
 endif ()
 
 # Find rapidjson
-find_package(Rapidjson)
+find_package(Rapidjson NAMES RapidJSON CONFIG REQUIRED)
 macro_log_feature(Rapidjson_FOUND "rapidjson" "C++ JSON parser" "https://rapidjson.org/" FALSE "" "")
 
 find_package(RenderDoc)
