{"$comment": ["NOTE: All changes made to this file will get overwritten by the next port release.", "Please contribute your changes to https://github.com/Azure/azure-sdk-for-cpp."], "name": "azure-security-attestation-cpp", "version-semver": "1.1.0", "port-version": 6, "description": ["Microsoft Azure Attestation Service SDK for C++", "This library provides API access to the Microsoft Azure Attestation service."], "homepage": "https://github.com/Azure/azure-sdk-for-cpp/tree/main/sdk/attestation/azure-security-attestation", "license": "MIT", "dependencies": [{"name": "azure-core-cpp", "default-features": false, "version>=": "1.8.0"}, "openssl", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}]}