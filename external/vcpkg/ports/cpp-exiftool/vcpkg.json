{"name": "cpp-exiftool", "version": "1.8.0", "description": "The C++ interface for exiftool provides the source code for a set of objects that allow C++ applications to easily leverage the full power of the exiftool application through a simple interface. This interface handles all the hard work of launching, monitoring, controlling, and communicating with an external exiftool process.", "homepage": "https://exiftool.org/cpp_exiftool/", "license": null, "supports": "linux", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}]}