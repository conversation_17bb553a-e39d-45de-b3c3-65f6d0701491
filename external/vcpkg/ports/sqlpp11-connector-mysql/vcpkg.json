{"name": "sqlpp11-connector-mysql", "version": "0.61", "description": "A C++ wrapper for MySQL meant to be used in combination with sqlpp11 (obsolete, use sqlpp11 with the mariadb/mysql feature instead)", "homepage": "https://github.com/rbock/sqlpp11-connector-mysql", "license": "BSD-2-<PERSON><PERSON>", "default-features": ["ma<PERSON>b"], "features": {"mariadb": {"description": "Use MariaDB connector", "dependencies": [{"name": "sqlpp11", "default-features": false, "features": ["ma<PERSON>b"], "version>=": "0.61"}]}, "mysql": {"description": "Use MySQL connector", "dependencies": [{"name": "sqlpp11", "default-features": false, "features": ["mysql"], "version>=": "0.61"}]}}}