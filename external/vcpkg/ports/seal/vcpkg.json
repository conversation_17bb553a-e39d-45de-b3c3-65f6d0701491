{"name": "seal", "version": "4.1.2", "description": "Microsoft SEAL is an easy-to-use and powerful homomorphic encryption library.", "homepage": "https://github.com/microsoft/SEAL", "license": "MIT", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "default-features": ["ms-gsl", "zlib", "zstd"], "features": {"hexl": {"description": "Use Intel® HEXL for acceleration of low-level kernels.", "dependencies": ["hexl"]}, "ms-gsl": {"description": "Use ms-gsl for span view", "dependencies": ["ms-gsl"]}, "no-throw-tran": {"description": "Do not throw when operating on transparent ciphertexts"}, "zlib": {"description": "Use zlib for compressed serialization", "dependencies": ["zlib"]}, "zstd": {"description": "Use zstd for compressed serialization", "dependencies": ["zstd"]}}}