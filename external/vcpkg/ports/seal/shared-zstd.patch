diff --git a/CMakeLists.txt b/CMakeLists.txt
index 4cc8a01..b8c92c2 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -205,7 +205,7 @@ if(SEAL_USE_ZSTD)
                     message(FATAL_ERROR "Zstandard: must be static")
                 endif()
             elseif(TARGET zstd::libzstd_shared)
-                message(FATAL_ERROR "Zstandard: must be static")
+                set(zstd_static "zstd::libzstd_shared")
             else()
                 message(FATAL_ERROR "Zstandard: not found")
             endif()
