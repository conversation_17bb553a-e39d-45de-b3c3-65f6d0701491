{"name": "nuklear", "version-date": "2022-05-12", "description": "This is a minimal state immediate mode graphical user interface toolkit written in ANSI C and licensed under public domain", "homepage": "https://github.com/Immediate-Mode-UI/Nuklear", "license": "Unlicense OR MIT", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"demo": {"description": "Install demo files"}, "example": {"description": "Install example files"}}}