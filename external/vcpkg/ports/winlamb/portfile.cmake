# Header-only library
vcpkg_from_github(
    OUT_SOURCE_PATH SOURCE_PATH
    REPO rodrigocfd/winlamb
    REF b7a6767994b2a8c0782c6b6e7b9e78a224a078cf
    SHA512 343a900ef003eac47489b34d2b5affc1f133929ff036d535f604fbc5771767075835eed59acd7b93674729badf28ac99f6ba10ac3bf34af6aa4ac49021925e7c
    HEAD_REF master
)

file(GLOB WINLAMB_PUBLIC_HEADERS ${SOURCE_PATH}/*.h)
file(GLOB WINLAMB_INTERNAL_HEADERS ${SOURCE_PATH}/internals/*.h)
file(INSTALL ${WINLAMB_PUBLIC_HEADERS} DESTINATION ${CURRENT_PACKAGES_DIR}/include/${PORT})
file(INSTALL ${WINLAMB_INTERNAL_HEADERS} DESTINATION ${CURRENT_PACKAGES_DIR}/include/${PORT}/internals)

file(INSTALL ${SOURCE_PATH}/win10.exe.manifest DESTINATION ${CURRENT_PACKAGES_DIR}/share/${PORT})

file(INSTALL ${SOURCE_PATH}/LICENSE.txt DESTINATION ${CURRENT_PACKAGES_DIR}/share/${PORT} RENAME copyright)
