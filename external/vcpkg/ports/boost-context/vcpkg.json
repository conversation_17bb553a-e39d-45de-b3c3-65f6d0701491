{"$comment": "Automatically generated by scripts/boost/generate-ports.ps1", "name": "boost-context", "version": "1.87.0", "port-version": 1, "description": "Boost context module", "homepage": "https://www.boost.org/libs/context", "license": "BSL-1.0", "supports": "!uwp & !emscripten", "dependencies": [{"name": "boost-assert", "version>=": "1.87.0"}, {"name": "boost-cmake", "version>=": "1.87.0"}, {"name": "boost-config", "version>=": "1.87.0"}, {"name": "boost-core", "version>=": "1.87.0"}, {"name": "boost-headers", "version>=": "1.87.0"}, {"name": "boost-mp11", "version>=": "1.87.0"}, {"name": "boost-pool", "version>=": "1.87.0"}, {"name": "boost-predef", "version>=": "1.87.0"}, {"name": "boost-smart-ptr", "version>=": "1.87.0"}]}