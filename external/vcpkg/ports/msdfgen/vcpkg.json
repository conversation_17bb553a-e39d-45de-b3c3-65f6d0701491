{"name": "msdfgen", "version": "1.12", "description": "Multi-channel signed distance field generator", "homepage": "https://github.com/Chlumsky/msdfgen", "license": "MIT", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "default-features": ["extensions", "geometry-preprocessing"], "features": {"extensions": {"description": "Extended functionality that depends on external libraries - loading fonts and SVG files, generating PNG images.", "dependencies": ["freetype", "libpng", "tinyxml2"]}, "geometry-preprocessing": {"description": "Preprocessing of non-compliant vector geometry via the Skia library.", "dependencies": [{"name": "msdfgen", "default-features": false, "features": ["extensions"]}, {"name": "skia", "default-features": false}]}, "openmp": {"description": "Build with OpenMP support for multi-threaded code."}, "tools": {"description": "Generates an executable inside the tools folder.", "supports": "!uwp", "dependencies": [{"name": "msdfgen", "default-features": false, "features": ["extensions"]}]}}}