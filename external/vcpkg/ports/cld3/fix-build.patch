diff --git a/CMakeLists.txt b/CMakeLists.txt
index 2fa3908..37e9944 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -1,11 +1,11 @@
 # This cmake scripts only builds a static cld3 lib and the unittests.
 
-project(cld3)
 
 # Old versions of cmake dont search/find protobuf lite
 cmake_minimum_required(VERSION 3.9)
+project(cld3)
 
-find_package(Protobuf REQUIRED)
+find_package(Protobuf CONFIG REQUIRED)
 message(STATUS "Protobuf_FOUND= ${Protobuf_FOUND}")
 message(STATUS "Protobuf_VERSION= ${Protobuf_VERSION}")
 message(WARNING "Protobuf 2.5 and CLD3 seems happy together. This script does NOT check if your verison of protobuf is compatible.")
@@ -20,9 +20,6 @@ include(${CMAKE_CURRENT_SOURCE_DIR}/misc/myprotobuf.cmake)
 my_protobuf_generate_cpp(cld_3/protos PROTO_SRCS PROTO_HDRS src/feature_extractor.proto src/sentence.proto src/task_spec.proto)
 message(STATUS "PROTO_HDRS= ${PROTO_HDRS}")
 
-add_definitions(-fPIC) # Position Independant Code
-add_definitions(-D_GLIBCXX_USE_CXX11_ABI=0)
-add_definitions(-std=c++11) # Needed for std::to_string(), ...
 
 include_directories(${CMAKE_CURRENT_BINARY_DIR} ${Protobuf_INCLUDE_DIRS}) # needed to include generated pb headers
 
@@ -58,6 +55,10 @@ add_library(${PROJECT_NAME}
 	src/script_span/fixunicodevalue.cc
 	)
 
+target_compile_features(${PROJECT_NAME} PUBLIC cxx_std_11)
+target_link_libraries(${PROJECT_NAME} PUBLIC protobuf::libprotobuf-lite)
+
+if(BUILD_TESTS)
 # unit tests exec:
 add_executable(language_identifier_main src/language_identifier_main.cc)
 target_link_libraries(language_identifier_main cld3 ${Protobuf_LITE_LIBRARIES})
@@ -67,3 +68,4 @@ target_link_libraries(getonescriptspan_test cld3 ${Protobuf_LITE_LIBRARIES})
 
 add_executable(language_identifier_features_test src/language_identifier_features_test.cc)
 target_link_libraries(language_identifier_features_test cld3 ${Protobuf_LITE_LIBRARIES})
+endif()
diff --git a/src/base.h b/src/base.h
index 20189e2..3c610e3 100644
--- a/src/base.h
+++ b/src/base.h
@@ -71,7 +71,7 @@ typedef unsigned short uint16;  // NOLINT
 typedef signed int char32;
 #endif  // SWIG
 
-#ifdef COMPILER_MSVC
+#ifdef _MSC_VER
 typedef __int64 int64;
 #else
 typedef long long int64;  // NOLINT
diff --git a/src/script_span/utf8statetable.cc b/src/script_span/utf8statetable.cc
index 8fcfb1e..87da972 100644
--- a/src/script_span/utf8statetable.cc
+++ b/src/script_span/utf8statetable.cc
@@ -17,7 +17,7 @@
 // 32- or 16-bit Unicode values.
 //
 
-#ifdef COMPILER_MSVC
+#ifdef _MSC_VER
 // MSVC warns: warning C4309: 'initializing' : truncation of constant value
 // But the value is in fact not truncated.  0xFF still comes out 0xFF at
 // runtime.
diff --git a/src/sentence_features.h b/src/sentence_features.h
index cc0be88..e2e27d2 100644
--- a/src/sentence_features.h
+++ b/src/sentence_features.h
@@ -34,7 +34,7 @@ using WholeSentenceExtractor = FeatureExtractor<Sentence>;
 // for clang's -Wundefined-var-template.  However, MSVC has a bug which treats
 // this declaration as a definition, leading to multiple definition errors, so
 // omit this on MSVC.
-#if !defined(COMPILER_MSVC)
+#if !defined(_MSC_VER)
 template <>
 WholeSentenceFeature::Registry
     *RegisterableClass<WholeSentenceFeature>::registry_;
