diff --git a/CMakeLists.txt b/CMakeLists.txt
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -18,7 +18,8 @@ option(UUID_USING_CXX20_SPAN "Using span from std instead of gsl" OFF)
 add_library(${PROJECT_NAME} INTERFACE)
 target_include_directories(${PROJECT_NAME} INTERFACE
         $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
-        $<INSTALL_INTERFACE:include>)
+        $<INSTALL_INTERFACE:include>
+        $<INSTALL_INTERFACE:include/stduuid>)
 
 # Using system uuid generator
 if (UUID_SYSTEM_GENERATOR)
@@ -52,7 +52,7 @@
 
 if(UUID_ENABLE_INSTALL)
     # Install step and imported target
-    install(FILES include/uuid.h DESTINATION include)
+    install(FILES include/uuid.h DESTINATION include/stduuid)
     install(TARGETS ${PROJECT_NAME} EXPORT ${PROJECT_NAME}-targets)
     install(EXPORT ${PROJECT_NAME}-targets
             DESTINATION lib/cmake/${PROJECT_NAME})
