{"name": "st<PERSON><PERSON>", "version": "1.2.3", "description": "A C++17 cross-platform implementation for UUIDs", "homepage": "https://github.com/mariusbancila/stduuid", "license": "MIT", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"gsl-span": {"description": "Using span from gsl instead of std", "dependencies": ["ms-gsl"]}, "system-gen": {"description": "Enable operating system uuid generator", "dependencies": [{"name": "libuuid", "platform": "!osx & !windows"}]}}}