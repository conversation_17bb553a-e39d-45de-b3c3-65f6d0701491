diff --git a/CMakeLists.txt b/CMakeLists.txt
index 039ee53..d4218a4 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -23,11 +23,8 @@ if (UUID_SYSTEM_GENERATOR)
         find_library(CFLIB CoreFoundation REQUIRED)
         target_link_libraries(${PROJECT_NAME} INTERFACE ${CFLIB})
     else ()
-        find_package(Libuuid REQUIRED)
-        if (Libuuid_FOUND)
-            target_include_directories(${PROJECT_NAME} INTERFACE ${Libuuid_INCLUDE_DIRS})
-            target_link_libraries(${PROJECT_NAME} INTERFACE ${Libuuid_LIBRARIES})
-        endif ()
+        find_package(unofficial-libuuid CONFIG REQUIRED)
+        target_link_libraries(${PROJECT_NAME} INTERFACE unofficial::UUID::uuid)
     endif ()
 endif ()
 
diff --git a/cmake/Config.cmake.in b/cmake/Config.cmake.in
index fb981d2..8a3c8ab 100644
--- a/cmake/Config.cmake.in
+++ b/cmake/Config.cmake.in
@@ -4,8 +4,7 @@ include(CMakeFindDependencyMacro)
 if (@UUID_SYSTEM_GENERATOR@)
     if (WIN32 OR APPLE)
     else ()
-        list (APPEND CMAKE_MODULE_PATH ${CMAKE_CURRENT_LIST_DIR})
-        find_dependency(Libuuid REQUIRED)
+        find_dependency(unofficial-libuuid CONFIG)
     endif ()
 endif ()
 
