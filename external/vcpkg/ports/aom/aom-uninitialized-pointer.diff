diff --git a/build/cmake/aom_configure.cmake b/build/cmake/aom_configure.cmake
index aaef2c310..5500ad4a3 100644
--- a/build/cmake/aom_configure.cmake
+++ b/build/cmake/aom_configure.cmake
@@ -309,6 +309,8 @@ if(MSVC)
 
   # Disable MSVC warnings that suggest making code non-portable.
   add_compiler_flag_if_supported("/wd4996")
+  # Disable MSVC warnings for potentially uninitialized local pointer variable.
+  add_compiler_flag_if_supported("/wd4703")
   if(ENABLE_WERROR)
     add_compiler_flag_if_supported("/WX")
   endif()
