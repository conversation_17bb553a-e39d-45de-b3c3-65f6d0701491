--- a/cmake/OpenCVGenPkgconfig.cmake
+++ b/cmake/OpenCVGenPkgconfig.cmake
@@ -54,6 +54,12 @@ foreach(CVLib ${OpenCV_LIB_COMPONENTS})
     set(libname "${CVLib}")
   endif()
 
+  string(TOUPPER "${CMAKE_BUILD_TYPE}" build_type)
+  get_target_property(libsuffix ${CVLib} ${build_type}_POSTFIX)
+  if(libsuffix)
+    string(APPEND libname "${libsuffix}")
+  endif()
+
   set(libpath "\${exec_prefix}/${OPENCV_LIB_INSTALL_PATH}")
   list(APPEND OpenCV_LIB_COMPONENTS_ "-L${libpath}")
   list(APPEND OpenCV_LIB_COMPONENTS_ "-l${libname}")
