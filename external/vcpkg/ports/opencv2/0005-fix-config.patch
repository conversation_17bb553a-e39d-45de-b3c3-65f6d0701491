--- a/cmake/templates/OpenCVConfig.cmake.in
+++ b/cmake/templates/OpenCVConfig.cmake.in
@@ -97,7 +97,6 @@ set(OpenCV_USE_MANGLED_PATHS @OpenCV_USE_MANGLED_PATHS_CONFIGCMAKE@)
 # Extract the directory where *this* file has been installed (determined at cmake run-time)
 get_filename_component(OpenCV_CONFIG_PATH "${CMAKE_CURRENT_LIST_FILE}" PATH CACHE)
 
-if(NOT WIN32 OR ANDROID)
   if(ANDROID)
     set(OpenCV_INSTALL_PATH "${OpenCV_CONFIG_PATH}/../../..")
   else()
@@ -109,7 +108,6 @@ if(NOT WIN32 OR ANDROID)
   else()
     get_filename_component(OpenCV_INSTALL_PATH "${OpenCV_INSTALL_PATH}" REALPATH)
   endif()
-endif()
 
 # Presence of Android native camera wrappers
 set(OpenCV_HAVE_ANDROID_CAMERA @HAVE_opencv_androidcamera@)
