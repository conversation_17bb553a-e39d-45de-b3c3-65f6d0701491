--- a/cmake/OpenCVFindLibsGrfmt.cmake
+++ b/cmake/OpenCVFindLibsGrfmt.cmake
@@ -6,7 +6,7 @@
 if(BUILD_ZLIB)
   ocv_clear_vars(ZLIB_FOUND)
 else()
-  include(FindZLIB)
+  find_package(ZLIB REQUIRED)
   if(ZLIB_FOUND AND ANDROID)
     if(ZLIB_LIBRARIES STREQUAL "${ANDROID_SYSROOT}/usr/lib/libz.so" OR
         ZLIB_LIBRARIES STREQUAL "${ANDROID_SYSROOT}/usr/lib64/libz.so")
@@ -32,7 +32,7 @@ if(WITH_TIFF)
   if(BUILD_TIFF)
     ocv_clear_vars(TIFF_FOUND)
   else()
-    include(FindTIFF)
+    find_package(TIFF REQUIRED)
     if(TIFF_FOUND)
       ocv_parse_header("${TIFF_INCLUDE_DIR}/tiff.h" TIFF_VERSION_LINES TIFF_VERSION_CLASSIC TIFF_VERSION_BIG TIFF_VERSION TIFF_BIGTIFF_VERSION)
     endif()
@@ -74,7 +74,7 @@ if(WITH_JPEG)
   if(BUILD_JPEG)
     ocv_clear_vars(JPEG_FOUND)
   else()
-    include(FindJPEG)
+    find_package(JPEG REQUIRED)
   endif()
 
   if(NOT JPEG_FOUND)
@@ -95,7 +95,7 @@ if(WITH_JASPER)
   if(BUILD_JASPER)
     ocv_clear_vars(JASPER_FOUND)
   else()
-    include(FindJasper)
+    find_package(Jasper REQUIRED)
   endif()
 
   if(NOT JASPER_FOUND)
@@ -119,7 +119,7 @@ if(WITH_PNG)
   if(BUILD_PNG)
     ocv_clear_vars(PNG_FOUND)
   else()
-    include(FindPNG)
+    find_package(PNG REQUIRED)
     if(PNG_FOUND)
       include(CheckIncludeFile)
       check_include_file("${PNG_PNG_INCLUDE_DIR}/libpng/png.h" HAVE_LIBPNG_PNG_H)
