--- a/3rdparty/libtiff/CMakeLists.txt
+++ b/3rdparty/libtiff/CMakeLists.txt
@@ -102,7 +102,7 @@ if(UNIX AND (CMAKE_COMPILER_IS_GNUCXX OR CV_ICC))
 endif()
 
 add_library(${TIFF_LIBRARY} STATIC ${lib_srcs})
-target_link_libraries(${TIFF_LIBRARY} ${ZLIB_LIBRARIES})
+target_link_libraries(${TIFF_LIBRARY} ZLIB::ZLIB)
 
 set_target_properties(${TIFF_LIBRARY}
     PROPERTIES
--- a/3rdparty/openexr/CMakeLists.txt
+++ b/3rdparty/openexr/CMakeLists.txt
@@ -55,7 +55,7 @@ if(MSVC AND CV_ICC)
 endif()
 
 add_library(IlmImf STATIC ${lib_hdrs} ${lib_srcs})
-target_link_libraries(IlmImf ${ZLIB_LIBRARIES})
+target_link_libraries(IlmImf ZLIB::ZLIB)
 
 set_target_properties(IlmImf
     PROPERTIES
--- a/3rdparty/tbb/CMakeLists.txt
+++ b/3rdparty/tbb/CMakeLists.txt
@@ -116,7 +116,7 @@ endif()
 
 if(NOT EXISTS "${tbb_tarball}")
   message(STATUS "Downloading ${tbb_ver}_src.tgz")
-  file(DOWNLOAD "${tbb_url}" "${tbb_tarball}" TIMEOUT 600 STATUS __statvar)
+  message(FATAL_ERROR "    Downloads are not permitted during configure. Please pre-download the file \"${CACHE_CANDIDATE}\":\n    \n    vcpkg_download_distfile(OCV_DOWNLOAD\n        URLS \"${tbb_url}\"\n        FILENAME \"${tbb_tarball}\"\n        SHA512 0\n    )")
   if(NOT __statvar EQUAL 0)
     message(FATAL_ERROR "Failed to download TBB sources (${__statvar}): ${tbb_url}")
   endif()
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -275,23 +275,10 @@ if (ANDROID)
 endif()
 
 if(NOT DEFINED OPENCV_DOC_INSTALL_PATH)
-  if(ANDROID OR WIN32)
-    set(OPENCV_DOC_INSTALL_PATH doc)
-  elseif(INSTALL_TO_MANGLED_PATHS)
-    set(OPENCV_DOC_INSTALL_PATH share/OpenCV-${OPENCV_VERSION}/doc)
-  else()
-    set(OPENCV_DOC_INSTALL_PATH share/OpenCV/doc)
-  endif()
+    set(OPENCV_DOC_INSTALL_PATH share/opencv/doc)
 endif()
 
-if(WIN32)
-  if(DEFINED OpenCV_RUNTIME AND DEFINED OpenCV_ARCH)
-    set(OpenCV_INSTALL_BINARIES_PREFIX "${OpenCV_ARCH}/${OpenCV_RUNTIME}/")
-  else()
-    message(STATUS "Can't detect runtime and/or arch")
-    set(OpenCV_INSTALL_BINARIES_PREFIX "")
-  endif()
-elseif(ANDROID)
+if(ANDROID)
   set(OpenCV_INSTALL_BINARIES_PREFIX "sdk/native/")
 else()
   set(OpenCV_INSTALL_BINARIES_PREFIX "")
@@ -322,29 +309,13 @@ if(ANDROID)
   set(OPENCV_INCLUDE_INSTALL_PATH sdk/native/jni/include)
   set(OPENCV_SAMPLES_SRC_INSTALL_PATH samples/native)
 else()
-  set(LIBRARY_OUTPUT_PATH         "${OpenCV_BINARY_DIR}/lib")
-  set(3P_LIBRARY_OUTPUT_PATH      "${OpenCV_BINARY_DIR}/3rdparty/lib${LIB_SUFFIX}")
-  if(WIN32)
-    if(OpenCV_STATIC)
-      set(OPENCV_LIB_INSTALL_PATH   "${OpenCV_INSTALL_BINARIES_PREFIX}staticlib${LIB_SUFFIX}")
-    else()
-      set(OPENCV_LIB_INSTALL_PATH   "${OpenCV_INSTALL_BINARIES_PREFIX}lib${LIB_SUFFIX}")
-    endif()
-    set(OPENCV_3P_LIB_INSTALL_PATH  "${OpenCV_INSTALL_BINARIES_PREFIX}staticlib${LIB_SUFFIX}")
-    set(OPENCV_SAMPLES_SRC_INSTALL_PATH    samples/native)
-  else()
-    set(OPENCV_LIB_INSTALL_PATH     lib${LIB_SUFFIX})
-    set(OPENCV_3P_LIB_INSTALL_PATH  share/OpenCV/3rdparty/${OPENCV_LIB_INSTALL_PATH})
-    set(OPENCV_SAMPLES_SRC_INSTALL_PATH    share/OpenCV/samples)
-  endif()
-  set(OPENCV_INCLUDE_INSTALL_PATH "include")
-
-  math(EXPR SIZEOF_VOID_P_BITS "8 * ${CMAKE_SIZEOF_VOID_P}")
-  if(LIB_SUFFIX AND NOT SIZEOF_VOID_P_BITS EQUAL LIB_SUFFIX)
-    set(OPENCV_CONFIG_INSTALL_PATH lib${LIB_SUFFIX}/cmake/opencv)
-  else()
-    set(OPENCV_CONFIG_INSTALL_PATH share/OpenCV)
-  endif()
+  set(LIBRARY_OUTPUT_PATH                "${OpenCV_BINARY_DIR}/lib")
+  set(3P_LIBRARY_OUTPUT_PATH             "${LIBRARY_OUTPUT_PATH}")
+  set(OPENCV_LIB_INSTALL_PATH            "${OpenCV_INSTALL_BINARIES_PREFIX}lib")
+  set(OPENCV_3P_LIB_INSTALL_PATH         "${OPENCV_LIB_INSTALL_PATH}")
+  set(OPENCV_SAMPLES_SRC_INSTALL_PATH    "share/opencv2/samples")
+  set(OPENCV_INCLUDE_INSTALL_PATH        "include/opencv2.4")
+  set(OPENCV_CONFIG_INSTALL_PATH         "share/opencv2")
 endif()
 
 set(CMAKE_INSTALL_RPATH "${CMAKE_INSTALL_PREFIX}/${OPENCV_LIB_INSTALL_PATH}")
@@ -354,15 +325,8 @@ if(INSTALL_TO_MANGLED_PATHS)
   set(OPENCV_INCLUDE_INSTALL_PATH ${OPENCV_INCLUDE_INSTALL_PATH}/opencv-${OPENCV_VERSION})
 endif()
 
-if(WIN32)
-  # Postfix of DLLs:
-  set(OPENCV_DLLVERSION "${OPENCV_VERSION_MAJOR}${OPENCV_VERSION_MINOR}${OPENCV_VERSION_PATCH}")
+  set(OPENCV_DLLVERSION 2)
   set(OPENCV_DEBUG_POSTFIX d)
-else()
-  # Postfix of so's:
-  set(OPENCV_DLLVERSION "")
-  set(OPENCV_DEBUG_POSTFIX "")
-endif()
 
 if(DEFINED CMAKE_DEBUG_POSTFIX)
   set(OPENCV_DEBUG_POSTFIX "${CMAKE_DEBUG_POSTFIX}")
@@ -397,8 +361,6 @@ if(CMAKE_HOST_WIN32)
   endif()
 endif()
 
-find_host_program(GIT_EXECUTABLE NAMES ${git_names} PATH_SUFFIXES Git/cmd Git/bin DOC "git command line client")
-mark_as_advanced(GIT_EXECUTABLE)
 
 if(GIT_EXECUTABLE)
   execute_process(COMMAND ${GIT_EXECUTABLE} describe --tags --always --dirty --match "2.[0-9].[0-9]*"
@@ -501,7 +463,9 @@ if(BUILD_DOCS)
 endif(BUILD_DOCS)
 
 # --- Python Support ---
-include(cmake/OpenCVDetectPython.cmake)
+if(WITH_PYTHON)
+  include(cmake/OpenCVDetectPython.cmake)
+endif()
 
 # --- Java Support ---
 include(cmake/OpenCVDetectApacheAnt.cmake)
@@ -885,11 +849,7 @@ if(ANDROID)
 endif()
 
 if(DEFINED WITH_FFMPEG OR HAVE_FFMPEG)
-  if(WIN32)
-    status("    FFMPEG:"       WITH_FFMPEG         THEN "YES (prebuilt binaries)"                  ELSE NO)
-  else()
     status("    FFMPEG:"       HAVE_FFMPEG         THEN YES ELSE NO)
-  endif()
   status("      avcodec:"      FFMPEG_libavcodec_FOUND    THEN "YES (ver ${FFMPEG_libavcodec_VERSION})"    ELSE NO)
   status("      avformat:"     FFMPEG_libavformat_FOUND   THEN "YES (ver ${FFMPEG_libavformat_VERSION})"   ELSE NO)
   status("      avutil:"       FFMPEG_libavutil_FOUND     THEN "YES (ver ${FFMPEG_libavutil_VERSION})"     ELSE NO)
--- a/cmake/OpenCVCompilerOptions.cmake
+++ b/cmake/OpenCVCompilerOptions.cmake
@@ -269,7 +269,6 @@ if(MSVC)
     set(OPENCV_EXTRA_FLAGS "${OPENCV_EXTRA_FLAGS} /bigobj")
   endif()
   if(BUILD_WITH_DEBUG_INFO)
-    set(OPENCV_EXTRA_FLAGS_RELEASE "${OPENCV_EXTRA_FLAGS_RELEASE} /Zi")
   endif()
 
   if(ENABLE_SSE4_1 AND CV_ICC AND NOT OPENCV_EXTRA_FLAGS MATCHES "/arch:")
--- a/cmake/OpenCVFindLibsVideo.cmake
+++ b/cmake/OpenCVFindLibsVideo.cmake
@@ -185,12 +185,8 @@ endif(WITH_XIMEA)
 # --- FFMPEG ---
 ocv_clear_vars(HAVE_FFMPEG)
 if(WITH_FFMPEG)
-  if(WIN32 AND NOT ARM)
-    include("${OpenCV_SOURCE_DIR}/3rdparty/ffmpeg/ffmpeg_version.cmake")
+    find_package(FFMPEG REQUIRED)
     set(HAVE_FFMPEG TRUE)
-  elseif(PKG_CONFIG_FOUND)
-    ocv_check_modules(FFMPEG libavcodec libavformat libavutil libswscale)
-    ocv_check_modules(FFMPEG_libavresample libavresample)
     if(FFMPEG_libavresample_FOUND)
       ocv_append_build_options(FFMPEG FFMPEG_libavresample)
     endif()
@@ -211,9 +207,6 @@ if(WITH_FFMPEG)
         ocv_append_build_options(HIGHGUI FFMPEG)
       endif()
     endif()
-  else()
-    message(STATUS "Can't find ffmpeg - 'pkg-config' utility is missing")
-  endif()
 endif(WITH_FFMPEG)
 
 # --- VideoInput/DirectShow ---
--- a/cmake/OpenCVGenConfig.cmake
+++ b/cmake/OpenCVGenConfig.cmake
@@ -101,7 +101,7 @@ endif()
 configure_file("${OpenCV_SOURCE_DIR}/cmake/templates/OpenCVConfig.cmake.in" "${CMAKE_BINARY_DIR}/unix-install/OpenCVConfig.cmake" @ONLY)
 configure_file("${OpenCV_SOURCE_DIR}/cmake/templates/OpenCVConfig-version.cmake.in" "${CMAKE_BINARY_DIR}/unix-install/OpenCVConfig-version.cmake" @ONLY)
 
-if(UNIX) # ANDROID configuration is created here also
+if(1) # ANDROID configuration is created here also
   #http://www.vtk.org/Wiki/CMake/Tutorials/Packaging reference
   # For a command "find_package(<name> [major[.minor]] [EXACT] [REQUIRED|QUIET])"
   # cmake will look in the following dir on unix:
@@ -126,7 +126,7 @@ endif()
 # --------------------------------------------------------------------------------------------
 #  Part 3/3: ${BIN_DIR}/win-install/OpenCVConfig.cmake  -> For use within binary installers/packages
 # --------------------------------------------------------------------------------------------
-if(WIN32)
+if(0)
   set(OpenCV_INCLUDE_DIRS_CONFIGCMAKE "\"\${OpenCV_CONFIG_PATH}/include\" \"\${OpenCV_CONFIG_PATH}/include/opencv\"")
   set(OpenCV2_INCLUDE_DIRS_CONFIGCMAKE "\"\"")
 
--- a/cmake/OpenCVGenPkgconfig.cmake
+++ b/cmake/OpenCVGenPkgconfig.cmake
@@ -95,7 +95,7 @@ set(includedir  "\${prefix}/${OPENCV_INCLUDE_INSTALL_PATH}")
 if(INSTALL_TO_MANGLED_PATHS)
   set(OPENCV_PC_FILE_NAME "opencv-${OPENCV_VERSION}.pc")
 else()
-  set(OPENCV_PC_FILE_NAME opencv.pc)
+  set(OPENCV_PC_FILE_NAME opencv2.pc)
 endif()
 configure_file("${OpenCV_SOURCE_DIR}/cmake/templates/opencv-XXX.pc.in"
                "${CMAKE_BINARY_DIR}/unix-install/${OPENCV_PC_FILE_NAME}"
--- a/cmake/OpenCVModule.cmake
+++ b/cmake/OpenCVModule.cmake
@@ -86,10 +86,10 @@ macro(ocv_add_dependencies full_modname)
   endforeach()
   unset(__depsvar)
 
-  ocv_list_unique(OPENCV_MODULE_${full_modname}_REQ_DEPS)
-  ocv_list_unique(OPENCV_MODULE_${full_modname}_OPT_DEPS)
-  ocv_list_unique(OPENCV_MODULE_${full_modname}_PRIVATE_REQ_DEPS)
-  ocv_list_unique(OPENCV_MODULE_${full_modname}_PRIVATE_OPT_DEPS)
+  #ocv_list_unique(OPENCV_MODULE_${full_modname}_REQ_DEPS)
+  #ocv_list_unique(OPENCV_MODULE_${full_modname}_OPT_DEPS)
+  #ocv_list_unique(OPENCV_MODULE_${full_modname}_PRIVATE_REQ_DEPS)
+  #ocv_list_unique(OPENCV_MODULE_${full_modname}_PRIVATE_OPT_DEPS)
 
   set(OPENCV_MODULE_${full_modname}_REQ_DEPS ${OPENCV_MODULE_${full_modname}_REQ_DEPS}
     CACHE INTERNAL "Required dependencies of ${full_modname} module")
@@ -277,7 +277,7 @@ endfunction()
 
 # sort modules by dependencies
 function(__ocv_sort_modules_by_deps __lst)
-  ocv_list_sort(${__lst})
+  #ocv_list_sort(${__lst})
   set(${__lst}_ORDERED ${${__lst}} CACHE INTERNAL "")
   set(__result "")
   foreach (m ${${__lst}})
@@ -382,7 +382,7 @@ function(__ocv_resolve_dependencies)
     endforeach()
   endforeach()
 
-  ocv_list_sort(OPENCV_MODULES_BUILD)
+  #ocv_list_sort(OPENCV_MODULES_BUILD)
 
   foreach(m ${OPENCV_MODULES_BUILD})
 #    message(STATUS "FULL deps of ${m}: ${deps_${m}}")
@@ -397,7 +397,7 @@ function(__ocv_resolve_dependencies)
   # reorder dependencies
   foreach(m ${OPENCV_MODULES_BUILD})
     __ocv_sort_modules_by_deps(OPENCV_MODULE_${m}_DEPS)
-    ocv_list_sort(OPENCV_MODULE_${m}_DEPS_EXT)
+    #ocv_list_sort(OPENCV_MODULE_${m}_DEPS_EXT)
 
     set(OPENCV_MODULE_${m}_DEPS ${OPENCV_MODULE_${m}_DEPS} CACHE INTERNAL "Flattened dependencies of ${m} module")
     set(OPENCV_MODULE_${m}_DEPS_EXT ${OPENCV_MODULE_${m}_DEPS_EXT} CACHE INTERNAL "Extra dependencies of ${m} module")
@@ -581,8 +581,6 @@ macro(ocv_create_module)
     target_link_libraries(${the_module} LINK_PUBLIC ${OPENCV_MODULE_${the_module}_DEPS})
     target_link_libraries(${the_module} LINK_PUBLIC ${OPENCV_MODULE_${the_module}_DEPS})
     set(extra_deps ${OPENCV_MODULE_${the_module}_DEPS_EXT} ${OPENCV_LINKER_LIBS} ${IPP_LIBS} ${ARGN})
-    ocv_extract_simple_libs(extra_deps _simple_deps _other_deps)
-    target_link_libraries(${the_module} LINK_PRIVATE ${_simple_deps}) # this list goes to "export"
     target_link_libraries(${the_module} LINK_PRIVATE ${extra_deps})
   endif()

--- a/data/CMakeLists.txt
+++ b/data/CMakeLists.txt
@@ -5,8 +5,6 @@ if(ANDROID)
   install(FILES ${HAAR_CASCADES} DESTINATION sdk/etc/haarcascades COMPONENT libs)
   install(FILES ${LBP_CASCADES}  DESTINATION sdk/etc/lbpcascades  COMPONENT libs)
 else()
-  install(FILES ${HAAR_CASCADES} DESTINATION share/OpenCV/haarcascades COMPONENT libs)
-  install(FILES ${LBP_CASCADES}  DESTINATION share/OpenCV/lbpcascades  COMPONENT libs)
 endif()
 
 if(INSTALL_TESTS AND OPENCV_TEST_DATA_PATH)
@@ -51,4 +49,4 @@ if(INSTALL_TESTS AND OPENCV_TEST_DATA_PATH)
       install(FILES ${DATAFILES_CASCADES} DESTINATION share/OpenCV/testdata/data/haarcascades COMPONENT tests)
     endif()
   endif()
-endif()
\ No newline at end of file
+endif()
--- a/modules/core/CMakeLists.txt
+++ b/modules/core/CMakeLists.txt
@@ -1,9 +1,9 @@
 set(the_description "The Core Functionality")
 
 if (NOT HAVE_CUDA OR ENABLE_DYNAMIC_CUDA)
-  ocv_add_module(core PRIVATE_REQUIRED ${ZLIB_LIBRARIES})
+  ocv_add_module(core PRIVATE_REQUIRED ZLIB::ZLIB)
 else()
-  ocv_add_module(core PRIVATE_REQUIRED ${ZLIB_LIBRARIES} ${CUDA_LIBRARIES} ${CUDA_npp_LIBRARY})
+  ocv_add_module(core PRIVATE_REQUIRED ZLIB::ZLIB ${CUDA_LIBRARIES} ${CUDA_npp_LIBRARY})
 endif()
 
 ocv_module_include_directories("${OpenCV_SOURCE_DIR}/modules/dynamicuda/include/" ${ZLIB_INCLUDE_DIR})
--- a/modules/highgui/CMakeLists.txt
+++ b/modules/highgui/CMakeLists.txt
@@ -15,23 +15,23 @@ endif()
 
 if(HAVE_PNG OR HAVE_TIFF OR HAVE_OPENEXR)
   ocv_include_directories(${ZLIB_INCLUDE_DIR})
-  list(APPEND GRFMT_LIBS ${ZLIB_LIBRARIES})
+  list(APPEND GRFMT_LIBS ZLIB::ZLIB)
 endif()
 
 if(HAVE_JPEG)
   ocv_include_directories(${JPEG_INCLUDE_DIR})
-  list(APPEND GRFMT_LIBS ${JPEG_LIBRARIES})
+  list(APPEND GRFMT_LIBS JPEG::JPEG)
 endif()
 
 if(HAVE_PNG)
   add_definitions(${PNG_DEFINITIONS})
   ocv_include_directories(${PNG_INCLUDE_DIR})
-  list(APPEND GRFMT_LIBS ${PNG_LIBRARIES})
+  list(APPEND GRFMT_LIBS PNG::PNG)
 endif()
 
 if(HAVE_TIFF)
   ocv_include_directories(${TIFF_INCLUDE_DIR})
-  list(APPEND GRFMT_LIBS ${TIFF_LIBRARIES})
+  list(APPEND GRFMT_LIBS TIFF::TIFF)
 endif()
 
 if(HAVE_JASPER)
@@ -296,38 +296,5 @@ endif()
 ocv_add_precompiled_headers(${the_module})
 ocv_warnings_disable(CMAKE_CXX_FLAGS -Wno-deprecated-declarations -Wno-clobbered)
 
-if(WIN32 AND WITH_FFMPEG)
-  #copy ffmpeg dll to the output folder
-  if(MSVC64 OR MINGW64)
-    set(FFMPEG_SUFFIX _64)
-  endif()
-
-  set(ffmpeg_bare_name "opencv_ffmpeg${FFMPEG_SUFFIX}.dll")
-  set(ffmpeg_bare_name_ver "opencv_ffmpeg${OPENCV_DLLVERSION}${FFMPEG_SUFFIX}.dll")
-  set(ffmpeg_path "${OpenCV_SOURCE_DIR}/3rdparty/ffmpeg/${ffmpeg_bare_name}")
-
-  #if(MSVC AND CMAKE_VERSION VERSION_GREATER "2.8.2")
-  #  add_custom_command(TARGET ${the_module} POST_BUILD
-  #                     COMMAND ${CMAKE_COMMAND} -E copy "${ffmpeg_path}" "${EXECUTABLE_OUTPUT_PATH}/$<CONFIGURATION>/${ffmpeg_bare_name_ver}"
-  #                     COMMENT "Copying ${ffmpeg_path} to the output directory")
-  #else
-  if(MSVC_IDE)
-    add_custom_command(TARGET ${the_module} POST_BUILD
-                       COMMAND ${CMAKE_COMMAND} -E copy "${ffmpeg_path}" "${EXECUTABLE_OUTPUT_PATH}/Release/${ffmpeg_bare_name_ver}"
-                       COMMAND ${CMAKE_COMMAND} -E copy "${ffmpeg_path}" "${EXECUTABLE_OUTPUT_PATH}/Debug/${ffmpeg_bare_name_ver}"
-                       COMMENT "Copying ${ffmpeg_path} to the output directory")
-  elseif(MSVC AND (CMAKE_GENERATOR MATCHES "Visual"))
-    add_custom_command(TARGET ${the_module} POST_BUILD
-                       COMMAND ${CMAKE_COMMAND} -E copy "${ffmpeg_path}" "${EXECUTABLE_OUTPUT_PATH}/${CMAKE_BUILD_TYPE}/${ffmpeg_bare_name_ver}"
-                       COMMENT "Copying ${ffmpeg_path} to the output directory")
-  else()
-    add_custom_command(TARGET ${the_module} POST_BUILD
-                       COMMAND ${CMAKE_COMMAND} -E copy "${ffmpeg_path}" "${EXECUTABLE_OUTPUT_PATH}/${ffmpeg_bare_name_ver}"
-                       COMMENT "Copying ${ffmpeg_path} to the output directory")
-  endif()
-
-  install(FILES "${ffmpeg_path}" DESTINATION ${OPENCV_BIN_INSTALL_PATH} COMPONENT libs RENAME "${ffmpeg_bare_name_ver}")
-endif()
-
 ocv_add_accuracy_tests()
 ocv_add_perf_tests()
