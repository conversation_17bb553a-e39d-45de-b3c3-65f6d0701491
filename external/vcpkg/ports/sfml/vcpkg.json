{"name": "sfml", "version": "3.0.0", "port-version": 1, "description": "Simple and fast multimedia library", "homepage": "https://github.com/SFML/SFML", "license": "<PERSON><PERSON><PERSON>", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "default-features": ["audio", "graphics", "network", "window"], "features": {"audio": {"description": "Use sfml-audio library", "dependencies": ["libflac", "libogg", "libvorbis", "miniaudio"]}, "graphics": {"description": "Use sfml-graphics library", "dependencies": [{"name": "freetype", "default-features": false}, {"name": "sfml", "default-features": false, "features": ["window"]}, "stb"]}, "network": {"description": "Use sfml-network library"}, "window": {"description": "Use sfml-window library", "dependencies": ["opengl"]}}}