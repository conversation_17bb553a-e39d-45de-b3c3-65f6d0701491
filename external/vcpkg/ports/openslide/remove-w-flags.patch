diff --git a/Makefile.am b/Makefile.am
index d5fba9f80..5846768f5 100644
--- a/Makefile.am
+++ b/Makefile.am
@@ -60,7 +60,7 @@ endif
 .rc.lo:
 	$(AM_V_GEN)$(LIBTOOL) $(AM_V_lt) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --tag=RC --mode=compile $(RC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) -i "$<" -o "$@"
 
-src_libopenslide_la_CPPFLAGS = -pedantic -D_OPENSLIDE_BUILDING_DLL \
+src_libopenslide_la_CPPFLAGS = -D_OPENSLIDE_BUILDING_DLL \
 	$(GLIB2_CFLAGS) $(CAIRO_CFLAGS) $(SQLITE3_CFLAGS) $(LIBXML2_CFLAGS) \
 	$(OPENJPEG_CFLAGS) $(LIBTIFF_CFLAGS) $(LIBPNG_CFLAGS) \
 	$(GDKPIXBUF_CFLAGS) $(ZLIB_CFLAGS) -DG_LOG_DOMAIN=\"Openslide\" \
diff --git a/configure.ac b/configure.ac
index e401ae313..d4326ad96 100644
--- a/configure.ac
+++ b/configure.ac
@@ -195,7 +195,7 @@ AM_CONDITIONAL([CYGWIN_CROSS_TEST], [test -n "$CYGWIN_CROSS_TEST"])
 # Only enable this on MinGW, since otherwise gcc will complain about an
 # unknown option whenever it produces any *other* warnings
 if test "$host_os" = "mingw32"; then
-  CFLAG_MS_FORMAT=-Wno-pedantic-ms-format
+  CFLAG_MS_FORMAT=""
 else
   CFLAG_MS_FORMAT=""
 fi
@@ -203,7 +203,7 @@ AC_SUBST([CFLAG_MS_FORMAT])
 
 # The min/max glib version is actually 2.16, but glib doesn't have special
 # handling for API changes that old
-AC_SUBST(AM_CFLAGS, ['-Wall -Wextra -Wstrict-prototypes -Wmissing-prototypes -Wmissing-declarations -Wnested-externs $(CFLAG_MS_FORMAT) $(CFLAG_VISIBILITY) -DG_DISABLE_SINGLE_INCLUDES -DGLIB_VERSION_MIN_REQUIRED=GLIB_VERSION_2_26 -DGLIB_VERSION_MAX_ALLOWED=GLIB_VERSION_MIN_REQUIRED -fno-common'])
+AC_SUBST(AM_CFLAGS, ['$(CFLAG_MS_FORMAT) $(CFLAG_VISIBILITY) -DG_DISABLE_SINGLE_INCLUDES -DGLIB_VERSION_MIN_REQUIRED=GLIB_VERSION_2_26 -DGLIB_VERSION_MAX_ALLOWED=GLIB_VERSION_MIN_REQUIRED -fno-common'])
 
 AC_SUBST(FEATURE_FLAGS)
 
