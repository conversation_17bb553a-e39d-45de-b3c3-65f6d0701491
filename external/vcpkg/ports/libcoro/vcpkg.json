{"name": "libcoro", "version": "0.14.1", "description": "C++20 coroutine library", "homepage": "https://github.com/jbaldwin/libcoro", "license": "Apache-2.0", "dependencies": [{"name": "vcpkg-cmake", "host": true}], "features": {"networking": {"description": "Include networking features.", "supports": "linux", "dependencies": ["c-ares"]}, "tls": {"description": "Include SSL features.", "dependencies": [{"name": "libcoro", "default-features": false, "features": ["networking"]}, "openssl"]}}}