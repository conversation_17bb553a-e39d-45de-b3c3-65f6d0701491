#header-only library
vcpkg_from_github(
    OUT_SOURCE_PATH SOURCE_PATH
    REPO lunarmodules/lua-compat-5.3
    REF e245d3a18957e43ef902a59a72c8902e2e4435b9 # v0.10
    SHA512 541309275f464f611f7c402ec74c84192cbc8209f5624ee96961caaf9a0bc44f8486a2f4be3a25fb0123ce600b6d830489c91bbfddeda09e9cdf2df01beea950
    HEAD_REF master
)

file(INSTALL ${SOURCE_PATH}/c-api/compat-5.3.h DESTINATION ${CURRENT_PACKAGES_DIR}/include)
file(INSTALL ${SOURCE_PATH}/c-api/compat-5.3.c DESTINATION ${CURRENT_PACKAGES_DIR}/include)

file(INSTALL ${SOURCE_PATH}/LICENSE DESTINATION ${CURRENT_PACKAGES_DIR}/share/${PORT} RENAME copyright)
file(INSTALL ${CMAKE_CURRENT_LIST_DIR}/usage DESTINATION ${CURRENT_PACKAGES_DIR}/share/${PORT})
