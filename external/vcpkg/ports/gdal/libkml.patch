diff --git a/cmake/modules/packages/FindLibKML.cmake b/cmake/modules/packages/FindLibKML.cmake
index 9418247..589b83d 100644
--- a/cmake/modules/packages/FindLibKML.cmake
+++ b/cmake/modules/packages/FindLibKML.cmake
@@ -56,6 +56,8 @@ find_library(LIBKML_BASE_LIBRARY
              HINTS ${PC_LIBKML_LIBRARY_DIRS} )
 mark_as_advanced(LIBKML_BASE_LIBRARY)
 
+cmake_policy(PUSH)
+cmake_policy(SET CMP0057 NEW)
 set(libkml_known_components DOM CONVENIENCE ENGINE REGIONATOR)
 foreach(_comp IN LISTS libkml_known_components)
   if(${_comp} IN_LIST LibKML_FIND_COMPONENTS)
@@ -99,21 +101,20 @@ find_package_handle_standard_args(LibKML
 if(LIBKML_FOUND)
   set(LIBKML_INCLUDE_DIRS "${LIBKML_INCLUDE_DIR}")
   set(LIBKML_LIBRARIES "${LIBKML_BASE_LIBRARY}")
+  find_package(LibKML CONFIG REQUIRED)
   if(NOT TARGET LIBKML::LibKML)
-    add_library(LIBKML::LibKML UNKNOWN IMPORTED)
+    add_library(LIBKML::LibKML INTERFACE IMPORTED)
     set_target_properties(LIBKML::LibKML PROPERTIES
-                          INTERFACE_INCLUDE_DIRECTORIES "${LIBKML_INCLUDE_DIR}"
-                          IMPORTED_LINK_INTERFACE_LANGUAGES "C++"
-                          IMPORTED_LOCATION "${LIBKML_BASE_LIBRARY}")
+                          INTERFACE_LINK_LIBRARIES kmlbase)
   endif()
   foreach(_comp IN LISTS libkml_known_components)
     if(${_comp} IN_LIST LibKML_FIND_COMPONENTS)
+      string(TOLOWER ${_comp} _name)
       list(APPEND LIBKML_LIBRARIES "${LIBKML_${_comp}_LIBRARY}")
       if(NOT TARGET LIBKML::${_comp})
-        add_library(LIBKML::${_comp} UNKNOWN IMPORTED)
+        add_library(LIBKML::${_comp} INTERFACE IMPORTED)
         set_target_properties(LIBKML::${_comp} PROPERTIES
-                              IMPORTED_LINK_INTERFACE_LANGUAGES "C++"
-                              IMPORTED_LOCATION "${LIBKML_${_comp}_LIBRARY}")
+                              INTERFACE_LINK_LIBRARIES kml${_name})
       endif()
     endif()
   endforeach()
@@ -129,3 +130,4 @@ if(LIBKML_FOUND)
     endif()
   endforeach()
 endif()
+cmake_policy(POP)
