diff --git a/cmake/helpers/GdalDriverHelper.cmake b/cmake/helpers/GdalDriverHelper.cmake
index 838f0fd..380e20d 100644
--- a/cmake/helpers/GdalDriverHelper.cmake
+++ b/cmake/helpers/GdalDriverHelper.cmake
@@ -248,6 +248,10 @@ function(gdal_target_interfaces _TARGET)
             if (_res)
                 target_compile_options(${_TARGET} PRIVATE ${_res})
             endif ()
+            get_property(_res TARGET ${_LIB} PROPERTY INTERFACE_LINK_LIBRARIES)
+            if (_res)
+                gdal_target_interfaces(${_TARGET} ${_res})
+            endif ()
         endif ()
     endforeach ()
 endfunction()
