{"name": "gdal", "version-semver": "3.10.3", "description": "The Geographic Data Abstraction Library for reading and writing geospatial raster and vector data", "homepage": "https://gdal.org", "license": null, "supports": "!uwp", "dependencies": ["json-c", "libgeotiff", {"name": "pkgconf", "host": true}, {"name": "proj", "default-features": false, "features": ["tiff"]}, {"name": "tiff", "default-features": false}, {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}, {"name": "vcpkg-pkgconfig-get-modules", "host": true}, "zlib"], "default-features": ["gif", {"name": "hdf5", "platform": "!uwp"}, "iconv", "libkml", {"name": "libspatialite", "platform": "!uwp"}, "libxml2", "lzma", {"name": "netcdf", "platform": "!uwp & !(windows & arm64) & !android & !ios"}, "openjpeg", "openssl", "pcre2", {"name": "postgresql", "platform": "!uwp"}, "recommended-features", "webp", "zstd"], "features": {"archive": {"description": "Enable libarchive support", "dependencies": [{"name": "libarchive", "default-features": false}]}, "arrow": {"description": "Enable arrow support", "dependencies": [{"name": "arrow", "default-features": false}]}, "aws-ec2-windows": {"description": "Optimized detection of AWS EC2 Windows hosts", "dependencies": ["atl"]}, "cfitsio": {"description": "Enable cfitsio support", "dependencies": ["cfitsio"]}, "curl": {"description": "Enable CURL network support", "dependencies": [{"name": "curl", "default-features": false}]}, "expat": {"description": "Use EXPAT library", "dependencies": ["expat"]}, "freexl": {"description": "Enable FREEXL support", "dependencies": ["freexl"]}, "geos": {"description": "Enable GEOS support", "dependencies": ["geos"]}, "gif": {"description": "Enable GIF support", "dependencies": ["gif<PERSON>b"]}, "hdf5": {"description": "Enable HDF5 support", "dependencies": [{"name": "hdf5", "default-features": false, "features": ["cpp"]}]}, "iconv": {"description": "Use iconv library", "dependencies": ["libiconv"]}, "jpeg": {"description": "Use JPEG compression library", "dependencies": ["libjpeg-turbo"]}, "kea": {"description": "Enable KEA driver", "dependencies": [{"name": "gdal", "default-features": false, "features": ["hdf5"]}, "kealib"]}, "lerc": {"description": "Enable LERC support", "dependencies": ["lerc"]}, "libkml": {"description": "Enable the LibKML driver", "dependencies": ["libkml"]}, "libspatialite": {"description": "Create or update SpatiaLite databases using libspatialite", "dependencies": ["libspatialite"]}, "libxml2": {"description": "Use LibXML2 library", "dependencies": [{"name": "libxml2", "default-features": false}]}, "lzma": {"description": "Use LZMA library", "dependencies": ["liblzma"]}, "mysql-libmariadb": {"description": "Add mysql support using libmariadb", "dependencies": ["libmariadb"]}, "netcdf": {"description": "Enable NetCDF support", "dependencies": [{"name": "netcdf-c", "default-features": false}]}, "openjpeg": {"description": "Use OpenJPEG library", "dependencies": ["openjpeg"]}, "openssl": {"description": "Use OpenSSL library", "dependencies": ["openssl"]}, "parquet": {"description": "Enable parquet reading support", "dependencies": [{"name": "arrow", "default-features": false, "features": ["parquet"]}, {"name": "gdal", "default-features": false, "features": ["arrow"]}]}, "pcre2": {"description": "Enable PCRE2 support for sqlite3", "dependencies": ["pcre2"]}, "png": {"description": "Use PNG compression library", "dependencies": ["libpng"]}, "poppler": {"description": "Enable PDF reading support via poppler", "dependencies": [{"name": "poppler", "default-features": false, "features": ["private-api"]}]}, "postgresql": {"description": "Enable PostgreSQL support", "dependencies": ["libpq"]}, "qhull": {"description": "Use QHULL library", "dependencies": ["qhull"]}, "recommended-features": {"description": "Features that are explicity marked as recommended by GDAL.", "dependencies": [{"name": "gdal", "default-features": false, "features": ["curl", "expat", "geos", "jpeg", "lerc", "png", "qhull", "sqlite3"]}]}, "sqlite3": {"description": "Enable SQLite3 support", "dependencies": [{"name": "sqlite3", "features": ["rtree"]}]}, "system-libraries": {"$supports": "!windows", "description": "This feature does nothing. It is retained for compatibility."}, "tools": {"description": "Builds gdal and ogr executables"}, "webp": {"description": "Enable WEBP support", "dependencies": ["libwebp"]}, "zstd": {"description": "Use ZSTD library", "dependencies": ["zstd"]}}}