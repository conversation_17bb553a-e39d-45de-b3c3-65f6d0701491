#! /bin/sh
# Wrapper for windres to rc which do not understand '-i -o --output-format'.
# cvtres is invoked by the linker
scriptversion=2022-08-24.12; # UTC


nl='
'

# We need space, tab and new line, in precisely that order.  Quoting is
# there to prevent tools from complaining about whitespace usage.
IFS=" ""	$nl"

file_conv=

# func_file_conv build_file lazy
# Convert a $build file to $host form and store it in $file
# Currently only supports Windows hosts. If the determined conversion
# type is listed in (the comma separated) LAZY, no conversion will
# take place.
func_file_conv ()
{
  file=$1
  case $file in
    / | /[!/]*) # absolute file, and not a UNC file
      if test -z "$file_conv"; then
	# lazily determine how to convert abs files
	case `uname -s` in
	  MINGW*)
	    file_conv=mingw
	    ;;
	  CYGWIN* | MSYS*)
	    file_conv=cygwin
	    ;;
	  *)
	    file_conv=wine
	    ;;
	esac
      fi
      case $file_conv/,$2, in
	*,$file_conv,*)
	  ;;
	mingw/*)
	  file=`cmd //C echo "$file " | sed -e 's/"\(.*\) " *$/\1/'`
	  ;;
	cygwin/* | msys/*)
	  file=`cygpath -m "$file" || echo "$file"`
	  ;;
	wine/*)
	  file=`winepath -w "$file" || echo "$file"`
	  ;;
      esac
      ;;
  esac
}

# func_windres_wrapper rc args...
# Adjust compile command to suit rc instead of windres
func_windres_wrapper ()
{
  # Assume a capable shell
  bin=
  in=
  out=

  for arg
  do
    if test -z "$bin"; then
      bin=$1
    elif test -n "$eat"; then
      eat=
    else
      case $1 in
	--output-format=*)
	  ;;
	--define*)
	  eat=1
	  set -- "$@" "-d $2"
	  ;;
	--include-dir*)
	  eat=1
	  func_file_conv "$2"
	  set -- "$@" "-I $file"
	  ;;
	-o)
	  eat=1
	  func_file_conv "$2"
	  out="$file"
	  echo "OUTPUT:$file"
	  ;;
	*.obj)
	  func_file_conv "$1"
	  out="$file"
	  echo "OUTPUT:$file"
	  ;;
	-i)
	  eat=1
	  func_file_conv "$2" mingw
	  in="$file"
	  echo "INPUT:$file"
	  ;;
	-*)
	  set -- "$@" "${1//\\\"/\"}"
	  ;;
	*)
	  # libtool reorders arguments; save unqualified one as input
	  func_file_conv "$1"
	  in="$file"
	  echo "INPUT:$file"
	  ;;
      esac
    fi
    shift
  done
  echo "$bin" "$@" -fo "$out" "$in"
  exec "$bin" "$@" -fo "$out" "$in"
  exit 1
}

eat=

func_windres_wrapper "$@"
