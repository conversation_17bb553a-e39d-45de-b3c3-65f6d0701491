#!/usr/bin/bash
# cl_cpp_wrapper
# Wrapper around <PERSON>'s cl.exe to make it act more like Unix cpp

PATH="$PATH:/usr/bin"

case $MACHTYPE in
    *-msys)
        slash="-"
        ;;
    *)
        slash="/"
        ;;
esac

# prog specifies the program that should be run cl.exe
prog=cl.exe
debug=
cppopt=("${slash}nologo")
cppopt+=("${slash}E")
verbose=
shared_index=-1

processargs()
{
### Run through every option and convert it to the proper MS one
while test $# -gt 0; do
    case "$1" in
    -D*) optarg= ;;
    -*=*) optarg=`echo "$1" | sed 's/[-_a-zA-Z0-9]*=//'` ;;
    *) optarg= ;;
    esac
    gotparam=1
    case "$1" in
    --help)
        usage
        exit 0
        ;;
    --verbose)
        verbose=1
        ;;
    -*)
        # Remaining '-' options are passed to the compiler
        if test x$optarg != x ; then
            cppopt+=("${slash}${1:1}=$optarg")
        else
            cppopt+=("${slash}${1:1}")
        fi
        ;;

    /*)
        # All '/' options are assumed to be for cpp and are passed through
        cppopt+=("${slash}${1:1}")
        ;;

    *)
        file=$1
        #cppopt+=("$1")
        ;;
    esac
    shift
done
}

# Whitespace in paths is dealt with by setting IFS and using bash arrays

# processargs $CPP_FLAGS
IFS=""
processargs $@

if test x$V = x1 ; then
    verbose=1
fi

if test -n "$verbose" ; then
  echo -n "$prog"
  for opt in "${cppopt[@]}" ; do
    echo -n " \"$opt\""
  done
  echo ""
fi

[ $# -ge 1 -a -f "$1" ] && input="$file" || input="-"

input_file="${file:-/proc/self/fd/0}"
if [ "$input_file" == "/proc/self/fd/0" ]; then
    # CL does not support reading from STDIN so it is wrapped here. 
    tmpout=cpp_wrapper_$RANDOM.h
    /usr/bin/cp $input_file $tmpout
    exec $prog ${cppopt[@]} $tmpout
    rm -f $tmpout
else
    exec $prog ${cppopt[@]} $input_file
fi

