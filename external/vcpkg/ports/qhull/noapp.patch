diff -Naur b/CMakeLists.txt ff11b5d37d-0697029c1a.clean/CMakeLists.txt
--- b/CMakeLists.txt	2022-10-17 10:07:06.000000000 +0200
+++ ff11b5d37d-0697029c1a.clean/CMakeLists.txt	2022-10-17 10:08:40.000000000 +0200
@@ -113,6 +113,7 @@
     "BUILD_SHARED_LIBS;BUILD_STATIC_LIBS"
     ${BUILD_SHARED_LIBS}
 )
+option(BUILD_APPLICATIONS "Build applications" ON)
 
 if(INCLUDE_INSTALL_DIR)
 else()
@@ -517,131 +518,133 @@
 # If LINK_APPS_SHARED, applications are linked to reentrant qhull
 # ---------------------------------------
 
-if(${LINK_APPS_SHARED})
-    add_executable(qconvex src/qconvex/qconvex_r.c)
-    target_link_libraries(qconvex ${qhull_SHAREDR})
-    set_target_properties(qconvex PROPERTIES
-        COMPILE_DEFINITIONS "${qconvex_DEFINES}")
-
-    add_executable(qdelaunay src/qdelaunay/qdelaun_r.c)
-    target_link_libraries(qdelaunay ${qhull_SHAREDR})
-    set_target_properties(qdelaunay PROPERTIES
-        COMPILE_DEFINITIONS "${qdelaunay_DEFINES}")
-
-    add_executable(qhalf src/qhalf/qhalf_r.c)
-    target_link_libraries(qhalf ${qhull_SHAREDR})
-    set_target_properties(qhalf PROPERTIES
-        COMPILE_DEFINITIONS "${qhalf_DEFINES}")
-
-    add_executable(qhull src/qhull/unix_r.c)
-    target_link_libraries(qhull ${qhull_SHAREDR})
-    set_target_properties(qhull PROPERTIES
-        COMPILE_DEFINITIONS "${qhull_DEFINES}")
-
-    add_executable(qvoronoi src/qvoronoi/qvoronoi_r.c)
-    target_link_libraries(qvoronoi ${qhull_SHAREDR})
-    set_target_properties(qvoronoi PROPERTIES
-        COMPILE_DEFINITIONS "${qvoronoi_DEFINES}")
-
-    add_executable(rbox src/rbox/rbox_r.c)
-    target_link_libraries(rbox ${qhull_SHAREDR})
-    set_target_properties(rbox PROPERTIES
-        COMPILE_DEFINITIONS "${rbox_DEFINES}")
-else()
-    if(NOT ${BUILD_STATIC_LIBS})
-        message(FATAL_ERROR, " Nothing to build -- BUILD_SHARED_LIBS=OFF and BUILD_STATIC_LIBS=OFF")
-    endif()
-
-    add_executable(qconvex src/qconvex/qconvex.c)
-    target_link_libraries(qconvex ${qhull_STATIC})
-
-    add_executable(qdelaunay src/qdelaunay/qdelaun.c)
-    target_link_libraries(qdelaunay ${qhull_STATIC})
-
-    add_executable(qhalf src/qhalf/qhalf.c)
-    target_link_libraries(qhalf ${qhull_STATIC})
+if(BUILD_APPLICATIONS)
+    if(${LINK_APPS_SHARED})
+        add_executable(qconvex src/qconvex/qconvex_r.c)
+        target_link_libraries(qconvex ${qhull_SHAREDR})
+        set_target_properties(qconvex PROPERTIES
+            COMPILE_DEFINITIONS "${qconvex_DEFINES}")
+
+        add_executable(qdelaunay src/qdelaunay/qdelaun_r.c)
+        target_link_libraries(qdelaunay ${qhull_SHAREDR})
+        set_target_properties(qdelaunay PROPERTIES
+            COMPILE_DEFINITIONS "${qdelaunay_DEFINES}")
+
+        add_executable(qhalf src/qhalf/qhalf_r.c)
+        target_link_libraries(qhalf ${qhull_SHAREDR})
+        set_target_properties(qhalf PROPERTIES
+            COMPILE_DEFINITIONS "${qhalf_DEFINES}")
+
+        add_executable(qhull src/qhull/unix_r.c)
+        target_link_libraries(qhull ${qhull_SHAREDR})
+        set_target_properties(qhull PROPERTIES
+            COMPILE_DEFINITIONS "${qhull_DEFINES}")
+
+        add_executable(qvoronoi src/qvoronoi/qvoronoi_r.c)
+        target_link_libraries(qvoronoi ${qhull_SHAREDR})
+        set_target_properties(qvoronoi PROPERTIES
+            COMPILE_DEFINITIONS "${qvoronoi_DEFINES}")
+
+        add_executable(rbox src/rbox/rbox_r.c)
+        target_link_libraries(rbox ${qhull_SHAREDR})
+        set_target_properties(rbox PROPERTIES
+            COMPILE_DEFINITIONS "${rbox_DEFINES}")
+    else()
+        if(NOT ${BUILD_STATIC_LIBS})
+            message(FATAL_ERROR, " Nothing to build -- BUILD_SHARED_LIBS=OFF and BUILD_STATIC_LIBS=OFF")
+        endif()
 
-    add_executable(qhull src/qhull/unix_r.c)
-    target_link_libraries(qhull ${qhull_STATICR})
+        add_executable(qconvex src/qconvex/qconvex.c)
+        target_link_libraries(qconvex ${qhull_STATIC})
 
-    add_executable(qvoronoi src/qvoronoi/qvoronoi.c)
-    target_link_libraries(qvoronoi ${qhull_STATIC})
+        add_executable(qdelaunay src/qdelaunay/qdelaun.c)
+        target_link_libraries(qdelaunay ${qhull_STATIC})
 
-    add_executable(rbox src/rbox/rbox.c)
-    target_link_libraries(rbox ${qhull_STATIC})
-endif()
+        add_executable(qhalf src/qhalf/qhalf.c)
+        target_link_libraries(qhalf ${qhull_STATIC})
 
-# #@# 20
-# ---------------------------------------
-# Define testqset linked to qset.o, mem.o, and usermem.o
-# Define testqset_r linked to qset_r.o, mem_r.o, and usermem.o
-# ---------------------------------------
+        add_executable(qhull src/qhull/unix_r.c)
+        target_link_libraries(qhull ${qhull_STATICR})
 
-add_executable(testqset ${testqset_SOURCES})
-add_executable(testqset_r ${testqsetr_SOURCES})
+        add_executable(qvoronoi src/qvoronoi/qvoronoi.c)
+        target_link_libraries(qvoronoi ${qhull_STATIC})
 
-# ---------------------------------------
-# Define user_eg linked to reentrant qhull shared library
-# ---------------------------------------
-
-add_executable(user_eg src/user_eg/user_eg_r.c)
+        add_executable(rbox src/rbox/rbox.c)
+        target_link_libraries(rbox ${qhull_STATIC})
+    endif()
 
-if(${BUILD_SHARED_LIBS})
-    target_link_libraries(user_eg ${qhull_SHAREDR})
-    set_target_properties(user_eg PROPERTIES
-	COMPILE_DEFINITIONS "${user_eg_DEFINES}")
-else()
-    target_link_libraries(user_eg ${qhull_STATICR})
-endif()
+    # #@# 20
+    # ---------------------------------------
+    # Define testqset linked to qset.o, mem.o, and usermem.o
+    # Define testqset_r linked to qset_r.o, mem_r.o, and usermem.o
+    # ---------------------------------------
+
+    add_executable(testqset ${testqset_SOURCES})
+    add_executable(testqset_r ${testqsetr_SOURCES})
+
+    # ---------------------------------------
+    # Define user_eg linked to reentrant qhull shared library
+    # ---------------------------------------
+
+    add_executable(user_eg src/user_eg/user_eg_r.c)
+
+    if(${BUILD_SHARED_LIBS})
+        target_link_libraries(user_eg ${qhull_SHAREDR})
+        set_target_properties(user_eg PROPERTIES
+        COMPILE_DEFINITIONS "${user_eg_DEFINES}")
+    else()
+        target_link_libraries(user_eg ${qhull_STATICR})
+    endif()
 
-# ---------------------------------------
-# Define user_eg2 linked to reentrant qhull static library
-# ---------------------------------------
+    # ---------------------------------------
+    # Define user_eg2 linked to reentrant qhull static library
+    # ---------------------------------------
 
-add_executable(user_eg2 src/user_eg2/user_eg2_r.c)
+    add_executable(user_eg2 src/user_eg2/user_eg2_r.c)
 
-if(${BUILD_STATIC_LIBS})
-    target_link_libraries(user_eg2 ${qhull_STATICR})
-else()
-    target_link_libraries(user_eg2 ${qhull_SHAREDR})
-    set_target_properties(user_eg2 PROPERTIES
-	COMPILE_DEFINITIONS "${user_eg2_DEFINES}")
-endif()
+    if(${BUILD_STATIC_LIBS})
+        target_link_libraries(user_eg2 ${qhull_STATICR})
+    else()
+        target_link_libraries(user_eg2 ${qhull_SHAREDR})
+        set_target_properties(user_eg2 PROPERTIES
+        COMPILE_DEFINITIONS "${user_eg2_DEFINES}")
+    endif()
 
-# ---------------------------------------
-# Define user_eg3 linked to qhullcpp and qhullstatic_r static libraries
-# 
-# user_eg3 is not defined for shared libraries
-#   user_eg3 and qhullcpp must be compiled with the same compiler for setjmp/longjmp
-# ---------------------------------------
+    # ---------------------------------------
+    # Define user_eg3 linked to qhullcpp and qhullstatic_r static libraries
+    # 
+    # user_eg3 is not defined for shared libraries
+    #   user_eg3 and qhullcpp must be compiled with the same compiler for setjmp/longjmp
+    # ---------------------------------------
+
+    if(${BUILD_STATIC_LIBS})
+        add_executable(user_eg3 src/user_eg3/user_eg3_r.cpp)
+        # qhull_STATICR must be last, otherwise qh_fprintf,etc. are not loaded from qhull_CPP
+        target_link_libraries(user_eg3 ${qhull_CPP} ${qhull_STATICR})
+    endif()
 
-if(${BUILD_STATIC_LIBS})
-    add_executable(user_eg3 src/user_eg3/user_eg3_r.cpp)
-    # qhull_STATICR must be last, otherwise qh_fprintf,etc. are not loaded from qhull_CPP
-    target_link_libraries(user_eg3 ${qhull_CPP} ${qhull_STATICR})
+    # ---------------------------------------
+    # qhullp is qhull/unix.c linked to unsuported qh_QHpointer libqhull_p
+    # Included for testing qh_QHpointer 
+    # ---------------------------------------
+
+    add_executable(qhullp EXCLUDE_FROM_ALL src/qhull/unix.c)
+    target_link_libraries(qhullp ${qhull_SHAREDP})
+    set_target_properties(qhullp PROPERTIES
+        COMPILE_DEFINITIONS "${qhullp_DEFINES}")
+
+    # ---------------------------------------
+    # user_egp is user_eg/user_eg.c linked to unsuported qh_QHpointer libqhull_p
+    # Included for compatibility with qhull-2012.1 
+    # ---------------------------------------
+
+    add_executable(user_egp EXCLUDE_FROM_ALL src/user_eg/user_eg.c)
+    target_link_libraries(user_egp ${qhull_SHAREDP})
+    set_target_properties(user_egp PROPERTIES
+        COMPILE_DEFINITIONS "${user_egp_DEFINES}")
 endif()
 
 # ---------------------------------------
-# qhullp is qhull/unix.c linked to unsuported qh_QHpointer libqhull_p
-# Included for testing qh_QHpointer 
-# ---------------------------------------
-
-add_executable(qhullp EXCLUDE_FROM_ALL src/qhull/unix.c)
-target_link_libraries(qhullp ${qhull_SHAREDP})
-set_target_properties(qhullp PROPERTIES
-    COMPILE_DEFINITIONS "${qhullp_DEFINES}")
-
-# ---------------------------------------
-# user_egp is user_eg/user_eg.c linked to unsuported qh_QHpointer libqhull_p
-# Included for compatibility with qhull-2012.1 
-# ---------------------------------------
-
-add_executable(user_egp EXCLUDE_FROM_ALL src/user_eg/user_eg.c)
-target_link_libraries(user_egp ${qhull_SHAREDP})
-set_target_properties(user_egp PROPERTIES
-    COMPILE_DEFINITIONS "${user_egp_DEFINES}")
-
-# ---------------------------------------
 # Define test
 # ---------------------------------------
 
@@ -676,7 +679,9 @@
 # Define install
 # ---------------------------------------
 
-set(qhull_TARGETS_INSTALL ${qhull_TARGETS_APPLICATIONS})
+if(BUILD_APPLICATIONS)
+    set(qhull_TARGETS_INSTALL ${qhull_TARGETS_APPLICATIONS})
+endif()
 if (BUILD_SHARED_LIBS)
     list(APPEND qhull_TARGETS_INSTALL ${qhull_TARGETS_SHARED})
 endif()
