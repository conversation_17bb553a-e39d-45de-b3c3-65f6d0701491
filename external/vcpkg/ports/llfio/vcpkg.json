{"name": "llfio", "version-date": "2025-01-13", "maintainers": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "description": "P1031 low level file i/o and filesystem library for the C++ standard", "homepage": "https://github.com/ned14/llfio", "license": "Apache-2.0 OR BSL-1.0", "supports": "!uwp", "dependencies": ["outcome", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"polyfill-cxx17": {"description": "Polyfill C++17 entities", "dependencies": [{"name": "llfio", "default-features": false, "features": ["polyfill-cxx20"]}, {"name": "ned14-internal-quickcpplib", "default-features": false, "features": ["polyfill-cxx17"]}, {"name": "outcome", "default-features": false, "features": ["polyfill-cxx17"]}]}, "polyfill-cxx20": {"description": "Polyfill C++20 entities", "dependencies": [{"name": "ned14-internal-quickcpplib", "default-features": false, "features": ["polyfill-cxx20"]}, {"name": "outcome", "default-features": false, "features": ["polyfill-cxx20"]}]}, "run-tests": {"description": "Build and run the dependency validation tests"}, "status-code": {"description": "Have LLFIO use SG14 `status_code` (proposed `std::error`) instead of `std::error_code`."}}}