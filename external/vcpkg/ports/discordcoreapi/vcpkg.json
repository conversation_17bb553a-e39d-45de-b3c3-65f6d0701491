{"name": "discordcoreapi", "version": "2.0.8", "description": "A Discord bot library written in C++ using custom asynchronous coroutines.", "homepage": "https://discordcoreapi.com", "license": "MIT", "supports": "(windows & x64 & !xbox) | (linux & x64) | (osx & x64)", "dependencies": ["jsonifier", "libsodium", "openssl", "opus", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}]}