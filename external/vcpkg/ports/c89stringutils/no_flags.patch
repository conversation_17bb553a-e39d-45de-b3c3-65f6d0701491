diff --git a/CMakeLists.txt b/CMakeLists.txt
index 9e3c005..75dee3f 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -24,12 +24,6 @@ if (CMAKE_VERSION VERSION_GREATER_EQUAL "3.15")
     # the BUILD_INTERFACE genex
     set(gcc_like "$<COMPILE_LANG_AND_ID:C,CXX,ARMClang,AppleClang,Clang,GNU,LCC>")
     set(msvc "$<COMPILE_LANG_AND_ID:C,CXX,MSVC>")
-    target_compile_options(
-            "${PROJECT_NAME}_compiler_flags"
-            INTERFACE
-            "$<${gcc_like}:$<BUILD_INTERFACE:-Wshadow;-Wformat=2;-Wall;-pedantic>>"
-            "$<${msvc}:$<BUILD_INTERFACE:-W3;-WX;-Zi;-permissive->>"
-    )
 endif (CMAKE_VERSION VERSION_GREATER_EQUAL "3.15")
 # Set the build directories
 if (CMAKE_SYSTEM_NAME STREQUAL "Windows"
