LIBRARY JACK

EXPORTS

;FIXME these are unimplemented
;jack_port_uuid
;jack_get_cycle_times

jack_get_version
jack_get_version_string
jack_client_open
jack_client_new
jack_client_close
jack_client_name_size
jack_get_client_name
jack_get_uuid_for_client_name
jack_get_client_name_by_uuid
jack_internal_client_new
jack_internal_client_close
jack_activate
jack_deactivate
jack_client_thread_id
jack_is_realtime
jack_thread_wait
jack_cycle_wait
jack_cycle_signal
jack_set_process_thread
jack_set_thread_init_callback
jack_on_shutdown
jack_on_info_shutdown
jack_set_process_callback
jack_set_freewheel_callback
jack_set_buffer_size_callback
jack_set_sample_rate_callback
jack_set_client_registration_callback
jack_set_port_registration_callback
jack_set_port_connect_callback
jack_set_port_rename_callback
jack_set_graph_order_callback
jack_set_xrun_callback
jack_set_latency_callback
jack_set_freewheel
jack_set_buffer_size
jack_get_sample_rate
jack_get_buffer_size
jack_engine_takeover_timebase
jack_cpu_load
jack_port_register
jack_port_unregister
jack_port_get_buffer
jack_port_name
jack_port_short_name
jack_port_flags
jack_port_type
jack_port_type_id
jack_port_is_mine
jack_port_connected
jack_port_connected_to
jack_port_get_connections
jack_port_get_all_connections
jack_port_tie
jack_port_untie
jack_port_set_name
jack_port_rename
jack_port_set_alias
jack_port_unset_alias
jack_port_get_aliases
jack_port_request_monitor
jack_port_request_monitor_by_name
jack_port_ensure_monitor
jack_port_monitoring_input
jack_connect
jack_disconnect
jack_port_disconnect
jack_port_name_size
jack_port_type_size
jack_port_type_get_buffer_size
jack_port_set_latency
jack_port_get_latency_range
jack_port_set_latency_range
jack_recompute_total_latencies
jack_port_get_latency
jack_port_get_total_latency
jack_recompute_total_latency
jack_get_ports
jack_port_by_name
jack_port_by_id
jack_frames_since_cycle_start
jack_frame_time
jack_last_frame_time
jack_frames_to_time
jack_time_to_frames
jack_get_time
jack_set_error_function
jack_set_info_function
jack_free
jack_midi_get_event_count
jack_midi_event_get
jack_midi_clear_buffer
jack_midi_max_event_size
jack_midi_event_reserve
jack_midi_event_write
jack_midi_get_lost_event_count

