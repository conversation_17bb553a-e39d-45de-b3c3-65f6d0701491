diff --git a/Makefile.am b/Makefile.am
index 6135f2a..e68974f 100644
--- a/Makefile.am
+++ b/Makefile.am
@@ -8,7 +8,7 @@ INCLUDE = -I$(abs_top_builddir)/include \
 		  -I$(abs_top_builddir)/include/xor_codes
 
 AM_CPPFLAGS = $(CPPFLAGS) $(INCLUDE)
-AM_CPPFLAGS += -Werror -Wall
+AM_CPPFLAGS += -Wall
 
 AM_CFLAGS = -fPIC $(AM_CPPFLAGS) @GCOV_FLAGS@ -L/usr/local/lib
 
diff --git a/erasurecode.pc.in b/erasurecode.pc.in
index 148c382..5a8578f 100644
--- a/erasurecode.pc.in
+++ b/erasurecode.pc.in
@@ -11,5 +11,6 @@ Version: @LIBERASURECODE_VERSION@
 Requires:
 Conflicts:
 Libs: -L${libdir} -lerasurecode -ldl
-Libs.private: @ERASURECODE_STATIC_LIBS@ -lz
+Libs.private: -lXorcode -lnullcode -lerasurecode_rs_vand
+Requires.private: zlib
 Cflags: -I${includedir}/ -I${includedir}/liberasurecode
diff --git a/src/Makefile.am b/src/Makefile.am
index 693809e..097954d 100644
--- a/src/Makefile.am
+++ b/src/Makefile.am
@@ -32,7 +32,7 @@ liberasurecode_la_SOURCES = \
 		backends/shss/shss.c \
 		backends/phazrio/libphazr.c
 
-liberasurecode_la_CPPFLAGS = -Werror @GCOV_FLAGS@
+liberasurecode_la_CPPFLAGS = @GCOV_FLAGS@
 liberasurecode_la_LIBADD = \
 		builtin/null_code/libnullcode.la \
 		builtin/xor_codes/libXorcode.la \
