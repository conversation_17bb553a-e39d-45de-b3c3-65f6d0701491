diff --git a/cmake/OpusPackageVersion.cmake b/cmake/OpusPackageVersion.cmake
index 447ce3b..15ebd8e 100644
--- a/cmake/OpusPackageVersion.cmake
+++ b/cmake/OpusPackageVersion.cmake
@@ -4,7 +4,9 @@ endif()
 set(__opus_version INCLUDED)
 
 function(get_package_version PACKAGE_VERSION PROJECT_VERSION)
-
+  set(PACKAGE_VERSION "0" CACHE STRING "opus package version")
+  set(PROJECT_VERSION "0" CACHE STRING "opus project version")
+  return()
   find_package(Git)
   if(GIT_FOUND AND EXISTS "${CMAKE_CURRENT_LIST_DIR}/.git")
     execute_process(COMMAND ${GIT_EXECUTABLE}
