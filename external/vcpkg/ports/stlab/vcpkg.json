{"name": "stlab", "version": "1.7.1", "port-version": 2, "description": ["stlab is the ongoing work of what was Adobe Software Technology Lab.", "The Concurrency library provides futures and channels, high level constructs for implementing algorithms that eases the use of multiple CPU cores while minimizing contention. This library solves several problems of the C++11 and C++17 TS futures."], "homepage": "https://github.com/stlab/libraries", "license": "BSL-1.0", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "default-features": ["cpp17shims"], "features": {"cpp17shims": {"description": "Support C++11/14 by polyfilling stlab's use of `optional` and `variant` with boost. On by default.", "dependencies": ["boost-optional", "boost-variant"]}}}