{"name": "xeus", "version": "0.24.3", "port-version": 3, "description": "C++ implementation of the Jupyter kernel protocol", "homepage": "https://github.com/jupyter-xeus/xeus", "license": "BSD-3-<PERSON><PERSON>", "dependencies": ["cppzmq", {"name": "libuuid", "platform": "!windows & !osx"}, "<PERSON><PERSON><PERSON>-<PERSON><PERSON>", "openssl", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}, "xtl", "zeromq"]}