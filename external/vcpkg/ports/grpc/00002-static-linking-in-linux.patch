diff --git a/CMakeLists.txt b/CMakeLists.txt
index 8f78306f77..e09f8fcc1e 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -211,6 +211,11 @@ if (NOT DEFINED CMAKE_POSITION_INDEPENDENT_CODE)
 endif()
 list(APPEND CMAKE_MODULE_PATH "${CMAKE_CURRENT_SOURCE_DIR}/cmake/modules")
 
+if (gRPC_STATIC_LINKING AND NOT _gRPC_PLATFORM_WINDOWS)
+  # Force to static link
+  set(CMAKE_EXE_LINKER_FLAGS "-Bstatic")
+endif()
+
 if(MSVC)
   include(cmake/msvc_static_runtime.cmake)
   add_definitions(-D_WIN32_WINNT=0x600 -D_SCL_SECURE_NO_WARNINGS -D_CRT_SECURE_NO_WARNINGS -D_WINSOCK_DEPRECATED_NO_WARNINGS)
