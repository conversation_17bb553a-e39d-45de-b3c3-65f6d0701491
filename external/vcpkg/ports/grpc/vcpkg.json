{"name": "grpc", "version-semver": "1.71.0", "description": "gRPC is a modern, open source, high-performance remote procedure call (RPC) framework that can run anywhere. gRPC enables client and server applications to communicate transparently, and simplifies the building of connected systems.", "homepage": "https://github.com/grpc/grpc", "license": "Apache-2.0", "dependencies": [{"name": "abseil", "features": ["cxx17"]}, {"name": "c-ares", "platform": "!uwp"}, {"name": "grpc", "host": true, "features": ["codegen"]}, "openssl", "protobuf", {"name": "protobuf", "host": true}, "re2", "utf8-range", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}, "zlib"], "features": {"codegen": {"description": "Build code generator machinery", "supports": "!uwp"}}}