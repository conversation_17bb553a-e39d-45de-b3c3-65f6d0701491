diff --git a/CMakeLists.txt b/CMakeLists.txt
index 2f633092ae..43fa8f6d97 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -481,7 +481,7 @@ if (NOT EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/third_party/xds AND gRPC_DOWNLOAD_ARC
 endif()
 
 if(WIN32)
-  set(_gRPC_ALLTARGETS_LIBRARIES ${_gRPC_ALLTARGETS_LIBRARIES} ws2_32 crypt32)
+  set(_gRPC_ALLTARGETS_LIBRARIES ${_gRPC_ALLTARGETS_LIBRARIES} wsock32 ws2_32 crypt32 gdi32)
   set(_gRPC_STATIC_WIN32 STATIC)
 endif()
   
