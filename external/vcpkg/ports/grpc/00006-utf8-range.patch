diff --git a/CMakeLists.txt b/CMakeLists.txt
index 2d1af5ce7d..791b933345 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -4348,7 +4348,7 @@ if(gRPC_INSTALL)
   )
 endif()
 
-
+if(0)
 add_library(utf8_range_lib
   third_party/utf8_range/utf8_range.c
 )
@@ -4398,6 +4398,7 @@ if(gRPC_INSTALL)
     ARCHIVE DESTINATION ${gRPC_INSTALL_LIBDIR}
   )
 endif()
+endif()
 
 if(gRPC_BUILD_TESTS)
 
@@ -51142,9 +51143,9 @@ generate_pkgconfig(
   "high performance general RPC framework"
   "${gRPC_CORE_VERSION}"
   "absl_algorithm_container absl_any_invocable absl_base absl_bind_front absl_bits absl_check absl_cleanup absl_config absl_cord absl_core_headers absl_flags absl_flags_marshalling absl_flat_hash_map absl_flat_hash_set absl_function_ref absl_hash absl_inlined_vector absl_log absl_log_globals absl_log_severity absl_memory absl_no_destructor absl_optional absl_random_bit_gen_ref absl_random_distributions absl_random_random absl_span absl_status absl_statusor absl_str_format absl_strings absl_synchronization absl_time absl_type_traits absl_utility gpr"
-  "libcares openssl re2 zlib"
+  "libcares openssl re2 zlib utf8_range"
   "-lgrpc"
-  "-laddress_sorting -lupb_textformat_lib -lupb_json_lib -lupb_wire_lib -lupb_message_lib -lutf8_range_lib -lupb_mini_descriptor_lib -lupb_mem_lib -lupb_base_lib"
+  "-laddress_sorting -lupb_textformat_lib -lupb_json_lib -lupb_wire_lib -lupb_message_lib -lupb_mini_descriptor_lib -lupb_mem_lib -lupb_base_lib"
   "grpc.pc")
 
 # grpc_unsecure .pc file
@@ -51153,9 +51154,9 @@ generate_pkgconfig(
   "high performance general RPC framework without SSL"
   "${gRPC_CORE_VERSION}"
   "absl_algorithm_container absl_any_invocable absl_base absl_bind_front absl_bits absl_check absl_cleanup absl_config absl_cord absl_core_headers absl_flags absl_flags_marshalling absl_flat_hash_map absl_flat_hash_set absl_function_ref absl_hash absl_inlined_vector absl_log absl_log_globals absl_log_severity absl_memory absl_no_destructor absl_optional absl_random_bit_gen_ref absl_random_distributions absl_random_random absl_span absl_status absl_statusor absl_str_format absl_strings absl_synchronization absl_time absl_type_traits absl_utility gpr"
-  "libcares zlib"
+  "libcares zlib utf8_range"
   "-lgrpc_unsecure"
-  "-laddress_sorting -lupb_wire_lib -lupb_message_lib -lutf8_range_lib -lupb_mini_descriptor_lib -lupb_mem_lib -lupb_base_lib"
+  "-laddress_sorting -lupb_wire_lib -lupb_message_lib -lupb_mini_descriptor_lib -lupb_mem_lib -lupb_base_lib"
   "grpc_unsecure.pc")
 
 # grpc++ .pc file
@@ -51164,9 +51165,9 @@ generate_pkgconfig(
   "C++ wrapper for gRPC"
   "${gRPC_CPP_VERSION}"
   "absl_absl_check absl_absl_log absl_algorithm_container absl_any_invocable absl_base absl_bind_front absl_bits absl_check absl_cleanup absl_config absl_cord absl_core_headers absl_flags absl_flags_marshalling absl_flat_hash_map absl_flat_hash_set absl_function_ref absl_hash absl_inlined_vector absl_log absl_log_globals absl_log_severity absl_memory absl_no_destructor absl_optional absl_random_bit_gen_ref absl_random_distributions absl_random_random absl_span absl_status absl_statusor absl_str_format absl_strings absl_synchronization absl_time absl_type_traits absl_utility gpr grpc"
-  "libcares openssl re2 zlib"
+  "libcares openssl re2 zlib utf8_range"
   "-lgrpc++"
-  "-laddress_sorting -lupb_textformat_lib -lupb_json_lib -lupb_wire_lib -lupb_message_lib -lutf8_range_lib -lupb_mini_descriptor_lib -lupb_mem_lib -lupb_base_lib"
+  "-laddress_sorting -lupb_textformat_lib -lupb_json_lib -lupb_wire_lib -lupb_message_lib -lupb_mini_descriptor_lib -lupb_mem_lib -lupb_base_lib"
   "grpc++.pc")
 
 # grpc++_unsecure .pc file
@@ -51175,18 +51176,20 @@ generate_pkgconfig(
   "C++ wrapper for gRPC without SSL"
   "${gRPC_CPP_VERSION}"
   "absl_absl_check absl_absl_log absl_algorithm_container absl_any_invocable absl_base absl_bind_front absl_bits absl_check absl_cleanup absl_config absl_cord absl_core_headers absl_flags absl_flags_marshalling absl_flat_hash_map absl_flat_hash_set absl_function_ref absl_hash absl_inlined_vector absl_log absl_log_globals absl_log_severity absl_memory absl_no_destructor absl_optional absl_random_bit_gen_ref absl_random_distributions absl_random_random absl_span absl_status absl_statusor absl_str_format absl_strings absl_synchronization absl_time absl_type_traits absl_utility gpr grpc_unsecure"
-  "libcares zlib"
+  "libcares zlib utf8_range"
   "-lgrpc++_unsecure"
-  "-laddress_sorting -lupb_wire_lib -lupb_message_lib -lutf8_range_lib -lupb_mini_descriptor_lib -lupb_mem_lib -lupb_base_lib"
+  "-laddress_sorting -lupb_wire_lib -lupb_message_lib -lupb_mini_descriptor_lib -lupb_mem_lib -lupb_base_lib"
   "grpc++_unsecure.pc")
 
+if(gRPC_BUILD_GRPCPP_OTEL_PLUGIN)
 # grpcpp_otel_plugin .pc file
 generate_pkgconfig(
   "gRPC++ OpenTelemetry Plugin"
   "OpenTelemetry Plugin for gRPC C++"
   "${gRPC_CPP_VERSION}"
   "absl_absl_check absl_absl_log absl_algorithm_container absl_any_invocable absl_base absl_bind_front absl_bits absl_check absl_cleanup absl_config absl_cord absl_core_headers absl_flags absl_flags_marshalling absl_flat_hash_map absl_flat_hash_set absl_function_ref absl_hash absl_inlined_vector absl_log absl_log_globals absl_log_severity absl_memory absl_no_destructor absl_optional absl_random_bit_gen_ref absl_random_distributions absl_random_random absl_span absl_status absl_statusor absl_str_format absl_strings absl_synchronization absl_time absl_type_traits absl_utility gpr grpc grpc++ opentelemetry_api"
-  "libcares openssl re2 zlib"
+  "libcares openssl re2 zlib utf8_range"
   "-lgrpcpp_otel_plugin"
-  "-laddress_sorting -lupb_textformat_lib -lupb_json_lib -lupb_wire_lib -lupb_message_lib -lutf8_range_lib -lupb_mini_descriptor_lib -lupb_mem_lib -lupb_base_lib"
+  "-laddress_sorting -lupb_textformat_lib -lupb_json_lib -lupb_wire_lib -lupb_message_lib -lupb_mini_descriptor_lib -lupb_mem_lib -lupb_base_lib"
   "grpcpp_otel_plugin.pc")
+endif()
diff --git a/cmake/gRPCConfig.cmake.in b/cmake/gRPCConfig.cmake.in
index d552e0bb4e..5751f503ba 100644
--- a/cmake/gRPCConfig.cmake.in
+++ b/cmake/gRPCConfig.cmake.in
@@ -9,6 +9,7 @@ list(APPEND CMAKE_MODULE_PATH ${CMAKE_CURRENT_LIST_DIR}/modules)
 @_gRPC_FIND_ABSL@
 @_gRPC_FIND_RE2@
 @_gRPC_FIND_OPENTELEMETRY@
+@_gRPC_FIND_UTF8_RANGE@
 
 # Targets
 include(${CMAKE_CURRENT_LIST_DIR}/gRPCTargets.cmake)
diff --git a/cmake/upb.cmake b/cmake/upb.cmake
index 9156e5f48f..5323b5f10d 100644
--- a/cmake/upb.cmake
+++ b/cmake/upb.cmake
@@ -14,7 +14,9 @@
 
 set(UPB_ROOT_DIR ${CMAKE_CURRENT_SOURCE_DIR}/third_party/upb)
 
-set(_gRPC_UPB_INCLUDE_DIR "${UPB_ROOT_DIR}" "${CMAKE_CURRENT_SOURCE_DIR}/third_party/utf8_range")
+set(_gRPC_UPB_INCLUDE_DIR "${UPB_ROOT_DIR}")
 set(_gRPC_UPB_GRPC_GENERATED_DIR "${CMAKE_CURRENT_SOURCE_DIR}/src/core/ext/upb-gen" "${CMAKE_CURRENT_SOURCE_DIR}/src/core/ext/upbdefs-gen")
 
 set(_gRPC_UPB_LIBRARIES upb)
+set(_gRPC_FIND_UTF8_RANGE "find_dependency(utf8_range CONFIG)")
+add_library(utf8_range_lib ALIAS utf8_range::utf8_range)
\ No newline at end of file
