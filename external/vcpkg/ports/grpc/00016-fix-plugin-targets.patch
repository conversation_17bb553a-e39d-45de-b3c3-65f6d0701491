diff --git a/cmake/gRPCConfig.cmake.in b/cmake/gRPCConfig.cmake.in
index 7cad2abca1..c287f3b413 100644
--- a/cmake/gRPCConfig.cmake.in
+++ b/cmake/gRPCConfig.cmake.in
@@ -12,6 +12,6 @@ list(APPEND CMAKE_MODULE_PATH ${CMAKE_CURRENT_LIST_DIR}/modules)
 
 # Targets
 include(${CMAKE_CURRENT_LIST_DIR}/gRPCTargets.cmake)
-if(NOT CMAKE_CROSSCOMPILING)
+if(@gRPC_BUILD_CODEGEN@)
   include(${CMAKE_CURRENT_LIST_DIR}/gRPCPluginTargets.cmake)
 endif()
