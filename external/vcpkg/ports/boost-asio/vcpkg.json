{"$comment": "Automatically generated by scripts/boost/generate-ports.ps1", "name": "boost-asio", "version": "1.87.0", "port-version": 1, "description": "Boost asio module", "homepage": "https://www.boost.org/libs/asio", "license": "BSL-1.0", "dependencies": [{"name": "boost-align", "version>=": "1.87.0"}, {"name": "boost-assert", "version>=": "1.87.0"}, {"name": "boost-cmake", "version>=": "1.87.0"}, {"name": "boost-config", "version>=": "1.87.0"}, {"name": "boost-context", "platform": "!uwp & !emscripten", "version>=": "1.87.0"}, {"name": "boost-date-time", "version>=": "1.87.0"}, {"name": "boost-headers", "version>=": "1.87.0"}, {"name": "boost-system", "version>=": "1.87.0"}, {"name": "boost-throw-exception", "version>=": "1.87.0"}], "features": {"ssl": {"description": "Build with SSL support", "dependencies": [{"name": "openssl", "platform": "!emscripten"}]}}}