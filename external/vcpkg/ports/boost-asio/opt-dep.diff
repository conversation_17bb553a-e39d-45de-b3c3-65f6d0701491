diff --git a/CMakeLists.txt b/CMakeLists.txt
index a55a62f..be4452d 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -17,12 +17,16 @@ target_link_libraries(boost_asio
     Boost::align
     Boost::assert
     Boost::config
-    Boost::context
+    $<TARGET_NAME_IF_EXISTS:Boost::context>
     Boost::date_time
     Boost::system
     Boost::throw_exception
 )
 
+if(NOT TARGET Boost::context)
+  target_compile_definitions(boost_asio INTERFACE $<$<NOT:$<TARGET_EXISTS:Boost::context>>:BOOST_ASIO_DISABLE_BOOST_CONTEXT_FIBER>)
+endif()
+
 target_compile_features(boost_asio INTERFACE cxx_std_11)
 
 if(BUILD_TESTING AND EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/test/CMakeLists.txt")
