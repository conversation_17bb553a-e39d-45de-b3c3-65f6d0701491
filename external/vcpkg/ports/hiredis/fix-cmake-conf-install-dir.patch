diff --git a/CMakeLists.txt b/CMakeLists.txt
index fca0ee8..b67888e 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -125,7 +127,7 @@ export(EXPORT hiredis-targets
 if(WIN32)
     SET(CMAKE_CONF_INSTALL_DIR share/hiredis)
 else()
-    SET(CMAKE_CONF_INSTALL_DIR ${CMAKE_INSTALL_LIBDIR}/cmake/hiredis)
+    SET(CMAKE_CONF_INSTALL_DIR share/hiredis)
 endif()
 SET(INCLUDE_INSTALL_DIR include)
 include(CMakePackageConfigHelpers)
@@ -200,7 +204,7 @@ IF(ENABLE_SSL)
     if(WIN32)
         SET(CMAKE_CONF_INSTALL_DIR share/hiredis_ssl)
     else()
-        SET(CMAKE_CONF_INSTALL_DIR ${CMAKE_INSTALL_LIBDIR}/cmake/hiredis_ssl)
+        SET(CMAKE_CONF_INSTALL_DIR share/hiredis_ssl)
     endif()
     configure_package_config_file(hiredis_ssl-config.cmake.in ${CMAKE_CURRENT_BINARY_DIR}/hiredis_ssl-config.cmake
                                   INSTALL_DESTINATION ${CMAKE_CONF_INSTALL_DIR}
