diff --git a/CMakeLists.txt b/CMakeLists.txt
index 1eb391f2c..2665fa73e 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -360,6 +360,7 @@ if (CIVETWEB_ENABLE_CXX)
   else()
     add_cxx_compiler_flag(-std=${CIVETWEB_CXX_STANDARD})
   endif()
+  if(0)
   add_cxx_compiler_flag(-Wall)
   add_cxx_compiler_flag(-Wextra)
   add_cxx_compiler_flag(-Wshadow)
@@ -377,6 +378,7 @@ if (CIVETWEB_ENABLE_CXX)
     add_cxx_compiler_flag(-Werror)
     add_cxx_compiler_flag(/WX)
   endif()
+  endif()
   add_cxx_compiler_flag(-pedantic-errors)
   add_cxx_compiler_flag(-fvisibility=hidden)
   add_cxx_compiler_flag(-fstack-protector-strong RELEASE)
@@ -403,6 +405,7 @@ endif()
 
 if (NOT ZEPHYR)
   #Warnings: enable everything
+  if(0)
   add_c_compiler_flag(-Wall)
   add_c_compiler_flag(-Wextra)
   add_c_compiler_flag(-Wshadow)
@@ -422,7 +425,7 @@ if (NOT ZEPHYR)
   add_c_compiler_flag(-Wno-format-nonliteral) # printf(myFormatStringVar, ...)
   add_c_compiler_flag(-Wno-cast-qual) # const cast
   add_c_compiler_flag(/Wd4820) # padding
-
+  endif()
   add_c_compiler_flag(-pedantic-errors)
   add_c_compiler_flag(-fvisibility=hidden)
   add_c_compiler_flag(-fstack-protector-strong RELEASE)
