diff --git a/src/civetweb.c b/src/civetweb.c
index 9e321ed..0f11407 100644
--- a/src/civetweb.c
+++ b/src/civetweb.c
@@ -892,6 +892,12 @@ typedef unsigned short int in_port_t;
 #if defined(USE_X_DOM_SOCKET)
 #include <sys/un.h>
 #endif
+
+#if defined(__ANDROID_API__) && __ANDROID_API__ < 24
+    // Cf. https://android.googlesource.com/platform/bionic/+/master/docs/32-bit-abi.md#32_bit-and
+    #define fseeko fseek
+#endif
+
 #endif
 
 #define vsnprintf_impl vsnprintf
