{"$comment": "x86-windows is not within the upstream support matrix of Qt6", "name": "qtwebengine", "version": "6.8.3", "description": "Qt WebEngine provides functionality for rendering regions of dynamic web content.", "homepage": "https://www.qt.io/", "license": null, "supports": "!static & ((!x86 & !arm & native) | !windows)", "dependencies": [{"name": "ffmpeg", "platform": "!windows"}, {"name": "fontconfig", "platform": "!windows"}, {"name": "freetype", "platform": "!windows"}, {"name": "glib", "platform": "!windows"}, {"name": "gperf", "host": true}, {"name": "harfbuzz", "platform": "!windows"}, {"name": "icu", "platform": "!windows"}, {"name": "libevent", "platform": "!windows"}, {"name": "libjpeg-turbo", "platform": "!windows"}, {"name": "libpng", "platform": "!windows"}, {"name": "libvpx", "platform": "!windows"}, {"name": "libwebp", "platform": "!windows"}, {"name": "libxml2", "default-features": false, "features": ["icu"], "platform": "!windows"}, {"name": "minizip", "platform": "!windows"}, "opengl", {"name": "opus", "platform": "!windows"}, {"name": "protobuf", "platform": "!windows"}, {"name": "qtbase", "default-features": false, "features": ["gui", "network", "widgets"]}, {"name": "qtdeclarative", "default-features": false}, {"name": "qttools", "default-features": false}, {"$comment": "Requires GN host tool build by the port itself! (special version check)", "name": "qtwebengine", "host": true, "default-features": false}, {"name": "re2", "platform": "!windows"}, {"name": "snappy", "platform": "!windows"}, {"name": "vcpkg-get-python-packages", "host": true}, {"name": "vcpkg-tool-nodejs", "host": true}, {"name": "zlib", "platform": "!windows"}], "features": {"geolocation": {"description": "Build with Geolocation", "dependencies": [{"name": "qtlocation", "default-features": false}]}, "proprietary-codecs": {"description": "Enables the use of proprietary codecs such as h.264/h.265 and MP3."}, "spellchecker": {"description": "Provides a spellchecker"}, "webchannel": {"description": "Provides QtWebChannel integration", "dependencies": [{"name": "qtwebchannel", "default-features": false, "features": ["qml"]}]}}}