diff --git a/src/3rdparty/gn/build/build_win.ninja.template b/src/3rdparty/gn/build/build_win.ninja.template
index 7d2704c..b14186b 100644
--- a/src/3rdparty/gn/build/build_win.ninja.template
+++ b/src/3rdparty/gn/build/build_win.ninja.template
@@ -8,5 +8,5 @@ rule alink_thin
   description = LIB $out
 
 rule link
-  command = $ld /nologo $in /link $ldflags /PDB:$out.pdb /OUT:$out $solibs $libs
+  command = $ld /nologo $in $ldflags /PDB:$out.pdb /OUT:$out $solibs $libs
   description = LINK $out
diff --git a/src/gn/CMakeLists.txt b/src/gn/CMakeLists.txt
index 0fe3e4e..1e2556f 100644
--- a/src/gn/CMakeLists.txt
+++ b/src/gn/CMakeLists.txt
@@ -31,7 +31,7 @@ find_package(Ninja 1.7.2 REQUIRED)
 
 if(WIN32)
     set(GN_EXECUTABLE gn.exe)
-    if (CMAKE_CXX_COMPILER_ID STREQUAL "Clang" AND NOT MINGW)
+    if (CMAKE_CXX_COMPILER_ID STREQUAL "Clang" AND NOT MINGW OR MSVC)
         # Use lld-link instead of clang-cl.
         set(GN_LINKER ${CMAKE_LINKER})
     endif()
