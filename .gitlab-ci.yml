variables:
  GIT_SUBMODULE_STRATEGY: recursive
  GIT_SUBMODULE_DEPTH: 1
  BUILD_DIR: "${CI_PROJECT_DIR}/build"

stages:
  - build
  - release

build_third_party_x64-linux:
  stage: build
  image: aubo519/ubuntu-build-env:22.04
  rules:
    - if: $CI_COMMIT_REF_NAME =~ /vcpkg/
  script:
    - ./external/scripts/setup_vcpkg.sh
  cache:
    key: "${CI_PROJECT_NAME}_vcpkg_x64-linux_${CI_COMMIT_REF_NAME}"
    paths:
      - "third_party/vcpkg/**"
    policy: push
  tags:
    - docker
  timeout: 12h

build_third_party_arm64-linux:
  stage: build
  image: aubo519/ubuntu-build-env_arm64:22.04
  rules:
    - if: $CI_COMMIT_REF_NAME =~ /vcpkg/
  script:
    - ./external/scripts/setup_vcpkg.sh
  cache:
    key: "${CI_PROJECT_NAME}_vcpkg_arm64-linux_${CI_COMMIT_REF_NAME}"
    paths:
      - "third_party/vcpkg/**"
    policy: push
  tags:
    - docker
  timeout: 12h

export_third_party:
  stage: build
  image: aubo519/ubuntu-build-env:22.04
  rules:
    - if: $CI_COMMIT_REF_NAME =~ /vcpkg/
  cache:
    - key: "${CI_PROJECT_NAME}_vcpkg_x64-linux_${CI_COMMIT_REF_NAME}"
      paths:
        - "third_party/vcpkg/**"
      policy: pull
    - key: "${CI_PROJECT_NAME}_vcpkg_arm64-linux_${CI_COMMIT_REF_NAME}"
      paths:
        - "third_party/vcpkg/**"
      policy: pull
  script:
    - echo "Merging third_party libraries from all platforms..."
  artifacts:
    name: "${CI_PROJECT_NAME}_vcpkg_${CI_COMMIT_SHORT_SHA}"
    paths:
      - "third_party/vcpkg/**"
    expire_in: 2 months
  needs:
    - build_third_party_x64-linux
    - build_third_party_arm64-linux
  tags:
    - docker
  timeout: 12h

build_x64-linux:
  stage: build
  image: aubo519/ubuntu-build-env:22.04
  rules:
    - if: $CI_COMMIT_REF_NAME !~ /vcpkg/
  cache:
    key: "${CI_PROJECT_NAME}_${CI_COMMIT_REF_SLUG}_x64-linux"
    paths:
      - ${BUILD_DIR}_x64-linux/*.deb
    policy: push
  script:
    - mkdir -p ${BUILD_DIR}_x64-linux
    - cmake -G Ninja -DCMAKE_BUILD_TYPE=Release -S . -B ${BUILD_DIR}_x64-linux
    - cmake --build ${BUILD_DIR}_x64-linux -j$(nproc)
    - cpack -B ${BUILD_DIR}_x64-linux --config ${BUILD_DIR}_x64-linux/CPackConfig.cmake
  tags:
    - docker
  timeout: 24h

build_arm64-linux:
  rules:
    - if: $CI_COMMIT_REF_NAME !~ /vcpkg/
  stage: build
  image: aubo519/ubuntu-build-env_arm64:22.04
  cache:
    key: "${CI_PROJECT_NAME}_${CI_COMMIT_REF_SLUG}_arm64-linux"
    paths:
      - ${BUILD_DIR}_arm64-linux/*.deb
    policy: push
  script:
    - mkdir -p ${BUILD_DIR}_arm64-linux
    - cmake -G Ninja -DCMAKE_BUILD_TYPE=Release -S . -B ${BUILD_DIR}_arm64-linux
    - cmake --build ${BUILD_DIR}_arm64-linux -j$(nproc)
    - cpack -B ${BUILD_DIR}_arm64-linux --config ${BUILD_DIR}_arm64-linux/CPackConfig.cmake
  tags:
    - docker
  timeout: 24h

package_release:
  stage: release
  image: aubo519/ubuntu-build-env:22.04
  rules:
    - if: $CI_COMMIT_REF_NAME !~ /vcpkg/
  cache:
    - key: "${CI_PROJECT_NAME}_${CI_COMMIT_REF_SLUG}_x64-linux"
      paths:
        - ${BUILD_DIR}_x64-linux/${CI_PROJECT_NAME}_*.deb
      policy: pull
    - key: "${CI_PROJECT_NAME}_${CI_COMMIT_REF_SLUG}_arm64-linux"
      paths:
        - ${BUILD_DIR}_arm64-linux/${CI_PROJECT_NAME}_*.deb
      policy: pull
  script:
    - |
      mkdir -p ${CI_PROJECT_NAME}
      cp ${BUILD_DIR}_x64-linux/${CI_PROJECT_NAME}_*.deb ${CI_PROJECT_NAME}/
      cp ${BUILD_DIR}_arm64-linux/${CI_PROJECT_NAME}_*.deb ${CI_PROJECT_NAME}/
  artifacts:
    name: "${CI_PROJECT_NAME}_${CI_COMMIT_SHORT_SHA}"
    paths:
      - ${CI_PROJECT_NAME}
    expire_in: 6 months
  needs:
    - build_x64-linux
    - build_arm64-linux
  tags:
    - docker
  timeout: 1h

release:
  stage: release
  image: registry.gitlab.com/gitlab-org/release-cli:latest
  script:
    - apk add --no-cache git jq
    - |
      set -x
      echo "Generating release notes from commit logs..."
      CURRENT_COMMIT=$(git rev-parse HEAD)
      CURRENT_TAG=$(git tag --points-at ${CURRENT_COMMIT})

      if [ -n "${CURRENT_TAG}" ]; then
        PREVIOUS_TAG=$(git tag --sort=-v:refname | awk -v cur_tag="${CURRENT_TAG}" '/^v[0-9]+\.[0-9]+\.[0-9]+$/ && $0 != cur_tag {print; exit}')
      else
        PREVIOUS_TAG=$(git tag --sort=-v:refname | awk '/^v[0-9]+\.[0-9]+\.[0-9]+$/ {print; exit}')
      fi

      if [ -z "${PREVIOUS_TAG}" ]; then
        echo "No previous version tag found, generating release notes from all commits."
        RELEASE_NOTES=$(git log --pretty=format:"- %B")
      else
        echo "Found previous version tag ${PREVIOUS_TAG}, generating release notes from commits since this tag."
        RELEASE_NOTES=$(git log ${PREVIOUS_TAG}..HEAD --pretty=format:"- %B")
      fi

      # 添加两个空格作为换行符
      RELEASE_NOTES=$(echo "${RELEASE_NOTES}" | awk '{print $0 "  "}')

      # 使用 awk 移除无效的空行（包括只包含空格或换行符的行）
      RELEASE_NOTES=$(echo "${RELEASE_NOTES}" | awk 'NF')

      echo "Release Notes:"
      echo "${RELEASE_NOTES}"

      # 创建 JSON 格式的 assets-link
      asset="{\"name\":\"Download ${CI_PROJECT_NAME} ${CI_COMMIT_TAG}\",\"url\":\"${CI_PROJECT_URL}/-/jobs/artifacts/${CI_COMMIT_REF_NAME}/download?job=release\"}"
      assetjson=$(echo $asset | jq -c .)

      echo "Creating GitLab release for tag ${CI_COMMIT_TAG}..."
      echo "Asset Link: ${assetjson}"

      # 创建 Release
      release-cli create --name "Release ${CI_COMMIT_TAG}" --tag-name "${CI_COMMIT_TAG}" \
        --description $'This release includes the following features and fixes:\n'"${RELEASE_NOTES}" \
        --assets-link="${assetjson}"
  needs:
    - package_release
  artifacts:
    name: "${CI_PROJECT_NAME}_${CI_COMMIT_REF_NAME}_${CI_COMMIT_SHORT_SHA}"
    paths:
      - ${CI_PROJECT_NAME}
    expire_in: never
  tags:
    - docker
  only:
    - /^v\d+\.\d+\.\d+$/
  timeout: 12h
